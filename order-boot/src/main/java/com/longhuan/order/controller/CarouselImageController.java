package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.entity.CarouselImageEntity;
import com.longhuan.order.service.CarouselImageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 订单
 *
 * <AUTHOR>
 * @date 2024/07/29
 */
@Api(tags = "业务订单接口")
@RestController
@RequestMapping("/carouselImage")
@RequiredArgsConstructor
public class CarouselImageController {

    private final CarouselImageService carouselImageService;

    /**
     * 轮播图片查询接口
     */
    @ApiOperation(value = "轮播图片查询接口")
    @PostMapping("/getCarouselImages")
    public Result<List<CarouselImageEntity>> pageApproveList() {
        return Result.success(carouselImageService.getCarouselImages());
    }

}
