package com.longhuan.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.github.yulichang.wrapper.segments.Fun;
import com.longhuan.approve.api.constants.PreFileTypeEnums;
import com.longhuan.approve.api.pojo.dto.UploadDesignateTypeDTO;
import com.longhuan.approve.api.pojo.dto.changyin.ChangYinIdCardUpdateApplyResDTO;
import com.longhuan.approve.api.pojo.dto.changyin.ChangYinIdCardUpdateQueryResDTO;
import com.longhuan.approve.api.pojo.dto.changyin.ChangYinLoanUploadResDTO;
import com.longhuan.approve.api.pojo.dto.changyin.ChangYinResBodyDTO;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiAfterReplenishVerifyResponse;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.GlobalConstants;
import com.longhuan.common.core.constant.MsgConstants;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.constants.SwitchConstants;
import com.longhuan.common.redis.pojo.DictVO;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.pojo.SwitchVO;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.redis.util.DictUtils;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.core.enums.AfterLoanPatchesEnum;
import com.longhuan.order.enums.ChangYinIdCardTypeStatusEnum;
import com.longhuan.order.enums.MortgageEnums;
import com.longhuan.order.feign.*;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.*;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.order.util.EasyExcelUtil;
import com.longhuan.resource.pojo.dto.FundResourceDTO;
import com.longhuan.resource.pojo.dto.FundResourceResultDTO;
import com.longhuan.user.pojo.dto.MessageContent;
import com.longhuan.user.pojo.vo.DeptInfoVO;
import com.longhuan.user.pojo.vo.GetRoleNameByUserIdVO;
import com.longhuan.user.pojo.vo.UserInfoVO;
import com.longhuan.user.pojo.vo.UserStoreVO;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import com.fasterxml.jackson.core.type.TypeReference;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PostLoanSupplementReviewServiceImpl implements PostLoanSupplementReviewService {
    private final OrderInfoMapper orderInfoMapper;
    private final DataPermissionService dataPermissionService;
    private final UserFeign userFeign;
    private final OrderAmountMapper orderAmountMapper;
    private final OrderNodeRecordMapper orderNodeRecordMapper;
    private final OrderFileMapper orderFileMapper;
    private final OrderStateService orderStateService;
    private final AfterLoanPatchesEntityService afterLoanPatchesEntityService;
    private final PatchesAuditConclusionEntityService patchesAuditConclusionService;
    private static final String LOCK_KEY_APPROVAL_SUBMIT_PATCHES = "order:approval:submit:patches";
    private final RedisService redisService;
    private final CustomerMortgageInfoMapper customerMortgageInfoMapper;
    private final FundMortgageModelConfigMapper fundMortgageModelConfigMapper;
    private final DataAreaMapper dataAreaMapper;
    private final StoreAddressInfoMapper storeAddressInfoMapper;
    private final DictUtils dictUtils;
    private final FundInfoMapper fundInfoMapper;
    private final FundProductMappingMapper fundProductMappingMapper;
    private final static String LICENSE_PLATE_NUMBER = "LICENSE_PLATE_NUMBER";
    private final ResourceFeign resourceFeign;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final ApproveFeign approveFeign;
    private final OrderIdCardUpdateMapper orderIdCardUpdateMapper;
    private final OrderFileMenuService orderFileMenuService;
    private final DingDrawMoneyFeign dingDrawMoneyFeign;
    private final SwitchUtils switchUtils;
    private final MessageFeign messageFeign;

    @Override
    public Page<PagePatchesVO> pagePatches(PagePatchesDTO dto, LoginUser currentUser) {
        List<Integer> userIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getContractApproval())) {
            Result<List<Integer>> userIdByLikeNameList = userFeign.getUserIdByLikeNameList(dto.getContractApproval());
            if (ObjUtil.isNotNull(userIdByLikeNameList) && CollectionUtil.isNotEmpty(userIdByLikeNameList.getData())) {
                userIdList.addAll(userIdByLikeNameList.getData());
            } else {
                return new Page<>(dto.getPageNum(), dto.getPageSize());
            }
        }
        MPJLambdaWrapper<OrderInfoEntity> queryWrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getOrderNumber, OrderInfoEntity::getCustomerName,
                        OrderInfoEntity::getCustomerPhone, OrderInfoEntity::getCurrentNode,
                        OrderInfoEntity::getApplyAmount,
                        OrderInfoEntity::getTerm,
                        OrderInfoEntity::getProductName, OrderInfoEntity::getSource, OrderInfoEntity::getManagerId,
                        OrderInfoEntity::getPaymentTime,
                        OrderInfoEntity::getRegionName,
                        OrderInfoEntity::getTeamId,
                        OrderInfoEntity::getTeamName,
                        OrderInfoEntity::getStoreName,
                        OrderInfoEntity::getFundId
                )
                .selectAs(OrderInfoEntity::getId, PagePatchesVO::getOrderId)
                .selectAs(OrderInfoEntity::getPreApplyTime, PagePatchesVO::getApplyDate)
                .selectAs(OrderInfoEntity::getVehicleNumber, PagePatchesVO::getVehicleNumber)
                .selectAs(OrderInfoEntity::getRiskUserId, PagePatchesVO::getRisiUserId)
                .selectAs(OrderInfoEntity::getGpsState, PagePatchesVO::getGpsState)
                .selectAs(OrderInfoEntity::getFundName, PagePatchesVO::getFundName)
                .selectAs(CustomerMortgageInfoEntity::getMortgageType, PagePatchesVO::getMortgageType)
                .selectAs(FinalFundInfoEntity::getFundCreditTime, PagePatchesVO::getAppropriationTime)
                //.selectAs(OrderInfoEntity::getRiskUserId, PagePatchesVO::getIsReturn)
                .selectFunc("CASE WHEN %s IS NULL THEN 0 ELSE 1 END", arg -> arg.accept(
                        Fun.f("t", OrderInfoEntity::getRiskUserId)
                ), PagePatchesVO::getIsReturn)
                .selectAs(OrderInfoEntity::getQualityTestCommitTime, PagePatchesVO::getQualityTestCommitTime)
                .selectAs(OrderInfoEntity::getLastNodeFinishTime, PagePatchesVO::getApprovalSubmitTime)
                .selectAs(AfterLoanPatchesEntity::getId, PagePatchesVO::getPatchesId)
                .selectAs(AfterLoanPatchesEntity::getAfterLoanStatus, PagePatchesVO::getPatchesStatus)
//                .selectAs(OrderNodeRecordEntity::getCreateBy, PagePatchesVO::getContractApproverId)
                .leftJoin(CustomerMortgageInfoEntity.class, CustomerMortgageInfoEntity::getOrderId, OrderInfoEntity::getId)
                .leftJoin(FinalFundInfoEntity.class, FinalFundInfoEntity::getOrderId, OrderInfoEntity::getId)
                .leftJoin(AfterLoanPatchesEntity.class, AfterLoanPatchesEntity::getOrderId, OrderInfoEntity::getId)
                // 抵押待办状态筛选
                .notIn(OrderInfoEntity::getCurrentNode,
                        Arrays.asList(States.SYSTEM_TERMINAL.getNode(), States.PROCESS_TERMINAL.getNode()))
                .like(StrUtil.isNotBlank(dto.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, dto.getVehicleNumber())
                .like(StrUtil.isNotBlank(dto.getCustomerName()), OrderInfoEntity::getCustomerName, dto.getCustomerName())
                .like(StrUtil.isNotBlank(dto.getCustomerPhone()), OrderInfoEntity::getCustomerPhone, dto.getCustomerPhone())
                .like(StrUtil.isNotBlank(dto.getOrderNumber()), OrderInfoEntity::getOrderNumber, dto.getOrderNumber())
                .eq(OrderInfoEntity::getPaymentState, 2)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .eq(OrderInfoEntity::getPaymentType, 2)
                .eq(AfterLoanPatchesEntity::getDeleteFlag, 0)
                .ge(OrderInfoEntity::getCurrentNode, 5000)
                .eq(ObjUtil.isNotNull(dto.getProductId()), OrderInfoEntity::getProductId, dto.getProductId())
                .eq(ObjUtil.isNotNull(dto.getFundId()), OrderInfoEntity::getFundId, dto.getFundId())
                .eq(ObjUtil.isNotNull(dto.getStatus()), AfterLoanPatchesEntity::getAfterLoanStatus, dto.getStatus())
                .eq(AfterLoanPatchesEntity::getDeleteFlag, 0)
                .in(AfterLoanPatchesEntity::getAfterLoanStatus,
                        Arrays.asList(AfterLoanPatchesEnum.AFTER_LOAN_PATCHES.getCode(),
                                AfterLoanPatchesEnum.CONTRACT_POST_REVIEW.getCode(),
//                                AfterLoanPatchesEnum.CONTRACT_APPROVED.getCode(),
                                AfterLoanPatchesEnum.CONTRACT_OVERRULE.getCode(),
                                AfterLoanPatchesEnum.FUNDS_WAIT_AUDIT.getCode(),
                                AfterLoanPatchesEnum.FUNDS_REJECT.getCode()

                        ))
                //新增所属大区、所属门店、放款时间筛选条件
                .like(StringUtils.isNotEmpty(dto.getRegionName()),OrderInfoEntity::getRegionName, dto.getRegionName())
                .like(StringUtils.isNotEmpty(dto.getStoreName()),OrderInfoEntity::getStoreName, dto.getStoreName())
                .between(dto.getPaymentTimeStart()!=null&&dto.getPaymentTimeEnd()!=null,OrderInfoEntity::getPaymentTime, dto.getPaymentTimeStart(), dto.getPaymentTimeEnd())
                .orderByDesc(OrderInfoEntity::getPaymentTime);
        dataPermissionService.limitOrder(currentUser, queryWrapper);
        Page<PagePatchesVO> pageList = orderInfoMapper.selectJoinPage(
                new Page<>(dto.getPageNum(), dto.getPageSize()),
                PagePatchesVO.class,
                queryWrapper
        );

        List<PagePatchesVO> records = pageList.getRecords();
        List<Integer> teamIds = records.stream().map(PagePatchesVO::getTeamId).distinct().toList();
        Map<Integer, String> branchNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(teamIds)) {
            try {
                branchNameMap = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData().stream().filter(deptInfoVO -> deptInfoVO.getName() != null).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
            } catch (Exception e) {
                log.error("PostLoanSupplementReviewServiceImpl.pagePatches error for teamIds:{} e:{}", teamIds, e.getMessage());
            }
        }
        List<Integer> orderIdList1 = records.stream().map(PagePatchesVO::getOrderId).distinct().toList();
        List<OrderNodeRecordEntity> orderNodeRecordEntity = orderNodeRecordMapper.selectList(
                new LambdaQueryWrapper<OrderNodeRecordEntity>()
                        .in(OrderNodeRecordEntity::getOrderId, orderIdList1)
                        .eq(OrderNodeRecordEntity::getCurrentNode, States.PAYMENT_CONTRACT_APPROVAL.getNode())
                        .eq(OrderNodeRecordEntity::getNextNode, States.FUNDS_PAYMENT_APPROVAL.getNode())
                        .eq(OrderNodeRecordEntity::getEvent, 1)
                        .orderByDesc(OrderNodeRecordEntity::getCreateTime)
        );
        Map<Integer, Integer> orderNodeRecordMap = orderNodeRecordEntity.stream()
                .sorted(Comparator.comparing(OrderNodeRecordEntity::getCreateTime).reversed())
                .collect(Collectors.toMap(
                        OrderNodeRecordEntity::getOrderId,
                        OrderNodeRecordEntity::getCreateBy,
                        (existing, replacement) -> existing // 保留已存在的值（即时间较早的）
                ));
        Map<Integer, String> finalBranchNameMap = branchNameMap;
        ObjectMapper objectMapper = new ObjectMapper();
        SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.FUND_PATCHES_EXPIRE_TIME);
        Map<String, Map<String,Integer>> map = new HashMap<>();
        if (Objects.equals(switchInfo.getSwitchFlag(),1) && StringUtils.isNotBlank(switchInfo.getValue())){
            try {
                map = objectMapper.readValue(switchInfo.getValue(), new TypeReference<Map<String,Map<String,Integer>>>() {});
                log.info("PostLoanSupplementReviewServiceImpl.patchesDetail map:{}",map);
            } catch (JsonProcessingException e) {
                log.info("PostLoanSupplementReviewServiceImpl.patchesDetail error:{}",e.getMessage());
            }
        }
        Map<String, Map<String, Integer>> finalMap = map;
        List<Integer> contractApproverIdList = records.stream().map(PagePatchesVO::getContractApproverId).distinct().toList();
        Result<List<UserInfoVO>> userIdListResult = userFeign.searchUserNameByUserIds(contractApproverIdList);
        Map<Integer, String> userInfoVOMap = new HashMap<>();
        if (Result.isSuccess(userIdListResult)&& ObjUtil.isNotNull(userIdListResult.getData())){
            userInfoVOMap = userIdListResult.getData()
                    .stream().collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getName));
        }
        Map<Integer, String> finalUserInfoVOMap = userInfoVOMap;
        records.forEach(item -> {
//            item.setCustomerPhone(StrUtil.hide(item.getCustomerPhone(), 0, 7));
            List<OrderAmountEntity> orderAmountEntities = orderAmountMapper.selectList(new LambdaQueryWrapper<OrderAmountEntity>()
                    .eq(OrderAmountEntity::getOrderId, item.getOrderId())
                    .orderByDesc(OrderAmountEntity::getCreateTime));
            if (CollUtil.isNotEmpty(orderAmountEntities)) {
                OrderAmountEntity orderAmountEntity = orderAmountEntities.get(0);
                item.setCustomerAmount(orderAmountEntity.getCustomerConfirmAmount());
                item.setApplyAmount(orderAmountEntity.getCustomerConfirmAmount());
            }
            if (item.getCurrentNode() <= States.FUNDS_FINAL_APPROVE.getNode()) {
                item.setAppropriationTime(null);
            }

            if (CollUtil.isNotEmpty(orderNodeRecordMap)) {
                item.setContractApproverId(orderNodeRecordMap.getOrDefault(item.getOrderId(), null));
            }
            if (ObjUtil.isNotEmpty(item.getContractApproverId())) {
                if (CollUtil.isNotEmpty(finalUserInfoVOMap)){
                    item.setContractApprover(finalUserInfoVOMap.getOrDefault(item.getContractApproverId(), ""));
                }
            }

            if (finalMap.containsKey(String.valueOf(item.getFundId()))){
                Map<String, Integer> fundMap = finalMap.get(String.valueOf(item.getFundId()));
                Integer days = fundMap.get(Objects.equals(item.getMortgageType(), 0) ? "1" : "2");
                long daysBetween = ChronoUnit.DAYS.between(item.getPaymentTime().toLocalDate(), LocalDate.now());
                long remainingDays = days - daysBetween;
                // 示例：判断是否超时
//                if (daysBetween > days) {
//                    // 已超时
//                    long overdueDays = daysBetween - days;
//                    item.setPatchesCountDown(Math.toIntExact(overdueDays));
//                } else {
                    item.setPatchesCountDown(Math.toIntExact(remainingDays));
//                }
            }
        });
        // 查询经办人信息
        List<Integer> manageIds = records.stream().map(PagePatchesVO::getManagerId).filter(Objects::nonNull).toList();
        Result<List<UserStoreVO>> listResult = userFeign.searchUserStoreBatch(manageIds);
        if (Result.isSuccess(listResult)) {

            Map<Integer, UserStoreVO> managerInfos =
                    listResult.getData().stream().collect(Collectors.toMap(UserStoreVO::getUserId, item -> item));


            records.stream().filter(item -> item.getManagerId() != null).forEach(record -> {
                        UserStoreVO userInfoVOS = managerInfos.get(record.getManagerId());
                        record.setManagerName(userInfoVOS.getName());
//                        record.setStoreName(userInfoVOS.getStore());
                        record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(finalBranchNameMap.get(record.getTeamId()), ""));
                    }
            );
        }
        //客户预约节点 ，贷款金额为客户确认金额
        //if (Objects.equals(States.CUSTOMER_APPOINTMENT.getNode(), orderApproveDTO.getCurrentNode()) && !records.isEmpty()) {
        //}
        // 补录状态订单展示退回详情
        if (CollUtil.isNotEmpty(records)) {
            List<Integer> orderIdList = records.stream().filter(record -> Objects.equals(States.BUSINESS_ADDED_INFO.getNode(), record.getCurrentNode()))
                    .map(PagePatchesVO::getOrderId).toList();
            if (CollUtil.isNotEmpty(orderIdList)) {
                Map<Integer, List<OrderNodeRecordEntity>> returnReasonMap = orderNodeRecordMapper.selectList(
                                new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                        .in(OrderNodeRecordEntity::getOrderId, orderIdList)
                                        .eq(OrderNodeRecordEntity::getNextNode, States.BUSINESS_ADDED_INFO.getNode())
                                        .orderByDesc(OrderNodeRecordEntity::getCreateTime)).stream()
                        .collect(Collectors.groupingBy(OrderNodeRecordEntity::getOrderId));
                log.info("pageApproveList returnReason order {}", returnReasonMap.keySet());
                records.stream().filter(record -> Objects.equals(States.BUSINESS_ADDED_INFO.getNode(), record.getCurrentNode()))
                        .filter(record -> returnReasonMap.containsKey(record.getOrderId()))
                        .forEach(
                                record -> returnReasonMap.get(record.getOrderId()).stream()
                                        .findFirst().ifPresent(reason -> {
                                                    record.setReturnReason(reason.getRemarkExternal());
                                                    record.setIsReturn(1);
                                                }
                                        )
                        );
            }
        }
        // 订单展示签约客服
        if (CollUtil.isNotEmpty(records)) {
            List<Integer> orderIdList = records.stream().map(PagePatchesVO::getOrderId).toList();
            if (CollUtil.isNotEmpty(orderIdList)) {
                Map<Integer, List<OrderNodeRecordEntity>> returnReasonMap = orderNodeRecordMapper.selectList(
                                new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                        .in(OrderNodeRecordEntity::getOrderId, orderIdList)
                                        .eq(OrderNodeRecordEntity::getCurrentNode, States.PAYMENT_APPLY_INFORMATION.getNode())
                                        .orderByDesc(OrderNodeRecordEntity::getCreateTime)).stream()
                        .collect(Collectors.groupingBy(OrderNodeRecordEntity::getOrderId));
                List<Integer> signCustomerIdList = new ArrayList<>();
                records.stream()
                        .filter(record -> returnReasonMap.containsKey(record.getOrderId()))
                        .forEach(
                                record -> returnReasonMap.get(record.getOrderId()).stream()
                                        .findFirst().ifPresent(reason -> {
                                                    record.setSignCustomerId(reason.getUpdateBy());
                                                    signCustomerIdList.add(reason.getUpdateBy());
                                                }
                                        )
                        );
                List<UserInfoVO> data = userFeign.searchUserNameBatch(signCustomerIdList).getData();
                Map<Integer, String> signCustomerNameMap = data.stream().collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getName, (v1, v2) -> v1));

                records.forEach(record -> record.setSignCustomer(signCustomerNameMap.get(record.getSignCustomerId())));
            }
        }
        if (CollUtil.isNotEmpty(records) && !CollectionUtils.isEmpty(userIdList)) {
            List<PagePatchesVO> collect = records.stream()
                    .filter(record -> userIdList.contains(record.getContractApproverId()))
                    .toList();
            return pageList.setRecords(collect);
        }
        return pageList;
    }

    @Override
    public Boolean patchesSubmit(PatchesSubmitDTO dto, LoginUser currentUser) {
        if (StringUtils.isBlank(dto.getMortgageTime())){
            throw new BusinessException("抵押时间不能为空");
        }
//        Integer orderId = dto.getOrderId();
        AfterLoanPatchesEntity entity = afterLoanPatchesEntityService.getOne(new LambdaQueryWrapper<AfterLoanPatchesEntity>()
                .eq(AfterLoanPatchesEntity::getId, dto.getPatchesId())
                .eq(AfterLoanPatchesEntity::getDeleteFlag, 0));
        if (ObjUtil.isEmpty(entity)) {
            throw new BusinessException("补件信息不能为空");
        }
        ;
        if (Objects.equals(entity.getAfterLoanStatus(), AfterLoanPatchesEnum.CONTRACT_APPROVED.getCode())) {
            throw new BusinessException("已审核通过，请勿重复操作");
        }
        if (Objects.equals(entity.getAfterLoanStatus(), AfterLoanPatchesEnum.CONTRACT_POST_REVIEW.getCode())) {
            throw new BusinessException("已提交审核，请勿重复操作");
        }
        // 校验数据
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getFundId,
                        OrderInfoEntity::getMortgageState,
                        OrderInfoEntity::getCurrentNode,
                        OrderInfoEntity::getPaymentType)
                .eq(OrderInfoEntity::getId, entity.getOrderId()));
        Integer paymentType = dto.getPaymentType();
        //抵押待办修改预加押、抵押状态
        Integer mortgageState = Convert.toInt(dto.getMortgageState(), 0);

        if (ObjUtil.isNull(orderInfoEntity)) {
            throw new BusinessException("订单不存在");
        }
        ;
        Integer currentMortgageState = orderInfoEntity.getMortgageState();
        Integer currentNode = orderInfoEntity.getCurrentNode();
        FundEnum fundEnum = FundEnum.getFundEnum(orderInfoEntity.getFundId());

        Assert.notNull(fundEnum, () -> {
            throw new BusinessException("资方不能为空");
        });
        // 抵押文件校验
        checkMortgageFile(paymentType, entity.getOrderId(), fundEnum);
        // 订单状态和抵押状态校验
        checkMortgageStatus(currentMortgageState, mortgageState, currentNode, paymentType);
        log.info("MortgageServiceImpl.saveMortgage orderId:{} ,currentNode:{} ,currentMortgageState:{} ," +
                        "mortgageState:{}",
                entity.getOrderId(),
                currentNode,
                currentMortgageState, mortgageState);
        LambdaUpdateWrapper<OrderInfoEntity> updateWrapper = new LambdaUpdateWrapper<OrderInfoEntity>()
                .set(OrderInfoEntity::getPaymentType, paymentType);
        //判断富民银行抵押状态
        if (orderInfoEntity.getFundId() == FundEnum.FU_MIN.getValue()) {
            if (Objects.equals(mortgageState, MortgageEnums.MORTGAGE_STATUS_PRE.getCode())) {
                updateWrapper.set(OrderInfoEntity::getMortgageState, MortgageEnums.MORTGAGE_STATUS_PRE.getCode());
            }
        } else {
            updateWrapper.set(OrderInfoEntity::getMortgageState, mortgageState);
        }
        updateWrapper.set(OrderInfoEntity::getAdvanceMortgageState, dto.getAdvanceMortgageState())
                .eq(OrderInfoEntity::getId, entity.getOrderId());
        orderInfoMapper.update(updateWrapper);
        entity.setAfterLoanStatus(AfterLoanPatchesEnum.CONTRACT_POST_REVIEW.getCode());
        entity.setPatchesSubmissionTime(LocalDateTime.now());
        entity.setMortgageTime(LocalDateTime.parse(dto.getMortgageTime(),  DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
        PatchesAuditConclusionEntity patchesAuditConclusionEntity = new PatchesAuditConclusionEntity();
        patchesAuditConclusionEntity.setPatchesId(entity.getId());
        patchesAuditConclusionEntity.setOrderId(entity.getOrderId());
        patchesAuditConclusionEntity.setProcessState(3);
        patchesAuditConclusionEntity.setReviewer(currentUser.getUserId());
        patchesAuditConclusionService.save(patchesAuditConclusionEntity);


        return afterLoanPatchesEntityService.updateById(entity);
    }

    @Override
    public Page<PagePatchesVO> pagePatchesApproval(PagePatchesDTO dto, LoginUser currentUser) {
        List<Integer> userIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getContractApproval())) {
            Result<List<Integer>> userIdByLikeNameList = userFeign.getUserIdByLikeNameList(dto.getContractApproval());
            if (ObjUtil.isNotNull(userIdByLikeNameList) && CollectionUtil.isNotEmpty(userIdByLikeNameList.getData())) {
                userIdList.addAll(userIdByLikeNameList.getData());
            } else {
                return new Page<>(dto.getPageNum(), dto.getPageSize());
            }
        }
        MPJLambdaWrapper<OrderInfoEntity> queryWrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getOrderNumber, OrderInfoEntity::getCustomerName,
                        OrderInfoEntity::getCustomerPhone, OrderInfoEntity::getCurrentNode,
                        OrderInfoEntity::getApplyAmount,
                        OrderInfoEntity::getTerm,
                        OrderInfoEntity::getProductName, OrderInfoEntity::getSource, OrderInfoEntity::getManagerId,
                        OrderInfoEntity::getPaymentTime,
                        OrderInfoEntity::getRegionName,
                        OrderInfoEntity::getTeamId,
                        OrderInfoEntity::getTeamName
                )
                .selectAs(OrderInfoEntity::getId, PagePatchesVO::getOrderId)
                .selectAs(OrderInfoEntity::getPreApplyTime, PagePatchesVO::getApplyDate)
                .selectAs(OrderInfoEntity::getVehicleNumber, PagePatchesVO::getVehicleNumber)
                .selectAs(OrderInfoEntity::getRiskUserId, PagePatchesVO::getRisiUserId)
                .selectAs(OrderInfoEntity::getGpsState, PagePatchesVO::getGpsState)
                .selectAs(FundInfoEntity::getName, PagePatchesVO::getFundName)
                .selectAs(FinalFundInfoEntity::getFundCreditTime, PagePatchesVO::getAppropriationTime)
//                .selectAs(OrderNodeRecordEntity::getCreateBy, PagePatchesVO::getContractApproverId)
                .selectFunc("CASE WHEN %s IS NULL THEN 0 ELSE 1 END", arg -> arg.accept(
                        Fun.f("t", OrderInfoEntity::getRiskUserId)
                ), PagePatchesVO::getIsReturn)
                .selectAs(OrderInfoEntity::getQualityTestCommitTime, PagePatchesVO::getQualityTestCommitTime)
                .selectAs(OrderInfoEntity::getLastNodeFinishTime, PagePatchesVO::getApprovalSubmitTime)
                .selectAs(AfterLoanPatchesEntity::getId, PagePatchesVO::getPatchesId)
                .selectAs(AfterLoanPatchesEntity::getAfterLoanStatus, PagePatchesVO::getPatchesStatus)
                .selectAs(AfterLoanPatchesEntity::getPatchesSubmissionTime, PagePatchesVO::getPatchesSubmissionTime)
                .leftJoin(FundInfoEntity.class, FundInfoEntity::getId, OrderInfoEntity::getFundId)
                // 面签状态筛选
                .leftJoin(FinalFundInfoEntity.class, FinalFundInfoEntity::getOrderId, OrderInfoEntity::getId)
                .leftJoin(AfterLoanPatchesEntity.class, AfterLoanPatchesEntity::getOrderId, OrderInfoEntity::getId)
                // 抵押待办状态筛选
                .notIn(OrderInfoEntity::getCurrentNode,
                        Arrays.asList(States.SYSTEM_TERMINAL.getNode(), States.PROCESS_TERMINAL.getNode()))
                .like(StrUtil.isNotBlank(dto.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, dto.getVehicleNumber())
                .like(StrUtil.isNotBlank(dto.getCustomerName()), OrderInfoEntity::getCustomerName, dto.getCustomerName())
                .like(StrUtil.isNotBlank(dto.getCustomerPhone()), OrderInfoEntity::getCustomerPhone, dto.getCustomerPhone())
                .like(StrUtil.isNotBlank(dto.getOrderNumber()), OrderInfoEntity::getOrderNumber, dto.getOrderNumber())
                .eq(OrderInfoEntity::getPaymentState, 2)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .eq(OrderInfoEntity::getPaymentType, 2)
                .eq(AfterLoanPatchesEntity::getDeleteFlag, 0)
                .ge(OrderInfoEntity::getCurrentNode, 5000)
                .eq(ObjUtil.isNotNull(dto.getProductId()), OrderInfoEntity::getProductId, dto.getProductId())
                .eq(ObjUtil.isNotNull(dto.getFundId()), OrderInfoEntity::getFundId, dto.getFundId())
                .in(AfterLoanPatchesEntity::getAfterLoanStatus,
                        Arrays.asList(AfterLoanPatchesEnum.AFTER_LOAN_PATCHES.getCode(),
                                AfterLoanPatchesEnum.CONTRACT_POST_REVIEW.getCode(),
                                AfterLoanPatchesEnum.CONTRACT_OVERRULE.getCode(),
                                AfterLoanPatchesEnum.CONTRACT_APPROVED.getCode()
//                                AfterLoanPatchesEnum.CONTRACT_OVERRULE.getCode()
                        )
                )
                .eq(ObjUtil.isNotNull(dto.getStatus()), AfterLoanPatchesEntity::getAfterLoanStatus, dto.getStatus())
                .orderByDesc(OrderInfoEntity::getPaymentTime)
//                .distinct()
                ;
        dataPermissionService.limitPatches(currentUser, queryWrapper);
        log.info("pageApproveList 查询条件SQL：{}", queryWrapper.getSqlSelect());
        Page<PagePatchesVO> pageList = orderInfoMapper.selectJoinPage(
                new Page<>(dto.getPageNum(), dto.getPageSize()),
                PagePatchesVO.class,
                queryWrapper
        );

        List<PagePatchesVO> records = pageList.getRecords();
        List<Integer> teamIds = records.stream().map(PagePatchesVO::getTeamId).distinct().toList();
        Map<Integer, String> branchNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(teamIds)) {
            try {
                branchNameMap = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData().stream().filter(deptInfoVO -> deptInfoVO.getName() != null).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
            } catch (Exception e) {
                log.error("OrderServiceImpl.pageApproveList error for teamIds:{} e:{}", teamIds, e.getMessage());
            }
        }
        List<Integer> orderIdList1 = records.stream().map(PagePatchesVO::getOrderId).distinct().toList();
        List<OrderNodeRecordEntity> orderNodeRecordEntity = orderNodeRecordMapper.selectList(
                new LambdaQueryWrapper<OrderNodeRecordEntity>()
                        .in(OrderNodeRecordEntity::getOrderId, orderIdList1)
                        .eq(OrderNodeRecordEntity::getCurrentNode, States.PAYMENT_CONTRACT_APPROVAL.getNode())
                        .eq(OrderNodeRecordEntity::getNextNode, States.FUNDS_PAYMENT_APPROVAL.getNode())
                        .eq(OrderNodeRecordEntity::getEvent, 1)
                        .orderByDesc(OrderNodeRecordEntity::getCreateTime)
        );
        Map<Integer, Integer> orderNodeRecordMap = orderNodeRecordEntity.stream()
                .sorted(Comparator.comparing(OrderNodeRecordEntity::getCreateTime).reversed())
                .collect(Collectors.toMap(
                        OrderNodeRecordEntity::getOrderId,
                        OrderNodeRecordEntity::getCreateBy,
                        (existing, replacement) -> existing // 保留已存在的值（即时间较早的）
                ));
        Map<Integer, String> finalBranchNameMap = branchNameMap;
        ObjectMapper objectMapper = new ObjectMapper();
        SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.FUND_PATCHES_EXPIRE_TIME);
        Map<String, Map<String,Integer>> map = new HashMap<>();
        if (Objects.equals(switchInfo.getSwitchFlag(),1) && StringUtils.isNotBlank(switchInfo.getValue())){
            try {
                map = objectMapper.readValue(switchInfo.getValue(), new TypeReference<Map<String,Map<String,Integer>>>() {});
                log.info("PostLoanSupplementReviewServiceImpl.patchesDetail map:{}",map);
            } catch (JsonProcessingException e) {
                log.info("PostLoanSupplementReviewServiceImpl.patchesDetail error:{}",e.getMessage());
            }
        }
        Map<String, Map<String, Integer>> finalMap = map;
        List<Integer> contractApproverIdList = records.stream().map(PagePatchesVO::getContractApproverId).distinct().toList();
        Result<List<UserInfoVO>> userIdListResult = userFeign.searchUserNameByUserIds(contractApproverIdList);
        Map<Integer, String> userInfoVOMap = new HashMap<>();
        if (Result.isSuccess(userIdListResult)&& ObjUtil.isNotNull(userIdListResult.getData())){
            userInfoVOMap = userIdListResult.getData()
                    .stream().collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getName));
        }
        Map<Integer, String> finalUserInfoVOMap = userInfoVOMap;
        records.forEach(item -> {
//            item.setCustomerPhone(StrUtil.hide(item.getCustomerPhone(), 0, 7));
            List<OrderAmountEntity> orderAmountEntities = orderAmountMapper.selectList(new LambdaQueryWrapper<OrderAmountEntity>()
                    .eq(OrderAmountEntity::getOrderId, item.getOrderId())
                    .orderByDesc(OrderAmountEntity::getCreateTime));
            if (CollUtil.isNotEmpty(orderAmountEntities)) {
                OrderAmountEntity orderAmountEntity = orderAmountEntities.get(0);
                item.setCustomerAmount(orderAmountEntity.getCustomerConfirmAmount());
                item.setApplyAmount(orderAmountEntity.getCustomerConfirmAmount());
            }
            if (item.getCurrentNode() <= States.FUNDS_FINAL_APPROVE.getNode()) {
                item.setAppropriationTime(null);
            }

            if (CollUtil.isNotEmpty(orderNodeRecordMap)) {
                item.setContractApproverId(orderNodeRecordMap.getOrDefault(item.getOrderId(), null));
            }
            if (ObjUtil.isNotEmpty(item.getContractApproverId())) {
                if (CollUtil.isNotEmpty(finalUserInfoVOMap)){
                    item.setContractApprover(finalUserInfoVOMap.getOrDefault(item.getContractApproverId(), ""));
                }
            }

            if (finalMap.containsKey(String.valueOf(item.getFundId()))){
                Map<String, Integer> fundMap = finalMap.get(String.valueOf(item.getFundId()));
                Integer days = fundMap.get(Objects.equals(item.getMortgageType(), 0) ? "1" : "2");
                long daysBetween = ChronoUnit.DAYS.between(item.getPaymentTime().toLocalDate(), LocalDate.now());
                long remainingDays = days - daysBetween;
                // 示例：判断是否超时
//                if (daysBetween > days) {
//                    // 已超时
//                    long overdueDays = daysBetween - days;
//                    item.setPatchesCountDown(Math.toIntExact(overdueDays));
//                } else {
                item.setPatchesCountDown(Math.toIntExact(remainingDays));
//                }
            }
        });
        // 查询经办人信息
        List<Integer> manageIds = records.stream().map(PagePatchesVO::getManagerId).filter(Objects::nonNull).toList();
        Result<List<UserStoreVO>> listResult = userFeign.searchUserStoreBatch(manageIds);
        if (Result.isSuccess(listResult)) {

            Map<Integer, UserStoreVO> managerInfos =
                    listResult.getData().stream().collect(Collectors.toMap(UserStoreVO::getUserId, item -> item));


            records.stream().filter(item -> item.getManagerId() != null).forEach(record -> {
                        UserStoreVO userInfoVOS = managerInfos.get(record.getManagerId());
                        record.setManagerName(userInfoVOS.getName());
                        record.setStoreName(userInfoVOS.getStore());
                        record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(finalBranchNameMap.get(record.getTeamId()), ""));
                    }
            );

        }
        //客户预约节点 ，贷款金额为客户确认金额
        //if (Objects.equals(States.CUSTOMER_APPOINTMENT.getNode(), orderApproveDTO.getCurrentNode()) && !records.isEmpty()) {
        //}
        // 补录状态订单展示退回详情
        if (CollUtil.isNotEmpty(records)) {
            List<Integer> orderIdList = records.stream().filter(record -> Objects.equals(States.BUSINESS_ADDED_INFO.getNode(), record.getCurrentNode()))
                    .map(PagePatchesVO::getOrderId).toList();
            if (CollUtil.isNotEmpty(orderIdList)) {
                Map<Integer, List<OrderNodeRecordEntity>> returnReasonMap = orderNodeRecordMapper.selectList(
                                new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                        .in(OrderNodeRecordEntity::getOrderId, orderIdList)
                                        .eq(OrderNodeRecordEntity::getNextNode, States.BUSINESS_ADDED_INFO.getNode())
                                        .orderByDesc(OrderNodeRecordEntity::getCreateTime)).stream()
                        .collect(Collectors.groupingBy(OrderNodeRecordEntity::getOrderId));
                log.info("pageApproveList returnReason order {}", returnReasonMap.keySet());
                records.stream().filter(record -> Objects.equals(States.BUSINESS_ADDED_INFO.getNode(), record.getCurrentNode()))
                        .filter(record -> returnReasonMap.containsKey(record.getOrderId()))
                        .forEach(
                                record -> returnReasonMap.get(record.getOrderId()).stream()
                                        .findFirst().ifPresent(reason -> {
                                                    record.setReturnReason(reason.getRemarkExternal());
                                                    record.setIsReturn(1);
                                                }
                                        )
                        );
            }
        }
        // 订单展示签约客服
        if (CollUtil.isNotEmpty(records)) {
            List<Integer> orderIdList = records.stream().map(PagePatchesVO::getOrderId).toList();
            if (CollUtil.isNotEmpty(orderIdList)) {
                Map<Integer, List<OrderNodeRecordEntity>> returnReasonMap = orderNodeRecordMapper.selectList(
                                new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                        .in(OrderNodeRecordEntity::getOrderId, orderIdList)
                                        .eq(OrderNodeRecordEntity::getCurrentNode, States.PAYMENT_APPLY_INFORMATION.getNode())
                                        .orderByDesc(OrderNodeRecordEntity::getCreateTime)).stream()
                        .collect(Collectors.groupingBy(OrderNodeRecordEntity::getOrderId));
                List<Integer> signCustomerIdList = new ArrayList<>();
                records.stream()
                        .filter(record -> returnReasonMap.containsKey(record.getOrderId()))
                        .forEach(
                                record -> returnReasonMap.get(record.getOrderId()).stream()
                                        .findFirst().ifPresent(reason -> {
                                                    record.setSignCustomerId(reason.getUpdateBy());
                                                    signCustomerIdList.add(reason.getUpdateBy());
                                                }
                                        )
                        );
                List<UserInfoVO> data = userFeign.searchUserNameBatch(signCustomerIdList).getData();
                Map<Integer, String> signCustomerNameMap = data.stream().collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getName, (v1, v2) -> v1));

                records.forEach(record -> record.setSignCustomer(signCustomerNameMap.get(record.getSignCustomerId())));
            }
        }
        if (CollUtil.isNotEmpty(records) && !CollectionUtils.isEmpty(userIdList)) {
            List<PagePatchesVO> collect = records.stream()
                    .filter(record -> userIdList.contains(record.getContractApproverId()))
                    .toList();
            return pageList.setRecords(collect);
        }
        return pageList;
    }

    @Override
    public Boolean patchesApproval(PatchesApprovalDTO dto, LoginUser currentUser) {
        AfterLoanPatchesEntity entity = afterLoanPatchesEntityService.getOne(new LambdaQueryWrapper<AfterLoanPatchesEntity>()
                .eq(AfterLoanPatchesEntity::getId, dto.getPatchesId())
                .eq(AfterLoanPatchesEntity::getDeleteFlag, 0));
        if (ObjUtil.isEmpty(entity)) {
            throw new BusinessException("补件信息不能为空");
        }
        ;
        if (Objects.equals(entity.getAfterLoanStatus(), AfterLoanPatchesEnum.AFTER_LOAN_PATCHES.getCode())) {
            throw new BusinessException("当前状态为待提交");
        }

        if (Objects.equals(entity.getAfterLoanStatus(), AfterLoanPatchesEnum.CONTRACT_APPROVED.getCode())) {
            throw new BusinessException("当前状态为审核通过");
        }
        Integer orderId = entity.getOrderId();
        String lockValue = UUID.randomUUID().toString();
        // States node = dto.getNode();
        try {
            Integer status = dto.getStatus();
            Integer userId = currentUser.getUserId();
            redisService.acquireLock(LOCK_KEY_APPROVAL_SUBMIT_PATCHES + orderId, lockValue, 60 * 1000L, 5000);
            PatchesAuditConclusionEntity patchesAuditConclusionEntity = new PatchesAuditConclusionEntity();
            patchesAuditConclusionEntity.setPatchesId(entity.getId());
            patchesAuditConclusionEntity.setOrderId(entity.getOrderId());
            patchesAuditConclusionEntity.setAuditConclusion(dto.getRemark());
            patchesAuditConclusionEntity.setProcessState(dto.getStatus());
            patchesAuditConclusionEntity.setReviewer(userId);
            boolean save = patchesAuditConclusionService.save(patchesAuditConclusionEntity);
            if (save) {
                entity.setAfterLoanStatus( Objects.equals(status,1) ?
                        AfterLoanPatchesEnum.CONTRACT_APPROVED.getCode() : AfterLoanPatchesEnum.CONTRACT_OVERRULE.getCode());
                afterLoanPatchesEntityService.updateById( entity);
                if (status == 1) {
                    orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                            .set(OrderInfoEntity::getMortgageState, 3)
                            .eq(OrderInfoEntity::getId, orderId));
                }
            }
            if (!Objects.equals(status, 1)){
                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
                //签约客服
                Map<Integer, Integer> returnReasonMap = orderNodeRecordMapper.selectList(
                                new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                        .eq(OrderNodeRecordEntity::getOrderId, orderId)
                                        .eq(OrderNodeRecordEntity::getCurrentNode, States.PAYMENT_APPLY_INFORMATION.getNode())
                                        .orderByDesc(OrderNodeRecordEntity::getCreateTime)).stream()
                        .collect(Collectors.groupingBy(OrderNodeRecordEntity::getOrderId)).entrySet().stream()
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry.getValue().stream()
                                        .max(Comparator.comparing(OrderNodeRecordEntity::getCreateTime))
                                        .get()
                                        .getUpdateBy()
                        ));
                List<Integer> userIdList = new ArrayList<>(returnReasonMap.values().stream()
                        .distinct()
                        .toList());
                userIdList.add(orderInfoEntity.getManagerId());
                List<UserInfoVO> data = userFeign.searchUserNameBatch(userIdList).getData();
                Map<Integer, String> signCustomerNameMap = data.stream().collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getMobile, (v1, v2) -> v1));
                log.info("PostLoanSupplementReviewServiceImpl.patchesApproval signCustomerNameMap:{}", signCustomerNameMap);
                MessageContent messageContent = new MessageContent()
                        .setMsgType(MsgConstants.MSG_TEXT)
                        .setSendType(MsgConstants.SEND_DD_NOTICE)
                        .setContent("客户:"+orderInfoEntity.getCustomerName()+",车牌号:"+orderInfoEntity.getVehicleNumber()+"贷后补件失败,失败原因:"+dto.getRemark()+"请尽快完成贷后补件")
                        .setReceiver(signCustomerNameMap.get(returnReasonMap.get(entity.getOrderId())));
                messageFeign.sendMessage(messageContent);
                MessageContent messageContent1 = new MessageContent()
                        .setMsgType(MsgConstants.MSG_TEXT)
                        .setSendType(MsgConstants.SEND_DD_NOTICE)
                        .setContent("客户:"+orderInfoEntity.getCustomerName()+",车牌号:"+orderInfoEntity.getVehicleNumber()+"贷后补件失败,失败原因:"+dto.getRemark()+"请尽快完成贷后补件")
                        .setReceiver(signCustomerNameMap.get(orderInfoEntity.getManagerId()));
                messageFeign.sendMessage(messageContent1);
            }else {
                boolean flag = uploadFundFile(orderId);
                PatchesAuditConclusionEntity patchesAuditConclusion = new PatchesAuditConclusionEntity();
                patchesAuditConclusion.setPatchesId(entity.getId());
                patchesAuditConclusion.setOrderId(entity.getOrderId());
                Integer afterLoanStatus = 0;
                int fundUploadFlag = 0;
                int fundPatchCount = 0;
                if (flag){
                    afterLoanStatus =  AfterLoanPatchesEnum.FUNDS_APPROVED.getCode();
                    patchesAuditConclusion.setProcessState(5);
                    fundUploadFlag = 1;
                }else {
                    afterLoanStatus =  AfterLoanPatchesEnum.FUNDS_REJECT.getCode();
                    fundPatchCount = entity.getFundPatchCount()+1;
                    patchesAuditConclusion.setProcessState(4);
                }
                entity.setFundUploadFlag(fundUploadFlag);
                entity.setFundPatchCount(fundPatchCount);
                entity.setAfterLoanStatus(afterLoanStatus);
                afterLoanPatchesEntityService.updateById(entity);
                patchesAuditConclusionService.save(patchesAuditConclusion);
            }
            redisService.releaseLock(LOCK_KEY_APPROVAL_SUBMIT_PATCHES + orderId, lockValue);
//            return submitResultVO;
        } catch (InterruptedException e) {
            //releaseCredits(node, orderId);
            log.error("Lock acquisition failed for orderId: {}", orderId);
            throw new BusinessException("操作频繁，请稍后再试");
        } catch (Exception e) {
            //发生异常释放额度
            // releaseCredits(node, orderId);
            log.info("PostLoanSupplementReviewServiceImpl.patchesApproval error:{}", e.getMessage());
        } finally {
            redisService.releaseLock(LOCK_KEY_APPROVAL_SUBMIT_PATCHES + orderId, lockValue);
        }
        return null;
    }

    @Override
    public AppointmentMortgageDetailVO patchesDetail(Integer patchesId) {
        log.info("patchesDetail patchesId = {}", patchesId);
        AfterLoanPatchesEntity byId = afterLoanPatchesEntityService.getById(patchesId);
        if (ObjUtil.isEmpty(byId)) {
            throw new BusinessException("补件记录不存在");
        }
        AppointmentMortgageDetailVO appointmentMortgageDetailVO = new AppointmentMortgageDetailVO();

        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectJoinList(OrderInfoEntity.class,
                new MPJLambdaWrapper<OrderInfoEntity>()
                        .selectAll(OrderInfoEntity.class)
                        .selectAs(OrderVehicleInfoEntity::getVehicleNumber, OrderInfoEntity::getVehicleNumber)
                        .innerJoin(OrderVehicleInfoEntity.class, on ->
                                on.eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                                        .eq(OrderVehicleInfoEntity::getOrderId, OrderInfoEntity::getId))
                        .eq(OrderInfoEntity::getId, byId.getOrderId())
                        .orderByDesc(OrderVehicleInfoEntity::getId)
        );

//        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
//        Assert.notNull(orderInfoEntity, "订单信息不存在");
        if (CollUtil.isEmpty(orderInfoEntityList)) {
            throw new BusinessException("订单信息不存在");
        }
        OrderInfoEntity orderInfoEntity = orderInfoEntityList.get(0);
        //查询订单车辆信息
        String vehicleNumber = orderInfoEntity.getVehicleNumber();
        Integer paymentType = orderInfoEntity.getPaymentType();
        Integer fundId = orderInfoEntity.getFundId();
        String fundName = orderInfoEntity.getFundName();

        List<AppointmentMortgageDetailVO> appointmentMortgageDetailVOList = customerMortgageInfoMapper.selectJoinList(AppointmentMortgageDetailVO.class,
                new MPJLambdaWrapper<CustomerMortgageInfoEntity>()
                        .eq(CustomerMortgageInfoEntity::getOrderId, byId.getOrderId())
                        .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
        );
        log.info("appointmentMortgageDetailVOList.size = {}", appointmentMortgageDetailVOList.size());

        if (CollUtil.isNotEmpty(appointmentMortgageDetailVOList)) {
            appointmentMortgageDetailVO = appointmentMortgageDetailVOList.get(0);
        }
        if (Objects.isNull(paymentType) || paymentType <= 0) {
            DictVO dict = dictUtils.getDictInfo(LICENSE_PLATE_NUMBER);
            List<DictVO.DictItemInfo> dictItemInfo = dict.getDictItemInfo();
            DictVO.DictItemInfo dictItem = dictItemInfo.stream().filter(item -> vehicleNumber.contains(item.getName())).findFirst().orElse(null);
            paymentType = Objects.nonNull(dictItem) ? 2 : 1;
            orderInfoEntity.setPaymentType(paymentType);
            orderInfoMapper.updateById(orderInfoEntity);
        }

        //--------------edit by zangxx at 2023/08/08  start---------------
        //配置
        List<FundMortgageModelConfigEntity> configEntityList = fundMortgageModelConfigMapper.selectList(new LambdaQueryWrapper<FundMortgageModelConfigEntity>()
                .eq(FundMortgageModelConfigEntity::getFundId, fundId)
                .eq(FundMortgageModelConfigEntity::getDeleteFlag, 0)
        );
        log.info("CustomerAppointmentServiceImpl configEntityList.size = {}", configEntityList.size());
        if (CollUtil.isNotEmpty(configEntityList)) {
            //抵押方式可选
            List<Integer> mortgageTypeScope = new ArrayList<>();
            //办理渠道可选
            List<Integer> mortgageChannelScope = new ArrayList<>();
            configEntityList.forEach(item -> {
                mortgageTypeScope.add(item.getMortgageType());
                mortgageChannelScope.add(item.getMortgageChannel());
            });
            //            appointmentMortgageDetailVO.setMortgageType(configEntityList.get(0).getMortgageType());
            appointmentMortgageDetailVO.setMortgageTypeScope(mortgageTypeScope);
            appointmentMortgageDetailVO.setMortgageChannelScope(mortgageChannelScope);
            appointmentMortgageDetailVO.setChannelType(configEntityList.get(0).getChannelType());
        }
        String city = vehicleNumber.substring(0, 2);
        log.info("CustomerAppointmentServiceImpl city = {}", city);
        DataAreaEntity dataAreaEntity = dataAreaMapper.selectOne(new LambdaQueryWrapper<DataAreaEntity>()
                .like(DataAreaEntity::getCarRegion, city));
        if (ObjectUtil.isNotEmpty(dataAreaEntity)) {
            //抵押城市
            appointmentMortgageDetailVO.setVehicleMortgageCity(dataAreaEntity.getAreaName());
        }
        StoreAddressInfoEntity storeAddressInfo = storeAddressInfoMapper.selectById(appointmentMortgageDetailVO.getStoreId());
        if (ObjectUtil.isNotEmpty(storeAddressInfo)) {
            appointmentMortgageDetailVO.setStoreAreaName(storeAddressInfo.getAreaName());
            appointmentMortgageDetailVO.setStoreCityName(storeAddressInfo.getCityName());
            appointmentMortgageDetailVO.setStoreProvinceName(storeAddressInfo.getProvinceName());
        }
        //抵押待办-返回抵押相关状态
        appointmentMortgageDetailVO.setAdvanceMortgageState(orderInfoEntity.getAdvanceMortgageState())
                .setMortgageState(orderInfoEntity.getMortgageState());
        appointmentMortgageDetailVO.setCurrentNode(orderInfoEntity.getCurrentNode());

        //--------------edit by zangxx at 2023/08/08  end---------------

        appointmentMortgageDetailVO.setVehicleNumber(vehicleNumber)
                .setPaymentType(paymentType)
                .setFundId(fundId)
                .setFundName(fundName)
                .setMortgageChannelStr(ObjectUtil.isNotNull(appointmentMortgageDetailVO.getMortgageChannel()) ? dictUtils.getDictLabel(GlobalConstants.DictType.MORTGAGE_CHANNEL.name(), appointmentMortgageDetailVO.getMortgageChannel()) : "")
        ;
        appointmentMortgageDetailVO.setPatchesId(byId.getId());
//        if (byId.getAfterLoanStatus().equals(AfterLoanPatchesEnum.CONTRACT_OVERRULE.getCode()) || byId.getAfterLoanStatus().equals(AfterLoanPatchesEnum.CONTRACT_APPROVED.getCode())) {
            List<PatchesAuditConclusionEntity> entityList = patchesAuditConclusionService.list(new LambdaQueryWrapper<PatchesAuditConclusionEntity>()
                    .eq(PatchesAuditConclusionEntity::getPatchesId, byId.getId())
                            .eq(PatchesAuditConclusionEntity::getDeleteFlag, 0)
                    .orderByDesc(PatchesAuditConclusionEntity::getCreateTime));
            if (CollUtil.isNotEmpty(entityList)) {
                Optional<PatchesAuditConclusionEntity> latestEntity = entityList.stream()
                        .filter(res -> (Objects.equals(res.getProcessState(), 1) || Objects.equals(res.getProcessState(), 2)))
                        .max(Comparator.comparing(PatchesAuditConclusionEntity::getCreateTime));
                if (latestEntity.isPresent()){
                    appointmentMortgageDetailVO.setPatchesRemark(latestEntity.get().getAuditConclusion());
                }
                appointmentMortgageDetailVO.setFundRejectCount(byId.getFundPatchCount());
                List<PatchesAuditConclusionEntity> rejectList = entityList.stream()
                        .filter(res -> Objects.equals(res.getProcessState(), 4))
                        .sorted(Comparator.comparing(PatchesAuditConclusionEntity::getCreateTime))
                        .toList();

                if (CollUtil.isNotEmpty(rejectList)) {
                    StringBuilder reasonBuilder = new StringBuilder();
                    for (int i = 0; i < rejectList.size(); i++) {
                        if (i > 0) reasonBuilder.append("; ");

                        if (rejectList.get(i).getAuditConclusion().contains("风控拒绝") &&rejectList.get(i).getAuditConclusion().contains("P1")){
                            rejectList.get( i).setAuditConclusion("抵押权人信息对比失败");
                        }
                        if (rejectList.get(i).getAuditConclusion().contains("风控拒绝") &&rejectList.get(i).getAuditConclusion().contains("P2")){
                            rejectList.get( i).setAuditConclusion("车辆信息对比失败");
                        }
                        if (rejectList.get(i).getAuditConclusion().contains("风控拒绝") &&rejectList.get(i).getAuditConclusion().contains("P3")){
                            rejectList.get( i).setAuditConclusion("借款人信息对比失败");
                        }
                        if (rejectList.get(i).getAuditConclusion().contains("风控拒绝") &&rejectList.get(i).getAuditConclusion().contains("P4")){
                            rejectList.get( i).setAuditConclusion("影像资料不符合要求");
                        }
                        reasonBuilder.append("第").append(i + 1).append("次反馈拒绝理由: ")
                                .append(rejectList.get(i).getAuditConclusion());
                    }
                    appointmentMortgageDetailVO.setFundRejectReason(reasonBuilder.toString());
                }
            }
//        }
        appointmentMortgageDetailVO.setPatchesType(byId.getAfterLoanStatus());
        appointmentMortgageDetailVO.setMortgageTime(byId.getMortgageTime());
        return appointmentMortgageDetailVO;
    }

    @Override
    public List<PatchworkProcessVO> patchworkProcess(PatchworkProcessDTO dto) {
        List<PatchworkProcessVO> list = new ArrayList<>();
        List<PatchesAuditConclusionEntity> patchesList = patchesAuditConclusionService.list(
                new LambdaQueryWrapper<PatchesAuditConclusionEntity>()
                        .eq(PatchesAuditConclusionEntity::getPatchesId, dto.getPatchesId())
                        .eq(PatchesAuditConclusionEntity::getDeleteFlag, 0)
                        .orderByDesc(PatchesAuditConclusionEntity::getCreateTime)
        );
        AfterLoanPatchesEntity byId = afterLoanPatchesEntityService.getById(dto.getPatchesId());
        PatchworkProcessVO vo = new PatchworkProcessVO();
        vo.setStatus("贷后补件");
        vo.setOperatingTime(byId.getCreateTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
        list.add(vo);
        if (!CollectionUtils.isEmpty(patchesList)) {
            for (PatchesAuditConclusionEntity patchesAuditConclusionEntity : patchesList) {
                PatchworkProcessVO patchworkProcessVO = new PatchworkProcessVO();
                switch (patchesAuditConclusionEntity.getProcessState()) {
                    case 1:
                        patchworkProcessVO.setStatus("合同岗审核通过");
                        break;
                    case 2:
                        patchworkProcessVO.setStatus("合同岗审核驳回");
                        break;
                    case 3:
                        patchworkProcessVO.setStatus("提交审核");
                        break;
                    case 4:
                        patchworkProcessVO.setStatus("资方审核通过");
                        break;
                    case 5:
                        patchworkProcessVO.setStatus("资方审核拒绝");
                        break;
                    default:
                        break;
                }
                GetRoleNameByUserIdVO roleNameByUserId = dingDrawMoneyFeign.getRoleNameByUserId(patchesAuditConclusionEntity.getReviewer());
                if (ObjUtil.isNotNull(roleNameByUserId)) {
                    patchworkProcessVO.setStatusName(roleNameByUserId.getRoleName());
                    patchworkProcessVO.setRoleName(roleNameByUserId.getRoleName());
                }
                patchworkProcessVO.setReason(patchesAuditConclusionEntity.getAuditConclusion());
                patchworkProcessVO.setOperatingTime(patchesAuditConclusionEntity.getCreateTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
                list.add(patchworkProcessVO);
            }
            if (Objects.equals(list.get(0).getStatus(),"合同岗审核通过")){
                PatchworkProcessVO patchworkProcessVO = new PatchworkProcessVO();
                patchworkProcessVO.setStatus("资方审核中");
                patchworkProcessVO.setStatusName(list.get(0).getStatusName());
                patchworkProcessVO.setReason(list.get(0).getReason());
                patchworkProcessVO.setOperatingTime(list.get(0).getOperatingTime());
                list.add(patchworkProcessVO);
            }
        }
        return list;
    }

    @Override
    public List<EmployerProductListVO> employerProductList() {
        List<EmployerProductListVO> list = new ArrayList<>();
        LambdaQueryWrapper<FundInfoEntity> queryWrapper = new LambdaQueryWrapper<FundInfoEntity>();
        queryWrapper.eq(FundInfoEntity::getDeleteFlag, 0);
        queryWrapper.eq(FundInfoEntity::getFundFlag, 1);
        List<FundInfoEntity> fundInfoEntities = fundInfoMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(fundInfoEntities)) {
            for (FundInfoEntity fundInfoEntity : fundInfoEntities) {
                EmployerProductListVO employerProductListVO = new EmployerProductListVO();
                employerProductListVO.setValue(fundInfoEntity.getId());
                employerProductListVO.setLabel(fundInfoEntity.getName());
                List<FundProductMappingEntity> fundProductMappingEntities = fundProductMappingMapper.selectList(
                        new LambdaQueryWrapper<FundProductMappingEntity>()
                                .eq(FundProductMappingEntity::getFundId, fundInfoEntity.getId())
                                .eq(FundProductMappingEntity::getStatus, 0)
                                .eq(FundProductMappingEntity::getDeleteFlag, 0)
                );
                List<ProductListVO> productListVO = new ArrayList<>();
                if (!CollectionUtils.isEmpty(fundProductMappingEntities)) {
                    for (FundProductMappingEntity fundProductMappingEntity : fundProductMappingEntities) {
                        ProductListVO productVO = new ProductListVO();
                        productVO.setValue(fundProductMappingEntity.getProductId());
                        productVO.setLabel(fundProductMappingEntity.getProductName());
                        productListVO.add(productVO);
                    }

                }
                employerProductListVO.setChildren(productListVO);
                list.add(employerProductListVO);
            }
        }
        return list;
    }

    /**
     * 补件上传资方
     */
    public void uploadFundFileTask() {
        log.info("PostLoanSupplementReviewServiceImpl.uploadFundFileTask start");
        List<AfterLoanPatchesEntity> patchesList = afterLoanPatchesEntityService.list(
                new LambdaQueryWrapper<AfterLoanPatchesEntity>()
                        .eq(AfterLoanPatchesEntity::getAfterLoanStatus, AfterLoanPatchesEnum.CONTRACT_APPROVED.getCode())
                        .and(q ->
                                q.eq(AfterLoanPatchesEntity::getFundUploadFlag, 0)
                                        .or()
                                        .isNull(AfterLoanPatchesEntity::getFundUploadFlag))
                        .eq(AfterLoanPatchesEntity::getFundUploadFlag, 0)
                        .eq(AfterLoanPatchesEntity::getDeleteFlag, 0)
        );
        List<Integer> patchesIdUploadList = new ArrayList<>();
        if (CollUtil.isNotEmpty(patchesList)) {
            patchesList.forEach(patches -> {
                try {
                    boolean flag = uploadFundFile(patches.getOrderId());
                    if (flag) {
                        patchesIdUploadList.add(patches.getId());
                    } else {
                        log.info("PostLoanSupplementReviewServiceImpl.patchesSubmit error for upload resource fail orderId:{}", patches.getId());
                    }

                } catch (Exception e) {
                    log.error("PostLoanSupplementReviewServiceImpl.uploadFundFileTask error for orderId:{}", patches.getId());
                }
            });
        }

        //更新上传状态
        if (CollUtil.isNotEmpty(patchesIdUploadList)) {
            log.info("PostLoanSupplementReviewServiceImpl.uploadFundFileTask patchesIdUploadList:{}", patchesIdUploadList);
            LambdaUpdateWrapper<AfterLoanPatchesEntity> luw = new LambdaUpdateWrapper<>();
            luw.set(AfterLoanPatchesEntity::getFundUploadFlag, 1);
            luw.in(AfterLoanPatchesEntity::getId, patchesIdUploadList);
            afterLoanPatchesEntityService.update(luw);
        }

        log.info("PostLoanSupplementReviewServiceImpl.uploadFundFileTask end");

    }

    @Override
    public GetOrderIdVO getOrderId(String vin) {
        return orderInfoMapper.selectJoinOne(
                GetOrderIdVO.class,
                new MPJLambdaWrapper<>(OrderInfoEntity.class)
                        .selectAs(OrderInfoEntity::getId, GetOrderIdVO::getOrderId)
                        .selectAs(OrderInfoEntity::getSourceType, GetOrderIdVO::getSourceType)
                        .selectAs(OrderInfoEntity::getStoreName, GetOrderIdVO::getStoreName)
                        .selectAs(OrderInfoEntity::getRegionId, GetOrderIdVO::getRegionId)
                        .leftJoin(OrderVehicleInfoEntity.class, OrderVehicleInfoEntity::getOrderId, OrderInfoEntity::getId)
                        .eq(OrderVehicleInfoEntity::getVin, vin)
                        .eq(OrderInfoEntity::getDeleteFlag, 0)
                        .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
//                        .eq(OrderInfoEntity::getCurrentNode, States.STORE_EVALUATION.getNode())
                        .eq(OrderInfoEntity::getSourceType, 1)
                        .orderByDesc(OrderInfoEntity::getCreateTime)
                        .last("limit 1")
        );
    }

    /**
     * 检查抵押文件
     *
     * @param paymentType 付款类型
     * @param orderId     订单 ID
     * @param fundEnum    基金枚举
     */
    private void checkMortgageFile(Integer paymentType, Integer orderId, FundEnum fundEnum) {
        //  1:先抵押后放款,2:抵押回执放款
        // 先抵押后放款 必传抵押登记联
        if (Objects.equals(paymentType, MortgageEnums.PRE_PLEDGE_POST_DISBURSEMENT.getCode())) {
            // 68,抵押登记联,DYDJL
            Long fileCount = orderFileMapper.selectCount(new LambdaQueryWrapper<OrderFileEntity>()
                    .eq(OrderFileEntity::getOrderId, orderId)
                    .eq(OrderFileEntity::getFileId, 68)
                    .eq(OrderFileEntity::getDeleteFlag, 0)
            );
            Assert.isTrue(fileCount > 0, () -> {
                throw new BusinessException("抵押登记联不能为空");
            });
        }
        // 凭抵押回执放款
        if (Objects.equals(paymentType, MortgageEnums.PLEDGE_RECEIPT_DISBURSEMENT.getCode())) {

            // 默认必传抵押回执
            boolean isNeedToCheckMortgage = true;

            // 富民抵押为线上 不用传抵押回执
            if (FundEnum.FU_MIN.equals(fundEnum)) {
                isNeedToCheckMortgage = false;
            }
            if (FundEnum.ZHONG_HENG_TONG_HUI.equals(fundEnum) || FundEnum.ZHONG_HENG.equals(fundEnum)
                        || FundEnum.CHANG_YIN.equals(fundEnum) || FundEnum.LAN_HAI.equals(fundEnum)
            ) {
                CustomerMortgageInfoEntity mortgageInfo = customerMortgageInfoMapper.selectOne(
                        new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                                .select(CustomerMortgageInfoEntity::getMortgageType)
                                .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                                .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                        ,false
                );
                Integer mortgageType = mortgageInfo.getMortgageType();
                if (ObjUtil.equals(mortgageType, 0)) {
                    isNeedToCheckMortgage = false;
                }
            }


            if (isNeedToCheckMortgage) {
                // 53,抵押回执,DYHZ
                Long fileCount = orderFileMapper.selectCount(new LambdaQueryWrapper<OrderFileEntity>()
                        .eq(OrderFileEntity::getOrderId, orderId)
                        .eq(OrderFileEntity::getFileId, 53)
                        .eq(OrderFileEntity::getDeleteFlag, 0)
                );

                Assert.isTrue(fileCount > 0, () -> {
                    throw new BusinessException("抵押回执不能为空");
                });

            }

        }
    }

    private void checkMortgageStatus(Integer currentMortgageState, Integer mortgageState, Integer currentNode, Integer paymentType) {
        if (Objects.equals(paymentType, MortgageEnums.PRE_PLEDGE_POST_DISBURSEMENT.getCode())) {
            if (mortgageState != 3) {
                throw new BusinessException("先抵押后付款方式提交的抵押状态不正确");
            }
        }
        if (Objects.equals(paymentType, MortgageEnums.PLEDGE_RECEIPT_DISBURSEMENT.getCode()) && mortgageState == 2) {
            if (!States.CUSTOMER_APPOINTMENT.getNode().equals(currentNode)) {
                throw new BusinessException("抵押登记联不能为空");
            }
        }

    }

    /**
     * 上传资方贷后材料
     */
    private boolean uploadFundFile(Integer orderId) {
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isNull(orderInfo)) {
            return false;
        }

        FundEnum fundEnum = FundEnum.getFundEnum(orderInfo.getFundId());
        boolean isUpload = false;
        switch (fundEnum) {
            case FU_MIN:
                //上传富民补件
                // 抵押登记联上传路径 upload/afterLoan/yyyyMMdd/申请编号_VEHICLE_REGIST.pdf
                FinalFundInfoEntity fundInfoEntity = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                                .eq(FinalFundInfoEntity::getOrderId, orderId)
                                .eq(FinalFundInfoEntity::getFundId, FundEnum.FU_MIN.getValue())
                                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        , false
                );
                FundResourceDTO.SftpDTO sftpDTO = new FundResourceDTO.SftpDTO();
                String fileName = fundInfoEntity.getCreditReqNo() + "_" + "VEHICLE_REGIST.pdf";
                String destPath = "upload/afterLoan/" + DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN) + "/" + fileName;
                sftpDTO.setDestPath(destPath);

                FundResourceDTO fundResourceDTO = new FundResourceDTO()
                        .setLinkId(orderId)
                        .setType(4)
                        .setFund(FundEnum.FU_MIN)
                        .setSftpDTOList(List.of(sftpDTO));
                Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(fundResourceDTO);
                if (Result.isSuccess(listResult)) {
                    isUpload = true;
                }
                break;
            case YING_FENG:
                //判断是否预加押
                List<String> preFileTypeCodeList = new ArrayList<>();
                preFileTypeCodeList.add(PreFileTypeEnums.PLEDGE_CERTIFICAT.getCode());
                UploadDesignateTypeDTO designateTypeDTO = new UploadDesignateTypeDTO();
                designateTypeDTO.setFundId(FundEnum.YING_FENG.getValue());
                designateTypeDTO.setPreId(orderInfo.getPreId());
                designateTypeDTO.setType(2);
                designateTypeDTO.setPreFileTypeCodeList(preFileTypeCodeList);
                Result<Boolean> result = approveFeign.uploadDesignateType(designateTypeDTO);
                if (Result.isSuccess(result) && result.getData()) {
                    isUpload = true;
                }
                break;
            case ZHONG_HENG_TONG_HUI:
            case ZHONG_HENG:
                Result<Boolean> hengtongResult = approveFeign.hengTongJiaYaUpdate(orderId);
                if (Result.isSuccess(hengtongResult) && hengtongResult.getData()) {
                    isUpload = true;
                }
                break;
            case LAN_HAI:
                FinalFundInfoEntity fundInfoEntity1 = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                                .eq(FinalFundInfoEntity::getOrderId, orderId)
                                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        , false
                );
//                if (StringUtils.isNotBlank(fundInfoEntity1.getLoanAfterReplenishNo()) && !Objects.equals(fundInfoEntity1.getLoanPurpose(),"1")){
//                    Result<LanHaiAfterReplenishVerifyResponse> responseResult = approveFeign.lanHaiPayAfterReplenishQuery(orderId);
//                    if (Result.isSuccess(responseResult) && Objects.equals(responseResult.getData().getHandlerStatus(),"001")){
//                        isUpload = true;
//                    }else {
//                        approveFeign.lanHaiPayAfterReplenish(orderId);
//                    }
//                }else if (Objects.equals(fundInfoEntity1.getLoanPurpose(),"1")){
//                    isUpload = true;
//                }else {
//                    approveFeign.lanHaiPayAfterReplenish(orderId);
//                }
                if (Objects.equals(fundInfoEntity1.getLoanPurpose(),"1")){
                    isUpload = true;
                }else {
                    approveFeign.lanHaiPayAfterReplenish(orderId);
                }
                break;
        }
        return isUpload;
    }
    @Override
    public GetManagerIdVO getManagerId(String vin) {
        GetManagerIdVO getManagerIdVO = orderInfoMapper.selectJoinOne(
                GetManagerIdVO.class,
                new MPJLambdaWrapper<>(OrderInfoEntity.class)
                        .selectAs(OrderInfoEntity::getId, GetManagerIdVO::getOrderId)
                        .selectAs(OrderInfoEntity::getManagerId, GetManagerIdVO::getManagerId)
                        .selectAs(OrderInfoEntity::getCustomerName, GetManagerIdVO::getCustomerName)
                        .selectAs(OrderInfoEntity::getVehicleNumber, GetManagerIdVO::getVehicleNumber)
                        .leftJoin(OrderVehicleInfoEntity.class, OrderVehicleInfoEntity::getOrderId, OrderInfoEntity::getId)
                        .eq(OrderVehicleInfoEntity::getVin, vin)
                        .eq(OrderInfoEntity::getDeleteFlag, 0)
                        .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderInfoEntity::getCreateTime)
                        .last("limit 1")
        );
        if (ObjUtil.isNotNull(getManagerIdVO) && ObjUtil.isNotEmpty(getManagerIdVO.getManagerId())){
            Result<UserInfoVO> userInfoVOResult = userFeign.searchUserName(getManagerIdVO.getManagerId());
            if (Result.isSuccess(userInfoVOResult) && ObjUtil.isNotEmpty(userInfoVOResult.getData())) {
                getManagerIdVO.setMobile(userInfoVOResult.getData().getMobile());
            }
        }
        return getManagerIdVO;
    }

    @Override
    public Page<PagePatchesVO> loanUploadList(PagePatchesDTO dto, LoginUser currentUser) {
        log.info("权限信息 currentUser:{}",currentUser);
        MPJLambdaWrapper<OrderInfoEntity> wrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getOrderNumber, OrderInfoEntity::getCustomerName,
                        OrderInfoEntity::getCustomerPhone, OrderInfoEntity::getCurrentNode,
                        OrderInfoEntity::getApplyAmount,
                        OrderInfoEntity::getTerm,
                        OrderInfoEntity::getProductName, OrderInfoEntity::getSource, OrderInfoEntity::getManagerId,
                        OrderInfoEntity::getPaymentTime,
                        OrderInfoEntity::getRegionName,
                        OrderInfoEntity::getTeamId,
                        OrderInfoEntity::getTeamName,
                        OrderInfoEntity::getStoreName
                        )
                .selectAs(OrderInfoEntity::getId, PagePatchesVO::getOrderId)
                .selectAs(OrderInfoEntity::getPreApplyTime, PagePatchesVO::getApplyDate)
                .selectAs(OrderInfoEntity::getVehicleNumber, PagePatchesVO::getVehicleNumber)
                .selectAs(OrderInfoEntity::getRiskUserId, PagePatchesVO::getRisiUserId)
                .selectAs(OrderInfoEntity::getGpsState, PagePatchesVO::getGpsState)
                .selectAs(FundInfoEntity::getName, PagePatchesVO::getFundName)
                .selectAs(FinalFundInfoEntity::getFundCreditTime, PagePatchesVO::getAppropriationTime)
                .selectAs(FinalFundInfoEntity::getLoanPurpose, PagePatchesVO::getLoanPurpose)
                .leftJoin(FundInfoEntity.class, FundInfoEntity::getId, OrderInfoEntity::getFundId)
                .leftJoin(FinalFundInfoEntity.class, FinalFundInfoEntity::getOrderId, OrderInfoEntity::getId)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
//                .eq(OrderInfoEntity::getFundId,FundEnum.CHANG_YIN.getValue())
                .ge(OrderInfoEntity::getCurrentNode, 5000)
                .eq(OrderInfoEntity::getPaymentState, 2)
                // 抵押待办状态筛选
                .notIn(OrderInfoEntity::getCurrentNode,
                        Arrays.asList(States.SYSTEM_TERMINAL.getNode(), States.PROCESS_TERMINAL.getNode()))
                .like(StrUtil.isNotBlank(dto.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, dto.getVehicleNumber())
                .like(StrUtil.isNotBlank(dto.getCustomerName()), OrderInfoEntity::getCustomerName, dto.getCustomerName())
                .like(StrUtil.isNotBlank(dto.getCustomerPhone()), OrderInfoEntity::getCustomerPhone, dto.getCustomerPhone())
                .like(StrUtil.isNotBlank(dto.getOrderNumber()), OrderInfoEntity::getOrderNumber, dto.getOrderNumber())
                .eq(ObjUtil.isNotNull(dto.getProductId()), OrderInfoEntity::getProductId, dto.getProductId())
                .eq(AfterLoanPatchesEntity::getDeleteFlag, 0)
                //新增所属大区、所属门店、放款时间，是否上传筛选条件
                .like(StringUtils.isNotEmpty(dto.getRegionName()),OrderInfoEntity::getRegionName, dto.getRegionName())
                .like(StringUtils.isNotEmpty(dto.getStoreName()),OrderInfoEntity::getStoreName, dto.getStoreName())
                .eq(StringUtils.isNotEmpty(dto.getLoanPurpose()),FinalFundInfoEntity::getLoanPurpose,dto.getLoanPurpose())
                .between(dto.getPaymentTimeStart()!=null&&dto.getPaymentTimeEnd()!=null,OrderInfoEntity::getPaymentTime, dto.getPaymentTimeStart(), dto.getPaymentTimeEnd())
                .orderByDesc(OrderInfoEntity::getPaymentTime);
        dataPermissionService.limitOrder(currentUser, wrapper);
        Page<PagePatchesVO> pageList = orderInfoMapper.selectJoinPage(
                new Page<>(dto.getPageNum(), dto.getPageSize()),
                PagePatchesVO.class,
                wrapper
        );
        List<PagePatchesVO> records = pageList.getRecords();
//        //total条数有问题，需要重新计算
//        pageList.setTotal(records.size());
        records.forEach(item -> {
            if (item.getCurrentNode() <= States.FUNDS_FINAL_APPROVE.getNode()) {
                item.setAppropriationTime(null);
            }
        });
        // 查询经办人信息
        List<Integer> teamIds = records.stream().map(PagePatchesVO::getTeamId).distinct().toList();
        Map<Integer, String> branchNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(teamIds)) {
            try {
                branchNameMap = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData().stream().filter(deptInfoVO -> deptInfoVO.getName() != null).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
            } catch (Exception e) {
                log.error("com.longhuan.order.service.impl.PostLoanSupplementReviewServiceImpl.loanUploadList error for teamIds:{} e:{}", teamIds, e.getMessage());
            }
        }
        Map<Integer, String> finalBranchNameMap = branchNameMap;
        List<Integer> manageIds = records.stream().map(PagePatchesVO::getManagerId).filter(Objects::nonNull).toList();
        Result<List<UserStoreVO>> listResult = userFeign.searchUserStoreBatch(manageIds);
        if (Result.isSuccess(listResult)) {
            Map<Integer, UserStoreVO> managerInfos =
                    listResult.getData().stream().collect(Collectors.toMap(UserStoreVO::getUserId, item -> item));
            records.stream().filter(item -> item.getManagerId() != null).forEach(record -> {
                        UserStoreVO userInfoVOS = managerInfos.get(record.getManagerId());
                        record.setManagerName(userInfoVOS.getName());
                        record.setStoreName(userInfoVOS.getStore());
                        record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(finalBranchNameMap.get(record.getTeamId()), ""));
                    }
            );
        }
        return pageList;
    }


    @Override
    public Boolean loanUploadInfo(Integer orderId) {
        ChangYinResBodyDTO<ChangYinLoanUploadResDTO> result = null;
        try {
            result = approveFeign.changYinLoanPurposeUpload(orderId);
        } catch (Exception e) {
            throw new BusinessException(e);
        }
        if (!ChangYinResBodyDTO.isSuccess(result)){
            throw new BusinessException("调用长银接口失败"+result.getHead().getRespMsg());
        }
        if (result.getBody().getStatus().equals("1")){
            //上传成功 更新 final_fund_info 表的 loanPurpose 状态
            finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .set(FinalFundInfoEntity::getLoanPurpose, "1")
                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                    .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0));
            return true;
        }else{
            throw new BusinessException("长银推送失败"+result.getBody().getStatusDesc());
        }
    }

    @Override
    public Boolean idCardUpdateApply(Integer orderId) {
        OrderIdCardUpdateEntity orderIdCardUpdateEntity =
                orderIdCardUpdateMapper.selectOne(new LambdaQueryWrapper<OrderIdCardUpdateEntity>()
                        .eq(OrderIdCardUpdateEntity::getOrderId, orderId)
                        .eq(OrderIdCardUpdateEntity::getDeleteFlag, 0)
                );
        if (ObjUtil.isNull(orderIdCardUpdateEntity)){
            throw new BusinessException("未找到身份证信息");
        }
        ChangYinResBodyDTO<ChangYinIdCardUpdateApplyResDTO> result = approveFeign.changYinIdCardUpdateApply(orderId);
        if (ChangYinResBodyDTO.isSuccess(result)){

            ChangYinIdCardUpdateApplyResDTO body = result.getBody();
            orderIdCardUpdateEntity.setStatus(body.getStatus());
            orderIdCardUpdateMapper.updateById(orderIdCardUpdateEntity);
            if ("1".equals(body.getStatus())){
                throw new BusinessException("身份证更新失败"+body.getStatusDesc());
            }
        }else {
            throw new BusinessException("身份证更新失败"+result.getHead().getRespMsg());
        }
        return true;
    }

    @Override
    public Boolean saveUpdateIdCardInfo(OrderIdCardUpdateEntity entity) {
        Integer orderId = entity.getOrderId();
        if (ObjUtil.isNull(orderId)){
            throw new BusinessException("订单id为空");
        }
        OrderIdCardUpdateEntity orderIdCardUpdateEntity =
                orderIdCardUpdateMapper.selectOne(new LambdaQueryWrapper<OrderIdCardUpdateEntity>()
                        .eq(OrderIdCardUpdateEntity::getOrderId, orderId)
                        .eq(OrderIdCardUpdateEntity::getDeleteFlag, 0)
                );
        if (ObjUtil.isNull(orderIdCardUpdateEntity)){
            orderIdCardUpdateMapper.insert(entity);
        }else {
            orderIdCardUpdateEntity.setCustName(entity.getCustName());
            orderIdCardUpdateEntity.setIdNoStartDate(entity.getIdNoStartDate());
            orderIdCardUpdateEntity.setIdNoEndDate(entity.getIdNoEndDate());
            orderIdCardUpdateEntity.setIdNo(entity.getIdNo());
            orderIdCardUpdateMapper.updateById(orderIdCardUpdateEntity);
        }
        return true;
    }

    @Override
    public String idCardUpdateQuery(Integer orderId) {
        OrderIdCardUpdateEntity orderIdCardUpdateEntity =
                orderIdCardUpdateMapper.selectOne(new LambdaQueryWrapper<OrderIdCardUpdateEntity>()
                        .eq(OrderIdCardUpdateEntity::getOrderId, orderId)
                        .eq(OrderIdCardUpdateEntity::getDeleteFlag, 0)
                );
        if (ObjUtil.isNull(orderIdCardUpdateEntity)){
            throw new BusinessException("未找到身份证信息");
        }
        ChangYinResBodyDTO<ChangYinIdCardUpdateQueryResDTO> result = null;
        try {
            result = approveFeign.changYinIdCardUpdateQuery(orderId);
            if (!ChangYinResBodyDTO.isSuccess(result)){
                throw new BusinessException("查询失败"+result.getHead().getRespMsg());
            }
            if (ChangYinIdCardTypeStatusEnum.ACCOUNT_SUCCESS.getCode().equals(result.getBody().getStatus())){
                orderIdCardUpdateEntity.setStatus("0");
                orderIdCardUpdateMapper.updateById(orderIdCardUpdateEntity);
                return ChangYinIdCardTypeStatusEnum.ACCOUNT_SUCCESS.getDescription();
            }
            if (ChangYinIdCardTypeStatusEnum.ACCOUNT_PASS.getCode().equals(result.getBody().getStatus())){
                orderIdCardUpdateEntity.setStatus("2");
                orderIdCardUpdateMapper.updateById(orderIdCardUpdateEntity);
                return ChangYinIdCardTypeStatusEnum.ACCOUNT_PASS.getDescription();
            }
            if (ChangYinIdCardTypeStatusEnum.ACCOUNT_FAIL.getCode().equals(result.getBody().getStatus())){
                orderIdCardUpdateEntity.setStatus("1");
                orderIdCardUpdateMapper.updateById(orderIdCardUpdateEntity);
                throw new BusinessException(  ChangYinIdCardTypeStatusEnum.ACCOUNT_FAIL.getDescription() + result.getBody().getStatusDesc());
            }
        } catch (Exception e) {
            throw new BusinessException("查询失败");
        }
        return null;
    }

    @Override
    public OrderIdCardUpdateEntity idCardQueryByOrderId(Integer orderId) {
        OrderIdCardUpdateEntity orderIdCardUpdateEntity =
                orderIdCardUpdateMapper.selectOne(new LambdaQueryWrapper<OrderIdCardUpdateEntity>()
                        .eq(OrderIdCardUpdateEntity::getOrderId, orderId)
                        .eq(OrderIdCardUpdateEntity::getDeleteFlag, 0)
                );
        return orderIdCardUpdateEntity;
    }

    @Override
    public void exportExcel(PagePatchesDTO pagePatchesDTO, LoginUser loginUser, HttpServletResponse response) {
        pagePatchesDTO.setPageSize(Integer.MAX_VALUE);
        Page<PagePatchesVO> pagePatchesVOPage = pagePatches(pagePatchesDTO, loginUser);
        List<PagePatchesVO> records = pagePatchesVOPage.getRecords();
        List<PatchesExcelVO> patchesExcelVOS = new ArrayList<>();
        records.stream().forEach(pagePatchesVO -> {
            PatchesExcelVO patchesExcelVO = BeanUtil.copyProperties(pagePatchesVO, PatchesExcelVO.class);
            patchesExcelVO.setPatchesStatus(startCheck(pagePatchesVO.getPatchesStatus())); //贷后状态
            patchesExcelVO.setCurrentNode(startCheck(pagePatchesVO.getCurrentNode()));  //订单状态
            patchesExcelVOS.add(patchesExcelVO);
        });
        if (CollUtil.isEmpty(patchesExcelVOS)) {
            throw new BusinessException("该用户未查询到贷后补件列表信息");
        }
        response.addHeader("charset", "utf-8");
        String fileName = String.format("贷后补件%s.xlsx", LocalDate.now());
        String encodeName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        Optional<MediaType> mediaTypeOptional = MediaTypeFactory.getMediaType(encodeName);
        MediaType mediaType = mediaTypeOptional.orElse(MediaType.APPLICATION_OCTET_STREAM);
        log.info("com.longhuan.order.service.impl.PostLoanSupplementReviewServiceImpl.moneyAfterPatchesExport mediaType = {}", JSONUtil.toJsonStr(mediaType));
        response.setContentType(String.valueOf(mediaType));
        log.info("com.longhuan.order.service.impl.PostLoanSupplementReviewServiceImpl.moneyAfterPatchesExport response contentType = {}", response.getContentType());
        response.setHeader("Content-disposition", "attachment;filename=" + encodeName);
        try {
            EasyExcel.write(response.getOutputStream())
                    // 动态头
                    .head(head())
                    .registerWriteHandler(EasyExcelUtil.getHorizontalCellStyleStrategy())
                    .registerWriteHandler(new EasyExcelUtil.CustomCellWriteWidthConfig())
                    .sheet("贷后信息")
                    // 表格数据
                    .doWrite(patchesExcelVOS);
        } catch (IOException e) {
            log.error("com.longhuan.order.service.impl.PostLoanSupplementReviewServiceImpl.moneyAfterPatchesExport error", e);
        }
    }

    @Override
    public String loanProveDownload(MenuFileDTO menuFileDTO) {
        List<Tree<Integer>> fileMenuList = orderFileMenuService.menuFile(menuFileDTO);
        String jsonStr = JSONUtil.toJsonStr(fileMenuList);
        ObjectMapper objectMapper = new ObjectMapper();
        String resourceId;
        try {
            List<Map<String, Object>> maps = objectMapper.readValue(jsonStr, new TypeReference<List<Map<String, Object>>>() {
            });
            List<Map<String, Object>> data = (List<Map<String, Object>>)maps.get(0).get("fileList");
            List<Map<String, Object>> orderFileInfo = (List<Map<String, Object>>)data.get(0).get("orderFileInfo");
            resourceId = String.valueOf(orderFileInfo.get(0).get("resourceId"));
            log.info("com.longhuan.order.service.impl.PostLoanSupplementReviewServiceImpl.loanProveDownload resourceId是：{}",resourceId);
        } catch (JsonProcessingException e) {
            log.info("com.longhuan.order.service.impl.PostLoanSupplementReviewServiceImpl.loanProveDownload errer:{}", e);
            throw new BusinessException("获取文件resourceId异常 err: " + e);
        }
        Result<String> ImgUrl = resourceFeign.temporaryAccessRouteRequest(resourceId, 60);
        log.info("com.longhuan.order.service.impl.PostLoanSupplementReviewServiceImpl.loanProveDownload ImgUrl是：{}",ImgUrl);
        return ImgUrl.getData();
    }

    @Override
    public void exports(PagePatchesDTO pagePatchesDTO, LoginUser loginUser, HttpServletResponse response) {
        pagePatchesDTO.setPageNum(1);
        pagePatchesDTO.setPageSize(Integer.MAX_VALUE);
        Page<PagePatchesVO> pagePatchesVOPage = loanUploadList(pagePatchesDTO, loginUser);
        List<PagePatchesVO> records = pagePatchesVOPage.getRecords();
        List<PatchesChangYinExcelVO> patchesExcelVOS = new ArrayList<>();
        records.stream().forEach(pagePatchesVO -> {
            PatchesChangYinExcelVO patchesChangYinExcelVO = BeanUtil.copyProperties(pagePatchesVO, PatchesChangYinExcelVO.class);
            if (pagePatchesVO!=null&&StrUtil.isNotBlank(pagePatchesVO.getLoanPurpose())){
                switch (pagePatchesVO.getLoanPurpose()){
                    case "0" -> patchesChangYinExcelVO.setLoanPurpose("未上传");
                    case "1" -> patchesChangYinExcelVO.setLoanPurpose("已上传");
                    default -> patchesChangYinExcelVO.setLoanPurpose("");
                }
                patchesExcelVOS.add(patchesChangYinExcelVO);
            }
        });



        response.addHeader("charset", "utf-8");
        String fileName = String.format("长银补件%s.xlsx", LocalDate.now());
        String encodeName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        Optional<MediaType> mediaTypeOptional = MediaTypeFactory.getMediaType(encodeName);
        MediaType mediaType = mediaTypeOptional.orElse(MediaType.APPLICATION_OCTET_STREAM);
        log.info("com.longhuan.order.service.impl.PostLoanSupplementReviewServiceImpl.moneyAfterPatchesExport mediaType = {}", JSONUtil.toJsonStr(mediaType));
        response.setContentType(String.valueOf(mediaType));
        log.info("com.longhuan.order.service.impl.PostLoanSupplementReviewServiceImpl.moneyAfterPatchesExport response contentType = {}", response.getContentType());
        response.setHeader("Content-disposition", "attachment;filename=" + encodeName);
        try {
            EasyExcel.write(response.getOutputStream())
                    // 动态头
                    .head(changyinHead())
                    .registerWriteHandler(EasyExcelUtil.getHorizontalCellStyleStrategy())
                    .registerWriteHandler(new EasyExcelUtil.CustomCellWriteWidthConfig())
                    .sheet("长银补件")
                    // 表格数据
                    .doWrite(patchesExcelVOS);
        } catch (IOException e) {
            log.error("com.longhuan.order.service.impl.PostLoanSupplementReviewServiceImpl.moneyAfterPatchesExport error", e);
        }

    }

    @Override
    public void sendPatchesMessage() {
        ObjectMapper objectMapper = new ObjectMapper();
        SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.FUND_PATCHES_REMIND_DAYS);
        Map<String, Map<String,List<Integer>>> map = new HashMap<>();
        if (Objects.equals(switchInfo.getSwitchFlag(),1) && StringUtils.isNotBlank(switchInfo.getValue())){
            try {
                map = objectMapper.readValue(switchInfo.getValue(), new TypeReference<Map<String, Map<String,List<Integer>>>>() {});
                log.info("PostLoanSupplementReviewServiceImpl.sendPatchesMessage map:{}",map);
            } catch (JsonProcessingException e) {
                log.info("PostLoanSupplementReviewServiceImpl.sendPatchesMessage error:{}",e.getMessage());
            }
        }
        List<AfterLoanPatchesEntity> list = afterLoanPatchesEntityService.list(
                new LambdaQueryWrapper<AfterLoanPatchesEntity>()
                        .eq(AfterLoanPatchesEntity::getDeleteFlag, 0)
                        .in(AfterLoanPatchesEntity::getAfterLoanStatus, Arrays.asList(
                                AfterLoanPatchesEnum.AFTER_LOAN_PATCHES,
                                AfterLoanPatchesEnum.CONTRACT_OVERRULE,
                                AfterLoanPatchesEnum.FUNDS_REJECT
                        ))
        );
        if (CollUtil.isNotEmpty( list)){
            List<Integer> orderIdList = list.stream().map(AfterLoanPatchesEntity::getOrderId).toList();
            log.info("PostLoanSupplementReviewServiceImpl.sendPatchesMessage orderIdList:{}",orderIdList);
            List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>().in(OrderInfoEntity::getId, orderIdList));
            Map<Integer, OrderInfoEntity> orderMap = orderInfoEntityList.stream().collect(Collectors.toMap(OrderInfoEntity::getId, v -> v));
            //获取抵押方式
            List<CustomerMortgageInfoEntity> customerMortgageInfoEntityList = customerMortgageInfoMapper.selectList(new LambdaQueryWrapper<CustomerMortgageInfoEntity>().in(CustomerMortgageInfoEntity::getId, orderIdList).eq(CustomerMortgageInfoEntity::getDeleteFlag, 0));
            Map<Integer, Integer> latestMortgageTypeMap = customerMortgageInfoEntityList.stream()
                    .collect(Collectors.groupingBy(CustomerMortgageInfoEntity::getOrderId))
                    .entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().stream()
                                    .max(Comparator.comparing(CustomerMortgageInfoEntity::getCreateTime))
                                    .get()
                                    .getMortgageType()
                    ));
            //获取签约客服
            Map<Integer, Integer> returnReasonMap = orderNodeRecordMapper.selectList(
                            new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                    .in(OrderNodeRecordEntity::getOrderId, orderIdList)
                                    .eq(OrderNodeRecordEntity::getCurrentNode, States.PAYMENT_APPLY_INFORMATION.getNode())
                                    .orderByDesc(OrderNodeRecordEntity::getCreateTime)).stream()
                    .collect(Collectors.groupingBy(OrderNodeRecordEntity::getOrderId)).entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().stream()
                                    .max(Comparator.comparing(OrderNodeRecordEntity::getCreateTime))
                                    .get()
                                    .getUpdateBy()
                    ));
            //签约客服id
            List<Integer> distinctValues = returnReasonMap.values().stream()
                    .distinct()
                    .toList();
            log.info("PostLoanSupplementReviewServiceImpl.sendPatchesMessage returnReasonMap:{}  distinctValues:{}",returnReasonMap,distinctValues);
            //获取业务员
            List<Integer> managerIdList = orderInfoEntityList.stream().map(OrderInfoEntity::getManagerId).toList();
            //获取手机号
            List<Integer> userIdList = new ArrayList<>();
            userIdList.addAll(managerIdList);
            userIdList.addAll(distinctValues);
            List<UserInfoVO> data = userFeign.searchUserNameBatch(userIdList).getData();
            Map<Integer, String> signCustomerNameMap = data.stream().collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getMobile, (v1, v2) -> v1));
            Map<String, Map<String, List<Integer>>> finalMap = map;
            list.forEach(entity ->{
                OrderInfoEntity orderInfoEntity = orderMap.get(entity.getOrderId());
                Integer i = latestMortgageTypeMap.get(entity.getOrderId());
                if (finalMap.containsKey(String.valueOf(orderInfoEntity.getFundId()))){
                    Map<String, List<Integer>> fundMap = finalMap.get(String.valueOf(orderInfoEntity.getFundId()));
                    List<Integer> days = fundMap.get(Objects.equals(i, 0) ? "1" : "2");
                    LocalDate startDate = orderInfoEntity.getPaymentTime().toLocalDate();
                    LocalDate currentDate = LocalDate.now();
                    long daysBetween = startDate.until(currentDate, ChronoUnit.DAYS);
                    if (days.contains(Math.toIntExact(daysBetween))){
                        log.info("PostLoanSupplementReviewServiceImpl.sendPatchesMessage orderId:{} daysBetween:{}",orderInfoEntity.getId(),daysBetween);
                        MessageContent messageContent = new MessageContent()
                                .setMsgType(MsgConstants.MSG_TEXT)
                                .setSendType(MsgConstants.SEND_DD_NOTICE)
                                .setContent("客户:"+orderInfoEntity.getCustomerName()+",车牌号:"+orderInfoEntity.getVehicleNumber()+"放款后第"+daysBetween+"天,还未完成贷后补件,请尽快完成补件！")
                                .setReceiver(signCustomerNameMap.get(returnReasonMap.get(entity.getOrderId())));
                        messageFeign.sendMessage(messageContent);
                        MessageContent messageContent1 = new MessageContent()
                                .setMsgType(MsgConstants.MSG_TEXT)
                                .setSendType(MsgConstants.SEND_DD_NOTICE)
                                .setContent("客户:"+orderInfoEntity.getCustomerName()+",车牌号:"+orderInfoEntity.getVehicleNumber()+"放款后第"+daysBetween+"天,还未完成贷后补件,请尽快完成补件！")
                                .setReceiver(signCustomerNameMap.get(orderInfoEntity.getManagerId()));
                        messageFeign.sendMessage(messageContent1);
                    }
                }
            });
        }
    }

    //  贷后补件
    public List<List<String>> head() {
        List<List<String>> list = new ArrayList<>();
        list.add(Arrays.asList("客户姓名"));
        list.add(Arrays.asList("车牌号"));
        list.add(Arrays.asList("资方"));
        list.add(Arrays.asList("放款时间"));
        list.add(Arrays.asList("所属门店"));
        list.add(Arrays.asList("所属大区"));
        list.add(Arrays.asList("贷后状态"));
        list.add(Arrays.asList("贷款金额"));
        list.add(Arrays.asList("产品名称"));
        list.add(Arrays.asList("订单状态"));
        list.add(Arrays.asList("订单编号"));
        list.add(Arrays.asList("业务员"));
        list.add(Arrays.asList("订单来源"));
        return list;
    }
    //长银补件
    public List<List<String>> changyinHead() {
        List<List<String>> list = new ArrayList<>();
        list.add(Arrays.asList("客户姓名"));
        list.add(Arrays.asList("订单编号"));
        list.add(Arrays.asList("门店"));
        list.add(Arrays.asList("大区"));
        list.add(Arrays.asList("业务员"));
        list.add(Arrays.asList("车牌号"));
        list.add(Arrays.asList("资方"));
        list.add(Arrays.asList("产品名称"));
        list.add(Arrays.asList("期数"));
        list.add(Arrays.asList("是否上传"));
        list.add(Arrays.asList("放款时间"));
        list.add(Arrays.asList("申请时间"));
        return list;
    }

    public String startCheck(Integer code) {
        String start;
        switch (code) {//贷后状态
            case 15000:
                start = "待审批";
                break;
            case 10000:
                start = "待提交";
                break;
            case 17500:
                start = "审核驳回";
                break;
            case 8000:
                start = "贷款结清";
                break;
            case 5000:
                start = "放款成功";
                break;
            default:
                start = "未知状态";
        }
        return start;
    }
}
