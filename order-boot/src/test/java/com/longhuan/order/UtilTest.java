package com.longhuan.order;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.longhuan.approve.api.pojo.vo.YingFengInfoVO;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.vo.SubmitResultVO;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.order.util.crypt.SM4Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.jcajce.provider.asymmetric.rsa.RSAUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
class UtilTest {


    @Test
    public void setTraceId() {

        Assertions.assertTrue(IdcardUtil.isValidCard("310106198706063300"));
    }
    @Test
    public void testDate() {
        LocalDateTime localDateTime = LocalDate.now().minusDays(9).atStartOfDay();
        log.info("localDateTime {}", localDateTime);
    }
    @Test
    public void testSM4Util() throws Exception {

        System.out.println(SM4Util.generateKey());

    }

    @Test
    public void testRandom() {
        String json = "[[10768,1037,3092,3434],[10768,1037,3092,3434,11471],[3092,3434,11471],[1037,3092,3434,11471],[10768,1037,3434,11471],[10768,1037,10571,3434,11471],[1037,3092,10571],[10768,1037,10571],[10768,1037,10571,3434],[10768,1037,10571,3434,11471],[10768,1037,10571,3434,11471],[10768,1037,3092,10571,3434,11471],[10768,1037,3092,11471,10571,3434],[10768,1037,11471,10571,3434],[10768,1037,3092,11471,10571,3434]]";
        TypeReference<List<List<Integer>>> typeReference = new TypeReference<List<List<Integer>>>() {
        };
        List<List<Integer>> list = JSONUtil.toBean(json, typeReference, true);

        Random random = new Random();

        List<Integer> res = new ArrayList<>();
        list.forEach(item -> {
            Collections.shuffle(item);
            int randomIndex = random.nextInt(item.size());
            Integer o = item.get(randomIndex);
            log.info("item {} randomIndex {} o {}", item, randomIndex, o);
            res.add(o);
        });

        Map<Integer, Long> countgroup = res.stream().collect(Collectors.groupingBy(Integer::intValue, Collectors.counting()));
        countgroup.forEach((k, v) -> log.info("k {} v {}", k, v));

    }

    @Test
    public void testLanben() {
        boolean result = false;
        int orderId = 24;
        String onlineEvaluate = "车况简述：车况一般";
        BigDecimal lanBenMileage = BigDecimal.valueOf(13.45);
        Integer evaluateStatus = Convert.toInt(3, 0);
        log.info("order {} lanben data evaluateStatus {}", orderId, evaluateStatus);
        // 没有评估结果
        //        if (evaluateStatus == 4) {
        //
        //            throw new BusinessException("蓝本价评估单被驳回！" + Convert.toStr(data.getFailDesc()));
        //        }

        // 没有评估结果
        if (evaluateStatus != 3) {
            result = true;
        }

        // 车况较差
        if (StringUtils.isNotBlank(onlineEvaluate) && onlineEvaluate.contains("较差")) {
            result = true;
        }

        // 蓝本价里程数如果大于输入里程数
        if (lanBenMileage == null) result = true;

        BigDecimal multiply = lanBenMileage.multiply(BigDecimal.valueOf(10000));

        boolean b = multiply.compareTo(BigDecimal.valueOf(130000.00)) > 0;
        log.info("multiply {} result {} b {}", multiply, result, b);
        System.out.println(multiply);
        Assertions.assertFalse(result || b);
    }


    @Test
    public void testOrderState() throws JsonProcessingException {
        String json = "{\"code\":\"00000\",\"msg\":\"一切ok\",\"data\":{\"id\":2577,\"orderId\":1110,\"creditReqNo\":\"1728804356433282786\",\"spOrderNum\":null,\"zhOrderNum\":null,\"preId\":3038,\"creditApplyNo\":\"CPA202410132602891089\",\"loanApplyNo\":\"202410152604168811\",\"loanReqNo\":\"1728981548847721164\",\"contractNo\":\"db257007248b4a5ca7cf8642905bfb003038\",\"zhPaymentState\":null,\"zhPaymentFailReason\":null,\"creditNo\":\"T202410132602891088\",\"creditAmt\":39000.00,\"creditStatus\":\"1\",\"creditFailReason\":null,\"creditContractEnd\":\"2024-11-13T00:00:00\",\"zhPaymentTime\":null,\"yfPaymentState\":\"1\",\"yfPaymentFailReason\":\"SUCCESS <p><span style=\\\"color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); font-size: 13px;\\\">最后解押在7天内，上次还款期数满18期符合</span></p> 放款金额->39000.0\",\"yfPaymentTime\":\"2024-10-16T14:55:25\"}}";

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
//        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.registerModule(new JavaTimeModule());
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(Result.class, YingFengInfoVO.class);
        Result<YingFengInfoVO>   result =      objectMapper.readValue(json, javaType);

        boolean empty = ObjUtil.isEmpty(result.getData());
        log.info(" empty {}", empty);
        if (!Result.isSuccess(result) || empty){
         log.info("return");
        }




    }
}
