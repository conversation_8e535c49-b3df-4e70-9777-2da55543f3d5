package com.longhuan.order.statemachine.action;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.longhuan.common.core.enums.RoleEnum;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.entity.DistributeAreaEntity;
import com.longhuan.order.pojo.entity.OrderApproveDistributeEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.entity.RiskAiIntelligentAuditEntity;
import com.longhuan.order.service.OrderSettleApplyInfoService;
import com.longhuan.order.service.impl.OrderSendMessageImpl;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.user.enums.TodoInfoEnums;
import com.longhuan.user.pojo.dto.TodoInfoMessageDTO;
import com.longhuan.user.pojo.dto.getUserIdByStoreIdAndRoleIdDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessageHeaders;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 状态转移动作，在转移进行的时候触发的动作
 */
@Slf4j
@Component
public class AlAuditAction extends OrderNodeAction implements Action<States, Events> {

    private final OrderSendMessageImpl orderSendMessage;
    @Autowired
    private  RiskAiIntelligentAuditMapper riskAiIntelligentAuditMapper;

    @Autowired
    private UserFeign userFeign;


    public AlAuditAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper, OrderSendMessageImpl orderSendMessage) {
        super(orderInfoMapper, orderNodeRecordMapper);
        this.orderSendMessage = orderSendMessage;
    }

    @Override
    public void execute(StateContext<States, Events> context) {


        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();

        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);


        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event, currentNode, nextNode);

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);

        // 1. 修改订单表状态
        if (Boolean.TRUE.equals(target.getId().getValid())) {
            saveOrderNode(orderId, nextNode);
        }

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);

        //3.增加暂存数据记录
        RiskAiIntelligentAuditEntity riskAiIntelligentAuditEntity = riskAiIntelligentAuditMapper.selectOne(new LambdaQueryWrapper<RiskAiIntelligentAuditEntity>()
                .eq(RiskAiIntelligentAuditEntity::getOrderId, orderId)
                .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
        );
        //如果有数据 置为失效 如果没有新增
        if (ObjUtil.isNotNull(riskAiIntelligentAuditEntity)){
            riskAiIntelligentAuditEntity.setDeleteFlag(1);
            riskAiIntelligentAuditMapper.updateById(riskAiIntelligentAuditEntity);
        }
        RiskAiIntelligentAuditEntity insertInfo = new RiskAiIntelligentAuditEntity();
        insertInfo.setOrderId(orderId)
                .setOrderNumber(orderInfoEntity.getOrderNumber())
                .setOrderSource(orderInfoEntity.getSourceType())
                .setAiReportStatus(0)
                .setAiReportRequestTime(LocalDateTime.now())
                .setIsAutoAudit(1)
        ;
        riskAiIntelligentAuditMapper.insert(insertInfo);


        // 4. 发送消息
        Integer currentEventCode = context.getEvent().getCode();
        if (Objects.equals(currentNode, States.QUALITY_INSPECTION_FINISH.getNode()) ){
            orderSendMessage.sendMessage(orderId, currentNode, currentEventCode);
        }

    }



}