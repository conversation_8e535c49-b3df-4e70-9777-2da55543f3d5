package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.AddPhoneInvestigateDTO;
import com.longhuan.order.pojo.entity.PhoneInvestigateEntity;
import com.longhuan.order.pojo.vo.PhoneInvestigateVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PhoneInvestigateConverter {

    PhoneInvestigateEntity dtoToEntity(AddPhoneInvestigateDTO addPhoneInvestigateDTO);

    List<PhoneInvestigateVO> entityListToDtoList(List<PhoneInvestigateEntity> phoneInvestigateEntities);
}
