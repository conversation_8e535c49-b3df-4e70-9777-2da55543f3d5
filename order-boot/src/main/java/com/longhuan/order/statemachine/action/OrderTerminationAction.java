package com.longhuan.order.statemachine.action;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.entity.DeptQuotaEntity;
import com.longhuan.order.pojo.entity.DeptQuotaRecordEntity;
import com.longhuan.order.pojo.entity.PreApprovalApplyInfoEntity;
import com.longhuan.order.service.impl.OrderSendMessageImpl;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 状态转移动作，在转移进行的时候触发的动作
 * 订单拒绝操作
 */
@Slf4j
@Component
public class OrderTerminationAction extends OrderNodeAction implements Action<States, Events> {
    private final OrderSendMessageImpl orderSendMessage;
    private final DeptQuotaRecordMapper deptQuotaRecordMapper;
    private final DeptQuotaMapper deptQuotaMapper;
    private final PreApprovalApplyInfoMapper preApprovalApplyInfoMapper;
    public OrderTerminationAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper, OrderSendMessageImpl orderSendMessage, DeptQuotaRecordMapper deptQuotaRecordMapper, DeptQuotaMapper deptQuotaMapper, PreApprovalApplyInfoMapper preApprovalApplyInfoMapper) {
        super(orderInfoMapper, orderNodeRecordMapper);
        this.orderSendMessage = orderSendMessage;
        this.deptQuotaRecordMapper = deptQuotaRecordMapper;
        this.deptQuotaMapper = deptQuotaMapper;
        this.preApprovalApplyInfoMapper = preApprovalApplyInfoMapper;
    }
    @Override
    public void execute(StateContext<States, Events> context) {


        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();

        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);


        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event, currentNode, nextNode);

        // 1. 修改订单表状态
        if (Boolean.TRUE.equals(target.getId().getValid())) {
            saveOrderNode(orderId, nextNode);
        }

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);

        // 3. 发送消息
        Integer currentEventCode = context.getEvent().getCode();
        if (Objects.equals(currentNode, States.RISK_FINAL_APPROVE.getNode())
                || Objects.equals(currentNode, States.PAYMENT_CONTRACT_APPROVAL.getNode())
                || (Objects.equals(currentNode, States.FUNDS_PAYMENT_APPROVAL.getNode()) && "1".equals(String.valueOf(currentEventCode)))
                || Objects.equals(currentNode, States.FUNDS_PAYMENT_FAIL.getNode())
                || Objects.equals(currentNode, States.PAYMENT_SUCCESS.getNode())) {
            orderSendMessage.sendMessage(orderId, currentNode, currentEventCode);
        }
        // 4. 释放额度
        LocalDate now = LocalDate.now();
        deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                .set(DeptQuotaEntity::getEnable, 1)
                .lt(DeptQuotaEntity::getEndDate, now)
                .eq(DeptQuotaEntity::getEnable, 0)
                .eq(DeptQuotaEntity::getDeleteFlag, 0));
        DeptQuotaRecordEntity deptQuotaRecordEntity=deptQuotaRecordMapper.selectOne(new LambdaQueryWrapper<DeptQuotaRecordEntity>()
                .eq(DeptQuotaRecordEntity::getOrderId, orderId)
                .eq(DeptQuotaRecordEntity::getAction, 0)
                .eq(DeptQuotaRecordEntity::getDeleteFlag, 0));
        if (ObjUtil.isNotNull(deptQuotaRecordEntity)){
            int update= deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                    .setIncrBy(DeptQuotaEntity::getRemainingQuota, deptQuotaRecordEntity.getQuota())
                    .eq(DeptQuotaEntity::getDeptId, deptQuotaRecordEntity.getDeptId())
                    .eq(DeptQuotaEntity::getId, deptQuotaRecordEntity.getDeptQuotaId())
                    .eq(DeptQuotaEntity::getDeleteFlag, 0));
            if (update==0){
                log.error("OrderTerminationAction execute quotaReleaseFailed DeptQuotaEntity=0，orderid:{}", orderId);
            }else {
                deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                        .set(DeptQuotaRecordEntity::getAction, 1)
                        .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
            }
        }
        preApprovalApplyInfoMapper.update(new LambdaUpdateWrapper<PreApprovalApplyInfoEntity>()
                .set(PreApprovalApplyInfoEntity::getProductId, null)
                .eq(PreApprovalApplyInfoEntity::getId, orderInfoMapper.selectById(orderId).getPreId()));
    }
}
