<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.longhuan.order.mapper.PreFddSignTaskMapper">
    <select id="getInvalidInfo" resultType="com.longhuan.order.pojo.vo.SignTaskVO">
        select DATE_PART('day', now()::timestamp - create_time::timestamp) as days,
               id, pre_id, sign_task_id, sign_task_status
        from lh_pre_fdd_sign_task
        where sign_task_id is not null
          and sign_task_status is null and delete_flag = 0
          and DATE_PART('day', now()::timestamp - create_time::timestamp) > 0
    </select>
</mapper>