package com.longhuan.approve.boot.pojo.vo;

import com.longhuan.auto.loan.common.enums.BankPayResultCodeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class BankPaySendVO {


    /**
     * 支付请求流水号
     */
    private String payReqNo;

    /**
     * 支付响应流水号
     */
    private String payResNo;

    /**
     * 支付结果码
     */
    private BankPayResultCodeEnum resultCode;

    /**
     * 支付结果描述
     */
    private String resultMsg;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 成功时间
     */
    private LocalDateTime payTime;

    /**
     * 请求参数
     */
    private String requestJson;

    /**
     * 响应参数
     */
    private String responseJson;

    /**
     * 银行账户ID
     */
    private Integer accountBankId;

}
