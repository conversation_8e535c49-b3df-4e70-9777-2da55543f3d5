package com.longhuan.order.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.approve.api.pojo.dto.FuMinPreviewContractDTO;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.ContractFileService;
import com.longhuan.order.service.ContractService;
import com.longhuan.resource.pojo.vo.FileVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 合同接口
 *
 * <AUTHOR>
 * @date 2024/08/18
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/contract")
@RequiredArgsConstructor
public class ContractController {

    private final ContractService contractService;
    private final ContractFileService contractFileService;
    private final RedisService redisService;
    /**
     * 列表
     *
     * @param contractListDTO 合同清单 DTO
     * @return {@link Result }<{@link List }<{@link FileVO }>>
     */
    @PostMapping("/list")
    public Result<Page<ContractListVO>> list(@Validated @RequestBody ContractListDTO contractListDTO) {
        return Result.success(contractService.list(contractListDTO));
    }
    /**
     * 合并预览合同
     *
     * @param contractUrlsDTO 合同清单 DTO
     * @return {@link Result }<{@link List }<{@link FileVO }>>
     */
    @PostMapping("/previewTheComprehensiveContract")
    public Result<String> previewComprehensiveContract(@RequestBody ContractUrlsDTO contractUrlsDTO) {
        return Result.success(contractService.previewComprehensiveContract(contractUrlsDTO.getOrderId()));
    }
    /**
     * 生成合同
     *
     * @param orderId 次序id
     * @return {@link Result }<{@link String }>
     */
    @GetMapping("/generate")
    public Result<String> generate(@RequestParam Integer orderId) {
        contractFileService.generateContractSignList(orderId);
        return Result.success("合同生成中");
    }

    /**
     * 合同生成重试
     *
     * @return {@link Result }<{@link String }>
     */
    @ApiOperation(value = "合同生成重试")
    @GetMapping("/contractGenerateRetry")
    public Result<String> contractGenerateRetry() {
        contractFileService.contractGenerateRetry();
        return Result.success("调用成功");
    }

    /**
     * 初始化合同snapshot
     *
     * @param orderId 次序id
     * @return {@link Result }<{@link String }>
     */
    @GetMapping("/initContractSnapshot")
    public Result<String> initContractSnapshot(@RequestParam Integer orderId) {
        contractFileService.initContractSnapshot(orderId);
        return Result.success("初始化成功");
    }

    @ApiOperation(value = "盈峰合同上传")
    @PostMapping("/file/info")
    Result<List<ContractResourceVO>> getContractUrls(@RequestBody ContractUrlsDTO contractUrlsDTO) {
        return Result.success(contractService.getContractUrls(contractUrlsDTO));
    }

    @ApiOperation(value = "上传盈峰合同成功回传信息")
    @PostMapping("/fund/upload/info")
    Result<Boolean> getFundUploadFileInfo(@RequestBody List<ContractToFundVO> fundVOList) {
        return Result.success(contractService.getFundUploadFileInfo(fundVOList));
    }

    @ApiOperation(value = "定时取资方签署完文件")
    @PostMapping("/fund/download/file")
    Result<Boolean> getFundDownloadFile() {
        return Result.success(contractService.getFundDownloadFile());
    }

    @ApiOperation(value = "判断合同是否签署完成")
    @PostMapping("/update/order")
    Result<Boolean> updateOrderContractStatus() {
        return Result.success(contractService.updateOrderContactStatus());
    }


    /**
     * restart签约
     *
     * @return {@link Result }<{@link String }>
     */
    @ApiOperation(value = "合同重签")
    @PostMapping("/restart/sign")
    public Result<Boolean> restartSign(@RequestBody RestartContactDTO restartContactDTO) {
        return Result.success(contractService.restartSign(restartContactDTO));
    }

    /**
     * 客户合同签约信息
     */
    @GetMapping("/contractInfo/{orderId}")
    public Result<ContractInfoVO> getContractInfo(@PathVariable("orderId") Integer orderId) {
        return Result.success(contractService.getContractInfo(orderId));
    }


    /**
     * 资方特殊合同签署
     */
    @PostMapping("/fundSignSubmit")
    public Result<ContractFundSignVO> fundSignSubmit(@RequestBody ContractFundSignSubmitDTO submitDTO) {

        Integer orderId = submitDTO.getOrderId();
        Assert.notNull(orderId, ()->{throw new BusinessException("订单id不能为空");});
        String lockKey = contractService.getFundSignSubmitLockKey(orderId);
        String requestId = IdUtil.randomUUID();
        try {
            Boolean b = redisService.tryLock(lockKey, requestId, 90);
            if (!b) {
                log.info("fundSignSubmit lock failed, orderId = {}", orderId);
                throw new BusinessException("资方签署中，请勿重复提交");
            }
            return Result.success(contractService.fundSignSubmit(submitDTO));
        } finally {
            redisService.releaseLock(lockKey, requestId);
        }
    }

    /**
     * 获取资方签约状态
     */
    @PostMapping("/fund/sign/resul")
    public Result<ContractFundSignResulVO> getFundSignVOResult(@RequestBody ContractFundSignResulDTO resultDTO){
        return Result.success(contractService.getFundSignVOResult(resultDTO));
    }


    /**
     * 更新合同签约状态
     */
    @PostMapping("/update/sign/status")
    public Result<Boolean> updateSignStatus(@RequestBody UpdateContractSignStatusDTO signStatusDTO){
        return Result.success(contractService.updateSignStatus(signStatusDTO));
    }

    @ApiOperation(value = "富民合同预览")
    @PostMapping("/fuMin/preview/contract")
    Result<Boolean> getContractUrls(@RequestBody FuMinPreviewContractDTO fuMinPreviewContractDTO) {
        return Result.success(contractService.fuMinPreviewContract(fuMinPreviewContractDTO));
    }


    /**
     * 盈峰合同催签
     */
    @PostMapping("/fund/urge/sign")
    public Result<Boolean> yingFengUrgeSign(@RequestBody ContractFundSignResulDTO resultDTO){
        return Result.success(contractService.yingFengUrgeSign(resultDTO));
    }

    /**
     * 是否开启线上签约合同
     */
    @PostMapping("/fund/sign/switch")
    public Result<Boolean> signSwitch(){
        return Result.success(contractService.signSwitch());
    }


    /**
     * 资方合同预览列表
     *
     */
    @PostMapping("/fund/preview/list")
    public Result<Page<ContractListVO>> preview(@Validated @RequestBody ContractListDTO contractListDTO) {
        return Result.success(contractService.preview(contractListDTO));
    }


    /**
     * 长银放款失败合同下载
     */
    @PostMapping("/fund/loanPassFundDownLoad")
    public Result<Boolean> loanPassFundDownLoad(@RequestBody ContractFundSignResulDTO resultDTO){
        return Result.success(contractService.loanPassFundDownLoad(resultDTO));
    }


    /**
     * 蓝海抵押合同
     */
    @GetMapping("/lanhai/mortgage")
    public Result<Boolean> createMortgageContract(@RequestParam Integer orderId){
        return Result.success(contractService.createMortgageContract(orderId));
    }

    /**
     * 蓝海解除抵押合同
     */
    @GetMapping("/lanhai/mortgageRelieve")
    public Result<Boolean> createMortgageRelieveContract(@RequestParam Integer orderId){
        return Result.success(contractService.createMortgageRelieveContract(orderId));
    }

    /**
     * 根据签署任务ID获取资方信息
     *
     * @param signTaskId 签署任务ID
     * @return 资方信息
     */
    @ApiOperation(value = "根据签署任务ID获取资方信息")
    @GetMapping("/fund/info")
    public Result<FundInfoBySignTaskVO> getFundInfoBySignTaskId(@RequestParam String signTaskId) {
        return Result.success(contractService.getFundInfoBySignTaskId(signTaskId));
    }

    /**
     * 根据订单ID和合同名称查询合同资源
     *
     * @param orderId 订单ID（必填）
     * @param name 合同名称（可选，默认为"履约告知函"）
     * @return 合同资源ID
     */
    @ApiOperation(value = "根据订单ID和合同名称查询合同资源")
    @GetMapping("/resource")
    public Result<String> getContractResource(@RequestParam Integer orderId,
                                            @RequestParam(required = false, defaultValue = "履约告知函") String name) {
        return Result.success(contractService.getContractResource(orderId, name));
    }

    /**
     * 合同生成
     */
    @GetMapping("/create/contract")
    public Result<String> createContract(@RequestParam String templateNumber){
        return Result.success(contractService.createContract(templateNumber));
    }

}
