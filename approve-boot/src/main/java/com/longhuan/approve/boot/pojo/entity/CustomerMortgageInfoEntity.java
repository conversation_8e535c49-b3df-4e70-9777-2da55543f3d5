package com.longhuan.approve.boot.pojo.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.longhuan.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 客户抵押贷款信息实体
 *
 * <AUTHOR>
 * @date 2024/08/16
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "客户抵押信息")
@TableName("LH_CUSTOMER_MORTGAGE_INFO")
@KeySequence("LH_CUSTOMER_MORTGAGE_INFO_ID_SEQ")
@EqualsAndHashCode(callSuper = true)
public class CustomerMortgageInfoEntity extends BaseEntity {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    @TableId
    private Integer id;

    /**
     * 订单 ID
     */
    private Integer orderId;

    /**
     * 抵押方式(0:线上,1:线下)
     */
    @ApiModelProperty(value = "抵押方式(0:线上,1:线下)")
    private Integer mortgageType;
    /**
     * 期望办理时间
     */
    @ApiModelProperty(value = "期望办理时间")
    private Date processingTime;
    /**
     * 资料交付方式（2：车管所会面，3：快递邮寄）
     */
    @ApiModelProperty(value = "资料交付方式（2：车管所会面，3：快递邮寄）")
    private Integer fileDeliveryType;
    /**
     * 资料归还方式（2：现场归还，3：快递邮寄）
     */
    @ApiModelProperty(value = "资料归还方式（2：现场归还，3：快递邮寄）")
    private Integer fileReturnType;
    /**
     * 办理机构ID
     */
    @ApiModelProperty(value = "办理机构ID")
    private Integer institutionId;
    /**
     * 办理机构名称
     */
    @ApiModelProperty(value = "办理机构名称")
    private String institutionName;
    /**
     * 客户地址(省)
     */
    @ApiModelProperty(value = "客户地址(省)")
    private Integer mortgageProvince;
    /**
     * 客户地址(省)名称
     */
    @ApiModelProperty(value = "客户地址(省)名称")
    private String mortgageProvinceName;
    /**
     * 客户地址(市)
     */
    @ApiModelProperty(value = "客户地址(市)")
    private Integer mortgageCity;
    /**
     * 客户地址(市)名称
     */
    @ApiModelProperty(value = "客户地址(市)名称")
    private String mortgageCityName;
    /**
     * 客户地址(区)
     */
    @ApiModelProperty(value = "客户地址(区)")
    private Integer mortgageArea;
    /**
     * 客户地址(区)名称
     */
    @ApiModelProperty(value = "客户地址(区)名称")
    private String mortgageAreaName;
    /**
     * 客户详细地址
     */
    @ApiModelProperty(value = "客户详细地址")
    private String mortgageDetail;

    /**
     * 抵押处理人
     */
    @ApiModelProperty(value = "抵押处理人")
    private String dealUser;

    /**
     * 办抵渠道（枚举值）
     */
    @ApiModelProperty(value = "办抵渠道")
    private Integer mortgageChannel;

    /**
     * 门店id
     */
    @ApiModelProperty(value = "门店id")
    private Integer storeId;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    /**
     * 办理方式
     * 0-门店/1-第三方
     */
    private Integer dealMethod;

    /**
     * 抵押办理联系人方式
     */
    @ApiModelProperty(value = "抵押办理联系人方式")
    private String dealContactPhone;

    /**
     * 抵押办理联系人姓名
     */
    @ApiModelProperty(value = "抵押办理联系人姓名")
    private String dealContactName;

    /**
     * 选择代理人方式
     * 1：自选服务商, 2：平台推荐
     */
    private Integer agentRadio;

    /**
     * 抵押通过时间
     */
    private LocalDateTime mortgageTime;


}
