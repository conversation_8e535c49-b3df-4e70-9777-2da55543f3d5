package com.longhuan.order.statemachine;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.order.pojo.dto.ApprovalSubmitDTO;
import com.longhuan.order.pojo.dto.ApprovalHistoryQueryDTO;
import com.longhuan.order.pojo.dto.OrderRecordDTO;
import com.longhuan.order.pojo.vo.ApprovalHistoryVO;
import com.longhuan.order.pojo.vo.SubmitResultVO;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;

import java.util.List;

public interface OrderStateService {

    /**
     * 初始状态
     *
     * @param orderId 订单 ID
     * @return int
     */
    int initialStates(Integer orderId);

    List<States> getOrderCurrentNode(Integer orderId);

    /**
     * 批准
     *
     * @param state   州
     * @param orderId 订单 ID
     * @param userId  用户 ID
     * @param remark  备注
     * @return {@link SubmitResultVO }
     */
    SubmitResultVO approve(States state, Integer orderId, Integer userId, String remark);

    /**
     * 获取状态
     *
     * @param orderId 订单 ID
     * @return {@link States }
     */
    States getState(Integer orderId);

    /**
     * 获取当前节点
     *
     * @param orderId 订单 ID
     * @return {@link States }
     */
    States getCurrentNode(Integer orderId);
    SubmitResultVO invalid(States state, Integer orderId, Integer userId, String remark);

    /**
     * 获取订单批准历史记录
     *
     * @param historyDTO  历史 DTO
     * @param currentUser 当前用户
     * @return {@link Page }<{@link ApprovalHistoryVO }>
     */
    Page<ApprovalHistoryVO> getOrderApprovalHistory(OrderRecordDTO historyDTO, LoginUser currentUser);

    /**
     * 获取审批历史记录（支持预审和订单合并查询）
     *
     * @param queryDTO 查询参数
     * @param currentUser 当前用户
     * @return {@link Page }<{@link ApprovalHistoryVO }>
     */
    Page<ApprovalHistoryVO> getApprovalHistoryCombined(ApprovalHistoryQueryDTO queryDTO, LoginUser currentUser);

    /**
     * send 事件
     *
     * @param operationState 操作状态
     * @param event          事件
     * @param orderId        订单 ID
     * @param userId         用户 ID
     * @return {@link SubmitResultVO }
     */
    SubmitResultVO sendEvent(States operationState, Events event, Integer orderId, Integer userId);

    /**
     * send 事件
     *
     * @param currentStates  当前状态
     * @param events         事件
     * @param orderId        订单 ID
     * @param userId         用户 ID
     * @param remark         备注
     * @param remarkExternal 备注 外部
     * @param rejectReason   拒绝原因
     * @return {@link SubmitResultVO }
     */
    SubmitResultVO sendEvent(States currentStates, Events events, Integer orderId, Integer userId,
                             String remark, String remarkExternal, String rejectReason);

    /**
     * send 事件
     *
     * @param operationState 操作状态
     * @param events         事件
     * @param orderId        订单 ID
     * @param userId         用户 ID
     * @param submitDTO      提交 DTO
     * @return {@link SubmitResultVO }
     */
    SubmitResultVO sendEvent(States operationState, Events events, Integer orderId, Integer userId, ApprovalSubmitDTO submitDTO);

    SubmitResultVO orderCancel(Integer orderId, Integer userId, String remark);
}
