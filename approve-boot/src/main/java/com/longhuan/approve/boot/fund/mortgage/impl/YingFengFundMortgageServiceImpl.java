package com.longhuan.approve.boot.fund.mortgage.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.approve.api.constants.LanHaiMortgageEnums;
import com.longhuan.approve.api.pojo.vo.YingFengApiResult;
import com.longhuan.approve.boot.enums.FundApplyNodeEnums;
import com.longhuan.approve.boot.enums.FundMortgageStatusEnum;
import com.longhuan.approve.boot.enums.yingfeng.YingFengDictEnum;
import com.longhuan.approve.boot.feign.OrderFeign;
import com.longhuan.approve.boot.feign.ResourceFeign;
import com.longhuan.approve.boot.fund.finall.FinalFundInfoService;
import com.longhuan.approve.boot.fund.mortgage.FundMortgageStrategy;
import com.longhuan.approve.boot.fund.yingfeng.YingFengService;
import com.longhuan.approve.boot.mapper.FinalFundInfoMapper;
import com.longhuan.approve.boot.mapper.FundUndoMortgageInfoMapper;
import com.longhuan.approve.boot.mapper.YingFengInfoMapper;
import com.longhuan.approve.boot.pojo.dto.FundMortgageCommonDTO;
import com.longhuan.approve.boot.pojo.dto.OnlineMortgageApplyDTO;
import com.longhuan.approve.boot.pojo.dto.OnlineMortgageGetOnlineMortgageInfoDTO;
import com.longhuan.approve.boot.pojo.entity.FinalFundInfoEntity;
import com.longhuan.approve.boot.pojo.entity.FundUndoMortgageInfoEntity;
import com.longhuan.approve.boot.pojo.entity.YingFengInfoEntity;
import com.longhuan.approve.boot.pojo.vo.FundMortgageCommonVO;
import com.longhuan.approve.boot.pojo.vo.OnlineMortgageGetOnlineMortgageInfoVO;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.pojo.dto.MortgageStateUpdateDTO;
import com.longhuan.resource.pojo.dto.FundResourceDTO;
import com.longhuan.resource.pojo.dto.FundResourceResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("MORTGAGE_YING_FENG")
@RequiredArgsConstructor
public class YingFengFundMortgageServiceImpl implements FundMortgageStrategy {

    private final YingFengService yingFengService;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final YingFengInfoMapper yingFengInfoMapper;
    private final FundUndoMortgageInfoMapper fundUndoMortgageInfoMapper;
    private final FinalFundInfoService finalFundInfoService;
    private final OrderFeign orderFeign;
    private final ResourceFeign resourceFeign;


    @Override
    public FundMortgageCommonVO mortgageCreate(FundMortgageCommonDTO fundMortgageCommonDTO) {
        //TODO: 直接调用三方接口 更新状态
        FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, fundMortgageCommonDTO.getOrderId())
                .eq(FinalFundInfoEntity::getFundId, FundEnum.YING_FENG.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(fundInfo)){
            throw new BusinessException("找不到对应的终审信息");
        }
        YingFengInfoEntity yingFengInfoEntity = yingFengInfoMapper.selectOne(new LambdaQueryWrapper<YingFengInfoEntity>()
                .eq(YingFengInfoEntity::getPreId, fundMortgageCommonDTO.getPreId())
                .eq(YingFengInfoEntity::getDeleteFlag, 0)
                .orderByDesc(YingFengInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(yingFengInfoEntity) || ObjUtil.isNull(yingFengInfoEntity.getCreditApplyNo())){
            throw new BusinessException("找不到对应的信息");
        }
        OnlineMortgageApplyDTO request = new OnlineMortgageApplyDTO();
        request.setCreditApplyNo(yingFengInfoEntity.getCreditApplyNo());
        request.setMortgageName(fundMortgageCommonDTO.getMortgageName());
        request.setMortgagePhone(fundMortgageCommonDTO.getMortgagePhone());
        request.setAttendProCode(fundMortgageCommonDTO.getAttendProCode());
        request.setAttendCityCode(fundMortgageCommonDTO.getAttendCityCode());
        request.setAttendCode(fundMortgageCommonDTO.getAttendCode());
        log.info("YingFengFundMortgageServiceImpl.mortgageCreate request:{}", JSONUtil.toJsonStr(request));
        YingFengApiResult<String> response = yingFengService.onlineMortgageApply(request);
        log.info("YingFengFundMortgageServiceImpl.mortgageCreate response:{}", JSONUtil.toJsonStr(response));
        if (!response.isSuccess()){
            throw new BusinessException(response.getMessage());
        }
        FundMortgageCommonVO fundMortgageCommonVO = new FundMortgageCommonVO();
        fundMortgageCommonVO.setFlag( true);
        return fundMortgageCommonVO;
    }


    @Override
    public FundMortgageCommonVO mortgageQuery(FundMortgageCommonDTO fundMortgageCommonDTO) {

        FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, fundMortgageCommonDTO.getOrderId())
                .eq(FinalFundInfoEntity::getFundId, FundEnum.YING_FENG.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(fundInfo)){
            throw new BusinessException("找不到对应的终审信息");
        }
        YingFengInfoEntity yingFengInfoEntity = yingFengInfoMapper.selectOne(new LambdaQueryWrapper<YingFengInfoEntity>()
                .eq(YingFengInfoEntity::getPreId, fundMortgageCommonDTO.getPreId())
                .eq(YingFengInfoEntity::getDeleteFlag, 0)
                .orderByDesc(YingFengInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(yingFengInfoEntity) || ObjUtil.isNull(yingFengInfoEntity.getCreditApplyNo())){
            throw new BusinessException("找不到对应的信息");
        }

        OnlineMortgageGetOnlineMortgageInfoDTO request = new OnlineMortgageGetOnlineMortgageInfoDTO();
        request.setCreditApplyNo(yingFengInfoEntity.getCreditApplyNo());
        log.info("YingFengFundMortgageServiceImpl.mortgageQuery request:{}", JSONUtil.toJsonStr(request));
        YingFengApiResult<OnlineMortgageGetOnlineMortgageInfoVO> response = yingFengService.onlineMortgageGetOnlineMortgageInfo(request);
        log.info("YingFengFundMortgageServiceImpl.mortgageQuery response:{}", JSONUtil.toJsonStr(response));

        if (!(ObjUtil.isNotNull(response)&& ObjUtil.isNotNull(response.getData()) &&ObjUtil.isNotNull(response.getCode()) &&ObjUtil.equals("200", response.getCode()) )) {
            throw new BusinessException(response.getMessage());
        }
        //更新抵押状态
        updateMortgageState(fundMortgageCommonDTO.getOrderId(), response.getData() ,fundInfo);
        //上传抵押登记联
        uploadMortgageInfo(fundMortgageCommonDTO.getOrderId(), response.getData(),fundInfo);



        FundMortgageCommonVO fundMortgageCommonVO = new FundMortgageCommonVO();
        fundMortgageCommonVO.setMortgageResult( response.getData().getZrChannelStatus().getDescription());
        return fundMortgageCommonVO;
    }

    private void uploadMortgageInfo(Integer orderId, OnlineMortgageGetOnlineMortgageInfoVO data, FinalFundInfoEntity fundInfo) {
        FundResourceDTO fundResourceDTO = new FundResourceDTO()
                .setLinkId(orderId)
                .setType(5)
                .setFund(FundEnum.YING_FENG)
                .setPushList(data.getImgList());

        Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(fundResourceDTO);

    }

    private void updateMortgageState(Integer orderId, OnlineMortgageGetOnlineMortgageInfoVO data, FinalFundInfoEntity finalFundInfo) {
        FundUndoMortgageInfoEntity fundUndoMortgageInfoEntity = fundUndoMortgageInfoMapper.selectOne(new LambdaQueryWrapper<FundUndoMortgageInfoEntity>()
                .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                .eq(FundUndoMortgageInfoEntity::getOperateType, "1")
                .eq(FundUndoMortgageInfoEntity::getHistory, 0)
        );
        MortgageStateUpdateDTO mortgageStateUpdateDTO = new MortgageStateUpdateDTO();
        mortgageStateUpdateDTO.setOrderId(orderId);
        Integer mortgageState = 0;
        FundMortgageStatusEnum fundNodeStatus = null;

        YingFengDictEnum.ZrChannelStatus zrChannelStatus = data.getZrChannelStatus();
        String jksFailedReason = data.getJksFailedReason();

        switch (zrChannelStatus) {
            case NOT_ACCEPTED:
                mortgageState =1;
                break;
            case PRE_ACCEPTANCE_SUCCESS:
            case   VALIDATION_SUCCESS:
            case    ACCEPTANCE_SUCCESS:
            case    ARCHIVED:
                mortgageState = 2;
                break;
            case RETURNED:
                mortgageState = 4;
                break;

        }
        finalFundInfo.setMortgageState(fundNodeStatus);
        finalFundInfo.setFundNode(FundApplyNodeEnums.MORTGAGE_RESULT_QUERY);
        finalFundInfo.setFailReason(StringUtils.isNotBlank(jksFailedReason) ? jksFailedReason : null);
        finalFundInfoService.updateById(finalFundInfo);

        //如果是空 说明未发起撤回  如果不是空 结果为最终时 把对象从表中删除
        if (ObjUtil.isNotNull(fundUndoMortgageInfoEntity)) {
            if (ObjUtil.isNotNull(mortgageState)) {
                if (YingFengDictEnum.ZrChannelStatus.RETURNED.equals(zrChannelStatus)) {
                    fundUndoMortgageInfoEntity.setDeleteFlag(1);
                    fundUndoMortgageInfoEntity.setHistory(1);
                }
            }
            fundUndoMortgageInfoMapper.updateById(fundUndoMortgageInfoEntity);
        }
        log.info("YingFengFundMortgageServiceImpl.updateMortgageState mortgageState {}", mortgageState);
        mortgageStateUpdateDTO.setMortgageState(mortgageState);
        mortgageStateUpdateDTO.setMortgageType(0);
        orderFeign.updateMortgageState(mortgageStateUpdateDTO);
        log.info("YingFengFundMortgageServiceImpl.updateMortgageState end");
    }

}
