package com.longhuan.order.pojo.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.longhuan.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 门店GPS配置操作记录表
 * @TableName lh_store_gps_config_log
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "门店GPS配置操作记录表")
@TableName("lh_store_gps_config_log")
@KeySequence("lh_store_gps_config_log_id_seq")
@EqualsAndHashCode(callSuper = true)
public class StoreGpsConfigLogEntity extends BaseEntity {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId
    private Integer id;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 免费门店ID列表（JSON格式）
     */
    @ApiModelProperty(value = "免费门店ID列表（JSON格式）")
    private String freeStoreIds;

    /**
     * 分期门店ID列表（JSON格式）
     */
    @ApiModelProperty(value = "分期门店ID列表（JSON格式）")
    private String installmentStoreIds;
}
