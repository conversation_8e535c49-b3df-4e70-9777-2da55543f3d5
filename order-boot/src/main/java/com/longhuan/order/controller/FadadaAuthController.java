package com.longhuan.order.controller;

import com.fasc.open.api.bean.base.BaseRes;
import com.fasc.open.api.config.HttpConfig;
import com.fasc.open.api.stratey.DefaultJsonStrategy;
import com.fasc.open.api.v5_1.client.OpenApiClient;
import com.fasc.open.api.v5_1.res.common.ECorpAuthUrlRes;
import com.fasc.open.api.v5_1.res.corp.GetIdentifiedStatusRes;
import com.fasc.open.api.v5_1.res.seal.GetSealFreeSignUrlRes;
import com.fasc.open.api.v5_1.res.signtask.CreateSignTaskRes;
import com.fasc.open.api.v5_1.res.signtask.SignTaskActorGetUrlRes;
import com.fasc.open.api.v5_1.res.signtask.SignTaskApplyReportRes;
import com.fasc.open.api.v5_1.res.signtask.SignTaskDownloadReportRes;
import com.fasc.open.api.v5_1.res.user.UserRes;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.config.FadadaConfig;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.vo.FileTOFddVO;
import com.longhuan.order.pojo.vo.FreeSignUrlVO;
import com.longhuan.order.pojo.vo.SignTaskDetailVO;
import com.longhuan.order.pojo.vo.UserAuthUrlVO;
import com.longhuan.order.pojo.vo.fdd.FreeLoginVO;
import com.longhuan.order.service.FadadaAuthService;
import com.longhuan.order.service.FadadaFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequiredArgsConstructor
@Api("法大大接口")
public class FadadaAuthController {

    private final FadadaAuthService fadadaAuthService;
    private final FadadaConfig fadadaConfig;
    private final FadadaFileService fadadaFileService;


    @ApiOperation(value = "初始化")
    @PostMapping("/fdd/init")
    Result<Boolean> getFddUserAuthUrl() {
        // 初始化客户端
        OpenApiClient openApiClient = new OpenApiClient(fadadaConfig.getAppId(), fadadaConfig.getAppSecret(), fadadaConfig.getServerUrl());

        // http超时配置设置， 默认不设置超时时间
        // 如果不设置超时时间 该设置可忽略
        HttpConfig httpConfig = new HttpConfig();
        httpConfig.setConnectTimeout(1000000);
        httpConfig.setReadTimeout(1000000);
        openApiClient.setHttpConfig(httpConfig);

        // Json序列化策率，默认使用Jackson，如果要使用其他如FastJson,Gson等实现JsonStrategy接口即可
        // 如果使用默认该设置可以忽略
        openApiClient.setJsonStrategy(new DefaultJsonStrategy());
        return Result.success();
    }

//    @ApiOperation(value = "查询个人认证/授权状态")
//    @PostMapping("/fdd/auth/user")
//    BaseRes<UserRes> getUserAuthStatus(@RequestBody @Validated UserAuthReqDTO userAuthReqDTO) {
//        return fadadaAuthService.getUserAuthStatus(userAuthReqDTO);
//    }

    @ApiOperation(value = "个人授权链接")
    @PostMapping("/fdd/auth/url")
    Result<UserAuthUrlVO> getFddUserAuthUrl(@RequestBody @Validated UserAuthReqDTO userAuthReqDTO) {
        return Result.success(fadadaAuthService.getFddUserAuthUrl(userAuthReqDTO));
    }

    @ApiOperation(value = "更新授权结果")
    @PostMapping("/fdd/auth/result")
    Result<Boolean> updateFddUserAuthResult(@RequestBody @Validated UserAuthResultDTO authResultDTO) {
        return Result.success(fadadaAuthService.updateFddUserAuthResult(authResultDTO));
    }


    @ApiOperation(value = "解除授权")
    @PostMapping("/fdd/auth/unbind")
    Result<Boolean> userUnbind(@RequestParam("preId") Integer preId) {
        return Result.success(fadadaAuthService.userUnbind(preId));
    }

    @ApiOperation(value = "上传文件到法大大服务器")
    @PostMapping("/fdd/upload/url")
    Result<Boolean> uploadFile(@RequestBody List<FileTOFddVO> vo) {
        return Result.success(fadadaAuthService.uploadFile(vo));
    }

    @ApiOperation(value = "上传文件到法大大服务器-新")
    @PostMapping("/new/fdd/upload/url")
    Result<Boolean> getFaddFileUrl(@RequestParam("files") List<MultipartFile> files,
                                   @RequestParam("fundId") Integer fundId, @RequestParam("filePurpose") String filePurpose) {
        return Result.success(fadadaAuthService.getFaddFileUrl(files, fundId, filePurpose));
    }

    @ApiOperation(value = "文件处理")
    @PostMapping("/fdd/file/process")
    Result<Boolean> processFile(@RequestBody List<String> fileUids) {
        return Result.success(fadadaAuthService.processFile(fileUids));
    }

    @ApiOperation(value = "创建预审授权书签署任务")
    @PostMapping("/fdd/create/sign")
    BaseRes<CreateSignTaskRes> createSignTask(@RequestBody SignTaskDTO signTaskDTO) {
        return fadadaAuthService.createSignTask(signTaskDTO);
    }


    @ApiOperation(value = "获取参与方签署链接")
    @PostMapping("/fdd/sign/url")
    BaseRes<SignTaskActorGetUrlRes> getActorSignUrl(@RequestBody ActorSingUrlDTO actorSingUrlDTO) {
        return fadadaAuthService.getActorSignUrl(actorSingUrlDTO);
    }

//    @ApiOperation(value = "删除签署任务")
//    @PostMapping("/fdd/doc/delete")
//    BaseRes<Void> delete() {
//        return fadadaAuthService.deleteDoc();
//    }

    @ApiOperation(value = "下载签署完成的文件")
    @PostMapping("/fdd/sign/file")
    BaseRes<Void> getSignOwnerFile(@RequestParam Integer preId) {
        return fadadaAuthService.getSignOwnerFile(preId);
    }

    @ApiOperation(value = "获取签署参与方刷脸底图")
    @PostMapping("/fdd/identify/info")
    Result<Integer> getIdentifyInfo(@RequestParam @Validated Integer preId,
                                    @RequestParam @Validated String signTaskId,
                                    @RequestParam @Validated String actorId) {
        return Result.success(fadadaAuthService.getIdentityInfo(preId,signTaskId,actorId));
    }

    /**
     * 查询签署任务详情
     * 法大大跳转h5页面后调用详情接口
     * @param signTaskDetailDTO
     * @return
     */
    @ApiOperation(value = "查询签署任务详情")
    @PostMapping("/fdd/sign/detail")
    Result<SignTaskDetailVO> getSignTaskDetail(@RequestBody SignTaskDetailDTO signTaskDetailDTO) {
        return Result.success(fadadaAuthService.getSignTaskDetail(signTaskDetailDTO));
    }

    @ApiOperation(value = "列表")
    @PostMapping("/get-openId-list")
    Result<Boolean> getList() {
        return Result.success(fadadaAuthService.getList());
    }


    @ApiOperation(value = "创建订单合同签署任务")
    @PostMapping("/fdd/contract/sign")
    BaseRes<CreateSignTaskRes> contractSignTask(@RequestBody ContractTaskDTO contractTaskDTO) {
        return fadadaAuthService.contractSignTask(contractTaskDTO);
    }

    @ApiOperation(value = "获取企业授权链接")
    @PostMapping("/fdd/corp/auth/url")
    BaseRes<ECorpAuthUrlRes> getFaddCorpAuthUrl() {
        return fadadaAuthService.getFaddCorpAuthUrl();
    }

    @ApiOperation(value = "获取印章")
    @GetMapping("/fdd/free/sign/url")
    BaseRes<GetSealFreeSignUrlRes> getSealFreeSignUrl(@RequestParam("openCorpId") String openCorpId) {
        return fadadaAuthService.getSealFreeSignUrl(openCorpId);
    }


    @ApiOperation(value = "企业授权状态")
    @PostMapping("/fdd/corp/auth/state")
    BaseRes<GetIdentifiedStatusRes> getFaddCorpAuthState() {
        return fadadaAuthService.getFaddCorpAuthState();
    }


    @ApiOperation(value = "获取合同签署链接")
    @PostMapping("/fdd/contract/sign/url")
    BaseRes<SignTaskActorGetUrlRes> getContractSignUrl(@RequestBody ContractSingUrlDTO contractSingUrlDTO) {
        return fadadaAuthService.getContractSignUrl(contractSingUrlDTO);
    }

    @ApiOperation(value = "签署任务补偿")
    @PostMapping("/fdd/sign/task")
    Result<Boolean> signTaskResult() {
        return Result.success(fadadaAuthService.signTaskResult());
    }

    @ApiOperation(value = "查询个人授权状态")
    @PostMapping("/fdd/user/get")
    Result<Boolean> getUserAuthStatus() {
        return Result.success(fadadaAuthService.batchUpdateAuthStatus());
    }

    @ApiOperation(value = "查询指定用户授权状态")
    @GetMapping("/fdd/user/authStatus")
    BaseRes<UserRes> getAuthStatusByClientUserId(@RequestParam("clientUserId") String clientUserId) {
        return fadadaAuthService.getAuthStatusByClientUserId(clientUserId);
    }

    @ApiOperation(value = "重新获取法法大大签署后文件")
    @PostMapping("/fdd/retry/downloadFile")
    Result<Boolean> retryDownloadFile() {
        return Result.success(fadadaAuthService.retryDownloadFile());
    }

    @ApiOperation(value = "法大大合同签署状态同步")
    @PostMapping("/fdd/contract/sign/task")
    Result<Boolean> contractSignTaskResult() {
        return Result.success(fadadaAuthService.contractSignTaskResult());
    }

    @ApiOperation(value = "清除无效签署任务信息")
    @PostMapping("/fdd/delete/sign/task")
    Result<Boolean> deleteSignTask() {
        return Result.success(fadadaAuthService.deleteSignTask());
    }


    @ApiOperation(value = "申请证据报告")
    @PostMapping("/fdd/apply/report")
    BaseRes<SignTaskApplyReportRes> applyReport(@RequestBody ApplyReportDTO applyReportDTO) {
        return fadadaFileService.applyReport(applyReportDTO);
    }

    @ApiOperation(value = "下载证据报告")
    @GetMapping("/fdd/download/report")
    BaseRes<SignTaskDownloadReportRes> downloadReport(@RequestParam("reportDownloadId")String reportDownloadId) {
        return fadadaFileService.downloadReport(reportDownloadId);
    }

    @ApiOperation(value = "法大大合同签署文件同步")
    @PostMapping("/fdd/contract/sync/file")
    Result<Boolean> syncFddContractFile() {
        return Result.success(fadadaAuthService.syncFddContractFile());
    }

    @ApiOperation(value = "法大大合同签署文件同步")
    @PostMapping("/fdd/contract/sync/file/{orderId}")
    Result<Boolean> syncFddContractFileByOrderId(@PathVariable("orderId") Integer orderId) {
        return Result.success(fadadaAuthService.syncFddContractFileByOrderId(orderId));
    }

    @ApiOperation(value = "重新签署授权书二维码链接")
    @PostMapping("/fdd/sign/auth/QrCode")
    public ResponseEntity<byte[]> getSignAuthQrCode(@RequestBody UserAuthReqDTO userAuthReqDTO){
        return fadadaAuthService.getSignAuthQrCode(userAuthReqDTO);
    }

    @ApiOperation(value = "抵押代理人实名授权")
    @PostMapping("/fdd/manager/auth/url")
    Result<UserAuthUrlVO> agentAuthUrl(@RequestBody @Validated AgentAuthReqDTO userAuthReqDTO) {
        return Result.success(fadadaAuthService.agentAuthUrl(userAuthReqDTO));
    }

    @ApiOperation(value = "个人用户免验证签链接")
    @PostMapping("/fdd/user/freeSign/url")
    Result<FreeSignUrlVO> userFreeSignUrl(@RequestBody @Validated AgentAuthReqDTO userAuthReqDTO) {
        return Result.success(fadadaAuthService.userFreeSignUrl(userAuthReqDTO));
    }

    @ApiOperation(value = "同步用户免验证签")
    @PostMapping("/fdd/user/syn/freeSign")
    Result<Boolean> userSynFreeSign(@RequestBody @Validated AgentAuthReqDTO userAuthReqDTO) {
        return Result.success(fadadaAuthService.userSynFreeSign(userAuthReqDTO));
    }
    @ApiOperation(value = "同步用户授权状态")
    @GetMapping("/fdd/user/syn/authStatus")
    Result<Boolean> authStatusSynByClientUserId(@RequestParam("idNumber") String idNumber, @RequestParam("phone") String phone) {
        return Result.success(fadadaAuthService.authStatusSynByClientUserId(idNumber, phone));
    }

    @ApiOperation(value = "创建个人免验签签署任务")
    @PostMapping("/fdd/free/contract/sign")
    BaseRes<CreateSignTaskRes> freeContractSignTask(@RequestBody ContractTaskDTO contractTaskDTO) {
        return fadadaAuthService.freeContractSignTask(contractTaskDTO);
    }

    @ApiOperation(value = "法大大快捷签")
    @PostMapping("/fdd/free/login")
    Result<FreeLoginVO> fddFreeLogin(@RequestBody SignTaskDTO signTaskDTO) {
        return Result.success(fadadaAuthService.fddFreeLogin(signTaskDTO));
    }

}
