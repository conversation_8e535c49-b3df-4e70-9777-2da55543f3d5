package com.longhuan.approve.boot.pojo.dto.mzkeshang.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 050100 平台文件通知
 *
 */
@Data
@Accessors(chain = true)
public class KeShangPlatformFileNoticeRequest {
    /**
     * 贷款申请编号
     * 规则：平台号（4位大写字母，不足4位补X）+渠道号（4位大写字母，不足4位补X）+
     * 请求时间（14位yyyyMMddHHMISS）+序号（8位）
     */
    @JsonProperty("LoanAplyNO")
    private String loanAplyNO;

    /**
     * 文件列表
     */
    @JsonProperty("FileList")
    private List<FileItem> fileList;

    @Data
    @Accessors(chain = true)
    public static class FileItem {

        /**
         * 文件名称（文件名字加后缀）
         */
        @JsonProperty("FileNm")
        private String fileNm;

        /**
         * 文件路径（SFTP服务器保存影像件路径）
         */
        @JsonProperty("FilePath")
        private String filePath;

        /**
         * 文件类型
         */
        @JsonProperty("FileTp")
        private String fileTp;
    }
}
