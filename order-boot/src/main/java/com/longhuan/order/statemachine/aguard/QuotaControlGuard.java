package com.longhuan.order.statemachine.aguard;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.order.mapper.DeptQuotaMapper;
import com.longhuan.order.mapper.DeptQuotaRecordMapper;
import com.longhuan.order.mapper.OrderAmountMapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.pojo.entity.DeptQuotaEntity;
import com.longhuan.order.pojo.entity.DeptQuotaRecordEntity;
import com.longhuan.order.pojo.entity.OrderAmountEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.guard.Guard;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 资方终审
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QuotaControlGuard implements Guard<States, Events> {
    private final DeptQuotaRecordMapper deptQuotaRecordMapper;
    private final DeptQuotaMapper deptQuotaMapper;
    private final EnvUtil envUtil;
    private final OrderAmountMapper orderAmountMapper;
    private final OrderInfoMapper orderInfoMapper;

    @Override
    public boolean evaluate(StateContext<States, Events> context) {
        Integer orderId = context.getMessageHeaders().get(StateConstants.ORDER_ID, Integer.class);
        LocalDate now = LocalDate.now();
        deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                .set(DeptQuotaEntity::getEnable, 1)
                .lt(DeptQuotaEntity::getEndDate, now)
                .eq(DeptQuotaEntity::getEnable, 0)
                .eq(DeptQuotaEntity::getDeleteFlag, 0));
        if (CollUtil.isEmpty(deptQuotaRecordMapper.selectList(new LambdaQueryWrapper<DeptQuotaRecordEntity>()
                .eq(DeptQuotaRecordEntity::getOrderId, orderId)
                .eq(DeptQuotaRecordEntity::getDeleteFlag, 0)
                .orderByDesc(DeptQuotaRecordEntity::getId)))) {
            return true;
        }
        DeptQuotaRecordEntity deptQuotaRecordEntity=deptQuotaRecordMapper.selectOne(new LambdaQueryWrapper<DeptQuotaRecordEntity>()
                .eq(DeptQuotaRecordEntity::getOrderId, orderId)
                .eq(DeptQuotaRecordEntity::getAction, 0)
                .eq(DeptQuotaRecordEntity::getDeleteFlag, 0)
                .orderByDesc(DeptQuotaRecordEntity::getId), false);
        if (ObjUtil.isNull(deptQuotaRecordEntity)){
            return getQuota(orderId);
        }
        DeptQuotaEntity deptQuotaEntity = deptQuotaMapper.selectById(deptQuotaRecordEntity.getDeptQuotaId());
        if (ObjUtil.isNull(deptQuotaEntity)){
            deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                    .set(DeptQuotaRecordEntity::getDeleteFlag, 1)
                    .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
            return getQuota(orderId);
        }
        OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                .eq(OrderAmountEntity::getOrderId, orderId)
                .eq(OrderAmountEntity::getDeleteFlag, 0));
        if (deptQuotaEntity.getBeginDate().isAfter(now)
                || deptQuotaEntity.getEndDate().isBefore(now)
                ||deptQuotaEntity.getEnable()==1
                ||deptQuotaRecordEntity.getQuota().compareTo(orderAmountEntity.getCustomerConfirmAmount())<0
        ) {
            int update = deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                    .setIncrBy(DeptQuotaEntity::getRemainingQuota, deptQuotaRecordEntity.getQuota())
                    .eq(DeptQuotaEntity::getId, deptQuotaRecordEntity.getDeptQuotaId())
                    .eq(DeptQuotaEntity::getDeptId, deptQuotaRecordEntity.getDeptId())
                    .eq(DeptQuotaEntity::getDeleteFlag, 0));
            if (update > 0) {
                deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                        .set(DeptQuotaRecordEntity::getAction, 1)
                        .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
            } else {
                log.error("QuotaControlGuard evaluate update dept quota fail orderid:{}", orderId);
                deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                        .set(DeptQuotaRecordEntity::getDeleteFlag, 1)
                        .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
            }
            return getQuota(orderId);
        }
        return true;
    }

    private boolean getQuota(Integer orderId) {
        LocalDate now=LocalDate.now();;
        DeptQuotaRecordEntity deptQuotaRecordEntity;
        OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                .eq(OrderAmountEntity::getOrderId, orderId)
                .eq(OrderAmountEntity::getDeleteFlag, 0)
        );
        OrderInfoEntity orderInfoEntity=orderInfoMapper.selectById(orderId);
        while (true){
            DeptQuotaEntity oldDeptQuotaEntity=deptQuotaMapper.selectOne(new LambdaQueryWrapper<DeptQuotaEntity>()
                    .eq(DeptQuotaEntity::getDeptId, orderInfoEntity.getRegionId())
                    .le(DeptQuotaEntity::getBeginDate, now)
                    .ge(DeptQuotaEntity::getEndDate, now)
                    .eq(DeptQuotaEntity::getEnable, 0)
                    .eq(DeptQuotaEntity::getFundId, orderInfoEntity.getFundId())
                    .eq(DeptQuotaEntity::getType, 0)
                    .eq(DeptQuotaEntity::getDeleteFlag, 0)
                    .orderByAsc(DeptQuotaEntity::getEndDate));
            if (ObjUtil.isEmpty(oldDeptQuotaEntity)){
                    log.error("QuotaControlGuard evaluate update  dept quota fail noBalanceWasFound orderid:{}", orderId);
                return false;

            }
            if (orderAmountEntity.getCustomerConfirmAmount().compareTo(oldDeptQuotaEntity.getRemainingQuota())>0){
                log.error("QuotaControlGuard evaluate update dept quota fail theBalanceIsInsufficient orderid:{}", orderId);
                return false;
            }
            int update2 = deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                    .setDecrBy(DeptQuotaEntity::getRemainingQuota, orderAmountEntity.getCustomerConfirmAmount())
                    .eq(DeptQuotaEntity::getRemainingQuota, oldDeptQuotaEntity.getRemainingQuota())
                    .eq(DeptQuotaEntity::getId, oldDeptQuotaEntity.getId())
                    .eq(DeptQuotaEntity::getEnable, 0)
                    .eq(DeptQuotaEntity::getType, 0)
                    .eq(DeptQuotaEntity::getDeleteFlag, 0));
            if (update2==1){
                deptQuotaRecordEntity=new DeptQuotaRecordEntity()
                        .setDeptQuotaId(oldDeptQuotaEntity.getId())
                        .setDeptId(oldDeptQuotaEntity.getDeptId())
                        .setOrderId(orderId)
                        .setAction(0)
                        .setQuota(orderAmountEntity.getCustomerConfirmAmount());
                deptQuotaRecordMapper.insert(deptQuotaRecordEntity);
                break;
            }
        }
        return true;
    }
}

