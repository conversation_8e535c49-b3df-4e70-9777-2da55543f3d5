package com.longhuan.order.controller;


import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.vo.tencentYun.TencentYUnVO;
import com.longhuan.order.service.TencentYunService;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.DeleteMediaRequest;
import com.tencentcloudapi.vod.v20180717.models.DeleteMediaResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


import java.util.Properties;

@Slf4j
@Api(tags = "腾讯云多人视频会议")
@RestController
@RequestMapping("/api/v1/tencentYUn")
@RequiredArgsConstructor
public class TencentYunController {

    private final TencentYunService tencentYunService;


    @ApiOperation(value = "腾讯云多人视频会议登录接口")
    @PostMapping("/getLoginVo")
    public Result<TencentYUnVO> getLoginVo( @CurrentUser LoginUser loginUser ) {
        return  tencentYunService.getLoginVo(loginUser);

    }
//    @ApiOperation(value = "下载云点播上面的音视频")
//    @PostMapping("/downloadVideo")
//    public void downloadVideo( String [] fileIds,String userId) {
//          tencentYunService.downloadVideo(fileIds, userId);
//
//    }


//    # 功能：第三方回调sign校验
//    # 参数：
//    # key：控制台配置的密钥key
//    # body：腾讯云回调返回的body体
//    #  sign：腾讯云回调返回的签名值sign
//    # 返回值：
//    #  Status：OK 表示校验通过，FAIL 表示校验失败，具体原因参考Info
//    #  Info：成功/失败信息
    @ApiOperation(value = "音视频录制回调接口")
    @PostMapping("/recordingCallback")
//    public void recordingCallback(@RequestBody String body, HttpServletRequest request) throws Exception {
    public void recordingCallback(@RequestBody String body, HttpServletRequest request) throws Exception {
               tencentYunService.recordingCallback(body,request);
        }



    /**
     *  删除音视频
     * @param fileId 通过点播平台唯一ID删除掉音视频
     * @throws Exception
     */
    private void delVideo(String  fileId) throws Exception {
        try{
            //创建文件对象
            Properties properties = new Properties();
            //加载文件获取数据 文件带后缀
            properties.load(Thread.currentThread().getContextClassLoader().getResourceAsStream
                    ("application.properties"));
            //根据key来获取value
            String secretId = properties.getProperty("secretid");
            String secretKey = properties.getProperty("secretkey");
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(secretId, secretKey);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("vod.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            VodClient client = new VodClient(cred, "", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            DeleteMediaRequest req = new DeleteMediaRequest();
            req.setFileId(fileId);
            // 返回的resp是一个DeleteMediaResponse的实例，与请求对象对应
            DeleteMediaResponse resp = client.DeleteMedia(req);
            // 输出json格式的字符串回包
            // 输出json格式的字符串回包
            log.info("删除音视频成功");
            log.info("删除音视频：", DeleteMediaResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            log.debug(e.toString());
        }
    }





}
