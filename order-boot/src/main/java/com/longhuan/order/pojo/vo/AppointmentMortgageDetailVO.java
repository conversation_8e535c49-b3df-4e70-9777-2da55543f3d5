package com.longhuan.order.pojo.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 预约抵押详情信息VO
 *
 * <AUTHOR>
 * @date 2024/08/17
 */
@Data
@Accessors(chain = true)
public class AppointmentMortgageDetailVO {
    private Integer id;
    /**
     * 贷后补件ID
     */
    private Integer patchesId;
    /**
     * 贷后补件合同岗驳回意见
     */
    private String patchesRemark;
    /**
     * 贷后补件资方拒绝次数
     */
    private Integer fundRejectCount;
    /**
     * 贷后补件资方拒绝原因
     */
    private String fundRejectReason;
    /**
     * 贷后补件状态
     */
    private Integer patchesType;
    /**
     * 订单 ID
     */
    private Integer orderId;

    /**
     * 抵押方式(0:线上办抵,1:线下办抵,2:第三方办抵)
     */
    private Integer mortgageType;

    /**
     * 抵押方式范围
     */
    private List<Integer> mortgageTypeScope;

    /**
     * 资方id
     */
    private Integer fundId;
    /**
     * 资方名字
     */
    private String fundName;
    /**
     * 办理机构ID
     */
//    private Integer institutionId;
    /**
     * 办理机构名称
     */
//    private String institutionName;
    /**
     * 客户地址(省)
     */
    private Integer mortgageProvince;
    /**
     * 客户地址(省)名称
     */
    private String mortgageProvinceName;
    /**
     * 客户城市(市)
     */
    private Integer mortgageCity;
    /**
     * 客户城市(市)名称
     */
    private String mortgageCityName;
    /**
     * 客户地址(区)
     */
    private Integer mortgageArea;
    /**
     * 客户地址(区)名称
     */
    private String mortgageAreaName;
    /**
     * 客户详细地址
     */
    private String mortgageDetail;
    /**
     * 车牌号
     */
    private String vehicleNumber;
    /**
     * 放款方式（1:先抵押后放款,2:抵押回执放款）
     */
    private Integer paymentType;
    /**
     * 期望办理时间
     */
    private Date processingTime;
    /**
     * 资料交付方式（2：车管所会面，3：快递邮寄）
     */
    private Integer fileDeliveryType;
    /**
     * 资料归还方式（2：现场归还，3：快递邮寄）
     */
    private Integer fileReturnType;

    /**
     * 办抵渠道（枚举值）
     */
    private Integer mortgageChannel;
    /**
     * 办抵渠道可选范围
     */
    private List<Integer> mortgageChannelScope;
    /**
     * 办抵渠道
     */
    private String mortgageChannelStr;

    /**
     * 渠道类型
     * 0-资方；1-公司
     */
    private Integer channelType;

    /**
     * 门店id
     */
    private Integer storeId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店地址(省)
     */
    private String storeProvinceName;
    /**
     * 门店地址(市)
     */
    private String storeCityName;
    /**
     * 门店地址(区)
     */
    private String storeAreaName;

    /**
     * 预加押状态（0-否，1-是（未完成），2-是（已完成）
     */
    private Integer advanceMortgageState;
    /**
     * 抵押状态（0:未抵押,1:抵押中,2:抵押完成,3:预抵押，4：抵押失败）
     */
    private Integer mortgageState;

    /**
     * 抵押城市
     */
    private String vehicleMortgageCity;

    /**
     * 办理方式
     * 0-门店/1-第三方
     */
    private Integer dealMethod;

    /**
     * 抵押办理联系人方式
     */
    @ApiModelProperty(value = "抵押办理联系人方式")
    private String dealContactPhone;

    /**
     * 抵押办理联系人姓名
     */
    @ApiModelProperty(value = "抵押办理联系人姓名")
    private String dealContactName;

    /**
     * 当前节点
     */
    private Integer currentNode;

    /**
     * 解抵状态
     */
    private String releaseMortgageState;


    /**
     * 选择代理人方式
     * 1：自选服务商, 2：平台推荐
     */
    private Integer agentRadio;


    private String source;

    /**
     * 代理人姓名
     */
    @ApiModelProperty(value = "代理人姓名")
    private String proxyName;
    /**
     * 代理人手机号
     */
    @ApiModelProperty(value = "代理人手机号")
    private String proxyPhone;
    /**
     * 代理人邮寄地址
     */
    @ApiModelProperty(value = "代理人邮寄地址")
    private String recAddress;

    @ApiModelProperty(value = "抵押失败原因")
    private String failReason;
    /**
     * 省份编码
     */
    @ApiModelProperty(value = "省份编码")
    private String provincialCode;
    /**
     * 省名称
     */
    @ApiModelProperty(value = "省名称")
    private String provinceName;
    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    private String cityCode;
    /**
     * 市名称
     */
    @ApiModelProperty(value = "市名称")
    private String cityName;

    /**
     * 抵押时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "抵押时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime mortgageTime;
    /**
     * 服务商（中瑞或者铜车署）
     */
    @ApiModelProperty(value = "服务商（中瑞或者铜车署）")
    private String spName;


}
