package com.longhuan.order.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.feign.RiskFeign;
import com.longhuan.order.pojo.dto.AddCustomerInfoDTO;
import com.longhuan.order.pojo.dto.OrderCustomerInfoDTO;
import com.longhuan.order.pojo.dto.OrderGuaranteePersonDTO;
import com.longhuan.order.pojo.dto.ThreeFactorVerifyDTO;
import com.longhuan.order.pojo.entity.OrderCustomerInfoEntity;
import com.longhuan.order.pojo.entity.OrderGuarantorEntity;
import com.longhuan.order.pojo.vo.OrderCustomerInfoVo;
import com.longhuan.order.service.OrderCustomerInfoService;
import com.longhuan.order.service.XinshuService;
import com.longhuan.risk.pojo.vo.FactorVO;
import io.swagger.annotations.ApiOperation;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 客户信息控制器
 *
 * <AUTHOR>
 * @Date 2024/7/24
 */
@RestController
@RequestMapping("/orderCustomerInfo")
@RequiredArgsConstructor
public class OrderCustomerInfoController {
    private final OrderCustomerInfoService orderCustomerInfoService;
    private final RiskFeign riskFeign;
    private final XinshuService xinshuService;


    /**
     * 查询客户信息页面回显数据
     *
     * @param orderId 订单id
     * @return 客户信息页面回显数据
     */
    @PostMapping("/getCustomerInfoByOrderId/{orderId}")
    public Result<OrderCustomerInfoVo> getCustomerInfoByOrderId(@PathVariable("orderId") Integer orderId) {
        return Result.success(orderCustomerInfoService.getCustomerInfoByOrderId(orderId));
    }

    /**
     * 保存客户信息
     *
     * @param orderCustomerInfoDTO 客户信息DTO
     * @return 保存结果
     */
    @PostMapping("/insertOrderCustomerInfo")
    public Result<OrderCustomerInfoEntity> updateOrderCustomerInfo(@RequestBody @Validated OrderCustomerInfoDTO orderCustomerInfoDTO) {
        return Result.success(orderCustomerInfoService.updateOrderCustomerInfo(orderCustomerInfoDTO));
    }


    /**
     * 三要素验证
     *
     * @return {@link Result< Boolean>}
     */
    @PostMapping("/threeFactorVerify")
    public Result<Boolean> threeFactorVerify(@RequestBody @Validated ThreeFactorVerifyDTO verifyDTO) {
        Result<FactorVO> factorData = riskFeign.getFactorData(verifyDTO.getName(), verifyDTO.getPhone(), verifyDTO.getIdNumber());
        if (Result.isSuccess(factorData) && null != factorData.getData()){
            return Result.success(true);
        }
        return Result.success(false);
    }

    /**
     * 维护客户信息
     *
     * @param addCustomerInfoDTO 添加客户信息 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @PostMapping("/maintainCustomerInfo")
    public Result<Boolean> maintainCustomerInfo(@RequestBody @Validated AddCustomerInfoDTO addCustomerInfoDTO,@CurrentUser LoginUser loginUser) {
        return Result.success(orderCustomerInfoService.maintainCustomerInfo(addCustomerInfoDTO,loginUser));
    }

    /**
     * 保存担保人信息接口
     */
    @ApiOperation(value = "保存担保人信息接口")
    @PostMapping("/saveGuaranteePerson")
    public Result<Boolean> saveGuaranteePerson(@RequestBody  @Validated OrderGuaranteePersonDTO guaranteePersonDTO){
        return Result.success(orderCustomerInfoService.saveGuaranteePerson(guaranteePersonDTO));
    }
    /**
     * 获取担保人信息
     */
    @ApiOperation(value = "获取担保人信息")
    @GetMapping("/getGuaranteePersonByOrderId")
    public Result<List<OrderGuarantorEntity>> getGuaranteePerson(@Validated @RequestParam("orderId") @NotNull(message = "订单ID不能为空") Integer orderId){
        return Result.success(orderCustomerInfoService.getGuaranteePerson(orderId));
    }

    /**
     * 暂存客户信息
     *
     * @param orderCustomerInfoDTO 客户信息DTO
     * @return 保存结果
     */
    @PostMapping("/insertStagingOrderCustomerInfo")
    public Result<OrderCustomerInfoEntity> insertStagingOrderCustomerInfo(@RequestBody OrderCustomerInfoDTO orderCustomerInfoDTO) {
        return Result.success(orderCustomerInfoService.insertStagingOrderCustomerInfo(orderCustomerInfoDTO));
    }
    /**
     * 获取订单工作单位信息
     */
    @ApiOperation(value = "获取订单工作单位信息")
    @GetMapping("/getOrderWorkUnitInfo")
    public Result<JSONObject> getOrderWorkUnitInfo(@Validated @RequestParam("orderId") @NotNull(message = "订单ID不能为空") Integer orderId){
        return Result.success(xinshuService.getOrderWorkUnitInfo(orderId));
    }
    /**
     * 获取订单工作单位历史信息
     *
     */
    @ApiOperation(value = "获取订单工作单位历史信息")
    @GetMapping("/getOrderWorkUnitInfoHistory")
    public Result<JSONObject> getOrderWorkUnitInfoHistory(@Validated @RequestParam("orderId") @NotNull(message = "订单ID不能为空") Integer orderId){
        return Result.success(xinshuService.getOrderWorkUnitInfoHistory(orderId));
    }

    /**
     * 根据企业名称检索企业信息
     * @return 企业信息列表
     */
    @GetMapping("/searchEnterprise")
    public Result<List<String>> searchEnterprise(@RequestParam("enterpriseName") String enterpriseName) {
        return Result.success(xinshuService.searchEnterpriseByName(enterpriseName));
    }
}
