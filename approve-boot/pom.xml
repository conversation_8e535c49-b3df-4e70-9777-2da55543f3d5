<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.longhuan</groupId>
        <artifactId>longhuan-approve</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>approve-boot</artifactId>
    <dependencies>
        <!--内部项目-->
        <!-- sftp上传依赖包 -->
        <dependency>
            <groupId>com.github.mwiede</groupId>
            <artifactId>jsch</artifactId>
        </dependency>
        <dependency>
            <groupId>com.longhuan</groupId>
            <artifactId>approve-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.longhuan</groupId>
            <artifactId>risk-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.longhuan</groupId>
            <artifactId>order-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.longhuan</groupId>
            <artifactId>user-api</artifactId>

        </dependency>
        <dependency>
            <groupId>com.longhuan</groupId>
            <artifactId>data-api</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.longhuan</groupId>
            <artifactId>resource-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.longhuan.common</groupId>
            <artifactId>common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.longhuan.common</groupId>
            <artifactId>common-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.longhuan.common</groupId>
            <artifactId>common-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.longhuan.common</groupId>
            <artifactId>common-log</artifactId>
        </dependency>
        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.longhuan.common</groupId>
            <artifactId>common-nacos-discovery</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.statemachine</groupId>
            <artifactId>spring-statemachine-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.yulichang</groupId>
            <artifactId>mybatis-plus-join-boot-starter</artifactId>
        </dependency>
        <!-- 富民银行 start-->
        <!--        <dependency>-->

        <!--            <groupId>com.fbank.openapi</groupId>-->
        <!--            <artifactId>fbank-api</artifactId>-->

        <!--        </dependency>-->

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.78</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk18on</artifactId>
            <version>1.78</version>
        </dependency>

        <!-- 富民银行 end-->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
        </dependency>



        <!-- yop-basic-sdk -->
        <!-- 基础类包 -->
        <dependency>
            <groupId>com.yeepay.yop.sdk</groupId>
            <artifactId>yop-java-sdk</artifactId>
            <version>4.4.15</version>
        </dependency>
        <!--  下方两个软算法包可以根据需要引入其一或者全部 -->
        <!--  1.国际软算法(RSA) -->
        <dependency>
            <groupId>com.yeepay.yop.sdk</groupId>
            <artifactId>yop-java-sdk-crypto-inter</artifactId>
            <version>4.4.15</version>
        </dependency>
        <!--  2.商密软算法(SM2) -->
        <dependency>
            <groupId>com.yeepay.yop.sdk</groupId>
            <artifactId>yop-java-sdk-crypto-gm</artifactId>
            <version>4.4.15</version>
        </dependency>


        <!-- Jackson XML for parsing XML -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>

        <!-- Jackson Core for JSON processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <!-- Jackson Datatype JSR310 -->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.52</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.3.0</version>
            <scope>compile</scope>
        </dependency>

        <!--贷后还款 -->
        <dependency>
            <groupId>com.longhuan.auto.loan</groupId>
            <artifactId>auto-loan-api</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.5.2</version> <!-- 使用最新版本 -->
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>sit</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>

            <distributionManagement>
                <repository>
                    <!-- 与 settings.xml 中的 server id 相对应 -->
                    <id>maven-releases</id>
                    <url>http://192.168.110.238:8081/repository/maven-releases</url>
                </repository>
            </distributionManagement>
        </profile>
        <profile>
            <id>prd</id>
            <distributionManagement>
                <repository>
                    <!-- 与 settings.xml 中的 server id 相对应 -->
                    <id>maven-releases</id>
                    <url>http://172.16.12.24:8081/repository/maven-releases</url>
                </repository>
            </distributionManagement>
        </profile>
    </profiles>
    <build>
        <finalName>${project.parent.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>