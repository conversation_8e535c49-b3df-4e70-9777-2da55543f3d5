package com.longhuan.order.settleCalculation.service.impl.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.approve.api.constants.FundRepayModeEnum;
import com.longhuan.approve.api.pojo.dto.FundRepayCalcDTO;
import com.longhuan.approve.api.pojo.vo.FundRepayCalcVO;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.enums.FundDeductBizTypeEnums;
import com.longhuan.common.core.enums.FundDeductRepayStatusEnums;
import com.longhuan.common.core.enums.FundDeductRepayTypeEnums;
import com.longhuan.common.core.enums.OrderFeeDetailExpandTypeEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.converter.FundDeductConverter;
import com.longhuan.order.enums.FundRepayStatusEnum;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.mapper.FundRepaymentInfoMapper;
import com.longhuan.order.mapper.OrderPayApplicationMapper;
import com.longhuan.order.pojo.dto.FundEarlyRepaymentCalcDTO;
import com.longhuan.order.pojo.dto.SettleCalculationContext;
import com.longhuan.order.pojo.entity.FundRepaymentDeductEntity;
import com.longhuan.order.pojo.entity.FundRepaymentInfoEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.entity.OrderPayApplicationInfoEntity;
import com.longhuan.order.pojo.vo.SettleCalculationVO;
import com.longhuan.order.service.FundRepaymentDeductService;
import com.longhuan.order.settleCalculation.service.impl.strategy.SettleCalculationStrategy;
import com.longhuan.order.settleCalculation.service.impl.strategy.impl.Annotation.SupportSettleType;
import com.longhuan.order.settleCalculation.service.impl.strategy.impl.enums.SettleType;
import com.longhuan.order.settleCalculation.service.impl.strategy.impl.enums.LiquidatedDamagesRatioEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.longhuan.order.settleCalculation.service.impl.strategy.impl.SettleCalculationMethod.CLOSED_PERIOD_REPAY_MODE;

/**
 * 非委外非赎回策略
 */
@Slf4j
@Component
@SupportSettleType(SettleType.NON_OUTSOURCING_NON_REDEEM)
@RequiredArgsConstructor
public class NonOutsourcingNonRedeemStrategy implements SettleCalculationStrategy {
    static final int ONLINE_REGION_ID = 56;
    private final ApproveFeign approveFeign;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final FundRepaymentDeductService fundRepaymentDeductService;
    private final OrderPayApplicationMapper orderPayApplicationMapper;
    public SettleCalculationVO calculate(SettleCalculationContext context) {
        Assert.notNull(context.getFundSettlementAmount(), "资方结清金额不能为空");
        return ObjUtil.equals(ONLINE_REGION_ID, context.getRegionId()) ? onlineCalculate(context) : SettleCalculationMethod.defaultSettlementPolicy(context);
    }

    /**
     * 线上订单计算
     */
    private SettleCalculationVO onlineCalculate(SettleCalculationContext context) {
        log.info("NonOutsourcingNonRedeemStrategy.onlineCalculate context:{}",context);
        //线上封闭期外结清策略
        if (context.getClosedPeriod() <= context.getPaidPeriods()) {
            // 结清金额=资方结清金额
            return SettleCalculationMethod.directSettlement(context.getFundSettlementAmount());
        }
        if (Objects.equals(context.getClosedPeriodRepayMode(), 0)) {
            // 结清金额=资方结清金额
            return SettleCalculationMethod.directSettlement(context.getFundSettlementAmount());
        }
        //线上封闭期内结清策略
        else {
            //线上封闭期内封闭期结清策略
            if (ObjUtil.equals(context.getClosedPeriodRepayMode(),CLOSED_PERIOD_REPAY_MODE)) {
                // 结清金额=资方结清金额+放款金额*(封闭期数-已还期数) * 产品综合费率
                Assert.notNull(context.getLoanAmount(), () -> new BusinessException("放款金额不能为空"));
                Assert.notNull(context.getProductRate(), () -> new BusinessException("产品综合费率不能为空"));
                return SettleCalculationMethod.calculateWithProductRate(context.getFundSettlementAmount(), context.getLoanAmount(),
                        context.getClosedPeriod(), context.getPaidPeriods(), context.getProductRate());
            }
            //线上封闭期内违约金结清策略
            else {
                //结清金额=资方结清金额+剩余本金*6%【违约金】
                Assert.notNull(context.getRemainingPrincipal(), () -> new BusinessException("剩余本金不能为空"));
                return SettleCalculationMethod.calculateWithPenalty(context.getFundSettlementAmount(),
                        context.getRemainingPrincipal(), LiquidatedDamagesRatioEnum.PENALTY_6_PERCENT);
            }
        }
    }

    @Override
    public void buildLHContext(SettleCalculationContext context,
                               OrderInfoEntity orderInfoEntity,
                               LocalDate payDate) {
        log.info("NonOutsourcingNonRedeemStrategy.buildLHContext context:{} orderInfoEntity:{} payDate:{}",context,orderInfoEntity,payDate);
        FundRepayCalcDTO reqDTO = new FundRepayCalcDTO();
        reqDTO.setOrderId(orderInfoEntity.getId());
        reqDTO.setRepayMode(FundRepayModeEnum.ONLINE);
        //本金
        BigDecimal principal = BigDecimal.ZERO;
        BigDecimal amountTotal = BigDecimal.ZERO;
        int fundSettleFlag = 0;
        int term = 0;
        Result<FundRepayCalcVO> result;
         try{
             result = approveFeign.repayCalc(reqDTO);
             if (Objects.equals(orderInfoEntity.getFundId(), FundEnum.CHANG_YIN.getValue()) && Result.isSuccess(result) && result.getData().getRepayAmt().compareTo(BigDecimal.ZERO) == 0){
                 result = Result.failed();
                 log.info("NonOutsourcingNonRedeemStrategy.buildLHContext changyin result:{}", result);
             }
         }catch (Exception e){
             log.error("approveFeign.repayCalc error", e);
             context.setRedeem(true);
             if (context.getClosedPeriod() != 6 && !context.getFundName().equals("长安新生") && context.getPaidPeriods() < context.getClosedPeriod()) {
                 //直接设置已还期数等于封闭期
                 context.setPaidPeriods(context.getClosedPeriod()-1);
             }
             result = Result.failed();
         }
        if (Result.isSuccess(result)) {
            log.info("NonOutsourcingNonRedeemStrategy.buildLHContext result:{}",result);
            principal = result.getData().getPrincipal();
            amountTotal = result.getData().getRepayAmt().add(result.getData().getGuaraFeeAmt()).add(result.getData().getGuaraFeeOdAmt());
            Long count = fundRepaymentInfoMapper.selectCount(
                    new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                            .eq(FundRepaymentInfoEntity::getOrderId, orderInfoEntity.getId())
                            .eq(FundRepaymentInfoEntity::getFundId, orderInfoEntity.getFundId())
                            .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
            );
            context.setGuaraFeeAmt(result.getData().getGuaraFeeAmt());
            context.setGuaraFeeOdAmt(result.getData().getGuaraFeeOdAmt());
            context.setPaidPeriods(count.intValue())
                    .setSettlePaidPeriods(count.intValue()+1);
        } else {
            log.error("NonOutsourcingNonRedeemStrategy.buildLHContext approveFeign.repayCalc error");

            List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getOrderId, orderInfoEntity.getId())
                            .eq(FundRepaymentDeductEntity::getFundId, orderInfoEntity.getFundId())
                            .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                            .eq(FundRepaymentDeductEntity::getIsRepurchase, 0)
                            .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                    FundDeductBizTypeEnums.PAYMENT,
                                    FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                    FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                    FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE,
                                    FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION
                            ))
                            .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                            .orderByAsc(FundRepaymentDeductEntity::getUpdateTime)
            );
            log.info("NonOutsourcingNonRedeemStrategy.buildLHContext FundRepaymentDeductEntityList:{}",list);
            if (CollUtil.isNotEmpty(list)) {
                List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList = fundRepaymentInfoMapper.selectList(
                        new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                .eq(FundRepaymentInfoEntity::getOrderId, orderInfoEntity.getId())
                                .ge(FundRepaymentInfoEntity::getTerm, list.get(0).getTerm())
                                .eq(FundRepaymentInfoEntity::getFundId, orderInfoEntity.getFundId())
                                .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                );
                BigDecimal repaymentPrincipal = fundRepaymentInfoEntityList.stream()
                        .map(FundRepaymentInfoEntity::getRepaymentPrincipal)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal actuallyPrincipal = fundRepaymentInfoEntityList.stream()
                        .map(FundRepaymentInfoEntity::getActuallyPrincipal)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                principal=repaymentPrincipal.subtract(actuallyPrincipal);
                log.info("NonOutsourcingNonRedeemStrategy.buildLHContext repaymentPrincipal:{} actuallyPrincipal:{}",repaymentPrincipal,actuallyPrincipal);
                if (Objects.equals(repaymentPrincipal.compareTo(actuallyPrincipal), 0)) {
                    if (Objects.equals(list.size(), 1)) {
                        //本金
                        principal = fundRepaymentInfoEntityList.stream()
                                .filter(entity -> entity.getTerm() >= list.get(0).getTerm())
                                .map(FundRepaymentInfoEntity::getActuallyPrincipal)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        amountTotal = fundRepaymentInfoEntityList.stream()
                                .filter(entity -> entity.getTerm() >= list.get(0).getTerm())
                                .map(FundRepaymentInfoEntity::getActuallyAmountTotal)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    } else {
                        //本金
                        principal = fundRepaymentInfoEntityList.stream()
                                .filter(entity -> entity.getTerm() >= list.get(list.size() - 1).getTerm())
                                .map(FundRepaymentInfoEntity::getActuallyPrincipal)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        amountTotal = fundRepaymentInfoEntityList.stream()
                                .filter(entity -> entity.getTerm() >= list.get(list.size() - 1).getTerm())
                                .map(FundRepaymentInfoEntity::getActuallyAmountTotal)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    log.info("NonOutsourcingNonRedeemStrategy.buildLHContext principal:{} amountTotal:{}",principal,amountTotal);
                    fundSettleFlag = 1;
                }
                term = list.get(0).getTerm();
                //判断是否有结清期数
                boolean notEmpty = CollUtil.isNotEmpty(fundRepaymentInfoEntityList.stream()
                        .filter(entity -> Objects.equals(entity.getRepaymentStatus(), FundRepayStatusEnum.SETTLED))
                        .toList());
                //获取这个集合中结清的数据量
                int size = fundRepaymentInfoEntityList.stream()
                        .filter(entity -> Objects.equals(entity.getRepaymentStatus(), FundRepayStatusEnum.SETTLED))
                        .toList().size();
                //筛选出所有已结清（SETTLED）状态的还款记录，并从中找出最大的期数（Term），若没有匹配项则返回 0。
                int i = fundRepaymentInfoEntityList.stream()
                        .filter(entity -> Objects.equals(entity.getRepaymentStatus(), FundRepayStatusEnum.SETTLED))
                        .mapToInt(FundRepaymentInfoEntity::getTerm)
                        .max().orElse(0);
                boolean equals = Objects.equals(size,i);
                //获取这个集合中结清的数据量
                int size1 = fundRepaymentInfoEntityList.stream()
                        .filter(entity -> Objects.equals(entity.getRepaymentStatus(), FundRepayStatusEnum.SETTLED))
                        .toList().size();
                log.info("NonOutsourcingNonRedeemStrategy.buildLHContext notEmpty:{} size:{} i:{}  equals:{}",notEmpty,size,i,equals);
                context
//                        .setPaidPeriods( notEmpty ? (equals ? (Objects.equals(term,0) ? size1 : term) : size1 ) : 0)
                        .setPaidPeriods(term-1)
                        .setOverdueUnpaid(orderInfoEntity.getTotalOverdueAmount())
                        .setSettlePaidPeriods(term);
                log.info("NonOutsourcingNonRedeemStrategy.buildLHContext 1 context:{}",context);
            }else {
                context.setRedeem(true);
                if (context.getClosedPeriod() != 6 && !context.getFundName().equals("长安新生") && context.getPaidPeriods() < context.getClosedPeriod()) {
                    //直接设置已还期数等于封闭期
                    context.setPaidPeriods(context.getClosedPeriod()-1);
                }
                List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList = fundRepaymentInfoMapper.selectList(
                        new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                .eq(FundRepaymentInfoEntity::getOrderId, orderInfoEntity.getId())
                                .eq(FundRepaymentInfoEntity::getFundId, orderInfoEntity.getFundId())
                                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                );
                LocalDate now = LocalDate.now();
                BigDecimal overdueRepaymentTotal = fundRepaymentInfoEntityList.stream()
                        .filter(entity -> entity.getRepaymentDate().isBefore(now))
                        .map(e->ObjUtil.defaultIfNull(e.getRepaymentPrincipal(),BigDecimal.ZERO).add(ObjUtil.defaultIfNull(e.getRepaymentInterest(),BigDecimal.ZERO)))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal overdueActuallyTotal = fundRepaymentInfoEntityList.stream()
                        .filter(entity -> entity.getRepaymentDate().isBefore(now))
                        .map(e->ObjUtil.defaultIfNull(e.getActuallyPrincipal(),BigDecimal.ZERO).add(ObjUtil.defaultIfNull(e.getActuallyInterest(),BigDecimal.ZERO)))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal repaymentPrincipal = fundRepaymentInfoEntityList.stream()
                        .filter(entity -> !entity.getRepaymentDate().isBefore(now))
                        .map(FundRepaymentInfoEntity::getRepaymentPrincipal)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal actuallyPrincipal = fundRepaymentInfoEntityList.stream()
                        .filter(entity -> !entity.getRepaymentDate().isBefore(now))
                        .map(FundRepaymentInfoEntity::getActuallyPrincipal)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                principal=repaymentPrincipal.subtract(actuallyPrincipal);
                context.setOverdueUnpaid(overdueRepaymentTotal.subtract(overdueActuallyTotal));
            }
        }

        List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList = fundRepaymentInfoMapper.selectList(
                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(FundRepaymentInfoEntity::getFundId, orderInfoEntity.getFundId())
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
        );
        log.info("NonOutsourcingNonRedeemStrategy.buildLHContext fundRepaymentInfoEntityList:{}",fundRepaymentInfoEntityList);
        context.setRegionId(orderInfoEntity.getRegionId())
                .setLoanAmount(orderInfoEntity.getApprovalAmount())
                //逾期天数 结清计算器判断下。
                .setOverdueDays(null != payDate ?
                            getOverdueDays(payDate, fundRepaymentInfoEntityList) :
                        orderInfoEntity.getOverdueDays())
                .setFundSettlementAmount(amountTotal)
                .setRemainingPrincipal(principal)
//                .setRedeemAmount()
//                .setRedeemSingleRepay()
//                .setRedeemPaidAmount()

                .setUseDays(calUseDays(orderInfoEntity.getPaymentTime().toLocalDate(),
                        orderInfoEntity.getId(),
                        orderInfoEntity.getFundId(),
                        fundSettleFlag,
                        payDate));
        log.info("NonOutsourcingNonRedeemStrategy.buildLHContext 2 context:{}",context);

    }

    /**
     * 计算逾期天数
     *
     * @param payDate
     * @param fundRepaymentInfoEntityList
     * @return
     */
    private Integer getOverdueDays(LocalDate payDate,
                                   List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList) {

        //未还款的最早期数
        Optional<FundRepaymentInfoEntity> min = fundRepaymentInfoEntityList.stream().filter(v ->
                        FundRepayStatusEnum.NONE.getValue() == v.getRepaymentStatus().getValue())
                .min(Comparator.comparing(FundRepaymentInfoEntity::getTerm));
        if (min.isPresent()) {
            FundRepaymentInfoEntity fundRepaymentInfoEntity = min.get();
            //应还款日期
            LocalDate repaymentDate = fundRepaymentInfoEntity.getRepaymentDate();
            //选择的结清日期在应还款日期前或等于应还款日期,逾期天数=0
            if (payDate.isBefore(repaymentDate)
                    || payDate.isEqual(repaymentDate)) {
                return 0;
            }
            //选择的结清日期在应还款日期后
            else {
                return (int) ChronoUnit.DAYS.between(repaymentDate, payDate);
            }
        }
        return 0;
    }

    /**
     * 计算使用天数
     * 1.存在已结清期数
     * 当前日期 - 上次结清应还日期
     * 2.存在逾期期数
     * 当前日期 - 下一个应还期数应还日期
     * 3.存在实还日期 部分还款
     * 当前日期 - 实还日期
     * 4.存在未到还款日 1期在当前日期之后
     * 当前日期 - 放款日期
     * 5.存在1期逾期 1期在当前日期之前
     * 当前日期 - 1期还款日期
     */
    private Integer calUseDays(LocalDate paymentDate,
                               Integer orderId,
                               Integer fundId,
                               Integer fundSettleFlag,
                               LocalDate payDate) {
        LocalDate beginDate = LocalDate.now();
        //若payDate 不为空,则代表传入选择结清时间
        if (null != payDate) {
            beginDate = payDate;
        }
        LocalDate earliestOverdueDate;
        FundRepaymentInfoEntity calRepaymentInfo;
        int useDays;
        log.info("NonOutsourcingNonRedeemStrategy.calUseDays - Begin, paymentDate: {}, orderId: {}, fundId: {} fundSettleFlag:{} payDate:{} ", paymentDate, orderId, fundId,fundSettleFlag,payDate);
        if (Objects.equals(fundSettleFlag, 1)) {
            List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                            .eq(FundRepaymentDeductEntity::getFundId, fundId)
                            .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                            .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                    FundDeductBizTypeEnums.PAYMENT,
                                    FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                    FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                    FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE,
                                    FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION
                            ))
                            .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                            .orderByAsc(FundRepaymentDeductEntity::getUpdateTime)
            );
            if (CollUtil.isNotEmpty(list)) {
                FundRepaymentInfoEntity fundRepaymentInfoEntity = fundRepaymentInfoMapper.selectOne(
                        new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                                .eq(FundRepaymentInfoEntity::getFundId, fundId)
                                .eq(FundRepaymentInfoEntity::getTerm, list.get(0).getTerm())
                                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(FundRepaymentInfoEntity::getUpdateTime)
                                .last("limit 1")
                );
                List<OrderPayApplicationInfoEntity> orderPayApplicationInfoEntityList = orderPayApplicationMapper.selectList(
                        new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                                .eq(OrderPayApplicationInfoEntity::getOrderId, orderId)
                                .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY)
//                                        .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.CASHIER_APPROVAL)
                                .orderByAsc(OrderPayApplicationInfoEntity::getCreateTime)
                                .last("limit 1")
                );

                earliestOverdueDate = fundRepaymentInfoEntity.getRepaymentDate();
                if (beginDate.isAfter(earliestOverdueDate) || beginDate.isEqual(earliestOverdueDate)) {
                    if (CollUtil.isNotEmpty(orderPayApplicationInfoEntityList)) {
                        useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, orderPayApplicationInfoEntityList.get(0).getPaymentTime().toLocalDate());
                    } else {
                        useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
                    }
                } else {
                    if (!Objects.equals(list.get(0).getTerm(), 1)) {
                        FundRepaymentInfoEntity fundRepaymentInfoEntity1 = fundRepaymentInfoMapper.selectOne(
                                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                                        .eq(FundRepaymentInfoEntity::getFundId, fundId)
                                        .eq(FundRepaymentInfoEntity::getTerm, list.get(0).getTerm() - 1)
                                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                        .orderByDesc(FundRepaymentInfoEntity::getUpdateTime)
                                        .last("limit 1")
                        );
                        useDays = (int) ChronoUnit.DAYS.between(fundRepaymentInfoEntity1.getRepaymentDate(), beginDate);
                        if (CollUtil.isNotEmpty(orderPayApplicationInfoEntityList)) {
                            useDays = (int) ChronoUnit.DAYS.between(fundRepaymentInfoEntity1.getRepaymentDate(), orderPayApplicationInfoEntityList.get(0).getPaymentTime().toLocalDate());
                        }
                    } else {
                        useDays = (int) ChronoUnit.DAYS.between(paymentDate, beginDate);
                        if (CollUtil.isNotEmpty(orderPayApplicationInfoEntityList)) {
                            useDays = (int) ChronoUnit.DAYS.between(paymentDate, orderPayApplicationInfoEntityList.get(0).getPaymentTime().toLocalDate());
                        }
                    }
                }
                log.info("RepaymentServiceImpl.calRepurchaseUseDays - Last settled, useDays: {}", useDays);
                return useDays;
            }
        }
        // 判断全部还款计划是否未到期
        calRepaymentInfo = fundRepaymentInfoMapper.selectOne(
                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                        .eq(FundRepaymentInfoEntity::getFundId, fundId)
                        .eq(FundRepaymentInfoEntity::getTerm, 1)
                        .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                , false
        );

        log.info("NonOutsourcingNonRedeemStrategy.calUseDays - First term repayment info: {}", calRepaymentInfo);

        if (ObjUtil.isNotNull(calRepaymentInfo) &&ObjUtil.equals(calRepaymentInfo.getRepaymentStatus(),FundRepayStatusEnum.OVERDUE)) {
            // 1 期逾期
            earliestOverdueDate = calRepaymentInfo.getRepaymentDate();
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("NonOutsourcingNonRedeemStrategy.calUseDays - First term overdue, useDays: {}", useDays);
            return useDays;
        } else if (ObjUtil.isNotNull(calRepaymentInfo) && ObjUtil.equals(calRepaymentInfo.getRepaymentStatus(),FundRepayStatusEnum.NONE)) {
            // 1期未逾期
            earliestOverdueDate = paymentDate;
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("NonOutsourcingNonRedeemStrategy.calUseDays - First term not overdue, useDays: {}", useDays);
            return useDays;
        }
        // 存在实还日期 部分还款
        calRepaymentInfo = fundRepaymentInfoMapper.selectOne(
                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                        .eq(FundRepaymentInfoEntity::getFundId, fundId)
                        .isNotNull(FundRepaymentInfoEntity::getActuallyDate)
                        .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FundRepaymentInfoEntity::getTerm)
                , false
        );

        log.info("NonOutsourcingNonRedeemStrategy.calUseDays - Partial repayment info: {}", calRepaymentInfo);

        if (ObjUtil.isNotNull(calRepaymentInfo)) {
            earliestOverdueDate = calRepaymentInfo.getActuallyDate();
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("NonOutsourcingNonRedeemStrategy.calUseDays - Partial repayment, useDays: {}", useDays);
            return useDays;
        }

        // 存在逾期 获取第一个逾期
        calRepaymentInfo = fundRepaymentInfoMapper.selectOne(
                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                        .eq(FundRepaymentInfoEntity::getFundId, fundId)
                        .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.OVERDUE)
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                        .orderByAsc(FundRepaymentInfoEntity::getTerm)
                , false
        );

        log.info("NonOutsourcingNonRedeemStrategy.calUseDays - First overdue repayment info: {}", calRepaymentInfo);

        if (ObjUtil.isNotNull(calRepaymentInfo)) {
            earliestOverdueDate = calRepaymentInfo.getRepaymentDate();
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("NonOutsourcingNonRedeemStrategy.calUseDays - First overdue, useDays: {}", useDays);
            return useDays;
        }
        // 存在已结清期数 没有逾期
        calRepaymentInfo = fundRepaymentInfoMapper.selectOne(
                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                        .eq(FundRepaymentInfoEntity::getFundId, fundId)
                        .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FundRepaymentInfoEntity::getTerm)
                , false
        );
        log.info("NonOutsourcingNonRedeemStrategy.calUseDays - Last settled repayment info: {}", calRepaymentInfo);
        if (ObjUtil.isNotNull(calRepaymentInfo)) {
            earliestOverdueDate = calRepaymentInfo.getRepaymentDate();
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("NonOutsourcingNonRedeemStrategy.calUseDays - Last settled, useDays: {}", useDays);
            return useDays;
        }
        log.info("NonOutsourcingNonRedeemStrategy.calUseDays - No matching conditions, returning 0");
        return 0;
    }

}
