package com.longhuan.order.statemachine.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.base.BaseEntity;
import com.longhuan.common.core.constant.GlobalConstants;
import com.longhuan.common.core.enums.RoleEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.core.result.ResultCode;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.util.DictUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.mapper.OrderNodeRecordMapper;
import com.longhuan.order.mapper.PreNodeRecordMapper;
import com.longhuan.order.mapper.RiskAiIntelligentAuditMapper;
import com.longhuan.order.pojo.dto.ApprovalHistoryQueryDTO;
import com.longhuan.order.pojo.dto.ApprovalSubmitDTO;
import com.longhuan.order.pojo.dto.OrderRecordDTO;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.entity.OrderNodeRecordEntity;
import com.longhuan.order.pojo.entity.PreNodeRecordEntity;
import com.longhuan.order.pojo.entity.RiskAiIntelligentAuditEntity;
import com.longhuan.order.pojo.vo.ApprovalHistoryVO;
import com.longhuan.order.pojo.vo.SubmitResultVO;
import com.longhuan.order.statemachine.OrderStateMachineInterceptor;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.user.pojo.vo.UserInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.StateMachineEventResult;
import org.springframework.statemachine.access.StateMachineAccessor;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static cn.hutool.poi.excel.sax.ElementName.v;

/**
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderStateServiceImpl implements OrderStateService {

    private final StateMachinePersister<States, Events, Integer> persist;


    private final StateMachineFactory<States, Events> stateMachineFactory;
    private final OrderInfoMapper orderInfoMapper;
    private final OrderNodeRecordMapper orderNodeRecordMapper;
    private final PreNodeRecordMapper preNodeRecordMapper;
    private final UserFeign userFeign;
    private final OrderStateMachineInterceptor orderStateMachineInterceptor;
    private final DictUtils dictUtils;
    private final RiskAiIntelligentAuditMapper riskAiIntelligentAuditMapper;

    private static States getApproveNextNode(States node, Events event) {
        if (States.CUSTOMER_APPOINTMENT.equals(node)) {

            return switch (event) {
                case GPS_INSTALL_FINISH -> States.GPS_INSTALL_APPLY;
                case REVIEW_APPOINTMENT_FINISH, REVIEW_APPOINTMENT_START -> States.MANAGER_INTERVIEW;
                case CONTRACT_SIGNING_FINISH -> States.CONTRACT_SIGNING;
                default -> node;
            };
        }

        return node;
    }

    private static States getApproveCurrentNode(States node, Events event) {

        if (States.FUNDS_FINAL_APPROVE.equals(node)) {
            if (Events.CUSTOMER_CONFIRM == event) {
                node = States.CUSTOMER_CONFIRM;
            }
        } else if (States.CUSTOMER_APPOINTMENT.equals(node)) {
            if (event == null) return node;
            return switch (event) {
                case GPS_INSTALL_FINISH -> States.GPS_INSTALL_APPLY;
                case REVIEW_APPOINTMENT_FINISH, REVIEW_APPOINTMENT_START -> States.MANAGER_INTERVIEW;
                case CONTRACT_SIGNING_FINISH -> States.CONTRACT_SIGNING;
                default -> node;
            };
        }

        return node;
    }

    private static SubmitResultVO eventResult(Integer orderId, boolean eventResult, States initialState,
                                              States finalStates) {
        return new SubmitResultVO()
                .setOrderId(orderId)
                .setNode(initialState)
                .setStatus(eventResult ? 1 : 2)
                .setTargetNode(finalStates);
    }

    /**
     * 订单状态初始化
     *
     * @param orderId
     * @return
     */

    @Override
    public int initialStates(Integer orderId) {
        StateMachine<States, Events> stateMachine = stateMachineFactory.getStateMachine();
        try {

            // 根据业务 id 恢复
            persist.restore(stateMachine, orderId);

            // 初始化current_node
            OrderInfoEntity orderInfoEntity = new OrderInfoEntity();
            orderInfoEntity.setId(orderId);
            orderInfoEntity.setCurrentNode(States.BUSINESS_ADDED_INFO.getNode());
            orderInfoEntity.setLastNodeFinishTime(LocalDateTime.now());
            orderInfoMapper.updateById(orderInfoEntity);

            // 持久化 进件状态
            persist.persist(stateMachine, orderId);
        } catch (Exception e) {
            throw new BusinessException(e);
        } finally {
            stateMachine.stopReactively().subscribe();
        }
        return 0;
    }

    /**
     * 订单审批通过
     *
     * @param orderId
     * @param userId
     * @param remark
     * @return
     */
    @Override
    public SubmitResultVO approve(States state, Integer orderId, Integer userId, String remark) {

        return sendEvent(state, Events.AGREES, orderId, userId, remark, remark, null);
    }

    /**
     * 订单作废
     */
    @Override
    public SubmitResultVO invalid(States state, Integer orderId, Integer userId, String remark) {
        return sendEvent(state, Events.CANCEL, orderId, userId, remark, null, null);

    }

    @Override
    public States getState(Integer orderId) {

        StateMachine<States, Events> stateMachine = stateMachineFactory.getStateMachine();
        try {
            persist.restore(stateMachine, orderId);

            return stateMachine.getState().getId();
        } catch (Exception e) {
            log.info("get order state error {}", e.getMessage(), e);
        } finally {
            stateMachine.stopReactively().subscribe();
        }
        return null;
    }

    /**
     * 获取当前节点
     *
     * @param orderId 订单 ID
     * @return {@link States }
     */
    @Override
    public States getCurrentNode(Integer orderId) {

        OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getCurrentNode)
                .eq(OrderInfoEntity::getId, orderId));
        return States.getNode(orderInfo.getCurrentNode());
    }

    /**
     * 订单当前节点名称
     */
    @Override
    public List<States> getOrderCurrentNode(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId,
                        OrderInfoEntity::getCurrentNode,
                        OrderInfoEntity::getState,
                        OrderInfoEntity::getGpsState,
                        OrderInfoEntity::getManagementConclusion,
                        OrderInfoEntity::getMortgageState,
                        OrderInfoEntity::getPaymentState)
                .eq(OrderInfoEntity::getId, orderId));

        States node = States.getNode(orderInfoEntity.getCurrentNode());

        // 资方审批
        if (States.FUNDS_FINAL_APPROVE.equals(node)) {
            // 客户确认
            return List.of(States.FUNDS_FINAL_APPROVE);
        } else if (States.CUSTOMER_APPOINTMENT.equals(node)) {
            List<States> nodeList = new LinkedList<>();
            nodeList.add(States.CUSTOMER_APPOINTMENT);
            if (Convert.toInt(orderInfoEntity.getGpsState(), 0) == 0) {
                nodeList.add(States.GPS_INSTALL_APPLY);
            }
            if (Convert.toInt(orderInfoEntity.getMortgageState(), 0) == 0) {
                nodeList.add(States.MORTGAGE_PENDING);
            }
            if (Convert.toInt(orderInfoEntity.getState(), 0) == 0) {
                nodeList.add(States.CONTRACT_SIGNING);
            }
            return nodeList;
        }
        return List.of(node);
    }

    /**
     * 订单审批记录
     */
    @Override
    public Page<ApprovalHistoryVO> getOrderApprovalHistory(OrderRecordDTO historyDTO, LoginUser currentUser) {


        Page<OrderNodeRecordEntity> page = new Page<>(historyDTO.getPageNum(), historyDTO.getPageSize());

        Integer orderId = historyDTO.getOrderId();
        LambdaQueryWrapper<OrderNodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<OrderNodeRecordEntity>()
                .eq(OrderNodeRecordEntity::getOrderId, orderId).eq(OrderNodeRecordEntity::getDeleteFlag, 0)
                .orderByDesc(OrderNodeRecordEntity::getCreateTime);

        Page<OrderNodeRecordEntity> pageList = orderNodeRecordMapper.selectPage(page, lambdaQueryWrapper);
        List<OrderNodeRecordEntity> records = pageList.getRecords();
        Map<Integer, String> approverMap = getApproverMap(records);

        //  内外部原因
        boolean showRiskReason;
        List<Integer> roleIds = currentUser.getRoleIds();

        if (RoleEnum.SYS_ADMIN.hasRole(roleIds) || RoleEnum.BIZ_SUPER_ADMIN.hasRole(roleIds)
                || RoleEnum.RISK_AMOUNT_APPROVE_FINAL.hasRole(roleIds)
                || RoleEnum.RISK_AMOUNT_APPROVE.hasRole(roleIds)
                || RoleEnum.RISK_VEHICLE_ASSESSOR.hasRole(roleIds)
                || RoleEnum.RISK_CONTRACT_APPROVE.hasRole(roleIds)
                ||RoleEnum.RISK_AMOUNT_APPROVE_ONLINE.hasRole(roleIds)

        ) {
            showRiskReason = true;
        } else {
            showRiskReason = false;
        }

        boolean showCSReason;
        if (RoleEnum.SYS_ADMIN.hasRole(roleIds) || RoleEnum.BIZ_SUPER_ADMIN.hasRole(roleIds)
                || RoleEnum.CUSTOMER_SERVICE_SPECIALIST.hasRole(roleIds)
                || RoleEnum.CUSTOMER_SERVICE_SUPERVISOR.hasRole(roleIds)
        ) {
            showCSReason = true;
        } else {
            showCSReason = false;
        }

        List<Integer> riskNodeList = List.of(States.RISK_FINAL_APPROVE.getNode(), States.RISK_FIRST_APPROVE.getNode(),
                States.OVERALL_REVIEW.getNode(), States.PAYMENT_CONTRACT_APPROVAL.getNode());

        List<RiskAiIntelligentAuditEntity> riskAiIntelligentAudits = riskAiIntelligentAuditMapper.selectList(new LambdaQueryWrapper<RiskAiIntelligentAuditEntity>()
                .eq(RiskAiIntelligentAuditEntity::getOrderId, orderId)
                .orderByDesc(RiskAiIntelligentAuditEntity::getCreateTime)
                );

        List<ApprovalHistoryVO> vos = new ArrayList<>();
        int count = 0; // 使用局部变量替代数组计数器

        for (OrderNodeRecordEntity nodeRecord : records) {
            Events event = nodeRecord.getEvent();

            if(Events.BACK_FUNDS_PAYMENT_PAYMENT_APPLY_INFORMATION == event
                    && States.PAYMENT_APPLY_INFORMATION.getNode().equals(nodeRecord.getNextNode())
                    && States.FUNDS_PAYMENT_APPROVAL.getNode().equals(nodeRecord.getCurrentNode())
            ){
                // 放款蓄水池 里面的订单卡单，在4500 节点进行驳回，驳回审核展示为合同审核，将currentNode 修改为合同审核
                nodeRecord.setCurrentNode(States.PAYMENT_CONTRACT_APPROVAL.getNode());
            }

            ApprovalHistoryVO vo = new ApprovalHistoryVO();
            vo.setId(nodeRecord.getId());
            vo.setApproveName(approverMap.get(nodeRecord.getCreateBy()));

            States currentStates = States.getNode(nodeRecord.getCurrentNode());
            vo.setCurrentNode(getApproveCurrentNode(currentStates, event).getDesc());
            vo.setNextNode(getApproveNextNode(States.getNode(nodeRecord.getNextNode()), event).getDesc());
            vo.setApprovalTime(nodeRecord.getCreateTime());

            vo.setApprovalCost(nodeRecord.getApprovalCost());
            vo.setApprovalStatus(event.getDesc());

            if (showRiskReason && riskNodeList.contains(nodeRecord.getCurrentNode())) {
                vo.setApprovalReason(nodeRecord.getRemark());
            } else if (showCSReason && States.PAYMENT_APPLY_INFORMATION.getNode().equals(nodeRecord.getCurrentNode())) {
                vo.setApprovalReason(nodeRecord.getRemark());
            }
            vo.setReasonExternal(nodeRecord.getRemarkExternal());

            if (Events.REJECT == event) {
                String rejectReason = nodeRecord.getRejectReason();
                if (NumberUtil.isNumber(rejectReason)) {
                    vo.setRejectReason(dictUtils.getDictLabel(GlobalConstants.DictType.REVIEW_REFUSE_REASON.name(), Convert.toInt(rejectReason)));
                } else {
                    vo.setRejectReason(rejectReason);
                }

                vo.setRejectReasonSecond(dictUtils.getDictLabel(GlobalConstants.DictType.REVIEW_REFUSE_REASON.name(),
                        Convert.toInt(rejectReason), nodeRecord.getRejectReasonSecond()));

            } else if (Events.BACK == event || Events.BACK_FUNDS_PAYMENT_PAYMENT_APPLY_INFORMATION == event) {
                String rejectReason = nodeRecord.getRejectReason();
                if (StrUtil.isNotEmpty(rejectReason)) {
                    if (currentStates == States.PAYMENT_CONTRACT_APPROVAL) {
                        vo.setRejectReason(dictUtils.getDictLabel(GlobalConstants.DictType.RETURN_REASON_CONTRACT.name(), Convert.toInt(rejectReason)));
                    } else if (Arrays.asList(States.RISK_FIRST_APPROVE, States.RISK_FINAL_APPROVE).contains(currentStates)) {
                        vo.setRejectReason(dictUtils.getDictLabel(GlobalConstants.DictType.DECLINE_REASON.name(), Convert.toInt(rejectReason)));
                    }
                }
            }

            if (currentStates == States.QUALITY_INSPECTION_FINISH) {
                    RiskAiIntelligentAuditEntity riskAiIntelligentAudit = riskAiIntelligentAudits.get(count);
                    if (ObjUtil.isNotNull(riskAiIntelligentAudit)) {
                        String aiReport = "";
                        Integer aiReportStatus = riskAiIntelligentAudit.getAiReportStatus();
                        if (ObjUtil.equals(aiReportStatus, 0)) {
                            aiReport = "生成中";
                        } else if (ObjUtil.equals(aiReportStatus, 1)) {
                            aiReport = "已生成";
                        } else if (ObjUtil.equals(aiReportStatus, 2)) {
                            aiReport = "失败转人工";
                        }
                        vo.setAiReporTime(riskAiIntelligentAudit.getUpdateTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
                        vo.setAiReportStatus(aiReport);
                        if (ObjUtil.isNotNull(riskAiIntelligentAudit.getAuditResults())){
                            vo.setAuditResults(ObjUtil.equals(riskAiIntelligentAudit.getAuditResults(), 1) ? "标准件" : "非标准件");
                        }
                    }
                count  = count + 1;
            }
            vos.add(vo);
        }

        Page<ApprovalHistoryVO> newPage = new Page<>(pageList.getCurrent(), pageList.getSize(), pageList.getTotal());

        // 展示当前节点
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId, OrderInfoEntity::getCurrentNode)
                .eq(OrderInfoEntity::getId, orderId));

        ArrayList<ApprovalHistoryVO> approvalHistoryVOS = new ArrayList<>(vos);

        States states = States.getNode(orderInfoEntity.getCurrentNode());
        ApprovalHistoryVO element = new ApprovalHistoryVO()
                .setCurrentNode(states.getDesc())
                .setApprovalStatus("审批中");
        if (States.SETTLED == states) {
            element.setApprovalStatus("完成");
        } else if (States.PAYMENT_SUCCESS == states) {
            element.setApprovalStatus("放款成功");
        } else if (States.FUNDS_PAYMENT_FAIL == states) {
            element.setApprovalStatus("放款失败");
        } else if (States.PROCESS_TERMINAL == states) {
            element.setApprovalStatus("流程终止");
        } else if (States.SYSTEM_TERMINAL == states) {
            element.setApprovalStatus("流程终止");
        } else if (States.CUSTOMER_CONFIRM == states) {
            element.setApprovalStatus("待确认");
        } else if (States.BUSINESS_ADDED_INFO == states) {
            element.setApprovalStatus("待完成");
        } else if (States.CUSTOMER_APPOINTMENT == states) {
            element.setApprovalStatus("待提交");
        } else {
            element.setApprovalStatus("审批中");
        }
        approvalHistoryVOS.add(0, element);



        newPage.setRecords(approvalHistoryVOS);
        return newPage;
    }

    @Override
    public SubmitResultVO sendEvent(States currentStates, Events events, Integer orderId, Integer userId) {
        return sendEvent(currentStates, events, orderId, userId, null);
    }

    @Override
    public SubmitResultVO sendEvent(States currentStates, Events events, Integer orderId, Integer userId,
                                    String remark, String remarkExternal, String rejectReason) {
        return sendEvent(currentStates, events, orderId, userId, new ApprovalSubmitDTO()
                .setRemark(remark).setRemarkExternal(remarkExternal).setRejectReason(rejectReason)
        );

    }

    @Override
    public SubmitResultVO sendEvent(States currentStates, Events events, Integer orderId, Integer userId,
                                    ApprovalSubmitDTO submitDTO) {
        Objects.requireNonNull(orderId, "orderId is null");
        log.info("orderId:{} ,currentStates:{} ,submitDTO:{}", orderId, currentStates, submitDTO);


        StateMachine<States, Events> stateMachine = stateMachineFactory.getStateMachine();
        StateMachineAccessor<States, Events> stateMachineAccessor = stateMachine.getStateMachineAccessor();
        stateMachineAccessor.withRegion().addStateMachineInterceptor(orderStateMachineInterceptor);
        try {

            // 根据业务 id 获取状态
            persist.restore(stateMachine, orderId);

            State<States, Events> state = stateMachine.getState();

            States initialState = state.getId();


            if (currentStates != null && !Objects.equals(initialState, currentStates)) {
                throw new BusinessException(ResultCode.CHECK_ORDER_STATUS_FALSE);
            }
            MessageBuilder<Events> eventsMessageBuilder = MessageBuilder.withPayload(events);
            eventsMessageBuilder
                    .setHeader(StateConstants.ORDER_ID, orderId).setHeader(StateConstants.USER_ID, userId);

            if (submitDTO != null) {
                if (submitDTO.getRemark() != null) {

                    eventsMessageBuilder.setHeader(StateConstants.REMARK, submitDTO.getRemark());

                }
                if (submitDTO.getRemarkExternal() != null) {

                    eventsMessageBuilder.setHeader(StateConstants.REMARK_EXTERNAL, submitDTO.getRemarkExternal());

                }
                if (submitDTO.getRejectReason() != null) {

                    eventsMessageBuilder.setHeader(StateConstants.REJECT_REASON, submitDTO.getRejectReason());

                }
                if (submitDTO.getRejectReasonSecond() != null) {

                    eventsMessageBuilder.setHeader(StateConstants.REJECT_REASON_SECOND, submitDTO.getRejectReasonSecond());

                }
            }
            Message<Events> message = eventsMessageBuilder.build();
            // 状态流转
            boolean eventResult = sendChangeEvent(orderId, message, stateMachine);

            if (!eventResult) {
                throw new BusinessException("审批流程未配置");
            }

            States finalStates = stateMachine.getState().getId();

            return eventResult(orderId, eventResult, initialState, finalStates);

        } catch (Exception e) {
            log.error("error {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        } finally {
            stateMachine.stopReactively().subscribe();
        }
    }

    @Override
    public SubmitResultVO orderCancel(Integer orderId, Integer userId, String remark) {
        return null;
    }

    /**
     * 状态机变更流程状态
     *
     * @param orderId      进件Id
     * @param message      消息
     * @param stateMachine 状态机
     */
    private boolean sendChangeEvent(Integer orderId, Message<Events> message,
                                    StateMachine<States, Events> stateMachine) {
        AtomicBoolean eventResult = new AtomicBoolean(true);
        stateMachine.sendEvent(Mono.just(message))
                .doOnComplete(() -> sendEventComplete(orderId, stateMachine))
                .doOnError(error -> {
                    log.error("sendEventComplete error {}",error.getMessage(), error);
                    throw new BusinessException(error);
                }).subscribe(result -> {
                    eventResult.set(sendEventSubscribe(result, orderId));
                });

        return eventResult.get();
    }

    private boolean sendEventSubscribe(StateMachineEventResult<States, Events> result, Integer orderId) {
        log.info("Order {} Event result:{} ,message:{}", orderId, result.getResultType(), result.getMessage());
        StateMachineEventResult.ResultType resultType = result.getResultType();
        if (StateMachineEventResult.ResultType.DENIED == resultType) {
            log.error("Order {} event DENIED !", orderId);
            return false;
        }
        return true;
    }


    private void sendEventComplete(Integer orderId, StateMachine<States, Events> stateMachine) {
        log.info("stateMachine persist orderId:{}", orderId);
        // 持久化状态机
        try {
            persist.persist(stateMachine, orderId);

        } catch (Exception e) {
            log.error("persist error {}", e.getMessage(), e);
            throw new BusinessException("订单状态变更失败");
        }
    }

    /**
     * 审批人姓名
     *
     * @param recordEntities
     * @return
     */
    private Map<Integer, String> getApproverMap(List<OrderNodeRecordEntity> recordEntities) {
        if (CollUtil.isEmpty(recordEntities)) {
            return Map.of();
        }

        List<Integer> approves = recordEntities.stream().map(BaseEntity::getCreateBy).toList();
        Result<List<UserInfoVO>> listResult = userFeign.searchUserNameBatch(approves);

        if (listResult == null || listResult.getData() == null || listResult.getData().isEmpty()) {
            return Map.of();
        }

        return listResult.getData().stream()
                .collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getName));


    }

    @Override
    public Page<ApprovalHistoryVO> getApprovalHistoryCombined(ApprovalHistoryQueryDTO queryDTO, LoginUser currentUser) {
        List<ApprovalHistoryVO> allRecords = new ArrayList<>();

        // 情况1：只有 preId，查询预审记录
        if (queryDTO.getPreId() != null && queryDTO.getOrderId() == null) {
            allRecords = getPreApprovalHistory(queryDTO.getPreId());
        }
        // 情况2：传入 orderId（无论是否同时传入preId），根据orderId查询订单获取preId，然后合并查询
        else if (queryDTO.getOrderId() != null) {
            // 通过orderId查询订单信息获取preId
            Integer preId = getPreIdByOrderId(queryDTO.getOrderId());
            if (preId == null) {
                // 如果订单不存在或没有关联的preId，只返回订单审批记录
                OrderRecordDTO orderRecordDTO = new OrderRecordDTO();
                orderRecordDTO.setOrderId(queryDTO.getOrderId());
                orderRecordDTO.setPageNum(queryDTO.getPageNum());
                orderRecordDTO.setPageSize(queryDTO.getPageSize());
                return getOrderApprovalHistory(orderRecordDTO, currentUser);
            }

            // 查询订单审批历史
            OrderRecordDTO orderRecordDTO = new OrderRecordDTO();
            orderRecordDTO.setOrderId(queryDTO.getOrderId());
            orderRecordDTO.setPageNum(1);
            orderRecordDTO.setPageSize(Integer.MAX_VALUE); // 获取所有记录用于合并
            Page<ApprovalHistoryVO> orderHistory = getOrderApprovalHistory(orderRecordDTO, currentUser);

            // 查询预审记录
            List<ApprovalHistoryVO> preHistory = getPreApprovalHistory(preId);

            // 分别对两个列表进行排序，然后合并
            // 对预审记录排序
            preHistory.sort((a, b) -> {
                if (a.getApprovalTime() == null && b.getApprovalTime() == null) return 0;
                if (a.getApprovalTime() == null) return 1;
                if (b.getApprovalTime() == null) return -1;
                return b.getApprovalTime().compareTo(a.getApprovalTime());
            });

            // 对订单记录排序（对于订单记录，null时间的记录代表当前状态，应该排在前面）
            List<ApprovalHistoryVO> orderRecords = new ArrayList<>(orderHistory.getRecords());
            orderRecords.sort((a, b) -> {
                if (a.getApprovalTime() == null && b.getApprovalTime() == null) return 0;
                if (a.getApprovalTime() == null) return -1; // null时间排在前面
                if (b.getApprovalTime() == null) return 1;  // null时间排在前面
                return b.getApprovalTime().compareTo(a.getApprovalTime());
            });

            // 合并已排序的记录（先添加订单记录，再添加预审记录，确保订单记录组在前）
            allRecords.addAll(orderRecords);
            allRecords.addAll(preHistory);
        }

        // 对于情况1（只有preId），需要排序；情况2已经分别排序过了
        if (queryDTO.getPreId() != null && queryDTO.getOrderId() == null) {
            // 按时间倒序排列
            allRecords.sort((a, b) -> {
                if (a.getApprovalTime() == null && b.getApprovalTime() == null) return 0;
                if (a.getApprovalTime() == null) return 1;
                if (b.getApprovalTime() == null) return -1;
                return b.getApprovalTime().compareTo(a.getApprovalTime());
            });
        }

        // 分页处理
        int pageNum = queryDTO.getPageNum();
        int pageSize = queryDTO.getPageSize();
        int total = allRecords.size();
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, total);

        List<ApprovalHistoryVO> pageRecords = start < total ? allRecords.subList(start, end) : new ArrayList<>();

        Page<ApprovalHistoryVO> resultPage = new Page<>(pageNum, pageSize, total);
        resultPage.setRecords(pageRecords);

        return resultPage;
    }

    /**
     * 通过订单ID获取预审ID
     *
     * @param orderId 订单ID
     * @return 预审ID，如果订单不存在或没有关联的preId则返回null
     */
    private Integer getPreIdByOrderId(Integer orderId) {
        if (orderId == null) {
            return null;
        }

        OrderInfoEntity orderInfo = orderInfoMapper.selectOne(
                new LambdaQueryWrapper<OrderInfoEntity>()
                        .select(OrderInfoEntity::getPreId)
                        .eq(OrderInfoEntity::getId, orderId)
                        .eq(OrderInfoEntity::getDeleteFlag, 0)
        );

        return orderInfo != null ? orderInfo.getPreId() : null;
    }

    /**
     * 获取预审审批历史
     *
     * @param preId 预审ID
     * @return 预审审批历史列表
     */
    private List<ApprovalHistoryVO> getPreApprovalHistory(Integer preId) {
        // 查询预审节点记录
        List<PreNodeRecordEntity> preRecords = preNodeRecordMapper.selectList(
                new LambdaQueryWrapper<PreNodeRecordEntity>()
                        .eq(PreNodeRecordEntity::getPreId, preId)
                        .eq(PreNodeRecordEntity::getDeleteFlag, 0)
                        .orderByDesc(PreNodeRecordEntity::getCreateTime)
        );

        if (CollUtil.isEmpty(preRecords)) {
            return new ArrayList<>();
        }

        // 获取审批人信息
        Map<Integer, String> approverMap = getApproverMapByUserIds(preRecords.stream()
                .map(record -> record.getCreateBy())
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));

        // 转换为 ApprovalHistoryVO
        return preRecords.stream().map(record -> {
            ApprovalHistoryVO vo = new ApprovalHistoryVO();
            vo.setId(record.getId());
            vo.setApproveName(approverMap.get(record.getCreateBy()));

            // 设置节点名称
            States currentState = States.getNode(record.getCurrentNode());
            States nextState = States.getNode(record.getNextNode());
            vo.setCurrentNode(currentState.getDesc());
            vo.setNextNode(nextState.getDesc());

            vo.setApprovalTime(record.getCreateTime());
            vo.setApprovalReason(record.getRemark());
            vo.setApprovalStatus("完成"); // 预审记录默认为完成状态

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 根据用户ID列表获取审批人信息映射
     *
     * @param userIds 用户ID列表
     * @return 用户ID到姓名的映射
     */
    private Map<Integer, String> getApproverMapByUserIds(List<Integer> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return new HashMap<>();
        }

        Result<List<UserInfoVO>> listResult = userFeign.searchUserNameByUserIds(userIds);
        if (!Result.isSuccess(listResult) || CollUtil.isEmpty(listResult.getData())) {
            return new HashMap<>();
        }

        return listResult.getData().stream()
                .collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getName));
    }


}
