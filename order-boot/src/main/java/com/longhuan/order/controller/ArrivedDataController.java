package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.ArrivedDataDTO;
import com.longhuan.order.pojo.entity.ArrivedDataEntity;
import com.longhuan.order.pojo.vo.ArrivedDataVO;
import com.longhuan.order.service.ArrivedDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 抵押权人信息控制层
 *
 * <AUTHOR> css
 * @date : 2025-05-23
 */
@Api(tags = "抵押权人信息功能接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/arrived")
public class ArrivedDataController {
    private final ArrivedDataService arrivedDataService;

    /**
     * 查询列表
     *
     * @return 实例对象
     */
    @ApiOperation(value = "分页查询抵押权人")
    @PostMapping("/page/arrivedDataList")
    public Result<Page<ArrivedDataVO>> selectArrivedDataList(@RequestBody ArrivedDataDTO dto, @CurrentUser LoginUser loginUser) {
        return Result.success(arrivedDataService.selectArrivedDataList(dto, loginUser));
    }

    /**
     * 通过抵押权人id获取详情
     *
     * @param id 抵押权人id
     * @return {@link Result< ArrivedDataEntity >}
     */
    @ApiModelProperty("通过抵押权人id获取详情")
    @GetMapping("/queryInfoById/{id}")
    public Result<ArrivedDataEntity> queryInfo(@PathVariable("id") Integer id) {
        return Result.success(arrivedDataService.queryById(id));
    }

    /**
     * 新增抵押权人
     *
     * @param dto 抵押权人信息
     */
    @PostMapping("/insertArrivedDataInfo")
    public Result<Boolean> insertArrivedDataInfo(@RequestBody @Validated ArrivedDataDTO dto) {
        return Result.success(arrivedDataService.insertArrivedDataInfo(dto));
    }

    /**
     * 编辑抵押权人
     *
     * @param dto 抵押权人信息
     */
    @PostMapping("/updateArrivedDataInfo")
    public Result<Boolean> updateArrivedDataInfo(@RequestBody @Validated ArrivedDataDTO dto) {
        return Result.success(arrivedDataService.updateArrivedDataInfo(dto));
    }

    /**
     * 删除抵押权人
     *
     * @param id 主键
     * @return 是否成功
     */
    @ApiOperation("删除抵押权人")
    @PostMapping("/deleteById/{id}")
    public Result<Boolean> deleteById(@PathVariable("id") Integer id) {
        return Result.success(arrivedDataService.deleteById(id));
    }
}