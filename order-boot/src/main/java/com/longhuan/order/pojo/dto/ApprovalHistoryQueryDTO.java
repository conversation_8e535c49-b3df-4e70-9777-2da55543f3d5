package com.longhuan.order.pojo.dto;

import com.longhuan.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 审批历史查询DTO
 * 支持同时查询订单和预审的审批历史
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel("审批历史查询")
public class ApprovalHistoryQueryDTO extends BasePageQuery {
    
    @ApiModelProperty("进件Id")
    private Integer orderId;
    
    @ApiModelProperty("预审Id")
    private Integer preId;
}
