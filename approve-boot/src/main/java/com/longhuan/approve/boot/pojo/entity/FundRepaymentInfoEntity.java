package com.longhuan.approve.boot.pojo.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.longhuan.approve.boot.enums.FundRepayStatusEnum;
import com.longhuan.common.core.base.BaseEntity;
import com.longhuan.common.core.enums.FundRepayEventStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/10/06
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "资方还款信息", description = "资方还款信息表")
@TableName("LH_FUND_REPAYMENT_INFO")
@KeySequence("LH_FUND_REPAYMENT_INFO_ID_SEQ")
@EqualsAndHashCode(callSuper = true)
public class FundRepaymentInfoEntity extends BaseEntity {

    /**
     *
     */
    @ApiModelProperty(value = "", notes = "")
    @TableId
    private Integer id;

    @ApiModelProperty(value = "订单ID", notes = "关联订单的ID")
    private Integer orderId;

    @ApiModelProperty(value = "资方id")
    private Integer fundId;

    /**
     * 期次
     */
    @ApiModelProperty(value = "期次", notes = "还款期次")
    private Integer term;

    /**
     * 应还款日期
     */
    @ApiModelProperty(value = "应还款日期", notes = "应还款的日期")
    private LocalDate repaymentDate;

    /**
     * 应还本金
     */
    @ApiModelProperty(value = "应还本金", notes = "应还的本金金额")
    private BigDecimal repaymentPrincipal;

    /**
     * 应还利息
     */
    @ApiModelProperty(value = "应还利息", notes = "应还的利息金额")
    private BigDecimal repaymentInterest;

    /**
     * 应还罚息
     */
    @ApiModelProperty(value = "应还罚息", notes = "应还的罚息金额")
    private BigDecimal repaymentPenaltyInterest;

    /**
     * 应还复利
     */
    @ApiModelProperty(value = "应还复利", notes = "应还的复利金额")
    private BigDecimal repaymentPsCommOdAmount;

    /**
     * 应还费用金额
     */
    @ApiModelProperty(value = "应还费用金额", notes = "应还的费用金额")
    private BigDecimal repaymentPsFeeAmount;

    /**
     * 应还担保费金额
     */
    @ApiModelProperty(value = "应还担保费金额", notes = "应还的担保费金额")
    private BigDecimal repaymentGuaraFeeAmount;

    /**
     * 应还担保费罚息
     */
    private BigDecimal repaymentGuaraFeeOdAmount;

    /**
     * 应还总金额
     */
    @ApiModelProperty(value = "应还总金额", notes = "应还的总金额")
    private BigDecimal repaymentAmountTotal;

    /**
     * 实还日期
     */
    @ApiModelProperty(value = "实还日期", notes = "实际还款的日期")
    private LocalDate actuallyDate;

    /**
     * 实还本金
     */
    @ApiModelProperty(value = "实还本金", notes = "实际还的本金金额")
    private BigDecimal actuallyPrincipal;

    /**
     * 实还利息
     */
    @ApiModelProperty(value = "实还利息", notes = "实际还的利息金额")
    private BigDecimal actuallyInterest;

    /**
     * 实还罚息
     */
    @ApiModelProperty(value = "实还罚息", notes = "实际还的罚息金额")
    private BigDecimal actuallyPenaltyInterest;

    /**
     * 实还复利
     */
    @ApiModelProperty(value = "实还复利", notes = "实际还的复利金额")
    private BigDecimal actuallyCommOdAmount;

    /**
     * 实还费用金额
     */
    @ApiModelProperty(value = "实还费用金额", notes = "实际还的费用金额")
    private BigDecimal actuallyFeeAmount;

    /**
     * 实还担保费金额
     */
    @ApiModelProperty(value = "实还担保费金额", notes = "实际还的担保费金额")
    private BigDecimal actuallyGuaraFeeAmount;

    /**
     * 实还担保费罚息
     */
    @ApiModelProperty(value = "实还担保费罚息", notes = "实际还的担保费罚息")
    private BigDecimal actuallyGuaraFeeOdAmount;

    /**
     * 违约金
     */
    @ApiModelProperty(value = "违约金", notes = "违约金金额")
    private BigDecimal actuallyPenalty;

    /**
     * 实还总金额
     */
    @ApiModelProperty(value = "实还总金额", notes = "实还总金额")
    private BigDecimal actuallyAmountTotal;

    /**
     * 实还总金额
     */
    @ApiModelProperty(value = "应还引流费", notes = "应还引流费")
    private BigDecimal repaymentReferral;

    /**
     * 实还总金额
     */
    @ApiModelProperty(value = "实还引流费", notes = "实还引流费")
    private BigDecimal actuallyReferral;

    /**
     * 还款状态 0：未还 1：部分已还 2：逾期 3：已结清
     */
    private FundRepayStatusEnum repaymentStatus;

    /**
     * 是否逾期
     */
    private Integer isOverdue;

    /**
     * 事件状态: 0-无 ，1-整笔结清
     */
    private FundRepayEventStatusEnum eventStatus;

    /**
     * 优惠利息
     */
    private BigDecimal discountInterest;

    /**
     * 优惠费用
     */
    private BigDecimal discountFee;

    /**
     * 剩余优惠利息
     */
    private BigDecimal leftDiscountInterest;

    /**
     * 剩余优惠费用
     */
    private BigDecimal leftDiscountFee;

    /**
     * 减免金额
     */
    @ApiModelProperty(value = "减免金额", notes = "减免金额")
    private BigDecimal reductionAmount;

    /**
     * 减免金额
     */
    @ApiModelProperty(value = "减免金额", notes = "减免金额")
    private BigDecimal reduceAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", notes = "备注")
    private String remark;

    /**
     * 是否代偿(单笔) 0-未代偿 1-代偿中 2-已代偿
     */
    @ApiModelProperty(value = "是否代偿(单笔)", notes = "是否代偿(单笔)")
    private Integer isdaichang;

}
