package com.longhuan.order.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.feign.DigitalizeFeign;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.dto.digitalize.DigitalizeCreateCompanySignDTO;
import com.longhuan.order.pojo.entity.CaseInfoEntity;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.ZhongXinService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;

/**
 * 众信
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@Api(tags = "众信")
@RestController
@RequestMapping("/api/v1/Product/ZhongXin")
@RequiredArgsConstructor
public class ZhongXinController {

    private final ZhongXinService zhongXinService;


    /**
     * 云启推送案件到众信
     */
    @ApiOperation(value = "云启推送案件到众信")
    @PostMapping("/pushCaseToZhongXin")
    public Result<String> pushCaseToZhongXin(Integer orderId) {
        return zhongXinService.pushCaseToZhongXin(orderId);

    }

    /**
     * 案件驳回至云启操作
     */
    @ApiOperation(value = "案件驳回至云启操作")
    @PostMapping("/collection/rejection")
    public ZhongXinApiResult<CollectionRejectionVO> caseDismissedToYunqi(@RequestParam("data") String data) {
        return ZhongXinApiResult.success(zhongXinService.caseDismissedToYunqi(data));
    }

    @ApiOperation(value = "案件状态流转成功/失败通知")
    @PostMapping("/result/notify")
    public ZhongXinApiResult<Boolean> caseStatusNotice(@RequestParam("list") String list) {
        return ZhongXinApiResult.success(zhongXinService.caseStatusNotice(list));
    }

    /**
     * 云启还款计划查询
     */
    @ApiOperation(value = "云启还款计划查询")
    @PostMapping("/repay/list")
    public ZhongXinApiResult<String> repaymentPlanQuery(@RequestParam("case_no") String case_no) {
        return ZhongXinApiResult.success(zhongXinService.repaymentPlanQuery(case_no));

    }

    /**
     * 结清单通知推送[债转+委托]【发起推送】
     */
    @ApiOperation(value = "结清单通知推送[债转+委托]【发起推送】")
    @PostMapping("/settle/notify")
    public ZhongXinApiResult<Boolean> pushSettleNotify(@RequestParam("datainfo") String datainfo) throws JsonProcessingException {
        return ZhongXinApiResult.success(zhongXinService.pushSettleNotify(datainfo));
    }

    /**
     * 推送新增回款单
     */
    @ApiOperation(value = "推送新增回款单")
    @PostMapping("/retrieve/accept")

    public ZhongXinApiResult<Boolean> pushRepaymentNotify(@RequestParam("params") String params) {
        return ZhongXinApiResult.success(zhongXinService.pushRepaymentNotify(params));
    }

    /**
     * 回款单审核通过，异步通知汇丰
     */
    @ApiOperation(value = "回款单审核通过，异步通知汇丰")
    @PostMapping("/payment/notification")
    public ZhongXinApiResult<Boolean> paymentNotification(@RequestBody PaymentNotificationDTO paymentNotificationDTO) {
        return ZhongXinApiResult.success(zhongXinService.paymentNotification(paymentNotificationDTO));
    }

    /**
     * 门店保全审核管理审核通过【发起推送】
     */
    @ApiOperation(value = "门店保全审核管理审核通过【发起推送】")
    @PostMapping("/preservation/notify")
    public ZhongXinApiResult<Boolean> preservationNotify(@RequestParam("params") String datainfo) throws IOException, URISyntaxException {
        return ZhongXinApiResult.success(zhongXinService.preservationNotify(datainfo));
    }

    /**
     * 云启获取更新门店信息（h5页面获取）
     */
    @ApiOperation(value = "云启获取更新门店信息（h5页面获取）")
    @GetMapping("/common/store/list")
    public ZhongXinApiResult<List<StoreAddressInfoListVO>> getStoreList() {
        return ZhongXinApiResult.success(zhongXinService.getStoreList());
    }

    /**
     * 查看汇丰合同页面
     */
    @ApiOperation(value = "查看汇丰合同页面")
    @PostMapping("sign/all/contracts")
    public ZhongXinApiResult<ReceiveHuiFengContractsVO> signAllContracts(@RequestParam("case_no") String list) throws JsonProcessingException {
        return ZhongXinApiResult.success(zhongXinService.signAllContracts(list));
    }

    /**
     * 查看合同地址
     */
    @ApiOperation(value = "查看合同地址")
    @PostMapping("sign/contract/address")
    public ZhongXinApiResult<ContractVO> signContractAddress(@RequestParam("content") String content) {
        return ZhongXinApiResult.success(zhongXinService.signContractAddress(content));
    }

    /**
     * 废除协议免责声明
     */
    @ApiOperation(value = "废除协议免责声明")
    @PostMapping("agreement/discard")
    public ZhongXinApiResult<Boolean> agreementDiscard(@RequestParam("content") String content) {
        return ZhongXinApiResult.success(zhongXinService.agreementDiscard(content));
    }

    /**
     * 生成协议
     */
    @ApiOperation(value = "生成协议")
    @PostMapping("agreement")
    public ZhongXinApiResult<Boolean> agreementCreate(@RequestParam("content") String content) {
        return ZhongXinApiResult.success(zhongXinService.agreementCreate(content));
    }

    /**
     * 案件分配部门更换
     */
    @ApiOperation(value = "案件分配部门更换")
    @PostMapping("case/change")
    public ZhongXinApiResult<Boolean> caseDepartmentUpdate(@RequestParam("content") String content) {
        return ZhongXinApiResult.success(zhongXinService.caseDepartmentUpdate(content));
    }

    @ApiOperation(value = "推送案件到众信")
    @PostMapping("/pushToZhongXinAsBackCase")
    public Result<Object> pushToZhongXinAsBackCase(@RequestBody BackCaseDTO backCaseDTO) {
        return Result.success(zhongXinService.pushToZhongXinAsBackCase(backCaseDTO));
    }

    @ApiOperation(value = "回款单推送案件到众信")
    @PostMapping("/pushToZhongXinAsRetrieveAccept")
    public Result<Boolean> pushToZhongXinAsRetrieveAccept(@RequestBody ReceivePaymentDTO dto) {
        return Result.success(zhongXinService.pushToZhongXinAsRetrieveAccept(dto));
    }

    private final DigitalizeFeign digitalizeFeign;

    @PostMapping("/dfdfad")
    public Result<DigitalizeCreateCompanySignVO> dfdfad(@RequestBody List<DigitalizeCreateCompanySignDTO.DataList> debtTransfersContractListVO) {
        return digitalizeFeign.createCompanySign(new DigitalizeCreateCompanySignDTO().setData_list(debtTransfersContractListVO));

    }
    /**
     * 金蝶推送案件到众信
     */
    @ApiOperation(value = "金蝶推送案件到众信")
    @PostMapping("/jindie/pushCaseToZhongXin")
    public Result<String> jindiePushCaseToZhongXin(@RequestParam("digitalOrderId") String digitalOrderId) {
        return zhongXinService.jindiePushCaseToZhongXin(digitalOrderId);

    }
    /**
     * 数字化推送案件到众信
     */
    @ApiOperation(value = "数字化推送案件到众信")
    @PostMapping("/digitalize/pushCaseToZhongXin")
    public Result<String> digitalizePushCaseToZhongXin(@RequestBody CaseInfoEntity caseInfoEntity) {
        return zhongXinService.digitalizePushCaseToZhongXin(caseInfoEntity);

    }
    /**
     * 接收众信推送的跟进方式
     */
    @ApiOperation(value = "接收众信推送的跟进方式")
    @PostMapping("/follow/way/receive")
    public Result<Boolean> receiveFollowWay(@RequestParam("content") String content) {
        return Result.success(zhongXinService.receiveFollowWay(content));

    }
    /**
     * 定时更新众信状态
     */
    @ApiOperation(value = "定时更新众信流转状态")
    @PostMapping("/updateZhongXinStatus")
    public Result<Boolean> updateZhongXinStatus() {
      return   Result.success(zhongXinService.updateZhongXinStatus());
    }
    /**
     * 接收众信撤回状态
     */
    @ApiOperation(value = "接收众信撤回状态")
    @PostMapping("/recall/receive")
    public Result<Boolean> receiveRecall(@RequestParam("content") String content) {
        return Result.success(zhongXinService.receiveRecall(content));

    }

}
