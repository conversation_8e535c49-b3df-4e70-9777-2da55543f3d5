package com.longhuan.order.converter;

import com.longhuan.order.pojo.entity.PreApprovalApplyInfoEntity;
import com.longhuan.order.pojo.entity.OrderContactPersonEntity;
import com.longhuan.order.pojo.dto.OrderContactPersonDTO;
import com.longhuan.order.pojo.vo.OrderContactPersonVo;
import com.longhuan.risk.pojo.dto.ContactDTO;
import io.swagger.annotations.ApiModelProperty;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/29
 */
@Mapper(componentModel = "spring")
public interface OrderContactPersonConverter {
    OrderContactPersonEntity dto2Entity(OrderContactPersonDTO dto);

    OrderContactPersonVo spouse2Vo(PreApprovalApplyInfoEntity spouse);

    List<OrderContactPersonVo> personList2VoList(List<OrderContactPersonEntity> spouse);


    @Mapping(source = "entity.name", target = "name")
    @Mapping(source = "entity.phone", target = "phoneNumber")
    @Mapping(source = "entity.idNumber", target = "idNumber")
    @Mapping(source = "entity.relation", target = "index")
    ContactDTO entity2Dto(OrderContactPersonEntity entity);


}
