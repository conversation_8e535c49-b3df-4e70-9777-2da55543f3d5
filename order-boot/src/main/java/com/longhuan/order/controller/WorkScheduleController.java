package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.WorkScheduleDTO;
import com.longhuan.order.pojo.vo.WorkScheduleVO;
import com.longhuan.order.service.WorkScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "开发排期")
@RestController
@RequestMapping("/api/v1/schedule")
@RequiredArgsConstructor
public class WorkScheduleController {

    private final WorkScheduleService workScheduleService;
    @ApiOperation(value = "开发排期列表")
    @PostMapping("/list")
    public Result<List<WorkScheduleVO>> getList(@RequestBody WorkScheduleDTO workScheduleDTO) {
        return Result.success(workScheduleService.getList(workScheduleDTO));
    }
}
