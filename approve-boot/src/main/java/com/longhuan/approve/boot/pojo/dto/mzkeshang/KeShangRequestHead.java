package com.longhuan.approve.boot.pojo.dto.mzkeshang;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 梅州客商接口请求头实体类
 */
@Data
@Accessors(chain = true)
public class KeShangRequestHead {

    /**
     * 版本号
     * 固定值: 1
     */
    @JsonProperty("version")
    private String version;

    /**
     * 商户编号
     * 由银行统一分配，固定值: KSYH
     */
    @JsonProperty("mer_id")
    private String merId;

    /**
     * 接口ID
     * 接口名
     */
    @JsonProperty("api_id")
    private String apiId;

    /**
     * 交易流水
     * 用来标识接口请求的唯一性，序列流水
     */
    @JsonProperty("trans_no")
    private String transNo;

    /**
     * 请求时间
     * 格式: yyyyMMddhi24mmss
     */
    @JsonProperty("req_time")
    private String reqTime;

    /**
     * 签名
     * 签名信息
     */
    @JsonProperty("sign")
    private String sign;

    /**
     * 签名类型
     * 固定值: SM2
     */
    @JsonProperty("sign_type")
    private String signType;

    /**
     * 报文体内容
     * SM4加密JSON数据
     */
    @JsonProperty("info_content")
    private String infoContent;
}
