package com.longhuan.approve.boot.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum GpsRepaymentTypeEnum {
    TYPE_1(1, "提前还款单期"),
    TYPE_2(2, "到期还款"),
    TYPE_3(3, "逾期还款"),
    TYPE_4(4, "提前整笔结清");

    @EnumValue
    @JsonValue
    private final int code;
    private final String name;


    /**
     * 根据 code 获取对应的 name
     *
     * @param code 枚举 code
     * @return 对应的 name，如果没有找到则返回 null
     */
    public static String getNameByCode(int code) {
        for (GpsRepaymentTypeEnum value : values()) {
            if (value.getCode() == code) {
                return value.getName();
            }
        }
        return null;
    }


}
