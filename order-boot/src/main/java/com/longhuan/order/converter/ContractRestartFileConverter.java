package com.longhuan.order.converter;

import com.longhuan.order.pojo.entity.ContractRestartFileEntity;
import com.longhuan.order.pojo.vo.ContractRestartFileInfoVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ContractRestartFileConverter {

    List<ContractRestartFileInfoVO> entityListToVoList(List<ContractRestartFileEntity> contractRestartFileInfoVOList);
}
