package com.longhuan.order.constants;

public interface ExemptionApprovalConstants {
    /**
     * 特批资管副总审批
     */
    String TP_MANAGER_VICE_PRESIDENT_APPROVE_ID = "TP_MANAGER_VICE_PRESIDENT_APPROVE_ID";
    /**
     * 特批区总审批
     */
    String TP_AREA_PRESIDENT_APPROVE_ID = "TP_AREA_PRESIDENT_APPROVE_ID";
    /**
     * 特批资管主管审批
     */
    String TP_ASSET_MANAGER_APPROVE_ID = "TP_ASSET_MANAGER_APPROVE_ID";
    /**
     * 特批运营总监审批
     */
    String TP_OPERATION_DIRECTOR_APPROVED_ID = "TP_OPERATION_DIRECTOR_APPROVED_ID";
    /**
     * 特批总裁审批
     */
    String TP_PRESIDENT_APPROVE_ID = "TP_PRESIDENT_APPROVE_ID";
    /**
     * 区域资管副总审批
     */
    String QY_MANAGER_VICE_PRESIDENT_APPROVE_ID = "TP_MANAGER_VICE_PRESIDENT_APPROVE_ID";
    /**
     * 区域区总审批
     */
    String QY_AREA_PRESIDENT_APPROVE_ID = "TP_AREA_PRESIDENT_APPROVE_ID";
    /**
     * 区域公司发起门店经理
     */
    String QY_COMPANY_INITIATE_STORE_MANAGER_APPROVE_ID = "QY_COMPANY_INITIATE_STORE_MANAGER_APPROVE_ID";
    /**
     * 区域公司发起资管主管
     */
    String QY_COMPANY_INITIATE_ASSET_MANAGER_APPROVE_ID = "QY_COMPANY_INITIATE_ASSET_MANAGER_APPROVE_ID";
    /**
     * 区域公司发起品质管理师
     */
    String QY_COMPANY_INITIATE_QUALITY_MANAGER_APPROVE_ID = "QY_COMPANY_INITIATE_QUALITY_MANAGER_APPROVE_ID";
    /**
     * 区域公司发起资管副总
     */
    String QY_COMPANY_INITIATE_ASSET_VICE_PRESIDENT_APPROVE_ID = "QY_COMPANY_INITIATE_ASSET_VICE_PRESIDENT_APPROVE_ID";
    /**
     * 区域公司发起总经理
     */
    String QY_COMPANY_INITIATE_PRESIDENT_APPROVE_ID = "QY_COMPANY_INITIATE_PRESIDENT_APPROVE_ID";
    /**
     * 资管审批人
     */
    String ASSET_APPROVE_ID = "ASSET_APPROVE_ID";
    /**
     * 资管品质管理室
     */
    String ASSET_QUALITY_MANAGEMENT_ROOM_APPROVE_ID = "ASSET_QUALITY_MANAGEMENT_ROOM_APPROVE_ID";
    /**
     * 资管直接主管
     */
    String ASSET_DIRECTOR_APPROVE_ID = "ASSET_DIRECTOR_APPROVE_ID";
    /**
     * 资管运营总监
     */
    String ASSET_OPERATION_DIRECTOR_APPROVE_ID = "ASSET_OPERATION_DIRECTOR_APPROVE_ID";
    /**
     * 资管区域总经理
     */
    String ASSET_REGION_GENERAL_MANAGER_APPROVE_ID = "ASSET_REGION_GENERAL_MANAGER_APPROVE_ID";

    /**
     * 区域公司发起门店经理
     */
    String QY_COMPANY_INITIATE_STORE_MANAGER_APPROVE_KEY = "QY_COMPANY_INITIATE_STORE_MANAGER_APPROVE_KEY";
    /**
     * 区域公司发起资管主管
     */
    String QY_COMPANY_INITIATE_ASSET_MANAGER_APPROVE_KEY = "QY_COMPANY_INITIATE_ASSET_MANAGER_APPROVE_KEY";
    /**
     * 区域公司发起品质管理师
     */
    String QY_COMPANY_INITIATE_QUALITY_MANAGER_APPROVE_KEY = "QY_COMPANY_INITIATE_QUALITY_MANAGER_APPROVE_KEY";
    /**
     * 区域公司发起资管副总
     */
    String QY_COMPANY_INITIATE_ASSET_VICE_PRESIDENT_APPROVE_KEY = "QY_COMPANY_INITIATE_ASSET_VICE_PRESIDENT_APPROVE_KEY";
    /**
     * 区域公司发起总经理
     */
    String QY_COMPANY_INITIATE_PRESIDENT_APPROVE_KEY = "QY_COMPANY_INITIATE_PRESIDENT_APPROVE_KEY";
    String ZFQY_COMPANY_INITIATE_PRESIDENT_APPROVE_KEY = "ZFQY_COMPANY_INITIATE_PRESIDENT_APPROVE_KEY";

    /**
     * 资管区域总经理
     */
    String ASSET_REGION_GENERAL_MANAGER_APPROVE_KEY = "ASSET_REGION_GENERAL_MANAGER_APPROVE_KEY";
    /**
     * 资管副总
     */
    String TSASSET_VICE_PRESIDENT_APPROVE_ID = "TSASSET_VICE_PRESIDENT_APPROVE_ID";
    /**
     * 大区总经理
     */
    String TSREGION_GENERAL_MANAGER_APPROVE_ID = "TSREGION_GENERAL_MANAGER_APPROVE_ID";
    /**
     * 品质管理师
     */
    String TSQUALITY_MANAGER_APPROVE_ID = "TSQUALITY_MANAGER_APPROVE_ID";
    /**
     * 资管经理
     */
    String TSASSET_MANAGER_APPROVE_ID = "TSASSET_MANAGER_APPROVE_ID";
    /**
     * 运营总监
     */
    String TSOPERATION_DIRECTOR_APPROVE_ID = "TSOPERATION_DIRECTOR_APPROVE_ID";
    /**
     * 执行副总裁
     */
    String TSEXECUTIVE_VICE_PRESIDENT_APPROVE_ID = "TSEXECUTIVE_VICE_PRESIDENT_APPROVE_ID";


    /**
     * 大区总经理
     */
    String FZTSREGION_GENERAL_MANAGER_APPROVE_ID = "FZTSREGION_GENERAL_MANAGER_APPROVE_ID";
    /**
     * 品质管理师
     */
    String FZTSQUALITY_MANAGER_APPROVE_ID = "FZTSQUALITY_MANAGER_APPROVE_ID";
    /**
     * 资管经理
     */
    String FZTSASSET_MANAGER_APPROVE_ID = "FZTSASSET_MANAGER_APPROVE_ID";

    /**
     * 执行副总裁
     */
    String FZTSEXECUTIVE_VICE_PRESIDENT_APPROVE_ID = "FZTSEXECUTIVE_VICE_PRESIDENT_APPROVE_ID";
}
