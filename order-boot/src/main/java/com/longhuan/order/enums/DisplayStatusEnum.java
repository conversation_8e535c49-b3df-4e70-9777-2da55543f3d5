package com.longhuan.order.enums;

import com.longhuan.order.statemachine.enums.States;

/**
 * 订单展示状态枚举
 *
 * <AUTHOR>
 * @date 2024/7/4
 */
public enum DisplayStatusEnum {

    // 预审阶段
    PENDING_ENTRY("待录入"),
    PENDING_AUTHORIZATION("待授权"),
    PENDING_PRE_APPROVAL("待预审"),
    PENDING_FUND_PRE_APPROVAL("待资方预审"),
    PENDING_PRODUCT_SELECTION("待选产品"),

    // 提报阶段
    PENDING_SUBMISSION("待提报"),
    UNDER_REVIEW("审核中"),
    CUSTOMER_CONFIRMATION("待客户补录"),

    // 签约阶段
    PENDING_SIGNATURE("待签约"),
    CONTRACT_COMPLETED("已签约"),

    // 请款阶段
    PENDING_PAYMENT_REQUEST("请款审核中"),
    CONTRACT_REVIEW_PENDING("合同岗请款审核中"),
    FUND_PAYMENT_REQUEST_PENDING("资方请款审核中"),

    // 放款阶段
    PAYMENT_PROCESSING("放款中"),
    PAYMENT_SUCCESS("已放款");

    private final String displayValue;

    DisplayStatusEnum(String displayValue) {
        this.displayValue = displayValue;
    }

    public String getDisplayValue() {
        return displayValue;
    }

    /**
     * 根据 States 获取对应的展示状态
     */
    public static DisplayStatusEnum fromState(States state, Integer nextNode) {
        if (state == null) return null;

        return switch (state) {
            case BUSINESS_ADDED_INFO -> nextNode > state.getNode() ? CUSTOMER_CONFIRMATION : PENDING_SUBMISSION;
            case QUALITY_INSPECTION, QUALITY_INSPECTION_FINISH, STORE_EVALUATION, OVERALL_REVIEW,
                 RISK_FIRST_APPROVE_ASSIGN, RISK_FIRST_APPROVE, FUNDS_FINAL_APPROVE -> UNDER_REVIEW;
            case CUSTOMER_APPOINTMENT -> nextNode > state.getNode() ? PENDING_SIGNATURE : PENDING_SUBMISSION;
            case CUSTOMER_CONFIRM -> CUSTOMER_CONFIRMATION;
            case PAYMENT_APPLY_INFORMATION -> PENDING_PAYMENT_REQUEST;
            case PAYMENT_CONTRACT_APPROVAL -> CONTRACT_REVIEW_PENDING;
            case FUNDS_PAYMENT_APPROVAL -> FUND_PAYMENT_REQUEST_PENDING;
            case FUNDS_PAYMENT_PROCESS -> PAYMENT_PROCESSING;
            case PAYMENT_SUCCESS -> PAYMENT_SUCCESS;
            default -> UNDER_REVIEW;
        };
    }

}
