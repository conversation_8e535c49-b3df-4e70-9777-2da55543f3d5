package com.longhuan.order.statemachine.action;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.longhuan.approve.api.pojo.dto.FundApproveFinalDTO;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.entity.DeptQuotaEntity;
import com.longhuan.order.pojo.entity.DeptQuotaRecordEntity;
import com.longhuan.order.pojo.entity.OrderAmountEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.service.OrderService;
import com.longhuan.order.service.impl.OrderSendMessageImpl;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 状态转移动作，在转移进行的时候触发的动作
 */
@Slf4j
@Component
public class RiskFinalApproveAction extends OrderNodeAction implements Action<States, Events> {
    private final OrderService orderService;
    private final OrderSendMessageImpl orderSendMessage;
    private final ApproveFeign approveFeign;

    public RiskFinalApproveAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper, OrderService orderService,
                                  OrderSendMessageImpl orderSendMessage, ApproveFeign approveFeign) {
        super(orderInfoMapper, orderNodeRecordMapper);
        this.orderService = orderService;
        this.orderSendMessage = orderSendMessage;
        this.approveFeign = approveFeign;
    }

    @Override
    public void execute(StateContext<States, Events> context) {


        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();
        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);

        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event,
                currentNode, nextNode);
        // 1. 修改订单表状态
        if (Boolean.TRUE.equals(target.getId().getValid())) {
            saveOrderNode(orderId, nextNode);
        }

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);
        try {
            // 3. 发送消息
            orderSendMessage.sendMessage(orderId, currentNode, context.getEvent().getCode());
            // 0. 资方终审流程
            orderService.pushFundApprovalFinal(orderId);
        } catch (Exception e) {
            log.error("风控终审推送资方终审流程异常 orderId:{} ,error:{}", orderId, e.getMessage(), e);
        }

    }




}