package com.longhuan.xxl.job;

import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.xxl.feign.ApproveFeign;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 资方 还款计划逾期更新
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FundSyncOverdueRepaymentHandler {
    private final ApproveFeign approveFeign;

    @XxlJob("fundSyncOverdueRepaymentHandler")
    
    public ReturnT<String> fundSyncOverdueRepaymentHandler() {
        log.info("fundSyncOverdueRepaymentHandler started");

        try {
            approveFeign.fundSyncOverdueRepayment(List.of(FundEnum.YING_FENG, FundEnum.FU_MIN,
                    FundEnum.CHANG_YIN,FundEnum.ZHONG_HENG_TONG_HUI,FundEnum.ZHONG_HENG, FundEnum.LAN_HAI));
        } catch (Exception e) {
            log.error("fundSyncOverdueRepaymentHandler error", e);
            return ReturnT.FAIL;
        }

        log.info("fundSyncOverdueRepaymentHandler completed successfully");
        return ReturnT.SUCCESS;
    }

}
