package com.longhuan.order.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.approve.api.pojo.dto.MortgageStatusQueryDTO;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.pojo.dto.MortgageOrdersDTO;
import com.longhuan.order.pojo.dto.OrderArrivedDTO;
import com.longhuan.order.pojo.entity.FinalFundInfoEntity;
import com.longhuan.order.pojo.entity.OrderFundAreaEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.vo.CancelMortgageVO;
import com.longhuan.order.pojo.vo.MortgageOrdersListsVO;
import com.longhuan.order.pojo.vo.OrderInfoVO;
import com.longhuan.order.pojo.vo.OrderMortgageVO;
import com.longhuan.order.service.FinalFundInfoService;
import com.longhuan.order.service.FinalFundInfoServices;
import com.longhuan.order.service.OrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 抵押页面
 */
@RestController
@RequestMapping("/api/v1/finalFundInfo")
@RequiredArgsConstructor
@Api("资方抵押接口")
public class FinalFundInfoController {

    private final FinalFundInfoService finalFundInfoService;
    private final ApproveFeign approveFeign;
    private final OrderService orderService;
    private final FinalFundInfoServices finalFundInfoServices;

    /**
     *   查询资方抵押完成接口
     * @param mortgageOrdersDTO
     * @param loginUser
     * @return
     */
    @PostMapping("/getMortgageStateFList")
    public Result<Page<MortgageOrdersListsVO>> etFinalFundInfoByMortgageState(@RequestBody MortgageOrdersDTO mortgageOrdersDTO,
                                                                              @CurrentUser LoginUser loginUser) {
        return Result.success(finalFundInfoService.getFinalFundInfoByMortgageState(mortgageOrdersDTO,loginUser));
    }

    /**
     * 获取资方抵押H5/小程序页面  解抵
     */
    @ApiOperation(value = "获取富民抵押H5/小程序页面-解抵")
    @GetMapping("/fuMinInitialTokenForReleasePledge/{orderId}")
    public Result<String> getMortgageUrlForReleasePledge(@PathVariable("orderId") Integer orderId) {
        return Result.success(finalFundInfoService.getMortgageUrlForReleasePledge(orderId));
    }

    /**
     *   解抵撤销
     */
    @ApiOperation(value = "解抵撤销")
    @GetMapping("/getMortgageUrlForReleasePledgeCancel/{orderId}")
    public Result<Boolean> getMortgageUrlForReleasePledgeCancel(@PathVariable("orderId") Integer orderId) {
        return Result.success(finalFundInfoService.getMortgageUrlForReleasePledgeCancel(orderId));
    }

    /**
     *   抵押结果查询
     */
    @ApiOperation(value = "抵押结果查询")
    @PostMapping("/fuMinMortgageStatusQuery")
    public Result<String> fuMinMortgageStatusQuery(@RequestBody MortgageStatusQueryDTO mortgageStatusQueryDTO) {
        return Result.success(finalFundInfoService.mortgageStatusQuery(mortgageStatusQueryDTO));
    }

    @ApiOperation(value = "定时任务更新解抵查询结果")
    @PostMapping("/updateMortgageCancelQuery")
    public Result<String> updateMortgageCancelQuery() {
        finalFundInfoService.updateMortgageCancelQuery();
        return Result.success();
    }

    /**
     *   抵押撤销
     */
    @ApiOperation(value = "抵押撤销")
    @GetMapping("/cancelMortgageStatus/{orderId}")
    public Result<Boolean> cancelMortgageStatus(@PathVariable("orderId") Integer orderId) {
        return Result.success(finalFundInfoService.cancelMortgageStatus(orderId));
    }

    /**
     *   蓝海抵押详情
     */
    @ApiOperation(value = "蓝海抵押详情")
    @GetMapping("/queryInfoById/{orderId}")
    public Result<OrderMortgageVO> queryInfoById(@PathVariable("orderId") Integer orderId, Integer type) {
        return Result.success(finalFundInfoService.queryInfoById(orderId, type));
    }

    /**
     *   蓝海抵押详情更新
     */
    @ApiOperation(value = "蓝海抵押详情更新")
    @PostMapping("/arrivedEdit")
    public Result<Boolean> arrivedEdit(@RequestBody OrderArrivedDTO orderArrivedDTO) {
        return Result.success(finalFundInfoService.arrivedEdit(orderArrivedDTO));
    }

    /**
     * 给业务员发送抵押修改  5000节点后
     */
    @ApiOperation(value = "给业务员发送抵押修改")
    @PostMapping("/sendMessageEditInfo")
    public Result<Boolean> sendMessageEditInfo(@RequestBody OrderInfoEntity orderInfo) {
        return Result.success(orderService.sendMessageEditInfo(orderInfo));
    }


    @ApiOperation(value = "中恒/富民/蓝海/长银资方业务员发送抵押修改")
    @PostMapping("/sendMessageEditInfoAndFund")
    public Result<Boolean> sendMessageEditInfoAndFund(@RequestBody OrderInfoEntity orderInfo) {
        return Result.success(finalFundInfoServices.sendMessageEditInfoAndFund(orderInfo));
    }
    /**
     * 推送钉钉消息
     */
    @ApiOperation(value = "资方推送钉钉失败消息")
    @GetMapping("/dingDingMessagePush")
    public Result<Boolean> dingDingMessagePush() {
         return Result.success(finalFundInfoServices.dingDingMessagePush());
    }
    /**
     * 解抵申请详情-抵押信息
     */
    @ApiOperation(value = "解抵申请详情-抵押信息")
    @GetMapping("/cancelMortgageDetails/{orderId}")
    public Result<CancelMortgageVO> cancelMortgageDetails(@PathVariable("orderId") Integer orderId) {
        return Result.success(finalFundInfoServices.cancelMortgageDetails(orderId));
    }
    /**
     * 抵押失败原因
     */
    @ApiOperation(value = "抵押失败原因")
    @PostMapping("/failReason")
    public Result<FinalFundInfoEntity> failReason(@RequestBody FinalFundInfoEntity finalFundInfo) {
        return Result.success(finalFundInfoService.failReason(finalFundInfo));
    }

    /**
     * 查询资方地址
     */
    @ApiOperation(value = "查询资方地址")
    @GetMapping("/queryFundArea")
    public Result<List<Map<String, Object>>> queryFundArea(@RequestParam Integer fundId) {
        return  Result.success(finalFundInfoService.queryFundArea(fundId));
    }

}
