package com.longhuan.order.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 门店GPS分期和收费策略配置VO
 *
 * <AUTHOR>
 * @date 2025/01/01
 */
@Data
@Accessors(chain = true)
public class StoreGpsConfigVO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 门店ID，关联lh_dept表
     */
    @ApiModelProperty(value = "门店ID，关联lh_dept表")
    private Integer deptId;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String deptName;

    /**
     * 是否支持分期（0：不支持，1：支持）
     */
    @ApiModelProperty(value = "是否支持分期（0：不支持，1：支持）")
    private Integer is_installment;

    /**
     * 是否支持免费（0：不支持，1：支持）
     */
    @ApiModelProperty(value = "是否支持免费（0：不支持，1：支持）")
    private Integer is_free;
}
