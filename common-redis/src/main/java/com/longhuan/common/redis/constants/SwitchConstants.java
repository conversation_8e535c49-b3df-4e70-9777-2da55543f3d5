package com.longhuan.common.redis.constants;

/**
 * 开关常数
 *
 * <AUTHOR>
 * @date 2024/10/31
 */
public class SwitchConstants {
    /**
     * 风险审批金额校验开关
     */
    public static final String RISK_APPROVE_AMOUNT = "RISK_APPROVE_AMOUNT";

    /**
     * 盈峰赎回规则管控
     */
    public static final String YINGFENG_REPURCHASE_RULE = "YINGFENG_REPURCHASE_RULE";
    /**
     * 资方线上签约
     */
    public static final String ONLINE_SIGN = "ONLINE_SIGN";
    /**
     * 富民申请类型
     */
    public static final String FU_MIN_APPLY_TYPE = "FU_MIN_APPLY_TYPE";

    /**
     * 富民消费贷放款开关
     */
    public static final String FU_MIN_CONSUMER_PAY_LIMIT ="FU_MIN_CONSUMER_PAY_LIMIT";
    /**
     * ocr调用类型
     */
    public static final String OCR_CALL_TYPE = "OCR_CALL_TYPE";

    /**
     * 富民赎回规则管控
     */
    public static final String FUMIN_REPURCHASE_RULE = "FUMIN_REPURCHASE_RULE";

    private SwitchConstants() {
        throw new IllegalStateException("Constants  class");
    }

    /**
     * 富民车辆保险管控
     */
    public static final String FU_MIN_INSURANCE = "FU_MIN_INSURANCE";

    /**
     * 富民GPS安装确认书管控
     */
    public static final String FU_MIN_GPS_INSTALL = "FU_MIN_GPS_INSTALL";

    /**
     * 富民资产证明管控
     */
    public static final String FU_MIN_FINANCIAL_STATEMENT = "FU_MIN_FINANCIAL_STATEMENT";

    /**
     * 通汇赎回规则管控
     */
    public static final String TONGHUI_REPURCHASE_RULE = "TONGHUI_REPURCHASE_RULE";

    /**
     * 蓝海赎回规则管控
     */
    public static final String LAN_HAI_REPURCHASE_RULE = "LAN_HAI_REPURCHASE_RULE";


    /**
     * 智能AI额度报告资方开关
     */
    public static final String INTELLIGENT_AI_QUOTA = "INTELLIGENT_AI_QUOTA";

    /**
     * 蓝海同步数字化贷后开关
     */
    public static final String SYNC_LAN_HAI_TO_ShuZiHua_PAY_AFTER = "SYNC_LAN_HAI_TO_ShuZiHua_PAY_AFTER";

    /**
     * 智能AI报告按钮开关
     */
    public static final String INTELLIGENT_AI_REPORT_BUTTON = "INTELLIGENT_AI_REPORT_BUTTON";

    /**
     * 智能AI合同报告资方开关
     */
    public static final String SMART_CONTRACT_REPORT = "SMART_CONTRACT_REPORT";
    /**
     * 单点登录开关
     */
    public static final String SINGLE_SIGN_ON = "SINGLE_SIGN_ON";
    /**
     * 多点登录人员
     */
    public static final String MULTI_LOGIN_PERSONNEL = "MULTI_LOGIN_PERSONNEL";
    /**
     * 费用申请导出条数限制
     */
    public static final String EXPORT_EXPENSE_APPLICATIONS_LIMIT = "EXPORT_EXPENSE_APPLICATIONS_LIMIT";

    /**
     * 通汇放款抵押时间开关
     */
    public static final String TONGHUI_PAY_MORTGAGE_TIME = "TONG_HUI_MORTGAGE_TIME";

    /**
     * 资方贷后补件有效期  1线上  2线下
     */
    public static final String FUND_PATCHES_EXPIRE_TIME = "FUND_PATCHES_EXPIRE_TIME";
    /**
     * 资方贷后补件提醒天数  1线上  2线下
     */
    public static final String FUND_PATCHES_REMIND_DAYS = "FUND_PATCHES_REMIND_DAYS";
}

