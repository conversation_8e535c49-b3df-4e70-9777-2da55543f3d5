package com.longhuan.order.controller;

import com.longhuan.approve.api.pojo.dto.changyin.ChangYinUpdateAccountDTO;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.ManageBankAccountSignEntity;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.CustomerAppointmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户预约接口
 *
 * <AUTHOR>
 * @date 2024/08/16
 */
@Api(tags = "客户预约接口")
@RestController
@RequestMapping("/api/v1/customer/appointment")
@RequiredArgsConstructor
public class CustomerAppointmentController {

    /**
     * 客户预约服务
     */
    private final CustomerAppointmentService customerAppointmentService;

    @ApiOperation(value = "业务详情")
    @GetMapping("/business_detail")
    public Result<BusinessDetailVO> businessDetail(@RequestParam("orderId") Integer orderId) {
        return Result.success(customerAppointmentService.businessDetail(orderId));
    }

    /**
     * 签约详情信息
     *
     * @param orderId 订单 ID
     * @return {@link Result }<{@link AppointmentSignDetailVO }>
     */
    @ApiOperation(value = "签约详情")
    @GetMapping("/sign_detail")
    public Result<AppointmentSignDetailVO> signDetail(@RequestParam("orderId") Integer orderId) {
        return Result.success(customerAppointmentService.signDetail(orderId));
    }

    /**
     * 修改签约方式
     * 
     * @param updateSignTypeDTO 包含订单ID和签约类型的DTO对象
     * @return Result<Boolean> 返回修改结果，成功返回true，失败返回false
     */
    @ApiOperation(value = "修改签约方式")
    @PostMapping("/update_sign_type")
    public Result<Boolean> updateSignType(@RequestBody @Validated UpdateSignTypeDTO updateSignTypeDTO) {
        return Result.success(customerAppointmentService.updateSignType(updateSignTypeDTO.getOrderId(), updateSignTypeDTO.getSignType()));
    }

    /**
     * 救签约信息
     *
     * @param appointmentSignDetailVO 约会签约详情信息VO
     * @return {@link Result }<{@link AppointmentSignDetailVO }>
     */
    @ApiOperation(value = "保存签约信息")
    @PostMapping("/save_sign_info")
    public Result<Boolean> saveSignInfo(@Validated @RequestBody AppointmentSignDetailVO appointmentSignDetailVO, @CurrentUser LoginUser loginUser) {
        return Result.success(customerAppointmentService.saveSignInfo(appointmentSignDetailVO, loginUser));
    }

    /**
     * 线上面签发起派单
     * @param orderId
     * @return
     */
    @ApiOperation(value = "线上面签发起派单")
    @PostMapping("/online_signing_assignments")
    public Result<Boolean> onlineSigningAssignments(@RequestParam Integer orderId){
        return Result.success(customerAppointmentService.onlineSigningAssignments(orderId));
    }

    /**
     * 抵押详情信息
     *
     * @param orderId 订单 ID
     * @return {@link Result }<{@link AppointmentMortgageDetailVO }>
     */
    @ApiOperation(value = "抵押详情")
    @GetMapping("/mortgage_detail")
    public Result<AppointmentMortgageDetailVO> mortgageDetail(@RequestParam("orderId") Integer orderId) {
        return Result.success(customerAppointmentService.mortgageDetail(orderId));
    }

    /**
     * 保存抵押信息
     *
     * @param appointmentMortgageDetailDTO 约会抵押详情信息VO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "保存抵押信息")
    @PostMapping("/save_mortgage_info")
    public Result<Boolean> saveMortgageInfo(@Validated @RequestBody AppointmentMortgageDetailDTO appointmentMortgageDetailDTO,@CurrentUser LoginUser loginUser) {
        return Result.success(customerAppointmentService.saveMortgageInfo(appointmentMortgageDetailDTO, loginUser));
    }

//    /**
//     * GPS费详情信息
//     *
//     * @param orderId 订单 ID
//     * @return {@link Result }<{@link AppointmentGpsFeeDetailVO }>
//     */
//    @ApiOperation(value = "GPS费用详情")
//    @GetMapping("/gps_fee_detail")
//    public Result<AppointmentGpsFeeDetailVO> gpsFeeDetail(@RequestParam("orderId") Integer orderId) {
//        return Result.success(customerAppointmentService.gpsFeeDetail(orderId));
//    }

//    /**
//     * 救GPS费
//     *
//     * @param appointmentGpsFeeDetailVO 预约GPS详情信息VO
//     * @return {@link Result }<{@link Boolean }>
//     */
//    @ApiOperation(value = "保存GPS费用信息")
//    @PostMapping("/save_gps_fee")
//    public Result<Boolean> saveGpsFee(@Validated @RequestBody AppointmentGpsFeeDetailVO appointmentGpsFeeDetailVO, @CurrentUser LoginUser loginUser) {
//        return Result.success(customerAppointmentService.saveGpsFee(appointmentGpsFeeDetailVO, loginUser));
//    }

    /**
     * GPS详情信息
     *
     * @param orderId 次序id
     * @return {@link Result }<{@link AppointmentGpsDetailVO }>
     */
    @ApiOperation(value = "GPS详情")
    @GetMapping("/gps_detail")
    public Result<AppointmentGpsDetailVO> gpsDetail(@RequestParam("orderId") Integer orderId) {
        return Result.success(customerAppointmentService.gpsDetail(orderId));
    }

    /**
     * 保存GPS信息
     *
     * @param appointmentGpsDetailDTO 预约GPS详情信息DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "保存GPS信息")
    @PostMapping("/save_gps_info")
    public Result<Boolean> saveGpsInfo(@Validated @RequestBody AppointmentGpsDetailDTO appointmentGpsDetailDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(customerAppointmentService.saveGpsInfo(appointmentGpsDetailDTO, loginUser));
    }

    /**
     * 通联支付接口
     *
     * @param orderId 通联支付接口
     * @return {@link Result }<{@link TongLianPayVO }>
     */
    @GetMapping("/pay/{orderId}")
    public Result<TongLianPayVO> pay(@PathVariable("orderId") Integer orderId){
        return Result.success(customerAppointmentService.customerPay(orderId));
    }
    /**
     * 判断订单是否为电销线上订单
     */
    @GetMapping("/isOnlineOrder")
    public Result<Boolean> isOnlineOrder(@RequestParam("orderId") Integer orderId){
        return Result.success(customerAppointmentService.isOnlineOrder(orderId));
    }
    /**
     * 通联支付-金蝶
     *
     * @param tongLianPayDTO 通联支付 DTO
     * @return {@link Result }<{@link TongLianPayVO }>
     */
    @PostMapping("/kingDeePay")
    public Result<TongLianPayResultVO> kingDeePay(@RequestParam TongLianPayDTO tongLianPayDTO){
        return Result.success(customerAppointmentService.kingDeePay(tongLianPayDTO));
    }

    /**
     * 通联支付查询-金蝶
     *
     * @param kingDeeSearchDTO 通联支付查询 DTO
     * @return {@link Result }<{@link TongLianPayVO }>
     */
    @PostMapping("/kingDeeSearchTranx")
    public Result<TongLianSearchVO> kingDeeSearchTranx(@RequestParam KingDeeSearchDTO kingDeeSearchDTO){
        return Result.success(customerAppointmentService.kingDeeSearchTranx(kingDeeSearchDTO));
    }

    /**
     * TL Close 交易
     *
     * @param tlCloseTransactionDTO TL 关闭交易 DTO
     * @return {@link Result }<{@link TlCloseTransactionVO }>
     */
    @PostMapping("/tlCloseTransaction")
    public Result<TlCloseTransactionVO> tlCloseTransaction(@RequestParam TlCloseTransactionDTO tlCloseTransactionDTO){
        return Result.success(customerAppointmentService.tlCloseTransaction(tlCloseTransactionDTO.getReqsn(),tlCloseTransactionDTO.getOrderId()));
    }

    /**
     * 通联查询支付状态接口
     *
     * @param orderId 通联查询支付状态接口
     * @return {@link Result }<{@link String }>
     */
    @GetMapping("/searchTranx/{orderId}")
    public Result<String> searchTranx(@PathVariable("orderId") Integer orderId, @CurrentUser LoginUser loginUser) {
        return Result.success(customerAppointmentService.searchTranx(orderId, loginUser));
    }

    @ApiOperation(value = "支付回调")
    @PostMapping(value = "/payCallback",consumes = "application/x-www-form-urlencoded;charset=UTF-8")
    public String payCallback(@Validated @ModelAttribute("tlPatCallbackDTO") TlPatCallbackDTO tlPatCallbackDTO){
        return customerAppointmentService.payCallback(tlPatCallbackDTO);
    }

    /**
     * 短信触发
     *
     * @param smsTriggeredDTO 短信触发DTO smsTriggeredDTO
     * @return {@link String }
     */
    @PostMapping("/SMSTriggered")
    public Result<String> smsTriggered(@Validated @RequestBody SMSTriggeredDTO smsTriggeredDTO) {
        return Result.success(customerAppointmentService.smsTriggered(smsTriggeredDTO));
    }

    /**
     * 客户经理短信触发
     *
     * @param smsTriggeredDTO 短信触发DTO smsTriggeredDTO
     * @return {@link String }
     */
    @PostMapping("/manageSMSTriggered")
    public Result<String> manageSMSTriggered(@Validated @RequestBody SMSTriggeredDTO smsTriggeredDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(customerAppointmentService.manageSMSTriggered(smsTriggeredDTO,loginUser));
    }

    /**
     * 通联绑卡
     *
     * @param tlSignDTO TL 签名 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "通联绑卡")
    @PostMapping("/paymentSign")
    public Result<String> paymentSign(@Validated @RequestBody TLSignDTO tlSignDTO) {
        return Result.success(customerAppointmentService.bankCardSign(tlSignDTO));
    }

    /**
     * 客户经理通联绑卡
     *
     * @param tlSignDTO TL 签名 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "通联绑卡")
    @PostMapping("/managePaymentSign")
    public Result<String> manPaymentSign(@Validated @RequestBody TLSignDTO tlSignDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(customerAppointmentService.manageBankCardSign(tlSignDTO,loginUser));
    }

    /**
     * 客户换卡
     *
     * @param orderId TL 签名 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "客户换卡")
    @GetMapping("/replaceCard/{orderId}")
    public Result<Boolean> replaceCard(@PathVariable("orderId") Integer orderId) {
        return Result.success(customerAppointmentService.replaceCard(orderId));
    }

    /**
     * 更新订单文件
     *
     * @param updateOrderFileDTO 更新订单文件 DTO
     * @return {@link Result }<{@link List }<{@link OrderFileVo }>>
     */
    @PostMapping("/updateOrderFile")
    public Result<Boolean> updateOrderFile(@RequestBody UpdateOrderFileDTO updateOrderFileDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(customerAppointmentService.updateOrderFile(updateOrderFileDTO,loginUser));
    }

    /**
     * 查询绑卡信息
     *
     * @param orderId 订单 ID
     * @return {@link Result }<{@link SearchCardListVO }>
     */
    @ApiOperation(value = "查询绑卡信息")
    @GetMapping("/searchCardList/{orderId}")
    public Result<SearchCardListVO> searchCardList(@PathVariable("orderId") Integer orderId) {
        return Result.success(customerAppointmentService.searchCardList(orderId));
    }

    /**
     * 通联绑卡解约（客户经理）
     *
     * @param tlSignDTO TL 签名 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "通联绑卡解约")
    @PostMapping("/paymentTermination")
    public Result<String> paymentTermination(@Validated @RequestBody TLSignDTO tlSignDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(customerAppointmentService.bankCardTermination(tlSignDTO,loginUser));
    }

    /**
     * 通联绑卡解约（客户）
     *
     * @param bankCardTerminationDTO TL 签名 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "通联绑卡解约")
    @PostMapping("/customerPaymentTermination")
    public Result<String> customerPaymentTermination(@Validated @RequestBody CustomerCardTerminationDTO bankCardTerminationDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(customerAppointmentService.customerPaymentTermination(bankCardTerminationDTO,loginUser));
    }

    /**
     * 设置默认卡
     *
     * @param smsTriggeredDTO 设置默认卡 smsTriggeredDTO
     * @return {@link String }
     */
    @PostMapping("/setDefaultCard")
    public Result<Boolean> setDefaultCard(@Validated @RequestBody SMSTriggeredDTO smsTriggeredDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(customerAppointmentService.setDefaultCard(smsTriggeredDTO,loginUser));
    }

    /**
     * 查询绑卡信息
     *
     * @param loginUser TL 签名 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "查询绑卡信息")
    @GetMapping("/searchBankCardNum")
    public Result<List<ManageBankAccountSignEntity>> searchBankCardNum(@CurrentUser LoginUser loginUser) {
        return Result.success(customerAppointmentService.searchBankCardNum(loginUser));
    }

    /**
     * 查询绑卡数量
     *
     * @param userId TL 签名 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "查询绑卡数量")
    @GetMapping("/searchCardNum")
    public Result<Integer> searchCardNum(@RequestParam Integer userId) {
        return Result.success(customerAppointmentService.searchCardNum(userId));
    }

    /**
     * 获取开户行
     *
     * @param bankCardNum 银行卡号
     * @return {@link Result }<{@link String }>
     */
    @ApiOperation(value = "获取开户行")
    @GetMapping("/getBankCardName")
    public Result<String> searchCardNum(@RequestParam String bankCardNum) {
        return Result.success(customerAppointmentService.getBankCardName(bankCardNum));
    }

    @PostMapping("/searchTranxScheduled")
    public Result<String> searchTranxScheduled() {
        return Result.success(customerAppointmentService.searchTranxScheduled());
    }
    @ApiOperation(value = "修改放款方式")
    @PostMapping("/modifyTheLoanMethod")
    public Result<Boolean> modifyTheLoanMethod(@Validated @RequestBody ModifyTheLoanMethodDTO modifyTheLoanMethodDTO) {
        return Result.success(customerAppointmentService.modifyTheLoanMethod(modifyTheLoanMethodDTO));
    }

//    @ApiOperation(value = "长银更换卡号")
//    @PostMapping("/modifyAccount")
//    public Result<Boolean> modifyAccount(@RequestBody ChangYinUpdateAccountDTO changYinUpdateAccountDTO) {
//        return Result.success(customerAppointmentService.modifyAccount(changYinUpdateAccountDTO));
//    }

}
