package com.longhuan.order.converter;

import com.longhuan.order.pojo.entity.PreFddFinishFileEntity;
import com.longhuan.order.pojo.vo.PreFddFinishFileVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 已签署文件转换器
 *
 * <AUTHOR>
 * @date 2024/07/24
 */
@Mapper(componentModel = "spring")
public interface PreFddFinishFileConverter {


    List<PreFddFinishFileVO> entityList2VoList(List<PreFddFinishFileEntity> list);
}
