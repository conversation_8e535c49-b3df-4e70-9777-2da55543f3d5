package com.longhuan.approve.boot.enums.yingfeng;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.longhuan.approve.boot.enums.PaymentReconMethodEnum;
import com.longhuan.approve.boot.enums.RepaymentReconFlagEnum;
import com.longhuan.approve.boot.enums.RepaymentReconTypeEnum;
import com.longhuan.common.core.enums.dict.DictContactRelation;
import lombok.AllArgsConstructor;
import lombok.Getter;

public interface YingFengDictEnum {

    @Getter
    enum Relationship {
        /**
         * 亲属关系类型
         */
        OTHER_RELATIVE(1, "其他亲属"),
        COLLEAGUE(2, "同事"),
        MOTHER(3, "母亲"),
        FATHER(4, "父亲"),
        FRIEND(5, "朋友"),
        SPOUSE(6, "配偶"),
        CHILDREN(7, "子女"),
        BROTHER(8, "哥哥"),
        YOUNGER_BROTHER(9, "弟弟"),
        OLDER_SISTER(10, "姐姐"),
        YOUNGER_SISTER(11, "妹妹");

        private final Integer id;
        private final String desc;

        Relationship(Integer id, String desc) {
            this.id = id;
            this.desc = desc;
        }

        public static Relationship fromGlobalRelationship(DictContactRelation globalRelationship) {
            if (globalRelationship == null) {
                return OTHER_RELATIVE;
            }

            return switch (globalRelationship) {
                case FATHER -> FATHER;
                case MOTHER -> MOTHER;
                case CHILDREN -> CHILDREN;
                case COLLEAGUE -> COLLEAGUE;
                case SPOUSE -> SPOUSE;
                case FRIEND -> FRIEND;
                default -> OTHER_RELATIVE;
            };
        }
    }

    @Getter
    enum LoanQueryStatus {

        WAIT("0", "放款中"),
        PASS("1", "已放款"),
        REJECT("2", "放款失败");;
        @EnumValue
        @JsonValue
        private final String code;
        private final String description;

        LoanQueryStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static LoanQueryStatus fromCode(String code) {
            for (LoanQueryStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    @Getter
    enum CreditStatus {
        WAIT("0", "审核中"),
        PASS("1", "审核通过"),
        REJECT("2", "审核拒绝");;
        @EnumValue
        @JsonValue
        private final String code;
        private final String description;

        CreditStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static CreditStatus fromCode(String code) {
            for (CreditStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }

    }


    @Getter
    @AllArgsConstructor
    enum RepaymentType {
        ADVANCE_SETTLEMENT("0", "提前结清"),
        NORMAL_REPAYMENT("1", "按期正常还款"),
        OVERDUE_REPAYMENT("2", "逾期还款");

        @EnumValue
        @JsonValue
        private final String code;
        private final String description;

        public static RepaymentType fromCode(String code) {
            for (RepaymentType type : RepaymentType.values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }

        public static RepaymentReconTypeEnum fromGlobalRepaymentType(String code) {
            RepaymentType repaymentType = RepaymentType.fromCode(code);
            if (repaymentType == null) {
                return null;
            }
            return switch (repaymentType) {
                case ADVANCE_SETTLEMENT -> RepaymentReconTypeEnum.ADVANCE_SETTLEMENT;
                case NORMAL_REPAYMENT -> RepaymentReconTypeEnum.NORMAL_REPAYMENT;
                case OVERDUE_REPAYMENT -> RepaymentReconTypeEnum.OVERDUE_REPAYMENT;
            };
        }
    }

    @Getter
    @AllArgsConstructor
    enum RepaymentFlag {
        CUSTOMER_REPAYMENT("1", "客户还款"),
        COMPENSATION_REPAYMENT("2", "代偿当期/整笔回购");

        @EnumValue
        @JsonValue
        private final String code;
        private final String description;

        public static RepaymentFlag fromCode(String code) {
            for (RepaymentFlag flag : RepaymentFlag.values()) {
                if (flag.getCode().equals(code)) {
                    return flag;
                }
            }
            return null;
        }

        public static RepaymentReconFlagEnum fromGlobalRepaymentFlag(String code) {
            RepaymentFlag repaymentFlag = RepaymentFlag.fromCode(code);
            if (repaymentFlag == null) {
                return null;
            }
            return switch (repaymentFlag) {
                case CUSTOMER_REPAYMENT -> RepaymentReconFlagEnum.CUSTOMER_REPAYMENT;
                case COMPENSATION_REPAYMENT -> RepaymentReconFlagEnum.COMPENSATION_REPAYMENT;
            };
        }
    }


    @Getter
    @AllArgsConstructor
    enum RepaymentMethod {
        BAOFU("1", "宝付代扣清分"),
        YIBAO("2", "易宝代扣清分"),
        BANK_TRANSFER("3", "银行转账"),
        KUAIQIAN("4", "快钱代扣清分"),
        ZHONGJIN("5", "中金"),
        TONGLIAN("6", "通联代扣清分");

        @EnumValue
        @JsonValue
        private final String code;
        private final String description;

        public static RepaymentMethod fromCode(String code) {
            for (RepaymentMethod method : RepaymentMethod.values()) {
                if (method.getCode().equals(code)) {
                    return method;
                }
            }
            return null;
        }

        public static PaymentReconMethodEnum fromGlobalPaymentMethod(String code) {
            RepaymentMethod paymentMethod = RepaymentMethod.fromCode(code);
            if (paymentMethod == null) {
                return null;
            }
            return switch (paymentMethod) {
                case BAOFU -> PaymentReconMethodEnum.BAOFU;
                case YIBAO -> PaymentReconMethodEnum.YIBAO;
                case BANK_TRANSFER -> PaymentReconMethodEnum.BANK_TRANSFER;
                case KUAIQIAN -> PaymentReconMethodEnum.KUAIQIAN;
                case ZHONGJIN -> PaymentReconMethodEnum.ZHONGJIN;
                case TONGLIAN -> PaymentReconMethodEnum.TONGLIAN;
            };
        }
    }

    @Getter
    @AllArgsConstructor
    enum DeductApplyRepayType{
        EXPIRED("1", "到期"),
        OVERDUE("2", "逾期"),
        EARLY("0", "提前");
        @EnumValue
        @JsonValue
        private final String code;
        private final String description;

    }


    /**
     * 代扣状态
     * 0-处理中，10-代扣成功，20-代扣失败
     */
    @Getter
    enum DeductionStatus {
        PROCESSING("0", "处理中"),
        SUCCESS("10", "代扣成功"),
        FAIL("20", "代扣失败");

        @EnumValue
        @JsonValue
        private final String code;
        private final String description;

        DeductionStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static DeductionStatus fromCode(String code) {
            for (DeductionStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
           return null;
        }
    }

    @Getter
    @AllArgsConstructor
    enum TransStatus {
        USED(0, "已被使用不可发起还款"),
        CAN_BE_INITIATED(1, "可被发起还款"),
        SUCCESS(2, "还款成功");
        @EnumValue
        @JsonValue
        private final Integer code;
        private final String description;

    }

    @Getter
    @AllArgsConstructor
    enum TransferRepayType {
        PREPAYMENT(0, "提前还款"),
        REGULAR_OR_OVERDUE(1, "正常/逾期还款");

        @EnumValue
        @JsonValue
        private final Integer code;
        private final String description;
    }


    /**
     * 合同状态（0：待车主签署;20：待代理人签署;30：待金融机构签署;40：签署完成）
     */
    @Getter
    enum ZrContractStatus {
        /**
         * 待车主签署
         */
        PENDING_OWNER_SIGN(0, "待车主签署"),

        /**
         * 待代理人签署
         */
        PENDING_AGENT_SIGN(20, "待代理人签署"),

        /**
         * 待金融机构签署
         */
        PENDING_FINANCIAL_SIGN(30, "待金融机构签署"),

        /**
         * 签署完成
         */
        SIGN_COMPLETED(40, "签署完成");
        @EnumValue
        @JsonValue
        private final Integer code;
        private final String description;

        ZrContractStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public static ZrContractStatus fromCode(String code) {
            for (ZrContractStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }


    /**
     *交科所状态(O：未受理;A：预受理成功;B：验证成功;D：受理成功;S：已归档;F：退办（验证失败）)
     */
    @Getter
    enum ZrChannelStatus {

            /**
             * 未受理
             */
            NOT_ACCEPTED("O", "未受理"),

            /**
             * 预受理成功
             */
            PRE_ACCEPTANCE_SUCCESS("A", "预受理成功"),

            /**
             * 验证成功
             */
            VALIDATION_SUCCESS("B", "验证成功"),

            /**
             * 受理成功
             */
            ACCEPTANCE_SUCCESS("D", "受理成功"),

            /**
             * 已归档
             */
            ARCHIVED("S", "已归档"),

            /**
             * 退办（验证失败）
             */
            RETURNED("F", "退办（验证失败）");

            @EnumValue
            @JsonValue
            private final String code;
            private final String description;

            ZrChannelStatus(String code, String description) {
                this.code = code;
                this.description = description;
            }
            public static ZrChannelStatus fromCode(String code) {
                for (ZrChannelStatus status : values()) {
                    if (status.code.equals(code)) {
                        return status;
                    }
                }
                throw new IllegalArgumentException("Unknown channel status code: " + code);
            }


    }
}
