package com.longhuan.order.statemachine;

import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.Getter;

@Getter
public class StatesException extends BusinessException {
    private States source;
    private States target;
    private Events event;

    public StatesException(String s) {
        super(s);
    }

    public StatesException(String s, Events event) {
        super(s);
        this.event = event;
    }

    public StatesException(String s, States source, Events event) {
        super(s);
        this.source = source;
        this.event = event;
    }

    public StatesException(String s, States source, States target, Events event) {
        super(s);
        this.source = source;
        this.target = target;
        this.event = event;
    }
}
