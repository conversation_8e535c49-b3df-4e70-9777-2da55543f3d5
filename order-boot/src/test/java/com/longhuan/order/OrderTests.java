package com.longhuan.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.format.FastDateFormat;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.order.feign.DigitalizeFeign;
import com.longhuan.order.kingdee.feign.KingdeeFeign;
import com.longhuan.order.kingdee.pojo.ProductSaveDTO;
import com.longhuan.order.kingdee.pojo.TheFinalReviewRejectedAllOfThemDTO;
import com.longhuan.order.mapper.DigitalOutsourcingOrderEntityMapper;
import com.longhuan.order.mapper.PreOcrVehicleInfoMapper;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.pojo.vo.chepaida.ChepaidaCarLatestPositionVo;
import com.longhuan.order.service.*;
import com.longhuan.order.service.impl.OrderApproveDistributeServiceImpl;
import com.longhuan.order.service.impl.PreApprovalApplyInfoServiceImpl;
import com.longhuan.order.util.DigitalizeZhongxinEncryptUtil;
import com.longhuan.order.util.LocalDateTimeAdapter;
import com.longhuan.order.util.SignatureUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.util.LinkedMultiValueMap;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

@Slf4j
@EnableAspectJAutoProxy
@SpringBootTest(classes = OrderApplication.class)
public class OrderTests {
    @Resource
    OrderSendMessage orderSendMessage;
    @Resource
    PreManagerApprovalService preManagerApprovalService;
    @Resource
    PreApprovalApplyInfoServiceImpl preApprovalApplyInfoServiceImpl;
    @Resource
    FundFeeService fundFeeService;
    @Resource
    OrderPayApplicationService orderPayApplicationService;
    @Autowired
    private ReviewService reviewService;
    @Autowired
    private PreOcrVehicleInfoMapper preOcrVehicleInfoMapper;
    @Autowired
    private OrderService orderService;
    @Resource
    private OrderApproveDistributeServiceImpl orderApproveDistributeService;
    @Resource
    private DigitalizeFeign digitalizeFeign;
    @Autowired
    private ZhongXinService zhongXinService;
    @Resource
    private KingdeeFeign kingdeeFeign;
    @Autowired
    private KingdeeOutsourcingService kingdeeOutsourcingService;
    @Autowired
    private DigitalizeZhongxinEncryptUtil digitalizeZhongxinEncryptUtil;
    @Autowired
    private DigitalOutsourcingOrderEntityMapper digitalOutsourcingOrderEntityMapper;
    @Autowired
    private OrderVehicleGpsLogService orderVehicleGpsLogService;

    @Test
    void testView() {
        test(null, null, 60476, 188, "18032830741");
    }

    private PreOcrIdentityCardEntity test(IdentityCardDTO identityCardDTO, LoginUser loginUserInfo, Integer clueId, Integer servicerId, String phone) {

        return new PreOcrIdentityCardEntity().setPreId(4444);
    }

    //    @Test
//    void testReview2() {
//        for(int i=0;i<10;i++)
//        {
//            Integer randomRiskUserId = orderService.getRandomRiskUserId();
//
//            log.info("randomRiskUserId: {}", randomRiskUserId);
//        }
//    }
    @Test
    void testReview() {
        List<PreOcrVehicleInfoEntity> infoEntityList = preOcrVehicleInfoMapper.selectList(new LambdaQueryWrapper<PreOcrVehicleInfoEntity>()
                .eq(PreOcrVehicleInfoEntity::getPreId, 665));
        log.info("getMileage: {} ", infoEntityList.get(0).getMileage());
    }

    private PreApprovalApplyInfoEntity test2(PreApprovalApplyInfoEntity preApprovalApplyInfoEntity) {
        return preApprovalApplyInfoEntity;
    }

    @Test
    void testSendMessage() {

        orderSendMessage.sendWeChatMsg(339, 2700, 1);
    }

    @Test
    void testSendMessage2() {

        List<PreFundInfoVO> preFundInfoVOS = preManagerApprovalService.preFundList(1103,new LoginUser());
        log.info("preFundInfoVOS: {}", preFundInfoVOS);

    }

    @Test
    void testSendMessage3() {

        StoreProductDTO infoDTO = new StoreProductDTO();
        infoDTO.setPreId(1109);
        infoDTO.setFundId(11);
        preApprovalApplyInfoServiceImpl.storeProductList(infoDTO, null);

    }

    @Test
    void guaranteeFeeCalc() {
        fundFeeService.guaranteeFeeCalc(YearMonth.now().minusMonths(1));
    }

    @Test
    void testSendMessage4() {
        String request = "{\"pageNum\":1,\"pageSize\":20,\"paymentDetails\":\"\",\"payeeAccount\":\"\",\"feeType\":14,\"orderNumber\":\"\",\"paymentTime\":\"\",\"vehicleNumber\":\"\"}";
        PayApplicationPageListDTO dto = JSONUtil.toBean(request, PayApplicationPageListDTO.class);
        LoginUser loginuser = new LoginUser();
        loginuser.setUserId(315);
        loginuser.setRoleIds(List.of(1));
        loginuser.setScopes("data:all");
        Page<OrderPayApplyListVO> orderPayApplyListVOPage = orderPayApplicationService.payApplicationPageList(dto, loginuser);
        log.info("orderPayApplyListVOPage: {}", orderPayApplyListVOPage.getRecords());

    }

    @Test
    void testOrderAssign() {
        orderApproveDistributeService.distributeOrder();
    }

    @Test
    void testOrderAssign2() {
//        TheFinalReviewRejectedAllOfThemDTO dto =new TheFinalReviewRejectedAllOfThemDTO();
//        dto.setOrderId(1201);
//        dto.setFinalApprovalStatus(1);
        ProductSaveDTO dto = new ProductSaveDTO();
        dto.setOrderId(1201);
        dto.setCapitalId(5);
        dto.setCapitalName("盈峰");
        dto.setProductId(52);
        dto.setProductName("龙优C-36");
        String jsonPrettyStr = JSONUtil.toJsonStr(dto, new JSONConfig().setIgnoreNullValue(false));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, "kIpceo0x7YtbekBY", iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, "DTQ6ViaQsbj43eZ5DivhlrsPBUhYj7alC6fA1IDKaOzl9ahyh21evZejoGt7s3PY");
        String s = kingdeeFeign.productSave(body, sign, iv, timestamp);
        System.out.println("s = " + s);
    }

    @Test
    void test() {
        //  通知数字化开始审批
        DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
        digitalizeWeiwaiOrderStatusDTO.setOrder_id("836538859522650113");
        digitalizeWeiwaiOrderStatusDTO.setStatus(1);
        String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
        log.info("通知数字化开始审批 {}", s);
    }

    @Test
    void test22() throws JsonProcessingException {
        //  通知数字化开始审批
        CaseInfoEntity caseInfoEntity = new CaseInfoEntity();
        caseInfoEntity.setDigitalOrderId("826092917967216641");
        caseInfoEntity.setCaseNo("LJ_AJ_831172870378668033");
        Result<String> stringResult = zhongXinService.digitalizePushCaseToZhongXin(caseInfoEntity);
        log.info("通知数字化开始审批 {}", stringResult);
    }

    @Test
    void testStoreApp() {
        OrderApproveDTO orderApproveDTO = new OrderApproveDTO();
        orderApproveDTO.setCurrentNode(1200);
        Page<OrderApproveListVO> orderApproveListVOPage = orderService.pageApproveList(orderApproveDTO, new LoginUser().setUserId(12354)
                .setRoleIds(List.of(121)).setDeptIds(List.of(123)));
        log.info("orderApproveListVOPage: {}", orderApproveListVOPage.getRecords());

    }

    @Test
    void test2(){
        // 原始JSON字符串（需要转义引号）
        String jsonStr = "[{\"attachmentName\":\"左前45度\",\"attachmentLists\":[{\"attachmentName\":\"绿植.jpg\",\"resourceId\":\"2b0f1a810e2c44df8846e1df1bbf77b2.jpg\"}]},{\"attachmentName\":\"仪表盘\",\"attachmentLists\":[{\"attachmentName\":\"绿植.jpg\",\"resourceId\":\"1f588e5fee5046dc969caa5f96578400.jpg\"}]},{\"attachmentName\":\"活体检测照片\",\"attachmentLists\":[{\"attachmentName\":\"绿植.jpg\",\"resourceId\":\"dad077a2233048049543d2d1ef7cb15c.jpg\"}]},{\"attachmentName\":\"中控台\",\"attachmentLists\":[{\"attachmentName\":\"绿植.jpg\",\"resourceId\":\"8af92507efe44b46a4373cada33d2bf2.jpg\"}]},{\"attachmentName\":\"人车合影\",\"attachmentLists\":[{\"attachmentName\":\"绿植.jpg\",\"resourceId\":\"5bf11fcd484d4fc3b4d8bb6bb87549c4.jpg\"}]},{\"attachmentName\":\"车身铭牌\",\"attachmentLists\":[{\"attachmentName\":\"绿植.jpg\",\"resourceId\":\"f602778a24c94a1690f941d7a13fbd20.jpg\"}]},{\"attachmentName\":\"右后45度\",\"attachmentLists\":[{\"attachmentName\":\"绿植.jpg\",\"resourceId\":\"1d6ce6c837ce4e84a2834638d05c8fd0.jpg\"}]},{\"attachmentName\":\"发动机舱\",\"attachmentLists\":[{\"attachmentName\":\"绿植.jpg\",\"resourceId\":\"669e50a9fcd745dea20dd676278b4ae5.jpg\"},{\"attachmentName\":\"微信图片_20241218092030 - 副本 - 副本 - 副本 - 副本 - 副本.jpg\",\"resourceId\":\"cfb7573eaf4541c898ff0f79d35943f4.jpg\"}]}]";

        ObjectMapper objectMapper = new ObjectMapper();

        try {
            // 解析JSON到对象列表
            List<AttachmentListDTO> groups = objectMapper.readValue(
                    jsonStr,
                    new TypeReference<List<AttachmentListDTO>>() {}
            );

            // 遍历解析结果
            for (AttachmentListDTO group : groups) {
                System.out.println("外层 attachmentName: " + group.getAttachmentName());
                System.out.println("包含 " + group.getAttachmentLists().size() + " 个附件：");

                for (AttachmentListDTO.AttachmentList item : group.getAttachmentLists()) {
                    System.out.println("  附件名称: " + item.getAttachmentName());
                    System.out.println("  资源ID: " + item.getResourceId());
                    System.out.println("  --------------------");
                }
                System.out.println("====================================");
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }
    @Test
    void test3(){
        long num = 1744685400;
        Instant instant = Instant.ofEpochMilli(num*1000);
        LocalDateTime localrejectionTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        log.info("localrejectionTime: {}", localrejectionTime);
    }
    @Test
    void test4(){
        // 通知金蝶状态
        KingdeeStatusRequestDTO kingdeeStatusRequestDTO = new KingdeeStatusRequestDTO();
        kingdeeStatusRequestDTO.setCode(1);
        kingdeeStatusRequestDTO.setMsg("成功");
        KingdeeRequestData data = new KingdeeRequestData();
        data.setOrderNumber("201708361");
        data.setFlowType("委外保全");
        data.setOutsourceOrg("众信天下投资（深圳）有限公司");
        data.setOutsourceTime("2025-04-28 09:15:42.045");
        data.setOutsourceStatus(1);
        kingdeeStatusRequestDTO.setData(data);
        String kingdeeorderStatus = kingdeeOutsourcingService.pushKingdeeorderStatus(kingdeeStatusRequestDTO);
        log.info("通知金蝶状态 {}", kingdeeorderStatus);

    }
    // 获取当前时间
    LocalDateTime now = LocalDateTime.now();
    @Test
    void test55(){


        // 转换为 Unix 时间戳（秒级）
        long timestamp = now.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
        String jsonObject=new JSONObject()
                .set("case_no","HF"+"********7889361921")
                .set("receivable_amount",   71400)
                .set("collect_name","")
                .set("payment_name","")
                .set("payment_date",timestamp)
                .set("collect_bankcard","")
                .set("payment_bankcard","")
                .set("payment_method","结清款")
                .set("opening_bank","")
                .set("payment_bank","")
                .set("payment_img","")
                .set("remark","通过")
                .set("check_at", Instant.now().getEpochSecond())
                .set("status",2)
                .set("check_opinion","通过")
                .set("full_name","")
                .set("req_no",now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()+"********7889361921").toString();
        log.info("OrderPayApplicationServiceImpl digitalizeFeign.weiwaiDigitalizeZhongXinPaymentNotification:{}",jsonObject);
        LinkedMultiValueMap<String, Object> objectObjectLinkedMultiValueMap = new LinkedMultiValueMap<>();
        objectObjectLinkedMultiValueMap.add("DATA",digitalizeZhongxinEncryptUtil.encode(jsonObject));
        log.info("OrderPayApplicationServiceImpl digitalizeFeign.weiwaiDigitalizeZhongXinPaymentNotification:{}",objectObjectLinkedMultiValueMap);

    }
    @Test
    void test66(){

        String payload = new JSONObject()
                .set("order_id", "********7889361921")
                .set("name", "李雷")
                .set("receivable_amount", 71400)
                .set("is_help", 2)
                .set("status", 0)
                .set("is_verify", 0)
                .set("is_gps", 0)
                .set("req_no",now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()+"********7889361921")
                .set("usage_days",118)
                .toString();
        log.info("OrderPayApplicationServiceImpl digitalizeFeign.weiwaiDigitalizeZhongXinPaymentNotification:{}",payload);
        LinkedMultiValueMap<String, Object> objectLinkedMultiValueMap = new LinkedMultiValueMap<>();
        objectLinkedMultiValueMap.add("datainfo",digitalizeZhongxinEncryptUtil.encode(payload));
        log.info("OrderPayApplicationServiceImpl digitalizeFeign.weiwaiDigitalizeZhongXinPaymentNotification:{}",objectLinkedMultiValueMap);

    }
    @Test
    void test77(){
        String filePath = "C:\\Users\\<USER>\\Desktop\\新建 XLSX 工作表.xlsx"; // 替换为实际文件路径

        try {
            List<List<Object>> data = readExcel(filePath);

            // 打印读取的数据
            for (List<Object> row : data) {
                for (Object cell : row) {
                    System.out.print(cell + "\t");

                }
                System.out.println();
            }

            System.out.println("Excel文件读取成功！共读取 " + data.size() + " 行数据。");

        } catch (IOException e) {
            System.err.println("读取Excel文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    private static final FastDateFormat DATE_FORMAT = FastDateFormat.getInstance("yyyy-MM-dd");
    /**
     * 读取Excel文件中的数据
     * @param filePath Excel文件路径
     * @return 包含所有行和单元格数据的列表
     * @throws IOException 如果文件读取失败
     */
    public static List<List<Object>> readExcel(String filePath) throws IOException {
        List<List<Object>> data = new ArrayList<>();
        FileInputStream file = null;
        Workbook workbook = null;

        try {
            file = new FileInputStream(new File(filePath));

            // 根据文件扩展名判断使用哪种工作簿
            if (filePath.toLowerCase().endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(file); // 处理.xlsx格式
            } else if (filePath.toLowerCase().endsWith(".xls")) {
                workbook = new HSSFWorkbook(file); // 处理.xls格式
            } else {
                throw new IllegalArgumentException("不支持的文件格式: " + filePath);
            }

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 迭代每一行
            Iterator<Row> rowIterator = sheet.iterator();
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();

                // 跳过空行
                if (isRowEmpty(row)) {
                    continue;
                }

                List<Object> rowData = new ArrayList<>();

                // 迭代每一个单元格
                for (int i = 0; i < row.getLastCellNum(); i++) {
                    Cell cell = row.getCell(i);
                    rowData.add(getCellValue(cell));
                }

                data.add(rowData);
            }

        } finally {
            // 关闭资源
            if (workbook != null) workbook.close();
            if (file != null) file.close();
        }

        return data;
    }

    /**
     * 判断行是否为空
     */
    private static boolean isRowEmpty(Row row) {
        if (row == null) return true;

        for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取单元格的值，并转换为合适的Java类型
     */
    private static Object getCellValue(Cell cell) {
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();

            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    // 处理日期类型
                    return DATE_FORMAT.format(cell.getDateCellValue());
                } else {
                    // 处理数字类型
                    double numericValue = cell.getNumericCellValue();
                    // 判断是否为整数
                    if (numericValue == (long) numericValue) {
                        return (long) numericValue;
                    } else {
                        return numericValue;
                    }
                }

            case BOOLEAN:
                return cell.getBooleanCellValue();

            case FORMULA:
                // 处理公式，获取公式计算结果
                FormulaEvaluator evaluator = cell.getSheet().getWorkbook().getCreationHelper().createFormulaEvaluator();
                CellValue cellValue = evaluator.evaluate(cell);

                switch (cellValue.getCellType()) {
                    case STRING:
                        return cellValue.getStringValue();
                    case NUMERIC:
                        return cellValue.getNumberValue();
                    case BOOLEAN:
                        return cellValue.getBooleanValue();
                    default:
                        return null;
                }

            case BLANK:
                return null;

            default:
                return cell.toString();
        }
    }


    @Test
    void test88(){
        String filePath = "C:\\Users\\<USER>\\Desktop\\新建 XLSX 工作表.xlsx"; // 替换为你的Excel文件路径

        try (FileInputStream fis = new FileInputStream(new File(filePath));
             Workbook workbook = getWorkbook(fis, filePath)) {

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 遍历每一行
            for (Row row : sheet) {
                // 遍历当前行的每一列
                for (Cell cell : row) {
                    // 获取单元格数据
                    String cellValue = getCellValueAsString(cell);
                    System.out.print(cellValue + "\t");
                }
                System.out.println(); // 换行
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    // 根据文件扩展名创建对应的Workbook对象
    private static Workbook getWorkbook(FileInputStream fis, String filePath) throws IOException {
        if (filePath.endsWith(".xlsx")) {
            return new XSSFWorkbook(fis); // 处理.xlsx文件
        } else if (filePath.endsWith(".xls")) {
            return new HSSFWorkbook(fis); // 处理.xls文件
        } else {
            throw new IllegalArgumentException("不支持的文件格式");
        }
    }

    // 将单元格数据转换为字符串类型
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        CellType cellType = cell.getCellType();
        switch (cellType) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 获取数值
                    double numericValue = cell.getNumericCellValue();
                    // 如果数值是整数类型（没有小数部分），转换为长整数再转字符串
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            default:
                return cell.toString();
        }
    }
    @Test
    void test99(){
        String filePath = "C:\\Users\\<USER>\\Desktop\\测试excal.xlsx"; // 你的Excel文件路径

        List<ExcelData> excelDataList = readExcelFile(filePath);

    }
    private static List<ExcelData> readExcelFile(String filePath) {
        List<ExcelData> dataList = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(new File(filePath));
             Workbook workbook = getWorkbook(fis, filePath)) {

            Sheet sheet = workbook.getSheetAt(0);

            for (Row row : sheet) {
                if (row.getRowNum() == 0) {
                    continue; // 跳过表头行
                }

                String column1 = getCellValueAsString(row.getCell(0));
                String column2 = getCellValueAsString(row.getCell(1));
                String column3 = getCellValueAsString(row.getCell(2));
                String column4 = getCellValueAsString(row.getCell(3));
                String column5 = getCellValueAsString(row.getCell(4));
                String column6 = getCellValueAsString(row.getCell(5));

                ExcelData excelData = new ExcelData();
                excelData.setXvhao(column1);
                excelData.setCompany(column2);
                excelData.setAccount(column3);
                excelData.setLink(column4);
                excelData.setPhone(column5);
                excelData.setAddress(column6);
                dataList.add(excelData);
            }
            System.out.println(dataList);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return dataList;
    }
@Test
void test100(){
   Integer installCarId=726347;
    Integer digitalOrderId=1971;
    orderVehicleGpsLogService.getCarInfo(installCarId,digitalOrderId);

}

@Test
    void test111(){
    String carInfo="{\"code\":0,\"message\":\"成功\",\"result\":[{\"carId\":726347,\"bindUserAccount\":\"胡悦钦\",\"carOwnerName\":\"杨建\",\"carOwnerPhone\":\"***********\",\"cardType\":\"IDCARD\",\"carOwnerIdCard\":\"360121198812030017\",\"carPlateNumber\":\"沪C5Q7R8\",\"carModel\":\"奔驰奔驰E级2017款 奔驰E级 E 300 L 运动豪华型\",\"carEngineNumber\":\"********\",\"carFrameNumber\":\"LE4ZG4JB9HL122709\",\"carType\":\"\",\"imeis\":\"***************,***************,***************,***************\",\"createTime\":\"2023-05-09 17:53:43\",\"updateTime\":\"2023-05-09 17:53:47\",\"equtList\":[{\"imei\":\"***************\",\"deviceName\":\"EV26-QC-54729\",\"activationTime\":\"2023-05-09 17:40:41\",\"expiration\":\"2034-05-09 00:00:00\",\"mcType\":\"EV26-QC\",\"sim\":null,\"driverPhone\":null,\"vehicleIcon\":\"automobile\",\"equipType\":\"WIRED\",\"mcTypeUseScope\":\"aotomobile\",\"reMark\":null,\"installTime\":\"2023-05-09\"},{\"imei\":\"***************\",\"deviceName\":\"GT740-13392\",\"activationTime\":\"2023-05-09 17:21:35\",\"expiration\":\"2033-05-09 00:00:00\",\"mcType\":\"GT740\",\"sim\":null,\"driverPhone\":null,\"vehicleIcon\":\"automobile\",\"equipType\":\"WIRELESS\",\"mcTypeUseScope\":\"aotomobile\",\"reMark\":null,\"installTime\":\"2023-05-09\"},{\"imei\":\"***************\",\"deviceName\":\"GT740-04284\",\"activationTime\":\"2023-05-09 11:12:30\",\"expiration\":\"2033-05-09 00:00:00\",\"mcType\":\"GT740\",\"sim\":null,\"driverPhone\":null,\"vehicleIcon\":\"automobile\",\"equipType\":\"WIRELESS\",\"mcTypeUseScope\":\"aotomobile\",\"reMark\":null,\"installTime\":\"2023-05-09\"},{\"imei\":\"***************\",\"deviceName\":\"K90-85619\",\"activationTime\":\"2023-05-08 11:41:06\",\"expiration\":\"2033-05-08 00:00:00\",\"mcType\":\"K90\",\"sim\":null,\"driverPhone\":null,\"vehicleIcon\":\"automobile\",\"equipType\":\"WIRELESS\",\"mcTypeUseScope\":\"aotomobile\",\"reMark\":null,\"installTime\":\"2023-05-09\"}],\"overallSum\":\"259300.00\",\"loanMonth\":\"36\",\"loanSum\":200000.0000,\"salesman\":\"张春东\",\"homeAddress\":\"浙江省杭州市富阳市,七里香溪-别墅园东北323米\",\"workAddress\":\"浙江省杭州市西湖区马塍路,崇文公寓西南31米\",\"loanExpirationDate\":\"\",\"sex\":0,\"carColor\":\"黑色\",\"loanDate\":\"2023-05-09\",\"repaymentType\":\"STAGES\",\"carStatus\":\"UNDERWAY\",\"carImageList\":[],\"imeiImageList\":[{\"pid\":7385585,\"fileUrl\":\"http://cdncpd.jimicloud.com/image_1683625263953_5a787b01acc64f5b8ad60a78fa1dc9b7.jpg\"}]}]}";
//    Gson gson =  new GsonBuilder().registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter()).create();
    Gson gson =  new Gson();

    CarInfoResponseVO response = gson.fromJson(carInfo, CarInfoResponseVO.class);

    log.info("OrderVehicleGpsLogServiceImpl.getCarInfo response:{}", response);
    if (response.getCode() == 0){
        List<CarInfoResponseVO.CarDetail> result = response.getResult();
        if (result != null && result.size() > 0){
            CarInfoResponseVO.CarDetail carDetail = result.get(0);
            List<CarInfoResponseVO.Equipment> equtList = carDetail.getEqutList();
            if (CollUtil.isNotEmpty(equtList)){
                for (CarInfoResponseVO.Equipment equipment : equtList) {
                    log.info("OrderVehicleGpsLogServiceImpl.getCarInfo equipment:{}", equipment);
                }
            }


            DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = digitalOutsourcingOrderEntityMapper.selectById(1971);
            digitalOutsourcingOrderEntity.setGpsNumber(carDetail.getImeis());
            digitalOutsourcingOrderEntity.setEqutList(JSONUtil.toJsonStr(carDetail.getEqutList()));
            digitalOutsourcingOrderEntityMapper.updateById(digitalOutsourcingOrderEntity);
        }
    }

}
@Test
void test112(){
    ChepaidaCarLatestPositionVo chepaidaCarLatestPositionVo = orderVehicleGpsLogService.chepaidaCarLatestPosition("***************");
    log.info("OrderVehicleGpsLogServiceImpl.getCarInfo chepaidaCarLatestPositionVo:{}", chepaidaCarLatestPositionVo);
}

}




