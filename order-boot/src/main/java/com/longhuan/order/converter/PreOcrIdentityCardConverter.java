package com.longhuan.order.converter;

import com.longhuan.order.pojo.entity.PreOcrIdentityCardEntity;
import com.longhuan.order.pojo.vo.PreOcrIdentityCardVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.time.LocalDate;
import java.time.Period;

/**
 * ocr识别身份证信息表
 *
 * <AUTHOR>
 * @date 2024/08/16
 */
@Mapper(componentModel = "spring")
public interface PreOcrIdentityCardConverter {
    @Mappings({
            @Mapping(target = "age", source = 	"entity.birthDate"),
    })
    PreOcrIdentityCardVO dtoToVo (PreOcrIdentityCardEntity entity);


    /**
     * 计算年龄
     *
     * @param birthDate 出生日期
     * @return 年龄
     */
    default  Integer calculatedAge(LocalDate birthDate) {
        LocalDate currentDate = LocalDate.now();
        Period age = Period.between(birthDate, currentDate);
        return age.getYears();
    }

}
