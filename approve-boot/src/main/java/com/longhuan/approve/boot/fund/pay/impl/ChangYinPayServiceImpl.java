package com.longhuan.approve.boot.fund.pay.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.longhuan.approve.api.pojo.dto.FundAmountChangeDTO;
import com.longhuan.approve.api.pojo.dto.FundPayDTO;
import com.longhuan.approve.api.pojo.dto.changyin.ChangYinResBodyDTO;
import com.longhuan.approve.api.pojo.dto.zhongheng.coppercardept.HengTongCopperCarDeptMortgageCancelDTO;
import com.longhuan.approve.boot.converter.ChangYinConverter;
import com.longhuan.approve.boot.enums.FundApiRecordStatusEnum;
import com.longhuan.approve.boot.enums.FundPaymentStatusEnum;
import com.longhuan.approve.boot.enums.InitStatusEnums;
import com.longhuan.approve.boot.feign.MessageFeign;
import com.longhuan.approve.boot.feign.OrderFeign;
import com.longhuan.approve.boot.fund.changyin.ChangYinService;
import com.longhuan.approve.boot.fund.pay.FundPayStrategy;
import com.longhuan.approve.boot.mapper.CustomerMortgageInfoMapper;
import com.longhuan.approve.boot.mapper.FinalFundInfoMapper;
import com.longhuan.approve.boot.mapper.FundUndoMortgageInfoMapper;
import com.longhuan.approve.boot.mapper.OrderInfoMapper;
import com.longhuan.approve.boot.pojo.dto.FundApiRecordDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.ChangYinLoanQueryDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.response.ChangYinLimitAdjustQueryResDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.response.ChangYinLoanApplyResDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.response.ChangYinLoanQueryResDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.response.ChangYinPayHandlerDTO;
import com.longhuan.approve.boot.pojo.entity.CustomerMortgageInfoEntity;
import com.longhuan.approve.boot.pojo.entity.FinalFundInfoEntity;
import com.longhuan.approve.boot.pojo.entity.FundUndoMortgageInfoEntity;
import com.longhuan.approve.boot.pojo.entity.OrderInfoEntity;
import com.longhuan.approve.boot.service.HengTongCopperCarDeptService;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.MsgConstants;
import com.longhuan.common.core.enums.PreFundResultEnum;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.user.pojo.dto.MessageContent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service("PAY_CHANG_YIN")
@RequiredArgsConstructor
public class ChangYinPayServiceImpl implements FundPayStrategy {

    private final ChangYinService  changYinService;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final CustomerMortgageInfoMapper customerMortgageInfoMapper;
    private final OrderFeign orderFeign;
    private final ChangYinConverter changYinConverter;
    private final FundUndoMortgageInfoMapper fundUndoMortgageInfoMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final HengTongCopperCarDeptService hengTongCopperCarDeptService;
    private final MessageFeign messageFeign;


    @Override
    public FundApiRecordDTO pay(FundPayDTO fundPayDTO) {
        FundApiRecordDTO fundApiRecordDTO = new FundApiRecordDTO();
        try {
            Integer orderId = fundPayDTO.getOrderId();
            String remark = "";
            FundApiRecordStatusEnum status = FundApiRecordStatusEnum.NOT_CALLED;
            // 构建请求参数
            ChangYinResBodyDTO<ChangYinLoanApplyResDTO> loanResult = changYinService.loanApplyV2ByOrderId(orderId);
            if (ChangYinResBodyDTO.isSuccess(loanResult)){
                if (ObjUtil.isNotNull(loanResult.getBody().getApplCde())){
                    String loanSeq = loanResult.getBody().getLoanSeq();
                    String loanNo = loanResult.getBody().getLoanNo();
                    String outLoanSeq = loanResult.getBody().getOutLoanSeq();

                    finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                            .set(FinalFundInfoEntity::getLoanBillNo, loanSeq)
                            .set(FinalFundInfoEntity::getLoanNo, loanNo)
                            .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                            .eq(FinalFundInfoEntity::getOrderId, orderId)
                            .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                            .eq(FinalFundInfoEntity::getLoanReqNo, outLoanSeq)
                    );

                    fundApiRecordDTO.setStatus(FundApiRecordStatusEnum.CALL_SUCCESS);
                    fundApiRecordDTO.setRemark("发起成功");
                }else {
                    String remark1 = null;
                    if (loanResult != null) {
                        remark1 = loanResult.getHead().getRespMsg();
                    } else {
                        remark1 = "长银发起失败";
                    }
                    if (ObjUtil.isNotNull(loanResult.getBody().getDnSts())){
                        remark1 += "放款失败原因："+loanResult.getBody().getLoanResultDesc();
                    }
                    fundApiRecordDTO.setStatus(FundApiRecordStatusEnum.CALL_FAILED);
                    fundApiRecordDTO.setRemark(remark1);
                }
            }else{
                fundApiRecordDTO.setStatus(FundApiRecordStatusEnum.CALL_FAILED);
                fundApiRecordDTO.setRemark(loanResult.getHead().getRespMsg());
            }
        } catch (BusinessException e) {
            log.info("ChangYinPayServiceImpl.pay BusinessException error: {}", e.getMessage(), e);
            fundApiRecordDTO.setStatus(FundApiRecordStatusEnum.CALL_FAILED);
            fundApiRecordDTO.setRemark(e.getMessage());
        } catch (Exception e) {
            log.error("ChangYinPayServiceImpl.pay Exception error: {}", e.getMessage(), e);
            fundApiRecordDTO.setStatus(FundApiRecordStatusEnum.CALL_FAILED);
            fundApiRecordDTO.setRemark("发起失败，系统异常");
        }
        return fundApiRecordDTO;
    }

    @Override
    public FundApiRecordDTO payResult(FundPayDTO fundPayDTO) {
        FundApiRecordStatusEnum status = FundApiRecordStatusEnum.NOT_CALLED;
        String remark = null;
        try {
            Integer orderId = fundPayDTO.getOrderId();
            FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(
                    Wrappers.<FinalFundInfoEntity>lambdaQuery()
                            .eq(FinalFundInfoEntity::getOrderId, orderId)
                            .eq(FinalFundInfoEntity::getStatus, InitStatusEnums.SUCCESS)
                            .eq(FinalFundInfoEntity::getFundResult, PreFundResultEnum.PASS)
                            .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                            .orderByDesc(FinalFundInfoEntity::getCreateTime)
                    , false
            );
            Assert.notNull(finalFundInfo, "未获取到长银申请信息！");

            //构建请求参数
            ChangYinLoanQueryDTO changYinLoanQueryDTO = new ChangYinLoanQueryDTO();
            changYinLoanQueryDTO.setApplCde(finalFundInfo.getCreditNo())
                    .setLoanSeq(finalFundInfo.getLoanBillNo())
                    .setOutLoanSeq(finalFundInfo.getLoanReqNo());
            log.info("发起长银放款申请查询请求参数：{}", JSONUtil.toJsonStr(changYinLoanQueryDTO));
            ChangYinResBodyDTO<ChangYinLoanQueryResDTO> queryResult = changYinService.loanQueryV2(changYinLoanQueryDTO);

            ChangYinResBodyDTO<ChangYinPayHandlerDTO> resultHandlerDTO = changYinConverter.payQueryTOHandlerDTO(queryResult);
            changYinService.updateOrderPaymentStatus(resultHandlerDTO);
        } catch (Exception e) {
            log.info("ChangYinPayServiceImpl.payResult Exception e:", e);
            status = FundApiRecordStatusEnum.CALL_FAILED;
        }
        return new FundApiRecordDTO().setStatus(status).setRemark(remark);
    }

    @Override
    public FundApiRecordDTO cancel(FundPayDTO fundPayDTO) {
        Integer orderId = fundPayDTO.getOrderId();
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
//                .ne(FinalFundInfoEntity::getStatus, InitStatusEnums.FAIL)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0));

        if (ObjUtil.isNull(finalFundInfo)) {
            log.info("ChangYinPayServiceImpl.cancel not found finalFundInfoEntity orderId:{}", orderId);
            return null;
        }

        cancelMortgage(orderId);

        if ((ObjUtil.equals(finalFundInfo.getFundResult(), PreFundResultEnum.PASS) || ObjUtil.equals(finalFundInfo.getFundResult(), PreFundResultEnum.WAIT))
                &&( ObjUtil.equals(finalFundInfo.getPaymentStatus(), FundPaymentStatusEnum.NONE) || ObjUtil.isNull(finalFundInfo.getPaymentStatus()))
        ) {
            try {
                changYinService.limitAdjustApplyV2ByOrderId(orderId);
                ChangYinResBodyDTO<ChangYinLimitAdjustQueryResDTO> queryResultDTO = changYinService.limitAdjustQueryV2ByOrderId(orderId);
                if (ChangYinResBodyDTO.isSuccess(queryResultDTO)) {
                    ChangYinLimitAdjustQueryResDTO bodyDTO = queryResultDTO.getBody();
                    String adjustStatus = bodyDTO.getAdjustStatus();
                    if (!StrUtil.equals(adjustStatus, "01") && !StrUtil.equals(adjustStatus, "03")) {
                        try {
                            OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
                            String  message = String.format("订单编号%s客户%s\n长银取消订单失败,原因:%s", orderInfo.getOrderNumber(), orderInfo.getCustomerName(), adjustStatus + bodyDTO.getAdjustStatusDesc());
                            List<String> receiver = new ArrayList<>();
                            receiver.add("17320803303");
                            receiver.add("16682227790");
                            receiver.add("18831137306");
                            for (String mobile : receiver) {
                                // 发送消息
                                MessageContent messageContent = new MessageContent()
                                        .setMsgType(MsgConstants.MSG_TEXT)
                                        .setSendType(MsgConstants.SEND_DD_NOTICE)
                                        .setContent(message)
                                        .setReceiver(mobile);

                                messageFeign.sendMessage(messageContent);
                            }
                        } catch (Exception e) {
                            log.error("ChangYinPayServiceImpl.cancel sendMessage orderId:{}, error: {}", orderId, e.getMessage(), e);
                        }

                    }

                    if (StrUtil.equals(adjustStatus, "03")) {
                        throw new BusinessException("撤销处理中，请稍后再试");
                    }
//                    if (StrUtil.equals(adjustStatus, "02") && !StrUtil.equals(bodyDTO.getAdjustStatusDesc(), "额度已失效")) {
                    if (StrUtil.equals(adjustStatus, "02")) {
                        throw new BusinessException("撤销失败" + ObjUtil.defaultIfNull(bodyDTO.getAdjustStatusDesc(), ""));
                    }
                    if (StrUtil.equals(adjustStatus, "99")) {
                        throw new BusinessException("撤销失败：掉单" + ObjUtil.defaultIfNull(bodyDTO.getAdjustStatusDesc(), ""));
                    }
                }
            } catch (BusinessException e) {
                throw e;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    /**
     * 抵押
     */
    private void cancelMortgage(Integer orderId) {
        CustomerMortgageInfoEntity customerMortgageInfo = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                .eq(CustomerMortgageInfoEntity::getMortgageType, 0)
                .orderByDesc(CustomerMortgageInfoEntity::getUpdateTime)
                .last("limit 1")
        );
        //线下不用管
        if (ObjUtil.isNotNull(customerMortgageInfo)){
            OrderInfoEntity infoEntity = orderInfoMapper.selectById(orderId);
            if (ObjUtil.isNotNull(infoEntity) && (ObjUtil.equals(infoEntity.getMortgageState(),0) || ObjUtil.equals(infoEntity.getMortgageState(),4))){
                return;
            }
            //查看解抵表中是否存在  如果存在 等待解抵完成
            FundUndoMortgageInfoEntity fundUndoMortgageInfoEntity = fundUndoMortgageInfoMapper.selectOne(new LambdaQueryWrapper<FundUndoMortgageInfoEntity>()
                    .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                    .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                    .eq(FundUndoMortgageInfoEntity::getCancel, 0)
            );
            log.info("ChangYinPayServiceImpl.cancel fundUndoMortgageInfoEntity: {}", JSONUtil.toJsonStr(fundUndoMortgageInfoEntity));
            if (ObjUtil.isNotNull(fundUndoMortgageInfoEntity) && StrUtil.equals(fundUndoMortgageInfoEntity.getOperateType(),"1")){
                //撤回中
                if (StrUtil.equals(fundUndoMortgageInfoEntity.getMortgageStatus(),"0") || StrUtil.equals(fundUndoMortgageInfoEntity.getMortgageStatus(),"4")){
                    return;
                }
                throw new BusinessException("请等待撤回结果");
            }
            if (ObjUtil.isNotNull(fundUndoMortgageInfoEntity) && StrUtil.equals(fundUndoMortgageInfoEntity.getOperateType(),"2")){
                //解抵中
                if (StrUtil.equals(fundUndoMortgageInfoEntity.getMortgageStatus(),"8") || StrUtil.equals(fundUndoMortgageInfoEntity.getMortgageStatus(),"9")){
                    return;
                }
                throw new BusinessException("请等待解抵结果");
            }
            if (ObjUtil.isNull(fundUndoMortgageInfoEntity)){
                //调用抵押撤销
                HengTongCopperCarDeptMortgageCancelDTO hengTongCopperCarDeptMortgageCancelDTO = new HengTongCopperCarDeptMortgageCancelDTO();
                hengTongCopperCarDeptMortgageCancelDTO.setOrderId(orderId);
                hengTongCopperCarDeptMortgageCancelDTO.setType("E");
                try {
                    String copperCarId = hengTongCopperCarDeptService.cancelOrder(hengTongCopperCarDeptMortgageCancelDTO);
                    log.info("ChangYinPayServiceImpl.cancel copperCarId: {}", copperCarId);
                } catch (Exception e) {
                    throw new BusinessException("撤销失败,请去发起抵押撤销或解抵");
                }
                throw new BusinessException("请等待抵押撤销结果");
            }
        }

    }

    @Override
    public FundApiRecordDTO amountChange(FundAmountChangeDTO fundAmountChangeDTO) {
        return null;
    }




}
