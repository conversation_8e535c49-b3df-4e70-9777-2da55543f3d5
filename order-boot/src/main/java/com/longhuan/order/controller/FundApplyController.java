package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.FundApplyConfirmDTO;
import com.longhuan.order.pojo.dto.FundApplyFundFinalApplyDTO;
import com.longhuan.order.pojo.dto.FundApplyMatchFundDTO;
import com.longhuan.order.pojo.dto.FundApplyQueryUseFundDTO;
import com.longhuan.order.pojo.vo.FundApplyInfoVO;
import com.longhuan.order.pojo.vo.FundApplyMatchFundVO;
import com.longhuan.order.pojo.vo.FundApplyUseVO;
import com.longhuan.order.service.FundApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 资方放款失败处理
 * <AUTHOR>
 * @date 2024/09/19
 */
@Api(tags = "资方申请接口")
@RestController
@RequestMapping("/api/v1/fundApply")
@RequiredArgsConstructor
public class FundApplyController {

    private final FundApplyService fundApplyService;


    @ApiOperation(value = "对当前放款失败的资方重新发起申请")
    @GetMapping("/resetPaymentApply/{orderId}")
    Result<Boolean> resetFundPaymentApply(@PathVariable("orderId") Integer orderId){
        return Result.success(fundApplyService.resetFundPaymentApplyByOrderId(orderId));
    }

    @ApiOperation(value = "查询可使用的资方")
    @PostMapping("/queryUseList")
    Result<List<FundApplyUseVO>> queryUseFund(@RequestBody FundApplyQueryUseFundDTO useFundDTO){
        return Result.success(fundApplyService.queryUseFund(useFundDTO.getOrderId()));
    }

    @ApiOperation(value = "资方审批（终审）")
    @PostMapping("/apply")
    Result<Boolean> fundFinalApply(@RequestBody FundApplyFundFinalApplyDTO finalApplyDTO){
        return Result.success(fundApplyService.fundFinalApply(finalApplyDTO.getOrderId(), finalApplyDTO.getFundId()));
    }

    @ApiOperation(value = "匹配资方")
    @PostMapping("/match")
    Result<FundApplyMatchFundVO> matchFund(@RequestBody FundApplyMatchFundDTO matchFundDTO){
        return Result.success(fundApplyService.matchFund(matchFundDTO.getOrderId()));
    }

    @ApiOperation(value = "确认更换资方")
    @PostMapping("/confirm")
    Result<Boolean> confirm(@RequestBody FundApplyConfirmDTO confirmDTO){
        return Result.success(fundApplyService.confirm(confirmDTO.getOrderId()));
    }


    @ApiOperation(value = "获取资方审批结论（终审）")
    @GetMapping("/getConclusionInfoByOrderId/{orderId}")
    Result<FundApplyInfoVO> getConclusionInfoByOrderId(@PathVariable("orderId") Integer orderId){
        return Result.success(fundApplyService.getConclusionInfoByOrderId(orderId));
    }


}
