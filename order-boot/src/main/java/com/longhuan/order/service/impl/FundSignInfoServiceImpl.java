package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.converter.FundSignInfoConverter;
import com.longhuan.order.enums.FundSignStatusEnum;
import com.longhuan.order.mapper.FundSignInfoMapper;
import com.longhuan.order.pojo.dto.FundSignDetailDTO;
import com.longhuan.order.pojo.dto.FundSignInfoDTO;
import com.longhuan.order.pojo.entity.FundSignDetailEntity;
import com.longhuan.order.pojo.entity.FundSignInfoEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.service.FundSignDetailService;
import com.longhuan.order.service.FundSignInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class FundSignInfoServiceImpl extends ServiceImpl<FundSignInfoMapper, FundSignInfoEntity> implements FundSignInfoService {

    private final FundSignInfoConverter fundSignInfoConverter;
    private final FundSignDetailService fundSignDetailService;
    private final FundSignInfoMapper fundSignInfoMapper;


    @Override
    public boolean saveFundSignInfo(FundSignInfoDTO dto) {
        Integer orderId = dto.getOrderId();
        FundSignInfoEntity fundSignInfo = fundSignInfoConverter.dto2Entity(dto);
        List<FundSignDetailDTO> signDetailList = dto.getFundSignDetailList();

        FundSignInfoEntity signInfo = super.getOne(Wrappers.<FundSignInfoEntity>lambdaQuery()
                        .eq(FundSignInfoEntity::getOrderId, orderId)
                        .eq(FundSignInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FundSignInfoEntity::getCreateTime)
                , false);
        if (signInfo != null) {
            if (FundSignStatusEnum.SUCCESS == signInfo.getSignStatus()) {
                return true;
            }else{
                fundSignInfo.setId(signInfo.getId());
            }
        }
        super.saveOrUpdate(fundSignInfo);

        // 移除旧的数据
        if (CollUtil.isNotEmpty(signDetailList)) {
            super.remove(Wrappers.<FundSignInfoEntity>lambdaQuery()
                    .eq(FundSignInfoEntity::getOrderId, orderId)
                    .eq(FundSignInfoEntity::getDeleteFlag, 0));
            List<FundSignDetailEntity> detailList = fundSignInfoConverter.detailDtoList2DetailEntityList(signDetailList);
            // 保存新的数据
            detailList.forEach(detail -> {
                detail.setFundSignId(fundSignInfo.getFundSignId());
            });
            fundSignDetailService.saveBatch(detailList);
        }

        return true;
    }

    @Override
    public void verifyFundSignInfo(OrderInfoEntity orderInfoEntity) throws BusinessException {
        Integer orderId = orderInfoEntity.getId();
        FundEnum fundEnum = FundEnum.getFundEnum(orderInfoEntity.getFundId());
        Long signedCount = fundSignInfoMapper.selectCount(new LambdaQueryWrapper<FundSignInfoEntity>()
                .eq(FundSignInfoEntity::getOrderId, orderId)
                .eq(FundSignInfoEntity::getFundId, orderInfoEntity.getFundId())
                .eq(FundSignInfoEntity::getSignStatus, FundSignStatusEnum.SUCCESS.getCode())
                .eq(FundSignInfoEntity::getDeleteFlag, 0));

//        if (shouldCheckSignedCount(fundEnum)) {
//            if (signedCount == 0) {
//                log.info("FundSignInfoServiceImpl.verifyFundSignInfo, fund not signed yet, orderId = {}", orderId);
//                throw new BusinessException("请先完成资方合同签署");
//            }
//        }
    }

    private boolean shouldCheckSignedCount(FundEnum fundEnum) {
        return fundEnum == FundEnum.FU_MIN || fundEnum == FundEnum.YING_FENG;
    }

}
