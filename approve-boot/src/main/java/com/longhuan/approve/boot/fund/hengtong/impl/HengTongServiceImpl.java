package com.longhuan.approve.boot.fund.hengtong.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.approve.api.constants.FundRepayModeEnum;
import com.longhuan.approve.api.constants.ZhongHengSelOrderStateEnum;
import com.longhuan.approve.api.pojo.dto.*;
import com.longhuan.approve.api.pojo.dto.zhongheng.hengtong.HengTongATMCardInfoSyncDTO;
import com.longhuan.approve.api.pojo.dto.zhongheng.hengtong.HengTongPreRepayPlanDTO;
import com.longhuan.approve.api.pojo.vo.TongHuiInitiateRefundVO;
import com.longhuan.approve.api.pojo.vo.ZhongHengApiResult;
import com.longhuan.approve.api.pojo.vo.zhongheng.*;
import com.longhuan.approve.boot.client.ZhongHengApiClient;
import com.longhuan.approve.boot.constants.FundConstant;
import com.longhuan.approve.boot.enums.FundPaymentStatusEnum;
import com.longhuan.approve.boot.enums.*;
import com.longhuan.approve.boot.enums.zhongheng.*;
import com.longhuan.approve.boot.feign.*;
import com.longhuan.approve.boot.fund.finall.FinalFundInfoService;
import com.longhuan.approve.boot.fund.hengtong.HengTongService;
import com.longhuan.approve.boot.mapper.*;
import com.longhuan.approve.boot.pojo.dto.*;
import com.longhuan.approve.boot.pojo.dto.CommuteAndPayDTO;
import com.longhuan.approve.boot.pojo.dto.ProcessEndDTO;
import com.longhuan.approve.boot.pojo.dto.TongHuiCompensateSwitchDTO;
import com.longhuan.approve.boot.pojo.dto.hengtong.*;
import com.longhuan.approve.boot.pojo.dto.hengtong.DaiChangChaZhangTongZhiDTO;
import com.longhuan.approve.boot.pojo.dto.keshang.OrderInfoAddDTO;
import com.longhuan.approve.boot.pojo.dto.yingfeng.YingFengTYDaKuanPingZhengSelDTO;
import com.longhuan.approve.boot.pojo.entity.*;
import com.longhuan.approve.boot.pojo.vo.*;
import com.longhuan.approve.boot.pojo.vo.hengtong.HengTongAccountVO;
import com.longhuan.approve.boot.pojo.vo.hengtong.PaymentResVO;
import com.longhuan.approve.boot.pojo.vo.hengtong.PreCreditQueryResVO;
import com.longhuan.approve.boot.pojo.vo.hengtong.PreCreditResVO;
import com.longhuan.approve.boot.service.*;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.GlobalConstants;
import com.longhuan.common.core.constant.MsgConstants;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.core.enums.dict.*;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.constants.SwitchConstants;
import com.longhuan.common.redis.util.DictUtils;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.order.enums.PayApplicationAuditTypeEnum;
import com.longhuan.order.enums.PayApplicationEventEnums;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.resource.pojo.dto.FundResourceDTO;
import com.longhuan.resource.pojo.dto.FundResourceResultDTO;
import com.longhuan.resource.pojo.vo.FileVO;
import com.longhuan.risk.pojo.dto.fhld.AssessmentDto;
import com.longhuan.risk.pojo.dto.fhld.DetectionsDTO;
import com.longhuan.risk.pojo.dto.fhld.PersonDTO;
import com.longhuan.risk.pojo.dto.fhld.PhoneDTO;
import com.longhuan.user.pojo.dto.MessageContent;
import com.longhuan.user.pojo.vo.UserInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 恒通  中恒通汇
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HengTongServiceImpl implements HengTongService {

    private static final String INDENT = "   ";
    private static final String SUB_INDENT = "     ";

    private final ZhongHengApiClient zhongHengApiClient;
    private final PreFundInfoMapper preFundInfoMapper;
    private final OrderFeign orderFeign;
    private final UserFeign userFeign;
    private final OrderInfoMapper orderInfoMapper;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final ResourceFeign resourceFeign;
    private final RiskFeign riskFeign;
    private final OrderFileMapper orderFileMapper;
    private final FundApproveMapper fundApproveMapper;

    private final DictUtils dictUtils;
    private final FundRepaymentInfoService fundRepaymentInfoService;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final CustomerMortgageInfoMapper customerMortgageInfoMapper;
    private final FundBaseInfoService fundBaseInfoService;
    private final FundRepaymentDeductMapper fundRepaymentDeductMapper;
    private final FundRepaymentDeductService fundRepaymentDeductService;
    private final MessageFeign messageFeign;
    private final EnvUtil envUtil;
    private final FundRepurchaseResultMapper fundRepurchaseResultMapper;
    private final FinalFundInfoService finalFundInfoService;
    private static final String heTongCode = "FK001";
    private final OrderPayApplicationMapper orderPayApplicationMapper;
    private final OrderPayApplyNodeRecordMapper  orderPayApplyNodeRecordMapper;
    private final FundRefundInfoEntityService fundRefundInfoEntityService;
    private final DingDrawMoneyFeign dingDrawMoneyFeign;
    private final SprEductionUsageEntityService sprEductionUsageEntityService;
    private final OrderSettleApplyInfoMapper orderSettleApplyInfoMapper;
    private final OrderVehicleInfoMapper orderVehicleInfoMapper;
    private final AfterLoanPatchesMapper afterLoanPatchesMapper;
	private final OrderFeeDetailMapper orderFeeDetailMapper;
    private final SwitchUtils switchUtils;

    @Override
    public String yuShouXinAdd(FundPreBaseDTO fundPreBaseDTO) {
        log.info("HengTongServiceImpl.yuShouXinAdd start, preId: {}", fundPreBaseDTO.getPreId());

        try {
            //构建预授信DTO
            PreCreditReqDTO preCreditReqDTO = new PreCreditReqDTO()
                    .setXingMing(fundPreBaseDTO.getName())
                    .setShouJiHao(fundPreBaseDTO.getPhone())
                    .setShenFenZhengHao(fundPreBaseDTO.getIdNumber())
                    .setCheLing(Convert.toStr(fundPreBaseDTO.getVehicleAge()))
                    .setGuoHuCiShu(fundPreBaseDTO.getTransferTimes())
                    .setUserAge(Convert.toStr(IdcardUtil.getAgeByIdCard(fundPreBaseDTO.getIdNumber())))
                    .setCheLiangZhuCeShiJian(fundPreBaseDTO.getRegisterDate())
                    .setQingQiuType("2") //固定 2
                    .setLaiYuanType("2") //固定 2
                    .setFuJianInfo(getPreFileSupmentList(fundPreBaseDTO.getPreId(), fundPreBaseDTO)); //征信查询授权书 不传

            String generateJzyReport = generateJzyReport(1, fundPreBaseDTO.getName(), fundPreBaseDTO.getIdNumber(), fundPreBaseDTO.getPhone());
            preCreditReqDTO.setYanZhenJieGuo(generateJzyReport);

            String fuHaoLvDongJieGuo = null;
            try {
                fuHaoLvDongJieGuo = generateFhldReport(fundPreBaseDTO.getIdNumber());
            } catch (Exception e) {
                log.error("HengTongServiceImpl.yuShouXinAdd generateLegalReport e:{}:", e.getMessage(), e);
                throw new BusinessException("符号律动报告生成失败");
            }
            if (ObjUtil.isNull(fuHaoLvDongJieGuo)) {
                log.info("HengTongServiceImpl.yuShouXinAdd generateLegalReport fuHaoLvDongJieGuo is null preId:{}", fundPreBaseDTO.getPreId());
                throw new BusinessException("符号律动报告生成失败");
            }
            preCreditReqDTO.setFuHaoLvDongJieGuoXS(fuHaoLvDongJieGuo);

            HuaRuiVO huaRuiReport = fundApproveMapper.getHuaRuiReport(fundPreBaseDTO.getName(), fundPreBaseDTO.getIdNumber());
            if (ObjUtil.isNull(huaRuiReport) || StrUtil.isBlank(huaRuiReport.getCreditReport())) {
                log.info("HengTongServiceImpl.yuShouXinAdd getHuaRuiReport is null preId:{}", fundPreBaseDTO.getPreId());
            } else {
                preCreditReqDTO.setZhengXinData(huaRuiReport.getCreditReport());
            }

            ZhongHengApiResult<PreCreditResVO> result = zhongHengApiClient.yuShouXinAdd(preCreditReqDTO);

            if (!ZhongHengApiResult.isSuccess(result)) {
                log.error("HengTongServiceImpl.yuShouXinAdd fail result:{}", result);
                //异常直接拒绝
                PreCreditQueryResVO vo = new PreCreditQueryResVO();
                vo.setState(ZhongHengDictEnum.YuShouXinSelState.REJECTED.getCode());
                vo.setReviewMsg(result.getMsg());
                handlePreCreditStatusResultByPreId(fundPreBaseDTO.getPreId(), vo);

                //设置拒绝状态
                fundPreBaseDTO.setCallFundPreResult(PreFundResultEnum.REJECT);
                return null;
            }
            log.info("HengTongServiceImpl.yuShouXinAdd success result:{}", result);
            //获取预授信信息
            PreCreditResVO responseData = result.getResponseData();
            //更新预授信申请号
            String ysxOrderNum = responseData.getYSXOrderNum();
            preFundInfoMapper.update(new LambdaUpdateWrapper<PreFundInfoEntity>()
                    .set(PreFundInfoEntity::getFundResult, PreFundResultEnum.WAIT)
                    .set(PreFundInfoEntity::getCreditReqNo, ysxOrderNum)
                    .eq(PreFundInfoEntity::getPreId, fundPreBaseDTO.getPreId())
                    .eq(PreFundInfoEntity::getFundId, fundPreBaseDTO.getFundId())
                    .eq(PreFundInfoEntity::getDeleteFlag, 0));

            log.info("HengTongServiceImpl.yuShouXinAdd end");

            return ysxOrderNum;
        } catch (BusinessException e) {
            log.error("HengTongServiceImpl.yuShouXinAdd fail msg:{}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("HengTongServiceImpl.yuShouXinAdd fail msg:{}", e.getMessage(), e);
            throw new BusinessException("中恒通汇预授信申请异常: err:" + e.getMessage());
        }
    }

    /**
     * 预授信查询
     *
     * @param preId       预审id
     * @param ySXOrderNum 预授信申请号
     * @return 预授信查询结果
     */
    @Override
    public ZhongHengApiResult<PreCreditQueryResVO> yuShouXinSel(Integer preId, String ySXOrderNum) {
        log.info("HengTongServiceImpl.yuShouXinSel start, preId: {}, ySXOrderNum: {}", preId, ySXOrderNum);
        try {
            PreCreditQueryReqDTO ZHTHYuShouXinSelDTO = new PreCreditQueryReqDTO();
            ZHTHYuShouXinSelDTO.setYSXOrderNum(ySXOrderNum);
            ZhongHengApiResult<PreCreditQueryResVO> result = zhongHengApiClient.yuShouXinSel(ZHTHYuShouXinSelDTO);
            if (!ZhongHengApiResult.isSuccess(result)) {
                log.info("HengTongServiceImpl.yuShouXinSel fail result:{}", result);
                throw new BusinessException(result.getMsg());
            }
            log.info("HengTongServiceImpl.yuShouXinSel success result:{}", result);

            handlePreCreditQueryResult(result.getResponseData());

            return result;
        } catch (Exception e) {
            log.error("HengTongServiceImpl.yuShouXinSel fail msg:{}", e.getMessage(), e);
            throw new BusinessException("中恒通汇预授信查询异常: err:" + e.getMessage());
        }
    }


    /**
     * 预授信查询结果处理
     *
     * @param selVO 预审查询结果
     */
    @Override
    public void handlePreCreditQueryResult(PreCreditQueryResVO selVO) {
        try {
            log.info("HengTongServiceImpl.handlePreCreditQueryResult start, creditReqNo: {}, result: {}", selVO.getYSXOrderNum(), selVO);


            preFundInfoMapper.update(new LambdaUpdateWrapper<PreFundInfoEntity>()
                    .set(PreFundInfoEntity::getFundNode, FundApplyNodeEnums.FUND_PRE_APPROVAL_RESULT_QUERY)
                    .eq(PreFundInfoEntity::getCreditReqNo, selVO.getYSXOrderNum())
                    .eq(PreFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                    .eq(PreFundInfoEntity::getDeleteFlag, 0)
            );
            // 更新订单授信状态
            ZhongHengDictEnum.YuShouXinSelState yuShouXinSelState = ZhongHengDictEnum.YuShouXinSelState.getByCode(selVO.getState());
            PreFundResultEnum updatePreStatus = null;
            switch (yuShouXinSelState) {
                case APPROVING -> updatePreStatus = PreFundResultEnum.WAIT;
                case APPROVED -> updatePreStatus = PreFundResultEnum.PASS;
                case REJECTED -> updatePreStatus = PreFundResultEnum.REJECT;
                default -> updatePreStatus = PreFundResultEnum.WAIT;
            }
            BigDecimal creditAmount = BigDecimal.ZERO;

            // 预审不返回预授信金额 暂时取风控授信成数金额
            PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                    .select(PreFundInfoEntity::getId,PreFundInfoEntity::getPreId,PreFundInfoEntity::getCreditAmount)
                    .eq(PreFundInfoEntity::getCreditReqNo, selVO.getYSXOrderNum())
                    .eq(PreFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                    .eq(PreFundInfoEntity::getDeleteFlag, 0));

            if (ObjUtil.isNull(preFundInfoEntity)) {
                log.info("HengTongServiceImpl.handlePreCreditQueryResult preFundInfoEntity is null, creditReqNo: {}", selVO.getYSXOrderNum());
                throw new BusinessException("预审记录不存在");
            }
            if (preFundInfoEntity.getCreditAmount() != null) {
                creditAmount = preFundInfoEntity.getCreditAmount();
            }

            PreApproveFundStatusDTO preApproveFundStatusDTO = new PreApproveFundStatusDTO()
                    .setPreId(preFundInfoEntity.getPreId())
                    .setFundId(FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                    .setStatus(updatePreStatus)
                    .setCreditAmt(creditAmount)
                    .setFailReason(selVO.getReviewMsg());
            log.info("HengTongServiceImpl.handlePreCreditQueryResult PreApproveFundStatusDTO created: {}", JSONUtil.toJsonStr(preApproveFundStatusDTO));
            orderFeign.preUpdateFundStatus(preApproveFundStatusDTO);

            log.info("HengTongServiceImpl.handlePreCreditQueryResult end preId: {}, result: {}", preFundInfoEntity.getPreId(), selVO);
        } catch (BusinessException e) {
            log.error("HengTongServiceImpl.handlePreCreditQueryResult fail msg:{}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("HengTongServiceImpl.handlePreCreditQueryResult fail msg:{}", e.getMessage(), e);
            throw new BusinessException("中恒通汇预授信查询结果处理异常: err:" + e.getMessage());
        }
    }


    /**
     * 预授信查询结果处理 根据预审id
     *
     */
    @Override
    public void handlePreCreditStatusResultByPreId(Integer preId, PreCreditQueryResVO selVO) {
        try {
            log.info("HengTongServiceImpl.handlePreCreditQueryResultByPreId start, creditReqNo: {}, result: {}", selVO.getYSXOrderNum(), selVO);


            preFundInfoMapper.update(new LambdaUpdateWrapper<PreFundInfoEntity>()
                    .set(PreFundInfoEntity::getFundNode, FundApplyNodeEnums.FUND_PRE_APPROVAL_RESULT_QUERY)
                    .eq(PreFundInfoEntity::getPreId, preId)
                    .eq(PreFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                    .eq(PreFundInfoEntity::getDeleteFlag, 0)
            );
            // 更新订单授信状态
            ZhongHengDictEnum.YuShouXinSelState yuShouXinSelState = ZhongHengDictEnum.YuShouXinSelState.getByCode(selVO.getState());
            PreFundResultEnum updatePreStatus = null;
            switch (yuShouXinSelState) {
                case APPROVING -> updatePreStatus = PreFundResultEnum.WAIT;
                case APPROVED -> updatePreStatus = PreFundResultEnum.PASS;
                case REJECTED -> updatePreStatus = PreFundResultEnum.REJECT;
                default -> updatePreStatus = PreFundResultEnum.WAIT;
            }
            BigDecimal creditAmount = BigDecimal.ZERO;

            // 预审不返回预授信金额 暂时取风控授信成数金额
            PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                    .select(PreFundInfoEntity::getId,PreFundInfoEntity::getPreId,PreFundInfoEntity::getCreditAmount)
                    .eq(PreFundInfoEntity::getPreId, preId)
                    .eq(PreFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                    .eq(PreFundInfoEntity::getDeleteFlag, 0));

            if (ObjUtil.isNull(preFundInfoEntity)) {
                log.info("HengTongServiceImpl.handlePreCreditQueryResultByPreId preFundInfoEntity is null, creditReqNo: {}", selVO.getYSXOrderNum());
                throw new BusinessException("预审记录不存在");
            }
            if (preFundInfoEntity.getCreditAmount() != null) {
                creditAmount = preFundInfoEntity.getCreditAmount();
            }

            PreApproveFundStatusDTO preApproveFundStatusDTO = new PreApproveFundStatusDTO()
                    .setPreId(preFundInfoEntity.getPreId())
                    .setFundId(FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                    .setStatus(updatePreStatus)
                    .setCreditAmt(creditAmount)
                    .setFailReason(selVO.getReviewMsg());
            log.info("HengTongServiceImpl.handlePreCreditQueryResultByPreId PreApproveFundStatusDTO created: {}", JSONUtil.toJsonStr(preApproveFundStatusDTO));
            orderFeign.preUpdateFundStatus(preApproveFundStatusDTO);

            log.info("HengTongServiceImpl.handlePreCreditQueryResultByPreId end preId: {}, result: {}", preFundInfoEntity.getPreId(), selVO);
        } catch (BusinessException e) {
            log.error("HengTongServiceImpl.handlePreCreditQueryResultByPreId fail msg:{}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("HengTongServiceImpl.handlePreCreditQueryResultByPreId fail msg:{}", e.getMessage(), e);
            throw new BusinessException("中恒通汇预授信查询结果处理异常: err:" + e.getMessage());
        }
    }

    /**
     * 预授信查询结果处理
     *
     * @param preId 预审id
     */
    @Override
    public void handlePreCreditQueryResult(Integer preId) {
        try {
            log.info("HengTongServiceImpl.handlePreCreditQueryResult start, preId: {}", preId);
            PreFundInfoEntity preFundInfo = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                    .eq(PreFundInfoEntity::getPreId, preId)
                    .eq(PreFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                    .eq(PreFundInfoEntity::getDeleteFlag, 0));
            if (preFundInfo == null) {
                log.info("HengTongServiceImpl.handlePreCreditQueryResult preFundInfo is null, preId: {}", preId);
                return;
            }
            if (ObjUtil.equals(preFundInfo.getFundResult(), PreFundResultEnum.PASS)) {
                log.info("HengTongServiceImpl.handlePreCreditQueryResult preFundInfo status is SUCCESS, preId: {}", preId);
                return;
            }
            //查询授信结果并处理
            yuShouXinSel(preId, preFundInfo.getCreditReqNo());

            log.info("HengTongServiceImpl.handlePreCreditQueryResult end preId: {}", preId);
        } catch (BusinessException e) {
            log.error("HengTongServiceImpl.handlePreCreditQueryResult fail msg:{}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("HengTongServiceImpl.handlePreCreditQueryResult fail msg:{}", e.getMessage(), e);
            throw new BusinessException("中恒通汇预授信查询结果处理异常: err:" + e.getMessage());
        }
    }

    /**
     * 终审授信结果查询
     *
     * @param orderId 订单id
     */
    @Override
    public void handleFinalCreditQueryResult(Integer orderId) {
        log.info("HengTongServiceImpl.handleFinalCreditQueryResult start, orderId: {}", orderId);
        // 终审通过
        BigDecimal fundCreditAmt = BigDecimal.ZERO;
        OrderAmountCalDTO amountCalDTO = fundBaseInfoService.getApplyAmountByOrderId(orderId);
        Assert.notNull(amountCalDTO, ()->new BusinessException("获取订单金额失败"));
        BigDecimal totalAmount = amountCalDTO.getTotalAmount();
        BigDecimal riskAmount = amountCalDTO.getRiskAmount();
        // 预审额度 = 总评额度和风控额度 最小值
        fundCreditAmt = totalAmount.min(riskAmount);

        FinalApproveFundStatusDTO fundStatusDTO = new FinalApproveFundStatusDTO()
                .setOrderId(orderId)
                .setFundId(FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                .setStatus(PreFundResultEnum.PASS)
                .setFundCreditAmt(fundCreditAmt)
                .setFundRemark(null)
                .setFundCreditTime(LocalDateTime.now());

        log.info("HengTongServiceImpl.handleFinalCreditQueryResult FinalApproveFundStatusDTO created: {}", JSONUtil.toJsonStr(fundStatusDTO));
        orderFeign.updateFundFinalStatus(fundStatusDTO);
    }

    /**
     * 14.2通联签约发送验证码
     */
    @Override
    public ZhongHengApiResult<ZhongHengTYTLFaSongYZMVO> sendBankCardMessage(HengTongBankCardSMSCodeDTO hengTongBankCardSMSCodeDTO) {
        return zhongHengApiClient.hengTongSendBankCardMessage(hengTongBankCardSMSCodeDTO, ZhongHengApiEnums.YQ_TLFaSongYZM);
    }

    /**
     * 14.3通联签约签约_1.0.0
     *
     * @param dto 订单id
     * @return {@link ZhongHengApiResult < ZhongHengFangKuanSelVO >}
     */
    @Override
    public ZhongHengApiResult<ZhongHengTYTLQianYueVO> hengTongSendBankCardSign(HengTongBankSignDTO dto) {
        return zhongHengApiClient.hengTongSendBankCardSign(dto, ZhongHengApiEnums.YQ_TLQianYue);
    }

    /**
     * 14.7通联签约解约
     *
     * @return {@link ZhongHengApiResult < ZhongHengTYTLJieYueVO >}
     */
    @Override
    public ZhongHengApiResult<ZhongHengTYTLJieYueVO> hengtongBankCardCancelSign(HengTongBankCancelSignDTO dto) {
        return zhongHengApiClient.hengTongCancelBankCardSign(dto, ZhongHengApiEnums.YQ_TLJieYue);
    }

    /**
     * 3.8 加押状态更新
     *
     * @param orderId
     */
    @Override
    public ZhongHengApiResult<Void>
    hengTongJiaYaUpdate(Integer orderId) {
        //获取订单文件
        Integer dydjlFileId = resourceFeign.selectFileConfigByCode("DYDJL").getData();
        List<OrderFileEntity> fileList = orderFileMapper.selectList(
                new LambdaQueryWrapper<OrderFileEntity>()
                        .eq(OrderFileEntity::getOrderId, orderId)
                        .eq(OrderFileEntity::getFileId, dydjlFileId)
                        .eq(OrderFileEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(fileList)){
            log.info("HengTongServiceImpl.hengTongJiaYaUpdate dydjlFileId is null, orderId: {}", orderId);
            return null;
        }
        ArrayList<HengTongJiaYaUpdateDTO.FuJianInfoDTO> fuJianInfoDTOList = new ArrayList<>();
        fileList.forEach(file -> {
            HengTongJiaYaUpdateDTO.FuJianInfoDTO fuJianInfoDTO = new HengTongJiaYaUpdateDTO.FuJianInfoDTO();
            fuJianInfoDTO.setFuJianTypeNum("C005");
            fuJianInfoDTO.setFuJianURL(getViewResourceUrl(file.getResourceId()));
            fuJianInfoDTOList.add(fuJianInfoDTO);
        });

        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Integer fundId = orderInfo.getFundId();

        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, fundId)
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );

        HengTongJiaYaUpdateDTO hengTongJiaYaUpdateDTO = new HengTongJiaYaUpdateDTO();
        hengTongJiaYaUpdateDTO.setOrderNum(orderInfo.getOrderNumber())
                .setOrderNum(finalFundInfo.getLoanBillNo())
                .setSPOrderNum(orderInfo.getOrderNumber())
                .setJiaYaState("1")
                .setFuJianInfo(fuJianInfoDTOList);
        return zhongHengApiClient.hengTongJiaYaUpdate(hengTongJiaYaUpdateDTO);
    }

    /**
     * 14.5打款凭证查询
     *
     * @param orderId 订单id
     * @return {@link ZhongHengApiResult<  ZhongHengTYDaKuanPingZhengSelVO >}
     */
    @Override
    public ZhongHengApiResult<ZhongHengTYDaKuanPingZhengSelVO> daKuanPingdZhengSel(Integer orderId) {

        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));

        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI)
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );
        Assert.notNull(finalFundInfo, () -> new BusinessException("授信申请信息不存在"));

        String orderNum = orderInfo.getOrderNumber();
        String sPOrderNum = finalFundInfo.getLoanBillNo();

        YingFengTYDaKuanPingZhengSelDTO pingZhengSelDTO = new YingFengTYDaKuanPingZhengSelDTO()
                .setOrderNum(orderNum)
                .setSpOrderNum(sPOrderNum);
        return zhongHengApiClient.tYDaKuanPingZhengSel(pingZhengSelDTO);
    }

    /**
     * 14.5打款凭证查询结果处理
     *
     * @param voucherVO 打款凭证查询结果
     */
    @Override
    public void handlePaymentVoucher(ZhongHengTYDaKuanPingZhengSelVO voucherVO) {
        String orderNum = voucherVO.getOrderNum();
        String spOrderNum = voucherVO.getSpOrderNum();
        log.info("HengTongServiceImpl.handlePaymentVoucher start, orderNum: {}, spOrderNum: {}", orderNum, spOrderNum);
        List<ZhongHengTYDaKuanPingZhengSelVO.PingZheng> dkPingZheng = voucherVO.getDKPingZheng();
        if (CollUtil.isEmpty(dkPingZheng)) {
            log.info("HengTongServiceImpl.handlePaymentVoucher dkPingZheng is empty, orderNum: {}, spOrderNum: {}", orderNum, spOrderNum);
            return;
        }

        OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getOrderNumber, spOrderNum)
                .eq(OrderInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
        );
        log.info("HengTongServiceImpl.handlePaymentVoucher orderInfo: {}", orderInfo);
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));

        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getLoanBillNo, orderNum)
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI)
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );
        log.info("HengTongServiceImpl.handlePaymentVoucher finalFundInfo: {}", finalFundInfo);
        Assert.notNull(finalFundInfo, () -> new BusinessException("授信申请信息不存在"));

        //获取打款凭证文件id
        Integer fundSettlementFileId = resourceFeign.selectFileConfigByCode(FundConstant.PAYMENT_VOUCHER).getData();
        //判断文件是否已同步
        Long count = orderFileMapper.selectCount(new LambdaQueryWrapper<OrderFileEntity>()
                .eq(OrderFileEntity::getOrderId, orderInfo.getId())
                .eq(OrderFileEntity::getFileId, fundSettlementFileId)
                .eq(OrderFileEntity::getDeleteFlag, 0)
        );
        //文件已同步
        if (count > 0 && dkPingZheng.size() <= count) {
            log.info("HengTongServiceImpl.handlePaymentVoucher file is already sync, orderNum: {}, spOrderNum: {}", orderNum, spOrderNum);
            return;
        }
        for (ZhongHengTYDaKuanPingZhengSelVO.PingZheng pingZheng : dkPingZheng) {
            try {
                String voucherUrl = pingZheng.getPzUrl();
                if (StrUtil.isNotEmpty(voucherUrl)) {
                    log.info("HengTongServiceImpl.handlePaymentVoucher start voucherUrl: {}", voucherUrl);
                    URI uri = new URI(voucherUrl);
                    RestTemplate restTemplate = new RestTemplate();
                    ResponseEntity<byte[]> forEntity = restTemplate.exchange(uri, HttpMethod.GET, null, byte[].class);

                    MultipartFile multipartFile = new MockMultipartFile("files", "paymentVoucher.jpg",
                            MediaType.APPLICATION_OCTET_STREAM_VALUE, forEntity.getBody());

                    // 调用上传接口
                    List<MultipartFile> files = new ArrayList<>();
                    files.add(multipartFile);
                    List<FileVO> data = resourceFeign.uploadFile(files).getData();
                    if (CollUtil.isNotEmpty(data)) {
                        FileVO fileResource = data.get(0);
                        // 从URL中获取文件名
                        String fileName = FileUtil.getName(URLUtil.getPath(voucherUrl));
                        if (StrUtil.isEmpty(fileName)) {
                            fileName = fileResource.getResourceName();
                        }
                        OrderFileEntity orderFile = new OrderFileEntity();
                        orderFile.setFileId(fundSettlementFileId);
                        orderFile.setOrderId(orderInfo.getId());
                        orderFile.setFileName(fileResource.getResourceName());
                        orderFile.setResourceId(fileResource.getResourceId());
                        orderFile.setResourceName(fileName);
                        orderFileMapper.insert(orderFile);
                        log.info("HengTongServiceImpl.handlePaymentVoucher end voucherUrl: {} resourceId:{}", voucherUrl, fileResource.getResourceId());
                    }

                }
            } catch (Exception e) {
                log.error("queryCreditXmlReport error msg:{}", e.getMessage(), e);
            }
        }


    }

    /**
     * 14.6 打款通知结果处理
     */
    @Override
    public void handlePaymentResult(PaymentResVO paymentResVO) {
        ZhongHengDictEnum.DaKuanState daKuanState = paymentResVO.getDaKuanState();
        String failReason = paymentResVO.getDaKuanNote();

        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getLoanBillNo, paymentResVO.getOrderNum())
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );
        log.info("HengTongServiceImpl.handlePaymentResult finalFundInfo: {}", finalFundInfo);
        Assert.notNull(finalFundInfo, () -> new BusinessException("未查询到预审批信息"));

        OrderInfoEntity orderInfo = orderInfoMapper.selectById(finalFundInfo.getOrderId());
        log.info("HengTongServiceImpl.handlePaymentResult orderInfo: {}", orderInfo);
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));
        List<OrderAmountEntity> orderAmountList = fundApproveMapper.getOrderAmountEntity(orderInfo.getId());
        log.info("HengTongServiceImpl.handlePaymentResult orderAmountList: {}", orderAmountList);
        Assert.notEmpty(orderAmountList, () -> new BusinessException("额度信息获取失败"));
        OrderAmountEntity orderAmountEntity = orderAmountList.get(0);

        FundPaymentStatusEnum fundPaymentStatusEnum = switch (daKuanState) {
            case SUCCESS -> FundPaymentStatusEnum.PASS;
            case FAIL -> FundPaymentStatusEnum.FAIL;
        };
        OrderFundPaymentEnum orderFundPaymentEnum = OrderFundPaymentEnum.WAIT;
        if (FundPaymentStatusEnum.PASS.equals(fundPaymentStatusEnum)) {
            orderFundPaymentEnum = OrderFundPaymentEnum.PASS;
        } else {
            orderFundPaymentEnum = OrderFundPaymentEnum.FAIL;
        }
        //如果为失败修改资方为中恒
        LocalDateTime paymentTime = null;
        FundEnum finalFundEnum = FundEnum.ZHONG_HENG_TONG_HUI;
        if (OrderFundPaymentEnum.FAIL.equals(orderFundPaymentEnum)) {
            finalFundEnum = FundEnum.ZHONG_HENG;
            paymentTime = ObjUtil.defaultIfNull(finalFundInfo.getPaymentTime(), LocalDateTime.now());
            orderFundPaymentEnum = OrderFundPaymentEnum.PASS;
            fundPaymentStatusEnum = FundPaymentStatusEnum.PASS;
            fundRepaymentInfoMapper.update(new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
                    .set(FundRepaymentInfoEntity::getFundId, finalFundEnum.getValue())
                    .eq(FundRepaymentInfoEntity::getFundId, orderInfo.getFundId())
                    .eq(FundRepaymentInfoEntity::getOrderId, orderInfo.getId())
                    .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
            );
        }
        finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                .set(FinalFundInfoEntity::getRemark, failReason)
                .set(FinalFundInfoEntity::getFundResult, PreFundResultEnum.PASS)
                .set(FinalFundInfoEntity::getPaymentStatus, fundPaymentStatusEnum.getValue())
                .set(FinalFundInfoEntity::getPaymentAmount, orderAmountEntity.getCustomerConfirmAmount())
                .set(FinalFundInfoEntity::getPaymentTime, paymentTime)
                .set(FinalFundInfoEntity::getFundId, finalFundEnum.getValue())
                .eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
                .eq(FinalFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue()));

        OrderApproveFundPaymentStatusDTO fundPaymentStatusDTO = new OrderApproveFundPaymentStatusDTO()
                .setOrderId(orderInfo.getId())
                .setFundId(finalFundEnum.getValue())
                .setLoanApplyNo(paymentResVO.getOrderNum())
                .setLoanAmt(orderAmountEntity.getCustomerConfirmAmount())
                .setLoanPayTime(paymentTime)
                .setFailReason(failReason)
                .setStatus(orderFundPaymentEnum);

        orderFeign.updateFundPaymentStatus(fundPaymentStatusDTO);
    }


    @Override
    public void handlePleasePaymentResult(QingKuanZiLiaoShenHeTongZhiDTO dto) {
        String orderNum = dto.getOrderNum();
        String spOrderNum = dto.getSpOrderNum();
        String failReason = dto.getQingKuanShenHeNote();
        ZhongHengDictEnum.NoticeType noticeType = dto.getQingKuanShenHeNoteType();
        ZhongHengDictEnum.QingKuanState resultState = dto.getQingKuanShenHeState();

        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getLoanBillNo, orderNum)
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );
        log.info("HengTongServiceImpl.handlePleasePaymentResult finalFundInfo: {}", finalFundInfo);
        Assert.notNull(finalFundInfo, () -> new BusinessException("未查询到审批信息"));
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(finalFundInfo.getOrderId());
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));
        log.info("HengTongServiceImpl.handlePleasePaymentResult orderInfo: {}", orderInfo);

        List<OrderAmountEntity> orderAmountList = fundApproveMapper.getOrderAmountEntity(orderInfo.getId());
        Assert.notEmpty(orderAmountList, () -> new BusinessException("额度信息获取失败"));
        log.info("HengTongServiceImpl.handlePleasePaymentResult orderAmountList: {}", orderAmountList);

        OrderAmountEntity orderAmountEntity = orderAmountList.get(0);
        OrderFundPaymentEnum paymentStatus = OrderFundPaymentEnum.WAIT;

        if (noticeType == ZhongHengDictEnum.NoticeType.INFO_AUDIT) {
            switch (resultState) {
                case SUCCESS -> {
                    log.info("HengTongServiceImpl.handlePleasePaymentResult INFO_AUDIT SUCCESS orderNum={}, spOrderNum={}", orderNum, spOrderNum);
                    // 请款资料审核通过 待放款
                    paymentStatus = OrderFundPaymentEnum.WAIT;
                }
                case AUDITING -> {
                    log.info("HengTongServiceImpl.handlePleasePaymentResult INFO_AUDIT AUDITING orderNum={}, spOrderNum={}", orderNum, spOrderNum);
                    return;
                }
                case FAIL ->{
                    log.info("HengTongServiceImpl.handlePleasePaymentResult INFO_AUDIT FAIL orderNum={}, spOrderNum={}", orderNum, spOrderNum);
                    paymentStatus = OrderFundPaymentEnum.FAIL;
                    finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                            .set(FinalFundInfoEntity::getPaymentStatus, FundPaymentStatusEnum.FAIL)
                            .set(FinalFundInfoEntity::getPaymentTime, dto.getQingKuanShenHeTime())
                            .eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
                            .eq(FinalFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                            .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                    );
                    OrderApproveFundPaymentStatusDTO fundPaymentStatusDTO = new OrderApproveFundPaymentStatusDTO()
                            .setOrderId(orderInfo.getId())
                            .setFundId(FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                            .setLoanApplyNo(orderNum)
                            .setLoanAmt(orderAmountEntity.getCustomerConfirmAmount())
                            .setLoanPayTime(dto.getQingKuanShenHeTime())
                            .setFailReason(failReason)
                            .setStatus(paymentStatus);

                    orderFeign.updateFundPaymentStatus(fundPaymentStatusDTO);

                }

            }
        } else if (noticeType == ZhongHengDictEnum.NoticeType.LOAN_NOTICE) {
            switch (resultState) {
                case SUCCESS -> {
                    log.info("HengTongServiceImpl.handlePleasePaymentResult LOAN_NOTICE SUCCESS orderNum={}, spOrderNum={}", orderNum, spOrderNum);
                    paymentStatus = OrderFundPaymentEnum.PASS;

                    // 同步还款计划表
                    dto.getHuanKuanList().forEach(huanKuanJiHua -> {
                        FundRepaymentInfoEntity fundRepaymentInfo = new FundRepaymentInfoEntity()
                                .setOrderId(orderInfo.getId())
                                .setFundId(FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                                .setTerm(Convert.toInt(huanKuanJiHua.getQiShu()))
                                .setRepaymentDate(Convert.toLocalDateTime(huanKuanJiHua.getYsTime()).toLocalDate())
                                .setRepaymentPrincipal(Convert.toBigDecimal(huanKuanJiHua.getYsBenJin()))
                                .setRepaymentInterest(Convert.toBigDecimal(huanKuanJiHua.getYsLiXi()))
                                .setRepaymentAmountTotal(Convert.toBigDecimal(huanKuanJiHua.getYsHeJi()));

                        boolean isOverdue = fundRepaymentInfoService.determineOverdue(fundRepaymentInfo.getRepaymentAmountTotal(), fundRepaymentInfo.getActuallyAmountTotal(),
                                LocalDate.now(), fundRepaymentInfo.getRepaymentDate(), fundRepaymentInfo.getActuallyDate());
                        fundRepaymentInfo.setIsOverdue(isOverdue ? 1 : 0);

                        Long repaymentTermCount = fundRepaymentInfoMapper.selectCount(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                .eq(FundRepaymentInfoEntity::getTerm, fundRepaymentInfo.getTerm())
                                .eq(FundRepaymentInfoEntity::getOrderId, orderInfo.getId())
                                .eq(FundRepaymentInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0));
                        if (repaymentTermCount == 0) {
                            fundRepaymentInfoMapper.insert(fundRepaymentInfo);
                        } else {
                            fundRepaymentInfoMapper.update(fundRepaymentInfo, new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
                                    .eq(FundRepaymentInfoEntity::getTerm, fundRepaymentInfo.getTerm())
                                    .eq(FundRepaymentInfoEntity::getOrderId, orderInfo.getId())
                                    .eq(FundRepaymentInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                                    .eq(FundRepaymentInfoEntity::getDeleteFlag, 0));
                        }

                    });
                    finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                            .set(FinalFundInfoEntity::getFundResult, PreFundResultEnum.AWAITING_FUND_CONFIRMATION)
                            .set(FinalFundInfoEntity::getPaymentStatus, FundPaymentStatusEnum.PASS)
                            .set(FinalFundInfoEntity::getPaymentTime, dto.getQingKuanShenHeTime())
                            .eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
                            .eq(FinalFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                            .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                    );

                }
                case AUDITING -> {
                    log.info("HengTongServiceImpl.handlePleasePaymentResult LOAN_NOTICE AUDITING orderNum={}, spOrderNum={}", orderNum, spOrderNum);
                    return;
                }
                case FAIL -> {
                    log.info("HengTongServiceImpl.handlePleasePaymentResult LOAN_NOTICE FAIL orderNum={}, spOrderNum={}", orderNum, spOrderNum);
                    paymentStatus = OrderFundPaymentEnum.FAIL;
                    finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                            .set(FinalFundInfoEntity::getPaymentStatus, FundPaymentStatusEnum.FAIL)
                            .set(FinalFundInfoEntity::getPaymentTime, dto.getQingKuanShenHeTime())
                            .eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
                            .eq(FinalFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                            .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                    );

                    OrderApproveFundPaymentStatusDTO fundPaymentStatusDTO = new OrderApproveFundPaymentStatusDTO()
                            .setOrderId(orderInfo.getId())
                            .setFundId(FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                            .setLoanApplyNo(orderNum)
                            .setLoanAmt(orderAmountEntity.getCustomerConfirmAmount())
                            .setLoanPayTime(dto.getQingKuanShenHeTime())
                            .setFailReason(failReason)
                            .setStatus(paymentStatus);

                    orderFeign.updateFundPaymentStatus(fundPaymentStatusDTO);
                }

            }
        }



    }


    @Override
    public ZhongHengApiResult<OrderInfoAddResDTO> hengTongPay(OrderInfoAddDTO carBaseDTO) {

        return zhongHengApiClient.hengTongPay(carBaseDTO);
    }

    /**
     * 请款提交 构建请求 DTO
     *
     * @param orderId 订单 ID
     * @return {@link OrderInfoAddDTO }
     */
    @Override
    public OrderInfoAddDTO buildRequestDTO(Integer orderId) {
        // 获取贷款申请信息
        FinalFundInfoEntity fundInfoEntity = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );

        CustomerBaseDTO customerBaseDTO = fundApproveMapper.getCustomerFinalBaseInfo(orderId);
        FundPreCarBaseDTO carBaseDTO = fundApproveMapper.getCarFinalBaseInfo(orderId);

        OrderInfoAddDTO orderInfoAddDTO = new OrderInfoAddDTO();
        //放款失败 并且调用成功
        if (StrUtil.isNotBlank(fundInfoEntity.getLoanBillNo())) {
            orderInfoAddDTO.setOrderNum(fundInfoEntity.getLoanBillNo());
        }
        orderInfoAddDTO.setUserName(customerBaseDTO.getName());
        orderInfoAddDTO.setSex(Convert.toInt(customerBaseDTO.getGender()));
        orderInfoAddDTO.setUserNamePY(PinyinUtil.getPinyin(customerBaseDTO.getName(),""));
        orderInfoAddDTO.setIdCardNum(customerBaseDTO.getIdNumber());
        String nation = dictUtils.getDictLabel(GlobalConstants.DictType.NATION.name(), Convert.toInt(customerBaseDTO.getNation()));
        orderInfoAddDTO.setMinZu(ZhongHengNationEnum.getValueByName(nation));
        orderInfoAddDTO.setIdCardQianFaTime(customerBaseDTO.getValidityStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());

        orderInfoAddDTO.setIdCareDaoQiTime(Objects.equals(customerBaseDTO.getValidityEnd(), "长期") ? LocalDate.of(2999, 1, 1) :
                LocalDate.parse(customerBaseDTO.getValidityEnd(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));

        orderInfoAddDTO.setPhoneNum(customerBaseDTO.getPhone());
        orderInfoAddDTO.setUserAge(Convert.toInt(customerBaseDTO.getAge()));
        Integer maritalStatus = switch (Objects.requireNonNull(DictMaritalStatus.fromCode(Convert.toInt(customerBaseDTO.getMaritalStatus(), 0)))) {
            case UNMARRIED -> 1;
            case MARRIED_WITH_CHILDREN, MARRIED_WITHOUT_CHILDREN -> 2;
            case DIVORCED -> 3;
            default -> 4;
        };
        orderInfoAddDTO.setHunYingState(maritalStatus);

        orderInfoAddDTO.setXueLi(switch (Objects.requireNonNull(DictEducationLevel.fromCode(customerBaseDTO.getEducationalBackground()))) {
            case DOCTOR -> 1;
            case MASTER -> 2;
            case UNIVERSITY_BACHELOR -> 3;
            case UNIVERSITY_SPECIALTY -> 4;
            case TECHNICAL_SECONDARY_SCHOOL -> 5;
            case HIGH_SCHOOL -> 6;
            default -> 7;
        });

        orderInfoAddDTO.setHuJiSheng(customerBaseDTO.getIdCardProvinceName());
        orderInfoAddDTO.setHuJiShi(customerBaseDTO.getIdCardCityName());
        orderInfoAddDTO.setHuJiXiangXi(customerBaseDTO.getIdCardDetailedAddress());


        orderInfoAddDTO.setJuZhuSheng(customerBaseDTO.getResidentialProName());
        orderInfoAddDTO.setJuZhuShi(customerBaseDTO.getResidentialCityName());
        orderInfoAddDTO.setJuZhuXiangXi(customerBaseDTO.getResidentialDetailedAddress());

        orderInfoAddDTO.setGongYangZiNvNum(ZhonHengDependentChildrenCode.getCodeByNumber(ObjUtil.defaultIfNull(customerBaseDTO.getNumberOfDependents(), 0)));
        orderInfoAddDTO.setHomePhone(customerBaseDTO.getPhone());
        orderInfoAddDTO.setJuZhuState(switch (Objects.requireNonNull(DictResidentStatus.fromCode(customerBaseDTO.getResideStatus()))) {
            case OWNED_WITH_MORTGAGE -> 1;
            case OWNED_NO_MORTGAGE -> 2;
            case RELATIVE_HOUSING -> 4;
            case RENTED -> 6;
            default -> 7;
        });
        if (customerBaseDTO.getStartResideDate() != null) {
            orderInfoAddDTO.setQiJuTime(LocalDate.parse(customerBaseDTO.getStartResideDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } else {
            orderInfoAddDTO.setQiJuTime(LocalDate.now());
        }
        DataAreaDTO cityByVehicleNumber = new DataAreaDTO();
        if (carBaseDTO.getVehicleNumber() != null) {
            cityByVehicleNumber = fundApproveMapper.getCityByVehicleNumber(carBaseDTO.getVehicleNumber().substring(0, 2));
        }
        if (customerBaseDTO.getIdCardCityName() != null) {
            cityByVehicleNumber = fundApproveMapper.getCityByVehicleNumber(carBaseDTO.getVehicleNumber().substring(0, 2));
        }
        orderInfoAddDTO.setYouBian(ObjUtil.defaultIfBlank(cityByVehicleNumber.getPostcode(),"000000"));
        orderInfoAddDTO.setFangWuZuJin(Convert.toBigDecimal(customerBaseDTO.getHouseRent(),BigDecimal.ZERO));
        orderInfoAddDTO.setDanWeiName(customerBaseDTO.getEnterpriseName());
        orderInfoAddDTO.setSuoShuBuMen(ObjUtil.defaultIfBlank(customerBaseDTO.getDepartment(), "未知"));
        orderInfoAddDTO.setZhiWuType(1);
        orderInfoAddDTO.setDanWeiType(switch (Objects.requireNonNull(DictCompanyType.fromCode(customerBaseDTO.getEnterpriseNature()))) {
            case STATE_OWNED_ENTERPRISE -> 1;
            case PRIVATE_ENTERPRISE -> 2;
            case FOREIGN_ENTERPRISE -> 3;
            case PUBLIC_INSTITUTION -> 4;
            case INDIVIDUAL_BUSINESS -> 5;
            default -> 6;
        });
        orderInfoAddDTO.setHangYeType("21");
        orderInfoAddDTO.setDanWeiDiSheng(customerBaseDTO.getEnterpriseProName());
        orderInfoAddDTO.setDanWeiDiShi(customerBaseDTO.getEnterpriseCityName());
        orderInfoAddDTO.setDanWeiDiXiangXi(customerBaseDTO.getEnterpriseDetailsAddress());
        orderInfoAddDTO.setDanWeiPhone(ObjUtil.defaultIfBlank(customerBaseDTO.getEnterprisePhone(), customerBaseDTO.getPhone()));
        String currentUnitStartDate = customerBaseDTO.getCurrentUnitStartDate();
        if (currentUnitStartDate != null) {

            orderInfoAddDTO.setDanWeiKaiShiTime(LocalDate.parse(currentUnitStartDate, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        } else {
            orderInfoAddDTO.setDanWeiKaiShiTime(LocalDate.now());
        }
        orderInfoAddDTO.setYueZongShouRu(convertAmount(customerBaseDTO.getMonthlyIncome()));
        orderInfoAddDTO.setFaXinRi(Convert.toInt(customerBaseDTO.getPayday(),1));
        orderInfoAddDTO.setYueZongZhiChu(convertAmount(customerBaseDTO.getMonthlyExpenditure()));
        List<ContactPersonDTO> contactPersonList = fundApproveMapper.getContactPerson(customerBaseDTO.getOrderId());
        if (CollUtil.isNotEmpty(contactPersonList)) {
            List<OrderInfoAddDTO.LianXiRenInfoDTO> lianXiRenInfo = new ArrayList<>();
            for (int i = 0; i <contactPersonList.size(); i++) {
                ContactPersonDTO person = contactPersonList.get(i);
                OrderInfoAddDTO.LianXiRenInfoDTO lianXiRenInfoDTO = new OrderInfoAddDTO.LianXiRenInfoDTO();
                lianXiRenInfoDTO.setLXRName(person.getContactName());
                if (i==0){
                    if (orderInfoAddDTO.getHunYingState() == 2){
                        lianXiRenInfoDTO.setLXRGuanXi(6);
                    }
                }
                if (i==0 && ObjUtil.equals(person.getRelation(), DictContactRelation.RELATIVE.getCode())){
                    //我的生日 idCardNum
                    String myBirthDay = getBirthDateByIdCard(orderInfoAddDTO.getIdCardNum());
                    //性别
                    String genderByIdCard = getGenderByIdCard(person.getIdNumber());
                    //生日
                    String otherBirthDay = getBirthDateByIdCard(person.getIdNumber());
                    //第一联系人关系
                    int relationship = getRelationship(myBirthDay, otherBirthDay, genderByIdCard);
                    lianXiRenInfoDTO.setLXRGuanXi(relationship);
                } else {
                    lianXiRenInfoDTO.setLXRGuanXi(switch (Objects.requireNonNull(DictContactRelation.fromCode(person.getRelation()))) {
                        case MOTHER -> 3;
                        case FATHER -> 4;
                        case FRIEND -> 5;
                        case CHILDREN -> 7;
                        case COLLEAGUE -> 2;
                        case SPOUSE -> 6;
                        default -> 1;
                    });
                }
                lianXiRenInfoDTO.setLXRPhone(person.getContactPhone());
                lianXiRenInfoDTO.setLXRIdCareNum(person.getIdNumber());
                lianXiRenInfoDTO.setLXRDanWeiPhone(person.getContactPhone());
                lianXiRenInfoDTO.setLXRDanWeiName("未知");
                lianXiRenInfo.add(lianXiRenInfoDTO);
            }
            orderInfoAddDTO.setLianXiRenInfo(lianXiRenInfo);
        }


        //放款收款账号
        List<BankAccountSignEntity> signCards = fundApproveMapper.getSignCard(orderId, 5);
        if (CollUtil.isNotEmpty(signCards)) {
            BankAccountSignEntity bankAccountSign = signCards.get(0);
            orderInfoAddDTO.setQKYinHangName(bankAccountSign.getName());
            orderInfoAddDTO.setQKYinHangNum(bankAccountSign.getBankCardNumber());
            orderInfoAddDTO.setQKYinHang(bankAccountSign.getBankName());
        }


        orderInfoAddDTO.setFuJianInfo(getPaymentFileSupmentList(orderId));


        orderInfoAddDTO.setShiBieNum(carBaseDTO.getVin());
        orderInfoAddDTO.setFaDongJiNum(carBaseDTO.getEngineNumber());
        orderInfoAddDTO.setChaPaiNum(carBaseDTO.getVehicleNumber());
        orderInfoAddDTO.setPinPai(carBaseDTO.getBrand());
        orderInfoAddDTO.setCheXi(carBaseDTO.getVehicleSeries());
        orderInfoAddDTO.setXingHao(carBaseDTO.getVehicleModel());
        String productionDate = carBaseDTO.getProductionDate();
        if (productionDate != null) {

            orderInfoAddDTO.setChuChangTime(LocalDate.parse(productionDate, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        }
        orderInfoAddDTO.setZhuCeTime(carBaseDTO.getRegisterDate());
        orderInfoAddDTO.setCheLing(carBaseDTO.getVehicleAge());
        String buyDate = carBaseDTO.getBuyDate();
        if (buyDate != null) {

            orderInfoAddDTO.setGouMaiTime(LocalDate.parse(buyDate, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        }
        VehicleBodyTypeDTO vehicleBodyType = fundApproveMapper.getVehicleBodyType(carBaseDTO.getVin());
        orderInfoAddDTO.setCheLiangType(getVehicleType(vehicleBodyType));
        Car300DataEntity car300Info = fundApproveMapper.getCar300Info(carBaseDTO.getVin());
        orderInfoAddDTO.setRanLiao(getFuelType(car300Info));
        orderInfoAddDTO.setDangWei(getTransmission(carBaseDTO.getTransmissionType()));
        orderInfoAddDTO.setPaiLiang(carBaseDTO.getDisplacement());

        if (car300Info.getDealerLowBuyPriceExcellent() != null && car300Info.getDealerLowSoldPriceExcellent() != null && car300Info.getIndividualPriceExcellent() != null) {
            Map<String, BigDecimal> evaluationDataMap = getStringBigDecimalMap(car300Info);
            orderInfoAddDTO.setChe300EvaluationData(JSONObject.toJSONString(evaluationDataMap));
            orderInfoAddDTO.setDealerMaxPurchasePrice(String.valueOf(car300Info.getIndividualLowSoldPriceExcellent().multiply(BigDecimal.valueOf(100)).intValue()));
        }

        if (StrUtil.isNotBlank(carBaseDTO.getEmission())) {
            orderInfoAddDTO.setPaiFangBiaoZhun(carBaseDTO.getEmission());
        } else {
            //如果是纯电
            if (ObjUtil.equals(orderInfoAddDTO.getRanLiao(), 4)) {
                //纯电动
                orderInfoAddDTO.setPaiFangBiaoZhun("纯电车");
            } else {
                orderInfoAddDTO.setPaiFangBiaoZhun("国VI");
            }
        }
        orderInfoAddDTO.setDingYuan(carBaseDTO.getSeatCount());
        orderInfoAddDTO.setBiaoXianLiCheng(carBaseDTO.getMileage().intValue());
        orderInfoAddDTO.setSuoYouRenXingZhi(1);
        orderInfoAddDTO.setGuoHuCiShu(Convert.toInt(carBaseDTO.getTransferTimes()));
        orderInfoAddDTO.setZongHePingJia(switch (Convert.toStr(carBaseDTO.getAppraiseLevel(), "C")) {
            case "A" -> 1;
            case "B" -> 2;
            case "C" -> 3;
            case "D" -> 4;
            case "E" -> 5;
            case "F" -> 6;
            default -> 3;
        });
        //获取门店信息
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, ()-> new BusinessException("订单不存在"));
        String storeName = userFeign.getDeptById(orderInfo.getDeptId()).getData();
        StoreAddressInfoEntity storeAddressInfo = fundApproveMapper.getStoreAddressInfo(storeName);
        Assert.notNull(storeAddressInfo, ()-> new BusinessException("门店地址信息不存在"));
        orderInfoAddDTO.setMenDianSheng(storeAddressInfo.getProvinceName());
        orderInfoAddDTO.setMenDianShi(storeAddressInfo.getCityName());
        orderInfoAddDTO.setMenDianXiangQing(storeAddressInfo.getDetail());
        orderInfoAddDTO.setHuoKeType(1);

        List<OrderAmountEntity> orderAmountEntity = fundApproveMapper.getOrderAmountEntity(orderId);
        if (CollUtil.isNotEmpty(orderAmountEntity)) {
            OrderAmountEntity orderAmountEntity1 = orderAmountEntity.get(0);
            BigDecimal menDianPingGuEDu = BigDecimal.ZERO;

            String carNo = carBaseDTO.getVehicleNumber();
            if (carNo.contains("京") ||
                    (carBaseDTO.getVehicleNumber().length() == 7 &&
                        (carNo.contains("沪") && !carNo.contains("沪C"))
                         || carNo.contains("粤A") || carNo.contains("粤B") || carNo.contains("浙A")
                    )
            ) {
                //取沪牌车的车贷额度
                PreFundInfoEntity preFundInfo = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                                .eq(PreFundInfoEntity::getPreId, customerBaseDTO.getPreId())
                                .eq(PreFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                                .eq(PreFundInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(PreFundInfoEntity::getCreateTime)
                        , false
                );
                menDianPingGuEDu = preFundInfo.getCreditAmount();
            } else {
                menDianPingGuEDu = orderAmountEntity1.getSoftReviewAmount();
            }
            orderInfoAddDTO.setRuanPingEDu(orderAmountEntity1.getSoftReviewAmount());
            orderInfoAddDTO.setMenDianPingGuEDu(menDianPingGuEDu);
        }
        // 抵押物信息备注
        orderInfoAddDTO.setDiYaWuNote(1);
        // 业务类型
        orderInfoAddDTO.setYeWuType(1);
        // 业务属性
        orderInfoAddDTO.setYeWuShuXing(2);
        // 产品名称
        String productName;
        ProducteJoinEnum producteJoinEnum = null;
        if (null != customerBaseDTO.getProductName()){
            producteJoinEnum = ProducteJoinEnum.getProducteJoinEnum(customerBaseDTO.getProductName());
        }
        if (null != producteJoinEnum){
            productName = producteJoinEnum.getProdcutCode();
        }else{
            productName = "LH-"+customerBaseDTO.getTerm();
        }

        if (!envUtil.isPrd()) {
            // 龙通K-等额本息-36期-20250318     龙诚K-等额本息-36期-20241211
            productName = "龙通K-等额本息-36期-20250318";
        }

        orderInfoAddDTO.setShenQingChanPin(productName);
        orderInfoAddDTO.setShenQingYongTu(dictUtils.getDictLabel(GlobalConstants.DictType.FUNDS_PURPOSE.name(), Convert.toInt(customerBaseDTO.getApplyPurpose())));

        orderInfoAddDTO.setShenQingEDu(customerBaseDTO.getApplyAmount());
        // 每月最高接受可还款额
        orderInfoAddDTO.setMeiYueZuiGaoHuanKuanE(customerBaseDTO.getMonthlyIncome());

        if (carBaseDTO.getAnnualDate() != null) {

            // 车辆年审到期日
            orderInfoAddDTO.setCheLiangNianShenDaoQiRi(LocalDate.parse(carBaseDTO.getAnnualDate(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        }
        if (ObjUtil.isNotNull(carBaseDTO.getCompulsoryDate())){
            // 保险到期日（三者）、保险到期日（车损），  保险到期日（交强险） 都取 交强险到期日，
            LocalDate compulsoryDate = LocalDate.parse(carBaseDTO.getCompulsoryDate(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
            // 三者保险到期日
            if (carBaseDTO.getThirdInsuranceDate() != null) {
                orderInfoAddDTO.setBaoXianSanZheDaoQiRi(LocalDate.parse(carBaseDTO.getThirdInsuranceDate(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
            } else {
                orderInfoAddDTO.setBaoXianSanZheDaoQiRi(compulsoryDate);
            }
            // 车损险到期日
            if (carBaseDTO.getVehicleInsuranceDate() != null) {
                orderInfoAddDTO.setBaoXianCheSunDaoQiRi(LocalDate.parse(carBaseDTO.getVehicleInsuranceDate(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
            } else {
                orderInfoAddDTO.setBaoXianCheSunDaoQiRi(compulsoryDate);
            }
            // 交强险到期日
            orderInfoAddDTO.setBaoXianJiaoQiangDaoQiRi(compulsoryDate);
        }

        // 是否有其他自置房产
        orderInfoAddDTO.setIsQiTaZiZhiFangChan(1);

        // 同盾报告
        orderInfoAddDTO.setTongDunBaoGao("");
        // 主借人验真结果
        String generateJzyReport = generateJzyReport(1, customerBaseDTO.getName(), customerBaseDTO.getIdNumber(), customerBaseDTO.getPhone());
        orderInfoAddDTO.setYanZhenJieGuo(generateJzyReport);

        // 符号律动结果
        String fuHaoLvDongJieGuo = null;
        try {
            fuHaoLvDongJieGuo = generateFhldReport(customerBaseDTO.getIdNumber());
        } catch (Exception e) {
            log.error("HengTongServiceImpl.buildOrderInfoAddDTO generateLegalReport e:{}:", e.getMessage(), e);
            throw new BusinessException("符号律动报告生成失败");
        }
        if (ObjUtil.isNull(fuHaoLvDongJieGuo)) {
            log.info("HengTongServiceImpl.yuShouXinAdd generateLegalReport fuHaoLvDongJieGuo is null orderId:{}", orderId);
            throw new BusinessException("符号律动报告生成失败");
        }
        orderInfoAddDTO.setFuHaoLvDongJieGuoXS(fuHaoLvDongJieGuo);

        orderInfoAddDTO.setFuHaoLvDongJieGuo(fuHaoLvDongJieGuo);
        // 风控模型结果
        String riskPolicyReport = generateRiskPolicyReport(orderId);
        if (StrUtil.isBlank(riskPolicyReport)) {
            log.info("HengTongServiceImpl.yuShouXinAdd generateRiskPolicyReport riskPolicyReport is null orderId:{}", orderId);
            throw new BusinessException("风控模型结果生成失败");
        }
        orderInfoAddDTO.setFenKongMoXingJieGuo(riskPolicyReport);


        // 合作方客户编号
        orderInfoAddDTO.setSPUserNum(Convert.toStr(customerBaseDTO.getOrderId()));
        // 合作方订单编号
        orderInfoAddDTO.setSPOrderNum(customerBaseDTO.getOrderNumber());
        // 合作方业务员名单
        Result<UserInfoVO> userInfoVOResult = userFeign.searchByUserId(customerBaseDTO.getManagerId());
        if (Result.isSuccess(userInfoVOResult)) {
            UserInfoVO userInfoVO = userInfoVOResult.getData();
            orderInfoAddDTO.setSPYeWuYuan(userInfoVO.getName() + "+" +  userInfoVO.getJobNumber());

        }
        orderInfoAddDTO.setSPDAQV(customerBaseDTO.getRegionName());
        orderInfoAddDTO.setSPMENDIAN(customerBaseDTO.getStoreName());
        orderInfoAddDTO.setSPTUANDUI(customerBaseDTO.getTeamName());
        orderInfoAddDTO.setEmail(ObjUtil.defaultIfBlank(customerBaseDTO.getEmail(),"未知@unknown.com"));

        List<FddFileVO> fddFile = fundApproveMapper.getFddFile(customerBaseDTO.getPreId(), 0);
        //浩程达授权书
        List<FddFileVO> hcdList = fddFile.stream()
                .filter(file -> file.getFileName().contains(ZhongHengFddFileEnums.JUNHE_CREDIT_CHECK_AUTHORIZATION.getName()))
                .sorted(Comparator.comparing(FddFileVO::getCreateTime).reversed())
                .limit(1)
                .toList();
        if (!CollUtil.isEmpty(hcdList)) {
            FddFileVO fddFileVO = hcdList.get(0);
            //法大大的url（浩程达人行征信查询授权书）
            orderInfoAddDTO.setShouQuanShu2(getViewResourceUrl(fddFileVO.getResourceId()));

        }
        List<FddFileVO> hrList = fddFile.stream()
                .filter(file ->file.getFileName().contains(ZhongHengFddFileEnums.ZHONGHENG_INFORMATION_QUERY_AUTHORIZATION_TENANT.getName()))
                .sorted(Comparator.comparing(FddFileVO::getCreateTime).reversed())
                .limit(1)
                .toList();
        if (!CollUtil.isEmpty(hrList)) {
            FddFileVO fddFileVO = hrList.get(0);
            //法大大的url（其他三方数据查询授权书）
            orderInfoAddDTO.setShouQuanShu1(getViewResourceUrl(fddFileVO.getResourceId()));
        }

        // 颜色
        orderInfoAddDTO.setYanSe(carBaseDTO.getVehicleColor());
        // 评估人
        orderInfoAddDTO.setPingGuRen(ObjUtil.defaultIfBlank(carBaseDTO.getAppraiseName(), "蓝本价评估"));

        // 签约类型
        orderInfoAddDTO.setQianYueType(11);

        // 第一联系人验真结果
        contactPersonList.stream().min(Comparator.comparingInt(ContactPersonDTO::getIndex))
                .ifPresent(item -> {
                    String generateJzyReportFirst = generateJzyReport(2, item.getContactName(), item.getIdNumber(), item.getContactPhone());
                    orderInfoAddDTO.setDiYiLianXiRenYanZhenJieGuo(generateJzyReportFirst);
                });

        // 解压情况
        orderInfoAddDTO.setJieYaQingKuang(4);

        orderInfoAddDTO.setShenPiType(2);
        // 资方授权书
        List<OrderInfoAddDTO.ZiFangShouQuanShuDTO> attachments = new ArrayList<>();
        List<FddFileVO> fddFileByFundIdList = fundApproveMapper.getFddFile(customerBaseDTO.getPreId(), 0);
        //授权书文件名
        List<String> accreditFileNames = ZhongHengFddFileEnums.getAccreditFileNames();
        for (FddFileVO fddFileVO : fddFileByFundIdList) {
            boolean containsSubstring = accreditFileNames.stream()
                    .anyMatch(accreditFileName -> fddFileVO.getFileName().contains(accreditFileName));
            if (!containsSubstring) {
                continue;
            }

            OrderInfoAddDTO.ZiFangShouQuanShuDTO ziFangShouQuanShu = new OrderInfoAddDTO.ZiFangShouQuanShuDTO();
            String resourceUrl = getViewResourceUrl(fddFileVO.getResourceId());

            ziFangShouQuanShu.setSQSYuLanURL(resourceUrl);
            ziFangShouQuanShu.setSQSXiaZaiURL(resourceUrl);

            // 名称
            ziFangShouQuanShu.setSQSName(fddFileVO.getFileName());
            if (fddFileVO.getFileName().contains(ZhongHengFddFileEnums.REJECTED_GUARANTEE_APPLICATION.getName())) {
                //君合-委托担保申请 替换 通汇-委托担保申请
                ziFangShouQuanShu.setSQSName(fddFileVO.getFileName().replace("华通-委托担保服务申请书", "通汇-委托担保申请"));
            }
            if (fddFileVO.getFileName().contains(ZhongHengFddFileEnums.JUNHE_CREDIT_CHECK_AUTHORIZATION.getName())) {
                //华通-个人征信查询报送授权书 替换 君合-征信查询授权书
                ziFangShouQuanShu.setSQSName(fddFileVO.getFileName().replace("华通-个人征信查询报送授权书", "君合-征信查询授权书"));
            }
            if (fddFileVO.getFileName().contains(ZhongHengFddFileEnums.CAR_SERVICE_CONTRACT.getName())) {
                //汇丰-汽车服务 替换 通汇-汽车服务合同
                ziFangShouQuanShu.setSQSName(fddFileVO.getFileName().replace("汇丰-汽车服务", "通汇-汽车服务合同"));
            }
            //授权书签署时间 (必填) 格式：YYYY-MM-dd HH:mm:ss
            ziFangShouQuanShu.setSQSQianShuTime(Convert.toLocalDateTime(fddFileVO.getCreateTime()));
            attachments.add(ziFangShouQuanShu);
        }
        orderInfoAddDTO.setZiFangShouQuanShu(attachments);

        // 我方预审批订单编号
        orderInfoAddDTO.setYSXOrderNum(fundInfoEntity.getCreditReqNo());
        // 最终产品
        orderInfoAddDTO.setZuiZhongChanPin(productName);
        // 最终额度
        orderInfoAddDTO.setZuiZhongEDu(customerBaseDTO.getCustomerConfirmAmount());
        // 加押状态
        Integer advanceMortgageState = customerBaseDTO.getAdvanceMortgageState();
        //放款方式
        Integer paymentType = orderInfo.getPaymentType();

        CustomerMortgageInfoEntity customerMortgageInfo = customerMortgageInfoMapper.selectOne(new MPJLambdaWrapper<CustomerMortgageInfoEntity>()
                .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                .last("LIMIT 1")
        );
        Assert.notNull(customerMortgageInfo, ()-> new BusinessException("抵押信息不存在"));
        //抵押方式
        Integer mortgageType = customerMortgageInfo.getMortgageType();
        // 线上抵押：无需文件
        if (mortgageType == 0) {
            orderInfoAddDTO.setJiaYaStatus(3);
            orderInfoAddDTO.setJiaYaState(3);
        }
        // 线下抵押
        else {
            // 根据预加押状态判断
            if (paymentType == 2) { // 已加押
                orderInfoAddDTO.setJiaYaState(2);
                orderInfoAddDTO.setJiaYaStatus(2);
            } else if (paymentType == 1) { // 预加押未完成
                orderInfoAddDTO.setJiaYaState(1);
                orderInfoAddDTO.setJiaYaStatus(1);
            } else {
                throw new BusinessException("非法的预加押状态");
            }
        }
        // 加押公司
        orderInfoAddDTO.setJiaYaGongSi(1);
        // 授信结果
        orderInfoAddDTO.setShouXinJieGuo(1);
        // 法大大客户id
        orderInfoAddDTO.setFaDaDaKeHuId(getFddUserNumber(customerBaseDTO.getIdNumber()));
        // GPS平台id
        String gpsCarId = fundApproveMapper.getOrderGpsInfo(orderId);
        orderInfoAddDTO.setGPSCarId(StrUtil.isNotBlank(gpsCarId) ? gpsCarId : carBaseDTO.getVin());
        // 抵押登记时间
        orderInfoAddDTO.setDiYaDengJiTime(getMortgageDate(orderId));
        //  银行卡签约信息
        orderInfoAddDTO.setOderYinHangQianYue(getBankCardBindInfo(orderId));
        // 合同信息
        orderInfoAddDTO.setHeTongInfo(getContractFile(orderId,orderInfo.getPreId()));
        //新车参考价  厂商指导家
        OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(new MPJLambdaWrapper<OrderVehicleInfoEntity>()
                .eq(OrderVehicleInfoEntity::getVin, carBaseDTO.getVin())
                .eq(OrderVehicleInfoEntity::getOrderId, orderId)
                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                .orderByDesc(OrderVehicleInfoEntity::getCreateTime)
                .last("LIMIT 1")
        );
        if (ObjUtil.isNull(orderVehicleInfoEntity) || ObjUtil.isNull(orderVehicleInfoEntity.getGuidePrice())){
            throw new BusinessException("厂商指导价不存在");
        }
        orderInfoAddDTO.setXinCheCanKaoJia(orderVehicleInfoEntity.getGuidePrice().multiply(BigDecimal.valueOf(10000)).toString());

        return orderInfoAddDTO;
    }

    private static @NotNull Map<String, BigDecimal> getStringBigDecimalMap(Car300DataEntity car300Info) {
        Map<String, BigDecimal> evaluationDataMap = new LinkedHashMap<>();
        evaluationDataMap.put(EvaluationDataEnum.DEALER_HIGH_SOLD_PRICE_EXCELLENT.getValue(), car300Info.getDealerHighSoldPriceExcellent());
        evaluationDataMap.put(EvaluationDataEnum.DEALER_HIGH_BUY_PRICE_EXCELLENT.getValue(), car300Info.getDealerHighBuyPriceExcellent());
        evaluationDataMap.put(EvaluationDataEnum.DEALER_PRICE_EXCELLENT.getValue(), car300Info.getDealerPriceExcellent());
        evaluationDataMap.put(EvaluationDataEnum.INDIVIDUAL_PRICE_EXCELLENT.getValue(), car300Info.getIndividualPriceExcellent());
        evaluationDataMap.put(EvaluationDataEnum.DEALER_LOW_SOLD_PRICE_EXCELLENT.getValue(), car300Info.getDealerLowSoldPriceExcellent());
        evaluationDataMap.put(EvaluationDataEnum.INDIVIDUAL_LOW_SOLD_PRICE_EXCELLENT.getValue(), car300Info.getIndividualLowSoldPriceExcellent());
        evaluationDataMap.put(EvaluationDataEnum.DEALER_LOW_BUY_PRICE_EXCELLENT.getValue(), car300Info.getDealerLowBuyPriceExcellent());
        // 排序从低到高
        return evaluationDataMap.entrySet().stream()
                .sorted(Map.Entry.comparingByValue())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
    }

    /**
     * 抵押代办完成时间
     * @param orderId
     * @return
     */
    private LocalDate getMortgageDate(Integer orderId) {
        Boolean mortageTimeSwitch = switchUtils.checkSwitch(SwitchConstants.TONGHUI_PAY_MORTGAGE_TIME);
        if (mortageTimeSwitch) {
            CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                    .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                    .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                    .last("LIMIT 1"));
            if (ObjUtil.isNotNull(customerMortgageInfoEntity) && ObjUtil.equals(customerMortgageInfoEntity.getMortgageType(), 0)
                    && ObjUtil.isNotNull(customerMortgageInfoEntity.getMortgageTime())) {
                return customerMortgageInfoEntity.getMortgageTime().toLocalDate();
            }
        }

        return fundApproveMapper.getMortgageDate(orderId);
    }

    /**
     * 获取合约文件
     *
     * @param orderId 订单 ID
     * @return {@link List }<{@link OrderInfoAddDTO.HeTongInfoDTO }>
     */
    private List<OrderInfoAddDTO.HeTongInfoDTO> getContractFile(Integer orderId, Integer preId) {
        // HTName	合同名称	String	50	必填
        //QianShuShiJian	签署时间	String	50	必填	格式：YYYY-MM-dd  HH:mm:ss
        //YuLanUrl	预览地址	String	500	必填
        //XiaZaiUrl	下载地址	String	500	必填
        List<FddFileVO> fddAuthorizationList = fundApproveMapper.getFddFile(preId, 0);
        List<FddFileVO> fddContractList = fundApproveMapper.getFddFile(orderId, 1);
        fddContractList.addAll(fddAuthorizationList);
        List<String> contractFileNames = ZhongHengFddFileEnums.getContractFileNames();

        Map<String, FddFileVO> stringFddFileVOHashMap = fddContractList.stream()
                .filter(fddFileVO -> contractFileNames.stream().anyMatch(contractFileName -> fddFileVO.getFileName().contains(contractFileName)))
                .collect(Collectors.toMap(
                        fddFileVO -> contractFileNames.stream().filter(contractFileName -> fddFileVO.getFileName().contains(contractFileName)).findFirst().orElse(null),
                        fddFileVO -> fddFileVO,
                        (existing, replacement) -> existing // 保留第一个匹配的值，忽略后续的重复值
                ));

        return stringFddFileVOHashMap.values().stream()
                .map(item -> {
                    OrderInfoAddDTO.HeTongInfoDTO heTongInfoDTO = new OrderInfoAddDTO.HeTongInfoDTO();
                    //判断是否未融资租赁合同
                    if (item.getFileName().contains(ZhongHengFddFileEnums.FINANCING_LEASE_CONTRACT.getName())) {
                        //需要传入合同号
                        heTongInfoDTO.setHeTongNum(fundApproveMapper.getContractNum("VEHICLE_FINANCIAL_LEASE_CONTRACT_NUMBER", orderId));
                    }
                    // 合同名称
                    heTongInfoDTO.setHTName(item.getFileName());
                    if (item.getFileName().contains(ZhongHengFddFileEnums.REJECTED_GUARANTEE_APPLICATION.getName())) {
                        //华通-委托担保服务申请书 替换 通汇-委托担保申请
                        heTongInfoDTO.setHTName(item.getFileName().replace("华通-委托担保服务申请书", "通汇-委托担保申请"));
                    }
                    if (item.getFileName().contains(ZhongHengFddFileEnums.JUNHE_CREDIT_CHECK_AUTHORIZATION.getName())) {
                        //华通-个人征信查询报送授权书 替换 君合-征信查询授权书
                        heTongInfoDTO.setHTName(item.getFileName().replace("华通-个人征信查询报送授权书", "君合-征信查询授权书"));
                    }
                    if (item.getFileName().contains(ZhongHengFddFileEnums.CAR_SERVICE_CONTRACT.getName())) {
                        //汇丰-汽车服务 替换 通汇-汽车服务合同
                        heTongInfoDTO.setHTName(item.getFileName().replace("汇丰-汽车服务", "通汇-汽车服务合同"));
                    }
                    if (item.getFileName().contains(ZhongHengFddFileEnums.COMMISSION_STATEMENT.getName())) {
                        //通汇-声明 替换 通汇-委托担保合同
                        heTongInfoDTO.setHTName(item.getFileName().replace("通汇-声明", "通汇-委托担保合同"));
                    }
                    if (item.getFileName().contains(ZhongHengFddFileEnums.COMMISSION_STATEMENT.getName())) {
                        //通汇-声明 替换 通汇-委托担保合同
                        heTongInfoDTO.setHTName(item.getFileName().replace("通汇-声明", "通汇-委托担保合同"));
                    }
                    if (item.getFileName().contains(ZhongHengFddFileEnums.ZHONGHENG_INFORMATION_QUERY_AUTHORIZATION_TENANT.getName())) {
                        //中恒通汇-信息查询授权书-承租人 替换 中恒-信息查询授权书
                        heTongInfoDTO.setHTName(item.getFileName().replace( "中恒通汇-信息查询授权书-承租人","中恒-信息查询授权书"));
                    }

                    // 签署时间
                    heTongInfoDTO.setQianShuShiJian(Convert.toLocalDateTime(item.getCreateTime()));
                    String viewUrl = getViewResourceUrl(item.getResourceId());
                    // 预览地址
                    heTongInfoDTO.setYuLanUrl(viewUrl);
                    // 下载地址
                    heTongInfoDTO.setXiaZaiUrl(viewUrl);
                    return heTongInfoDTO;
                })
                .collect(Collectors.toList());

    }


    /**
     * 获取查看资源 URL
     *
     * @param resourceId 资源 ID
     * @return {@link String }
     */
    private String getViewResourceUrl(String resourceId) {
        if (StrUtil.isNotEmpty(resourceId)) {
            Result<String> stringResult = resourceFeign.longTimeAccessRouteRequest(resourceId);
            if (Result.isSuccess(stringResult)) {
                return stringResult.getData();
            }
        }
        return resourceId;
    }

    /**
     * 获取银行卡绑定信息
     *
     * @param orderId 订单 ID
     * @return {@link List }<{@link OrderInfoAddDTO.OderYinHangQianYueDTO }>
     */
    private List<OrderInfoAddDTO.OderYinHangQianYueDTO> getBankCardBindInfo(Integer orderId) {
        // YHKQianYueNum	签约号	String	50	必填
        //KaiHuHang	开户行	String	50	必填
        //KaNum	卡号	String	50	必填
        //YLPhone	预留手机号	String	50	必填
        // TODO
        //放款收款账号
        List<Integer> bindTypeList = Arrays.asList(4, 5);
        List<OrderInfoAddDTO.OderYinHangQianYueDTO> orderInfoAddDTO = new ArrayList<>();
        bindTypeList.forEach(bindType -> {
            List<BankAccountSignEntity> signCards = fundApproveMapper.getSignCard(orderId, bindType);
            if (CollUtil.isNotEmpty(signCards)) {
                orderInfoAddDTO.addAll(signCards.stream().map(this::convertToQianYueDTO).toList());
            }
        });
        return orderInfoAddDTO;
    }

    private OrderInfoAddDTO.OderYinHangQianYueDTO convertToQianYueDTO(BankAccountSignEntity item) {
        OrderInfoAddDTO.OderYinHangQianYueDTO oderYinHangQianYueDTO = new OrderInfoAddDTO.OderYinHangQianYueDTO();
        oderYinHangQianYueDTO.setYHKQianYueNum(item.getSignProtocolNo());
        oderYinHangQianYueDTO.setKaiHuHang(item.getBankName());
        oderYinHangQianYueDTO.setKaNum(item.getBankCardNumber());
        oderYinHangQianYueDTO.setYLPhone(item.getPhone());
        oderYinHangQianYueDTO.setYHKQYType(ObjUtil.equals(item.getSignPlate(), SignPlateEnum.HENG_TONG_TONG_LIAN) ? "1" : "2");
        return oderYinHangQianYueDTO;
    }

    private String getFddUserNumber(String idNumber) {
        return fundApproveMapper.getFddUserNumber(idNumber);
    }

    private Integer getTransmission(String transmission) {

        if (StrUtil.isNotEmpty(transmission)) {
            if (transmission.contains("CVT") || transmission.contains("DHT") || transmission.contains("自动") || transmission.contains("无极")
                    || transmission.contains("手自")) {
                return 2;
            }
            if (transmission.contains("手动")) {
                return 1;
            }
        }
        return 2;
    }

    /**
     * 燃料<p>
     * * 必填	码值如下： 1:汽油    2:柴油    3:插电混动    4:纯电动 5. 油电混动 6. 增程式 CNG(天然气)
     *
     * @param car300Info
     * @return
     */
    private Integer getFuelType(Car300DataEntity car300Info) {
        Integer fuelType = Convert.toInt(car300Info.getFuelType(), 0);
        Integer greenType = Convert.toInt(car300Info.getGreenType(), 0);

        if (greenType == 1) {
            return 4;
        } else if (greenType == 2 || greenType == 4 || fuelType == 3) {
            return 3;
        } else if (fuelType == 8 || fuelType == 5) {
            return 6;
        } else {
            if (fuelType == 1) {
                return 2;
            } else {
                return 1;
            }
        }
    }

    /**
     * 车辆类型  1:三厢    2：两厢    3：SUV    4:MPV
     *
     * @return
     */
    private Integer getVehicleType(VehicleBodyTypeDTO vehicleBodyType) {

        if (vehicleBodyType == null) {
            return 2;
        }

        String seriesType = vehicleBodyType.getSeriesType();
        String carBodyForm = vehicleBodyType.getCarBodyForm();

        if (StrUtil.isNotEmpty(seriesType) && seriesType.equals("MPV")) {
            return 4;
        } else if (StrUtil.isNotEmpty(seriesType) && seriesType.equals("SUV")) {
            return 3;
        } else {
            if (StrUtil.isNotEmpty(carBodyForm) && carBodyForm.equals("两厢")) {
                return 2;
            }
            if (StrUtil.isNotEmpty(carBodyForm) && carBodyForm.equals("三厢")) {
                return 1;
            }
        }
        return 1;
    }


    /**
     * 获取付款列表
     *
     * @param orderId 订单 ID
     * @return {@link List }<{@link OrderInfoAddDTO.FuJianInfoDTO }>
     */
    private List<OrderInfoAddDTO.FuJianInfoDTO> getPaymentFileSupmentList(Integer orderId) {

        Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(new FundResourceDTO().setFund(FundEnum.ZHONG_HENG_TONG_HUI)
                .setType(3).setLinkId(orderId)
        );
        if (!Result.isSuccess(listResult)) {
            if (StrUtil.isNotBlank(listResult.getMsg())){
                throw new BusinessException(listResult.getMsg());
            } else {
                throw new BusinessException("未获取到相关附件");
            }
        }

        List<OrderInfoAddDTO.FuJianInfoDTO> fuJianInfoDTOS =
                Optional.ofNullable(listResult).map(Result::getData).orElse(null).stream().map(item -> {
                    OrderInfoAddDTO.FuJianInfoDTO fuJianInfoDTO = new OrderInfoAddDTO.FuJianInfoDTO();
                    String fileCode = item.getFileCode();
                    String filePath = item.getFilePath();
                    fuJianInfoDTO.setFuJianTypeNum(fileCode);
                    fuJianInfoDTO.setFuJianURL(filePath);
                    return fuJianInfoDTO;

                }).toList();
        return fuJianInfoDTOS;
    }

    /**
     * 获取付款列表
     *
     * @param preId 订单 ID
     * @return {@link List }<{@link OrderInfoAddDTO.FuJianInfoDTO }>
     */
    private List<PreCreditReqDTO.FuJianURL> getPreFileSupmentList(Integer preId, FundPreBaseDTO fundPreBaseDTO) {

        Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(new FundResourceDTO().setFund(FundEnum.ZHONG_HENG_TONG_HUI)
                .setType(1).setLinkId(preId).setCustomerBaseInfo(new FundResourceDTO.CustomerBaseInfo().setCustomerIdNo(fundPreBaseDTO.getIdNumber())
                        .setPhone(fundPreBaseDTO.getPhone())
                )
        );
        Assert.isTrue(Result.isSuccess(listResult), "获取通汇文件列表失败");

        List<PreCreditReqDTO.FuJianURL> fuJianInfoDTOS =
                Optional.ofNullable(listResult).map(Result::getData).orElse(null).stream().map(item -> {
                    PreCreditReqDTO.FuJianURL fuJianInfoDTO = new PreCreditReqDTO.FuJianURL();
                    String fileCode = item.getFileCode();
                    String filePath = item.getFilePath();
                    fuJianInfoDTO.setFuJianTypeNum(fileCode);
                    fuJianInfoDTO.setFuJianURL(filePath);
                    return fuJianInfoDTO;

                }).toList();
        return fuJianInfoDTOS;
    }


    /**
     * * 月总收入<p>
     * * 必填	码值如下： 1：2000元以下
     * 2：2000元到5000元   3：5000元到8000元
     * 4：8000元到11000元   5：11000元到15000元
     * 6：15000元到20000元   7：20000到25000元
     * 8：25000元到30000元   9：30000以上
     *
     * @param monthlyIncome
     * @return
     */
    private Integer convertAmount(BigDecimal monthlyIncome) {
        if (monthlyIncome == null) {
            return 1;
        }
        if (monthlyIncome.compareTo(BigDecimal.valueOf(2000)) < 0) {
            return 1;
        } else if (monthlyIncome.compareTo(BigDecimal.valueOf(5000)) < 0) {
            return 2;
        } else if (monthlyIncome.compareTo(BigDecimal.valueOf(8000)) < 0) {
            return 3;
        } else if (monthlyIncome.compareTo(BigDecimal.valueOf(11000)) < 0) {
            return 4;
        } else if (monthlyIncome.compareTo(BigDecimal.valueOf(15000)) < 0) {
            return 5;
        } else if (monthlyIncome.compareTo(BigDecimal.valueOf(20000)) < 0) {
            return 6;
        } else if (monthlyIncome.compareTo(BigDecimal.valueOf(25000)) < 0) {
            return 7;
        } else if (monthlyIncome.compareTo(BigDecimal.valueOf(30000)) < 0) {
            return 8;
        } else {
            return 9;
        }
    }


    /**
     * 生成符律涉诉信息报告
     */
    public String generateFhldReport(String idNumber) {
        String jsonData = fundApproveMapper.getFhldInfo(idNumber);
        if (StrUtil.isBlank(jsonData)){
            return null;
        }

        StringBuilder report = new StringBuilder();
        AssessmentDto assessment = JSONUtil.toBean(jsonData, AssessmentDto.class);

        buildHeader(assessment, report);        // 构建头部信息
        buildLegalJudgement(assessment, report); // 案件结论
        buildMainBody(assessment, report);      // 主体内容
        return report.toString();
    }

    //===== 头部信息 =====
    private void buildHeader(AssessmentDto data, StringBuilder report) {
        report.append(MessageFormat.format("风险等级：{0}。", data.getResult()))
                .append(MessageFormat.format("审核策略：{0}。", data.getSuggestion()));
    }

    //===== 案件结论成 =====
    private void buildLegalJudgement(AssessmentDto data, StringBuilder report) {
        boolean hasCriminal = containsCaseType(data, "刑事");
        boolean hasCivil = containsCaseType(data, "民事");
        boolean hasExecution = containsCaseType(data, "执行");

        //人工审核：未命中刑事案件&命中民事案件。
        //拒绝：命中刑事案件&命中民事案件。
        //拒绝：命中刑事案件&未命中民事案件。
        //通过：未命中刑事案件&未命中民事案件。
        String auditResult;
        if (hasCriminal && hasCivil) {
            auditResult = "拒绝";
        } else if (hasCriminal && !hasCivil) {
            auditResult = "拒绝";
        } else if (!hasCriminal && !hasCivil) {
            auditResult = "通过";
        } else {
            auditResult = "人工审核";
        }

        String caseSummary = MessageFormat.format("{0}：{1}&{2}。",
                auditResult,
                hasCriminal ? "命中刑事案件" : "未命中刑事案件",
                hasCivil ? "命中民事案件" : "未命中民事案件");
        report.append(caseSummary);


        // 民事案件命中详情
        if (hasCivil) {
            report.append("命中民事案件：");
            List<String> civilCases = extractCaseDetails(data, "民事");
            civilCases.forEach(caseInfo -> report.append(INDENT).append(caseInfo).append(""));
        }

        // 执行案件命中详情
        if (hasExecution) {
            report.append("命中执行案件：");
            List<String> execCases = extractCaseDetails(data, "执行");
            execCases.forEach(caseInfo -> report.append(INDENT).append(caseInfo).append(""));
        }

        if (!hasCivil && !hasExecution) {
            report.append("未命中民事案件。未命中执行案件。");
        }
    }

    //===== 主体内容（身份检测 + 手机检测） =====
    private void buildMainBody(AssessmentDto data, StringBuilder report) {
        // 报告标题
        report.append("符律客户涉诉信息报告")
                .append(MessageFormat.format("符律报告等级：{0}                              决策建议：{1}",
                        data.getResult(), data.getSuggestion()));

        // 身份信息检测
        report.append("身份信息检测");
        buildPersonSection(data.getContent().getPerson(), report);

        // 手机风险检测
        report.append("手机风险检测");
        buildPhoneSection(data.getContent().getPhone(), report);
    }

    //===== 身份检测模块 =====
    private void buildPersonSection(PersonDTO person, StringBuilder report) {
        // 基本风险检测
        appendDetection(report, INDENT, "身份信息风险检测", person.getDetections());
        appendDetection(report, INDENT, "司法风险名单检测", person.getDetections());

        // 司法名单详情
        person.getDetections().stream()
                .filter(d -> "司法风险名单检测".equals(d.getName()))
                .findFirst()
                .ifPresent(detection -> {
                    report.append(MessageFormat.format("{0}司法风险名单详情：", INDENT));
                    if (CollUtil.isEmpty(detection.getDetails())) {
                        appendNoHitDetails(report);
                    } else {
                        appendHitDetails(report, detection);
                    }
                });

        // 其他检测项
        appendOtherDetections(report, person.getDetections());
    }

    //===== 手机检测模块 =====
    private void buildPhoneSection(PhoneDTO phone, StringBuilder report) {
        appendOtherDetections(report, phone.getDetections());
    }

    private void appendDetection(StringBuilder report, String indent, String title, List<DetectionsDTO> detections) {
        detections.stream()
                .filter(d -> title.equals(d.getName()))
                .findFirst()
                .ifPresent(d -> {
                    report.append(MessageFormat.format("{0}{1}：{2}", indent, title, d.getConclusion()))
                            .append(MessageFormat.format("{0}结论分析：{1}", indent, d.getAnalysis()));
                });
    }

    // 判断是否存在某类案件
    private boolean containsCaseType(AssessmentDto data, String caseType) {
        DetectionsDTO detectionsDTO = getDetectionsDTO(data);
        if (detectionsDTO == null) return false;

        return detectionsDTO.getDetails().stream()
                .filter(detail -> detail.getValues() != null)
                .flatMap(detail -> detail.getValues().stream())
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .anyMatch(value -> value.contains(caseType));
    }

    // 提取案件详情（如 "（2020）浙0109执5978号;终结本次执行程序;15000.00元"）
    private List<String> extractCaseDetails(AssessmentDto data, String caseType) {
        DetectionsDTO detectionsDTO = getDetectionsDTO(data);
        if (detectionsDTO == null) return Collections.emptyList();

        return detectionsDTO.getDetails().stream()
                .filter(detail -> detail.getValues() != null && detail.getValues().size() == 1)
                .filter(detail -> detail.getValues().stream()
                        .flatMap(Collection::stream)
                        .filter(Objects::nonNull)
                        .anyMatch(value -> value.contains(caseType)))
                .map(dt -> {
                    List<String> values = dt.getValues().stream()
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());

                    return Stream.of(6, 3, 0)
                            .filter(index -> index < values.size())
                            .map(values::get)
                            .collect(Collectors.joining(";"));
                })
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    // 获取DetectionsDTO对象
    private DetectionsDTO getDetectionsDTO(AssessmentDto data) {
        try {
            return data.getContent().getPerson().getDetections().stream()
                    .filter(detection -> "司法风险名单检测".equals(detection.getName()))
                    .findFirst()
                    .orElse(null);
        } catch (Exception e) {
            log.error("Error processing detections", e);
            return null;
        }
    }

    // 未命中详情
    private void appendNoHitDetails(StringBuilder report) {
        report.append(MessageFormat.format("{0}{1}：{2}",
                SUB_INDENT, "被执行情况", "当前司法名单被执行情况未命中。"));
        report.append(MessageFormat.format("{0}{1}：{2}",
                SUB_INDENT, "涉案情况", "当前司法名单涉案情况未命中。"));
        report.append(MessageFormat.format("{0}{1}：{2}",
                SUB_INDENT, "失信公告", "当前司法名单失信公告未命中。"));
        report.append(MessageFormat.format("{0}{1}：{2}",
                SUB_INDENT, "限高公告", "当前司法名单限高公告未命中。"));
        report.append(MessageFormat.format("{0}{1}：{2}",
                SUB_INDENT, "执行公告", "当前司法名单执行公告未命中。"));
        report.append(MessageFormat.format("{0}{1}：{2}",
                SUB_INDENT, "涉案公告", "当前司法名单涉案公告未命中。"));
    }

    // 命中详情
    private void appendHitDetails(StringBuilder report, DetectionsDTO detection) {
        detection.getDetails().forEach(detail -> {
            String listType = detail.getName(); // 如 "执行公告" 或 "涉案公告"
            List<List<String>> cases = detail.getValues();

            // 创建 header 与列索引的映射（例如 ["案号"->0, "案件类型"->1]）
            Map<String, Integer> headerMap = new LinkedHashMap<>();
            for (int i = 0; i < detail.getHeader().size(); i++) {
                headerMap.put(detail.getHeader().get(i), i);
            }

            // 输出命中情况
            report.append(MessageFormat.format("{0}{1}：当前司法名单{2}【{3}条】",
                    SUB_INDENT, listType, cases.isEmpty() ? "未命中" : "命中", cases.size()));

            // 遍历每条案件数据
            for (int i = 0; i < cases.size(); i++) {
                List<String> fields = cases.get(i);
                report.append(MessageFormat.format("{0}{1} 第{2}条", SUB_INDENT, listType, i + 1));

                // 动态遍历所有 header 字段，按顺序生成内容
                for (String headerName : detail.getHeader()) {
                    Integer idx = headerMap.get(headerName);
                    if (idx != null && idx < fields.size()) {
                        String value = fields.get(idx);

                        // 处理特殊字段（例如判决结果）
                        if ("判决结果".equals(headerName)) {
                            value = "详情请查看详细报告";
                        }

                        report.append(MessageFormat.format("{0}{1}：{2}",
                                SUB_INDENT, headerName, value));
                    }
                }

                report.append(""); // 每条案件间隔空行
            }
        });
    }

    // 其他检测项
    private void appendOtherDetections(StringBuilder report, List<DetectionsDTO> detections) {
        appendDetection(report, INDENT, "非银多头借款申请风险检测", detections);
        appendDetection(report, INDENT, "银行多头借款申请风险检测", detections);
        appendDetection(report, INDENT, "身份重复评估风险检测", detections);
        appendDetection(report, INDENT, "手机重复评估风险检测", detections);
    }


    /**
     *  生成验真数据报告
     * @param type 1:主借人 2:第一联系人
     *
     * 主借人手机号:13889448666。
     * 主借人要素验证:手机要素一致。
     * 主借人入网时长:[24,-1)。
     * 第一联系人手机号:***********。
     * 第一联系人要素验证:手机要素一致。
     * 第一联系人入网时长:[24,+)。
     */
    private String generateJzyReport(int type, String name, String idNumber,String phone) {
        JzyDataDTO jzyDataDTO = fundApproveMapper.getJzyValidation(name, idNumber, phone);
        if (jzyDataDTO == null) {
            log.error("HengTongServiceImpl.generateJzyReport 获取数据失败 idNumber:{}, name:{}, phone:{}", idNumber, name, phone);
            throw new BusinessException("未获取到相关信息");
        }
        StringBuilder report = new StringBuilder();
        String prefix = type == 1 ? "主借人" : "第一联系人";
        report.append(prefix).append("手机号:").append(phone).append("。")
               .append(prefix).append("要素验证:").append(jzyDataDTO.getValidationDesc()).append("。")
               .append(prefix).append("入网时长:").append(jzyDataDTO.getNetworkDurationDesc()).append("。");
        return report.toString();
    }

    /**
     * 生成风控模型报告
     *
     * 风险等级:C1C2。
     * 审核策略:人工审核。
     * 风险分数1:560。
     * 风险分数2:560。
     */
    private String generateRiskPolicyReport(Integer orderId) {
        String suggestion = fundApproveMapper.getOrderRiskPolicySuggestionPreLoan(orderId);
        OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                        .select(OrderInfoEntity::getPreId)
                .eq(OrderInfoEntity::getId, orderId)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
        );
        PreFundInfoEntity preFundInfo = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                .eq(PreFundInfoEntity::getPreId, orderInfo.getPreId())
                .eq(PreFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                .eq(PreFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(PreFundInfoEntity::getCreateTime)
                ,false
        );
        if (StrUtil.isBlank(suggestion)) {
            log.error("HengTongServiceImpl.generateRiskPolicyReport 未获取到风控策略报告 {}", orderId);
            throw new BusinessException("未获取到风控策略报告");
        }
        RiskModelDTO dto = JSONUtil.toBean(suggestion, RiskModelDTO.class);
        String softwareCredit = dto.getLevel();
        if (StrUtil.isBlank(softwareCredit)) {
            // 自动通过
            if ((StrUtil.equalsAny(dto.getSuggestion(), "A1A2", "A1B2", "B1A2", "B1B2"))
                    && preFundInfo.getEvaluationAmount() != null
                    && preFundInfo.getEvaluationAmount().compareTo(BigDecimal.valueOf(200000)) <= 0) {

                softwareCredit = "自动通过";
            } else {
                // 人工审批
                softwareCredit = "人工审批";
            }
        }


        StringBuilder report = new StringBuilder();
        report.append(MessageFormat.format("{0}{1}：{2}",
                SUB_INDENT, "风险等级", dto.getSuggestion()));
        report.append(MessageFormat.format("{0}{1}：{2}",
                SUB_INDENT, "审核策略", softwareCredit));
        report.append(MessageFormat.format("{0}{1}：{2}",
                SUB_INDENT, "风险分数1", dto.getScore1()));
        report.append(MessageFormat.format("{0}{1}：{2}",
                SUB_INDENT, "风险分数2", dto.getScore2()));
        return report.toString();
    }

    @Override
    public ZhongHengApiResult<Void> hengTongRepayDeduct(HengTongRepayDeductDTO hengTongRepayDeductDTO) {
//        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
//        Integer fundId = orderInfo.getFundId();
//        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
//                        .eq(FinalFundInfoEntity::getOrderId, orderId)
//                        .eq(FinalFundInfoEntity::getFundId, fundId)
//                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
//                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
//                , false
//        );
//        HengTongRepayDeductDTO hengTongRepayDeductDTO = new HengTongRepayDeductDTO();
//        hengTongRepayDeductDTO.setOrderNum(orderInfo.getOrderNumber())
//                .setOrderNum(finalFundInfo.getLoanBillNo())
//                .setHkType("1")
//                .setYhkQianYueType("1")
//                .setHkJinE(null); //划扣本期金额不需要传入
        return zhongHengApiClient.hengTongRepayDeduct(hengTongRepayDeductDTO);
    }

    /**
     * 提前结清试算
     */
    @Override
    public HengTongSettleProvisionalEstimateVO hengTongSettleProvisionalEstimate(Integer orderId , ZhongHengDictEnum.ZhongHengRepayModeEnum zhongHengRepayTypeEnum) {
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Integer fundId = orderInfo.getFundId();
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, fundId)
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );
        HengTongSettleProvisionalEstimateDTO hengTongSettleProvisionalEstimateDTO = new HengTongSettleProvisionalEstimateDTO();
        hengTongSettleProvisionalEstimateDTO.setOrderNum(finalFundInfo.getLoanBillNo())
                .setSpOrderNum(orderInfo.getOrderNumber())
                .setJieQingTime(DateUtil.format(new Date(), "yyyy-MM-dd"))
                .setTqjqType(zhongHengRepayTypeEnum.getCode());
        ZhongHengApiResult<HengTongSettleProvisionalEstimateVO> hengTongSettleProvisionalEstimateVOZhongHengApiResult = zhongHengApiClient.hengTongSettleProvisionalEstimate(hengTongSettleProvisionalEstimateDTO);

        if (!ZhongHengApiResult.isSuccess(hengTongSettleProvisionalEstimateVOZhongHengApiResult)) {
            throw new BusinessException(hengTongSettleProvisionalEstimateVOZhongHengApiResult.getMsg());
        }
        HengTongSettleProvisionalEstimateVO data = hengTongSettleProvisionalEstimateVOZhongHengApiResult.getResponseData();
        return data;
    }

    /**
     * 对公转账
     */
    @Override
    public ZhongHengApiResult<Void> hengTongCorporateTransfer(HengTongRepayCorporateTransferDTO dto) {
        return zhongHengApiClient.hengTongCorporateTransfer(dto);
    }

    /**
     * 4.5 借款信息查询
     */
    @Override
    public HengTongLoanInfoQueryVO hengTongLoanInfoQuery(Integer orderId) {
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Integer fundId = orderInfo.getFundId();
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, fundId)
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );
        if (ObjUtil.isNull(finalFundInfo)){
            throw new BusinessException("终审信息不存在");
        }
        HengTongLoanInfoQueryDTO hengTongLoanInfoQueryDTO = new HengTongLoanInfoQueryDTO();
        hengTongLoanInfoQueryDTO.setOrderNum(finalFundInfo.getLoanBillNo())
                .setSPOrderNum(orderInfo.getOrderNumber());
        ZhongHengApiResult<HengTongLoanInfoQueryVO> result = zhongHengApiClient.hengTongLoanInfoQuery(hengTongLoanInfoQueryDTO);

        if (!ZhongHengApiResult.isSuccess(result)){
            throw new BusinessException(result.getMsg());
        }

        return result.getResponseData();
    }

    /**
     * 4.1还款/逾期结果通知
     */
    @Override
    public void hengTongRepayDeductResult(HengTongRepayOverdueDTO callBackDTO) {
        log.info("HengTongServiceImpl.hengTongRepayDeductResult callBackDTO:{}", callBackDTO);
        String orderNum = callBackDTO.getSpOrderNum();

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                        .eq(OrderInfoEntity::getOrderNumber, orderNum)
                        .eq(OrderInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderInfoEntity::getCreateTime)
                , false
        );
        Integer orderId = orderInfoEntity.getId();
        Integer fundId = orderInfoEntity.getFundId();
        String applyNo = callBackDTO.getOrderNum();
        String qiShu = callBackDTO.getQiShu();
        BigDecimal repayAmt = ObjUtil.isNull(callBackDTO.getSsHeJi())? BigDecimal.ZERO : new BigDecimal(callBackDTO.getSsHeJi());
        String failReason = "";
        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(
                new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                        .last("limit 1")
        );
        if (finalFundInfoEntity == null) {
            log.warn("HengTongServiceImpl.hengTongRepayDeductResult not found applyNo:{}", applyNo);
            throw new BusinessException("终审信息为空");
        }
        log.info("HengTongServiceImpl.hengTongRepayDeductResult applyNo:{} finalFundInfoEntity:{}", applyNo, finalFundInfoEntity);

        ZhongHengDictEnum.ZhongHengOrderStateEnum orderState = callBackDTO.getOrderState();
        //通知只会返回划扣成功的通知
        FundDeductRepayStatusEnums fundRepayStatus = FundDeductRepayStatusEnums.REPAYMENT_SUCCESS;

        FundDeductRepayTypeEnums fundDeductRepayType = switch (orderState) {
            case PART_RETURN -> FundDeductRepayTypeEnums.NORMAL_REPAYMENT;
            case OVERDUE -> FundDeductRepayTypeEnums.OVERDUE_REPAYMENT;
            case settle -> FundDeductRepayTypeEnums.EARLY_SETTLEMENT;
        };
        if (Objects.equals(new BigDecimal(callBackDTO.getTSYingHuanBenJin()).compareTo(new BigDecimal(callBackDTO.getYsBenJin())),0)){
            fundDeductRepayType = FundDeductRepayTypeEnums.EARLY_SETTLEMENT;
        }else {
            if (Objects.equals(new BigDecimal(callBackDTO.getYsYuQiWeiYueJin()).compareTo(BigDecimal.ZERO),0)){
                fundDeductRepayType = FundDeductRepayTypeEnums.NORMAL_REPAYMENT;
            }else {
                fundDeductRepayType = FundDeductRepayTypeEnums.OVERDUE_REPAYMENT;
            }
        }
        // 同步还款计划
        if (updateRepaymentPlanInfo(orderId, fundId)) return;

        //查看当期是否有还款记录 根据返回的 期号 和 订单号
        FundRepaymentDeductEntity fundRepaymentDeduct = fundRepaymentDeductMapper.selectOne(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                .eq(FundRepaymentDeductEntity::getTerm, Integer.parseInt(qiShu))
                .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                        FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                        FundDeductBizTypeEnums.PAYMENT,
                        FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                        FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                        FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE,
                        FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION
                        ))
                .orderByDesc(FundRepaymentDeductEntity::getIndex)
                .last("limit 1")
        );


        String repayOrderNo = null;
        //如果状态相等 不用继续判断
        if (ObjUtil.isNotNull(fundRepaymentDeduct) && fundRepayStatus == fundRepaymentDeduct.getRepayStatus()) {
            log.info("HengTongServiceImpl.hengTongRepayDeductResult  fundRepayStatus:{} fundRepaymentDeduct.getRepayStatus:{} is equal",  fundRepayStatus, fundRepaymentDeduct.getRepayStatus());
            return;
        }
        try {
            OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
            orderApproveFundPlanStatusDTO.setOrderId(orderId);
            orderFeign.updateFundPlanStatus(orderApproveFundPlanStatusDTO);
        } catch (Exception e) {
            log.error("hengtong hengTongRepayDeductResult updateFundPlanStatus error orderId:{} err:{}", orderId, e.getMessage(), e);
        }
        try {
            orderFeign.updateOrderFundRepayment(orderId);
        } catch (Exception e) {
            log.error("hengtong hengTongRepayDeductResult updateOrderFundRepayment error orderId:{} err:{}", orderId, e.getMessage(), e);
        }
        //如果为还款成功 并且为回购 则执行赎回逻辑  TODO 未写赎回逻辑

        if (fundRepaymentDeduct == null) {
            //新增还款记录
            int term = 0;
            FundRepaymentInfoEntity fundRepaymentInfo = null;
            if (ObjUtil.equals(fundRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)) {
                //还款成功
                if (ObjUtil.equals(fundDeductRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)) {
                    FundRepaymentInfoEntity lastRepayment = fundRepaymentInfoService.getOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                            .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                            .eq(FundRepaymentInfoEntity::getFundId, fundId)
                            .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                            .orderByDesc(FundRepaymentInfoEntity::getTerm)
                            .last("limit 1"));

                    //获取通过整笔结清的期数
                    LocalDate actuallyDate = lastRepayment.getActuallyDate();
                    fundRepaymentInfo = fundRepaymentInfoService.getOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                            .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                            .eq(FundRepaymentInfoEntity::getFundId, fundId)
                            .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                            .eq(FundRepaymentInfoEntity::getActuallyDate, actuallyDate)
                            .orderByAsc(FundRepaymentInfoEntity::getTerm)
                            .last("limit 1")
                    );

                } else {
                    repayAmt = CollUtil.isNotEmpty(callBackDTO.getHuanKuanShiShouList())
                            ? (StringUtils.isNotBlank(callBackDTO.getHuanKuanShiShouList().get(0).getSsHeJiM()) ? new BigDecimal(callBackDTO.getHuanKuanShiShouList().get(0).getSsHeJiM()) : BigDecimal.ZERO)
                            : BigDecimal.ZERO;
                    //正常还款  逾期还款 取最新还款的期数
                    fundRepaymentInfo = fundRepaymentInfoService.getOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                            .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                            .eq(FundRepaymentInfoEntity::getFundId, fundId)
                            .eq(FundRepaymentInfoEntity::getTerm,Integer.parseInt(callBackDTO.getQiShu()))
//                            .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                            .orderByDesc(FundRepaymentInfoEntity::getTerm)
                            .last("limit 1"));
                }
            }

            if (fundRepaymentInfo != null) {
                term = fundRepaymentInfo.getTerm();
                if (fundRepaymentInfo.getActuallyAmountTotal().compareTo(repayAmt) < 0) {
                    failReason += " 注: 当前还款包含其他待还期数依次划扣";

                }
            }
            int index = 1;
            String reqJsonStr = JSONObject.toJSONString(callBackDTO);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate actuallyDate  = ObjUtil.isNull(callBackDTO.getSsTime()) ? null : LocalDate.parse(callBackDTO.getSsTime(), formatter);
            repayOrderNo =  String.format("%s_%d_%d", finalFundInfoEntity.getCreditReqNo(), Integer.parseInt(callBackDTO.getQiShu()), index);
            log.info("HengTongServiceImpl.hengTongRepayDeductResult not found repayOrderNo:{}", repayOrderNo);
            failReason = "通汇发起 " + failReason;
            fundRepaymentDeductService.saveRepaymentDeductInfo(repayOrderNo, index, fundDeductRepayType,
                    Integer.parseInt(callBackDTO.getQiShu()), orderId,fundId, actuallyDate, reqJsonStr, null,
                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING, failReason, repayAmt
            );
        }else if (ObjUtil.equals(fundRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS) && !ObjUtil.equals(fundDeductRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)) {
//            repayOrderNo = fundRepaymentDeduct.getDeductReqNo();
            repayAmt = CollUtil.isNotEmpty(callBackDTO.getHuanKuanShiShouList())
                    ? (StringUtils.isNotBlank(callBackDTO.getHuanKuanShiShouList().get(0).getSsHeJiM()) ? new BigDecimal(callBackDTO.getHuanKuanShiShouList().get(0).getSsHeJiM()) : BigDecimal.ZERO)
                    : BigDecimal.ZERO;
            FundRepaymentInfoEntity deductRepaymentInfo = fundRepaymentInfoService.getOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                    .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                    .eq(FundRepaymentInfoEntity::getFundId, fundId)
                    .eq(FundRepaymentInfoEntity::getTerm, fundRepaymentDeduct.getTerm())
                    .orderByDesc(FundRepaymentInfoEntity::getTerm)
                    .last("limit 1"));
            if (deductRepaymentInfo.getActuallyAmountTotal().compareTo(repayAmt) < 0) {
                failReason += " 注: 当前还款包含其他待还期数依次划扣";

            }
            repayOrderNo = fundRepaymentDeduct.getDeductReqNo();
            OrderPayApplicationInfoEntity payApplicationInfo = orderPayApplicationMapper.selectOne(
                    new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                            .eq(OrderPayApplicationInfoEntity::getPaymentDetails, repayOrderNo)
                            .orderByDesc(OrderPayApplicationInfoEntity::getCreateTime)
                            .last("limit 1"));
            if (deductRepaymentInfo.getRepaymentDate().isAfter(payApplicationInfo.getPaymentTime().toLocalDate())
                    || deductRepaymentInfo.getRepaymentDate().isEqual(payApplicationInfo.getPaymentTime().toLocalDate())){
                fundDeductRepayType = FundDeductRepayTypeEnums.NORMAL_REPAYMENT;
            }else {
                fundDeductRepayType = FundDeductRepayTypeEnums.OVERDUE_REPAYMENT;
            }
        }

        log.info("HengTongServiceImpl.hengTongRepayDeductResult repayOrderNo:{} fundRepaymentDeduct:{}", repayOrderNo, fundRepaymentDeduct);
        //更新划扣还款状态
        fundRepaymentDeductService.updateDeductStatus(repayOrderNo, fundRepayStatus, failReason, repayAmt);

        //更新订单还款状态
        OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
        orderApproveFundPlanStatusDTO.setOrderId(orderId);
        orderFeign.updateFundPlanStatus(orderApproveFundPlanStatusDTO);
        orderFeign.updateOrderFundRepayment(orderId);


        FundRepaymentDeductEntity repaymentDeduct = fundRepaymentDeductService.getOne(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                .eq(FundRepaymentDeductEntity::getDeductReqNo, repayOrderNo)
                .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                .eq(FundRepaymentDeductEntity::getRepayType, fundDeductRepayType)
                .orderByDesc(FundRepaymentDeductEntity::getCreateTime)
                .last("limit 1")
        );
        log.info("HengTongServiceImpl.hengTongRepayDeductResult fundRepaymentDeduct:{}", JSONUtil.toJsonStr(repaymentDeduct));
        if ((Objects.equals(fundRepaymentDeduct.getBizType(), FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION) ||Objects.equals(fundRepaymentDeduct.getBizType(), FundDeductBizTypeEnums.OFFLINE_REPAYMENT)) && Objects.equals(fundDeductRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)){

        }else {
            updateApproveInfo(orderInfoEntity, repaymentDeduct, repayAmt, repayOrderNo,null, null);
        }
        //肯定还款成功

        try {
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
            log.info("HengTongServiceImpl.hengTongRepayDeductResult.sendMessage.begin deductReqNo:{} orderInfo:{}", repayOrderNo, JSONUtil.toJsonStr(orderInfo));
            //给当前业务员发送还款成功消息
            String message;
            String name = orderInfo.getCustomerName();
            String date = LocalDate.now().format(DateTimeFormatter.ofPattern(DatePattern.CHINESE_DATE_PATTERN));

            if (Objects.equals(FundDeductRepayTypeEnums.EARLY_SETTLEMENT, fundDeductRepayType)) {
                message = String.format(OrderSendMessageEnums.getEnumByStatus(5000, 2).getMessage(), name, date, repayAmt);
            } else {
                message = String.format(OrderSendMessageEnums.getEnumByStatus(5000, 1).getMessage(), name, date, repayAmt, repaymentDeduct.getTerm());
            }
            UserInfoVO userInfo = userFeign.searchUserName(orderInfo.getManagerId()).getData();
            MessageContent messageContent = new MessageContent()
                    .setMsgType(MsgConstants.MSG_TEXT)
                    .setSendType(MsgConstants.SEND_DD_NOTICE)
                    .setContent(message)
                    .setReceiver(userInfo.getMobile());
            messageFeign.sendMessage(messageContent);
            log.info("HengTongServiceImpl.hengTongRepayDeductResult.sendMessage.end deductReqNo:{} message:{}", repayOrderNo, message);

        } catch (Exception e) {
            log.error("HengTongServiceImpl.orderStatusChangeCallBack.sendMessage deductReqNo:{} error:{}", repayOrderNo, e.getMessage());
        }

        //推送众信
        try {
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
            orderFeign.pushToZhongXinAsRetrieveAccept(new ReceivePaymentDTO()
                    .setOrderId(repaymentDeduct.getOrderId())
                    .setTerm(repaymentDeduct.getTerm())
                    .setAmount(repayAmt)
                    .setTradingTime(LocalDateTime.now())
                    .setPayer(orderInfo.getCustomerName())
                    .setPayee(FundEnum.getFundEnum(orderInfo.getFundId()).getFundName())
                    .setTradingMethods(OrderFeeDetailTradingMethodsEnum.FUND_BUCKLES)
                    .setSettling(0));
        } catch (Exception e) {
            log.error("HengTongServiceImpl.orderStatusChangeCallBack pushToZhongXinAsBackCase error:{}", e.getMessage(), e);
        }

    }

    private void updateApproveInfo(OrderInfoEntity orderInfoEntity,FundRepaymentDeductEntity repaymentDeduct,BigDecimal repayAmt,String repayOrderNo,LocalDateTime tradingTime,String remark){
        try {
            //保存费用明细
            if (ObjUtil.isNotNull(repaymentDeduct)) {
                OrderPayApplicationInfoEntity payApplicationInfo = orderPayApplicationMapper.selectOne(
                        new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                                .eq(OrderPayApplicationInfoEntity::getPaymentDetails, repaymentDeduct.getDeductReqNo())
                                .orderByDesc(OrderPayApplicationInfoEntity::getCreateTime)
                                .last("limit 1"));
                FundDeductRepayTypeEnums repayType = repaymentDeduct.getRepayType();
                OrderFeeDetailExpandTypeEnum expandType = ObjUtil.equals(repayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)? OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT:OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT;
                saveFeeDetail(repaymentDeduct.getTerm(), repaymentDeduct.getOrderId(), repaymentDeduct.getDeductReqNo(), payApplicationInfo.getPayeeAmount(), expandType,tradingTime,"中恒信合");
                //更新查账明细
                //查账明细
                if (ObjUtil.isNotNull(payApplicationInfo)){
                    //更新查账明细
                    payApplicationInfo.setPaymentDetails(repayOrderNo);
                    payApplicationInfo.setCurrentNode(PayApplicationNodeEnums.SUCCESS);
                    orderPayApplicationMapper.updateById(payApplicationInfo);
                    //查账节点
                    OrderPayApplyNodeRecordEntity orderPayApplyNodeRecordEntity = orderPayApplyNodeRecordMapper.selectOne(
                            new LambdaQueryWrapper<OrderPayApplyNodeRecordEntity>()
                                    .eq(OrderPayApplyNodeRecordEntity::getApplyInfoId, payApplicationInfo.getId())
                                    .orderByDesc(OrderPayApplyNodeRecordEntity::getCreateTime)
                                    .last("limit 1"));

                    OrderPayApplyNodeRecordEntity newRecord = new OrderPayApplyNodeRecordEntity();
                    newRecord.setApplyInfoId(payApplicationInfo.getId());
                    newRecord.setCurrentNode(PayApplicationNodeEnums.APPROVE_APPROVAL);
                    newRecord.setNextNode(PayApplicationNodeEnums.SUCCESS);
                    newRecord.setRemark(remark);
                    orderPayApplyNodeRecordMapper.insert(newRecord);
                    List<SprEductionUsageEntity> list = sprEductionUsageEntityService.list(
                            new LambdaQueryWrapper<SprEductionUsageEntity>()
                                    .eq(SprEductionUsageEntity::getDeleteFlag, 0)
                                    .eq(SprEductionUsageEntity::getVehicleNumber, orderInfoEntity.getVehicleNumber())
                                    .eq(SprEductionUsageEntity::getUsageStatus, 2)
                                    .in(SprEductionUsageEntity::getApprovalTemplate, Arrays.asList(4,5))
                    );
                    List<SprEductionUsageEntity> collect = new ArrayList<>();
                    if (Objects.equals(payApplicationInfo.getFeeType(),OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)){
                        collect = list.stream()
                                .filter(item -> item.getApprovalTemplate() == 4)
                                .toList();
                    }
                    if (Objects.equals(payApplicationInfo.getFeeType(),OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT)){
                        collect = list.stream()
                                .filter(item -> item.getApprovalTemplate() == 5)
                                .toList();
                    }
                    if (CollUtil.isNotEmpty(collect)){
                        List<Integer> idList = collect.stream().map(SprEductionUsageEntity::getId).toList();
                        sprEductionUsageEntityService.update(
                                new LambdaUpdateWrapper<SprEductionUsageEntity>()
                                        .set(SprEductionUsageEntity::getUsageStatus,1)
                                        .eq(SprEductionUsageEntity::getDeleteFlag,0)
                                        .in(SprEductionUsageEntity::getId,idList)
                        );
                    }
                }
            }
        } catch (Exception e) {
            log.error("HengTongServiceImpl.hengTongRepayDeductResult saveFeeDetail deductReqNo:{} error:{}", repayOrderNo, e.getMessage(), e);
        }
    }
    @Override
    public boolean updateRepaymentPlanInfo(Integer orderId, Integer fundId) {
        HengTongLoanInfoQueryVO hengTongLoanInfoQueryVO = hengTongLoanInfoQuery(orderId);

        ZhongHengSelOrderStateEnum loanStatus = hengTongLoanInfoQueryVO.getOrderState();

        log.info("HengTongServiceImpl.hengTongRepayDeductResult orderId:{}, loanStatus:{}", orderId, loanStatus);

        List<HengTongLoanInfoQueryVO.HuanKuanList> planList = hengTongLoanInfoQueryVO.getHuanKuanList();
        if (CollUtil.isNotEmpty(planList)) {
            planList.forEach(HuanKuanInfo -> updateRepaymentPlan(orderId,loanStatus, HuanKuanInfo, fundId));
        } else {
            return true;
        }
        return false;
    }

    /**
     * 保存交易明细
     */
    @Override
    public void saveFeeDetail(Integer term, Integer orderId, String repayOrderNo, BigDecimal amount, OrderFeeDetailExpandTypeEnum expenseType,LocalDateTime tradingTime ,String payee) {
        FundRepaymentInfoEntity updateRepaymentInfo = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                .eq(FundRepaymentInfoEntity::getTerm, term)
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
        );
        FundRepaymentDeductEntity fundRepaymentDeduct = fundRepaymentDeductMapper.selectOne(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                .eq(FundRepaymentDeductEntity::getDeductReqNo, repayOrderNo)
                .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                .orderByDesc(FundRepaymentDeductEntity::getIndex)
                .last("limit 1")
        );
        OrderFeeDetailTradingMethodsEnum tradingMethods =     ObjUtil.equals(fundRepaymentDeduct.getBizType(),  FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION) ?  OrderFeeDetailTradingMethodsEnum.CORPORATE_TRANSFERS :   OrderFeeDetailTradingMethodsEnum.FUND_BUCKLES;
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(updateRepaymentInfo.getOrderId());
            orderFeign.saveOrderFeeDetail(new OrderFeeDetailSaveDTO()
                    .setOrderId(updateRepaymentInfo.getOrderId())
                    .setTradingSerialNumber(repayOrderNo)
                    .setAmount(amount)
                    .setTradingMethods(tradingMethods)
                    .setPayer(orderInfoEntity.getCustomerName())
                    .setPayee(payee)
                    .setExpenseType(expenseType)
                    .setStatus(OrderFeeDetailStatusEnum.INCOME)
                    .setTerm(updateRepaymentInfo.getTerm())
                    .setTradingTime(ObjUtil.isNotNull(updateRepaymentInfo.getActuallyDate())?updateRepaymentInfo.getActuallyDate().atStartOfDay():tradingTime)
                    .setRemark(null));

    }

    /**
     * 获取放款前还款计划
     *
     */
    @Override
    public HengTongPreRepayPlanVO hengTongPreRepayPlan(HengTongPreRepayPlanDTO preRepayPlanDTO) {
        ZhongHengApiResult<HengTongPreRepayPlanVO> preRepayPlanResult = zhongHengApiClient.hengTongPreRepayPlan(preRepayPlanDTO);
        if (preRepayPlanResult.isSuccess()) {
            return preRepayPlanResult.getResponseData();
        }
        return null;
    }

    @Override
    public void daiChangTongZhiResult(DaiChangTongZhiDTO daiChangTongZhiDTO) {

        //查询订单信息
        OrderInfoEntity order = orderInfoMapper.selectOne(new LambdaUpdateWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getOrderNumber, daiChangTongZhiDTO.getSpOrderNum())
                .last("LIMIT 1"));

        if (ObjUtil.isNotNull(order)) {
            //订单id
            Integer orderId = order.getId();
            //还款金额
            BigDecimal actuallyTotalAmount = new BigDecimal(daiChangTongZhiDTO.getDcHeJi());
            //还款日期
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime dateTime = LocalDateTime.parse(daiChangTongZhiDTO.getDangQiYingHuanTime() + " 00:00:00", formatter);
            LocalDate repayDate = dateTime.toLocalDate();

            // 代偿单期
            if(("1").equals(daiChangTongZhiDTO.getDcDanQiOrZhengBi())) {
                // 获取赎回还款计划表
                FundRepaymentInfoEntity fundRepaymentInfoList = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                        .in(FundRepaymentInfoEntity::getFundId, Arrays.asList(FundEnum.ZHONG_HENG.getValue(), FundEnum.ZHONG_HENG_TONG_HUI.getValue()))
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                        .eq(FundRepaymentInfoEntity::getTerm,Integer.valueOf(daiChangTongZhiDTO.getDangQiYingHuanQiShu()))
                        .orderByDesc(FundRepaymentInfoEntity::getTerm)
                        .last("LIMIT 1")
                );
                if (fundRepaymentInfoList == null) {
                    log.warn("HengTongServiceImpl.daiChangTongZhiResult.DQ: No repayment info found for orderId:{}, actuallyTotalAmount:{}", orderId, actuallyTotalAmount);
                    return;
                }

                // 资方终审信息
                FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .in(FinalFundInfoEntity::getFundId, Arrays.asList(FundEnum.ZHONG_HENG.getValue(), FundEnum.ZHONG_HENG_TONG_HUI.getValue()))
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .last("LIMIT 1"));

                if (ObjUtil.isNull(finalFundInfo)) {
                    log.warn("HengTongServiceImpl.daiChangTongZhiResult.DQ: No finalFundInfo found for orderId:{}, actuallyTotalAmount:{}", orderId, actuallyTotalAmount);
                    return;
                }

	            // 获取付款申请表信息
	            OrderPayApplicationInfoEntity entity1 = orderPayApplicationMapper.selectOne(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
			            .eq(OrderPayApplicationInfoEntity::getOrderId, order.getId())
			            .eq(OrderPayApplicationInfoEntity::getRepaymentTerm,Integer.valueOf(daiChangTongZhiDTO.getDangQiYingHuanQiShu()))
			            .eq(OrderPayApplicationInfoEntity::getPaymentDetails,daiChangTongZhiDTO.getDcOrderNum())
			            .last("LIMIT 1")
	            );
				if (ObjUtil.isNull(entity1)) {
					// 更新资方还款信息表
					fundRepaymentInfoMapper.update(new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
							.eq(FundRepaymentInfoEntity::getOrderId,orderId)
							.eq(FundRepaymentInfoEntity::getTerm,Integer.valueOf(daiChangTongZhiDTO.getDangQiYingHuanQiShu()))
							.set(FundRepaymentInfoEntity::getIsdaichang, 1)
							.set(FundRepaymentInfoEntity::getRepaymentDate, LocalDate.parse(daiChangTongZhiDTO.getDangQiYingHuanTime()))
							.set(FundRepaymentInfoEntity::getRepaymentPrincipal,Convert.toBigDecimal(daiChangTongZhiDTO.getDcBenJin(),BigDecimal.ZERO))
							.set(FundRepaymentInfoEntity::getRepaymentInterest, Convert.toBigDecimal(daiChangTongZhiDTO.getDcLiXi(),BigDecimal.ZERO))
							.set(FundRepaymentInfoEntity::getRepaymentPenaltyInterest,Convert.toBigDecimal(daiChangTongZhiDTO.getDcWeiYueJin(),BigDecimal.ZERO))
							.set(FundRepaymentInfoEntity::getRepaymentAmountTotal,Convert.toBigDecimal(daiChangTongZhiDTO.getDcHeJi(),BigDecimal.ZERO))
					);

					GetNextNodeDTO dto = new GetNextNodeDTO();
					dto.setFeeType(OrderFeeDetailExpandTypeEnum.SINGLE_PERIOD_COMPENSATION);
					dto.setApplyType(OrderFeeDetailStatusEnum.SPENDING);
					dto.setPayeeType(PayApplicationPayeeTypeEnum.ZHONG_HENG);
					dto.setCurrentNode(PayApplicationNodeEnums.ACCOUNTANT_APPLY);

					// 同时给付款申请单表中插入一条数据
					OrderPayApplicationInfoEntity entity = new OrderPayApplicationInfoEntity();
					entity
							.setOrderId(orderId)
							.setCurrentNode(orderFeign.getNextNode(dto).getData())
							.setPayeeType(PayApplicationPayeeTypeEnum.ZHONG_HENG)
							.setFeeType(OrderFeeDetailExpandTypeEnum.SINGLE_PERIOD_COMPENSATION)
							.setPaymentDetails(daiChangTongZhiDTO.getDcOrderNum())
							.setPayeeAccount("中恒信合（厦门）融资租赁有限公司")
							.setPayeeAccountName(daiChangTongZhiDTO.getShouKuanYinHang())
							.setPayeeAccountNumber(daiChangTongZhiDTO.getShouKuanYinHangNum())
							.setPayeeAmount(Convert.toBigDecimal(daiChangTongZhiDTO.getDcHeJi(),BigDecimal.ZERO))
							.setApplyType(OrderFeeDetailStatusEnum.SPENDING)
							.setRepaymentTerm(Integer.valueOf(daiChangTongZhiDTO.getDangQiYingHuanQiShu()))
							.setRepayPrincipal(Convert.toBigDecimal(daiChangTongZhiDTO.getDcBenJin(),BigDecimal.ZERO))
							.setPenalty(Convert.toBigDecimal(daiChangTongZhiDTO.getDcWeiYueJin(),BigDecimal.ZERO))
							.setOrderSource(1)
							.setDeleteFlag(0);
					orderPayApplicationMapper.insert(entity);
				}
            }

            //代偿整笔
            if (("2").equals(daiChangTongZhiDTO.getDcDanQiOrZhengBi())) {
	            FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
			            .eq(FinalFundInfoEntity::getOrderId, orderId)
			            .in(FinalFundInfoEntity::getFundId, Arrays.asList(FundEnum.ZHONG_HENG.getValue(), FundEnum.ZHONG_HENG_TONG_HUI.getValue()))
			            .eq(FinalFundInfoEntity::getDeleteFlag, 0)
			            .last("LIMIT 1"));

	            if (ObjUtil.isNull(finalFundInfo)) {
		            log.warn("HengTongServiceImpl.daiChangTongZhiResult: No finalFundInfo found for orderId:{}, actuallyTotalAmount:{}", orderId, actuallyTotalAmount);
		            return;
	            }
	            // 计算发生起始期数
	            Integer eventStartTerm = Integer.valueOf(daiChangTongZhiDTO.getDangQiYingHuanQiShu());

	            fundRepurchaseResultMapper.update(new LambdaUpdateWrapper<FundRepurchaseResultEntity>()
			            .eq(FundRepurchaseResultEntity::getOrderId, orderId)
			            .in(FundRepurchaseResultEntity::getFundId, Arrays.asList(FundEnum.ZHONG_HENG.getValue(), FundEnum.ZHONG_HENG_TONG_HUI.getValue()))
			            .eq(FundRepurchaseResultEntity::getDeleteFlag, 0)
			            .set(FundRepurchaseResultEntity::getDeleteFlag, 1)
	            );

	            // 计算待回购金额
	            BigDecimal amountTotal = Convert.toBigDecimal(daiChangTongZhiDTO.getDcHeJi(),BigDecimal.ZERO);
	            BigDecimal prePrincipal = Convert.toBigDecimal(daiChangTongZhiDTO.getDcBenJin(),BigDecimal.ZERO);
	            BigDecimal preInterest = Convert.toBigDecimal(daiChangTongZhiDTO.getDcLiXi(),BigDecimal.ZERO);
	            BigDecimal prePenalty = Convert.toBigDecimal(daiChangTongZhiDTO.getDcWeiYueJin(),BigDecimal.ZERO);

	            FundRepurchaseResultEntity fundRepurchaseResult = new FundRepurchaseResultEntity();
	            fundRepurchaseResult
			            .setFundFlowNo(order.getFundId() + "_" + eventStartTerm + "_" + daiChangTongZhiDTO.getDcDanQiOrZhengBi())
			            .setLoanApplyNo(finalFundInfo.getLoanBillNo())
			            .setOrderId(orderId)
			            .setFundId(order.getFundId())
			            .setEventStartTerm(eventStartTerm)
			            .setPrePrincipal(prePrincipal)
			            .setPreInterest(preInterest)
			            .setPrePenalty(prePenalty)
			            .setPreRepayAmt(amountTotal)
			            .setRepurchaseDate(repayDate.toString())
			            .setRepurchaseResult(ObjUtil.isNotNull(eventStartTerm) ? 1 : 2);
	            Long count = fundRepurchaseResultMapper.selectCount(new LambdaQueryWrapper<FundRepurchaseResultEntity>()
			            .eq(FundRepurchaseResultEntity::getFundFlowNo, order.getFundId() + "_" + eventStartTerm + "_" + daiChangTongZhiDTO.getDcDanQiOrZhengBi())
			            .eq(FundRepurchaseResultEntity::getOrderId, orderId)
			            .eq(FundRepurchaseResultEntity::getFundId, order.getFundId())
			            .eq(FundRepurchaseResultEntity::getDeleteFlag, 0)
	            );

	            if (count > 0) {
		            fundRepurchaseResultMapper.update(fundRepurchaseResult, new LambdaUpdateWrapper<FundRepurchaseResultEntity>()
				            .eq(FundRepurchaseResultEntity::getFundFlowNo, order.getFundId() + "_" + eventStartTerm + "_" + daiChangTongZhiDTO.getDcDanQiOrZhengBi())
				            .eq(FundRepurchaseResultEntity::getOrderId, orderId)
				            .eq(FundRepurchaseResultEntity::getFundId, order.getFundId())
				            .eq(FundRepurchaseResultEntity::getDeleteFlag, 0)
		            );

	            } else {
		            //保存回购结果
		            fundRepurchaseResultMapper.insert(fundRepurchaseResult);
	            }

	            GetNextNodeDTO dto = new GetNextNodeDTO();
	            dto.setFeeType(OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT);
	            dto.setApplyType(OrderFeeDetailStatusEnum.SPENDING);
	            dto.setPayeeType(PayApplicationPayeeTypeEnum.ZHONG_HENG);
	            dto.setCurrentNode(PayApplicationNodeEnums.ACCOUNTANT_APPLY);

	            // 同时给付款申请单表中插入一条数据
	            OrderPayApplicationInfoEntity entity = new OrderPayApplicationInfoEntity();
	            entity
			            .setOrderId(orderId)
			            .setCurrentNode(orderFeign.getNextNode(dto).getData())
			            .setPayeeType(PayApplicationPayeeTypeEnum.ZHONG_HENG)
			            .setFeeType(OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)
			            .setPaymentDetails(daiChangTongZhiDTO.getDcOrderNum())
			            .setPayeeAccount("中恒信合（厦门）融资租赁有限公司")
			            .setPayeeAccountName(daiChangTongZhiDTO.getShouKuanYinHang())
			            .setPayeeAccountNumber(daiChangTongZhiDTO.getShouKuanYinHangNum())
			            .setPayeeAmount(Convert.toBigDecimal(daiChangTongZhiDTO.getDcHeJi(),BigDecimal.ZERO))
			            .setApplyType(OrderFeeDetailStatusEnum.SPENDING)
			            .setRepaymentTerm(Integer.valueOf(daiChangTongZhiDTO.getDangQiYingHuanQiShu()))
			            .setRepayPrincipal(Convert.toBigDecimal(daiChangTongZhiDTO.getDcBenJin(),BigDecimal.ZERO))
			            .setPenalty(Convert.toBigDecimal(daiChangTongZhiDTO.getDcWeiYueJin(),BigDecimal.ZERO))
			            .setOrderSource(1)
			            .setDeleteFlag(0);
	            orderPayApplicationMapper.insert(entity);
            }
        }
    }

    @Override
    public void daiChangChaZhangTongZhiResult(DaiChangChaZhangTongZhiDTO daiChangChaZhangTongZhiDTO) {
        //查询订单信息
        OrderInfoEntity order = orderInfoMapper.selectOne(new LambdaUpdateWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getOrderNumber, daiChangChaZhangTongZhiDTO.getSpOrderNum())
                .last("LIMIT 1"));

        if (ObjUtil.isNotNull(order)) {
            //订单id
            Integer orderId = order.getId();
            //还款金额
            BigDecimal actuallyTotalAmount = new BigDecimal(daiChangChaZhangTongZhiDTO.getDcHeJi());
            //还款日期
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime dateTime = LocalDateTime.parse(daiChangChaZhangTongZhiDTO.getDcTime() + " 00:00:00", formatter);
            LocalDate repayDate = dateTime.toLocalDate();

            //更新为赎回状态
            OrderInfoEntity orderInfo = new OrderInfoEntity();
            orderInfo.setIsRepurchase(1);
            orderInfoMapper.update(orderInfo, new LambdaQueryWrapper<OrderInfoEntity>()
                    .eq(OrderInfoEntity::getId, orderId)
            );


			// 获取付款申请表信息
	        OrderPayApplicationInfoEntity entity = orderPayApplicationMapper.selectOne(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
			        .eq(OrderPayApplicationInfoEntity::getOrderId, order.getId())
			        .eq(OrderPayApplicationInfoEntity::getPaymentDetails,daiChangChaZhangTongZhiDTO.getDcOrderNum())
			        .last("LIMIT 1")
	        );
			if (entity != null){
				// 若为单期代偿款
				if (entity.getFeeType().equals(OrderFeeDetailExpandTypeEnum.SINGLE_PERIOD_COMPENSATION)){
					orderPayApplicationMapper.update(new LambdaUpdateWrapper<OrderPayApplicationInfoEntity>()
							.eq(OrderPayApplicationInfoEntity::getOrderId, order.getId())
							.eq(OrderPayApplicationInfoEntity::getRepaymentTerm, entity.getRepaymentTerm())
							.eq(OrderPayApplicationInfoEntity::getPaymentDetails,daiChangChaZhangTongZhiDTO.getDcOrderNum())
							.set(OrderPayApplicationInfoEntity::getPayAccount,"龙环汇丰信息咨询(北京)有限公司")
							.set(OrderPayApplicationInfoEntity::getPayAccountName,daiChangChaZhangTongZhiDTO.getFuKuanYinHang())
							.set(OrderPayApplicationInfoEntity::getPayeeAccountNumber,daiChangChaZhangTongZhiDTO.getFuKuanYinHangNum())
							.set(OrderPayApplicationInfoEntity::getPaymentTime,Convert.toLocalDateTime(daiChangChaZhangTongZhiDTO.getDcTime()))
					);
				}
			}
			// 否则走原有的赎回逻辑
			else{
				// 获取赎回还款计划表
				List<FundRepaymentInfoEntity> fundRepaymentInfoList = fundRepaymentInfoMapper.selectList(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
						.eq(FundRepaymentInfoEntity::getOrderId, orderId)
						.in(FundRepaymentInfoEntity::getFundId, Arrays.asList(FundEnum.ZHONG_HENG.getValue(), FundEnum.ZHONG_HENG_TONG_HUI.getValue()))
						.eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
						.orderByDesc(FundRepaymentInfoEntity::getTerm)
				);


				if (CollUtil.isEmpty(fundRepaymentInfoList)) {
					log.warn("HengTongServiceImpl.daiChangChaZhangTongZhiResult: No repayment info found for orderId:{}, actuallyTotalAmount:{}", orderId, actuallyTotalAmount);
					return;
				}
				FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
						.eq(FinalFundInfoEntity::getOrderId, orderId)
						.in(FinalFundInfoEntity::getFundId, Arrays.asList(FundEnum.ZHONG_HENG.getValue(), FundEnum.ZHONG_HENG_TONG_HUI.getValue()))
						.eq(FinalFundInfoEntity::getDeleteFlag, 0)
						.last("LIMIT 1"));

				if (ObjUtil.isNull(finalFundInfo)) {
					log.warn("HengTongServiceImpl.daiChangChaZhangTongZhiResult: No finalFundInfo found for orderId:{}, actuallyTotalAmount:{}", orderId, actuallyTotalAmount);
					return;
				}

				// 初始化累加变量
				BigDecimal actuallyPrincipalSum = BigDecimal.ZERO;
				BigDecimal actuallyInterestSum = BigDecimal.ZERO;
				BigDecimal actuallyPenaltyInterestSum = BigDecimal.ZERO;
				BigDecimal actuallyAmountTotalSum = BigDecimal.ZERO;

				// 计算发生起始期数
				Integer eventStartTerm = null;

				// 遍历还款计划表，累加实还金额
				for (FundRepaymentInfoEntity fundRepaymentInfo : fundRepaymentInfoList) {

					// 如果累加的总金额等于回购总金额，则返回相应的期数 如果累加的总金额大于回购总金额，则返回null
					if (actuallyAmountTotalSum.compareTo(actuallyTotalAmount) < 0) {
						log.info("HengTongServiceImpl.daiChangChaZhangTongZhiResult: Found term:{}, orderId:{}, actuallyTotalAmount:{}", fundRepaymentInfo.getTerm(), orderId, actuallyTotalAmount);
						actuallyPrincipalSum = actuallyPrincipalSum.add(fundRepaymentInfo.getActuallyPrincipal());
						actuallyInterestSum = actuallyInterestSum.add(fundRepaymentInfo.getActuallyInterest());
						actuallyPenaltyInterestSum = actuallyPenaltyInterestSum.add(fundRepaymentInfo.getActuallyPenaltyInterest());
						actuallyAmountTotalSum = actuallyAmountTotalSum.add(fundRepaymentInfo.getActuallyAmountTotal());
					}

					if (actuallyAmountTotalSum.compareTo(actuallyTotalAmount) == 0) {
						log.warn("HengTongServiceImpl.daiChangChaZhangTongZhiResult: Principal sum is greater than actuallyTotalAmount for orderId:{}, actuallyTotalAmount:{}", orderId, actuallyTotalAmount);
						eventStartTerm = fundRepaymentInfo.getTerm();
						break;
					}
				}

				fundRepurchaseResultMapper.update(new LambdaUpdateWrapper<FundRepurchaseResultEntity>()
						.eq(FundRepurchaseResultEntity::getOrderId, orderId)
						.in(FundRepurchaseResultEntity::getFundId, Arrays.asList(FundEnum.ZHONG_HENG.getValue(), FundEnum.ZHONG_HENG_TONG_HUI.getValue()))
						.eq(FundRepurchaseResultEntity::getDeleteFlag, 0)
						.set(FundRepurchaseResultEntity::getDeleteFlag, 1)
				);

				// 计算待回购金额
				BigDecimal prePrincipal = actuallyPrincipalSum;
				BigDecimal preInterest = actuallyInterestSum;
				BigDecimal prePenalty = actuallyPenaltyInterestSum;
				BigDecimal preRepayAmt = actuallyAmountTotalSum;

				FundRepurchaseResultEntity fundRepurchaseResult = new FundRepurchaseResultEntity();
				fundRepurchaseResult
						.setFundFlowNo(order.getFundId() + "_" + eventStartTerm)
						.setLoanApplyNo(finalFundInfo.getLoanBillNo())
						.setOrderId(orderId)
						.setFundId(FundEnum.ZHONG_HENG_TONG_HUI.getValue())
						.setEventStartTerm(eventStartTerm)
						.setPrePrincipal(prePrincipal)
						.setPreInterest(preInterest)
						.setPrePenalty(prePenalty)
						.setPreRepayAmt(preRepayAmt)
						.setRepurchaseDate(repayDate.toString())
						.setRepurchaseResult(ObjUtil.isNotNull(eventStartTerm) ? 1 : 2);
				Long count = fundRepurchaseResultMapper.selectCount(new LambdaQueryWrapper<FundRepurchaseResultEntity>()
						.eq(FundRepurchaseResultEntity::getFundFlowNo, order.getFundId() + "_" + eventStartTerm)
						.eq(FundRepurchaseResultEntity::getOrderId, orderId)
						.eq(FundRepurchaseResultEntity::getFundId, order.getFundId())
						.eq(FundRepurchaseResultEntity::getDeleteFlag, 0)
				);
				if (count > 0) {
					fundRepurchaseResultMapper.update(fundRepurchaseResult, new LambdaUpdateWrapper<FundRepurchaseResultEntity>()
							.eq(FundRepurchaseResultEntity::getFundFlowNo, order.getFundId() + "_" + eventStartTerm)
							.eq(FundRepurchaseResultEntity::getOrderId, orderId)
							.eq(FundRepurchaseResultEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
							.eq(FundRepurchaseResultEntity::getDeleteFlag, 0)
					);
				} else {
					//保存回购结果
					fundRepurchaseResultMapper.insert(fundRepurchaseResult);
				}
			}
        }
    }

	@Override
	public ZhongHengApiResult<Void> daichangChaZhangTZ(DaiChangChaZhangTongZhiDTO dto) {
		return zhongHengApiClient.daiChangChaZhangTZ(dto);
	}

    @Override
    public void daiChangShenHeTongZhiResult(DaiChangShenHeTongZhiDTO daiChangShenHeTongZhiDTO) {
		if (daiChangShenHeTongZhiDTO.getDaiChangShenHeState().equals("T")){
			//查询订单信息
			OrderInfoEntity order = orderInfoMapper.selectOne(new LambdaUpdateWrapper<OrderInfoEntity>()
					.eq(OrderInfoEntity::getOrderNumber, daiChangShenHeTongZhiDTO.getSpOrderNum())
					.last("LIMIT 1"));

			if (ObjUtil.isNotNull(order)) {
				//订单id
				Integer orderId = order.getId();
				//还款金额
				BigDecimal actuallyTotalAmount = new BigDecimal(daiChangShenHeTongZhiDTO.getDCHeJi());
				//还款日期
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
				LocalDateTime dateTime = LocalDateTime.parse(daiChangShenHeTongZhiDTO.getDcTime(), formatter);
				LocalDate repayDate = dateTime.toLocalDate();

				//更新为赎回状态
				OrderInfoEntity orderInfo = new OrderInfoEntity();
				orderInfo.setIsRepurchase(1);
				orderInfoMapper.update(orderInfo, new LambdaQueryWrapper<OrderInfoEntity>()
						.eq(OrderInfoEntity::getId, orderId)
				);

		        // 获取付款申请表信息
		        OrderPayApplicationInfoEntity entity = orderPayApplicationMapper.selectOne(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
				        .eq(OrderPayApplicationInfoEntity::getOrderId, order.getId())
				        .eq(OrderPayApplicationInfoEntity::getPaymentDetails,daiChangShenHeTongZhiDTO.getDcOrderNum())
				        .last("LIMIT 1")
		        );
		        if (entity != null){
			        // 若为单期代偿款
			        if (entity.getFeeType().equals(OrderFeeDetailExpandTypeEnum.SINGLE_PERIOD_COMPENSATION)){
				        FundRepaymentInfoEntity fundRepaymentInfoEntity = fundRepaymentInfoMapper.selectOne(new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
						        .eq(FundRepaymentInfoEntity::getOrderId,orderId)
						        .eq(FundRepaymentInfoEntity::getTerm,Integer.valueOf(daiChangShenHeTongZhiDTO.getDangQiYingHuanQiShu()))
						        .last("LIMIT 1")
				        );
				        if ( fundRepaymentInfoEntity != null){
					        BigDecimal bj = fundRepaymentInfoEntity.getRepaymentPrincipal();
					        BigDecimal lx = fundRepaymentInfoEntity.getRepaymentInterest();
					        LocalDate yhdate = fundRepaymentInfoEntity.getRepaymentDate();
					        // 获取当前日期
					        LocalDate currentDate = LocalDate.now();
					        // 计算逾期天数
					        long overdueDays = Math.max(0, ChronoUnit.DAYS.between(yhdate,currentDate));
							if(overdueDays > 0){
								//违约金
								BigDecimal wyj = bj.add(lx)
										.multiply(new BigDecimal("0.005"))
										.multiply(BigDecimal.valueOf(overdueDays))
										.setScale(2, RoundingMode.HALF_UP);

								//应收合计
								BigDecimal hj = bj
										.add(lx)
										.add(wyj)
										.setScale(2, RoundingMode.HALF_UP);

								// 更新资方还款信息表 重新计算当期的还款计划的违约金以及应还合计
						        fundRepaymentInfoMapper.update(new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
								        .eq(FundRepaymentInfoEntity::getOrderId,orderId)
								        .eq(FundRepaymentInfoEntity::getTerm,Integer.valueOf(daiChangShenHeTongZhiDTO.getDangQiYingHuanQiShu()))
								        .set(FundRepaymentInfoEntity::getRepaymentPenaltyInterest, wyj)
								        .set(FundRepaymentInfoEntity::getRepaymentAmountTotal, hj)
						        );
							}
				        }

				        OrderFeeDetailEntity aa = new  OrderFeeDetailEntity();
						aa.setOrderId(entity.getOrderId());
						aa.setAmount(Convert.toBigDecimal(daiChangShenHeTongZhiDTO.getDCHeJi(),BigDecimal.ZERO));
						aa.setExpenseType(OrderFeeDetailExpandTypeEnum.SINGLE_PERIOD_COMPENSATION);
						aa.setFeeReduction(BigDecimal.ZERO);
						aa.setTradingMethods(OrderFeeDetailTradingMethodsEnum.CORPORATE_TRANSFERS);
						aa.setStatus(OrderFeeDetailStatusEnum.SPENDING);
						aa.setPayee("中恒信合");
						aa.setTradingTime(LocalDateTime.now());
				        aa.setDeleteFlag(0);
				        aa.setCreateBy(1);
				        aa.setOrderSource(1);
						orderFeeDetailMapper.insert(aa);
			        }

					// 整笔代偿
					if (entity.getFeeType().equals(OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)) {
						//更新为赎回状态
						OrderInfoEntity orderInfoEntity = new OrderInfoEntity();
						orderInfoEntity.setIsRepurchase(1);
						orderInfoMapper.update(orderInfoEntity, new LambdaQueryWrapper<OrderInfoEntity>()
								.eq(OrderInfoEntity::getId, orderId)
						);

						// 更新同步从当前赎回期数开始  剩余的还款计划 单期代偿字段都变更为1
						fundRepaymentInfoMapper.update(new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
								.eq(FundRepaymentInfoEntity::getOrderId, orderId)
								.ge(FundRepaymentInfoEntity::getTerm, Integer.valueOf(daiChangShenHeTongZhiDTO.getDangQiYingHuanQiShu()))
								.eq(FundRepaymentInfoEntity::getDeleteFlag,0)
								.set(FundRepaymentInfoEntity::getIsdaichang,1)
								.set(FundRepaymentInfoEntity::getUpdateTime,LocalDateTime.now())
						);

						OrderFeeDetailEntity aa = new  OrderFeeDetailEntity();
						aa.setOrderId(entity.getOrderId());
						aa.setAmount(Convert.toBigDecimal(daiChangShenHeTongZhiDTO.getDCHeJi(),BigDecimal.ZERO));
						aa.setExpenseType(OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT);
						aa.setFeeReduction(BigDecimal.ZERO);
						aa.setTradingMethods(OrderFeeDetailTradingMethodsEnum.CORPORATE_TRANSFERS);
						aa.setStatus(OrderFeeDetailStatusEnum.SPENDING);
						aa.setPayee("中恒信合");
						aa.setTradingTime(LocalDateTime.now());
						aa.setDeleteFlag(0);
						aa.setCreateBy(1);
						aa.setOrderSource(1);
						orderFeeDetailMapper.insert(aa);
					}

					// 更改当前节点为 审核通过
			        orderPayApplicationMapper.update(new LambdaUpdateWrapper<OrderPayApplicationInfoEntity>()
							        .eq(OrderPayApplicationInfoEntity::getOrderId, orderId)
							        .eq(OrderPayApplicationInfoEntity::getPaymentDetails,daiChangShenHeTongZhiDTO.getDcOrderNum())
							        .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
							        .set(OrderPayApplicationInfoEntity::getCurrentNode, 30)
			        );

					SaveAuditNodeDTO savedto = new SaveAuditNodeDTO();
					savedto.setApplyInfoId(entity.getId());
					savedto.setCurrentNode(PayApplicationNodeEnums.APPROVE_APPROVAL);
					savedto.setNextNode(PayApplicationNodeEnums.SUCCESS);
					savedto.setAuditType(PayApplicationAuditTypeEnum.YUNQI);
					savedto.setProcessId(null);
					savedto.setRemark("自动处理");
					savedto.setEvent(PayApplicationEventEnums.APPROVE_PASS);
					savedto.setCurrentUserId(1);
					savedto.setApproveTime(LocalDateTime.now());
					orderFeign.saveAuditNode(savedto);
		        }
	        }
		}
		else {
			//查询订单信息
			OrderInfoEntity order = orderInfoMapper.selectOne(new LambdaUpdateWrapper<OrderInfoEntity>()
					.eq(OrderInfoEntity::getOrderNumber, daiChangShenHeTongZhiDTO.getSpOrderNum())
					.last("LIMIT 1"));
			if (ObjUtil.isNotNull(order)) {
				//订单id
				Integer orderId = order.getId();

				/*orderPayApplicationMapper.update(new LambdaUpdateWrapper<OrderPayApplicationInfoEntity>()
						.eq(OrderPayApplicationInfoEntity::getOrderId,orderId)
						.eq(OrderPayApplicationInfoEntity::getPaymentDetails,daiChangShenHeTongZhiDTO.getDcOrderNum())
						.eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
						.set(OrderPayApplicationInfoEntity::getCurrentNode,-10)
				);*/

				SaveAuditNodeDTO savedto = new SaveAuditNodeDTO();
				savedto.setApplyInfoId(order.getId());
				savedto.setCurrentNode(PayApplicationNodeEnums.APPROVE_APPROVAL);
				savedto.setNextNode(PayApplicationNodeEnums.FAIL);
				savedto.setAuditType(PayApplicationAuditTypeEnum.YUNQI);
				savedto.setProcessId(null);
				savedto.setRemark("自动处理");
				savedto.setEvent(PayApplicationEventEnums.APPROVE_REVOKE);
				savedto.setCurrentUserId(1);
				savedto.setApproveTime(LocalDateTime.now());
				orderFeign.saveAuditNode(savedto);
			}
		}
    }

    public void updateRepaymentPlan(Integer orderId, ZhongHengSelOrderStateEnum loanStatus, HengTongLoanInfoQueryVO.HuanKuanList huanKuanInfo , Integer fundId) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        FundRepayStatusEnum fundRepayStatusEnum = FundRepayStatusEnum.NONE;
        Integer term = Integer.parseInt(huanKuanInfo.getQiShu());
        log.info("HengTongServiceImpl.updateRepaymentPlan orderId:{}, term:{} HuanKuanInfo:{}", orderId, term, huanKuanInfo);
        //应还日期
        LocalDate repaymentDate = LocalDate.parse(huanKuanInfo.getYsTime(), formatter);
        //应收合计
        BigDecimal repaymentAmountTotal = new BigDecimal(huanKuanInfo.getYsHeJi());
        //实还日期
        LocalDate actuallyDate  = ObjUtil.isNull(huanKuanInfo.getSsTime()) ? null : LocalDate.parse(huanKuanInfo.getSsTime(), formatter);
        //实还金额
        BigDecimal actuallyAmountTotal  = ObjUtil.isNull(huanKuanInfo.getSsHeJi()) ? BigDecimal.ZERO : new BigDecimal(huanKuanInfo.getSsHeJi());


        FundRepaymentInfoEntity repaymentInfoEntity = new FundRepaymentInfoEntity();

        repaymentInfoEntity.setOrderId(orderId);
        repaymentInfoEntity.setFundId(fundId);
        repaymentInfoEntity.setTerm(term);
        repaymentInfoEntity.setRepaymentDate(repaymentDate);
        repaymentInfoEntity.setRepaymentAmountTotal(repaymentAmountTotal);
        repaymentInfoEntity.setActuallyAmountTotal(actuallyAmountTotal);
        repaymentInfoEntity.setActuallyDate(actuallyDate);
//        if (ObjUtil.isNotNull(actuallyDate)){
//            fundRepayStatusEnum = FundRepayStatusEnum.SETTLED;
//        }
        if (Objects.equals(huanKuanInfo.getIsJieQing(),"0")){
            if (LocalDate.now().isAfter(repaymentDate)){
                fundRepayStatusEnum = FundRepayStatusEnum.OVERDUE;
            }else {
                if (!Objects.equals(actuallyAmountTotal,BigDecimal.ZERO)){
                    fundRepayStatusEnum = FundRepayStatusEnum.PART_RETURN;
                }else {
                    fundRepayStatusEnum = FundRepayStatusEnum.NONE;
                }
            }
//            if (Objects.equals(loanStatus,ZhongHengSelOrderStateEnum.OVERDUE)){
//                fundRepayStatusEnum = FundRepayStatusEnum.OVERDUE;
//            }
        }else {
            fundRepayStatusEnum = FundRepayStatusEnum.SETTLED;
        }
        repaymentInfoEntity.setRepaymentStatus(fundRepayStatusEnum);

        repaymentInfoEntity.setRepaymentPrincipal(ObjUtil.isNull(huanKuanInfo.getYsBenJin()) ? BigDecimal.ZERO : new BigDecimal(huanKuanInfo.getYsBenJin()));
        repaymentInfoEntity.setRepaymentInterest(ObjUtil.isNull(huanKuanInfo.getYsLiXi()) ? BigDecimal.ZERO : new BigDecimal(huanKuanInfo.getYsLiXi()));
        repaymentInfoEntity.setRepaymentPenaltyInterest(ObjUtil.isNull(huanKuanInfo.getYsYuQiWeiYueJin()) ? BigDecimal.ZERO : new BigDecimal(huanKuanInfo.getYsYuQiWeiYueJin()));
        repaymentInfoEntity.setRepaymentReferral(ObjUtil.isNull(huanKuanInfo.getYsYinLiuFei()) ? BigDecimal.ZERO : new BigDecimal(huanKuanInfo.getYsYinLiuFei()));

        repaymentInfoEntity.setActuallyPrincipal(ObjUtil.isNull(huanKuanInfo.getSsBenJin()) ? BigDecimal.ZERO : new BigDecimal(huanKuanInfo.getSsBenJin()));
        repaymentInfoEntity.setActuallyInterest(ObjUtil.isNull(huanKuanInfo.getSsLiXi()) ? BigDecimal.ZERO : new BigDecimal(huanKuanInfo.getSsLiXi()));
        repaymentInfoEntity.setActuallyPenaltyInterest(ObjUtil.isNull(huanKuanInfo.getSsYuQiWeiYueJin()) ? BigDecimal.ZERO : new BigDecimal(huanKuanInfo.getSsYuQiWeiYueJin()));
        repaymentInfoEntity.setActuallyReferral(ObjUtil.isNull(huanKuanInfo.getSsYinLiuFei()) ? BigDecimal.ZERO : new BigDecimal(huanKuanInfo.getSsYinLiuFei()));
        repaymentInfoEntity.setReductionAmount(ObjUtil.isNull(huanKuanInfo.getJmHeJi()) ? BigDecimal.ZERO : new BigDecimal(huanKuanInfo.getJmHeJi()));
        boolean isOverdue = false;
        if (ObjUtil.equals(FundRepayStatusEnum.OVERDUE, fundRepayStatusEnum)) {
            isOverdue = true;
        } else {
            //设置还款状态、是否预期
            isOverdue = fundRepaymentInfoService.determineOverdue(repaymentInfoEntity.getRepaymentAmountTotal(), repaymentInfoEntity.getActuallyAmountTotal(),
                    LocalDate.now(), repaymentInfoEntity.getRepaymentDate(), repaymentInfoEntity.getActuallyDate());
        }

        repaymentInfoEntity.setIsOverdue(isOverdue ? 1 : 0);

        Long count = fundRepaymentInfoMapper.selectCount(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                .eq(FundRepaymentInfoEntity::getTerm, term)
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
        );

        if (Objects.equals(count, 0L)) {
            fundRepaymentInfoMapper.insert(repaymentInfoEntity);
        } else {
            fundRepaymentInfoMapper.update(repaymentInfoEntity, new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
                    .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                    .eq(FundRepaymentInfoEntity::getTerm, term)
                    .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                    .eq(FundRepaymentInfoEntity::getIsdaichang,0)
            );
        }
    }

    /**
     * @param repaymentDate   应还日期
     * @param repaymentAmountTotal 应还金额
     * @param actuallyDate  实还日期
     * @param actuallyAmountTotal  实还金额
     * @return
     */
    public static FundRepayStatusEnum checkRepaymentStatus(LocalDate repaymentDate,BigDecimal repaymentAmountTotal, LocalDate actuallyDate,BigDecimal actuallyAmountTotal) {

        LocalDate today = LocalDate.now();

        if (ObjUtil.isNull(actuallyDate)) {
            //今天 < =  应还日期   未还          4.1 < 4.3
            if (repaymentDate.isEqual(today) || repaymentDate.isBefore(today)) {
                return FundRepayStatusEnum.NONE;
            } else {
                return FundRepayStatusEnum.OVERDUE;
            }
        } else {
            //实还日期 > 应还日期
            if (actuallyDate.isAfter(repaymentDate)) {
                if (actuallyAmountTotal.compareTo(BigDecimal.ZERO) == 0) {
                    return FundRepayStatusEnum.OVERDUE;
                } else if (actuallyAmountTotal.compareTo(repaymentAmountTotal) >= 0) {
                    return FundRepayStatusEnum.SETTLED;
                }
            } else {
                //实还日期 <= 应还日期
                if (actuallyAmountTotal.compareTo(repaymentAmountTotal) >= 0) {
                    return FundRepayStatusEnum.SETTLED;
                }else if (actuallyAmountTotal.compareTo(BigDecimal.ZERO) > 0) {
                    return FundRepayStatusEnum.PART_RETURN;
                }
            }
        }
        return FundRepayStatusEnum.NONE; // 默认返回未还
    }


    private FinalFundInfoEntity getOrderIdByApplyNo(String applyNo) {
        return finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .select(FinalFundInfoEntity::getId, FinalFundInfoEntity::getOrderId)
                .eq(FinalFundInfoEntity::getCreditReqNo, applyNo)
                .eq(FinalFundInfoEntity::getDeleteFlag, 0));
    }

    /**
     * 根据身份证号获取出生年月日
     * @param idCard 身份证号
     * @return 出生年月日，格式为 "yyyy-MM-dd"
     */
    public static String getBirthDateByIdCard(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            throw new IllegalArgumentException("身份证号不合法");
        }
        String birthDateStr = idCard.substring(6, 14);
        String year = birthDateStr.substring(0, 4);
        String month = birthDateStr.substring(4, 6);
        String day = birthDateStr.substring(6, 8);
        return year + "-" + month + "-" + day;
    }

    /**
     * 根据身份证号获取性别
     * @param idCard 身份证号
     * @return 性别，"男"或"女"
     */
    public static String getGenderByIdCard(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            throw new IllegalArgumentException("身份证号不合法");
        }
        char genderChar = idCard.charAt(16);
        return (genderChar - '0') % 2 == 1 ? "男" : "女";
    }

    /**
     * 根据两个生日和性别判断关系
     * @param myBirthday 我的生日，格式为 "yyyy-MM-dd"
     * @param otherBirthday 另一个人的生日，格式为 "yyyy-MM-dd"
     * @param otherGender 另一个人的性别，"男"或"女"
     * @return 8表示哥哥，9表示弟弟，10表示姐姐，11表示妹妹
     */
    public static int getRelationship(String myBirthday, String otherBirthday, String otherGender) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate myDate = LocalDate.parse(myBirthday, formatter);
        LocalDate otherDate = LocalDate.parse(otherBirthday, formatter);

        if (myDate.isBefore(otherDate)) {
            return "男".equals(otherGender) ? 8 : 10; // 哥哥或姐姐
        } else if (myDate.isAfter(otherDate)) {
            return "男".equals(otherGender) ? 9 : 11; // 弟弟或妹妹
        } else {
            return "男".equals(otherGender) ? 9 : 11; // 生日相同，返回弟弟或妹妹
        }
    }

    @Override
    public Result<T> updateOrderStatus() {
        //查询通恒划扣中的数据

        List<FundRepaymentDeductEntity> list = fundRepaymentDeductMapper.selectList(
                new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                        .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)
                        .in(FundRepaymentDeductEntity::getFundId, FundEnum.ZHONG_HENG, FundEnum.ZHONG_HENG_TONG_HUI)
                        .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)

        );
        //查询
        for (int i = 0; i < list.size(); i++) {
            Integer orderId = list.get(i).getOrderId();
            Integer term = list.get(i).getTerm();
            HengTongHuaKouFoSelVO hengTongHuaKouFoSelVO = hengTongHuaKouFoSel(orderId, term);

            if (ObjUtil.isNotNull(hengTongHuaKouFoSelVO) && ObjUtil.equals(hengTongHuaKouFoSelVO.getHuaKouState(), "2") ){
                List<HengTongHuaKouFoSelVO.HuaKouList> huaKouList = hengTongHuaKouFoSelVO.getHuaKouList();
                // 使用流 API 获取划扣时间最新的记录
                HengTongHuaKouFoSelVO.HuaKouList huakouInfo = huaKouList.stream()
                        .max(Comparator.comparing(HengTongHuaKouFoSelVO.HuaKouList::getHuaKouTime)).get();
                //划扣成功 划扣中  不处理
                if (!ObjUtil.equals(huakouInfo.getHuaKouJieGuoState(), "3")) {
                    continue;
                }
                FundDeductRepayStatusEnums fundRepayStatus =FundDeductRepayStatusEnums.REPAYMENT_FAILED;


                String repayOrderNo = list.get(i).getDeductReqNo();
                String failReason = huakouInfo.getHuaKouBeiZhu();
                BigDecimal repayAmt = new BigDecimal(huakouInfo.getHuaKouJinE());
                fundRepaymentDeductService.updateDeductStatus(repayOrderNo, fundRepayStatus, failReason, repayAmt);

                //更新订单还款状态
                OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
                orderApproveFundPlanStatusDTO.setOrderId(orderId);
                orderFeign.updateFundPlanStatus(orderApproveFundPlanStatusDTO);
                orderFeign.updateOrderFundRepayment(orderId);
            }
        }

        return null;
    }

    @Override
    public HengTongHuaKouFoSelVO hengTongHuaKouFoSel(Integer orderId,Integer term) {

        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Integer fundId = orderInfo.getFundId();
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, fundId)
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );
        if (ObjUtil.isNull(finalFundInfo)){
            throw new BusinessException("终审信息不存在");
        }
        log.info("HengTongServiceImpl.hengTongHuaKouFoSel finalFundInfo:{}",finalFundInfo);
        HengTongHuaKouFoSelDTO hengTongHuaKouFoSelDTO = new HengTongHuaKouFoSelDTO();
        hengTongHuaKouFoSelDTO.setOrderNum(finalFundInfo.getLoanBillNo())
                        .setSpOrderNum(orderInfo.getOrderNumber())
                        .setQiShu(term.toString());
        ZhongHengApiResult<HengTongHuaKouFoSelVO> hengTongHuaKouFoSelVOZhongHengApiResult = null;
        try {
             hengTongHuaKouFoSelVOZhongHengApiResult = zhongHengApiClient.hengTongHuaKouFoSel(hengTongHuaKouFoSelDTO);
             if (!ZhongHengApiResult.isSuccess(hengTongHuaKouFoSelVOZhongHengApiResult)){
                 throw new BusinessException(hengTongHuaKouFoSelVOZhongHengApiResult.getMsg());
             }
             log.info("HengTongServiceImpl.hengTongHuaKouFoSel hengTongHuaKouFoSelVOZhongHengApiResult:{}", hengTongHuaKouFoSelVOZhongHengApiResult);
             return hengTongHuaKouFoSelVOZhongHengApiResult.getResponseData();

        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 4.7对公转账审核通知
     * @param hengTongDuiGongSHTongZhiDTO
     */
    @Override
    public void hengTongDuiGongSHTongZhiResult(HengTongDuiGongSHTongZhiDTO hengTongDuiGongSHTongZhiDTO) {
        log.info("HengTongServiceImpl.hengTongSettleResult hengTongDuiGongSHTongZhiDTO: {}", hengTongDuiGongSHTongZhiDTO);
        String qdfyOrderNum = hengTongDuiGongSHTongZhiDTO.getQdfyOrderNum();
        //查账明细
        OrderPayApplicationInfoEntity payApplicationInfo = orderPayApplicationMapper.selectOne(
                new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                        .eq(OrderPayApplicationInfoEntity::getPaymentDetails, qdfyOrderNum)
                        .orderByDesc(OrderPayApplicationInfoEntity::getCreateTime)
                        .last("limit 1"));

        FundRepaymentDeductEntity info = fundRepaymentDeductMapper.selectOne(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                .eq(FundRepaymentDeductEntity::getDeductReqNo, qdfyOrderNum)
                .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION,
                                FundDeductBizTypeEnums.OFFLINE_REPAYMENT
                        ))
                .orderByDesc(FundRepaymentDeductEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(info)){
            throw new BusinessException("资方代扣申请记录不存在");
        }
        String shouKuanShenHeState = hengTongDuiGongSHTongZhiDTO.getShouKuanShenHeState();
        if (ObjUtil.equals(shouKuanShenHeState, "X")){
            info.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_PROCESSING);
        }
        if (ObjUtil.equals(shouKuanShenHeState, "T")){
//            payApplicationInfo.setCurrentNode(PayApplicationNodeEnums.SUCCESS);
//            orderPayApplicationMapper.updateById(payApplicationInfo);
//            //查账节点
//            OrderPayApplyNodeRecordEntity orderPayApplyNodeRecordEntity = orderPayApplyNodeRecordMapper.selectOne(
//                    new LambdaQueryWrapper<OrderPayApplyNodeRecordEntity>()
//                            .eq(OrderPayApplyNodeRecordEntity::getApplyInfoId, payApplicationInfo.getId())
//                            .orderByDesc(OrderPayApplyNodeRecordEntity::getCreateTime)
//                            .last("limit 1"));
//            orderPayApplyNodeRecordEntity.setCurrentNode(PayApplicationNodeEnums.SUCCESS);
//            orderPayApplyNodeRecordEntity.setNextNode(PayApplicationNodeEnums.SUCCESS);
//
//            OrderPayApplyNodeRecordEntity newRecord = new OrderPayApplyNodeRecordEntity();
//            newRecord.setApplyInfoId(payApplicationInfo.getId());
//            newRecord.setCurrentNode(PayApplicationNodeEnums.APPROVE_APPROVAL);
//            newRecord.setNextNode(PayApplicationNodeEnums.SUCCESS);
//            orderPayApplyNodeRecordMapper.insert(newRecord);
            if (ObjUtil.isNotNull(payApplicationInfo)){
                payApplicationInfo.setFundAuditor(hengTongDuiGongSHTongZhiDTO.getShouKuanShenHeRen());
                orderPayApplicationMapper.updateById(payApplicationInfo);
            }
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(payApplicationInfo.getOrderId());
            if (Objects.equals(info.getRepayType(), FundDeductRepayTypeEnums.EARLY_SETTLEMENT)){
                info.setDeductAmount(payApplicationInfo.getPayeeAmount());
                updateApproveInfo(orderInfoEntity, info, payApplicationInfo.getPayeeAmount(), info.getDeductReqNo(),payApplicationInfo.getPaymentTime(),hengTongDuiGongSHTongZhiDTO.getShouKuanShenHeNote());
            }

        }
        //流程中止
        if (ObjUtil.equals(shouKuanShenHeState, "F")){
            info.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_FAILED);
            //查账节点 改为终止
            if (ObjUtil.isNotNull(payApplicationInfo)){
                payApplicationInfo.setFundAuditor(hengTongDuiGongSHTongZhiDTO.getShouKuanShenHeRen());
                payApplicationInfo.setCurrentNode(PayApplicationNodeEnums.FAIL);
                orderPayApplicationMapper.updateById(payApplicationInfo);
                //查账节点
                OrderPayApplyNodeRecordEntity orderPayApplyNodeRecordEntity = orderPayApplyNodeRecordMapper.selectOne(
                        new LambdaQueryWrapper<OrderPayApplyNodeRecordEntity>()
                                .eq(OrderPayApplyNodeRecordEntity::getApplyInfoId, payApplicationInfo.getId())
                                .orderByDesc(OrderPayApplyNodeRecordEntity::getCreateTime)
                                .last("limit 1"));
                orderPayApplyNodeRecordEntity.setCurrentNode(PayApplicationNodeEnums.FAIL);
                orderPayApplyNodeRecordEntity.setNextNode(PayApplicationNodeEnums.FAIL);

                OrderPayApplyNodeRecordEntity newRecord = new OrderPayApplyNodeRecordEntity();
                newRecord.setApplyInfoId(payApplicationInfo.getId());
                newRecord.setCurrentNode(PayApplicationNodeEnums.APPROVE_APPROVAL);
                newRecord.setEvent(2);
                newRecord.setNextNode(PayApplicationNodeEnums.FAIL);
                newRecord.setRemark(hengTongDuiGongSHTongZhiDTO.getShouKuanShenHeNote());
                orderPayApplyNodeRecordMapper.insert(newRecord);


            }
        }
        fundRepaymentDeductMapper.updateById(info);
    }

    /**
     * 6.1结清通知
     * @param hengTongSettleDTO
     */
    @Override
    public void hengTongSettleResult(HengTongSettleDTO hengTongSettleDTO) {
        log.info("HengTongServiceImpl.hengTongSettleResult hengTongSettleDTO: {}", hengTongSettleDTO);
        OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getOrderNumber, hengTongSettleDTO.getSpOrderNum())
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .orderByDesc(OrderInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderInfo)){
            throw new BusinessException("订单不存在");
        }
        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(
                new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .last("limit 1")
        );
        if (ObjUtil.isNull(finalFundInfoEntity)){
            throw new BusinessException("资方终审列表为空");
        }

//        HengTongSettleApplyDTO hengTongSettleApplyDTO = new HengTongSettleApplyDTO();
//        hengTongSettleApplyDTO.setOrderNum(hengTongSettleDTO.getOrderNum());
//        hengTongSettleApplyDTO.setSpOrderNum(hengTongSettleDTO.getSpOrderNum());
//
//        hengTongJieQingShengQing(hengTongSettleApplyDTO);

        //更新成功
        finalFundInfoEntity.setSettleApplyStatus(FundSettleApplyStatusEnums.APPLYING);
        finalFundInfoMapper.updateById(finalFundInfoEntity);

    }

    @Override
    public Result< List<HengTongAccountVO>> selectAccNoInfo() {
        List<HengTongAccountVO> list = new ArrayList<>();
        for (ZhongHengDictEnum.ZhongHengBankAccountEnum status : ZhongHengDictEnum.ZhongHengBankAccountEnum.values()) {
            list.add(new HengTongAccountVO().setAccNo(status.getCode()).setAccName(status.getDescription()));
        }
       return Result.success(list);
    }

    @Override
    public boolean hengTongJieQingShengQing(HengTongSettleApplyDTO hengTongSettleApplyDTO) {
        log.info("HengTongServiceImpl.hengTongJieQingShengQing hengTongSettleApplyDTO: {}", hengTongSettleApplyDTO);
        OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getOrderNumber, hengTongSettleApplyDTO.getSpOrderNum())
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .orderByDesc(OrderInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderInfo)){
            throw new BusinessException("订单不存在");
        }
        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(
                new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .last("limit 1")
        );
        if (ObjUtil.isNull(finalFundInfoEntity)){
            throw new BusinessException("资方终审列表为空");
        }
        ZhongHengApiResult<HengTongSettleApplyDTO> hengTongSettleApplyDTOZhongHengApiResult = null;
        try {
             hengTongSettleApplyDTOZhongHengApiResult = zhongHengApiClient.hengTongJieQingShengQing(hengTongSettleApplyDTO);
            if (!ZhongHengApiResult.isSuccess(hengTongSettleApplyDTOZhongHengApiResult)){
                throw new BusinessException(hengTongSettleApplyDTOZhongHengApiResult.getMsg());
            }
        } catch (BusinessException e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            FundSettleApplyStatusEnums fundSettleApplyStatusEnums = FundSettleApplyStatusEnums.APPLY_SUCCESS;
            if (!ZhongHengApiResult.isSuccess(hengTongSettleApplyDTOZhongHengApiResult)){
                 fundSettleApplyStatusEnums = FundSettleApplyStatusEnums.APPLY_FAILED;
            }
            finalFundInfoEntity.setSettleApplyStatus(fundSettleApplyStatusEnums);
            finalFundInfoMapper.updateById(finalFundInfoEntity);
        }
        return true;
    }

    @Override
    public void hengTongSettleApproveResult(HengTongSettleApproveDTO hengTongSettleApproveDTO) {
        log.info("HengTongServiceImpl.hengTongSettleApproveResult hengTongSettleApproveDTO: {}", hengTongSettleApproveDTO);
        String spOrderNum = hengTongSettleApproveDTO.getSpOrderNum();
        OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getOrderNumber, spOrderNum)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .orderByDesc(OrderInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderInfo)){
            throw new BusinessException("订单不存在");
        }
        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(
                new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .last("limit 1")
        );
        if (ObjUtil.isNull(finalFundInfoEntity)){
            throw new BusinessException("资方终审列表为空");
        }
        //下载结清证明
        if (StrUtil.equals(hengTongSettleApproveDTO.getShenHeState(), "X")){
            return;
        }
        if (StrUtil.equals(hengTongSettleApproveDTO.getShenHeState(), "R")){
            //驳回了 重新下载
            HengTongSettleDTO hengTongSettleDTO = new HengTongSettleDTO()
                    .setSpOrderNum(hengTongSettleApproveDTO.getSpOrderNum())
                    .setOrderNum(hengTongSettleApproveDTO.getSpOrderNum());
            hengTongSettleResult(hengTongSettleDTO);
        }
        if (StrUtil.equals(hengTongSettleApproveDTO.getShenHeState(), "T")){
            //下载结清证明
            List<HengTongSettleApproveDTO.JieQingZhengMing> jieQingZhengMingList = hengTongSettleApproveDTO.getJieQingZhengMing();
            if (CollUtil.isNotEmpty(jieQingZhengMingList)){

                List<String> imageUrls = jieQingZhengMingList.stream().map(HengTongSettleApproveDTO.JieQingZhengMing::getJqzmurl).toList();
                List<MultipartFile> files = new ArrayList<>();
                imageUrls.forEach(imageUrl -> {

                    String cleanedUrl = imageUrl
                            .replaceAll("([^:/])//+", "$1/") // 合并连续斜杠为单个
                            .replaceAll("结清证明", URLEncoder.encode("结清证明", StandardCharsets.UTF_8));

                    String base64Url = convertFileToBase64(cleanedUrl);
                    // 转换为字节数组
                    byte[] decodedBytes = Base64.getDecoder().decode(base64Url);
                    MultipartFile multipartFile = new MockMultipartFile("files", "settleInfo.pdf",
                            MediaType.APPLICATION_PDF_VALUE, decodedBytes);
                    files.add(multipartFile);
                });
                Result<List<FileVO>> listResult = resourceFeign.uploadFile(files);
                if (Result.isSuccess(listResult) && CollUtil.isNotEmpty(listResult.getData())){
                    Integer fundSettlementFileId = resourceFeign.selectFileConfigByCode(FundConstant.FUND_SETTLEMENT_VOUCHER).getData();
                    List<FileVO> data = listResult.getData();
                    data.forEach(fileVO -> {
                        OrderFileEntity orderFileEntity = new OrderFileEntity()
                                .setOrderId(orderInfo.getId())
                                .setFileId(fundSettlementFileId)
                                .setResourceId(data.get(0).getResourceId())
                                .setResourceName(data.get(0).getResourceName());
                        orderFileMapper.insert(orderFileEntity);
                        finalFundInfoEntity.setSettleApplyStatus(FundSettleApplyStatusEnums.DOWNLOADED);
                        finalFundInfoMapper.updateById(finalFundInfoEntity);
                    });
                    OrderSettleApplyInfoEntity orderSettleApplyInfoEntity = new OrderSettleApplyInfoEntity();
                    orderSettleApplyInfoEntity.setOrderId(orderInfo.getId());
                    orderSettleApplyInfoEntity.setApplyStatus(-10);
                    orderSettleApplyInfoEntity.setFundResult(2);
                    orderSettleApplyInfoEntity.setDeleteFlag(1);
                    orderSettleApplyInfoMapper.insert(orderSettleApplyInfoEntity);
                }


            }
        }
    }

    public static String convertFileToBase64(String urlString)  {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); // 设置连接超时时间（可选）
            connection.setReadTimeout(5000);    // 设置读取超时时间（可选）

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (InputStream inputStream = connection.getInputStream();
                     ByteArrayOutputStream baos = new ByteArrayOutputStream()) {

                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        baos.write(buffer, 0, bytesRead);
                    }

                    byte[] fileBytes = baos.toByteArray();
                    return Base64.getEncoder().encodeToString(fileBytes);
                }
            } else {
                throw new Exception("Failed to open connection. Response code: " + responseCode);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Integer uploadSupplementaryBatch(HengTongBuChongToYunQiBatchDTO batchDTO) {

        //查询日期区间放款的订单
        List<OrderInfoEntity> orderInfoList = orderInfoMapper.selectJoinList(OrderInfoEntity.class, new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId, OrderInfoEntity::getOrderNumber)
                .innerJoin(FinalFundInfoEntity.class, on ->
                        on.in(FinalFundInfoEntity::getFundId, List.of(FundEnum.ZHONG_HENG_TONG_HUI.getValue(), FundEnum.ZHONG_HENG.getValue()))
                                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                )
                .eq(FinalFundInfoEntity::getOrderId, OrderInfoEntity::getId)
                .le(ObjUtil.isNotNull(batchDTO.getEndDate()), OrderInfoEntity::getPaymentTime, batchDTO.getEndDate())
                .ge(ObjUtil.isNotNull(batchDTO.getStartDate()), OrderInfoEntity::getPaymentTime, batchDTO.getStartDate())
                .eq(ObjUtil.isNotNull(batchDTO.getOrderId()), OrderInfoEntity::getId, batchDTO.getOrderId())
                .ge(OrderInfoEntity::getState, 5000)
                .in(OrderInfoEntity::getFundId, List.of(FundEnum.ZHONG_HENG_TONG_HUI.getValue(), FundEnum.ZHONG_HENG.getValue()))
                .eq(OrderInfoEntity::getDeleteFlag, 0)
        );
        log.info("HengTongServiceImpl.uploadSupplementaryBatch orderInfoList:{}", JSONUtil.toJsonStr(orderInfoList));

        Integer count = 0;
        for (OrderInfoEntity orderInfo : orderInfoList) {
            try {
                List<FundResourceResultDTO> data = resourceFeign.fundResourceUpload(new FundResourceDTO().setFund(FundEnum.ZHONG_HENG_TONG_HUI)
                        .setType(0).setLinkId(orderInfo.getId())
                ).getData();
                if (CollUtil.isNotEmpty(data)) {
                    for (FundResourceResultDTO fundResourceResultDTO : data) {
                        String result = zhongHengApiClient.buChongToYunQi(orderInfo.getOrderNumber(), fundResourceResultDTO.getFilePath());
                        if (StrUtil.contains(result, "成功")) {
                            count++;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("HengTongServiceImpl.uploadSupplementaryBatch orderId:{} err {}", orderInfo.getId(), e.getMessage());
            }

        }
        return count;
    }
    /**
     * 查询通汇 月还款金额 期数   结清款总额
     * @return
     */
    @Override
    public HengTongRepayDTO getRepayInfo(GetRepayInfoDTO dto) {
        log.info("HengTongServiceImpl.getRepayInfo start for orderId: {}", dto.getOrderId());
        BigDecimal repayAmt = BigDecimal.ZERO;
        BigDecimal jianMianJinE = BigDecimal.ZERO;

        FundRepaymentInfoEntity info = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                .eq(FundRepaymentInfoEntity::getOrderId,dto.getOrderId())
                .eq(FundRepaymentInfoEntity::getFundId, dto.getFundId())
                .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                .orderByAsc(FundRepaymentInfoEntity::getTerm)
                .last("limit 1")
        );
        if (ObjUtil.isNull(info)){
            throw new BusinessException("未查询到还款计划");
        }
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
        List<SprEductionUsageEntity> list = sprEductionUsageEntityService.list(
                new LambdaQueryWrapper<SprEductionUsageEntity>()
                        .eq(SprEductionUsageEntity::getDeleteFlag, 0)
                        .eq(SprEductionUsageEntity::getVehicleNumber, orderInfoEntity.getVehicleNumber())
                        .eq(SprEductionUsageEntity::getUsageStatus, 2)
                        .in(SprEductionUsageEntity::getApprovalTemplate, Arrays.asList(4,5))
        );
        HengTongRepayDTO hengTongRepayDTO = new HengTongRepayDTO();
        BigDecimal dingDrawMoney = BigDecimal.ZERO;
        if (ObjUtil.equals(dto.getRepayType(), FundDeductRepayTypeEnums.EARLY_SETTLEMENT.getCode())){
            HengTongSettleProvisionalEstimateVO settleProvisionalEstimateVO =hengTongSettleProvisionalEstimate(dto.getOrderId(), ZhongHengDictEnum.ZhongHengRepayModeEnum.LIQUIDATED_DAMAGES);
            repayAmt = settleProvisionalEstimateVO.getTsYingHuanHeJi();
            hengTongRepayDTO.setRepayAmt(repayAmt);
            hengTongRepayDTO.setJianMianJinE(settleProvisionalEstimateVO.getTsYingHuanYinLiuFei());
            hengTongRepayDTO.setTerm(info.getTerm());
            if (CollUtil.isNotEmpty(list)){
                dingDrawMoney = list.stream()
                        .filter(entity -> entity.getApprovalTemplate() == 4)
                        .map(SprEductionUsageEntity::getReductionAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            hengTongRepayDTO.setDingDingJianMianJinE(dingDrawMoney);
            hengTongRepayDTO.setOverdueDays(orderInfoEntity.getOverdueDays());
            hengTongRepayDTO.setRepayInterest(ObjUtil.defaultIfNull(settleProvisionalEstimateVO.getTsYingHuanLiXi(), BigDecimal.ZERO) );
            hengTongRepayDTO.setRepayPrincipal(ObjUtil.defaultIfNull(settleProvisionalEstimateVO.getTsYingHuanBenJin(), BigDecimal.ZERO));
        }

        if (ObjUtil.equals(dto.getRepayType(), FundDeductRepayTypeEnums.NORMAL_REPAYMENT.getCode())){
            //获取还款金额
            HengTongLoanInfoQueryVO hengTongLoanInfoQueryVO = hengTongLoanInfoQuery(dto.getOrderId());
            List<HengTongLoanInfoQueryVO.HuanKuanList> huanKuanList = hengTongLoanInfoQueryVO.getHuanKuanList();
            Optional<HengTongLoanInfoQueryVO.HuanKuanList> first = huanKuanList.stream().filter(obj -> obj.getQiShu().equals(info.getTerm().toString())).findFirst();
            HengTongLoanInfoQueryVO.HuanKuanList huanKuanListEntity = null;
            huanKuanListEntity = first.orElse(null);
            if (ObjUtil.isNull(huanKuanListEntity)){
                throw  new BusinessException("查询不到对应的还款信息");
            }
//            剩余应收合计 = 应收合计 - 实收合计 - 减免合计
            BigDecimal shengYuyingshouheji = new BigDecimal(StringUtils.isNotBlank(huanKuanListEntity.getYsHeJi()) ? huanKuanListEntity.getYsHeJi() : "0")
                    .subtract(new BigDecimal(StringUtils.isNotBlank(huanKuanListEntity.getSsHeJi()) ? huanKuanListEntity.getSsHeJi() : "0"))
                    .subtract(new BigDecimal(StringUtils.isNotBlank(huanKuanListEntity.getJmHeJi()) ? huanKuanListEntity.getJmHeJi() : "0"));
//            剩余应收本金 = 应收本金 - 实收本金 - 减免本金
            BigDecimal shengyuyinghsoubenji = new BigDecimal(StringUtils.isNotBlank(huanKuanListEntity.getYsBenJin()) ? huanKuanListEntity.getYsBenJin() : "0")
                    .subtract(new BigDecimal(StringUtils.isNotBlank(huanKuanListEntity.getSsBenJin()) ? huanKuanListEntity.getSsBenJin() : "0"))
                    .subtract(new BigDecimal(StringUtils.isNotBlank(huanKuanListEntity.getJmBenJin()) ? huanKuanListEntity.getJmBenJin() : "0"));
//            剩余应收利息 = 应收利息 - 实收利息 - 减免利息
            BigDecimal shengyuyinghsoulixi = new BigDecimal(StringUtils.isNotBlank(huanKuanListEntity.getYsLiXi()) ? huanKuanListEntity.getYsLiXi() : "0")
                    .subtract(new BigDecimal(StringUtils.isNotBlank(huanKuanListEntity.getSsLiXi()) ? huanKuanListEntity.getSsLiXi() : "0"))
                    .subtract(new BigDecimal(StringUtils.isNotBlank(huanKuanListEntity.getJmLiXi()) ? huanKuanListEntity.getJmLiXi() : "0"));
//            剩余违约金 = 应收违约金 - 实收违约金 - 减免违约金
            BigDecimal shengyuweiyuejin = new BigDecimal(StringUtils.isNotBlank(huanKuanListEntity.getYsYuQiWeiYueJin()) ? huanKuanListEntity.getYsYuQiWeiYueJin() : "0")
                    .subtract(new BigDecimal(StringUtils.isNotBlank(huanKuanListEntity.getSsYuQiWeiYueJin()) ? huanKuanListEntity.getSsYuQiWeiYueJin() : "0"))
                    .subtract(new BigDecimal(StringUtils.isNotBlank(huanKuanListEntity.getJmYuQiWeiYueJin()) ? huanKuanListEntity.getJmYuQiWeiYueJin() : "0"));

            int daysDifference = 0;
            BigDecimal multiply = BigDecimal.ZERO;
            if (StringUtils.isNotBlank(dto.getTransferTime())){
                //转账时间逾期天数
                daysDifference =(int) ChronoUnit.DAYS.between(ObjUtil.isNotNull(info.getActuallyDate()) ? info.getActuallyDate() : info.getRepaymentDate(),LocalDateTime.parse(dto.getTransferTime(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)).toLocalDate());
                if (daysDifference >= 0){
                    //逾期费
                    multiply = (shengyuyinghsoubenji.add(shengyuyinghsoulixi)).multiply(BigDecimal.valueOf(0.005)).multiply(BigDecimal.valueOf(daysDifference));
                }
            }
            repayAmt = shengYuyingshouheji;
            //判断是否逾期  逾期三日内算出减免金额
            jianMianJinE =   shengyuweiyuejin;
            //查询出来的当期还款金额
            hengTongRepayDTO.setRepayAmt(StringUtils.isNotBlank(dto.getTransferTime()) ? (multiply.add(shengyuyinghsoubenji).add(shengyuyinghsoulixi)).setScale(2, RoundingMode.HALF_UP) : repayAmt.setScale(2, RoundingMode.HALF_UP));
            hengTongRepayDTO.setTerm(info.getTerm());
            hengTongRepayDTO.setJianMianJinE(StringUtils.isNotBlank(dto.getTransferTime()) ?  multiply.setScale(2, RoundingMode.HALF_UP) : jianMianJinE.setScale(2, RoundingMode.HALF_UP));
            if (CollUtil.isNotEmpty(list)){
                dingDrawMoney = list.stream()
                        .filter(entity -> entity.getApprovalTemplate() == 5)
                        .map(SprEductionUsageEntity::getReductionAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            hengTongRepayDTO.setDingDingJianMianJinE(dingDrawMoney.setScale(2, RoundingMode.HALF_UP));
            hengTongRepayDTO.setOverdueDays(StringUtils.isNotBlank(hengTongLoanInfoQueryVO.getOrderYuQiTianShu()) ? Integer.valueOf(hengTongLoanInfoQueryVO.getOrderYuQiTianShu()) : 0);
            hengTongRepayDTO.setRepayInterest(shengyuyinghsoulixi);
            hengTongRepayDTO.setRepayCompoundInterest(StringUtils.isNotBlank(huanKuanListEntity.getYsYinLiuFei()) ? new BigDecimal(huanKuanListEntity.getYsYinLiuFei()) : BigDecimal.ZERO);
            hengTongRepayDTO.setRepayPrincipal(shengyuyinghsoubenji);
        }
        log.info("HengTongServiceImpl.getRepayInfo end for hengTongRepayDTO: {}", hengTongRepayDTO);
        return hengTongRepayDTO;
    }

    public static void main(String[] args) {
        String a = null;
        System.out.println("new = " + new BigDecimal(a));
    }
    @Override
    public boolean hengTongRepayByInfo(HengTongRepayCorporateDTO hengTongRepayCorporateDTO) {
        Integer orderId = hengTongRepayCorporateDTO.getOrderId();
        Integer fundId = hengTongRepayCorporateDTO.getFundId();
        Integer term = hengTongRepayCorporateDTO.getTerm();
        BigDecimal repayAmt = new BigDecimal(hengTongRepayCorporateDTO.getSkJinE());
        //查账明细
        OrderPayApplicationInfoEntity payApplicationInfo = orderPayApplicationMapper.selectOne(
                new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                        .eq(OrderPayApplicationInfoEntity::getOrderId, orderId)
                        .orderByDesc(OrderPayApplicationInfoEntity::getCreateTime)
                        .last("limit 1"));
        if (ObjUtil.isNull(payApplicationInfo)){
            throw new BusinessException("未查询到账单信息");
        }

        //查账节点
        OrderPayApplyNodeRecordEntity orderPayApplyNodeRecordEntity = orderPayApplyNodeRecordMapper.selectOne(
                new LambdaQueryWrapper<OrderPayApplyNodeRecordEntity>()
                        .eq(OrderPayApplyNodeRecordEntity::getApplyInfoId, payApplicationInfo.getId())
                        .orderByDesc(OrderPayApplyNodeRecordEntity::getCreateTime)
                        .last("limit 1"));


        OrderInfoEntity orderInfo = orderInfoMapper.selectById(hengTongRepayCorporateDTO.getOrderId());
        FundEnum fundEnum = FundEnum.getFundEnum(fundId);
        FinalFundInfoEntity fundApplyInfo = finalFundInfoService.getInfoByOrderId(orderId, fundEnum);
        Assert.notNull(fundApplyInfo, "未获取到恒通申请信息");
        FundRepaymentInfoEntity fundRepaymentInfo = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                .orderByAsc(FundRepaymentInfoEntity::getTerm)
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                .last("limit 1")
        );
        Assert.notNull(fundRepaymentInfo, ()-> new BusinessException("未获取到待还还款计划"));
        //是否预期
        boolean flag = false;
        flag = !fundRepaymentInfo.getRepaymentDate().isAfter(payApplicationInfo.getPaymentTime().toLocalDate())
                && !fundRepaymentInfo.getRepaymentDate().isEqual(payApplicationInfo.getPaymentTime().toLocalDate());
        String creditReqNo = fundApplyInfo.getCreditReqNo();
        //获取最近一个应还期数
        FundRepayModeEnum repayMode = FundRepayModeEnum.OFFLINE;
        FundDeductRepayTypeEnums  repayType =   ObjUtil.equals(hengTongRepayCorporateDTO.getHkType(),"1")?  (flag ? FundDeductRepayTypeEnums.OVERDUE_REPAYMENT : FundDeductRepayTypeEnums.NORMAL_REPAYMENT) :   FundDeductRepayTypeEnums.EARLY_SETTLEMENT;
        String repayOrderNo = getRepayOrderNo(orderId, term, creditReqNo, repayType, repayMode,fundEnum ,repayAmt);


        String dtoJsonStr = null;
        String resJsonStr = null;
        String failReason = "";
        //获取结清金额

        FundDeductRepayStatusEnums repayStatus = FundDeductRepayStatusEnums.REPAYMENT_FAILED;
        //发起还款申请 调用还款_对公转账
        int num = 1;
        try {
            HengTongRepayCorporateTransferDTO requestDTO = new HengTongRepayCorporateTransferDTO();
            requestDTO.setOrderNum(fundApplyInfo.getLoanBillNo())
                    .setSpOrderNum(orderInfo.getOrderNumber())
                    .setHkType(hengTongRepayCorporateDTO.getHkType())
                    .setTiQianJieQingSuanFa(ZhongHengDictEnum.ZhongHengRepayLoanModeEnum.PENALTY_MODE.getCode())
                    .setSkJinE(repayAmt.toString())
                    .setSkType(ZhongHengDictEnum.ZhongHengPaymentTypeEnum.RENTAL_CUSTOMER_REPAYMENT.getCode())
                    .setZhuanZhangTime(DateUtil.format(hengTongRepayCorporateDTO.getZhuanZhangTime(),  "yyyy-MM-dd"))
                    .setZhuanZhangRen(hengTongRepayCorporateDTO.getZhuanZhangRen())
                    .setShouKuanYingHang(hengTongRepayCorporateDTO.getShouKuanYingHang())
                    .setShouKuanZhangHao(hengTongRepayCorporateDTO.getShouKuanZhangHao())
                    .setJianMianJinE(hengTongRepayCorporateDTO.getJianMianJinE())
                    .setQdfyOrderNum(repayOrderNo)
                    .setQiShu(term.toString())
                    .setFuJianInfo(buildFuJianInfo(hengTongRepayCorporateDTO.getResourceIds()))
            ;
            ZhongHengApiResult<Void> voidZhongHengApiResult = hengTongCorporateTransfer(requestDTO);
            if (!ZhongHengApiResult.isSuccess(voidZhongHengApiResult)){
                throw new BusinessException("结清还款申请失败"+voidZhongHengApiResult.getMsg());
            }
            dtoJsonStr = toJsonStr(requestDTO);
            resJsonStr = toJsonStr(voidZhongHengApiResult);
            log.info("hengtong repay apply success orderId:{}", orderId);
            repayStatus = FundDeductRepayStatusEnums.REPAYMENT_PROCESSING;

            num = 0;
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessage());
        } finally {
            //更新查账明细
            payApplicationInfo.setPaymentDetails(repayOrderNo);
            payApplicationInfo.setCurrentNode(PayApplicationNodeEnums.APPROVE_APPROVAL);
            payApplicationInfo.setDeleteFlag(num);
//            payApplicationInfo.setReductionAmount(ObjUtil.isNotEmpty(hengTongRepayCorporateDTO.getJianMianJinE()) ? new BigDecimal(hengTongRepayCorporateDTO.getJianMianJinE()) : BigDecimal.ZERO);
            orderPayApplicationMapper.updateById(payApplicationInfo);
            orderPayApplyNodeRecordEntity.setCurrentNode(PayApplicationNodeEnums.ACCOUNTANT_APPLY);
            orderPayApplyNodeRecordEntity.setNextNode(PayApplicationNodeEnums.APPROVE_APPROVAL);
            orderPayApplyNodeRecordEntity.setDeleteFlag(num);
            orderPayApplyNodeRecordMapper.updateById(orderPayApplyNodeRecordEntity);
            log.info("hengtong repay apply end orderId:{}", orderId);
            fundRepaymentDeductMapper.update(new LambdaUpdateWrapper<FundRepaymentDeductEntity>()
                    .set(FundRepaymentDeductEntity::getRepayStatus, repayStatus)
                    .set(FundRepaymentDeductEntity::getReqContent, dtoJsonStr)
                    .set(FundRepaymentDeductEntity::getRespContent, resJsonStr)
                    .set(FundRepaymentDeductEntity::getFailReason, failReason)
                    .set(FundRepaymentDeductEntity::getBizType, FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION)
                    .set(FundRepaymentDeductEntity::getRepayDate, LocalDate.now())
                    .set(FundRepaymentDeductEntity::getDeductAmount, repayAmt)
                    .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                    .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                    .eq(FundRepaymentDeductEntity::getTerm, term)
                    .eq(FundRepaymentDeductEntity::getDeductReqNo, repayOrderNo)
            );
        }

        return true;
    }
    @Override
    public TongHuiInitiateRefundVO tongHuiInitiateRefund(TongHuiInitiateRefundDTO dto) {
        try {
            ZhongHengApiResult<TongHuiInitiateRefundVO> zhongHengApiResult = null;
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
            Assert.notNull(orderInfoEntity, () -> new BusinessException("订单不存在"));
            FundEnum fundEnum = FundEnum.getFundEnum(orderInfoEntity.getFundId());
            FinalFundInfoEntity fundApplyInfo = finalFundInfoService.getInfoByOrderId(dto.getOrderId(), fundEnum);
            //HengTongRefundRequestDTO  HengTongRefundRequestVO
            HengTongRefundRequestDTO requestDTO = new HengTongRefundRequestDTO();
            requestDTO.setTFOrderNum(StringUtils.isNotEmpty(dto.getRefundOrderNumber()) ? dto.getRefundOrderNumber() : null);
            requestDTO.setOrderNum(fundApplyInfo.getLoanBillNo());
            requestDTO.setSPOrderNum(orderInfoEntity.getOrderNumber());
            List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                            .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                    FundDeductBizTypeEnums.PAYMENT,
                                    FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                    FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                    FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE,
                                    FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION
                            ))
                            .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
            );
            if (CollUtil.isEmpty(list)){
                requestDTO.setTFType(ZhongHengDictEnum.ZhongHengPaymentTypeEnum.RENTAL_CUSTOMER_REPAYMENT.getCode());
            }else {
                requestDTO.setTFType(ZhongHengDictEnum.ZhongHengPaymentTypeEnum.REFU.getCode());
            }

            requestDTO.setTFJinE(String.valueOf(dto.getPayeeAmount()));
            requestDTO.setShouKuanRen(dto.getPayeeAccount());
            requestDTO.setShouKuanYinHang(dto.getPayeeAccountName());
            requestDTO.setShouKuanZhangHao(dto.getPayeeAccountNumber());
            requestDTO.setQDFYOrderNum(dto.getChannelOrderNumber());
            requestDTO.setTFYuanYin(dto.getRemark());
            List<HengTongRepayCorporateTransferDTO.FuJianInfo> fuJianInfos = buildFuJianInfo(dto.getResourceIdList());
            requestDTO.setFuJianInfo(fuJianInfos.stream()
                    .map(fuJianInfo -> {
                        HengTongRefundRequestDTO.FuJianInfoDTO dto1 =new HengTongRefundRequestDTO.FuJianInfoDTO();
                        dto1.setFuJianTypeNum("TK001");
                        dto1.setFuJianURL(fuJianInfo.getFuJianURL());
                        return dto1;
                    })
                    .collect(Collectors.toList()));
            zhongHengApiResult = zhongHengApiClient.tongHuiInitiateRefund(requestDTO);
            if (!ZhongHengApiResult.isSuccess(zhongHengApiResult)){
                throw new BusinessException("退款申请失败"+zhongHengApiResult.getMsg());
            }
            if (!Objects.equals(zhongHengApiResult.getCode(),"000000")){
                throw new BusinessException("退款申请失败"+zhongHengApiResult.getMsg());
            }
            return zhongHengApiResult.getResponseData();
        } catch (BusinessException e) {
            log.error("hengtong refund apply error orderId:{} error:{}", dto.getOrderId(), e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    public void tuiKuanShenQingTongZhiResult(TuiKuanShenQingTongZhiDTO dto) {
        log.info("HengTongServiceImpl.tuiKuanShenQingTongZhiResult dto:{}",dto);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(
                new LambdaQueryWrapper<OrderInfoEntity>()
                        .eq(OrderInfoEntity::getOrderNumber,dto.getSpOrderNum())
                        .eq(OrderInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderInfoEntity::getCreateTime)
                        .last("limit 1")
        );
        if (orderInfoEntity != null){
            FundRefundInfoEntity one = fundRefundInfoEntityService.getOne(
                    new LambdaQueryWrapper<FundRefundInfoEntity>()
                            .eq(FundRefundInfoEntity::getOrderId, orderInfoEntity.getId())
                            .eq(FundRefundInfoEntity::getFundId, orderInfoEntity.getFundId())
                            .eq(FundRefundInfoEntity::getChannelOrderNumber, dto.getQdFYOrderNum())
                            .eq(FundRefundInfoEntity::getRefundOrderNumber, dto.getTfOrderNum())
                            .eq(FundRefundInfoEntity::getDeleteFlag, 0)
                            .orderByDesc(FundRefundInfoEntity::getCreateTime)
                            .last("limit 1")
            );
            if (one != null){
                OrderPayApplicationInfoEntity orderPayApplicationInfoEntity = orderPayApplicationMapper.selectById(one.getApplyId());
                OrderPayApplyNodeRecordEntity newRecord = new OrderPayApplyNodeRecordEntity();
                newRecord.setApplyInfoId(orderPayApplicationInfoEntity.getId());
                newRecord.setCurrentNode(PayApplicationNodeEnums.APPROVE_APPROVAL);
                // T：成功  X：审核中  F：失败   R:驳回
                switch (dto.getTuiFeiShenHeState()){
                    case "T":
                        orderPayApplicationInfoEntity.setCurrentNode(PayApplicationNodeEnums.SUCCESS);
                        orderPayApplicationMapper.updateById(orderPayApplicationInfoEntity);
                        newRecord.setNextNode(PayApplicationNodeEnums.SUCCESS);
                        newRecord.setEvent(1);
                        orderPayApplyNodeRecordMapper.insert(newRecord);
                        orderFeign.saveOrderFeeDetail(new OrderFeeDetailSaveDTO()
                                .setOrderId(orderInfoEntity.getId())
                                .setTradingSerialNumber(dto.getTfOrderNum())
                                .setAmount((StringUtils.isNotEmpty(dto.getTfJinE()) ? new BigDecimal(dto.getTfJinE()) : orderPayApplicationInfoEntity.getPayeeAmount()))
                                .setTradingMethods(OrderFeeDetailTradingMethodsEnum.FUND_REFUND)
                                .setPayer(orderPayApplicationInfoEntity.getPayAccount())
                                .setPayee(StringUtils.isNotEmpty(dto.getShouKuanRen()) ? dto.getShouKuanRen() : orderPayApplicationInfoEntity.getPayeeAccount())
                                .setExpenseType(orderPayApplicationInfoEntity.getFeeType())
                                .setStatus(OrderFeeDetailStatusEnum.SPENDING)
                                .setTerm(orderPayApplicationInfoEntity.getRepaymentTerm())
                                .setTradingTime(LocalDateTime.parse(dto.getTuiFeiShenHeTime(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)))
                                .setRemark(null));
                        break;
                    case "X":
                        orderPayApplicationInfoEntity.setCurrentNode(PayApplicationNodeEnums.APPROVE_APPROVAL);
                        orderPayApplicationMapper.updateById(orderPayApplicationInfoEntity);
                        break;
                    case "F":
                        orderPayApplicationInfoEntity.setCurrentNode(PayApplicationNodeEnums.FAIL);
                        orderPayApplicationMapper.updateById(orderPayApplicationInfoEntity);
                        newRecord.setNextNode(PayApplicationNodeEnums.FAIL);
                        newRecord.setEvent(2);
                        newRecord.setRemark(dto.getTuiFeiShenHeNote());
                        orderPayApplyNodeRecordMapper.insert(newRecord);
                        break;
                    case "R":
                        orderPayApplicationInfoEntity.setCurrentNode(PayApplicationNodeEnums.ACCOUNTANT_APPLY);
                        orderPayApplicationMapper.updateById(orderPayApplicationInfoEntity);
                        newRecord.setNextNode(PayApplicationNodeEnums.ACCOUNTANT_APPLY);
                        newRecord.setEvent(3);
                        newRecord.setRemark(dto.getTuiFeiShenHeNote());
                        orderPayApplyNodeRecordMapper.insert(newRecord);
                        break;
                }
                one.setResult_json(JSON.toJSONString(dto));
                fundRefundInfoEntityService.updateById(one);
            }
        }
    }

    @Override
    public Boolean tongHuiCompensateSwitch(TongHuiCompensateSwitchDTO dto) {
        log.info("HengTongServiceImpl.tongHuiCompensateSwitch dto:{}",JSONUtil.toJsonStr(dto));
        try {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
            Assert.notNull(orderInfoEntity, () -> new BusinessException("订单不存在"));
            FundEnum fundEnum = FundEnum.getFundEnum(orderInfoEntity.getFundId());
            FinalFundInfoEntity fundApplyInfo = finalFundInfoService.getInfoByOrderId(dto.getOrderId(), fundEnum);
            TongHuiCompensateDTO requestDTO =new TongHuiCompensateDTO();
            requestDTO.setOrderNum(fundApplyInfo.getLoanBillNo());
            requestDTO.setSpOrderNum(orderInfoEntity.getOrderNumber());
            switch (dto.getCompensateStatus()){
                case 1:
                    ZhongHengApiResult<Void> activationResult = zhongHengApiClient.compensationActivation(requestDTO);
                    if (!ZhongHengApiResult.isSuccess(activationResult)){
                        throw new BusinessException("代偿开启失败");
                    }
                    if (!Objects.equals(activationResult.getCode(),"000000")){
                        throw new BusinessException("代偿开启失败"+activationResult.getMsg());
                    }
                    break;
                case 2:
                    ZhongHengApiResult<Void> closureResult =  zhongHengApiClient.compensatoryClosure(requestDTO);
                    if (!ZhongHengApiResult.isSuccess(closureResult)){
                        throw new BusinessException("代偿关闭失败");
                    }
                    if (!Objects.equals(closureResult.getCode(),"000000")){
                        throw new BusinessException("代偿关闭失败"+closureResult.getMsg());
                    }
                    break;
                default:
                    break;
            }
            return true;
        } catch (BusinessException e) {
            log.error("HengTongServiceImpl.tongHuiCompensateSwitch error:{}", e.getMessage());
        }
        return false;
    }

    @Override
    public Boolean compensateSettleNotice(DaiChangJieQingTongZhiResultDTO daiChangJieQingTongZhiResultDTO) {
        ZhongHengApiResult<Void> result = zhongHengApiClient.compensateSettleNotice(daiChangJieQingTongZhiResultDTO);
        if (!ZhongHengApiResult.isSuccess(result)){
            throw new BusinessException("代偿结清通知失败");
        }
        if (!Objects.equals(result.getCode(),"000000")){
            throw new BusinessException("代偿结清通知失败"+result.getMsg());
        }
        return true;
    }

    @Override
    public String zhHeTongBuChongPiLiang(Map<String, Object> requestData) {
        String result = zhongHengApiClient.zhHeTongBuChongPiLiang(requestData);
        return result;
    }

    @Override
    public Boolean updateHengTongRepayDeductStatus(UpdateHengTongRepayDeductStatusDTO dto) {
        log.info("HengTongServiceImpl.updateHengTongRepayDeductStatus dto:{}",JSONUtil.toJsonStr(dto));
        if (ObjUtil.isNotEmpty(dto) && ObjUtil.isNotEmpty(dto.getOrderId())){
            FundRepaymentDeductEntity fundRepaymentDeductEntity = fundRepaymentDeductService.getOne(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                            .eq(FundRepaymentDeductEntity::getFundId,FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                            .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                            .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                    FundDeductBizTypeEnums.PAYMENT,
                                    FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                    FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE
                            ))
                            .orderByDesc(FundRepaymentDeductEntity::getId)
                            .last("limit 1")
            );
            if (ObjUtil.isNotNull(fundRepaymentDeductEntity)){
                log.info("HengTongServiceImpl.updateHengTongRepayDeductStatus fundRepaymentDeductEntity:{}",JSONUtil.toJsonStr(fundRepaymentDeductEntity));
                Integer orderId = fundRepaymentDeductEntity.getOrderId();
                Integer term = fundRepaymentDeductEntity.getTerm();
                BigDecimal repayAmt = fundRepaymentDeductEntity.getDeductAmount();
                FundDeductRepayTypeEnums repayType = fundRepaymentDeductEntity.getRepayType();
                String failReason = "";
                try {
                    HengTongHuaKouFoSelVO hengTongHuaKouFoSelVO = hengTongHuaKouFoSel(fundRepaymentDeductEntity.getOrderId(), fundRepaymentDeductEntity.getTerm());
                    if (ObjUtil.isNotNull(hengTongHuaKouFoSelVO) && ObjUtil.equals(hengTongHuaKouFoSelVO.getHuaKouState(), "2") ){
                        List<HengTongHuaKouFoSelVO.HuaKouList> huaKouList = hengTongHuaKouFoSelVO.getHuaKouList();
                        log.info("HengTongServiceImpl.updateHengTongRepayDeductStatus huaKouList:{}",JSONUtil.toJsonStr(huaKouList));
                        // 使用流 API 获取划扣时间最新的记录
                        HengTongHuaKouFoSelVO.HuaKouList huaKouInfo = huaKouList.stream()
                                .max(Comparator.comparing(HengTongHuaKouFoSelVO.HuaKouList::getHuaKouTime)).get();
                        log.info("HengTongServiceImpl.updateHengTongRepayDeductStatus huaKouInfo:{}",JSONUtil.toJsonStr(huaKouInfo));

                        //划扣成功 划扣中  不处理
                        if (ObjUtil.isNull(huaKouInfo)){
                            throw new BusinessException("未查询到划扣信息");
                        }
                        if (Objects.equals(huaKouInfo.getHuaKouJieGuoState(), "3") && huaKouList.stream()
                                .anyMatch(huaKou -> "2".equals(huaKou.getHuaKouJieGuoState()))){
                            Optional<HengTongHuaKouFoSelVO.HuaKouList> latestSuccessRecord = huaKouList.stream()
                                    .filter(huaKou -> "2".equals(huaKou.getHuaKouJieGuoState()))  // 筛选划扣成功的记录
                                    .max(Comparator.comparing(HengTongHuaKouFoSelVO.HuaKouList::getHuaKouTime));
                            if (latestSuccessRecord.get().getHuaKouJinE().compareTo(String.valueOf(repayAmt)) ==0) {
                                huaKouInfo = latestSuccessRecord.get();
                            }
                        }
                        if (ObjUtil.equals(huaKouInfo.getHuaKouJieGuoState(), "2")){
                            //获取还款金额
                            HengTongLoanInfoQueryVO hengTongLoanInfoQueryVO = hengTongLoanInfoQuery(orderId);
                            log.info("HengTongServiceImpl.updateHengTongRepayDeductStatus hengTongLoanInfoQueryVO:{}",JSONUtil.toJsonStr(hengTongLoanInfoQueryVO));
                            List<HengTongLoanInfoQueryVO.HuanKuanList> huanKuanList = hengTongLoanInfoQueryVO.getHuanKuanList();
                            Optional<HengTongLoanInfoQueryVO.HuanKuanList> first = huanKuanList.stream().filter(obj -> obj.getQiShu().equals(term.toString())).findFirst();

                            HengTongLoanInfoQueryVO.HuanKuanList huanKuanListEntity = null;
                            huanKuanListEntity = first.orElse(null);
                            if (ObjUtil.isNull(huanKuanListEntity)){
                                throw  new BusinessException("查询不到对应的还款信息");
                            }
                            log.info("HengTongServiceImpl.updateHengTongRepayDeductStatus huanKuanListEntity:{}",JSONUtil.toJsonStr(huanKuanListEntity));
                            //查询出来的当期还款金额
                            BigDecimal huanKuanAmount = ObjUtil.isNull(huanKuanListEntity.getSsHeJi()) ? BigDecimal.ZERO : new BigDecimal(huanKuanListEntity.getSsHeJi());
                            if (huanKuanAmount.compareTo(BigDecimal.ZERO)==0){
                                throw new BusinessException("还款信息未同步 请稍等");
                            }

                            //划扣成功
                            log.info("hengTongRepayServiceImpl.updateHengTongRepayDeductStatus repay result success orderId:{}", orderId);
                            // 更新资方还款记录 实还信息
                            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
                            updateRepaymentPlanInfo(orderId,orderInfoEntity.getFundId());


                            if (ObjUtil.isNotNull(repayAmt) && !Objects.equals(repayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)) {
                                //判断是否还款其他逾期期数
                                FundRepaymentInfoEntity deductFundRepaymentInfo = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                                        .eq(FundRepaymentInfoEntity::getTerm, term)
                                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                );
                                log.info("HengTongServiceImpl.updateHengTongRepayDeductStatus deductFundRepaymentInfo:{}",JSONUtil.toJsonStr(deductFundRepaymentInfo));
                                if (ObjUtil.isNotNull(deductFundRepaymentInfo) && deductFundRepaymentInfo.getActuallyAmountTotal().compareTo(repayAmt) < 0){
                                    failReason += " 注: 当前还款包含其他待还期数依次划扣";
                                }

                            }

                            fundRepaymentDeductMapper.update(new LambdaUpdateWrapper<FundRepaymentDeductEntity>()
                                    .set(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                                    .set(ObjUtil.isNotNull(repayAmt), FundRepaymentDeductEntity::getDeductAmount, repayAmt)
                                    .set(StrUtil.isNotBlank(failReason), FundRepaymentDeductEntity::getFailReason, failReason)
                                    .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                                    .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                                    .eq(FundRepaymentDeductEntity::getTerm, term)
                                    .eq(FundRepaymentDeductEntity::getDeductReqNo, fundRepaymentDeductEntity.getDeductReqNo())
                            );

                            log.info("HengTongServiceImpl.updateHengTongRepayDeductStatus update repayment info order:{} term:{}", orderId, term);
                            LocalDateTime completionTime =  DateUtil.parse(huaKouInfo.getHuaKouTime()).toLocalDateTime();
                            if (Objects.equals(repayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)) {
                                //提前结清 全部期数更新为已结清
                                fundRepaymentInfoMapper.update(new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
                                        .set(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                                        .set(Objects.nonNull(completionTime), FundRepaymentInfoEntity::getActuallyDate, completionTime)
                                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                                );
                            }


                            try {
                                OrderFeeDetailExpandTypeEnum expandType = ObjUtil.equals(repayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT) ?
                                        OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT : OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT;

                                //保存费用明细 todo
                                saveFeeDetail(term, orderId,  fundRepaymentDeductEntity.getDeductReqNo(), fundRepaymentDeductEntity.getDeductAmount(), expandType,null,FundEnum.getFundEnum(fundRepaymentDeductEntity.getFundId()).getFundName());
                            } catch (Exception e) {
                                log.error("hengTongRepayServiceImpl.updateHengTongRepayDeductStatus saveFeeDetail deductReqNo:{} error:{}", fundRepaymentDeductEntity.getDeductReqNo(), e.getMessage(), e);
                            }
                            //更新订单还款状态
                            try {
                                OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
                                orderApproveFundPlanStatusDTO.setOrderId(orderId);
                                orderFeign.updateFundPlanStatus(orderApproveFundPlanStatusDTO);
                            } catch (Exception e) {
                                log.error("hengTongRepayServiceImpl.updateHengTongRepayDeductStatus repay result processing updateFundPlanStatus orderId:{}", orderId);
                            }

                            try {
                                orderFeign.updateOrderFundRepayment(orderId);
                            } catch (Exception e) {
                                log.error("hengTongRepayServiceImpl.updateHengTongRepayDeductStatus repay result processing updateOrderFundRepayment orderId:{}", orderId);
                            }
                        }
                    }
                    log.info("HengTongServiceImpl.updateHengTongRepayDeductStatus hengTongHuaKouFoSelVO:{}", JSONUtil.toJsonStr(hengTongHuaKouFoSelVO));
                } catch (Exception e) {
                    log.info("HengTongServiceImpl.updateHengTongRepayDeductStatus error:{}", e.getMessage());
                }
            }
        }else {
            List<FundRepaymentDeductEntity> entityList = new ArrayList<>();
            List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getFundId,FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                            .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)
                            .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                    FundDeductBizTypeEnums.PAYMENT,
                                    FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                    FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE
                            ))
            );

            List<FundRepaymentDeductEntity> list1 = fundRepaymentDeductService.list(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .select(FundRepaymentDeductEntity::getOrderId, FundRepaymentDeductEntity::getTerm)
                            .eq(FundRepaymentDeductEntity::getFundId,FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                            .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_FAILED)
                            .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                    FundDeductBizTypeEnums.PAYMENT,
                                    FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                    FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE
                            ))
                            .groupBy(FundRepaymentDeductEntity::getOrderId, FundRepaymentDeductEntity::getTerm)
            );
            List<FundRepaymentDeductEntity> list2 = fundRepaymentDeductService.list(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .select(FundRepaymentDeductEntity::getOrderId, FundRepaymentDeductEntity::getTerm)
                            .eq(FundRepaymentDeductEntity::getFundId,FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                            .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                            .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                    FundDeductBizTypeEnums.PAYMENT,
                                    FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                    FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE
                            ))
                            .groupBy(FundRepaymentDeductEntity::getOrderId, FundRepaymentDeductEntity::getTerm)
            );
            // 将划扣成功的转为set
            Set<String> list2KeySet = list2.stream()
                    .map(entity -> entity.getOrderId() + "_" + entity.getTerm())
                    .collect(Collectors.toSet());
            // 划扣失败的期数
            List<FundRepaymentDeductEntity> diffList = list1.stream()
                    .filter(entity -> !list2KeySet.contains(entity.getOrderId() + "_" + entity.getTerm()))
                    .toList();
            if (CollUtil.isNotEmpty(diffList) && CollUtil.isNotEmpty(list)){
                List<FundRepaymentDeductEntity> diff = new ArrayList<>();
                list1.forEach(entity -> {
                    FundRepaymentDeductEntity entity1 = fundRepaymentDeductService.getOne(
                            new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                                    .eq(FundRepaymentDeductEntity::getFundId,FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                                    .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                                    .eq(FundRepaymentDeductEntity::getOrderId, entity.getOrderId())
                                    .eq(FundRepaymentDeductEntity::getTerm, entity.getTerm())
                                    .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_FAILED)
                                    .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                            FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                            FundDeductBizTypeEnums.PAYMENT,
                                            FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                            FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE
                                    ))
                                    .orderByDesc(FundRepaymentDeductEntity::getCreateTime)
                                    .last("limit 1")
                    );
                    diff.add(entity1);
                });
                log.info("HengTongServiceImpl.updateHengTongRepayDeductStatus diffList:{}", JSONUtil.toJsonStr(diffList));
                log.info("HengTongServiceImpl.updateHengTongRepayDeductStatus list:{}", JSONUtil.toJsonStr(list));
                entityList.addAll(diff);
                //将划扣失败的转为set
                Set<String> diffListKeySet = diffList.stream()
                        .map(entity -> entity.getOrderId() + "_" + entity.getTerm())
                        .collect(Collectors.toSet());
                // 划扣中的期数
                List<FundRepaymentDeductEntity> diffList1 = list.stream()
                        .filter(entity -> !diffListKeySet.contains(entity.getOrderId() + "_" + entity.getTerm()))
                        .toList();
                entityList.addAll(diffList1);
            }
            if (CollUtil.isNotEmpty(entityList)){
                log.info("HengTongServiceImpl.updateHengTongRepayDeductStatus entityList:{}", JSONUtil.toJsonStr(entityList));
                List<HengTongHuaKouFoSelVO> hengTongHuaKouFoSelVOList = new ArrayList<>();
                for (FundRepaymentDeductEntity fundRepaymentDeductEntity : entityList) {
                    Integer orderId = fundRepaymentDeductEntity.getOrderId();
                    Integer term = fundRepaymentDeductEntity.getTerm();
                    BigDecimal repayAmt = fundRepaymentDeductEntity.getDeductAmount();
                    FundDeductRepayTypeEnums repayType = fundRepaymentDeductEntity.getRepayType();
                    String failReason = "";
                    try {
                        HengTongHuaKouFoSelVO hengTongHuaKouFoSelVO = hengTongHuaKouFoSel(fundRepaymentDeductEntity.getOrderId(), fundRepaymentDeductEntity.getTerm());
                        if (ObjUtil.isNotNull(hengTongHuaKouFoSelVO) && ObjUtil.equals(hengTongHuaKouFoSelVO.getHuaKouState(), "2") ){
                            log.info("HengTongServiceImpl.updateHengTongRepayDeductStatusList hengTongHuaKouFoSelVO:{}", JSONUtil.toJsonStr(hengTongHuaKouFoSelVO));
                            List<HengTongHuaKouFoSelVO.HuaKouList> huaKouList = hengTongHuaKouFoSelVO.getHuaKouList();
                            // 使用流 API 获取划扣时间最新的记录
                            HengTongHuaKouFoSelVO.HuaKouList huaKouInfo = huaKouList.stream()
                                    .max(Comparator.comparing(HengTongHuaKouFoSelVO.HuaKouList::getHuaKouTime)).get();
                            if (ObjUtil.isNull(huaKouInfo)){
                                throw new BusinessException("未查询到划扣信息");
                            }
                            if (Objects.equals(huaKouInfo.getHuaKouJieGuoState(), "3") && huaKouList.stream()
                                    .anyMatch(huaKou -> "2".equals(huaKou.getHuaKouJieGuoState()))){
                                Optional<HengTongHuaKouFoSelVO.HuaKouList> latestSuccessRecord = huaKouList.stream()
                                        .filter(huaKou -> "2".equals(huaKou.getHuaKouJieGuoState()))  // 筛选划扣成功的记录
                                        .max(Comparator.comparing(HengTongHuaKouFoSelVO.HuaKouList::getHuaKouTime));
                                if (latestSuccessRecord.get().getHuaKouJinE().compareTo(String.valueOf(repayAmt)) ==0) {
                                    huaKouInfo = latestSuccessRecord.get();
                                }
                            }
                            if (ObjUtil.equals(huaKouInfo.getHuaKouJieGuoState(), "2")){
                                //获取还款金额
                                HengTongLoanInfoQueryVO hengTongLoanInfoQueryVO = hengTongLoanInfoQuery(orderId);
                                log.info("HengTongServiceImpl.updateHengTongRepayDeductStatusList hengTongLoanInfoQueryVO:{}", JSONUtil.toJsonStr(hengTongLoanInfoQueryVO));
                                List<HengTongLoanInfoQueryVO.HuanKuanList> huanKuanList = hengTongLoanInfoQueryVO.getHuanKuanList();
                                Optional<HengTongLoanInfoQueryVO.HuanKuanList> first = huanKuanList.stream().filter(obj -> obj.getQiShu().equals(term.toString())).findFirst();

                                HengTongLoanInfoQueryVO.HuanKuanList huanKuanListEntity = null;
                                huanKuanListEntity = first.orElse(null);
                                if (ObjUtil.isNull(huanKuanListEntity)){
                                    throw  new BusinessException("查询不到对应的还款信息");
                                }
                                //查询出来的当期还款金额
                                BigDecimal huanKuanAmount = ObjUtil.isNull(huanKuanListEntity.getSsHeJi()) ? BigDecimal.ZERO : new BigDecimal(huanKuanListEntity.getSsHeJi());
                                if (huanKuanAmount.compareTo(BigDecimal.ZERO)==0){
                                    throw new BusinessException("还款信息未同步 请稍等");
                                }

                                //划扣成功
                                log.info("hengTongRepayServiceImpl.updateHengTongRepayDeductStatusList repay result success orderId:{}", orderId);
                                // 更新资方还款记录 实还信息
                                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
                                updateRepaymentPlanInfo(orderId,orderInfoEntity.getFundId());

                                if (ObjUtil.isNotNull(repayAmt) && !Objects.equals(repayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)) {
                                    //判断是否还款其他逾期期数
                                    FundRepaymentInfoEntity deductFundRepaymentInfo = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                            .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                                            .eq(FundRepaymentInfoEntity::getTerm, term)
                                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                    );
                                    log.info("HengTongServiceImpl.updateHengTongRepayDeductStatusList deductFundRepaymentInfo:{}", JSONUtil.toJsonStr(deductFundRepaymentInfo));
                                    if (ObjUtil.isNotNull(deductFundRepaymentInfo) && deductFundRepaymentInfo.getActuallyAmountTotal().compareTo(repayAmt) < 0){
                                        failReason += " 注: 当前还款包含其他待还期数依次划扣";
                                    }

                                }

                                fundRepaymentDeductMapper.update(new LambdaUpdateWrapper<FundRepaymentDeductEntity>()
                                        .set(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                                        .set(ObjUtil.isNotNull(repayAmt), FundRepaymentDeductEntity::getDeductAmount, repayAmt)
                                        .set(StrUtil.isNotBlank(failReason), FundRepaymentDeductEntity::getFailReason, failReason)
                                        .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                                        .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                                        .eq(FundRepaymentDeductEntity::getTerm, term)
                                        .eq(FundRepaymentDeductEntity::getDeductReqNo, fundRepaymentDeductEntity.getDeductReqNo())
                                );

                                log.info("hengTongRepayServiceImpl.updateHengTongRepayDeductStatusList update repayment info order:{} term:{}", orderId, term);
                                LocalDateTime completionTime =  DateUtil.parse(huaKouInfo.getHuaKouTime()).toLocalDateTime();
                                if (Objects.equals(repayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)) {
                                    //提前结清 全部期数更新为已结清
                                    fundRepaymentInfoMapper.update(new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
                                            .set(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                                            .set(Objects.nonNull(completionTime), FundRepaymentInfoEntity::getActuallyDate, completionTime)
                                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                            .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                                    );
                                }


                                try {
                                    OrderFeeDetailExpandTypeEnum expandType = ObjUtil.equals(repayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT) ?
                                            OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT : OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT;

                                    //保存费用明细 todo
                                    saveFeeDetail(term, orderId,  fundRepaymentDeductEntity.getDeductReqNo(), fundRepaymentDeductEntity.getDeductAmount(), expandType,null,FundEnum.getFundEnum(fundRepaymentDeductEntity.getFundId()).getFundName());
                                } catch (Exception e) {
                                    log.error("hengTongRepayServiceImpl.updateHengTongRepayDeductStatusList saveFeeDetail deductReqNo:{} error:{}", fundRepaymentDeductEntity.getDeductReqNo(), e.getMessage(), e);
                                }
                                //更新订单还款状态
                                try {
                                    OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
                                    orderApproveFundPlanStatusDTO.setOrderId(orderId);
                                    orderFeign.updateFundPlanStatus(orderApproveFundPlanStatusDTO);
                                } catch (Exception e) {
                                    log.error("hengTongRepayServiceImpl.updateHengTongRepayDeductStatusList repay result processing updateFundPlanStatus orderId:{}", orderId);
                                }

                                try {
                                    orderFeign.updateOrderFundRepayment(orderId);
                                } catch (Exception e) {
                                    log.error("hengTongRepayServiceImpl.updateHengTongRepayDeductStatusList repay result processing updateOrderFundRepayment orderId:{}", orderId);
                                }
                            }
                        }
                        hengTongHuaKouFoSelVOList.add(hengTongHuaKouFoSelVO);
                    } catch (Exception e) {
                        log.info("HengTongServiceImpl.updateHengTongRepayDeductStatusList error:{}", e.getMessage());
                    }
                }
                log.info("HengTongServiceImpl.updateHengTongRepayDeductStatusList hengTongHuaKouFoSelVOList:{}", JSONUtil.toJsonStr(hengTongHuaKouFoSelVOList));
            }
        }
        return true;
    }

	/**
	 * 中恒-嘉泰代偿后客户还款通知
	 */
	@Override
	public ZhongHengApiResult<CommuteAndPayVO> daiChangHouHKTZ(CommuteAndPayDTO dto) {
		log.info("HengTongServiceImpl.daiChangHouHKTZ dto:{}", JSONUtil.toJsonStr(dto));
		ZhongHengApiResult<CommuteAndPayVO> result = new ZhongHengApiResult<CommuteAndPayVO>();
		try {
			result = zhongHengApiClient.daiChangHouHKTZ(dto);
			log.info("HengTongServiceImpl.daiChangHouHKTZ result:{}", JSONUtil.toJsonStr(result));
			if (!ZhongHengApiResult.isSuccess(result)) {
				throw new BusinessException("嘉泰代偿后客户还款通知失败:"+ result.getMsg());
			}
			if (ObjUtil.isNull(result.getResponseData())){
				throw new BusinessException("嘉泰代偿后客户还款通知失败:"+ result.getMsg());
			}
		} catch (Exception e) {
			throw new BusinessException(e.getMessage());
		}
		return  result;
	}

	private String getRepayOrderNo(Integer orderId, Integer term, String creditReqNo, FundDeductRepayTypeEnums repayType, FundRepayModeEnum repayMode , FundEnum fundEnum, BigDecimal skJinE) {
        FundRepaymentDeductEntity fundRepaymentDeductEntity = fundRepaymentDeductMapper.selectOne(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                .eq(FundRepaymentDeductEntity::getTerm, term)
                .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                        FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION,
                        FundDeductBizTypeEnums.PAYMENT,
                        FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                        FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                        FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE,
                        FundDeductBizTypeEnums.WITHHOLDING_REQUESTS
                ))
                .orderByDesc(FundRepaymentDeductEntity::getIndex)
                .last("limit 1")
        );
        int index = 1;
        if (fundRepaymentDeductEntity != null) {
            FundDeductRepayStatusEnums repayStatus = fundRepaymentDeductEntity.getRepayStatus();
            if (repayStatus == FundDeductRepayStatusEnums.REPAYMENT_FAILED) {
                index = fundRepaymentDeductEntity.getIndex() + 1;

            } else if (repayStatus == FundDeductRepayStatusEnums.REPAYMENT_PROCESSING) {
                log.error("getRepayOrderNo orderId:{} term:{} exist repay apply ", orderId, term);
                throw new BusinessException("存在还款申请");
            } else if (repayStatus == FundDeductRepayStatusEnums.REPAYMENT_SUCCESS) {
//                log.error("getRepayOrderNo orderId:{} term:{} exist repay success ", orderId, term);
//                throw new BusinessException("存在还款成功");
                index = fundRepaymentDeductEntity.getIndex() + 1;
            } else if (repayStatus == null) {
                return fundRepaymentDeductEntity.getDeductReqNo();
            }

        }

        BigDecimal repayAmount = skJinE;

        String repayOrderNo = String.format("%s_%d_%d", creditReqNo, term, index);

        FundDeductBizTypeEnums bizType;
        if (ObjUtil.equals(repayMode, FundRepayModeEnum.OFFLINE)) {
            bizType = FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION;
        } else {
            bizType = FundDeductBizTypeEnums.WITHHOLDING_REQUESTS;
        }

        fundRepaymentDeductEntity = new FundRepaymentDeductEntity();

        fundRepaymentDeductEntity.setOrderId(orderId);
        fundRepaymentDeductEntity.setTerm(term);
        fundRepaymentDeductEntity.setDeductReqNo(repayOrderNo);
        fundRepaymentDeductEntity.setIndex(index);
        fundRepaymentDeductEntity.setFundId(fundEnum.getValue());
        fundRepaymentDeductEntity.setRepayType(repayType);
        fundRepaymentDeductEntity.setBizType(bizType);
        fundRepaymentDeductEntity.setDeductAmount(repayAmount);

        fundRepaymentDeductMapper.insert(fundRepaymentDeductEntity);

        return repayOrderNo;
    }


    private List<HengTongRepayCorporateTransferDTO.FuJianInfo> buildFuJianInfo(List<String> resourceIds) {
        List<HengTongRepayCorporateTransferDTO.FuJianInfo> fuJianInfos = new ArrayList<>();
        for (String resoureId : resourceIds) {
            Result<String> stringResult = resourceFeign.longTimeAccessRouteRequest(resoureId);
            if (!Result.isSuccess(stringResult)) {
                throw new BusinessException("转化url失败");
            }
            HengTongRepayCorporateTransferDTO.FuJianInfo fuJianInfo = new HengTongRepayCorporateTransferDTO.FuJianInfo();
            fuJianInfo.setFuJianTypeNum(heTongCode).setFuJianURL(stringResult.getData());
            fuJianInfos.add(fuJianInfo);
        }
        return fuJianInfos;
    }
    private String toJsonStr(Object dto) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(dto);
        } catch (JsonProcessingException e) {
            log.error("hengTongRepayServiceImpl toJsonStr failed error: {}", e.getMessage());
            throw new BusinessException(e);
        }
    }

    /**
     * 18.7 银行卡信息同步(只能是贷后换卡方可调用)
     * @param dto
     * @return
     */
    @Override
    public ZhongHengApiResult<HengTongATMCardInfoSyncVO> hengTongATMCardInfoSync(HengTongATMCardInfoSyncDTO dto) {
        log.info("HengTongServiceImpl.hengTongATMCardInfoSync dto:{}", JSONUtil.toJsonStr(dto));
        ZhongHengApiResult<HengTongATMCardInfoSyncVO> result = new ZhongHengApiResult<HengTongATMCardInfoSyncVO>();
        try {
            result = zhongHengApiClient.hengTongATMCardInfoSync(dto);
            log.info("HengTongServiceImpl.hengTongATMCardInfoSync result:{}", JSONUtil.toJsonStr(result));
            if (!ZhongHengApiResult.isSuccess(result)) {
               throw new BusinessException("银行卡信息同步失败:"+ result.getMsg());
            }
            if (ObjUtil.isNull(result.getResponseData())  || ObjUtil.isNull(result.getResponseData().getResult()) || StrUtil.equals(result.getResponseData().getResult(),"2")){
                throw new BusinessException("银行卡信息同步失败:"+ result.getMsg());
            }

        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return  result;
    }

    @Override
    public Boolean getDingDrawMoney() {
        List<SprEductionUsageEntity> entityList = sprEductionUsageEntityService.list(
                new LambdaQueryWrapper<SprEductionUsageEntity>()
                        .eq(SprEductionUsageEntity::getDeleteFlag, 0)
        );
        List<Integer> idList = entityList.stream().map(SprEductionUsageEntity::getInstanceId).toList();
        String approvalName3 = "减免申请";
        List<SprEductionUsageEntity> list = new ArrayList<>();
        Map<Integer, Map<String, String>> integerMapMap3 = dingDrawMoneyFeign.updateDingDrawInfo(approvalName3);
        log.info("FundDeductServiceImpl.getDingDrawMoney integerMapMap3 {}",JSONUtil.toJsonStr(integerMapMap3));

        integerMapMap3.forEach((k, v) -> {
            if (!idList.contains(k)){
                if (v.containsKey("DDSelectField_1JE5XBGR9GZK0") && !v.get("DDSelectField_1JE5XBGR9GZK0").contains("特殊结清")){
                    try {
                        SprEductionUsageEntity sprEductionUsageEntity=new SprEductionUsageEntity();
                        sprEductionUsageEntity.setInstanceId(k);
                        sprEductionUsageEntity.setVehicleNumber(v.getOrDefault("TextField-JMBEQWRP", ""));
                        sprEductionUsageEntity.setReductionAmount(v.containsKey("TextField-K0XJ0G3V") ? BigDecimal.valueOf(Double.parseDouble(v.get("TextField-K0XJ0G3V").replaceAll("[^\\d.-]", ""))) : BigDecimal.ZERO);
                        if (v.get("DDSelectField_1JE5XBGR9GZK0").contains("结清减免")){
                            sprEductionUsageEntity.setApprovalTemplate(4);
                        }else if (v.get("DDSelectField_1JE5XBGR9GZK0").contains("月供清逾")){
                            sprEductionUsageEntity.setApprovalTemplate(5);
                        }
                        sprEductionUsageEntity.setApprovalDetails(JSONUtil.toJsonStr(v));
                        sprEductionUsageEntity.setUsageStatus(2);
                        list.add(sprEductionUsageEntity);
                    } catch (NumberFormatException e) {
                        log.info("HengTongServiceImpl.getDingDrawMoney value:{} error:{}",v,e.getMessage());
                    }
                }
            }
        });
        log.info("FundDeductServiceImpl.getDingDrawMoney saveList {}",JSONUtil.toJsonStr(list));
        return sprEductionUsageEntityService.saveBatch(list);
    }
    /**
     * 定时同步 先抵押后放款 的  数据
     */
    @Override
    public Boolean autoPatchesByPaymentType() {
        MPJLambdaWrapper<OrderInfoEntity> wrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .distinct()
                .selectAll(OrderInfoEntity.class)
                .innerJoin(OrderFileEntity.class, on -> on
                        .eq(OrderFileEntity::getOrderId, OrderInfoEntity::getId)
                        .eq(OrderFileEntity::getFileId, 68)
                        .eq(OrderFileEntity::getDeleteFlag, 0)
                        .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                        .last("limit 1")
                )
                .leftJoin(AfterLoanPatchesEntity.class, on -> on // 添加这一部分
                        .eq(OrderInfoEntity::getId, AfterLoanPatchesEntity::getOrderId)
                        .eq(AfterLoanPatchesEntity::getDeleteFlag, 1)
                        .eq(AfterLoanPatchesEntity::getThPatches, 1)
                )
                .leftJoin(CustomerMortgageInfoEntity.class, on -> on //
                        .eq(CustomerMortgageInfoEntity::getOrderId, OrderInfoEntity::getId)
                        .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                        .last("limit 1")
                )
                .isNull(AfterLoanPatchesEntity::getOrderId) // 确保AfterLoanPatchesEntity中没有匹配记录
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .eq(OrderInfoEntity::getFundId,15)
                .ge(OrderInfoEntity::getState, 5000)
                .eq(OrderInfoEntity::getPaymentType,1)
                .eq(CustomerMortgageInfoEntity::getMortgageType, 0);
        List<OrderInfoEntity> orderInfoEntities = orderInfoMapper.selectJoinList(OrderInfoEntity.class, wrapper);
        log.info("HengTongServiceImpl.autoPatchesByPaymentType orderInfoEntities:{}", JSONUtil.toJsonStr(orderInfoEntities));
        orderInfoEntities.stream().forEach(orderInfo -> {
            boolean flag = false;
            try {
                hengTongJiaYaUpdate(orderInfo.getId());
                flag =true;
            } catch (Exception e) {
                log.error("HengTongServiceImpl.autoPatchesByPaymentType error:{}", e.getMessage());
            } finally {
                if(flag){
                    AfterLoanPatchesEntity afterLoanPatchesEntity = new AfterLoanPatchesEntity();
                    afterLoanPatchesEntity.setOrderId(orderInfo.getId());
                    afterLoanPatchesEntity.setAfterLoanStatus(20000);
                    afterLoanPatchesEntity.setDeleteFlag(1);
                    afterLoanPatchesEntity.setThPatches(1);
                    afterLoanPatchesMapper.insert(afterLoanPatchesEntity);
                }
            }
        });
        return true;
    }

	/**
	 * 中恒-流程终止申请
	 */
	@Override
	public ZhongHengApiResult<ProcessEndVO> processEndNotice(ProcessEndDTO dto) {
		log.info("HengTongServiceImpl.processEndNotice dto:{}", JSONUtil.toJsonStr(dto));
		ZhongHengApiResult<ProcessEndVO> result = new ZhongHengApiResult<ProcessEndVO>();
		try {
			result = zhongHengApiClient.processEndNotice(dto);
			log.info("HengTongServiceImpl.processEndNotice result:{}", JSONUtil.toJsonStr(result));
			if (!ZhongHengApiResult.isSuccess(result)) {
				throw new BusinessException("流程终止申请失败:"+ result.getMsg());
			}
			if (ObjUtil.isNull(result.getResponseData())){
				throw new BusinessException("流程终止申请失败:"+ result.getMsg());
			}
		} catch (Exception e) {
			throw new BusinessException(e.getMessage());
		}
		return  result;
	}

    @Override
    public Boolean autoPatchesByPaymentTypeByOrderId(Integer orderId) {
        log.info("HengTongServiceImpl.autoPatchesByPaymentTypeByOrderId orderId:{}", orderId);
        boolean flag = false;
        try {
            hengTongJiaYaUpdate(orderId);
            flag =true;
        } catch (Exception e) {
            log.error("HengTongServiceImpl.autoPatchesByPaymentType error:{}", e.getMessage());
        } finally {
            if (flag) {
                AfterLoanPatchesEntity afterLoanPatchesEntity = new AfterLoanPatchesEntity();
                afterLoanPatchesEntity.setOrderId(orderId);
                afterLoanPatchesEntity.setAfterLoanStatus(20000);
                afterLoanPatchesEntity.setDeleteFlag(1);
                afterLoanPatchesEntity.setThPatches(1);
                afterLoanPatchesMapper.insert(afterLoanPatchesEntity);
            }
        }
        return true;
    }

}

