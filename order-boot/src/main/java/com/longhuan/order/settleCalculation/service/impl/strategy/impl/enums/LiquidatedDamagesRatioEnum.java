package com.longhuan.order.settleCalculation.service.impl.strategy.impl.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.math.BigDecimal;

@Getter
public enum LiquidatedDamagesRatioEnum {
    PENALTY_0_PERCENT(new BigDecimal("0"),  "0%"),
    PENALTY_1_PERCENT(new BigDecimal("0.01"),  "1%"),
    PENALTY_2_PERCENT(new BigDecimal("0.02"),  "2%"),
    PENALTY_4_PERCENT(new BigDecimal("0.04"),  "4%"),
    PENALTY_6_PERCENT(new BigDecimal("0.06"),  "6%"),
    PENALTY_8_PERCENT(new BigDecimal("0.08"),  "8%"),
    ;
    @EnumValue
    @JsonValue
    private final BigDecimal value;
    private final String desc;

    LiquidatedDamagesRatioEnum(BigDecimal value,  String desc) {
        this.value = value;
        this.desc = desc;
    }
}
