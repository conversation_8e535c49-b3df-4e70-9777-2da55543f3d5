package com.longhuan.approve.boot.fund.keshang;

import com.longhuan.approve.boot.pojo.dto.FundFinalBaseDTO;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.request.KeShangRepaymentDetailRequest;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.response.KeShangRepaymentPlanResponse;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.response.KeShangRepaymentResultResponse;

/**
 * KeShangRepaymentService
 *
 * @date 2025/9/2 10:11
 */
public interface KeShangRepaymentService {
    /**
     * 查询还款结果
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangRepaymentResultResponse queryRepaymentResult(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 还款计划查询接口
     * @param orderId
     * @return
     */
    Boolean repaymenPlanQuery(Integer orderId);

    /**
     * 还款计划信息
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangRepaymentPlanResponse.RepaymentPlanItem repaymentPlanInfo(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 还款流水信息
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangRepaymentDetailRequest repaymentDetailInfo(FundFinalBaseDTO fundFinalBaseDTO);
}
