package com.longhuan.resource.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jcraft.jsch.ChannelSftp;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.order.pojo.vo.FileTOFddVO;
import com.longhuan.resource.config.LanHaiConfig;
import com.longhuan.resource.converter.FileResourceConverter;
import com.longhuan.resource.feign.OrderFeign;
import com.longhuan.resource.mapper.FileResourceMapper;
import com.longhuan.resource.mapper.OrderFileMapper;
import com.longhuan.resource.pojo.dto.*;
import com.longhuan.resource.pojo.entity.*;
import com.longhuan.resource.pojo.vo.*;
import com.longhuan.resource.service.*;
import com.longhuan.resource.utils.FileUploadUtils;
import com.longhuan.resource.utils.GenericFileParser;
import com.longhuan.resource.utils.SftpUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

@Service
@Slf4j
@RequiredArgsConstructor
public class FileResourceServiceImpl extends ServiceImpl<FileResourceMapper, FileResourceEntity> implements FileResourceService {

    private static final String TO_BOB = "ToBob";
    private static final String SUPPLEMENT = "supplement";
    private final OrderFileMapper orderFileMapper;
    private final FileResourceMapper fileResourceMapper;
    private final OrderFeign orderFeign;
    private final FileResourceConverter fileResourceConverter;
    private final FileMenuService fileMenuService;
    private final FileAssoService fileAssoService;
    private final FileConfigService fileConfigService;
    private final OrderFileService orderFileService;
    private final ThumbService thumbService;
    private final EnvUtil envUtil;
    private final AccessService accessService;
    private final ImageToPdfService imageToPdfService;
    private final LanHaiConfig lanHaiConfig;
    @Value("${file.filePath}")
    private String baseFilePath;
  /*  @Value("${aliyun.live.accessKeyId}")
    private String accessKeyId;
    @Value("${aliyun.live.accessKeySecret}")
    private String accessKeySecret;
    @Value("${aliyun.live.oSSEndpoint}")
    private String endpoint;
    @Value("${aliyun.live.oSSBucket}")
    private String oSSBucket;*/

    /**
     * 文件到多部分文件
     *
     * @param file 文件
     * @return {@link MultipartFile }
     */
    public static MultipartFile fileToMultipartFile(File file) {
        log.info("ContractServiceImpl.fileToMultipartFile begin file:{}", file);
        try {
            if (!(file != null && file.exists())) {
                log.info("ContractServiceImpl.fileToMultipartFile file:{}", JSONUtil.toJsonStr(file));
            }
            // 获取文件输入流
            FileInputStream fileInputStream = new FileInputStream(file);
            return getMockMultipartFile(file.getName(), fileInputStream);
        } catch (Exception e) {
            log.error("ContractServiceImpl.fileToMultipartFile e:", e);
            throw new BusinessException("文件转MultipartFile失败");
        }
    }

    /**
     * 获取 Mock Multipart 文件
     *
     * @param filename        文件名
     * @param fileInputStream 文件输入流
     * @return {@link MockMultipartFile }
     * @throws IOException io异常
     */
    private static @NotNull MockMultipartFile getMockMultipartFile(String filename, InputStream fileInputStream) throws IOException {
        // 将文件输入流转换为字节数组
        byte[] fileContent = IOUtils.toByteArray(fileInputStream);
        log.info("ContractServiceImpl.fileToMultipartFile fileContent:{}", fileContent.length);
        // 创建 MultipartFile
        MockMultipartFile mockMultipartFile = new MockMultipartFile(
                // 表单字段名称
                "file",
                // 文件名称
                filename,
                // MIME类型
                "application/pdf",
                // 文件内容
                fileContent);
        // 关闭输入流
        fileInputStream.close();
        return mockMultipartFile;
    }

    public static List<LanHaiLprVo> parseLanHaiLprVoTxt(String filePath) throws FileNotFoundException, IOException {
        List<LanHaiLprVo> dataList = new ArrayList<>();
        File file = new File(filePath);

        if (!file.exists()) {
            throw new FileNotFoundException("文件不存在: " + filePath);
        }

        try (FileInputStream fis = new FileInputStream(file);
             BufferedReader reader = new BufferedReader(new InputStreamReader(fis, StandardCharsets.UTF_8))) {

            String line;
            while ((line = reader.readLine()) != null) {
                // 使用 SOH 控制字符 \u0001 分割字段
                String[] fields = line.trim().split("\u0001");
                // 创建 VO 并填充数据
                LanHaiLprVo vo = new LanHaiLprVo();
                vo.setInterestRateId(fields[0]);             // 3位
                vo.setInterestRateType(fields[1]);           // 3位
                vo.setCurrency(fields[2]);                   // 2位
                vo.setEffectiveDate(fields[3]);              // 8位
                vo.setTermUnit(fields[4]);                   // 1位
                vo.setTermCount(new BigDecimal(fields[5]));  // 5位整数
                vo.setBaseRate(new BigDecimal(fields[6]));   // 12位，保留8位小数
                vo.setStatus(fields[7]);                     // 1位
                vo.setRateCategory(fields[8]);               // 25位（未使用）
                vo.setEntryUserId(fields[9]);                // 20位（未使用）
                dataList.add(vo);
            }
        }

        return dataList;
    }

    /**
     * 交易文件
     *
     * @param files 文件
     * @return {@link List }<{@link FileVO }>
     */
    public List<FileVO> handlerResourceFile(List<MultipartFile> files) {
        log.info("FileResourceServiceImpl.handlerResourceFile files:{}", files);
        return handlerResourceFile(files, null);
    }

    /**
     * handler 资源文件
     *
     * @param files  文件
     * @param belong 属于
     * @return {@link List }<{@link FileVO }>
     */
    @Override
    @Transactional
    public List<FileVO> handlerResourceFile(List<MultipartFile> files, Integer belong) {
        log.info("FileResourceServiceImpl.handlerResourceFile files:{} belong:{}", JSONUtil.toJsonStr(files), belong);

        List<FileVO> fileVOList = new ArrayList<>();
        if (CollUtil.isEmpty(files)) {
            throw new BusinessException("文件上传内容不能为空");
        }

        List<String> okFileSuffixList = Lists.newArrayList("ok", "OK", "Ok");
        //批量文件
        for (MultipartFile file : files) {
            log.info("FileResourceServiceImpl.handlerResourceFile file:{}", JSONUtil.toJsonStr(file));
            String contentType = file.getContentType();
            String oldFileName = file.getOriginalFilename();

            log.info("FileResourceServiceImpl.handlerResourceFile oldName:{} type:{}", oldFileName, contentType);
            String suffix = FileUtil.extName(oldFileName);

            if (StrUtil.isBlank(suffix)) {
                throw new BusinessException("文件格式不正确");
            }

            //增加逻辑：文件为空且非ok类的文件,才跳过,ok文件用以判断文件上传或下载逻辑是否成功
            if (file.isEmpty() && !okFileSuffixList.contains(suffix)) {
                // 跳过空文件
                log.info("FileResourceServiceImpl.handlerResourceFile file is null");
                continue;
            }
            try {
                byte[] bytes = file.getBytes();

                // 创建缩略图
                if (thumbService.isImages(suffix)) {
                    log.info("FileResourceServiceImpl.handlerResourceFile createThumbnail suffix:{}", suffix);

                    String realImageType = thumbService.checkRealImageType(bytes, contentType);

                    if (StrUtil.isNotBlank(realImageType) && ((!realImageType.equals(suffix) || !"jpg".equalsIgnoreCase(suffix)))) {
                        // 转换图片成jpg
                        suffix = ImgUtil.IMAGE_TYPE_JPG;
                        bytes = thumbService.convertImageType(bytes, ImgUtil.IMAGE_TYPE_JPG);
                    }

                    bytes = thumbService.createThumbnail(bytes);
                }

                //根据日期创建文件夹
                String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern(DatePattern.SIMPLE_MONTH_PATTERN));
                String absDatePath = baseFilePath + datePath;

                FileUtil.mkdir(absDatePath);
                // 随机文件名
                String randomFileName = IdUtil.randomUUID().replaceAll("-", "") + "." + suffix;
                // 绝对文件路径
                String absFilePath = absDatePath + "/" + randomFileName;
                // 相对文件路径
                String relativeFilePath = File.separator + datePath + File.separator + randomFileName;


                Path paths = Paths.get(absFilePath);

                //写文件
                Files.write(paths, bytes);

                //记录文件信息
                FileResourceEntity fileResourceEntity = new FileResourceEntity()
                        .setFileName(randomFileName).setFileOldName(oldFileName)
                        .setPrePath(baseFilePath)
                        .setFilePath(relativeFilePath)
                        .setFileType(suffix)
                        .setFileUid(IdUtil.simpleUUID());
                fileResourceMapper.insert(fileResourceEntity);
                log.info("FileResourceServiceImpl.handlerResourceFile fileResourceEntity:{}", JSONUtil.toJsonStr(fileResourceEntity));
                //数据处理
                fileVOList.add(new FileVO().setResourceId(fileResourceEntity.getFileUid())
                        .setResourceName(fileResourceEntity.getFileName()));


            } catch (Exception e) {
                log.error("FileResourceServiceImpl.handlerResourceFile e:", e);
                throw new BusinessException("文件上传失败");
            }


        }
        //获取法大大服务器路径
        if (belong != null) {
            List<String> resourceIds = fileVOList.stream().map(FileVO::getResourceId).toList();
            log.info("FileResourceServiceImpl.handlerResourceFile resourceIds:{}", JSONUtil.toJsonStr(resourceIds));
            batchFileTOFdd(resourceIds, belong);
        }
        log.info("FileResourceServiceImpl.handlerResourceFile fileVOList:{}", fileVOList);
        return fileVOList;
    }

    @Override
    public String batchDownloadFiles(List<String> resourceIds, HttpServletResponse response) {
        if (CollUtil.isEmpty(resourceIds)) {
            throw new BusinessException("下载文件传参不能为空");
        }
        List<FileResourceEntity> fileResourceEntityList = fileResourceMapper.selectList(new LambdaQueryWrapper<FileResourceEntity>()
                .in(FileResourceEntity::getFileUid, resourceIds));

        if (CollUtil.isEmpty(fileResourceEntityList)) {
            throw new BusinessException("资源不存在");
        }
        response.reset();
        response.setCharacterEncoding("utf-8");
        //设置压缩包的名字，date为时间戳
        String downloadName = "lh_down_" + DateUtil.now() + ".zip";

        //设置压缩包名称
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment;fileName=\"" + downloadName + "\"");
        //设置压缩流：直接写入response，实现边压缩边下载
        ZipOutputStream zipOs = null;

        try {
            zipOs = new ZipOutputStream(new BufferedOutputStream(response.getOutputStream()));
            //设置压缩方法
            zipOs.setMethod(ZipOutputStream.DEFLATED);
            List<File> files = new ArrayList<>();
            for (FileResourceEntity item : fileResourceEntityList) {
                //               本地测试
                //               files.add(Paths.get(FILE_PATH + baseFilePath + item.getFilePath()).toFile());
                files.add(Paths.get(baseFilePath + item.getFilePath()).toFile());
            }
            try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                for (File file : files) {
                    // 创建ZIP条目
                    ZipEntry zipEntry = new ZipEntry(file.getName());
                    zipOut.putNextEntry(zipEntry);

                    // 将文件内容写入ZIP输出流
                    try (FileInputStream fis = new FileInputStream(file);
                         BufferedInputStream bis = new BufferedInputStream(fis)) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = bis.read(buffer)) > 0) {
                            zipOut.write(buffer, 0, length);
                        }
                    }
                    zipOut.closeEntry();
                }
            }

        } catch (IOException e) {
            throw new BusinessException(e);
        }


        return Result.success().getMsg();
    }

    /**
     * 下载文件
     *
     * @param resourceId 资源id
     * @param response   响应
     * @return {@link String }
     */
    @Override
    public String downloadFile(String resourceId, HttpServletResponse response) {
        if (StrUtil.isEmpty(resourceId)) {
            throw new BusinessException("下载文件传参不能为空");
        }
        FileDTO fileInfo = getFileInfo(resourceId);
        String fileName = fileInfo.getFileName();
        InputStream inputStream = fileInfo.getInputStream();
        try {
            response.setHeader("Cache-Control", "max-age=1800");
            response.setHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

            // 根据文件扩展名设置Content-Type
            MediaType contentType = null;
            if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
                contentType = MediaType.IMAGE_JPEG;
            } else if (fileName.endsWith(".png")) {
                contentType = MediaType.IMAGE_PNG;
            } else if (fileName.endsWith(".pdf")) {
                contentType = MediaType.APPLICATION_PDF;
            } else {
                Optional<MediaType> mediaTypeOptional = MediaTypeFactory.getMediaType(fileName);
                contentType = mediaTypeOptional.orElse(MediaType.APPLICATION_OCTET_STREAM);
            }

            response.setContentType(contentType.toString());

            IoUtil.copy(inputStream, response.getOutputStream());

        } catch (IOException e) {
            log.error("文件下载失败", e);
            throw new BusinessException("文件下载失败", e);
        } finally {
            try {
                // inputStream
                inputStream.close();
            } catch (IOException e) {
                log.error("关闭输出流时发生异常", e);
            }
        }
        return Result.success().getMsg();
    }

    /**
     * 获取文件信息
     *
     * @param resourceId 资源id
     * @return {@link InputStream }
     */
    public FileDTO getFileInfo(String resourceId) {
        log.info("getFileInfo {}", resourceId);
        List<FileResourceEntity> fileResourceEntityList = fileResourceMapper.selectList(
                new LambdaQueryWrapper<FileResourceEntity>()
                        .eq(FileResourceEntity::getFileUid, resourceId)
        );

        if (fileResourceEntityList.isEmpty()) {
            throw new BusinessException("未找到相关文件资源");
        }

        // 假设我们只处理一个文件的情况
        FileResourceEntity fileResourceEntity = fileResourceEntityList.get(0);
//        if (envUtil.isDev()) {
//            baseFilePath = "D:\\lh\\files";
//        }
        String filePath = fileResourceEntity.getFilePath();
        log.info("filePath={}", filePath);

        String fileName = fileResourceEntity.getFileName();

        FileInputStream fileInputStream = null;

        String fullPath = baseFilePath + filePath;
        try {
            // 获取文件扩展名
            if (thumbService.isImages(FileUtil.extName(fileName))) {
                fileInputStream = thumbService.getThumbFileInputStream(filePath, FileUploadUtils.MAX_THUMBNAIL);
            } else {
                fileInputStream = new FileInputStream(fullPath);
            }
        } catch (Exception e) {
            log.error("文件不存在", e);
            throw new BusinessException("文件不存在");
        }
        return new FileDTO().setFileName(fileName).setFilePath(fullPath)
                .setInputStream(fileInputStream);
    }

    @Override
    public ResponseEntity<byte[]> previewImg(String resourceId) {
        return previewImg(resourceId, "car");
    }

    @Override
    public ResponseEntity<byte[]> previewImg(String resourceId, String thumbnail) {
        log.info("previewImg resourceId={}", resourceId);
        if (StrUtil.isEmpty(resourceId)) {
            throw new BusinessException("文件id不能为空");
        }
        FileResourceEntity fileResourceEntity = fileResourceMapper.selectOne(new LambdaQueryWrapper<FileResourceEntity>()
                .eq(FileResourceEntity::getFileUid, resourceId));

        byte[] imageBytes = null;
        try {
            if (Objects.isNull(fileResourceEntity)) {
                throw new BusinessException("文件不存在");
            }
            if (StrUtil.isNotEmpty(thumbnail) && thumbService.isImages(fileResourceEntity.getFileType())) {
                imageBytes = thumbService.getThumbnailImageBytes(fileResourceEntity.getFilePath(), FileUploadUtils.MAX_THUMBNAIL);
            } else {
                imageBytes = getImageBytes(fileResourceEntity.getFilePath());
            }
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + fileResourceEntity.getFileName() + "\"");
            headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            headers.add(HttpHeaders.PRAGMA, "no-cache");
            headers.add(HttpHeaders.EXPIRES, "0");

            Optional<MediaType> mediaTypeOptional = MediaTypeFactory.getMediaType(fileResourceEntity.getFileName());
            MediaType mediaType = mediaTypeOptional.orElse(MediaType.APPLICATION_OCTET_STREAM);
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(mediaType)
                    .body(imageBytes);

        } catch (IOException e) {
            throw new BusinessException(e);
        }
    }

    private List<String> batchFileTOFdd(List<String> resourceIds, Integer fileBelong) {
        List<FileTOFddVO> voList = fileToLocal(resourceIds, fileBelong);
        log.info("---------本地处理完成---------");
        if (CollUtil.isNotEmpty(voList)) {
            log.info("---------调用法大大---------");
            voList.forEach(vo -> {
                File file = new File(vo.getPdfPath());
                vo.setFileBytes(FileUtil.readBytes(file));
            });

            Result<Boolean> orderResult = orderFeign.uploadFileTOFdd(voList);
            log.info("orderResult={}", orderResult);

        }
        List<String> fileUids = voList.stream().map(FileTOFddVO::getFileUid).toList();

        return fileUids;
    }

    @Override
    public boolean processFile(List<String> resourceIds) {

        Result<Boolean> orderResult = orderFeign.processFile(resourceIds);
        return orderResult.getData();
    }

    @Override
    public List<DownZipFileVO> downLoadSignZip(SignZipDTO signZipDTO) {
        // 临时路径
        File tempFile = FileUtil.createTempFile("sign", ".zip", false);
        log.info("tempFile={}", tempFile.getAbsolutePath());

        // 下载zip文件到本地
        downloadZip(signZipDTO.getFddUrl(), tempFile);

        // 解压zip文件到指定目录
        List<FileDTO> filePathList = unzipFile(signZipDTO, tempFile.getAbsolutePath(), tempFile.getParent());

        //将文件信息保存
        List<DownZipFileVO> downZipFileVOS = saveFileResource(filePathList, signZipDTO);

        boolean delete = tempFile.delete();
        log.info("tempFile {} delete={}", tempFile, delete);

        return downZipFileVOS;
    }


    private void downloadZip(String fileURL, File tempFile) {
        log.info("download file url:{},tempFile:{}", fileURL, tempFile);
        try {
            URL url = new URL(fileURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            InputStream inputStream = connection.getInputStream();
            FileOutputStream outputStream = new FileOutputStream(tempFile);

            int read;
            byte[] buffer = new byte[1024];
            while ((read = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, read);
            }
            outputStream.close();
        } catch (IOException e) {
            throw new BusinessException(e);
        }

    }

    /**
     * 解压缩临时文件
     *
     * @param signZipDTO    签名 zip dto
     * @param zipFilePath   zip 临时文件路径
     * @param destDirectory dest 临时目录
     */
    private List<FileDTO> unzipFile(SignZipDTO signZipDTO, String zipFilePath, String destDirectory) {
        log.info("unzipFile zipFilePath={},destDirectory={}", zipFilePath, destDirectory);
        File destDir = new File(destDirectory);
        if (!destDir.exists()) {
            destDir.mkdir();
        }
        List<FileDTO> filePathList = new ArrayList<>();
        try (ZipInputStream zipIn = new ZipInputStream(new FileInputStream(zipFilePath))) {

            ZipEntry entry = zipIn.getNextEntry();
            while (entry != null) {
                FileDTO fileDTO = new FileDTO();
                String newFileName = entry.getName().split("/")[1];
                log.info("unzipFile newFileName = {}, ", newFileName);

                // 调整
                if (signZipDTO.getSignType() == 0) {
                    newFileName = IdUtil.simpleUUID().substring(10) + "_" + newFileName;
                }
                //99适用于富民融资担保服务合同
                else if (99 == signZipDTO.getSignType()
                        && StringUtils.isNotBlank(signZipDTO.getResourceId())) {
                    FileResourceEntity fileResourceEntity = fileResourceMapper.selectOne(new LambdaQueryWrapper<FileResourceEntity>()
                            .eq(FileResourceEntity::getFileUid, signZipDTO.getResourceId())
                            .eq(FileResourceEntity::getDeleteFlag, 0));
                    newFileName = fileResourceEntity.getFileOldName();
                }
                String filePath = destDirectory + File.separator + newFileName;
                fileDTO.setFilePath(filePath);
                fileDTO.setFileName(newFileName);
                filePathList.add(fileDTO);
                if (!entry.isDirectory()) {
                    extractFile(zipIn, filePath);
                } else {
                    File dir = new File(filePath);
                    dir.mkdir();
                }
                entry = zipIn.getNextEntry();
            }
            zipIn.closeEntry();


        } catch (IOException e) {
            throw new BusinessException(e);
        }
        log.info("unzipFile filePathList={}", filePathList);
        return filePathList;
    }

    private void extractFile(ZipInputStream zipIn, String filePath) {
        try {
            BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(filePath));
            int len;
            byte[] buffer = new byte[1024];
            while ((len = zipIn.read(buffer)) > 0) {
                bos.write(buffer, 0, len);
            }
            bos.close();
        } catch (IOException e) {
            throw new BusinessException(e);
        }

    }

    /**
     * 保存文件资源
     * <p>
     * 临时路径移动到永久路径
     *
     * @param tempFileList 文件路径列表
     * @param signZipDTO
     * @return {@link List }<{@link DownZipFileVO }>
     */
    private List<DownZipFileVO> saveFileResource(List<FileDTO> tempFileList, SignZipDTO signZipDTO) {
        log.info("saveFileResource tempFileList={}", tempFileList);
        List<FileResourceEntity> fileResourceEntityList = tempFileList.stream()
                .filter(Objects::nonNull)
                .map(tempFile -> {
                    String fileType = FileUtil.getSuffix(tempFile.getFilePath());

                    String fileUid = IdUtil.simpleUUID();
                    String fileName = fileUid + "." + fileType;
                    if (FileUtil.exist(tempFile.getFilePath())) {
                        fileUid = IdUtil.simpleUUID();
                        //99=富民融资担保服务合同,特殊处理
                        if (99 == signZipDTO.getSignType()) {
                            fileName = tempFile.getFileName();
                        } else {
                            fileName = fileUid + "." + fileType;
                        }
                    }

                    String monthDir = DateUtil.format(new Date(), "yyyyMM");

                    String filePath = monthDir + File.separator + fileName;

                    Path fullPath = Paths.get(baseFilePath, filePath);
                    log.info("saveFileResource tempFile={},fullPath={}", tempFile, fullPath);

                    //是否覆盖(99:富民融资担保服务合同,覆盖)
                    FileUtil.move(new File(tempFile.getFilePath()), fullPath.toFile(), 99 == signZipDTO.getSignType());

                    return new FileResourceEntity()
                            .setFileName(fileName)
                            .setFileOldName(FileUtil.getName(tempFile.getFilePath()))
                            .setFilePath(filePath)
                            .setFileType(fileType)
                            .setFileUid(fileUid)
                            .setPrePath(null);
                }).toList();

        saveBatch(fileResourceEntityList);

        return fileResourceConverter.entityListToZipVo(fileResourceEntityList);

    }

    private List<FileTOFddVO> fileToLocal(List<String> resourceIds, Integer fileBelong) {

        List<FileTOFddVO> fileTOFddVOS = new ArrayList<>();
        List<FileResourceEntity> fileList = fileResourceMapper.selectList(new LambdaQueryWrapper<FileResourceEntity>()
                .in(FileResourceEntity::getFileUid, resourceIds)
                .orderByAsc(FileResourceEntity::getId)
        );
        fileList.forEach(resource -> {
            String docFullPath = baseFilePath + resource.getFilePath();
            FileTOFddVO fileTOFddVO = new FileTOFddVO()
                    .setFileUid(resource.getFileUid())
                    .setPdfPath(docFullPath)
                    .setFileBelong(fileBelong);
            fileTOFddVOS.add(fileTOFddVO);
        });

        return fileTOFddVOS;
    }

    @Override
    public List<CarImageVO> getCarImage(Integer orderId, String code) {
        // 根据订单号查询车辆图片
        FileMenuEntity fileMenuEntity = fileMenuService.getFileMenuByCode(code);
        if (fileMenuEntity == null) {
            log.warn("fileMenuEntity {} is null", code);
            return List.of();
        }

        List<Integer> fileIds = fileAssoService.selectByMenuId(fileMenuEntity.getId())
                .stream().map(FileAssoEntity::getFileId).toList();

        if (fileIds.isEmpty()) {
            log.warn("file asso {} is null", code);
            return List.of();
        }

        List<FileConfigEntity> fileConfigEntities = fileConfigService.selectByFileIds(fileIds);

        Map<Integer, String> configCode = fileConfigEntities.stream().filter(entity -> entity.getCode() != null)
                .collect(Collectors.toMap(FileConfigEntity::getId, FileConfigEntity::getCode));

        return orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                .select(OrderFileEntity::getFileId, OrderFileEntity::getResourceId)
                .in(OrderFileEntity::getFileId, fileIds)
                .eq(OrderFileEntity::getOrderId, orderId).eq(OrderFileEntity::getDeleteFlag, 0)
        ).stream().map(file ->
        {
            CarImageVO carImageVO = new CarImageVO();
            carImageVO.setCode(configCode.get(file.getFileId()));
            carImageVO.setResourceId(file.getResourceId());
            carImageVO.setUrl(accessService.createTemporaryUrl(file.getResourceId(), 12));
            return carImageVO;
        }).toList();
    }

    @Override
    public List<FileResourceResultVO> getFileByResourceIds(List<String> resourceIds) {
        // 查询文件资源列表
        List<FileResourceEntity> fileResourceList = this.list(Wrappers.<FileResourceEntity>lambdaQuery()
                .in(FileResourceEntity::getFileUid, resourceIds));

        if (CollUtil.isEmpty(fileResourceList)) {
            return Collections.emptyList();
        }

        List<FileResourceResultVO> fileResourceResultVOS = fileResourceConverter.entity2VoList(fileResourceList);

        // 获取文件内容并赋值到VO中
        fileResourceResultVOS.forEach(fileResource -> {
            try {
                byte[] fileBytes = getFileBytes(fileResource.getFilePath());
                fileResource.setFileContent(fileBytes);
            } catch (IOException e) {
                log.error("Error reading file: " + fileResource.getFilePath(), e);
                throw new BusinessException("Failed to read file: " + fileResource.getFilePath(), e);
            }
        });

        return fileResourceResultVOS;
    }

    /**
     * 下载 Third Resource
     *
     * @param dto
     * @return {@link List }<{@link FileResourceResultVO }>
     */
    @Override
    public List<FileResourceResultVO> downLoadThirdResource(ThirdResourceDTO dto) {
        try {
            if (Objects.isNull(dto)) {
                return null;
            }
            log.info("FileResourceServiceImpl.downLoadThirdResource begin dto:{}", JSONUtil.toJsonStr(dto));
            List<String> urls = dto.getUrls();
            List<FileResourceEntity> fileResourceEntities = new ArrayList<>();
            urls.forEach(fileURL -> {
                log.info("downLoadThirdResource fileURL={}", fileURL);
                try {
                    String fileName = dto.getFileName();
                    List<FileVO> fileOutputStream = getFileOutputStream(fileURL, fileName);
                    FileResourceEntity fileResourceEntity = new FileResourceEntity();
                    fileResourceEntity.setFileUid(fileOutputStream.get(0).getResourceId());
                    fileResourceEntities.add(fileResourceEntity);
                    log.info("FileResourceServiceImpl.downLoadThirdResource fileOutputStream:{}", JSONUtil.toJsonStr(fileOutputStream));
                } catch (IOException e) {
                    throw new BusinessException(e);
                }
            });
            log.info("FileResourceServiceImpl.downLoadThirdResource begin fileResourceEntities:{}", JSONUtil.toJsonStr(fileResourceEntities));
            return fileResourceConverter.entity2VoList(fileResourceEntities);
        } catch (Exception e) {
            log.error("FileResourceServiceImpl.downLoadThirdResource e:", e);
            throw new BusinessException(e);
        }
    }

    /**
     * 上传 FADD
     *
     * @param resourceIds 资源 ID
     * @return boolean
     */
    @Override
    public boolean uploadFadd(List<String> resourceIds) {
        batchFileTOFdd(resourceIds, 999);
        return true;
    }

    /**
     * 解析竖排txt文件
     *
     * @param fileUid 文件uid
     * @return 一个二维字符串列表，其中每个内部列表表示文件中的一行数据，
     * 每个元素表示该行中的一个字段。
     * @throws FileNotFoundException 如果指定的文件不存在。
     * @throws IOException           如果读取文件时发生输入/输出错误。
     */
    @Override
    public List<List<String>> parseVerticalTxtByFileUid(String fileUid) throws FileNotFoundException, IOException {
        log.info("FileResourceServiceImpl.parseVerticalTxtByFileUid start: fileUid={}", fileUid);

        // 查询文件资源实体
        LambdaQueryWrapper<FileResourceEntity> resourceLqw = new LambdaQueryWrapper<>();
        resourceLqw.eq(FileResourceEntity::getFileUid, fileUid);
        FileResourceEntity fileResourceEntity = fileResourceMapper.selectOne(resourceLqw, false);

        if (fileResourceEntity == null) {
            log.warn("FileResourceServiceImpl.parseVerticalTxtByFileUid file not found: fileUid={}", fileUid);
            return new ArrayList<>();
        }

        String filePath = fileResourceEntity.getPrePath() + fileResourceEntity.getFilePath();

        // 解析垂直文本文件
        List<List<String>> data;
        try {
            data = GenericFileParser.parseVerticalTxt(filePath);
            log.info("FileResourceServiceImpl.parseVerticalTxtByFileUid parsed successfully: fileUid={}", fileUid);
        } catch (FileNotFoundException e) {
            log.error("FileResourceServiceImpl.parseVerticalTxtByFileUid file not found: filePath={}, error={}", filePath, e.getMessage(), e);
            throw e;
        } catch (IOException e) {
            log.error("FileResourceServiceImpl.parseVerticalTxtByFileUid IO error: filePath={}, error={}", filePath, e.getMessage(), e);
            throw e;
        }

        return data;
    }

    @Override
    public List<FileResourceBytesVO> queryFileBytes(List<String> resourceIds) {
        if (CollUtil.isEmpty(resourceIds)) {
            return Collections.emptyList();
        }
        // 查询文件资源列表
        List<FileResourceEntity> fileResourceList = this.list(Wrappers.<FileResourceEntity>lambdaQuery()
                .in(FileResourceEntity::getFileUid, resourceIds));

        if (CollUtil.isEmpty(fileResourceList)) {
            return Collections.emptyList();
        }

        List<FileResourceBytesVO> bytesVOS = fileResourceConverter.entity2BytesVoList(fileResourceList);

        // 获取文件内容并赋值到VO中
        bytesVOS.forEach(fileResource -> {
            try {
                byte[] fileBytes = getFileBytes(fileResource.getFilePath());
                fileResource.setFileContent(fileBytes);
            } catch (IOException e) {
                log.error("Error reading file: " + fileResource.getFilePath(), e);
                throw new BusinessException("Failed to read file: " + fileResource.getFilePath(), e);
            }
        });
        return bytesVOS;
    }

    @Override
    public FileResourceResultVO bytesWriteFile(WriteFileDTO writeFileDTO) {
        // 创建 MultipartFile
        MockMultipartFile mockMultipartFile = new MockMultipartFile(
                // 表单字段名称
                "file",
                // 文件名称
                writeFileDTO.getFileName(),
                // MIME类型
                "application/pdf",
                // 文件内容
                writeFileDTO.getFileContent());

        List<MultipartFile> multipartFiles = List.of(mockMultipartFile);
        log.info("ContractServiceImpl.bytesWriteFile:{}", multipartFiles);
        List<FileVO> fileVOList = handlerResourceFile(multipartFiles);
        log.info("ContractServiceImpl.bytesWriteFile fileVOList:{}", JSONUtil.toJsonStr(fileVOList));
        //默认一个
        FileVO fileVO = fileVOList.get(0);
        FileResourceResultVO resultVO = new FileResourceResultVO();
        resultVO.setFileUid(fileVO.getResourceId());
        resultVO.setFileName(fileVO.getResourceName());
        //        // 指定输出文件路径
        ////        String outputPath = "D:\\lh\\files\\202410\\signed.pdf";
        //        String fileName = writeFileDTO.getFileName();
        //        //根据日期创建文件夹
        //        SimpleDateFormat sdf = new SimpleDateFormat(DatePattern.SIMPLE_MONTH_PATTERN);
        //        String datePath = sdf.format(new Date());
        //        String outputPath = baseFilePath + datePath + File.separator + fileName;
        //
        //        // 将编码后的数据写入到本地文件
        //        try {
        //            Files.write(Paths.get(outputPath), writeFileDTO.getFileContent());
        //        } catch (IOException e) {
        //            throw new RuntimeException(e);
        //        }
        return resultVO;
    }

    @Override
    public ResponseEntity<byte[]> fileContent(String resourceId) {
        if (StrUtil.isEmpty(resourceId)) {
            throw new BusinessException("文件id不能为空");
        }
        FileResourceEntity fileResourceEntity = fileResourceMapper.selectOne(new LambdaQueryWrapper<FileResourceEntity>()
                .eq(FileResourceEntity::getFileUid, resourceId));

        if (Objects.isNull(fileResourceEntity)) {
            throw new BusinessException("文件不存在");
        }

        String fullPath = baseFilePath + fileResourceEntity.getFilePath();

        byte[] imageBytes = FileUtil.readBytes(fullPath);

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + fileResourceEntity.getFileName() + "\"");
        headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
        headers.add(HttpHeaders.PRAGMA, "no-cache");
        headers.add(HttpHeaders.EXPIRES, "0");

        MediaType mediaType = MediaTypeFactory.getMediaType(fileResourceEntity.getFileName()).orElse(MediaType.APPLICATION_OCTET_STREAM);
        return ResponseEntity.ok()
                .headers(headers)
                .contentType(mediaType)
                .body(imageBytes);
    }

    @Override
    public Result<FileInfoVO> fileInfo(String resourceId) {
        FileResourceEntity fileResourceEntity = fileResourceMapper.selectOne(new LambdaQueryWrapper<FileResourceEntity>()
                .eq(FileResourceEntity::getFileUid, resourceId));

        if (Objects.isNull(fileResourceEntity)) {
            throw new BusinessException("文件不存在");
        }

        return Result.success(fileResourceConverter.entity2Vo(fileResourceEntity));
    }

    @Override
    public FileVO uploadFileByUuid(String uuid, MultipartFile file) {
        Long l = fileResourceMapper.selectCount(new LambdaQueryWrapper<FileResourceEntity>().eq(FileResourceEntity::getFileUid, uuid));
        if (l > 0) {
            throw new BusinessException("文件已存在");
        }
        if (file.isEmpty()) {
            // 跳过空文件
            throw new BusinessException("文件不存在");
        }


        String contentType = file.getContentType();
        String oldFileName = file.getOriginalFilename();
        log.info("FileResourceServiceImpl.uploadFileByUuid oldName:{} type:{}", oldFileName, contentType);

        String suffix = FileUtil.extName(oldFileName);

        if (StrUtil.isBlank(suffix)) {
            throw new BusinessException("文件格式不正确");
        }

        try {
            byte[] bytes = file.getBytes();

            // 创建缩略图
            if (thumbService.isImages(suffix)) {
                log.info("FileResourceServiceImpl.uploadFileByUuid createThumbnail suffix:{}", suffix);

                String realImageType = thumbService.checkRealImageType(bytes, contentType);

                if (StrUtil.isNotBlank(realImageType) && !realImageType.equals(suffix)) {
                    // 转换图片成jpg
                    suffix = ImgUtil.IMAGE_TYPE_JPG;
                    bytes = thumbService.convertImageType(bytes, ImgUtil.IMAGE_TYPE_JPG);
                }

                bytes = thumbService.createThumbnail(bytes);
            }

            //根据日期创建文件夹
            String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern(DatePattern.SIMPLE_MONTH_PATTERN));
            String absDatePath = baseFilePath + datePath;

            FileUtil.mkdir(absDatePath);
            // 随机文件名
            String randomFileName = IdUtil.randomUUID().replaceAll("-", "") + "." + suffix;
            // 绝对文件路径
            String absFilePath = absDatePath + "/" + randomFileName;
            // 相对文件路径
            String relativeFilePath = File.separator + datePath + File.separator + randomFileName;


            Path paths = Paths.get(absFilePath);

            //写文件
            Files.write(paths, bytes);

            //记录文件信息
            FileResourceEntity fileResourceEntity = new FileResourceEntity()
                    .setFileName(randomFileName).setFileOldName(oldFileName)
                    .setPrePath(baseFilePath)
                    .setFilePath(relativeFilePath)
                    .setFileType(suffix)
                    .setFileUid(uuid);
            fileResourceMapper.insert(fileResourceEntity);
            log.info("FileResourceServiceImpl.uploadFileByUuid fileResourceEntity:{}", JSONUtil.toJsonStr(fileResourceEntity));
            //数据处理
            return new FileVO().setResourceId(fileResourceEntity.getFileUid())
                    .setResourceName(fileResourceEntity.getFileName());


        } catch (Exception e) {
            log.error("FileResourceServiceImpl.uploadFileByUuid e:", e);
            throw new BusinessException("文件上传失败");
        }
    }

    @Override
    public byte[] mergeImageToPdf(List<String> resourceIds) {
        if (CollUtil.isEmpty(resourceIds)) {
            return new byte[0];
        }
        // 图片转成PDF
        List<File> imageFileList = fileResourceMapper.selectList(new LambdaQueryWrapper<FileResourceEntity>()
                        .in(FileResourceEntity::getFileUid, resourceIds)).stream().map(FileResourceEntity::getFilePath)
                .filter(Objects::nonNull)
                .map(path -> Paths.get(baseFilePath + path).toFile())
                .filter(path -> thumbService.isImages(FileUtil.extName(path))).toList();
        if (CollUtil.isEmpty(imageFileList)) {
            throw new BusinessException("未找到图片文件");
        }
        try {
            log.info("imageToPdfService.convertImagesToPdf imageFileList:{}", imageFileList);
            File outputFile = Paths.get("/tmp/pdf", IdUtil.fastSimpleUUID() + ".pdf").toFile();
            log.info("imageToPdfService.convertImagesToPdf outputFile:{}", outputFile);
            imageToPdfService.convertImagesToPdf(imageFileList, outputFile);
            log.info("imageToPdfService.convertImagesToPdf upload outputFile:{}", outputFile);

            log.info("imageToPdfService.convertImagesToPdf delete outputFile:{}", outputFile);

            byte[] bytes = FileUtil.readBytes(outputFile);
            FileUtil.del(outputFile);
            return bytes;
        } catch (Exception e) {
            log.error("imageToPdfService.convertImagesToPdf error {}", e.getMessage(), e);
            throw new BusinessException("图片合并失败");
        }
    }

    /**
     * 获取文件输出流
     *
     * @param fileURL  文件 URL
     * @param fileName 文件名
     * @return {@link List }<{@link FileVO }>
     * @throws IOException io异常
     */
    private List<FileVO> getFileOutputStream(String fileURL, String fileName) throws IOException {
        try {
            URL url = new URL(fileURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            InputStream inputStream = connection.getInputStream();
            List<MultipartFile> multipartFiles = List.of(getMockMultipartFile(fileName, inputStream));
            log.info("ContractServiceImpl.uploadTempDirectory multipartFiles:{}", multipartFiles);
            return handlerResourceFile(multipartFiles);
        } catch (IOException e) {
            throw new BusinessException(e);
        }
    }

    private byte[] getImageBytes(String filename) throws IOException {
        //拼接文件路径
        String path = baseFilePath + filename;
        log.info("预览路径，path={}", path);
        // 实现从文件系统中读取图片文件并转换为字节数组的逻辑
        // 注意：这里需要处理文件不存在、权限问题等
        File file = new File(path);
        if (!file.exists()) {
            throw new BusinessException("图片不存在");
        } else {
            return Files.readAllBytes(Paths.get(path));

        }
    }

    private FileVO dealFileData(String finalFileName, String realPath) {

        FileResourceEntity fileResourceEntity = fileResourceMapper.selectOne(new LambdaQueryWrapper<FileResourceEntity>()
                .eq(FileResourceEntity::getFileName, finalFileName));

        return new FileVO().setResourceId(fileResourceEntity.getFileUid()).setResourceName(fileResourceEntity.getFileName());
    }

    private byte[] getFileBytes(String filename) throws IOException {
        //拼接文件路径
        String path = baseFilePath + filename;
        log.info("预览路径，path={}", path);
        // 实现从文件系统中读取图片文件并转换为字节数组的逻辑
        // 注意：这里需要处理文件不存在、权限问题等
        File file = new File(path);
        if (!file.exists()) {
            return null;
        } else {
            return Files.readAllBytes(Paths.get(path));

        }
    }

    @Override
    public String getBase64ByResourceId(String resourceId) {
        if (StrUtil.isEmpty(resourceId)) {
            throw new BusinessException("文件id不能为空");
        }

        FileResourceEntity fileResourceEntity = fileResourceMapper.selectOne(new LambdaQueryWrapper<FileResourceEntity>()
                .eq(FileResourceEntity::getFileUid, resourceId));

        if (ObjectUtil.isNull(fileResourceEntity)) {
            throw new BusinessException("文件不存在");
        }

        File file = FileUtil.file(baseFilePath + fileResourceEntity.getFilePath());

        if (!FileUtil.exist(file)) {
            throw new BusinessException("文件不存在: " + file);
        }
        String base64 = Base64.encode(file);

        return base64;
    }

    @Override
    public Map<String, List<List<String>>> parseCsv(Integer titleLine, List<String> fileUidList) {
        Map<String, List<List<String>>> resultMap = new HashMap<>();

        for (String fileUid : fileUidList) {
            try {
                // 假设通过 fileUid 获取文件路径的逻辑
                // 查询文件资源实体
                LambdaQueryWrapper<FileResourceEntity> resourceLqw = new LambdaQueryWrapper<>();
                resourceLqw.eq(FileResourceEntity::getFileUid, fileUid);
                FileResourceEntity fileResourceEntity = fileResourceMapper.selectOne(resourceLqw, false);

                if (fileResourceEntity == null) {
                    log.warn("FileResourceServiceImpl.parseCsv file not found: fileUid={}", fileUid);
                    throw new BusinessException("文件不存在");
                }

                String filePath = fileResourceEntity.getPrePath() + fileResourceEntity.getFilePath();

                CsvReader reader = CsvUtil.getReader();
                CsvData csvData = reader.read(FileUtil.getReader(filePath, CharsetUtil.CHARSET_UTF_8));

                List<CsvRow> rows = csvData.getRows();

                if (titleLine != null && titleLine > 0 && rows.size() >= titleLine) {
                    rows = rows.subList(titleLine, rows.size());
                }

                List<List<String>> csvRows = rows.stream()
                        .map(CsvRow::getRawList)
                        .toList();

                resultMap.put(fileUid, csvRows);
            } catch (Exception e) {
                log.error("解析CSV文件失败，fileUid: {}", fileUid, e);
                throw new BusinessException("解析CSV文件失败");
            }
        }
        return resultMap;
    }

    @Override
    public void downloadFile(String resourceId, HttpServletResponse response, String range, Integer thumbnail) {
        if (StrUtil.isEmpty(resourceId)) {
            throw new BusinessException("下载文件传参不能为空");
        }
        log.info("downloadFile resourceId:{} thumbnail:{}", resourceId, thumbnail);
        if (range == null || StrUtil.isEmpty(range) || !range.startsWith("bytes=")) {
            viewImage(resourceId, response, thumbnail);
            return;
        }
        // bytes=0-
        // bytes=0-1024,2048-4096,8192-
        FileDTO fileInfo = getFileInfo(resourceId);
        String fileName = fileInfo.getFileName();
        if (fileName.endsWith(".mp4") || fileName.endsWith(".mov")
                || fileName.endsWith(".MP4") || fileName.endsWith(".MOV")) {
            streamVideo(fileInfo.getFilePath(), response, range);
        } else {
            downloadFile(resourceId, response);
        }
    }


    @Transactional
    @Override
    public List<FileVO> uploadVerticalTxt(List<UploadVerticalTxtDTO> dtoList) {

        Assert.notEmpty(dtoList, () -> new BusinessException("参数错误。"));

        log.info("FileResourceServiceImpl.uploadVerticalTxt dtoList:{}", JSONUtil.toJsonStr(dtoList));

        LocalDate now = LocalDate.now();
        List<FileVO> fileVOList = Lists.newArrayList();

        dtoList.forEach(dto -> {
            //拼接字符串
            StringBuilder sb = parseVerticalTxt(dto.getDataList());
            FileVO fileVO = new FileVO();

            MockMultipartFile mockMultipartFile = new MockMultipartFile("files", dto.getFileName(), "text/plain", sb.toString().getBytes());

            String contentType = mockMultipartFile.getContentType();
            String oldFileName = mockMultipartFile.getOriginalFilename();
            log.info("FileResourceServiceImpl.uploadVerticalTxt oldName:{} type:{}", oldFileName, contentType);

            String suffix = FileUtil.extName(oldFileName);

            if (StringUtils.isBlank(suffix)) {
                throw new BusinessException("文件格式不正确");
            }

            try {
                byte[] bytes = mockMultipartFile.getBytes();

                //根据日期创建文件夹
                String datePath = now.format(DateTimeFormatter.ofPattern(DatePattern.SIMPLE_MONTH_PATTERN));
                String absDatePath = baseFilePath + datePath;

                FileUtil.mkdir(absDatePath);
                // 指定文件名
                String randomFileName = dto.getFileName();
                // 绝对文件路径
                String absFilePath = absDatePath + "/" + randomFileName;
                // 相对文件路径
                String relativeFilePath = datePath + "/" + randomFileName;
                Path paths = Paths.get(absFilePath);
                //写文件
                Files.write(paths, bytes);


                //记录文件信息
                FileResourceEntity fileResourceEntity = new FileResourceEntity()
                        .setFileName(randomFileName).setFileOldName(oldFileName)
                        .setPrePath(baseFilePath)
                        .setFilePath(relativeFilePath)
                        .setFileType(suffix)
                        .setFileUid(IdUtil.simpleUUID());
                fileResourceMapper.insert(fileResourceEntity);
                log.info("FileResourceServiceImpl.uploadVerticalTxt fileResourceEntity:{}", JSONUtil.toJsonStr(fileResourceEntity));
                fileVO.setResourceId(fileResourceEntity.getFileUid())
                        .setResourceName(fileResourceEntity.getFileName());
                fileVOList.add(fileVO);
            } catch (Exception e) {
                log.error("FileResourceServiceImpl.uploadVerticalTxt e:", e);
                throw new BusinessException("文件上传失败");
            }
        });

        log.info("FileResourceServiceImpl.uploadVerticalTxt fileVOList:{}", JSON.toJSONString(fileVOList));
        return fileVOList;
    }

    @Override
    public void uploadVerticalTxtToSftp(List<String> resourceIdList) {

        Assert.notEmpty(resourceIdList, () -> new BusinessException("参数错误。"));

        log.info("FileResourceServiceImpl.uploadVerticalTxtToSftp resourceIdList:{}", resourceIdList);

        //yyyyMMdd
        DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN);
        LocalDate now = LocalDate.now();

        //拼接sftp文件保存路径
        String destPath =
                lanHaiConfig.getPrefix()
                        + "/" + lanHaiConfig.getMerchantId()
                        + "/" + lanHaiConfig.getMerchantId() + TO_BOB
                        + "/" + dtf2.format(now)
                        + "/" + SUPPLEMENT;


        //查询文件信息
        List<FileResourceEntity> fileResourceEntityList = fileResourceMapper.selectList(
                new LambdaQueryWrapper<FileResourceEntity>()
                        .in(FileResourceEntity::getFileUid, resourceIdList)
                        .eq(FileResourceEntity::getDeleteFlag, 0)
                        .orderByDesc(FileResourceEntity::getCreateTime)
        );

        Assert.notEmpty(fileResourceEntityList, () -> new BusinessException("文件不存在。"));

        ChannelSftp channelSftp = null;
        try {
            channelSftp = SftpUtil.ChannelSftpOld(lanHaiConfig.getSftpHost(),
                    lanHaiConfig.getSftpPort(),
                    lanHaiConfig.getSftpUser(),
                    lanHaiConfig.getSftpPwd());
            //创建文件目录
            SftpUtil.createDirectory(channelSftp, destPath);

            for (FileResourceEntity v : fileResourceEntityList) {
                String resourceFilePath = v.getFilePath();
                String fileName = v.getFileName();
                String fullFilePath = baseFilePath + resourceFilePath;

                //文件根路径
                String filePath = destPath + "/" + fileName;
                //连接sftp并上传
                SftpUtil.upload(channelSftp, fullFilePath, filePath);
            }
        } catch (Exception e) {
            log.error("FileResourceServiceImpl.uploadVerticalTxtToSftp,上传竖线分割文本至sftp,error:{}", e.getMessage(), e);
            throw new BusinessException("上传文件至sftp失败");
        } finally {
            if (channelSftp != null) {
                try {
                    SftpUtil.closeSession(channelSftp);
                } catch (Exception e) {
                    log.error("FileResourceServiceImpl.uploadVerticalTxtToSftp,关闭SFTP连接时发生错误,error:{}", e.getMessage(), e);
                }
            }
        }
    }

    /* @Override
     /**
      * 删除文件 (回收站模式)
      * <p>
      * 假删除文件
      * 1. 文件存储路径 eg：/data/file/yyyyMM/uuid.png
      * 2. 更新resource表 deleteFlag
      * 3. 回收目录  eg: /data/file/recycle/yyyyMM/uuid.png
      *
      * @param fileDelDTO File del dto
      * @param loginUser  登录用户
      * @return {@link String }
      */
    @Override
    public String deleteFile(FileDelDTO fileDelDTO, LoginUser loginUser) {
        log.info("FileResourceServiceImpl.deleteFile fileDelDTO:{} userId:{} ", JSON.toJSONString(fileDelDTO), loginUser.getUserId());

        FileResourceEntity fileResourceEntity = fileResourceMapper.selectOne(new LambdaQueryWrapper<FileResourceEntity>()
                .eq(FileResourceEntity::getFileUid, fileDelDTO.getResourceId()));

        Assert.notNull(fileResourceEntity, () -> new BusinessException("文件不存在"));

        Integer dataType = Convert.toInt(fileResourceEntity.getDataType(), 0);

        if (dataType == 1) {
            return "skip";
        }

        String filePath = fileResourceEntity.getFilePath();

        if (StringUtils.isEmpty(filePath)) {
            return "skip";
        }
        // 实际源地址
        Path realFilePath = Paths.get(baseFilePath, filePath);

        // 回收目录地址
        Path recycleFilePath = Paths.get(baseFilePath, "recycle", filePath);

        // 3 移动文件到回收目录
        Path resultPath = FileUtil.move(realFilePath, recycleFilePath, false);

        if (recycleFilePath.compareTo(resultPath) == 0) {
            log.info("FileResourceServiceImpl.deleteFile move srcPath:{} targetPath:{}", realFilePath, resultPath);
            fileResourceMapper.update(new LambdaUpdateWrapper<FileResourceEntity>()
                    .set(FileResourceEntity::getDeleteFlag, 1)
                    .set(FileResourceEntity::getUpdateBy, loginUser.getUserId())
                    .eq(FileResourceEntity::getFileUid, fileDelDTO.getResourceId()));

            return "success";
        } else {
            log.error("FileResourceServiceImpl.deleteFile move fail srcPath:{} targetPath:{}", realFilePath, resultPath);
            return "fail";
        }

    }

   /* @Override
    public List<FileVO> getAliyunUploadResource(LiveFileDTO liveFileDTO) {
        Integer fileId = liveFileDTO.getFileId();
        Integer orderId = liveFileDTO.getOrderId();
        String fileName = liveFileDTO.getFileName();
        log.info("Resource getAliyunUploadResource 开始处理阿里云文件 orderId :{}, filePathList:{} ", liveFileDTO.getOrderId(), liveFileDTO.getFilePathList());
        List<FileVO> fileVOList = new ArrayList<>();
        List<String> filePathList = Arrays.asList(liveFileDTO.getFilePathList().split(","));
        for (String filePath : filePathList) {
            MultipartFile mockMultipartFile = ossToMultipartFile(filePath);
            if (mockMultipartFile != null) {
                List<FileVO> resultList = handlerResourceFile(List.of(mockMultipartFile), null);
                if (ObjectUtil.isNotNull(resultList) && resultList.size() > 0) {
                    FileVO fileVO = resultList.get(0);
                    log.info("Resource getAliyunUploadResource filePathList 上传阿里云文件成功 filePath:{}, 个数:{}", filePath, resultList.size());
                    SaveOrderFileDTO orderFileDTO = new SaveOrderFileDTO();
                    orderFileDTO.setFileId(fileId);
                    orderFileDTO.setOrderId(orderId);
                    orderFileDTO.setFileName(fileName);
                    orderFileDTO.setResourceName(fileVO.getResourceName());
                    orderFileDTO.setResourceId(fileVO.getResourceId());
                    orderFeign.saveOrderFile(orderFileDTO);

                    fileVOList.add(resultList.get(0));
                }
            }
        }
        return fileVOList;
    }*/

    private void viewImage(String resourceId, HttpServletResponse response, Integer thumbnail) {

        if (StrUtil.isEmpty(resourceId)) {
            throw new BusinessException("下载文件传参不能为空");
        }

        List<FileResourceEntity> fileResourceEntityList = fileResourceMapper.selectList(
                new LambdaQueryWrapper<FileResourceEntity>()
                        .eq(FileResourceEntity::getFileUid, resourceId));

        if (fileResourceEntityList.isEmpty()) {
            throw new BusinessException("未找到相关文件资源");
        }

        // 假设我们只处理一个文件的情况
        FileResourceEntity fileResourceEntity = fileResourceEntityList.get(0);
//        if (envUtil.isDev()) {
//            baseFilePath = "D:\\lh\\files";
//        }
        String filePath = fileResourceEntity.getFilePath();
        log.info("filePath={}", filePath);

        String fileName = fileResourceEntity.getFileName();

        InputStream inputStream = null;

        String fullPath = baseFilePath + filePath;
        try {
            // 获取文件扩展名
            if (thumbService.isImages(FileUtil.extName(fileName))) {
                if (thumbnail != null && thumbnail > 0) {
                    thumbnail = thumbnail <= FileUploadUtils.MAX_THUMBNAIL ? thumbnail : FileUploadUtils.MAX_THUMBNAIL;
                    inputStream = thumbService.getThumbFileInputStream(filePath, thumbnail);
                } else {
                    inputStream = thumbService.getThumbFileInputStream(filePath, FileUploadUtils.MAX_THUMBNAIL);
                }

            }
            if (inputStream == null) {
                inputStream = new FileInputStream(fullPath);
            }
        } catch (Exception e) {
            log.error("文件不存在", e);
            throw new BusinessException("文件不存在");
        }


        try {
            response.setHeader("Cache-Control", "max-age=1800");
            response.setHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

            // 根据文件扩展名设置Content-Type
            MediaType contentType = null;
            if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
                contentType = MediaType.IMAGE_JPEG;
            } else if (fileName.endsWith(".png")) {
                contentType = MediaType.IMAGE_PNG;
            } else if (fileName.endsWith(".pdf")) {
                contentType = MediaType.APPLICATION_PDF;
            } else {
                Optional<MediaType> mediaTypeOptional = MediaTypeFactory.getMediaType(fileName);
                contentType = mediaTypeOptional.orElse(MediaType.APPLICATION_OCTET_STREAM);
            }

            response.setContentType(contentType.toString());

            IoUtil.copy(inputStream, response.getOutputStream());

        } catch (IOException e) {
            log.error("文件下载失败", e);
            throw new BusinessException("文件下载失败", e);
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                log.error("关闭输出流时发生异常", e);
            }
        }
        return;
    }

    private void streamVideo(String videoPath, HttpServletResponse response, String range) {
        long fileSize = FileUtil.size(new File(videoPath));
        try (RandomAccessFile file = new RandomAccessFile(videoPath, "r")) {

            // 解析 Range header
            String[] ranges = range.substring("bytes=".length()).split(",");
            long[] positions = parseRange(ranges[0], fileSize); // 只处理第一个 Range
            long start = positions[0];
            long end = positions[1];

            // 设置响应头
            long contentLength = end - start + 1;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader(HttpHeaders.CONTENT_LENGTH, "" + contentLength);
            response.setHeader(HttpHeaders.ACCEPT_RANGES, "bytes");
            response.setHeader(HttpHeaders.CONTENT_RANGE, String.format("bytes %d-%d/%d", start, end, fileSize));
            response.setStatus(HttpStatus.SC_PARTIAL_CONTENT);
            // 创建输入流并跳转到指定位置
            file.seek(start);
            InputStream inputStream = new BufferedInputStream(new FileInputStream(file.getFD())) {
                @Override
                public int read(byte[] b) throws IOException {
                    long segLength = Math.min(b.length, end - start + 1);
                    return super.read(b, 0, (int) segLength);
                }
            };


            IoUtil.copy(inputStream, response.getOutputStream());

        } catch (IOException e) {
            log.error("文件下载失败", e);
            throw new BusinessException("文件下载失败", e);
        }
    }

    // 解析 Range 字符串，如 "0-1023"
    private long[] parseRange(String rangeStr, long fileSize) {
        String[] parts = rangeStr.split("-");
        long start = Long.parseLong(parts[0]);
        long end = parts.length > 1 && !parts[1].isEmpty() ? Long.parseLong(parts[1]) : fileSize - 1;
        end = Math.min(end, fileSize - 1);
        return new long[]{start, end};
    }

    /**
     * @param objectName
     * @return
     */
    /*private MultipartFile ossToMultipartFile(String objectName) {
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            // 获取OSS对象输入流
            OSSObject ossObject = ossClient.getObject(new GetObjectRequest(oSSBucket, objectName));
            InputStream inputStream = ossObject.getObjectContent();
            // 创建MultipartFile对象
            MultipartFile multipartFile = getMultipartFile(inputStream, objectName);
            // 关闭输入流
            inputStream.close();
            // 关闭OSSObject对象
            ossObject.close();
            return multipartFile;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            // 关闭OSSClient
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    private MultipartFile getMultipartFile(InputStream inputStream, String objectName) throws Exception {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 将字节数组封装为MultipartFile
            byte[] fileContent = outputStream.toByteArray();
            return new MockMultipartFile("files", objectName, "video/mp4", new ByteArrayInputStream(fileContent));
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert OSS file to MultipartFile: " + e.getMessage(), e);
        }
    }*/


    /**
     * 拼接“|”字符串并换行
     *
     * @param dataList
     * @return
     */
    public StringBuilder parseVerticalTxt(List<List<String>> dataList) {

        StringBuilder sb = new StringBuilder();
        String SOH = "\u0001";


        //拼接字符串
        for (List<String> strings : dataList) {
            for (int j = 0; j < strings.size(); j++) {
                if (j != strings.size() - 1) {
                    sb.append(strings.get(j)).append(SOH);
                } else {
                    sb.append(strings.get(j)).append("\n");
                }
            }
        }
        return sb;

    }

    @Override
    public void batchUploadSftp(BatchUploadSftpDTO batchUploadSftpDTO) {

        Assert.notNull(batchUploadSftpDTO, () -> new BusinessException("参数错误"));

        log.info("FileResourceServiceImpl.uploadSftpV2 batchUploadSftpDTO:{}", JSON.toJSONString(batchUploadSftpDTO));

        List<String> resourceIdList = batchUploadSftpDTO.getSingleSftpFileDTOList().stream().map(SingleSftpFileDTO::getFileUid).toList();
        //查询文件信息
        List<FileResourceEntity> fileResourceEntityList = fileResourceMapper.selectList(
                new LambdaQueryWrapper<FileResourceEntity>()
                        .in(FileResourceEntity::getFileUid, resourceIdList)
                        .eq(FileResourceEntity::getDeleteFlag, 0));

        Assert.notEmpty(fileResourceEntityList, () -> new BusinessException("文件不存在"));

        SftpBaseDTO sftpBaseDTO = batchUploadSftpDTO.getSftpBaseDTO();
        ChannelSftp channelSftp = null;
        try {
            channelSftp = SftpUtil.ChannelSftpOld(sftpBaseDTO.getHost(),
                    sftpBaseDTO.getPort(),
                    sftpBaseDTO.getUserName(),
                    sftpBaseDTO.getPassword());

            SftpUtil.createDirectory(channelSftp, batchUploadSftpDTO.getDestPath());
            for (SingleSftpFileDTO v : batchUploadSftpDTO.getSingleSftpFileDTOList()) {
                String resourceFilePath = v.getFilePath();
                String fileName = v.getFileName();
                String fullFilePath = baseFilePath + resourceFilePath;

                //文件根路径
                String filePath = batchUploadSftpDTO.getDestPath() + "/" + fileName;
                //连接sftp并上传
                SftpUtil.upload(channelSftp, fullFilePath, filePath);
            }
        } catch (Exception e) {
            log.error("FileResourceServiceImpl.uploadSftpV2,error:{}", e.getMessage(), e);
            throw new BusinessException("上传文件至sftp失败");
        } finally {
            if (channelSftp != null) {
                try {
                    SftpUtil.closeSession(channelSftp);
                } catch (Exception e) {
                    log.error("FileResourceServiceImpl.uploadSftpV2,关闭SFTP连接时发生错误,error:{}", e.getMessage(), e);
                }
            }
        }
    }

    @Override
    public void batchDownloadByNameZip(BatchDownloadByNameZipDTO batchDownloadByNameZipDTO, HttpServletResponse response) {
        List<BatchDownloadByNameZipDTO.ZipFile> zipFileList = batchDownloadByNameZipDTO.getZipFileList();
        if (CollUtil.isEmpty(zipFileList)) {
            throw new BusinessException("下载配置不能为空");
        }
    
        // 设置响应头
        response.reset();
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/zip");

        String totalZipName = "lh_down_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")) + ".zip";
        String encodedFilename = URLEncoder.encode(totalZipName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + totalZipName + "\"; filename*=UTF-8''" + encodedFilename);

        try (ZipOutputStream zipOut = new ZipOutputStream(new BufferedOutputStream(response.getOutputStream()))) {
            for (BatchDownloadByNameZipDTO.ZipFile zipFile : zipFileList) {
                List<String> resourceIds = zipFile.getResourceIds();
                if (CollUtil.isEmpty(resourceIds)) {
                    throw new BusinessException("下载文件传参不能为空");
                }

                List<FileResourceEntity> fileResourceEntityList = fileResourceMapper.selectList(
                        new LambdaQueryWrapper<FileResourceEntity>().in(FileResourceEntity::getFileUid, resourceIds)
                );
                if (CollUtil.isEmpty(fileResourceEntityList)) {
                    throw new BusinessException("资源不存在");
                }

                ByteArrayOutputStream subZipStream = new ByteArrayOutputStream();
                try (ZipOutputStream subZipOut = new ZipOutputStream(subZipStream)) {
                    for (FileResourceEntity item : fileResourceEntityList) {
                        File file = Paths.get(baseFilePath + item.getFilePath()).toFile();
                        ZipEntry subEntry = new ZipEntry(file.getName());
                        subZipOut.putNextEntry(subEntry);

                        try (FileInputStream fis = new FileInputStream(file);
                             BufferedInputStream bis = new BufferedInputStream(fis)) {
                            byte[] buffer = new byte[1024];
                            int length;
                            while ((length = bis.read(buffer)) > 0) {
                                subZipOut.write(buffer, 0, length);
                            }
                        }
                        subZipOut.closeEntry();
                    }
                }

                ZipEntry subZipEntry = new ZipEntry(zipFile.getZipName() + ".zip");
                zipOut.putNextEntry(subZipEntry);
                zipOut.write(subZipStream.toByteArray());
                zipOut.closeEntry();
            }
        } catch (IOException e) {
            throw new BusinessException("ZIP打包失败", e);
        }
    }

}
