package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.approve.api.constants.CopperCarDeptMortgageEnums;
import com.longhuan.approve.api.pojo.dto.FundApproveCancelDTO;
import com.longhuan.approve.api.pojo.dto.FundApprovePreDTO;
import com.longhuan.approve.api.pojo.dto.MortgageStatusQueryDTO;
import com.longhuan.approve.api.pojo.dto.zhongheng.coppercardept.SearchOrderStatusDTO;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.GlobalConstants;
import com.longhuan.common.core.enums.PreApplyInfoFundStatus;
import com.longhuan.common.core.enums.PreFundResultEnum;
import com.longhuan.common.core.enums.RoleEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.core.result.ResultCode;
import com.longhuan.common.core.util.QrCodeUtils;
import com.longhuan.common.redis.constants.SwitchConstants;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.pojo.SwitchVO;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.converter.ApprovalApplyInfoConverter;
import com.longhuan.order.enums.*;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.feign.RiskFeign;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.kingdee.Kingdee;
import com.longhuan.order.kingdee.pojo.KingdeeOrderFlowDTO;
import com.longhuan.order.kingdee.service.KingdeeService;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.pojo.vo.MenuVO;
import com.longhuan.order.service.*;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.risk.enums.FundProductResult;
import com.longhuan.risk.pojo.vo.FundProductVO;
import com.longhuan.risk.pojo.vo.VehicleInfoVO;
import com.longhuan.user.api.DeptDetailVO;
import com.longhuan.user.pojo.vo.*;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/08/13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApprovalServiceImpl implements ApprovalService {

    private static final String LOCK_KEY_APPROVAL_SUBMIT = "order:approval:submit:";
    private static final int PAGE_SIZE = 1000;
    private final OrderStateService orderStateService;
    private final PreApprovalApplyInfoMapper preApprovalApplyInfoMapper;
    private final ApprovalApplyInfoConverter approvalApplyInfoConverter;
    private final OrderFileService orderFileService;
    private final RiskFeign riskFeign;
    private final OrderVehicleInfoMapper orderVehicleInfoMapper;
    private final LanBenVehicleDataMapper lanBenVehicleDataMapper;
    private final JzgVehicleDataMapper jzgVehicleDataMapper;
    private final OrderPageInfoService orderPageInfoService;
    private final DataPermissionService dataPermissionService;
    private final OrderInfoMapper orderInfoMapper;
    private final PreRiskPolicyResultMapper preRiskPolicyResultMapper;
    private final OrderNodeRecordMapper orderNodeRecordMapper;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final ApproveFeign approveFeign;
    private final PreFundInfoMapper preFundInfoMapper;
    private final SwitchUtils switchUtils;
    private final OrderAmountMapper orderAmountMapper;
    private final SwitchService switchService;
    private final DictService dictService;
    private final UserFeign userFeign;
    private final RedisService redisService;
    private final DeptQuotaRecordMapper deptQuotaRecordMapper;
    private final DeptQuotaMapper deptQuotaMapper;
    private final BankAccountSignMapper bankAccountSignMapper;
    private final OrderCompanyInfoMapper orderCompanyInfoMapper;
    private final OrderSendMessageImpl orderSendMessageImpl;
    private final OrderApproveDistributeService orderApproveDistributeService;
    private final KingdeeService kingdeeService;
    private final GpsService gpsService;
    private final OrderGpsInfoMapper orderGpsInfoMapper;
    private final GpsInstallInfoMapper gpsInstallInfoMapper;
    private final CustomerMortgageInfoMapper customerMortgageInfoMapper;
    private final SignTaskMapper signTaskMapper;
    private final CustomerAppointmentService customerAppointmentService;
    @Value("${wechat.message.customer-auth-url}")
    private String authPath;
    private final IntelligentRiskControlService intelligentRiskControlService;
    private final FileAssoMapper fileAssoMapper;
    private final FileConfigMapper fileConfigMapper;
    private final FileMenuMapper fileMenuMapper;
    private final OrderFileMenuService orderFileMenuService;
    private final IntelligentRiskInfoEntityMapper intelligentRiskInfoEntityMapper;
    private final RiskFundAutoConfigService riskFundAutoConfigService;
    private final RiskAiIntelligentAuditMapper riskAiIntelligentAuditMapper;
    private final Integer aiUserId = 99999;
    private final StoreQuotaService storeQuotaService;
    private final StoreQuotaMapper storeQuotaMapper;
    private final LoanReservoirRulesService loanReservoirRulesService;
    private final OrderLoanReservoirService orderLoanReservoirService;

    /**
     * 设置门店报表单元格样式
     *
     * @param sheet
     * @param workbook
     */
    public static void createExcel(Sheet sheet, Workbook workbook) {
        // 创建一个新的字体样式
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);

        // 创建一个新的单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setWrapText(true); // 启用自动换行
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        applyCellStyleToSheet(sheet, cellStyle);
    }

    private static void applyCellStyleToSheet(Sheet sheet, CellStyle cellStyle) {
        for (Row row : sheet) {
            for (Cell cell : row) {
                if (cell == null) {
                    cell = row.createCell(cell.getColumnIndex());
                }
                cell.setCellStyle(cellStyle);
            }
        }
    }

    @Override
    public Page<ApprovalHistoryVO> approvalHistory(OrderRecordDTO historyDTO, LoginUser currentUser) {

        return orderStateService.getOrderApprovalHistory(historyDTO, currentUser);

    }

    @Override
    public Page<ApprovalHistoryVO> approvalHistoryCombined(ApprovalHistoryQueryDTO queryDTO, LoginUser currentUser) {
        return orderStateService.getApprovalHistoryCombined(queryDTO, currentUser);
    }

    /**
     * 风控初审分配状态变更
     *
     * @param orderId 订单 ID
     * @param userId
     * @return {@link SubmitResultVO }
     */
    @Override
    public SubmitResultVO riskOrderAssign(int orderId, int userId) {

        return orderStateService.sendEvent(States.RISK_FIRST_APPROVE_ASSIGN, Events.AGREES, orderId, userId, new ApprovalSubmitDTO().setRemarkExternal("风控初审派单"));

    }

    /**
     * 审批提交
     *
     * @param submitDTO
     * @param currentUser
     * @return
     */
    @Override
    public SubmitResultVO approvalSubmit(ApprovalSubmitDTO submitDTO, LoginUser currentUser) {
        log.info("approvalSubmit{}", JSONUtil.toJsonStr(submitDTO));
        log.info("currentUser{}", JSONUtil.toJsonStr(currentUser));
        Integer orderId = submitDTO.getOrderId();
        String lockValue = UUID.randomUUID().toString();
        States node = submitDTO.getNode();
//        //首先判断车辆评估时间是否超过10天（判定节点只有请款资料节点）
//        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
//        //判断订单节点是否请款资料
//        if (ObjUtil.equals(orderInfoEntity.getCurrentNode(),States.PAYMENT_APPLY_INFORMATION.getNode())) {
//            verificationOfVehicleAssessmentTime(orderId);
//            verificationOfContractSigningTime(orderId);
//        }
        try {
            redisService.acquireLock(LOCK_KEY_APPROVAL_SUBMIT + orderId, lockValue, 60, 5000);

            States backNode = submitDTO.getBackNode();
            Integer result = submitDTO.getResult();
            Integer userId = currentUser.getUserId();
            if (StrUtil.isNotBlank(submitDTO.getStoreAppraiser())) {
                orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                        .set(OrderInfoEntity::getStoreAppraiser, submitDTO.getStoreAppraiser())
                        .eq(OrderInfoEntity::getId, orderId)
                        .eq(OrderInfoEntity::getDeleteFlag, 0));
            }
            Objects.requireNonNull(orderId, () -> {
                throw new BusinessException("订单不存在");
            });
            Objects.requireNonNull(result, () -> {
                throw new BusinessException("审批结论不存在");
            });
            Objects.requireNonNull(userId, () -> {
                throw new BusinessException("审批用户不存在");
            });
            // 2.总评复核：
            Events currentEvent = getEventsAndValidate(result, node, backNode, orderId, submitDTO.getStoreId(), submitDTO.getAppraiserId(), submitDTO.getAppraiserName());
            States currentNode = getNode(result, node, backNode, orderId);

            if (currentEvent == null) {
                throw new BusinessException("审批流程未配置");
            }

            if (currentEvent == Events.REJECT || currentEvent == Events.CANCEL) {
                Result<String> stringResult = approveFeign.fundApproveCancel(new FundApproveCancelDTO().setType(2).setLinkId(orderId));
                if (!Result.isSuccess(stringResult)) {
                    if (currentNode != States.FUNDS_FINAL_APPROVE) {
                        throw new BusinessException(stringResult.getMsg());
                    }
                }
                resetPreFundInfo(orderId);
            }
            if (currentEvent == Events.BACK && currentNode == States.PAYMENT_APPLY_INFORMATION) {
                resetOrderProcessStatus(orderId, submitDTO.getRedoNodeList());
            }else if (currentEvent == Events.AGREES && currentNode == States.PAYMENT_APPLY_INFORMATION){
                //电销校验交强险到期日
                verificationOfCompulsoryTrafficInsurance(orderId);
            }else if ((currentEvent == Events.AGREES || currentEvent == Events.OVERALL_REVIEW_2_FUNDS_FINAL_APPROVE)
                    && currentNode == States.OVERALL_REVIEW) {
                checkMinAmount(orderId);
            }
            if (currentNode==States.RISK_FIRST_APPROVE
                    ||currentNode==States.RISK_FINAL_APPROVE
                    ||currentNode==States.RISK_FIRST_APPROVE_ASSIGN){
                List<Integer> roleIds = currentUser.getRoleIds();
                Assert.isTrue(RoleEnum.RISK_AMOUNT_APPROVE.hasRole(roleIds)
                        ||RoleEnum.RISK_AMOUNT_APPROVE_FINAL.hasRole(roleIds)
                        ||RoleEnum.RISK_ROLE.hasRole(roleIds)
                        ||RoleEnum.RISK_ASSIGN_ORDER.hasRole(roleIds)
                        ||RoleEnum.BIZ_SUPER_ADMIN.hasRole(roleIds)
                        ||RoleEnum.SYS_ADMIN.hasRole(roleIds)
                        ||RoleEnum.RISK_AMOUNT_APPROVE_ONLINE.hasRole(roleIds),"当前用户不是风控初审人员");
                if (currentNode==States.RISK_FIRST_APPROVE){
                    Boolean onlineOrder = customerAppointmentService.isOnlineOrder(orderId);
                    OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
                    if (onlineOrder&&ObjUtil.equals(orderInfoEntity.getFundId(),FundEnum.CHANG_YIN.getValue())) {
                        OrderGpsInfoEntity orderGpsInfoEntity = orderGpsInfoMapper.selectOne(new LambdaQueryWrapper<OrderGpsInfoEntity>()
                                .eq(OrderGpsInfoEntity::getOrderId, orderId)
                                .in(OrderGpsInfoEntity::getGpsStatus, 0, 1, 2, 3)
                                .eq(OrderGpsInfoEntity::getReservationInstallStatus, 1)
                                .eq(OrderGpsInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(OrderGpsInfoEntity::getCreateTime)
                                .last("limit 1"));
                        if (ObjUtil.equals(submitDTO.getGpsType(), 1)) {
                            if (ObjUtil.isNotNull(orderGpsInfoEntity)) {
                                throw new BusinessException("已有GPS信息，请拆除或取消安装后更改GPS类型");
                            }
                            orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                                    .set(OrderInfoEntity::getGpsState, 2)
                                    .eq(OrderInfoEntity::getId, orderId)
                                    .eq(OrderInfoEntity::getDeleteFlag, 0));
                            orderPageInfoService.updateOrderPageInfo(orderId, States.GPS_INSTALL_APPLY, 1);
                        } else {
                            if (ObjUtil.isNull(orderGpsInfoEntity)) {
                                orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                                        .set(OrderInfoEntity::getGpsState, 0)
                                        .eq(OrderInfoEntity::getId, orderId)
                                        .eq(OrderInfoEntity::getDeleteFlag, 0));
                                orderPageInfoService.updateOrderPageInfo(orderId, States.GPS_INSTALL_APPLY, 0);
                            }
                        }
                    }
                }
            }
            List<OrderNodeRecordEntity> orderNodeRecordEntityList = orderNodeRecordMapper.selectList(
                    new LambdaQueryWrapper<OrderNodeRecordEntity>()
                            .eq(OrderNodeRecordEntity::getOrderId, orderId)
                            .eq(OrderNodeRecordEntity::getDeleteFlag, 0)
            );
            boolean exists = orderNodeRecordEntityList.stream()
                    .anyMatch(record -> record.getCurrentNode() != null && Objects.equals(record.getCurrentNode(),States.QUALITY_INSPECTION_FINISH.getNode()));
            if (Objects.equals(submitDTO.getNode(), States.QUALITY_INSPECTION) && !exists
                    && (Objects.equals(currentEvent, Events.AGREES) || Objects.equals(currentEvent, Events.AGREES_QUALITY_INSPECTION_2_OVERALL_REVIEW) || Objects.equals(currentEvent, Events.AGREES_QUALITY_INSPECTION_2_STORE_EVALUATION))){
                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);

                List<RiskAiIntelligentAuditEntity> riskAiIntelligentAuditEntities = riskAiIntelligentAuditMapper.selectList(
                        new LambdaQueryWrapper<RiskAiIntelligentAuditEntity>()
                                .eq(RiskAiIntelligentAuditEntity::getOrderId, orderId)
                                .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
                                .orderByDesc(RiskAiIntelligentAuditEntity::getCreateTime)
                );
                if (CollUtil.isNotEmpty(riskAiIntelligentAuditEntities)){
                    riskAiIntelligentAuditEntities.forEach(riskAiIntelligentAuditEntity -> {
                        riskAiIntelligentAuditEntity.setDeleteFlag(1);
                        riskAiIntelligentAuditMapper.updateById(riskAiIntelligentAuditEntities.get(0));
                    });
                }
                RiskAiIntelligentAuditEntity insertInfo = new RiskAiIntelligentAuditEntity();
                insertInfo.setOrderId(orderId)
                        .setOrderNumber(orderInfoEntity.getOrderNumber())
                        .setOrderSource(orderInfoEntity.getSourceType())
                        .setAiReportStatus(0)
                        .setAiReportRequestTime(LocalDateTime.now())
                        .setIsAutoAudit(2)
                ;
                riskAiIntelligentAuditMapper.insert(insertInfo);
            }
            SubmitResultVO submitResultVO = switch (currentEvent) {
                case AGREES -> orderStateService.sendEvent(currentNode, Events.AGREES, orderId, userId, submitDTO);
                case REJECT -> orderStateService.sendEvent(currentNode, Events.REJECT, orderId, userId, submitDTO);
                case BACK -> orderStateService.sendEvent(currentNode, Events.BACK, orderId, userId, submitDTO);
                case AGREES_RISK_FIRST_SINGLE_AGREES ->
                        orderStateService.sendEvent(currentNode, Events.AGREES_RISK_FIRST_SINGLE_AGREES, orderId, userId,
                                submitDTO);
                case REVIEW_APPOINTMENT_FINISH ->
                        orderStateService.sendEvent(States.CUSTOMER_APPOINTMENT, Events.REVIEW_APPOINTMENT_FINISH,
                                orderId, userId);
                case AGREES_QUALITY_INSPECTION_2_AL_INTELLIGENT_AUDIT ->
                        orderStateService.sendEvent(States.QUALITY_INSPECTION, Events.AGREES_QUALITY_INSPECTION_2_AL_INTELLIGENT_AUDIT, orderId, userId, submitDTO);
                //总评复核 跳转到 资方终审
                case OVERALL_REVIEW_2_FUNDS_FINAL_APPROVE  ->
                        orderStateService.sendEvent(States.OVERALL_REVIEW,
                                Events.OVERALL_REVIEW_2_FUNDS_FINAL_APPROVE, orderId, userId, submitDTO);
                //资方放款放款蓄水池 卡单驳回到 请款资料
                case BACK_FUNDS_PAYMENT_PAYMENT_APPLY_INFORMATION  ->
                        orderStateService.sendEvent(States.FUNDS_PAYMENT_APPROVAL,
                                Events.BACK_FUNDS_PAYMENT_PAYMENT_APPLY_INFORMATION, orderId, userId, submitDTO);
                default -> orderStateService.sendEvent(currentNode, currentEvent, orderId, userId, submitDTO);
            };
            if (ObjUtil.equals(submitDTO.getNode(), States.QUALITY_INSPECTION)
                    || ObjUtil.equals(submitDTO.getNode(), States.RISK_FIRST_APPROVE)
                    || ObjUtil.equals(submitDTO.getNode(), States.MANAGER_INTERVIEW)
                    || ObjUtil.equals(submitDTO.getNode(), States.PAYMENT_APPLY_INFORMATION)) {
                //AI风控智能审核不进行下一步派单，有可能是智能审核通过
                if(currentEvent != Events.AGREES_QUALITY_INSPECTION_2_AL_INTELLIGENT_AUDIT){
                    OrderApproveDistributeService orderApproveDistributeService = SpringContentUtils.getBean(OrderApproveDistributeService.class);
                    orderApproveDistributeService.updateOrderApproveDistribute(new UpdateOrderApproveDistributeDTO()
                            .setOrderId(submitDTO.getOrderId())
                            .setSource(0)
                            .setBusinessType(2)
                            .setCurrentNode(submitDTO.getNode().getNode()));
                }
            }
//            redisService.releaseLock(LOCK_KEY_APPROVAL_SUBMIT + orderId, lockValue);
            log.info("ApprovalServiceImpl.approvalSubmit submitDTO:{} currentEvent:{}",JSONUtil.toJsonStr(submitDTO),currentEvent);
            if (Objects.equals(submitDTO.getNode(), States.QUALITY_INSPECTION)
                    && (Objects.equals(currentEvent, Events.AGREES) || Objects.equals(currentEvent, Events.AGREES_QUALITY_INSPECTION_2_OVERALL_REVIEW) || Objects.equals(currentEvent, Events.AGREES_QUALITY_INSPECTION_2_STORE_EVALUATION))
            || Objects.equals(currentEvent, Events.AGREES_QUALITY_INSPECTION_2_AL_INTELLIGENT_AUDIT)){
                try {
                    RiskAiIntelligentAuditEntity riskAiIntelligentAuditEntity = riskAiIntelligentAuditMapper.selectOne(new LambdaQueryWrapper<RiskAiIntelligentAuditEntity>()
                            .eq(RiskAiIntelligentAuditEntity::getOrderId, orderId)
                            .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
                            .orderByDesc(RiskAiIntelligentAuditEntity::getCreateTime)
                            .last("limit 1")
                    );
                    //如果是失败转人工的话，按原来流程走
                    if (riskAiIntelligentAuditEntity != null && ObjUtil.equals(riskAiIntelligentAuditEntity.getAiReportStatus(), 2)){
                        log.info("ApprovalServiceImpl.approvalSubmit ai 智能审核 失败转人工,不在进行查询风控查询 orderId:{}", orderId);
                    }else {
                        log.info("ApprovalServiceImpl.approvalSubmit intelligentRisk 开始风控请求:{} type:{} step{}",orderId,1,2);
                        intelligentRiskControlService.intelligentRisk(orderId, 2, null);
                        log.info("ApprovalServiceImpl.approvalSubmit push ai risk report orderId:{} type:{} step{}",orderId,1,2);
                    }
                } catch (Exception e){
                    log.error("ApprovalServiceImpl.approvalSubmit type:{} error:{}",1, e.getMessage());
                }
            }
            if (Objects.equals(submitDTO.getNode(), States.PAYMENT_APPLY_INFORMATION) && Objects.equals(currentEvent, Events.AGREES)){
                try {
                    intelligentRiskControlService.intelligentContract(orderId, 2, null);
                    log.info("ApprovalServiceImpl.approvalSubmit push ai risk report orderId:{} type:{} step{}",orderId,2,2);
                } catch (Exception e){
                    log.error("ApprovalServiceImpl.approvalSubmit type:{} error:{}",2, e.getMessage());
                }
            }
            if (Objects.equals(submitDTO.getNode(), States.PAYMENT_APPLY_INFORMATION)
                    && (Objects.equals(currentEvent, Events.BACK)
                    || Objects.equals(currentEvent, Events.RESIGN_CONTRACT) || Objects.equals(currentEvent, Events.REBIND_BLANK_CARD)
                    || Objects.equals(currentEvent, Events.BACK_PAYMENT_APPLY_INFORMATION_2_BUSINESS_ADDED_INFO)
                    || Objects.equals(currentEvent, Events.RE_PAYMENT_PAY) || Objects.equals(currentEvent, Events.CANCEL)
            )){
                try {
                    int update = intelligentRiskInfoEntityMapper.update(
                            new LambdaUpdateWrapper<IntelligentRiskInfoEntity>()
                                    .set(IntelligentRiskInfoEntity::getDeleteFlag,1)
                                    .eq(IntelligentRiskInfoEntity::getOrderId, orderId)
                                    .eq(IntelligentRiskInfoEntity::getType,2)
                    );
                    log.info("ApprovalServiceImpl.approvalSubmit update ai contract report orderId:{} update:{}",orderId,update);
                } catch (Exception e){
                    log.error("ApprovalServiceImpl.approvalSubmit update ai contract report error:{}", e.getMessage());
                }
            }
            if ((Objects.equals(submitDTO.getNode(), States.PAYMENT_CONTRACT_APPROVAL) && Objects.equals(currentEvent, Events.BACK))
                    || Objects.equals(currentEvent, Events.BACK_FUNDS_PAYMENT_PAYMENT_APPLY_INFORMATION) ){
                try {
                    int update = intelligentRiskInfoEntityMapper.update(
                            new LambdaUpdateWrapper<IntelligentRiskInfoEntity>()
                                    .set(IntelligentRiskInfoEntity::getDeleteFlag,1)
                                    .eq(IntelligentRiskInfoEntity::getOrderId, orderId)
                                    .eq(IntelligentRiskInfoEntity::getType,2)
                    );
                    log.info("ApprovalServiceImpl.approvalSubmit update ai contract report orderId:{} update:{}",orderId,update);
                } catch (Exception e){
                    log.error("ApprovalServiceImpl.approvalSubmit update ai contract report error:{}", e.getMessage());
                }
            }
            if (Objects.equals(currentEvent, Events.BACK_FUNDS_PAYMENT_PAYMENT_APPLY_INFORMATION) ){
                // 拦截订单进行 驳回
                orderLoanReservoirService.reject(orderId,currentUser);
            }
            //总评复核通过资方终审 自动风控审批添加记录
            if(Objects.equals(currentEvent,Events.OVERALL_REVIEW_2_FUNDS_FINAL_APPROVE)){
                //添加风控初审记录
                RiskAiIntelligentAuditEntity riskAiIntelligentAuditEntity = riskAiIntelligentAuditMapper.selectOne(new LambdaQueryWrapper<RiskAiIntelligentAuditEntity>()
                                .eq(RiskAiIntelligentAuditEntity::getOrderId, orderId)
                                .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
                                .orderByDesc(RiskAiIntelligentAuditEntity::getCreateTime)
                                .last("limit 1")
                         );
                intelligentRiskControlService.insertRecord(orderId,
                        States.OVERALL_REVIEW.getNode(),//当前
                        States.RISK_FIRST_APPROVE_ASSIGN.getNode(),//下一个
                        States.STORE_EVALUATION.getNode(),//上一个
                        "",
                        "总评复核通过到风控初审分配自动流转",
                        Events.AGREES,aiUserId);
                intelligentRiskControlService.insertRecord(orderId,
                    States.RISK_FIRST_APPROVE.getNode(),//当前
                    States.FUNDS_FINAL_APPROVE.getNode(),//下一个
                    States.RISK_FIRST_APPROVE_ASSIGN.getNode(),//上一个
                        riskAiIntelligentAuditEntity.getInternalReason(),
                        riskAiIntelligentAuditEntity.getAuditRemarks(),
                    Events.AGREES,aiUserId);
            }
            try {
                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
                if (Objects.equals(currentNode, States.RISK_FIRST_APPROVE)
                        || Objects.equals(currentNode, States.RISK_FIRST_APPROVE_ASSIGN)
                        || Objects.equals(currentNode, States.QUALITY_INSPECTION)
                        || (Objects.equals(currentNode, States.CUSTOMER_APPOINTMENT) && ObjUtil.equals(orderInfoEntity.getReviewState(), 1))
                        || Objects.equals(currentNode, States.PAYMENT_APPLY_INFORMATION)) {
                    Integer state = currentNode.getNode();
                    if (Objects.equals(currentNode, States.CUSTOMER_APPOINTMENT)) {
                        state = States.MANAGER_INTERVIEW.getNode();
                    }
                    orderApproveDistributeService.updateOrderApproveDistribute(new UpdateOrderApproveDistributeDTO()
                            .setOrderId(orderId)
                            .setOrderNumber(orderInfoEntity.getOrderNumber())
                            .setSource(0)
                            .setBusinessType(2)
                            .setCurrentNode(state));

                }
            } catch (Exception e) {
                log.error("OrderTerminationAction execute error", e);
            }
            return submitResultVO;
        } catch (InterruptedException e) {
            releaseCredits(node, orderId);
            log.error("Lock acquisition failed for orderId: {}", orderId);
            throw new BusinessException("操作频繁，请稍后再试");
        } catch (Exception e) {
            //发生异常释放额度
            releaseCredits(node, orderId);
            throw e;
        } finally {
            redisService.releaseLock(LOCK_KEY_APPROVAL_SUBMIT + orderId, lockValue);
        }
    }
    //判断最小金额修改到风控额度
    private void checkMinAmount(Integer orderId) {
        OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                .eq(OrderAmountEntity::getOrderId, orderId)
                .eq(OrderAmountEntity::getDeleteFlag, 0)
                .orderByDesc(OrderAmountEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNotNull(orderAmountEntity)){
            BigDecimal littleAmount = Stream.of(orderAmountEntity.getPreAmount(),
                            orderAmountEntity.getHopeAmount(),
                            orderAmountEntity.getTotalAmount(),
                            orderAmountEntity.getFundPreAmount())
                    .filter(Objects::nonNull) // 过滤掉null值以避免比较时出错
                    .min(BigDecimal::compareTo)
                    .orElse(null); // 如果所有字段都为null，则返回null或设置默认值
            BigDecimal riskAmount = Objects.nonNull(littleAmount) ? littleAmount: BigDecimal.valueOf(0.00);
            // 将最小金额更新到 LH_ORDER_AMOUNT 表的 risk_amount 字段
            orderAmountMapper.update(new LambdaUpdateWrapper<OrderAmountEntity>()
                    .set(OrderAmountEntity::getRiskAmount, riskAmount)
                    .eq(OrderAmountEntity::getId, orderAmountEntity.getId()));
        }
    }
    //判断合同签署时间是否超过十天
    private void verificationOfContractSigningTime(Integer orderId) {
        SignTaskEntity signTaskEntity = signTaskMapper.selectOne(
                new LambdaQueryWrapper<SignTaskEntity>()
                        .eq(SignTaskEntity::getBusiId, orderId)
                        .eq(SignTaskEntity::getDeleteFlag, 0)
                        .eq(SignTaskEntity::getSignType, 1)
                        .orderByDesc(SignTaskEntity::getCreateTime)
                        .last("limit 1")
        );
        if(ObjUtil.isEmpty(signTaskEntity)){
            throw new BusinessException("合同签署信息不存在");
        }
        if (ObjUtil.isEmpty(signTaskEntity.getCreateTime())) {
            throw new BusinessException("合同签署时间不能为空");
        }
        LocalDate signDate = signTaskEntity.getCreateTime().toLocalDate();
        // 计算两个日期之间的自然日差（不含当天）
        LocalDate now = LocalDate.now();
        long signDaysBetween = ChronoUnit.DAYS.between(signDate, now);
        // 判断是否超过10天
        if (signDaysBetween > 10) {
            throw new BusinessException("合同签约日期超限，请重新签署合同");
        }
    }
    //判断车辆评估时间是否超过十天
    private void verificationOfVehicleAssessmentTime(Integer orderId) {
        OrderVehicleInfoEntity  orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(
                new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                        .eq(OrderVehicleInfoEntity::getOrderId, orderId)
                        .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderVehicleInfoEntity::getCreateTime)
                        .last("limit 1")
        );
        String carEvaluateTime = switch (ObjUtil.isNotNull(orderVehicleInfoEntity.getCarReportType()) ? orderVehicleInfoEntity.getCarReportType() :1){
            case 1 -> {
                LanBenVehicleDataEntity lanBenVehicleDataEntity = lanBenVehicleDataMapper.selectOne(
                        new LambdaQueryWrapper<LanBenVehicleDataEntity>()
                                .eq(LanBenVehicleDataEntity::getVin, orderVehicleInfoEntity.getVin())
                                .eq(LanBenVehicleDataEntity::getDeleteFlag, 0)
                                .orderByDesc(LanBenVehicleDataEntity::getUpdateTime)
                                .last("limit 1")
                );
                if(lanBenVehicleDataEntity != null){
                    yield lanBenVehicleDataEntity.getUpdateTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
                }else {
                    yield null;
                }
            }
            case 2 -> {
                JzgVehicleDataEntity jzgVehicleDataEntity = jzgVehicleDataMapper.selectOne(
                        new LambdaQueryWrapper<JzgVehicleDataEntity>()
                                .eq(JzgVehicleDataEntity::getVin, orderVehicleInfoEntity.getVin())
                                .eq(JzgVehicleDataEntity::getDeleteFlag, 0)
                                .orderByDesc(JzgVehicleDataEntity::getUpdateTime)
                                .last("limit 1")
                );
                if(jzgVehicleDataEntity != null){
                    yield jzgVehicleDataEntity.getUpdateTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
                }else {
                    yield null;
                }
            }
            default -> null;
        };
        carEvaluateTime = ObjUtil.isNotEmpty(orderVehicleInfoEntity.getAppraiseTime()) ? orderVehicleInfoEntity.getAppraiseTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)) : carEvaluateTime;
        // 判断 carEvaluateTime 是否为空
        if (StringUtils.isEmpty(carEvaluateTime)) {
            throw new BusinessException("评车时间不能为空");
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN);
        LocalDate evaluateDate;
        try {
            evaluateDate = LocalDate.parse(carEvaluateTime, formatter);
        } catch (Exception e) {
            throw new BusinessException("评车时间格式不正确");
        }

        LocalDate now = LocalDate.now();

        // 计算两个日期之间的自然日差（不含当天）
        long daysBetween = ChronoUnit.DAYS.between(evaluateDate, now);

        if (daysBetween > 10) {
            throw new BusinessException("车辆评估日期超限，需要重新评车");
        }
    }
    private void verificationOfCompulsoryTrafficInsurance(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, ()->new BusinessException("订单不存在"));
        if (ObjUtil.equals(orderInfoEntity.getFundId(),FundEnum.ZHONG_HENG_TONG_HUI.getValue())){
            OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(new MPJLambdaWrapper<OrderVehicleInfoEntity>()
                    .eq(OrderVehicleInfoEntity::getOrderId, orderId)
                    .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderVehicleInfoEntity::getId), false);
            Assert.notNull(orderVehicleInfoEntity,()->new BusinessException( "车辆信息不存在"));
            LocalDate compulsoryDate = orderVehicleInfoEntity.getCompulsoryDate();
            Assert.notNull(compulsoryDate,()->new BusinessException( "未获取到交强险到期日,请在申请信息里车辆信息页修改交强险到期日"));
            LocalDate now = LocalDate.now();
            if (now.isAfter(compulsoryDate.minusDays(30))) {
                throw new BusinessException("交强险到期日不足30天，将影响请款");
            }
        }




    }

    /**
     * 重置订单流程状态
     *
     * @param orderId      订单 ID
     * @param redoNodeList 重做节点列表
     */
    private void resetOrderProcessStatus(Integer orderId, List<Integer> redoNodeList) {
        if (CollUtil.isEmpty(redoNodeList)) {
            return;
        }

        for (Integer redoNode : redoNodeList) {
            States states = States.getByNode(redoNode);
            if (states == null) {
                return;
            }
            OrderInfoEntity orderInfoEntity = new OrderInfoEntity();
            orderInfoEntity.setId(orderId);
            boolean updateFlag = false;
            switch (states) {
                case MANAGER_INTERVIEW:
                    orderInfoEntity.setReviewState(0);
                    updateFlag = true;
                    break;
                case GPS_INSTALL_APPLY:
//                    orderInfoEntity.setGpsState(0);
                    updateFlag = true;
                    break;
                case MORTGAGE_PENDING:
                    Long count = orderInfoMapper.selectCount(new LambdaQueryWrapper<OrderInfoEntity>()
                            .eq(OrderInfoEntity::getId, orderId).eq(OrderInfoEntity::getFundId, FundEnum.FU_MIN.getValue()));
                    if (count == 0) {
                        orderInfoEntity.setMortgageState(0);
                        updateFlag = true;
                    }
                    break;
            }
            if (updateFlag) {
                orderInfoMapper.updateById(orderInfoEntity);
                orderPageInfoService.updateOrderPageInfo(orderId, states, 0);
            }
        }
    }

    private void releaseCredits(States node, Integer orderId) {
        if (Objects.equals(States.BUSINESS_ADDED_INFO.getNode(), node.getNode())) {
            DeptQuotaRecordEntity deptQuotaRecordEntity = deptQuotaRecordMapper.selectOne(new LambdaQueryWrapper<DeptQuotaRecordEntity>()
                    .eq(DeptQuotaRecordEntity::getOrderId, orderId)
                    .eq(DeptQuotaRecordEntity::getAction, 0)
                    .eq(DeptQuotaRecordEntity::getDeleteFlag, 0)
                    .orderByDesc(DeptQuotaRecordEntity::getId), false);
            if (ObjUtil.isNotNull(deptQuotaRecordEntity)) {
                int update = deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                        .setIncrBy(DeptQuotaEntity::getRemainingQuota, deptQuotaRecordEntity.getQuota())
                        .eq(DeptQuotaEntity::getId, deptQuotaRecordEntity.getDeptQuotaId())
                        .eq(DeptQuotaEntity::getDeptId, deptQuotaRecordEntity.getDeptId())
                        .eq(DeptQuotaEntity::getDeleteFlag, 0));
                if (update > 0) {
                    deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                            .set(DeptQuotaRecordEntity::getAction, 1)
                            .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
                } else {
                    log.error("ApprovalServiceImpl getEventsAndValidate update dept quota fail orderid:{}", orderId);
                    deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                            .set(DeptQuotaRecordEntity::getDeleteFlag, 1)
                            .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
                }
            }
        }
    }

    private States getNode(Integer result, States node, States backNode, Integer orderId) {
        if (Stream.of(States.MANAGER_INTERVIEW,
                        States.CONTRACT_SIGNING,
                        States.GPS_INSTALL_APPLY,
                        States.MORTGAGE_PENDING,
                        States.GPS_FEE_PAYMENT)
                .anyMatch(value -> Objects.equals(value.getNode(), node.getNode()))) {
            return States.CUSTOMER_APPOINTMENT;
        }
        return node;

    }

    /**
     * 转换状态机中的映射关系
     *
     * @param result
     * @param node
     * @return
     */
    @Override
    public Events getEventsAndValidate(Integer result, States node, States backNode, Integer orderId, Integer storeId, Integer appraiserId, String appraiserName) {
        Events currentEvent = Events.getExtendEvents(node, backNode);
        if (currentEvent == null) {
            currentEvent = Events.getEnum(result);
        }
        if (currentEvent == Events.AGREES) {
            if (node != null) {

                if (Objects.equals(States.MANAGER_INTERVIEW.getNode(), node.getNode())) {
                    currentEvent = Events.REVIEW_APPOINTMENT_FINISH;
                }
                if (Objects.equals(States.PAYMENT_APPLY_INFORMATION.getNode(), node.getNode())) {
                    //验证绑卡数量
                    checkCardNum(orderId);
                    // 验证请款资料文件
                    verifyPaymentApplyInformation(orderId);
                    OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
                    OrderGpsInfoEntity orderGpsInfoEntity = orderGpsInfoMapper.selectOne(
                            new LambdaQueryWrapper<OrderGpsInfoEntity>()
                                    .eq(OrderGpsInfoEntity::getOrderId, orderId)
                                    .eq(OrderGpsInfoEntity::getDeleteFlag, 0)
                                    .orderByDesc(OrderGpsInfoEntity::getCreateTime)
                                    .last("limit 1")
                    );
                    if (ObjUtil.isNotEmpty(orderInfoEntity) &&ObjUtil.isNotEmpty(orderGpsInfoEntity)){
                        if (Objects.equals(orderGpsInfoEntity.getInstallType(),0) &&(Objects.equals(orderInfoEntity.getSourceType(), 1) && (Objects.equals(orderInfoEntity.getRegionId(), 24) || Objects.equals(orderInfoEntity.getRegionId(), 56)))){
                            GpsDetailVO detail = gpsService.detail(orderId);
                            if (ObjUtil.isNotEmpty(detail)){
                                List<GpsDetailVO.GpsInfo> gpsInfoList = detail.getGpsInfoList();
                                if (CollUtil.isEmpty(gpsInfoList)){
                                    throw new BusinessException("请先上传GPS文件");
                                }else {
                                    orderInfoEntity.setGpsState(2);
                                    orderInfoMapper.updateById(orderInfoEntity);
                                    orderGpsInfoEntity.setGpsStatus(2);
                                    orderGpsInfoMapper.updateById(orderGpsInfoEntity);
                                    gpsInstallInfoMapper.update(
                                            new LambdaUpdateWrapper<GpsInstallInfoEntity>()
                                                    .eq(GpsInstallInfoEntity::getGpsId, orderGpsInfoEntity.getId())
                                                    .eq(GpsInstallInfoEntity::getDeleteFlag, 0)
                                                    .set(GpsInstallInfoEntity::getStatus, 1)
                                    );
                                }
                            }
                        }
                    }

                    //线上抵押验证抵押状态
                    try {
                        verifyFundMortgageState(orderId);
                    } catch (BusinessException e) {
                        log.error("ApprovalServiceImpl getEventsAndValidate verifyFundMortgageState fail orderid:{} e:{}", orderId, e.getMessage(), e);
                        throw e;
                    } catch (Exception e) {
                        log.error("ApprovalServiceImpl getEventsAndValidate verifyFundMortgageState fail orderid:{} e:{}", orderId, e.getMessage(), e);
                        throw new BusinessException("订单状态异常");
                    }

                }
                if (Objects.equals(States.QUALITY_INSPECTION.getNode(), node.getNode())) {
                    if (notExistRecord(orderId, Collections.singletonList(States.OVERALL_REVIEW.getNode()))) {
                        // 蓝本价接口在线评估车况显示较差时才会流转到这一节点
                        currentEvent = getQualityInspectionEvent(orderId, storeId, appraiserId);
                    } else {
                        currentEvent = Events.AGREES;
                    }
                    //判断配置的资方 AI只能风控是否开启,开启的话跳到新的节点
                    Boolean flag = riskFundAutoConfigService.handelRiskAutoConfigCurrentEvent(orderId);
                    if (!flag && !Objects.equals(currentEvent, Events.AGREES_QUALITY_INSPECTION_2_ACCIDENT_VEHICLE_REJECT)){
                        //判断蓝本价，如果蓝本价还没有生成则抛出
                        judgeLanben(orderId);
                        currentEvent = Events.AGREES_QUALITY_INSPECTION_2_AL_INTELLIGENT_AUDIT;
                    }
                }
                if (Objects.equals(States.STORE_EVALUATION.getNode(), node.getNode())) {
// 还有评估大于40万的需要
// 评价等级DEF
//                    currentEvent = getStoreEvaluationEvent(orderId);
                    // 车评必过总评
                    currentEvent = Events.AGREES_STORE_EVALUATION_2_OVERALL_REVIEW;

                }
                if (Objects.equals(States.OVERALL_REVIEW.getNode(), node.getNode())) {
                    RiskAiIntelligentAuditEntity riskAiIntelligentAudit = riskAiIntelligentAuditMapper.selectOne(new LambdaQueryWrapper<RiskAiIntelligentAuditEntity>()
                            .eq(RiskAiIntelligentAuditEntity::getOrderId, orderId)
                            .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
                            .orderByDesc(RiskAiIntelligentAuditEntity::getCreateTime)
                            .last("limit 1"));
                    if (riskAiIntelligentAudit != null && ObjUtil.equals(riskAiIntelligentAudit.getIsRiskFirst(), 2)){
                        currentEvent = Events.OVERALL_REVIEW_2_FUNDS_FINAL_APPROVE;
                    }

                }
                if (Objects.equals(States.PAYMENT_CONTRACT_APPROVAL.getNode(), node.getNode())){
                    OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
                    loanReservoirRulesService.judgOrderReservoirRules(orderInfoEntity);
                }
            }

        } else if (currentEvent == Events.BACK) {
            //            currentEvent = Events.getExtendEvents(node, backNode);
            // 资方放款放款蓄水池拦截订单驳回
            if (ObjUtil.equals(node.getNode(),States.FUNDS_PAYMENT_APPROVAL.getNode()) &&
                    ObjUtil.equals(backNode.getNode(),States.PAYMENT_APPLY_INFORMATION.getNode())){
                currentEvent = Events.BACK_FUNDS_PAYMENT_PAYMENT_APPLY_INFORMATION;
            }
        } else if (currentEvent == Events.REJECT ||currentEvent ==Events.CANCEL) {

            if (Arrays.asList(States.PAYMENT_APPLY_INFORMATION.getNode(), States.PAYMENT_CONTRACT_APPROVAL.getNode()).contains(node.getNode())) {
                // 校验是否可以拒绝
                verifyCanRejected(orderId);
            }

        }

        return currentEvent;
    }

    private void judgeLanben(Integer orderId) {
        OrderVehicleInfoEntity orderVehicleInfoEntity =
                orderVehicleInfoMapper.selectOne(new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                        .select(OrderVehicleInfoEntity::getVin, OrderVehicleInfoEntity::getMileage,
                                OrderVehicleInfoEntity::getBrand,OrderVehicleInfoEntity::getCarReportType)
                        .eq(OrderVehicleInfoEntity::getOrderId, orderId).last("limit 1"));

        String vin = orderVehicleInfoEntity.getVin();
        Integer reportType = Convert.toInt(orderVehicleInfoEntity.getCarReportType(), 1);

        Result<VehicleInfoVO> vehicleInfoVOResult = riskFeign.searchVehicleInfo(vin, reportType);

        if (!Result.isSuccess(vehicleInfoVOResult)) {
            log.info("lanBan vehicle data not finish");
            throw new BusinessException("蓝本价获取失败，请重新提交！");
        }

        //车况较差及蓝本价没有评估结果的订单或者蓝本价里程数如果大于输入里程数订单均需要过总评复核
        VehicleInfoVO data = vehicleInfoVOResult.getData();
        if (data != null) {
            Integer evaluateStatus = Convert.toInt(data.getEvaluateStatus(), 0);
            log.info("order {} lanben data evaluateStatus {}", orderId, evaluateStatus);
            // 没有评估结果
            if (evaluateStatus == 4) {
                if (Objects.equals(reportType, 1)) {
                    throw new BusinessException("蓝本价评估单被驳回！" + Convert.toStr(data.getFailDesc()));
                }
                if (Objects.equals(reportType, 2)) {
                    throw new BusinessException("精真估评估单被驳回！" + Convert.toStr(data.getFailDesc()));
                }

            }

            // 没有评估结果
            if (evaluateStatus != 3) {
                if (Objects.equals(reportType, 1)) {
                    throw new BusinessException("蓝本价车辆正在评估中");
                }
                if (Objects.equals(reportType, 2)) {
                    throw new BusinessException("精真估车辆正在评估中");
                }
            }
        }
    }

    private void verifyCanRejected(Integer orderId) {

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getFundId, OrderInfoEntity::getMortgageState)
                .eq(OrderInfoEntity::getId, orderId));

        if (ObjUtil.isNull(orderInfoEntity)) {
            log.warn("verifyCanRejected orderId {} not exist", orderId);
            return;
        }

        if (Objects.equals(orderInfoEntity.getFundId(), FundEnum.FU_MIN.getValue())) {

            if (Objects.equals(orderInfoEntity.getMortgageState(), 2)
                    || Objects.equals(orderInfoEntity.getMortgageState(), 3)) {
                throw new BusinessException("富民订单请先完成解抵流程");
            }
        }
    }

    /**
     * // 还有评估大于40万的需要
     * // 评价等级DEF
     * // 进总评
     *
     * @param orderId
     * @return
     */
    private Events getStoreEvaluationEvent(Integer orderId) {

        OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                .eq(OrderVehicleInfoEntity::getOrderId, orderId)
                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                .last("limit 1")
        );
        String appraiseLevel = orderVehicleInfoEntity.getAppraiseLevel();
        List<String> levelList = Arrays.asList("D", "E", "F");
        if (StrUtil.isBlank(appraiseLevel) || levelList.contains(appraiseLevel)) {
            return Events.AGREES_STORE_EVALUATION_2_OVERALL_REVIEW;
        }

        BigDecimal appraisePrice = orderVehicleInfoEntity.getAppraisePrice();
        if (appraisePrice == null || appraisePrice.compareTo(new BigDecimal(400000)) > 0) {
            return Events.AGREES_STORE_EVALUATION_2_OVERALL_REVIEW;
        }

        return Events.AGREES;

    }

    /**
     * 不存在批通过记录
     *
     * @param orderId
     * @return
     */
    private boolean notExistRecord(Integer orderId, List<Integer> nodeList) {

        Long l = orderNodeRecordMapper.selectCount(new LambdaQueryWrapper<OrderNodeRecordEntity>()
                .eq(OrderNodeRecordEntity::getOrderId, orderId).in(OrderNodeRecordEntity::getCurrentNode, nodeList)
                .eq(OrderNodeRecordEntity::getEvent, Events.AGREES)
        );

        return l == 0;
    }

    /**
     * 验证请款资料
     *
     * @param orderId 订单id
     */
    private void verifyPaymentApplyInformation(Integer orderId) {
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        FundEnum fundEnum = FundEnum.getFundEnum(orderInfo.getFundId());

        if (fundEnum == FundEnum.YING_FENG) {
            //获取文件
            //借款合同（线下）
            String loanContract = orderFileService.getResourceIdByOrderId(orderId, "ZZJKHTQS");
            if (StrUtil.isBlank(loanContract)) {
                throw new BusinessException("请先上传借款合同");
            }
            //抵押合同（线下）
            String pledgeContract = orderFileService.getResourceIdByOrderId(orderId, "ZFDYHTQS");
            if (StrUtil.isBlank(pledgeContract)) {
                throw new BusinessException("请先上传抵押合同");
            }
        } else if (fundEnum == FundEnum.CHANG_YIN) {
            //验证是否上传车检标
            String annualMark = orderFileService.getResourceIdByOrderId(orderId, "ANNUAL_MARK");
            if (StrUtil.isBlank(annualMark)) {
                throw new BusinessException("请先上传车辆年检标");
            }
        }

        if (fundEnum == FundEnum.FU_MIN) {
            if (ObjUtil.equals(orderInfo.getOrderType(), 1)) {
                //判断经营贷富民营业执照是否存在
                Long count = orderCompanyInfoMapper.selectCount(new LambdaQueryWrapper<OrderCompanyInfoEntity>()
                        .eq(OrderCompanyInfoEntity::getOrderId, orderId).eq(OrderCompanyInfoEntity::getDeleteFlag, 0));

                if (count <= 0) {
                    throw new BusinessException("请上传营业执照");
                }
            }

            SwitchVO gpsInstallSwitchInfo = switchUtils.getSwitchInfo(SwitchConstants.FU_MIN_GPS_INSTALL);
            if (ObjUtil.isNotEmpty(gpsInstallSwitchInfo) && Objects.equals(gpsInstallSwitchInfo.getSwitchFlag(), 1)) {
                //验证GPS安装确认书是否上传
                String gpsInstall = orderFileService.getResourceIdByOrderId(orderId, "GPS_INSTALL");
                if (StrUtil.isBlank(gpsInstall)) {
                    throw new BusinessException("请在补充资料上传GPS安装确认书");
                }
            }

            SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.FU_MIN_INSURANCE);
            if(ObjUtil.isNotEmpty(switchInfo) && Objects.equals(switchInfo.getSwitchFlag(), 1)){
                OrderAmountEntity orderAmount = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                        .eq(OrderAmountEntity::getOrderId, orderId)
                        .eq(OrderAmountEntity::getDeleteFlag, 0)
                        .last("limit 1"));

                if(orderAmount.getCustomerConfirmAmount().compareTo(new BigDecimal(switchInfo.getValue())) > 0){
                    String insurance = orderFileService.getResourceIdByOrderId(orderId, "COMMERCIAL_INSURANCE");
                    if (StrUtil.isBlank(insurance)) {
                        throw new BusinessException("请在补充资料上传车辆商业险");
                    }
                }
            }
            SwitchVO financialStatementSwitchInfo = switchUtils.getSwitchInfo(SwitchConstants.FU_MIN_FINANCIAL_STATEMENT);
            if(ObjUtil.isNotEmpty(financialStatementSwitchInfo) && Objects.equals(financialStatementSwitchInfo.getSwitchFlag(), 1)){
                OrderAmountEntity orderAmount = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                        .eq(OrderAmountEntity::getOrderId, orderId)
                        .eq(OrderAmountEntity::getDeleteFlag, 0)
                        .last("limit 1"));

                if(orderAmount.getCustomerConfirmAmount().compareTo(new BigDecimal(financialStatementSwitchInfo.getValue())) > 0){
                    String insurance = orderFileService.getResourceIdByOrderId(orderId, "FINANCIAL_PROOF");
                    if (StrUtil.isBlank(insurance)) {
                        throw new BusinessException("请在补充资料上传银行流水");
                    }
                }
            }



            //验证富民抵押状态
            Result<String> stringResult = approveFeign.fuMinMortgageStatusQuery(new MortgageStatusQueryDTO().setOrderId(orderId).setMortgageType("1"));
            if (ObjUtil.isNull(stringResult)) {
                throw new BusinessException("抵押在状态查询失败");
            }
            String description = stringResult.getData();
            if (checkFuMinStatus(description)) {
                throw new BusinessException("订单抵押状态未完成");
            }

        }
        Integer type = Convert.toInt(2, 0);
        List<FileMenuEntity> fileMenuList = fileMenuMapper.selectList(new LambdaQueryWrapper<FileMenuEntity>()
                .eq(!Objects.equals(type, 0), FileMenuEntity::getType, type)
                .orderByAsc(FileMenuEntity::getSort)
        );
        if (CollUtil.isNotEmpty(fileMenuList)) {
            List<Integer> menuIdList = fileMenuList.stream().map(FileMenuEntity::getId).toList();
            log.info("ApprovalServiceImpl getEventsAndValidate menuIdList:{}", JSONUtil.toJsonStr(menuIdList));
            List<MenuVO> menuVOList = orderFileMenuService.getMenuFileList(orderId, menuIdList);
            if (CollUtil.isNotEmpty(fileMenuList)) {
                List<Integer> menuFileIdList = menuVOList.stream().map(MenuVO::getFileId).toList();
                List<OrderFileEntity> orderFileEntityList = orderFileService.list(
                        new LambdaQueryWrapper<OrderFileEntity>()
                                .eq(OrderFileEntity::getOrderId, orderId)
                                .eq(OrderFileEntity::getDeleteFlag, 0)
                );
                log.info("ApprovalServiceImpl getEventsAndValidate orderFileEntityList:{}", orderFileEntityList);
                List<Integer> fileIdList= orderFileEntityList.stream().map(OrderFileEntity::getFileId).toList();
                List<Integer> resultFileIdList = menuFileIdList.stream()
                        .filter(id -> !fileIdList.contains(id))
                        .toList();
                if (CollUtil.isNotEmpty(resultFileIdList)){
                    log.info("ApprovalServiceImpl getEventsAndValidate resultFileIdList:{}", resultFileIdList);
                    OrderInfoEntity orderInfoEntity1 = orderInfoMapper.selectById(orderId);
                    List<FileAssoEntity> fileAssoEntityList = fileAssoMapper.selectList(
                            new LambdaQueryWrapper<FileAssoEntity>()
                                    .in(FileAssoEntity::getFileId, resultFileIdList)
                                    .eq(FileAssoEntity::getFundId,orderInfoEntity1.getFundId())
                                    .eq(FileAssoEntity::getRequired, 0)
                                    .eq(FileAssoEntity::getDeleteFlag, 0)
                    );
                    log.info("ApprovalServiceImpl getEventsAndValidate fileAssoEntityList:{}", fileAssoEntityList);
                    if (CollUtil.isNotEmpty(fileAssoEntityList)){
                        List<Integer> assoFileIdList=fileAssoEntityList.stream().map(FileAssoEntity::getFileId).toList();
                        List<FileConfigEntity> fileConfigEntityList = fileConfigMapper.selectList(
                                new LambdaQueryWrapper<FileConfigEntity>()
                                        .in(FileConfigEntity::getId, assoFileIdList)
                                        .eq(FileConfigEntity::getDeleteFlag, 0)
                        );
                        if (CollUtil.isNotEmpty(fileConfigEntityList)) {
                            if (fundEnum == FundEnum.FU_MIN && Objects.equals(fileConfigEntityList.get(0).getCode(),"COMMERCIAL_INSURANCE")){
                                SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.FU_MIN_INSURANCE);
                                if(ObjUtil.isNotEmpty(switchInfo) && Objects.equals(switchInfo.getSwitchFlag(), 1)){
                                    OrderAmountEntity orderAmount = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                                            .eq(OrderAmountEntity::getOrderId, orderId)
                                            .eq(OrderAmountEntity::getDeleteFlag, 0)
                                            .last("limit 1"));

                                    if(orderAmount.getCustomerConfirmAmount().compareTo(new BigDecimal(switchInfo.getValue())) > 0){
                                        String insurance = orderFileService.getResourceIdByOrderId(orderId, "COMMERCIAL_INSURANCE");
                                        if (StrUtil.isBlank(insurance)) {
                                            throw new BusinessException("请在补充资料上传车辆商业险");
                                        }
                                    }
                                }
                            }else if (fundEnum == FundEnum.FU_MIN && Objects.equals(fileConfigEntityList.get(0).getCode(),"FINANCIAL_PROOF")){
                                SwitchVO financialStatementSwitchInfo = switchUtils.getSwitchInfo(SwitchConstants.FU_MIN_FINANCIAL_STATEMENT);
                                if(ObjUtil.isNotEmpty(financialStatementSwitchInfo) && Objects.equals(financialStatementSwitchInfo.getSwitchFlag(), 1)){
                                    OrderAmountEntity orderAmount = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                                            .eq(OrderAmountEntity::getOrderId, orderId)
                                            .eq(OrderAmountEntity::getDeleteFlag, 0)
                                            .last("limit 1"));

                                    if(orderAmount.getCustomerConfirmAmount().compareTo(new BigDecimal(financialStatementSwitchInfo.getValue())) > 0){
                                        String insurance = orderFileService.getResourceIdByOrderId(orderId, "FINANCIAL_PROOF");
                                        if (StrUtil.isBlank(insurance)) {
                                            throw new BusinessException("请在补充资料上传银行流水");
                                        }
                                    }
                                }
                            } else {
                                log.info("ApprovalServiceImpl getEventsAndValidate fileConfigEntityList:{}", fileConfigEntityList);
                                throw new BusinessException(fileConfigEntityList.get(0).getName()+"附件未上传");
                            }
                        }
                    }
                    FileConfigEntity fileConfigEntity = fileConfigMapper.selectOne(
                            new LambdaQueryWrapper<FileConfigEntity>()
                                    .eq(FileConfigEntity::getCode, FileConfigEnums.CALL_DETAIL.getCode())
                                    .eq(FileConfigEntity::getDeleteFlag,0)
                                    .orderByDesc(FileConfigEntity::getCreateTime)
                                    .last("limit 1")
                    );
                    List<OrderFileEntity> list = orderFileService.list(
                            new LambdaQueryWrapper<OrderFileEntity>()
                                    .eq(OrderFileEntity::getFileId,fileConfigEntity.getId())
                                    .eq(OrderFileEntity::getOrderId,orderId)
                                    .eq(OrderFileEntity::getDeleteFlag,0)
                    );
                    if (CollUtil.isEmpty(list)){
                        throw new BusinessException(fileConfigEntity.getName()+"附件未上传");
                    }
                }
            }
        }
    }

    private boolean checkFuMinStatus(String description) {
        boolean flag = true;
        if (FuMinMortgageEnums.PUSH_TO_TMRI.getCode().equals(description) || FuMinMortgageEnums.ACCEPT_COMPLETED.getCode().equals(description) || FuMinMortgageEnums.COMPLETED.getCode().equals(description)) {
            flag = false;
        }
        return flag;
    }

    /**
     * 验证绑卡数量
     *
     * @param orderId 订单id
     */
    private void checkCardNum(Integer orderId) {
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        FundEnum fundEnum = FundEnum.getFundEnum(orderInfo.getFundId());
        Long count = bankAccountSignMapper.selectCount(new LambdaQueryWrapper<BankAccountSignEntity>()
                .eq(BankAccountSignEntity::getOrderId, orderId)
                .eq(BankAccountSignEntity::getSignState, 1)
                .eq(BankAccountSignEntity::getDeleteFlag, 0));

        int cardNum = 0;
        switch (fundEnum) {
            case YING_FENG -> {
                cardNum = 2;
            }
            case FU_MIN -> {
                cardNum = 1;
            }
            default -> {
                log.info("该资方暂未开启请款");
            }
        }

        if (count < cardNum) {
            throw new BusinessException("客户未完成绑卡，请联系客户完成绑卡！");
        }
    }

    /**
     * 检查车辆状态
     *
     * <P>1. 车评较差过  总评
     * <P>2. 事故车 盈峰 拒绝或进 车评
     * <P>3. 公里数小于报告里程数进 车评
     * <P>4. 蔚来、睿蓝、乐道、飞凡、董火虫车过 总评
     * <P>5. 其它进 风控初审
     *
     * @param orderId 次序id
     * @return boolean
     */
    private Events getQualityInspectionEvent(Integer orderId, Integer storeId, Integer appraiserId) {
        Events defaultEvent = Events.AGREES;


        OrderVehicleInfoEntity orderVehicleInfoEntity =
                orderVehicleInfoMapper.selectOne(new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                        .select(OrderVehicleInfoEntity::getVin, OrderVehicleInfoEntity::getMileage,
                                OrderVehicleInfoEntity::getBrand,OrderVehicleInfoEntity::getCarReportType)
                        .eq(OrderVehicleInfoEntity::getOrderId, orderId).last("limit 1"));

        String vin = orderVehicleInfoEntity.getVin();
        BigDecimal mileage = orderVehicleInfoEntity.getMileage() != null ? orderVehicleInfoEntity.getMileage() : BigDecimal.ZERO;
        String brand = orderVehicleInfoEntity.getBrand();
        Integer reportType = Convert.toInt(orderVehicleInfoEntity.getCarReportType(), 1);

        Result<VehicleInfoVO> vehicleInfoVOResult = riskFeign.searchVehicleInfo(vin, reportType);

        if (!Result.isSuccess(vehicleInfoVOResult)) {
            log.info("lanBan vehicle data not finish");
            throw new BusinessException("蓝本价获取失败，请重新提交！");
            //return Events.AGREES_QUALITY_INSPECTION_2_OVERALL_REVIEW;
        }

        //车况较差及蓝本价没有评估结果的订单或者蓝本价里程数如果大于输入里程数订单均需要过总评复核
        VehicleInfoVO data = vehicleInfoVOResult.getData();
        if (data != null) {
            String onlineEvaluate = data.getOnlineEvaluate();
            BigDecimal lanBenMileage = data.getMileage();
            BigDecimal evaluateAmount = Convert.toBigDecimal(data.getEvaluateAmount(), BigDecimal.ZERO);
            Integer evaluateStatus = Convert.toInt(data.getEvaluateStatus(), 0);
            log.info("order {} lanben data evaluateStatus {}", orderId, evaluateStatus);
            // 没有评估结果
            if (evaluateStatus == 4) {
                if (Objects.equals(reportType, 1)){
                    throw new BusinessException("蓝本价评估单被驳回！" + Convert.toStr(data.getFailDesc()));
                }
                if (Objects.equals(reportType, 2)){
                    throw new BusinessException("精真估评估单被驳回！" + Convert.toStr(data.getFailDesc()));
                }

            }


            // 没有评估结果
            if (evaluateStatus != 3) {
                if (Objects.equals(reportType, 1)){
                    throw new BusinessException("蓝本价车辆正在评估中");
                }
                if (Objects.equals(reportType, 2)){
                    throw new BusinessException("精真估车辆正在评估中");
                }
            }

            // 车况较差
            if (StringUtils.isNotBlank(onlineEvaluate)) {
                if (onlineEvaluate.contains("较差")) {
                    return Events.AGREES_QUALITY_INSPECTION_2_OVERALL_REVIEW;
                }
                if (onlineEvaluate.contains("事故")) {

                    OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
                    if (ObjUtil.isNotNull(orderInfoEntity) && Objects.equals(orderInfoEntity.getSourceType(), 1) &&
                            (Objects.equals(orderInfoEntity.getRegionId(), 24) || Objects.equals(orderInfoEntity.getRegionId(), 56)) && ObjUtil.isEmpty(appraiserId)) {
                        return Events.AGREES_QUALITY_INSPECTION_2_ACCIDENT_VEHICLE_REJECT;
                    }

                    Integer fundId = getFundId(orderId);
                    if (FundEnum.YING_FENG.getValue() == fundId) {
                        // 盈峰 事故车直接拒绝
                        return Events.AGREES_QUALITY_INSPECTION_2_ACCIDENT_VEHICLE_REJECT;
                    } else {
                        if (ObjUtil.isNotEmpty(appraiserId)) {
                            Result<UserInfoVO> userInfoVOResult = userFeign.searchUserName(appraiserId);
                            String userName = "";
                            if (Result.isSuccess(userInfoVOResult) && ObjUtil.isNotEmpty(userInfoVOResult.getData())) {
                                userName = userInfoVOResult.getData().getName();
                            }
                            orderVehicleInfoMapper.update(new LambdaUpdateWrapper<OrderVehicleInfoEntity>()
                                    .set(OrderVehicleInfoEntity::getStoreId, storeId)
                                    .set(OrderVehicleInfoEntity::getAppraiserId, appraiserId)
                                    .set(OrderVehicleInfoEntity::getAppraiseName, userName)
                                    .eq(OrderVehicleInfoEntity::getOrderId, orderId)
                                    .eq(OrderVehicleInfoEntity::getDeleteFlag, 0));
                            orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                                    .set(OrderInfoEntity::getStoreAppraiser, userName)
                                    .eq(OrderInfoEntity::getId, orderId)
                                    .eq(OrderInfoEntity::getDeleteFlag, 0));
                        }
                        return Events.AGREES_QUALITY_INSPECTION_2_STORE_EVALUATION;
                    }
                }
            }
            // 评估价为零
            if (BigDecimal.ZERO.equals(evaluateAmount)) {
                return Events.AGREES_QUALITY_INSPECTION_2_OVERALL_REVIEW;
            }

            // 蓝本价里程数如果大于输入里程数
            if (lanBenMileage == null) {
                return Events.AGREES_QUALITY_INSPECTION_2_OVERALL_REVIEW;
            }

            BigDecimal multiply = lanBenMileage.multiply(BigDecimal.valueOf(10000));

            boolean b = multiply.compareTo(mileage) > 0;
            if (b) {
                return Events.AGREES_QUALITY_INSPECTION_2_OVERALL_REVIEW;
            }
        }
        // 蔚来、睿蓝、乐道、飞凡、董火虫过总评复核 萤火虫
        if (Arrays.asList("蔚来", "飞凡汽车","睿蓝汽车","乐道","萤火虫").contains(brand)) {
            return Events.AGREES_QUALITY_INSPECTION_2_OVERALL_REVIEW;
        }

        return defaultEvent;
    }

    private Integer getFundId(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId, OrderInfoEntity::getFundId)
                .eq(OrderInfoEntity::getId, orderId));
        if (orderInfoEntity == null) {
            throw new BusinessException("订单不存在");
        }

        return orderInfoEntity.getFundId();
    }

    /**
     * 订单提交
     *
     * @param submitDTO   提交 DTO
     * @param currentUser 当前用户
     * @return {@link SubmitResultVO }
     */
    @Override
    @Kingdee
    public SubmitResultVO orderSubmit(OrderSubmitDTO submitDTO, LoginUser currentUser) {
        Integer orderId = submitDTO.getOrderId();
        Integer result = submitDTO.getResult();
        String remark = submitDTO.getRemark();
        Integer userId = currentUser.getUserId();

        Objects.requireNonNull(orderId, "进件Id不存在");
        Objects.requireNonNull(result, "提交结论不存在");
        Objects.requireNonNull(userId, "审批用户不存在");

        SubmitResultVO approve = orderStateService.approve(States.BUSINESS_ADDED_INFO, orderId, userId, remark);
//        intelligentRiskControlService.intelligentRisk(orderId);
        try {
            intelligentRiskControlService.intelligentRisk(orderId, 1, null);
            log.info("ApprovalServiceImpl.orderSubmit push ai risk report orderId:{} step{}",orderId,1);
        } catch (Exception e){
            log.error("ApprovalServiceImpl.orderSubmit e:{}", e.getMessage());
        }
        return approve;
    }

    /**
     * 订单提交
     *
     * @param submitDTO   提交 DTO
     * @param currentUser 当前用户
     * @return {@link SubmitResultVO }
     */
    @Override
    @Kingdee
    public SubmitResultVO orderCancel(OrderSubmitDTO submitDTO, LoginUser currentUser) {
        Integer orderId = submitDTO.getOrderId();
        Integer result = submitDTO.getResult();
        String remark = submitDTO.getRemark();
        Integer userId = currentUser.getUserId();

        Objects.requireNonNull(orderId, "进件Id不存在");
        Objects.requireNonNull(userId, "审批用户不存在");

        // 订单取消校验
        States currentNode = orderStateService.getCurrentNode(orderId);
        Assert.notEquals(currentNode, States.PAYMENT_SUCCESS, () -> {
            throw new BusinessException("放款成功，无法取消订单！");
        });
        Assert.notEquals(currentNode, States.FUNDS_PAYMENT_APPROVAL, () -> {
            throw new BusinessException("资方放款中，无法取消订单！");
        });

        Assert.notEquals(currentNode, States.FUNDS_FINAL_APPROVE, () -> {
            throw new BusinessException("资方审批中，无法取消订单！");
        });

        // gps是否已安装
        /*int gpsStatus = gpsService.checkOrderGpsStatus(orderId);
        if (gpsStatus == 1) {
            throw new BusinessException("gps安装中，无法取消订单！");
        } else if (gpsStatus == 2) {

            throw new BusinessException("已安装GPS，无法取消订单！");
        }*/

        // 取消资方订单
        // 富民： 未撤销不能再重新进件，额度有效期是30天，30天后额度会过期
        Result<String> stringResult = approveFeign.fundApproveCancel(new FundApproveCancelDTO().setType(2).setLinkId(orderId));
        if (!Result.isSuccess(stringResult)) {
            throw new BusinessException(stringResult.getMsg());
        }
        resetPreFundInfo(orderId);

        try {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
            if (Objects.equals(currentNode, States.RISK_FIRST_APPROVE)
                    || Objects.equals(currentNode, States.RISK_FIRST_APPROVE_ASSIGN)
                    || Objects.equals(currentNode, States.QUALITY_INSPECTION)
                    || (Objects.equals(currentNode, States.CUSTOMER_APPOINTMENT) && ObjUtil.equals(orderInfoEntity.getReviewState(), 1))
                    || Objects.equals(currentNode, States.PAYMENT_APPLY_INFORMATION)) {
                Integer node = currentNode.getNode();
                if (Objects.equals(currentNode, States.CUSTOMER_APPOINTMENT)) {
                    node = States.MANAGER_INTERVIEW.getNode();
                }
                orderApproveDistributeService.updateOrderApproveDistribute(new UpdateOrderApproveDistributeDTO()
                        .setOrderId(orderId)
                        .setOrderNumber(orderInfoEntity.getOrderNumber())
                        .setSource(0)
                        .setBusinessType(3)
                        .setCurrentNode(node));

            }
        } catch (Exception e) {
            log.error("OrderTerminationAction execute error", e);
        }
        return orderStateService.sendEvent(currentNode, Events.CANCEL, orderId, userId,
                new ApprovalSubmitDTO().setRemarkExternal(remark));
    }

    /**
     * 重置预审资方信息
     *
     * @param orderId 订单 ID
     */
    private void resetPreFundInfo(Integer orderId) {
        // 重置资方的预审记录
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId, OrderInfoEntity::getFundId, OrderInfoEntity::getPreId)
                .eq(OrderInfoEntity::getId, orderId));
        Integer fundId = orderInfoEntity.getFundId();
        Integer preId = orderInfoEntity.getPreId();
        if (Objects.equals(fundId, FundEnum.FU_MIN.getValue())) {

            PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                    .eq(PreFundInfoEntity::getPreId, preId)
                    .eq(PreFundInfoEntity::getFundId, FundEnum.FU_MIN.getValue())
                    .eq(PreFundInfoEntity::getDeleteFlag, 0));

            preFundInfoMapper.update(new LambdaUpdateWrapper<PreFundInfoEntity>()
                    .set(PreFundInfoEntity::getDeleteFlag, 1)
                    .eq(PreFundInfoEntity::getPreId, preId)
                    .eq(PreFundInfoEntity::getFundId, FundEnum.FU_MIN.getValue())
                    .eq(PreFundInfoEntity::getDeleteFlag, 0));
            if (ObjUtil.isNull(preFundInfoEntity)){
                return;
            }
            PreFundInfoEntity newPreFundInfoEntity = new PreFundInfoEntity();
            newPreFundInfoEntity.setPreId(preId);
            newPreFundInfoEntity.setFundId(FundEnum.FU_MIN.getValue());
            newPreFundInfoEntity.setResult(preFundInfoEntity.getResult());
            newPreFundInfoEntity.setLevel(preFundInfoEntity.getLevel());
            newPreFundInfoEntity.setRejectReason(preFundInfoEntity.getRejectReason());
            newPreFundInfoEntity.setEvaluationAmount(preFundInfoEntity.getEvaluationAmount());
            newPreFundInfoEntity.setCreditAmountFormula(preFundInfoEntity.getCreditAmountFormula());
            newPreFundInfoEntity.setCreditAmount(preFundInfoEntity.getCreditAmount());
            newPreFundInfoEntity.setRetry(0)
                    .setStatus(0)
                    .setFundResult(PreFundResultEnum.INIT);
            preFundInfoMapper.insert(newPreFundInfoEntity);

            approveFeign.fundApprovePre(new FundApprovePreDTO().setFund(FundEnum.getFundEnum(fundId)).setPreId(preId));

        }
    }

    /**
     * 批准 提交 验证
     *
     * @param submitDTO 提交 DTO
     * @return {@link SubmitValidateVO }
     */
    @Override
    public SubmitValidateVO approvalSubmitValidate(ApprovalSubmitValidateDTO submitDTO) {
        String lockKey = "ApprovalService:approvalSubmitValidate"+submitDTO.getOrderId();
        String requestId = IdUtil.randomUUID();
        Boolean tryLock = redisService.tryLock(lockKey, requestId, 30, TimeUnit.MINUTES);
        if (!tryLock) {
            throw new BusinessException("正在处理中，请稍后");
        }
        try {
            Integer orderId = submitDTO.getOrderId();
            States node = submitDTO.getNode();
            Integer result = submitDTO.getResult();
            Events events = Events.getEnum(result);
            SubmitValidateVO submitValidate = new SubmitValidateVO();
            List<SubmitValidateVO.TipsInfo> hardInfo = new ArrayList<>();
            List<SubmitValidateVO.TipsInfo> tipsInfo = new ArrayList<>();
            if (events == Events.AGREES || events == Events.AGREES_RISK_FIRST_SINGLE_AGREES) {
                if (Objects.equals(States.BUSINESS_ADDED_INFO.getNode(), node.getNode())) {
                    SubmitValidateVO riskSubmitValidate = this.riskSubmitValidate(orderId);
                    List<SubmitValidateVO.TipsInfo> riskHardInfo = riskSubmitValidate.getHardInfo();
                    if (CollUtil.isNotEmpty(riskHardInfo)) {
                        hardInfo.addAll(riskHardInfo);
                    }
                    List<SubmitValidateVO.TipsInfo> riskTipsInfo = riskSubmitValidate.getTipsInfo();
                    if (CollUtil.isNotEmpty(riskTipsInfo)) {
                        tipsInfo.addAll(riskTipsInfo);
                    }
                    //门店额度控制
                    SubmitValidateVO storeQuotaSubmitValidate = storeQuotaSubmitValidate(orderId);
                    if (ObjUtil.isNotNull(storeQuotaSubmitValidate) && CollUtil.isNotEmpty(storeQuotaSubmitValidate.getHardInfo())) {
                        hardInfo.addAll(storeQuotaSubmitValidate.getHardInfo());
                    } else {
                        //额度控制管控
                        SubmitValidateVO qualityInspectionSubmitValidate = qualityInspectionSubmitValidate(orderId);
                        List<SubmitValidateVO.TipsInfo> qualityInspectionHardInfo = qualityInspectionSubmitValidate.getHardInfo();
                        if (CollUtil.isNotEmpty(qualityInspectionHardInfo)) {
                            hardInfo.addAll(qualityInspectionHardInfo);
                        }
                        List<SubmitValidateVO.TipsInfo> qualityInspectionTipsInfo = qualityInspectionSubmitValidate.getTipsInfo();
                        if (CollUtil.isNotEmpty(qualityInspectionTipsInfo)) {
                            tipsInfo.addAll(qualityInspectionTipsInfo);
                        }
                    }
                }
            }
            submitValidate.setHardInfo(hardInfo);
            submitValidate.setTipsInfo(tipsInfo);
            return submitValidate;
        }finally {
            redisService.releaseLock(lockKey, requestId);
        }
    }

    private SubmitValidateVO storeQuotaSubmitValidate(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, () -> new BusinessException("未查询到订单信息"));

        // 获取当月第一天和最后一天
        LocalDate now = LocalDate.now();
        LocalDate startOfMonth = now.withDayOfMonth(1);
        LocalDate endOfMonth = now.withDayOfMonth(now.lengthOfMonth());

        StoreQuotaEntity storeQuotaEntity = storeQuotaMapper.selectOne(new LambdaQueryWrapper<StoreQuotaEntity>()
                .eq(StoreQuotaEntity::getDeptId, orderInfoEntity.getDeptId())
                .eq(StoreQuotaEntity::getFundId, orderInfoEntity.getFundId())
                .eq(StoreQuotaEntity::getRegionId, orderInfoEntity.getRegionId())
                .eq(StoreQuotaEntity::getDeleteFlag, 0)
                .eq(StoreQuotaEntity::getEnable, 0)
                .and(q -> q
                        .between(StoreQuotaEntity::getBeginDate, startOfMonth, endOfMonth)
                        .or()
                        .between(StoreQuotaEntity::getEndDate, startOfMonth, endOfMonth)
                        .or(q2 -> q2
                                .le(StoreQuotaEntity::getBeginDate, startOfMonth)
                                .ge(StoreQuotaEntity::getEndDate, endOfMonth)
                        )
                )
        );
        if (storeQuotaEntity == null){
            return new SubmitValidateVO();
        }
        OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                .eq(OrderAmountEntity::getOrderId, orderId)
                .eq(OrderAmountEntity::getDeleteFlag, 0)
                .orderByDesc(OrderAmountEntity::getCreateTime)
                .last("limit 1"));
        Assert.notNull(orderAmountEntity, () -> new BusinessException("未查询到订单金额信息"));
        BigDecimal preAmount = orderAmountEntity.getPreAmount();
        BigDecimal hopeAmount = orderAmountEntity.getHopeAmount();
        BigDecimal minValue = preAmount.min(hopeAmount);

        BigDecimal creditLine = storeQuotaService.getCreditLineStoreQuota(orderInfoEntity.getDeptId(), orderInfoEntity.getFundId(),orderInfoEntity.getRegionId(),startOfMonth, endOfMonth);
        BigDecimal  storeInitQuota = storeQuotaEntity.getInitQuota();
        // 计算剩余额度 = 门店初始额度 - 授信额度
        BigDecimal remainingQuota = storeInitQuota.subtract(creditLine);
        List<SubmitValidateVO.TipsInfo> hardInfoList = new ArrayList<>();
        if (minValue.compareTo(remainingQuota) > 0) {
            hardInfoList.add(new SubmitValidateVO.TipsInfo().setTitle("额度管控提示").setMsg("门店当前剩余额度不足！剩余可用额度为：" + remainingQuota + "（元）"));

        }
        return new SubmitValidateVO().setHardInfo(hardInfoList);
    }

    @Override
    public SubmitValidateVO qualityInspectionSubmitValidate(Integer orderId) {
        List<SubmitValidateVO.TipsInfo> hardInfoList = new ArrayList<>();
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, () -> new BusinessException("未查询到订单信息"));

        //移动到资料补录环节，风控额度校验调整成 预审批额度和客户申请额度两者取低值校验
        OrderAmountEntity orderAmount = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                .eq(OrderAmountEntity::getOrderId, orderId)
                .eq(OrderAmountEntity::getDeleteFlag, 0)
                .orderByDesc(OrderAmountEntity::getCreateTime)
                .last("limit 1"));
        Assert.notNull(orderAmount, () -> new BusinessException("未查询到订单金额信息"));
        BigDecimal preAmount = orderAmount.getPreAmount();
        BigDecimal hopeAmount = orderAmount.getHopeAmount();
        BigDecimal minValue = preAmount.min(hopeAmount);

        DeptQuotaRecordEntity deptQuotaRecordEntity = deptQuotaRecordMapper.selectOne(new LambdaQueryWrapper<DeptQuotaRecordEntity>()
                .eq(DeptQuotaRecordEntity::getOrderId, orderId)
                .eq(DeptQuotaRecordEntity::getAction, 0)
                .eq(DeptQuotaRecordEntity::getDeleteFlag, 0));
        if (ObjUtil.isNotNull(deptQuotaRecordEntity)) {
            DeptQuotaEntity deptQuotaEntity = deptQuotaMapper.selectById(deptQuotaRecordEntity.getDeptQuotaId());
            if (ObjUtil.isNull(deptQuotaEntity)) {
                deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                        .set(DeptQuotaRecordEntity::getAction, 1)
                        .set(DeptQuotaRecordEntity::getDeleteFlag, 1)
                        .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
                deptQuotaRecordEntity = null;
            } else {
               /* OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                        .eq(OrderAmountEntity::getOrderId, orderId)
                        .eq(OrderAmountEntity::getDeleteFlag, 0));*/
                LocalDate now = LocalDate.now();
                if (deptQuotaEntity.getBeginDate().isAfter(now)
                        || deptQuotaEntity.getEndDate().isBefore(now)
                        || deptQuotaEntity.getEnable() == 1
                        || deptQuotaRecordEntity.getQuota().compareTo(minValue) > 0
                ) {
                    int update = deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                            .setIncrBy(DeptQuotaEntity::getRemainingQuota, deptQuotaRecordEntity.getQuota())
                            .eq(DeptQuotaEntity::getId, deptQuotaRecordEntity.getDeptQuotaId())
                            .eq(DeptQuotaEntity::getDeptId, deptQuotaRecordEntity.getDeptId())
                            .eq(DeptQuotaEntity::getDeleteFlag, 0));
                    if (update > 0) {
                        deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                                .set(DeptQuotaRecordEntity::getAction, 1)
                                .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
                    } else {
                        log.error("ApprovalServiceImpl.qualityInspectionSubmitValidate evaluate update dept quota fail orderid:{}", orderId);
                        deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                                .set(DeptQuotaRecordEntity::getDeleteFlag, 1)
                                .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
                    }
                    deptQuotaRecordEntity = null;
                }
            }
        }
        if (ObjUtil.isNull(deptQuotaRecordEntity)) {
          /* OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                    .eq(OrderAmountEntity::getOrderId, orderId)
                    .eq(OrderAmountEntity::getDeleteFlag, 0)
            );
            Assert.notNull(orderAmountEntity, () -> new BusinessException("未查询到订单额度信息"));*/
            while (true) {
                LocalDate now = LocalDate.now();
                deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                        .set(DeptQuotaEntity::getEnable, 1)
                        .lt(DeptQuotaEntity::getEndDate, now)
                        .eq(DeptQuotaEntity::getEnable, 0)
                        .eq(DeptQuotaEntity::getDeleteFlag, 0));
                DeptQuotaEntity deptQuotaEntity = deptQuotaMapper.selectOne(new LambdaQueryWrapper<DeptQuotaEntity>()
                        .eq(DeptQuotaEntity::getDeptId, orderInfoEntity.getRegionId())
                        .le(DeptQuotaEntity::getBeginDate, now)
                        .ge(DeptQuotaEntity::getEndDate, now)
                        .eq(DeptQuotaEntity::getEnable, 0)
                        .eq(DeptQuotaEntity::getType, 0)
                        .eq(DeptQuotaEntity::getFundId, orderInfoEntity.getFundId())
                        .eq(DeptQuotaEntity::getDeleteFlag, 0)
                        .orderByAsc(DeptQuotaEntity::getEndDate), false);
                if (ObjUtil.isEmpty(deptQuotaEntity)) {
                    if (deptQuotaMapper.selectCount(new LambdaQueryWrapper<DeptQuotaEntity>()
                            .eq(DeptQuotaEntity::getFundId, orderInfoEntity.getFundId())
                            .eq(DeptQuotaEntity::getDeleteFlag, 0)) > 0) {
                        //判断禁用的情况下不限制
                        if (deptQuotaMapper.selectCount(new LambdaQueryWrapper<DeptQuotaEntity>()
                                .eq(DeptQuotaEntity::getDeptId, orderInfoEntity.getRegionId())
                                .le(DeptQuotaEntity::getBeginDate, now)
                                .ge(DeptQuotaEntity::getEndDate, now)
                                .eq(DeptQuotaEntity::getEnable, 1)
                                .eq(DeptQuotaEntity::getType, 0)
                                .eq(DeptQuotaEntity::getFundId, orderInfoEntity.getFundId())
                                .eq(DeptQuotaEntity::getDeleteFlag, 0)) > 0) {
                            break;
                        }
                        hardInfoList.add(new SubmitValidateVO.TipsInfo().setTitle("额度管控提示").setMsg("当前无可用额度！"));
                    }
                    break;
                }
                if (minValue.compareTo(deptQuotaEntity.getRemainingQuota()) > 0) {
                    hardInfoList.add(new SubmitValidateVO.TipsInfo().setTitle("额度管控提示").setMsg("当前剩余额度不足！剩余可用额度为：" + deptQuotaEntity.getRemainingQuota() + "（元）"));
                    break;
                }
                int update = deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                        .setDecrBy(DeptQuotaEntity::getRemainingQuota, minValue)
                        .eq(DeptQuotaEntity::getRemainingQuota, deptQuotaEntity.getRemainingQuota())
                        .eq(DeptQuotaEntity::getEnable, 0)
                        .eq(DeptQuotaEntity::getType, 0)
                        .eq(DeptQuotaEntity::getId, deptQuotaEntity.getId())
                        .eq(DeptQuotaEntity::getDeleteFlag, 0));
                if (update == 1) {
                    deptQuotaRecordEntity = new DeptQuotaRecordEntity()
                            .setDeptQuotaId(deptQuotaEntity.getId())
                            .setDeptId(deptQuotaEntity.getDeptId())
                            .setOrderId(orderId)
                            .setAction(0)
                            .setQuota(minValue);
                    deptQuotaRecordMapper.insert(deptQuotaRecordEntity);
                    break;
                }
            }
        }
        return new SubmitValidateVO().setHardInfo(hardInfoList);
    }

    /**
     * 风险 提交 验证
     *
     * @param orderId 订单ID
     * @return {@link SubmitValidateVO }
     */
    public SubmitValidateVO riskSubmitValidate(Integer orderId) {
        List<SubmitValidateVO.TipsInfo> tipsInfoList = new ArrayList<>();
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, "未查询到订单信息");
        OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                .eq(OrderAmountEntity::getOrderId, orderId)
                .eq(OrderAmountEntity::getDeleteFlag, 0)
        );
        Assert.notNull(orderAmountEntity, "未查询到订单信息");
        Integer fundId = orderInfoEntity.getFundId();
        BigDecimal riskAmount = orderAmountEntity.getRiskAmount();
        if (Objects.equals(FundEnum.YING_FENG.getValue(), fundId) && switchUtils.checkSwitch(SwitchConstants.RISK_APPROVE_AMOUNT)) {
            SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.RISK_APPROVE_AMOUNT);
            String remark = switchInfo.getRemark();
            String tips = switchInfo.getTips();
            BigDecimal value = new BigDecimal(switchInfo.getValue());
            BigDecimal calAmount = value.subtract(riskAmount);
            if (calAmount.compareTo(BigDecimal.ZERO) <= 0) {
                tipsInfoList.add(new SubmitValidateVO.TipsInfo().setTitle(remark).setMsg(tips));
            } else {
                switchService.updateSwitchValue(SwitchConstants.RISK_APPROVE_AMOUNT, calAmount.toString());
            }
        }
        return new SubmitValidateVO().setTipsInfo(tipsInfoList);
    }

    /**
     * 订单页面状态
     *
     * @param orderRecordDTO 订单记录 DTO
     * @return {@link List }<{@link OrderPageVO }>
     */
    @Override
    public List<OrderPageVO> orderPageStatus(OrderRecordDTO orderRecordDTO) {
        Integer node = orderRecordDTO.getNode();
        List<States> pageList;
        if (Objects.equals(States.CUSTOMER_APPOINTMENT.getNode(), node)) {
            pageList = List.of(States.MANAGER_INTERVIEW, States.CONTRACT_SIGNING, States.GPS_INSTALL_APPLY,
                    States.MORTGAGE_PENDING, States.GPS_FEE_PAYMENT);
        } else if (Objects.equals(States.BUSINESS_ADDED_INFO.getNode(), node)) {
            pageList = List.of(States.ADDED_BUSINESS_CUSTOMER, States.ADDED_BUSINESS_CAR, States.ADDED_BUSINESS_ORDER
                    , States.ADDED_BUSINESS_ATTACHMENT);
        } else if (Objects.equals(States.CUSTOMER_CONFIRM.getNode(), node)) {
            pageList = List.of(States.CUSTOMER_CONFIRM_ORDER, States.CUSTOMER_CONFIRM_IMAGE, States.CUSTOMER_CONFIRM_BANKCARD);
        } else {
            pageList = List.of(States.ADDED_BUSINESS_CUSTOMER, States.ADDED_BUSINESS_CAR, States.ADDED_BUSINESS_ORDER
                    , States.ADDED_BUSINESS_ATTACHMENT);
        }
        return orderPageInfoService.getOrderPageVOS(orderRecordDTO, pageList);
    }

    /**
     * 初始订单状态
     *
     * @param orderId 次序id
     */
    @Override
    public void initialOrderStates(Integer orderId) {
        orderStateService.initialStates(orderId);
    }

    /**
     * 列表
     *
     * @param preApprovalListDTO 预先批准清单 DTO
     * @param loginUserInfo      登录用户信息
     * @return {@link Page }<{@link PreApprovalListVO }>
     */
    @Override
    public IPage<PreApprovalListVO> list(PreApprovalListDTO preApprovalListDTO, LoginUser loginUserInfo) {
        log.info("SingleRepaymentServiceImpl,list,入参:preApprovalListDTO:{},loginUserInfo:{}", JSONUtil.toJsonStr(loginUserInfo));
        //                        .eq(PreApprovalApplyInfoEntity::getId, PreApprovalFddAuthEntity::getPreId)
        // .eq(PreApprovalApplyInfoEntity::getAccountManagerId, loginUserInfo.getUserId())
        //                .ne(PreApprovalApplyInfoEntity::getManagerState, PreApplyInfoManagerStatus.APPROVAL_SUCCESS)
        MPJLambdaWrapper<PreApprovalApplyInfoEntity> queryWrapper = new MPJLambdaWrapper<PreApprovalApplyInfoEntity>()
                .selectAs(PreApprovalApplyInfoEntity::getId, PreApprovalListVO::getId)
                .selectAs(PreApprovalApplyInfoEntity::getName, PreApprovalListVO::getName)
                .selectAs(PreApprovalApplyInfoEntity::getPhone, PreApprovalListVO::getPhoneNumber)
                .selectAs(PreApprovalApplyInfoEntity::getLoanPeriod, PreApprovalListVO::getTerm)
                .selectAs(PreApprovalApplyInfoEntity::getCreateTime, PreApprovalListVO::getApplyDate)
                .selectAs(PreApprovalApplyInfoEntity::getFundStatus, PreApprovalListVO::getFundStatus)
                .selectAs(PreApprovalApplyInfoEntity::getRiskStatus, PreApprovalListVO::getRiskStatus)
                .selectAs(PreApprovalApplyInfoEntity::getLoanAmount, PreApprovalListVO::getApplyAmount)
                .selectAs(PreApprovalApplyInfoEntity::getBusinessStatus, PreApprovalListVO::getBusinessStatus)
                .selectAs(PreApprovalApplyInfoEntity::getRegionId, PreApprovalListVO::getRegionId)
                .selectAs(PreApprovalApplyInfoEntity::getRegionName, PreApprovalListVO::getRegionName)
                .selectAs(PreApprovalFddAuthEntity::getAuthResult, PreApprovalListVO::getAuthResult)
                .selectAs(PreApprovalApplyInfoEntity::getSourceType, PreApprovalListVO::getSourceType)
                .selectAs(PreApprovalApplyInfoEntity::getCustomerLevel, PreApprovalListVO::getCustomerLevel)
                .selectAs(PreApprovalApplyInfoEntity::getManagerState,PreApprovalListVO::getManagerState)
                .select(PreApprovalApplyInfoEntity::getTeamId)

                .leftJoin(PreApprovalFddAuthEntity.class, on -> on
                        .eq(PreApprovalApplyInfoEntity::getIdNumber, PreApprovalFddAuthEntity::getIdNumber)
                        .eq(PreApprovalApplyInfoEntity::getPhone, PreApprovalFddAuthEntity::getPhone)
                        .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                        .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                )
                .leftJoin(OrderInfoEntity.class, on -> on .eq(PreApprovalApplyInfoEntity::getId, OrderInfoEntity::getPreId)
                        .notIn(OrderInfoEntity::getCurrentNode,States.PROCESS_TERMINAL.getNode(), States.SYSTEM_TERMINAL.getNode())
                        .eq(OrderInfoEntity::getDeleteFlag, 0))
                .innerJoin(PreOcrVehicleInfoEntity.class, on -> on .eq(PreApprovalApplyInfoEntity::getId, PreOcrVehicleInfoEntity::getPreId))
                .like(StringUtils.isNotBlank(preApprovalListDTO.getVehicleNumber()), PreOcrVehicleInfoEntity::getUpdateVehicleNumber, preApprovalListDTO.getVehicleNumber())
                .eq(PreApprovalApplyInfoEntity::getType, 0)
                .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
                .selectAs(PreFundInfoEntity::getFundCreditAmount, PreApprovalListVO::getFundPreAmount)
                .leftJoin(PreFundInfoEntity.class, on -> on
                        .eq(PreFundInfoEntity::getFundId,PreApprovalApplyInfoEntity::getFundId)
                        .eq(PreFundInfoEntity::getPreId, PreApprovalApplyInfoEntity::getId)
                        .eq(PreFundInfoEntity::getFundResult,PreFundResultEnum.PASS)
                        .isNotNull(PreApprovalApplyInfoEntity::getProductId)
                        .eq(PreFundInfoEntity::getDeleteFlag, 0))
                .eq(ObjUtil.isNotNull(preApprovalListDTO.getFundStatus()), PreApprovalApplyInfoEntity::getFundStatus, preApprovalListDTO.getFundStatus())
                .eq(ObjUtil.isNotNull(preApprovalListDTO.getRegionId()), PreApprovalApplyInfoEntity::getRegionId, preApprovalListDTO.getRegionId())
                //展示预审提交成功数据
                .eq(PreApprovalApplyInfoEntity::getBusinessStatus, ApprovalApplyInfoBusinessStatus.SUBMITTED_PRELIMINARY_APPROVAL.getCode())
                .like(StringUtils.isNotEmpty(preApprovalListDTO.getStoreName()), PreApprovalApplyInfoEntity::getStoreName, preApprovalListDTO.getStoreName())
                //.eq(ObjUtil.isNotNull(preApprovalListDTO.getTeamId()), PreApprovalApplyInfoEntity::getTeamId,preApprovalListDTO.getTeamId())

                .like(StringUtils.isNotBlank(preApprovalListDTO.getName()), PreApprovalApplyInfoEntity::getName,
                        preApprovalListDTO.getName())
                .like(StringUtils.isNotBlank(preApprovalListDTO.getPhoneNumber()),
                        PreApprovalApplyInfoEntity::getPhone, preApprovalListDTO.getPhoneNumber())
                //添加筛选条件
                .like(StringUtils.isNotBlank(preApprovalListDTO.getRegionName()), PreApprovalApplyInfoEntity::getRegionName, preApprovalListDTO.getRegionName())
                .in(CollUtil.isNotEmpty(preApprovalListDTO.getSource()), PreApprovalApplyInfoEntity::getSourceType, preApprovalListDTO.getSource())
//                .in(CollUtil.isNotEmpty(preApprovalListDTO.getRiskStatusList()),PreApprovalApplyInfoEntity::getRiskStatus,preApprovalListDTO.getRiskStatusList())
                .in(CollUtil.isNotEmpty(preApprovalListDTO.getFundStatusList()),PreApprovalApplyInfoEntity::getFundStatus,preApprovalListDTO.getFundStatusList())

                .between(preApprovalListDTO.getApplyStartDate()!=null&&preApprovalListDTO.getApplyEndDate()!=null,PreApprovalApplyInfoEntity::getCreateTime, preApprovalListDTO.getApplyStartDate(), preApprovalListDTO.getApplyEndDate())
                .orderByDesc(PreApprovalApplyInfoEntity::getCreateTime);
        if (ObjUtil.isNotNull(preApprovalListDTO.getRiskStatus())) {
            switch (preApprovalListDTO.getRiskStatus()) {
                //风控流程状态 0:未发起，1：风控推送，2：补数完成，3：风控规则，5：风控通过，6：风控拒绝
                case 1:
                    queryWrapper.in(PreApprovalApplyInfoEntity::getRiskStatus, Arrays.asList(1, 2, 3));
                case 2:
                    queryWrapper.eq(PreApprovalApplyInfoEntity::getRiskStatus, 5);
                    break;
                case 3:
                    queryWrapper.eq(PreApprovalApplyInfoEntity::getRiskStatus, 6);
                    break;
                default:
                    queryWrapper.eq(PreApprovalApplyInfoEntity::getRiskStatus, 0);
                    break;
            }
        }
        if (CollUtil.isNotEmpty(preApprovalListDTO.getRiskStatusList())){
            Set<Integer> riskStatusList =new HashSet<>();
            preApprovalListDTO.getRiskStatusList().forEach( res ->{
                switch ( res){
                    case 1 -> riskStatusList.addAll(Arrays.asList(1, 2, 3));
                    case 2 -> riskStatusList.add(5);
                    case 3 -> riskStatusList.add(6);
                    default -> riskStatusList.add(0);
                }
            });
            if(CollUtil.isNotEmpty(riskStatusList)){
                queryWrapper.in(PreApprovalApplyInfoEntity::getRiskStatus, riskStatusList.stream().toList());
            }
        }
        String scopes = loginUserInfo.getScopes();
        if (StrUtil.isNotEmpty(scopes) && StrUtil.contains(scopes, "system:pc")) {
            //添加pc端字段
            queryWrapper.selectAs(PreApprovalApplyInfoEntity::getIdNumber, PreApprovalListVO::getIdNumber)
                    .selectAs(PreOcrVehicleInfoEntity::getUpdateVehicleNumber, PreApprovalListVO::getVehicleNumber)
                    .selectAs(PreApprovalApplyInfoEntity::getStoreId, PreApprovalListVO::getStoreId)
                    .selectAs(PreApprovalApplyInfoEntity::getAccountManagerId, PreApprovalListVO::getAccountManagerId);
        }
        //姓名、手机号、车牌号、订单号
        if (StrUtil.isNotBlank(preApprovalListDTO.getQueryMore())) {
            String queryMore = preApprovalListDTO.getQueryMore().trim();
            String small = queryMore.toUpperCase();
            queryWrapper.and(wp -> wp.like(PreApprovalApplyInfoEntity::getName, queryMore)
                    .or()
                    .like(PreApprovalApplyInfoEntity::getPhone, queryMore)
                    .or()
                    .like(PreApprovalApplyInfoEntity::getIdNumber, queryMore)
                    .or()
                    .like(PreOcrVehicleInfoEntity::getVehicleNumber, small)
            );
        }
        // 数据权限控制
        dataPermissionService.limitPreApproval(loginUserInfo, queryWrapper);

        Page<PreApprovalListVO> listVOPage =
                preApprovalApplyInfoMapper.selectJoinPage(new Page<>(preApprovalListDTO.getPageNum(),
                                preApprovalListDTO.getPageSize()), PreApprovalListVO.class,
                        queryWrapper
                );

        List<PreApprovalListVO> records = listVOPage.getRecords();
        List<Integer> roleIds = loginUserInfo.getRoleIds();
        boolean showCreditMatchRule = RoleEnum.RISK_ROLE.hasRole(roleIds)
                || RoleEnum.SYS_ADMIN.hasRole(roleIds) || RoleEnum.BIZ_SUPER_ADMIN.hasRole(roleIds);

        records.forEach(item -> {
            boolean flag = Objects.equals(item.getSourceType(), 1) && (Objects.equals(item.getRegionId(),24) || Objects.equals(item.getRegionId(),56));
            item.setIsOnlineOrder(flag ? 1 : 2);
            //风控拒绝时显示拒绝原因
            if (ObjUtil.isNotNull(item) && ObjUtil.isNotNull(item.getRiskStatus()) && Objects.equals(item.getRiskStatus(), PreRiskStatusEnum.RISK_DENIED.getCode())) {
                List<PreRiskPolicyResultEntity> riskResultList = preRiskPolicyResultMapper.selectList(new LambdaQueryWrapper<PreRiskPolicyResultEntity>()
                        .eq(PreRiskPolicyResultEntity::getPreId, item.getId())
                        .eq(PreRiskPolicyResultEntity::getDeleteFlag, 0)
                        .in(PreRiskPolicyResultEntity::getResult, RiskPolicyResult.REJECT, RiskPolicyResult.WARN)
                );
                if (CollUtil.isNotEmpty(riskResultList)) {
                    String riskRefuseReason = riskResultList.stream().map(PreRiskPolicyResultEntity::getSuggestion).collect(Collectors.joining(","));
                    item.setRiskRefuseReason(riskRefuseReason);
                    if (showCreditMatchRule) {
                        riskResultList.stream().filter(entity ->
                                "征信规则".equals(entity.getPolicyName())).findFirst().ifPresent(entity -> {

                                    String funds = entity.getFunds();

                                    List<FundProductVO> list = JSONUtil.toList(funds, FundProductVO.class);
                                    String creditMatchRule = list.stream()
                                            .filter(vo -> Objects.equals(vo.getResult(), FundProductResult.REJECT))
                                            .map(fundProductVO -> {
                                                String matchRule = fundProductVO.getMatchRule().stream().map(rule ->
                                                        dictService.getDictLabel(GlobalConstants.DictType.PRE_RISK_FUND_SUGGESTION, rule)
                                                ).filter(Objects::nonNull).collect(Collectors.joining("<br/>"));

                                                if (StrUtil.isNotBlank(matchRule)) {
                                                    matchRule = "【" + fundProductVO.getFundName() + "】<br/>" + matchRule;
                                                    return matchRule;
                                                }
                                                return null;
                                            })
                                            .filter(Objects::nonNull)
                                            .collect(Collectors.joining("<br/>"));

                                    item.setCreditMatchRule(creditMatchRule);

                                }

                        );
                    }

                }
            } else if (Objects.equals(item.getFundStatus(), PreApplyInfoFundStatus.APPROVAL_FAIL.getCode())) {
                preFundInfoMapper.selectList(new LambdaQueryWrapper<PreFundInfoEntity>()
                        .eq(PreFundInfoEntity::getPreId, item.getId())
                        .eq(PreFundInfoEntity::getFundId, item.getFundId())
                        .eq(PreFundInfoEntity::getFundResult, PreFundResultEnum.REJECT)
                ).stream().findFirst().ifPresent(fundInfo -> item.setRiskRefuseReason(fundInfo.getFundRemark()));
            }

            Integer customerLevel = item.getCustomerLevel();
            if (customerLevel != null) {
                String customerScore = switch (customerLevel) {
                    case 1 -> "A";
                    case 3 -> "C";
                    default -> "B";
                };
                item.setCustomerScore(customerScore);
            }

            PreRiskStatusEnum status = PreRiskStatusEnum.fromValue(item.getRiskStatus());
            switch (status) {
                case NOT_INITIATED -> item.setRiskStatus(PreRiskStatusEnum.NOT_INITIATED.getCode());
                case RISK_PUSHED, DATA_COMPLETED, RISK_RULE ->
                        item.setRiskStatus(PreRiskStatusEnum.RISK_PUSHED.getCode());
                case RISK_APPROVED -> item.setRiskStatus(PreRiskStatusEnum.DATA_COMPLETED.getCode());
                case RISK_DENIED -> item.setRiskStatus(PreRiskStatusEnum.RISK_RULE.getCode());
                default -> {
                    // 可以在这里添加日志记录或其他异常处理
                }
            }
            //蓝海添加 风控命中借贷纠纷
//            item.setRiskPreWarn(getPreJzyShesu(item.getId()));
            item.setRiskPreWarn("");
            item.setPreStatus(PreListStatusEnum.PENDING_PRE_TRIAL);

            //如果客户经理预审批状态是 "已通过" 就直接显示预审通过
            if (item.getManagerState().equals(PreApplyInfoManagerStatus.APPROVAL_SUCCESS)){
                item.setPreStatus(PreListStatusEnum.APPROVAL_SUCCESS);
            }else {
                //查询是否有进行中订单
                if (ObjUtil.notEqual(item.getBusinessStatus(),ApprovalApplyInfoBusinessStatus.SUBMITTED_PRELIMINARY_APPROVAL)){
                    item.setPreStatus(PreListStatusEnum.TO_BE_ENTERED);
                }else if (ObjUtil.notEqual(item.getAuthResult(),"success")){
                    item.setPreStatus(PreListStatusEnum.TO_BE_AUTHORIZED);
                }else if (ObjUtil.isNull(item.getOrderId())){
                    item.setPreStatus(PreListStatusEnum.PENDING_PRE_TRIAL);
                }else if (!Objects.equals(item.getRiskStatus(),PreRiskStatusEnum.DATA_COMPLETED.getCode())){
                    //系统预审
                    item.setPreStatus(PreListStatusEnum.PENDING_PRE_TRIAL);
                }else if (!Objects.equals(item.getFundStatus(),PreApplyInfoFundStatus.APPROVAL_SUCCESS.getCode())){
                    //资方预审
                    item.setPreStatus(PreListStatusEnum.PENDING_PRE_CAPITAL);
                }else {
                    //选择产品
                    item.setPreStatus(PreListStatusEnum.PRODUCTS_TO_BE_SELECTED);
                }

            }
        });


        if (StrUtil.isNotBlank(scopes) && StrUtil.contains(scopes, "system:pc")) {
            //添加门店及业务员
            List<Integer> storeIds = records.stream().map(PreApprovalListVO::getStoreId).toList();
            List<Integer> teamIds = records.stream().map(PreApprovalListVO::getTeamId).filter(Objects::nonNull).toList();
            List<DeptInfoVO> branchNameVOS = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData();
            List<UserStoreVO> userStoreVOS = userFeign.getUserStoreVoByDeptIds(storeIds).getData();
            List<Integer> managerIds = records.stream().map(PreApprovalListVO::getAccountManagerId).toList();
            List<UserInfoVO> managerInfos = userFeign.searchUserNameBatch(managerIds).getData();
            Map<Integer, String> storeNameMap = userStoreVOS.stream().filter(userStoreVO -> userStoreVO.getStoreId() != null)
                    .collect(Collectors.toMap(UserStoreVO::getStoreId, UserStoreVO::getStore, (v1, v2) -> v1));
            Map<Integer, String> managerNameMap = managerInfos.stream().collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getName, (v1, v2) -> v1));
            Map<Integer, String> branchNameMap = branchNameVOS.stream().filter(e -> ObjUtil.isNotNull(e.getName())).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
            records.forEach(record -> {
                record.setStoreName(storeNameMap.get(record.getStoreId()));
                record.setAccountManagerName(managerNameMap.get(record.getAccountManagerId()));
                record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(branchNameMap.get(record.getTeamId()), ""));
            });
            //根据筛选条件筛选业务员
            records.removeIf(item -> StringUtils.isNotBlank(preApprovalListDTO.getAccountManagerName())&&!item.getAccountManagerName().contains(preApprovalListDTO.getAccountManagerName()));
        }
        if (StringUtils.isNotEmpty(preApprovalListDTO.getStoreDivision())) {
            Result<List<DeptDetailVO>> deptDetailVO = userFeign.getDeptDetailVO(preApprovalListDTO.getStoreDivision());
            if (Result.isSuccess(deptDetailVO) && !CollectionUtils.isEmpty(deptDetailVO.getData())) {
                List<Integer> ids = deptDetailVO.getData().stream()
                        .map(DeptDetailVO::getId)
                        .toList();
                List<PreApprovalListVO> filteredList = records.stream()
                        .filter(preApprovalListVO -> ids.contains(preApprovalListVO.getTeamId()))
                        .toList();
                if (!CollectionUtils.isEmpty(filteredList)) {
                    listVOPage.setRecords(filteredList);
                    listVOPage.setTotal(filteredList.size());
                    return listVOPage;
                } else {
                    return new Page<>();
                }
            } else {
                return new Page<>();
            }
        }
        return listVOPage;
    }

    /**
     * 获取除申请人外其他信息
     *
     * @param preId 预申请id
     * @return {@link PreApprovalApplyInfoVO}
     */
    @Override
    public PreApprovalApplyInfoVO queryByOtherInfo(Integer preId) {
        PreApprovalApplyInfoEntity preApprovalApplyInfo =
                preApprovalApplyInfoMapper.selectOne(new LambdaQueryWrapper<PreApprovalApplyInfoEntity>().eq(PreApprovalApplyInfoEntity::getSourceId, preId).ne(PreApprovalApplyInfoEntity::getType, 0));
        return approvalApplyInfoConverter.entity2Vo(preApprovalApplyInfo);
    }

    /**
     * 获取申请人信息
     *
     * @param preId 预申请id
     * @return {@link PreApprovalApplyInfoVO}
     */
    @Override
    public PreApprovalApplyInfoVO queryById(Integer preId) {
        PreApprovalApplyInfoEntity preApprovalApplyInfo = preApprovalApplyInfoMapper.selectById(preId);
        PreApprovalApplyInfoVO preApprovalApplyInfoVO = approvalApplyInfoConverter.entity2Vo(preApprovalApplyInfo);
        if (ObjUtil.isNotNull(preApprovalApplyInfoVO)&&ObjUtil.isNotNull(preApprovalApplyInfoVO.getCustomerLevel())){
            switch (preApprovalApplyInfoVO.getCustomerLevel()){
                case 1:
                    preApprovalApplyInfoVO.setCustomerLevelName("A");
                    break;
                case 3:
                    preApprovalApplyInfoVO.setCustomerLevelName("C");
                    break;
                case 2:
                    preApprovalApplyInfoVO.setCustomerLevelName("B");
                    break;
            }
        }
        return preApprovalApplyInfoVO;
    }

    /**
     * 批量更新资方终审结果
     *
     * @return {@link String }
     */
    @Override
    public String batchUpdateFundsFinalResult() {

        List<OrderInfoEntity> orderInfoEntities = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId, OrderInfoEntity::getCurrentNode, OrderInfoEntity::getManagementConclusion)
                .eq(OrderInfoEntity::getCurrentNode, States.FUNDS_FINAL_APPROVE.getNode())
                .eq(OrderInfoEntity::getDeleteFlag, 0)
        );
        log.info("批量更新资方终审结果，共{}条", orderInfoEntities.size());
        for (OrderInfoEntity orderInfoEntity : orderInfoEntities) {
            Integer managementConclusion = orderInfoEntity.getManagementConclusion();
            Integer orderId = orderInfoEntity.getId();
            log.info("更新资方终审结果，订单id:{},managementConclusion:{}", orderId, managementConclusion);
            try {

                if (Objects.equals(managementConclusion, 1)) {
                    orderStateService.sendEvent(States.FUNDS_FINAL_APPROVE, Events.AGREES, orderId, 1);

                } else if (Objects.equals(managementConclusion, 2)) {

                    FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                            .select(FinalFundInfoEntity::getId, FinalFundInfoEntity::getFundRemark)
                            .eq(FinalFundInfoEntity::getOrderId, orderId)
                            .orderByDesc(FinalFundInfoEntity::getCreateTime)
                            .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                            .last("limit 1")
                    );

                    if (finalFundInfoEntity != null) {
                        log.info("更新资方终审结果，订单id:{},fundRemark:{}", orderId,
                                finalFundInfoEntity.getFundRemark());
                        orderStateService.sendEvent(States.FUNDS_FINAL_APPROVE, Events.REJECT, orderId, 1
                                , new ApprovalSubmitDTO().setRemarkExternal(finalFundInfoEntity.getFundRemark()));
                    }
                } else if (Objects.equals(managementConclusion, 3)) {
                    FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                            .select(FinalFundInfoEntity::getId, FinalFundInfoEntity::getFundRemark)
                            .eq(FinalFundInfoEntity::getOrderId, orderId)
                            .orderByDesc(FinalFundInfoEntity::getCreateTime)
                            .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                            .last("limit 1")
                    );
                    String fundRemark;
                    if (finalFundInfoEntity != null) {
                        fundRemark = finalFundInfoEntity.getFundRemark();
                    } else {
                        fundRemark = "资方终审驳回";
                    }
                    orderStateService.sendEvent(States.FUNDS_FINAL_APPROVE, Events.BACK, orderId, 1
                            , new ApprovalSubmitDTO().setRemarkExternal(fundRemark));
                }

            } catch (Exception e) {
                log.error("批量更新资方终审结果失败，订单id:{}", orderId, e);
            }
        }

        return "ok";
    }

    /**
     * 订单状态
     *
     * @param orderIdDTO 订单 ID DTO
     * @return {@link OrderApprovalStatusVO }
     */
    @Override
    public OrderApprovalStatusVO orderStatus(OrderIdDTO orderIdDTO) {
        Assert.notNull(orderIdDTO.getOrderId(), "订单id不能为空");
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderIdDTO.getOrderId());

        if (Objects.nonNull(orderInfoEntity)) {
            return new OrderApprovalStatusVO()
                    .setPreId(orderInfoEntity.getPreId())
                    .setCurrentNode(orderInfoEntity.getCurrentNode())
                    .setNodeName(States.getNode(orderInfoEntity.getCurrentNode()).getDesc());
        }
        return new OrderApprovalStatusVO();
    }

    /**
     * 订单结清
     */
    @Override
    public SubmitResultVO orderSettled(OrderIdDTO orderIdDTO) {
        Assert.notNull(orderIdDTO.getOrderId(), "订单id不能为空");

        ApprovalSubmitDTO submitDTO = new ApprovalSubmitDTO();

        return orderStateService.sendEvent(States.PAYMENT_SUCCESS, Events.SETTLED, orderIdDTO.getOrderId(), 1, submitDTO);

    }

    @Override
    public void exportList(PreApprovalListDTO preApprovalListDTO, LoginUser loginUserInfo, HttpServletResponse response) {
        MPJLambdaWrapper<PreApprovalApplyInfoEntity> queryWrapper = buildQueryWrapper(preApprovalListDTO, loginUserInfo);

        String fileName = "预审列表.xlsx";
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(outputStream, PreApprovalExportListVO.class).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();

        Page<PreApprovalExportListVO> page = new Page<>(1, PAGE_SIZE);
        boolean hasMoreData = true;
        while (hasMoreData) {
            IPage<PreApprovalExportListVO> pageData = preApprovalApplyInfoMapper.selectJoinPage(
                    page, PreApprovalExportListVO.class,
                    queryWrapper);
            List<PreApprovalExportListVO> records = pageData.getRecords();
            if (records.isEmpty()) {
                hasMoreData = false;
                break;
            }

            processRecords(records, loginUserInfo, preApprovalListDTO);
            excelWriter.write(records, writeSheet);
            page.setCurrent(page.getCurrent() + 1);
        }
        // 获取Workbook对象
        Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
        // 获取Sheet对象
        Sheet sheet = workbook.getSheetAt(0);
        // 执行额外的Excel创建操作（如果有）
        createExcel(sheet, workbook);
        excelWriter.finish();
        byte[] bytes = outputStream.toByteArray();

        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + URLEncoder.encode(fileName, StandardCharsets.UTF_8) + "\"");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        try (ServletOutputStream outputStream1 = response.getOutputStream()) {
            outputStream1.write(bytes);
            outputStream1.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private MPJLambdaWrapper<PreApprovalApplyInfoEntity> buildQueryWrapper(PreApprovalListDTO preApprovalListDTO, LoginUser loginUserInfo) {
        MPJLambdaWrapper<PreApprovalApplyInfoEntity> queryWrapper = new MPJLambdaWrapper<PreApprovalApplyInfoEntity>()
                .selectAs(PreApprovalApplyInfoEntity::getId, PreApprovalExportListVO::getId)
                .selectAs(PreApprovalApplyInfoEntity::getName, PreApprovalExportListVO::getName)
                .selectAs(PreApprovalApplyInfoEntity::getPhone, PreApprovalExportListVO::getPhoneNumber)
                .selectAs(PreApprovalApplyInfoEntity::getLoanAmount, PreApprovalExportListVO::getApplyAmount)
                .selectAs(PreApprovalApplyInfoEntity::getLoanPeriod, PreApprovalExportListVO::getTerm)
                .selectAs(PreApprovalApplyInfoEntity::getCreateTime, PreApprovalExportListVO::getApplyDate)
                .selectAs(PreApprovalApplyInfoEntity::getFundStatus, PreApprovalExportListVO::getFundStatus)
                .selectAs(PreApprovalApplyInfoEntity::getRiskStatus, PreApprovalExportListVO::getRiskStatus)
                .selectAs(PreApprovalApplyInfoEntity::getBusinessStatus, PreApprovalExportListVO::getBusinessStatus)
                .selectAs(PreApprovalApplyInfoEntity::getRegionId, PreApprovalExportListVO::getRegionId)
                .selectAs(PreApprovalApplyInfoEntity::getRegionName, PreApprovalExportListVO::getRegionName)
                .select(PreApprovalApplyInfoEntity::getTeamId)

                .leftJoin(PreApprovalFddAuthEntity.class, on -> on
                        .eq(PreApprovalApplyInfoEntity::getIdNumber, PreApprovalFddAuthEntity::getIdNumber)
                        .eq(PreApprovalApplyInfoEntity::getPhone, PreApprovalFddAuthEntity::getPhone)
                        .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                        .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0))
                .innerJoin(PreOcrVehicleInfoEntity.class, on -> on
                        .eq(PreApprovalApplyInfoEntity::getId, PreOcrVehicleInfoEntity::getPreId))
                .like(StringUtils.isNotBlank(preApprovalListDTO.getVehicleNumber()), PreOcrVehicleInfoEntity::getUpdateVehicleNumber, preApprovalListDTO.getVehicleNumber())
                .eq(PreApprovalApplyInfoEntity::getType, 0)
                .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)

                .eq(ObjUtil.isNotNull(preApprovalListDTO.getFundStatus()), PreApprovalApplyInfoEntity::getFundStatus, preApprovalListDTO.getFundStatus())
                .eq(ObjUtil.isNotNull(preApprovalListDTO.getRegionId()), PreApprovalApplyInfoEntity::getRegionId, preApprovalListDTO.getRegionId())
                // 展示预审提交成功数据
                .eq(PreApprovalApplyInfoEntity::getBusinessStatus, ApprovalApplyInfoBusinessStatus.SUBMITTED_PRELIMINARY_APPROVAL.getCode())
                .like(StringUtils.isNotEmpty(preApprovalListDTO.getStoreName()), PreApprovalApplyInfoEntity::getStoreName, preApprovalListDTO.getStoreName())
                .like(StringUtils.isNotBlank(preApprovalListDTO.getName()), PreApprovalApplyInfoEntity::getName, preApprovalListDTO.getName())
                .like(StringUtils.isNotBlank(preApprovalListDTO.getPhoneNumber()), PreApprovalApplyInfoEntity::getPhone, preApprovalListDTO.getPhoneNumber())
                .orderByDesc(PreApprovalApplyInfoEntity::getCreateTime);

        if (ObjUtil.isNotNull(preApprovalListDTO.getRiskStatus())) {
            switch (preApprovalListDTO.getRiskStatus()) {
                // 风控流程状态 0:未发起，1：风控推送，2：补数完成，3：风控规则，5：风控通过，6：风控拒绝
                case 1:
                    queryWrapper.in(PreApprovalApplyInfoEntity::getRiskStatus, Arrays.asList(1, 2, 3));
                    break;
                case 2:
                    queryWrapper.eq(PreApprovalApplyInfoEntity::getRiskStatus, 5);
                    break;
                case 3:
                    queryWrapper.eq(PreApprovalApplyInfoEntity::getRiskStatus, 6);
                    break;
                default:
                    queryWrapper.eq(PreApprovalApplyInfoEntity::getRiskStatus, 0);
                    break;
            }
        }

        String scopes = loginUserInfo.getScopes();
        if (StrUtil.isNotEmpty(scopes) && StrUtil.contains(scopes, "system:pc")) {
            // 添加pc端字段
            queryWrapper.selectAs(PreApprovalApplyInfoEntity::getIdNumber, PreApprovalExportListVO::getIdNumber)
                    .selectAs(PreOcrVehicleInfoEntity::getUpdateVehicleNumber, PreApprovalExportListVO::getVehicleNumber)
                    .selectAs(PreApprovalApplyInfoEntity::getStoreId, PreApprovalExportListVO::getStoreId)
                    .selectAs(PreApprovalApplyInfoEntity::getAccountManagerId, PreApprovalExportListVO::getAccountManagerId)
                    .leftJoin(PreOcrVehicleInfoEntity.class, PreOcrVehicleInfoEntity::getPreId, PreApprovalApplyInfoEntity::getId);
        }

        // 数据权限控制
        dataPermissionService.limitPreApproval(loginUserInfo, queryWrapper);

        return queryWrapper;
    }

    private void processRecords(List<PreApprovalExportListVO> records, LoginUser loginUserInfo, PreApprovalListDTO preApprovalListDTO) {
        List<Integer> roleIds = loginUserInfo.getRoleIds();
        boolean showCreditMatchRule = RoleEnum.RISK_ROLE.hasRole(roleIds)
                || RoleEnum.SYS_ADMIN.hasRole(roleIds) || RoleEnum.BIZ_SUPER_ADMIN.hasRole(roleIds);
        String scopes = loginUserInfo.getScopes();
        records.forEach(item -> {
            //风控拒绝时显示拒绝原因
            if (Objects.equals(item.getRiskStatus(), PreRiskStatusEnum.RISK_DENIED.getCode())) {
                List<PreRiskPolicyResultEntity> riskResultList = preRiskPolicyResultMapper.selectList(new LambdaQueryWrapper<PreRiskPolicyResultEntity>()
                        .eq(PreRiskPolicyResultEntity::getPreId, item.getId())
                        .eq(PreRiskPolicyResultEntity::getDeleteFlag, 0)
                        .in(PreRiskPolicyResultEntity::getResult, RiskPolicyResult.REJECT, RiskPolicyResult.WARN)
                );
                if (CollUtil.isNotEmpty(riskResultList)) {
                    String riskRefuseReason = riskResultList.stream().map(PreRiskPolicyResultEntity::getSuggestion).collect(Collectors.joining(","));
                    item.setRiskRefuseReason(riskRefuseReason);
                    if (showCreditMatchRule) {
                        riskResultList.stream().filter(entity ->
                                "征信规则".equals(entity.getPolicyName())).findFirst().ifPresent(entity -> {

                                    String funds = entity.getFunds();

                                    List<FundProductVO> list = JSONUtil.toList(funds, FundProductVO.class);
                                    String creditMatchRule = list.stream()
                                            .filter(vo -> Objects.equals(vo.getResult(), FundProductResult.REJECT))
                                            .map(fundProductVO -> {
                                                String matchRule = fundProductVO.getMatchRule().stream().map(rule ->
                                                        dictService.getDictLabel(GlobalConstants.DictType.PRE_RISK_FUND_SUGGESTION, rule)
                                                ).collect(Collectors.joining("<br/>"));

                                                if (StrUtil.isNotBlank(matchRule)) {
                                                    matchRule = "【" + fundProductVO.getFundName() + "】<br/>" + matchRule;
                                                    return matchRule;
                                                }
                                                return null;
                                            })

                                            .collect(Collectors.joining("<br/>"));

                                    item.setCreditMatchRule(creditMatchRule);

                                }

                        );
                    }

                }
            } else if (Objects.equals(item.getFundStatus(), PreApplyInfoFundStatus.APPROVAL_FAIL.getCode())) {
                preFundInfoMapper.selectList(new LambdaQueryWrapper<PreFundInfoEntity>()
                        .eq(PreFundInfoEntity::getPreId, item.getId())
                        .eq(PreFundInfoEntity::getFundId, item.getFundId())
                        .eq(PreFundInfoEntity::getFundResult, PreFundResultEnum.REJECT)
                ).stream().findFirst().ifPresent(fundInfo -> item.setRiskRefuseReason(fundInfo.getFundRemark()));
            }
            PreRiskStatusEnum status = PreRiskStatusEnum.fromValue(item.getRiskStatus());
            switch (status) {
                case NOT_INITIATED -> item.setRiskStatus(PreRiskStatusEnum.NOT_INITIATED.getCode());
                case RISK_PUSHED, DATA_COMPLETED, RISK_RULE ->
                        item.setRiskStatus(PreRiskStatusEnum.RISK_PUSHED.getCode());
                case RISK_APPROVED -> item.setRiskStatus(PreRiskStatusEnum.DATA_COMPLETED.getCode());
                case RISK_DENIED -> item.setRiskStatus(PreRiskStatusEnum.RISK_RULE.getCode());
                default -> {
                    // 可以在这里添加日志记录或其他异常处理
                }
            }
        });


        if (StrUtil.isNotBlank(scopes) && StrUtil.contains(scopes, "system:pc")) {
            //添加门店及业务员
            List<Integer> storeIds = records.stream().map(PreApprovalExportListVO::getStoreId).toList();
            List<Integer> teamIds = records.stream().map(PreApprovalExportListVO::getTeamId).filter(Objects::nonNull).toList();
            List<DeptInfoVO> branchNameVOS = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData();
            List<UserStoreVO> userStoreVOS = userFeign.getUserStoreVoByDeptIds(storeIds).getData();
            List<Integer> managerIds = records.stream().map(PreApprovalExportListVO::getAccountManagerId).toList();
            List<UserInfoVO> managerInfos = userFeign.searchUserNameBatch(managerIds).getData();
            Map<Integer, String> storeNameMap = userStoreVOS.stream().filter(userStoreVO -> userStoreVO.getStoreId() != null)
                    .collect(Collectors.toMap(UserStoreVO::getStoreId, UserStoreVO::getStore, (v1, v2) -> v1));
            Map<Integer, String> managerNameMap = managerInfos.stream().collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getName, (v1, v2) -> v1));
            Map<Integer, String> branchNameMap = branchNameVOS.stream().filter(e -> ObjUtil.isNotNull(e.getName())).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
            records.forEach(record -> {
                record.setStoreName(storeNameMap.get(record.getStoreId()));
                record.setAccountManagerName(managerNameMap.get(record.getAccountManagerId()));
                record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(branchNameMap.get(record.getTeamId()), ""));
            });
        }
        records.forEach(item -> {
            switch (item.getRiskStatus()) {
                case 0:
                    item.setRiskStatusStr("未发起");
                    break;
                case 1:
                    item.setRiskStatusStr("审批中");
                    break;
                case 2:
                    item.setRiskStatusStr("审批通过");
                    break;
                case 3:
                    item.setRiskStatusStr("审批拒绝");
                    break;
                default:
                    item.setRiskStatusStr("");
                    break;
            }
            switch (item.getFundStatus()) {
                case 0:
                    item.setFundStatusStr("未发起");
                    break;
                case 1:
                    item.setFundStatusStr("审批中");
                    break;
                case 2:
                    item.setFundStatusStr("审批通过");
                    break;
                case 3:
                    item.setFundStatusStr("审批拒绝");
                    break;
                default:
                    item.setFundStatusStr("");
                    break;
            }
            SimpleDateFormat sdf = new SimpleDateFormat(DatePattern.NORM_DATETIME_PATTERN);
            item.setApplyDateStr(sdf.format(item.getApplyDate()));
        });

        if (StringUtils.isNotEmpty(preApprovalListDTO.getStoreDivision())) {
            Result<List<DeptDetailVO>> deptDetailVO = userFeign.getDeptDetailVO(preApprovalListDTO.getStoreDivision());
            if (Result.isSuccess(deptDetailVO) && !CollectionUtils.isEmpty(deptDetailVO.getData())) {
                List<Integer> ids = deptDetailVO.getData().stream()
                        .map(DeptDetailVO::getId)
                        .toList();

                List<PreApprovalExportListVO> filteredList = records.stream()
                        .filter(preApprovalListVO -> ids.contains(preApprovalListVO.getTeamId()))
                        .toList();

                if (!CollectionUtils.isEmpty(filteredList)) {
                    records.clear();
                    records.addAll(filteredList);
                } else {
                    records.clear();
                }
            } else {
                records.clear();
            }
        }
    }

    @Override
    public List<RegionInfoVO> regionList() {
        return userFeign.regionList();
    }

    @Override
    public ResponseEntity<byte[]> userToAuth(Integer preId, LoginUser loginUser) {
        log.info("ApprovalServiceImpl.userToAuth preId = {}", preId);
        Integer userId = loginUser.getUserId();
        byte[] imageBytes;
        try {
            // 通用验证
            org.springframework.util.Assert.notNull(preId, "预审id不能为空");
            PreApprovalApplyInfoEntity applyInfoEntity = preApprovalApplyInfoMapper.selectById(preId);
            org.springframework.util.Assert.notNull(applyInfoEntity, "预审信息不存在");

            // 设置header
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.setContentDisposition(ContentDisposition.builder("attachment")
                    .filename(userId + ".png")
                    .build());
            // 路径配置
//            String url = "https://sit.baicaiec.com/longhuan/h5/transferPage/perId=%s&path=applicationSubmissionPage";
            String url = authPath;
            url = String.format(url, preId);
            String content = "客户姓名：" + applyInfoEntity.getName();
            url = orderSendMessageImpl.replaceUrl(url);

            // 返回响应实体
            imageBytes = QrCodeUtils.generateQrCode(content, url);
            return new ResponseEntity<>(imageBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("ApprovalServiceImpl.userToAuth e:", e);
            throw new BusinessException(e);
        }
    }

    @Override
    public UserDetailInfoVO getManagerInfo(LoginUser loginUser) {
        log.info("ApprovalServiceImpl.getManagerInfo loginUser:{}", loginUser);
        UserDetailInfoVO managerInfo = new UserDetailInfoVO();
        Result<UserDetailInfoVO> userDetailInfoVO = userFeign.searchUserDetailById(loginUser.getUserId());
        if (Result.isSuccess(userDetailInfoVO)) {
            return userDetailInfoVO.getData();
        }
        return managerInfo;
    }

    /**
     * 获取蓝海极证云涉诉命中提醒
     * @param orderId
     * @param loginUserInfo
     * @return
     */
    @Override
    public String riskReviewPreWarn(Integer orderId, LoginUser loginUserInfo) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isNotNull(orderInfoEntity) && ObjUtil.isNotNull(orderInfoEntity.getPreId())
                && ObjUtil.isNotNull(orderInfoEntity.getFundId()) && ObjUtil.equals(orderInfoEntity.getFundId(), FundEnum.LAN_HAI.getValue())){
            String  result = getPreJzyShesu(orderInfoEntity.getPreId());
            return result;
        }
        return null;
    }

    @Override
    public Boolean updateStatus(Integer orderId, Integer pageId, Integer status) {
        orderPageInfoService.updateOrderPageInfo(orderId, States.getNode(pageId), status);
        return true;
    }


    /**
     * 获取蓝海极证云涉诉命中提醒
     * @return
     */
    private String getPreJzyShesu(Integer preId){
        PreRiskPolicyResultEntity riskPolicyResultEntity = preRiskPolicyResultMapper.selectOne(new LambdaQueryWrapper<PreRiskPolicyResultEntity>()
                .eq(PreRiskPolicyResultEntity::getPolicyName, "涉诉")
                .eq(PreRiskPolicyResultEntity::getPreId, preId)
                .eq(PreRiskPolicyResultEntity::getDeleteFlag, 0)
                .orderByDesc(PreRiskPolicyResultEntity::getCreateTime)
                .last("limit 1"));

        if (ObjUtil.isNotEmpty(riskPolicyResultEntity)){
            List<FundProductVO> list = JSONUtil.toList(riskPolicyResultEntity.getFunds(), FundProductVO.class);
            FundProductVO fundProductVO = list.stream().filter(item -> ObjUtil.equals(FundEnum.LAN_HAI.getValue(), item.getFundId())
                    && ObjUtil.equals(item.getResult(),  FundProductResult.WARN)).findFirst().orElse(null);
            if (ObjUtil.isNotEmpty(fundProductVO)){
                return fundProductVO.getSuggestion();
            }
        }
        return null;
    }


    /**
     * 验证资方抵押状态
     */
    private void verifyFundMortgageState(Integer orderId) {
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isNull(orderInfo)) {
            return;
        }
        //判断是否未线上抵押
        CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                        .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                        .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                ,false
        );
        if (ObjUtil.isNull(customerMortgageInfoEntity)) {
            return;
        }

        Integer fundId = orderInfo.getFundId();
        FundEnum fundEnum = FundEnum.getFundEnum(fundId);
        if (ObjUtil.isNull(fundEnum)) {
            return;
        }
        if (Objects.equals(customerMortgageInfoEntity.getMortgageType(), 0)) {
            switch (fundEnum) {
                case CHANG_YIN:
                case ZHONG_HENG_TONG_HUI:
                case ZHONG_HENG:
                    SearchOrderStatusDTO hengTongStatusDTO = new SearchOrderStatusDTO();
                    hengTongStatusDTO.setOrderId(orderId);
                    hengTongStatusDTO.setOrderType("E");
                    Result<String> hengTongResult = approveFeign.hengTongCopperCarMortgageStatus(hengTongStatusDTO);
                    if (!Result.isSuccess(hengTongResult)){
                        throw new BusinessException(hengTongResult.getMsg());
                    }
                    CopperCarDeptMortgageEnums mortgageEnums = CopperCarDeptMortgageEnums.getByCode(hengTongResult.getData());
                    if (mortgageEnums == null) {
                        throw new BusinessException("抵押状态异常");
                    }
                    // 定义允许的抵押状态集合
                    Set<CopperCarDeptMortgageEnums> validStates = Set.of(
                            CopperCarDeptMortgageEnums.PUSH_TO_TMRI,
                            CopperCarDeptMortgageEnums.ACCEPT_COMPLETED,
                            CopperCarDeptMortgageEnums.COMPLETED
                    );

                    if (!validStates.contains(mortgageEnums)) {
                        throw new BusinessException("线上抵押未完成");
                    }
            }

        }
    }
}
