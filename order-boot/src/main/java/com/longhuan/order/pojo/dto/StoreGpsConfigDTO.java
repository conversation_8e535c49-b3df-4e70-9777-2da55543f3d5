package com.longhuan.order.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 门店GPS分期和收费策略配置DTO
 *
 * <AUTHOR>
 * @date 2025/01/01
 */
@Data
@Accessors(chain = true)
public class StoreGpsConfigDTO {
    /**
     * 门店ID，关联lh_dept表
     */
    @ApiModelProperty(value = "门店ID，关联lh_dept表", required = true)
    @NotNull(message = "门店ID不能为空")
    private Integer deptId;

    /**
     * 是否支持分期（0：不支持，1：支持）
     */
    @ApiModelProperty(value = "是否支持分期（0：不支持，1：支持）", required = true)
    @NotNull(message = "是否支持分期不能为空")
    private Integer isInstallment;

    /**
     * 是否支持免费（0：不支持，1：支持）
     */
    @ApiModelProperty(value = "是否支持免费（0：不支持，1：支持）", required = true)
    @NotNull(message = "是否支持免费不能为空")
    private Integer isFree;
}
