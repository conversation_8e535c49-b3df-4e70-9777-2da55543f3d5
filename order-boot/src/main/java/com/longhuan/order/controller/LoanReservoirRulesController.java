package com.longhuan.order.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.pojo.dto.LoanReservoirRulesDTO;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.vo.LoanReservoirRulesVO;
import com.longhuan.order.service.LoanReservoirRulesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * 放款蓄水池规则
 *
 * @date 2025/8/18 10:53
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/loanReservoirRules")
@Api("放款蓄水池规则")
public class LoanReservoirRulesController {
    private final LoanReservoirRulesService loanReservoirRulesService;
    private final OrderInfoMapper orderInfoMapper;

    @ApiOperation("列表查询")
    @PostMapping("/pageList")
    public Result<Page<LoanReservoirRulesVO>> list(@RequestBody LoanReservoirRulesDTO loanReservoirRulesDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(loanReservoirRulesService.list(loanReservoirRulesDTO,loginUser));
    }

    @ApiOperation("新增")
    @PostMapping("/save")
    public Result<Boolean> add(@RequestBody LoanReservoirRulesDTO loanReservoirRulesDTO, @CurrentUser LoginUser loginUser) {
       Assert.notNull(loanReservoirRulesDTO.getFundIds(), () -> new BusinessException("资方不能为空"));
       Assert.isFalse(ObjUtil.isNotNull(loanReservoirRulesDTO.getEndDate())
                && loanReservoirRulesDTO.getEndDate().isBefore(LocalDateTime.now()), () -> new BusinessException("失效时间不能早于当前时间"));
        loanReservoirRulesService.save(loanReservoirRulesDTO,loginUser);
        return Result.success(Boolean.TRUE);
    }

    @ApiOperation("更新开启关闭")
    @PostMapping("/updateEnable")
    public Result<Boolean> updateEnable(@RequestBody LoanReservoirRulesDTO loanReservoirRulesDTO, @CurrentUser LoginUser loginUser) {
        loanReservoirRulesService.updateEnable(loanReservoirRulesDTO,loginUser);
        return Result.success(Boolean.TRUE);
    }

    @ApiOperation("操作日志列表查询")
    @PostMapping("/pageOperationList")
    public Result<Page<LoanReservoirRulesVO>> pageOperationList(@RequestBody LoanReservoirRulesDTO loanReservoirRulesDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(loanReservoirRulesService.pageOperationList(loanReservoirRulesDTO,loginUser));
    }
    @ApiOperation("测试命中")
    @PostMapping("/test")
    public Result<Boolean> test(@RequestBody LoanReservoirRulesDTO loanReservoirRulesDTO) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(loanReservoirRulesDTO.getId());
        loanReservoirRulesService.judgOrderReservoirRules(orderInfoEntity);
        return Result.success(true);
    }

    @ApiOperation("失效规时间则进行放款")
    @PostMapping("/failureTimeRuleToLoan")
    public Result<Boolean> failureTimeRuleToLoan() {
        loanReservoirRulesService.failureTimeRuleToLoan();
        return Result.success(true);
    }

}
