package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.OrderIdDTO;
import com.longhuan.risk.pojo.vo.CreditReportVO;
import com.longhuan.order.service.RiskService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/***
 * 征信报告
 * <AUTHOR>
 * @date 2024/08/28
 */
@RestController
@RequestMapping("/api/v1/credit")
@RequiredArgsConstructor
public class CreditReportController {
    private final RiskService riskService;

    @ApiOperation("征信报告")
    @PostMapping("/report")
    public Result<CreditReportVO> creditReport(@RequestBody OrderIdDTO orderIdDTO) {
        return Result.success(riskService.creditReport(orderIdDTO));
    }
}
