package com.longhuan.order.settleCalculation.service.impl.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.enums.FundRepayStatusEnum;
import com.longhuan.order.mapper.OrderPayApplicationMapper;
import com.longhuan.order.mapper.RepurchaseRepaymentInfoMapper;
import com.longhuan.order.pojo.dto.FundEarlyRepaymentCalcDTO;
import com.longhuan.order.pojo.dto.SettleCalculationContext;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.SettleCalculationVO;
import com.longhuan.order.service.FundRepaymentDeductService;
import com.longhuan.order.settleCalculation.service.impl.strategy.SettleCalculationStrategy;
import com.longhuan.order.settleCalculation.service.impl.strategy.impl.Annotation.SupportSettleType;
import com.longhuan.order.settleCalculation.service.impl.strategy.impl.enums.SettleType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 非委外赎回结清策略
 */
@Slf4j
@Component
@SupportSettleType(SettleType.NON_OUTSOURCING_REDEEM)
@RequiredArgsConstructor
public class NonOutsourcingRedeemStrategy implements SettleCalculationStrategy {
    private final RepurchaseRepaymentInfoMapper repurchaseRepaymentInfoMapper;
    private final OrderPayApplicationMapper orderPayApplicationMapper;
    private final FundRepaymentDeductService fundRepaymentDeductService;

    @Override
    public SettleCalculationVO calculate(SettleCalculationContext context) {
        Assert.notNull(context.getRemainingPrincipal(), () -> new BusinessException("剩余本金不能为空"));
        Assert.notNull(context.getIRRInterest(), () -> new BusinessException("IRR利率不能为空"));
        Assert.notNull(context.getUseDays(), () -> new BusinessException("使用天数不能为空"));
        //赎回策略初始金额限定剩余本金
        BigDecimal fundSettlementAmount = context.getFundSettlementAmount();
        context.setFundSettlementAmount(context.getRemainingPrincipal());
        SettleCalculationVO settleCalculationVO;
        if (Objects.equals(context.getClosedPeriodRepayMode(), 0)) {
            // 结清金额=资方结清金额
            settleCalculationVO = SettleCalculationMethod.directSettlement(context.getFundSettlementAmount());
        } else {
            //调用通用结算策略
            settleCalculationVO = SettleCalculationMethod.defaultSettlementPolicy(context);
        }
        //增加逾期违约金
        cleanUpOverdue(settleCalculationVO, context);
        //增加赎回特殊IRR结清策略
        calculateWithIRRRate(settleCalculationVO, context.getFundSettlementAmount(), context.getIRRInterest(), context.getUseDays());
        context.setFundSettlementAmount(fundSettlementAmount);
        return settleCalculationVO;
    }

    /**
     * 逾期违约金=逾期天数*0.5%*逾期当期剩余未还部分+逾期当期剩余未还部分
     *
     * @param settleCalculationVO 结清信息
     * @param context             结清参数
     */
    private void cleanUpOverdue(SettleCalculationVO settleCalculationVO, SettleCalculationContext context) {
        if (ObjUtil.isNotNull(context.getOverdueDays()) && context.getOverdueDays() > 0) {
            Assert.notNull(context.getOverdueUnpaid(), () -> new BusinessException("逾期未还金额不能为空"));
            // 逾期违约金=逾期天数*0.5%*逾期当期剩余未还部分+逾期当期剩余未还部分
            BigDecimal latePenaltyAmount = context.getOverdueUnpaid().multiply(BigDecimal.valueOf(0.005))
                    .multiply(BigDecimal.valueOf(context.getOverdueDays()))
                    .add(context.getOverdueUnpaid()).setScale(2, RoundingMode.HALF_UP);
            settleCalculationVO
                    .setSettlementAmount(settleCalculationVO.getSettlementAmount().add(latePenaltyAmount))
                    .setLatePenaltyAmount(latePenaltyAmount);
        }
    }

    @Override
    public void buildLHContext(SettleCalculationContext context,
                               OrderInfoEntity orderInfoEntity,
                               LocalDate payDate) {
        List<RepurchaseRepaymentInfoEntity> repurchaseRepaymentInfoEntityList = repurchaseRepaymentInfoMapper.selectList(
                new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                        .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(RepurchaseRepaymentInfoEntity::getFundId, orderInfoEntity.getFundId())
                        .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
        );
//剩余本金
        BigDecimal repaymentPrincipal = repurchaseRepaymentInfoEntityList.stream()
                .filter(entity -> !Objects.equals(entity.getRepaymentStatus(), FundRepayStatusEnum.SETTLED))
                .map(RepurchaseRepaymentInfoEntity::getRepaymentPrincipal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal actuallyPrincipal = repurchaseRepaymentInfoEntityList.stream()
                .filter(entity -> !Objects.equals(entity.getRepaymentStatus(), FundRepayStatusEnum.SETTLED))
                .map(RepurchaseRepaymentInfoEntity::getActuallyPrincipal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        List<OrderPayApplicationInfoEntity> orderPayApplicationInfoEntityList = orderPayApplicationMapper.selectList(new LambdaQueryWrapper<>());
        BigDecimal redeemSingleRepay = orderPayApplicationInfoEntityList.stream()
                .filter(entity -> Objects.equals(entity.getFeeType(), OrderFeeDetailExpandTypeEnum.SINGLE_PERIOD_COMPENSATION))
                .map(OrderPayApplicationInfoEntity::getPayeeAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //赎回金额
        orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                        .eq(OrderPayApplicationInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)
                        .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                        .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS))
                .stream().map(OrderPayApplicationInfoEntity::getPayeeAmount)
                .reduce(BigDecimal::add).ifPresentOrElse(context::setRedeemAmount, () -> context.setRedeemAmount(BigDecimal.ZERO));
        context.setRegionId(orderInfoEntity.getRegionId())
                .setLoanAmount(orderInfoEntity.getApprovalAmount())
                .setOverdueDays(null != payDate
                        ? getOverdueDays(payDate, repurchaseRepaymentInfoEntityList)
                        : orderInfoEntity.getOverdueDays())
                .setOverdueUnpaid(orderInfoEntity.getTotalOverdueAmount())
                .setFundSettlementAmount(repaymentPrincipal)
                .setRemainingPrincipal(repaymentPrincipal)
                .setRedeemSingleRepay(redeemSingleRepay)
                .setRedeemPaidAmount(repurchaseRepaymentInfoEntityList.stream()
                        .filter(entity -> Objects.equals(entity.getRepaymentStatus(), FundRepayStatusEnum.SETTLED))
                        .map(RepurchaseRepaymentInfoEntity::getRepaymentPrincipal)
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                .setPaidPeriods(CollUtil.isNotEmpty(repurchaseRepaymentInfoEntityList.stream()
                        .filter(entity -> Objects.equals(entity.getRepaymentStatus(), FundRepayStatusEnum.SETTLED))
                        .toList()) ? repurchaseRepaymentInfoEntityList.stream()
                        .filter(entity -> Objects.equals(entity.getRepaymentStatus(), FundRepayStatusEnum.SETTLED))
                        .toList().size() : 0)
                .setUseDays(calRepurchaseUseDays(orderInfoEntity.getPaymentTime().toLocalDate(),
                        orderInfoEntity.getId(),
                        orderInfoEntity.getFundId(),
                        Objects.equals(repaymentPrincipal.compareTo(actuallyPrincipal), 0) ? 1 : 2, payDate))
                .setSettlePaidPeriods(repurchaseRepaymentInfoEntityList.stream()
                        .filter(entity -> !Objects.equals(entity.getRepaymentStatus(), FundRepayStatusEnum.SETTLED))
                        .filter(entity -> !Objects.equals(entity.getRepaymentStatus(), FundRepayStatusEnum.OVERDUE))
                        .map(RepurchaseRepaymentInfoEntity::getTerm)
                        .min(Integer::compareTo).isPresent() ? repurchaseRepaymentInfoEntityList.stream()
                        .filter(entity -> !Objects.equals(entity.getRepaymentStatus(), FundRepayStatusEnum.SETTLED))
                        .filter(entity -> !Objects.equals(entity.getRepaymentStatus(), FundRepayStatusEnum.OVERDUE))
                        .map(RepurchaseRepaymentInfoEntity::getTerm)
                        .min(Integer::compareTo).get() : 0);

    }

    /**
     * 计算逾期天数
     *
     * @param payDate
     * @param repurchaseRepaymentInfoEntityList
     * @return
     */
    private Integer getOverdueDays(LocalDate payDate,
                                   List<RepurchaseRepaymentInfoEntity> repurchaseRepaymentInfoEntityList) {

        //未还款的最早期数
        Optional<RepurchaseRepaymentInfoEntity> min = repurchaseRepaymentInfoEntityList.stream().filter(v ->
                        FundRepayStatusEnum.NONE.getValue() == v.getRepaymentStatus().getValue())
                .min(Comparator.comparing(RepurchaseRepaymentInfoEntity::getTerm));
        if (min.isPresent()) {
            RepurchaseRepaymentInfoEntity entity = min.get();
            //应还款日期
            LocalDate repaymentDate = entity.getRepaymentDate();
            //选择的结清日期在应还款日期前或等于应还款日期,逾期天数=0
            if (payDate.isBefore(repaymentDate)
                    || payDate.isEqual(repaymentDate)) {
                return 0;
            }
            //选择的结清日期在应还款日期后
            else {
                return (int) ChronoUnit.DAYS.between(repaymentDate, payDate);
            }
        }
        return 0;
    }

    /**
     * 计算赎回使用天数
     * 1.存在已结清期数
     * 当前日期 - 上次结清应还日期
     * 2.存在逾期期数
     * 当前日期 - 下一个应还期数应还日期
     * 3.存在实还日期 部分还款
     * 当前日期 - 实还日期
     * 4.存在未到还款日 1期在当前日期之后
     * 当前日期 - 放款日期
     * 5.存在1期逾期 1期在当前日期之前
     * 当前日期 - 1期还款日期
     */
    private Integer calRepurchaseUseDays(LocalDate paymentDate,
                                         Integer orderId,
                                         Integer fundId,
                                         Integer fundSettleFlag,
                                         LocalDate payDate) {
        LocalDate beginDate = LocalDate.now();
        //若payDate 不为空,则代表传入选择结清时间
        if (null != payDate) {
            beginDate = payDate;
        }
        LocalDate earliestOverdueDate;
        RepurchaseRepaymentInfoEntity calRepaymentInfo;
        int useDays;
        log.info("NonOutsourcingRedeemStrategy.calRepurchaseUseDays - Begin, paymentDate: {}, orderId: {}, fundId: {}", paymentDate, orderId, fundId);
        if (Objects.equals(getFundSettleFlag(orderId, fundId), fundSettleFlag)) {
            List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                            .eq(FundRepaymentDeductEntity::getFundId, fundId)
                            .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                            .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                    FundDeductBizTypeEnums.PAYMENT,
                                    FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                    FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                    FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE,
                                    FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION
                            ))
                            .eq(FundRepaymentDeductEntity::getIsRepurchase, 1)
                            .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                            .orderByAsc(FundRepaymentDeductEntity::getUpdateTime)
            );
            if (CollUtil.isNotEmpty(list)) {
                RepurchaseRepaymentInfoEntity fundRepaymentInfoEntity = repurchaseRepaymentInfoMapper.selectOne(
                        new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                                .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                                .eq(RepurchaseRepaymentInfoEntity::getFundId, fundId)
                                .eq(RepurchaseRepaymentInfoEntity::getTerm, list.get(0).getTerm())
                                .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(RepurchaseRepaymentInfoEntity::getUpdateTime)
                                .last("limit 1")
                );
                List<OrderPayApplicationInfoEntity> orderPayApplicationInfoEntityList = orderPayApplicationMapper.selectList(
                        new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                                .eq(OrderPayApplicationInfoEntity::getOrderId, orderId)
                                .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY)
//                                .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.CASHIER_APPROVAL)
                                .orderByAsc(OrderPayApplicationInfoEntity::getCreateTime)
                                .last("limit 1")
                );
                earliestOverdueDate = fundRepaymentInfoEntity.getRepaymentDate();
                if (beginDate.isAfter(earliestOverdueDate) || beginDate.isEqual(earliestOverdueDate)) {
                    useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
                    if (CollUtil.isNotEmpty(orderPayApplicationInfoEntityList)) {
                        useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, orderPayApplicationInfoEntityList.get(0).getPaymentTime().toLocalDate());
                    }
                } else {
                    if (!Objects.equals(list.get(0).getTerm(), 1)) {
                        RepurchaseRepaymentInfoEntity fundRepaymentInfoEntity1 = repurchaseRepaymentInfoMapper.selectOne(
                                new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                                        .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                                        .eq(RepurchaseRepaymentInfoEntity::getFundId, fundId)
                                        .eq(RepurchaseRepaymentInfoEntity::getTerm, list.get(0).getTerm() - 1)
                                        .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                                        .orderByDesc(RepurchaseRepaymentInfoEntity::getUpdateTime)
                                        .last("limit 1")
                        );
                        useDays = (int) ChronoUnit.DAYS.between(fundRepaymentInfoEntity1.getRepaymentDate(), beginDate);
                        if (CollUtil.isNotEmpty(orderPayApplicationInfoEntityList)) {
                            useDays = (int) ChronoUnit.DAYS.between(fundRepaymentInfoEntity1.getRepaymentDate(), orderPayApplicationInfoEntityList.get(0).getPaymentTime().toLocalDate());
                        }
                    } else {
                        useDays = (int) ChronoUnit.DAYS.between(paymentDate, beginDate);
                        if (CollUtil.isNotEmpty(orderPayApplicationInfoEntityList)) {
                            useDays = (int) ChronoUnit.DAYS.between(paymentDate, orderPayApplicationInfoEntityList.get(0).getPaymentTime().toLocalDate());
                        }
                    }
                }
                log.info("RepaymentServiceImpl.calRepurchaseUseDays - Last settled, useDays: {}", useDays);
                return useDays;
            }
        }
        // 判断全部还款计划是否未到期
        calRepaymentInfo = repurchaseRepaymentInfoMapper.selectOne(
                new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                        .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                        .eq(RepurchaseRepaymentInfoEntity::getFundId, fundId)
                        .eq(RepurchaseRepaymentInfoEntity::getTerm, 1)
                        .ne(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                , false
        );

        log.info("NonOutsourcingRedeemStrategy.calRepurchaseUseDays - First term repayment info: {}", calRepaymentInfo);

        if (ObjUtil.isNotNull(calRepaymentInfo) && calRepaymentInfo.getRepaymentStatus() == FundRepayStatusEnum.OVERDUE) {
            // 1 期逾期
            earliestOverdueDate = calRepaymentInfo.getRepaymentDate();
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("NonOutsourcingRedeemStrategy.calRepurchaseUseDays - First term overdue, useDays: {}", useDays);
            return useDays;
        } else if (ObjUtil.isNotNull(calRepaymentInfo) && calRepaymentInfo.getRepaymentStatus() == FundRepayStatusEnum.NONE) {
            // 1期未逾期
            earliestOverdueDate = paymentDate;
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("NonOutsourcingRedeemStrategy.calRepurchaseUseDays - First term not overdue, useDays: {}", useDays);
            return useDays;
        }

        // 存在实还日期 部分还款
        calRepaymentInfo = repurchaseRepaymentInfoMapper.selectOne(
                new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                        .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                        .eq(RepurchaseRepaymentInfoEntity::getFundId, fundId)
                        .isNotNull(RepurchaseRepaymentInfoEntity::getActuallyDate)
                        .ne(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(RepurchaseRepaymentInfoEntity::getTerm)
                , false
        );

        log.info("NonOutsourcingRedeemStrategy.calRepurchaseUseDays - Partial repayment info: {}", calRepaymentInfo);

        if (ObjUtil.isNotNull(calRepaymentInfo)) {
            earliestOverdueDate = calRepaymentInfo.getActuallyDate();
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("NonOutsourcingRedeemStrategy.calRepurchaseUseDays - Partial repayment, useDays: {}", useDays);
            return useDays;
        }
        // 存在逾期 获取第一个逾期
        calRepaymentInfo = repurchaseRepaymentInfoMapper.selectOne(
                new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                        .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                        .eq(RepurchaseRepaymentInfoEntity::getFundId, fundId)
                        .eq(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.OVERDUE)
                        .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                        .orderByAsc(RepurchaseRepaymentInfoEntity::getTerm)
                , false
        );

        log.info("NonOutsourcingRedeemStrategy.calRepurchaseUseDays - First overdue repayment info: {}", calRepaymentInfo);

        if (ObjUtil.isNotNull(calRepaymentInfo)) {
            earliestOverdueDate = calRepaymentInfo.getRepaymentDate();
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("NonOutsourcingRedeemStrategy.calRepurchaseUseDays - First overdue, useDays: {}", useDays);
            return useDays;
        }

        // 存在已结清期数 没有逾期
        calRepaymentInfo = repurchaseRepaymentInfoMapper.selectOne(
                new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                        .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                        .eq(RepurchaseRepaymentInfoEntity::getFundId, fundId)
                        .eq(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(RepurchaseRepaymentInfoEntity::getTerm)
                , false
        );

        log.info("NonOutsourcingRedeemStrategy.calRepurchaseUseDays - Last settled repayment info: {}", calRepaymentInfo);

        if (ObjUtil.isNotNull(calRepaymentInfo)) {
            earliestOverdueDate = calRepaymentInfo.getRepaymentDate();
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("NonOutsourcingRedeemStrategy.calRepurchaseUseDays - Last settled, useDays: {}", useDays);
            return useDays;
        }
        log.info("NonOutsourcingRedeemStrategy.calRepurchaseUseDays - No matching conditions, returning 0");
        return 0;
    }

    private Integer getFundSettleFlag(Integer orderId, Integer fundId) {
        List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(
                new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                        .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                        .eq(FundRepaymentDeductEntity::getFundId, fundId)
                        .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                        .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                        .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                FundDeductBizTypeEnums.PAYMENT,
                                FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE
                        ))
                        .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                        .orderByAsc(FundRepaymentDeductEntity::getUpdateTime)
        );
        if (CollUtil.isEmpty(list)) {
            return 2;
        }
        // 获取剩余本金和已还款期数
        List<RepurchaseRepaymentInfoEntity> repaymentInfoList = repurchaseRepaymentInfoMapper.selectList(
                new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                        .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                        .eq(RepurchaseRepaymentInfoEntity::getFundId, fundId)
                        .ge(RepurchaseRepaymentInfoEntity::getTerm, list.get(0).getTerm())
                        .eq(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
        );
        BigDecimal repaymentPrincipal = repaymentInfoList.stream()
                .map(RepurchaseRepaymentInfoEntity::getRepaymentPrincipal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal actuallyPrincipal = repaymentInfoList.stream()
                .map(RepurchaseRepaymentInfoEntity::getActuallyPrincipal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (Objects.equals(repaymentPrincipal.compareTo(actuallyPrincipal), 0)) {
            return 1;
        }
        return 2;
    }

    /**
     * 结清金额=金额+金额*IRR利率/12/30*使用天数(利息)
     *
     * @param settleCalculationVO 结算信息
     * @param amount              金额
     * @param irrInterest         IRR利率
     * @param useDays             使用天数
     * @return 结清信息
     */
    private void calculateWithIRRRate(SettleCalculationVO settleCalculationVO, BigDecimal amount, BigDecimal irrInterest, Integer useDays) {
        BigDecimal interest = amount.multiply(irrInterest)
                .divide(BigDecimal.valueOf(12 * 30), 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(useDays)).setScale(2, RoundingMode.HALF_UP);
        settleCalculationVO.setSettlementAmount(settleCalculationVO.getSettlementAmount().add(interest))
                .setInterestAmount(settleCalculationVO.getInterestAmount().add(interest))
                .setUseDays(useDays);
    }
}
