package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.RepaymentCalBaseEntity;
import com.longhuan.order.pojo.entity.FundRepaymentInfoEntity;
import com.longhuan.order.pojo.entity.RepaymentInfoEntity;
import com.longhuan.order.pojo.entity.RepurchaseRepaymentInfoEntity;
import com.longhuan.order.pojo.vo.RepaymentInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 还款转换器
 *
 * <AUTHOR>
 * @date 2024/08/13
 */
@Mapper(componentModel = "spring")
public interface RepaymentConverter {

    List<RepaymentInfoVO> entityToVo(List<RepaymentInfoEntity> entity);


    @Mapping(target = "repaymentAmount", source = "repaymentAmountTotal")
    @Mapping(target = "actuallyAmount", source = "actuallyPrincipal")
    @Mapping(target = "repaymentCompound", source = "repaymentPsCommOdAmount")
    @Mapping(target = "actuallyCompound", source = "actuallyCommOdAmount")
    @Mapping(target = "repaymentGuarantee", source = "repaymentGuaraFeeAmount")
    @Mapping(target = "actuallyGuarantee", source = "actuallyGuaraFeeAmount")
    @Mapping(target = "repaymentOther", source = "repaymentPsFeeAmount")
    @Mapping(target = "actuallyOther", source = "actuallyFeeAmount")
    @Mapping(target = "repaymentGuaraFeeOd", source = "repaymentGuaraFeeOdAmount")
    @Mapping(target = "actuallyGuaraFeeOd", source = "actuallyGuaraFeeOdAmount")
    RepaymentInfoVO fundEntityToVo(FundRepaymentInfoEntity entity);

    @Mapping(target = "repaymentAmount", source = "repaymentAmountTotal")
    @Mapping(target = "actuallyAmount", source = "actuallyPrincipal")
    RepaymentInfoVO repurchaseEntityToVo(RepurchaseRepaymentInfoEntity entity);


    List<RepaymentInfoVO> fundEntityListToVoList(List<FundRepaymentInfoEntity> entity);

    @Mapping(target = "repaymentAmount", source = "repaymentAmountTotal")
    @Mapping(target = "actuallyAmount", source = "actuallyPrincipal")
    List<RepaymentInfoVO> repurchaseListToVoList(List<RepurchaseRepaymentInfoEntity> entity);

    @Mapping(target = "serviceFee", expression = "java(RepaymentConverter.calServiceFee(entity.getRepaymentGuarantee(),entity.getInstallmentBrokerageServiceFee()))")
    RepaymentInfoVO entityToVo(RepaymentInfoEntity entity);


    List<RepaymentCalBaseEntity> repurchaseListToCalBaseList(List<RepurchaseRepaymentInfoEntity> entity);

    List<RepaymentCalBaseEntity> fundListToCalBaseList(List<FundRepaymentInfoEntity> entity);

    /**
     * 计算服务费
     *
     * @param repaymentGuarantee             还款保证
     * @param installmentBrokerageServiceFee 分期经纪服务费
     * @return {@link BigDecimal }
     */
    static BigDecimal calServiceFee(BigDecimal repaymentGuarantee, BigDecimal installmentBrokerageServiceFee) {
        if (Objects.isNull(repaymentGuarantee) && Objects.isNull(installmentBrokerageServiceFee)) {
            return BigDecimal.ZERO;
        }
        if (Objects.nonNull(repaymentGuarantee)) {
            return repaymentGuarantee.add(installmentBrokerageServiceFee);
        }
        return installmentBrokerageServiceFee;
    }
}
