package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.RelatedOrderInfoDTO;
import com.longhuan.order.pojo.dto.SaveOrderInfoDTO;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.entity.RiskCustomerBlacklistEntity;
import com.longhuan.order.pojo.vo.RelatedOrderVO;
import com.longhuan.order.pojo.vo.RiskCustomerBlacklistVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31
 */
@Mapper(componentModel = "spring")
public interface RiskCustomerBlacklistConverter {

    List<RiskCustomerBlacklistVO> entityList2VOList(List<RiskCustomerBlacklistEntity> entityList);
}
