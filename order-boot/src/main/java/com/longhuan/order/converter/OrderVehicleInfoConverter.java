package com.longhuan.order.converter;

import cn.hutool.core.date.DatePattern;
import com.longhuan.order.pojo.dto.VehicleInfoEditDTO;
import com.longhuan.order.pojo.entity.OrderVehicleInfoEntity;
import com.longhuan.order.pojo.vo.OrderVehicleInfoVO;
import com.longhuan.order.pojo.vo.StoreAppraiserInfoVO;
import com.longhuan.order.pojo.vo.VehicleInfoVO;
import com.longhuan.order.pojo.vo.VehicleSupplementInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * 次序车辆信息转换
 *
 * <AUTHOR>
 * @date 2024/10/19
 */
@Mapper(componentModel = "spring")
public interface OrderVehicleInfoConverter {

    OrderVehicleInfoEntity dto2Entity(VehicleInfoEditDTO vehicleInfoEditDTO);

    /**
     * 实体2vo
     *
     * @param orderVehicleInfoEntity 订购车辆信息实体
     * @return {@link OrderVehicleInfoVO}
     */
    OrderVehicleInfoVO entity2vo(OrderVehicleInfoEntity orderVehicleInfoEntity);

    /**
     * 实体 2 订购 VO
     *
     * @param orderVehicleInfoEntity 订单车辆信息实体
     * @return {@link VehicleInfoVO }
     */
    VehicleInfoVO entity2OrderVo(OrderVehicleInfoEntity orderVehicleInfoEntity);
    /**
     * 实体 2 补充 VO
     *
     * @param orderVehicleInfoEntity 订单车辆信息实体
     * @return {@link VehicleSupplementInfoVO }
     */
    @Mappings({
            @Mapping(target = "color", source = "vehicleColor"),
            @Mapping(target = "transferNumber", source = "transferTimes"),
            @Mapping(target = "yearTransferNumber", source = "transferOneTimes"),
            @Mapping(target = "mortgagesTotalNumber", source = "mortgageTimeCount"),
            @Mapping(target = "twoYearMortgagesNumber", source = "mortgageTime"),
            @Mapping(target = "buyDate", dateFormat = DatePattern.NORM_DATE_PATTERN),
            @Mapping(target = "productionDate", dateFormat = DatePattern.NORM_DATE_PATTERN),
            @Mapping(target = "compulsoryDate", dateFormat = DatePattern.NORM_DATE_PATTERN),
            @Mapping(target = "thirdInsuranceDate", dateFormat = DatePattern.NORM_DATE_PATTERN),
            @Mapping(target = "annualDate", dateFormat = DatePattern.NORM_DATE_PATTERN),
            @Mapping(target = "lastMortgageReleaseDate", dateFormat = DatePattern.NORM_DATE_PATTERN),
            @Mapping(target = "lastTransferRegistrationDate", dateFormat = DatePattern.NORM_DATE_PATTERN)
    })
    VehicleSupplementInfoVO entity2SupplementVo(OrderVehicleInfoEntity orderVehicleInfoEntity);


    StoreAppraiserInfoVO entity2StoreAppraiser(OrderVehicleInfoEntity orderVehicleInfoEntity);
}