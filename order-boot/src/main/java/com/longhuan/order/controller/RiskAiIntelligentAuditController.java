package com.longhuan.order.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.RiskAiIntelligentAuditDTO;
import com.longhuan.order.pojo.vo.RiskAiIntelligentAuditListVO;
import com.longhuan.order.service.RiskAiIntelligentAuditService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;


/**
 * RiskAiIntelligentAuditController
 *
 * @date 2025/7/28 9:22
 */
@Api(tags = "风控AI审核")
@RestController
@RequestMapping("/api/v1/riskAiIntelligentAudit")
@RequiredArgsConstructor
public class RiskAiIntelligentAuditController {
    private final RiskAiIntelligentAuditService riskAiIntelligentAuditService;


    /**
     * 获取查询列表
     * @return
     */
    @ApiOperation(value = "获取查询列表")
    @PostMapping("/pageList")
    public Result<Page<RiskAiIntelligentAuditListVO>> pageList(@RequestBody RiskAiIntelligentAuditDTO dto, @CurrentUser LoginUser loginUser) {
        Page<RiskAiIntelligentAuditListVO> list = riskAiIntelligentAuditService.pageList(dto, loginUser);
        return Result.success(list);
    }

    /**
     * 四分钟内查询没有结果的数据主动查询 如果还是为{} 推送失败请求
     */
    @GetMapping("/autoQueryAndPushErrorIntelligentRisk")
    public Result<Boolean>  autoQueryAndPushErrorIntelligentRisk() {
        riskAiIntelligentAuditService.autoQueryAndPushErrorIntelligentRisk();
        return Result.success(true);
    }



    /**
     * 两分钟内没结果 继续去重新推送
     */
    @GetMapping("/pushAlRequestTwo")
    public Result<Boolean>  pushAlRequestTwo() {
        riskAiIntelligentAuditService.pushAlRequestTwo();
        return Result.success(true);
    }
}
