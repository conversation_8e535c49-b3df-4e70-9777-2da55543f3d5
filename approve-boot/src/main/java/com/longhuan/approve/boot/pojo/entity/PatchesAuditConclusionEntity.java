package com.longhuan.approve.boot.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

import com.longhuan.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 贷后补件审核结论表
 * @TableName lh_patches_audit_conclusion
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "资方产品关联表")
@TableName("lh_patches_audit_conclusion")
@KeySequence("lh_patches_audit_conclusion_id_seq")
@EqualsAndHashCode(callSuper = true)
public class PatchesAuditConclusionEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId
    private Integer id;

    /**
     * 主键ID
     */
    private Integer patchesId;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 审核结论
     */
    private String auditConclusion;

    /**
     * 审核状态 1合同岗通过 2合同岗驳回 3合同岗待审核 4资方拒绝 5资方通过
     */
    private Integer processState;

    /**
     * 审核人
     */
    private Integer reviewer;

}