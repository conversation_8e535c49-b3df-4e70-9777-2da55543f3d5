package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.CarModelInfoReqDTO;
import com.longhuan.order.pojo.vo.CarModelInfoVo;
import com.longhuan.order.service.CarModelInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class CarModelInfoController {

    private final CarModelInfoService carModelInfoService;

    /**
     * 车型信息
     */
    @PostMapping("/car/modelInfo")
    public Result<List<CarModelInfoVo>> getCarModelInfo(@RequestBody @Validated CarModelInfoReqDTO carModelInfoReqDTO) {
        return Result.success(carModelInfoService.getCarModelInfo(carModelInfoReqDTO));
    }

}
