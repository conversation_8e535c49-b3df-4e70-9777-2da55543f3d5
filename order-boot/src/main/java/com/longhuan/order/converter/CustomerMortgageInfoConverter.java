package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.AppointmentMortgageDetailDTO;
import com.longhuan.order.pojo.entity.CustomerMortgageInfoEntity;
import org.mapstruct.Mapper;

/**
 * 客户签约信息转炉
 *
 * <AUTHOR>
 * @date 2024/08/16
 */
@Mapper(componentModel = "spring")
public interface CustomerMortgageInfoConverter {
    /**
     * DTO 转实体
     *
     * @param appointmentMortgageDetailDTO 约会抵押详情信息VO
     * @return {@link CustomerMortgageInfoEntity }
     */
    CustomerMortgageInfoEntity dtoToEntity(AppointmentMortgageDetailDTO appointmentMortgageDetailDTO);
}
