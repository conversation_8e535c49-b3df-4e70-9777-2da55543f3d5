package com.longhuan.order.statemachine.action;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.RepaymentListDTO;
import com.longhuan.order.pojo.entity.DeptQuotaEntity;
import com.longhuan.order.pojo.entity.DeptQuotaRecordEntity;
import com.longhuan.order.pojo.entity.OrderAmountEntity;
import com.longhuan.order.service.ContractFileService;
import com.longhuan.order.service.OrderSendMessage;
import com.longhuan.order.service.RepaymentService;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 状态转移动作，在转移进行的时候触发的动作
 */
@Slf4j
@Component
public class FundsFinalApproveAction extends OrderNodeAction implements Action<States, Events> {
    private final OrderSendMessage orderSendMessage;

    private final ContractFileService contractFileService;

    private final RepaymentService repaymentService;
    private final DeptQuotaRecordMapper deptQuotaRecordMapper;
    private final DeptQuotaMapper deptQuotaMapper;
    private final OrderAmountMapper orderAmountMapper;

    public FundsFinalApproveAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper, OrderSendMessage orderSendMessage, ContractFileService contractFileService, RepaymentService repaymentService, DeptQuotaRecordMapper deptQuotaRecordMapper, DeptQuotaMapper deptQuotaMapper, OrderAmountMapper orderAmountMapper) {
        super(orderInfoMapper, orderNodeRecordMapper);
        this.orderSendMessage = orderSendMessage;
        this.contractFileService = contractFileService;
        this.repaymentService = repaymentService;
        this.deptQuotaRecordMapper = deptQuotaRecordMapper;
        this.deptQuotaMapper = deptQuotaMapper;
        this.orderAmountMapper = orderAmountMapper;
    }

    @Override
    public void execute(StateContext<States, Events> context) {


        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();
        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);

        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event, currentNode, nextNode);

        try {
            //生成还款计划表
            RepaymentListDTO repaymentListDTO = new RepaymentListDTO()
                    .setOrderId(orderId);
            repaymentService.list(repaymentListDTO);


            Integer code = event.getCode();
            // 发送微信消息通知客户
            log.info("CustomerConformAction.execute sendWeChatMsg orderId:{} node:{} status:{}", orderId, nextNode, code);
            orderSendMessage.sendWeChatMsg(orderId, nextNode, code);

        } catch (Exception e) {
            log.error("生成还款计划表 {}", e.getMessage(), e);
        }


        // 1. 修改订单表状态
        if (Boolean.TRUE.equals(target.getId().getValid())) {
            saveOrderNode(orderId, nextNode);
        }

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);

        //3.更新额度
        DeptQuotaRecordEntity deptQuotaRecordEntity=deptQuotaRecordMapper.selectOne(new LambdaQueryWrapper<DeptQuotaRecordEntity>()
                .eq(DeptQuotaRecordEntity::getOrderId, orderId)
                .eq(DeptQuotaRecordEntity::getAction, 0)
                .eq(DeptQuotaRecordEntity::getDeleteFlag, 0)
                .orderByDesc(DeptQuotaRecordEntity::getId),false);
        if (ObjUtil.isNull(deptQuotaRecordEntity)){
            return ;
        }
        OrderAmountEntity orderAmountEntity=orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                .eq(OrderAmountEntity::getOrderId, orderId)
                .eq(OrderAmountEntity::getDeleteFlag, 0)
                .orderByDesc(OrderAmountEntity::getId),false);
        BigDecimal differenceAmount=deptQuotaRecordEntity.getQuota().subtract(orderAmountEntity.getCustomerConfirmAmount());
        int update = deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                .setIncrBy(DeptQuotaEntity::getRemainingQuota, differenceAmount)
                .eq(DeptQuotaEntity::getId, deptQuotaRecordEntity.getDeptQuotaId())
                .eq(DeptQuotaEntity::getDeptId, deptQuotaRecordEntity.getDeptId())
                .eq(DeptQuotaEntity::getDeleteFlag, 0));
        if (update==0){
            if (ObjUtil.notEqual(differenceAmount, BigDecimal.ZERO)){
                log.error("RiskFinalApproveAction execute ModifyTheDifference orderid:{}", orderId);
                deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                        .set(DeptQuotaRecordEntity::getDeleteFlag, 1)
                        .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
            }
        }else {
            deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                    .set(DeptQuotaRecordEntity::getQuota, orderAmountEntity.getCustomerConfirmAmount())
                    .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
        }

    }
}