package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.OrderSuperaddInfoDTO;
import com.longhuan.order.pojo.dto.SaveOrderSuperaddInfoDTO;
import com.longhuan.order.pojo.entity.OrderPatchInfoEntity;
import com.longhuan.order.pojo.vo.OrderSuperAddInfoVO;
import org.mapstruct.Mapper;

import java.util.List;


@Mapper(componentModel = "spring")
public interface OrderSuperAddInfoConverter {

    OrderPatchInfoEntity dto2Entity(SaveOrderSuperaddInfoDTO dto);

    List<OrderSuperaddInfoDTO> listEntity2DtoList(List<OrderPatchInfoEntity> entityList);


    List<OrderSuperAddInfoVO> listEntity2VOList(List<OrderPatchInfoEntity> entityList);


}



