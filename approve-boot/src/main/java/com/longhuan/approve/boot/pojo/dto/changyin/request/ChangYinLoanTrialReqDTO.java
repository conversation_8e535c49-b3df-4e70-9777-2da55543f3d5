package com.longhuan.approve.boot.pojo.dto.changyin.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 贷款试算请求实体类
 * 描述：用于贷前贷款试算。
 */
@Data
@Accessors(chain = true)
public class ChangYinLoanTrialReqDTO {

    /**
     * 外部试算流水号
     * 是否必填：是
     */
    private String outTrailSeq;

    /**
     * 身份证
     * 是否必填：否
     */
    private String idNo;

    /**
     * 贷款品种
     * 是否必填：是
     * 描述：线下约定
     */
    private String loanType;

    /**
     * 申请金额
     * 是否必填：是
     */
    private BigDecimal applyAmt;

    /**
     * 申请期限
     * 是否必填：是
     * 描述：根据产品手册规约，合作方上送对应期数；参照申请期限
     */
    private String applyTnr;

    /**
     * 定价利率
     * 是否必填：是
     * 描述：年化利率；小数形式,不带百分号；长银定价利率
     */
    private BigDecimal priceIntRat;

    /**
     * 对客展示利率
     * 是否必填：否
     * 描述：小数形式,不带百分号
     */
    private BigDecimal custShowRate;

    /**
     * 还款间隔
     * 是否必填：是
     * 描述：参照还款间隔
     */
    private String loanFreq;

    /**
     * 合同签订日期
     * 是否必填：是
     * 描述：格式：yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate contSignDt;

    /**
     * 首次还款日
     * 是否必填：否
     * 描述：格式：yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate firstPayDt;

    /**
     * 还款方式
     * 是否必填：否
     * 描述：参照还款方式
     */
    private String mtdCde;
}