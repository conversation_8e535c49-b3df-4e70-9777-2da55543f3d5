package com.longhuan.user.api;


import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.user.pojo.dto.*;
import com.longhuan.user.pojo.vo.*;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户 API
 *
 * <AUTHOR>
 * @date 2024/07/01
 */
public interface UserApi {
    // 内部用户登录相关


    /**
     * 按手机号搜索用户详细信息
     *
     * @param mobile 手机号
     * @return {@link Result }<{@link UserDetailInfoVO }>
     */
    @GetMapping("/user/search/info/by/mobile")
    Result<UserDetailInfoVO> searchUserDetailByMobile(@RequestParam String mobile);

    /**
     * 按用户名搜索用户详细信息
     *
     * @param username 用户名
     * @return {@link Result }<{@link UserDetailInfoVO }>
     */
    @PostMapping("/user/search/info")
    Result<UserDetailInfoVO> searchUserDetailByUsername(@RequestParam String username);

    /**
     * 按 ID 搜索用户详细信息
     *
     * @param userId 用户 ID
     * @return {@link Result }<{@link UserDetailInfoVO }>
     */
    @GetMapping("/user/search/info/by/id")
    Result<UserDetailInfoVO> searchUserDetailById(@RequestParam Integer userId);

    /**
     * 验证用户密码
     *
     * @param userPasswordDTO 用户密码 DTO
     * @return {@link Result }<{@link UserDetailInfoVO }>
     */
    @PostMapping("/user/validate")
    Result<UserDetailInfoVO> validateUserPassword(@RequestBody UserPasswordDTO userPasswordDTO);


    /**
     * 保存用户登录记录信息
     *
     * @param loginRecordDto 登录记录 DTO
     * @return {@link Result }<{@link Integer }>
     */
    @PostMapping("/user/login/record/save")
    Result<Integer> saveUserLoginRecordInfo(@RequestBody LoginRecordDto loginRecordDto);

    /**
     * 发送登录验证码
     *
     * @param phone 电话
     * @param code  法典
     * @return {@link Result }<{@link String }>
     */
    @PostMapping("/user/account/validate/send")
    Result<String> sendLoginValidateCode(@RequestParam String phone, @RequestParam String code);


    // 业务相关

    /**
     * 查询用户门店部门信息
     *
     * @param userStoreDTO 用户存储 DTO
     * @return {@link Result }<{@link UserAndDeptUsersVO }>
     */
    @PostMapping("/user/select_users_store")
    Result<UserAndDeptUsersVO> selectUsersStore(@RequestBody UserStoreDTO userStoreDTO);


    /**
     * 按团队 ID 搜索用户 ID
     *
     * @param userIdByTeamIdDTO 按团队 ID DTO 划分用户 ID
     * @return {@link Result }<{@link List }<{@link Integer }>>
     */
    @PostMapping("/userIdsByTeamIds")
    Result<List<Integer>> searchUserIdsByTeamIds(@RequestBody UserIdByTeamIdDTO userIdByTeamIdDTO);

    /**
     * 批量按手机号转换成userId
     *
     * @param mobilesDTO 移动 DTO
     * @return {@link Result }<{@link List }<{@link UserInfoVO }>>
     */
    @PostMapping("/user/mobile/convert")
    Result<List<UserInfoVO>> mobile2UserId(@RequestBody List<String> mobilesDTO);

    /**
     * 批量按工号转换成userId
     *
     * @param jobNumbers 职位编号
     * @return {@link Result }<{@link List }<{@link UserInfoVO }>>
     */
    @PostMapping("/user/jobNumber/convert")
    Result<List<UserInfoVO>> jobNumber2UserId(@RequestBody List<String> jobNumbers);


    @PostMapping("/user/digitization/sync")
    Result<String> syncDigitizationUsers(@RequestBody List<UsersDTO> usersDTOList);

    @GetMapping("/user/searchQuit/searchUserByRoleId")
    Result<List<Integer>> searchUserByRoleId(@RequestParam Integer roleId);


    /**
     * 发送消息
     *
     * @param phone 电话
     * @param code  法典
     * @return {@link Result }<{@link String }>
     */
    @GetMapping("/user/sendMessage")
    Result<String> sendMessage(@RequestParam String phone, @RequestParam String code);

    /**
     * 通过 UUID 搜索二维码信息
     *
     * @param uuid uuid
     * @return {@link Result }<{@link UserQrCodeVO }>
     */
    @GetMapping("/qrcode/searchByUuid")
    Result<UserQrCodeVO> searchQrCodeByUuid(@RequestParam String uuid);

    /**
     * 通过 UUID 获取二维码文件信息
     *
     * @param uuid uuid
     * @return {@link Result }<{@link UserQrCodeVO }>
     */
    @GetMapping("/qrcode/getQrCodeFileByUuid")
    Result<QrCodeFileVO> getQrCodeFileByUuid(@RequestParam String uuid);

    /**
     * 增加二维码短链记录
     *
     * @param qrCodeFileDTO 二维码文件 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @PostMapping("/user/insertQrCodeFile")
    Result<Boolean> insertQrCodeFile(@RequestBody QrCodeFileDTO qrCodeFileDTO);

    /**
     * 作废用户所有二维码短链记录
     *
     * @param userId 用户 ID
     * @return {@link Result }<{@link Boolean }>
     */
    @GetMapping("/user/codeInvalid")
    Result<Boolean> codeInvalid(Integer userId);


    @GetMapping("/user/searchByUserId")
    Result<UserInfoVO> searchByUserId(@RequestParam Integer userId);


    /**
     * 按userId查询用户基本信息
     *
     * @param userId 用户 ID
     * @return {@link Result }<{@link UserInfoVO }>
     */
    @PostMapping("/user/name/search/by/id")
    Result<UserInfoVO> searchUserName(@RequestParam Integer userId);

    /**
     * 按userId批量查询用户基本信息
     *
     * @param userIds 用户 ID
     * @return {@link Result }<{@link List }<{@link UserInfoVO }>>
     */
    @PostMapping("/user/name/search/by/ids")
    Result<List<UserInfoVO>> searchUserNameByUserIds(@RequestBody List<Integer> userIds);

    @PostMapping("/user/name/batch/search/by/id")
    Result<List<UserInfoVO>> searchUserNameBatch(@RequestBody List<Integer> userIds);


    /**
     * 批量查询用户所属门店信息
     *
     * @param userIds 用户 ID
     * @return {@link Result }<{@link List }<{@link UserStoreVO }>>
     */
    @PostMapping("/user/store/batch/search/by/id")
    Result<List<UserStoreVO>> searchUserStoreBatch(@RequestBody List<Integer> userIds);

    /**
     * 按部门名称获取部门id
     *
     * @param deptName 部门名称
     * @return {@link Result }<{@link Integer }>
     */
    @GetMapping("/user/getDeptByName")
    Result<Integer> getDeptByName(@RequestParam String deptName);

    /**
     * 按部门id获取部门名称
     *
     * @param deptId 部门 ID
     * @return {@link Result }<{@link String }>
     */
    @GetMapping("/user/getDeptById")
    Result<String> getDeptById(@RequestParam Integer deptId);

    /**
     * 按部门id获取子部门Id列表
     *
     * @param deptId 部门 ID
     * @return {@link Result }<{@link List }<{@link Integer }>>
     */
    @PostMapping("/fund/getChildDeptById")
    Result<List<Integer>> getChildDeptById(@RequestBody Integer deptId);

    @Deprecated
    @GetMapping("/user/getAreaByUserId")
    Result<String> getAreaByUserId(@RequestParam Integer userId);


    /**
     * 生成二维码图片
     *
     * @param generateCodeDTO 生成代码 DTO
     * @return {@link ResponseEntity }<{@link byte[] }>
     */
    @PostMapping("/generateCodeFile")
    ResponseEntity<byte[]> generateCodeFile(@RequestBody GenerateCodeDTO generateCodeDTO);
    /**
     * 生成二维码
     *
     * @param generateCodeDTO 生成代码 DTO
     * @return {@link Result }<{@link String }>
     */
    @PostMapping("/generateCode")
    Result<String> generateCode(@RequestBody  GenerateCodeDTO generateCodeDTO);

    @PostMapping("/generateQrCodeUrl")
    String generateQrCodeUrl(@RequestBody @Validated QrCodeDTO qrCodeDTO);

    /**
     * 按部门名称模糊查询部门Id列表
     *
     * @param deptName 部门名称
     * @return {@link List }<{@link Integer }>
     */
    @PostMapping("/getDeptIdByLikeName")
    Result<List<Integer>> getDeptIdByLikeName(@RequestParam String deptName);

    /**
     * 根据部门ids获取用户信息
     *
     * @param deptIds 部门 ID
     * @return {@link List }<{@link UserStoreVO }>
     */

    @PostMapping("/getUserStoreVoByDeptIds")
    Result<List<UserStoreVO>> getUserStoreVoByDeptIds(@RequestBody List<Integer> deptIds);

    /**
     * 获取第一层级信息 (大区、公司、电销部)
     *
     * @param deptId
     * @return
     */
    @GetMapping("/dept/region/info")
    Result<RegionInfoVO> getRegionInfoByDeptId(@RequestParam("deptId") Integer deptId);

    @PostMapping("/getStoreManagerByStoreIDAndRoleId")
    Result<List<Integer>> getUserIdByStoreIdAndRoleId(@RequestBody getUserIdByStoreIdAndRoleIdDTO dto);

    @PostMapping("/user/getUserIdByRoleIds")
    Result<List<Integer>> getUserIdByRoleIds(@RequestBody SearchUserIdByRoleIdsDTO dto);

    @PostMapping("/user/sync/getSyncDeptByLhDeptIds")
    Result<List<DeptSyncInfoVO>> getSyncDeptByLhDeptIds(@RequestBody SearchDeptSyncInfoDTO dto);

    @PostMapping("/getTheBranchNameBasedOnTheTeamId")
    Result<List<DeptInfoVO>> getTheBranchNameBasedOnTheTeamId(@RequestBody List<Integer> teamIds);

    @PostMapping("/dept/getDeptUsers")
    Result<List<Integer>> getDeptUsers(@RequestBody List<Integer> deptIds);

    @PostMapping("/user/queryUserSyncInfoList")
    Result<List<UserSyncInfoListVO>> queryUserSyncInfoList(@RequestBody SearchUserSyncInfoDTO dto);

    /**
     * 根据名称模糊查询用户id
     *
     * @param userName 用户名
     * @return {@link Result }<{@link List }<{@link Integer }>>
     */
    @PostMapping("/user/getUserIdByLikeNameList")
    Result<List<Integer>> getUserIdByLikeNameList(@RequestParam String userName);

    @PostMapping("/user/saveOrUpdateUser")
    Result<UserInfoVO> saveOrUpdateUser(@RequestBody SaveOrUpdateUserDTO dto);

    /**
     * 根据类型获取部门列表
     */
    @PostMapping("/dept/getDeptListByDeptType")
    Result<List<Tree<Integer>>> getDeptListByDeptType(@RequestBody DeptListDTO deptListDTO);

    /**
     * 部门分页
     */
    @PostMapping("/dept/getDeptPage")
    Result<Page<DeptPageVO>> getDeptPage(@RequestBody DeptListDTO deptListDTO);

    /**
     * 保存或更新部门
     *
     * @param saveOrUpdateDeptDTO 保存或更新部门 dto
     * @return {@link Boolean }
     */
    @PostMapping("/dept/saveOrUpdateDept")
    Result<Boolean> saveOrUpdateDept(@RequestBody SaveOrUpdateDeptDTO saveOrUpdateDeptDTO);

    /**
     * 根据条件搜索用户基本分页信息
     */
    @PostMapping("/user/getUserList")
    Result<Page<UserBasicPageVO>> searchUserBasicPage(@RequestBody UserBasicPageDTO dto);

    /**
     * 保存或更新用户详情
     */
    @PostMapping("/user/saveOrUpdateUserDetail")
    Result<UserInfoVO> saveOrUpdateUser(@RequestBody UserDetailSaveOrUpdateDTO dto);

    @PostMapping("/role/getRoleList")
    Result<List<RoleVO>> getRoleList(@RequestBody RoleListPlatformDTO platform);

    @PostMapping("/dept/getDeptDetail")
    Result<DeptDetailVO> getDeptDetail(@RequestBody DeptDetailDTO detailDTO);

    @GetMapping("/getStoreInfoByDeptId")
    @ApiOperation("根据部门id获取区域信息")
    Result<StoreInfoVO> getStoreInfoByDeptId(@Param("deptId") Integer deptId);

    /**
     * 更新用户删除标志
     *
     * @param userId 用户ID
     * @param deleteFlag 删除标志：0=在职，1=离职
     * @return 更新结果
     */
    @PostMapping("/user/updateDeleteFlag")
    @ApiOperation("更新用户删除标志")
    Result<Boolean> updateUserDeleteFlag(@RequestParam("userId") Integer userId, @RequestParam("deleteFlag") Integer deleteFlag);

    @PostMapping("/dept/getStoreInfoByDeptId")
    @ApiOperation("根据团队获取上级门店信息")
    Result<StoreInfoVO> getStoreInfoByTeamId(@RequestBody SearchStoreInfoByTeamIdDTO dto);
    /**
     * 接收各系统待办消息
     *
     * @param todoInfoMessageDTO
     * @return
     */
    @PostMapping("/user/todo/deal/message")
    Result<TodoInfoMessageVO> dealMessage(@RequestBody TodoInfoMessageDTO todoInfoMessageDTO);

    /**
     * 按角色 ID 获取 DEPT 用户
     *
     * @param deptUsersByRoleIdsDTO 按角色 ID DTO 划分 DEPT 用户
     * @return {@link Result }<{@link List }<{@link UserInfoVO }>>
     */
    @PostMapping("/user/dept/by/role")
    Result<List<UserInfoVO>> getDeptUsersByRoleIds(@RequestBody DeptUsersByRoleIdsDTO deptUsersByRoleIdsDTO);

    /**
     * 按 ID 获取用户信息
     *
     * @param userId 用户 ID
     * @return {@link Result }<{@link UserInfoVO }>
     */
    @PostMapping("/user/info/by/id")
    Result<UserInfoVO> getUserInfoById(@RequestParam("userId") Integer userId);


    /**
     * 按角色 ID 获取用户信息
     *
     * @param roleId 角色 ID
     * @return {@link Result }<{@link List }<{@link UserInfoVO }>>
     */
    @GetMapping("/user/infoByRoleId")
    Result<List<UserInfoVO>> getUserInfoByRoleId(@RequestParam("roleId") Integer roleId);

    /**
     * 根据分部名称获取部门信息
     *
     * @param deptName
     * @return
     */
    @GetMapping("/user/getDeptDetailVO")
    Result<List<DeptDetailVO>> getDeptDetailVO(@RequestParam("deptName") String deptName);

    @GetMapping("/user/regionList")
    List<RegionInfoVO> regionList();

    @GetMapping("/user/getTeamManagerByTeamId")
    Result<UserInfoVO> getTeamManagerByTeamId(@RequestParam Integer teamId);

    @PostMapping("/user/todo/listByUserId")
    Result<Page<TodoListVO>> todoList(@RequestBody TodoListDTO todoListDTO);

    //    @PostMapping("/dept/getDeptInfoByDeptId")
//     Result<RegionInfoVO> getDeptInfoByDeptId(@RequestBody ThreePartyDeptDTO threePartyDeptDTO);
    @PostMapping("/user/getUserDingDeptMapping")
    List<UserDingDeptMappingVO> getUserDingDeptMapping(@RequestBody UserDingDeptMappingDTO userDingDeptMappingDTO);

    @PostMapping("/user/getDeptDetailVOByDeptName")
    Result<DeptDetailVO> getDeptDetailVOByDeptName(@RequestParam("deptName") String deptName);

    @PostMapping("/user/getDeptDetailListVOByDeptName")
    Result<List<DeptDetailVO>> getDeptDetailListVOByDeptName(@RequestBody List<String> deptNames);


    /**
     * 按部门id获取子部门Id列表
     *
     * @param deptIds 部门 ID
     * @return {@link Result }<{@link List }<{@link Integer }>>
     */
    @PostMapping("/fund/getChildNameDeptById")
    Result<List<DeptEntityVO>> getChildNameDeptById(@RequestBody List<Integer> deptIds);

    @PostMapping("/user/getDeptDetailVOByjdDeptId")
    Result<DeptDetailVO> getDeptDetailVOByjdDeptId(@RequestParam String deptId);

    //    @PostMapping("/dept/getThreePartyDeptList")
//    Result<Page<DisposalCompanyVO>> getThreePartyDeptList(@RequestBody ThreePartyDeptDTO threePartyDeptDTO);
    @PostMapping("/user/getJinDieSyncDeptByLhDeptIds")
    Result<List<DeptSyncInfoVO>> getJinDieSyncDeptByLhDeptIds(@RequestBody SearchDeptSyncInfoDTO searchDeptSyncInfoDTO);

    /**
     * 按人员名称搜索用户详细信息
     *
     * @param name 人员名称
     * @return {@link Result }
     */
    @GetMapping("/user/search/info/by/name")
    Result<UserDetailInfoVO> searchUserDetailByName(@RequestParam String name);
    @PostMapping("/user/sync/getSyncDeptByUserId")
    Result<List<DeptSyncInfoVO>> getSyncDeptByUserId(@RequestBody SearchDeptSyncInfoDTO dto);

    /**
     * 获取大区门店列表
     * @param regionListDTO
     * @return
     */
    @PostMapping("/user/getRegionList")
    Result<List<Tree<Integer>>> getRegionListInfo(@RequestBody RegionListDTO regionListDTO) ;

    /**
     * 贷后使用获取大区门店列表
     * @return
     */
    @PostMapping("/user/getRegionLists")
    Result<List<Tree<Integer>>> getRegionListInfos() ;


    /**
     * 根据部门列表
     *
     * @return 实例对象
     */
    @GetMapping("/user/getUserDeptAssignmentAll")
    Result<List<Integer>> getUserDeptAssignmentAll(@RequestParam("userId") Integer userId);


    /**
     * 根据业务员id查询业务员身份证号
     */
    @GetMapping("/user/getIdNumberCardByUserId")
    Result<String> getIdNumberCardByUserId(@RequestParam("userId") Integer userId);


    /**
     * 获取贷后值班日志
     */
    @PostMapping("/api/v1/duty/getDutyLog")
    Result<Page<GetDutyLoginVO>> getDutyLogin(@RequestBody GetDutyLoginDTO dto);
    /**
     * 根据id判断是否为值班账号
     */
    @PostMapping("/api/v1/duty/isDuty/{userId}")
    Result<Boolean> isDuty(@PathVariable Integer userId);


    /**
     * 根据部门id获取用户信息
     */
    @PostMapping("/user/getUserDeptAssignmentAllByDeptId")
    Result<List<UserDeptRoleVO>> getUserDeptAssignmentAllByDeptId(@RequestBody DeptAssetUserInfoByDeptIdDTO dto);

    /**
     * 三方处置公司列表
     *
     * @param threePartyDeptDTO
     * @return
     */
    @PostMapping("/dept/getThreePartyDeptList")
    Result<Page<DisposalCompanyVO>> getThreePartyDeptList(@RequestBody ThreePartyDeptDTO threePartyDeptDTO);


    @PostMapping("/user/exitUserByRoleIds")
    Result<List<Integer>> exitUserByRoleIds(@RequestBody SearchUserExitRoleIdListDTO dto);

    @PostMapping("/dept/getDeptIdListByType")
    Result<List<Integer>> getDeptIdListByType(@RequestBody SearchDeptListByTypeDTO dto);
    /**
     * 根据传入部门ID及角色Id获取用户
     *
     * @return {@link Result }<{@link List }<{@link DeptEntityVO }>>
     */
    @PostMapping("/dept/getUserIdsByRoleIdsAndDeptIds")
    Result<Map<Integer,List<UserInfoVO>>> getUserIdsByRoleIdsAndDeptIds(@RequestBody DeptUsersByRoleIdsDTO deptUsersByRoleIdsDTO);

    /**
     *
     * 根据部门ID获取分部门ids
     * @param deptId
     * @return
     */
    @PostMapping("/fund/getBranchDeptById")
    Result<List<Integer>> getBranchDeptById(@RequestBody Integer deptId);

    /**
     * 根据部门ID获取门店ids
     * @param deptId
     * @return
     */
    @PostMapping("/fund/getChildStoreById")
    Result<List<Integer>> getChildStoreById(@RequestBody Integer deptId);
    /**
     * 向所有实例中具有用户id的连接发送消息
     * @param userId 用户id
     * @param message 消息内容
     * @return 是否至少有一个实例成功处理
     */
    @PostMapping("/user/sse/sendByUserId")
    Result<Boolean> sendByUserId(@RequestParam Integer userId, @RequestParam String message);
    /**
     * 检查所有实例中是否存在指定用户id的连接
     * @param userId 用户id
     * @return 是否至少有一个实例存在匹配的连接
     */
    @GetMapping("/user/sse/existsByUserId")
    Result<Boolean> existsByUserId(@RequestParam Integer userId);
    /**
     * 向所有实例中具有指指定用户id的连接发送事件
     * @param userId 用户id
     * @param event 事件名称
     * @return 是否至少有一个实例成功处理
     */
    @PostMapping("/user/sse/sendEventByUserId")
     Result<Boolean> sendEventByUserId(@RequestParam Integer userId, @RequestParam String event);
    /**
     * 向所有实例中具有指定用户id的连接发送带有数据的事件
     * @param userId 用户id
     * @param event 事件名称
     * @param message 消息内容
     * @return 是否至少有一个实例成功处理
     */
    @PostMapping("/user/sse/sendEventWithDataByUserId")
    Result<Boolean> sendEventWithDataByUserId(@RequestParam Integer userId,
                                             @RequestParam String event,
                                             @RequestParam String message);

}