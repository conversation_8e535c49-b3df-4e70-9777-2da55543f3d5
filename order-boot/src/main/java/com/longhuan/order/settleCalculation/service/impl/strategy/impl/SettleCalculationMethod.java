package com.longhuan.order.settleCalculation.service.impl.strategy.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.pojo.dto.SettleCalculationContext;
import com.longhuan.order.pojo.vo.SettleCalculationVO;
import com.longhuan.order.settleCalculation.service.impl.strategy.impl.enums.LiquidatedDamagesRatioEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

class SettleCalculationMethod {
    static final int CLOSED_PERIOD_REPAY_MODE = 1;
    static final int DEFAULT_CLOSED_PERIOD = 6;
    public static SettleCalculationVO defaultSettlementPolicy(SettleCalculationContext context) {
        //封闭期外结清策略
        if (context.getClosedPeriod() <= context.getPaidPeriods()) {
            return directSettlement(context.getFundSettlementAmount());
        }
        if (Objects.equals(context.getClosedPeriodRepayMode(),0)) {
            // 结清金额=资方结清金额
            return SettleCalculationMethod.directSettlement(context.getFundSettlementAmount());
        }
        //封闭期内结清策略
        else {
            //封闭期模式
            if (ObjUtil.equals(context.getClosedPeriodRepayMode(),CLOSED_PERIOD_REPAY_MODE)) {
                // 结清金额=资方结清金额+放款金额*(封闭期数-已还期数) * 产品综合费率
                Assert.notNull(context.getLoanAmount(), "放款金额不能为空");
                Assert.notNull(context.getProductRate(), "产品综合费率不能为空");
                return calculateWithProductRate(context.getFundSettlementAmount(), context.getLoanAmount(),
                        context.getClosedPeriod(), context.getPaidPeriods(), context.getProductRate());
            }
            //违约金模式
            else {
                Assert.notBlank(context.getFundName(), () -> new BusinessException("资方名称不能为空"));
                //长安新生
                if (context.getFundName().equals("长安新生")) {
                    Assert.notNull(context.getRemainingPrincipal(),  () -> new BusinessException("剩余本金不能为空"));
                    // 已还期数<50%封闭期
                    if (context.getPaidPeriods() * 2 < context.getClosedPeriod()) {
                        return calculateWithPenalty(context.getFundSettlementAmount(),
                                context.getRemainingPrincipal(), LiquidatedDamagesRatioEnum.PENALTY_4_PERCENT);
                    }
                    // 已还期数>=50%封闭期
                    else {
                        return calculateWithPenalty(context.getFundSettlementAmount(),
                                context.getRemainingPrincipal(), LiquidatedDamagesRatioEnum.PENALTY_2_PERCENT);
                    }
                }
                //其他
                else {
                    //封闭期非6期
                    if (context.getClosedPeriod() != DEFAULT_CLOSED_PERIOD) {
                        Assert.notNull(context.getRemainingPrincipal(), () -> new BusinessException( "剩余本金不能为空"));
                        // 已还期数<50%封闭期
                        if (context.getPaidPeriods() * 2 < context.getClosedPeriod()) {
                            return calculateWithPenalty(context.getFundSettlementAmount(),
                                    context.getRemainingPrincipal(), LiquidatedDamagesRatioEnum.PENALTY_8_PERCENT);
                        }
                        // 已还期数>=50%封闭期
                        else {
                            return calculateWithPenalty(context.getFundSettlementAmount(),
                                    context.getRemainingPrincipal(), LiquidatedDamagesRatioEnum.PENALTY_6_PERCENT);
                        }
                    }
                    //封闭期6期
                    else {
                        //结清金额=资方结清金额+（剩余本金*2%或放款金额*客户风险等级对应利率）违约金
                        Assert.notNull(context.getRemainingPrincipal(),  () -> new BusinessException("剩余本金不能为空"));
                        Assert.notNull(context.getLoanAmount(),  () -> new BusinessException("放款金额不能为空"));
                        Assert.notNull(context.getCustomerRating(),  () -> new BusinessException("客户风险等级不能为空"));
                        return calculateWithDynamicRate(context.getFundSettlementAmount(), context.getRemainingPrincipal(), context.getLoanAmount(), context.getCustomerRating());
                    }

                }
            }
        }
    }
    /**
     * 结清金额=金额
     * @param amount 金额
     * @return SettleCalculationVO 结算信息
     */
    static SettleCalculationVO directSettlement(BigDecimal amount) {
        return new SettleCalculationVO()
                .setSettlementAmount(amount)
                .setSettleMode("封闭期外结清模式")
                .setSettleModeType(1);
    }

    /**
     * 结清金额=金额+放款金额*(封闭期数-已还期数) * 产品综合费率[利息]
     * @param amount 金额
     * @param loanAmount 放款金额
     * @param closedPeriod 封闭期数
     * @param paidPeriods 已还期数
     * @param productRate 产品综合费率
     * @return SettleCalculationVO 结算信息
     */
    public static SettleCalculationVO calculateWithProductRate(BigDecimal amount,
                                                               BigDecimal loanAmount,
                                                               int closedPeriod,
                                                               int paidPeriods,
                                                               BigDecimal productRate) {

        BigDecimal interest = loanAmount.multiply(BigDecimal.valueOf(closedPeriod - paidPeriods)).multiply(productRate).setScale(2, RoundingMode.HALF_UP);
        return new SettleCalculationVO()
                .setSettlementAmount(amount.add(interest))
                .setInterestAmount(interest)
                .setSettleMode("封闭期内封闭期模式")
                .setSettleModeType(3);

    }

    /**
     * 结清金额=金额+剩余本金*违约金比率(违约金)
     * @param amount 金额
     * @param remainingPrincipal 剩余本金
     * @param penaltyRate 违约金比例
     * @return SettleCalculationVO 结算信息
     */
    public static SettleCalculationVO calculateWithPenalty(BigDecimal amount,
                                                           BigDecimal remainingPrincipal,
                                                           LiquidatedDamagesRatioEnum penaltyRate) {
        BigDecimal penalty = remainingPrincipal.multiply(penaltyRate.getValue()).setScale(2, RoundingMode.HALF_UP);
        return new SettleCalculationVO()
                 .setSettlementAmount(amount.add(penalty))
                 .setPenaltyAmount(penalty)
                 .setSettlePenaltyRate(penaltyRate.getValue())
                .setSettleMode("封闭期内违约金模式"+penaltyRate.getDesc())
                .setSettleModeType(2);
    }

    /**
     * 结清金额=金额+（剩余本金*2%或放款金额*客户风险等级对应利率）违约金
     * @param amount 金额
     * @param remainingPrincipal 剩余本金
     * @param loanAmount 放款金额
     * @param customerRating 客户风险等级
     * @return SettleCalculationVO 结算信息
     */
    public static SettleCalculationVO calculateWithDynamicRate(BigDecimal amount,
                                                               BigDecimal remainingPrincipal,
                                                               BigDecimal loanAmount,
                                                               Integer customerRating) {
        // 根据客户风险等级获取基础违约金比例
        LiquidatedDamagesRatioEnum penaltyRate = switch (customerRating) {
            case 1 -> LiquidatedDamagesRatioEnum.PENALTY_2_PERCENT;
            case 2 -> LiquidatedDamagesRatioEnum.PENALTY_1_PERCENT;
            case 3 -> LiquidatedDamagesRatioEnum.PENALTY_0_PERCENT;
            default -> throw new BusinessException("Invalid customer rating: " + customerRating);
        };
        // 计算两种违约金取较小值
        BigDecimal loanBasedPenalty = loanAmount.multiply(penaltyRate.getValue()).setScale(2, RoundingMode.HALF_UP);
        BigDecimal principalBasedPenalty = remainingPrincipal.multiply(LiquidatedDamagesRatioEnum.PENALTY_2_PERCENT.getValue()).setScale(2, RoundingMode.HALF_UP);
        BigDecimal finalPenalty = principalBasedPenalty.min(loanBasedPenalty);
        BigDecimal finalPenaltyRate= finalPenalty.compareTo(loanBasedPenalty)==0?penaltyRate.getValue():LiquidatedDamagesRatioEnum.PENALTY_2_PERCENT.getValue();
        String settleMode= finalPenalty.compareTo(loanBasedPenalty)==0?"客户等级利率"+penaltyRate.getDesc():"放款金额利率"+LiquidatedDamagesRatioEnum.PENALTY_2_PERCENT.getDesc();
        return new SettleCalculationVO()
                .setSettlementAmount(amount.add(finalPenalty))
                .setPenaltyAmount(finalPenalty)
                .setSettlePenaltyRate(finalPenaltyRate)
                .setSettleMode("封闭期内动态违约金模式"+settleMode)
                .setSettleModeType(2);
    }

}
