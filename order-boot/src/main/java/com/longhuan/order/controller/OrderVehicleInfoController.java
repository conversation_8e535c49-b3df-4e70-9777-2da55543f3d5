package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.vo.AppraiserInfoDTO;
import com.longhuan.order.pojo.vo.StoreAppraiserInfoDetailVO;
import com.longhuan.order.pojo.vo.StoreAppraiserInfoVO;
import com.longhuan.order.pojo.vo.VehicleInfoVO;
import com.longhuan.order.service.OrderVehicleInfoService;
import com.longhuan.order.service.RiskService;
import com.longhuan.user.pojo.vo.StoreAppraisersListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "订单车辆信息接口")
@RestController
@RequestMapping("/vehicle")
@RequiredArgsConstructor
public class OrderVehicleInfoController {

    private final OrderVehicleInfoService orderVehicleInfoService;

    private final RiskService riskService;

    @ApiOperation(value = "根据订单号查询车辆信息")
    @PostMapping("/info")
    public Result<VehicleInfoVO> getVehicleInfo(@RequestBody @Validated VehicleInfoDTO vehicleInfoDTO) {
        return Result.success(orderVehicleInfoService.getVehicleInfo(vehicleInfoDTO));
    }

    @ApiOperation(value = "车辆信息补录")
    @PostMapping("/edit")
    public Result<Boolean> updateVehicleInfo(@RequestBody @Validated VehicleInfoEditDTO vehicleInfoDTO) {
        return Result.success(orderVehicleInfoService.updateVehicleInfo(vehicleInfoDTO));
    }

    /**
     * 车辆报告
     */
    @GetMapping("/getCarReport/{orderId}")
    public Result<String> getCarReport(@PathVariable("orderId") Integer orderId) {
        return Result.success(orderVehicleInfoService.getCarReport(orderId));
    }

    /**
     * 车三百评估值
     */
    @GetMapping("/getCar300Price/{orderId}")
    public Result<String> getCar300Price(@PathVariable("orderId") Integer orderId) {
        return Result.success(orderVehicleInfoService.getCar300Price(orderId));
    }

    @PostMapping("/modifyTheVehicleInformation")
    public Result<Boolean> modifyTheVehicleInformation(@RequestBody @Validated VehicleInfoEditDTO vehicleInfoDTO) {
        return Result.success(orderVehicleInfoService.modifyTheVehicleInformation(vehicleInfoDTO));
    }

    /**
     * 门店评估师列表
     */
    @PostMapping("/storeAppraisersList")
    public Result<List<StoreAppraisersListVO>> storeAppraisersList(@RequestBody StoreAppraisersListDTO dto) {
        return Result.success(orderVehicleInfoService.storeAppraisersList(dto));
    }
    /**
     * 门店评估师回显
     */
    @PostMapping("/storeAppraiserInfo")
    public Result<StoreAppraiserInfoDetailVO> storeAppraiserInfo(@RequestParam("orderId") Integer orderId) {
        return Result.success(orderVehicleInfoService.storeAppraiserInfo(orderId));
    }
    /**
     * 门店评估师分配
     */
    @PostMapping("/storeAppraiserAssignment")
    public Result<Boolean> storeAppraiserAssignment(@RequestBody StoreAppraiserAssignmentDTO dto) {
        return Result.success(orderVehicleInfoService.storeAppraiserAssignment(dto));
    }
    /**
     * 门店评估师保存
     *
     * @param infoDTO
     * @return {@link Result }<{@link Boolean }>
     */
    @PostMapping("/appraiser/info/save")
    public Result<Boolean> saveStoreAppraiserInfo(@RequestBody AppraiserInfoDTO infoDTO, @CurrentUser LoginUser loginUser) {


        return Result.success(orderVehicleInfoService.saveStoreAppraiserInfo(infoDTO, loginUser));
    }

    /**
     * 重新推送蓝本价评估报告
     *
     * @param infoDTO
     * @return {@link Result }<{@link Boolean }>
     */
    @PostMapping("/lanben/report/push/again")
    public Result<Boolean> saveStoreAppraiserInfo(@RequestBody OrderIdDTO infoDTO, @CurrentUser LoginUser loginUser) {

        return Result.success(riskService.rePushLanBenReport(infoDTO.getOrderId()));
    }

    /**
     * 车辆信息暂存
     *
     * @param vehicleInfoDTO
     */
    @ApiOperation(value = "车辆信息暂存")
    @PostMapping("/stagingVehicleInfo")
    public Result<Boolean> stagingVehicleInfo(@RequestBody VehicleInfoEditDTO vehicleInfoDTO) {
        return Result.success(orderVehicleInfoService.stagingVehicleInfo(vehicleInfoDTO));
    }

}
