package com.longhuan.order.signBank.baofu;

import com.longhuan.order.signBank.baofu.builder.BaoFuRequestBuilder;
import com.longhuan.order.signBank.baofu.dto.BankCardBindParams;
import com.longhuan.order.signBank.baofu.dto.BankCardBindRequest;
import com.longhuan.order.signBank.baofu.dto.BankCardBindVerfyResult;
import com.longhuan.order.signBank.baofu.processor.BaoFuResponseProcessor;
import com.longhuan.order.signBank.baofu.strategy.BankCardBindParamsProcessor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 重构后的BaoFu组件测试类
 */
@Slf4j
public class BaoFuRefactoredTest {

    @Test
    public void testBaoFuRequestBuilder() {
        // 测试请求构建器
        try {
            BankCardBindParams params = new BankCardBindParams();
            params.setCard_no("****************");
            params.setCard_name("测试用户");
            params.setCard_id("****************78");
            params.setCard_mobile("***********");
            params.setCard_type("101");
            params.setId_card_type("01");

            BankCardBindParamsProcessor processor = new BankCardBindParamsProcessor();

            // 由于没有真实的证书文件，这里预期会抛出异常
            BaoFuRequestBuilder.buildRequest(
                params,
                12345,
                BankCardBindRequest.class,
                processor,
                "test_member_id",
                "test_terminal_id",
                "invalid_cert_path"
            );

            assert false : "应该抛出异常";
        } catch (Exception e) {
            log.info("请求构建器测试通过（预期抛出异常）: {}", e.getMessage());
        }
    }

    @Test
    public void testBaoFuResponseProcessor() {
        // 测试响应处理器
        try {
            // 模拟解密配置
            Map<String, Set<String>> decryptConfig = new HashMap<>();
            decryptConfig.put("BankCardBindVerfyResult", Set.of("protocol_no", "bank_code", "bank_name"));

            // 使用无效的响应数据，预期会抛出异常
            BaoFuResponseProcessor.processResponse(
                "invalid_response",
                BankCardBindVerfyResult.class,
                decryptConfig,
                "invalid_public_cert",
                "invalid_private_cert",
                "invalid_password"
            );

            assert false : "应该抛出异常";
        } catch (Exception e) {
            log.info("响应处理器测试通过（预期抛出异常）: {}", e.getMessage());
        }
    }

    @Test
    public void testBaoFuResponseProcessorWithEmptyResponse() {
        // 测试空响应处理
        try {
            Map<String, Set<String>> decryptConfig = new HashMap<>();
            
            BaoFuResponseProcessor.processResponse(
                "",
                BankCardBindVerfyResult.class,
                decryptConfig,
                "cert_path",
                "private_cert",
                "password"
            );

            assert false : "空响应应该抛出异常";
        } catch (Exception e) {
            assert e.getMessage().contains("宝付返回结果为空") : "异常消息应该包含'宝付返回结果为空'";
            log.info("空响应处理测试通过");
        }
    }

    @Test
    public void testDecryptConfigStructure() {
        // 测试解密配置结构
        Map<String, Set<String>> decryptConfig = new HashMap<>();
        decryptConfig.put("BankCardBindResult", Set.of("unique_code"));
        decryptConfig.put("BankCardBindVerfyResult", Set.of("protocol_no", "bank_code", "bank_name"));

        // 验证配置结构
        assert decryptConfig.containsKey("BankCardBindResult") : "应该包含BankCardBindResult配置";
        assert decryptConfig.containsKey("BankCardBindVerfyResult") : "应该包含BankCardBindVerfyResult配置";
        
        Set<String> bindFields = decryptConfig.get("BankCardBindResult");
        assert bindFields.contains("unique_code") : "BankCardBindResult应该包含unique_code字段";
        
        Set<String> verifyFields = decryptConfig.get("BankCardBindVerfyResult");
        assert verifyFields.contains("protocol_no") : "BankCardBindVerfyResult应该包含protocol_no字段";
        assert verifyFields.contains("bank_code") : "BankCardBindVerfyResult应该包含bank_code字段";
        assert verifyFields.contains("bank_name") : "BankCardBindVerfyResult应该包含bank_name字段";

        log.info("解密配置结构测试通过");
    }
}
