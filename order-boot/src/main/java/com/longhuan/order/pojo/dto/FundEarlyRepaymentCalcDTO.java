package com.longhuan.order.pojo.dto;

import com.longhuan.approve.api.pojo.vo.FundRepayCalcVO;
import com.longhuan.approve.api.pojo.vo.LoanSettlementVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 提前结清还款试算
 */
@Data
@Accessors(chain = true)
public class FundEarlyRepaymentCalcDTO {
    /**
     * 资方结清试算
     */
    private FundEarlyRepaymentCalcDTO.FundSettleDTO fundSettleDTO;

    /**
     * 资方名称
     */
    private String fundName;

    /**
     * 结清试算
     */
    private LoanSettlementVO settlementVO;

    /**
     * 还款总金额
     */
    private BigDecimal repayAmountTotal;

    /**
     * 结清违约金
     */
    private BigDecimal firmRepayAmountTotal;

    /**
     * 结清本息
     */
    private BigDecimal fundRepayAmt;
    /**
     * 违约金减免金额
     */
    private BigDecimal deductionAmount;
    /**
     * 结清违约金利率
     */
    private BigDecimal settlePenaltyRate = BigDecimal.ZERO;
    /**
     * 已还期数
     */
    private Integer instalmentsRepaid;

    /**
     * gps分期费用结清金额
     */
    private BigDecimal remainingGpsAmount;

    @Data
    @Accessors(chain = true)
    public static class FundSettleDTO {
        /**
         * 还款日期
         */
        private String repayDate;

        /**
         * 应还本金
         * numeric(16, 2)，必填
         */
        private BigDecimal principal = BigDecimal.ZERO;

        /**
         * 违约金
         */
        private BigDecimal penalty = BigDecimal.ZERO;

        /**
         * 应还利息
         */
        private BigDecimal interest = BigDecimal.ZERO;

        /**
         * 应还罚息
         */
        private BigDecimal fine = BigDecimal.ZERO;

        /**
         * 应还款金额
         */
        private BigDecimal repayAmt = BigDecimal.ZERO;

        /**
         * 应还复利
         */
        private BigDecimal comInterest = BigDecimal.ZERO;

        /**
         * 应还费用
         */
        private BigDecimal odFeeAmt = BigDecimal.ZERO;

        /**
         * 应还担保费
         */
        private BigDecimal guaraFeeAmt = BigDecimal.ZERO;

        /**
         * 应还担保费罚息
         */
        private BigDecimal guaraFeeOdAmt = BigDecimal.ZERO;


        /**
         * 期次
         */
        private Integer period;

        /**
         * 还款账户	线下还款账户(富民)
         */
        FundRepayCalcVO.FundRepayAcct fundRepayAcct;
    }


}
