package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.RiskAccidentCarDTO;
import com.longhuan.order.pojo.dto.RiskAccidentCarLogDTO;
import com.longhuan.order.pojo.entity.RiskAccidentCarEntity;
import com.longhuan.order.pojo.entity.RiskAccidentCarLogEntity;
import com.longhuan.order.pojo.vo.RiskAccidentCarVO;
import com.longhuan.order.service.RiskAccidentCarService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 风控事故车数据 控制层
 *
 * <AUTHOR> css
 * @date : 2025-08-26
 */
@Api(tags = "风控事故车功能接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/riskAccidentCar")
public class RiskAccidentCarController {
    private final RiskAccidentCarService RiskAccidentCarService;

    /**
     * 查询列表
     *
     * @return 实例对象
     */
    @ApiOperation(value = "分页查询风控事故车")
    @PostMapping("/page/RiskAccidentCarList")
    public Result<Page<RiskAccidentCarVO>> selectRiskAccidentCarList(@RequestBody RiskAccidentCarDTO dto) {
        return Result.success(RiskAccidentCarService.selectRiskAccidentCarList(dto));
    }

    /**
     * 查询操作记录
     *
     * @return 实例对象
     */
    @ApiOperation(value = "分页查询操作记录")
    @PostMapping("/page/RiskAccidentCarLogList")
    public Result<Page<RiskAccidentCarLogEntity>> selectRiskAccidentCarLogList(@RequestBody RiskAccidentCarLogDTO dto) {
        return Result.success(RiskAccidentCarService.selectRiskAccidentCarLogList(dto));
    }

    /**
     * 通过风控事故车id获取详情
     *
     * @param id 风控事故车id
     * @return {@link Result< RiskAccidentCarEntity >}
     */
    @ApiModelProperty("通过风控事故车id获取详情")
    @GetMapping("/queryInfoById/{id}")
    public Result<RiskAccidentCarEntity> queryInfo(@PathVariable("id") Integer id) {
        return Result.success(RiskAccidentCarService.queryById(id));
    }

    /**
     * 新增风控事故车
     *
     * @param dto 风控事故车信息
     */
    @PostMapping("/insertRiskAccidentCarInfo")
    public Result<Boolean> insertRiskAccidentCarInfo(@RequestBody @Validated RiskAccidentCarDTO dto) {
        return Result.success(RiskAccidentCarService.insertRiskAccidentCarInfo(dto));
    }

    /**
     * 修改风险事故车Vin
     *
     * @param id 风险事故车id
     * @param vin 车架号
     */
    @GetMapping("/updateVinById")
    public Result<Boolean> updateVinById(@RequestParam Integer id, @RequestParam String vin) {
        return Result.success(RiskAccidentCarService.updateVinById(id, vin));
    }

    /**
     * 修改风险事故车状态
     *
     * @param id 风险事故车id
     * @param remark 解除原因
     */
    @GetMapping("/updateStatusById")
    public Result<Boolean> updateStatusById(@RequestParam Integer id, @RequestParam Integer status, @RequestParam String remark) {
        return Result.success(RiskAccidentCarService.updateStatusById(id, status, remark));
    }

    /**
     * 通过风控事故车vin获取详情
     *
     * @param vin 风控事故车vin
     * @return {@link Result< RiskAccidentCarEntity >}
     */
    @ApiOperation("通过风控事故车vin获取详情")
    @GetMapping("/queryInfoByVin")
    public Result<RiskAccidentCarEntity> queryInfoByVin(@RequestParam("vin") String vin) {
        return Result.success(RiskAccidentCarService.queryInfoByVin(vin));
    }

    @ApiOperation(value = "导出风控事故车")
    @PostMapping(value = "/exportRiskAccident")
    public void exportRiskAccident(@RequestBody RiskAccidentCarDTO dto, HttpServletResponse response) {
        RiskAccidentCarService.exportRiskAccident(dto,response);
    }

    @ApiOperation("根据订单ID新增")
    @GetMapping("/queryInfoByOrderId")
    public Result<Boolean> insertByOrderId(@RequestParam("orderId")Integer orderId, @RequestParam("remarkExternal")String remarkExternal) {
        return Result.success(RiskAccidentCarService.insertByOrderId(orderId, remarkExternal));
    }


}