package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.LoanReservoirRulesDTO;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.LoanReservoirRulesVO;
import com.longhuan.order.service.LoanReservoirRulesService;
import com.longhuan.user.pojo.vo.DeptEntityVO;
import com.longhuan.user.pojo.vo.UserInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * LoanReservoirRulesServiceImol
 *
 * <AUTHOR>
 * @date 2025/8/18 11:14
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LoanReservoirRulesServiceImpl implements LoanReservoirRulesService {
    private final LoanReservoirRulesMapper loanReservoirRulesMapper;
    private final LoanReservoirRulesLogsMapper loanReservoirRulesLogsMapper;
    private final UserFeign userFeign;
    private final FundInfoMapper fundInfoMapper;
    private final OrderLoanReservoirMapper orderLoanReservoirMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final RedisService redisService;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final String CODE_SEQUENCE_PREFIX = "reservoir_rules_code_sequence:";
    private static final int MAX_SEQUENCE = 99;

    /**
     * 规则列表查询
     * @param dto
     * @param loginUser
     * @return
     */
    @Override
    public Page<LoanReservoirRulesVO> list(LoanReservoirRulesDTO dto, LoginUser loginUser) {

        Objects.requireNonNull(loginUser, "用户信息不存在");
        // 数据权限
        List<Integer> roleIds = loginUser.getRoleIds();

        MPJLambdaWrapper<LoanReservoirRulesEntity> queryWrapper = new MPJLambdaWrapper<LoanReservoirRulesEntity>()
                .selectAll(LoanReservoirRulesEntity.class)
                .eq(ObjUtil.isNotNull(dto.getEnable()), LoanReservoirRulesEntity::getEnable, dto.getEnable())
                .eq(ObjUtil.isNotNull(dto.getSourceType()), LoanReservoirRulesEntity::getSourceType, dto.getSourceType())
                .like(ObjUtil.isNotEmpty(dto.getRuleNumber()), LoanReservoirRulesEntity::getRuleNumber, dto.getRuleNumber())
                .eq(LoanReservoirRulesEntity::getDeleteFlag, 0)
                .orderByDesc(LoanReservoirRulesEntity::getCreateTime);

        if (ObjUtil.isNotEmpty(dto.getFundIds())) {
            String[] fundIdArray = dto.getFundIds().split(",");
            queryWrapper.and(wrapper -> {
                for (String fundId : fundIdArray) {
                    wrapper.or().apply("{0} = ANY(STRING_TO_ARRAY(fund_ids, ','))", fundId.trim());
                }
            });
        }

        if (ObjUtil.isNotNull(dto.getAreaId()) && ObjUtil.equals(0, dto.getAreaId())){
            queryWrapper.and(wp -> wp.isNotNull(LoanReservoirRulesEntity::getRegionIds)
                    .or()
                    .isNotNull(LoanReservoirRulesEntity::getDeptIds)
            );
        }else {
            if (ObjUtil.isNotEmpty(dto.getRegionIds())) {
                String[] regionIdArray = dto.getRegionIds().split(",");
                queryWrapper.and(wrapper -> {
                    for (String regionId : regionIdArray) {
                        wrapper.or().apply("{0} = ANY(STRING_TO_ARRAY(region_ids, ','))", regionId.trim());
                    }
                });
            }

            if (ObjUtil.isNotEmpty(dto.getDeptIds())) {
                String[] deptIdArray = dto.getDeptIds().split(",");
                queryWrapper.and(wrapper -> {
                    for (String deptId : deptIdArray) {
                        wrapper.or().apply("{0} = ANY(STRING_TO_ARRAY(dept_ids, ','))", deptId.trim());
                    }
                });
            }
        }

        if (ObjUtil.isNotNull(dto.getBeginDate()) && ObjUtil.isNotNull(dto.getEndDate())) {
            queryWrapper.and(wrapper -> wrapper
                    .or(on -> on.between(LoanReservoirRulesEntity::getBeginDate, dto.getBeginDate(), dto.getEndDate()))
                    .or(on -> on.between(LoanReservoirRulesEntity::getEndDate, dto.getBeginDate(), dto.getEndDate()))
                    .or(on -> on.ge(LoanReservoirRulesEntity::getBeginDate, dto.getBeginDate())
                            .le(LoanReservoirRulesEntity::getEndDate, dto.getEndDate()))
                    .or(on -> on.le(LoanReservoirRulesEntity::getBeginDate, dto.getBeginDate())
                            .ge(LoanReservoirRulesEntity::getEndDate, dto.getEndDate())));
        }

        // 权限控制
        //dataPermissionService.limitStoreQuota(loginUser, queryWrapper);

        Page<LoanReservoirRulesVO> pageList = loanReservoirRulesMapper.selectJoinPage(new Page<>(dto.getPageNum(), dto.getPageSize()), LoanReservoirRulesVO.class, queryWrapper);
        List<LoanReservoirRulesVO> records = pageList.getRecords();

        records.forEach(record -> {
            handleRecordData(record);
        });

        return pageList;
    }

    /**
     * 新增规则
     * @param req
     * @param loginUser
     */
    @Override
    public void save(LoanReservoirRulesDTO req, LoginUser loginUser) {
        String ruleNumber = generateCodeRuleNumber();
      /*  String number = ruleNumber.substring(0, ruleNumber.length() - 2);
        if (ObjUtil.equals( number,"99")){
            Assert.isFalse(ObjUtil.equals( number,"99"), () -> new BusinessException("当前编号已大于99"));
        }*/
        LoanReservoirRulesEntity loanReservoirRulesEntity = new LoanReservoirRulesEntity();
        LoanReservoirRulesLogsEntity loanReservoirRulesLogsEntity = new LoanReservoirRulesLogsEntity();

        loanReservoirRulesEntity.setFundIds(req.getFundIds());
        loanReservoirRulesEntity.setRegionIds(ObjUtil.isNotEmpty(req.getRegionIds()) ? req.getRegionIds() : null);
        loanReservoirRulesEntity.setDeptIds(ObjUtil.isNotEmpty(req.getDeptIds()) ? req.getDeptIds() : null);
        loanReservoirRulesEntity.setSourceType(req.getSourceType());
        loanReservoirRulesEntity.setEnable(req.getEnable());
        loanReservoirRulesEntity.setBeginDate(req.getBeginDate());
        loanReservoirRulesEntity.setEndDate(req.getEndDate());
        loanReservoirRulesEntity.setRuleNumber(ruleNumber);
        if(ObjUtil.isNotNull(loginUser) && ObjUtil.isNotNull(loginUser.getUserId())){
            loanReservoirRulesEntity.setCreateBy(loginUser.getUserId());
            loanReservoirRulesEntity.setUpdateBy(loginUser.getUserId());
            loanReservoirRulesLogsEntity.setCreateBy(loginUser.getUserId());
            loanReservoirRulesLogsEntity.setUpdateBy(loginUser.getUserId());
        }
        int insert = loanReservoirRulesMapper.insert(loanReservoirRulesEntity);
        if (insert > 0){
            loanReservoirRulesLogsEntity.setRuleId(loanReservoirRulesEntity.getId());
            loanReservoirRulesLogsEntity.setOperateEnable(loanReservoirRulesEntity.getEnable());
            loanReservoirRulesLogsMapper.insert(loanReservoirRulesLogsEntity);
        }
    }

    /**
     * 规则开启或关闭
     * @param loanReservoirRulesDTO
     * @param loginUser
     */
    @Override
    public void updateEnable(LoanReservoirRulesDTO loanReservoirRulesDTO, LoginUser loginUser) {
        Objects.requireNonNull(loginUser, "用户信息不存在");
        Integer ruleId = loanReservoirRulesDTO.getId();

        //  如果关闭，蓄水池订单流出
        if(ObjUtil.equals(loanReservoirRulesDTO.getEnable(), 1)) {
            ruleFailureToLoan(ruleId);
        }

        loanReservoirRulesMapper.update(new LambdaUpdateWrapper<LoanReservoirRulesEntity>()
                .set(LoanReservoirRulesEntity::getEnable, loanReservoirRulesDTO.getEnable())
                .eq(LoanReservoirRulesEntity::getId, loanReservoirRulesDTO.getId())
        );
        LoanReservoirRulesLogsEntity loanReservoirRulesLogsEntity = new LoanReservoirRulesLogsEntity();
        loanReservoirRulesLogsEntity.setRuleId(loanReservoirRulesDTO.getId());
        loanReservoirRulesLogsEntity.setOperateEnable(loanReservoirRulesDTO.getEnable());
        loanReservoirRulesLogsMapper.insert(loanReservoirRulesLogsEntity);


    }

    /**
     * 操作日志查询
     * @param dto
     * @param loginUser
     * @return
     */
    @Override
    public Page<LoanReservoirRulesVO> pageOperationList(LoanReservoirRulesDTO dto, LoginUser loginUser) {

        MPJLambdaWrapper<LoanReservoirRulesLogsEntity> queryWrapper = new MPJLambdaWrapper<LoanReservoirRulesLogsEntity>()
                .selectAs(LoanReservoirRulesEntity::getRuleNumber, LoanReservoirRulesVO::getRuleNumber)
                .selectAs(LoanReservoirRulesEntity::getSourceType, LoanReservoirRulesVO::getSourceType)
                .selectAs(LoanReservoirRulesEntity::getFundIds, LoanReservoirRulesVO::getFundIds)
                .selectAs(LoanReservoirRulesEntity::getRegionIds, LoanReservoirRulesVO::getRegionIds)
                .selectAs(LoanReservoirRulesEntity::getDeptIds, LoanReservoirRulesVO::getDeptIds)
                .selectAs(LoanReservoirRulesEntity::getBeginDate, LoanReservoirRulesVO::getBeginDate)
                .selectAs(LoanReservoirRulesEntity::getEndDate, LoanReservoirRulesVO::getEndDate)
                .selectAs(LoanReservoirRulesLogsEntity::getCreateTime, LoanReservoirRulesVO::getCreateTime)
                .selectAs(LoanReservoirRulesLogsEntity::getCreateBy, LoanReservoirRulesVO::getCreateBy)
                .selectAs(LoanReservoirRulesLogsEntity::getOperateEnable, LoanReservoirRulesVO::getEnable)
                .leftJoin(LoanReservoirRulesEntity.class, on -> on.eq(LoanReservoirRulesEntity::getId, LoanReservoirRulesLogsEntity::getRuleId))
                .eq(ObjUtil.isNotNull(dto.getEnable()),LoanReservoirRulesLogsEntity::getOperateEnable, dto.getEnable())
                .like(ObjUtil.isNotEmpty(dto.getRuleNumber()),LoanReservoirRulesEntity::getRuleNumber, dto.getRuleNumber())
                .eq(ObjUtil.isNotNull(dto.getSourceType()), LoanReservoirRulesEntity::getSourceType, dto.getSourceType())
                .eq(LoanReservoirRulesLogsEntity::getDeleteFlag, 0)
                .eq(LoanReservoirRulesEntity::getDeleteFlag, 0)
                .orderByDesc(LoanReservoirRulesLogsEntity::getCreateTime);

        if (ObjUtil.isNotNull(dto.getOperateStartDate()) && ObjUtil.isNotNull(dto.getOperateEndDate()) ){
            queryWrapper.between(LoanReservoirRulesLogsEntity::getCreateTime,dto.getOperateStartDate(), dto.getOperateEndDate());
        }

        //操作人
        List<Integer> operatorIdList = new ArrayList<>();
        if (StrUtil.isNotBlank(dto.getOperatorName())) {
            operatorIdList = userFeign.getUserIdByLikeNameList(dto.getOperatorName()).getData();
            if (CollUtil.isEmpty(operatorIdList)) {
                return new Page<>(dto.getPageNum(), dto.getPageSize(), 0);
            }else {
                queryWrapper.in(LoanReservoirRulesLogsEntity::getCreateBy, operatorIdList);
            }
        }
        if (ObjUtil.isNotEmpty(dto.getFundIds())) {
            String[] fundIdArray = dto.getFundIds().split(",");
            queryWrapper.and(wrapper -> {
                for (String fundId : fundIdArray) {
                    wrapper.or().apply("{0} = ANY(STRING_TO_ARRAY(fund_ids, ','))", fundId.trim());
                }
            });
        }
        if (ObjUtil.isNotNull(dto.getAreaId()) && ObjUtil.equals(0, dto.getAreaId())){
            queryWrapper.and(wp -> wp.isNotNull(LoanReservoirRulesEntity::getRegionIds)
                    .or()
                    .isNotNull(LoanReservoirRulesEntity::getDeptIds)
            );
        }else {
            if (ObjUtil.isNotEmpty(dto.getRegionIds())) {
                String[] regionIdArray = dto.getRegionIds().split(",");
                queryWrapper.and(wrapper -> {
                    for (String regionId : regionIdArray) {
                        wrapper.or().apply("{0} = ANY(STRING_TO_ARRAY(region_ids, ','))", regionId.trim());
                    }
                });
            }

            if (ObjUtil.isNotEmpty(dto.getDeptIds())) {
                String[] deptIdArray = dto.getDeptIds().split(",");
                queryWrapper.and(wrapper -> {
                    for (String deptId : deptIdArray) {
                        wrapper.or().apply("{0} = ANY(STRING_TO_ARRAY(dept_ids, ','))", deptId.trim());
                    }
                });
            }
        }

        if (ObjUtil.isNotNull(dto.getBeginDate()) && ObjUtil.isNotNull(dto.getEndDate())) {
            queryWrapper.and(wrapper -> wrapper
                    .or(on -> on.between(LoanReservoirRulesEntity::getBeginDate, dto.getBeginDate(), dto.getEndDate()))
                    .or(on -> on.between(LoanReservoirRulesEntity::getEndDate, dto.getBeginDate(), dto.getEndDate()))
                    .or(on -> on.ge(LoanReservoirRulesEntity::getBeginDate, dto.getBeginDate())
                            .le(LoanReservoirRulesEntity::getEndDate, dto.getEndDate()))
                    .or(on -> on.le(LoanReservoirRulesEntity::getBeginDate, dto.getBeginDate())
                            .ge(LoanReservoirRulesEntity::getEndDate, dto.getEndDate())));
        }




        Page<LoanReservoirRulesVO> pageList = loanReservoirRulesLogsMapper
                .selectJoinPage(new Page<>(dto.getPageNum(), dto.getPageSize()), LoanReservoirRulesVO.class, queryWrapper);

        List<LoanReservoirRulesVO> records = pageList.getRecords();
        records.forEach(record -> {
            handleRecordData(record);
        });
        return pageList;
    }

    /**
     * 判断订单是否满足放款蓄水池规则
     * @param orderInfoEntity
     */
    @Override
    public void judgOrderReservoirRules(OrderInfoEntity orderInfoEntity) {
        log.info("LoanReservoirRulesServiceImpl.judgOrderReservoirRules 判断订单是否满足放款蓄水池规则 orderId:{}",orderInfoEntity.getId());
        if (ObjUtil.isNotNull(orderInfoEntity)){
            Integer fundId = orderInfoEntity.getFundId();
            Integer regionId = orderInfoEntity.getRegionId();
            Integer deptId = orderInfoEntity.getDeptId();

            //查询资方设置的开启的规则
            List<LoanReservoirRulesEntity> rulesEntityList = loanReservoirRulesMapper
                    .selectList(new LambdaQueryWrapper<LoanReservoirRulesEntity>()
                            .eq(LoanReservoirRulesEntity::getEnable, 0)
                            .eq(LoanReservoirRulesEntity::getDeleteFlag, 0)
                            .apply("{0} = ANY(STRING_TO_ARRAY(fund_ids, ','))", Integer.toString(fundId))
                    );
            Set<Integer> ruleIds = new HashSet<>();
            LocalDateTime now = LocalDateTime.now();
            if (CollUtil.isNotEmpty(rulesEntityList)){
                //循环规则，判断订单是否命中规则
                rulesEntityList.forEach(rulesEntity -> {
                    Boolean isSatisfyRegionIds = isSatisfyRegionIds(rulesEntity,regionId);
                    Boolean isSatisfyDeptIds = isSatisfyDeptIds(rulesEntity,deptId,isSatisfyRegionIds);
                    Boolean isSatisfySourceType = isSatisfySourceType(rulesEntity,orderInfoEntity.getSourceType());
                    Boolean isSatisfyTime = isSatisfyTime(rulesEntity,orderInfoEntity.getSourceType());

                    if (isSatisfyRegionIds && isSatisfyDeptIds && isSatisfySourceType && isSatisfyTime){
                        ruleIds.add(rulesEntity.getId());
                    }

                    /*Boolean isEffective = false;
                    if (ObjUtil.isNotNull(rulesEntity.getBeginDate()) && ObjUtil.isNotNull(rulesEntity.getEndDate())){

                        isEffective = !now.isBefore(rulesEntity.getBeginDate())
                                && !now.isAfter(rulesEntity.getEndDate());
                    }

                    if (ObjUtil.isEmpty(rulesEntity.getRegionIds())
                            && ObjUtil.isEmpty(rulesEntity.getDeptIds())
                            && ObjUtil.isNull(rulesEntity.getSourceType())
                            && ObjUtil.isNull(rulesEntity.getBeginDate())){
                        ruleIds.add(rulesEntity.getId());
                    }
                    if (ObjUtil.isNotEmpty(rulesEntity.getRegionIds())
                            && ObjUtil.isEmpty(rulesEntity.getDeptIds())
                            && ObjUtil.isNull(rulesEntity.getSourceType())
                            && ObjUtil.isNull(rulesEntity.getBeginDate())){
                        List<Integer> regionIdList = Arrays.stream(rulesEntity.getRegionIds().split(",")).map(String::trim).filter(s -> !s.isEmpty())
                                .map(s -> {
                                    try {
                                        return Integer.valueOf(s);
                                    } catch (NumberFormatException e) {
                                        log.warn("Invalid regionId format: {}", s);
                                        return null;
                                    }
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());

                        if (regionIdList.contains(regionId)){
                            ruleIds.add(rulesEntity.getId());
                        }
                    }

                    if (ObjUtil.isNotEmpty(rulesEntity.getDeptIds())
                            && ObjUtil.isNull(rulesEntity.getSourceType())
                            && ObjUtil.isNull(rulesEntity.getBeginDate())){
                        List<Integer> deptIdList = Arrays.stream(rulesEntity.getDeptIds().split(",")).map(String::trim).filter(s -> !s.isEmpty())
                                .map(s -> {
                                    try {
                                        return Integer.valueOf(s);
                                    } catch (NumberFormatException e) {
                                        log.warn("Invalid regionId format: {}", s);
                                        return null;
                                    }
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());

                        if (deptIdList.contains(deptId)){
                            ruleIds.add(rulesEntity.getId());
                        }
                    }
                    if (ObjUtil.isNotNull(rulesEntity.getSourceType()) && ObjUtil.isNull(rulesEntity.getBeginDate())){
                        if (ObjUtil.equal(rulesEntity.getSourceType(), orderInfoEntity.getSourceType())){
                            ruleIds.add(rulesEntity.getId());
                        }
                    }
                    if(isEffective){
                        ruleIds.add(rulesEntity.getId());
                    }*/

                });
                if (CollUtil.isNotEmpty(ruleIds)){
                    log.info("LoanReservoirRulesServiceImpl.judgOrderReservoirRules 订单命中放款蓄水池规则 orderId:{},ruleIds:{}",orderInfoEntity.getId(),ruleIds);
                    //将订单之前的订单设置成逻辑删除
                    orderLoanReservoirMapper.update(new LambdaUpdateWrapper<OrderLoanReservoirEntity>()
                            .set(OrderLoanReservoirEntity::getDeleteFlag, 1)
                            .eq(OrderLoanReservoirEntity::getOrderId, orderInfoEntity.getId())
                            .eq(OrderLoanReservoirEntity::getDeleteFlag, 0)
                    );

                    OrderLoanReservoirEntity orderLoanReservoirEntity = new OrderLoanReservoirEntity();
                    orderLoanReservoirEntity.setOrderId(orderInfoEntity.getId());
                    orderLoanReservoirEntity.setOrderNumber(orderInfoEntity.getOrderNumber());
                    orderLoanReservoirEntity.setRuleIds(ruleIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
                    orderLoanReservoirEntity.setContractAuditTime(now);
                    orderLoanReservoirMapper.insert(orderLoanReservoirEntity);
                }
            }
        }


    }

    /**
     * 定时查询规则进行放款
     */
    @Override
    public void failureTimeRuleToLoan() {
        LocalDateTime now = LocalDateTime.now();
        //1、查询生效的规则
        List<LoanReservoirRulesEntity> useRulesList = loanReservoirRulesMapper.selectList(new LambdaQueryWrapper<LoanReservoirRulesEntity>()
                .eq(LoanReservoirRulesEntity::getEnable, 1)
                .eq(LoanReservoirRulesEntity::getDeleteFlag, 0)
                .isNotNull(LoanReservoirRulesEntity::getBeginDate)
                .isNotNull(LoanReservoirRulesEntity::getEndDate)
                .and(wrapper -> wrapper
                        // 在有效时间范围内的规则
                        .or(w -> w.le(LoanReservoirRulesEntity::getBeginDate, now)
                                .ge(LoanReservoirRulesEntity::getEndDate, now))
                )
        );
        //2.生效规则进行打开，保存操作日志
        if (CollUtil.isNotEmpty(useRulesList)){
            log.info("failureTimeRuleToLoan 查询生效的规则数量:{},ruleIds:{}",useRulesList.size(), JSONUtil.toJsonStr(useRulesList));
            useRulesList.forEach(rulesEntity -> {
                Integer ruleId = rulesEntity.getId();
                loanReservoirRulesMapper.update(new LambdaUpdateWrapper<LoanReservoirRulesEntity>()
                        .set(LoanReservoirRulesEntity::getEnable, 0)
                        .eq(LoanReservoirRulesEntity::getId, ruleId)
                );
                LoanReservoirRulesLogsEntity loanReservoirRulesLogsEntity = new LoanReservoirRulesLogsEntity();
                loanReservoirRulesLogsEntity.setRuleId(ruleId);
                loanReservoirRulesLogsEntity.setOperateEnable(0);
                loanReservoirRulesLogsMapper.insert(loanReservoirRulesLogsEntity);
                ruleFailureToLoan(rulesEntity.getId());
            });
        }

        //3.查询失效规则
        List<LoanReservoirRulesEntity> rulesEntityList = loanReservoirRulesMapper.selectList(new LambdaQueryWrapper<LoanReservoirRulesEntity>()
                .eq(LoanReservoirRulesEntity::getDeleteFlag, 0)
                .eq(LoanReservoirRulesEntity::getEnable, 0)
                .isNotNull(LoanReservoirRulesEntity::getBeginDate)
                .isNotNull(LoanReservoirRulesEntity::getEndDate)
                .and(wrapper -> wrapper
                        // endDate < 当前时间
                        .lt(LoanReservoirRulesEntity::getEndDate, now)
                        // beginDate > 当前时间
                        .or()
                        .gt(LoanReservoirRulesEntity::getBeginDate, now)
                )
        );
        //4.失效规则进行关闭，进行放款，保存操作日志
        if (CollUtil.isNotEmpty(rulesEntityList)){
            log.info("failureTimeRuleToLoan 查询失效的规则数量:{},ruleIds:{}",rulesEntityList.size(), JSONUtil.toJsonStr(rulesEntityList));
            rulesEntityList.forEach(rulesEntity -> {
                Integer ruleId = rulesEntity.getId();
                loanReservoirRulesMapper.update(new LambdaUpdateWrapper<LoanReservoirRulesEntity>()
                        .set(LoanReservoirRulesEntity::getEnable, 1)
                        .eq(LoanReservoirRulesEntity::getId, ruleId)
                );
                LoanReservoirRulesLogsEntity loanReservoirRulesLogsEntity = new LoanReservoirRulesLogsEntity();
                loanReservoirRulesLogsEntity.setRuleId(ruleId);
                loanReservoirRulesLogsEntity.setOperateEnable(1);
                loanReservoirRulesLogsMapper.insert(loanReservoirRulesLogsEntity);

                ruleFailureToLoan(rulesEntity.getId());
            });
        }
    }

    @Override
    public Map<String, Object> testLoan() {
        Map<String, Object> result = new HashMap<>();
        List<OrderInfoEntity> orderInfoEntityList1 = orderInfoMapper.selectList(new MPJLambdaWrapper<OrderInfoEntity>()
                .innerJoin(FinalFundInfoEntity.class, on -> on
                        .eq(OrderInfoEntity::getId, FinalFundInfoEntity::getOrderId)
                        .eq(OrderInfoEntity::getFundId, FinalFundInfoEntity::getFundId)
                        .eq(OrderInfoEntity::getDeleteFlag, 0))
                .and(qw -> {
                    qw.eq(OrderInfoEntity::getPaymentState,0)
                            .or().isNull(OrderInfoEntity::getPaymentState);
                })
                .in(OrderInfoEntity::getState, Arrays.asList(4500, 4550))
                //.eq(FinalFundInfoEntity::getFundResult, PreFundResultEnum.PASS)
                .lt(FinalFundInfoEntity::getRetry, 20)
                .and(qw -> {
                    qw.eq(FinalFundInfoEntity::getPaymentStatus, 0)
                            .or().isNull(FinalFundInfoEntity::getPaymentStatus);
                })
                .in(FinalFundInfoEntity::getId, List.of(171,522,391))
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime).last("limit 10")
        );
        List<String> orderNumbers1 = orderInfoEntityList1.stream().map(OrderInfoEntity::getOrderNumber).collect(Collectors.toList());
        log.info("testLoan 放款数据 size:{} orderNumber",orderNumbers1.size(),JSONUtil.toJsonStr(orderNumbers1));
        result.put("orderNumbers_loan",orderNumbers1);
        result.put("orderNumbers_loan_size",orderNumbers1.size());
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(new MPJLambdaWrapper<OrderInfoEntity>()
                .innerJoin(FinalFundInfoEntity.class, on -> on
                        .eq(OrderInfoEntity::getId, FinalFundInfoEntity::getOrderId)
                        .eq(OrderInfoEntity::getFundId, FinalFundInfoEntity::getFundId)
                        .eq(OrderInfoEntity::getDeleteFlag, 0))
                .and(qw -> {
                    qw.eq(OrderInfoEntity::getPaymentState, 0)
                            .or().isNull(OrderInfoEntity::getPaymentState);
                })
                .leftJoin(OrderLoanReservoirEntity.class, on -> on
                        .eq(OrderInfoEntity::getId, OrderLoanReservoirEntity::getOrderId)
                        .eq(OrderLoanReservoirEntity::getDeleteFlag, 0))
                .in(OrderInfoEntity::getState, Arrays.asList(4500, 4550))
                //.eq(FinalFundInfoEntity::getFundResult, PreFundResultEnum.PASS)
                .lt(FinalFundInfoEntity::getRetry, 20)
                .and(qw -> {
                    qw.eq(FinalFundInfoEntity::getPaymentStatus, 0)
                            .or().isNull(FinalFundInfoEntity::getPaymentStatus);
                })
                .and(qw -> {
                    qw.in(OrderLoanReservoirEntity::getReservoirStatus, List.of(2,3))
                            .or().isNull(OrderLoanReservoirEntity::getId);
                })
                .in(FinalFundInfoEntity::getId, List.of(171,522,391))
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .orderByDesc(FinalFundInfoEntity::getCreateTime).last("limit 10"));

        List<String> orderNumbers = orderInfoEntityList.stream().map(OrderInfoEntity::getOrderNumber).collect(Collectors.toList());
        result.put("orderNumbers_Reservoir",orderNumbers);
        result.put("orderNumbers_Reservoir_size",orderNumbers.size());
        log.info("testLoan 蓄水池拦截放款数据 size:{} orderNumber",orderNumbers.size(),JSONUtil.toJsonStr(orderNumbers));
        return result;

    }

    private static String getCurrentTimestamp() {
        SimpleDateFormat sdf = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN);
        return sdf.format(new Date());
    }

    /**
     * 失效规则进行放款
     * @param ruleId
     */
    private void ruleFailureToLoan(Integer ruleId) {
        // 1.只有一个规则的进行放水
        orderLoanReservoirMapper.update(new LambdaUpdateWrapper<OrderLoanReservoirEntity>()
                .set(OrderLoanReservoirEntity::getReservoirStatus, 2)
                .eq(OrderLoanReservoirEntity::getDeleteFlag, 0)
                .eq(OrderLoanReservoirEntity::getReservoirStatus, 1)
                .eq(OrderLoanReservoirEntity::getRuleIds,  Integer.toString(ruleId))
        );
        //2.关闭规则后，查询不止命中这一个规则的订单
        List<OrderLoanReservoirEntity> orderLoanList = orderLoanReservoirMapper
                .selectList(new LambdaQueryWrapper<OrderLoanReservoirEntity>()
                        .eq(OrderLoanReservoirEntity::getDeleteFlag, 0)
                        .eq(OrderLoanReservoirEntity::getReservoirStatus, 1)
                        .apply("{0} = ANY(STRING_TO_ARRAY(rule_ids, ','))", Integer.toString(ruleId))
                        .ne(OrderLoanReservoirEntity::getRuleIds,  Integer.toString(ruleId))
                );
        //更新订单，删除该规则
        orderLoanList.forEach(orderLoan -> {
            List<String> ruleIds = Arrays.stream(orderLoan.getRuleIds().split(","))
                    .filter(id -> !id.equals(Integer.toString(ruleId)))
                    .collect(Collectors.toList());
            String updateRuleIds = String.join(",", ruleIds);
            orderLoanReservoirMapper.update(new LambdaUpdateWrapper<OrderLoanReservoirEntity>()
                    .set(OrderLoanReservoirEntity::getRuleIds, updateRuleIds)
                    .eq(OrderLoanReservoirEntity::getId, orderLoan.getId())
            );
        });
    }

    /**
     * 处理数据
     * @param record
     */
    private void handleRecordData(LoanReservoirRulesVO record) {
        Result<UserInfoVO> userInfoVOResult = userFeign.searchUserName(record.getCreateBy());
        if (Result.isSuccess(userInfoVOResult) && ObjUtil.isNotEmpty(userInfoVOResult.getData())) {
            record.setCreateName(userInfoVOResult.getData().getName());
        }

        if (ObjUtil.isNotEmpty(record.getFundIds())){
            List<Integer> fundIdList = Arrays.stream(record.getFundIds().split(","))
                    .map(String::trim).filter(s -> !s.isEmpty()).map(Integer::valueOf).collect(Collectors.toList());
            List<FundInfoEntity> fundInfoEntityList = fundInfoMapper.selectBatchIds(fundIdList);
            record.setFundNames(fundInfoEntityList.stream() .filter(entity -> ObjUtil.equal(entity.getDeleteFlag(),0))
                    .map(FundInfoEntity::getName).collect(Collectors.joining("、")));
        }

        StringBuilder areaNamesBuilder = new StringBuilder();

        if (ObjUtil.isNotEmpty(record.getRegionIds())){
            List<Integer> regionIdList = Arrays.stream(record.getRegionIds().split(","))
                    .map(String::trim).filter(s -> !s.isEmpty()).map(Integer::valueOf).collect(Collectors.toList());
            Result<List<DeptEntityVO>> deptInfoEntityResult = userFeign.selectDeptBatchIds(regionIdList);

            if (Result.isSuccess(deptInfoEntityResult) && CollUtil.isNotEmpty(deptInfoEntityResult.getData())){
                String regionNames = deptInfoEntityResult.getData().stream()
                        .filter(entity -> ObjUtil.equal(entity.getDeleteFlag(),0))
                        .map(DeptEntityVO::getName).collect(Collectors.joining(" "));
                areaNamesBuilder.append(" ").append(regionNames);

            }
        }
        if (ObjUtil.isNotEmpty(record.getDeptIds())){
            List<Integer> deptIdList = Arrays.stream(record.getDeptIds().split(","))
                    .map(String::trim).filter(s -> !s.isEmpty()).map(Integer::valueOf).collect(Collectors.toList());
            Result<List<DeptEntityVO>> deptInfoEntityResult = userFeign.selectDeptBatchIds(deptIdList);
            if (Result.isSuccess(deptInfoEntityResult) && CollUtil.isNotEmpty(deptInfoEntityResult.getData())){
                String regionNames =   deptInfoEntityResult.getData().stream()
                        .filter(entity -> ObjUtil.equal(entity.getDeleteFlag(), 0))
                        .map(entity -> {
                            String name = entity.getName() != null ? entity.getName() : "";
                            String parentName = entity.getParentName() != null ? entity.getParentName() : "";
                            return parentName + "/" + name;
                        })
                        .collect(Collectors.joining(" "));
                areaNamesBuilder.append(" ").append(regionNames);
            }
        }
        record.setAreaNames(areaNamesBuilder.toString());
    }

    public String generateCodeRuleNumber() {
        String currentDate = LocalDate.now().format(DATE_FORMATTER);
        String redisKey = CODE_SEQUENCE_PREFIX + currentDate;
        String prefix = "XS" + currentDate;
        // 使用Redis的INCR命令原子性地增加序列号
        Long sequence = redisService.increment(redisKey);
        // 检查是否超过最大值
        if (sequence > MAX_SEQUENCE) {
            throw new BusinessException("当天序列号已用完，最大值为" + MAX_SEQUENCE);
        }
        // 如果是当天第一次生成，设置过期时间
        if (sequence == 1) {
            redisService.expire(redisKey, 2, TimeUnit.DAYS);
        }
        return prefix + String.format("%02d", sequence);
    }

    /**
     * 判断订单是否命中规则 大区
     * @param rulesEntity
     * @return
     */
    public Boolean isSatisfyRegionIds(LoanReservoirRulesEntity rulesEntity,Integer regionId){
        Boolean isSatisfyRegionIds = false;
        if (ObjUtil.isEmpty(rulesEntity.getRegionIds())){
            isSatisfyRegionIds = true;
        }
        if (ObjUtil.isNotEmpty(rulesEntity.getRegionIds())){
            List<Integer> regionIdList = Arrays.stream(rulesEntity.getRegionIds().split(",")).map(String::trim).filter(s -> !s.isEmpty())
                    .map(s -> {
                        try {
                            return Integer.valueOf(s);
                        } catch (NumberFormatException e) {
                            log.warn("Invalid regionId format: {}", s);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (regionIdList.contains(regionId)){
                isSatisfyRegionIds = true;
            }
        }
        return isSatisfyRegionIds;
    }

    /**
     * 判断订单是否命中规则 门店
     * @param rulesEntity
     * @return
     */
    public Boolean isSatisfyDeptIds(LoanReservoirRulesEntity rulesEntity,Integer deptId,Boolean isSatisfyRegionIds){
        Boolean isSatisfyDeptIds = false;
        if (ObjUtil.isEmpty(rulesEntity.getDeptIds())){
            isSatisfyDeptIds = true;
        }
        //如果满足大区的话，则门店也满足
        if (isSatisfyRegionIds){
            return true;
        }
        if (ObjUtil.isNotEmpty(rulesEntity.getDeptIds())){
            List<Integer> deptIdList = Arrays.stream(rulesEntity.getDeptIds().split(",")).map(String::trim).filter(s -> !s.isEmpty())
                    .map(s -> {
                        try {
                            return Integer.valueOf(s);
                        } catch (NumberFormatException e) {
                            log.warn("Invalid regionId format: {}", s);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (deptIdList.contains(deptId)){
                isSatisfyDeptIds = true;
            }
        }
        return isSatisfyDeptIds;
    }

    /**
     * 判断订单是否命中规则 订单来源
     * @param rulesEntity
     * @return
     */
    public Boolean isSatisfySourceType(LoanReservoirRulesEntity rulesEntity,Integer sourceType){
        Boolean isSatisfySourceType = false;
        if (ObjUtil.isNull(rulesEntity.getSourceType())){
            isSatisfySourceType = true;
        }
        if (ObjUtil.isNotNull(rulesEntity.getSourceType())){
            if (ObjUtil.equal(rulesEntity.getSourceType(), sourceType)){
                isSatisfySourceType = true;
            }
        }
        return isSatisfySourceType;
    }

    /**
     * 判断订单是否命中规则 有效期
     * @param rulesEntity
     * @return
     */
    public Boolean isSatisfyTime(LoanReservoirRulesEntity rulesEntity,Integer sourceType){
        Boolean isSatisfyTime = false;
        LocalDateTime now = LocalDateTime.now();
        if (ObjUtil.isNotNull(rulesEntity.getBeginDate()) && ObjUtil.isNotNull(rulesEntity.getEndDate())){
            isSatisfyTime = !now.isBefore(rulesEntity.getBeginDate())
                    && !now.isAfter(rulesEntity.getEndDate());
        }else {
            isSatisfyTime = true;
        }
        return isSatisfyTime;
    }

}
