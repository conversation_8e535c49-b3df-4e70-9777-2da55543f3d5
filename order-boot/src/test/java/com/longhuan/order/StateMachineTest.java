package com.longhuan.order;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.mapper.OrderFeeInfoMapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.pojo.dto.ApprovalSubmitDTO;
import com.longhuan.order.pojo.dto.OrderIdDTO;
import com.longhuan.order.pojo.dto.OrderSubmitDTO;
import com.longhuan.order.pojo.entity.OrderFeeInfoEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.vo.SubmitResultVO;
import com.longhuan.order.service.ApprovalService;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import io.micrometer.tracing.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static com.longhuan.order.statemachine.enums.States.CUSTOMER_APPOINTMENT;

@Slf4j
@SpringBootTest(classes = OrderApplication.class)
class StateMachineTest {
    @Autowired
    private Tracer tracer;
    @Autowired
    private OrderStateService orderStateService;
    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private ApprovalService approvalService;
    @Autowired
    private OrderFeeInfoMapper orderFeeInfoMapper;

    private static void getInfo(Integer orderId, SubmitResultVO approve) {
        log.info("orderId:{} ,approve:{}", orderId, approve);
    }

    private void sendFinishEvent(Integer orderId, Integer userId, Events event) {
        orderStateService.sendEvent(States.CUSTOMER_APPOINTMENT, event, orderId, userId);
    }

    @Test
     void testPaymentFail() throws InterruptedException {
        orderStateService.sendEvent(States.FUNDS_PAYMENT_APPROVAL, Events.FAIL, 723,
                1, new ApprovalSubmitDTO().setRemarkExternal("测试放款失败重进请款"));

        orderStateService.sendEvent(States.FUNDS_PAYMENT_FAIL, Events.RE_PAYMENT_PAY, 723,
                1, new ApprovalSubmitDTO().setRemarkExternal("付款失败后重新请款"));

        Thread.sleep(10000);
    }

    @Test
    void testCancel(){
        OrderSubmitDTO submitDTO = new OrderSubmitDTO();
        submitDTO.setOrderId(833);
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(1);
        approvalService.orderCancel(submitDTO,loginUser);
    }

    @Test
    void testManaual() {

        List<OrderInfoEntity> orderInfoList = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getState, CUSTOMER_APPOINTMENT.getNode()).eq(OrderInfoEntity::getId,305)
        );
        for (OrderInfoEntity orderInfoEntity : orderInfoList) {
            try {
                Integer state = orderInfoEntity.getState();
                Integer orderId = orderInfoEntity.getId();
                if (Objects.equals(CUSTOMER_APPOINTMENT.getNode(), state)) {
                    //                    orderInfoEntity.setContractState(2);
                    //                    orderInfoMapper.updateById(orderInfoEntity);
                    //                    sendFinishEvent(orderId, 1, Events.CONTRACT_SIGNING_FINISH);
                    //                    orderInfoEntity.setMortgageState(2);
                    //                    orderInfoMapper.updateById(orderInfoEntity);
                    //                    sendFinishEvent(orderId, 1, Events.MORTGAGE_LOAN_FINISH);
                    orderInfoEntity.setGpsState(2);
                    orderInfoMapper.updateById(orderInfoEntity);

                    sendFinishEvent(orderId, 1, Events.GPS_INSTALL_FINISH);
                    //                    orderInfoEntity.setReviewState(2);
                    //                    orderInfoMapper.updateById(orderInfoEntity);
                    //                    sendFinishEvent(orderId, 1, Events.REVIEW_APPOINTMENT_FINISH);
                }
                List<OrderFeeInfoEntity> orderFeeInfoEntities = orderFeeInfoMapper.selectList(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                        .eq(OrderFeeInfoEntity::getOrderId, orderId)
                        .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                );
                if (!orderFeeInfoEntities.isEmpty()) {
                    OrderFeeInfoEntity orderFeeInfoEntity = orderFeeInfoEntities.get(0);
                    Assert.notNull(orderFeeInfoEntity, "订单" + orderId + "GPS费用信息不存在");
                    orderFeeInfoEntity.setGpsFeeStatus(2);
                    orderFeeInfoMapper.updateById(orderFeeInfoEntity);
                    sendFinishEvent(orderId, 1, Events.GPS_FEE_PAY_FINISH);
                }
            } catch (Exception e) {
                log.error("订单手动回调失败 e:", e);
            }
        }
    }
@Test
void testSettle(){
    OrderIdDTO orderIdDTO = new OrderIdDTO();orderIdDTO.setOrderId(625);
    approvalService.orderSettled(orderIdDTO);
}
    @Test
    void testEvent() {
//        Events eventsAndValidate = approvalService
//                .getEventsAndValidate(3, States.getNode(2000), States.getNode(2000)
//                , 303);
//
//        Assertions.assertEquals(Events.BACK, eventsAndValidate);
//
//
//         eventsAndValidate = approvalService
//                .getEventsAndValidate(14001000,States.getNode(1400), States.getNode(1400)
//                        , 303);
//
//        Assertions.assertEquals(Events.BACK_OVERALL_REVIEW_2_BUSINESS_ADDED_INFO, eventsAndValidate);
//
//
//        eventsAndValidate = approvalService
//                .getEventsAndValidate(3,States.getNode(1400), States.getNode(1000)
//                        , 303);
//
//        Assertions.assertEquals(Events.BACK_OVERALL_REVIEW_2_BUSINESS_ADDED_INFO, eventsAndValidate);

    }

    @BeforeEach
    public void setTraceId() {
        MDC.put("traceId", UUID.randomUUID().toString().replaceAll("-", ""));
    }

    @Test
    void testRiskFinal2AddedInfo() {
        OrderInfoEntity entity = new OrderInfoEntity().setState(States.RISK_FINAL_APPROVE.getNode())
                .setCurrentNode(States.RISK_FINAL_APPROVE.getNode());
        orderInfoMapper.insert(entity);
        Integer orderId = entity.getId();
        MDC.put("traceId", UUID.randomUUID().toString().replaceAll("-", ""));
        approvalService.approvalSubmit(new ApprovalSubmitDTO().setRemark("testRiskFinal2AddedInfo")
                        .setOrderId(orderId).setNode(States.RISK_FINAL_APPROVE).setBackNode(States.BUSINESS_ADDED_INFO)
                        .setResult(3)
                , new LoginUser().setUserId(1)

        );
        showState(orderId);
    }
    @Test
    void testCustomerAppointmentAgress() throws InterruptedException {
        Integer orderId = 368;
        MDC.put("traceId", UUID.randomUUID().toString().replaceAll("-", ""));
        orderStateService.sendEvent(States.CUSTOMER_APPOINTMENT,Events.AGREES, orderId, 1);
        showState(orderId);

        Thread.sleep(10000);
    }
    @Test
    void testCustomerAppointment() {
        Integer orderId = 368;
        tracer.withSpan(tracer.nextSpan().start());
        orderStateService.sendEvent(States.CUSTOMER_APPOINTMENT,Events.REVIEW_APPOINTMENT_FINISH, orderId, 1);
        showState(orderId);
    }

    @Test
    void testPaymentApply() {
        Integer orderId = 34;
        MDC.put("traceId", UUID.randomUUID().toString().replaceAll("-", ""));
        orderStateService.approve(States.PAYMENT_APPLY_INFORMATION, orderId, 1, "付款神器千秋");
        showState(orderId);
    }
    @Test
    void testFundsFinal() {
        Integer orderId = 311;
        MDC.put("traceId", UUID.randomUUID().toString().replaceAll("-", ""));
        orderStateService.sendEvent(States.FUNDS_FINAL_APPROVE, Events.AGREES, orderId, 1);
        showState(orderId);
    }
    @Test
    void testRiskFirst2FundsFinal() throws InterruptedException {
        Integer orderId = 348;
        MDC.put("traceId", UUID.randomUUID().toString().replaceAll("-", ""));
        orderStateService.sendEvent(States.RISK_FIRST_APPROVE, Events.AGREES_RISK_FIRST_SINGLE_AGREES, orderId, 1);
        showState(orderId);

        Thread.sleep(100000);
    }

    @Test
    void testCustomerSignFinish() {
        Integer orderId = 118;
        MDC.put("traceId", UUID.randomUUID().toString().replaceAll("-", ""));
        //        orderStateService.approve(States.CUSTOMER_APPOINTMENT, orderId, 1, "testPass");
        orderStateService.sendEvent(States.CUSTOMER_APPOINTMENT, Events.GPS_INSTALL_FINISH, orderId, 1);
        showState(orderId);
    }

    @Test
    void testCustomerConformPass() {
        Integer orderId = 40;
        MDC.put("traceId", UUID.randomUUID().toString().replaceAll("-", ""));
        orderStateService.approve(States.CUSTOMER_CONFIRM, orderId, 1, "testPass");
        showState(orderId);
    }

    @Test
    void testRiskFinalPass() {
        Integer orderId = 159;
        MDC.put("traceId", UUID.randomUUID().toString().replaceAll("-", ""));
        orderStateService.approve(States.RISK_FINAL_APPROVE, orderId, 1, "testRiskFinalPass");
        showState(orderId);
    }

//    @Test
//    void testPass() {
//        Integer orderId = 352;
//        MDC.put("traceId", UUID.randomUUID().toString().replaceAll("-", ""));
//
//        orderStateService.approve(States.OVERALL_REVIEW, orderId, 1, string);
//        showState(orderId);
//    }

    @Test
    void testBack() {
        Integer orderId = 352;
        ApprovalSubmitDTO approvalSubmitDTO = new ApprovalSubmitDTO();
        approvalSubmitDTO.setOrderId(orderId).setResult(1).setRemark("testBack").setRejectReason("testBackReason")
                .setRemarkExternal("testRemarkExternal");
        orderStateService.sendEvent(States.OVERALL_REVIEW, Events.AGREES, orderId, 1, approvalSubmitDTO);
        showState(orderId);
    }

    @Test
    void testInitialStateMachine() {
        OrderInfoEntity entity = new OrderInfoEntity();
        orderInfoMapper.insert(entity);
        Integer orderId = entity.getId();
        orderStateService.initialStates(orderId);
        showState(orderId);
    }

    private void showState(Integer orderId) {
        States state = orderStateService.getState(orderId);
        log.info("order {} , state : {} ,desc: {}", orderId, state.getNode(), state.getDesc());
    }

    @Test
    void testStater2Finish() {


        States currentState = States.BUSINESS_ADDED_INFO;
        OrderInfoEntity entity = new OrderInfoEntity()
                .setCurrentNode(currentState.getNode())
                .setCustomerName("审批流测试数据");
        orderInfoMapper.insert(entity);

        Integer orderId = entity.getId();
        log.info("orderId:{}", orderId);

        while (currentState != States.PAYMENT_SUCCESS) {


            log.info("orderId:{} ,currentState:{}", orderId, currentState);


            orderStateService.approve(currentState, orderId, 1, currentState.getDesc() + "通过");


            States state = orderStateService.getState(orderId);

            Assertions.assertNotEquals(currentState, state);

            currentState = state;
        }
    }

    @Test
    void testFinish2Stater() {


        States currentState = States.FUNDS_PAYMENT_APPROVAL;
        OrderInfoEntity entity = new OrderInfoEntity().setCurrentNode(currentState.getNode());
        orderInfoMapper.insert(entity);

        Integer orderId = entity.getId();
        log.info("orderId:{}", orderId);

        while (currentState != States.BUSINESS_ADDED_INFO) {
            orderStateService.sendEvent(currentState, Events.BACK, orderId, 1);

            States state = orderStateService.getState(orderId);

            Assertions.assertNotEquals(currentState, state);

            currentState = state;

        }
    }

    /**
     * 客户签约流程
     */
    @Test
    void testCustomerSign() {
        MDC.put("traceId", UUID.randomUUID().toString().replaceAll("-", ""));
        OrderInfoEntity entity = new OrderInfoEntity()
                .setState(States.FUNDS_FINAL_APPROVE.getNode());
        orderInfoMapper.insert(entity);
        SubmitResultVO approve = null;
        Integer orderId = entity.getId();
        log.info("orderId:{}", orderId);


        //        SubmitResultVO approve = orderStateService.approve(States.FUNDS_APPROVE, orderId, 1, States
        //        .FUNDS_APPROVE.getDesc() + "通过test");

        //        showState(orderId);
        States currentStates = null;
        approve = orderStateService.sendEvent(null, Events.CUSTOMER_CONFIRM, orderId, 1);

        approve = orderStateService.sendEvent(null, Events.REVIEW_APPOINTMENT_FINISH, orderId, 1);

        approve = orderStateService.sendEvent(null, Events.GPS_INSTALL_FINISH, orderId, 1);


        approve = orderStateService.approve(null, orderId, 1, "通过test");


    }
}
