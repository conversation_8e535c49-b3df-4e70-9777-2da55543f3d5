spring:
  application:
    name: longhuan-order
  profiles:
    active: dev
  main:
    allow-circular-references: true
  cloud:
    openfeign:
      httpclient:
        connection-timeout: 300000
        ok-http:
          read-timeout: 300s
      client:
        config:
          default:
            connect-timeout: 300000
            read-timeout: 300000
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ${NACOS_SERVER_ADDR:192.168.110.238:8848}
        username: ${NACOS_SERVER_USERNAME:nacos}
        password: ${NACOS_SERVER_PASSWORD:nacos}
        namespace: ${NACOS_SERVER_NAMESPACE:}
        group: ${NACOS_SERVER_GROUP:DEFAULT_GROUP}

      config:
        # 配置中心地址
        server-addr: ${NACOS_SERVER_ADDR:192.168.110.238:8848}
        username: ${NACOS_SERVER_USERNAME:nacos}
        password: ${NACOS_SERVER_PASSWORD:nacos}
        namespace: ${NACOS_SERVER_NAMESPACE:}
        # 配置文件格式
        file-extension: yml
  config:
    import:
      - optional:nacos:longhuan-order.yml?refreshEnabled=true
kingdee:
  datapull:
    codeurl: /longhuan/h5/idInfoPage?sourceType=1&clueId=%s&phone=%s
    staticCodeUrl: /longhuan/h5/idInfoPage?sourceType=1&servicePhone=%s&serviceId=%s
    msgUrl: /longhuan/h5/messagePage?msg=%s
    businessAddedUrl: /longhuan/h5/transferPage?path=supplementInfo&preId=%s&orderId=%s&exteriorToken=%s
    customerConfirmUrl: /longhuan/h5/transferPage?path=creditConfirmDetails&preId=%s&orderId=%s&exteriorToken=%s
    contractSigning: /longhuan/h5/transferPage?path=customerContract&preId=%s&orderId=%s&exteriorToken=%s
    carInfoPageUrl: /longhuan/h5/transferPage?path=carInfoPage&preId=%s&exteriorToken=%s
logging:
  level:
    com.longhuan.order.feign.DigitalizeFeign: debug
    com.longhuan.order.kingdee.feign.KingdeeFeign: debug
    com.longhuan.order.feign.GPSFeign: debug
    com.longhuan.order.feign.XueliFeign: debug
    com.longhuan.order.feign.IntelligentRiskControlFeign: debug
    com.longhuan.order.signBank.lianlian.LianLianSignFeign: debug
    com.longhuan.order.feign.TuQiangOLFeign: debug