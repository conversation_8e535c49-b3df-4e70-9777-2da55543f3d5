package com.longhuan.risk.policy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.longhuan.risk.enums.FundProductResult;
import com.longhuan.risk.enums.RiskEngineType;
import com.longhuan.risk.enums.RiskPolicyType;
import com.longhuan.risk.pojo.dto.RiskEngineDTO;
import com.longhuan.risk.pojo.vo.FundProductVO;
import com.longhuan.risk.pojo.vo.PolicyResultVO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 风控策略
 */
@Slf4j
public abstract class AbstractRiskPolicy {
    protected RiskEngineType type;
    protected String stage;
    protected String policyName;
    protected RiskPolicyType defaultResult;
    protected String suggestion;

    @Getter
    protected Integer order = -1;


    public abstract PolicyResultVO execute(RiskEngineDTO riskEngineDTO);

    public PolicyResultVO template(RiskEngineDTO riskEngineDTO) {
        // 策略类型
        if (type != null && riskEngineDTO.getType() != type && type != RiskEngineType.ALL) {
            return null;
        }

        try {
            long startTime = System.currentTimeMillis();
            log.info("{} policyName:{} start!", this.getClass().getName(), policyName);
            PolicyResultVO execute = execute(riskEngineDTO);
            log.info("{} policyName:{} finish cost {}ms!", this.getClass().getName(), policyName, System.currentTimeMillis() - startTime);
            return execute;
        } catch (Exception e) {
            log.error("{} policyName:{} execute error!", this.getClass().getName(), policyName, e);
            return pass();
        }

    }

    protected PolicyResultVO reject() {
        return new PolicyResultVO()
                .setStage(stage)
                .setPolicyName(policyName)
                .setResult(defaultResult)
                .setSuggestion(suggestion);

    }

    protected PolicyResultVO pass() {
        return new PolicyResultVO()
                .setStage(stage)
                .setPolicyName(policyName)
                .setResult(RiskPolicyType.PASS);
    }

    protected PolicyResultVO getFundResult(List<FundProductVO> list) {
        if (CollUtil.isNotEmpty(list)) {
            log.info("getFundResult:{}", list);

            boolean matchPass = list.stream().filter(Objects::nonNull)
                    .map(FundProductVO::getResult)
                    .filter(Objects::nonNull)
                    .anyMatch(FundProductResult.PASS::equals);

            boolean matchWarn = list.stream().filter(Objects::nonNull)
                    .map(FundProductVO::getResult)
                    .filter(Objects::nonNull)
                    .anyMatch(FundProductResult.WARN::equals);

            boolean matchReject = list.stream().filter(Objects::nonNull)
                    .map(FundProductVO::getResult)
                    .filter(Objects::nonNull)
                    .anyMatch(FundProductResult.REJECT::equals);

            String rejectSuggestion = list.stream().filter(Objects::nonNull)
                    .filter(vo -> FundProductResult.REJECT.equals(vo.getResult()))
                    .map(FundProductVO::getSuggestion).filter(Objects::nonNull).distinct()
                    .collect(Collectors.joining(","));


            RiskPolicyType result;
            if (matchWarn) { //存在 通过和拒绝的请求
                result = RiskPolicyType.WARN;
            } else if (matchReject && matchPass) { //存在 通过和拒绝的请求
                result = RiskPolicyType.WARN;
            } else if (!matchReject) { // 没有拒绝的情况
                result = RiskPolicyType.PASS;
            } else {
                result = RiskPolicyType.REJECT == defaultResult ? RiskPolicyType.REJECT : defaultResult;
            }

            String finalSuggestion = null;
            if (RiskPolicyType.REJECT.equals(result) || RiskPolicyType.WARN.equals(result)) {
                finalSuggestion = StrUtil.isNotEmpty(rejectSuggestion) ? rejectSuggestion : suggestion;
            }

            return new PolicyResultVO()
                    .setStage(stage)
                    .setPolicyName(policyName)
                    .setResult(result)
                    .setSuggestion(finalSuggestion)
                    .setFunds(list);
        }
        return pass();
    }
}
