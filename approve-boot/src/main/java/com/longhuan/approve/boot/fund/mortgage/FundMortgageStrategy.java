package com.longhuan.approve.boot.fund.mortgage;

import com.longhuan.approve.boot.pojo.dto.FundMortgageCommonDTO;
import com.longhuan.approve.boot.pojo.vo.FundMortgageCommonVO;

/**
 * 资方还款策略
 *
 * <AUTHOR>
 * @date 2024/10/16
 */
public interface FundMortgageStrategy {


    /**
     * 抵押申请
     */
    FundMortgageCommonVO mortgageCreate(FundMortgageCommonDTO fundMortgageCommonDTO);

    /**
     * 抵押查询
     */
    FundMortgageCommonVO mortgageQuery(FundMortgageCommonDTO fundMortgageCommonDTO);

}
