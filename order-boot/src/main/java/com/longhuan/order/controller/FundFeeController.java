package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.FundFeeExportDTO;
import com.longhuan.order.pojo.vo.FundMarginFeeVO;
import com.longhuan.order.service.FundFeeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.YearMonth;
import java.util.List;

@Api(tags = "资方费用计算接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/fund/fee")
public class FundFeeController {
    private final FundFeeService fundFeeService;

    // 保证金  我司->融担->资方
    @GetMapping("/marginFee/warn")
    @ApiOperation(value = "资方保证金预警")
    public Result<List<FundMarginFeeVO>> marginFeeWarn() {

        return Result.success(fundFeeService.marginFeeWarn());
    }

    // 服务费 资方->我司
    @PostMapping("/serviceFee/export")
    @ApiOperation(value = "导出服务费核算报表")
    public void serviceFeeExport(HttpServletResponse response, @RequestBody FundFeeExportDTO fundFeeExportDTO) {

        fundFeeService.serviceFeeExport(response, fundFeeExportDTO);

    }

    // 担保费 我司->融担
    @PostMapping("/guaranteeFee/calc")
    @ApiOperation(value = "担保公司担保费计算")
    public void guaranteeFeeCalc(@RequestParam("yearMonth") YearMonth yearMonth) {

        fundFeeService.guaranteeFeeCalc(yearMonth);

    }

}
