package com.longhuan.order.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.pojo.dto.OrderSettleAddDTO;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.vo.OrderSettleDetailVO;
import com.longhuan.order.pojo.vo.OrderSettleVO;
import com.longhuan.order.service.OrderSettleApplyInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@Api(tags = "订单结清单接口")
@RestController
@RequestMapping("/api/v1/order/settle")
@RequiredArgsConstructor
public class OrderSettleController {
    private final OrderSettleApplyInfoService orderSettleApplyInfoService;

    @ApiOperation(value = "结清单申请批量添加")
    @GetMapping("/apply/batch/add")
    Result<Boolean> applyBatchAdd() {
        return Result.success(orderSettleApplyInfoService.applyBatchAdd());
    }


    @ApiOperation(value = "结清单申请手动添加")
    @PostMapping("/apply/manual/add")
    Result<Boolean> applyManualAdd(@RequestBody @Validated OrderSettleAddDTO orderSettleDTO,
                                   @CurrentUser LoginUser loginUser) {
        return Result.success(orderSettleApplyInfoService.applyManualAdd(orderSettleDTO, loginUser));
    }

    @ApiOperation(value = "待添加结清单订单列表")
    @PostMapping("/order/list")
    Result<Page<OrderSettleVO>> orderList(@RequestBody OrderSettleDTO orderSettleDTO,
                                          @CurrentUser LoginUser loginUser) {
        return Result.success(orderSettleApplyInfoService.orderList(orderSettleDTO, loginUser));
    }

    @ApiOperation(value = "结清单申请列表")
    @PostMapping("/apply/list")
    Result<Page<OrderSettleVO>> applyList(@RequestBody OrderSettleDTO orderSettleDTO,
                                          @CurrentUser LoginUser loginUser) {
        return Result.success(orderSettleApplyInfoService.applyList(orderSettleDTO, loginUser));
    }
    @ApiOperation(value = "结清单品质审批提交")
    @PostMapping("/quality/approve/submit")
    Result<Boolean> qualityApproveSubmit(@RequestBody @Validated OrderSettleApproveDTO settleFinanceDTO,
                                         @CurrentUser LoginUser loginUser) {
        return Result.success(orderSettleApplyInfoService.qualityApproveSubmit(settleFinanceDTO, loginUser));
    }

    @ApiOperation(value = "结清单财务审批提交")
    @PostMapping("/finance/approve/submit")
    Result<Boolean> financeApproveSubmit(@RequestBody @Validated OrderSettleApproveDTO settleFinanceDTO,
                                         @CurrentUser LoginUser loginUser) {
        return Result.success(orderSettleApplyInfoService.financeApproveSubmit(settleFinanceDTO, loginUser));
    }

    @ApiOperation(value = "获取免责声明签署二维码")
    @PostMapping("/disclaimer/file/signing")
    ResponseEntity<byte[]> signingDisclaimerFile(@RequestBody @Validated SettleApplyDTO applyDTO) {
        Objects.requireNonNull(applyDTO.getSettleId(),()->{ throw  new BusinessException("参数 settleId 不能为空");});
        Objects.requireNonNull(applyDTO.getOrderId(),()->{ throw  new BusinessException("参数 orderId 不能为空");});

        return orderSettleApplyInfoService.getDisclaimerSignTask(applyDTO.getSettleId(), applyDTO.getOrderId());
    }

    @ApiOperation(value = "结清单证明生成")
    @PostMapping("/certificate/file/generator")
    Result<String> generatorCertificateFile(@RequestBody @Validated SettleApplyDTO applyDTO) {
        Objects.requireNonNull(applyDTO.getSettleId(),()->{ throw  new BusinessException("参数 settleId 不能为空");});
        Objects.requireNonNull(applyDTO.getOrderId(),()->{ throw  new BusinessException("参数 orderId 不能为空");});


        return Result.success(orderSettleApplyInfoService.generatorCertificateFile(applyDTO.getSettleId(), applyDTO.getOrderId()));
    }

    @ApiOperation(value = "结清单gps待办提交")
    @PostMapping("/gps/detail/submit")
    Result<Boolean> gpsPendingSubmit(@RequestBody @Validated OrderSettleGpsDTO settleGPSDTO,
                                     @CurrentUser LoginUser loginUser) {
        return Result.success(orderSettleApplyInfoService.gpsPendingSubmit(settleGPSDTO, loginUser));
    }

    @ApiOperation(value = "结清单资料归还提交")
    @PostMapping("/express/info/submit")
    Result<Boolean> expressInfoSubmit(@RequestBody @Validated OrderSettleExpressDTO settleExpressDTO,
                                      @CurrentUser LoginUser loginUser) {
        return Result.success(orderSettleApplyInfoService.expressInfoSubmit(settleExpressDTO, loginUser));
    }

    @ApiOperation(value = "结清单申请详情")
    @PostMapping("/apply/detail")
    Result<OrderSettleDetailVO> applyDetail(@RequestBody @Validated SettleApplyDTO settleApplyDTO,
                                            @CurrentUser LoginUser loginUser) {
        return Result.success(orderSettleApplyInfoService.applyDetail(settleApplyDTO));
    }
}
