package com.longhuan.order.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.FaceSignerLiveDTO;
import com.longhuan.order.pojo.entity.FaceSignerLiveFileEntity;
import com.longhuan.order.service.FaceSignerLiveRecordService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * FaceSignerLiveRecordController
 *
 * @date 2025/5/26 10:29
 */
@Api("阿里云面签")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/faceSignerLiveRecord")
public class FaceSignerLiveRecordController {
    private final FaceSignerLiveRecordService faceSignerLiveRecordService;

    /**
     * 获取 Token
     * @param roomId 房间ID
     * @param userId 用户ID
     * @return 包含 Token 和时间戳的响应
     */
    @GetMapping("/token")
    public Result<?> generateToken(@RequestParam String roomId, @RequestParam String userId) {
        long timestamp = System.currentTimeMillis() / 1000 + 24 * 60 * 60; // 当前时间戳（秒级）+ 24 小时有效期
        String token = faceSignerLiveRecordService.generateToken(roomId, userId, timestamp);
        Map<String, Object> response = new HashMap<>();
        response.put("token", token);
        response.put("timestamp", timestamp);
        return Result.success(response);
    }

    /**
     * 1.主播与观众连麦两种角色
     * 2.配置实时音频管理应用获取key
     * 3.生成推拉流地址（直播和观众连麦）
     * 4.用户userId唯一
     */
    /**
     *
     * @param faceSignerLiveDTO
     * @return
     */
    @PostMapping("/generateStreamUrl")
    public Result<String> generateStreamUrl(@RequestBody FaceSignerLiveDTO faceSignerLiveDTO) {
        String url = faceSignerLiveRecordService.generateStreamUrl(faceSignerLiveDTO);
        return Result.success(url);
    }

    /**
     * 创建预备房间
     * @return
     */
    @PostMapping("/createPreRoom")
    public Result<FaceSignerLiveDTO> createPreRoom(@RequestBody FaceSignerLiveDTO faceSignerLiveDTO, @CurrentUser LoginUser currentUser) {
        // 创建预备房间并返回房间ID
        FaceSignerLiveDTO result = faceSignerLiveRecordService.createPreRoom(faceSignerLiveDTO,currentUser);
        return Result.success(result);
    }

    /**
     * 创建房间,返回房间信息和需要加入房间的用户id
     * @return
     */
    @PostMapping("/createRoom")
    public Result<FaceSignerLiveDTO> createRoom(@RequestBody FaceSignerLiveDTO faceSignerLiveDTO, @CurrentUser LoginUser currentUser) {
        // 创建房间并返回房间ID
        FaceSignerLiveDTO result = faceSignerLiveRecordService.createRoom(faceSignerLiveDTO,currentUser);
        return Result.success(result);
    }

    /**
     * 加入房间，判断用户是否可以加入房间
     * @param faceSignerLiveDTO
     * @return
     */
    @PostMapping("/joinRoom")
    public Result<FaceSignerLiveDTO> joinRoom(@RequestBody FaceSignerLiveDTO faceSignerLiveDTO, @CurrentUser LoginUser currentUser) {
        if (ObjectUtil.isEmpty(faceSignerLiveDTO.getOrderId())){
            return Result.failed("订单ID为空");
        }
        if (ObjectUtil.isEmpty(faceSignerLiveDTO.getUserSource())){
            return Result.failed("用户来源为空");
        }
        FaceSignerLiveDTO result = faceSignerLiveRecordService.joinRoom(faceSignerLiveDTO,currentUser);
        return Result.success(result);
    }


    /**
     * 开始rtc云端录制任务
     * @param faceSignerLiveDTO
     * @return
     */
    @PostMapping("/startRtcRecord")
    public Result<String> startRtcRecord(@RequestBody FaceSignerLiveDTO faceSignerLiveDTO, @CurrentUser LoginUser currentUser) {
        if (ObjectUtil.isEmpty(faceSignerLiveDTO.getRoomId())){
            return Result.failed("房间号为空");
        }
        if (ObjectUtil.isNull(faceSignerLiveDTO.getUserList()) || faceSignerLiveDTO.getUserList().size() == 0){
            return Result.failed("进入频道的用户为空");
        }
        Map<String, Object> result = faceSignerLiveRecordService.startRtcRecord(faceSignerLiveDTO,currentUser);
        if (result.get("code").equals("0")){
            return Result.success(result.get("msg").toString());
        }else {
            return Result.failed(result.get("msg").toString());
        }
    }

    /**
     * 更新rtc云端录制任务
     * @param faceSignerLiveDTO
     * @return
     */
    @PostMapping("/updateRtcRecord")
    public Result<String> updateRtcRecord(@RequestBody FaceSignerLiveDTO faceSignerLiveDTO) {
        if (ObjectUtil.isEmpty(faceSignerLiveDTO.getRoomId())){
            return Result.failed("房间号为空");
        }
        if (ObjectUtil.isNull(faceSignerLiveDTO.getUserList()) || faceSignerLiveDTO.getUserList().size() == 0){
            return Result.failed("进入频道的用户为空");
        }
        Map<String, Object> result = faceSignerLiveRecordService.updateRtcRecord(faceSignerLiveDTO);
        if (result.get("code").equals("0")){
            return Result.success(result.get("msg").toString());
        }else {
            return Result.failed(result.get("msg").toString());
        }
    }

    /**
     * 停止rtc云端录制任务
     * @param faceSignerLiveDTO
     * @return
     */
    @PostMapping("/stopRtcRecord")
    public Result<String> stopRtcRecord(@RequestBody FaceSignerLiveDTO faceSignerLiveDTO) {
        if (ObjectUtil.isEmpty(faceSignerLiveDTO.getRoomId())){
            return Result.failed("房间号为空");
        }
      /*  if(ObjectUtil.isEmpty(faceSignerLiveDTO.getLiveRecordTaskId())){
            return Result.failed("录制任务ID为空");
        }*/
        Map<String, Object> result = faceSignerLiveRecordService.stopRtcRecord(faceSignerLiveDTO);
        if (result.get("code").equals("0")){
            return Result.success(result.get("msg").toString());
        }else {
            return Result.failed(result.get("msg").toString());
        }
    }

    /**
     * 查询rtc云端录制文件与任务信息,下载录制完成的视频到服务器
     * @return
     */
    @GetMapping("/queryRtcRecord")
    public Result<String> queryRtcRecordToHandle(@RequestParam(name = "orderId") Integer orderId) {
         String result = faceSignerLiveRecordService.queryRtcRecordToHandle(orderId);
         return Result.success(result);

    }

    /**
     * 查询rtc云端录制文件与任务信息,下载录制完成的视频到服务器
     * @return
     */
    @GetMapping("/queryRtcRecordTemp")
    public Result<String> queryRtcRecordTemp(@RequestParam(name = "roomId") String roomId) {
        Map<String, Object> result = faceSignerLiveRecordService.queryRtcRecordTemp(roomId);
        if (result.get("code").equals("0")){
            return Result.success(result.get("msg").toString());
        }else {
            return Result.failed(result.get("msg").toString());
        }

    }

    /**
     * 查询频道内在线用户列表
     * @return
     */
    @PostMapping("/queryChannelUser")
    public Result<?> queryChannelUser(@RequestBody FaceSignerLiveDTO faceSignerLiveDTO) {
        Map<String, Object> result = faceSignerLiveRecordService.queryChannelUser(faceSignerLiveDTO);
        if (result.get("code").equals("0")){
            return Result.success(result.get("msg"));
        }else {
            return Result.failed(result.get("msg").toString());
        }
    }

    /**
     * 删除频道
     * @return
     */
    @PostMapping("/deleteChannel")
    public Result<?> deleteChannel(@RequestBody FaceSignerLiveDTO faceSignerLiveDTO) {
        Map<String, Object> result = faceSignerLiveRecordService.deleteChannel(faceSignerLiveDTO);
        if (result.get("code").equals("0")){
            return Result.success(result.get("msg"));
        }else {
            return Result.failed(result.get("msg").toString());
        }
    }

    /**
     * 阿里云直播回调地址
     * @param callbackData
     * @return
     */
    @PostMapping(value = "/handleAliyunLiveRecordCallback")
    public Map<String,Object> handleAliyunLiveRecordCallback(@RequestBody String callbackData) {
        Boolean flag = faceSignerLiveRecordService.handleAliyunLiveRecordCallback(callbackData);
        Map<String,Object> result = new HashMap<>();
        if (flag){
            result.put("Code",0);
            result.put("Msg","Success");
            return result;
        }else {
            result.put("Code",1);
            result.put("Msg","fail");
            return result;
        }
    }


    /**
     * 根据房间号查询任务Id
     * @return
     */
    @GetMapping("/queryTaskIdByRoomId")
    public Result<String> queryTaskIdByRoomId(@RequestParam(name = "roomId") String roomId) {
        Map<String, Object> result = faceSignerLiveRecordService.queryTaskIdByRoomId(roomId);
        if (result.get("code").equals("0")){
            return Result.success(result.get("msg").toString());
        }else {
            return Result.failed(result.get("msg").toString());
        }
    }


    /**
     * 发送微信公众号和钉钉消息
     * @return
     */
    @PostMapping("/sendFaceSignerMessage")
    public Result<?> sendFaceSignerMessage(@RequestBody FaceSignerLiveDTO faceSignerLiveDTO, @CurrentUser LoginUser currentUser) {
        if (ObjectUtil.isNull(faceSignerLiveDTO.getOrderId())){
            return Result.failed("订单号为空");
        }
        if (ObjectUtil.isNull(faceSignerLiveDTO.getSendType())){
            return Result.failed("发送类型为空");
        }
        if (!List.of(0,1,2).contains(faceSignerLiveDTO.getSendType())){
            return Result.failed("发送类型错误");
        }
        Map<String, Object> result = faceSignerLiveRecordService.sendFaceSignerMessage(faceSignerLiveDTO, currentUser);
        if (result.get("code").equals("0")){
            return Result.success(result.get("msg"));
        }else {
            return Result.failed(result.get("msg").toString());
        }
    }

    /**
     * 处理频道内没有用户
     * @return
     */
    @GetMapping("/handelChannelNoUserListTask")
    public Result<Boolean> handelChannelNoUserListTask(@RequestParam(name = "roomId", required = false) String roomId) {
        faceSignerLiveRecordService.handelChannelNoUserListTask(roomId);
        return Result.success(true);
    }

  /*  *//**
     * 保存用户进入的设备信息
     * @return
     *//*
    @PostMapping("/insertUserDevice")
    public Result<?> insertUserDevice(@RequestBody FaceSignerLiveDTO faceSignerLiveDTO, @CurrentUser LoginUser currentUser) {
        Map<String, Object> result = faceSignerLiveRecordService.insertUserDevice(faceSignerLiveDTO, currentUser);
        if (result.get("code").equals("0")){
            return Result.success(result.get("msg"));
        }else {
            return Result.failed(result.get("msg").toString());
        }
    }

    @GetMapping("/getFaceSignerLiveFileList")
    public Result<List<FaceSignerLiveFileEntity>> getFaceSignerLiveFileList(@RequestParam(name = "orderId") Integer orderId) {
        List<FaceSignerLiveFileEntity> list = faceSignerLiveRecordService.getFaceSignerLiveFileList(orderId);
        return Result.success(list);

    }

    @PostMapping("/getReviewVideoUrl")
    public Result<?> getReviewVideoUrl(@RequestBody FaceSignerLiveDTO faceSignerLiveDTO) {
        if (ObjectUtil.isNull(faceSignerLiveDTO.getAliyunVideoPath())){
            return Result.failed("视频地址为空");
        }
        Map<String, Object> result = faceSignerLiveRecordService.getReviewVideoUrl(faceSignerLiveDTO);
        if (result.get("code").equals("0")){
            return Result.success(result.get("msg"));
        }else {
            return Result.failed(result.get("msg").toString());
        }
    }*/
    /**
     *查询对方所在页面
     */
    @GetMapping("/checkTheOtherPersonSPage/{orderId}")
    public Result<String> checkTheOtherPersonSPage(@PathVariable Integer orderId,
                                                   @RequestParam(name = "userType") Integer userType,
                                                   @RequestParam(name="nowPage") String nowPage) {
        String result = faceSignerLiveRecordService.checkTheOtherPersonSPage(orderId, userType, nowPage);
        return Result.success(result);
    }
}
