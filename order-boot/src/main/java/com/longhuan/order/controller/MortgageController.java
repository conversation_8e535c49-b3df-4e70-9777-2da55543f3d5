package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.vo.CancelOrderResVO;
import com.longhuan.order.pojo.vo.MortgageDetailVO;
import com.longhuan.order.service.MortgageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @program: new-longhuan-order
 * @description: 抵押待办
 * @author: zangxx
 * @create: 2024-08-16 13:54
 **/
@Api(tags = "抵押待办")
@RestController
@RequestMapping("/api/v1/mortgage")
@RequiredArgsConstructor
public class MortgageController {

    private final MortgageService mortgageService;

    @ApiOperation(value = "中瑞抵押下单")
    @PostMapping("/submit")
    Result<Boolean> submitMortgage(@RequestBody @Validated ZhongRuiCarDeptMortgageDTO zhongRuiCarDeptMortgageDTO) {
        return Result.success(mortgageService.submitMortgage(zhongRuiCarDeptMortgageDTO));
    }

    @ApiOperation(value = "办抵流程信息")
    @PostMapping("/detail")
    Result<MortgageDetailVO> getMortgageDetail(@RequestBody @Validated SubmitMortgageDTO submitMortgageDTO, @CurrentUser LoginUser currentUser) {
        return Result.success(mortgageService.getMortgageDetail(submitMortgageDTO, currentUser));
    }

    @ApiOperation(value = "根据订单id获取办抵详情")
    @PostMapping("/detailByOrderId/{orderId}")
    Result<MortgageDetailVO> getMortgageDetailByOrderId(@PathVariable(value = "orderId") Integer orderId) {
        return Result.success(mortgageService.getMortgageDetailByOrderId(orderId));
    }


    @ApiOperation(value = "根据工号查询员工姓名")
    @GetMapping("/userName")
    Result<String> getUserNameByJobNumber(@RequestParam("jobNumber") String jobNumber) {
        return Result.success(mortgageService.getUserNameByJobNumber(jobNumber));
    }

    @ApiOperation(value = "查询中瑞办抵订单详情")
    @GetMapping("/getDetailByNo")
    Result<Boolean> getDetailByNo() {
        return Result.success(mortgageService.getDetailByNo());
    }

    @ApiOperation(value = "保存抵押待办信息")
    @PostMapping("/deal")
    Result<Boolean> saveMortgage(@RequestBody @Validated MortgageDealSubmitDTO mortgageDealSubmitDTO, @CurrentUser LoginUser currentUser) {
        return Result.success(mortgageService.mortgageDetialSubmit(mortgageDealSubmitDTO, currentUser));
    }

    @ApiOperation(value = "更新抵押状态")
    @PostMapping("/updateMortgageState")
    Result<Boolean> updateMortgageState(@RequestBody MortgageStateUpdateDTO mortgageStateUpdateDTO) {
        return Result.success(mortgageService.updateMortgageState(mortgageStateUpdateDTO));
    }

    @ApiOperation(value = "查询中瑞办抵详情根据订单编号")
    @PostMapping("/getDetailByOrderId")
    Result<String> getDetailByOrderId(@RequestBody @Validated ZhongRuiCarDeptMortgageDTO zhongRuiCarDeptMortgageDTO) {
        return Result.success(mortgageService.getDetailByOrderId(zhongRuiCarDeptMortgageDTO));
    }

    /**
     * 抵押、解抵 撤回
     */
    @ApiOperation(value = "抵押、解抵 撤回")
    @PostMapping("/cancelMortgage")
    public Result<Boolean> cancelMortgage(@RequestBody @Validated  ZhongRuiCarDeptMortgageDTO zhongRuiCarDeptMortgageDTO) {
        return Result.success(mortgageService.cancelMortgage(zhongRuiCarDeptMortgageDTO));
    }

    @ApiOperation(value = "查询订单列表接口")
    @PostMapping("/getMortgageList")
    public Result<List<ZhongRuiOrderInfoDTO>> getMortgageList(@RequestBody ZhongRuiCarInfoDTO zhongRuiCarInfoDTO) {
        return Result.success(mortgageService.getMortgageList(zhongRuiCarInfoDTO));
    }

    @ApiOperation(value = "客户审核结果接口")
    @PostMapping("/customerAudits")
    public Result<CancelOrderResVO> customerAudits(@RequestBody @Validated  ZhongRuiCustomerAuditsDTO zhongRuiCustomerAuditsDTO) {
        return Result.success(mortgageService.customerAudits(zhongRuiCustomerAuditsDTO));
    }

    @ApiOperation(value = "更新寄件信息接口")
    @PostMapping("/updateFilePostInfo")
    public Result<CancelOrderResVO> updateFilePostInfo(@RequestBody @Validated  ZhongRuiFilePostInfoDTO zhongRuiFilePostInfoDTO) {
        return Result.success(mortgageService.updateFilePostInfo(zhongRuiFilePostInfoDTO));
    }
    /**
     * 自动提交线上抵押待办
     */
    @ApiOperation(value = "自动提交线上抵押待办")
    @PostMapping("/autoSubmitMortgage")
    public Result<Boolean> autoSubmitMortgage() {
        return Result.success(mortgageService.autoSubmitMortgage());
    }

}
