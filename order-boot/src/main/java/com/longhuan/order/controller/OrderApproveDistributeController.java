package com.longhuan.order.controller;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.mapper.DistributeAreaMapper;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.dto.SearchDistributeAreasDTO;
import com.longhuan.order.pojo.dto.UpdateDistributeAreaDTO;
import com.longhuan.order.pojo.dto.UpdateOrderApproveDistributeDTO;
import com.longhuan.order.pojo.entity.DistributeAreaEntity;
import com.longhuan.order.pojo.vo.DistributeAreaVO;
import com.longhuan.order.pojo.vo.OrderApproveDistributeVO;
import com.longhuan.order.pojo.vo.TaskStatisticsVO;
import com.longhuan.order.service.OrderApproveDistributeService;
import com.longhuan.order.service.UserOnlineDistributionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;



@RestController
@RequiredArgsConstructor
@Api("客服派单接口")
@RequestMapping("/api/v1/orderApproveDistribute")
public class OrderApproveDistributeController {


    private final OrderApproveDistributeService orderApproveDistributeService;
    private final DistributeAreaMapper distributeAreaMapper;
    private final UserOnlineDistributionService userOnlineDistributionService;

    /**
     * 客服派单
     *
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "客服派单")
    @PostMapping("/distributeOrder")
    public Result<Boolean> distributeOrder() {
        return Result.success(orderApproveDistributeService.distributeOrder());
    }

    /**
     * 搜索分布区域
     *
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "搜索分布区域")
    @PostMapping("/searchDistributeAreas")
    public Result<IPage<DistributeAreaVO>> searchDistributeAreas(@Validated @RequestBody SearchDistributeAreasDTO searchDistributeAreasDTO) {
        return Result.success(orderApproveDistributeService.searchDistributeAreas(searchDistributeAreasDTO));
    }

    /**
     * 更新分发区域
     *
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "更新分发区域")
    @PostMapping("/updateDistributeArea")
    public Result<Boolean> updateDistributeArea(@Validated @RequestBody UpdateDistributeAreaDTO updateDistributeAreaDTO,@CurrentUser LoginUser loginUser) {
        return Result.success(orderApproveDistributeService.updateDistributeArea(updateDistributeAreaDTO,loginUser));
    }
    /**
     * 批量更新分发区域
     *
     *  @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "批量更新分发区域")
    @PostMapping("/updateDistributeAreaBatch")
    public Result<Boolean> updateDistributeAreaBatch(@Validated @RequestBody BatchUpdateDistributeAreaDTO updateDistributeAreaDTOList, @CurrentUser LoginUser loginUser) {
        if (CollUtil.isEmpty(updateDistributeAreaDTOList.getUpdateDistributeAreaDTOList())){
            updateDistributeAreaDTOList.setUpdateDistributeAreaDTOList(distributeAreaMapper.selectList(new LambdaQueryWrapper<DistributeAreaEntity>()
                    .eq(DistributeAreaEntity::getDeleteFlag, 0)
                    .ne(DistributeAreaEntity::getServiceDispatch, updateDistributeAreaDTOList.getServiceDispatch())).stream().map(e->{
                UpdateDistributeAreaDTO updateDistributeAreaDTO=new UpdateDistributeAreaDTO();
                updateDistributeAreaDTO
                        .setAreaType(e.getAreaType());
                updateDistributeAreaDTO.setId(e.getId());
                updateDistributeAreaDTO.setServiceDispatch(updateDistributeAreaDTOList.getServiceDispatch());
                return updateDistributeAreaDTO;
            }).toList());
        }
        for (UpdateDistributeAreaDTO updateDistributeAreaDTO : updateDistributeAreaDTOList.getUpdateDistributeAreaDTOList()){
            orderApproveDistributeService.updateDistributeArea(updateDistributeAreaDTO,loginUser);
        }
        return Result.success(true);
    }
    /**
     * 更新订单审批时间
     *
     * @param updateOrderApproveDistributeDTO 更新分发区域 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "更新订单审批时间")
    @PostMapping("/updateOrderApproveDistribute")
    public Result<Boolean> updateOrderApproveDistribute(@Validated @RequestBody UpdateOrderApproveDistributeDTO updateOrderApproveDistributeDTO) {
        return Result.success(orderApproveDistributeService.updateOrderApproveDistribute(updateOrderApproveDistributeDTO));
    }

    /**
     * 退回重新分配
     *
     * @param updateOrderApproveDistributeDTO 退回重新分配 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "退回重新分配")
    @PostMapping("/returnDistributeOrder")
    public Result<Boolean> returnDistributeOrder(@Validated @RequestBody UpdateOrderApproveDistributeDTO updateOrderApproveDistributeDTO) {
        return Result.success(orderApproveDistributeService.returnDistributeOrder(updateOrderApproveDistributeDTO));
    }
    /**
     * 查询区域
     */
    @ApiOperation(value = "查询区域")
    @PostMapping("/queryDistributeAreaToDigitalize")
    public Result<List<DistributeAreaVO>> queryDistributeAreaToDigitalize(@RequestParam(required = false) Integer serviceDispatch) {
        return Result.success(orderApproveDistributeService.queryDistributeAreaToDigitalize(serviceDispatch));
    }
    /**
     * 查询当前登录用户门店是否开启客服派单
     */
    @ApiOperation(value = "查询当前登录用户门店是否开启客服派单")
    @PostMapping("/checkAreaStatus")
    public Result<Boolean> checkAreaStatus(@CurrentUser LoginUser loginUser) {
        return Result.success(userOnlineDistributionService.checkAreaStatus(loginUser));
    }
    /**
     * 申请客服派单
     *
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "申请客服派单")
    @PostMapping("/initiateDispatch")
    public Result<Boolean> initiateDispatch(@Validated @RequestBody OrderApproveDistributeInsertDTO dto) {
        return Result.success(orderApproveDistributeService.initiateDispatch(dto));
    }
    @ApiOperation(value = "查询未审批列表")
    @PostMapping("/queryOrderApproveDistributeList")
    public Result<Page<OrderApproveDistributeVO>> queryOrderApproveDistributeList(@Validated @RequestBody QueryOrderApproveDistributeListDTO queryOrderApproveDistributeListDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderApproveDistributeService.queryOrderApproveDistributeList(queryOrderApproveDistributeListDTO,loginUser));
    }
    @ApiOperation(value = "派单明细列表导出")
    @PostMapping("/export/queryOrderApproveDistributeList")
    public void exportOrderApproveDistributeList(@RequestBody QueryOrderApproveDistributeListDTO queryOrderApproveDistributeListDTO, @ApiParam(hidden = true) @CurrentUser LoginUser loginUser, HttpServletResponse response) {
        orderApproveDistributeService.exportOrderApproveDistributeList(queryOrderApproveDistributeListDTO, loginUser, response);
    }
    @ApiOperation(value = "客服定时下线")
    @PostMapping("/theCustomerServiceIsAutomaticallyOffline")
    public Result<Boolean> theCustomerServiceIsAutomaticallyOffline() {
        userOnlineDistributionService.theCustomerServiceIsAutomaticallyOffline();
        return Result.success(true);
    }
    @ApiOperation(value = "是否展示更多")
    @PostMapping("/whetherToShowMoreButtons")
    public Result<Boolean> whetherToShowMoreButtons(@CurrentUser LoginUser loginUser) {
        return Result.success(orderApproveDistributeService.whetherToShowMoreButtons(loginUser));
    }
    @PostMapping("/clearStaleData")
    public Result<Boolean> clearStaleData(){
        return Result.success(orderApproveDistributeService.clearStaleData());
    }

    @ApiOperation(value = "当前任务统计")
    @PostMapping("/taskStatistics")
    public Result<TaskStatisticsVO> taskStatistics(@CurrentUser LoginUser loginUser) {
        return Result.success(orderApproveDistributeService.taskStatistics(loginUser));
    }
    @ApiOperation(value = "临时更新区域表的区域为大区")
    @PostMapping("/updateAreaTypeToRegionId")
    public Result<String> updateAreaTypeToRegionId(@RequestBody QueryOrderApproveDistributeListDTO dto) {
        return Result.success(orderApproveDistributeService.updateAreaTypeToRegionId(dto));
    }

}