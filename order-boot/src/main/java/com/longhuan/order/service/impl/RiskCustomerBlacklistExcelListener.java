package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.feign.DigitalizeFeign;
import com.longhuan.order.mapper.RiskCustomerBlacklistLogMapper;
import com.longhuan.order.mapper.RiskCustomerBlacklistMapper;
import com.longhuan.order.pojo.dto.RiskCustomerBlacklistImportDTO;
import com.longhuan.order.pojo.dto.digitalize.AddBlacklistDTO;
import com.longhuan.order.pojo.entity.RiskCustomerBlacklistEntity;
import com.longhuan.order.pojo.entity.RiskCustomerBlacklistLogEntity;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
public class RiskCustomerBlacklistExcelListener extends AnalysisEventListener<RiskCustomerBlacklistImportDTO> {

    private static final int BATCH_COUNT = 100;
    private List<RiskCustomerBlacklistImportDTO> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
    private RiskCustomerBlacklistMapper riskCustomerBlacklistMapper;
    private RiskCustomerBlacklistLogMapper riskCustomerBlacklistLogMapper;
    private DigitalizeFeign digitalizeFeign;
    //重复数据
    @Getter
    private List<RiskCustomerBlacklistEntity> repeat = new ArrayList<>();
    //不重复数据
    @Getter
    private List<RiskCustomerBlacklistEntity> noRepeat = new ArrayList<>();



    public RiskCustomerBlacklistExcelListener(RiskCustomerBlacklistMapper riskCustomerBlacklistMapper,RiskCustomerBlacklistLogMapper riskCustomerBlacklistLogMapper) {
        this.riskCustomerBlacklistMapper = riskCustomerBlacklistMapper;
        this.riskCustomerBlacklistLogMapper = riskCustomerBlacklistLogMapper;
    }
    /**
     * 每一条数据解析都会来调用
     */
    @Override
    public void invoke(RiskCustomerBlacklistImportDTO data, AnalysisContext context) {
        log.info("解析到一条数据: {}", data);
        // 数据校验
        validateImportData(data);
        cachedDataList.add(data);
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存
        if (cachedDataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 数据校验
     */
    private void validateImportData(RiskCustomerBlacklistImportDTO data) {
        if (data == null
                || StringUtils.isBlank(data.getName())
                || StringUtils.isBlank(data.getPhoneNumber())
                || StringUtils.isBlank(data.getIdNumber())
                || StringUtils.isBlank(data.getBlackReason())
                || StringUtils.isBlank(data.getRemarks())
        ){
            throw new BusinessException("有必填项未填写，请先补充完成再导入");
        }
        // 验证手机号格式
        if (!data.getPhoneNumber().matches("^1[3-9]\\d{9}$")) {
            throw new BusinessException("手机号格式不正确: " + data.getPhoneNumber());
        }
        // 验证身份证号格式（简单验证）
        if (data.getIdNumber().length() != 18) {
            throw new BusinessException("身份证号长度应为18位: " + data.getIdNumber());
        }
    }
    private void insertLog(Integer blacklistId,Integer blacklistType,String blacklistContent,String blackReason,String remarks) {
        RiskCustomerBlacklistLogEntity riskCustomerBlacklistLog = new RiskCustomerBlacklistLogEntity();
        riskCustomerBlacklistLog.setBlacklistId(blacklistId);
        riskCustomerBlacklistLog.setBlacklistType(blacklistType);
        riskCustomerBlacklistLog.setBlacklistContent(blacklistContent);
        riskCustomerBlacklistLog.setBlackReason(blackReason);
        riskCustomerBlacklistLog.setRemarks(remarks);
        riskCustomerBlacklistLogMapper.insert(riskCustomerBlacklistLog);
    }
    /**
     * 存储数据库
     */
    private void saveData() {
        log.info("{}条数据，开始存储数据库！", cachedDataList.size());
        cachedDataList.forEach(importDTO -> {
            List<RiskCustomerBlacklistEntity> riskCustomerBlacklistEntities = riskCustomerBlacklistMapper.selectList(Wrappers.<RiskCustomerBlacklistEntity>lambdaQuery()
                    .eq(RiskCustomerBlacklistEntity::getIdNumber, importDTO.getIdNumber())
                    .eq(RiskCustomerBlacklistEntity::getPhoneNumber, importDTO.getPhoneNumber())
                    .eq(RiskCustomerBlacklistEntity::getStatus, 0)
                    .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0)
            );
            if (CollUtil.isEmpty(riskCustomerBlacklistEntities)){
                //添加黑名单数据
                RiskCustomerBlacklistEntity entity = new RiskCustomerBlacklistEntity();
                BeanUtils.copyProperties(importDTO, entity);
                entity.setDeleteFlag(0); // 有效
                entity.setStatus(0); // 默认状态为生效
                entity.setInfoSource(0);
                int insert = riskCustomerBlacklistMapper.insert(entity);
                if (insert>0){
                    //添加操作日志
                    insertLog(entity.getId(), 1, "添加黑名单数据", entity.getBlackReason(), entity.getRemarks());
                    noRepeat.add(entity);
                    //发送到数字化系统
                    digitalizeFeign.addBlacklist(
                            new AddBlacklistDTO()
                                    .setCustomerName(entity.getName())
                                    .setMobile(entity.getPhoneNumber())
                                    .setIdNumber(entity.getIdNumber())
                                    .setBlackReason(entity.getRemarks())
                                    .setType(entity.getBlackReason())

                    );
                }
            }else {
                //修改黑名单数据
                RiskCustomerBlacklistEntity entity = riskCustomerBlacklistEntities.get(0);
                if (entity.equals(importDTO.getBlackReason())){
                    insertLog(entity.getId(), 1, "添加黑名单数据", entity.getBlackReason(), entity.getRemarks());
                }else {
                    int update = riskCustomerBlacklistMapper.update(Wrappers.<RiskCustomerBlacklistEntity>lambdaUpdate()
                            .set(RiskCustomerBlacklistEntity::getBlackReason, entity.getBlackReason() + importDTO.getBlackReason())
                            .eq(RiskCustomerBlacklistEntity::getId, entity.getId())
                    );
                    if (update>0){
                        //添加操作日志
                        insertLog(entity.getId(), 2, "修改黑名单数据", entity.getBlackReason(), entity.getRemarks());
                        repeat.add(entity);
                    }
                }
            }
        });
        log.info("存储数据库成功！");
    }

    /**
     * 所有数据解析完成了都会来调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        saveData();
    }
}
