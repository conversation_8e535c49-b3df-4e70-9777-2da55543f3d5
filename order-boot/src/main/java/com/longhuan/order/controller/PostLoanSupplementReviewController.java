package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.OrderFileEntity;
import com.longhuan.order.pojo.entity.OrderIdCardUpdateEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.PostLoanSupplementReviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "贷后补件")
@RestController
@RequestMapping("/api/v1/patches")
@RequiredArgsConstructor
public class PostLoanSupplementReviewController {
    private final PostLoanSupplementReviewService postLoanSupplementReviewService;

    @ApiOperation(value = "贷后补件列表")
    @PostMapping("/pagePatches")
    public Result<Page<PagePatchesVO>> pagePatches(@RequestBody PagePatchesDTO dto, @CurrentUser LoginUser currentUser) {
        return Result.success(postLoanSupplementReviewService.pagePatches(dto, currentUser));
    }

    @ApiOperation(value = "补件详情")
    @GetMapping("/patchesDetail")
    public Result<AppointmentMortgageDetailVO> patchesDetail(@RequestParam("patchesId") Integer patchesId) {
        return Result.success(postLoanSupplementReviewService.patchesDetail(patchesId));
    }

    @ApiOperation(value = "贷后补件提交")
    @PostMapping("/patchesSubmit")
    public Result<Boolean> patchesSubmit(@RequestBody PatchesSubmitDTO dto, @CurrentUser LoginUser currentUser) {
        return Result.success(postLoanSupplementReviewService.patchesSubmit(dto, currentUser));
    }

    @ApiOperation(value = "贷后补件合同审批列表")
    @PostMapping("/pagePatchesApproval")
    public Result<Page<PagePatchesVO>> pagePatchesApproval(@RequestBody PagePatchesDTO dto, @CurrentUser LoginUser currentUser) {
        return Result.success(postLoanSupplementReviewService.pagePatchesApproval(dto, currentUser));
    }

    @ApiOperation(value = "贷后补件合同审批")
    @PostMapping("/patchesApproval")
    public Result<Boolean> patchesApproval(@RequestBody PatchesApprovalDTO dto, @CurrentUser LoginUser currentUser) {
        return Result.success(postLoanSupplementReviewService.patchesApproval(dto, currentUser));
    }

    @ApiOperation(value = "补件流程")
    @PostMapping("/patchworkProcess")
    public Result<List<PatchworkProcessVO>> patchworkProcess(@RequestBody PatchworkProcessDTO dto) {
        return Result.success(postLoanSupplementReviewService.patchworkProcess(dto));
    }

    @ApiOperation(value = "资方产品列表")
    @PostMapping("/employerProductList")
    public Result<List<EmployerProductListVO>> employerProductList() {
        return Result.success(postLoanSupplementReviewService.employerProductList());
    }

    @ApiOperation(value = "上传资方文件任务")
    @GetMapping("/uploadFundFileTask")
    public Result<Boolean> uploadFundFileTask() {
        postLoanSupplementReviewService.uploadFundFileTask();
        return Result.success(true);
    }

    /**
     * 通过车架号获取订单id（电销使用）
     */
    @ApiOperation(value = "通过车架号获取订单id")
    @PostMapping("/getOrderId")
    public GetOrderIdVO getOrderId(@RequestParam("vin") String vin) {
        return postLoanSupplementReviewService.getOrderId(vin);
    }

    @ApiOperation(value = "通过车架号获取客户经理id")
    @PostMapping("/getManagerId")
    public GetManagerIdVO getManagerId(@RequestParam("vin") String vin) {
        return postLoanSupplementReviewService.getManagerId(vin);
    }

    @ApiOperation(value = "贷款用途证明上传列表")
    @PostMapping("/loanUploadList")
    public Result<Page<PagePatchesVO>> loanUploadList(@RequestBody PagePatchesDTO dto, @CurrentUser LoginUser currentUser) {
        return Result.success(postLoanSupplementReviewService.loanUploadList(dto, currentUser));
    }

    @ApiOperation(value = "贷款用途证明上传")
    @GetMapping("/loanUploadInfo")
    public Result<Boolean> loanUploadInfo(@RequestParam Integer orderId) {
        return Result.success(postLoanSupplementReviewService.loanUploadInfo(orderId));
    }

    @ApiOperation(value = "身份证更新申请")
    @GetMapping("/idCardUpdateApply")
    public Result<Boolean> idCardUpdateApply(@RequestParam Integer orderId) {
        return Result.success(postLoanSupplementReviewService.idCardUpdateApply(orderId));
    }


    @ApiOperation(value = "保存更新的身份证信息")
    @PostMapping("/saveUpdateIdCardInfo")
    public Result<Boolean> saveUpdateIdCardInfo(@RequestBody OrderIdCardUpdateEntity entity) {
        return Result.success(postLoanSupplementReviewService.saveUpdateIdCardInfo(entity));
    }

    @ApiOperation(value = "主动查询身份证变更状态")
    @GetMapping("/idCardUpdateQuery")
    public Result<String> idCardUpdateQuery(@RequestParam Integer orderId) {
        return Result.success(postLoanSupplementReviewService.idCardUpdateQuery(orderId));
    }

    @ApiOperation(value = "查询保存的身份证信息")
    @GetMapping("/idCardQueryByOrderId")
    public Result<OrderIdCardUpdateEntity> idCardQueryByOrderId(@RequestParam Integer orderId) {
        return Result.success(postLoanSupplementReviewService.idCardQueryByOrderId(orderId));
    }

    /**
     * 贷后补件列表导出
     *
     * @param pagePatchesDTO 订单id
     */
    @ApiOperation(value = "贷后补件列表导出")
    @PostMapping(value = "/export")
    public void exportExcel(@RequestBody PagePatchesDTO pagePatchesDTO, @CurrentUser LoginUser loginUser, HttpServletResponse response) {
        postLoanSupplementReviewService.exportExcel(pagePatchesDTO, loginUser, response);
    }
    /**
     * 长银补件列表导出!
     */
    @ApiOperation(value = "长银补件列表导出")
    @PostMapping(value = "changyin/export")
    public void exports(@RequestBody PagePatchesDTO pagePatchesDTO, @CurrentUser LoginUser loginUser, HttpServletResponse response) {
        postLoanSupplementReviewService.exports(pagePatchesDTO, loginUser, response);
    }

    /**
     * 下载
     */
    @ApiOperation(value = "贷后贷款用途证明下载")
    @PostMapping("/loanProveDownload")
    public Result<String> loanProveDownload(@RequestBody MenuFileDTO menuFileDTO) {
        String Imgurl = postLoanSupplementReviewService.loanProveDownload(menuFileDTO);
        return Result.success(Imgurl);
    }
    /**
     * 定时推送补件消息通知
     */
    @ApiOperation(value = "定时推送补件消息通知")
    @PostMapping("/sendPatchesMessage")
    public void sendPatchesMessage() {
        postLoanSupplementReviewService.sendPatchesMessage();
    }
}
