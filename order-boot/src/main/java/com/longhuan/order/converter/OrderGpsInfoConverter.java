package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.AppointmentGpsDetailDTO;
import com.longhuan.order.pojo.entity.OrderGpsInfoEntity;
import com.longhuan.order.pojo.vo.AppointmentGpsDetailVO;
import com.longhuan.order.pojo.vo.OrderGpsInfoVO;
import org.mapstruct.Mapper;

/**
 * 客户签约信息转炉
 *
 * <AUTHOR>
 * @date 2024/08/16
 */
@Mapper(componentModel = "spring")
public interface OrderGpsInfoConverter {
    /**
     * DTO 转实体
     *
     * @param appointmentGpsDetailDTO 预约GPS详情信息DTO
     * @return {@link OrderGpsInfoEntity }
     */
    OrderGpsInfoEntity dtoToEntity(AppointmentGpsDetailDTO appointmentGpsDetailDTO);

    OrderGpsInfoVO entityToVO(OrderGpsInfoEntity entity);

    AppointmentGpsDetailVO entityToAppointmentGpsDetailVO(OrderGpsInfoEntity orderGpsInfoEntity);
}
