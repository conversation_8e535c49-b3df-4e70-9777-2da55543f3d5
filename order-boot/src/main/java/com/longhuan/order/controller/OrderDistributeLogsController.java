package com.longhuan.order.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;



@Api(tags = "派单分配操作记录")
@RestController
@RequestMapping("/api/v1/distributeLogs")
@RequiredArgsConstructor
public class OrderDistributeLogsController {
    private final OrderDistributeLogsService orderDistributeLogsService;


    @ApiOperation(value = "分页查询派单操作记录")
    @PostMapping("/page/distributeLogsList")
    public Result<Page<OrderApproveDistributeVO>> pageApproveList(@RequestBody QueryOrderApproveDistributeListDTO dto, @CurrentUser LoginUser loginUser) {
        return Result.success(orderDistributeLogsService.pageList(dto, loginUser));
    }

    @ApiOperation(value = "临时更新区域表的区域为大区")
    @PostMapping("/updateAreaTypeToRegionId")
    public Result<String> updateAreaTypeToRegionId(@RequestBody QueryOrderApproveDistributeListDTO dto) {
        return Result.success(orderDistributeLogsService.updateAreaTypeToRegionId(dto));
    }


}
