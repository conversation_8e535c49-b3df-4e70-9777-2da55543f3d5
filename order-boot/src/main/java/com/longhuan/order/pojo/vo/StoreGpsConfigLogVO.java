package com.longhuan.order.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 门店GPS配置操作记录VO
 *
 * <AUTHOR>
 * @date 2025/01/01
 */
@Data
@Accessors(chain = true)
public class StoreGpsConfigLogVO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 免费门店ID列表
     */
    @ApiModelProperty(value = "免费门店ID列表")
    private List<Integer> freeStoreIds;

    /**
     * 分期门店ID列表
     */
    @ApiModelProperty(value = "分期门店ID列表")
    private List<Integer> installmentStoreIds;

    /**
     * 操作结果（1：成功，0：失败）
     */
    @ApiModelProperty(value = "操作结果（1：成功，0：失败）")
    private Integer operationResult;

    /**
     * 操作结果描述
     */
    @ApiModelProperty(value = "操作结果描述")
    private String operationResultDesc;

    /**
     * 错误信息（操作失败时记录）
     */
    @ApiModelProperty(value = "错误信息（操作失败时记录）")
    private String errorMessage;

    /**
     * 影响的门店数量
     */
    @ApiModelProperty(value = "影响的门店数量")
    private Integer affectedStoresCount;
}
