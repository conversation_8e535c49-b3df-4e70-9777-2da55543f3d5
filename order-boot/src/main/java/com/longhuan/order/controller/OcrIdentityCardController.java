package com.longhuan.order.controller;

import cn.hutool.core.util.ObjUtil;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.converter.PreOcrIdentityCardConverter;
import com.longhuan.order.kingdee.Kingdee;
import com.longhuan.order.mapper.PreApprovalApplyInfoMapper;
import com.longhuan.order.pojo.dto.IdentityCardDTO;
import com.longhuan.order.pojo.entity.PreApprovalApplyInfoEntity;
import com.longhuan.order.pojo.entity.PreOcrIdentityCardEntity;
import com.longhuan.order.pojo.vo.CheckProtectionVo;
import com.longhuan.order.pojo.vo.PreIdVO;
import com.longhuan.order.pojo.vo.PreOcrIdentityCardVO;
import com.longhuan.order.service.PreOcrIdentityCardService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * ocr身份证
 * <AUTHOR>
 * @Date 2024/7/8
 */
@RestController
@RequestMapping("/ocrIdentityCard")
@RequiredArgsConstructor
public class OcrIdentityCardController {
    private final PreOcrIdentityCardService preOcrIdentityCardService;
    private final PreOcrIdentityCardConverter preOcrIdentityCardConverter;
    private final PreApprovalApplyInfoMapper preApprovalApplyInfoMapper;
    /**
     * 新增身份证信息
     *
     * @param identityCardDTO 身份证信息
     * @return Result
     */
    @PostMapping
    @Kingdee
    public Result<PreIdVO> insertIdentityCard(@RequestBody @Validated IdentityCardDTO identityCardDTO,
                                              @CurrentUser() LoginUser loginUserInfo) {
        return Result.success(preOcrIdentityCardService.savePreIdentityCard(identityCardDTO, loginUserInfo));
    }

    /**
     * 根据预审批申请id查询身份证信息
     *
     * @param preId 预审批申请id
     * @return 身份证信息
     */
    @GetMapping("/getDetailsByApplyInfoId/{preId}")
    public Result<PreOcrIdentityCardVO> getDetailsByApplyInfoId(@PathVariable("preId") Integer preId) {
        PreOcrIdentityCardEntity identityCard = preOcrIdentityCardService.getDetailsByApplyInfoId(preId);
        PreOcrIdentityCardVO vo = null;
        if (ObjUtil.isNotNull(identityCard)){
             vo = preOcrIdentityCardConverter.dtoToVo(identityCard);
        }
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(preId);
        if (ObjUtil.isNotEmpty(preApprovalApplyInfoEntity) && ObjUtil.isNotEmpty(vo)){
            vo.setLoanPeriod(preApprovalApplyInfoEntity.getLoanPeriod());
        }
        return Result.success(vo);
    }

    /**
     * 校验用户是否受到保护
     *
     * @param checkProtectionVo 校验参数
     * @return 是否受到保护结果
     */
    @PostMapping("/checkProtection")
    public Result<Integer> checkProtection(@RequestBody CheckProtectionVo checkProtectionVo) {
        return Result.success(preOcrIdentityCardService.checkProtection(checkProtectionVo.getName(), checkProtectionVo.getIdNumber(), checkProtectionVo.getAccountManagerId(),null));
    }
}
