package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.approve.api.pojo.dto.RepurchaseRepayCalcDTO;
import com.longhuan.approve.api.pojo.vo.YingFengRepurchaseRepayCalcVO;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.FundRepurchaseResultDTO;
import com.longhuan.order.pojo.dto.SearchOptionalOrderDTO;
import com.longhuan.order.pojo.vo.OrderPayApplyListVO;
import com.longhuan.order.service.RepurchaseRepayService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 赎回信息相关接口
 *
 */
@Api(tags = "赎回信息")
@RestController
@RequestMapping("/api/v1/repurchase")
@RequiredArgsConstructor
public class FundRepurchaseRepayController {

    private final RepurchaseRepayService repurchaseRepayService;

    /**
     * 赎回试算
     * @param repurchaseRepayCalcDTO
     * @return
     */
    @PostMapping("/repayCalc")
    public Result<YingFengRepurchaseRepayCalcVO> repurchaseRepayCalc(@RequestBody RepurchaseRepayCalcDTO repurchaseRepayCalcDTO){

        return Result.success(repurchaseRepayService.repurchaseRepayCalc(repurchaseRepayCalcDTO));
    }

    /**
     * 赎回还款申请
     * @param repurchaseRepayCalcDTO
     * @return
     */
    @PostMapping("/assetRepayment")
    public Result<Boolean> assetRepayment(@RequestBody RepurchaseRepayCalcDTO repurchaseRepayCalcDTO){

        return Result.success(repurchaseRepayService.assetRepayment(repurchaseRepayCalcDTO));
    }

    /**
     * 赎回订单信息
     * @param
     * @return
     */
    @PostMapping("/orderPay")
    public Result<Page<OrderPayApplyListVO>> orderPayApplyInfo(@RequestBody SearchOptionalOrderDTO searchOptionalOrderDTO, @CurrentUser LoginUser loginUser){

        return Result.success(repurchaseRepayService.orderPayApplyInfo(searchOptionalOrderDTO, loginUser));
    }

    /**
     * 赎回结果处理
     * @return
     */
    @PostMapping("/queryRepurchaseRepayment")
    public Result<Boolean> queryRepurchaseRepayment(@RequestBody List<FundRepurchaseResultDTO> list){

        return Result.success(repurchaseRepayService.queryRepurchaseRepayment(list));
    }

    /**
     * 获取资方赎回文件
     * @return
     */
    @PostMapping("/repurchase/contract")
    public Result<Boolean> downloadRepurchaseContract(){

        return Result.success(repurchaseRepayService.downloadRepurchaseContract());
    }
}
