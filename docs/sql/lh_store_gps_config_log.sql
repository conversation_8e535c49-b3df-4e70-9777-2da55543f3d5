-- 门店GPS配置操作记录表
-- 表名: lh_store_gps_config_log
-- 描述: 记录门店GPS分期和收费策略配置的操作历史

-- 创建表
CREATE TABLE IF NOT EXISTS lh_store_gps_config_log (
    id serial,
    operation_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    operator_id INTEGER NOT NULL,
    operator_name VARCHAR(100) NOT NULL,
    free_store_ids TEXT,
    installment_store_ids TEXT,
    delete_flag INTEGER NOT NULL DEFAULT 0,
    create_by INTEGER,
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    update_by INTEGER,
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_lh_store_gps_config_log_operator_id ON lh_store_gps_config_log(operator_id);
CREATE INDEX IF NOT EXISTS idx_lh_store_gps_config_log_operation_time ON lh_store_gps_config_log(operation_time);
CREATE INDEX IF NOT EXISTS idx_lh_store_gps_config_log_delete_flag ON lh_store_gps_config_log(delete_flag);

-- 添加字段注释
COMMENT ON TABLE lh_store_gps_config_log IS '门店GPS配置操作记录表';
COMMENT ON COLUMN lh_store_gps_config_log.id IS '主键';
COMMENT ON COLUMN lh_store_gps_config_log.operation_time IS '操作时间';
COMMENT ON COLUMN lh_store_gps_config_log.operator_id IS '操作人ID';
COMMENT ON COLUMN lh_store_gps_config_log.operator_name IS '操作人姓名';
COMMENT ON COLUMN lh_store_gps_config_log.free_store_ids IS '免费门店ID列表（JSON格式）';
COMMENT ON COLUMN lh_store_gps_config_log.installment_store_ids IS '分期门店ID列表（JSON格式）';
COMMENT ON COLUMN lh_store_gps_config_log.delete_flag IS '删除标记（0：未删除，1：已删除）';
COMMENT ON COLUMN lh_store_gps_config_log.create_by IS '创建人';
COMMENT ON COLUMN lh_store_gps_config_log.create_time IS '创建时间';
COMMENT ON COLUMN lh_store_gps_config_log.update_by IS '更新人';
COMMENT ON COLUMN lh_store_gps_config_log.update_time IS '更新时间';
