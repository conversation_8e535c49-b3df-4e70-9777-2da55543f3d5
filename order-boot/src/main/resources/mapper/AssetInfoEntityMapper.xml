<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.longhuan.order.mapper.AssetInfoEntityMapper">
    <select id="selectAssetDisposalOrderApplyList" resultType="com.longhuan.order.pojo.vo.AssetDisposalOrderApplyVO">
        select
            t.id as orderId,
            t.order_number as orderNumber,
            t.customer_name as customerName,
            t.fund_name as fundName,
            t.product_name as productName,
            t.store_name as storeName,
            loa.customer_confirm_amount as customerConfirmAmount,
            t.overdue_days as overdueDays,
            t.payment_state as oderState,
            ( select MAX(lci.repayment_date) from LH_FUND_REPAYMENT_INFO lci where t.id = lci.order_id and
                lci.actually_date is null ) as overdueDate
        from lh_order_info t
                 left join
             lh_order_amount loa on t.id =loa.order_id
        where
            t.delete_flag = 0
          and t.payment_state=2 and t.is_overdue=1
          and t.id not in (select order_id from lh_asset_info where delete_flag = 0 and current_node !=0 )
    </select>

</mapper>