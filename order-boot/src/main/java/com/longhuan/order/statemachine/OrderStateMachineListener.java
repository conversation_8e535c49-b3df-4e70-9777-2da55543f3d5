package com.longhuan.order.statemachine;

import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import jdk.jfr.Event;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;
import org.springframework.statemachine.state.State;
import org.springframework.statemachine.transition.Transition;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OrderStateMachineListener extends StateMachineListenerAdapter<States, Events> {

    @Override
    public void stateChanged(State<States, Events> from, State<States, Events> to) {
        if (from != null && to != null) {
            // 状态确实改变了
            log.info("State change from: {} ,to: {}", from.getId(), to.getId());
        }

    }

    @Override
    public void transitionEnded(Transition<States, Events> transition) {
        if (transition != null) {
            log.info("Transition ended transition:{}", transition);
        }

    }

    @Override
    public void eventNotAccepted(Message<Events> event) {
        log.info("Event not accepted event:{}", event);
    }

    @Override
    public void stateMachineError(StateMachine<States, Events> stateMachine, Exception exception) {
        log.error("State machine error {}", exception.getMessage(), exception);
        super.stateMachineError(stateMachine, exception);
    }

    @Override
    public void transition(Transition<States, Events> transition) {
        if (transition != null) {

            log.info("Transition transition:{}", transition);
        }
    }


}
