package com.longhuan.approve.boot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiAfterReplenishVerifyResponse;
import com.longhuan.approve.boot.client.LanHaiClient;
import com.longhuan.approve.boot.enums.FundApiDictEnum;
import com.longhuan.approve.boot.enums.FundApiDictTypeEnum;
import com.longhuan.approve.boot.enums.lanhai.LanHaiApiEnums;
import com.longhuan.approve.boot.feign.MessageFeign;
import com.longhuan.approve.boot.feign.ResourceFeign;
import com.longhuan.approve.boot.feign.UserFeign;
import com.longhuan.approve.boot.mapper.FinalFundInfoMapper;
import com.longhuan.approve.boot.mapper.FundApiDictMapper;
import com.longhuan.approve.boot.mapper.FundApproveMapper;
import com.longhuan.approve.boot.mapper.OrderInfoMapper;
import com.longhuan.approve.boot.pojo.dto.CustomerBaseDTO;
import com.longhuan.approve.api.pojo.dto.RegularlyQueryPostLoanSupplementsDTO;
import com.longhuan.approve.boot.pojo.dto.lanhai.LanHaiResult;
import com.longhuan.approve.boot.pojo.dto.lanhai.request.LanHaiAfterReplenishRequest;
import com.longhuan.approve.boot.pojo.dto.lanhai.request.LanHaiAfterReplenishVerifyRequest;
import com.longhuan.approve.boot.pojo.dto.lanhai.request.LanHaiImageUploadRequest;
import com.longhuan.approve.boot.pojo.dto.lanhai.response.LanHaiImageUploadResponse;
import com.longhuan.approve.boot.pojo.dto.lanhai.response.LanHaiPayAfterReplenishResponse;
import com.longhuan.approve.boot.pojo.entity.FinalFundInfoEntity;
import com.longhuan.approve.boot.pojo.entity.FundApiDictEntity;
import com.longhuan.approve.boot.pojo.entity.OrderInfoEntity;
import com.longhuan.approve.boot.service.AfterLoanPatchesService;
import com.longhuan.approve.boot.service.FundApiDictService;
import com.longhuan.approve.boot.service.LanHaiExpandService;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.MsgConstants;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.enums.States;
import com.longhuan.resource.pojo.dto.FundResourceDTO;
import com.longhuan.resource.pojo.dto.FundResourceResultDTO;
import com.longhuan.user.pojo.dto.MessageContent;
import com.longhuan.user.pojo.vo.UserDetailInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class LanHaiExpandServiceImpl implements LanHaiExpandService {
    private final OrderInfoMapper orderInfoMapper;
    private final LanHaiClient lanHaiClient;
    private final FundApiDictService fundApiDictService;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final FundApproveMapper fundApproveMapper;
    private final ResourceFeign resourceFeign;
    private final UserFeign userFeign;
    private final MessageFeign messageFeign;
    private final FundApiDictMapper fundApiDictMapper;
    private final AfterLoanPatchesService afterLoanPatchesService;

    @Override
    public void payAfterReplenish(Integer orderId) {
        log.info("LanHaiServiceImpl.payAfterReplenish - orderId:{}", orderId);
        LanHaiAfterReplenishRequest lanHaiAfterReplenishRequest = payAfterReplenishBuild(orderId);
        log.info("LanHaiServiceImpl.payAfterReplenish - orderId:{} - lanHaiAfterReplenishRequest:{}", orderId, JSONUtil.toJsonStr(lanHaiAfterReplenishRequest));
        String oriRequestSerialNo = getOriRequestSerialNo();
        LanHaiResult<LanHaiPayAfterReplenishResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_PAY_AFTER_REPLENISH, lanHaiAfterReplenishRequest, LanHaiPayAfterReplenishResponse.class, oriRequestSerialNo);
        if (!LanHaiResult.isSuccess(result)) {
            log.error("LanHaiServiceImpl.payAfterReplenish - orderId:{} - result:{}", orderId, JSONUtil.toJsonStr(result));
            throw new BusinessException(result.getMsg());
        }
        log.info("LanHaiServiceImpl.payAfterReplenish - orderId:{} - result:{}", orderId, JSONUtil.toJsonStr(result));

        //保存流水号
        fundApiDictService.saveFundApiDictByLinkId(orderId, FundApiDictTypeEnum.LOAN_AFTER, FundApiDictEnum.LOAN_AFTER_REPLENISH, oriRequestSerialNo);

        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .last("limit 1")
        );
        if (ObjUtil.isNull(finalFundInfo)) {
            log.info("LanHaiServiceImpl.payAfterReplenish not finalFundInfo - orderId:{} ", orderId);
            throw new BusinessException("借款信息不存在");
        }
        String applyId = result.getData().getBody().getApplyId();
        FinalFundInfoEntity finalFundInfoUpdate = new FinalFundInfoEntity();
        finalFundInfoUpdate.setLoanAfterReplenishNo(applyId);
        finalFundInfoUpdate.setId(finalFundInfo.getId());
        finalFundInfoMapper.updateById(finalFundInfoUpdate);
    }

    @Override
    public void regularlyPushPostLoanSupplements(Integer days) {
        log.info("regularlyPushPostLoanSupplements days:{}",days);
        LocalDate startTime = LocalDate.now().minusDays(days);
        LocalDate endTime = LocalDate.now();
        Map<Integer,String> resultMap = new HashMap<>();
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(
                new LambdaQueryWrapper<OrderInfoEntity>()
                        .ge(OrderInfoEntity::getCurrentNode, States.PAYMENT_SUCCESS.getNode())
//                        .eq(OrderInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .eq(OrderInfoEntity::getPaymentType, 1)
                        .between(OrderInfoEntity::getCreateTime, startTime, endTime)
        );
        if (CollUtil.isNotEmpty(orderInfoEntityList)){
            orderInfoEntityList.forEach(orderInfoEntity -> {
                if (orderInfoEntity.getFundId().equals(FundEnum.LAN_HAI.getValue())){
                    try {
                        payAfterReplenish(orderInfoEntity.getId());
                    } catch (Exception e) {
                        log.error("batchPayAfterReplenish e:{}", e.getMessage());
                        resultMap.put(orderInfoEntity.getId(), e.getMessage());
                    }
                }else {
                    afterLoanPatchesService.payAfterReplenish(orderInfoEntity);
                }
            });
        }
        log.info("regularlyPushPostLoanSupplements resultMap:{}", resultMap);
    }

    @Override
    public String getOriRequestSerialNo() {
        String requestSerialNo = "LH" + RandomUtil.randomString(3) + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        log.info("LanHaiServiceImpl.getOriRequestSerialNo requestSerialNo:{}", requestSerialNo);
        return requestSerialNo;
    }

    @Override
    public Map<Integer, String> regularlyQueryPostLoanSupplements(RegularlyQueryPostLoanSupplementsDTO dto) {
        log.info("regularlyQueryPostLoanSupplements dto:{}",dto);
        LocalDate startTime = LocalDate.now().minusDays(dto.getStart());
        LocalDate endTime = LocalDate.now().minusDays(dto.getEnd());
        Map<Integer,String> resultMap = new HashMap<>();
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(
                new LambdaQueryWrapper<OrderInfoEntity>()
                        .ge(OrderInfoEntity::getCurrentNode, States.PAYMENT_SUCCESS.getNode())
                        .eq(OrderInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .eq(OrderInfoEntity::getPaymentType, 1)
                        .between(OrderInfoEntity::getCreateTime, startTime, endTime)
        );
        if (CollUtil.isNotEmpty(orderInfoEntityList)){
            orderInfoEntityList.forEach(orderInfoEntity -> {
                try {
                    LanHaiAfterReplenishVerifyResponse lanHaiAfterReplenishVerifyResponse = payAfterReplenishQuery(orderInfoEntity.getId());
                    if (!Objects.equals(lanHaiAfterReplenishVerifyResponse.getHandlerStatus(),"001")){
                        Result<UserDetailInfoVO> userDetailInfoVOResult = userFeign.searchUserDetailById(orderInfoEntity.getManagerId());
                        MessageContent messageContent = new MessageContent()
                                .setMsgType(MsgConstants.MSG_TEXT)
                                .setSendType(MsgConstants.SEND_DD_NOTICE)
                                .setContent("客户："+orderInfoEntity.getCustomerName()+",车牌号："+orderInfoEntity.getVehicleNumber()+"贷后补件失败，失败原因："+lanHaiAfterReplenishVerifyResponse.getErrMsg()+"，请尽快完成贷后补件")
                                .setReceiver(userDetailInfoVOResult.getData().getMobile());
                        messageFeign.sendMessage(messageContent);
                        resultMap.put(orderInfoEntity.getId(), JSONUtil.toJsonStr(lanHaiAfterReplenishVerifyResponse));
                    }
                    afterLoanPatchesService.saveEntity(orderInfoEntity,Objects.equals(lanHaiAfterReplenishVerifyResponse.getHandlerStatus(),"001"),lanHaiAfterReplenishVerifyResponse.getErrMsg(),lanHaiAfterReplenishVerifyResponse.getErrTime());
                } catch (Exception e) {
                    log.error("regularlyQueryPostLoanSupplements e:{}", e.getMessage());
                    resultMap.put(orderInfoEntity.getId(), e.getMessage());
                }
            });
        }
        log.info("regularlyQueryPostLoanSupplements resultMap:{}", resultMap);
        return resultMap;
    }

    @Override
    public LanHaiAfterReplenishVerifyResponse payAfterReplenishQuery(Integer orderId) {
        log.info("LanHaiServiceImpl.payAfterReplenishQuery - orderId:{}", orderId);
        FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(new LambdaQueryWrapper<FundApiDictEntity>()
                .eq(FundApiDictEntity::getLinkId, orderId)
                .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.LOAN_AFTER)
                .eq(FundApiDictEntity::getCode, FundApiDictEnum.LOAN_AFTER_REPLENISH)
                .eq(FundApiDictEntity::getDeleteFlag, 0)
                .orderByDesc(FundApiDictEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(fundApiDictEntity)) {
            log.info("LanHaiServiceImpl.payAfterReplenishQuery not fundApiDictEntity - orderId:{} ", orderId);
            throw new BusinessException("流水号不存在");
        }
        //查询借款编号
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjectUtil.isEmpty(finalFundInfo)) {
            throw new BusinessException("借款编号不存在");
        }
        LanHaiAfterReplenishVerifyRequest request = new LanHaiAfterReplenishVerifyRequest();
        request.setOriRequestSerialNo(fundApiDictEntity.getValue());
        request.setLoanInvoiceId(finalFundInfo.getLoanNo());
        LanHaiResult<LanHaiAfterReplenishVerifyResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_PAY_AFTER_REPLENISH_VERIFY, request, LanHaiAfterReplenishVerifyResponse.class, getOriRequestSerialNo());
        if (!LanHaiResult.isSuccess(result)) {
            log.error("LanHaiServiceImpl.payAfterReplenishQuery - orderId:{} - result:{}", orderId, JSONUtil.toJsonStr(result));
            throw new BusinessException(result.getMsg());
        }

//        if (Objects.equals(result.getData().getBody().getHandlerStatus(),"001")){
//            afterLoanPatchesService.update(
//                    new LambdaUpdateWrapper<AfterLoanPatchesEntity>()
//                            .set(AfterLoanPatchesEntity::getFundUploadFlag, 1)
//                            .eq(AfterLoanPatchesEntity::getOrderId, orderId)
//                            .eq(AfterLoanPatchesEntity::getDeleteFlag,0)
//            );
//        }

        return result.getData().getBody();
    }

    /**
     * 贷后补件申请构建
     */
    @Override
    public LanHaiAfterReplenishRequest payAfterReplenishBuild(Integer orderId) {
        log.info("LanHaiServiceImpl.payAfterReplenishBuild - orderId:{}", orderId);
        //查询借款编号
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjectUtil.isEmpty(finalFundInfo)) {
            throw new BusinessException("借款编号不存在");
        }
        LanHaiAfterReplenishRequest creditApplyRequest = new LanHaiAfterReplenishRequest();
        creditApplyRequest.setUserId(finalFundInfo.getFundUserId());
        creditApplyRequest.setLoanInvoiceId(finalFundInfo.getLoanNo());
        creditApplyRequest.setIsReturn("1");

        CustomerBaseDTO customerFinalBaseInfo = fundApproveMapper.getCustomerFinalBaseInfo(orderId);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        List<LanHaiAfterReplenishRequest.Attachment> attachmentsList = new ArrayList<>();
        List<LanHaiImageUploadResponse.FileList> fileList = imageUpload(new FundResourceDTO().setFund(FundEnum.LAN_HAI)
                .setType(4).setLinkId(orderId).setRongDanId(orderInfoEntity.getRongdanId())
                .setCustomerBaseInfo(new FundResourceDTO.CustomerBaseInfo().setPhone(customerFinalBaseInfo.getPhone())
                        .setCustomerIdNo(customerFinalBaseInfo.getIdNumber())
                )
        );
        for (LanHaiImageUploadResponse.FileList item : fileList) {
            LanHaiAfterReplenishRequest.Attachment attachments = new LanHaiAfterReplenishRequest.Attachment();
            attachments.setFileKind(item.getFileKind());
            attachments.setFileName(item.getFileName());
            attachmentsList.add(attachments);
        }
        creditApplyRequest.setAttachments(attachmentsList);
        return creditApplyRequest;
    }

    @Override
    public List<LanHaiImageUploadResponse.FileList> imageUpload(FundResourceDTO dto) {
        log.info("LanHaiServiceImpl.imageUpload linkId:{} dto:{}", dto.getLinkId(), JSONUtil.toJsonStr(dto));
        Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(dto);
        log.info("LanHaiServiceImpl.imageUpload linkId:{} listResult:{}", dto.getLinkId(), JSONUtil.toJsonStr(listResult));
        Assert.isTrue(Result.isSuccess(listResult), () -> new BusinessException("获取影像文件失败"));

        List<FundResourceResultDTO> data = listResult.getData();
        LanHaiImageUploadRequest lanHaiImageUploadRequest = new LanHaiImageUploadRequest();
        List<LanHaiImageUploadRequest.FileInfo> fileList = new ArrayList<>();

        data.forEach(item -> {
            LanHaiImageUploadRequest.FileInfo fileInfo = new LanHaiImageUploadRequest.FileInfo();
            fileInfo.setFileData(item.getFilePath());
            fileInfo.setFileKind(item.getFileCode());
            fileInfo.setFileName(item.getFileId());
            fileList.add(fileInfo);
        });
        log.info("LanHaiServiceImpl.imageUpload fileListSize:{}", fileList.size());
        lanHaiImageUploadRequest.setFileList(fileList);

        List<LanHaiImageUploadResponse.FileList> fileListResult = new ArrayList<>();
        // 分批上传，每批最多1个文件
        int batchSize = 1;
        for (int i = 0; i < fileList.size(); i += batchSize) {
            List<LanHaiImageUploadRequest.FileInfo> subList = fileList.subList(i, Math.min(fileList.size(), i + batchSize));
            log.info("LanHaiServiceImpl.imageUpload subListSize:{}", JSONUtil.toJsonStr(subList));
            LanHaiImageUploadRequest batchRequest = new LanHaiImageUploadRequest();
            batchRequest.setFileList(subList);

            LanHaiResult<LanHaiImageUploadResponse> batchResult = lanHaiClient.execute(
                    LanHaiApiEnums.LH_IMAGE_UPLOAD,
                    batchRequest,
                    LanHaiImageUploadResponse.class,
                    getOriRequestSerialNo()
            );

            if (!LanHaiResult.isSuccess(batchResult)) {
                throw new BusinessException("上传影像文件失败");
            }
            fileListResult.addAll(batchResult.getData().getBody().getFileList());
        }

        return fileListResult;
    }

}
