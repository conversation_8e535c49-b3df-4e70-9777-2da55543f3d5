<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.longhuan.order.mapper.OrderTerminalConfigMapper">

    <select id="getUnRiskFirst" resultType="com.longhuan.order.pojo.vo.OrderTerminalVO">
        select loi.id orderId,loi.customer_name customerName, loi.current_node currentNode,
        loi.create_time,
        (now()::date -loi.create_time::date) AS daysDifference
        from lh_order_info loi
        <if test="dto.startNode == 2500">
            inner join lh_final_fund_info lffi on loi.id = lffi.order_id and lffi.fund_result = 2
        </if>
        where loi.current_node != -1000
        and loi.current_node >= #{dto.startNode}
        and loi.current_node &lt; #{dto.lastNode}
        and now()::date - loi.create_time::date > #{dto.validityDay}
        order by loi.create_time
    </select>

    <select id="getTerminalInfo" resultType="com.longhuan.order.pojo.vo.OrderTerminalVO">
        select lpaai.id preId,lpaai.create_time,  lpaai.fund_status, loi.id orderId,loi.customer_name customerName, loi.current_node currentNode,
        loi.create_time
        from lh_pre_approval_apply_info lpaai
        left join lh_order_info loi on loi.pre_id = lpaai.id and loi.delete_flag = 0 and current_node in
        <foreach item="node" index="index" collection="dto.nodeList" open="(" separator="," close=")">
            #{node}
        </foreach>
        left join lh_pre_fund_info pf on lpaai.id= pf.pre_id and lpaai.fund_id=pf.fund_id and pf.delete_flag = 0
        <if test="dto.processStage != null and dto.processStage == 1">
            inner join lh_final_fund_info lffi on loi.id = lffi.order_id and lffi.fund_result = 2 and lffi.delete_flag = 0
        </if>
        where lpaai.fund_status = 2 and lpaai.delete_flag = 0
        <if test="dto.processStage!= null and dto.processStage == 0">
            and lpaai.business_status != -10
            and now()::date - pf.fund_credit_time::date > #{dto.validityDay}
        </if>
        <if test="dto.processStage!= null and dto.processStage == 1">
            and now()::date - lffi.fund_credit_time::date > #{dto.validityDay}
        </if>
        order by lpaai.create_time


    </select>
</mapper>