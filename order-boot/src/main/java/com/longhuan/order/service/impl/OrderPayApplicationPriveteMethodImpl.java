package com.longhuan.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.approve.api.pojo.dto.DaiChangJieQingTongZhiResultDTO;
import com.longhuan.approve.api.pojo.dto.FundRepayCalcDTO;
import com.longhuan.approve.api.pojo.dto.FundRepaymentDeductCheckDTO;
import com.longhuan.approve.api.pojo.dto.RepurchaseRepayCalcDTO;
import com.longhuan.approve.api.pojo.dto.lanhai.TrialRepayDTO;
import com.longhuan.approve.api.pojo.vo.YingFengInfoVO;
import com.longhuan.approve.api.pojo.vo.YingFengRepurchaseRepayCalcVO;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiTrialRepayResponse;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.GlobalConstants;
import com.longhuan.common.core.constant.MsgConstants;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.constants.SwitchConstants;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.pojo.SwitchVO;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.redis.util.DictUtils;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.order.converter.OrderPayApplyConverter;
import com.longhuan.order.enums.*;
import com.longhuan.order.feign.*;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.dto.digitalize.DigitalizeSettleList;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.*;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.order.util.CostDisclosureUtil;
import com.longhuan.order.util.DigitalizeZhongxinEncryptUtil;
import com.longhuan.user.enums.DingTaskMappingNodeEnum;
import com.longhuan.user.pojo.dto.*;
import com.longhuan.user.pojo.vo.*;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.longhuan.order.constants.OrderPayConstants.*;
import static com.longhuan.order.service.impl.OrderPayApplicationServiceImpl.splitString;

/**
 * 付款申请服务扩展类
 *
 * <AUTHOR>
 * @Date 2024/7/29
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderPayApplicationPriveteMethodImpl implements OrderPayApplicationPriveteMethod {

    //对公转账枚举
    final List<OrderFeeDetailExpandTypeEnum> offlineFeeEnumList = List.of(OrderFeeDetailExpandTypeEnum.INSTALLMENT_SECURITY_DEPOSIT, OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT,
            OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT, OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY, OrderFeeDetailExpandTypeEnum.INSTALLMENT_SERVICE_FEE);

    private final OrderPayApplyNodeRecordMapper orderPayApplyNodeRecordMapper;
    private final UserFeign userFeign;
    private final EnvUtil envUtil;
    private final OrderPayApplyConverter orderPayApplyConverter;
    private final OrderPayApplicationMapper orderPayApplicationMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final FundRepaymentDeductService fundRepaymentDeductService;
    private final ExemptionApplicationEntityMapper exemptionApplicationEntityMapper;
    private final OrderFeeInfoMapper orderFeeInfoMapper;
    private final DingTaskFeign dingTaskFeign;
    private final SwitchUtils switchUtils;
    private final ResourceFeign resourceFeign;
    private final RepurchaseRepaymentInfoMapper repurchaseRepaymentInfoMapper;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final RepurchaseProcessService repurchaseProcessService;
    private final FundRepaymentDeductMapper fundRepaymentDeductMapper;
    private final MessageFeign messageFeign;
    private final ZhongXinService zhongXinService;
    private final OrderSettleAmountRecordMapper orderSettleAmountRecordMapper;
    private final FundDeductService fundDeductService;
    private final CaseInfoService caseInfoService;
    private final ApproveFeign approveFeign;
    private final DigitalizeFeign digitalizeFeign;
    private final OutsourcingSettlementTrialCalculationsService outsourcingSettlementTrialCalculationsService;
    private final DigitalOutsourcingOrderEntityMapper digitalOutsourcingOrderEntityMapper;
    private final RepurchaseRepayService repurchaseRepayService;
    private final DictUtils dictUtils;
    private final  CaseInfoEntityMapper caseInfoEntityMapper;
    private final ManageBankAccountSignMapper manageBankAccountSignMapper;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final OutsourcingContractService outsourcingContractService;
    private final DigitalizeZhongxinEncryptUtil digitalizeZhongxinEncryptUtil;
    private final KingdeeOutsourcingService kingdeeOutsourcingService;
    private final DingDrawMoneyFeign dingDrawMoneyFeign;
    private final SprEductionUsageService sprEductionUsageService;
    private final DataPermissionService dataPermissionService;
    private final BankAccountSignMapper bankAccountSignMapper;
    private final AutomaticRecognitionInfoService automaticRecognitionInfoService;

    private final RedisService redisService;
    private final OrderPayApplicationExpandService orderPayApplicationExpandService;
    /**
     * 根据收款方类型和当前节点确定下一个节点
     *
     * @param feeType   费用类型
     * @param applyType   付费类型
     * @param payeeType   收款方类型
     * @param currentNode 当前节点
     * @return 下一个节点
     */
    @Override
    public PayApplicationNodeEnums getNextNode(OrderFeeDetailExpandTypeEnum feeType, OrderFeeDetailStatusEnum applyType, PayApplicationPayeeTypeEnum payeeType, PayApplicationNodeEnums currentNode) {
        //查账 收入 ->出纳
        //费用申请 支出 -> 账务审批 -> 出纳审核
        if (ObjUtil.equals(applyType, OrderFeeDetailStatusEnum.SPENDING) && List.of(OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT,OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT).contains(feeType)) {
            return switch (currentNode) {
                case ACCOUNTANT_APPLY ->
                        PayApplicationNodeEnums.ACCOUNTANT_APPROVAL;
                case ACCOUNTANT_APPROVAL ->
                        PayApplicationNodeEnums.CASHIER_APPROVAL;
                case CASHIER_APPROVAL ->
                        PayApplicationNodeEnums.SUCCESS;
                default -> currentNode;
            };
        }

        //转对公 收款单 发起申请->出纳审核
        if (ObjUtil.isNotNull(feeType)
                && List.of(OrderFeeDetailExpandTypeEnum.INSTALLMENT_SECURITY_DEPOSIT,
                        OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT, OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY,  OrderFeeDetailExpandTypeEnum.INSTALLMENT_SERVICE_FEE)
                .contains(feeType)
        ){
            return switch (currentNode) {
                case ACCOUNTANT_APPLY ->
                        PayApplicationNodeEnums.CASHIER_APPROVAL;
                case CASHIER_APPROVAL ->
                        PayApplicationNodeEnums.SUCCESS;
                default -> currentNode;
            };
        }

        //退费流程
        // 车务费、一次性保证金、分期月还、结清款、其他
        if (ObjUtil.isNotNull(feeType)
                && List.of(OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE)
                .contains(feeType)
        ){

            //客户
            if (ObjUtil.equal(payeeType, PayApplicationPayeeTypeEnum.CUSTOMER)) {
                return switch (currentNode) {
                    case ACCOUNTANT_APPLY ->
                        // 客户的付款单申请，下一个节点为门店经理审核
                            PayApplicationNodeEnums.STORE_MANAGER_APPROVAL;
                    case STORE_MANAGER_APPROVAL ->
                        // 客户的门店经理审核，下一个节点为会计审核
                            PayApplicationNodeEnums.ACCOUNTANT_APPROVAL;
                    default -> currentNode;
                };
            }
            //非客户
            return switch (currentNode) {
                case ACCOUNTANT_APPLY ->
                    // 业务员的付款单申请，下一个节点为门店经理审核
                        PayApplicationNodeEnums.STORE_MANAGER_APPROVAL;
                case STORE_MANAGER_APPROVAL ->
                    // 业务员的门店经理审核，下一个节点为大区运营审核
                        PayApplicationNodeEnums.REGION_MANAGER_APPROVAL;
                case REGION_MANAGER_APPROVAL ->
                    // 业务员的大区运营审核，下一个节点为会计审核
                        PayApplicationNodeEnums.ACCOUNTANT_APPROVAL;
                default -> currentNode;
            };
        }

        return switch (currentNode) {
            case ACCOUNTANT_APPLY ->
                    PayApplicationNodeEnums.ACCOUNTANT_APPROVAL;
            case ACCOUNTANT_APPROVAL ->
                    PayApplicationNodeEnums.CASHIER_APPROVAL;
            case CASHIER_APPROVAL ->
                    PayApplicationNodeEnums.SUCCESS;
            default -> currentNode;
        };
    }

    /**
     * 记录审核节点
     *
     * @param applyInfoId 付款申请信息ID
     * @param currentNode 当前节点
     * @param nextNode    下一个节点
     * @param auditType    审批类型
     * @param processId    钉钉审批流程ID
     * @param remark      备注
     * @param event       事件类型（1:通过，2：拒绝， 3:驳回）
     * @param approveTime 审核时间
     */
    @Override
    public void saveNodeRecord(int applyInfoId, PayApplicationNodeEnums currentNode, PayApplicationNodeEnums nextNode,
                               PayApplicationAuditTypeEnum auditType, String processId,
                               String remark, PayApplicationEventEnums event, Integer currentUserId, LocalDateTime approveTime) {
        try {
            OrderPayApplyNodeRecordEntity recordEntity = new OrderPayApplyNodeRecordEntity()
                    .setApplyInfoId(applyInfoId)
                    .setCurrentNode(currentNode)
                    .setRemark(remark)
                    .setEvent(event)
                    .setAuditType(auditType)
                    .setProcessId(processId)
                    .setNextNode(nextNode)
                    ;
            recordEntity.setCreateBy(currentUserId);
            recordEntity.setUpdateBy(currentUserId);
            recordEntity.setUpdateTime(approveTime);
            orderPayApplyNodeRecordMapper.insert(recordEntity);
        } catch (Exception e) {
            log.error("OrderPayApplicationServiceImpl.saveNodeRecord applyInfoId:{} err:{}", applyInfoId, e.getMessage(), e);
        }
    }

    /**
     * 处理付款申请的提交或更新
     *
     * @param payApplicationDTO 包含付款申请详细信息的数据传输对象
     * @param loginUser         当前登录用户信息
     * @return 如果操作成功返回true，否则返回false
     * @throws BusinessException 当订单已存在待审核的付款申请单时抛出
     */
    @Override
    public OrderPayApplicationInfoEntity saveOrderPayApplication(PayApplicationDTO payApplicationDTO, LoginUser loginUser) {
        String paymentVoucherList = "";
        if (CollUtil.isNotEmpty(payApplicationDTO.getResourceId())) {
            paymentVoucherList = JSONUtil.toJsonStr(payApplicationDTO.getResourceId());
        }

        OrderFeeDetailStatusEnum applyType = OrderFeeDetailExpandTypeEnum.toApplyType(payApplicationDTO.getPayeeType(), payApplicationDTO.getFeeType());
        if (payApplicationDTO.getId() != null) {
            OrderPayApplicationInfoEntity updateEntity = orderPayApplyConverter.dto2Entity(payApplicationDTO);
            updateEntity.setPaymentVoucherList(paymentVoucherList)
                    .setApplyType(applyType)
                    .setCurrentNode(getNextNode(payApplicationDTO.getFeeType(), applyType, payApplicationDTO.getPayeeType(), PayApplicationNodeEnums.ACCOUNTANT_APPLY))
            ;
            log.info("OrderPayApplicationServiceImpl OrderPayApplication update payApplicationDTO:{}", updateEntity);
            boolean updateSuccess = orderPayApplicationMapper.updateById(updateEntity) > 0;
            log.info("OrderPayApplicationServiceImpl OrderPayApplication update success:{}", updateSuccess);
            if (!updateSuccess){
                return null;
            }
            return orderPayApplicationMapper.selectById(payApplicationDTO.getId());
        }
        //todo 判断委外是否有减免
        if(ObjUtil.isNotEmpty(payApplicationDTO.getOrderApplicationSource())&&ObjUtil.equal(payApplicationDTO.getOrderApplicationSource(), 1)&&ObjUtil.equal(payApplicationDTO.getFeeType(), OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)){
            if(ObjUtil.equal(payApplicationDTO.getOrderSource(), 1)){
                CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getOrderId, payApplicationDTO.getOrderId()).eq(CaseInfoEntity::getDeleteFlag, 0).ne(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.FAIL.getCode()).orderByDesc(CaseInfoEntity::getCreateTime).last("limit 1"));
      if(ObjUtil.isNotEmpty(caseInfoEntity)){
    //查看是否有在途减免
   List<ExemptionApplicationEntity>    exemptionApplicationEntities=    exemptionApplicationEntityMapper.selectList(new LambdaQueryWrapper<ExemptionApplicationEntity>().eq(ExemptionApplicationEntity::getCaseId,caseInfoEntity.getId()).ne(ExemptionApplicationEntity::getReduceCurrentNode,ReduceApproveNodeEnums.FAIL).eq(ExemptionApplicationEntity::getDeleteFlag,0).ne(ExemptionApplicationEntity::getReduceCurrentNode,ReduceApproveNodeEnums.SUCCESS));
    if(CollUtil.isNotEmpty(exemptionApplicationEntities)){
        throw new BusinessException("存在未审批成功的减免流程，请先完成减免审批");
    }
      }
            }else if(ObjUtil.equal(payApplicationDTO.getOrderSource(), 2)){
                CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getDigitalOrderId, payApplicationDTO.getOrderNumber()).eq(CaseInfoEntity::getDataSource, 2).eq(CaseInfoEntity::getDeleteFlag, 0).ne(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.FAIL.getCode()).orderByDesc(CaseInfoEntity::getCreateTime).last("limit 1"));
                if(ObjUtil.isNotEmpty(caseInfoEntity)){
                    //查看是否有在途减免
                    List<ExemptionApplicationEntity>    exemptionApplicationEntities=    exemptionApplicationEntityMapper.selectList(new LambdaQueryWrapper<ExemptionApplicationEntity>().eq(ExemptionApplicationEntity::getCaseId,caseInfoEntity.getId()).ne(ExemptionApplicationEntity::getReduceCurrentNode,ReduceApproveNodeEnums.FAIL).eq(ExemptionApplicationEntity::getDeleteFlag,0).ne(ExemptionApplicationEntity::getReduceCurrentNode,ReduceApproveNodeEnums.SUCCESS));
                    if(CollUtil.isNotEmpty(exemptionApplicationEntities)){
                        throw new BusinessException("存在未审批成功的减免流程，请先完成减免审批");
                    }
                }
            }else if(ObjUtil.equal(payApplicationDTO.getOrderSource(), 3)){
                CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getDigitalOrderId, payApplicationDTO.getOrderNumber()).eq(CaseInfoEntity::getDataSource, 3).eq(CaseInfoEntity::getDeleteFlag, 0).ne(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.FAIL.getCode()).orderByDesc(CaseInfoEntity::getCreateTime).last("limit 1"));
                if(ObjUtil.isNotEmpty(caseInfoEntity)){
                    //查看是否有在途减免
                    List<ExemptionApplicationEntity>    exemptionApplicationEntities=    exemptionApplicationEntityMapper.selectList(new LambdaQueryWrapper<ExemptionApplicationEntity>().eq(ExemptionApplicationEntity::getCaseId,caseInfoEntity.getId()).ne(ExemptionApplicationEntity::getReduceCurrentNode,ReduceApproveNodeEnums.FAIL).eq(ExemptionApplicationEntity::getDeleteFlag,0).ne(ExemptionApplicationEntity::getReduceCurrentNode,ReduceApproveNodeEnums.SUCCESS));
                    if(CollUtil.isNotEmpty(exemptionApplicationEntities)){
                        throw new BusinessException("存在未审批成功的减免流程，请先完成减免审批");
                    }
                }
            }
        }
        Long count = 0L;
        if (offlineFeeEnumList.contains(payApplicationDTO.getFeeType())&&ObjUtil.equal(ObjUtil.defaultIfNull(payApplicationDTO.getOrderApplicationSource(), 0),0)) {
            //验证期数
            verifyOffline(payApplicationDTO.getOrderId(), payApplicationDTO.getRepaymentTerm(), payApplicationDTO.getFeeType());

            //对公转账类型
            count = orderPayApplicationMapper.selectCount(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                    .eq(OrderPayApplicationInfoEntity::getOrderId, payApplicationDTO.getOrderId())
                    .eq(OrderPayApplicationInfoEntity::getPayeeType, payApplicationDTO.getPayeeType())
                    .eq(OrderPayApplicationInfoEntity::getRepaymentTerm, payApplicationDTO.getRepaymentTerm())
                    .in(OrderPayApplicationInfoEntity::getFeeType, offlineFeeEnumList)
                    .notIn(OrderPayApplicationInfoEntity::getCurrentNode, List.of(PayApplicationNodeEnums.FAIL, PayApplicationNodeEnums.SUCCESS))
                    .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0));

            //验证是否存在划扣
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(payApplicationDTO.getOrderId());
            Assert.notNull(orderInfo, "订单不存在");
            boolean existDeduct = fundRepaymentDeductService.getCountByRepayType(orderInfo.getId(), orderInfo.getFundId(),
                    payApplicationDTO.getRepaymentTerm(), null, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING) > 0;
            if (existDeduct) {
                throw new BusinessException("存在划扣申请,请等待划扣完成");
            }

        } else {
            if (ObjUtil.equal(ObjUtil.defaultIfNull(payApplicationDTO.getOrderApplicationSource(), 0),0)){
                count = orderPayApplicationMapper.selectCount(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                        .eq(OrderPayApplicationInfoEntity::getOrderId, payApplicationDTO.getOrderId())
                        .eq(OrderPayApplicationInfoEntity::getPayeeType, payApplicationDTO.getPayeeType())
                        .eq(OrderPayApplicationInfoEntity::getFeeType, payApplicationDTO.getFeeType())
                        .ne(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.FAIL)
                        .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0));
            }else{
//                count = orderPayApplicationMapper.selectCount(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
//                        .eq(OrderPayApplicationInfoEntity::getOrderNumber, payApplicationDTO.getOrderNumber())
//                        .eq(OrderPayApplicationInfoEntity::getOrderApplicationSource, payApplicationDTO.getOrderApplicationSource())
//                        .eq(OrderPayApplicationInfoEntity::getOrderSource, payApplicationDTO.getOrderSource())
//                        .eq(OrderPayApplicationInfoEntity::getPayeeType, payApplicationDTO.getPayeeType())
//                        .eq(OrderPayApplicationInfoEntity::getFeeType, payApplicationDTO.getFeeType())
//                        .notIn(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.FAIL, PayApplicationNodeEnums.SUCCESS)
//                        .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
//                );
              if (payApplicationDTO.getOrderSource() == 1){
                  FundRepaymentDeductCheckDTO fundRepaymentDeductCheckDTO = new FundRepaymentDeductCheckDTO();
                  fundRepaymentDeductCheckDTO.setOrderId(payApplicationDTO.getOrderId());
                  FundRepaymentInfoEntity data = approveFeign.getLastUnRepaymentPlan(fundRepaymentDeductCheckDTO).getData();
                  payApplicationDTO.setRepaymentTerm(data.getTerm());
                  CaseInfoRepayCalcVO caseInfoRepayCalcVO = caseInfoService.earlyRepayCalc(payApplicationDTO.getOrderId());
                  payApplicationDTO.setLoanSettlementMethod(caseInfoRepayCalcVO.getLoanSettlementEnum());

              }
                if (ObjUtil.equal(payApplicationDTO.getFeeType(), OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT)){
                    if (payApplicationDTO.getOrderSource() != 1){
                        throw new BusinessException("请使用结清单");
                    }
                    Long l = orderPayApplicationMapper.selectCount(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                            .eq(OrderPayApplicationInfoEntity::getOrderId, payApplicationDTO.getOrderId())
                            .eq(OrderPayApplicationInfoEntity::getOrderApplicationSource, 1)
                            .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)
                            .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)
                            .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0));
                    if (l > 0) {
                        throw new BusinessException("已经申请过结清单，请继续使用结清单");
                    }
                    l=orderPayApplicationMapper.selectCount(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                            .eq(OrderPayApplicationInfoEntity::getOrderId, payApplicationDTO.getOrderId())
                            .eq(OrderPayApplicationInfoEntity::getOrderApplicationSource,1)
                            .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)
                            .ne(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.FAIL)
                            .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0));
                    if (l > 0) {
                        throw new BusinessException("存在待审核接清单，请继续使用结清单或审批拒绝再次使用月还款");
                    }
                }
                //减免id不为空判断减免是否生效
                if (CollUtil.isNotEmpty(payApplicationDTO.getReductionSourceId())){
                    List<ExemptionApplicationEntity> exemptionApplicationEntities = exemptionApplicationEntityMapper.selectBatchIds(payApplicationDTO.getReductionSourceId());
                    BigDecimal applyReductionAmounttotal = exemptionApplicationEntities.stream()
                            .map(record -> record.getApplyReductionAmount() != null ? record.getApplyReductionAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (applyReductionAmounttotal.compareTo(payApplicationDTO.getReductionAmount()) != 0){
                        throw new BusinessException("减免金额与减免单金额不一致，请重新选择");
                    }
                }
            }

        }

        if (count > 0) {
            log.warn("OrderPayApplicationServiceImpl OrderPayApplication found existing pending application for orderId:{}", payApplicationDTO.getOrderId());
            throw new BusinessException("存在待审核申请单，请勿重复申请");
        }

        OrderPayApplicationInfoEntity orderPayApplicationInfoEntity = new OrderPayApplicationInfoEntity();
        BeanUtil.copyProperties(payApplicationDTO, orderPayApplicationInfoEntity);

        orderPayApplicationInfoEntity.setCurrentNode(getNextNode(payApplicationDTO.getFeeType(), applyType, payApplicationDTO.getPayeeType(), PayApplicationNodeEnums.ACCOUNTANT_APPLY));
        orderPayApplicationInfoEntity.setPaymentVoucherList(paymentVoucherList);
        orderPayApplicationInfoEntity.setApplyUserId(loginUser.getUserId());
        orderPayApplicationInfoEntity.setApplyType(OrderFeeDetailExpandTypeEnum.toApplyType(payApplicationDTO.getPayeeType(), payApplicationDTO.getFeeType()));
        orderPayApplicationInfoEntity.setFeeDetails(payApplicationDTO.getFeeDetails());
        log.info("OrderPayApplicationServiceImpl OrderPayApplication end orderPayApplicationInfoEntity:{}", orderPayApplicationInfoEntity);

        //验证车务费审批
        if (payApplicationDTO.getFeeType() == OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE) {
            verifyCarFreeApply(payApplicationDTO.getOrderId(), payApplicationDTO.getPayeeType());
        }

        // 保存申请信息
        boolean insertSuccess = orderPayApplicationMapper.insert(orderPayApplicationInfoEntity) > 0;
        log.info("OrderPayApplicationServiceImpl OrderPayApplication insert success:{}", insertSuccess);
        if (CollUtil.isNotEmpty(payApplicationDTO.getReductionSourceId())){
            exemptionApplicationEntityMapper.update(new LambdaUpdateWrapper<ExemptionApplicationEntity>()
                    .set(ExemptionApplicationEntity::getIsUse, 1)
                    .in(ExemptionApplicationEntity::getId, payApplicationDTO.getReductionSourceId()));
        }
        //保存结清金额
        if(ObjUtil.equal(payApplicationDTO.getFeeType(), OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)){
        if(ObjUtil.equal(payApplicationDTO.getOrderSource(),1)){

         OrderInfoEntity orderInfo=   orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>().eq(OrderInfoEntity::getId, payApplicationDTO.getOrderId()).eq(OrderInfoEntity::getDeleteFlag, 0).orderByDesc(OrderInfoEntity::getCreateTime).last("limit 1"));
            if(ObjUtil.isNotEmpty(orderInfo)&&!ObjUtil.equal(orderInfo.getIsRepurchase(),1)){
                int usedDays;
                LocalDate beginDate = LocalDate.now();
                LocalDateTime now = LocalDateTime.now();
                FundRepaymentInfoEntity repaymentInfoEntity = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderInfo.getId())
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                        .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .orderByDesc(FundRepaymentInfoEntity::getTerm)
                        .last("limit 1"));
                //使用天数
                if (ObjectUtil.isNotEmpty(repaymentInfoEntity)) {
                    usedDays=(int) ChronoUnit.DAYS.between(repaymentInfoEntity.getRepaymentDate(), beginDate);
                } else {
                    usedDays=(int) ChronoUnit.DAYS.between(orderInfo.getPaymentTime(), now);
                }
         CaseInfoRepayCalcVO caseInfoRepayCalcVO= caseInfoService.earlyRepayCalc(payApplicationDTO.getOrderId());
            OrderSettleAmountRecordEntity entity = new OrderSettleAmountRecordEntity();
            entity.setOrderId(payApplicationDTO.getOrderId());
            entity.setOrderSource(1);
            entity.setOrderNumber(orderInfo.getOrderNumber());
            if(ObjUtil.isNotEmpty(caseInfoRepayCalcVO)){
                entity.setTrialSettlementAmount(caseInfoRepayCalcVO.getLoanSettlementAmount());
                entity.setActualSettlementAmount(caseInfoRepayCalcVO.getApplySettlementAmount());
                entity.setSettlementMethod(caseInfoRepayCalcVO.getLoanSettlementMethod());

            }
                entity.setUsageDays(usedDays);
            entity.setOutsourcingStatus(1);
            entity.setCommitType(2);
            entity.setOrderPayId(orderPayApplicationInfoEntity.getId());
            //todo 已还期数未添加
            orderSettleAmountRecordMapper.insert(entity);}

        }else if(ObjUtil.equal(payApplicationDTO.getOrderSource(),2)){
            DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderNo, payApplicationDTO.getOrderNumber()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0).eq(DigitalOutsourcingOrderEntity::getDataSource, 2).orderByDesc(DigitalOutsourcingOrderEntity::getCreateTime).last("limit 1"));
           if(ObjUtil.isNotEmpty(digitalOutsourcingOrderEntity)&&!ObjUtil.equal(digitalOutsourcingOrderEntity.getIsRepurchase(),1)){
            DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO=   new DigitalizeWeiwaiOrderStatusDTO();
            digitalizeWeiwaiOrderStatusDTO.setOrder_id(payApplicationDTO.getOrderNumber());
            Result<DigitalizeSettleList> digitalizeSettleList = digitalizeFeign.getDigitalizeSettleList(digitalizeWeiwaiOrderStatusDTO);
            log.info("DigitalizeServiceImpl.getDigitalizeSettleList result->{}", JSONUtil.toJsonStr(digitalizeSettleList));
            DigitalizeSettleList data = digitalizeSettleList.getData();
            DigitalizeRepaymentDetailVO weiwaiOrderPayApplyDetail = digitalizeFeign.getWeiwaiOrderApplyRepayment(digitalizeWeiwaiOrderStatusDTO);
            log.info("weiwaiOrderPayApply444444:{}", weiwaiOrderPayApplyDetail);
            Integer useDays=0;
            if(ObjUtil.isNotEmpty(weiwaiOrderPayApplyDetail)&&ObjUtil.isNotEmpty(weiwaiOrderPayApplyDetail.getDetails())){
                if(ObjUtil.isNotEmpty(weiwaiOrderPayApplyDetail.getDetails().getUsage_day())){
                    useDays=(Integer) weiwaiOrderPayApplyDetail.getDetails().getUsage_day();
                }

            }
            OrderSettleAmountRecordEntity entity = new OrderSettleAmountRecordEntity();
            entity.setOrderId(payApplicationDTO.getOrderId());
            entity.setOrderSource(2);
            entity.setOrderNumber(payApplicationDTO.getOrderNumber());
            if(ObjUtil.isNotEmpty(data)){
                entity.setTrialSettlementAmount(data.getSettleAmt());
                entity.setActualSettlementAmount(data.getSettleAmt());
            }
            entity.setOutsourcingStatus(1);
            entity.setUsageDays(useDays);
            entity.setCommitType(2);
            entity.setOrderPayId(orderPayApplicationInfoEntity.getId());
               //todo 已还期数未添加
               orderSettleAmountRecordMapper.insert(entity);}
        }else if(ObjUtil.equal(payApplicationDTO.getOrderSource(),3)){
            DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderNo, payApplicationDTO.getOrderNumber()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0).eq(DigitalOutsourcingOrderEntity::getDataSource, 3).orderByDesc(DigitalOutsourcingOrderEntity::getCreateTime).last("limit 1"));
           if(ObjUtil.isNotEmpty(digitalOutsourcingOrderEntity)&&!ObjUtil.equal(digitalOutsourcingOrderEntity.getIsRepurchase(),1)){
            try {
                ReductionRecordConditionalVO reductionRecordConditionalVO= outsourcingSettlementTrialCalculationsService.kingdeeEarlyRepayCalc(payApplicationDTO.getOrderNumber());

                if(ObjUtil.isNotEmpty(reductionRecordConditionalVO)){
                    OrderSettleAmountRecordEntity entity = new OrderSettleAmountRecordEntity();
                    entity.setOrderId(payApplicationDTO.getOrderId());
                    entity.setOrderSource(3);
                    entity.setOrderNumber(payApplicationDTO.getOrderNumber());
                    entity.setTrialSettlementAmount(reductionRecordConditionalVO.getLoanAmount());
                    entity.setActualSettlementAmount(reductionRecordConditionalVO.getLeaveAmt());
                    entity.setSettlementMethod(reductionRecordConditionalVO.getLoanSettlementMethod());

                    entity.setOutsourcingStatus(1);
                    entity.setUsageDays(ObjUtil.isNotEmpty(digitalOutsourcingOrderEntity)?digitalOutsourcingOrderEntity.getUsageDays():0);
                    entity.setCommitType(2);
                    entity.setOrderPayId(orderPayApplicationInfoEntity.getId());
                    entity.setInstalmentsRepaid(reductionRecordConditionalVO.getHaveTerm());
                    orderSettleAmountRecordMapper.insert(entity);

                }
            }catch (Exception e){
                throw new RuntimeException("获取金蝶结清金额数据失败");
            }


        }}
        }

        return insertSuccess ? orderPayApplicationInfoEntity : null;
    }

    /**
     * 验证车务费是否存在审批
     * 存在复制订单 相同车务费
     */
    private void verifyCarFreeApply(Integer orderId, PayApplicationPayeeTypeEnum payeeType) throws BusinessException {
        //查询车务费流水号
        Long carFeeCount = orderPayApplicationMapper.selectCount(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                .eq(OrderPayApplicationInfoEntity::getOrderId, orderId)
                .ne(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.FAIL)
                .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE)
                .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
        );
        if (carFeeCount > 0) {
            throw new BusinessException("存在车务费审批，请勿重复提交");
        }
        OrderFeeInfoEntity orderFeeInfo = orderFeeInfoMapper.selectOne(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                        .eq(OrderFeeInfoEntity::getOrderId, orderId)
                        .eq(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                        .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderFeeInfoEntity::getCreateTime)
                , false
        );
        if (ObjUtil.isNull(orderFeeInfo)) {
            return;
        }
        //查询车务费流水号
        Long paySnNumCount = orderPayApplicationMapper.selectJoinCount(new MPJLambdaWrapper<OrderPayApplicationInfoEntity>()
                .select(OrderPayApplicationInfoEntity::getId)
                .innerJoin(OrderFeeInfoEntity.class, OrderFeeInfoEntity::getOrderId, OrderPayApplicationInfoEntity::getOrderId)
                .ne(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.FAIL)
                .eq(OrderPayApplicationInfoEntity::getPayeeType, payeeType)
                .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE)
                .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                .eq(OrderFeeInfoEntity::getPaySnNumBack, orderFeeInfo.getPaySnNumBack())
        );
        if (paySnNumCount > 0) {
            throw new BusinessException("存在相同车务费支付流水申请，请勿重复提交");
        }

    }

    /**
     * 发起钉钉审批流程。
     *
     * @param payApplicationInfo 包含付款申请详细信息的实体对象
     * @param userId                     当前登录用户信息
     * @throws BusinessException 当当前人员未同步到钉钉或流程不存在时抛出
     */
    @Override
    public boolean initiateDingTalkApproval(List<String> resourceIds, OrderPayApplicationInfoEntity payApplicationInfo, Integer userId) {
        Assert.notNull(payApplicationInfo, () -> new BusinessException("订单不存在待审核的费用申请单"));
        log.info("OrderPayApplicationServiceImpl OrderPayApplication found payApplicationInfo:{}", payApplicationInfo);

        OrderInfoEntity orderInfo = orderInfoMapper.selectById(payApplicationInfo.getOrderId());
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));
        log.info("OrderPayApplicationServiceImpl OrderPayApplication found orderInfo:{}", orderInfo);

        String processCode = dingTaskFeign.getWorkFlowSchemaId("退费申请").getData();
        Assert.notBlank(processCode, () -> new BusinessException("流程不存在"));
        log.info("OrderPayApplicationServiceImpl OrderPayApplication processCode:{}", processCode);

        // 获取当前人员钉钉userId
        List<UserSyncInfoListVO> currentUserSyncInfoListVO = userFeign.queryUserSyncInfoList(new SearchUserSyncInfoDTO().setUserIdList(List.of(userId)).setOrigin(1)).getData();
        Assert.notEmpty(currentUserSyncInfoListVO, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        UserSyncInfoListVO currentUserSyncInfo = currentUserSyncInfoListVO.get(0);
        String userNumber = currentUserSyncInfo.getUserNumber();
        Assert.notBlank(userNumber, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        log.info("OrderPayApplicationServiceImpl OrderPayApplication current userNumber:{}", userNumber);

        //获取钉钉部门id
        UserDetailInfoVO currentUserDetailInfoVO = userFeign.searchUserDetailById(userId).getData();
        List<Integer> currentUserDeptIds = currentUserDetailInfoVO.getDeptIds();
        Assert.notEmpty(currentUserDeptIds, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        List<DeptSyncInfoVO> currentUserDeptSyncList = userFeign.getSyncDeptByLhDeptIds(new SearchDeptSyncInfoDTO().setLhDeptIdList(currentUserDeptIds).setOrigin(1)).getData();
        Assert.notEmpty(currentUserDeptSyncList, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        log.info("OrderPayApplicationServiceImpl OrderPayApplication current deptSyncList:{}", currentUserDeptSyncList);

        List<DingCreateProcessCmdDTO.FormComponentValue> formComponentValueList = new ArrayList<>(3);
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("收款主体")
                .setValue(payApplicationInfo.getPayeeType() == PayApplicationPayeeTypeEnum.CUSTOMER ? "客户" : "非客户"));

        // 拼接申请原因大文本
        String reason = "【订单编号】: " + ObjUtil.defaultIfNull(orderInfo.getOrderNumber(), "") + "\n" +
                "【收款主体类型】: " + ObjUtil.defaultIfNull(payApplicationInfo.getPayeeType().getDesc(), "") + "\n" +
                "【费用类型】: " + ObjUtil.defaultIfNull(payApplicationInfo.getFeeType().getDescription(), "") + "\n" +
                "【收款明细】: " + ObjUtil.defaultIfNull(payApplicationInfo.getPaymentDetails(), "") + "\n" +
                "【付款方账户】: " + ObjUtil.defaultIfNull(payApplicationInfo.getPayAccount(), "") + "\n" +
                "【付款方银行】: " + ObjUtil.defaultIfNull(payApplicationInfo.getPayAccountName(), "") + "\n" +
                "【付款方账号】: " + ObjUtil.defaultIfNull(payApplicationInfo.getPayAccountNumber(), "") + "\n" +
                "【收款方账户】: " + ObjUtil.defaultIfNull(payApplicationInfo.getPayeeAccount(), "") + "\n" +
                "【收款方银行】: " + ObjUtil.defaultIfNull(payApplicationInfo.getPayeeAccountName(), "") + "\n" +
                "【收款方账号】: " + ObjUtil.defaultIfNull(payApplicationInfo.getPayeeAccountNumber(), "") + "\n" +
                "【收款方银行网点】: " + ObjUtil.defaultIfNull(payApplicationInfo.getPayeeAccountBranchName(), "") + "\n" +
                "【收款方手机号】: " + ObjUtil.defaultIfNull(payApplicationInfo.getPayeePhone(), "") + "\n" +
                "【收款方身份证号】: " + ObjUtil.defaultIfNull(payApplicationInfo.getPayeeCardNumber(), "") + "\n" +
                "【收款合计金额】: " + ObjUtil.defaultIfNull(payApplicationInfo.getPayeeAmount(), BigDecimal.ZERO) + "\n" +
                "【备注】: " + ObjUtil.defaultIfNull(payApplicationInfo.getRemark(), "") + "\n";


        DingCreateProcessCmdDTO processCmdDTO = new DingCreateProcessCmdDTO();

        List<DingCreateProcessCmdDTO.TargetSelectActioner> targetSelectActionerList = new ArrayList<>();

        //获取大区品质主管用户id
        //获取部门用户id
        List<Integer> userIdsByDeptId = userFeign.getDeptUsers(List.of(orderInfo.getDeptId())).getData();
        Assert.notEmpty(userIdsByDeptId, () -> new BusinessException("未查询到审批人信息，无法发起审批"));
        //获取部门下门店经理
        List<Integer> userIdListByDeptAndRole = userFeign.getUserIdByRoleIds(new SearchUserIdByRoleIdsDTO().setRoleId(RoleEnum.STORE_MANAGER.getId()).setUserIdList(userIdsByDeptId)).getData();
        Assert.notEmpty(userIdListByDeptAndRole, () -> new BusinessException("未查询到审批人信息，无法发起审批"));
        //获取门店经理用户钉钉userId
        List<UserSyncInfoListVO> storeManagerUserList = userFeign.queryUserSyncInfoList(new SearchUserSyncInfoDTO().setUserIdList(List.of(userIdListByDeptAndRole.get(0))).setOrigin(1)).getData();
        Assert.notEmpty(storeManagerUserList, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        UserSyncInfoListVO storeManagerUserSyncInfo = storeManagerUserList.get(0);
        String storeManagerUserNumber = storeManagerUserSyncInfo.getUserNumber();
        Assert.notBlank(storeManagerUserNumber, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        log.info("OrderPayApplicationServiceImpl OrderPayApplication storeManagerUserNumber:{}", storeManagerUserNumber);

        if (!envUtil.isPrd()){
            reason += "【门店经理审批人】: " + ObjUtil.defaultIfNull(storeManagerUserSyncInfo.getName(), "")
                    +" "
                    + ObjUtil.defaultIfNull(storeManagerUserSyncInfo.getUserNumber(), "")
                    + "\n";
        }

        //判断是否为客户
        if (Objects.equals(payApplicationInfo.getPayeeType(), PayApplicationPayeeTypeEnum.CUSTOMER)) {
            if (envUtil.isPrd()) {
                //获取大区下一级 roleId = 86 的 品质主管
                targetSelectActionerList.add(new DingCreateProcessCmdDTO.TargetSelectActioner()
                        .setActionerKey(switchUtils.getStrValue(ORDER_PAY_CUSTOMER_STORE_MANAGER_ACTOR_KEY))
                        .setActionerUserIds(List.of(storeManagerUserNumber)));
            } else {
                //测试挡板
                targetSelectActionerList.add(new DingCreateProcessCmdDTO.TargetSelectActioner()
                        .setActionerKey(switchUtils.getStrValue(ORDER_PAY_CUSTOMER_STORE_MANAGER_ACTOR_KEY))
                        .setActionerUserIds(List.of("1723427808944208")));
            }


        } else {
            //获取门店经理用户id
            //orderInfo.dept_id下的roleId = 29 的是门店经理
            if (envUtil.isPrd()) {
                //获取大区下一级 roleId = 86 的 品质主管
                targetSelectActionerList.add(new DingCreateProcessCmdDTO.TargetSelectActioner()
                        .setActionerKey(switchUtils.getStrValue(ORDER_PAY_NO_CUSTOMER_STORE_MANAGER_ACTOR_KEY))
                        .setActionerUserIds(List.of(storeManagerUserNumber)));
            } else {
                //测试挡板
                targetSelectActionerList.add(new DingCreateProcessCmdDTO.TargetSelectActioner()
                        .setActionerKey(switchUtils.getStrValue(ORDER_PAY_NO_CUSTOMER_STORE_MANAGER_ACTOR_KEY))
                        .setActionerUserIds(List.of("1723427808944208")));
            }

            //获取大区品质主管用户id
            //获取大区id
            Integer regionId = orderInfo.getRegionId();

            List<UserDingDeptMappingVO> userDingDeptMapping = userFeign.getUserDingDeptMapping(
                    new UserDingDeptMappingDTO()
                            .setDingApproveNode(DingTaskMappingNodeEnum.FEE_RETURN_AREA_QUALITY_MANAGER.getCode())
                            .setRegionId(regionId)
                            .setUserType(1)
            );
            Assert.notEmpty(userDingDeptMapping, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
            UserDingDeptMappingVO userDingDeptMappingVO = userDingDeptMapping.get(0);
            String qualityManagerUserNumber=  userDingDeptMappingVO.getUserNumber();

           /* List<Integer> userIdsByRegionId = userFeign.getDeptUsers(List.of(regionId)).getData();
            Assert.notEmpty(userIdsByRegionId, () -> new BusinessException("未查询到审批人信息，无法发起审批"));

            //获取部门下品质主管
            List<Integer> userIdByStoreIdAndRoleId = userFeign.getUserIdByRoleIds(new SearchUserIdByRoleIdsDTO().setRoleId(RoleEnum.QUALITY_MANAGER.getId()).setUserIdList(userIdsByRegionId)).getData();
            Assert.notEmpty(userIdByStoreIdAndRoleId, () -> new BusinessException("未查询到审批人信息，无法发起审批"));

            //获取品质主管用户钉钉userId
            List<UserSyncInfoListVO> qualityManagerUserList = userFeign.queryUserSyncInfoList(new SearchUserSyncInfoDTO().setUserIdList(List.of(userIdByStoreIdAndRoleId.get(0))).setOrigin(1)).getData();
            Assert.notEmpty(qualityManagerUserList, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
            UserSyncInfoListVO qualityManagerUserListSyncInfo = qualityManagerUserList.get(0);
            String qualityManagerUserNumber = qualityManagerUserListSyncInfo.getUserNumber();*/
            Assert.notBlank(qualityManagerUserNumber, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
            log.info("OrderPayApplicationServiceImpl OrderPayApplication qualityManagerUserNumber:{}", qualityManagerUserNumber);

            if (!envUtil.isPrd()){
                reason += "【大区品质主管审批人】: " + ObjUtil.defaultIfNull(userDingDeptMappingVO.getUserName(), "")
                        +" "
                        + ObjUtil.defaultIfNull(userDingDeptMappingVO.getUserNumber(), "")
                        + "\n";
            }

            if (envUtil.isPrd()) {
                //获取大区下一级 roleId = 86 的 品质主管
                targetSelectActionerList.add(new DingCreateProcessCmdDTO.TargetSelectActioner()
                        .setActionerKey(switchUtils.getStrValue(ORDER_PAY_NO_CUSTOMER_REGION_QUALITY_MANAGER_ACTOR_KEY))
                        .setActionerUserIds(List.of(qualityManagerUserNumber)));
            } else {
                //测试挡板
                targetSelectActionerList.add(new DingCreateProcessCmdDTO.TargetSelectActioner()
                        .setActionerKey(switchUtils.getStrValue(ORDER_PAY_NO_CUSTOMER_REGION_QUALITY_MANAGER_ACTOR_KEY))
                        .setActionerUserIds(List.of("172342806212261")));
            }
        }

        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("申请原因")
                .setValue(reason));

        List<String> temporaryAccessRouteReqList = null;
        if (CollUtil.isNotEmpty(resourceIds)) {
            temporaryAccessRouteReqList = resourceIds.stream()
                    .map(i -> resourceFeign.temporaryAccessRouteRequest(i, 24).getData())
                    .collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(temporaryAccessRouteReqList)) {
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("附件")
                    .setValue(JSONUtil.toJsonStr(temporaryAccessRouteReqList))
            );
        }

        if (envUtil.isPrd()) {
            processCmdDTO.setProcessCode(processCode)
                    .setOriginatorUserId(userNumber)
                    .setDeptId(Convert.toLong(currentUserDeptSyncList.get(0).getDeptId()))
                    .setFormComponentValues(formComponentValueList)
                    .setTargetSelectActioners(targetSelectActionerList)
            ;
        }else{

            String[] originatorUserIds = {"17231840823381403", "17249814214855316", "17242903989855632"};

            Random random = new Random();

            String randomOriginatorUserId = originatorUserIds[random.nextInt(originatorUserIds.length)];

            processCmdDTO.setProcessCode(processCode)
                    .setOriginatorUserId(randomOriginatorUserId)
                    .setDeptId(926741152L)
                    .setFormComponentValues(formComponentValueList)
                    .setTargetSelectActioners(targetSelectActionerList)
            ;
        }

        Result<String> workflowProcessInstances = null;
        try {
            if(envUtil.isPrd()){
                workflowProcessInstances = dingTaskFeign.createWorkflowProcessInstances(processCmdDTO);
            }else{
                if (Objects.equals(payApplicationInfo.getPayeeType(), PayApplicationPayeeTypeEnum.CUSTOMER)) {
                    workflowProcessInstances = Result.success("qDJfbLyzSPuS77HJlB9PBg07861733911810");
                } else {
                    workflowProcessInstances = Result.success("U-XhRSu-QgOabcYzuHaroQ07861733829144");
                }
            }
        } catch (Exception e) {
            log.error("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances error:{}", e.getMessage());
            throw new BusinessException("发起钉钉审批实例失败");
        }
        if (Result.isSuccess(workflowProcessInstances) && StrUtil.isNotBlank(workflowProcessInstances.getData())) {
            log.info("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances:{}", workflowProcessInstances);
            orderPayApplicationMapper.update(new LambdaUpdateWrapper<OrderPayApplicationInfoEntity>()
                    .eq(OrderPayApplicationInfoEntity::getId, payApplicationInfo.getId())
                    .set(OrderPayApplicationInfoEntity::getProcessId, workflowProcessInstances.getData())
            );
            return true;
        } else {
            log.info("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances fail:{}", workflowProcessInstances);
            throw new BusinessException("发起钉钉审批实例失败");
        }
    }
    /**
     * 转对公验证
     */
    private void verifyOffline(Integer orderId, Integer term, OrderFeeDetailExpandTypeEnum feeType) {
        if (!offlineFeeEnumList.contains(feeType)) {
            return;
        }
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, ()-> new BusinessException("订单不存在"));

        if (!ObjUtil.equals(feeType, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)
                && !Integer.valueOf(1).equals(term)) {
            FundRepayStatusEnum repaymentStatus = null;
            if (ObjUtil.equals(orderInfo.getIsRepurchase(),1)) {
                RepurchaseRepaymentInfoEntity beforeRepaymentInfo = repurchaseRepaymentInfoMapper.selectOne(
                        new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                                .eq(RepurchaseRepaymentInfoEntity::getTerm, term - 1)
                                .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                                .eq(RepurchaseRepaymentInfoEntity::getFundId, orderInfo.getFundId())
                                .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(RepurchaseRepaymentInfoEntity::getCreateTime),
                        false
                );
                repaymentStatus = beforeRepaymentInfo.getRepaymentStatus();
            } else {
                FundRepaymentInfoEntity beforeRepaymentInfo = fundRepaymentInfoMapper.selectOne(
                        new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                .eq(FundRepaymentInfoEntity::getTerm, term - 1)
                                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                                .eq(FundRepaymentInfoEntity::getFundId, orderInfo.getFundId())
                                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(FundRepaymentInfoEntity::getCreateTime),
                        false
                );
                repaymentStatus = beforeRepaymentInfo.getRepaymentStatus();
            }
            if (!ObjUtil.equals(repaymentStatus, FundRepayStatusEnum.SETTLED)) {
                throw new BusinessException("请先还清之前的还款");
            }
        }

        if (ObjUtil.equals(feeType, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)) {
            long count = 0;
            if (ObjUtil.equals(orderInfo.getIsRepurchase(),1)) {
                count = repurchaseRepaymentInfoMapper.selectCount(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                        .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                        .eq(RepurchaseRepaymentInfoEntity::getFundId, orderInfo.getFundId())
                        .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                        .eq(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.OVERDUE));

            } else {
                count = fundRepaymentInfoMapper.selectCount(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                        .eq(FundRepaymentInfoEntity::getFundId, orderInfo.getFundId())
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                        .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.OVERDUE));

            }
            if (count > 0) {
                throw new BusinessException("请先还清所有逾期账单");
            }

        }
    }
    /**
     *  赎回转对公还款
     * @param orderPayApplicationInfo  订单还款信息
     * @param cashierApproveTime      出纳审核通过时间
     */
    public FundRepaymentDeductEntity repurchaseRepayment(OrderPayApplicationInfoEntity orderPayApplicationInfo, LocalDateTime cashierApproveTime){
        log.info("OrderPayApplicationServiceImpl.repurchaseRepayment orderPayApplicationInfo:{}", orderPayApplicationInfo);
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderPayApplicationInfo.getOrderId());
        Assert.notNull(orderInfo, "订单信息不存在");
        if (!ObjUtil.equals(orderInfo.getIsRepurchase(), 1)) {
            return null;
        }
        LocalDate repayDate = cashierApproveTime.toLocalDate();
        Integer orderId = orderPayApplicationInfo.getOrderId();
        log.info("OrderPayApplicationServiceImpl.repurchaseRepayment orderId:{}", orderId);
        Integer repaymentTerm = orderPayApplicationInfo.getRepaymentTerm();

        boolean isSettle = ObjUtil.equals(orderPayApplicationInfo.getFeeType(), OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT);

        log.info("OrderPayApplicationServiceImpl.repurchaseRepayment isSettle:{}", isSettle);

        RepurchaseRepaymentInfoEntity repurchaseRepaymentInfo = null;
        if (isSettle){
            //获取最近待还一期
            repurchaseRepaymentInfo = repurchaseRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                    .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderPayApplicationInfo.getOrderId())
                    .eq(RepurchaseRepaymentInfoEntity::getFundId, orderInfo.getFundId())
                    .ne(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                    .orderByAsc(RepurchaseRepaymentInfoEntity::getTerm)
                    .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                    .last("limit 1")
            );
        } else {
            //更新还款计划实还金额
            repurchaseRepaymentInfo = repurchaseRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                    .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderPayApplicationInfo.getOrderId())
                    .eq(RepurchaseRepaymentInfoEntity::getTerm, orderPayApplicationInfo.getRepaymentTerm())
                    .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
            );
        }
        log.info("OrderPayApplicationServiceImpl.repurchaseRepayment fundRepaymentInfo:{}", repurchaseRepaymentInfo);

        Assert.notNull(repurchaseRepaymentInfo, "还款计划不存在");

        //实还
        BigDecimal actuallyPenaltyInterest = ObjUtil.defaultIfNull(repurchaseRepaymentInfo.getActuallyPenaltyInterest(), BigDecimal.ZERO);
        BigDecimal actuallyInterest = ObjUtil.defaultIfNull(repurchaseRepaymentInfo.getActuallyInterest(), BigDecimal.ZERO);
        BigDecimal actuallyPrincipal = ObjUtil.defaultIfNull(repurchaseRepaymentInfo.getActuallyPrincipal(), BigDecimal.ZERO);
        BigDecimal actuallyPenalty = ObjUtil.defaultIfNull(repurchaseRepaymentInfo.getActuallyPenalty(), BigDecimal.ZERO);
        BigDecimal actuallyLatePenaltyAmount = ObjUtil.defaultIfNull(repurchaseRepaymentInfo.getActuallyLatePenaltyAmount(), BigDecimal.ZERO);

        // 原始应还
        BigDecimal originalRepaymentPenaltyInterest = ObjUtil.defaultIfNull(repurchaseRepaymentInfo.getRepaymentPenaltyInterest(), BigDecimal.ZERO);
        BigDecimal originalRepaymentInterest = ObjUtil.defaultIfNull(repurchaseRepaymentInfo.getRepaymentInterest(), BigDecimal.ZERO);
        BigDecimal originalRepaymentPrincipal = ObjUtil.defaultIfNull(repurchaseRepaymentInfo.getRepaymentPrincipal(), BigDecimal.ZERO);
        BigDecimal originalRepaymentPenalty = ObjUtil.defaultIfNull(repurchaseRepaymentInfo.getRepaymentPenalty(), BigDecimal.ZERO);
        BigDecimal originalRepaymentLatePenaltyAmount = ObjUtil.defaultIfNull(repurchaseRepaymentInfo.getRepaymentLatePenaltyAmount(), BigDecimal.ZERO);

        // 剩余应还罚息
        BigDecimal remainingRepaymentPenaltyInterest = originalRepaymentPenaltyInterest.subtract(actuallyPenaltyInterest);
        // 剩余应还利息
        BigDecimal remainingRepaymentInterest = originalRepaymentInterest.subtract(actuallyInterest);
        // 剩余应还本金
        BigDecimal remainingRepaymentPrincipal = originalRepaymentPrincipal.subtract(actuallyPrincipal);
        // 剩余应还违约金
        BigDecimal remainingRepaymentPenalty = originalRepaymentPenalty.subtract(actuallyPenalty);
        // 剩余应还滞纳金
        BigDecimal remainingRepaymentLatePenaltyAmount = originalRepaymentLatePenaltyAmount.subtract(actuallyLatePenaltyAmount);

        //先还罚息 后还利息 最后还本金
        BigDecimal payeeAmount = orderPayApplicationInfo.getPayeeAmount();

        //计算实还总金额 = 实还罚息 + 实还利息 + 实还本金 + 实还违约金
        BigDecimal actuallyAmountTotal = actuallyPenaltyInterest.add(actuallyInterest).add(actuallyPrincipal).add(actuallyPenalty);
        //结清金额
        BigDecimal settlementAmount = BigDecimal.ZERO;
        //逾期金额(罚息)
        BigDecimal latePenaltyAmount = BigDecimal.ZERO;
        //减免金额
        BigDecimal reductionAmount = BigDecimal.ZERO;
        //应收违约金
        BigDecimal penaltyAmount = repurchaseRepaymentInfo.getRepaymentPenalty();
        if (isSettle) {
            BigDecimal repayPrincipal = orderPayApplicationInfo.getRepayPrincipal();
            //如果还款本金为空 计算所有期数剩余本金
            if (ObjUtil.isNull(repayPrincipal)) {
                List<RepurchaseRepaymentInfoEntity> repaymentInfoPrincipalList = repurchaseRepaymentInfoMapper.selectList(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                        .select(RepurchaseRepaymentInfoEntity::getRepaymentPrincipal, RepurchaseRepaymentInfoEntity::getActuallyPrincipal)
                        .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                        .eq(RepurchaseRepaymentInfoEntity::getFundId, orderInfo.getFundId())
                        .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                );
                // 计算剩余本金 应还本金- 实还本金
                repayPrincipal = repaymentInfoPrincipalList.stream()
                        .map(RepurchaseRepaymentInfoEntity::getRepaymentPrincipal)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .subtract(repaymentInfoPrincipalList.stream()
                                .map(RepurchaseRepaymentInfoEntity::getActuallyPrincipal)
                                .reduce(BigDecimal.ZERO, BigDecimal::add));

            }
            //结清信息
            //还款本金 = 还款本金 + 本次还款本金
            actuallyPrincipal = actuallyPrincipal.add(repayPrincipal);
            settlementAmount = orderPayApplicationInfo.getSettlementAmount();
            latePenaltyAmount = orderPayApplicationInfo.getLatePenaltyAmount();
            reductionAmount = orderPayApplicationInfo.getReductionAmount();

            //设置实还信息
            actuallyAmountTotal = actuallyAmountTotal.add(orderPayApplicationInfo.getPayeeAmount());
            actuallyPenalty = actuallyPenalty.add(orderPayApplicationInfo.getPenaltyAmount());
            actuallyInterest = actuallyInterest.add(orderPayApplicationInfo.getSettlementAmount().subtract(repayPrincipal));
            actuallyPenaltyInterest = actuallyPenaltyInterest.add(orderPayApplicationInfo.getLatePenaltyAmount());
        } else {
            if (payeeAmount.compareTo(BigDecimal.ZERO) > 0 && remainingRepaymentPenaltyInterest.compareTo(BigDecimal.ZERO) > 0) {
                //罚息
                BigDecimal repayAmt = payeeAmount.min(remainingRepaymentPenaltyInterest);
                actuallyPenaltyInterest = actuallyPenaltyInterest.add(repayAmt);
                payeeAmount = payeeAmount.subtract(repayAmt);
            }
            if (payeeAmount.compareTo(BigDecimal.ZERO) > 0 && remainingRepaymentLatePenaltyAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 滞纳金
                BigDecimal repayAmt = payeeAmount.min(remainingRepaymentLatePenaltyAmount);
                actuallyLatePenaltyAmount = actuallyLatePenaltyAmount.add(repayAmt);
                payeeAmount = payeeAmount.subtract(repayAmt);
            }
            if (payeeAmount.compareTo(BigDecimal.ZERO) > 0 && remainingRepaymentPenalty.compareTo(BigDecimal.ZERO) > 0) {
                //违约金
                BigDecimal repayAmt = payeeAmount.min(remainingRepaymentPenalty);
                actuallyPenalty = actuallyPenalty.add(repayAmt);
                payeeAmount = payeeAmount.subtract(repayAmt);
            }
            if (payeeAmount.compareTo(BigDecimal.ZERO) > 0 && remainingRepaymentInterest.compareTo(BigDecimal.ZERO) > 0) {
                //利息
                BigDecimal repayAmt = payeeAmount.min(remainingRepaymentInterest);
                actuallyInterest = actuallyInterest.add(repayAmt);
                payeeAmount = payeeAmount.subtract(repayAmt);
            }
            if (payeeAmount.compareTo(BigDecimal.ZERO) > 0 && remainingRepaymentPrincipal.compareTo(BigDecimal.ZERO) > 0) {
                //本金
                BigDecimal repayAmt = payeeAmount.min(remainingRepaymentPrincipal);
                actuallyPrincipal = actuallyPrincipal.add(repayAmt);
                payeeAmount = payeeAmount.subtract(repayAmt);
            }
            log.info("OrderPayApplicationServiceImpl.repurchaseRepayment orderId:{} payeeAmount:{} remainingPayeeAmount:{}", orderPayApplicationInfo.getOrderId(), orderPayApplicationInfo.getPayeeAmount(), payeeAmount);
            //计算实还总金额
            actuallyAmountTotal = actuallyPenaltyInterest.add(actuallyInterest).add(actuallyPrincipal).add(actuallyPenalty).add(actuallyLatePenaltyAmount);
        }
        //判断还款状态
        FundRepayStatusEnum repaymentStatus;
        if (actuallyAmountTotal.compareTo(repurchaseRepaymentInfo.getRepaymentAmountTotal()) >= 0) {
            repaymentStatus = FundRepayStatusEnum.SETTLED;
        }else if (actuallyAmountTotal.compareTo(BigDecimal.ZERO) > 0) {
            repaymentStatus = FundRepayStatusEnum.PART_RETURN;
        }else {
            repaymentStatus = FundRepayStatusEnum.NONE;
        }

        //是否逾期
        int isOverdue = 0;
        LocalDate repaymentDate = repurchaseRepaymentInfo.getRepaymentDate();
        if (repaymentDate.isBefore(repayDate)) {
            //还款日期已过，更新还款状态
            if (!ObjUtil.equal(repaymentStatus, FundRepayStatusEnum.SETTLED)) {
                repaymentStatus = FundRepayStatusEnum.OVERDUE;
            }
            isOverdue = 1;
        }

        RepurchaseRepaymentInfoEntity repurchaseRepaymentInfoUpdate = new RepurchaseRepaymentInfoEntity();
        if (isSettle) {
            repurchaseRepaymentInfoUpdate.setSettlementAmount(settlementAmount)
                    .setLatePenaltyAmount(latePenaltyAmount)
                    .setReductionAmount(reductionAmount)
                    .setSettlementAmount(settlementAmount)

                    .setActuallyPrincipal(actuallyPrincipal)
                    .setActuallyInterest(actuallyInterest)
                    .setActuallyPenaltyInterest(actuallyPenaltyInterest)
                    .setActuallyAmountTotal(actuallyAmountTotal)
            ;
        } else {
            repurchaseRepaymentInfoUpdate
                    .setActuallyPrincipal(actuallyPrincipal)
                    .setActuallyLatePenaltyAmount(actuallyLatePenaltyAmount)
                    .setActuallyInterest(actuallyInterest)
                    .setActuallyPenaltyInterest(actuallyPenaltyInterest)
                    .setActuallyAmountTotal(actuallyAmountTotal)
                    .setIsOverdue(isOverdue)
                    .setRepaymentStatus(repaymentStatus)
                    .setActuallyDate(repayDate)
            ;
        }
        FundEarlyRepaymentCalcDTO fundEarlyRepaymentCalcDTO = fundDeductService.earlyRepayCalc(new FundRepayCalcEarlyDTO().setOrderId(orderPayApplicationInfo.getOrderId()).setLoanSettlementMethod(orderPayApplicationInfo.getLoanSettlementMethod()));

        //更新还款计划
        repurchaseRepaymentInfoMapper.update(repurchaseRepaymentInfoUpdate, new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                .eq(RepurchaseRepaymentInfoEntity::getId, repurchaseRepaymentInfo.getId())
        );
        log.info("OrderPayApplicationServiceImpl.repurchaseRepayment update fundRepaymentInfo repaymentId:{}", repurchaseRepaymentInfo.getId());

        //更新订单状态
        //更新订单还款状态
        try {
            OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
            orderApproveFundPlanStatusDTO.setOrderId(orderId);
            orderApproveFundPlanStatusDTO.setIsOutsourcing(Objects.equals(orderPayApplicationInfo.getOrderApplicationSource(), 1) ? 1 : 2);
            orderApproveFundPlanStatusDTO.setTerm(repaymentTerm);
            orderApproveFundPlanStatusDTO.setPayeeAmount(payeeAmount);
            orderApproveFundPlanStatusDTO.setCreateTime(orderPayApplicationInfo.getCreateTime());
            repurchaseProcessService.updateOrderPlanStatus(orderApproveFundPlanStatusDTO);
        } catch (Exception e) {
            log.error("OrderPayApplicationServiceImpl.repurchaseRepayment updateFundPlanStatus orderId:{}", orderId);
        }

        try {
            repurchaseProcessService.updateRepurchaseOrderStatus(orderId);
        } catch (Exception e) {
            log.error("OrderPayApplicationServiceImpl.repurchaseRepayment updateOrderFundRepayment orderId:{}", orderId);
        }
        //保存还款记录
        Long index = fundRepaymentDeductMapper.selectCount(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                .eq(FundRepaymentDeductEntity::getTerm, repaymentTerm));
        String deductReqNo = orderInfo.getOrderNumber() + "_" + repaymentTerm + "_" + index;
        Map<String, Integer> requestMap = new HashMap<>(1);
        requestMap.put("applyInfoId", orderPayApplicationInfo.getId());
        String reqJsonStr = JSONUtil.toJsonStr(requestMap);

        FundRepaymentDeductEntity fundRepaymentDeductEntity = new FundRepaymentDeductEntity()
                .setOrderId(orderId)
                .setDeductReqNo(deductReqNo)
                .setIndex(Math.toIntExact(index))
                .setRepayDate(ObjUtil.defaultIfNull(cashierApproveTime, LocalDateTime.now()).toLocalDate())
                .setFundId(orderInfo.getFundId())
                .setRespContent(null)
                .setReqContent(reqJsonStr)
                .setRepayType(
                        ObjUtil.equal(orderPayApplicationInfo.getFeeType(), OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)
                                ? FundDeductRepayTypeEnums.EARLY_SETTLEMENT
                                : ObjUtil.equal(isOverdue, 1)
                                ? FundDeductRepayTypeEnums.OVERDUE_REPAYMENT
                                : FundDeductRepayTypeEnums.NORMAL_REPAYMENT
                )
                .setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                .setTerm(repaymentTerm)
                .setBizType(FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION)
                .setFailReason(null)
                .setDeductAmount(orderPayApplicationInfo.getPayeeAmount());
        if (Objects.equals(orderInfo.getIsRepurchase(),1)){
            fundRepaymentDeductEntity.setIsRepurchase(1);
        }
        fundRepaymentDeductMapper.insert(fundRepaymentDeductEntity);
        log.info("OrderPayApplicationServiceImpl.repurchaseRepayment save fundRepaymentDeductEntity deductReqNo:{}", deductReqNo);

        try {
            String message = null;
            String name = orderInfo.getCustomerName();
            String date = LocalDate.now().format(DateTimeFormatter.ofPattern(DatePattern.CHINESE_DATE_PATTERN));
            if (isSettle) {
                message = String.format(OrderSendMessageEnums.getEnumByStatus(5000, 2).getMessage(), name, date, fundRepaymentDeductEntity.getDeductAmount());
            } else {
                message = String.format(OrderSendMessageEnums.getEnumByStatus(5000, 1).getMessage(), name, date, fundRepaymentDeductEntity.getDeductAmount(), fundRepaymentDeductEntity.getTerm());
            }
            UserInfoVO userInfo = userFeign.searchUserName(orderInfo.getManagerId()).getData();
            MessageContent messageContent = new MessageContent()
                    .setMsgType(MsgConstants.MSG_TEXT)
                    .setSendType(MsgConstants.SEND_DD_NOTICE)
                    .setContent(message)
                    .setReceiver(userInfo.getMobile());
            messageFeign.sendMessage(messageContent);
            log.info("OrderPayApplicationServiceImpl.repurchaseRepayment sendMessageToDingDing.end deductReqNo:{} message:{}", deductReqNo, message);
        } catch (Exception e) {
            log.error("OrderPayApplicationServiceImpl.repurchaseRepayment sendMessageToDingDing deductReqNo:{} error:{}", deductReqNo, e.getMessage(), e);
        }

        // todo 推送众信
        try {
            zhongXinService.pushToZhongXinAsRetrieveAccept(new ReceivePaymentDTO()
                    .setOrderId(orderId)
                    .setTerm(repaymentTerm)
                    .setAmount(orderPayApplicationInfo.getPayeeAmount())
                    .setTradingTime(LocalDateTime.now())
                    .setPayer(orderInfo.getCustomerName())
                    .setPayee(FundEnum.getFundEnum(orderInfo.getFundId()).getFundName())
                    .setTradingMethods(OrderFeeDetailTradingMethodsEnum.CORPORATE_TRANSFERS)
                    .setSettling(ObjUtil.equal(orderPayApplicationInfo.getFeeType(), OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)?1:0));

        } catch (Exception e) {
            log.error("OrderPayApplicationServiceImpl.repurchaseRepayment sendMessageToZhongXin orderId:{}", orderId);
        }
        if (isSettle||(ObjUtil.equals(repurchaseRepaymentInfoUpdate.getRepaymentStatus(),  FundRepayStatusEnum.SETTLED)&&  ObjUtil.equal(orderInfo.getTerm(), repurchaseRepaymentInfo.getTerm()))){
            orderSettleAmountRecordMapper.update(new LambdaUpdateWrapper<OrderSettleAmountRecordEntity>()
                    .set(OrderSettleAmountRecordEntity::getDeleteFlag, 1)
                    .eq(OrderSettleAmountRecordEntity::getOrderId, orderId)
                    .eq(OrderSettleAmountRecordEntity::getDeleteFlag, 0));
            OrderSettleAmountRecordEntity entity = new OrderSettleAmountRecordEntity()
                    .setOrderId(orderId)
                    .setOrderSource(1)
                    .setOrderNumber(orderInfo.getOrderNumber())
                    .setTrialSettlementAmount(settlementAmount)
                    .setApplySettlementAmount(actuallyAmountTotal)
                    .setActualSettlementAmount(actuallyAmountTotal)
                    .setSettlementMethod(orderPayApplicationInfo.getLoanSettlementMethod().getDesc())
                    .setOutsourcingStatus(1)
                    .setRemainingPrincipal(actuallyPrincipal)
                    .setInstalmentsRepaid(repaymentTerm-1);

            if (ObjUtil.equals(orderPayApplicationInfo.getLoanSettlementMethod(), LoanSettlementEnum.CLOSED_PERIOD_PENALTY_INSIDE)){
                entity.setSettlementMethod(entity.getSettlementMethod()+fundEarlyRepaymentCalcDTO.getSettlePenaltyRate().multiply(new BigDecimal("100"))+ "%");
            }
            if (ObjUtil.equals(repurchaseRepaymentInfoUpdate.getRepaymentStatus(),  FundRepayStatusEnum.SETTLED)&&  ObjUtil.equal(orderInfo.getTerm(), repurchaseRepaymentInfo.getTerm())){
                entity.setSettlementMethod("委外正常结清");
            }
            orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                            .eq(OrderPayApplicationInfoEntity::getOrderId, orderId)
                            .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                            .eq(OrderPayApplicationInfoEntity::getApplyType,1)
                            .in(OrderPayApplicationInfoEntity::getFeeType, List.of(OrderFeeDetailExpandTypeEnum.OTHER,
                                    OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE, OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT,
                                    OrderFeeDetailExpandTypeEnum.DISPOSABLE_SECURITY_DEPOSIT, OrderFeeDetailExpandTypeEnum.INSTALLMENT_SERVICE_FEE,
                                    OrderFeeDetailExpandTypeEnum.GPS_DATA_TRANSFER_FEE, OrderFeeDetailExpandTypeEnum.GPS_EQUIPMENT_COMPENSATION_FEE))
                            .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)).stream().map(OrderPayApplicationInfoEntity::getPayeeAmount)
                    .reduce(BigDecimal::add).ifPresentOrElse(entity::setRefundsTotalAmount, () -> entity.setRefundsTotalAmount(BigDecimal.ZERO));

            orderSettleAmountRecordMapper.insert(entity
            );
        }
        return fundRepaymentDeductEntity;
    }
    public void yingFengRepurchase(FundRepurchaseAccountEntity fundRepurchaseAccountEntity, Integer days, OrderInfoEntity orderInfoEntity) {

        RepurchaseRepayCalcDTO dto = new RepurchaseRepayCalcDTO();
        dto.setOrderId(orderInfoEntity.getId());
        //赎回试算
        YingFengRepurchaseRepayCalcVO repurchaseRepayCalc = repurchaseRepayService.repurchaseRepayCalc(dto);
        if (ObjectUtil.isNotEmpty(repurchaseRepayCalc.getTotal()) && CollUtil.isNotEmpty(repurchaseRepayCalc.getDetails())) {
            OrderPayApplicationInfoEntity orderPayApplyInfo = new OrderPayApplicationInfoEntity();
            orderPayApplyInfo.setOrderId(orderInfoEntity.getId())
                    .setFeeType(OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)
                    .setCurrentNode(PayApplicationNodeEnums.ACCOUNTANT_APPROVAL)
                    .setApplyUserId(1)
                    .setRemark("自动提交")
                    //付款单
                    .setApplyType(OrderFeeDetailStatusEnum.SPENDING);
            //收款主体-资方
            //付款方账户
            String payAccount = null;
            //付款方银行卡
            String payAccountNumber = null;
            //付款方开户行
            String payAccountName = null;
            try {
                String[] strings = splitString(dictUtils.getDictLabel(GlobalConstants.DictType.PAYEE_ACCOUNT.name(), 2));
                payAccount = strings[0];
                payAccountNumber = strings[2];
                payAccountName = strings[1];
            } catch (Exception e) {
                log.error("OrderPayApplicationServiceImpl.automatedRepurchaseRepay.error 格式化付款方信息失败，原因：{}", e.getMessage());
            }
            orderPayApplyInfo.setPayeeType(PayApplicationPayeeTypeEnum.YING_FENG);
            orderPayApplyInfo.setPaymentDetails("")
                    .setPayAccount(payAccount)
                    .setPayAccountName(payAccountName)
                    .setPayAccountNumber(payAccountNumber);
            //收款方账户
            orderPayApplyInfo.setPayeeAccount(fundRepurchaseAccountEntity.getAccountName());
            orderPayApplyInfo.setPayeeAccountName(fundRepurchaseAccountEntity.getAccountBank());
            orderPayApplyInfo.setPayeeAccountNumber(fundRepurchaseAccountEntity.getAccountNumber());
            //赎回金额
            orderPayApplyInfo.setPayeeAmount(repurchaseRepayCalc.getTotal().getTotalAmt());
            //赎回付款摘要
            Result<YingFengInfoVO> yingFengInfoVOResult = approveFeign.getYingFengInfoByOrderId(orderInfoEntity.getId());
            if (Result.isSuccess(yingFengInfoVOResult)) {
                YingFengInfoVO infoVO = yingFengInfoVOResult.getData();
                orderPayApplyInfo.setSummary(Objects.isNull(infoVO) ? "" : String.format("%s#龙环独资回购", infoVO.getLoanApplyNo()));
            }

            orderPayApplicationMapper.insert(orderPayApplyInfo);

            this.saveNodeRecord(orderPayApplyInfo.getId(), PayApplicationNodeEnums.ACCOUNTANT_APPLY, orderPayApplyInfo.getCurrentNode(), PayApplicationAuditTypeEnum.YUNQI
                    , null, orderPayApplyInfo.getRemark(), PayApplicationEventEnums.APPROVE_PASS, 1, LocalDateTime.now());

        }

    }
    public void lanHaiRepurchase(FundRepurchaseAccountEntity fundRepurchaseAccountEntity, Integer days, OrderInfoEntity orderInfoEntity, BigDecimal actuallyTotalAmount) {
        List<OrderPayApplicationInfoEntity> orderPayApplicationInfoEntities = orderPayApplicationMapper.selectList(
                new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                        .eq(OrderPayApplicationInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)
                        .eq(OrderPayApplicationInfoEntity::getPayeeType, PayApplicationPayeeTypeEnum.LAN_HAI)
                        .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(orderPayApplicationInfoEntities)) {
            OrderPayApplicationInfoEntity orderPayApplyInfo = new OrderPayApplicationInfoEntity();
            orderPayApplyInfo.setOrderId(orderInfoEntity.getId())
                    .setFeeType(OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)
                    .setCurrentNode(PayApplicationNodeEnums.ACCOUNTANT_APPROVAL)
                    .setApplyUserId(1)
                    .setRemark("自动提交")
                    //付款单
                    .setApplyType(OrderFeeDetailStatusEnum.SPENDING);
            //收款主体-资方
            //付款方账户
            String payAccount = null;
            //付款方银行卡
            String payAccountNumber = null;
            //付款方开户行
            String payAccountName = null;
            try {
                String[] strings = splitString(dictUtils.getDictLabel(GlobalConstants.DictType.PAYEE_ACCOUNT.name(), 2));
                payAccount = strings[0];
                payAccountNumber = strings[2];
                payAccountName = strings[1];
            } catch (Exception e) {
                log.error("OrderPayApplicationServiceImpl.lanHaiRepurchase.error 格式化付款方信息失败，原因：{}", e.getMessage());
            }
            orderPayApplyInfo.setPayeeType(PayApplicationPayeeTypeEnum.LAN_HAI);
            orderPayApplyInfo.setPaymentDetails("")
                    .setPayAccount(payAccount)
                    .setPayAccountName(payAccountName)
                    .setPayAccountNumber(payAccountNumber);
            //收款方账户
            orderPayApplyInfo.setPayeeAccount(fundRepurchaseAccountEntity.getAccountName());
            orderPayApplyInfo.setPayeeAccountName(fundRepurchaseAccountEntity.getAccountBank());
            orderPayApplyInfo.setPayeeAccountNumber(fundRepurchaseAccountEntity.getAccountNumber());
            FundRepayCalcDTO reqDTO = new FundRepayCalcDTO();
            reqDTO.setOrderId(orderInfoEntity.getId());
            FundRepaymentDeductEntity fundRepaymentDeductEntity = fundRepaymentDeductMapper.selectOne(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getOrderId, orderInfoEntity.getId())
                            .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                            .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                    FundDeductBizTypeEnums.PAYMENT,
                                    FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                    FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                    FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE
                            ))
                            .orderByDesc(FundRepaymentDeductEntity::getCreateTime)
                            .last("limit 1")
            );
            // todo 需要获取赎回金额
            BigDecimal payeeAmount = BigDecimal.ZERO;
            if (ObjUtil.isNotNull(fundRepaymentDeductEntity)) {
                payeeAmount = fundRepaymentDeductEntity.getDeductAmount();
            } else {
                try {
                    List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList = fundRepaymentInfoMapper.selectList(
                            new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                    .eq(FundRepaymentInfoEntity::getOrderId, orderInfoEntity.getId())
                                    .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                                    .orderByAsc(FundRepaymentInfoEntity::getTerm)
                    );
                    BigDecimal totalRepaymentPrincipal = fundRepaymentInfoEntityList.stream()
                            .map(FundRepaymentInfoEntity::getRepaymentPrincipal)
                            .filter(Objects::nonNull) // 过滤掉可能为 null 的值
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    // todo 还款试算
                    TrialRepayDTO dto = new TrialRepayDTO();
                    dto.setOrderId(orderInfoEntity.getId());
                    dto.setAmount(totalRepaymentPrincipal);
                    dto.setIsSettle(true);
                    Result<LanHaiTrialRepayResponse> lanHaiTrialRepayResponseResult = approveFeign.lanHaiTrialRepay(dto);
                    if (ObjUtil.isNotNull(lanHaiTrialRepayResponseResult.getData())) {
                        payeeAmount = lanHaiTrialRepayResponseResult.getData().getRepayAmount();
                    }
                } catch (Exception e) {
                    log.error("OrderPayApplicationServiceImpl.lanHaiRepurchase error:{}", e.getMessage());
                }
            }
            if (Objects.equals(payeeAmount, BigDecimal.ZERO)) {
                payeeAmount = actuallyTotalAmount;
            }

            log.info("OrderPayApplicationServiceImpl.lanHaiRepurchase fundRepaymentDeductEntity:{} orderId:{}", fundRepaymentDeductEntity, orderInfoEntity.getId());
            orderPayApplyInfo.setPayeeAmount(payeeAmount);
            //赎回付款摘要
            orderPayApplyInfo.setSummary(LocalDate.now().format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN)) + "线下还款");
            orderPayApplicationMapper.insert(orderPayApplyInfo);
            this.saveNodeRecord(orderPayApplyInfo.getId(), PayApplicationNodeEnums.ACCOUNTANT_APPLY, orderPayApplyInfo.getCurrentNode(), PayApplicationAuditTypeEnum.YUNQI
                    , null, orderPayApplyInfo.getRemark(), PayApplicationEventEnums.APPROVE_PASS, 1, LocalDateTime.now());

        }
    }
    @Override
    public ManageBankAccountSignEntity getDefaultCard(Integer managerId) {
        ManageBankAccountSignEntity manageBankAccountSignEntity = manageBankAccountSignMapper.selectOne(new MPJLambdaWrapper<ManageBankAccountSignEntity>()
                .eq(ManageBankAccountSignEntity::getUserId, managerId)
                .eq(ManageBankAccountSignEntity::getSignState, 1)
                .eq(ManageBankAccountSignEntity::getCardType, 2)
                .eq(ManageBankAccountSignEntity::getDeleteFlag, 0)
                .eq(ManageBankAccountSignEntity::getIsDefault, 1)
                .orderByDesc(ManageBankAccountSignEntity::getCreateTime)
                .last("limit 1"));
        if (ObjectUtil.isEmpty(manageBankAccountSignEntity)) {
            manageBankAccountSignEntity = manageBankAccountSignMapper.selectOne(new MPJLambdaWrapper<ManageBankAccountSignEntity>()
                    .eq(ManageBankAccountSignEntity::getUserId, managerId)
                    .eq(ManageBankAccountSignEntity::getSignState, 1)
                    .eq(ManageBankAccountSignEntity::getCardType, 2)
                    .eq(ManageBankAccountSignEntity::getDeleteFlag, 0)
                    .orderByDesc(ManageBankAccountSignEntity::getCreateTime)
                    .last("limit 1"));
        }
        return manageBankAccountSignEntity;
    }
    @Override
    public void fuMinRepurchase(FundRepurchaseAccountEntity fundRepurchaseAccountEntity, Integer days, OrderInfoEntity orderInfoEntity, BigDecimal actuallyTotalAmount) {
        List<OrderPayApplicationInfoEntity> orderPayApplicationInfoEntities = orderPayApplicationMapper.selectList(
                new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                        .eq(OrderPayApplicationInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)
                        .eq(OrderPayApplicationInfoEntity::getPayeeType, PayApplicationPayeeTypeEnum.FU_MIN)
                        .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(orderPayApplicationInfoEntities)) {
            OrderPayApplicationInfoEntity orderPayApplyInfo = new OrderPayApplicationInfoEntity();
            orderPayApplyInfo.setOrderId(orderInfoEntity.getId())
                    .setFeeType(OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)
                    .setCurrentNode(PayApplicationNodeEnums.ACCOUNTANT_APPROVAL)
                    .setApplyUserId(1)
                    .setRemark("自动提交")
                    //付款单
                    .setApplyType(OrderFeeDetailStatusEnum.SPENDING);
            //收款主体-资方
            //付款方账户
            String payAccount = null;
            //付款方银行卡
            String payAccountNumber = null;
            //付款方开户行
            String payAccountName = null;
            try {
                String[] strings = splitString(dictUtils.getDictLabel(GlobalConstants.DictType.PAYEE_ACCOUNT.name(), 2));
                payAccount = strings[0];
                payAccountNumber = strings[2];
                payAccountName = strings[1];
            } catch (Exception e) {
                log.error("OrderPayApplicationServiceImpl.fuMinRepurchase.error 格式化付款方信息失败，原因：{}", e.getMessage());
            }
            orderPayApplyInfo.setPayeeType(PayApplicationPayeeTypeEnum.FU_MIN);
            orderPayApplyInfo.setPaymentDetails("")
                    .setPayAccount(payAccount)
                    .setPayAccountName(payAccountName)
                    .setPayAccountNumber(payAccountNumber);
            //收款方账户
            orderPayApplyInfo.setPayeeAccount(fundRepurchaseAccountEntity.getAccountName());
            orderPayApplyInfo.setPayeeAccountName(fundRepurchaseAccountEntity.getAccountBank());
            orderPayApplyInfo.setPayeeAccountNumber(fundRepurchaseAccountEntity.getAccountNumber());
            BigDecimal payeeAmount = BigDecimal.ZERO;

            if (Objects.isNull(actuallyTotalAmount) || actuallyTotalAmount.compareTo(BigDecimal.ZERO) == 0){
                FundRepaymentDeductEntity fundRepaymentDeductEntity = fundRepaymentDeductMapper.selectOne(
                        new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                                .eq(FundRepaymentDeductEntity::getOrderId, orderInfoEntity.getId())
                                .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                                .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                                .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                        FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                        FundDeductBizTypeEnums.PAYMENT,
                                        FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                        FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                        FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE
                                ))
                                .orderByDesc(FundRepaymentDeductEntity::getCreateTime)
                                .last("limit 1")
                );

                FundRepayCalcEarlyDTO fundRepayCalcEarlyDTO = new FundRepayCalcEarlyDTO();
                fundRepayCalcEarlyDTO.setOrderId(orderInfoEntity.getId());
                fundRepayCalcEarlyDTO.setLoanSettlementMethod(LoanSettlementEnum.CLOSED_PERIOD_PENALTY_INSIDE);

                if (ObjUtil.isNotNull(fundRepaymentDeductEntity)) {
                    payeeAmount = fundRepaymentDeductEntity.getDeductAmount();
                } else {
                    try {
                        FundEarlyRepaymentCalcDTO fundEarlyRepaymentCalcDTO = fundDeductService.earlyRepayCalc(fundRepayCalcEarlyDTO);
                        if (ObjUtil.isNotNull(fundEarlyRepaymentCalcDTO)) {
                            payeeAmount = fundEarlyRepaymentCalcDTO.getFundRepayAmt();
                        }
                    } catch (Exception e) {
                        log.error("OrderPayApplicationServiceImpl.fuMinRepurchase error:{}", e.getMessage());
                    }
                }


//        Result<FundRepayCalcVO> fundRepayCalcVOResult = approveFeign.repayCalc(reqDTO);
                log.info("OrderPayApplicationServiceImpl.fuMinRepurchase fundRepaymentDeductEntity:{} orderId:{}", fundRepaymentDeductEntity, orderInfoEntity.getId());
                orderPayApplyInfo.setPayeeAmount(payeeAmount);
            }else {
                orderPayApplyInfo.setPayeeAmount(actuallyTotalAmount);
            }
            //赎回付款摘要
//        //todo  富民赎回付款摘要
//        Result<YingFengInfoVO> yingFengInfoVOResult = approveFeign.getYingFengInfoByOrderId(orderInfoEntity.getId());
//        if (Result.isSuccess(yingFengInfoVOResult)) {
//            YingFengInfoVO infoVO = yingFengInfoVOResult.getData();
            orderPayApplyInfo.setSummary(orderInfoEntity.getCustomerName() + "回购");
//        }

            orderPayApplicationMapper.insert(orderPayApplyInfo);

            this.saveNodeRecord(orderPayApplyInfo.getId(), PayApplicationNodeEnums.ACCOUNTANT_APPLY, orderPayApplyInfo.getCurrentNode(), PayApplicationAuditTypeEnum.YUNQI
                    , null, orderPayApplyInfo.getRemark(), PayApplicationEventEnums.APPROVE_PASS, 1, LocalDateTime.now());

        }
    }

    @Override
    public void directSettle(String orderNumber) {
        CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                .eq(CaseInfoEntity::getDigitalOrderId, orderNumber)
                .eq(CaseInfoEntity::getDeleteFlag, 0)
                .eq(CaseInfoEntity::getCurrentNode, 30));
        if (ObjUtil.isNull(caseInfoEntity)) {
            OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                    .eq(OrderInfoEntity::getOrderNumber, orderNumber)
                    .eq(OrderInfoEntity::getDeleteFlag, 0));
            OrderPayApplicationInfoEntity orderPayApplicationInfoEntity = orderPayApplicationMapper.selectOne(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                    .eq(OrderPayApplicationInfoEntity::getOrderId, orderInfo.getId())
                    .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                    .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT.getCode())
                    .orderByDesc(OrderPayApplicationInfoEntity::getCreateTime)
                    .last("limit 1"));
            CaseInfoEntity caseInfoEntity1 = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                    .eq(CaseInfoEntity::getOrderId, orderInfo.getId())
                    .eq(CaseInfoEntity::getDeleteFlag, 0)
                    .eq(CaseInfoEntity::getDataSource, 1)
                    .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
            if (ObjUtil.equals(caseInfoEntity1.getCirculationType(),1)){
                Integer count = caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                        .set(CaseInfoEntity::getIsSettled, 1)
                        .set(CaseInfoEntity::getVisitResult, 2)
                        .set(CaseInfoEntity::getSettledTime, orderPayApplicationInfoEntity.getCreateTime())
                        .eq(CaseInfoEntity::getId, caseInfoEntity1.getId())
                );
            }else if (ObjUtil.equals(caseInfoEntity1.getCirculationType(),2)){
                Integer count = caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                        .set(CaseInfoEntity::getIsSettled, 1)
                        .set(CaseInfoEntity::getPreservationResult, 3)
                        .set(CaseInfoEntity::getSettledTime, orderPayApplicationInfoEntity.getCreateTime())
                        .eq(CaseInfoEntity::getId, caseInfoEntity1.getId())
                );
            }
            CaseInfoRepayCalcVO caseInfoRepayCalcVO = caseInfoService.earlyRepayCalc(orderPayApplicationInfoEntity.getOrderId());
            BigDecimal actualAmount = orderPayApplicationInfoEntity.getPayeeAmount();
            BigDecimal reductionAmount = ObjUtil.defaultIfNull(orderPayApplicationInfoEntity.getReductionAmount(), BigDecimal.ZERO);
            BigDecimal depositAmount = ObjUtil.defaultIfNull(orderPayApplicationInfoEntity.getDeposit(), BigDecimal.ZERO);
            List<OrderPayApplicationInfoEntity> relatedOrders = orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                    .eq(OrderPayApplicationInfoEntity::getOrderNumber, orderPayApplicationInfoEntity.getOrderNumber())
                    .eq(OrderPayApplicationInfoEntity::getOrderApplicationSource, 1)
                    .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)
                    .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)
                    .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                    .eq(OrderPayApplicationInfoEntity::getOrderSource, 1));

            for (OrderPayApplicationInfoEntity entity : relatedOrders) {
                // 累加实收金额（仅包括 payeeAmount）
                actualAmount = actualAmount.add(ObjUtil.defaultIfNull(entity.getPayeeAmount(), BigDecimal.ZERO));
                //减免金额
                reductionAmount = reductionAmount.add(ObjUtil.defaultIfNull(entity.getReductionAmount(), BigDecimal.ZERO));
                //保证金
                depositAmount = depositAmount.add(ObjUtil.defaultIfNull(entity.getDeposit(), BigDecimal.ZERO));

            }
            caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                    .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                    .eq(CaseInfoEntity::getDeptId, DeptEnum.ZHONG_XIN.getId())
                    .eq(CaseInfoEntity::getDeleteFlag, 0)
                    .eq(CaseInfoEntity::getDataSource, 1)
                    .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
            orderSettleAmountRecordMapper.update(new LambdaUpdateWrapper<OrderSettleAmountRecordEntity>()
                    .set(OrderSettleAmountRecordEntity::getDeleteFlag, 1)
                    .eq(OrderSettleAmountRecordEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                    .eq(OrderSettleAmountRecordEntity::getDeleteFlag, 0));
            OrderSettleAmountRecordEntity entity = new OrderSettleAmountRecordEntity()
                    .setOrderId(orderPayApplicationInfoEntity.getOrderId())
                    .setOrderSource(orderPayApplicationInfoEntity.getOrderSource())
                    .setOrderNumber(orderPayApplicationInfoEntity.getOrderNumber())
                    .setTrialSettlementAmount(caseInfoRepayCalcVO.getLoanSettlementAmount())
                    .setApplySettlementAmount(actualAmount)
                    .setActualSettlementAmount(actualAmount)
                    .setSettlementMethod(caseInfoRepayCalcVO.getLoanSettlementMethod())
                    .setOutsourcingStatus(1)
                    .setUsageDays(caseInfoRepayCalcVO.getUsageDays())
                    .setRemainingPrincipal(caseInfoRepayCalcVO.getRemainingPrincipal())
                    .setInstalmentsRepaid(caseInfoRepayCalcVO.getInstalmentsRepaid());
            ExemptionApplicationEntity exemptionApplicationEntity = exemptionApplicationEntityMapper.selectOne(new LambdaQueryWrapper<ExemptionApplicationEntity>()
                    .eq(ExemptionApplicationEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                    .eq(ExemptionApplicationEntity::getDeleteFlag, 0)
                    .eq(ExemptionApplicationEntity::getIsUse, 1)
                    .orderByDesc(ExemptionApplicationEntity::getId), false);
            if (ObjUtil.isNotNull(exemptionApplicationEntity)){
                entity.setApplySettlementAmount(exemptionApplicationEntity.getApplySettlementAmount());
            }
            if (ObjUtil.equals(caseInfoRepayCalcVO.getLoanSettlementEnum(), LoanSettlementEnum.CLOSED_PERIOD_PENALTY_INSIDE)){
                entity.setSettlementMethod(entity.getSettlementMethod()+caseInfoRepayCalcVO.getSettlePenaltyRate().multiply(new BigDecimal("100"))+ "%");
            }
            orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                            .eq(OrderPayApplicationInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                            .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                            .eq(OrderPayApplicationInfoEntity::getApplyType,1)
                            .in(OrderPayApplicationInfoEntity::getFeeType, List.of(OrderFeeDetailExpandTypeEnum.OTHER,
                                    OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE, OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT,
                                    OrderFeeDetailExpandTypeEnum.DISPOSABLE_SECURITY_DEPOSIT, OrderFeeDetailExpandTypeEnum.INSTALLMENT_SERVICE_FEE,
                                    OrderFeeDetailExpandTypeEnum.GPS_DATA_TRANSFER_FEE, OrderFeeDetailExpandTypeEnum.GPS_EQUIPMENT_COMPENSATION_FEE))
                            .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)).stream().map(OrderPayApplicationInfoEntity::getPayeeAmount)
                    .reduce(BigDecimal::add).ifPresentOrElse(entity::setRefundsTotalAmount, () -> entity.setRefundsTotalAmount(BigDecimal.ZERO));
            orderSettleAmountRecordMapper.insert(entity);
            if (ObjUtil.isNotNull(caseInfoEntity)) {
                zhongXinService.pushToZhongXinAsRetrieveAcceptSettle(
                        new ZhongXinAsRetrieveAcceptSettleDTO()
                                .setCase_no(caseInfoEntity.getCaseNo())
                                .setReceivable_amount(orderPayApplicationInfoEntity.getPayeeAmount())
                                .setReduction_amount(BigDecimal.ZERO)
                                .setRemark(orderPayApplicationInfoEntity.getRemark())
                                .setFinance_remark("")
                );
            }
//                                                    if (ObjUtil.equals(orderInfo.getIsRepurchase(),1)){
//                                                    repurchaseRepaymentInfoMapper.update(new LambdaUpdateWrapper<RepurchaseRepaymentInfoEntity>()
//                                                            .set(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
//                                                            .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
//                                                    );
//                                                }else {fundRepaymentInfoMapper.update(new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
//                                                            .set(FundRepaymentInfoEntity::getRepaymentStatus,FundRepayStatusEnum.SETTLED)
//                                                            .eq(FundRepaymentInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId()));
//                                                }

            OrderStateService bean = SpringContentUtils.getBean(OrderStateService.class);
            bean.sendEvent(States.PAYMENT_SUCCESS, Events.SETTLED, orderPayApplicationInfoEntity.getOrderId(),
                    1);
            orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                    .set(OrderInfoEntity::getPlanState, 2)
                    .set(OrderInfoEntity::getCompanyPlanState, 1)
                    .eq(OrderInfoEntity::getId, orderPayApplicationInfoEntity.getOrderId()));

            if (ObjUtil.isNotNull(orderPayApplicationInfoEntity.getOrderApplicationSource()) && orderPayApplicationInfoEntity.getOrderApplicationSource() == 1 && Objects.equals(orderInfo.getFundId(), FundEnum.ZHONG_HENG_TONG_HUI.getValue())) {
                FundEnum fundEnum = FundEnum.getFundEnum(orderInfo.getFundId());
                FinalFundInfoEntity fundApplyInfo = finalFundInfoMapper.selectOne(
                        new LambdaQueryWrapper<FinalFundInfoEntity>()
                                .eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
                                .eq(FinalFundInfoEntity::getFundId, fundEnum.getValue())
                                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                                .last("limit 1")
                );
                DaiChangJieQingTongZhiResultDTO daiChangJieQingTongZhiResultDTO = new DaiChangJieQingTongZhiResultDTO();
                daiChangJieQingTongZhiResultDTO.setOrderNum(fundApplyInfo.getLoanBillNo());
                daiChangJieQingTongZhiResultDTO.setSpOrderNum(orderInfo.getOrderNumber());
                daiChangJieQingTongZhiResultDTO.setQiShu(ObjUtil.isNotNull(orderPayApplicationInfoEntity.getRepaymentTerm()) ? String.valueOf(orderPayApplicationInfoEntity.getRepaymentTerm()) : "");
                daiChangJieQingTongZhiResultDTO.setDcHeJi(String.valueOf(orderPayApplicationInfoEntity.getPayeeAmount()));
                daiChangJieQingTongZhiResultDTO.setDcBenJin(String.valueOf(orderPayApplicationInfoEntity.getPayeeAmount()));
                daiChangJieQingTongZhiResultDTO.setDcTime(orderPayApplicationInfoEntity.getCreateTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
                approveFeign.compensateSettleNotice(daiChangJieQingTongZhiResultDTO);
            }
        }
        if (ObjUtil.equals(caseInfoEntity.getDataSource(),2)){
            BigDecimal actualAmount =BigDecimal.ZERO;
            BigDecimal reductionAmount = BigDecimal.ZERO;
            BigDecimal depositAmount = BigDecimal.ZERO;
            List<OrderPayApplicationInfoEntity> relatedOrders = orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                    .eq(OrderPayApplicationInfoEntity::getOrderNumber,orderNumber)
                    .eq(OrderPayApplicationInfoEntity::getOrderApplicationSource, 1)
                    .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)
                    .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)
                    .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0));

            for (OrderPayApplicationInfoEntity entity : relatedOrders) {
                // 累加实收金额（仅包括 payeeAmount）
                actualAmount = actualAmount.add(ObjUtil.defaultIfNull(entity.getPayeeAmount(), BigDecimal.ZERO));
                //减免金额
                reductionAmount = reductionAmount.add(ObjUtil.defaultIfNull(entity.getReductionAmount(), BigDecimal.ZERO));
                //保证金
                depositAmount = depositAmount.add(ObjUtil.defaultIfNull(entity.getDeposit(), BigDecimal.ZERO));
            }
            BigDecimal totalAmount = actualAmount.add(reductionAmount).add(depositAmount);
            BigDecimal finalActualAmount = actualAmount;
            BigDecimal finalReductionAmount = reductionAmount;
            OrderPayApplicationInfoEntity orderPayApplicationInfoEntity = orderPayApplicationMapper.selectOne(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                    .eq(OrderPayApplicationInfoEntity::getOrderNumber, orderNumber)
                    .eq(OrderPayApplicationInfoEntity::getOrderApplicationSource, 1)
                    .eq(OrderPayApplicationInfoEntity::getOrderSource, 2)
                    .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)
                    .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)
                    .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0));
            Optional.ofNullable(outsourcingContractService.getDigitalizeSettleList(
                            new DigitalizeWeiwaiOrderStatusDTO().setOrder_id(orderPayApplicationInfoEntity.getOrderNumber())))
                    .ifPresent(zongValue -> {
                        DigitalOutsourcingOrderEntity digitalOrder = digitalOutsourcingOrderEntityMapper.selectOne(
                                new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>()
                                        .eq(DigitalOutsourcingOrderEntity::getOrderNo, orderPayApplicationInfoEntity.getOrderNumber())
                                        .eq(DigitalOutsourcingOrderEntity::getDataSource, 2)
                                        .eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0)
                                        .orderByDesc(DigitalOutsourcingOrderEntity::getCreateTime));

                        Optional.ofNullable(digitalOrder).ifPresent(order -> {
                            String payload = new JSONObject()
                                    .set("order_id", orderPayApplicationInfoEntity.getOrderNumber())
                                    .set("name", order.getCustomerName())
                                    .set("receivable_amount", finalActualAmount)
                                    .set("is_help", 1)
                                    .set("status", 1)
                                    .set("is_verify", 1)
                                    .set("is_gps", 1)
                                    .set("req_no", orderPayApplicationInfoEntity.getCreateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() + orderPayApplicationInfoEntity.getOrderNumber())
                                    .set("usage_days", order.getUsageDays())
                                    .set("receivable_total_amount", zongValue.getSettleAmt())
                                    .toString();
                            LinkedMultiValueMap<String, Object> objectLinkedMultiValueMap = new LinkedMultiValueMap<>();
                            objectLinkedMultiValueMap.add("datainfo", digitalizeZhongxinEncryptUtil.encode(payload));
                            digitalizeFeign.weiwaiDigitalizeZhongXinSettleNotify(objectLinkedMultiValueMap);
                            CaseInfoEntity caseInfoEntity1 = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                    .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                                    .eq(CaseInfoEntity::getDeleteFlag, 0)
                                    .eq(CaseInfoEntity::getDataSource, 2)
                                    .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                            if (ObjUtil.equals(caseInfoEntity1.getCirculationType(), 1)) {
                                Integer count = caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                                        .set(CaseInfoEntity::getIsSettled, 1)
                                        .set(CaseInfoEntity::getVisitResult, 2)
                                        .set(CaseInfoEntity::getSettledTime, orderPayApplicationInfoEntity.getCreateTime())
                                        .eq(CaseInfoEntity::getId, caseInfoEntity1.getId())
                                );
                            } else if (ObjUtil.equals(caseInfoEntity1.getCirculationType(), 2)) {
                                Integer count = caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                                        .set(CaseInfoEntity::getIsSettled, 1)
                                        .set(CaseInfoEntity::getPreservationResult, 3)
                                        .set(CaseInfoEntity::getSettledTime, orderPayApplicationInfoEntity.getCreateTime())
                                        .eq(CaseInfoEntity::getId, caseInfoEntity1.getId())
                                );
                            }else if (ObjUtil.equals(caseInfoEntity1.getCirculationType(), 3)) {
                                Integer count = caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                                        .set(CaseInfoEntity::getIsSettled, 1)
                                        .set(CaseInfoEntity::getTransferStatus, 2)
                                        .set(CaseInfoEntity::getSettledTime, orderPayApplicationInfoEntity.getCreateTime())
                                );
                            }
                            CaseInfoEntity caseInfoEntity2 = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                    .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                                    .eq(CaseInfoEntity::getDeptId, DeptEnum.ZHONG_XIN.getId())
                                    .eq(CaseInfoEntity::getDeleteFlag, 0)
                                    .eq(CaseInfoEntity::getDataSource, 2)
                                    .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                            if (ObjUtil.isNotNull(caseInfoEntity2)) {
                                zhongXinService.pushToZhongXinAsRetrieveAcceptSettle(
                                        new ZhongXinAsRetrieveAcceptSettleDTO()
                                                .setCase_no("HF" + caseInfoEntity2.getCaseNo())
                                                .setReceivable_amount(finalActualAmount)
                                                .setReduction_amount(finalReductionAmount)
                                                .setRemark(orderPayApplicationInfoEntity.getRemark())
                                                .setFinance_remark("")
                                );
                            }

                        });
                    });
        }
        if (ObjUtil.equals(caseInfoEntity.getDataSource(),3)){
            OrderPayApplicationInfoEntity orderPayApplicationInfoEntity = orderPayApplicationMapper.selectOne(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                    .eq(OrderPayApplicationInfoEntity::getOrderNumber, orderNumber)
                    .eq(OrderPayApplicationInfoEntity::getOrderApplicationSource, 1)
                    .eq(OrderPayApplicationInfoEntity::getOrderSource, 3)
                    .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)
                    .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)
                    .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0));
            CaseInfoEntity caseInfoEntity1 = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                    .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                    .eq(CaseInfoEntity::getDeleteFlag, 0)
                    .eq(CaseInfoEntity::getDataSource, 3)
                    .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
            BigDecimal actualAmount = orderPayApplicationInfoEntity.getPayeeAmount();
            BigDecimal reductionAmount = ObjUtil.defaultIfNull(orderPayApplicationInfoEntity.getReductionAmount(), BigDecimal.ZERO);
            BigDecimal totalAmount = actualAmount.add(reductionAmount);
            BigDecimal finalActualAmount = actualAmount;
            BigDecimal finalReductionAmount = reductionAmount;
            Optional.ofNullable(outsourcingSettlementTrialCalculationsService.getReductionRecordConditional(
                            new ReductionRecordConditionalDTO().setCaseId(caseInfoEntity1.getId()).setRepayType(2)))
                    .filter(zongValue -> totalAmount.compareTo(zongValue.getLoanAmount()) >= 0)
                    .ifPresent(zongValue -> {
                        DigitalOutsourcingOrderEntity digitalOrder = digitalOutsourcingOrderEntityMapper.selectOne(
                                new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>()
                                        .eq(DigitalOutsourcingOrderEntity::getOrderNo, orderPayApplicationInfoEntity.getOrderNumber())
                                        .eq(DigitalOutsourcingOrderEntity::getDataSource, 3)
                                        .eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0)
                                        .orderByDesc(DigitalOutsourcingOrderEntity::getCreateTime));

                        Optional.ofNullable(digitalOrder).ifPresent(order -> {
                            Integer count = caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                                    .set(CaseInfoEntity::getIsSettled, 1)
                                    .set(CaseInfoEntity::getVisitResult, 2)
                                    .set(CaseInfoEntity::getSettledTime, orderPayApplicationInfoEntity.getCreateTime())
                                    .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                                    .eq(CaseInfoEntity::getDeleteFlag, 0)
                                    .eq(CaseInfoEntity::getDataSource, 3)
                                    .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                            CaseInfoEntity caseInfoEntity2 = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                    .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                                    .eq(CaseInfoEntity::getDeleteFlag, 0)
                                    .eq(CaseInfoEntity::getDataSource, 3)
                                    .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                            KingdeeStatusRequestDTO kingdeeStatusRequestDTO = new KingdeeStatusRequestDTO();
                            kingdeeStatusRequestDTO.setCode(1);
                            kingdeeStatusRequestDTO.setMsg("成功");
                            KingdeeRequestData data = new KingdeeRequestData();
                            data.setOrderNumber(caseInfoEntity2.getDigitalOrderId());
                            data.setFlowType("委外保全");
                            data.setOutsourceOrg(caseInfoEntity2.getDisposalCompany());
                            data.setOutsourceTime(caseInfoEntity2.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                            data.setIs_seettle(1);
                            data.setSeettle_time(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                            data.setSeettle_amount(totalAmount.toPlainString());
                            kingdeeStatusRequestDTO.setData(data);
                            kingdeeOutsourcingService.pushKingdeeorderStatus(kingdeeStatusRequestDTO);
                            if (ObjUtil.equals(caseInfoEntity2.getDeptId(), DeptEnum.ZHONG_XIN.getId())) {
                                zhongXinService.pushToZhongXinAsRetrieveAcceptSettle(
                                        new ZhongXinAsRetrieveAcceptSettleDTO()
                                                .setCase_no(caseInfoEntity2.getCaseNo())
                                                .setReceivable_amount(finalActualAmount)
                                                .setReduction_amount(finalReductionAmount)
                                                .setRemark(orderPayApplicationInfoEntity.getRemark())
                                                .setFinance_remark("")
                                );
                            }

                        });
                    });
        }
    }

    @Override
    public SpecialRefundApplyVO getSpecialRefundApply(SpecialRefundApplyDTO dto) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());

        List<SprEductionUsageEntity> entityList = sprEductionUsageService.list(
                new LambdaQueryWrapper<SprEductionUsageEntity>()
                        .eq(SprEductionUsageEntity::getVehicleNumber, orderInfoEntity.getVehicleNumber())
                        .eq(SprEductionUsageEntity::getUsageStatus, 2)
                        .eq(SprEductionUsageEntity::getApprovalTemplate, 6)
                        .eq(SprEductionUsageEntity::getDeleteFlag, 0)
        );
        SpecialRefundApplyVO vo = new SpecialRefundApplyVO();
        if (CollUtil.isNotEmpty(entityList)){
            String approvalDetails = entityList.get(0).getApprovalDetails();
            Map<String, String> map = deserializeApprovalDetails(approvalDetails);
            if (CollUtil.isNotEmpty( map)){
                vo.setExistDingTalkApproval(true);
                vo.setVehicleNumber(map.getOrDefault("TextField_IR3ANOW44VC0", ""));
                vo.setCustomerName(map.getOrDefault("TextField-JMBEQWRO", ""));
                vo.setPayeeName(map.getOrDefault("TextField-JTDP3YED",""));
                vo.setRefundAmount(map.containsKey("NumberField-JMBEQWRQ") ? new BigDecimal(map.get("NumberField-JMBEQWRQ")) : BigDecimal.ZERO);
                vo.setRefundReason(map.getOrDefault("TextField-JTDP5QJV",""));
                vo.setApprovalStatus(1);
            }else {
                throw new BusinessException("审批详情转换异常");
            }
        }else {
            vo.setExistDingTalkApproval(false);
        }
        return vo;
    }

    @Override
    public void expenseApplicationExport(ExpenseApplicationExportDTO dto, LoginUser loginUser, HttpServletResponse response) {
        if (CollUtil.isEmpty(dto.getFeeType())){
            throw new BusinessException("费用类型不能为空");
        }
        if (ObjUtil.isNull(dto.getFeeDetails())){
            dto.setFeeDetails(1);
        }
        PayApplicationNodeEnums currentNode = dto.getCurrentNode();
        MPJLambdaWrapper<OrderPayApplicationInfoEntity> queryWrapper = new MPJLambdaWrapper<OrderPayApplicationInfoEntity>()
                .select(OrderInfoEntity::getManagerId,
                        OrderInfoEntity::getTeamId)
                .selectAs(OrderInfoEntity::getId, OrderPayApplyListVO::getOrderId)
                .selectAs(OrderFeeInfoEntity::getPaySnNumBack, OrderPayApplyListVO::getPaySnNumBack)
                .select(
                        "CASE WHEN t.order_source = 1 THEN t1.vehicle_number ELSE t3.vehicle_number END AS vehicleNumber",
                        "CASE WHEN t.order_source = 1 THEN t1.store_name ELSE t3.store_name END AS storeName",
                        "CASE WHEN t.order_source = 1 THEN t1.customer_name ELSE t3.customer_name END AS customerName",
                        "CASE WHEN t.order_source = 1 THEN t1.order_number ELSE t3.order_id END AS orderNumber",
                        "CASE WHEN t.order_source = 1 THEN t1.region_name ELSE t3.region_name END AS regionName",
                        "CASE WHEN t.order_source = 1 THEN t1.fund_name ELSE t3.fund_name END AS fundName",
                        "CASE WHEN t.order_source = 1 THEN t1.product_name ELSE t3.product_name END AS productName"
                )
                .selectAs(DigitalOutsourcingOrderEntity::getBusinessManager, OrderPayApplyListVO::getManagerName)
                .selectAs(OrderPayApplicationInfoEntity::getCurrentNode, OrderPayApplyListVO::getCurrentNode)
                .selectAs(OrderPayApplicationInfoEntity::getPaymentDetails, OrderPayApplyListVO::getPaymentDetails)
                .selectAs(OrderPayApplicationInfoEntity::getPayAccount, OrderPayApplyListVO::getPayAccount)
                .selectAs(OrderPayApplicationInfoEntity::getPayAccountName, OrderPayApplyListVO::getPayAccountName)
                .selectAs(OrderPayApplicationInfoEntity::getPayAccountNumber, OrderPayApplyListVO::getPayAccountNumber)
                .selectAs(OrderPayApplicationInfoEntity::getId, OrderPayApplyListVO::getId)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccount, OrderPayApplyListVO::getPayeeAccount)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccountName, OrderPayApplyListVO::getPayeeAccountName)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAmount, OrderPayApplyListVO::getPayeeAmount)
                .selectAs(OrderPayApplicationInfoEntity::getFeeType, OrderPayApplyListVO::getFeeType)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeType, OrderPayApplyListVO::getPayeeType)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccountNumber, OrderPayApplyListVO::getPayeeAccountNumber)
                .selectAs(OrderPayApplicationInfoEntity::getApplyUserId, OrderPayApplyListVO::getUserId)
                .selectAs(OrderPayApplicationInfoEntity::getCreateTime, OrderPayApplyListVO::getSubmitTime)
                .selectAs(OrderPayApplicationInfoEntity::getRemark, OrderPayApplyListVO::getRemark)
                .selectAs(OrderPayApplicationInfoEntity::getAccountantRemark, OrderPayApplyListVO::getAccountantRemark)
                .selectAs(OrderPayApplicationInfoEntity::getCashierRemark, OrderPayApplyListVO::getCashierRemark)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccountBranchName, OrderPayApplyListVO::getPayeeAccountBranchName)
                .selectAs(OrderPayApplicationInfoEntity::getPayeePhone, OrderPayApplyListVO::getPayeePhone)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeCardNumber, OrderPayApplyListVO::getPayeeCardNumber)
                .selectAs(OrderPayApplicationInfoEntity::getAccountantApproveTime, OrderPayApplyListVO::getAccountantApproveTime)
                .selectAs(OrderPayApplicationInfoEntity::getAccountantUserId, OrderPayApplyListVO::getAccountantChecker)
                .selectAs(OrderPayApplicationInfoEntity::getCashierApproveTime, OrderPayApplyListVO::getCashierApproveTime)
                .selectAs(OrderPayApplicationInfoEntity::getPaymentTime, OrderPayApplyListVO::getPaymentTime)
                .selectAs(OrderPayApplicationInfoEntity::getCashierUserId, OrderPayApplyListVO::getCashierChecker)
                .selectAs(OrderPayApplicationInfoEntity::getApplyType, OrderPayApplyListVO::getApplyType)
                .selectAs(OrderPayApplicationInfoEntity::getFeeDetails, OrderPayApplyListVO::getFeeDetails)
                .selectAs(OrderPayApplicationInfoEntity::getPaymentVoucherList, OrderPayApplyListVO::getPaymentVoucherList)
                .selectAs(OrderPayApplicationInfoEntity::getOrderSource, OrderPayApplyListVO::getOrderSource)
                .selectAs(OrderPayApplicationInfoEntity::getOrderApplicationSource, OrderPayApplyListVO::getOrderApplicationSource)
                .selectAs(OrderPayApplicationInfoEntity::getRepaymentTerm, OrderPayApplyListVO::getRepaymentTerm)
                .selectAs(OrderInfoEntity::getProductId, OrderPayApplyListVO::getProductId)
//                .selectAs(ProductInfoEntity::getName, OrderPayApplyListVO::getProductName)
//                .selectAs(ProductInfoEntity::getCashPerformance, OrderPayApplyListVO::getCashPerformance)
//                .selectAs(ProductInfoEntity::getTerm, OrderPayApplyListVO::getPeriod)
//                .selectAs(OrderAmountEntity::getCustomerConfirmAmount, OrderPayApplyListVO::getCustomerConfirmAmount)
                .selectAs(OrderPayApplicationInfoEntity::getSummary, OrderPayApplyListVO::getSummary)
                .selectAs(OrderInfoEntity::getPaymentTime, OrderPayApplyListVO::getLoanTime)
                .leftJoin(OrderInfoEntity.class, on -> on.eq(OrderInfoEntity::getId, OrderPayApplicationInfoEntity::getOrderId).eq(OrderPayApplicationInfoEntity::getOrderSource, 1))
                .leftJoin(OrderFeeInfoEntity.class,
                        on -> on.eq(OrderFeeInfoEntity::getOrderId, OrderPayApplicationInfoEntity::getOrderId).eq(OrderFeeInfoEntity::getGpsFeeStatus, 2).eq(OrderPayApplicationInfoEntity::getOrderSource, 1).eq(OrderFeeInfoEntity::getDeleteFlag,0))
                .leftJoin(DigitalOutsourcingOrderEntity.class, on -> on.eq(DigitalOutsourcingOrderEntity::getOrderId, OrderPayApplicationInfoEntity::getOrderNumber).ne(OrderPayApplicationInfoEntity::getOrderSource, 1).eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0))
                .leftJoin(CaseInfoEntity.class, on -> on.eq(CaseInfoEntity::getDigitalOrderId, DigitalOutsourcingOrderEntity::getOrderNo).eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS).eq(CaseInfoEntity::getDeleteFlag, 0))
//                .leftJoin(ProductInfoEntity.class,ProductInfoEntity::getId, OrderInfoEntity::getProductId)
//                .leftJoin(OrderAmountEntity.class, OrderAmountEntity::getOrderId, OrderInfoEntity::getId)
                .eq(ObjectUtil.isNotEmpty(dto.getFeeDetails()), OrderPayApplicationInfoEntity::getFeeDetails, dto.getFeeDetails())
                .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                .in(CollUtil.isNotEmpty(dto.getFeeType()), OrderPayApplicationInfoEntity::getFeeType, dto.getFeeType())
                .eq(ObjectUtil.isNotEmpty(dto.getPaymentDetails()), OrderPayApplicationInfoEntity::getPaymentDetails, dto.getPaymentDetails())
                .like(ObjectUtil.isNotEmpty(dto.getPayeeAccount()), OrderPayApplicationInfoEntity::getPayeeAccount, dto.getPayeeAccount())
                .and(ObjectUtil.isNotEmpty(dto.getVehicleNumber()), or -> or.like(OrderInfoEntity::getVehicleNumber, dto.getVehicleNumber())
                        .or().like(DigitalOutsourcingOrderEntity::getVehicleNumber, dto.getVehicleNumber())
                )
                .and(ObjectUtil.isNotEmpty(dto.getOrderNumber()), or -> or.like(OrderInfoEntity::getOrderNumber, dto.getOrderNumber())
                        .or().like(DigitalOutsourcingOrderEntity::getOrderId, dto.getOrderNumber())
                )
                .eq(ObjectUtil.isNotEmpty(dto.getCurrentNode()), OrderPayApplicationInfoEntity::getCurrentNode, dto.getCurrentNode())
                .and(StringUtils.hasText(dto.getCustomerName()), or -> or.like(OrderInfoEntity::getCustomerName, dto.getCustomerName())
                        .or().like(DigitalOutsourcingOrderEntity::getCustomerName, dto.getCustomerName())
                )
                .orderByDesc(OrderPayApplicationInfoEntity::getCreateTime);
        if (ObjUtil.isNotNull(dto.getPaymentTime())) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getPaymentTime,
                    LocalDateTime.of(dto.getPaymentTime(), LocalTime.MIN), LocalDateTime.of(dto.getPaymentTime(), LocalTime.MAX));
        }
        if (dto.getCashierApproveTimeStartDate() != null && dto.getCashierApproveTimeEndDate() == null) {
            queryWrapper.ge(OrderPayApplicationInfoEntity::getCashierApproveTime, getTimeOfStart(dto.getCashierApproveTimeStartDate()));
        }
        if (dto.getCashierApproveTimeStartDate() == null && dto.getCashierApproveTimeEndDate() != null) {
            queryWrapper.le(OrderPayApplicationInfoEntity::getCashierApproveTime, getTimeOfEnd(dto.getCashierApproveTimeEndDate()));
        }
        if (dto.getCashierApproveTimeStartDate() != null && dto.getCashierApproveTimeEndDate() != null) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getCashierApproveTime,
                    getTimeOfStart(dto.getCashierApproveTimeStartDate()),
                    getTimeOfEnd(dto.getCashierApproveTimeEndDate()));
        }
        if (dto.getApproveStartDate() != null && dto.getApproveEndDate() == null) {
            queryWrapper.ge(OrderPayApplicationInfoEntity::getCreateTime, getTimeOfStart(dto.getApproveStartDate()));
        }
        if (dto.getApproveStartDate() == null && dto.getApproveEndDate() != null) {
            queryWrapper.le(OrderPayApplicationInfoEntity::getCreateTime, getTimeOfEnd(dto.getApproveEndDate()));
        }
        if (dto.getApproveStartDate() != null && dto.getApproveEndDate() != null) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getCreateTime,
                    getTimeOfStart(dto.getApproveStartDate()),
                    getTimeOfEnd(dto.getApproveEndDate()));
        }

        if (dto.getPaymentStartTime() != null && dto.getPaymentEndTime() == null) {
            queryWrapper.ge(OrderPayApplicationInfoEntity::getPaymentTime, getTimeOfStart(dto.getApproveStartDate()));
        }
        if (dto.getPaymentStartTime() == null && dto.getPaymentEndTime() != null) {
            queryWrapper.le(OrderPayApplicationInfoEntity::getPaymentTime, getTimeOfEnd(dto.getApproveEndDate()));
        }
        if (dto.getPaymentStartTime() != null && dto.getPaymentEndTime() != null) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getPaymentTime,
                    getTimeOfStart(dto.getPaymentStartTime()),
                    getTimeOfEnd(dto.getPaymentEndTime()));
        }

        List<Integer> roleIds = loginUser.getRoleIds();
        String scopes = loginUser.getScopes();
        boolean hasRole = RoleEnum.ACCOUNTANT.hasRole(roleIds) || RoleEnum.CASHIER.hasRole(roleIds) || RoleEnum.SYS_ADMIN.hasRole(roleIds);
        if (currentNode == null || !hasRole || (scopes != null && !scopes.contains("data:all"))) {
            dataPermissionService.limitPayApplicationWithOrder(loginUser, queryWrapper);
        }

        List<OrderPayApplyListVO> records = orderPayApplicationMapper.selectJoinList(OrderPayApplyListVO.class, queryWrapper);
        if(CollUtil.isNotEmpty(records)){
            SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.EXPORT_EXPENSE_APPLICATIONS_LIMIT);
            int num = 0;
            if (Objects.equals(switchInfo.getSwitchFlag(),1) && StringUtils.hasText(switchInfo.getValue())){
                num = Integer.parseInt(switchInfo.getValue());
            }else {
                num = 5000;
            }
            log.info("OrderPayApplicationPriveteMethodImpl.expenseApplicationExport records.size:{} num:{}",records.size(),num);
            if (records.size() > num){
                log.info("OrderPayApplicationPriveteMethodImpl.expenseApplicationExport dingTaskExport start");
                orderPayApplicationExpandService.dingTaskExport(loginUser, dto, records);
                throw new BusinessException("正在导出中,稍后将导出文件发送到您的钉钉,请注意查收");
            }else {
                log.info("OrderPayApplicationPriveteMethodImpl.expenseApplicationExport pageExport start");
                pageExport(dto, records,response);
            }
        }else {
            log.info("OrderPayApplicationPriveteMethodImpl.expenseApplicationExport pageExport start");
            pageExport(dto, records,response);
        }
//        }else {
//            throw new BusinessException("暂无数据");
//        }
//        VehicleServiceFeesExportVO  车务费
//        CashBackPerformanceExportVO  现返绩效
//        RedemptionExportVO  赎回
//        ExpenseApplicationExportVO  通用

    }

    /**
     * 页面导出
     */
    private void pageExport(ExpenseApplicationExportDTO dto,
                            List<OrderPayApplyListVO> records,
                            HttpServletResponse response
//                            Map<Integer, UserInfoVO> finalAccountantCheckerMap,
//                            Map<Integer, UserInfoVO> finalCashierCheckerMap,
//                            Map<Integer, OrderPayApplyNodeRecordEntity> finalCollect
    ) {
        if (CollUtil.isNotEmpty(records)) {
            BigDecimal totalAmount = BigDecimal.ZERO;
// 客户经理id 提交人id List
            List<Integer> userIds = Stream.concat(
                            records.stream().filter(orderPayApplyListVO -> ObjUtil.isNotEmpty(orderPayApplyListVO.getManagerId()))
                                    .map(OrderPayApplyListVO::getManagerId),
                            records.stream().filter(orderPayApplyListVO -> ObjUtil.isNotEmpty(orderPayApplyListVO.getUserId()))
                                    .map(OrderPayApplyListVO::getUserId)
                    )
                    .distinct()
                    .toList();
            Map<Integer, UserInfoVO> userInfoMap = new HashMap<>();
            if (CollUtil.isNotEmpty(userIds)) {
                // 获取用户列表
                Result<List<UserInfoVO>> userInfoResult = userFeign.searchUserNameByUserIds(userIds);
                if (Result.isSuccess(userInfoResult) && ObjUtil.isNotEmpty(userInfoResult.getData())) {
                    userInfoMap = userInfoResult.getData().stream()
                            .collect(Collectors.toMap(UserInfoVO::getUserId, userInfoVO -> userInfoVO));
                }
            }
            for (OrderPayApplyListVO orderPayApplyListVO : records) {
                if (ObjUtil.isNotNull(orderPayApplyListVO.getPayeeAmount())) {
                    totalAmount = totalAmount.add(orderPayApplyListVO.getPayeeAmount());
                }
                if (orderPayApplyListVO.getUserId() != null && orderPayApplyListVO.getUserId() != 1) {
                    UserInfoVO userInfoVO = userInfoMap.get(orderPayApplyListVO.getUserId());
                    if (ObjUtil.isNotNull(userInfoVO)) {
                        orderPayApplyListVO.setUserName(userInfoVO.getName());
                    }
                } else {
                    orderPayApplyListVO.setUserName("系统自动提交");
                }
                if (StrUtil.isNotBlank(orderPayApplyListVO.getPaymentVoucherList())) {
                    // 将 JSON 字符串转换为 List<String>
                    List<String> paymentVoucherList = JSON.parseArray(orderPayApplyListVO.getPaymentVoucherList(), String.class);
                    orderPayApplyListVO.setResourceId(paymentVoucherList);
                }
                if (Objects.equals(orderPayApplyListVO.getFeeType(), OrderFeeDetailExpandTypeEnum.CURRENT_RETURN_PERFORMANCE.getCode())) {
                    if (ObjUtil.isNotEmpty(orderPayApplyListVO.getManagerId())) {
                        Integer managerId = orderPayApplyListVO.getManagerId();
                        UserInfoVO userInfoVO = userInfoMap.get(managerId);
                        if (ObjUtil.isNotNull(userInfoVO)) {
                            orderPayApplyListVO.setPayeeName(userInfoVO.getName());
                        }
                    }
                }
                orderPayApplyListVO.setBranchName(ObjUtil.defaultIfNull(orderPayApplyListVO.getStoreName(), ""));
            }
            List<Integer> teamIds = records.stream().map(OrderPayApplyListVO::getTeamId).filter(Objects::nonNull).toList();
            if (CollUtil.isNotEmpty(teamIds)) {
                List<DeptInfoVO> branchNameVOS = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData();
                Map<Integer, String> branchNameMap = branchNameVOS.stream().filter(e -> ObjUtil.isNotNull(e.getName())).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
                records.stream().filter(item -> item.getTeamId() != null).forEach(record -> {
                    record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(branchNameMap.get(record.getTeamId()), ""));
                });
            }
            //==========================================
            //会计审核人
            List<Integer> accountantCheckerList = records.stream()
                    .map(OrderPayApplyListVO::getAccountantChecker).filter(ObjUtil::isNotNull).toList();
            Map<Integer, UserInfoVO> accountantCheckerMap = new HashMap<>();
            if (CollUtil.isNotEmpty(accountantCheckerList)) {
                Result<List<UserInfoVO>> listResult = userFeign.searchUserNameByUserIds(accountantCheckerList);
                if (Result.isSuccess(listResult) && CollUtil.isNotEmpty(listResult.getData())) {
                    accountantCheckerMap = listResult.getData().stream()
                            .collect(Collectors.toMap(UserInfoVO::getUserId, Function.identity(), (existing, replacement) -> existing));
                }
            }
            Map<Integer, UserInfoVO> finalAccountantCheckerMap = accountantCheckerMap;
//出纳审核人
            List<Integer> cashierCheckerList = records.stream()
                    .map(OrderPayApplyListVO::getCashierChecker).filter(ObjUtil::isNotNull).toList();
            Map<Integer, UserInfoVO> cashierCheckerMap = new HashMap<>();
            if (CollUtil.isNotEmpty(cashierCheckerList)) {
                Result<List<UserInfoVO>> listResult = userFeign.searchUserNameByUserIds(cashierCheckerList);
                if (Result.isSuccess(listResult) && CollUtil.isNotEmpty(listResult.getData())) {
                    cashierCheckerMap = listResult.getData().stream()
                            .collect(Collectors.toMap(UserInfoVO::getUserId, Function.identity(), (existing, replacement) -> existing));
                }
            }
            Map<Integer, UserInfoVO> finalCashierCheckerMap = cashierCheckerMap;
//收款主体是客户的行号
//        List<String> customerList = records.stream()
//                .filter(record -> Objects.equals(record.getPayeeType(), PayApplicationPayeeTypeEnum.CUSTOMER.getCode()))
//                .map(OrderPayApplyListVO::getPayeeAccountNumber).filter(ObjUtil::isNotNull).toList();
//        Map<String, BankAccountSignEntity> customerCollectMap = new HashMap<>();
//        if (CollUtil.isNotEmpty(customerList)){
//            customerCollectMap = bankAccountSignMapper.selectList(
//                            new LambdaQueryWrapper<BankAccountSignEntity>()
//                                    .in(BankAccountSignEntity::getBankCardNumber, customerList)
//                                    .eq(BankAccountSignEntity::getDeleteFlag, 0)).stream()
//                    .collect(Collectors.toMap(BankAccountSignEntity::getBankCardNumber, Function.identity(), (existing, replacement) -> existing));
//        }
//        Map<String, BankAccountSignEntity> finalCustomerCollectMap = customerCollectMap;
//收款主体是汇丰员工的行号
//        List<String> businessPersonnelList = records.stream()
//                .filter(record -> Objects.equals(record.getPayeeType(), PayApplicationPayeeTypeEnum.BUSINESS_PERSONNEL.getCode()) && ObjectUtil.isNotEmpty(record.getManagerId()))
//                .map(OrderPayApplyListVO::getPayeeAccountNumber).filter(ObjUtil::isNotNull).toList();
//        Map<String, ManageBankAccountSignEntity> businessPersonnelCollectMap = new HashMap<>();
//        if (CollUtil.isNotEmpty(businessPersonnelList)){
//            businessPersonnelCollectMap = manageBankAccountSignMapper.selectList(
//                            new LambdaQueryWrapper<ManageBankAccountSignEntity>()
//                                    .in(ManageBankAccountSignEntity::getBankCardNumber, businessPersonnelList)
//                                    .eq(ManageBankAccountSignEntity::getDeleteFlag, 0)).stream()
//                    .collect(Collectors.toMap(ManageBankAccountSignEntity::getBankCardNumber, Function.identity(), (existing, replacement) -> existing));
//        }
//        Map<String, ManageBankAccountSignEntity> finalBusinessPersonnelCollectMap = businessPersonnelCollectMap;
// 出纳审核状态
            List<Integer> applyInfoIdList = records.stream()
                    .map(OrderPayApplyListVO::getId).filter(ObjUtil::isNotNull).toList();
            Map<Integer, OrderPayApplyNodeRecordEntity> collect = new HashMap<>();
            if (CollUtil.isNotEmpty(applyInfoIdList)) {
                collect = orderPayApplyNodeRecordMapper.selectList(
                                new LambdaQueryWrapper<OrderPayApplyNodeRecordEntity>()
                                        .in(OrderPayApplyNodeRecordEntity::getApplyInfoId, applyInfoIdList)
                                        .eq(OrderPayApplyNodeRecordEntity::getDeleteFlag, 0)
                                        .eq(OrderPayApplyNodeRecordEntity::getCurrentNode, PayApplicationNodeEnums.CASHIER_APPROVAL.getCode())).stream()
                        .collect(Collectors.toMap(OrderPayApplyNodeRecordEntity::getApplyInfoId, Function.identity(),
                                BinaryOperator.maxBy(Comparator.comparing(OrderPayApplyNodeRecordEntity::getCreateTime))));
            }
            Map<Integer, OrderPayApplyNodeRecordEntity> finalCollect = collect;
            // 创建字节数组输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
// 创建ExcelWriter对象，指定输出流
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            if (Objects.equals(dto.getFeeDetails(),1)){
                if (Objects.equals(dto.getFeeType().size(),1) && Objects.equals(dto.getFeeType().get(0), 2)) {
                    List<CashBackPerformanceExportVO> list = records.stream()
                            .map(exportVO -> {
                                CashBackPerformanceExportVO vo = new CashBackPerformanceExportVO();
                                vo.setOrderNumber(exportVO.getOrderNumber());
                                vo.setFeeType(exportVO.getFeeType());
                                vo.setFeeTypeToStr(ObjUtil.isNotEmpty(OrderFeeDetailExpandTypeEnum.fromCode(exportVO.getFeeType())) ? OrderFeeDetailExpandTypeEnum.fromCode(exportVO.getFeeType()).getDescription() : "");
                                vo.setVehicleNumber(exportVO.getVehicleNumber());
                                vo.setTransferTime(exportVO.getPaymentTime());
                                vo.setTransferTimeToStr(ObjUtil.isNotNull(exportVO.getPaymentTime()) ? exportVO.getPaymentTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                vo.setCashierApproveTime(exportVO.getCashierApproveTime());
                                vo.setCashierApproveTimeToStr(ObjUtil.isNotNull(exportVO.getCashierApproveTime()) ? exportVO.getCashierApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                vo.setCustomerName(exportVO.getCustomerName());
                                vo.setRegionName(exportVO.getRegionName());
                                vo.setStoreName(exportVO.getBranchName());
                                vo.setPaymentDetails(exportVO.getPaymentDetails());
                                vo.setFundName(exportVO.getFundName());
                                vo.setCashPerformance(exportVO.getCashPerformance() != null ? exportVO.getCashPerformance().multiply(new BigDecimal(100)) + "%" : "");
                                vo.setLoanTime(exportVO.getLoanTime());
                                vo.setLoanTimeToStr(ObjUtil.isNotNull(exportVO.getLoanTime()) ? exportVO.getLoanTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                vo.setProductName(exportVO.getProductName());
                                vo.setTerm(exportVO.getPeriod());
                                vo.setCustomerConfirmAmountToStr(ObjUtil.isNotNull(exportVO.getCustomerConfirmAmount()) ? String.valueOf(exportVO.getCustomerConfirmAmount()) : null);
                                vo.setTradeAmountToStr(ObjUtil.isNotNull(exportVO.getPayeeAmount()) ? String.valueOf(exportVO.getPayeeAmount()) : null);
                                vo.setRemark(exportVO.getRemark());
                                vo.setApplyType(exportVO.getApplyType());
                                vo.setApplyTypeToStr(exportVO.getApplyType() == 1 ? "付款单" : "收款单");
                                vo.setPayeeAccount(exportVO.getPayeeAccount());
                                vo.setPayeeAccountName(exportVO.getPayeeAccountName());
                                vo.setPayeeAccountNumber(exportVO.getPayeeAccountNumber());
                                vo.setPayeePhone(exportVO.getPayeePhone());
                                vo.setPayeeCardNumber(exportVO.getPayeeCardNumber());
                                vo.setPayAccount(exportVO.getPayAccount());
                                vo.setPayAccountNumber(exportVO.getPayAccountNumber());
                                vo.setPayAccountName(exportVO.getPayAccountName());
                                vo.setAuditStatus(PayApplicationNodeEnums.getDescriptionByCode(exportVO.getCurrentNode().getCode()));
                                vo.setSubmitTime(exportVO.getSubmitTime());
                                vo.setApplyRemark(exportVO.getRemark());
                                vo.setSubmitTimeToStr(exportVO.getSubmitTime() != null ? exportVO.getSubmitTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                vo.setAccountantUserName(finalAccountantCheckerMap.containsKey(exportVO.getAccountantChecker()) ? finalAccountantCheckerMap.get(exportVO.getAccountantChecker()).getName() : "");
                                vo.setAccountantApproveTime(exportVO.getAccountantApproveTime());
                                vo.setAccountantApproveTimeToStr(ObjUtil.isNotNull(exportVO.getAccountantApproveTime()) ? exportVO.getAccountantApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                vo.setAccountantRemark(exportVO.getAccountantRemark());
                                vo.setCashierChecker(finalCashierCheckerMap.containsKey(exportVO.getCashierChecker()) ? finalCashierCheckerMap.get(exportVO.getCashierChecker()).getName() : "");
                                vo.setCashierRemark(exportVO.getCashierRemark());
                                vo.setUserName(exportVO.getUserName());
                                vo.setUserRole(ObjUtil.isNotNull(exportVO.getUserId()) ? (exportVO.getUserId() == 1 ? "系统自动提交" : "线上运营人员") : "线上运营人员");
                                //                                        vo.setSummary(exportVO.getSummary());
                                return vo;
                            })
                            .toList();
                    // 创建WriteSheet对象，指定工作表名称、数据类和表头
                    WriteSheet writeSheet = EasyExcel.writerSheet("费用申请报表.xlsx")
                            .head(CashBackPerformanceExportVO.class)
                            .build();
                    // 写入实际数据到工作表
                    excelWriter.write(list, writeSheet);
//                if (CollUtil.isNotEmpty(list)) {
//                    // 创建合计行
//                    CashBackPerformanceExportVO totalRow = new CashBackPerformanceExportVO();
//                    totalRow.setOrderNumber("合计");
//                    // 计算金额合计
//                    BigDecimal tradeAmount = list.stream()
//                            .map(CashBackPerformanceExportVO::getTradeAmount)
//                            .filter(Objects::nonNull)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//                    totalRow.setTradeAmountToStr(String.valueOf(tradeAmount));
//                    // 写入合计行
//                    excelWriter.write(Collections.singletonList(totalRow), writeSheet);
//                }
                    int windowSize = CollUtil.isNotEmpty(list) ? list.size() : 100;
                    SXSSFWorkbook workbook = new SXSSFWorkbook(windowSize);
                    SXSSFSheet sheet;
                    if (workbook.getNumberOfSheets() > 0) {
                        sheet = workbook.getSheetAt(0);
                    } else {
                        sheet = workbook.createSheet("现返绩效");
                    }
                    adjustSheet1(sheet, windowSize,workbook);
                    workbook.dispose();
                }else {
//                        ExpenseApplicationExportVO
                    List<ExpenseApplicationExportVO> list = records.stream()
                            .map(exportVO -> {
                                ExpenseApplicationExportVO vo = new ExpenseApplicationExportVO();
//                                    vo.setOrderNumber(exportVO.getOrderNumber());
                                vo.setFeeType(exportVO.getFeeType());
                                vo.setFeeTypeToStr(ObjUtil.isNotEmpty(OrderFeeDetailExpandTypeEnum.fromCode(exportVO.getFeeType())) ? OrderFeeDetailExpandTypeEnum.fromCode(exportVO.getFeeType()).getDescription() : "");
                                vo.setVehicleNumber(exportVO.getVehicleNumber());
                                vo.setTransferTime(exportVO.getPaymentTime());
                                vo.setTransferTimeToStr(ObjUtil.isNotNull(exportVO.getPaymentTime()) ? exportVO.getPaymentTime().toLocalDate().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)) : "");
                                vo.setCashierApproveTime(exportVO.getCashierApproveTime());
                                vo.setCashierApproveTimeToStr(ObjUtil.isNotNull(exportVO.getCashierApproveTime()) ? exportVO.getCashierApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                vo.setCustomerName(exportVO.getCustomerName());
//                                    vo.setRegionName(exportVO.getRegionName());
//                                    vo.setStoreName(exportVO.getStoreName());
                                vo.setPaymentDetails(exportVO.getPaymentDetails());
                                vo.setFundName(exportVO.getFundName());
//                                    vo.setCashPerformance(exportVO.getCashPerformance() != null ? exportVO.getCashPerformance().multiply(new BigDecimal(100)) + "%" : "");
//                                    vo.setLoanTime(exportVO.getLoanTime());
//                                    vo.setLoanTimeToStr(ObjUtil.isNotNull(exportVO.getLoanTime()) ? exportVO.getLoanTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
//                                    vo.setProductName(exportVO.getProductName());
//                                    vo.setTerm(exportVO.getPeriod());
//                                    vo.setCustomerConfirmAmount(exportVO.getCustomerConfirmAmount());
                                vo.setTradeAmountToStr(ObjUtil.isNotNull(exportVO.getPayeeAmount()) ? String.valueOf(exportVO.getPayeeAmount()) : null);
                                vo.setTradeAmount(exportVO.getPayeeAmount());
                                vo.setRemark(exportVO.getRemark());
                                vo.setApplyType(exportVO.getApplyType());
                                vo.setApplyTypeToStr(exportVO.getApplyType() == 1 ? "付款单" : "收款单");
                                vo.setPayeeAccount(exportVO.getPayeeAccount());
                                vo.setPayeeAccountName(exportVO.getPayeeAccountName());
                                vo.setPayeeAccountNumber(exportVO.getPayeeAccountNumber());
                                vo.setPayeeAccountOpeningBranch(exportVO.getPayeeAccountName());
//                                    vo.setPayeePhone(exportVO.getPayeePhone());
//                                    vo.setPayeeCardNumber(exportVO.getPayeeCardNumber());
                                vo.setPayAccount(exportVO.getPayAccount());
                                vo.setPayAccountNumber(exportVO.getPayAccountNumber());
                                vo.setPayAccountName(exportVO.getPayAccountName());
//                                    vo.setAuditStatus(PayApplicationNodeEnums.getDescriptionByCode(exportVO.getCurrentNode().getCode()));
                                vo.setAuditStatus(finalCollect.containsKey(exportVO.getId()) ? (
                                        switch (finalCollect.get(exportVO.getId()).getEvent()){
                                            case APPROVE_PASS -> "通过";
                                            case APPROVE_REJECT -> "拒绝";
                                            case APPROVE_REVOKE -> "驳回";
                                        case REDIRECTED -> "转交";
                                        }) : "");
                                vo.setSubmitTime(exportVO.getSubmitTime());
                                vo.setApplyRemark(exportVO.getRemark());
                                vo.setSubmitTimeToStr(exportVO.getSubmitTime() != null ? exportVO.getSubmitTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                vo.setAccountantUserName(finalAccountantCheckerMap.containsKey(exportVO.getAccountantChecker()) ? finalAccountantCheckerMap.get(exportVO.getAccountantChecker()).getName() : "");
                                vo.setAccountantApproveTime(exportVO.getAccountantApproveTime());
                                vo.setAccountantApproveTimeToStr(ObjUtil.isNotNull(exportVO.getAccountantApproveTime()) ? exportVO.getAccountantApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                vo.setAccountantRemark(exportVO.getAccountantRemark());
                                vo.setCashierChecker(finalCashierCheckerMap.containsKey(exportVO.getCashierChecker()) ? finalCashierCheckerMap.get(exportVO.getCashierChecker()).getName() : "");
                                vo.setCashierRemark(exportVO.getCashierRemark());
                                vo.setUserName(exportVO.getUserName());
                                vo.setUserRole(ObjUtil.isNotNull(exportVO.getUserId()) ? (exportVO.getUserId() == 1 ? "系统自动提交" : "线上运营人员") : "线上运营人员");
                                vo.setSummary(exportVO.getSummary());
                                return vo;
                            })
                            .toList();
                    // 创建WriteSheet对象，指定工作表名称、数据类和表头
                    WriteSheet writeSheet = EasyExcel.writerSheet("费用申请报表.xlsx")
                            .head(ExpenseApplicationExportVO.class)
                            .build();
                    // 写入实际数据到工作表
                    excelWriter.write(list, writeSheet);
                    if (CollUtil.isNotEmpty(list)) {
                        // 创建合计行
                        ExpenseApplicationExportVO totalRow = new ExpenseApplicationExportVO();
                        totalRow.setPaymentDetails("合计");
                        // 计算金额合计
                        BigDecimal tradeAmount = list.stream()
                                .map(ExpenseApplicationExportVO::getTradeAmount)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        totalRow.setTradeAmountToStr(String.valueOf(tradeAmount));
                        // 写入合计行
                        excelWriter.write(Collections.singletonList(totalRow), writeSheet);
                    }
                    int windowSize = CollUtil.isNotEmpty(list) ? list.size() : 100;
                    SXSSFWorkbook workbook = new SXSSFWorkbook(windowSize);
                    SXSSFSheet sheet;
                    if (workbook.getNumberOfSheets() > 0) {
                        sheet = workbook.getSheetAt(0);
                    } else {
                        sheet = workbook.createSheet("费用申请");
                    }
                    adjustSheet1(sheet, windowSize,workbook);
                    workbook.dispose();
                }
            }
            if (Objects.equals(dto.getFeeDetails(),2)) {
                List<PayApplicationPageListExportVO> list = records.stream()
                        .map(exportVO -> {
                            PayApplicationPageListExportVO vo = new PayApplicationPageListExportVO();
                            vo.setId(exportVO.getId());
                            vo.setOrderId(exportVO.getOrderId());
                            vo.setPayeeAccount(exportVO.getPayeeAccount());
                            vo.setOrderNumber(exportVO.getOrderNumber());
                            vo.setCustomerName(exportVO.getCustomerName());
                            vo.setVehicleNumber(exportVO.getVehicleNumber());
                            vo.setRegionName(exportVO.getRegionName());
                            vo.setStoreName(exportVO.getBranchName());
                            vo.setUserName(exportVO.getUserName());
                            vo.setFeeType(exportVO.getFeeType());
                            vo.setFeeTypeToStr(ObjUtil.isNotEmpty(OrderFeeDetailExpandTypeEnum.fromCode(exportVO.getFeeType())) ? OrderFeeDetailExpandTypeEnum.fromCode(exportVO.getFeeType()).getDescription() : "");
                            vo.setPayeeAmount(exportVO.getPayeeAmount());
                            vo.setPayeeAmountToStr(String.valueOf(exportVO.getPayeeAmount()));
                            vo.setPayeeType(exportVO.getPayeeType());
                            vo.setPayeeTypeToStr(ObjUtil.defaultIfNull(PayApplicationPayeeTypeEnum.fromCode(exportVO.getPayeeType()).getDesc(), ""));
                            vo.setPayAccount(exportVO.getPayAccount());
                            vo.setPayeeAccountNumber(exportVO.getPayeeAccountNumber());
                            vo.setRemark(exportVO.getRemark());
                            vo.setCurrentNode(exportVO.getCurrentNode());
                            vo.setCurrentNodeToStr(PayApplicationNodeEnums.getDescriptionByCode(exportVO.getCurrentNode().getCode()));
                            vo.setPaymentTime(exportVO.getPaymentTime());
                            vo.setPaymentTimeToStr(ObjUtil.isNotEmpty(exportVO.getPaymentTime()) ? exportVO.getPaymentTime().toLocalDate().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)) : "");
                            vo.setCashierApproveTime(exportVO.getCashierApproveTime());
                            vo.setCashierApproveTimeToStr(ObjUtil.isNotEmpty(exportVO.getCashierApproveTime()) ? exportVO.getCashierApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                            vo.setSubmitTime(exportVO.getSubmitTime());
                            vo.setSubmitTimeToStr(ObjUtil.isNotEmpty(exportVO.getSubmitTime()) ? exportVO.getSubmitTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                            vo.setTeamId(exportVO.getTeamId());
                            vo.setBranchName(exportVO.getBranchName());
                            vo.setPaySnNumBack(exportVO.getPaySnNumBack());
                            vo.setManagerId(exportVO.getManagerId());
                            vo.setManagerName(exportVO.getManagerName());
                            vo.setPayeeAccountName(exportVO.getPayeeAccountName());
                            vo.setProductId(exportVO.getProductId());
                            vo.setPaymentDetails(exportVO.getPaymentDetails());
                            vo.setUserId(exportVO.getUserId());
                            vo.setPayAccountName(exportVO.getPayAccountName());
                            vo.setPayAccountNumber(exportVO.getPayAccountNumber());
                            vo.setResourceId(exportVO.getResourceId());
                            vo.setPaymentVoucherList(exportVO.getPaymentVoucherList());
                            vo.setAccountantRemark(exportVO.getAccountantRemark());
                            vo.setCashierRemark(exportVO.getCashierRemark());
                            vo.setFundName(exportVO.getFundName());
                            vo.setPayeeAccountBranchName(exportVO.getPayeeAccountBranchName());
                            vo.setPayeePhone(exportVO.getPayeePhone());
                            vo.setPayeeName(exportVO.getPayeeName());
                            vo.setPayeeCardNumber(exportVO.getPayeeCardNumber());
                            vo.setAccountantApproveTime(exportVO.getAccountantApproveTime());
                            vo.setAccountantChecker(exportVO.getAccountantChecker());
                            vo.setCashierChecker(exportVO.getCashierChecker());
                            vo.setApplyType(exportVO.getApplyType());
                            vo.setFundId(exportVO.getFundId());
                            vo.setSummary(exportVO.getSummary());
                            vo.setFeeDetails(exportVO.getFeeDetails());
                            vo.setProductName(exportVO.getProductName());
                            vo.setOrderSource(exportVO.getOrderSource());
                            vo.setOrderApplicationSource(exportVO.getOrderApplicationSource());
                            vo.setRepaymentTerm(exportVO.getRepaymentTerm());
                            return vo;
                        })
                        .toList();
                // 创建WriteSheet对象，指定工作表名称、数据类和表头
                WriteSheet writeSheet = EasyExcel.writerSheet("查账申请报表.xlsx")
                        .head(PayApplicationPageListExportVO.class)
                        .build();
                // 写入实际数据到工作表
                excelWriter.write(list, writeSheet);
                if (CollUtil.isNotEmpty(list)) {
                    // 创建合计行
                    PayApplicationPageListExportVO totalRow = new PayApplicationPageListExportVO();
                    totalRow.setOrderNumber("合计");
                    // 计算金额合计
                    BigDecimal amountTotal = list.stream()
                            .map(PayApplicationPageListExportVO::getPayeeAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    totalRow.setPayeeAmountToStr(amountTotal.toString());
                    // 写入合计行
                    excelWriter.write(Collections.singletonList(totalRow), writeSheet);
                }
                int windowSize = CollUtil.isNotEmpty(list) ? list.size() : 100;
                SXSSFWorkbook workbook = new SXSSFWorkbook(windowSize);
                SXSSFSheet sheet;
                if (workbook.getNumberOfSheets() > 0) {
                    sheet = workbook.getSheetAt(0);
                } else {
                    sheet = workbook.createSheet("查账申请");
                }
                adjustSheet1(sheet, windowSize,workbook);
                workbook.dispose();
            }
            excelWriter.finish();
// 将字节数组输出流转换为字节数组
            byte[] bytes = outputStream.toByteArray();
            // 设置文件下载头
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + URLEncoder.encode((Objects.equals(dto.getFeeDetails(),1) ? "费用申请" : "查账申请") +"报表导出"+".xlsx", StandardCharsets.UTF_8) + "\"");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            // 获取HttpServletResponse的输出流并写入字节数组
            try (ServletOutputStream outputStream1 = response.getOutputStream()) {
                outputStream1.write(bytes);
                outputStream1.flush();
            } catch (IOException e) {
                // 处理异常
                e.printStackTrace();
            }
        }

    }


    @Override
    public Boolean autoRecognize(MultipartFile file) {
        try {
            List<AutoRecognizeDTO> autoRecognizeDTOList = EasyExcel.read(file.getInputStream())
                    .head(AutoRecognizeDTO.class)
                    .sheet(0)
                    .doReadSync();
            log.info("OrderPayApplicationPriveteMethodImpl.autoRecognize autoRecognizeDTOList = " + autoRecognizeDTOList);
            List<OrderPayApplicationInfoEntity> orderPayApplicationInfoEntityList = orderPayApplicationMapper.selectList(
                    new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                            .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.CASHIER_APPROVAL)
                            .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                            .eq(OrderPayApplicationInfoEntity::getFeeDetails, 2)
                            .orderByDesc(OrderPayApplicationInfoEntity::getCreateTime)
            );
            AutomaticRecognitionInfoEntity infoEntity1 = automaticRecognitionInfoService.getOne(
                    new LambdaQueryWrapper<AutomaticRecognitionInfoEntity>()
                            .eq(AutomaticRecognitionInfoEntity::getDeleteFlag, 0)
                            .orderByDesc(AutomaticRecognitionInfoEntity::getIndex)
                            .last("limit 1")
            );
            List<AutomaticRecognitionInfoEntity> list = new ArrayList<>();
            List<AutoRecognizeDTO> matchedList = autoRecognizeDTOList.stream()
                    .filter(autoDto -> orderPayApplicationInfoEntityList.stream()
                            .anyMatch(entity -> {
                                if (Objects.equals(entity.getPayAccount(),"guoguo")){
                                    System.out.println("entity = " + entity);
                                }
                                // 检查时间是否相等
                                boolean timeMatch = false;
                                if (StringUtils.hasText(autoDto.getPaymentTime()) && ObjUtil.isNotNull(entity.getPaymentTime())) {
                                    try {
                                        LocalDate tradeTime = LocalDateTime.parse(autoDto.getPaymentTime(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)).toLocalDate();
                                        timeMatch = Objects.equals(tradeTime, entity.getPaymentTime().toLocalDate());
                                    } catch (Exception e) {
                                        log.info("OrderPayApplicationPriveteMethodImpl.autoRecognize tradeTime:{} entity.getPaymentTime:{} error:{}", autoDto.getPaymentTime(), entity.getPaymentTime(), e.getMessage());
                                    }
                                }
                                // 检查金额是否相等
                                boolean amountMatch = false;
                                if (StringUtils.hasText(autoDto.getPayeeAmount()) && ObjUtil.isNotNull(entity.getPayeeAmount())) {
                                    try {
                                        BigDecimal income = new BigDecimal(autoDto.getPayeeAmount());
                                        amountMatch = Objects.equals(income.compareTo(entity.getPayeeAmount()),0);
                                    } catch (NumberFormatException e) {
                                        log.info("OrderPayApplicationPriveteMethodImpl.autoRecognize income:{} entity.getPayeeAmount():{} error:{}",autoDto.getPayeeAmount(),entity.getPayeeAmount(), e.getMessage());
                                    }
                                }
                                boolean OtherAccountMatch = false;
                                if (StringUtils.hasText(autoDto.getPayAccount()) && StringUtils.hasText(entity.getPayAccount())) {
                                    try {
                                        OtherAccountMatch = Objects.equals(autoDto.getPayAccount(),entity.getPayAccount());
                                    } catch (Exception e) {
                                        log.info("OrderPayApplicationPriveteMethodImpl.autoRecognize autoDto.getPayAccount():{} entity.getPayAccount():{} error:{}",autoDto.getPayAccount(),entity.getPayAccount(), e.getMessage());
                                    }
                                }
                                if(timeMatch && amountMatch && OtherAccountMatch){
                                    entity.setCurrentNode(PayApplicationNodeEnums.SUCCESS);
                                    entity.setAccountantApproveTime(LocalDateTime.now());
                                    entity.setAccountantRemark("自动审批，审批通过");
                                    orderPayApplicationMapper.updateById(entity);
                                    OrderPayApplyNodeRecordEntity newRecord = new OrderPayApplyNodeRecordEntity();
                                    newRecord.setApplyInfoId(entity.getId());
                                    newRecord.setCurrentNode(PayApplicationNodeEnums.CASHIER_APPROVAL);
                                    newRecord.setEvent(PayApplicationEventEnums.APPROVE_PASS);
                                    newRecord.setNextNode(PayApplicationNodeEnums.SUCCESS);
                                    orderPayApplyNodeRecordMapper.insert(newRecord);
                                    AutomaticRecognitionInfoEntity infoEntity = new AutomaticRecognitionInfoEntity();
                                    infoEntity.setApplyInfoId(entity.getId());
                                    infoEntity.setPayeeAccount(autoDto.getPayeeAccount());
                                    infoEntity.setPaymentTime(LocalDateTime.parse(autoDto.getPaymentTime(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)).toLocalDate());
                                    infoEntity.setPayeeAccountNumber(autoDto.getPayeeAccountNumber());
                                    infoEntity.setPayeeAmount(new BigDecimal(autoDto.getPayeeAmount()));
                                    infoEntity.setPayAccount(autoDto.getPayAccount());
                                    infoEntity.setAuditResult(1);
                                    infoEntity.setIndex(ObjUtil.isNotNull(infoEntity1) ? infoEntity1.getIndex()+1 : 1);
                                    list.add(infoEntity);
                                }
                                return timeMatch && amountMatch && OtherAccountMatch;
                            }))
                    .toList();
            try {
                Set<AutoRecognizeDTO> matchedSet = new HashSet<>(matchedList);
                List<AutoRecognizeDTO> unmatchedList = autoRecognizeDTOList.stream()
                        .filter(dto -> !matchedSet.contains(dto))
                        .toList();
                log.info("OrderPayApplicationPriveteMethodImpl.autoRecognize no unmatchedList:{}",unmatchedList);
                if (CollUtil.isNotEmpty(unmatchedList)){
                    list.addAll(unmatchedList.stream().map(dto -> {
                        AutomaticRecognitionInfoEntity infoEntity = new AutomaticRecognitionInfoEntity();
                        infoEntity.setPayeeAccount(dto.getPayeeAccount());
                        infoEntity.setPaymentTime(LocalDate.parse(dto.getPaymentTime()));
                        infoEntity.setPayeeAccountNumber(dto.getPayeeAccountNumber());
                        infoEntity.setPayeeAmount(new BigDecimal(dto.getPayeeAmount()));
                        infoEntity.setPayAccount(dto.getPayAccount());
                        infoEntity.setAuditResult(2);
                        infoEntity.setIndex(ObjUtil.isNotNull(infoEntity1) ? infoEntity1.getIndex()+1 : 1);
                        return infoEntity;
                    }).toList());
                }
            } catch (Exception e) {
                log.info("OrderPayApplicationPriveteMethodImpl.autoRecognize unmatchedList error:{}",e.getMessage());
            }
            return automaticRecognitionInfoService.saveBatch(list);
        } catch (IOException e) {
            log.error("OrderPayApplicationPriveteMethodImpl.autoRecognize error:{}", e.getMessage());
            throw new BusinessException("读取Excel文件失败");
        }
    }

    @Override
    public void autoRecognizeExport(HttpServletResponse response) {
        List<AutomaticRecognitionInfoEntity> maxIndexRecords = automaticRecognitionInfoService.list(
                new LambdaQueryWrapper<AutomaticRecognitionInfoEntity>()
                        .eq(AutomaticRecognitionInfoEntity::getDeleteFlag, 0)
                        .inSql(AutomaticRecognitionInfoEntity::getIndex,
                                "SELECT MAX(index) FROM lh_automatic_recognition_info WHERE delete_flag = 0")
        );
        if (CollUtil.isNotEmpty(maxIndexRecords)){
            List<AutoRecognizeExportVO> list = maxIndexRecords.stream().map(item -> {
                AutoRecognizeExportVO vo = new AutoRecognizeExportVO();
                vo.setPayeeAccount(item.getPayeeAccount());
                vo.setPayeeAccountNumber(item.getPayeeAccountNumber());
                vo.setPaymentTime(item.getPaymentTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
                vo.setPayeeAmount(String.valueOf(item.getPayeeAmount()));
                vo.setPayAccount(item.getPayAccount());
                vo.setApprovalResult(Objects.equals(item.getAuditResult(), 1) ? "通过" : "未通过");
                return vo;
            }).toList();
            // 创建字节数组输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            // 创建ExcelWriter对象，指定输出流
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            // 创建WriteSheet对象，指定工作表名称、数据类和表头
            WriteSheet writeSheet = EasyExcel.writerSheet()
                    .head(AutoRecognizeExportVO.class)
                    .build();
            // 写入实际数据到工作表
            excelWriter.write(list, writeSheet);

            // 获取Workbook对象
            Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
            // 获取Sheet对象
            Sheet sheet = workbook.getSheetAt(0);
            // 调整列宽和行高
//            adjustSheet(sheet);
            CostDisclosureUtil.createExcel(workbook.getSheetAt(0), workbook);
            excelWriter.finish();
            // 将字节数组输出流转换为字节数组
            byte[] bytes = outputStream.toByteArray();
            // 设置文件下载头
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + URLEncoder.encode("审批结果.xlsx", StandardCharsets.UTF_8) + "\"");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            // 获取HttpServletResponse的输出流并写入字节数组
            try (ServletOutputStream outputStream1 = response.getOutputStream()) {
                outputStream1.write(bytes);
                outputStream1.flush();
            } catch (IOException e) {
                // 处理异常
                e.printStackTrace();
            }
        }else {
            throw new RuntimeException("暂无数据");
        }

    }

    public Map<String, String> deserializeApprovalDetails(String jsonString) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(jsonString, new TypeReference<Map<String, String>>() {});
        } catch (Exception e) {
            log.error("Failed to deserialize approval details JSON string: {}", jsonString, e);
            return null;
        }
    }

    private static LocalDateTime getTimeOfEnd(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.MAX);
    }

    private static LocalDateTime getTimeOfStart(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.MIN);
    }

    private void adjustSheet(Sheet sheet) {
        // 跟踪所有列
        if (sheet instanceof SXSSFSheet) {
            ((SXSSFSheet) sheet).trackAllColumnsForAutoSizing();
        }
        // 获取最大行数
        int rowCount = sheet.getLastRowNum();
        // 创建一个数组来存储每列的最大宽度
        int[] maxWidths = new int[sheet.getRow(0).getLastCellNum()];
        // 遍历每一行，计算每列的最大宽度
        for (int rowIndex = 0; rowIndex <= rowCount; rowIndex++) {
            Row currentRow = sheet.getRow(rowIndex);
            if (currentRow != null) {
                for (int cellIndex = 0; cellIndex < currentRow.getLastCellNum(); cellIndex++) {
                    Cell cell = currentRow.getCell(cellIndex);
                    if (cell != null) {
                        String cellValue = "";
                        switch (cell.getCellType()) {
                            case STRING:
                                cellValue = cell.getStringCellValue();
                                break;
                            case NUMERIC:
                                if (DateUtil.isCellDateFormatted(cell)) {
                                    cellValue = cell.getDateCellValue().toString();
                                } else {
                                    cellValue = String.valueOf(cell.getNumericCellValue());
                                }
                                break;
                            case BOOLEAN:
                                cellValue = String.valueOf(cell.getBooleanCellValue());
                                break;
                            case FORMULA:
                                cellValue = cell.getCellFormula();
                                break;
                            default:
                                cellValue = "";
                        }
                        int cellWidth = (cellValue.length()+4) * 256; // 256 units is approximately 1 character width
                        maxWidths[cellIndex] = Math.max(maxWidths[cellIndex], cellWidth);
                    }
                }
            }
        }
        // 设置每列的宽度
        for (int i = 0; i < maxWidths.length; i++) {
            sheet.setColumnWidth(i, maxWidths[i]);
        }
        // 设置所有行的高度
        int rowHeight = 25;
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            if (i!=0){
                row.setHeightInPoints(rowHeight);
            }else {
                row.setHeightInPoints(rowHeight+15);
            }
        }
    }

    private void adjustSheet1(Sheet sheet, Integer size, SXSSFWorkbook workbook){
        if (sheet instanceof SXSSFSheet) {
            ((SXSSFSheet) sheet).setRandomAccessWindowSize(size);
            ((SXSSFSheet) sheet).trackAllColumnsForAutoSizing();
        }
        // 获取最大行数
        int rowCount = sheet.getLastRowNum();
        // 创建一个数组来存储每列的最大宽度
        int maxCellCount = 0;
        for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row currentRow = sheet.getRow(rowIndex);
            if (currentRow != null) {
                int lastCellNum = currentRow.getLastCellNum();
                if (lastCellNum > maxCellCount) {
                    maxCellCount = lastCellNum;
                }
            }
        }
        int[] maxWidths = new int[maxCellCount];
        // 遍历每一行，计算每列的最大宽度
        for (int rowIndex = 0; rowIndex <= rowCount; rowIndex++) {
            Row currentRow = sheet.getRow(rowIndex);
            if (currentRow != null) {
                for (int cellIndex = 0; cellIndex < currentRow.getLastCellNum(); cellIndex++) {
                    Cell cell = currentRow.getCell(cellIndex);
                    if (cell != null) {
                        // 获取单元格的值
                        String cellValue = CostDisclosureUtil.getCellValue(cell);
                        // 计算单元格宽度，并更新最大宽度数组
                        int cellWidth = (cellValue.length() + 8) * 256; // 256 units is approximately 1 character width
                        maxWidths[cellIndex] = Math.max(maxWidths[cellIndex], cellWidth);
                    }
                }
            }
        }

        // 设置每列的宽度
        for (int i = 0; i < maxWidths.length; i++) {
            sheet.setColumnWidth(i, maxWidths[i]);
        }
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);

        // 创建一个新的单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setWrapText(true); // 启用自动换行
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        int rowHeight = 25;
        int lastRowNum = sheet.getLastRowNum();
        for (int i = 0; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            // 设置行高
            row.setHeightInPoints(i == 0 ? rowHeight + 15 : rowHeight);
            row.setRowStyle(cellStyle);
        }
    }
}
