package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.approve.api.constants.ChangYinPreLoanStatusEnum;
import com.longhuan.approve.api.constants.ContractTypeEnums;
import com.longhuan.approve.api.pojo.dto.FuMinPreviewContractDTO;
import com.longhuan.approve.api.pojo.dto.changyin.ChangYinLoanApplicationRespDTO;
import com.longhuan.approve.api.pojo.dto.changyin.ChangYinPreLoanQueryResDTO;
import com.longhuan.approve.api.pojo.dto.changyin.ChangYinResBodyDTO;
import com.longhuan.approve.api.pojo.dto.lanhai.LanHaiContractSignApplyDTO;
import com.longhuan.approve.api.pojo.dto.lanhai.LanHaiContractSignDownloadDTO;
import com.longhuan.approve.api.pojo.dto.lanhai.LanHaiContractSignFileDTO;
import com.longhuan.approve.api.pojo.dto.zhongheng.hengtong.HengTongPreRepayPlanDTO;
import com.longhuan.approve.api.pojo.vo.FMContractPreviewVO;
import com.longhuan.approve.api.pojo.vo.YingFengInfoVO;
import com.longhuan.approve.api.pojo.vo.changyin.ChangYinContractPreviewVO;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiContractSignApplyResponse;
import com.longhuan.approve.api.pojo.vo.yingfeng.YingFengQuerySignResultVO;
import com.longhuan.approve.api.pojo.vo.yingfeng.YingFengSendSignVO;
import com.longhuan.approve.api.pojo.vo.zhongheng.HengTongPreRepayPlanVO;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.enums.SignPlateEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.core.result.ResultCode;
import com.longhuan.common.core.util.NumberUtils;
import com.longhuan.common.redis.constants.SwitchConstants;
import com.longhuan.common.redis.pojo.SwitchVO;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.order.config.FadadaConfig;
import com.longhuan.order.converter.ContractToFundConverter;
import com.longhuan.order.enums.ContractEnum;
import com.longhuan.order.enums.ContractRestartEnum;
import com.longhuan.order.enums.FundSignStatusEnum;
import com.longhuan.order.enums.TemplateParamsCodeEnum;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.feign.FadadaFeign;
import com.longhuan.order.feign.ResourceFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.*;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.resource.pojo.dto.DownloadContractDTO;
import com.longhuan.resource.pojo.vo.DownLoadContractVO;
import com.longhuan.resource.pojo.vo.FileResourceResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.longhuan.common.core.enums.ParamsSnapshotEnum.*;
import static com.longhuan.order.service.ContractFileService.formatDate;

/**
 * 合同服务实施
 *
 * <AUTHOR>
 * @date 2024/08/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContractServiceImpl implements ContractService {
    private final OrderInfoMapper orderInfoMapper;
    private final OrderContractMapper orderContractMapper;
    private final ResourceFeign resourceFeign;
    private final ContractToFundMapper contractToFundMapper;
    private final ContractToFundConverter contractToFundConverter;
    private final OrderStateService orderStateService;
    private final ContractRestartSignMapper contractRestartSignMapper;
    private final ParamsSnapshotMapper paramsSnapshotMapper;

    private final ApproveFeign approveFeign;
    private final FundTemplateAssoMapper fundTemplateAssoMapper;
    private final FileTemplateInfoMapper fileTemplateInfoMapper;
    private final FundSignInfoMapper fundSignInfoMapper;
    private final ContractRestartFileMapper contractRestartFileMapper;
    private final FundInfoMapper fundInfoMapper;
    private final FadadaConfig fadadaConfig;
    private final FileToFddMapper fileToFddMapper;
    private final FadadaFeign fadadaFeign;
    private final FileResourceMapper fileResourceMapper;
    private final FileToFddService fileToFddService;
    private final FadadaAuthService fadadaAuthService;
    private final FundSignDetailMapper fundSignDetailMapper;
    private final ContractFileService contractFileService;
    private final OrderSendMessage orderSendMessage;
    private final BankAccountSignMapper bankAccountSignMapper;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final RedisService redisService;
    private final SignTaskMapper signTaskMapper;
    private final OrderParamsSnapshotService orderParamsSnapshotService;
    private final OrderArrivedMapper orderArrivedMapper;
    private final OrderVehicleInfoService orderVehicleInfoService;
    private final ProductInfoMapper productInfoMapper;
    private final OrderVehicleInfoMapper orderVehicleInfoMapper;
    private final FundProductMappingMapper fundProductMappingMapper;
    private final EnvUtil envUtil;
    private final OrderFeeInfoMapper orderFeeInfoMapper;

    @Value("${digitalize.HmacSHA256Key}")
    private String digitalizeKey;


    private final SwitchUtils switchUtils;
    private final static int DOWNLOAD_BATCH_SIZE = 1;

    /**
     * 列表
     *
     * @param contractList 合同清单
     * @return {@link Page }<{@link ContractListVO }>
     */
    @Override
    public Page<ContractListVO> list(ContractListDTO contractList) {
        Integer orderId = contractList.getOrderId();
        String name = contractList.getName();
        String number = contractList.getNumber();
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getId, orderId)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
        );
        Assert.notNull(orderInfoEntity, "订单不存在");
        Integer fundId = orderInfoEntity.getFundId();

        Integer contractState = orderInfoEntity.getContractState();

        //获取当前订单所有的合同
        Page<ContractListVO> contractListPage = orderContractMapper.selectJoinPage(new Page<>(contractList.getPageNum(), contractList.getPageSize()),
                ContractListVO.class,
                new MPJLambdaWrapper<OrderContractEntity>()
                        .select(OrderContractEntity::getId,
                                OrderContractEntity::getName,
                                OrderContractEntity::getNumber,
                                OrderContractEntity::getSignStatus,
                                OrderContractEntity::getResource,
                                OrderContractEntity::getContractFlag,
                                OrderContractEntity::getTemplateId)
                        .selectAs(FundTemplateAssoEntity::getFundId, ContractListVO::getFundId)
                        .innerJoin(FundTemplateAssoEntity.class, on -> on
                                .eq(FundTemplateAssoEntity::getTemplateId, OrderContractEntity::getTemplateId)
                                .eq(FundTemplateAssoEntity::getFundId, fundId)
                        )
                        .eq(OrderContractEntity::getOrderId, orderId)
                        .notIn(OrderContractEntity::getContractFlag,3,4 )
                        .like(!StrUtil.isEmpty(name), OrderContractEntity::getName, name)
                        .like(!StrUtil.isEmpty(number), OrderContractEntity::getNumber, number)
                        .eq(OrderContractEntity::getDeleteFlag, 0)
                        .orderByDesc(FundTemplateAssoEntity::getSort)
        );
        List<ContractListVO> records = contractListPage.getRecords();

        //        records.forEach(record -> record.setSignFlag(records.stream().anyMatch(item -> Objects.equals(item.getSignStatus(), ContractEnum.WAITING.getCode()))));
        //是否签署中
        records.forEach(record -> record.setIsSigning(records.stream().anyMatch(item -> Objects.equals(item.getSignStatus(), ContractEnum.SIGNING.getCode()))));

        //是否签署完成
        List<OrderContractEntity> signedList = orderContractMapper.selectList(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getSignStatus, ContractEnum.SIGNED.getCode())
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getDeleteFlag, 0));

        //查询全部合同
        List<OrderContractEntity> contractEntityList = orderContractMapper.selectList(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getDeleteFlag, 0));

        if(signedList.size() == contractEntityList.size()){
            records.forEach(record -> record.setIsSigned(true));
        }else {
            records.forEach(record -> record.setIsSigned(false));
        }

        return contractListPage;
    }


    @Override
    public List<ContractResourceVO> getContractUrls(ContractUrlsDTO contractUrlsDTO) {
        //查询资方需要签署的合同
        List<ContractToFundEntity> contractToFundList = contractToFundMapper.selectList(new LambdaQueryWrapper<ContractToFundEntity>()
                .eq(ContractToFundEntity::getOrderId, contractUrlsDTO.getOrderId())
                .eq(ContractToFundEntity::getFundId, contractUrlsDTO.getFundId())
                .in(CollUtil.isNotEmpty(contractUrlsDTO.getContractNameList()), ContractToFundEntity::getOriginalFileName, contractUrlsDTO.getContractNameList())
                .eq(ContractToFundEntity::getDeleteFlag, 0)
                .eq(ContractToFundEntity::getIsSend, 1)
                .orderByDesc(ContractToFundEntity::getCreateTime));
        log.info("ContractServiceImpl.getContractUrls contractToFundList:{}", contractToFundList);

        List<ContractResourceVO> contractResourceVOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(contractToFundList)) {
            Result<List<FileResourceResultVO>> fileResourceResult = resourceFeign.queryFileByIds(contractToFundList.stream().map(ContractToFundEntity::getOriginalFileUid).toList());
            //           本地测试
            //            Result<List<FileResourceResultVO>> fileResourceResult = resourceFeign.queryFileByIds(Collections.singletonList("22ff1381a2054db5a18b678dfbd0e20c"));
            log.info("ContractServiceImpl.getContractUrls fileResourceResult:{}", fileResourceResult);
            if (ResultCode.SUCCESS.getCode().equals(fileResourceResult.getCode())) {
                List<FileResourceResultVO> fileResourceResultVOList = fileResourceResult.getData();
                contractResourceVOList = contractToFundConverter.voListToVoList(fileResourceResultVOList);
            }
        }
        if (CollUtil.isEmpty(contractResourceVOList)) {
            return contractResourceVOList;
        }

        contractResourceVOList.forEach(contractResourceVO -> {
            String fileUid = contractResourceVO.getFileUid();
            Optional<ContractToFundEntity> optionalContractToFundEntity = contractToFundList.stream()
                    .filter(contractToFundEntity -> contractToFundEntity.getOriginalFileUid().equals(fileUid))
                    .findFirst();

            optionalContractToFundEntity.ifPresent(contractToFundEntity -> {
                contractResourceVO.setContractName(contractToFundEntity.getOriginalFileName());
            });
        });
        return contractResourceVOList;
    }

    @Override
    public boolean getFundUploadFileInfo(List<ContractToFundVO> fundVOList) {
        fundVOList.forEach(contractToFundVO -> {
            contractToFundMapper.update(new LambdaUpdateWrapper<ContractToFundEntity>()
                    .set(ContractToFundEntity::getFundFileName, contractToFundVO.getFundFilePath())
                    .eq(ContractToFundEntity::getOriginalFileUid, contractToFundVO.getFileUid())
            );
        });

        return true;
    }

    @Override
    public boolean getFundDownloadFile() {
        log.info("ContractServiceImpl.getFundDownloadFile start");
        //查询需要给资方签署的合同
        List<DownloadContractDTO> contractDTOList = contractToFundMapper.selectJoinList(
                DownloadContractDTO.class,
                new MPJLambdaWrapper<ContractToFundEntity>()
                        .selectAs(ContractToFundEntity::getOrderId, DownloadContractDTO::getOrderId)
                        .selectAs(ContractToFundEntity::getOriginalFileUid, DownloadContractDTO::getResource)
                        .selectAs(OrderContractEntity::getTemplateId, DownloadContractDTO::getTemplateId)
                        .selectAs(OrderContractEntity::getName, DownloadContractDTO::getName)
                        .selectAs(OrderContractEntity::getNumber, DownloadContractDTO::getNumber)
                        .selectAs(FundTemplateAssoEntity::getFundId, DownloadContractDTO::getFundId)
                        .selectAs(ContractToFundEntity::getFundFileName, DownloadContractDTO::getFundFileName)
                        .leftJoin(OrderContractEntity.class, on -> on
                                .eq(OrderContractEntity::getOrderId, ContractToFundEntity::getOrderId)
                                .eq(OrderContractEntity::getResource, ContractToFundEntity::getOriginalFileUid)
                                .eq(OrderContractEntity::getDeleteFlag, 0))
                        .leftJoin(FundTemplateAssoEntity.class, on -> on
                                .eq(OrderContractEntity::getTemplateId, FundTemplateAssoEntity::getTemplateId)
                                .eq(FundTemplateAssoEntity::getDeleteFlag, 0)
                        )
                        .leftJoin(OrderInfoEntity.class, on -> on.eq(OrderInfoEntity::getId, ContractToFundEntity::getOrderId))
                        .isNull(ContractToFundEntity::getNewFileUid)
                        .ge(OrderInfoEntity::getUpdateTime, LocalDateTime.now().minusDays(7))
                        .eq(OrderInfoEntity::getFundId, 5)
                        .eq(ContractToFundEntity::getDeleteFlag, 0)
                        .in(OrderInfoEntity::getCurrentNode, States.CUSTOMER_APPOINTMENT.getNode(), States.PAYMENT_APPLY_INFORMATION.getNode()
                                ,States.PAYMENT_CONTRACT_APPROVAL.getNode(), States.FUNDS_PAYMENT_APPROVAL.getNode(), States.PAYMENT_SUCCESS.getNode())
                        .orderByDesc(OrderInfoEntity::getCreateTime)
        );
        log.info("ContractServiceImpl.getFundDownloadFile contractDTOList.size:{}", contractDTOList.size());

        if (CollUtil.isEmpty(contractDTOList)) {
            return true;
        }
        contractDTOList.forEach(contractVO -> {
            if(ObjectUtil.isNotEmpty(contractVO.getOrderId())){
                Result<YingFengInfoVO> infoVOResult = approveFeign.getYingFengInfoByOrderId(contractVO.getOrderId());
                if(ObjectUtil.isNotEmpty(infoVOResult.getData())){
                    contractVO.setYingFengApplyNo(infoVOResult.getData().getCreditApplyNo());
                    List<DownloadContractDTO> fileList = new ArrayList<>();
                    fileList.add(contractVO);
                    dealBatchFile(fileList);
                }
            }

        });
//        //盈峰批量下载文件
//        List<DownloadContractDTO> fileList = contractDTOList.stream().filter(
//                contractVO -> ObjectUtil.isNotEmpty(contractVO.getFundId()) && contractVO.getFundId() == FundEnum.YING_FENG.getValue()).toList();
//        log.info("ContractServiceImpl.getFundDownloadFile fileList.size: {}", fileList.size());
//        // 计算需要多少个批次
//        int numberOfBatches = (int) Math.ceil((double) fileList.size() / DOWNLOAD_BATCH_SIZE);
//        log.info("ContractServiceImpl.getFundDownloadFile numberOfBatches: {}", numberOfBatches);
//        List<List<DownloadContractDTO>> batchList = new ArrayList<>();
//        for (int i = 0; i < numberOfBatches; i++) {
//            // 计算当前批次的起始和结束索引
//            int start = i * DOWNLOAD_BATCH_SIZE;
//            int end = Math.min(start + DOWNLOAD_BATCH_SIZE, fileList.size());
//
//            // 获取当前批次的数据
//            List<DownloadContractDTO> currentBatch = fileList.subList(start, end);
//
//            // 对当前批次的数据进行处理
//            batchList.add(currentBatch);
//
//        }
//        //进行文件处理获得法大大平台文件id
//        batchList.parallelStream().forEach(this::dealBatchFile);

        return true;
    }
    private void dealBatchFile(List<DownloadContractDTO> contractDTOList) {
        //下载盈峰文件
        Result<List<DownLoadContractVO>> result = resourceFeign.downLoadYingFengFile(contractDTOList);
        //        Result<List<DownLoadContractVO>> result = resourceFeign.downLoadYingFengFile(contractDTOList);
        log.info("ContractServiceImpl.getFundDownloadFile result:{}", JSONUtil.toJsonStr(result.getData()));
        if (ResultCode.SUCCESS.getCode().equals(result.getCode())) {
            List<DownLoadContractVO> downLoadContractVOList = result.getData();
            downLoadContractVOList.forEach(downLoadContractVO -> {
                //可能存在报错未取到文件情况，避免空值覆盖原值
                if (StrUtil.isNotEmpty(downLoadContractVO.getNewFileUid())) {
                    //拿到文件后新增一条合同数据，将旧合同的文件资源标记为删除
                    OrderContractEntity orderContractEntity = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                            .eq(OrderContractEntity::getResource, downLoadContractVO.getResource())
                            .eq(OrderContractEntity::getOrderId, downLoadContractVO.getOrderId())
                            .eq(OrderContractEntity::getDeleteFlag, 0));
                    if(ObjectUtil.isNotEmpty(orderContractEntity)){
                        orderContractEntity.setResource(downLoadContractVO.getNewFileUid());
                        orderContractMapper.insert(orderContractEntity);

                        orderContractMapper.update(new LambdaUpdateWrapper<OrderContractEntity>()
                                //                            .set(OrderContractEntity::getResource, downLoadContractVO.getNewFileUid())
                                .set(OrderContractEntity::getDeleteFlag, 1)
                                .eq(OrderContractEntity::getResource, downLoadContractVO.getResource()));

                        contractToFundMapper.update(new LambdaUpdateWrapper<ContractToFundEntity>()
                                .set(ContractToFundEntity::getNewFileUid, downLoadContractVO.getNewFileUid())
                                .eq(ContractToFundEntity::getOriginalFileUid, downLoadContractVO.getResource()));
                    }

                }
            });

        }
    }


    @Override
    public boolean updateOrderContactStatus() {
        log.info("ContractServiceImpl.updateOrderContactStatus start");
        //获取签约节点未完成的订单
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .ne(OrderInfoEntity::getContractState, ContractEnum.SIGNED.getCode())
                .in(OrderInfoEntity::getCurrentNode, States.CUSTOMER_CONFIRM.getNode(), States.CUSTOMER_APPOINTMENT.getNode(),
                        States.CONTRACT_SIGNING.getNode(), States.GPS_FEE_PAYMENT.getNode(),
                        States.PAYMENT_APPLY_INFORMATION.getNode(), States.PAYMENT_CONTRACT_APPROVAL.getNode(),
                        States.FUNDS_PAYMENT_APPROVAL.getNode())
        );
        log.info("ContractServiceImpl.updateOrderContactStatus orderInfoEntityList.size:{}", orderInfoEntityList.size());
        if (CollUtil.isEmpty(orderInfoEntityList)) {
            return true;
        }

        for (OrderInfoEntity orderInfoEntity : orderInfoEntityList) {
            Integer orderId = orderInfoEntity.getId();
            log.info("ContractServiceImpl.updateOrderContactStatus orderId:{}", orderId);

            boolean isFundSign = true;
            if(Objects.equals(orderInfoEntity.getFundId(), FundEnum.YING_FENG.getValue()) || Objects.equals(orderInfoEntity.getFundId(), FundEnum.FU_MIN.getValue())
                    || Objects.equals(orderInfoEntity.getFundId(), FundEnum.CHANG_YIN.getValue())
                    || Objects.equals(orderInfoEntity.getFundId(), FundEnum.LAN_HAI.getValue())
            ){
                //校验线上合同是否签署完成
                List<FundSignInfoEntity> fundSignInfoEntityList = fundSignInfoMapper.selectList(new LambdaQueryWrapper<FundSignInfoEntity>()
                        .eq(FundSignInfoEntity::getOrderId, orderId)
                        .eq(FundSignInfoEntity::getFundId, orderInfoEntity.getFundId())
                        .eq(FundSignInfoEntity::getSignStatus, FundSignStatusEnum.SUCCESS.getCode())
                        .eq(FundSignInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FundSignInfoEntity::getId)
                );
                log.info("ContractServiceImpl.updateOrderContactStatus fundSignInfoEntityList.size:{}", fundSignInfoEntityList.size());
                // 蓝海 先签署线上合同 再签署 资方合同 ; 富民，盈峰是先签署资方合同的
                if (CollUtil.isEmpty(fundSignInfoEntityList)) {
                    if (Objects.equals(orderInfoEntity.getFundId(), FundEnum.LAN_HAI.getValue())) {
                        // 蓝海 需要先签署线上合同，要向下执行更新合同状态为待资方签署
                        isFundSign = false;
                    } else {
                        continue;
                    }
                }
            }

            // 合同生成逻辑调整 资方生成完成后才生成我司合同

            // 不存在未签署的我司记录
            Long waitCount = orderContractMapper.selectCount(new LambdaQueryWrapper<OrderContractEntity>()
                    .eq(OrderContractEntity::getOrderId, orderId)
                    .eq(OrderContractEntity::getDeleteFlag, 0)
                    .eq(OrderContractEntity::getContractFlag, 0)
                    .ne(OrderContractEntity::getSignStatus, ContractEnum.SIGNED.getCode()));
            // 存在签署完成的我司记录
            Long finishCount = orderContractMapper.selectCount(new LambdaQueryWrapper<OrderContractEntity>()
                    .eq(OrderContractEntity::getOrderId, orderId)
                    .eq(OrderContractEntity::getContractFlag, 0)
                    .eq(OrderContractEntity::getDeleteFlag, 0)
                    .eq(OrderContractEntity::getSignStatus, ContractEnum.SIGNED.getCode()));

            log.info("ContractServiceImpl.updateOrderContactStatus orderId:{} waitCount:{} finishCount:{}", orderId, waitCount, finishCount);

            if (waitCount == 0 && finishCount != 0) {
                // 合同签约完成
                if (!isFundSign) {
                    //资方合同未签署完成更新待资方签署状态
                    int update = orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                            .set(OrderInfoEntity::getContractState, ContractEnum.ORDER_SIGNED_WAIT_DOWNLOAD.getCode())
                            .ne(OrderInfoEntity::getContractState, ContractEnum.ORDER_SIGNED_WAIT_FUND.getCode())
                            .eq(OrderInfoEntity::getId, orderId));

                    log.info("ContractServiceImpl.updateOrderContract.updateOrderContractStatus orderId= {} contractState= {}, update: {}", orderId, orderInfoEntity.getContractState(), update);
                } else {
                    orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                            .set(OrderInfoEntity::getContractState, ContractEnum.SIGNED.getCode())
                            .eq(OrderInfoEntity::getId, orderId));
                    orderStateService.sendEvent(States.CUSTOMER_APPOINTMENT, Events.CONTRACT_SIGNING_FINISH, orderId, 1);
                }

            }
        }

        //判断是否有签约中的合同，没有则更新订单合同状态为已签约
//        List<OrderInfoEntity> orderInfoList = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
//                .eq(OrderInfoEntity::getContractState, ContractEnum.SIGNING.getCode())
//                .eq(OrderInfoEntity::getDeleteFlag, 0));
//        log.info("ContractServiceImpl.updateOrderContactStatus orderInfoList.size:{}", orderInfoList.size());
//        if (CollUtil.isNotEmpty(orderInfoList)) {
//            orderInfoList.forEach(orderInfoEntity -> {
//                List<OrderContractEntity> orderContractList = orderContractMapper.selectList(new LambdaQueryWrapper<OrderContractEntity>()
//                        .eq(OrderContractEntity::getOrderId, orderInfoEntity.getId())
//                        .eq(OrderContractEntity::getDeleteFlag, 0)
//                        .and(q -> q.eq(OrderContractEntity::getSignStatus, ContractEnum.SIGNING.getCode())
//                                .or()
//                                .eq(OrderContractEntity::getSignStatus, ContractEnum.WAITING.getCode())));
//                if (CollUtil.isEmpty(orderContractList)) {
//                    orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
//                            .set(OrderInfoEntity::getContractState, ContractEnum.SIGNED.getCode())
//                            .eq(OrderInfoEntity::getId, orderInfoEntity.getId()));
//
//                    // 合同签约完成
//                    orderStateService.sendEvent(States.CUSTOMER_APPOINTMENT, Events.CONTRACT_SIGNING_FINISH, orderInfoEntity.getId(), 1);
//                }
//            });
//        }


        return true;
    }


    /**
     * 重新启动签约
     *
     * @return {@link Boolean }
     */
    @Override
    public Boolean restartSign(RestartContactDTO restartContactDTO) {
        //未生成合同
        int contractState = ContractRestartEnum.TO_SIGN.getCode();
        log.info("ContractServiceImpl.restartSign begin signId:{}", restartContactDTO.getSignId());

        //根据重签id查询重签记录
        ContractRestartSignEntity contractRestartSignEntity = contractRestartSignMapper.selectOne(new LambdaQueryWrapper<ContractRestartSignEntity>()
                .eq(ContractRestartSignEntity::getId, restartContactDTO.getSignId())
                .eq(ContractRestartSignEntity::getDeleteFlag, 0));
        Assert.notNull(contractRestartSignEntity, "重签信息不存在");
        if (Objects.equals(contractRestartSignEntity.getStatus(), ContractRestartEnum.SIGNED.getCode())){
            throw new BusinessException("合同已重签，无法再次重签");
        }
        //        if (Objects.equals(contractRestartSignEntity.getStatus(), ContractRestartEnum.SIGNING.getCode())){
        //            throw new BusinessException("签约中，请稍后");
        //        }
        //根据资方名称查询资方信息
        List<FundInfoEntity> fundInfoList = fundInfoMapper.selectList(new LambdaQueryWrapper<FundInfoEntity>()
                .like(FundInfoEntity::getName, contractRestartSignEntity.getFundName())
                .eq(FundInfoEntity::getDeleteFlag, 0));
        if (CollUtil.isEmpty(fundInfoList)) {
            throw new BusinessException("未找到资方信息");
        }
        try {
            FundInfoEntity fundInfoEntity = fundInfoList.get(0);
            //需要签署的资方合同模板
            List<FundTemplateAssoEntity> fundTemplateAssoList = fundTemplateAssoMapper.selectList(new MPJLambdaWrapper<FundTemplateAssoEntity>()
                    .eq(FundTemplateAssoEntity::getFundId, fundInfoEntity.getId())
                    .eq(FundTemplateAssoEntity::getStatus, 0)
                    .eq(FundTemplateAssoEntity::getType, 1)
                    .eq(FundTemplateAssoEntity::getDeleteFlag, 0)
                    .orderByAsc(FundTemplateAssoEntity::getSort)
            );
            List<Integer> templateIdList = fundTemplateAssoList.stream().map(FundTemplateAssoEntity::getTemplateId).toList();
            LocalDateTime paymentTime = contractRestartSignEntity.getPaymentTime(); // 示例：获取当前时间
            String targetDateStr = "2025-05-10";

            // 将字符串转为 LocalDate，再转为 LocalDateTime（默认时间为 00:00）
            LocalDate targetDate = LocalDate.parse(targetDateStr);
            LocalDateTime targetDateTime = targetDate.atStartOfDay();

            if (paymentTime.isBefore(targetDateTime)) {
                log.info("ContractServiceImpl.restartSign date before:{}", paymentTime);
            } else if (paymentTime.isAfter(targetDateTime)) {
                //放款时间大于2025-05-10，就签署承租人合同
                if(envUtil.isPrd()){
                    templateIdList = templateIdList.stream()
                            .filter(Objects::nonNull)
                            .filter(templateId -> templateId == 136)
                            .toList();
                }else {
                    templateIdList = templateIdList.stream()
                            .filter(Objects::nonNull)
                            .filter(templateId -> templateId == 139)
                            .toList();
                }

                log.info("ContractServiceImpl.restartSign date after:{}", paymentTime);
            }

            //封装生成合同参数
            Integer id = contractRestartSignEntity.getId();
            GenerateContractDTO generateContractDTO = new GenerateContractDTO()
                    .setId(id)
                    .setType(RESTART_SIGN_SNAPSHOT.getCode())
                    .setTemplateIdList(templateIdList);
            if(Objects.equals(contractRestartSignEntity.getDataSource(), 1)){
                //合同快照信息
                saveRestartSignSnapshot(contractRestartSignEntity, id);
            }else if(Objects.equals(contractRestartSignEntity.getDataSource(), 2)){
                //2：获取所有需要用到的数据
                List<ParamsSnapshotEntity> orderParamsSnapshotEntities = getTongHuiSnapshot(contractRestartSignEntity);
                //3:保存数据
                orderParamsSnapshotService.saveBatch(orderParamsSnapshotEntities);
            }

            //调用合同生成接口
            Result<List<GenerateContractVO>> result = resourceFeign.generateContract(generateContractDTO);
            log.info("ContractServiceImpl.restartSign result:{}", JSONUtil.toJsonStr(result));
            if (Result.isSuccess(result) && CollUtil.isNotEmpty(result.getData())) {
                //生成合同前删除对应订单合同
                contractRestartFileMapper.update(null, new LambdaUpdateWrapper<ContractRestartFileEntity>()
                        .set(ContractRestartFileEntity::getDeleteFlag, 1)
                        .eq(ContractRestartFileEntity::getSignId, restartContactDTO.getSignId())
                        .eq(ContractRestartFileEntity::getDeleteFlag, 0)
                );
                List<GenerateContractVO> data = result.getData();
                //初始化订单合同列表
                List<String> resourceIdList = new ArrayList<>();
                for (GenerateContractVO datum : data) {
                    ContractRestartFileEntity contractRestartFileEntity = new ContractRestartFileEntity()
                            .setSignId(id)
                            .setSignStatus(0)
                            .setTemplateId(datum.getTemplateId())
                            .setNumber(datum.getContractNumber())
                            .setName(datum.getName())
                            .setResource(datum.getResourceId());
                    //合同重签文件
                    contractRestartFileMapper.insert(contractRestartFileEntity);
                    log.info("ContractServiceImpl.restartSign ContractRestartFileEntity:{}", JSONUtil.toJsonStr(contractRestartFileEntity));
                    resourceIdList.add(datum.getResourceId());
                }

//                CompletableFuture.supplyAsync(() -> {
                    // 上传法大大
                    fadadaAuthService.batchUploadFileToFdd(resourceIdList);
//                    return "ok";
//                }).thenAccept(processResult -> log.info("上传合同文件到法大大: {}", processResult));
                log.info("ContractServiceImpl.generateContractSignList resourceIdList:{}", resourceIdList);

            }
            contractState= ContractRestartEnum.WAITING.getCode();
        } catch (Exception e) {
            contractState = ContractRestartEnum.BUILD_FAILED.getCode();
            log.error("ContractServiceImpl.restartSign e:", e);
            throw new BusinessException("生成补签合同签约列表失败");
        } finally {
            //更新合同重签主表状态
            contractRestartSignMapper.update(new LambdaUpdateWrapper<ContractRestartSignEntity>()
                    .set(ContractRestartSignEntity::getStatus, contractState)
                    .eq(ContractRestartSignEntity::getId, restartContactDTO.getSignId())
                    .eq(ContractRestartSignEntity::getDeleteFlag, 0)
            );
        }
        return true;
    }

    private List<ParamsSnapshotEntity> getTongHuiSnapshot(ContractRestartSignEntity restartSignEntity) {
        List<ParamsSnapshotEntity> paramsSnapshotEntityList = new ArrayList<>();
        Integer linkId = restartSignEntity.getId();
        //订单编号
        String orderNumber = restartSignEntity.getOrderNumber();
        OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getOrderNumber, orderNumber)
                .eq(OrderInfoEntity::getDeleteFlag, 0));
        Integer orderId = orderInfo.getId();

        //产品信息
        ProductInfoEntity productInfoEntity = productInfoMapper.selectById(orderInfo.getProductId());
        List<FundProductMappingEntity> fundProductMappingEntities = fundProductMappingMapper
                .selectList(new LambdaQueryWrapper<FundProductMappingEntity>()
                        .eq(FundProductMappingEntity::getProductId, orderInfo.getProductId())
                        .eq(FundProductMappingEntity::getFundId, orderInfo.getFundId())
                        .eq(FundProductMappingEntity::getDeleteFlag, 0));
        FundProductMappingEntity fundProductMappingEntity = new FundProductMappingEntity();
        if (!fundProductMappingEntities.isEmpty()) {
            fundProductMappingEntity = fundProductMappingEntities.get(0);
        }
        //封闭期
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.LOCK_IN_PERIOD, fundProductMappingEntity.getClosedPeriod());

        //资方信息
//        FundInfoEntity fundInfoEntity = fundInfoMapper.selectById(orderInfo.getFundId());

        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.CUSTOMER_NAME, restartSignEntity.getName());
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.CUSTOMER_ID_NUMBER, restartSignEntity.getIdCardNumber());
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.CUSTOMER_PHONE, restartSignEntity.getPhone());
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.CUSTOMER_ADDRESS, restartSignEntity.getAddress());
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_BRAND, restartSignEntity.getCarBrand());
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_MODEL, restartSignEntity.getCarModel());
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_LICENSE_PLATE_NUMBER, restartSignEntity.getVehicleNumber());
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_ENGINE_NUMBER, restartSignEntity.getEngineNumber());
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_VIN, restartSignEntity.getVin());
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.DISBURSEMENT_AMOUNT_NUMERIC, restartSignEntity.getLoanAmount() != null ? restartSignEntity.getLoanAmount().setScale(2, RoundingMode.HALF_UP) : "");
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.DISBURSEMENT_AMOUNT_TEXT, restartSignEntity.getLoanAmount() != null ? Convert.digitToChinese(restartSignEntity.getLoanAmount().setScale(2, RoundingMode.HALF_UP)) : "");

        //客户银行卡 开户行
        List<BankAccountSignEntity> bankAccountSignEntities = bankAccountSignMapper.selectList(new LambdaQueryWrapper<BankAccountSignEntity>()
                .eq(BankAccountSignEntity::getOrderId, orderId)
                .eq(BankAccountSignEntity::getSignState, 1)
                .eq(BankAccountSignEntity::getDeleteFlag, 0));
        BankAccountSignEntity bankAccountSignEntity = new BankAccountSignEntity();
        if (!bankAccountSignEntities.isEmpty()) {
            bankAccountSignEntity = bankAccountSignEntities.get(0);
        }
        //客户银行卡  户名
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.CUSTOMER_BANK_ACCOUNT_NAME, bankAccountSignEntity.getName());
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.CUSTOMER_BANK_BRANCH, bankAccountSignEntity.getBankNameUpdate());
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.CUSTOMER_BANK_ACCOUNT_NUMBER, bankAccountSignEntity.getBankCardNumber());
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.CUSTOMER_PHONE, bankAccountSignEntity.getPhone());

        //签订日期
//        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.SIGNING_DATE, formatDate(LocalDate.now().toString()));
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.TERM, restartSignEntity.getLoanTerm() != null ? restartSignEntity.getLoanTerm() : "");
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.ACTUAL_LEASE_ANNUALIZED_INTEREST_RATE, restartSignEntity.getIrr() != null ? restartSignEntity.getIrr().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP) : "");
        //贷款期数（年）贷款期限/12
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.TERM_YEAR, orderInfo.getTerm() != null ? restartSignEntity.getLoanTerm() / 12 : "");

        OrderVehicleInfoEntity vehicleInfo = orderVehicleInfoMapper.selectOne(new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                .eq(OrderVehicleInfoEntity::getOrderId, orderId)
                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0));
        //初次登记日期
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.INITIAL_REGISTRATION_DATE, vehicleInfo.getRegisterDate() != null ? vehicleInfo.getRegisterDate() : "");
        ParamsSnapshotEntity paramsSnapshotEntity = paramsSnapshotMapper.selectOne(new LambdaQueryWrapper<ParamsSnapshotEntity>()
                .eq(ParamsSnapshotEntity::getLinkId, orderId)
                .eq(ParamsSnapshotEntity::getCode, TemplateParamsCodeEnum.VEHICLE_FINANCIAL_LEASE_CONTRACT_NUMBER.getCode())
                .eq(ParamsSnapshotEntity::getType, ORDER_SNAPSHOT)
                .eq(ParamsSnapshotEntity::getDeleteFlag, 0));
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.VEHICLE_FINANCIAL_LEASE_CONTRACT_NUMBER, paramsSnapshotEntity.getValue());

        //融资租赁合同签署日期
        ParamsSnapshotEntity zulinDate = paramsSnapshotMapper.selectOne(new LambdaQueryWrapper<ParamsSnapshotEntity>()
                .eq(ParamsSnapshotEntity::getLinkId, orderId)
                .eq(ParamsSnapshotEntity::getCode, TemplateParamsCodeEnum.SIGNING_DATE.getCode())
                .eq(ParamsSnapshotEntity::getType, ORDER_SNAPSHOT)
                .eq(ParamsSnapshotEntity::getDeleteFlag, 0));
        //签订日期
        addOrderParamsSnapshot(paramsSnapshotEntityList, linkId, TemplateParamsCodeEnum.SIGNING_DATE, zulinDate.getValue());
        String dateStr = zulinDate.getValue();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年M月d日");
        LocalDate localDate = LocalDate.parse(dateStr, formatter);
        HengTongPreRepayPlanVO planVO = approveFeign.hengTongPreRepayPlan(new HengTongPreRepayPlanDTO().setShiJian(localDate).setZuiZhongEDu(restartSignEntity.getLoanAmount())
                .setChanPinLiLv(restartSignEntity.getIrr().multiply(new BigDecimal(100))).setChanPinQiShu(restartSignEntity.getLoanTerm())).getData();
        org.springframework.util.Assert.notNull(planVO, "通汇还款计划表" + orderId + "信息不存在");
        List<HengTongPreRepayPlanVO.Plan> planList = planVO.getHuanKuanList();
        for (HengTongPreRepayPlanVO.Plan repaymentInfoEntity : planList) {
            //月还总额
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(linkId)
                    .setType(RESTART_SIGN_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_" + repaymentInfoEntity.getQiShu())
                    .setValue(Convert.toStr(repaymentInfoEntity.getYsHeJi() != null ? repaymentInfoEntity.getYsHeJi().setScale(2, RoundingMode.HALF_UP) : null)));
            //还款日期
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(linkId)
                    .setType(RESTART_SIGN_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REPAYMENT_DATE + "_" + repaymentInfoEntity.getQiShu())
                    .setValue(repaymentInfoEntity.getYsTime().format(DatePattern.NORM_DATE_FORMATTER)));
            //还款单日
            String format = repaymentInfoEntity.getYsTime().format(DatePattern.NORM_DATE_FORMATTER);
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(linkId)
                    .setType(RESTART_SIGN_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REPAYMENT_DATE_NUMBER + "_" + repaymentInfoEntity.getQiShu())
                    .setValue(StrUtil.toString(LocalDate.parse(format).getDayOfMonth())));
            //剩余本金
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(linkId)
                    .setType(RESTART_SIGN_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REMAINING_PRINCIPAL + "_" + repaymentInfoEntity.getQiShu())
                    .setValue(Convert.toStr(repaymentInfoEntity.getYsShengYuBenJin() != null ? repaymentInfoEntity.getYsShengYuBenJin().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO)));

        }

        return paramsSnapshotEntityList;
    }
    /**
     * 添加 Order Params 快照
     *
     * @param list    列表
     * @param orderId 次序id
     * @param code    编码
     * @param value   价值
     */
    private void addOrderParamsSnapshot(List<ParamsSnapshotEntity> list, Integer orderId, TemplateParamsCodeEnum code, Object value) {
        ParamsSnapshotEntity entity = new ParamsSnapshotEntity();
        entity.setLinkId(orderId);
        entity.setType(RESTART_SIGN_SNAPSHOT);
        entity.setCode(code.getCode());
        entity.setValue(Convert.toStr(value));
        list.add(entity);
    }

    @Override
    public ContractInfoVO getContractInfo(Integer orderId) {
        ContractInfoVO vo = fetchContractInfo(orderId);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        if (ObjUtil.equal(orderInfoEntity.getRegionId(), 56)||ObjUtil.equals(orderInfoEntity.getRegionId(), 24)){
            OrderFeeInfoEntity orderFeeInfoEntity = orderFeeInfoMapper.selectOne(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                    .eq(OrderFeeInfoEntity::getOrderId, orderId)
                    .eq(OrderFeeInfoEntity::getFeeType, 1)
                    .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                    .eq(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                    .orderByDesc(OrderFeeInfoEntity::getCreateTime).last("limit 1"), false);
            if (ObjUtil.isNull(orderFeeInfoEntity)){
                throw new BusinessException("请先完成GPS费用支付");
            }
        }
        if (ObjUtil.isNotNull(vo)) {
            //是否需要资方签署
//            this.setFundSignStatus(vo);
            if (vo.getFundId() == FundEnum.YING_FENG.getValue()||
                    vo.getFundId() == FundEnum.FU_MIN.getValue() || vo.getFundId() == FundEnum.CHANG_YIN.getValue()
            || vo.getFundId() == FundEnum.LAN_HAI.getValue()) {
                vo.setFundSign(true);
            }
        }
        return vo;
    }
    /**
     * 异步资方签署提交
     *
     * @param submitDTO
     */
    @Async
    @Override
    public void asyncFundSignSubmit(ContractFundSignSubmitDTO submitDTO) {
        Integer orderId = submitDTO.getOrderId();
        Assert.notNull(orderId, ()->{throw new BusinessException("订单id不能为空");});
        String lockKey = getFundSignSubmitLockKey(orderId);
        String requestId = IdUtil.randomUUID();
        try {
            Boolean b = redisService.tryLock(lockKey, requestId, 90);
            if (!b) {
                log.info("fundSignSubmit lock failed, orderId = {}", orderId);
                return ;
            }
            fundSignSubmit(submitDTO);
        } finally {
            redisService.releaseLock(lockKey, requestId);
        }
    }

    /**
     * 资方签署提交
     *
     * @param submitDTO
     */
    @Override
    public ContractFundSignVO fundSignSubmit(ContractFundSignSubmitDTO submitDTO) {
        Integer orderId = submitDTO.getOrderId();
        log.info("ContractServiceImpl.fundSignSubmit orderId:{}", orderId);

        ContractFundSignVO vo = new ContractFundSignVO();

        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, "订单不存在");
        //资方id
        Integer fundId = orderInfo.getFundId();
        FundEnum fundEnum = FundEnum.getFundEnum(fundId);
        Assert.notNull(fundEnum, "资方不存在");
        //根据 fundEnum switch 匹配资方 （盈峰）发送短信签署 保存签署id 和link 到 lh_fund_sign_info
        switch (Objects.requireNonNull(fundEnum)) {
            case YING_FENG:
                //绑卡校验
                checkYingFengBindCard(orderId);
                //发送短信通知
                Result<YingFengSendSignVO> result = approveFeign.yingFengSendSign(orderId);
                log.info("ContractServiceImpl.fundSignSubmit yingFengSendSign result:{}", JSONUtil.toJsonStr(result));
                if (ResultCode.SUCCESS.getCode().equals(result.getCode())) {
                    if(ObjectUtil.isNotEmpty(result.getData())){
                        YingFengSendSignVO sendSignVO = result.getData();
                        //数据处理
                        List<FundSignInfoEntity> fundSignInfoList = fundSignInfoMapper.selectList(new LambdaQueryWrapper<FundSignInfoEntity>()
                                .eq(FundSignInfoEntity::getOrderId, orderId)
                                .eq(FundSignInfoEntity::getDeleteFlag, 0));
                        if(CollUtil.isNotEmpty(fundSignInfoList)){
                            FundSignInfoEntity fundSignInfo = fundSignInfoList.get(0);
                            fundSignInfo.setDeleteFlag(1);
                            fundSignInfoMapper.update(fundSignInfo, new LambdaUpdateWrapper<FundSignInfoEntity>()
                                    .eq(FundSignInfoEntity::getFundSignId, fundSignInfo.getFundSignId()));
                        }
                        FundSignInfoEntity fundSignInfo = new FundSignInfoEntity();
                        fundSignInfo.setFundId(fundId).setOrderId(orderId).setFundSignId(sendSignVO.getSignId());
                        fundSignInfoMapper.insert(fundSignInfo);
                        vo.setLink(sendSignVO.getSignId());
                    }else {
                        throw new BusinessException(result.getMsg());
                    }

                }else {
                    throw new BusinessException(result.getMsg());
                }
                break;
            case FU_MIN:
                // 校验是否已签约
                FundSignInfoEntity fundSignInfoEntity = fundSignInfoMapper.selectOne(new LambdaQueryWrapper<FundSignInfoEntity>()
                        .eq(FundSignInfoEntity::getOrderId, orderId)
                        .eq(FundSignInfoEntity::getFundId, FundEnum.FU_MIN.getValue())
                        .eq(FundSignInfoEntity::getDeleteFlag, 0)
                        .eq(FundSignInfoEntity::getSignStatus, FundSignStatusEnum.SUCCESS)
                );

                Assert.isNull(fundSignInfoEntity,()->{throw  new BusinessException("资方特殊合同已签约，不能重复签约");});

                //获取富民合同
                Result<List<FMContractPreviewVO>> fuMinContractPreviewAllResult = approveFeign.fuMinContractPreviewAll(orderId);

                if (!Result.isSuccess(fuMinContractPreviewAllResult) || CollUtil.isEmpty(fuMinContractPreviewAllResult.getData())) {
                    log.error("ContractServiceImpl.generateContractSignList 富民个人贷款借款合同获取失败 orderId:{}", orderId);
                } else {
                    List<FMContractPreviewVO> fuMinContracList = fuMinContractPreviewAllResult.getData();


                    List<Integer> list = fuMinContracList.stream().map(FMContractPreviewVO::getTemplateId).toList();
                    if (CollUtil.isNotEmpty(fuMinContracList)) {
                        orderContractMapper.update(new LambdaUpdateWrapper<OrderContractEntity>()
                                .set(OrderContractEntity::getDeleteFlag, 1)
                                .eq(OrderContractEntity::getOrderId, orderId)
                                .eq(OrderContractEntity::getDeleteFlag, 0)
                                .in(OrderContractEntity::getTemplateId, list)
                        );
                    }

                    for (FMContractPreviewVO fuMinContract : fuMinContracList) {
                        if (fuMinContract.getResourceId() == null) {
                            continue;
                        }

                        OrderContractEntity orderContractEntity = new OrderContractEntity()
                                .setOrderId(orderId)
                                .setContractFlag(1)
                                .setName(fuMinContract.getFileName())
                                .setSignStatus(0)
                                .setNumber(NumberUtils.getContractNumber())
                                .setFundGenerateFlag(1)
                                .setTemplateId(fuMinContract.getTemplateId())
                                .setResource(fuMinContract.getResourceId());
                        log.info("ContractServiceImpl.fuMinContractPreviewAllResult orderContractEntity:{}", JSONUtil.toJsonStr(orderContractEntity));
                        orderContractMapper.insert(orderContractEntity);
                    }
                }

                Result<FuMinPreviewContractDTO> fuMinPreviewContractDTOResult = approveFeign.fuMinLoanContractSign(orderId);

                fundSignInfoMapper.update(new LambdaUpdateWrapper<FundSignInfoEntity>()
                        .set(FundSignInfoEntity::getDeleteFlag, 1)
                        .eq(FundSignInfoEntity::getOrderId, orderId)
                        .eq(FundSignInfoEntity::getDeleteFlag, 0));


                FundSignStatusEnum signStatus = FundSignStatusEnum.SUCCESS;
                if (!Result.isSuccess(fuMinPreviewContractDTOResult)) {
                    signStatus = FundSignStatusEnum.FAILURE;
                }

                FundSignInfoEntity fundSignInfo = new FundSignInfoEntity()
                        .setOrderId(orderId)
                        .setFundId(fundId)
                        .setSignStatus(signStatus);
                fundSignInfoMapper.insert(fundSignInfo);

                if (FundSignStatusEnum.SUCCESS == signStatus) {
                    fuMinPreviewContract(fuMinPreviewContractDTOResult.getData());
                } else {
                    throw new BusinessException(fuMinPreviewContractDTOResult.getMsg());
                }
                break;
            case CHANG_YIN:
                log.info("ContractServiceImpl.generateContractSignList 长银合同预览 orderId:{}", orderId);
                Long changYinSignCount = fundSignInfoMapper.selectCount(new LambdaQueryWrapper<FundSignInfoEntity>()
                        .eq(FundSignInfoEntity::getOrderId, orderId)
                        .eq(FundSignInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                        .eq(FundSignInfoEntity::getDeleteFlag, 0)
                );
                if (changYinSignCount > 0){
                    throw  new BusinessException("资方特殊合同已签约，不能重复签约");
                }
                String changYinContractNumber = getChangYinContractNumber(orderInfo);
                //发起签约申请
                ChangYinResBodyDTO<ChangYinLoanApplicationRespDTO> changYinResBodyDTO = approveFeign.changYinPreLoanApplyV2ByOrderId(orderId, changYinContractNumber);
                if (ChangYinResBodyDTO.isSuccess(changYinResBodyDTO) && ObjUtil.equals(changYinResBodyDTO.getBody().getApplyStatus(), "1")) {
                    FundSignInfoEntity changYinSignInfo = new FundSignInfoEntity()
                            .setOrderId(orderId)
                            .setFundId(fundId)
                            .setSignStatus(FundSignStatusEnum.NOT_INITIATED);
                    fundSignInfoMapper.insert(changYinSignInfo);
                } else{
                    if (ObjUtil.isNotNull(changYinResBodyDTO.getBody()) && ObjUtil.isNotNull(changYinResBodyDTO.getBody().getApplyStatusDesc())) {
                        throw new BusinessException(changYinResBodyDTO.getBody().getApplyStatusDesc());
                    } else if (ObjUtil.isNotNull(changYinResBodyDTO.getHead()) && ObjUtil.isNotNull(changYinResBodyDTO.getHead().getRespMsg())) {
                        throw new BusinessException(changYinResBodyDTO.getHead().getRespMsg());
                    }else {
                        throw new BusinessException("长银合同签约申请失败");
                    }
                }
                break;
                case LAN_HAI:
                    //判断订单合同状态
                    Integer contractState = orderInfo.getContractState();
                    if (ObjUtil.equals(contractState, ContractEnum.ORDER_SIGNED_WAIT_DOWNLOAD.getCode())) {
                        log.info("ChangYinServiceImpl.syncPaymentDetailFileBatch: 订单合同状态为待拉取合同 orderId:{}",  orderId);
                        return vo;
                    }
                    if (!ObjUtil.equals(contractState, ContractEnum.ORDER_SIGNED_WAIT_FUND.getCode()) && !ObjUtil.equals(contractState, ContractEnum.SIGNED.getCode())) {
                        throw new BusinessException("请先完成合同签约");
                    }

                    Long lanHaiSignCount = fundSignInfoMapper.selectCount(new LambdaQueryWrapper<FundSignInfoEntity>()
                            .eq(FundSignInfoEntity::getOrderId, orderId)
                            .eq(FundSignInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                            .eq(FundSignInfoEntity::getDeleteFlag, 0)
                    );
                    if (lanHaiSignCount > 0){
                        throw new BusinessException("资方特殊合同签约已发起,请查询签约状态");
                    }
                    lanHaiFundSign(orderId, fundId);
                break;
            default:
                log.error("暂不支持该资方");
        }

        // 资方合同签署完成生成我司合同
        //蓝海资方先签约我司合同 再签约资方合同 不需要生成我司合同
        if (!ObjUtil.equals(fundId, FundEnum.LAN_HAI.getValue())) {
            contractFileService.generateContractSignList(orderId);
        }



        //定时任务获取签署结果 并保存 lh_fund_sign_info  lh_fund_sign_detail
        // 根据时间判断是否催签署 设置上限
        //        return this.sendFundSign(fundEnum, orderId);
        return vo;
    }

    private String getChangYinContractNumber(OrderInfoEntity orderInfo) {
        String contractNumber = contractFileService.generateContractNumber(TemplateParamsCodeEnum.ENTRUSTMENT_GUARANTEE_CONTRACT_NUMBER.getCode(), orderInfo.getOrderNumber());
        //委托担保合同编号
        ParamsSnapshotEntity entity = new ParamsSnapshotEntity();
        entity.setLinkId(orderInfo.getId());
        entity.setType(ORDER_SNAPSHOT);
        entity.setCode(TemplateParamsCodeEnum.ENTRUSTMENT_GUARANTEE_CONTRACT_NUMBER.getCode());
        entity.setValue(Convert.toStr(contractNumber));
        paramsSnapshotMapper.insert(entity);
        return contractNumber;

    }
    public String getGenerateContractLockKeyByTe(String templateNumber) {
        return "order:contract:generate:" + templateNumber;
    }

    public String getGenerateContractLockKey(Integer orderId) {
        return "order:contract:generate:" + orderId;
    }

    private void checkYingFengBindCard(Integer orderId) {
        //判断是否已成功绑定盈峰平台银行卡
        List<BankAccountSignEntity> bankAccountSignEntityList = bankAccountSignMapper.selectList(new LambdaQueryWrapper<BankAccountSignEntity>()
                .eq(BankAccountSignEntity::getOrderId, orderId)
                .eq(BankAccountSignEntity::getSignPlate, SignPlateEnum.YING_FENG.getValue())
                .eq(BankAccountSignEntity::getDeleteFlag, 0)
                .eq(BankAccountSignEntity::getSignState, 1));
        if(CollUtil.isEmpty(bankAccountSignEntityList)){
            throw new BusinessException("请先绑定银行卡");
        }
    }

    @Override
    public  String getFundSignSubmitLockKey(Integer orderId) {
        return "order:fund:sign:" + orderId;
    }
    @Override
    public ContractFundSignResulVO getFundSignVOResult(ContractFundSignResulDTO resulDTO) {
        //更新资方合同
        synFundContract();
        log.info("ContractServiceImpl.getFundSignVOResult orderId:{}", resulDTO.getOrderId());
        ContractFundSignResulVO vo = new ContractFundSignResulVO();
        if (ObjUtil.isNotNull(resulDTO) && ObjUtil.isNotNull(resulDTO.getOrderId())) {
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(resulDTO.getOrderId());
            if (!ObjUtil.equals(orderInfo.getFundId(), FundEnum.YING_FENG.getValue())) {
                try {
                    vo = updateFundSignStatus(orderInfo);
                    return vo;
                } catch (BusinessException e) {
                    log.error("ContractServiceImpl.getFundSignVOResult updateFundSignStatus error：{}", e.getMessage(), e);
                    throw e;
                } catch (Exception e) {
                    log.error("ContractServiceImpl.getFundSignVOResult updateFundSignStatus error：{}", e.getMessage(), e);
                    throw new BusinessException("更新资方合同签署状态失败");
                }
            }
        }
        //查询签署信息
        List<FundSignInfoEntity> signInfoEntityList = fundSignInfoMapper.selectJoinList(
                FundSignInfoEntity.class,
                new MPJLambdaWrapper<FundSignInfoEntity>()
                        .selectAll(FundSignInfoEntity.class)
                        .innerJoin(OrderInfoEntity.class, OrderInfoEntity::getId, FundSignInfoEntity::getOrderId)
                        .eq(ObjectUtil.isNotEmpty(resulDTO.getOrderId()), FundSignInfoEntity::getOrderId, resulDTO.getOrderId())
                        .eq(FundSignInfoEntity::getDeleteFlag, 0)
                        .eq(FundSignInfoEntity::getFundId, FundEnum.YING_FENG.getValue())
                        .and(qw -> qw.ne(FundSignInfoEntity::getSignStatus, FundSignStatusEnum.SUCCESS.getCode())
                                .or()
                                .isNull(FundSignInfoEntity::getSignStatus)
                        )
                        .ne(OrderInfoEntity::getCurrentNode, States.PROCESS_TERMINAL.getNode())
                        .ne(OrderInfoEntity::getCurrentNode, States.SYSTEM_TERMINAL.getNode())
        );
        if (CollUtil.isEmpty(signInfoEntityList)) {
            return null;
        }
        for (FundSignInfoEntity fundSignInfoEntity : signInfoEntityList){
            Result<YingFengQuerySignResultVO> result = approveFeign.yingFengQuerySignResult(fundSignInfoEntity.getFundSignId());
            log.info("ContractServiceImpl.getFundSignVOResult result:{}", JSONUtil.toJsonStr(result));
            if (ResultCode.SUCCESS.getCode().equals(result.getCode())) {
                YingFengQuerySignResultVO querySignResultVO = result.getData();
                vo.setSignFlowStatus(querySignResultVO.getStatus());
                //如果资方签署状态为成功，调用生成合同方法
//                if(Objects.equals(FundSignStatusEnum.SUCCESS.getCode(), querySignResultVO.getStatus())){
//                    contractFileService.generateContractSignList(fundSignInfoEntity.getOrderId());
//                    orderSendMessage.sendWeChatMsg(fundSignInfoEntity.getOrderId(), States.CUSTOMER_APPOINTMENT.getNode(), 1);
//                }
                FundSignInfoEntity fundSignInfo = new FundSignInfoEntity();
                //更新授信状态
                FundSignStatusEnum creditStatus = FundSignStatusEnum.fromCode(querySignResultVO.getStatus());
                fundSignInfo.setSignStatus(creditStatus);
                fundSignInfoMapper.update(fundSignInfo, new LambdaQueryWrapper<FundSignInfoEntity>()
                        .eq(FundSignInfoEntity::getFundSignId, querySignResultVO.getSignId()));
                //签署列表
                List<FundSignDetailEntity> fundSignDetailEntityList = fundSignDetailMapper.selectList(new LambdaQueryWrapper<FundSignDetailEntity>()
                        .eq(FundSignDetailEntity::getFundSignId, querySignResultVO.getSignId()));
                List<YingFengQuerySignResultVO.Signatories> signatories = querySignResultVO.getSignatories();
                log.info("ContractServiceImpl.getFundSignVOResult signatories:{}", JSONUtil.toJsonStr(signatories));
                for (YingFengQuerySignResultVO.Signatories signatory : signatories){
                    //签署详情
                    FundSignDetailEntity fundSignDetailEntity = new FundSignDetailEntity();
                    fundSignDetailEntity.setFundSignId(querySignResultVO.getSignId());
                    fundSignDetailEntity.setSort(signatory.getSignOrder())
                            .setSignStatus(Integer.valueOf(signatory.getSignStatus()))
                            .setAuthorizedSignatoryType(signatory.getAuthorizedSignatoryType())
                            .setIdNo(signatory.getIdNo())
                            .setIdType(signatory.getIdType())
                            .setName(signatory.getName())
                            .setSignLink(signatory.getSignLink());
                    if(signatory.getIdType().equals("10")){
                        vo.setSignLink(signatory.getSignLink());
                    }
                    if(CollUtil.isEmpty(fundSignDetailEntityList)){
                        fundSignDetailMapper.insert(fundSignDetailEntity);

                    }else {
                        fundSignDetailMapper.update(fundSignDetailEntity, new LambdaQueryWrapper<FundSignDetailEntity>()
                                .eq(FundSignDetailEntity::getFundSignId, querySignResultVO.getSignId())
                                .eq(FundSignDetailEntity::getIdNo, signatory.getIdNo()));
                    }
                }
            }
        }
        return vo;
    }
    private void synFundContract() {
        List<OrderContractEntity> fundContractList = orderContractMapper.selectList(new LambdaQueryWrapper<OrderContractEntity>()
                .in(OrderContractEntity::getSignStatus, ContractEnum.WAITING.getCode(), ContractEnum.SIGNING.getCode())
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .in(OrderContractEntity::getTemplateId, 23, 24));
        log.info("synFundContract contractEntityList ={}", JSONUtil.toJsonStr(fundContractList));
        if(CollUtil.isNotEmpty(fundContractList)){
            for (OrderContractEntity contractEntity : fundContractList){
                //资方合同是否签署完成
                List<FundSignInfoEntity> fundSignInfoList = fundSignInfoMapper.selectList(new LambdaQueryWrapper<FundSignInfoEntity>()
                        .eq(FundSignInfoEntity::getOrderId, contractEntity.getOrderId())
                        .eq(FundSignInfoEntity::getSignStatus, 1)
                        .eq(FundSignInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FundSignInfoEntity::getId));
                log.info("synFundContract orderId ={},fundSignInfoList.size() ={}", contractEntity.getOrderId(), fundSignInfoList.size());
                if(CollUtil.isNotEmpty(fundSignInfoList)){
                    contractEntity.setSignStatus(ContractEnum.SIGNED.getCode());
                    Integer count = orderContractMapper.update(contractEntity, new LambdaUpdateWrapper<OrderContractEntity>()
                            .eq(OrderContractEntity::getOrderId, contractEntity.getOrderId())
                            .eq(OrderContractEntity::getId, contractEntity.getId()));
                    log.info("ContractServiceImpl.getFundSignVOResult update contractInfoEntity count:{}", count);

                }

            }
        }
    }

    /**
     * 资方合同签署结果
     */
    private ContractFundSignResulVO updateFundSignStatus(OrderInfoEntity orderInfo) {
        ContractFundSignResulVO vo = new ContractFundSignResulVO();
        vo.setSignFlowStatus(FundSignStatusEnum.NOT_INITIATED.getCode());
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));
        Integer orderId = orderInfo.getId();
        Long signCount = fundSignInfoMapper.selectCount(new LambdaQueryWrapper<FundSignInfoEntity>()
                .eq(FundSignInfoEntity::getOrderId, orderInfo.getId())
                .eq(FundSignInfoEntity::getFundId, orderInfo.getFundId())
                .eq(FundSignInfoEntity::getSignStatus, FundSignStatusEnum.SUCCESS)
                .eq(FundSignInfoEntity::getDeleteFlag, 0)
        );
        if (signCount > 0){
            vo.setSignFlowStatus(FundSignStatusEnum.SUCCESS.getCode());
            log.info("updateFundSignStatus orderId {} not found signInfo", orderInfo.getId());
            return vo;
        }


        FundEnum fundEnum = FundEnum.getFundEnum(orderInfo.getFundId());
        FundSignStatusEnum signStatus = null;
        String failReason = "";
        switch (fundEnum) {
            case CHANG_YIN -> {
                ChangYinResBodyDTO<ChangYinPreLoanQueryResDTO> changYinResBodyDTO = approveFeign.changYinPreLoanQueryV2ByOrderId(orderId);
                if (!ChangYinResBodyDTO.isSuccess(changYinResBodyDTO)) {
                    throw new BusinessException("获取签署状态失败");
                }
                ChangYinPreLoanQueryResDTO changYinBody = changYinResBodyDTO.getBody();
                if (Objects.equals(changYinBody.getDnSts(), ChangYinPreLoanStatusEnum.PASS.getCode())) {
                    if (ObjUtil.equals(fundEnum, FundEnum.CHANG_YIN)) {
                        //下载合同
                        Result<List<ChangYinContractPreviewVO>> changYinContractPreviewVOS = approveFeign.changYinDownloadContract(orderId);
                        if (!Result.isSuccess(changYinContractPreviewVOS) || (Result.isSuccess(changYinContractPreviewVOS) && CollUtil.isEmpty(changYinContractPreviewVOS.getData()))) {
                            throw new BusinessException("合同生成中，请稍后再试");
                        }
                        //更新合同签约状态和resourceId
                        signStatus = FundSignStatusEnum.SUCCESS;
                        for (ChangYinContractPreviewVO datum : changYinContractPreviewVOS.getData()) {
                            log.info("ContractServiceImpl.updateFundSignStatus changYinContractPreviewVOS:{}", JSONUtil.toJsonStr(datum));
                            if (ObjUtil.equals(signStatus, FundSignStatusEnum.SUCCESS) && StrUtil.isBlank(datum.getResourceId())) {
                                signStatus = FundSignStatusEnum.NOT_INITIATED;
                            }
                            if (StrUtil.isBlank(datum.getResourceId())) {
                                continue;
                            }

                            OrderContractEntity orderContractEntity = new OrderContractEntity();
                            orderContractEntity.setResource(datum.getResourceId());
//                            orderContractEntity.setFundGenerateFlag(1);
                            orderContractEntity.setSignStatus(ContractEnum.SIGNED.getCode());
                            int update = orderContractMapper.update(orderContractEntity, new LambdaQueryWrapper<OrderContractEntity>()
                                    .eq(OrderContractEntity::getOrderId, orderId)
                                    .eq(OrderContractEntity::getName, datum.getFileName())
                                    .eq(OrderContractEntity::getDeleteFlag, 0)
                            );
                            if (ObjUtil.equals(signStatus, FundSignStatusEnum.SUCCESS) && update == 0) {
                                signStatus = FundSignStatusEnum.NOT_INITIATED;
                            }

                        }
                       /* if (ObjUtil.equals(signStatus, FundSignStatusEnum.SUCCESS)) {
                            //签署完成 ，放款合同签约成功
                            OrderContractEntity orderContractEntity = new OrderContractEntity();
                            orderContractEntity.setSignStatus(ContractEnum.SIGNED.getCode());
                            int update = orderContractMapper.update(orderContractEntity, new LambdaQueryWrapper<OrderContractEntity>()
                                    .eq(OrderContractEntity::getOrderId, orderId)
                                    .eq(OrderContractEntity::getName, ContractTypeEnums.FKSBCXFK.getDescription())
                                    .eq(OrderContractEntity::getDeleteFlag, 0)
                            );
                            if (ObjUtil.equals(signStatus, FundSignStatusEnum.SUCCESS) && update == 0) {
                                signStatus = FundSignStatusEnum.NOT_INITIATED;
                            }
                        }*/

                    }
                }
                if (Objects.equals(changYinBody.getDnSts(), ChangYinPreLoanStatusEnum.REJECT.getCode())) {
                    signStatus = FundSignStatusEnum.FAILURE;
                    failReason = ObjUtil.defaultIfBlank(changYinBody.getLoanStatusDesc(), "长银用信失败");
                }
                break;
            }
            case LAN_HAI -> {
                log.info("ContractServiceImpl.updateFundSignStatus LanHai orderId:{}", orderId);
                Integer contractState = orderInfo.getContractState();
                if (!ObjUtil.equals(contractState, ContractEnum.ORDER_SIGNED_WAIT_FUND.getCode()) && !ObjUtil.equals(contractState, ContractEnum.SIGNED.getCode())) {
                    signStatus = FundSignStatusEnum.NOT_INITIATED;
                } else {
                    signStatus = lanHaiFundSignResult(orderId);
                }
                break;
            }
        }
        //更新签约状态
        if (ObjUtil.isNotNull(signStatus)) {
            FundSignInfoEntity fundSingInfoUpdate = new FundSignInfoEntity();
            fundSingInfoUpdate.setSignStatus(signStatus);
            fundSignInfoMapper.update(fundSingInfoUpdate, new LambdaQueryWrapper<FundSignInfoEntity>()
                    .eq(FundSignInfoEntity::getOrderId, orderId)
                    .eq(FundSignInfoEntity::getFundId, orderInfo.getFundId())
                    .eq(FundSignInfoEntity::getDeleteFlag, 0)
            );
        }

        //长银用信拒绝 终止订单
        if (ObjUtil.equals(signStatus, FundSignStatusEnum.FAILURE) && ObjUtil.equals(fundEnum, FundEnum.CHANG_YIN)) {
            orderStateService.sendEvent(States.CUSTOMER_APPOINTMENT, Events.FUND_SIGNING_REJECT, orderId, 1,
                    new ApprovalSubmitDTO().setRemarkExternal(failReason));
        }


        vo.setSignFlowStatus(ObjUtil.isNull(signStatus) ? 0 :signStatus.getCode());

        return vo;
    }




    @Override
    public boolean updateSignStatus(UpdateContractSignStatusDTO signStatusDTO) {
        //add by zangxx at 2024/10/15
        //创建签署任务后将订单签署状态改为签约中
        OrderContractEntity orderContractEntity = new OrderContractEntity();
        orderContractEntity.setSignStatus(signStatusDTO.getSignStatus());
        int count = orderContractMapper.update(orderContractEntity, new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, signStatusDTO.getOrderId())
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .ne(OrderContractEntity::getSignStatus, ContractEnum.SIGNED.getCode())
        );

        return true;
    }
    private final EsignPreAuthorizationMapper esignPreAuthorizationMapper;
    @Override
    public String previewComprehensiveContract(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isEmpty(orderInfoEntity)){
            throw new BusinessException("订单不存在");
        }
        esignPreAuthorizationMapper.update(new LambdaUpdateWrapper<EsignPreAuthorizationEntity>()
                .set(EsignPreAuthorizationEntity::getDeleteFlag, 1)
                .eq(EsignPreAuthorizationEntity::getPreId, orderInfoEntity.getPreId())
                .eq(EsignPreAuthorizationEntity::getTemplateId, -2)
                .eq(EsignPreAuthorizationEntity::getDeleteFlag, 0));
        EsignPreAuthorizationEntity esignPreAuthorizationEntity = new EsignPreAuthorizationEntity();
//        EsignPreAuthorizationEntity esignPreAuthorizationEntity = esignPreAuthorizationMapper.selectOne(new LambdaQueryWrapper<EsignPreAuthorizationEntity>()
//                .eq(EsignPreAuthorizationEntity::getPreId, orderInfoEntity.getPreId())
//                .eq(EsignPreAuthorizationEntity::getTemplateId, -2)
//                .eq(EsignPreAuthorizationEntity::getDeleteFlag, 0));
//        if (ObjUtil.isEmpty(esignPreAuthorizationEntity)){
        List<ContractListVO> contractListVOList = orderContractMapper.selectJoinList(
                ContractListVO.class,
                new MPJLambdaWrapper<OrderContractEntity>()
                        .select(OrderContractEntity::getId,
                                OrderContractEntity::getName,
                                OrderContractEntity::getNumber,
                                OrderContractEntity::getSignStatus,
                                OrderContractEntity::getResource,
                                OrderContractEntity::getContractFlag,
                                OrderContractEntity::getTemplateId)
                        .eq(OrderContractEntity::getOrderId, orderId)
                        .eq(OrderContractEntity::getDeleteFlag, 0)
        );
        List<String> resourceIdList = new ArrayList<>(contractListVOList.size());
        contractListVOList.forEach(contractListVO -> resourceIdList.add(contractListVO.getResource()));
        GenerateContractVO generateContractVO = resourceFeign.consolidateAllContracts(resourceIdList).getData();
        if (ObjUtil.isNotEmpty(generateContractVO.getResourceId())){
            esignPreAuthorizationEntity = new EsignPreAuthorizationEntity()
                    .setPreId(orderInfoEntity.getPreId())
                    .setName(generateContractVO.getName())
                    .setSignStatus(ContractEnum.WAITING.getCode())
                    .setTemplateId(generateContractVO.getTemplateId())
                    .setNumber(generateContractVO.getContractNumber())
                    .setResource(generateContractVO.getResourceId());
            esignPreAuthorizationMapper.insert(esignPreAuthorizationEntity);
        }

    //    }
        return esignPreAuthorizationEntity.getResource();
    }

    @Override
    public Boolean fuMinPreviewContract(FuMinPreviewContractDTO fuMinPreviewContractDTO) {
        log.info("ContractServiceImpl.fuMinPreviewContract fuMinPreviewContractDTO:{}", JSONUtil.toJsonStr(fuMinPreviewContractDTO));
        OrderContractEntity orderContract = orderContractMapper.selectOne(
                new LambdaQueryWrapper<OrderContractEntity>()
                        .eq(OrderContractEntity::getOrderId, fuMinPreviewContractDTO.getOrderId())
                        .eq(OrderContractEntity::getTemplateId, fuMinPreviewContractDTO.getTemplateId())
                        .eq(OrderContractEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderContractEntity::getCreateTime)
                        .last("limit 1")
        );
        if (ObjUtil.isEmpty(orderContract)) {
            OrderContractEntity orderContractEntity = new OrderContractEntity();
            orderContractEntity.setOrderId(fuMinPreviewContractDTO.getOrderId());
            orderContractEntity.setContractFlag(1);
            orderContractEntity.setSignStatus(fuMinPreviewContractDTO.getSignStatus());
            orderContractEntity.setName(fuMinPreviewContractDTO.getName());
            orderContractEntity.setResource(fuMinPreviewContractDTO.getResource());
            orderContractEntity.setTemplateId(fuMinPreviewContractDTO.getTemplateId());
            orderContractEntity.setFundGenerateFlag(1);
            orderContractEntity.setNumber(NumberUtils.getContractNumber());
            return orderContractMapper.insert(orderContractEntity) == 1;
        }

        log.info("ContractServiceImpl.fuMinPreviewContract orderContract:{}", JSONUtil.toJsonStr(orderContract));
        OrderContractEntity updateOrderContractEntity = new OrderContractEntity();
        updateOrderContractEntity.setId(orderContract.getId())
                .setSignStatus(fuMinPreviewContractDTO.getSignStatus())
                .setResource(fuMinPreviewContractDTO.getResource());

        //更新委托扣款协议状态
        OrderContractEntity orderContractEntity = orderContractMapper.selectOne(
                new LambdaQueryWrapper<OrderContractEntity>()
                        .eq(OrderContractEntity::getOrderId, fuMinPreviewContractDTO.getOrderId())
                        .eq(OrderContractEntity::getTemplateId, 65)
                        .eq(OrderContractEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderContractEntity::getCreateTime)
                        .last("limit 1")
        );
        if (!ObjUtil.isEmpty(orderContractEntity)){
            OrderContractEntity updateOrderContractInfo = new OrderContractEntity();
            updateOrderContractInfo.setId(orderContractEntity.getId())
                    .setSignStatus(fuMinPreviewContractDTO.getSignStatus());
            orderContractMapper.updateById(updateOrderContractInfo);
        }
        log.info("ContractServiceImpl.fuMinPreviewContract orderContractEntity:{}", JSONUtil.toJsonStr(orderContractEntity));
        log.info("ContractServiceImpl.fuMinPreviewContract updateOrderContractEntity:{}", JSONUtil.toJsonStr(updateOrderContractEntity));
        return orderContractMapper.updateById(updateOrderContractEntity) == 1;
    }

    @Override
    public boolean yingFengUrgeSign(ContractFundSignResulDTO resultDTO) {
        log.info("ContractServiceImpl.yingFengUrgeSign orderId:{}", resultDTO.getOrderId());
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(resultDTO.getOrderId());
        if(ObjUtil.isEmpty(orderInfoEntity)){
            throw new BusinessException("订单不存在");
        }
        //查询签署信息
        List<FundSignInfoEntity> fundSignInfoEntityList = fundSignInfoMapper.selectList(new LambdaQueryWrapper<FundSignInfoEntity>()
                .eq(FundSignInfoEntity::getOrderId, resultDTO.getOrderId())
                .eq(FundSignInfoEntity::getFundId, orderInfoEntity.getFundId())
                .eq(FundSignInfoEntity::getDeleteFlag, 0));
        if(CollUtil.isEmpty(fundSignInfoEntityList)){
            throw new BusinessException("未查到签署信息");
        }
        FundSignInfoEntity fundSignInfoEntity = fundSignInfoEntityList.get(0);
        Result<Boolean> result = approveFeign.yingFengUrgeSign(fundSignInfoEntity.getFundSignId());
        if(!Result.isSuccess(result)){
            throw new BusinessException(result.getMsg());
        }
        log.info("ContractServiceImpl.yingFengUrgeSign result:{}", JSONUtil.toJsonStr(result));
        return result.getData();
    }

    @Override
    public Boolean signSwitch() {
        //获取线上签约开关状态
        SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.ONLINE_SIGN);
        log.info("ContractServiceImpl.signSwitch switchInfo:{}", JSONUtil.toJsonStr(switchInfo));
        Integer switchFlag = switchInfo.getSwitchFlag();
        return switchFlag == 1;
    }

    @Override
    public void saveFuMinContract(FuMinPreviewContractDTO data) {

        log.info("saveFuMinContract data:{}", data);
        int update = orderContractMapper.update(new LambdaUpdateWrapper<OrderContractEntity>()
                .set(OrderContractEntity::getDeleteFlag, 1)
                .eq(OrderContractEntity::getOrderId, data.getOrderId())
                .eq(OrderContractEntity::getTemplateId, data.getTemplateId())
                .eq(OrderContractEntity::getDeleteFlag, 0));

        log.info("saveFuMinContract update {}",update);

        OrderContractEntity contractEntity = new OrderContractEntity();
        contractEntity
                .setOrderId(data.getOrderId())
                .setSignStatus(ContractEnum.SIGNED.getCode())
                .setContractFlag(1)
                .setTemplateId(data.getTemplateId())
                .setResource(data.getResource())
                .setName(data.getName())
                .setNumber(NumberUtils.getContractNumber())
                .setFundGenerateFlag(1);
        orderContractMapper.insert(contractEntity);
    }

    @Override
    public Page<ContractListVO> preview(ContractListDTO contractListDTO) {
        Integer orderId = contractListDTO.getOrderId();

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getId, orderId)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
        );
        Assert.notNull(orderInfoEntity, "订单不存在");

        //获取当前订单所有的合同
        Page<ContractListVO> contractListPage = orderContractMapper.selectJoinPage(new Page<>(contractListDTO.getPageNum(), contractListDTO.getPageSize()),
                ContractListVO.class,
                new MPJLambdaWrapper<OrderContractEntity>()
                        .select(OrderContractEntity::getId,
                                OrderContractEntity::getName,
                                OrderContractEntity::getNumber,
                                OrderContractEntity::getSignStatus,
                                OrderContractEntity::getResource,
                                OrderContractEntity::getContractFlag,
                                OrderContractEntity::getTemplateId)
                        .innerJoin(FundTemplateAssoEntity.class, on -> on
                                .eq(FundTemplateAssoEntity::getTemplateId, OrderContractEntity::getTemplateId)
                                .eq(FundTemplateAssoEntity::getFundId, orderInfoEntity.getFundId())
                        )
                        .innerJoin(FileTemplateInfoEntity.class, on -> on
                                .eq(FileTemplateInfoEntity::getId, OrderContractEntity::getTemplateId))
                        .eq(OrderContractEntity::getOrderId, orderId)
                        .eq(OrderContractEntity::getDeleteFlag, 0)
                        .eq(FileTemplateInfoEntity::getFundSign, 1)
                        .orderByDesc(FundTemplateAssoEntity::getSort)
        );
        return contractListPage;
    }


    /**
     * 查询合同信息
     *
     * @param orderId 订单ID
     * @return 合同信息
     */
    private ContractInfoVO fetchContractInfo(Integer orderId) {
        ContractInfoVO contractInfoVO = orderInfoMapper.selectJoinOne(
                ContractInfoVO.class,
                new MPJLambdaWrapper<OrderInfoEntity>()
                        .selectAll(OrderInfoEntity.class)
                        .selectAs(OrderInfoEntity::getId, ContractInfoVO::getOrderId)
                        .selectAs(OrderAmountEntity::getCustomerConfirmAmount, ContractInfoVO::getApplyAmount)
                        .selectAs(OrderInfoEntity::getApplyPurpose, ContractInfoVO::getApplyPurpose)
                        .selectAs(OrderInfoEntity::getRepayMethod, ContractInfoVO::getRepayMethod)
                        .selectAs(OrderInfoEntity::getTerm, ContractInfoVO::getTerm)
                        .selectAs(OrderInfoEntity::getCreateTime, OrderInfoListVO::getApplyDate)
                        .selectAs(ProductInfoEntity::getName, ContractInfoVO::getProductName)
                        .selectAs(ProductInfoEntity::getYearRate, ContractInfoVO::getYearRate)
                        .selectAs(FundInfoEntity::getName, ContractInfoVO::getFundName)
                        .selectAs(FundSignInfoEntity::getSignStatus, ContractInfoVO::getFundSignStatus)
                        .selectAs(FundSignInfoEntity::getUpdateTime, ContractInfoVO::getContractSignTime)
                        .innerJoin(ProductInfoEntity.class, ProductInfoEntity::getId, OrderInfoEntity::getProductId)
                        .innerJoin(OrderAmountEntity.class, OrderAmountEntity::getOrderId, OrderInfoEntity::getId)
                        .innerJoin(FundInfoEntity.class, FundInfoEntity::getId, OrderInfoEntity::getFundId)
                        .leftJoin(FundSignInfoEntity.class, on -> on
                                .eq(FundSignInfoEntity::getOrderId, OrderInfoEntity::getId)
                                .eq(FundSignInfoEntity::getFundId, OrderInfoEntity::getFundId)
                                .eq(FundSignInfoEntity::getDeleteFlag, 0))
                        .eq(OrderInfoEntity::getId, orderId)
        );
        BankAccountSignEntity bankAccountSignEntity = bankAccountSignMapper.selectOne(new MPJLambdaWrapper<BankAccountSignEntity>()
                .eq(BankAccountSignEntity::getOrderId, orderId)
                .eq(BankAccountSignEntity::getSignState, 1)
                .eq(BankAccountSignEntity::getDeleteFlag, 0)
                .eq(BankAccountSignEntity::getSignPlate, 1)
                .orderByDesc(BankAccountSignEntity::getCreateTime)
                .last("limit 1"));
        contractInfoVO.setBindCardTime(bankAccountSignEntity.getUpdateTime());
        return contractInfoVO;
    }

    /**
     * 设置资金签名状态
     *
     * @param vo 合同信息
     */
    private void setFundSignStatus(ContractInfoVO vo) {
        Long fundSignCount = fundTemplateAssoMapper.selectJoinCount(
                new MPJLambdaWrapper<FundTemplateAssoEntity>()
                        .innerJoin(FileTemplateInfoEntity.class,
                                on -> on
                                        .eq(FileTemplateInfoEntity::getId, FundTemplateAssoEntity::getTemplateId)
                                        .eq(FileTemplateInfoEntity::getStatus, 0)
                                        .eq(FileTemplateInfoEntity::getFundSign, 1)
                                        .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                        )
                        .eq(FundTemplateAssoEntity::getFundId, vo.getFundId())
                        .eq(FundTemplateAssoEntity::getStatus, 0)
                        .eq(FundTemplateAssoEntity::getType, 1)
                        .eq(FundTemplateAssoEntity::getDeleteFlag, 0)
        );

        vo.setFundSign(fundSignCount != null && fundSignCount > 0);

    }

    /**
     * 保存重新启动签约快照
     *
     * @param contractRestartSignEntity 合同重新启动签约实体
     * @param id                        id
     */
    private void saveRestartSignSnapshot(ContractRestartSignEntity contractRestartSignEntity, Integer id) {
        paramsSnapshotMapper.insert(new ParamsSnapshotEntity()
                .setLinkId(id)
                .setType(RESTART_SIGN_SNAPSHOT)
                .setCode(TemplateParamsCodeEnum.CUSTOMER_NAME.getCode())
                .setValue(contractRestartSignEntity.getName())
        );
        paramsSnapshotMapper.insert(new ParamsSnapshotEntity()
                .setLinkId(id)
                .setType(RESTART_SIGN_SNAPSHOT)
                .setCode(TemplateParamsCodeEnum.CUSTOMER_ID_NUMBER.getCode())
                .setValue(contractRestartSignEntity.getIdCardNumber())
        );
        paramsSnapshotMapper.insert(new ParamsSnapshotEntity()
                        .setLinkId(id)
                        .setType(RESTART_SIGN_SNAPSHOT)
                        .setCode("PLACE_OF_SIGNING")
                // 无签订地点信息
                //.setValue(contractRestartSignEntity.getSignAddress())
        );
        paramsSnapshotMapper.insert(new ParamsSnapshotEntity()
                .setLinkId(id)
                .setType(RESTART_SIGN_SNAPSHOT)
                .setCode(TemplateParamsCodeEnum.CUSTOMER_PHONE.getCode())
                .setValue(contractRestartSignEntity.getPhone())
        );
        paramsSnapshotMapper.insert(new ParamsSnapshotEntity()
                .setLinkId(id)
                .setType(RESTART_SIGN_SNAPSHOT)
                .setCode(TemplateParamsCodeEnum.CUSTOMER_EMAIL.getCode())
                .setValue(contractRestartSignEntity.getEmail())
        );
        paramsSnapshotMapper.insert(new ParamsSnapshotEntity()
                .setLinkId(id)
                .setType(RESTART_SIGN_SNAPSHOT)
                .setCode(TemplateParamsCodeEnum.CUSTOMER_ADDRESS.getCode())
                .setValue(contractRestartSignEntity.getAddress())
        );
        paramsSnapshotMapper.insert(new ParamsSnapshotEntity()
                .setLinkId(id)
                .setType(RESTART_SIGN_SNAPSHOT)
                .setCode(TemplateParamsCodeEnum.SIGNING_DATE.getCode())
                .setValue(DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN))
        );

        //反担保协议
        paramsSnapshotMapper.insert(new ParamsSnapshotEntity()
                .setLinkId(id)
                .setType(RESTART_SIGN_SNAPSHOT)
                .setCode(TemplateParamsCodeEnum.LOAN_AGREEMENT_NUMBER.getCode())
                .setValue(contractRestartSignEntity.getOrderNumber())
        );
        paramsSnapshotMapper.insert(new ParamsSnapshotEntity()
                .setLinkId(id)
                .setType(RESTART_SIGN_SNAPSHOT)
                .setCode(TemplateParamsCodeEnum.GUARANTEE_CONTRACT_NUMBER.getCode())
                .setValue(generateContractNumber(TemplateParamsCodeEnum.GUARANTEE_CONTRACT_NUMBER.getCode(), contractRestartSignEntity.getOrderNumber()))
        );
        //品牌
        paramsSnapshotMapper.insert(new ParamsSnapshotEntity()
                .setLinkId(id)
                .setType(RESTART_SIGN_SNAPSHOT)
                .setCode(TemplateParamsCodeEnum.PLEDGED_VEHICLE_BRAND.getCode())
                .setValue(contractRestartSignEntity.getCarBrand())
        );
        //车型
        paramsSnapshotMapper.insert(new ParamsSnapshotEntity()
                .setLinkId(id)
                .setType(RESTART_SIGN_SNAPSHOT)
                .setCode(TemplateParamsCodeEnum.PLEDGED_VEHICLE_MODEL.getCode())
                .setValue(contractRestartSignEntity.getCarModel())
        );
        //发动机
        paramsSnapshotMapper.insert(new ParamsSnapshotEntity()
                .setLinkId(id)
                .setType(RESTART_SIGN_SNAPSHOT)
                .setCode(TemplateParamsCodeEnum.PLEDGED_VEHICLE_ENGINE_NUMBER.getCode())
                .setValue(contractRestartSignEntity.getEngineNumber())
        );
        //车架号
        paramsSnapshotMapper.insert(new ParamsSnapshotEntity()
                .setLinkId(id)
                .setType(RESTART_SIGN_SNAPSHOT)
                .setCode(TemplateParamsCodeEnum.PLEDGED_VEHICLE_VIN.getCode())
                .setValue(contractRestartSignEntity.getVin())
        );

    }

    private static String generateContractNumber(String code, String orderNumber) {
        //        String orderNum = extractPartAfterSeparator(orderNumber);
        String prefix = "";
        switch (code) {
            case "LOAN_AGREEMENT_NUMBER":
                prefix = "LHHF_CP";
                break;
            case "MORTGAGE_CONTRACT_NUMBER":
                prefix = "LHHFDY_CP";
                break;
            case "GUARANTEE_CONTRACT_NUMBER":
                prefix = "LHHFDB_CP";
                break;
            case "COUNTER_GUARANTEE_CONTRACT_NUMBER":
                prefix = "LHHFWTFDB_CP";
                break;
            case "BROKERAGE_SERVICE_CONTRACT_NUMBER":
                prefix = "LHHFJJFW_CP";
                break;
            case "ENTRUSTED_TRANSACTION_CONTRACT_NUMBER":
                prefix = "LHHFWTJY_CP";
                break;
            case "VEHICLE_FINANCIAL_LEASE_CONTRACT_NUMBER":
                prefix = "LHHFZL_CP";
                break;
            case "MOTOR_VEHICLE_MORTGAGE_CONTRACT_NUMBER":
                prefix = "LHHFDY_CP";
                break;
            case "ACCOUNTS_RECEIVABLE_FINANCING_APPROVAL_DOCUMENT_NUMBER":
                prefix = "LHHFRZHZ_CP";
                break;
            case "ACCOUNTS_RECEIVABLE_ASSIGNMENT_NOTICE_NUMBER":
                prefix = "LHHFZRTZ_CP";
                break;
        }

        return prefix + "_" + orderNumber;
    }
    private static String extractPartAfterSeparator(String input) {
        int index = input.indexOf("_");
        if (index != -1) {
            // 找到第二个下划线的位置
            index = input.indexOf("_", index + 1);
            if (index != -1) {
                // 提取下划线后面的部分
                return input.substring(index + 1);
            }
        }
        return ""; // 如果没有找到，则返回空字符串
    }

    @Override
    public Boolean loanPassFundDownLoad(ContractFundSignResulDTO resultDTO) {
        String templateCode = "LH_CY_0001";
        FileTemplateInfoEntity contractTemplateId = fileTemplateInfoMapper.selectOne(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                        .eq(FileTemplateInfoEntity::getTemplateNumber, templateCode)
                        .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FileTemplateInfoEntity::getId)
                , false
        );
        if (ObjUtil.isNull(contractTemplateId)) {
            log.info("ContractServiceImpl.LoanFailDownLoad contractTemplateId:{}",templateCode);
            throw new BusinessException("合同模板不存在");
        }
        List<OrderInfoEntity> orderInfoList = orderInfoMapper.selectJoinList(OrderInfoEntity.class, new MPJLambdaWrapper<OrderInfoEntity>()
                .leftJoin(OrderContractEntity.class, on -> on
                        .eq(OrderContractEntity::getOrderId, OrderInfoEntity::getId)
                        .eq(OrderContractEntity::getTemplateId, contractTemplateId.getId())
                        .eq(OrderContractEntity::getDeleteFlag, 0)
                )
                .eq(OrderInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .ge(OrderInfoEntity::getCurrentNode, States.PAYMENT_SUCCESS.getNode())
                .eq(ObjUtil.isNotNull(resultDTO.getOrderId()), OrderInfoEntity::getId, resultDTO.getOrderId())
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .isNull(OrderContractEntity::getId)
        );

        for (OrderInfoEntity orderInfo : orderInfoList) {
            Integer orderId = orderInfo.getId();
            Integer fundId = orderInfo.getFundId();
            FundEnum fundEnum = FundEnum.getFundEnum(fundId);
            if (ObjUtil.isNull(fundEnum)) {
                log.info("ContractServiceImpl.LoanFailDownLoad not fundEnum:{}",fundId);
                continue;
            }
            if (ObjUtil.equals(fundEnum, FundEnum.CHANG_YIN)) {
                //            orderId = 1407;
                Result<List<ChangYinContractPreviewVO>> listResult = approveFeign.downCardChangeInOutSupply(orderId);
                if ( Result.isSuccess(listResult) && CollUtil.isNotEmpty(listResult.getData()) && ObjUtil.isNotEmpty((listResult.getData().get(0).getResourceId())  )) {
                    OrderContractEntity orderContractEntity = new OrderContractEntity();
                    orderContractEntity.setOrderId(orderId)
                            .setName(ContractTypeEnums.FKSBCXFK.getDescription())
                            .setContractFlag(1)
                            .setSignStatus(2)
                            .setTemplateId(ContractTypeEnums.FKSBCXFK.getTemplateId())
                            .setResource(listResult.getData().get(0).getResourceId())
                            .setNumber(NumberUtils.getContractNumber());
                    orderContractMapper.insert(orderContractEntity);
                }
            }
        }
        return true;
    }

    @Override
    public Boolean createMortgageContract(Integer orderId) {
        String lockKey = getGenerateContractLockKey(orderId);
        String requestId = IdUtil.randomUUID();
        Boolean tryLock = redisService.tryLock(lockKey, requestId, 120);
        if (!tryLock) {
            log.info("ContractServiceImpl.generateContractSignList, lock failed, orderId = {}", lockKey);
            return true;
        }

        //生成合同前删除对应订单合同
        orderContractMapper.update(new LambdaUpdateWrapper<OrderContractEntity>()
                .set(OrderContractEntity::getDeleteFlag, 1)
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .eq(OrderContractEntity::getContractFlag, 3)
        );

        //生成合同前删除原签署任务
        signTaskMapper.update(new LambdaUpdateWrapper<SignTaskEntity>()
                .set(SignTaskEntity::getDeleteFlag, 1)
                .eq(SignTaskEntity::getBusiId, orderId)
                .eq(SignTaskEntity::getSignType, 1)
                .eq(SignTaskEntity::getDeleteFlag, 0));

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);;

        //生成合同快照
        this.initContractSnapshot(orderId);
        int contractState = ContractEnum.WAITING.getCode();
        try {
            Boolean contractFlag = false;
            log.info("ContractServiceImpl.createMortgageContract begin orderId:{}", orderId);
            org.springframework.util.Assert.notNull(orderId, "订单id不能为空");

            org.springframework.util.Assert.notNull(orderInfoEntity, "订单不存在");
            Integer fundId = orderInfoEntity.getFundId();
            List<FundTemplateAssoEntity> fundTemplateAssoList = fundTemplateAssoMapper.selectList(new MPJLambdaWrapper<FundTemplateAssoEntity>()
                    .eq(FundTemplateAssoEntity::getFundId, fundId)
                    .eq(FundTemplateAssoEntity::getStatus, 0)
                    .eq(FundTemplateAssoEntity::getType, 1)
                    .eq(FundTemplateAssoEntity::getDeleteFlag, 0)
                    .eq(FundTemplateAssoEntity::getContractFlag, 3)
            );
            log.info("ContractServiceImpl.createMortgageContract fundTemplateAssoList:{}", fundTemplateAssoList);
            List<Integer> list = fundTemplateAssoList.stream().map(FundTemplateAssoEntity::getTemplateId).toList();
            GenerateContractDTO generateContractDTO = new GenerateContractDTO().setId(orderId).setType(LANHAI_MORTGAGE_SNAPSHOT.getCode()).setTemplateIdList(list);
            Result<List<GenerateContractVO>> listResult = resourceFeign.generateContract(generateContractDTO);
            log.info("ContractServiceImpl.createMortgageContract listResult:{}", JSONUtil.toJsonStr(listResult));
            Map<Integer, List<GenerateContractVO>> generateContract = null;
            if (Result.isSuccess(listResult)) {
                List<GenerateContractVO> data = listResult.getData();
                if (CollUtil.isEmpty(data)) {
                    contractFlag = true;
                    log.error("ContractServiceImpl.createMortgageContract 订单生成合同失败 orderId:{}", orderId);
                }
                //将data转成map
                generateContract = data.stream()
                        .collect(Collectors.groupingBy(GenerateContractVO::getTemplateId));
                log.info("ContractServiceImpl.createMortgageContract generateContract:{}", JSONUtil.toJsonStr(generateContract));
            } else {
                contractFlag = true;
            }
            Map<Integer, List<GenerateContractVO>> generateContractMap = generateContract;
            org.springframework.util.Assert.notNull(generateContractMap, "合同信息不存在");

            //初始化订单合同列表
            List<String> resourceIdList = new ArrayList<>();
            log.info("ContractServiceImpl.createMortgageContract resourceIdList:{}", JSONUtil.toJsonStr(resourceIdList));
            for (FundTemplateAssoEntity fundTemplateAssoEntity : fundTemplateAssoList) {
                List<GenerateContractVO> generateContractInfo = generateContractMap.get(fundTemplateAssoEntity.getTemplateId());
                GenerateContractVO generateContractVO = generateContractInfo.get(0);
                OrderContractEntity orderContractEntity = new OrderContractEntity()
                        .setOrderId(orderId)
                        .setContractFlag(fundTemplateAssoEntity.getContractFlag())
                        .setName(fundTemplateAssoEntity.getTemplateName())
                        .setTemplateId(fundTemplateAssoEntity.getTemplateId())
                        .setSignStatus(0)
                        .setNumber(generateContractVO.getContractNumber())
                        .setResource(generateContractVO.getResourceId())
                        .setFundGenerateFlag(1);
                log.info("ContractServiceImpl.createMortgageContract orderContractEntity:{}", JSONUtil.toJsonStr(orderContractEntity));
                orderContractMapper.insert(orderContractEntity);
                //判断是否需要资方签署
                FileTemplateInfoEntity fileTemplateInfoEntity = fileTemplateInfoMapper.selectById(fundTemplateAssoEntity.getTemplateId());
                if (Objects.equals(fileTemplateInfoEntity.getFundSign(), 0)) {
                    resourceIdList.add(generateContractVO.getResourceId());
                }
            }

            if (contractFlag) {
                log.error("ContractServiceImpl.createMortgageContract 订单生成合同失败 orderId:{}", orderId);
                contractState = ContractEnum.BUILD_FAILED.getCode();
            } else {
                log.info("ContractServiceImpl.createMortgageContract 订单生成合同成功 orderId:{}", orderId);
                contractState = ContractEnum.SIGNING.getCode();
            }


            fadadaAuthService.batchUploadFileToFdd(resourceIdList);

            log.info("ContractServiceImpl.createMortgageContract resourceIdList:{}", resourceIdList);
        } catch (Exception e) {
            contractState = ContractEnum.BUILD_FAILED.getCode();
            log.error("ContractServiceImpl.createMortgageContract e:", e);
            throw new BusinessException("生成合同签约列表失败");
        } finally {

            redisService.releaseLock(lockKey, requestId);
        }
        return true;
    }

    private void initContractSnapshot(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        org.springframework.util.Assert.notNull(orderInfoEntity, "订单不存在");
        //1:清空订单参数快照
        paramsSnapshotMapper.update(new LambdaUpdateWrapper<ParamsSnapshotEntity>()
                .eq(ParamsSnapshotEntity::getLinkId, orderId)
                .eq(ParamsSnapshotEntity::getType, LANHAI_MORTGAGE_SNAPSHOT.getCode())
                .eq(ParamsSnapshotEntity::getDeleteFlag, 0)
                .set(ParamsSnapshotEntity::getDeleteFlag, 1));
        //2：添加快照数据
        List<ParamsSnapshotEntity> list = new ArrayList<>();
        List<OrderArrivedDTO> arrivedDataEntityList = orderArrivedMapper.selectJoinList(
                OrderArrivedDTO.class,
                new MPJLambdaWrapper<OrderArrivedEntity>()
                        .selectAs(ArrivedDataEntity::getMortgageAgentName, OrderArrivedDTO::getMortgageAgentName)
                        .selectAs(ArrivedDataEntity::getMortgageAgentPhone, OrderArrivedDTO::getMortgageAgentPhone)
                        .selectAs(ArrivedDataEntity::getMortgageAgentIdNumber, OrderArrivedDTO::getMortgageAgentIdNumber)
                        .selectAs(OrderArrivedAddressEntity::getPostAddress, OrderArrivedDTO::getPostAddress)
                        .selectAs(OrderArrivedAddressEntity::getPostCode, OrderArrivedDTO::getPostCode)
                        .innerJoin(ArrivedDataEntity.class, on -> on
                                .eq(OrderArrivedEntity::getArrivedId, ArrivedDataEntity::getId)
                                .eq(OrderArrivedEntity::getDeleteFlag, 0))
                        .innerJoin(OrderArrivedAddressEntity.class, on -> on
                                .eq(OrderArrivedAddressEntity::getArrivedId, OrderArrivedEntity::getId))
                        .eq(OrderArrivedEntity::getOrderId, orderId)
                        .eq(OrderArrivedAddressEntity::getType, 1)
                        .eq(OrderArrivedEntity::getDeleteFlag, 0)
        );
        if(CollUtil.isNotEmpty(arrivedDataEntityList)){
            OrderArrivedDTO orderArrivedDTO = arrivedDataEntityList.get(0);
            //借款用途
            addParamsSnapshot(list, orderId, TemplateParamsCodeEnum.MORTGAGE_AGENT_NAME, orderArrivedDTO.getMortgageAgentName());
            addParamsSnapshot(list, orderId, TemplateParamsCodeEnum.MORTGAGE_AGENT_PHONE, orderArrivedDTO.getMortgageAgentPhone());
            addParamsSnapshot(list, orderId, TemplateParamsCodeEnum.MORTGAGE_AGENT_ID_NUMBER, orderArrivedDTO.getMortgageAgentIdNumber());
            addParamsSnapshot(list, orderId, TemplateParamsCodeEnum.MORTGAGE_AGENT_ADDRESS, orderArrivedDTO.getPostAddress());
            addParamsSnapshot(list, orderId, TemplateParamsCodeEnum.MORTGAGE_AGENT_ZIP_CODE, orderArrivedDTO.getPostCode());
            //车辆信息
            VehicleInfoDTO vehicleInfoDTO = new VehicleInfoDTO();
            vehicleInfoDTO.setOrderId(orderId);
            VehicleInfoVO vehicleInfo = orderVehicleInfoService.getVehicleInfo(vehicleInfoDTO);
            addParamsSnapshot(list, orderId, TemplateParamsCodeEnum.PROVINCE_CITY_OF_PLEDGED, vehicleInfo.getLicenseProvince()+vehicleInfo.getLicenseCity());
            addParamsSnapshot(list, orderId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_LICENSE_PLATE_NUMBER, vehicleInfo.getVehicleNumber());
            //签订日期
            addParamsSnapshot(list, orderId, TemplateParamsCodeEnum.SIGNING_DATE, formatDate(LocalDate.now().toString()));
            //PLATE_KIND号牌类型
            String plateKind = vehicleInfo.getVehicleNumber().length() == 7 ? "小型汽车" : "小型新能源汽车";
            addParamsSnapshot(list, orderId, TemplateParamsCodeEnum.PLATE_KIND, plateKind);
            addParamsSnapshot(list, orderId, TemplateParamsCodeEnum.CUSTOMER_NAME, orderInfoEntity.getCustomerName());
        }
        orderParamsSnapshotService.saveBatch(list);
    }
    private void addParamsSnapshot(List<ParamsSnapshotEntity> list, Integer orderId, TemplateParamsCodeEnum code, Object value) {
        ParamsSnapshotEntity entity = new ParamsSnapshotEntity();
        entity.setLinkId(orderId);
        entity.setType(LANHAI_MORTGAGE_SNAPSHOT);
        entity.setCode(code.getCode());
        entity.setValue(Convert.toStr(value));
        list.add(entity);
    }

    @Override
    public Boolean createMortgageRelieveContract(Integer orderId) {
        String lockKey = getGenerateContractLockKey(orderId);
        String requestId = IdUtil.randomUUID();
        Boolean tryLock = redisService.tryLock(lockKey, requestId, 120);
        if (!tryLock) {
            log.info("ContractServiceImpl.createMortgageRelieveContract, lock failed, orderId = {}", lockKey);
            return true;
        }
        //生成合同前删除对应订单合同
        orderContractMapper.update(new LambdaUpdateWrapper<OrderContractEntity>()
                .set(OrderContractEntity::getDeleteFlag, 1)
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .eq(OrderContractEntity::getContractFlag, 4)
        );

        //生成合同前删除原签署任务
        signTaskMapper.update(new LambdaUpdateWrapper<SignTaskEntity>()
                .set(SignTaskEntity::getDeleteFlag, 1)
                .eq(SignTaskEntity::getBusiId, orderId)
                .eq(SignTaskEntity::getSignType, 1)
                .eq(SignTaskEntity::getDeleteFlag, 0));

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);

        //生成合同快照
        this.initRelieveContractSnapshot(orderId);
        int contractState = ContractEnum.WAITING.getCode();
        try {
            Boolean contractFlag = false;
            log.info("ContractServiceImpl.createMortgageRelieveContract begin orderId:{}", orderId);
            org.springframework.util.Assert.notNull(orderId, "订单id不能为空");

            org.springframework.util.Assert.notNull(orderInfoEntity, "订单不存在");
            Integer fundId = orderInfoEntity.getFundId();
            List<FundTemplateAssoEntity> fundTemplateAssoList = fundTemplateAssoMapper.selectList(new MPJLambdaWrapper<FundTemplateAssoEntity>()
                    .eq(FundTemplateAssoEntity::getFundId, fundId)
                    .eq(FundTemplateAssoEntity::getStatus, 0)
                    .eq(FundTemplateAssoEntity::getType, 1)
                    .eq(FundTemplateAssoEntity::getDeleteFlag, 0)
                    .eq(FundTemplateAssoEntity::getContractFlag, 4)
            );
            log.info("ContractServiceImpl.createMortgageRelieveContract fundTemplateAssoList:{}", fundTemplateAssoList);
            List<Integer> list = fundTemplateAssoList.stream().map(FundTemplateAssoEntity::getTemplateId).toList();
            GenerateContractDTO generateContractDTO = new GenerateContractDTO().setId(orderId).setType(LANHAI_MORTGAGE_RELIEVE_SNAPSHOT.getCode()).setTemplateIdList(list);
            Result<List<GenerateContractVO>> listResult = resourceFeign.generateContract(generateContractDTO);
            log.info("ContractServiceImpl.createMortgageRelieveContract listResult:{}", JSONUtil.toJsonStr(listResult));
            Map<Integer, List<GenerateContractVO>> generateContract = null;
            if (Result.isSuccess(listResult)) {
                List<GenerateContractVO> data = listResult.getData();
                if (CollUtil.isEmpty(data)) {
                    contractFlag = true;
                    log.error("ContractServiceImpl.createMortgageRelieveContract 订单生成合同失败 orderId:{}", orderId);
                }
                //将data转成map
                generateContract = data.stream()
                        .collect(Collectors.groupingBy(GenerateContractVO::getTemplateId));
                log.info("ContractServiceImpl.createMortgageRelieveContract generateContract:{}", JSONUtil.toJsonStr(generateContract));
            } else {
                contractFlag = true;
            }
            Map<Integer, List<GenerateContractVO>> generateContractMap = generateContract;
            org.springframework.util.Assert.notNull(generateContractMap, "合同信息不存在");

            //初始化订单合同列表
            List<String> resourceIdList = new ArrayList<>();
            log.info("ContractServiceImpl.createMortgageRelieveContract resourceIdList:{}", JSONUtil.toJsonStr(resourceIdList));
            for (FundTemplateAssoEntity fundTemplateAssoEntity : fundTemplateAssoList) {
                List<GenerateContractVO> generateContractInfo = generateContractMap.get(fundTemplateAssoEntity.getTemplateId());
                GenerateContractVO generateContractVO = generateContractInfo.get(0);
                OrderContractEntity orderContractEntity = new OrderContractEntity()
                        .setOrderId(orderId)
                        .setContractFlag(fundTemplateAssoEntity.getContractFlag())
                        .setName(fundTemplateAssoEntity.getTemplateName())
                        .setTemplateId(fundTemplateAssoEntity.getTemplateId())
                        .setSignStatus(0)
                        .setNumber(generateContractVO.getContractNumber())
                        .setResource(generateContractVO.getResourceId())
                        .setFundGenerateFlag(1);
                log.info("ContractServiceImpl.createMortgageRelieveContract orderContractEntity:{}", JSONUtil.toJsonStr(orderContractEntity));
                orderContractMapper.insert(orderContractEntity);
                //判断是否需要资方签署
                FileTemplateInfoEntity fileTemplateInfoEntity = fileTemplateInfoMapper.selectById(fundTemplateAssoEntity.getTemplateId());
                if (Objects.equals(fileTemplateInfoEntity.getFundSign(), 0)) {
                    resourceIdList.add(generateContractVO.getResourceId());
                }
            }

            if (contractFlag) {
                log.error("ContractServiceImpl.createMortgageRelieveContract 订单生成合同失败 orderId:{}", orderId);
                contractState = ContractEnum.BUILD_FAILED.getCode();
            } else {
                log.info("ContractServiceImpl.createMortgageRelieveContract 订单生成合同成功 orderId:{}", orderId);
                contractState = ContractEnum.SIGNING.getCode();
            }


            fadadaAuthService.batchUploadFileToFdd(resourceIdList);

            log.info("ContractServiceImpl.createMortgageRelieveContract resourceIdList:{}", resourceIdList);
        } catch (Exception e) {
            contractState = ContractEnum.BUILD_FAILED.getCode();
            log.error("ContractServiceImpl.createMortgageContract e:", e);
            throw new BusinessException("生成合同签约列表失败");
        } finally {

            redisService.releaseLock(lockKey, requestId);
        }
        return true;
    }

    private void initRelieveContractSnapshot(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        org.springframework.util.Assert.notNull(orderInfoEntity, "订单不存在");
        //1:清空订单参数快照
        paramsSnapshotMapper.update(new LambdaUpdateWrapper<ParamsSnapshotEntity>()
                .eq(ParamsSnapshotEntity::getLinkId, orderId)
                .eq(ParamsSnapshotEntity::getType, LANHAI_MORTGAGE_RELIEVE_SNAPSHOT.getCode())
                .eq(ParamsSnapshotEntity::getDeleteFlag, 0)
                .set(ParamsSnapshotEntity::getDeleteFlag, 1));
        //2：添加快照数据
        List<ParamsSnapshotEntity> list = new ArrayList<>();
        List<OrderArrivedDTO> arrivedDataEntityList = orderArrivedMapper.selectJoinList(
                OrderArrivedDTO.class,
                new MPJLambdaWrapper<OrderArrivedEntity>()
                        .selectAs(ArrivedDataEntity::getMortgageAgentName, OrderArrivedDTO::getMortgageAgentName)
                        .selectAs(ArrivedDataEntity::getMortgageAgentPhone, OrderArrivedDTO::getMortgageAgentPhone)
                        .selectAs(ArrivedDataEntity::getMortgageAgentIdNumber, OrderArrivedDTO::getMortgageAgentIdNumber)
                        .selectAs(OrderArrivedAddressEntity::getPostAddress, OrderArrivedDTO::getPostAddress)
                        .selectAs(OrderArrivedAddressEntity::getPostCode, OrderArrivedDTO::getPostCode)
                        .innerJoin(ArrivedDataEntity.class, on -> on
                                .eq(OrderArrivedEntity::getRelieveArrivedId, ArrivedDataEntity::getId)
                                .eq(OrderArrivedEntity::getDeleteFlag, 0))
                        .innerJoin(OrderArrivedAddressEntity.class, on -> on
                                .eq(OrderArrivedAddressEntity::getArrivedId, OrderArrivedEntity::getId))
                        .eq(OrderArrivedEntity::getOrderId, orderId)
                        .eq(OrderArrivedAddressEntity::getType, 2)
                        .eq(OrderArrivedEntity::getDeleteFlag, 0)
        );
        if(CollUtil.isNotEmpty(arrivedDataEntityList)){
            OrderArrivedDTO orderArrivedDTO = arrivedDataEntityList.get(0);
            //借款用途
            addParamsRelieveSnapshot(list, orderId, TemplateParamsCodeEnum.MORTGAGE_AGENT_NAME, orderArrivedDTO.getMortgageAgentName());
            addParamsRelieveSnapshot(list, orderId, TemplateParamsCodeEnum.MORTGAGE_AGENT_PHONE, orderArrivedDTO.getMortgageAgentPhone());
            addParamsRelieveSnapshot(list, orderId, TemplateParamsCodeEnum.MORTGAGE_AGENT_ID_NUMBER, orderArrivedDTO.getMortgageAgentIdNumber());
            addParamsRelieveSnapshot(list, orderId, TemplateParamsCodeEnum.MORTGAGE_AGENT_ADDRESS, orderArrivedDTO.getPostAddress());
            addParamsRelieveSnapshot(list, orderId, TemplateParamsCodeEnum.MORTGAGE_AGENT_ZIP_CODE, orderArrivedDTO.getPostCode());
            //车辆信息
            VehicleInfoDTO vehicleInfoDTO = new VehicleInfoDTO();
            vehicleInfoDTO.setOrderId(orderId);
            VehicleInfoVO vehicleInfo = orderVehicleInfoService.getVehicleInfo(vehicleInfoDTO);
            addParamsRelieveSnapshot(list, orderId, TemplateParamsCodeEnum.PROVINCE_CITY_OF_PLEDGED, vehicleInfo.getLicenseProvince() + vehicleInfo.getLicenseCity());
            addParamsRelieveSnapshot(list, orderId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_LICENSE_PLATE_NUMBER, vehicleInfo.getVehicleNumber());
            //签订日期
            addParamsRelieveSnapshot(list, orderId, TemplateParamsCodeEnum.SIGNING_DATE, formatDate(LocalDate.now().toString()));
            //PLATE_KIND号牌类型
            String plateKind = vehicleInfo.getVehicleNumber().length() == 7 ? "小型汽车" : "小型新能源汽车";
            addParamsRelieveSnapshot(list, orderId, TemplateParamsCodeEnum.PLATE_KIND, plateKind);
        }
        orderParamsSnapshotService.saveBatch(list);
    }

    private void addParamsRelieveSnapshot(List<ParamsSnapshotEntity> list, Integer orderId, TemplateParamsCodeEnum code, Object value) {
        ParamsSnapshotEntity entity = new ParamsSnapshotEntity();
        entity.setLinkId(orderId);
        entity.setType(LANHAI_MORTGAGE_RELIEVE_SNAPSHOT);
        entity.setCode(code.getCode());
        entity.setValue(Convert.toStr(value));
        list.add(entity);
    }

    /**
     * 蓝海 资方合同签约
     */
    private void lanHaiFundSign (Integer orderId, Integer fundId) {
        Result<LanHaiContractSignApplyResponse> lanHaiResult = approveFeign.lanHaiContractSignApplyByOrderId(new LanHaiContractSignApplyDTO().setOrderId(orderId));
        if (!Result.isSuccess(lanHaiResult)) {
            throw new BusinessException(lanHaiResult.getMsg());
        }
        FundSignStatusEnum lanHaiSignStatus;
        LanHaiContractSignApplyResponse lanHaiResultData = lanHaiResult.getData();
        String lanHaiDealFlag = lanHaiResultData.getDealFlag();
        if (StrUtil.equals(lanHaiDealFlag, "2")) {
            lanHaiSignStatus = FundSignStatusEnum.FAILURE;
        } else if (StrUtil.equals(lanHaiDealFlag, "1")) {
            lanHaiSignStatus = FundSignStatusEnum.NOT_INITIATED;
        } else {
            lanHaiSignStatus = FundSignStatusEnum.NOT_INITIATED;
        }
        String lanHaiSignId = "LANHAI_"+ orderId + "_" + DateUtil.currentSeconds() + RandomUtil.randomNumbers(6);
        FundSignInfoEntity changYinSignInfo = new FundSignInfoEntity()
                .setOrderId(orderId)
                .setFundId(fundId)
                 .setFundSignId(lanHaiSignId)
                .setSignStatus(lanHaiSignStatus);
        fundSignInfoMapper.insert(changYinSignInfo);
        //保存合同信息
        for (int i = 0; i < lanHaiResultData.getSignatureFiles().size(); i++) {
            LanHaiContractSignApplyResponse.SignatureFile signatory = lanHaiResultData.getSignatureFiles().get(i);
            FundSignDetailEntity fundSignDetailEntity = new FundSignDetailEntity();
            fundSignDetailEntity.setFundSignId(lanHaiSignId)
                    .setSort(String.valueOf(i + 1))
                    .setSignStatus(0)
                    .setSignLink(signatory.getFileName());
            Long count = fundSignDetailMapper.selectCount(new LambdaQueryWrapper<FundSignDetailEntity>()
                    .eq(FundSignDetailEntity::getFundSignId, lanHaiSignId)
                    .eq(FundSignDetailEntity::getName, signatory.getFileName())
                    .eq(FundSignDetailEntity::getDeleteFlag, 0)
            );
            if(count > 0 ){
                fundSignDetailMapper.update(fundSignDetailEntity, new LambdaQueryWrapper<FundSignDetailEntity>()
                        .eq(FundSignDetailEntity::getFundSignId, lanHaiSignId)
                        .eq(FundSignDetailEntity::getSignLink, signatory.getFileName()));
            }else {
                fundSignDetailMapper.insert(fundSignDetailEntity);
            }
        }
        try {
            lanHaiFundSignResult(orderId);
        } catch (Exception e) {
            log.info("ContractServiceImpl.lanHaiFundSign.lanHaiFundSignResult orderId:{} e:{}",  orderId, e.getMessage(), e);
        }
    }

    /**
     * 蓝海 资方合同签约结果
     */
    private FundSignStatusEnum lanHaiFundSignResult( Integer orderId){
        log.info("ContractServiceImpl.lanHaiFundSignResult orderId:{}", orderId);
        FundSignStatusEnum signStatus =  FundSignStatusEnum.NOT_INITIATED;
        //获取签约详情
        FundSignInfoEntity fundSignInfoEntity = fundSignInfoMapper.selectOne(new LambdaQueryWrapper<FundSignInfoEntity>()
                .eq(FundSignInfoEntity::getOrderId, orderId)
                .eq(FundSignInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FundSignInfoEntity::getDeleteFlag, 0)
        );
        if (ObjUtil.isEmpty(fundSignInfoEntity)) {
            log.info("ContractServiceImpl.lanHaiFundSignResult orderId:{} 未申请资方合同签约", orderId);
            throw new BusinessException("请先完成资方合同签约申请");
        }
        List<FundSignDetailEntity> fundSignDetailEntities = fundSignDetailMapper.selectList(new LambdaQueryWrapper<FundSignDetailEntity>()
                .eq(FundSignDetailEntity::getFundSignId, fundSignInfoEntity.getFundSignId())
                .eq(FundSignDetailEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(fundSignDetailEntities)) {
             log.info("ContractServiceImpl.lanHaiFundSignResult orderId:{} 未申请资方合同签约", orderId);
            throw new BusinessException("请先完成资方合同签约申请");
        }
        List<String> signLinkList = fundSignDetailEntities.stream().map(FundSignDetailEntity::getSignLink).toList();
        log.info("ContractServiceImpl.lanHaiFundSignResult orderId:{} signLinkList:{}", orderId, signLinkList);
        LocalDate signTime = fundSignDetailEntities.get(0).getCreateTime().toLocalDate();
        Result<List<LanHaiContractSignFileDTO>> contractSignResult = approveFeign.lanHaiContractSignDownloadByOrderId(new LanHaiContractSignDownloadDTO().setOrderId(orderId).setFileNameList(signLinkList).setSignTime(signTime));
        log.info("ContractServiceImpl.lanHaiFundSignResult orderId:{} contractSignResult:{}", orderId, JSONUtil.toJsonStr(contractSignResult));
        if (!Result.isSuccess(contractSignResult)) {
            throw new BusinessException(contractSignResult.getMsg());
        }
        signStatus = FundSignStatusEnum.SUCCESS;
        List<LanHaiContractSignFileDTO> signFileDTOList = contractSignResult.getData();
        for (LanHaiContractSignFileDTO datum : signFileDTOList) {
            if (ObjUtil.isNull(datum.getContractTemplateId())) {
                 log.info("ContractServiceImpl.lanHaiFundSignResult orderId:{} 资方合同未生成", orderId);
                signStatus = FundSignStatusEnum.NOT_INITIATED;
                continue;
            }
            if (StrUtil.isBlank(datum.getResourceId())) {
                log.info("ContractServiceImpl.lanHaiFundSignResult orderId:{} not resourceId", orderId);
                signStatus = FundSignStatusEnum.NOT_INITIATED;
                continue;
            }
            if (StrUtil.isBlank(datum.getOriginalFileName())) {
                log.info("ContractServiceImpl.lanHaiFundSignResult orderId:{} not originalFileName", orderId);
                signStatus = FundSignStatusEnum.NOT_INITIATED;
                continue;
            }
            //根据模板id更新合同

            OrderContractEntity orderContractUpdate = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                    .eq(OrderContractEntity::getOrderId, orderId)
                    .eq(OrderContractEntity::getTemplateId, datum.getContractTemplateId())
                    .eq(OrderContractEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderContractEntity::getCreateTime)
                    .last("limit 1")
            );
            if (ObjUtil.isNull(orderContractUpdate)) {
                log.info("ContractServiceImpl.lanHaiFundSignResult orderId:{} templateId:{} 合同信息不存在", orderId, datum.getContractTemplateId());
                throw new BusinessException("合同信息不存在");
            }
            //删除原有合同
            orderContractMapper.update(new LambdaUpdateWrapper<OrderContractEntity>()
                    .set(OrderContractEntity::getDeleteFlag, 1)
                    .eq(OrderContractEntity::getId, orderContractUpdate.getId())
                    .eq(OrderContractEntity::getOrderId, orderId));
            //新增
            orderContractUpdate.setId(null);
            orderContractUpdate.setResource(datum.getResourceId());
            orderContractUpdate.setSignStatus(ContractEnum.SIGNED.getCode());
            orderContractUpdate.setCreateTime(null);
            orderContractUpdate.setUpdateTime(null);
            int update = orderContractMapper.insert(orderContractUpdate);

            if (ObjUtil.equals(signStatus, FundSignStatusEnum.SUCCESS) && update == 0) {
                signStatus = FundSignStatusEnum.NOT_INITIATED;
            }
            if (ObjUtil.equals(signStatus,FundSignStatusEnum.SUCCESS)) {
                //更新签约明细
                 fundSignDetailMapper.update(new LambdaUpdateWrapper<FundSignDetailEntity>()
                         .set(FundSignDetailEntity::getSignStatus, 1)
                         .eq(FundSignDetailEntity::getFundSignId, fundSignInfoEntity.getFundSignId())
                         .eq(FundSignDetailEntity::getSignLink, datum.getOriginalFileName())
                         .eq( FundSignDetailEntity::getDeleteFlag, 0)
                 );
            } else {
                //更新签约明细
                fundSignDetailMapper.update(new LambdaUpdateWrapper<FundSignDetailEntity>()
                        .set(FundSignDetailEntity::getSignStatus, 2)
                        .eq(FundSignDetailEntity::getFundSignId, fundSignInfoEntity.getFundSignId())
                        .eq(FundSignDetailEntity::getSignLink, datum.getOriginalFileName())
                        .eq( FundSignDetailEntity::getDeleteFlag, 0)
                );
            }
        }
        if (CollUtil.isEmpty(signFileDTOList)) {
            signStatus = FundSignStatusEnum.NOT_INITIATED;
        }

        log.info("ContractServiceImpl.lanHaiFundSignResult orderId:{} signStatus:{}", orderId, signStatus);
        return signStatus;
    }

    /**
     * 根据签署任务ID获取资方信息
     *
     * @param signTaskId 签署任务ID
     * @return 资方信息
     */
    @Override
    public FundInfoBySignTaskVO getFundInfoBySignTaskId(String signTaskId) {
        log.info("ContractServiceImpl.getFundInfoBySignTaskId signTaskId:{}", signTaskId);

        // 参数校验
        Assert.notBlank(signTaskId, "签署任务ID不能为空");

        // 1. 根据sign_task_id查询lh_order_contract表，获取第一条匹配记录的order_id
        OrderContractEntity orderContract = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getSignTaskId, signTaskId)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .orderByAsc(OrderContractEntity::getId)
                .last("LIMIT 1"));

        if (orderContract == null) {
            throw new BusinessException("未找到对应的合同记录，签署任务ID：" + signTaskId);
        }

        Integer orderId = orderContract.getOrderId();
        log.info("ContractServiceImpl.getFundInfoBySignTaskId signTaskId:{} orderId:{}", signTaskId, orderId);

        // 2. 根据order_id查询订单信息获取fund_id
        OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getId, orderId)
                .eq(OrderInfoEntity::getDeleteFlag, 0));

        if (orderInfo == null) {
            throw new BusinessException("未找到对应的订单信息，订单ID：" + orderId);
        }

        Integer fundId = orderInfo.getFundId();
        if (fundId == null) {
            throw new BusinessException("订单未关联资方信息，订单ID：" + orderId);
        }

        log.info("ContractServiceImpl.getFundInfoBySignTaskId signTaskId:{} orderId:{} fundId:{}", signTaskId, orderId, fundId);

        // 3. 根据fund_id查询资方信息
        FundInfoEntity fundInfo = fundInfoMapper.selectOne(new LambdaQueryWrapper<FundInfoEntity>()
                .eq(FundInfoEntity::getId, fundId)
                .eq(FundInfoEntity::getDeleteFlag, 0));

        if (fundInfo == null) {
            throw new BusinessException("未找到对应的资方信息，资方ID：" + fundId);
        }

        // 4. 封装返回结果
        FundInfoBySignTaskVO result = new FundInfoBySignTaskVO()
                .setOrderId(orderId)
                .setOrderNumber(orderInfo.getOrderNumber())
                .setFundId(fundId)
                .setFundName(fundInfo.getName())
                .setFundCode(fundInfo.getCode())
                .setFundFlag(fundInfo.getFundFlag())
                .setApiEnable(fundInfo.getApiEnable())
                .setMaxMortgage(fundInfo.getMaxMortgage())
                .setDeratingLoanFlag(fundInfo.getDeratingLoanFlag())
                .setSignTaskId(signTaskId)
                .setSignStatus(orderContract.getSignStatus())
                .setContractFlag(orderContract.getContractFlag());

        log.info("ContractServiceImpl.getFundInfoBySignTaskId result:{}", JSONUtil.toJsonStr(result));
        return result;
    }

    @Override
    public String getContractResource(Integer orderId, String name) {
        log.info("ContractServiceImpl.getContractResource start, orderId:{}, name:{}", orderId, name);

        // 参数验证
        Assert.notNull(orderId, "订单ID不能为空");
        if (StrUtil.isBlank(name)) {
            name = "履约告知函";
        }

        // 查询合同信息
        OrderContractEntity contract = orderContractMapper.selectOne(
            new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, orderId)
                .like(OrderContractEntity::getName, name)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .last("LIMIT 1")
        );

        if (contract == null) {
            log.warn("ContractServiceImpl.getContractResource 未找到匹配的合同, orderId:{}, name:{}", orderId, name);
            throw new BusinessException("未找到匹配的合同资源");
        }

        String resource = contract.getResource();
        if (StrUtil.isBlank(resource)) {
            log.warn("ContractServiceImpl.getContractResource 合同资源为空, orderId:{}, name:{}, contractId:{}",
                    orderId, name, contract.getId());
            throw new BusinessException("合同资源为空");
        }

        log.info("ContractServiceImpl.getContractResource success, orderId:{}, name:{}, resource:{}",
                orderId, name, resource);
        return resource;
    }

    @Override
    public String createContract(String templateNumber) {
        String lockKey = getGenerateContractLockKeyByTe(templateNumber);
        String requestId = IdUtil.randomUUID();
        Boolean tryLock = redisService.tryLock(lockKey, requestId, 120);
        if (!tryLock) {
            log.info("ContractServiceImpl.createDbhContract, lock failed, orderId = {}", lockKey);
            return null;
        }

        Integer id = 0;
        //生成合同快照
        if (Objects.equals(templateNumber, "FM_DBH_0001")) {
            id = this.initDbhContractSnapshot();
        }
        try {
            // 富民-担保函-FM_DBH_0001
            FileTemplateInfoEntity fileTemplateInfo = fileTemplateInfoMapper.selectOne(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                    .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                    .eq(FileTemplateInfoEntity::getStatus, 0)
                    .eq(FileTemplateInfoEntity::getType, 1)
                    .eq(FileTemplateInfoEntity::getTemplateNumber, templateNumber), false);

            log.info("ContractServiceImpl.createDbhContract fileTemplateInfo:{}", fileTemplateInfo);
            if (fileTemplateInfo == null) {
                log.error("ContractServiceImpl.createDbhContract 未找到对应的模板信息, templateNumber:{}", templateNumber);
                throw new BusinessException("未找到对应的模板信息");
            }
            //fileTemplateInfo id转为list
            GenerateContractDTO generateContractDTO = new GenerateContractDTO().setId(id).setType(FUMIN_GUARANTEE_SNAPSHOT.getCode()).setTemplateIdList(List.of(fileTemplateInfo.getId()));
            Result<List<GenerateContractVO>> listResult = resourceFeign.generateContract(generateContractDTO);
            log.info("ContractServiceImpl.createDbhContract listResult:{}", JSONUtil.toJsonStr(listResult));
            if (Result.isSuccess(listResult)) {
                List<GenerateContractVO> data = listResult.getData();
                if (CollUtil.isEmpty(data)) {
                    log.error("ContractServiceImpl.createDbhContract 生成合同失败 id:{}", id);
                }
                return data.get(0).getResourceId();
            } else {
                throw new BusinessException("生成合同失败");
            }
        } catch (Exception e) {
            log.error("ContractServiceImpl.createDbhContract e:", e);
            throw new BusinessException("生成合同异常");
        } finally {
            redisService.releaseLock(lockKey, requestId);
        }
    }

    private Integer initDbhContractSnapshot() {
        Integer linkId = 1234;
        //1:清空订单参数快照
        paramsSnapshotMapper.update(new LambdaUpdateWrapper<ParamsSnapshotEntity>()
                .eq(ParamsSnapshotEntity::getLinkId, linkId)
                .eq(ParamsSnapshotEntity::getType, FUMIN_GUARANTEE_SNAPSHOT.getCode())
                .eq(ParamsSnapshotEntity::getDeleteFlag, 0)
                .set(ParamsSnapshotEntity::getDeleteFlag, 1));
        // TODO 2：添加快照数据
        List<ParamsSnapshotEntity> list = new ArrayList<>();
        addParamsDbhSnapshot(list, linkId, TemplateParamsCodeEnum.FUMIN_IOU_NUMBER, "11111");
        addParamsDbhSnapshot(list, linkId, TemplateParamsCodeEnum.GUARANTEE_CONTRACT_NUMBER, "2222");
        addParamsDbhSnapshot(list, linkId, TemplateParamsCodeEnum.SIGNING_DATE, formatDate(LocalDate.now().toString()));
        addParamsDbhSnapshot(list, linkId, TemplateParamsCodeEnum.CUSTOMER_NAME, "测试");
        addParamsDbhSnapshot(list, linkId, TemplateParamsCodeEnum.CUSTOMER_ID_NUMBER, "3213215641654");
        addParamsDbhSnapshot(list, linkId, TemplateParamsCodeEnum.DISBURSEMENT_AMOUNT_NUMERIC, "123");
        addParamsDbhSnapshot(list, linkId, TemplateParamsCodeEnum.DISBURSEMENT_AMOUNT_TEXT, "零壹贰");
        addParamsDbhSnapshot(list, linkId, TemplateParamsCodeEnum.ANNUALIZED_INTEREST_RATE, "0.12%");
        addParamsDbhSnapshot(list, linkId, TemplateParamsCodeEnum.BORROWING_START_DATE, formatDate(LocalDate.now().toString()));
        addParamsDbhSnapshot(list, linkId, TemplateParamsCodeEnum.BORROWING_END_DATE, formatDate(LocalDate.now().toString()));
        orderParamsSnapshotService.saveBatch(list);
        return linkId;
    }

    private void addParamsDbhSnapshot(List<ParamsSnapshotEntity> list, Integer orderId, TemplateParamsCodeEnum code, Object value) {
        ParamsSnapshotEntity entity = new ParamsSnapshotEntity();
        entity.setLinkId(orderId);
        entity.setType(FUMIN_GUARANTEE_SNAPSHOT);
        entity.setCode(code.getCode());
        entity.setValue(Convert.toStr(value));
        list.add(entity);
    }
}
