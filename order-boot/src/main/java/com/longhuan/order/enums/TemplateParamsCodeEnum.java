package com.longhuan.order.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 *模板参数编码枚举
 *
 *@authorchenxinkai
 *@date2024/08/22
 */
@Getter
@AllArgsConstructor
public enum TemplateParamsCodeEnum {
    LOAN_AGREEMENT_NUMBER("LOAN_AGREEMENT_NUMBER", "借款合同编号"),
    CUSTOMER_NAME("CUSTOMER_NAME", "客户姓名"),
    RESIDENT_ID("RESIDENT_ID", "居民身份证"),
    CUSTOMER_ID_NUMBER("CUSTOMER_ID_NUMBER", "客户身份证号码"),
    CUSTOMER_PHONE("CUSTOMER_PHONE", "客户电话"),
    CUSTOMER_EMAIL("CUSTOMER_EMAIL", "客户电子邮箱"),
    CUSTOMER_ADDRESS("CUSTOMER_ADDRESS", "客户住址"),
    CUSTOMER_ZIP_CODE("CUSTOMER_ZIP_CODE", "客户住址邮编"),
    CUSTOMER_HUKOU_ADDRESS("CUSTOMER_HUKOU_ADDRESS", "客户户籍地址"),
    FINANCING_PARTY_CREDIT_LIMIT("FINANCING_PARTY_CREDIT_LIMIT", "资方授信额度"),
    FINANCING_PARTY_CREDIT_TEXT("FINANCING_PARTY_CREDIT_TEXT", "资方授信额度大写"),
    CUSTOMER_SIGNATURE("CUSTOMER_SIGNATURE", "客户签字"),
    SIGNING_DATE("SIGNING_DATE", "签订日期"),
    LOAN_EXPIRY_DATE("LOAN_EXPIRY_DATE", "贷款到日期"),
    LOAN_EXPIRY_DATE_YMD("LOAN_EXPIRY_DATE_YMD", "贷款到日期"),
    TERM("TERM", "期数"),
    DISBURSEMENT_AMOUNT_NUMERIC("DISBURSEMENT_AMOUNT_NUMERIC", "放款金额（小写）"),
    DISBURSEMENT_AMOUNT_TEXT("DISBURSEMENT_AMOUNT_TEXT", "放款金额（大写）"),
    EFFECTIVE_ANNUAL_INTEREST_RATE("EFFECTIVE_ANNUAL_INTEREST_RATE", "执行年利率"),
    LOAN_PRIME_RATE_LPR("LOAN_PRIME_RATE_LPR", "LPR"),
    BASIS_POINTS("BASIS_POINTS", "基点"),
    ANNUALIZED_COMPREHENSIVE_ACTUAL_RATE("ANNUALIZED_COMPREHENSIVE_ACTUAL_RATE", "年化综合实际利率"),
    CUSTOMER_SEAL("CUSTOMER_SEAL", "客户签章"),
    FINANCING_PARTY_SEAL("FINANCING_PARTY_SEAL", "资方签章"),
    MORTGAGE_CONTRACT_NUMBER("MORTGAGE_CONTRACT_NUMBER", "抵押合同编号"),
    VEHICLE("VEHICLE", "车辆"),
    PLEDGED_VEHICLE_BRAND("PLEDGED_VEHICLE_BRAND", "抵押车辆品牌"),
    PLEDGED_VEHICLE_MODEL("PLEDGED_VEHICLE_MODEL", "抵押车辆型号"),
    PLEDGED_VEHICLE_LICENSE_PLATE_NUMBER("PLEDGED_VEHICLE_LICENSE_PLATE_NUMBER", "抵押车辆车牌号"),
    PLEDGED_VEHICLE_ENGINE_NUMBER("PLEDGED_VEHICLE_ENGINE_NUMBER", "抵押车辆发动机号"),
    PLEDGED_VEHICLE_VIN("PLEDGED_VEHICLE_VIN", "抵押车辆车架号"),
    PLEDGED_VEHICLE_COLOR("PLEDGED_VEHICLE_COLOR", "抵押车辆车身颜色"),
    PLEDGED_VEHICLE_SOFT_EVALUATION_AMOUNT("PLEDGED_VEHICLE_SOFT_EVALUATION_AMOUNT", "抵押车辆软评额度"),
    TERM_PLUS_MONTH("TERM_PLUS_MONTH", "期数+月"),
    PROVINCE_CITY_OF_PLEDGED_VEHICLE_LICENSE_PLATE("PROVINCE_CITY_OF_PLEDGED_VEHICLE_LICENSE_PLATE", "抵押车辆车牌归属地省市"),
    FINANCING_PARTY_CONTACT_ADDRESS("FINANCING_PARTY_CONTACT_ADDRESS", "资方联系地址"),
    FINANCING_PARTY_CONTACT_NUMBER("FINANCING_PARTY_CONTACT_NUMBER", "资方联系电话"),
    PLACE_OF_SIGNING("PLACE_OF_SIGNING", "签订地点"),
    PLACE_OF_PERFORMANCE("PLACE_OF_PERFORMANCE", "合同履行地"),
    GUARANTEE_CONTRACT_NUMBER("GUARANTEE_CONTRACT_NUMBER", "担保合同编号"),
    GUARANTEE_FEE("GUARANTEE_FEE", "担保费"),
    GUARANTEE_FEE_TEXT("GUARANTEE_FEE_TEXT", "担保费大写"),
    GUARANTOR_COMPANY_NAME("GUARANTOR_COMPANY_NAME", "担保公司名称"),
    GUARANTOR_COMPANY_LEGAL_REPRESENTATIVE("GUARANTOR_COMPANY_LEGAL_REPRESENTATIVE", "担保公司法定代表人"),
    GUARANTOR_COMPANY_ADDRESS("GUARANTOR_COMPANY_ADDRESS", "担保公司地址"),
    GUARANTOR_COMPANY_SEAL("GUARANTOR_COMPANY_SEAL", "担保公司签章"),
    COUNTER_GUARANTEE_CONTRACT_NUMBER("COUNTER_GUARANTEE_CONTRACT_NUMBER", "委托反担保合同编号"),
    LHHF_CO_LTD("LHHF_CO_LTD", "龙环汇丰信息咨询（北京）有限公司"),
    LHHF_CO_LTD_SEAL("LHHF_CO_LTD_SEAL", "龙环汇丰公章"),
    BROKERAGE_SERVICE_CONTRACT_NUMBER("BROKERAGE_SERVICE_CONTRACT_NUMBER", "《居间服务合同》编号"),
    GPS_SERVICE_FEE_PLUS_MORTGAGE_HANDLING_FEE("GPS_SERVICE_FEE_PLUS_MORTGAGE_HANDLING_FEE", "GPS服务费+抵押手续费"),
    GPS_SERVICE_FEE_PLUS_MORTGAGE_HANDLING_FEE_TEXT("GPS_SERVICE_FEE_PLUS_MORTGAGE_HANDLING_FEE_TEXT", "GPS服务费+抵押手续费大写"),
    MONTHLY_REPAYMENT_AMOUNT("MONTHLY_REPAYMENT_AMOUNT", "客户月还金额"),
    MONTHLY_REPAYMENT_AMOUNT_LAST("MONTHLY_REPAYMENT_AMOUNT_LAST", "最后一期还款金额"),
    REPAYMENT_DATE("REPAYMENT_DATE", "还款日"),
    REMAINING_PRINCIPAL("REMAINING_PRINCIPAL", "剩余本金"),
    CUSTOMER_BANK_ACCOUNT_NAME("CUSTOMER_BANK_ACCOUNT_NAME", "客户银行卡户名"),
    CUSTOMER_BANK_BRANCH("CUSTOMER_BANK_BRANCH", "客户银行卡开户行"),
    CUSTOMER_BANK_ACCOUNT_NUMBER("CUSTOMER_BANK_ACCOUNT_NUMBER", "客户银行卡账号"),
    ENTRUSTED_TRANSACTION_CONTRACT_NUMBER("ENTRUSTED_TRANSACTION_CONTRACT_NUMBER", "委托交易合同编号"),
    SIGNING_DATE_PLUS_3_YEARS("SIGNING_DATE_PLUS_3_YEARS", "签订日+3年"),
    SIGNING_DATE_PLUS_3_YEARS_YMD("SIGNING_DATE_PLUS_3_YEARS_YMD", "签订日+3年"),
    HNRT_CO_LTD("HNRT_CO_LTD", "河南融通拍卖有限公司"),
    HNRT_CO_LTD_SEAL("HNRT_CO_LTD_SEAL", "河南融通拍卖有限公司公章"),
    LOCK_IN_PERIOD("LOCK_IN_PERIOD", "封闭期"),
    LOCK_IN_PERIOD_1("LOCK_IN_PERIOD_1", "封闭期/2"),
    LOCK_IN_PERIOD_2("LOCK_IN_PERIOD_2", "封闭期/2+1"),
    GPS_FEE("GPS_FEE", "GPS费用"),
    HBLHDT_TECHNOLOGY_CO_LTD("HBLHDT_TECHNOLOGY_CO_LTD", "河北龙环谛听网络科技有限公司"),
    HBLHDT_TECHNOLOGY_CO_LTD_SEAL("HBLHDT_TECHNOLOGY_CO_LTD_SEAL", "河北龙环谛听网络科技有限公司公章"),
    ONE_TIME_BROKERAGE_SERVICE_FEE_AMOUNT("ONE_TIME_BROKERAGE_SERVICE_FEE_AMOUNT", "一次性支付居间服务费金额"),
    INSTALLMENT_BROKERAGE_SERVICE_FEE_AMOUNT("INSTALLMENT_BROKERAGE_SERVICE_FEE_AMOUNT", "分期居间服务费金额"),
    TOTAL_INSTALLMENT_BROKERAGE_SERVICE_FEE_AMOUNT("TOTAL_INSTALLMENT_BROKERAGE_SERVICE_FEE_AMOUNT", "分期居间服务费总金额"),
    INSTALLMENT_BROKERAGE_TERM("INSTALLMENT_BROKERAGE_TERM", "居间服务费期数"),
    MORTGAGEE("MORTGAGEE", "抵押权人"),
    MORTGAGEE_ADDRESS("MORTGAGEE_ADDRESS", "抵押权人地址"),
    MORTGAGEE_CONTACT_NUMBER("MORTGAGEE_CONTACT_NUMBER", "抵押权人联系电话"),
    MORTGAGEE_CONTACT_EMAIL("MORTGAGEE_CONTACT_EMAIL", "抵押权人联系邮箱"),
    MORTGAGEE_ZIP_CODE("MORTGAGEE_ZIP_CODE", "抵押权人邮编"),
    MORTGAGEE_UNIFIED_SOCIAL_CREDIT_CODE("MORTGAGEE_UNIFIED_SOCIAL_CREDIT_CODE", "抵押权人统一社会信用代码"),
    MORTGAGEE_1("MORTGAGEE_1", "抵押权人"),
    MORTGAGEE_ADDRESS_1("MORTGAGEE_ADDRESS_1", "抵押权人地址1"),
    MORTGAGEE_CONTACT_NUMBER_1("MORTGAGEE_CONTACT_NUMBER_1", "抵押权人联系电话1"),
    MORTGAGEE_CONTACT_EMAIL_1("MORTGAGEE_CONTACT_EMAIL_1", "抵押权人联系邮箱1"),
    MORTGAGEE_ZIP_CODE_1("MORTGAGEE_ZIP_CODE_1", "抵押权人邮编1"),
    MORTGAGEE_UNIFIED_SOCIAL_CREDIT_CODE_1("MORTGAGEE_UNIFIED_SOCIAL_CREDIT_CODE_1", "抵押权人统一社会信用代码1"),
    VEHICLE_FINANCIAL_LEASE_CONTRACT_NUMBER("VEHICLE_FINANCIAL_LEASE_CONTRACT_NUMBER", "车辆融资租赁合同编号"),
    MORTGAGEE_SEAL("MORTGAGEE_SEAL", "抵押权人公章"),
    FINANCIAL_LEASE_AMOUNT("FINANCIAL_LEASE_AMOUNT", "融租租赁金额"),
    FINANCIAL_LEASE_TEXT("FINANCIAL_LEASE_TEXT", "融租租赁金额大写"),
    INITIAL_REGISTRATION_DATE("INITIAL_REGISTRATION_DATE", "初次登记日期"),
    ACTUAL_LEASE_ANNUALIZED_INTEREST_RATE("ACTUAL_LEASE_ANNUALIZED_INTEREST_RATE", "实际租赁年化利率"),
    INITIAL_RENT_CNY("INITIAL_RENT_CNY", "初始租金（元）"),
    RENT_CNY("RENT_CNY", "租金（元）"),
    BUYOUT_AMOUNT_AFTER_CURRENT_RENT_PAYMENT_CNY("BUYOUT_AMOUNT_AFTER_CURRENT_RENT_PAYMENT_CNY", "当期租金支付后提前回购金额(元)"),
    MOTOR_VEHICLE_MORTGAGE_CONTRACT_NUMBER("MOTOR_VEHICLE_MORTGAGE_CONTRACT_NUMBER", "机动车抵押合同编号"),
    FUND_AGREEMENT_NUMBER("FUND_AGREEMENT_NUMBER", "资方合同编号"),
    ACCOUNTS_RECEIVABLE_FINANCING_APPROVAL_DOCUMENT_NUMBER("ACCOUNTS_RECEIVABLE_FINANCING_APPROVAL_DOCUMENT_NUMBER", "应收账款融资核准书编号"),
    ACCOUNTS_RECEIVABLE_ASSIGNMENT_NOTICE_NUMBER("ACCOUNTS_RECEIVABLE_ASSIGNMENT_NOTICE_NUMBER", "应收账款转让通知书编号"),
    ENTRUSTMENT_GUARANTEE_CONTRACT_NUMBER("ENTRUSTMENT_GUARANTEE_CONTRACT_NUMBER", "委托担保合同编号"),
    //贷款期限/12
    TERM_YEAR("TERM_YEAR", "贷款期数（年）"),
    TOTAL_LEASE_PRINCIPAL_AND_INTEREST("TOTAL_LEASE_PRINCIPAL_AND_INTEREST", "融租总本息"),
    TOTAL_LEASE_PRINCIPAL_AND_INTEREST_TEXT("TOTAL_LEASE_PRINCIPAL_AND_INTEREST_TEXT", "融租总本息（大写）"),
    REPAYMENT_DATE_NUMBER("REPAYMENT_DATE_NUMBER", "还款单日"),
    SIGNING_DATE_DAY("SIGNING_DATE_DAY", "签订日期(日)"),
    FUND_CREDIT_APPLY_NO("FUND_CREDITAPPLYNO", "资方业务编号"),
    PRE_CREDIT_AUTH_NUMBER("PRE_CREDIT_AUTH_NUMBER", "个人征信授权书编号"),
    MORTGAGE_COMPANY("MORTGAGE_COMPANY","加押公司"),
    SETTLEMENT_NUMBER("SETTLEMENT_NUMBER","结清编号"),
    SETTLEMENT_DATE("SETTLEMENT_DATE","结清日期"),
    SETTLEMENT_CONTRACT_NO_AND_MORTGAGE_NO("SETTLEMENT_CONTRACT_NO_AND_MORTGAGE_NO","单据编号"),
    FINAL_AMOUNT("FINAL_AMOUNT","最终额度"),
    LOAN_CONTRACT_NUMBER("LOAN_CONTRACT_NUMBER","借款合同编号"),
    REPURCHASE_TOTAL_AMT("REPURCHASE_TOTAL_AMT","赎回总额"),
    RONGDAN_COMPANY("RONGDAN_COMPANY","融担公司"),

    LOAN_DATE_DAY("LOAN_DATE_DAY","放款日期"),
    DEBT_ASSIGNEE_NAME("DEBT_ASSIGNEE_NAME","债权受让方"),
    DEBT_ASSIGNEE_ADDRESS("DEBT_ASSIGNEE_ADDRESS","债权受让方住所地"),
    DEBT_ASSIGNEE_LEGAL_PERSON("DEBT_ASSIGNEE_LEGAL_PERSON","债权受让方法人"),
    TRANSFER_CONTRACT_NUMBER("TRANSFER_CONTRACT_NUMBER","债权转让协议编号"),
    TRANSFER_DETAIL_NUMBER("TRANSFER_DETAIL_NUMBER","债权转让明细编号"),
    REMAINING_INTEREST("REMAINING_INTEREST", "剩余利息"),
    DEBT_TRANSFER_DATE("DEBT_TRANSFER_DATE", "债权转让日期"),
    TRANSFER_PRICE("TRANSFER_PRICE", "债权转让对价"),
    APPLY_PURPOSE("APPLY_PURPOSE", "借款用途"),
    PRODUCT_NAME("PRODUCT_NAME", "产品名称"),
    REPAY_METHOD("REPAY_METHOD", "还款方式"),
    //融担公司
    RONGDAN_COMPANY_NAME("RONGDAN_COMPANY_NAME","融担公司名称"),
    CUSTOMER_ORDER_NUMBER("CUSTOMER_ORDER_NUMBER","客户订单编号"),
    //车辆价值
    SOFT_REVIEW_AMOUNT("SOFT_REVIEW_AMOUNT","软评额度"),
    PLATE_KIND("PLATE_KIND","号牌种类"),
    MORTGAGE_AGENT_ADDRESS("MORTGAGE_AGENT_ADDRESS","抵押代理人地址"),
    MORTGAGE_AGENT_PHONE("MORTGAGE_AGENT_PHONE","抵押代理人电话"),
    MORTGAGE_AGENT_ZIP_CODE("MORTGAGE_AGENT_ZIP_CODE","抵押代理人邮编"),
    MORTGAGE_AGENT_NAME("MORTGAGE_AGENT_NAME","抵押代理人姓名"),
    MORTGAGE_AGENT_ID_NUMBER("MORTGAGE_AGENT_ID_NUMBER","抵押代理人身份证"),
    PROVINCE_CITY_OF_PLEDGED("PROVINCE_CITY_OF_PLEDGED","上牌地省市"),
    //合同岗要求：个人借款委托担保合同：LHHFWTDB-CP
    LANHAI_GUARANTEE_CONTRACT_NUMBER("LANHAI_GUARANTEE_CONTRACT_NUMBER","担保合同编号"),
    GPS_CONTENT_TEXT("GPS_CONTENT_TEXT", "客户告知函内容"),
    //蓝海 差异化定价
    ANNUALIZED_INTEREST_RATE("ANNUALIZED_INTEREST_RATE", "年利率"),
    ANNUALIZED_RATE_OF_GUARANTEE_FEE("ANNUALIZED_RATE_OF_GUARANTEE_FEE", "担保费年化费率"),
    TOTAL_AMOUNT_OF_GUARANTEE_FEE("TOTAL_AMOUNT_OF_GUARANTEE_FEE","担保费总金额"),
    TOTAL_AMOUNT_OF_GUARANTEE_FEE_TO_STR("TOTAL_AMOUNT_OF_GUARANTEE_FEE_TO_STR","担保费总金额大写"),
    REMAINING_PAYABLE_GUARANTEE_FEES("REMAINING_PAYABLE_GUARANTEE_FEES", "剩余应付担保费金额"),
    REMAINING_PAYABLE_GUARANTEE_FEES_TO_STR("REMAINING_PAYABLE_GUARANTEE_FEES_TO_STR", "剩余应付担保费金额大写"),
    INSTALLMENT_OF_GUARANTEE_FEE("INSTALLMENT_OF_GUARANTEE_FEE", "担保费分期"),
    //富民担保函
    FUMIN_IOU_NUMBER("FUMIN_IOU_NUMBER", "借据号"),
    BORROWING_START_DATE("BORROWING_START_DATE", "借款开始日期"),
    BORROWING_END_DATE("BORROWING_END_DATE", "借款结束日期"),
    ;
    @EnumValue
    @JsonValue
    private final String code;
    private final String value;
}
