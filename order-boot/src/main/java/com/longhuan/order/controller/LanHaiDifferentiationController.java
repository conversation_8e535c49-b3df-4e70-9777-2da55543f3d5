package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.ResetProductDTO;
import com.longhuan.order.service.LanHaiDifferentiationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 蓝海差异化定价
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/differentiation")
@RequiredArgsConstructor
public class LanHaiDifferentiationController {
    private final LanHaiDifferentiationService lanHaiDifferentiationService;

    /**
     * 是否需要重新选择产品
     */
    @GetMapping("/isResetProduct/{orderId}")
    public Result<Boolean> isResetProduct(@PathVariable("orderId") Integer orderId) {
        return Result.success(lanHaiDifferentiationService.isResetProduct(orderId));
    }

    /**
     * 重新选择产品
     */
    @PostMapping("/resetProduct")
    public Result<Boolean> resetProduct(@RequestBody ResetProductDTO resetProductDTO) {
        return Result.success(lanHaiDifferentiationService.resetProduct(resetProductDTO));
    }
}
