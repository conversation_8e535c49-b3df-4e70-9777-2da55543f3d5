package com.longhuan.order.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.approve.api.pojo.dto.FuMinPreviewContractDTO;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.vo.*;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * 合同服务
 *
 * <AUTHOR>
 * @date 2024/08/21
 */
public interface ContractService {

    Page<ContractListVO> list(ContractListDTO contractList);




    List<ContractResourceVO> getContractUrls(ContractUrlsDTO contractUrlsDTO);

    boolean getFundUploadFileInfo(List<ContractToFundVO> fundVOList);

    boolean getFundDownloadFile();

    boolean updateOrderContactStatus();

    Boolean restartSign(RestartContactDTO restartContactDTO);

    /**
     * 客户合同签约信息
     */
    ContractInfoVO getContractInfo(Integer orderId);

    @Async
    void asyncFundSignSubmit(ContractFundSignSubmitDTO submitDTO);

    /**
     * 资方签署提交
     */
    ContractFundSignVO fundSignSubmit(ContractFundSignSubmitDTO submitDTO);

    String getFundSignSubmitLockKey(Integer orderId);

    /**
     * 资方签署结果
     * @param resulDTO
     */
    ContractFundSignResulVO getFundSignVOResult(ContractFundSignResulDTO resulDTO);

    /**
     * 更新合同签署状态
     * @param signStatusDTO
     * @return
     */
    boolean updateSignStatus(UpdateContractSignStatusDTO signStatusDTO);

    String previewComprehensiveContract(Integer orderId);

    /**
     * 更新富民合同状态
     *
     * @param fuMinPreviewContractDTO 去预览合同 DTO
     * @return {@link Boolean }
     */
    Boolean fuMinPreviewContract(FuMinPreviewContractDTO fuMinPreviewContractDTO);

    /**
     * 盈峰合同催签
     * @param resultDTO
     * @return
     */
    boolean yingFengUrgeSign(ContractFundSignResulDTO resultDTO);

    /**
     * 是否开启线上签约合同
     */
    Boolean signSwitch();

    void saveFuMinContract(FuMinPreviewContractDTO data);

    /**
     * 资方合同预览
     * @param contractListDTO
     * @return
     */
    Page<ContractListVO> preview(ContractListDTO contractListDTO);

    /**
     * 放款后合同下载
     * @param resultDTO
     * @return
     */
    Boolean loanPassFundDownLoad(ContractFundSignResulDTO resultDTO);

    /**
     * 生成蓝海抵押合同
     * @param orderId
     * @return
     */
    Boolean createMortgageContract(Integer orderId);

    /**
     * 生成蓝海抵押解除合同
     * @param orderId
     * @return
     */
    Boolean createMortgageRelieveContract(Integer orderId);

    /**
     * 根据签署任务ID获取资方信息
     * @param signTaskId 签署任务ID
     * @return 资方信息
     */
    FundInfoBySignTaskVO getFundInfoBySignTaskId(String signTaskId);

    /**
     * 根据订单ID和合同名称查询合同资源
     * @param orderId 订单ID
     * @param name 合同名称（支持模糊查询）
     * @return 合同资源ID
     */
    String getContractResource(Integer orderId, String name);

    /**
     * 创建指定合同
     * @param templateNumber 模板编号
     * @return 是否成功
     */
    String createContract(String templateNumber);
}
