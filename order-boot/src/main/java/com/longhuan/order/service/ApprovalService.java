package com.longhuan.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.user.pojo.vo.RegionInfoVO;
import com.longhuan.user.pojo.vo.UserDetailInfoVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * 预先批准服务
 *
 * <AUTHOR>
 * @date 2024/08/13
 */
public interface ApprovalService {
    Page<ApprovalHistoryVO> approvalHistory(OrderRecordDTO historyDTO, LoginUser currentUser);

    /**
     * 审批历史（支持预审和订单合并查询）
     *
     * @param queryDTO 查询参数
     * @param currentUser 当前用户
     * @return 审批历史分页结果
     */
    Page<ApprovalHistoryVO> approvalHistoryCombined(ApprovalHistoryQueryDTO queryDTO, LoginUser currentUser);

    SubmitResultVO riskOrderAssign(int orderId, int userId);

    SubmitResultVO approvalSubmit(ApprovalSubmitDTO submitDTO, LoginUser currentUser);

    Events getEventsAndValidate(Integer result, States node, States backNode, Integer orderId, Integer storeId, Integer appraiserId, String appraiserName);

    SubmitResultVO orderSubmit(OrderSubmitDTO submitDTO, LoginUser currentUser);

    SubmitValidateVO qualityInspectionSubmitValidate(Integer orderId);

    List<OrderPageVO> orderPageStatus(OrderRecordDTO orderRecordDTO);

    void initialOrderStates(Integer orderId);

    /**
     * 列表
     *
     * @param preApprovalListDTO 预先批准清单 DTO
     * @param loginUserInfo      登录用户信息
     * @return {@link Page }<{@link PreApprovalListVO }>
     */
    IPage<PreApprovalListVO> list(PreApprovalListDTO preApprovalListDTO, LoginUser loginUserInfo);

    /**
     * 获取除申请人外其他信息
     *
     * @param preId 预申请id
     * @return {@link PreApprovalApplyInfoVO}
     */

    PreApprovalApplyInfoVO queryByOtherInfo(Integer preId);

    /**
     * 获取申请人信息
     *
     * @param preId 预申请id
     * @return {@link PreApprovalApplyInfoVO}
     */

    PreApprovalApplyInfoVO queryById(Integer preId);

    String batchUpdateFundsFinalResult();

    /**
     * 订单状态
     *
     * @param orderIdDTO 订单 ID DTO
     * @return {@link OrderApprovalStatusVO }
     */
    OrderApprovalStatusVO orderStatus(OrderIdDTO orderIdDTO);

    SubmitResultVO orderCancel(OrderSubmitDTO submitDTO, LoginUser currentUser);

    SubmitValidateVO approvalSubmitValidate(ApprovalSubmitValidateDTO submitDTO);

    SubmitResultVO orderSettled(OrderIdDTO orderIdDTO);

    void exportList(PreApprovalListDTO preApprovalListDTO, LoginUser loginUserInfo, HttpServletResponse response);

    List<RegionInfoVO> regionList();


    ResponseEntity<byte[]> userToAuth(Integer preId, LoginUser loginUser);

    UserDetailInfoVO getManagerInfo(LoginUser loginUser);

    /**
     * 获取蓝海极证云涉诉命中提醒
     * @param orderId
     * @param loginUserInfo
     * @return
     */
    String riskReviewPreWarn(Integer orderId, LoginUser loginUserInfo);

    Boolean updateStatus(Integer orderId, Integer pageId, Integer status);
}
