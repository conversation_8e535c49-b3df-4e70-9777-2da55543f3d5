package com.longhuan.order.statemachine;


public class StateConstants {
    private StateConstants() {
        throw new IllegalStateException("Utility class");
    }

    public static final String ORDER_ID = "ORDER_ID";
    public static final String USER_ID = "USER_ID";
    public static final String REMARK = "REMARK";
    public static final String REMARK_EXTERNAL = "REMARK_EXTERNAL";
    public static final String REJECT_REASON = "REJECT_REASON";
    public static final String REJECT_REASON_SECOND = "REJECT_REASON_SECOND";

}
