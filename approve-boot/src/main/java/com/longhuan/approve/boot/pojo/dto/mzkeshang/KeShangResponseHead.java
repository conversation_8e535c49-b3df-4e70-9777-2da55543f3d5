package com.longhuan.approve.boot.pojo.dto.mzkeshang;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.longhuan.approve.boot.pojo.dto.lanhai.LanHaiResult;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 梅州客商接口响应头实体类
 */
@Data
@Accessors(chain = true)
public class KeShangResponseHead {

    /**
     * 版本号
     */
    @JsonProperty("version")
    private String version;

    /**
     * 商户编号
     */
    @JsonProperty("mer_id")
    private String merId;

    /**
     * 接口ID
     */
    @JsonProperty("api_id")
    private String apiId;

    /**
     * 交易流水
     */
    @JsonProperty("trans_no")
    private String transNo;

    /**
     * 响应渠道
     * GMCD/ZLHJ/LHCD
     */
    @JsonProperty("rsp_channel_no")
    private String rspChannelNo;

    /**
     * 响应时间
     * yyyyMMddhi24mmss
     */
    @JsonProperty("rsp_time")
    private String rspTime;

    /**
     * 签名
     * 用于验证报文完整性
     */
    @JsonProperty("sign")
    private String sign;

    /**
     * 签名类型
     * SM2
     */
    @JsonProperty("sign_type")
    private String signType;

    /**
     * 错误码
     * 0000表示成功，9999表示失败
     */
    @JsonProperty("ret_code")
    private String retCode;

    /**
     * 错误信息
     * 错误信息描述，成功时为空或者"成功"
     */
    @JsonProperty("ret_msg")
    private String retMsg;

    /**
     * 报文体内容
     * SM4加密JSON数据
     */
    @JsonProperty("info_content")
    private String infoContent;

    public static boolean isSuccess(KeShangResponseHead result) {
        boolean flag = false;
        if (ObjectUtil.isNotEmpty(result)  && ObjectUtil.equals("0000",result.getRetCode())){
            //todo infoContent
            if (ObjectUtil.isNotEmpty(result.getInfoContent()) ){
                flag = true;
            }
        }
        return flag;
    }
}
