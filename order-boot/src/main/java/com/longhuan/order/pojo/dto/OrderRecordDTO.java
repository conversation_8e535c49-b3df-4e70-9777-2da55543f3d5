package com.longhuan.order.pojo.dto;

import com.longhuan.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("订单页签查询")
public class OrderRecordDTO extends BasePageQuery {
    @ApiModelProperty("进件Id")
    private Integer orderId;
    /**
     * 节点
     * 1000: 补录
     * 3000: 客户预约
     * 2700: 客户确认
     */
    private Integer node;


}
