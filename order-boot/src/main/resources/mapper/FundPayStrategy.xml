<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.longhuan.order.mapper.OrderVehicleInfoMapper">
    <select id="getApprovalVehicleInfo" resultType="com.longhuan.order.pojo.dto.ApprovalVehicleRespDTO">
        select lovi.vehicle_number, lovi.vehicle_type, nature_of_use, engine_number,register_date,issue_date,
               lovi.vin ,brand, vehicle_series, vehicle_model, vehicle_year, guide_price, mileage,
               lovi.update_time, to_char(lovi.update_time, 'YYYY-MM-DD') as applyDate,
               lvcr.response_body,lovi.seat_count,lovi.transfer_times
        from lh_pre_vehicle_info lovi
        left join lh_pre_vin_record lvcr on lovi.pre_id = lvcr.pre_id
        where lovi.pre_id = #{preId}
    </select>
    <!-- 定义 resultMap -->
    <resultMap id="carResultMap" type="com.longhuan.order.pojo.vo.OrderCarInfoVO">
        <result property="car_plate" column="car_plate"/>
        <result property="vin_code" column="vin_code"/>
        <result property="reg_date" column="reg_date"/>
        <result property="brand" column="brand"/>
        <result property="series" column="series"/>
        <result property="model" column="model"/>
        <result property="color" column="color"/>
        <result property="mileage" column="mileage"/>
        <result property="engine_num" column="engine_num"/>
        <result property="buy_date" column="buy_date"/>
        <result property="batch_date" column="batch_date"/>
        <result property="frame_num" column="frame_num"/>
        <result property="buy_use" column="buy_use"/>
        <result property="car_type" column="car_type"/>
        <result property="is_pledge" column="is_pledge"/>
        <result property="gear" column="gear"/>
        <result property="buy_price" column="buy_price"/>
        <result property="fuel" column="fuel"/>
        <result property="capacity" column="capacity"/>
        <result property="displacement" column="displacement"/>
        <result property="emission_standards" column="emission_standards"/>
        <result property="transfers_num" column="transfers_num"/>
        <result property="l_province" column="l_province"/>
        <result property="l_city" column="l_city"/>
        <result property="verification_date" column="verification_date"/>
        <result property="damage_date" column="damage_date"/>
        <result property="mandatory_date" column="mandatory_date"/>
        <result property="illegal_score" column="illegal_score"/>
        <result property="illegal_money" column="illegal_money"/>
        <result property="full_illegal" column="full_illegal"/>
        <result property="belong" column="belong"/>

        <result property="first_amount" column="first_amount"/>
        <result property="maintenance" column="maintenance"/>
        <result property="accident_record" column="accident_record"/>
        <result property="third_date" column="third_date"/>
        <result property="lack_insurance" column="lack_insurance"/>
        <result property="same_driver_license" column="same_driver_license"/>
        <result property="gps_state" column="gps_state"/>
        <result property="gps_uninstall_apply_at" column="gps_uninstall_apply_at"/>
        <result property="gps_uninstall_at" column="gps_uninstall_at"/>
        <result property="is_gabet" column="is_gabet"/>
        <result property="gabet_state" column="gabet_state"/>
    </resultMap>

    <!-- 使用 resultMap 的 select 语句 -->
    <select id="selectOrderCarJoinOne" resultMap="carResultMap">
        SELECT
            t.vehicle_number AS car_plate,
            t.vin AS vin_code,
            EXTRACT(EPOCH FROM t.register_date) AS reg_date,
            t.brand AS brand,
            t.vehicle_series AS series,
            t.vehicle_model AS model,
            t.vehicle_color AS color,
            t.mileage AS mileage,
            t.engine_number AS engine_num,
            EXTRACT(EPOCH FROM t.buy_date) AS buy_date,
            EXTRACT(EPOCH FROM t.production_date) AS batch_date,
            t.vin AS frame_num,
            t.nature_of_use AS buy_use,
            t.vehicle_type AS car_type,
            ' ' AS is_pledge,
            t.transmission_type AS gear,
            t.buy_price AS buy_price,
            t.fuel_type AS fuel,
            5 AS capacity,
            t.displacement AS displacement,
            t.emission AS emission_standards,
            t.mileage AS transfers_num,
            t.license_province AS l_province,
            t.license_city AS l_city,
            EXTRACT(EPOCH FROM t.annual_date) AS verification_date,
            EXTRACT(EPOCH FROM t.vehicle_insurance_date) AS damage_date,
            EXTRACT(EPOCH FROM t.compulsory_date) AS mandatory_date,
            5 AS illegal_score,
            5 AS illegal_money,
            5 AS full_illegal,
            CASE WHEN t.owner_type = 1 THEN '个人' WHEN t.owner_type = 2 THEN '公司' ELSE '未知' END AS belong,
            '' AS first_amount,
            '' AS maintenance,
            '' AS accident_record,
            EXTRACT(EPOCH FROM t.third_insurance_date) AS third_date,
            0 AS lack_insurance,
            '' AS same_driver_license,
            t1.gps_state AS gps_state,
            '' AS gps_uninstall_apply_at,
            '' AS gps_uninstall_at,
            '' AS is_gabet,
            '' AS gabet_state
        FROM
            LH_ORDER_VEHICLE_INFO t
                LEFT JOIN
            LH_ORDER_INFO t1 ON t1.id = t.order_id
        WHERE
            t1.id = #{orderId}
    </select>
    <!-- 定义 resultMap -->
    <resultMap id="carEvaluateResultMap" type="com.longhuan.order.pojo.vo.CarEvaluateVO">
        <result property="appraiser" column="appraiser"/>
        <result property="verify_time" column="verify_time"/>
        <result property="first_amount" column="first_amount"/>
        <result property="overview" column="overview"/>
        <result property="evaluate_amount" column="evaluate_amount"/>
        <result property="accident_state" column="accident_state"/>
        <result property="engine_overhal" column="engine_overhal"/>
        <result property="maintain" column="maintain"/>
        <result property="pledge_remark" column="pledge_remark"/>
        <result property="interior" column="interior"/>
        <result property="work_state" column="work_state"/>
        <result property="maintenance" column="maintenance"/>
        <result property="accident" column="accident"/>
        <result property="exterior" column="exterior"/>
        <result property="frame" column="frame"/>
        <result property="mechanical" column="mechanical"/>
        <result property="interior_state" column="interior_state"/>
    </resultMap>

    <!-- 使用 resultMap 的 select 语句 -->
    <select id="selectOrderVehicleoinOne" resultMap="carEvaluateResultMap">
        SELECT
            CASE
                WHEN t.appraise_name IS NOT NULL THEN t.appraise_name
                ELSE ''
                END AS appraiser,
            EXTRACT(EPOCH FROM t.appraise_time) AS verify_time,
            t1.soft_review_amount AS first_amount,
            t.appraise_level AS overview,
            t1.appraiser_amount AS evaluate_amount,
            '' AS accident_state,
            '' AS engine_overhal,
            '' AS maintain,
            '' AS pledge_remark,
            '' AS interior,
            '' AS work_state,
            '' AS maintenance,
            '' AS exterior,
            '' AS frame,
            '' AS interior_state,
            t.vin
        FROM
            LH_ORDER_VEHICLE_INFO t
                LEFT JOIN
            LH_ORDER_AMOUNT t1 ON t1.order_id = t.order_id

        WHERE
            t.order_id = #{orderId}
          and t.delete_flag = 0 order by t.create_time desc limit 1
    </select>

</mapper>