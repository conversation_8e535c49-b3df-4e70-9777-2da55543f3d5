package com.longhuan.order.statemachine.action;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.common.core.enums.RoleEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.converter.OrderContactPersonConverter;
import com.longhuan.order.feign.RiskFeign;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.service.OrderFileService;
import com.longhuan.order.service.RiskService;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.risk.pojo.dto.CarDTO;
import com.longhuan.risk.pojo.dto.ContactDTO;
import com.longhuan.risk.pojo.dto.RiskLaunchDTO;
import com.longhuan.risk.pojo.dto.RiskOrderLaunchDTO;
import com.longhuan.user.enums.TodoInfoEnums;
import com.longhuan.user.pojo.dto.TodoInfoMessageDTO;
import com.longhuan.user.pojo.dto.getUserIdByStoreIdAndRoleIdDTO;
import com.longhuan.user.pojo.vo.UserInfoVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 信息补录提交状态转移动作
 */
@Slf4j
@Component
public class BussinessAddedInfoAction extends OrderNodeAction implements Action<States, Events> {
    @Resource
    private RiskService riskService;
    private final OrderApproveDistributeMapper orderApproveDistributeMapper;
    private final DistributeAreaMapper distributeAreaMapper;
    private final UserFeign userFeign;

    public BussinessAddedInfoAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper, OrderApproveDistributeMapper orderApproveDistributeMapper, DistributeAreaMapper distributeAreaMapper, UserFeign userFeign
    ) {
        super(orderInfoMapper, orderNodeRecordMapper);
        this.orderApproveDistributeMapper = orderApproveDistributeMapper;
        this.distributeAreaMapper = distributeAreaMapper;
        this.userFeign = userFeign;
    }


    @Override
    public void execute(StateContext<States, Events> context) {


        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();
        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);

        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event, currentNode, nextNode);


        try {
            // TODO 异步调用风控数据
            riskService.riskLaunchOnOrder(orderId);
            // TODO 异步调用风控规则
            riskService.riskEngineOnOrder(orderId);
        } catch (Exception e) {
            log.error("risk service call error {}", e.getMessage(), e);
        }

        // 1. 修改订单表状态
        if (Boolean.TRUE.equals(target.getId().getValid())) {
            saveOrderNode(orderId, nextNode);
        }

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);
        //3.送入待分配任务池
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        DistributeAreaEntity distributeAreaEntity = distributeAreaMapper.selectOne(new LambdaQueryWrapper<DistributeAreaEntity>()
                .eq(DistributeAreaEntity::getStoreId, orderInfoEntity.getDeptId())
                .eq(DistributeAreaEntity::getServiceDispatch, 1)
                .eq(DistributeAreaEntity::getDeleteFlag, 0)
                .orderByDesc(DistributeAreaEntity::getCreateTime)
                .last("limit 1"));
        if (distributeAreaEntity != null) {
            Long count = orderApproveDistributeMapper.selectCount(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                    .eq(OrderApproveDistributeEntity::getOrderNumber, orderInfoEntity.getOrderNumber())
                    .eq(OrderApproveDistributeEntity::getOrderId, orderId)
                    .eq(OrderApproveDistributeEntity::getNode, nextNode)
                    .in(OrderApproveDistributeEntity::getState, 0, 1, 2, 5));
            if (count==0){
                OrderApproveDistributeEntity orderApproveDistributeEntity = new OrderApproveDistributeEntity()
                        .setOrderNumber(orderInfoEntity.getOrderNumber())
                        .setOrderId(orderId)
                        .setNode(nextNode)
                        .setNodeName(States.getByNode(nextNode).getDesc())
                        .setSource(0)
                        .setStoreId(orderInfoEntity.getDeptId())
                        .setTitle(orderInfoEntity.getCustomerName() + "-" + orderInfoEntity.getVehicleNumber() + "-" + orderInfoEntity.getStoreName())
                        .setState(0);
                orderApproveDistributeEntity.setCustomerName(orderInfoEntity.getCustomerName())
                        .setCustomerPhone(orderInfoEntity.getCustomerPhone())
                        .setManagerId(orderInfoEntity.getManagerId())
                        .setStoreName(orderInfoEntity.getStoreName())
                        .setRegionName(orderInfoEntity.getRegionName())
                        .setVehicleNumber(orderInfoEntity.getVehicleNumber())
                        .setStepDispose(currentNode<=nextNode?0:1);
                orderApproveDistributeMapper.insert(orderApproveDistributeEntity);
            }
        }else {
            List<Integer> userIds=userFeign.getUserIdByStoreIdAndRoleId(new getUserIdByStoreIdAndRoleIdDTO().setDeptId(orderInfoEntity.getDeptId()).setRoleId(RoleEnum.CUSTOMER_SERVICE_SPECIALIST.getId())).getData();
            List<TodoInfoMessageDTO.TodoUser> userDto=userFeign.searchUserNameBatch(userIds).getData().stream().map(e->new TodoInfoMessageDTO.TodoUser().setName(e.getName()).setJobNumber(e.getJobNumber()).setPhoneNumber(e.getMobile())).toList();

            TodoInfoMessageDTO todoInfoMessageDTO = new TodoInfoMessageDTO()
                    .setOrderNumber(orderInfoEntity.getOrderNumber())
                    .setOrderId(orderId)
                    .setNode(TodoInfoEnums.getByNode(nextNode))
                    .setSourceType(0)
                    .setRemark("订单：" + orderInfoEntity.getOrderNumber() + "待处理")
                    .setState(0)
                    .setTitle(orderInfoEntity.getCustomerName() + "-" + orderInfoEntity.getVehicleNumber() + "-" + orderInfoEntity.getStoreName())
                    .setAccessUrl(null)
                    .setTodoUserList(userDto);
            todoInfoMessageDTO.setCustomerName(orderInfoEntity.getCustomerName())
                    .setCustomerPhone(orderInfoEntity.getCustomerPhone())
                    .setManagerId(orderInfoEntity.getManagerId())
                    .setStoreName(orderInfoEntity.getStoreName())
                    .setRegionName(orderInfoEntity.getRegionName())
                    .setVehicleNumber(orderInfoEntity.getVehicleNumber());
            log.info("BussinessAddedInfoAction send todoMessage: {}", todoInfoMessageDTO);
            userFeign.dealMessage(todoInfoMessageDTO);
        }
    }
    /**
     * 修改订单表状态
     *
     * @param orderId
     * @param nextNode
     */
    @Override
    protected void saveOrderNode(Integer orderId, Integer nextNode) {
        OrderInfoEntity updateEntity = new OrderInfoEntity();

        updateEntity.setId(orderId);
        updateEntity.setCurrentNode(nextNode);
        updateEntity.setLastNodeFinishTime(LocalDateTime.now());
        updateEntity.setSubmitTime(LocalDateTime.now());
        orderInfoMapper.updateById(updateEntity);
    }
}