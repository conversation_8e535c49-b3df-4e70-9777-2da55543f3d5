package com.longhuan.order.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.SearchOrderBillDTO;
import com.longhuan.order.pojo.vo.OrderBillListVO;
import com.longhuan.order.service.OrderBillService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Api("查账")
@RequestMapping("/api/v1/order/bill")
public class OrderBillController {

    private final OrderBillService orderBillService;

    @PostMapping("/page")
    public Result<Page<OrderBillListVO>> page(@RequestBody SearchOrderBillDTO dto, @CurrentUser LoginUser loginUser){
        return Result.success(orderBillService.page(dto, loginUser));
    }
}
