package com.longhuan.order.settleCalculation.service.impl.strategy;

import com.longhuan.order.pojo.dto.SettleCalculationContext;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.vo.SettleCalculationVO;

import java.time.LocalDate;

public interface SettleCalculationStrategy {
    SettleCalculationVO calculate(SettleCalculationContext context);
    void buildLHContext(SettleCalculationContext context,
                        OrderInfoEntity orderInfoEntity,
                        LocalDate payDate);
}
