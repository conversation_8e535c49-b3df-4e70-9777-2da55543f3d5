package com.longhuan.user.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.user.pojo.dto.CaseInfoUserDTO;
import com.longhuan.user.pojo.dto.GetDeptIdByNameDTO;
import com.longhuan.user.pojo.dto.GetDutyLoginDTO;
import com.longhuan.user.pojo.dto.GetStoreAppraisersListDTO;
import com.longhuan.user.pojo.vo.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface DingDrawMoneyApi {

    @ApiOperation(value = "获取钉钉审批数据")
    @PostMapping("/approval/data/updateDingDrawMoney")
    Result<List<DingDrawMoneyVO>> getDingDrawMoney(@RequestParam("month") Integer month);
    @ApiOperation(value = "获取角色用户id")
    @GetMapping("/approval/data/getRoleUserIds")
    List<GetRoleUserIdVO> getRoleUserIds(@RequestParam("roleId") Integer roleId);

    @GetMapping("/approval/data/getUserPhone")
    List<GetUserPhoneVO> getUserPhone();

    @ApiOperation(value = "通过手机号获取钉钉id")
    @GetMapping("/approval/data/getDingUserId")
    UserDingUserIdVO getDingUserId(@RequestParam("mobile") String mobile);
    @ApiOperation(value = "通过用户列表中的roleId去获取用户信息")
    @GetMapping("/approval/data/getUserRoleList")
    List<GetRoleUserIdVO> getUserRoleList(@RequestParam("roleId") Integer roleId);
    @ApiOperation(value = "通过部门id获取部门名称")
    @GetMapping("/approval/data/getDeptId")
    List<String> getDeptId(@RequestParam("deptId") List<Integer> deptId);

    @ApiOperation(value = "获取钉钉审批完成的所有车牌号")
    @GetMapping("/approval/data/getCarNumberList")
    Map<String, CaseInfoUserDTO> getCarNumberList();

    @ApiOperation(value = "根据审批名称获取钉钉审批数据")
    @PostMapping("/approval/data/updateDingDrawInfo")
    Map<Integer,Map<String,String>> updateDingDrawInfo(@RequestParam("name") String name);

    @ApiOperation(value = "获取所有门店以及所属评估师")
    @PostMapping("/approval/data/getStoreAppraisersList")
    List<StoreAppraisersListVO> getStoreAppraisersList(@RequestBody GetStoreAppraisersListDTO dto);

    /**
     * 通过手机号获取用户部门信息
     */
    @ApiOperation(value = "通过手机号获取用户部门信息")
    @GetMapping("/approval/data/getUserDeptInfo")
    GetUserDeptInfoVO getUserDeptInfo(@RequestParam("mobile") String mobile);

    /**
     * 获取门店列表
     */
    @ApiOperation(value = "获取门店列表")
    @GetMapping("/approval/data/getStoreList")
    List<GetStoreListVO> getStoreList();
    /**
     * 通过部门名称匹配部门id
     */
    @ApiOperation(value = "通过部门名称匹配部门id")
    @PostMapping("/approval/data/getDeptIdByName")
    public Map<String,Integer> getDeptIdByName(@RequestBody GetDeptIdByNameDTO dto);
    @ApiOperation(value = "根据门店id获取部门名称")
    @PostMapping("/approval/data/getDeptNameByStoreId")
    public List<DeptInfoVO> getDeptNameByStoreId(@RequestBody List<Integer> storeId);
    /**
     * 根据用户id获取角色名称
     */
    @ApiOperation(value = "根据用户id获取角色名称")
    @GetMapping("/getRoleNameByUserId/{userId}")
    GetRoleNameByUserIdVO getRoleNameByUserId(@PathVariable("userId") Integer userId);
//    /**
//     * 获取贷后值班日志
//     */
//    @PostMapping("/api/v1/duty/getDutyLog")
//    Result<Page<GetDutyLoginVO>> getDutyLogin(@RequestBody GetDutyLoginDTO dto);
//    /**
//     * 根据id判断是否为值班账号
//     */
//    @PostMapping("/api/v1/duty/isDuty/{userId}")
//    Result<Boolean> isDuty(@PathVariable Integer userId);
}
