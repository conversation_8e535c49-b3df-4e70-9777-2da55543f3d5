package com.longhuan.order.settleCalculation.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.order.pojo.dto.LHSettleCalculationDTO;
import com.longhuan.order.pojo.dto.OrderSettleDTO;
import com.longhuan.order.pojo.dto.SettleCalculationContext;
import com.longhuan.order.pojo.vo.OrderSettleVO;
import com.longhuan.order.pojo.vo.SettleCalculationVO;
import com.longhuan.order.settleCalculation.vo.SettleCalculationListVO;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public interface SettleCalculationService {
    SettleCalculationVO calculate(SettleCalculationContext params);

    SettleCalculationVO calculateByOrderId(@NotNull(message = "订单id不能为空") Integer orderId,Integer closedPeriod);

    Page<SettleCalculationListVO> list(OrderSettleDTO orderSettleDTO);

    List<SettleCalculationVO> compute(LHSettleCalculationDTO dto);
}