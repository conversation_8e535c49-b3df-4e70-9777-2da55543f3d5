package com.longhuan.order.controller;

import cn.hutool.core.util.ObjUtil;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.RiskFundAutoConfigDTO;
import com.longhuan.order.pojo.vo.RiskFundAutoConfigVO;
import com.longhuan.order.service.RiskFundAutoConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * RiskAutoConfigController
 *
 * @date 2025/7/25 18:09
 */
@Api(tags = "风控审核自动配置")
@RestController
@RequestMapping("/api/v1/riskAutoConfig")
@RequiredArgsConstructor
public class RiskFundAutoConfigController {
    private final RiskFundAutoConfigService riskFundAutoConfigService;


    /**
     * 获取查询列表
     * @return
     */
    @ApiOperation(value = "获取查询列表")
    @GetMapping("/riskAutoConfigList")
    public Result<List<RiskFundAutoConfigVO> > riskAutoConfigList() {
        List<RiskFundAutoConfigVO> list = riskFundAutoConfigService.riskAutoConfigList();
        return Result.success(list);
    }

    /**
     * 查询开启列表
     * @return
     */
    @ApiOperation(value = "查询开启列表")
    @GetMapping("/queryOpenList")
    public Result<List<RiskFundAutoConfigVO> > queryOpenList() {
        List<RiskFundAutoConfigVO> list = riskFundAutoConfigService.queryOpenList();
        return Result.success(list);
    }

    /**
     * 操作开启关闭
     * @return
     */
    @ApiOperation(value = "操作开启关闭")
    @PostMapping("/operateRiskFundAutoConfig")
    public Result<Boolean> queryOpenList(@RequestBody RiskFundAutoConfigDTO dto) {
        Boolean flag = riskFundAutoConfigService.operateConfig(dto);
        if (flag){
            return Result.success(flag);
        }else {
            return Result.failed();
        }
    }



}
