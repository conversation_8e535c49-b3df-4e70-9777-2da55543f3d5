package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.approve.api.pojo.dto.FundAmountChangeDTO;
import com.longhuan.common.core.enums.OrderAmountEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.converter.OrderAmountConverter;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.mapper.FundInfoMapper;
import com.longhuan.order.mapper.OrderAmountMapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.pojo.dto.AmountCalDTO;
import com.longhuan.order.pojo.dto.RepaymentListDTO;
import com.longhuan.order.pojo.entity.FundInfoEntity;
import com.longhuan.order.pojo.entity.OrderAmountEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.vo.AmountCalVO;
import com.longhuan.order.service.AmountService;
import com.longhuan.order.service.RepaymentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Random;

import static com.longhuan.common.core.enums.OrderAmountEnum.*;


/**
 * 金额计算服务impl （实现）
 *
 * <AUTHOR>
 * @date 2024/09/03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AmountServiceImpl implements AmountService {
    private final OrderInfoMapper orderInfoMapper;
    private final OrderAmountMapper orderAmountMapper;
    private final OrderAmountConverter orderAmountConverter;
    private final FundInfoMapper fundInfoMapper;
    private final RepaymentService repaymentService;
    private final ApproveFeign approveFeign;

    /**
     * 计算金额
     * 希望贷款金额：
     * 预审时用，无实际意义（用户填写）
     * <p>
     * 软评额度：
     * 车300的评估价（第三方接口）
     * <p>
     * 预审批额度：
     * 预审批额度，根据软评额度计算出的额度（风控计算）
     * <p>
     * 申请金额：
     * 客户经理申请的额度（客户经理确认填写）
     * <p>
     * 总评额度：
     * 默认预审批的额度，若有总评复核，会调整总评额度（总评复核人员可调整无限制）
     * <p>
     * 风控额度：
     * 默认min(预审批额度、申请金额)，风控审批时，最终确认的额度（预审批额度、申请金额取最小值，风控审批人员可调整无限制）
     * <p>
     * 资方授信额度：
     * min(风控额度,总评额度)，最终资方审批通过的额度，若资方降额，也会更新该字段（默认预审批额度、申请金额取最小值，最终以资方审批额度为准）
     * <p>
     * 评估师额度：
     * 根据资方授信额度自动倒推出评估师额度，根据资方表中该资方的最大抵押率，向下随机一个数进行默认，比如资方的最大抵押率为80%（最小为70%），那随机数就是=70%的一个数，比如75%，那么评估师额度就等于资方授信额度 / 75%，保留2位小数（千位取整）
     * 评估师额度（此额度给资方推送，不再推送软评额度）
     * {根据风控额度自动倒推出评估师额度（蓝海产品、长安新生8成倒推，中关村、盈峰、苏商、客商按照9成倒推）
     * 倒推出的评估师额度大于软评额度，回显倒推额度；
     * 倒推出的评估师额度小于等于软评额度，回显软评额度；}
     * <p>
     * 客户确认额度：
     * 默认资方授信额度，若该资方允许下调放款金额，客户确认时可以向下调整放款金额，否则只能默认资方授信额度，该额度为最终放款额度（客户额度确认节点，可根据资方配置判断是否可向下调整）
     * <p>
     * 各额度计算节点：
     * 预审批完成后，软评额度、预审批额度、总评额度将会有值
     * 客户经理提交后，风控额度有初始化值、资方授信额度、评估师额度会有值
     * 总评复核后，修改了总评额度后，资方授信额度、评估师额度会变
     * 初审修改风控额度时，资方授信额度、评估师额度会变
     *
     * @param amountCalDTO 金额计算DTO
     * @return {@link AmountCalVO }
     */
    @Override
    public AmountCalVO calAmount(AmountCalDTO amountCalDTO) {
        try {
            Integer orderId = amountCalDTO.getOrderId();
            List<OrderAmountEntity> orderAmountList = orderAmountMapper.selectList(new LambdaQueryWrapper<OrderAmountEntity>()
                    .eq(OrderAmountEntity::getOrderId, orderId)
                    .eq(OrderAmountEntity::getDeleteFlag, 0)
            );
            OrderAmountEntity orderAmountEntity = null;
            if (CollUtil.isEmpty(orderAmountList)) {
                //初始化订单金额表
                orderAmountEntity = new OrderAmountEntity();
                orderAmountEntity.setOrderId(orderId);
                orderAmountMapper.insert(orderAmountEntity);
            } else {
                orderAmountEntity = orderAmountList.get(0);
            }
            BigDecimal amount = amountCalDTO.getAmount();
            OrderAmountEnum orderAmountEnum = amountCalDTO.getCalType();
            BigDecimal fundPreAmount = BigDecimal.ZERO;
            BigDecimal riskAmount = Objects.nonNull(orderAmountEntity.getRiskAmount()) ? orderAmountEntity.getRiskAmount() : BigDecimal.valueOf(0.00);
            BigDecimal softReviewAmount = Objects.nonNull(orderAmountEntity.getSoftReviewAmount()) ? orderAmountEntity.getSoftReviewAmount() : BigDecimal.valueOf(0.00);
            BigDecimal totalAmount = Objects.nonNull(orderAmountEntity.getTotalAmount()) ? orderAmountEntity.getTotalAmount() : BigDecimal.valueOf(0.00);
            BigDecimal hopeAmount = ObjUtil.defaultIfNull(orderAmountEntity.getHopeAmount(), BigDecimal.ZERO);
            log.info("AmountServiceImpl.calAmount orderId:{} softReviewAmount:{} totalAmount:{}", orderId, softReviewAmount, totalAmount);
            if (Objects.nonNull(orderAmountEnum)) {
                switch (orderAmountEnum) {
                    case TOTAL_AMOUNT:
                        fundPreAmount = amount.min(riskAmount);
                        updateOrderAmount(orderId, amount, TOTAL_AMOUNT);
//                        updateOrderAmount(orderId, fundPreAmount, FUND_PRE_AMOUNT);
//                        updateOrderAmount(orderId, calculateAssessmentAmount(orderId, fundPreAmount), APPRAISER_AMOUNT);
                        Result<String> fundAmountChangeTotalResult = approveFeign.fundAmountChange(new FundAmountChangeDTO()
                                .setLinkId(orderId).setType(2).setChangeAmount(fundPreAmount));
                        if (!Result.isSuccess(fundAmountChangeTotalResult)) {
                            throw new BusinessException(fundAmountChangeTotalResult.getMsg());
                        }
                        updateOrderAmount(orderId, fundPreAmount, CUSTOMER_CONFIRM_AMOUNT);
                        break;
                    case RISK_AMOUNT:
                        //风控额度≤（总评额度、资方授信额度、申请额度）三个额度的低值
                        fundPreAmount = orderAmountEntity.getFundPreAmount();
                        BigDecimal riskMaxAmount = totalAmount.min(fundPreAmount).min(hopeAmount);

                        if (riskMaxAmount.compareTo(amount) < 0){
                            throw new BusinessException("风控额度不能高于"+riskMaxAmount+"元");
                        }

                        fundPreAmount = amount.min(totalAmount);

                        updateOrderAmount(orderId, formatToThousand(amount), RISK_AMOUNT);
                        log.info("AmountServiceImpl.calAmount FUND_PRE_AMOUNT orderId:{} fundPreAmount:{} totalAmount:{} amount:{}", orderId, fundPreAmount, totalAmount, amount);
//                        updateOrderAmount(orderId, fundPreAmount, FUND_PRE_AMOUNT);
                        updateOrderAmount(orderId, calculateAssessmentAmount(orderId, amount, softReviewAmount), APPRAISER_AMOUNT);

                        Result<String> fundAmountChangeRiskResult = approveFeign.fundAmountChange(new FundAmountChangeDTO()
                                .setLinkId(orderId).setType(2).setChangeAmount(fundPreAmount));
                        if (!Result.isSuccess(fundAmountChangeRiskResult)) {
                            throw new BusinessException(fundAmountChangeRiskResult.getMsg());
                        }
                        updateOrderAmount(orderId, fundPreAmount, CUSTOMER_CONFIRM_AMOUNT);
                        break;
                    case CUSTOMER_CONFIRM_AMOUNT:
                        fundPreAmount = orderAmountEntity.getFundPreAmount();
                        BigDecimal oldAmount = orderAmountEntity.getCustomerConfirmAmount();
                        BigDecimal customerConfirmAmount = amount.min(fundPreAmount);

                        updateOrderAmount(orderId, customerConfirmAmount, CUSTOMER_CONFIRM_AMOUNT);
                        break;
                    case PRE_AMOUNT:
                        fundPreAmount = amount.min(riskAmount);
                        updateOrderAmount(orderId, amount, PRE_AMOUNT);
                        updateOrderAmount(orderId, fundPreAmount, FUND_PRE_AMOUNT);
//                        updateOrderAmount(orderId, calculateAssessmentAmount(orderId, fundPreAmount), APPRAISER_AMOUNT);
                        updateOrderAmount(orderId, fundPreAmount, CUSTOMER_CONFIRM_AMOUNT);
                        break;
                    case FUND_PRE_AMOUNT:
                        fundPreAmount = amount.min(riskAmount);
                        updateOrderAmount(orderId, amount, FUND_PRE_AMOUNT);
//                        updateOrderAmount(orderId, calculateAssessmentAmount(orderId, fundPreAmount), APPRAISER_AMOUNT);
                        updateOrderAmount(orderId, fundPreAmount, CUSTOMER_CONFIRM_AMOUNT);
                        break;
                    case REFRESH_RISK_AMOUNT:
                        //资料补录时可以调整申请金额 导致风控额度高于申请金额
                        //风控初审时更新为正确的风控额度
                        fundPreAmount = orderAmountEntity.getFundPreAmount();
                        BigDecimal riskMaxAmountDefault = totalAmount.min(fundPreAmount).min(hopeAmount);
                        if (riskMaxAmountDefault.compareTo(riskAmount) < 0){
                            calAmount(new AmountCalDTO().setOrderId(orderId).setAmount(riskMaxAmountDefault).setCalType(RISK_AMOUNT));
                        }
                    default:
                        break;
                }
            }
            orderAmountEntity = orderAmountMapper.selectById(orderAmountEntity.getId());
            log.info("After the update, the orderAmountEntity is:{}",orderAmountEntity);
            return orderAmountConverter.entity2Vo(orderAmountEntity);
        } catch (BusinessException e) {
            log.error("金额计算异常 orderId:{}, e:{}",amountCalDTO.getOrderId(),e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 更新次序金额
     *
     * @param orderId         订单ID
     * @param amount          金额
     * @param orderAmountEnum 次序金额枚举
     */
    @Override
    public void updateOrderAmount(Integer orderId, BigDecimal amount, OrderAmountEnum orderAmountEnum) {
        log.info("updateOrderAmount orderId:{} amount:{} orderAmountEnum:{}", orderId, amount, orderAmountEnum);
        List<OrderAmountEntity> orderAmountEntities = orderAmountMapper.selectList(new LambdaQueryWrapper<OrderAmountEntity>()
                .or().eq(OrderAmountEntity::getOrderId, orderId)
                .eq(OrderAmountEntity::getDeleteFlag, 0)
        );
        OrderAmountEntity orderAmountEntity;
        if (CollUtil.isEmpty(orderAmountEntities)) {
            orderAmountEntity = new OrderAmountEntity();
            orderAmountEntity.setOrderId(orderId);
            orderAmountMapper.insert(orderAmountEntity);
        } else {
            orderAmountEntity = orderAmountEntities.get(0);
        }
        if (Objects.nonNull(orderAmountEnum)) {
            switch (orderAmountEnum) {
                case HOPE_AMOUNT:
                    orderAmountEntity.setHopeAmount(amount);
                    break;
                case PRE_AMOUNT:
                    orderAmountEntity.setPreAmount(formatToThousand(amount));
                    break;
                case LOAN_AMOUNT:
                    orderAmountEntity.setLoanAmount(amount);
                    break;
                case TOTAL_AMOUNT:
                    orderAmountEntity.setTotalAmount(amount);
                    repaymentService.list(new RepaymentListDTO().setOrderId(orderId));
                    break;
                case RISK_AMOUNT:
                    orderAmountEntity.setRiskAmount(amount);
                    repaymentService.list(new RepaymentListDTO().setOrderId(orderId));
                    break;
                case FUND_PRE_AMOUNT:
                    orderAmountEntity.setFundPreAmount(formatToThousand(amount));
                    break;
                case APPRAISER_AMOUNT:
                    orderAmountEntity.setAppraiserAmount(amount);
                    break;
                case CUSTOMER_CONFIRM_AMOUNT:
                    orderAmountEntity.setCustomerConfirmAmount(formatToThousand(amount));
                    repaymentService.list(new RepaymentListDTO().setOrderId(orderId));
                    break;
                case SOFT_REVIEW_AMOUNT:
                    orderAmountEntity.setSoftReviewAmount(amount);
                    break;
                default:
                    log.error("金额类型错误");
//                throw new BusinessException("金额类型错误");
            }
        }
        //根据客户确定额度赋值到orderinfo表中订单具体是 消费贷还是经营贷款
        updateOrderType(orderId, orderAmountEntity.getCustomerConfirmAmount());
        orderAmountMapper.updateById(orderAmountEntity);
    }

    //根据客户确认额度确定是贷款类型  富民除外
    public void updateOrderType(Integer orderId,BigDecimal customerConfirmAmount) {
        if (ObjUtil.isNotNull(orderId)&&ObjUtil.isNotNull(customerConfirmAmount)){
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
            if (orderInfoEntity!=null){
                Integer fundId = orderInfoEntity.getFundId();
                if (!ObjUtil.equals(fundId, 11)) {
                    log.info("客户确认额度是:{}",customerConfirmAmount);
                    BigDecimal thirty = new BigDecimal("300000");
                    if (customerConfirmAmount.compareTo(thirty) >= 0) {
                        // 大于等于30万，设置为经营贷
                        orderInfoEntity.setOrderType(1);
                    } else {
                        // 小于30万，设置为消费贷
                        orderInfoEntity.setOrderType(0);
                    }
                    orderInfoMapper.updateById(orderInfoEntity);
                }
            }
        }
    }

    /**
     * 算评估金额
     *
     * @param orderId          订单ID
     * @param fundingAmount    资金金额
     * @param evaluationAmount 评估金额
     * @return {@link BigDecimal }
     */
    @Override
    public BigDecimal calculateAssessmentAmount(Integer orderId, BigDecimal fundingAmount, BigDecimal evaluationAmount) {
        log.info("AmountServiceImpl.calculateAssessmentAmount orderId:{} fundingAmount:{} evaluationAmount:{}", orderId, fundingAmount, evaluationAmount);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, "订单不存在");
        Integer fundId = orderInfoEntity.getFundId();
        FundInfoEntity fundInfoEntity = fundInfoMapper.selectById(fundId);
        Assert.notNull(orderInfoEntity, "资方不存在");
        BigDecimal max = fundInfoEntity.getMaxMortgage();
//        BigDecimal ltv = getRandomBigDecimal(max);
        // 将百分比转换为小数形式
//        BigDecimal ltvDecimal = max.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);
        log.info("AmountServiceImpl.calculateAssessmentAmount orderId:{} max:{}", orderId, max);
        // 计算评估师额度
        BigDecimal assessmentAmount = fundingAmount.divide(max, 0, RoundingMode.HALF_UP);
        log.info("AmountServiceImpl.calculateAssessmentAmount orderId:{} assessmentAmount:{}", orderId, assessmentAmount);
        return formatToThousand(assessmentAmount).max(roundToNearestThousand(evaluationAmount));
    }

    /**
     * 千位取整舍百位
     * @param amount 金额
     * @return {@link BigDecimal}
     */
    @Override
    public BigDecimal formatToThousand(BigDecimal amount) {
        return amount.divide(new BigDecimal("1000"), RoundingMode.DOWN)
                .setScale(0, RoundingMode.DOWN)
                .multiply(new BigDecimal("1000"));
    }

    /**
     * 四舍五入到最接近千
     *
     * @param number 数
     * @return {@link BigDecimal }
     */
    private static BigDecimal roundToNearestThousand(BigDecimal number) {
        // 将数字四舍五入到最近的 1000
//        BigDecimal rounded = number.divide(new BigDecimal("1000"), 0, RoundingMode.DOWN).multiply(new BigDecimal("1000"));
        // 计算 number 对 1000 的余数
      /*  BigDecimal remainder = number.remainder(new BigDecimal("1000"));

        // 如果余数大于等于 1，则将结果加 1000
        if (remainder.compareTo(BigDecimal.ONE) >= 0) {
            rounded = rounded.add(new BigDecimal("1000"));
        }*/
        return number;
    }

    /**
     * 获取随机大十进制
     *
     * @param max 麦克斯
     * @return {@link BigDecimal }
     */
    public static BigDecimal getRandomBigDecimal(BigDecimal max) {
        BigDecimal min = new BigDecimal(70);
        Random random = new Random();
        BigDecimal range = max.subtract(min);
        BigDecimal randomDecimal = BigDecimal.valueOf(random.nextDouble()).multiply(range).add(min);
        // 设置保留两位小数，并向下取整
        return randomDecimal.setScale(2, RoundingMode.DOWN);
    }
}
