package com.longhuan.common.core.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单费用详情表交易方式
 *
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum OrderFeeDetailTradingMethodsEnum {
    SCAN_THE_QR_CODE_TO_PAY(1, "扫码支付"),
    CORPORATE_TRANSFERS(2, "对公转账"),
    TONGTONGFU_BUCKLES(3, "统统付划扣"),
    LINKED_BUCKLES(4, "通联划扣"),
    FUND_BUCKLES(5, "资方划扣"),
    FUND_LENDING(6, "资方放款"),
    ONLINE_BANKING_PAYMENT(7, "网银支付"),
    FUND_REFUND(8, "资方退款"),
    LIAN_LIAN_BUCKLES(9, "连连划扣"),
    LIAN_LIAN_SCAN_CODE_BUCKLES(10, "连连扫码支付"),
    YI_BAO_BUCKLES(11, "易宝划扣"),
    BAO_FU_DEDUCT(12, "宝付划扣");
    @EnumValue
    @JsonValue
    private final Integer code;
    private final String description;

    public static OrderFeeDetailTradingMethodsEnum fromCode(int code) {
        for (OrderFeeDetailTradingMethodsEnum relation : values()) {
            if (relation.getCode() == code) {
                return relation;
            }
        }
        return null;
    }
}
