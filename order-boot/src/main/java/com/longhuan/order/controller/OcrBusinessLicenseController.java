package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.InsertOcrBusinessLicenseDTO;
import com.longhuan.order.pojo.entity.PreOcrBusinessLicenseEntity;
import com.longhuan.order.service.PreOcrBusinessLicenseService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * ocr营业执照
 *
 */
@RestController
@RequestMapping("/ocrBusinessLicense")
@RequiredArgsConstructor
public class OcrBusinessLicenseController {
    private final PreOcrBusinessLicenseService preOcrBusinessLicenseService;

    /**
     * 提交合同信息
     *
     * @param insertOcrVehicleInfoDTO 合同信息
     * @return 合同信息
     */
    @PostMapping("/insertOcrBusinessLicense")
    public Result<PreOcrBusinessLicenseEntity> insertOcrBusinessLicense(@RequestBody @Validated InsertOcrBusinessLicenseDTO insertOcrVehicleInfoDTO) {
        return Result.success(preOcrBusinessLicenseService.insertOcrBusinessLicense(insertOcrVehicleInfoDTO));
    }

    /**
     * 根据预审批id查询合同信息
     *
     * @param preId 预审批id
     * @return 合同信息
     */
    @GetMapping("/getDetailsByApplyInfoId/{preId}")
    public Result<PreOcrBusinessLicenseEntity> getDetailsByApplyInfoId(@PathVariable("preId") Integer preId) {
        return Result.success(preOcrBusinessLicenseService.getDetailsByApplyInfoId(preId));
    }

}
