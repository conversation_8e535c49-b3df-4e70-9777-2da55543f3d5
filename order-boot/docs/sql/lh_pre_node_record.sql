-- 预审流程节点状态记录表
-- 表名: lh_pre_node_record
-- 描述: 记录预审流程各个节点的状态变化和审批信息

-- 创建表
CREATE TABLE IF NOT EXISTS lh_pre_node_record (
    id serial,
    pre_id INTEGER NOT NULL,
    current_node INTEGER,
    next_node INTEGER,
    remark TEXT,
    delete_flag INTEGER NOT NULL DEFAULT 0,
    create_by INTEGER,
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    update_by INTEGER,
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 添加表注释
COMMENT ON TABLE lh_pre_node_record IS '预审流程节点状态记录表';

-- 添加字段注释
COMMENT ON COLUMN lh_pre_node_record.id IS '主键';
COMMENT ON COLUMN lh_pre_node_record.pre_id IS '预审ID';
COMMENT ON COLUMN lh_pre_node_record.current_node IS '当前节点';
COMMENT ON COLUMN lh_pre_node_record.next_node IS '下一个节点';
COMMENT ON COLUMN lh_pre_node_record.remark IS '审批意见内部';
COMMENT ON COLUMN lh_pre_node_record.delete_flag IS '是否有效（0：有效，1:无效）';
COMMENT ON COLUMN lh_pre_node_record.create_by IS '创建人';
COMMENT ON COLUMN lh_pre_node_record.create_time IS '创建时间';
COMMENT ON COLUMN lh_pre_node_record.update_by IS '更新人';
COMMENT ON COLUMN lh_pre_node_record.update_time IS '更新时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_lh_pre_node_record_pre_id ON lh_pre_node_record(pre_id);
CREATE INDEX IF NOT EXISTS idx_lh_pre_node_record_current_node ON lh_pre_node_record(current_node);
CREATE INDEX IF NOT EXISTS idx_lh_pre_node_record_create_time ON lh_pre_node_record(create_time);
CREATE INDEX IF NOT EXISTS idx_lh_pre_node_record_delete_flag ON lh_pre_node_record(delete_flag);
