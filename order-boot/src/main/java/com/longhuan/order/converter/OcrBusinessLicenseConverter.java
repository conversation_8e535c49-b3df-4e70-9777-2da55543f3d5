package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.InsertOcrBusinessLicenseDTO;
import com.longhuan.order.pojo.dto.InsertOcrVehicleInfoDTO;
import com.longhuan.order.pojo.entity.PreOcrBusinessLicenseEntity;
import com.longhuan.order.pojo.entity.PreOcrVehicleInfoEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;


@Mapper(componentModel = "spring")
public interface OcrBusinessLicenseConverter {

    PreOcrBusinessLicenseEntity dto2Entity(InsertOcrBusinessLicenseDTO insertOcrBusinessLicenseDTO);
}
