package com.longhuan.order.controller;


import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.pojo.dto.StoreQuotaDTO;
import com.longhuan.order.pojo.entity.StoreQuotaEntity;
import com.longhuan.order.pojo.vo.StoreQuotaVO;
import com.longhuan.order.pojo.vo.TableOperationLogVO;
import com.longhuan.order.service.StoreQuotaService;
import com.longhuan.order.service.TableOperationLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

/**
 * 门店额度控制接口
 *
 * <AUTHOR>
 */
@Api(tags = "门店额度控制接口")
@RestController
@RequestMapping("/api/v1/storeQuota")
@RequiredArgsConstructor
public class StoreQuotaController {
    private final StoreQuotaService storeQuotaService;
    private final TableOperationLogService tableOperationLogService;

    @ApiOperation("列表查询")
    @PostMapping("/pageList")
    public Result<Page<StoreQuotaVO>> list(@RequestBody StoreQuotaDTO storeQuotaDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(storeQuotaService.list(storeQuotaDTO,loginUser));
    }

    @ApiOperation("新增修改")
    @PostMapping("/saveOrUpdate")
    public Result<Boolean> add(@RequestBody StoreQuotaEntity storeQuota, @CurrentUser LoginUser loginUser) {
        Assert.notNull(storeQuota.getInitQuota(), () -> new BusinessException("初始额度不能为空"));
        Assert.notNull(storeQuota.getFundId(), () -> new BusinessException("资方不能为空"));
        Assert.isFalse(ObjUtil.isNull(storeQuota.getRegionId()) || ObjUtil.isNull(storeQuota.getRegionName()), () -> new BusinessException("大区不能为空"));
        Assert.isFalse(ObjUtil.isNull(storeQuota.getDeptId()) || ObjUtil.isNull(storeQuota.getDeptName()), () -> new BusinessException("门店不能为空"));
        Assert.notNull(ObjUtil.isNull(storeQuota.getEnable()), () -> new BusinessException("是否启用不能为空"));
        Assert.isFalse(ObjUtil.isNotNull(storeQuota.getEndDate()) && storeQuota.getEndDate().isBefore(LocalDate.now()), () -> new BusinessException("失效时间不能早于当前时间"));
        Assert.isFalse(ObjUtil.isNull(storeQuota.getBeginDate()) || ObjUtil.isNull(storeQuota.getEndDate()), () -> new BusinessException("生效时间不能为空"));

        storeQuotaService.saveOrUpdate(storeQuota,loginUser);
        return Result.success(Boolean.TRUE);
    }

    @ApiOperation("获取门店额度信息情况")
    @PostMapping("/getStoreQuotaInfo")
    public Result<StoreQuotaVO> getStoreQuotaInfo(@RequestBody StoreQuotaDTO dto) {
        Assert.notNull(dto.getId(), () -> new BusinessException("Id不能为空"));
        StoreQuotaVO result = storeQuotaService.getStoreQuotaInfo(dto);
        return Result.success(result);
    }

    @ApiOperation("获取资方大区额度情况")
    @PostMapping("/getFundRegionQuota")
    public Result<StoreQuotaVO> getFundRegionQuota(@RequestBody StoreQuotaDTO dto) {
        Assert.notNull(dto.getFundId(), () -> new BusinessException("资方不能为空"));
        Assert.notNull(dto.getRegionId(), () -> new BusinessException("大区不能为空"));
        StoreQuotaVO result = storeQuotaService.getFundRegionQuota(dto);
        return Result.success(result);
    }

    @ApiOperation("操作日志列表查询")
    @PostMapping("/pageOperationList")
    public Result<Page<TableOperationLogVO>> pageOperationList(@RequestBody StoreQuotaDTO storeQuotaDTO, @CurrentUser LoginUser loginUser) {
        Assert.notNull(storeQuotaDTO.getId(), () -> new BusinessException("ID不能为空"));
        return Result.success(storeQuotaService.pageOperationList(storeQuotaDTO,loginUser));
    }

    @ApiOperation("门店额度控制进行处理")
    @PostMapping("/failureTimeStoreQuota")
    public Result<Boolean> failureTimeStoreQuota() {
        storeQuotaService.failureTimeStoreQuota();
        return Result.success(true);
    }

}
