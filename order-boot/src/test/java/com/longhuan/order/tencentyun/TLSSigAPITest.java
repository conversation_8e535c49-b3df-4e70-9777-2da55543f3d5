package com.longhuan.order.tencentyun;

import com.tencentyun.TLSSigAPIv2;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;



public class TLSSigAPITest {

    @Before
    public void before() {
    }

    @After
    public void after() {
    }

    /**
     * Method: genSig(String identifier, long expire)
     */
    @Test
    public void testGenSig() {
        com.tencentyun.TLSSigAPIv2 api = new com.tencentyun.TLSSigAPIv2(1600069676, "3f6f7b3bacf65656428aba6af449179322e9d2b2142821e79a0622d3bfc5c6b7");
        System.out.print(api.genUserSig("139", 180 * 86400));
    }

    //使用userbuf生产privatemapkey
    @Test
    public void testGenSigWithUserBuf() {
        com.tencentyun.TLSSigAPIv2 api = new com.tencentyun.TLSSigAPIv2(1400000000, "5bd2850fff3ecb11d7c805251c51ee463a25727bddc2385f3fa8bfee1bb93b5e");
        System.out.println(api.genPrivateMapKey("xiaojun", 180 * 86400, 10000, 255));
    }

    //使用userbuf和字符串房间号生产privatemapkey
    @Test
    public void testGenSigWithUserBuf1() {
        com.tencentyun.TLSSigAPIv2 api = new TLSSigAPIv2(1400000000, "5bd2850fff3ecb11d7c805251c51ee463a25727bddc2385f3fa8bfee1bb93b5e");
        System.out.println(api.genPrivateMapKeyWithStringRoomID("xiaojun", 180 * 86400, "100000000", 255));
    }

    /**
     * Method: hmacsha256(String identifier, long currTime, long expire)
     */
    @Test
    public void testHmacsha256() {
    }

} 
