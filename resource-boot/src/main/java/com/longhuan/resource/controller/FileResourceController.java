package com.longhuan.resource.controller;

import cn.hutool.core.collection.CollUtil;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.resource.converter.FileResourceConverter;
import com.longhuan.resource.pojo.dto.*;
import com.longhuan.resource.pojo.entity.FileResourceEntity;
import com.longhuan.resource.pojo.vo.*;
import com.longhuan.resource.service.FileConfigService;
import com.longhuan.resource.service.FileResourceService;
import com.longhuan.resource.service.SftpUploadService;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 文件处理
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/fileResource")
public class FileResourceController {

    private final FileResourceService fileResourceService;
    private final FileResourceConverter fileResourceConverter;
    private final FileConfigService fileConfigService;
    private final SftpUploadService sftpUploadService;


    /**
     * 批量上传文件
     *
     * @param files
     * @return
     */

    @PostMapping(value = "/uploadFiles")
    public Result<List<FileVO>> uploadFile(@RequestParam("files") List<MultipartFile> files, Integer fileBelong) {
        return Result.success(fileResourceService.handlerResourceFile(files, fileBelong));
    }

    /**
     * 批量下载文件
     *
     * @param resourceIds
     * @return
     */
    @PostMapping(value = "/batchDownloadFiles")
    public void batchDownloadFiles(@RequestBody List<String> resourceIds, HttpServletResponse response) {
        fileResourceService.batchDownloadFiles(resourceIds, response);
    }

    @PostMapping("/batchDownloadByNameZip")
    public void batchDownloadByNameZip(@RequestBody BatchDownloadByNameZipDTO batchDownloadByNameZipDTO, HttpServletResponse response) {
        fileResourceService.batchDownloadByNameZip(batchDownloadByNameZipDTO, response);
    }


    /**
     * 获取文件基础信息
     *
     * @param resourceId 文件id
     */
    @GetMapping(value = "/queryInfoByResourceId/{resourceId}")
    public Result<FileResourceInfoResultVO> queryInfoByResourceId(@PathVariable("resourceId") String resourceId) {
        FileResourceEntity fileResource = fileResourceService.getById(resourceId);
        return Result.success(fileResourceConverter.infoVo2Entity(fileResource));
    }

    /**
     * 下载文件
     *
     * @param
     * @return
     */
    @GetMapping(value = "/downloadFile")
    public void downloadFile(String resourceId, HttpServletResponse response) {
        fileResourceService.downloadFile(resourceId, response);
    }


    /**
     * 删除资源
     *
     * @param
     * @return
     */
    @PostMapping(value = "/file/delete")
    public Result<String> deleteFile(@RequestBody @Validated FileDelDTO fileDelDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(fileResourceService.deleteFile(fileDelDTO, loginUser));
    }


    /**
     * 图片预览
     *
     * @param resourceId
     * @return
     * @throws IOException
     */
    @GetMapping("/image")
    public ResponseEntity<byte[]> getImage(@RequestParam("resourceId") String resourceId,
                                           @RequestParam(value = "thumbnail", required = false) String thumbnail) {
        return fileResourceService.previewImg(resourceId, thumbnail);
    }


    /**
     * 资源Id获取文件内容
     *
     * @param resourceId 资源 ID
     * @return {@link ResponseEntity }<{@link byte[] }>
     */
    @GetMapping("/file/content")
    public ResponseEntity<byte[]> fileContent(@RequestParam("resourceId") String resourceId) {
        return fileResourceService.fileContent(resourceId);
    }

    @GetMapping("/file/info")
    Result<FileInfoVO> fileInfo(@RequestParam String resourceId) {
        return fileResourceService.fileInfo(resourceId);
    }

    @ApiOperation(value = "法大大文件处理")
    @PostMapping("/fdd/process")
    Result<Boolean> processFile(@RequestBody List<String> resourceIds) {

        return Result.success(fileResourceService.processFile(resourceIds));
    }

    @ApiOperation(value = "下载法大大已签署完文件")
    @PostMapping("/downLoadSignZip")
    Result<List<DownZipFileVO>> downLoadSignZip(@RequestBody SignZipDTO signZipDTO) {
        return Result.success(fileResourceService.downLoadSignZip(signZipDTO));
    }

    /**
     * 蓝本价车评报告图片路径
     *
     * @param orderId
     * @return
     * @throws IOException
     */
    @GetMapping("/getCarImage")
    public List<CarImageVO> getCarImage(@RequestParam("orderId") Integer orderId, @RequestParam("code") String code) {
        return fileResourceService.getCarImage(orderId, code);
    }

    @PostMapping("/queryFileByIds")
    public Result<List<FileResourceResultVO>> queryFileByIds(@RequestBody List<String> resourceIds) {
        if (CollUtil.isEmpty(resourceIds)) {
            return Result.success(Collections.emptyList());
        }
        List<FileResourceResultVO> fileResourceList = fileResourceService.getFileByResourceIds(resourceIds);
        return Result.success(fileResourceList);
    }

    @GetMapping("/selectFileConfigByCode")
    public Result<Integer> selectFileConfigByCode(@RequestParam String code) {
        Integer fileResourceList = fileConfigService.selectFileConfigByCode(code);
        return Result.success(fileResourceList);
    }

    /**
     * 批量查询文件配置
     *
     * @return key code fileConfigId
     */
    @PostMapping("/selectFileConfigByCodeList")
    Result<Map<String, Integer>> selectFileConfigByCodeList(@RequestBody ResourceFileCodeDTO dto) {

        return Result.success(fileConfigService.selectFileConfigByCodes(dto.getCodes()));
    }

    /**
     * 第三方文件下载
     *
     * @param thirdResourceDTO
     * @return
     */
    @PostMapping("/third/downLoad")
    public Result<List<FileResourceResultVO>> downLoadThirdResource(@RequestBody ThirdResourceDTO thirdResourceDTO) {
        return Result.success(fileResourceService.downLoadThirdResource(thirdResourceDTO));
    }

    @PostMapping("/upload/fadd")
    public Result<Boolean> uploadFadd(@RequestBody List<String> resourceIds) {
        return Result.success(fileResourceService.uploadFadd(resourceIds));
    }

    /**
     * 上传sftp
     */
    @PostMapping("/uploadSftp")
    public Result<String> uploadSftp(@RequestBody UploadSftpDTO uploadSftpDTO) {
        return Result.success(sftpUploadService.uploadFile(uploadSftpDTO));
    }

    /**
     * 下载sftp
     */
    @PostMapping("/downloadSftp")
    public Result<FileVO> downloadSftp(@RequestBody DownloadSftpDTO downloadSftpDTO) {
        return Result.success(sftpUploadService.downloadFile(downloadSftpDTO));
    }

    /**
     * 解析竖线分割文本
     *
     * @param fileUid resource FileUid
     * @return 一个二维字符串列表，其中每个内部列表表示文件中的一行数据，每个元素表示该行中的一个字段。
     */
    @GetMapping("/parseVerticalTxt")
    public Result<List<List<String>>> parseVerticalTxt(@RequestParam("fileUid") String fileUid) {
        List<List<String>> parseList = null;
        try {
            parseList = fileResourceService.parseVerticalTxtByFileUid(fileUid);
        } catch (BusinessException e) {
            return Result.failed(e.getMessage());
        } catch (Exception e) {
            log.error("FileResourceController.parseVerticalTxt error {}", e.getMessage(), e);
            return Result.failed("解析失败");
        }
        return Result.success(parseList);
    }

    /**
     * 上传竖线分割文本返回文件id
     *
     * @param dtoList
     * @return resourceId
     */
    @PostMapping("/uploadVerticalTxt")
    public Result<List<FileVO>> uploadVerticalTxt(@RequestBody List<UploadVerticalTxtDTO> dtoList) {
        return Result.success(fileResourceService.uploadVerticalTxt(dtoList));
    }

    /**
     * 上传竖线分割文本至sftp
     *
     * @return resourceId
     */
    @PostMapping("/uploadVerticalTxtToSftp")
    public void uploadVerticalTxtToSftp(@RequestBody List<String> resourceIdList) {
        fileResourceService.uploadVerticalTxtToSftp(resourceIdList);
    }


    /**
     * 解析csv
     * map key 文件resourceId
     * value 一个二维字符串列表，其中每个内部列表表示文件中的一行数据，每个元素表示该行中的一个字段。
     */
    @PostMapping("/parseCsv")
    Result<Map<String, List<List<String>>>> parseCsv(@RequestBody ParseCsvDTO dto) {
        Map<String, List<List<String>>> stringListMap = null;
        try {
            stringListMap = fileResourceService.parseCsv(dto.getTitleLine(), dto.getFileUidList());
        } catch (Exception e) {
            log.error("FileResourceController.parseCsv error {}", e.getMessage(), e);
            return Result.failed("解析失败");
        }

        return Result.success(stringListMap);
    }


    /**
     * 指定uid上传文件
     *
     * @param uuid
     * @param file
     * @return
     */
    @PostMapping(value = "/uuid/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<FileVO> uploadFileByUuid(@RequestPart("uuid") String uuid, @RequestPart("file") MultipartFile file) {
        return Result.success(fileResourceService.uploadFileByUuid(uuid, file));
    }


    /**
     * 将图像合并到 PDF
     *
     * @param resourceIds 资源 ID
     * @return {@link ResponseEntity }<{@link byte[] }>
     */
    @PostMapping("/mergeImage/to/pdf")
    ResponseEntity<byte[]> mergeImageToPdf(@RequestBody List<String> resourceIds) {
        return ResponseEntity.ok(fileResourceService.mergeImageToPdf(resourceIds));
    }


    /**
     * 资源Id获取文件内容转化为文件流 做base64 处理
     *
     * @param resourceId 资源 ID
     */
    @GetMapping("/file/getBase64ByResourceId")
    public String getBase64ByResourceId(@RequestParam("resourceId") String resourceId) {
        return fileResourceService.getBase64ByResourceId(resourceId);
    }


    /**
     * 根据文件地址从阿里云下载视频文件上传到服务器
     * @param liveFileDTO
     * @return
     */
    /*@PostMapping(value = "/getAliyunUploadResource")
    public Result<List<FileVO>> getAliyunUploadResource(@RequestBody  LiveFileDTO liveFileDTO) {
        return Result.success(fileResourceService.getAliyunUploadResource(liveFileDTO));
    }*/

    /**
     * 批量上传sftp
     */
    @PostMapping("/batchUploadSftp")
    public void batchUploadSftp(@RequestBody BatchUploadSftpDTO batchUploadSftpDTO) {
        fileResourceService.batchUploadSftp(batchUploadSftpDTO);
    }

    /**
     * 指定文件存在则从sftp下载文件
     */
    @PostMapping("/downloadSftpIfFileExist")
    public Result<List<FileVO>> downloadSftpIfFileExist(@RequestBody SftpFileExistDTO sftpFileExistDTO) {
        return Result.success(sftpUploadService.downloadSftpIfFileExist(sftpFileExistDTO));
    }

    /**
     * 指定是否文件存在
     */
    @PostMapping("/ifFileExist")
    public Result<Boolean> ifFileExist(@RequestBody SftpFileExistDTO sftpFileExistDTO) {
        return Result.success(sftpUploadService.ifFileExist(sftpFileExistDTO));
    }
}
