<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.longhuan.order.mapper.ReviewMapper">
    <select id="getReviewInfo" resultType="com.longhuan.order.pojo.dto.ReviewInfoDTO">
        select
            lor.review_result ,lor.review_remark ,lor.refuse_reason,lor.file_uid,lor.review_video_uid,
            lfr.file_old_name ,lffc.file_uid as fundFileUid,
            lcsi.sign_type ,lcsi.sign_province_name ,
            lcsi.sign_city_name ,lcsi.sign_area_name ,lcsi.sign_detail,
            lfr2.file_old_name as signFileName,lfr3.file_old_name as videoFileName,
            lcsi.review_will As reviewWill
        from lh_customer_sign_info lcsi
                 left join lh_order_info loi  on lcsi.order_id = loi.id
                 left join lh_order_review lor on lcsi.order_id = lor.order_id
                 left join lh_fund_file_config lffc on lffc.fund_id = loi.fund_id
                 left join lh_file_resource lfr on lffc.file_uid = lfr.file_uid
                 left join lh_file_resource lfr2 on lor.file_uid = lfr2.file_uid
                 left join lh_file_resource lfr3 on lor.review_video_uid = lfr3.file_uid
        where loi.id = #{orderId}
    </select>

    <select id="getBusinessInfo" resultType="com.longhuan.order.pojo.dto.BusinessInfoDTO">
        select loi.order_number ,loi.fund_id ,loi.fund_name ,loi.apply_amount,
               loi.term ,loi.repay_method,loi.customer_name ,loi.store_name,loi.payment_type,
               lpi.term as guaranteeTerm,lpi.monthly_rate,lpi.name productName,
               lpaai."type" ,loci.phone , loci.id_number ,lpaai.account_manager_id,
               lovi.brand, lovi.vehicle_series, lovi.vehicle_model,lovi.vehicle_number,
               lovi.vin,lovi.mileage,estimated_value,lovi.register_date,
               lovi.owner_type,
               lpvi.holder
        from lh_order_info loi
            left join lh_product_info lpi on loi.product_id = lpi.id
            left join lh_pre_approval_apply_info lpaai on loi.pre_id = lpaai.id
            left join lh_pre_vehicle_info lpvi on lpvi.pre_id = loi.pre_id
            left join lh_order_customer_info loci on loci.id = loi.customer_id
            left join lh_order_vehicle_info lovi on lovi.order_id = loi.id
        where loi.id = #{orderId}
    </select>
</mapper>