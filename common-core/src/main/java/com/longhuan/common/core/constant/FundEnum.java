package com.longhuan.common.core.constant;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 资方枚举
 * 资方枚举 （蓝海：lanHai，长安：changAn，客商：hakka，苏商：su<PERSON><PERSON>，盈峰：ying<PERSON><PERSON>，中关村：zgc，中恒：zhong<PERSON><PERSON> , 富民：fuMin）
 * <AUTHOR>
 * @date 2024/10/17
 */
@Getter
public enum FundEnum {
    ZHONG_HENG(8, "ZHONG_HENG", "中恒"),
    LAN_HAI(3, "LAN_HAI", "蓝海"),
    CAI_XIN(10, "CAI_XIN", "财信"),
    ZHONG_GUAN_CUN(7, "ZHONG_GUAN_CUN", "中关村"),
    CHANG_AN(6, "CHANG_AN", "长安"),
    KE_SHANG(1, "KE_SHANG", "客商"),
    SU_SHANG(4, "SU_SHANG", "苏商"),
    FU_MIN(11, "FU_MIN", "富民"),
    YING_FENG(5, "YING_FENG", "盈峰"),
    NING_BO(12, "NING_BO", "宁波"),
    HAI_ER(13, "HAI_ER", "海尔"),
    ZHONG_HENG_SHANG_GAO(14, "ZHONG_HENG", "中恒山东高速"),
    ZHONG_HENG_TONG_HUI(15, "HENG_TONG", "通汇"),
    CHANG_YIN(16, "CHANG_YIN", "长银"),
    MZ_KE_SHANG(17, "KE_SHANG", "梅州客商"),
    ;

    @JsonValue
    private final int value;
    private final String fundCode;
    private final String fundName;

    FundEnum(int value, String fundCode, String fundName) {
        this.value = value;
        this.fundCode = fundCode;
        this.fundName = fundName;
    }


    /**
     * 获取资方枚举
     *
     * @param value 价值
     * @return {@link FundEnum }
     */
    public static FundEnum getFundEnum(int value) {
        return Arrays.stream(FundEnum.values()).filter(item -> Objects.equals(item.getValue(), value))
                .findFirst().orElseThrow(() -> new RuntimeException("未匹配到对应资方"));
    }
}
