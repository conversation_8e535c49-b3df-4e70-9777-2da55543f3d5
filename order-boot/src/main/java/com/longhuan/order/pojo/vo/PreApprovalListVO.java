package com.longhuan.order.pojo.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.longhuan.common.mybatis.annotation.FieldSensitive;
import com.longhuan.order.enums.ApprovalApplyInfoBusinessStatus;
import com.longhuan.order.enums.PreApplyInfoManagerStatus;
import com.longhuan.order.enums.PreListStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预先批准清单 DTO
 *
 * <AUTHOR>
 * @date 2024/08/13
 */
@Data
@Accessors
public class PreApprovalListVO {
    /**
     * 同上
     */
    private Integer id;
    /**
     * 名字
     */
    private String name;
    /**
     * 电话号码
     */
    @FieldSensitive("mobile")
    private String phoneNumber;
    /**
     * 申请日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private Date applyDate;
    /**
     * 申请金额
     */
    private BigDecimal applyAmount;
    /**
     * 期数
     */
    private Integer term;

    /**
     * 风控审批状态
     * 0-未发起，1-审批中，2-审批通过，3-审批拒绝
     */
    private Integer riskStatus;

    /**
     * 资方审批状态
     * 0-未发起，1-审批中，2-审批通过，3-审批拒绝
     */
    private Integer fundStatus;

    /**
     * 风控拒绝原因
     */
    private String riskRefuseReason;

    /**
     * 征信命中规则
     */
    private String creditMatchRule;
    /**
     * 资方Id
     */
    private Integer fundId;
    /**
     * 身份证号
     */
    private String idNumber;
    /**
     * 车牌号
     */
    private String vehicleNumber;
    /**
     * 门店id
     */
    private Integer storeId;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 大区id
     */
    private Integer regionId;
    /**
     * 大区名称
     */
    private String regionName;
    /**
     * 团队id
     */
    private Integer teamId;
    /**
     * 分部名称
     */
    private String branchName;
    /**
     * 客户经理id
     */
    private Integer accountManagerId;
    /**
     * 客户经理姓名
     */
    private String accountManagerName;
    /**
     * 业务状态
     */
    @ApiModelProperty(value = "业务状态", notes = "业务状态")
    private ApprovalApplyInfoBusinessStatus businessStatus;

    /**
     * 认证结果
     */
    private String authResult;

    /**
     * 客户评分等级 1：A，2：B，3：C，4：D
     */
    private Integer customerLevel;

    /**
     * 客户评分等级 A B C
     */
    private String customerScore;
    /**
     * 数据来源(0：门店，1：电销)
     */
    private Integer sourceType;
    /**
     * 是否为电销线上订单 1是 2否
     */
    private Integer isOnlineOrder;
    /**
     * 资方预审额度
     */
    private BigDecimal fundPreAmount;

    /**
     * 风控预审提醒
     */
    private String riskPreWarn;
    /**
     * 订单Id (进行中订单Id)
     */
    private Integer orderId;
    /**
     * 预审状态
     */
    private PreListStatusEnum preStatus;

    @ApiModelProperty(value = "客户经理预审批状态(0:未发起;1:预审中,2:预审通过,3:预审拒绝)")
    @JsonIgnore
    private PreApplyInfoManagerStatus managerState;

}
