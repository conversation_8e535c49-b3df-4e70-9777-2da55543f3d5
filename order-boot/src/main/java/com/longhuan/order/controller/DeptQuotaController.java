package com.longhuan.order.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.query.MPJLambdaQueryWrapper;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.mapper.DeptQuotaMapper;
import com.longhuan.order.pojo.dto.DeptQuotaPageDTO;
import com.longhuan.order.pojo.entity.DeptQuotaEntity;
import com.longhuan.order.pojo.vo.DeptQuotaVo;
import com.longhuan.order.service.DeptQuotaService;
import com.longhuan.common.core.enums.RoleEnum;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/***
 * 额度控制
 * <AUTHOR>
 * @date 2024/11/18
 */
@RestController
@RequestMapping("/api/v1/dept/quota")
@RequiredArgsConstructor
public class DeptQuotaController {
    private final DeptQuotaService deptQuotaService;
    private final DeptQuotaMapper deptQuotaMapper;

    /***
     * 列表查询
     */
    @ApiOperation("列表查询")
    @PostMapping("/page")
    public Result<IPage<DeptQuotaVo>> list(@RequestBody DeptQuotaPageDTO deptQuotaPageDTO) {
        Page<DeptQuotaVo> page = deptQuotaService.selectJoinListPage(Page.of(deptQuotaPageDTO.getPageNum(), deptQuotaPageDTO.getPageSize())
                , DeptQuotaVo.class, new MPJLambdaQueryWrapper<DeptQuotaEntity>()
                        .selectAll(DeptQuotaEntity.class)
                        .like(StrUtil.isNotBlank(deptQuotaPageDTO.getDeptName()), DeptQuotaEntity::getDeptName, deptQuotaPageDTO.getDeptName())
                        .eq(ObjUtil.isNotNull(deptQuotaPageDTO.getEnable()), DeptQuotaEntity::getEnable, deptQuotaPageDTO.getEnable())
                        .eq(ObjUtil.isNotNull(deptQuotaPageDTO.getType()), DeptQuotaEntity::getType, deptQuotaPageDTO.getType())
                        .eq(ObjUtil.isNotNull(deptQuotaPageDTO.getFundId()), DeptQuotaEntity::getFundId, deptQuotaPageDTO.getFundId())
                        .le(ObjUtil.isNotNull(deptQuotaPageDTO.getEffectiveTime()), DeptQuotaEntity::getBeginDate, deptQuotaPageDTO.getEffectiveTime())
                        .ge(ObjUtil.isNotNull(deptQuotaPageDTO.getEffectiveTime()), DeptQuotaEntity::getEndDate, deptQuotaPageDTO.getEffectiveTime())
                        .eq(DeptQuotaEntity::getDeleteFlag, 0)
                        .orderByDesc(DeptQuotaEntity::getId)
        );
        page.getRecords().forEach(item -> {
            item.setInitQuota(item.getInitQuota().divide(BigDecimal.valueOf(10000), RoundingMode.valueOf(2)));
            item.setRemainingQuota(item.getRemainingQuota().divide(BigDecimal.valueOf(10000), RoundingMode.valueOf(2)));
        });
        return Result.success(page);
    }

    @ApiOperation("新增修改")
    @PostMapping("/saveOrUpdate")
    public Result<Boolean> add(@RequestBody DeptQuotaEntity deptQuotaEntity) {
        if (deptQuotaEntity.getId() != null) {
            if(ObjUtil.isNotNull(deptQuotaEntity.getEnable()) && deptQuotaEntity.getEnable() == 1
                    && ObjUtil.isNull(deptQuotaEntity.getBeginDate()) && ObjUtil.isNull(deptQuotaEntity.getEndDate()) && ObjUtil.isNull(deptQuotaEntity.getInitQuota())){
                deptQuotaService.judgeStoreQuota(deptQuotaEntity);
            }
            while (true) {
                DeptQuotaEntity oldDeptQuotaEntity = deptQuotaService.getById(deptQuotaEntity.getId());
                Assert.isFalse(ObjUtil.isNotNull(deptQuotaEntity.getEndDate()) && deptQuotaEntity.getEndDate().isBefore(LocalDate.now()),
                        () -> new BusinessException("失效时间不能早于当前时间"));
                if (ObjUtil.equals(ObjUtil.defaultIfNull(deptQuotaEntity.getEnable(), oldDeptQuotaEntity.getEnable()), 0)) {
                    Assert.isFalse(ObjUtil.defaultIfNull(deptQuotaEntity.getEndDate(), oldDeptQuotaEntity.getEndDate()).isBefore(LocalDate.now()),
                            () -> new BusinessException("该额度已失效，无法启用"));
                    deptQuotaService.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                            .set(DeptQuotaEntity::getEnable, 1)
                            .eq(DeptQuotaEntity::getDeptId, oldDeptQuotaEntity.getDeptId())
                            .eq(DeptQuotaEntity::getFundId, oldDeptQuotaEntity.getFundId())
                            .and(wrapper -> wrapper
                                    .or(on -> on.between(DeptQuotaEntity::getEndDate, ObjUtil.defaultIfNull(deptQuotaEntity.getBeginDate(), oldDeptQuotaEntity.getBeginDate()), ObjUtil.defaultIfNull(deptQuotaEntity.getEndDate(), oldDeptQuotaEntity.getEndDate())))
                                    .or(on -> on.between(DeptQuotaEntity::getBeginDate, ObjUtil.defaultIfNull(deptQuotaEntity.getBeginDate(), oldDeptQuotaEntity.getBeginDate()), ObjUtil.defaultIfNull(deptQuotaEntity.getEndDate(), oldDeptQuotaEntity.getEndDate())))
                                    .or(on -> on.ge(DeptQuotaEntity::getBeginDate, ObjUtil.defaultIfNull(deptQuotaEntity.getBeginDate(), oldDeptQuotaEntity.getBeginDate()))
                                            .le(DeptQuotaEntity::getEndDate, ObjUtil.defaultIfNull(deptQuotaEntity.getEndDate(), oldDeptQuotaEntity.getEndDate())))
                                    .or(on -> on.le(DeptQuotaEntity::getBeginDate, ObjUtil.defaultIfNull(deptQuotaEntity.getBeginDate(), oldDeptQuotaEntity.getBeginDate()))
                                            .ge(DeptQuotaEntity::getEndDate, ObjUtil.defaultIfNull(deptQuotaEntity.getEndDate(), oldDeptQuotaEntity.getEndDate())))
                            )
                            .ne(DeptQuotaEntity::getId, deptQuotaEntity.getId()));
                }
                LambdaUpdateWrapper<DeptQuotaEntity> wrapper = new LambdaUpdateWrapper<DeptQuotaEntity>()
                        .set(ObjUtil.isNotNull(deptQuotaEntity.getEnable()), DeptQuotaEntity::getEnable, deptQuotaEntity.getEnable())
                        .set(ObjUtil.isNotNull(deptQuotaEntity.getBeginDate()), DeptQuotaEntity::getBeginDate, deptQuotaEntity.getBeginDate())
                        .set(ObjUtil.isNotNull(deptQuotaEntity.getEndDate()), DeptQuotaEntity::getEndDate, deptQuotaEntity.getEndDate())
                        .eq(DeptQuotaEntity::getId, deptQuotaEntity.getId());
                if (ObjUtil.isNotNull(deptQuotaEntity.getInitQuota())) {
                    BigDecimal difference=deptQuotaEntity.getInitQuota().multiply(BigDecimal.valueOf(10000)).subtract(oldDeptQuotaEntity.getInitQuota());
                    if ((oldDeptQuotaEntity.getRemainingQuota().add(difference).compareTo(BigDecimal.ZERO) < 0)) {
                        throw new BusinessException("更改之后剩余额度不能少于0");
                    }
                    wrapper.setIncrBy(ObjUtil.isNotNull(deptQuotaEntity.getInitQuota()), DeptQuotaEntity::getRemainingQuota,difference)
                            .set(ObjUtil.isNotNull(deptQuotaEntity.getInitQuota()), DeptQuotaEntity::getInitQuota, deptQuotaEntity.getInitQuota().multiply(BigDecimal.valueOf(10000)))
                            .eq(DeptQuotaEntity::getRemainingQuota, oldDeptQuotaEntity.getRemainingQuota());
                }
                int update = deptQuotaMapper.update(wrapper);
                if (update > 0) {
                    return Result.success();
                }
            }
        } else {
            Assert.notNull(deptQuotaEntity.getInitQuota(), () -> new BusinessException("初始额度不能为空"));
            Assert.isFalse(ObjUtil.isNull(deptQuotaEntity.getDeptId()) || ObjUtil.isNull(deptQuotaEntity.getDeptName()),
                    () -> new BusinessException("部门不能为空"));
            Assert.notNull(deptQuotaEntity.getType(), () -> new BusinessException("额度类型不能为空"));
            Assert.notNull(deptQuotaEntity.getFundId(), () -> new BusinessException("资方不能为空"));
            Assert.isFalse(ObjUtil.isNull(deptQuotaEntity.getBeginDate()) || ObjUtil.isNull(deptQuotaEntity.getEndDate()), () -> new BusinessException("生效时间不能为空"));
            deptQuotaEntity.setInitQuota(deptQuotaEntity.getInitQuota().multiply(BigDecimal.valueOf(10000)));
            if (ObjUtil.isNull(deptQuotaEntity.getRemainingQuota())) {
                deptQuotaEntity.setRemainingQuota(deptQuotaEntity.getInitQuota());
            } else {
                deptQuotaEntity.setRemainingQuota(deptQuotaEntity.getRemainingQuota().multiply(BigDecimal.valueOf(10000)));
            }
            if (ObjUtil.equals(deptQuotaEntity.getEnable(), 0)) {
                deptQuotaService.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                        .set(DeptQuotaEntity::getEnable, 1)
                        .eq(DeptQuotaEntity::getDeptId, deptQuotaEntity.getDeptId())
                        .eq(DeptQuotaEntity::getFundId, deptQuotaEntity.getFundId())
                        .and(wrapper -> wrapper
                                .or(on -> on.between(DeptQuotaEntity::getEndDate, deptQuotaEntity.getBeginDate(), deptQuotaEntity.getEndDate()))
                                .or(on -> on.between(DeptQuotaEntity::getBeginDate, deptQuotaEntity.getBeginDate(), deptQuotaEntity.getEndDate()))
                                .or(on -> on.ge(DeptQuotaEntity::getBeginDate, deptQuotaEntity.getBeginDate())
                                        .le(DeptQuotaEntity::getEndDate, deptQuotaEntity.getEndDate()))
                                .or(on -> on.le(DeptQuotaEntity::getBeginDate, deptQuotaEntity.getBeginDate())
                                        .ge(DeptQuotaEntity::getEndDate, deptQuotaEntity.getEndDate())))
                );
            }
            return Result.success(deptQuotaService.save(deptQuotaEntity));
        }
    }
    @ApiOperation("删除规则")
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody List<Integer> ids) {
        return Result.success(deptQuotaService.deleteDeptQuota(ids));
    }
    @ApiOperation("资方枚举")
    @PostMapping("/fundEnum")
    public Result<List<Map<String,Object>>> fundEnum(){
        Map<Integer,String> fundMap=new HashMap<>();
        for (FundEnum fund : FundEnum.values()) {
            fundMap.put(fund.getValue(), fund.getFundName());
        }
        // 将 Map 转换为 List<Map<String, Object>>
        List<Map<String, Object>> resultList = fundMap.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("value", entry.getKey());
                    map.put("name", entry.getValue());
                    return map;
                })
                .collect(Collectors.toList());

        return Result.success(resultList);
    }

}
