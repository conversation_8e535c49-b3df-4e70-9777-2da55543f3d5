package com.longhuan.order.statemachine.action;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.mapper.OrderNodeRecordMapper;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.entity.OrderNodeRecordEntity;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * 状态转移动作，在转移进行的时候触发的动作
 */
@Slf4j
@Component

public class ContractSignStateAction extends OrderNodeAction implements Action<States, Events> {

    public ContractSignStateAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper) {
        super(orderInfoMapper, orderNodeRecordMapper);
    }


    @Override
    public void execute(StateContext<States, Events> context) {


        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();
        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);

        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event, currentNode, nextNode);


        // 1. 修改订单表状态
        updateStates(orderId);

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);
    }

    /**
     * 修改订单表状态
     *
     * @param orderId
     */
    private void updateStates(Integer orderId) {
        OrderInfoEntity updateEntity = new OrderInfoEntity();

        updateEntity.setId(orderId);
        updateEntity.setContractState(2);
        this.orderInfoMapper.updateById(updateEntity);
    }

}