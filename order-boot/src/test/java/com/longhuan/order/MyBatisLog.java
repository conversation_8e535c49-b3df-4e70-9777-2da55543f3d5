package com.longhuan.order;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.longhuan.order.mapper.OrderAmountMapper;
import com.longhuan.order.pojo.dto.OrderApproveDTO;
import com.longhuan.order.pojo.entity.OrderAmountEntity;
import com.longhuan.order.pojo.vo.CustomerInfoVO;
import com.longhuan.order.pojo.vo.OrderDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@Slf4j
@SpringBootTest(classes = OrderApplication.class)
class MyBatisLog {

    @Autowired
    private OrderAmountMapper orderAmountMapper;
    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void test() {
        List<OrderAmountEntity> orderAmountEntities = orderAmountMapper.selectList(null);
        log.info("orderAmountEntities:{}", orderAmountEntities.size());
        int update = orderAmountMapper.update(new LambdaUpdateWrapper<OrderAmountEntity>()
                .set(OrderAmountEntity::getHopeAmount, 100.01)
                .eq(OrderAmountEntity::getId, 4)
        );
        log.info("update:{}", update);
    }

    @Test
    void test2() throws JsonProcessingException {
        OrderDetailVO orderDetailVO = new OrderDetailVO();

        CustomerInfoVO customerInfo = new CustomerInfoVO();
        customerInfo.setPhone("12345678901");
        orderDetailVO.setCustomerInfo(customerInfo);

        log.info("orderDetailVO:{}", objectMapper.writeValueAsString(orderDetailVO));
        log.info("customerInfo:{}", objectMapper.writeValueAsString(customerInfo));
    }
}