package com.longhuan.order.api;


import com.longhuan.common.core.enums.OrderFeeDetailExpandTypeEnum;
import com.longhuan.common.core.enums.OrderFeeDetailStatusEnum;
import com.longhuan.common.core.enums.PayApplicationNodeEnums;
import com.longhuan.common.core.enums.PayApplicationPayeeTypeEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.enums.Events;
import com.longhuan.order.enums.States;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.dto.accountBank.BankPayDeductQueryDTO;
import com.longhuan.order.pojo.dto.accountBank.BankPayDeductDTO;
import com.longhuan.order.pojo.dto.tongLianTong.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.pojo.vo.accountBank.BankDeductQueryVO;
import com.longhuan.order.pojo.vo.accountBank.BankDeductVO;
import com.longhuan.order.pojo.vo.tongliantong.TongLianTongProtocolPayVO;
import com.longhuan.order.pojo.vo.tongliantong.TongLianTongQtransreqVO;
import com.longhuan.user.pojo.vo.UserQrCodeVO;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.swing.plaf.nimbus.State;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单 API
 *
 * <AUTHOR>
 * @date 2024/07/03
 */
public interface OrderApi {
    @GetMapping("/order/searchUserByUuid")
    public Result<UserQrCodeVO> searchUserByUuid(@RequestParam String uuid);

    @PostMapping("/ocrIdentityCard/checkProtection")
    public Result<Integer> checkProtection(@RequestBody CheckProtectionVo checkProtectionVo);

    @PostMapping(value = "/fdd/upload/url")
    Result<Boolean> uploadFileTOFdd(@RequestBody List<FileTOFddVO> vo);

    @PostMapping(value = "/fdd/file/process")
    Result<Boolean> processFile(@RequestBody List<String> resourceIds);

    @PostMapping("/resetCddSequence")
    public Result<String> resetCddSequence();

    @PostMapping("/resetFddSequence")
    public Result<String> resetFddSequence();

    @PostMapping("/fddFinishFile/list")
    public Result<List<PreFddFinishFileVO>> queryFinishFileList(@RequestBody PreFddFinishFileListReqDTO fddFinishFileListReqDTO);

    @GetMapping("/api/v1/pre/approval/risk/launch/batch")
    public Result<String> riskLaunchBatch();

    @GetMapping("/api/v1/pre/approval/risk/data/batch")
    public Result<String> riskDataBatch();

    @PostMapping("/api/v1/order/menu/save_order_file")
    Result<Integer> saveOrderFile(@RequestBody SaveOrderFileDTO saveOrderFileDTO);

    @PostMapping("/api/v1/order/menu/batchOrderFile")
    Result<Boolean> batchOrderFile(@Validated @RequestBody BatchSaveOrderFileDTO batchSaveOrderFileDTO);

    @GetMapping("/api/v1/pre/approval/risk/policy/batch")
    public Result<String> riskPolicyBatch();

    @GetMapping("/api/v1/pre/approval/risk/result/batch")
    public Result<String> riskResultBatch();

    @GetMapping("/api/v1/customer/appointment/searchTranxScheduled")
    public Result<String> searchTranxScheduled();

    @GetMapping("/api/v1/pre/managerApproval/updateFund")
    Result<Boolean> preManagerUpdateFund(@RequestBody PreManagerUpdateFundDTO dto);

    @GetMapping("/api/v1/customer/appointment/searchCardNum")
    Result<Integer> searchBankCardNum(@RequestParam Integer userId);

    @GetMapping("/api/v1/mortgage/getDetailByNo")
    Result<Boolean> getZhongRuiMortgageInfo();

    @GetMapping("/api/v1/gps/detailByOrderId/{orderId}")
    Result<GpsDetailVO> gpsDetailByOrderId(@PathVariable("orderId") Integer orderId);

    @PostMapping("/api/v1/contract/file/info")
    Result<List<ContractResourceVO>> getContractUrls(@RequestBody ContractUrlsDTO contractUrlsDTO);

    @GetMapping("/api/v1/manual/callback/order")
    Result<String> manualCallback();

    @PostMapping("/api/v1/contract/fund/upload/info")
    Result<Boolean> getFundUploadFileInfo(@RequestBody List<ContractToFundVO> fundVOList);

    @PostMapping("/api/v1/contract/fund/download/file")
    Result<Boolean> getFundDownloadFile();

    @PostMapping("/api/v1/contract/update/order")
    Result<Boolean> updateOrderContactStatus();

    @ApiModelProperty(value = "通过订单id查询订单联系人信息")
    @GetMapping("/orderContactPerson/getOrderContactPersonList/{orderId}")
    Result<List<OrderContactPersonVo>> getOrderContactPersonList(@PathVariable("orderId") Integer orderId);

    @ApiOperation(value = "根据订单id获取办抵详情")
    @PostMapping("/api/v1/mortgage/detailByOrderId/{orderId}")
    Result<MortgageDetailVO> getMortgageDetailByOrderId(@PathVariable(value = "orderId") Integer orderId);


    @PostMapping("/api/v1/amount/cal")
    Result<AmountCalVO> calAmount(@RequestBody AmountCalDTO amountCalDTO);

    @ApiOperation(value = "预审更新资方状态")
    @PostMapping("/api/v1/pre/managerApproval/preUpdateFundStatus")
    Result<Boolean> preUpdateFundStatus(@RequestBody PreApproveFundStatusDTO fundStatusDTO);

    @ApiOperation(value = "合同生成重试")
    @GetMapping("/api/v1/contract/contractGenerateRetry")
    Result<String> contractGenerateRetry();

    @ApiOperation(value = "更新资方放款审批状态")
    @PostMapping("/api/v1/order/updateFundPaymentStatus")
    Result<Boolean> updateFundPaymentStatus(@RequestBody @Validated OrderApproveFundPaymentStatusDTO fundStatusDTO);

    @ApiOperation(value = "更新资方合同")
    @PostMapping("/api/v1/order/updateFundContract")
    Result<Boolean> updateFundContract(@RequestBody OrderFundContractDTO fundStatusDTO);

    @ApiOperation(value = "更新资方终审审批状态")
    @PostMapping("/api/v1/order/updateFundFinalStatus")
    Result<Boolean> updateFundFinalStatus(@RequestBody @Validated FinalApproveFundStatusDTO fundStatusDTO);

    @ApiOperation(value = "更新资方还款状态")
    @PostMapping("/api/v1/order/updateFundPlanStatus")
    Result<Boolean> updateFundPlanStatus(@RequestBody @Validated OrderApproveFundPlanStatusDTO fundStatusDTO);

    @ApiOperation(value = "审批流资方结果更新")
    @GetMapping("/api/v1/approval/fund/final/result/batch")
    Result<String> fundFinalResultBatch();

    @ApiOperation(value = "根据订单id获取GPS信息")
    @GetMapping("/api/v1/gps/getInfoByOrderId/{orderId}")
    Result<OrderGpsInfoVO> getGPSInfoByOrderId(@PathVariable("orderId") @Validated Integer orderId);

    @ApiOperation(value = "蓝本车辆评估报告PDF")
    @GetMapping("/vehicle/getCarReport/{orderId}")
    Result<String> getCarReport(@PathVariable("orderId") Integer orderId);

    @ApiOperation(value = "签署任务补偿")
    @PostMapping("/fdd/sign/task")
    Result<Boolean> signTaskResult();

    @ApiOperation(value = "根据订单id和文件配置编码获取文件uid")
    @PostMapping("/orderFile/getResourceIdByOrderId")
    Result<String> getResourceIdByOrderId(@RequestParam("orderId") Integer orderId,
                                          @RequestParam("fileConfigCode") String fileConfigCode);

    @ApiOperation(value = "根据订单id和文件配置编码获取文件uid")
    @PostMapping("/orderFile/getResourceMapByOrderId")
    Result<Map<String, List<String>>> getResourceMapByOrderId(@RequestBody ResourceMapByOrderFileDTO dto);

    /**
     * 根据订单id查询金额结果
     *
     * @return {@link Result< AmountCalVO>}
     */
    @GetMapping("/api/v1/amount/queryByOrderId/{orderId}")
    Result<AmountCalVO> queryAmountByOrderId(@PathVariable Integer orderId);

    @ApiOperation(value = "获取面签视频文件")
    @GetMapping("/api/v1/review/queryReviewVideoUidByOrderId")
    Result<String> queryReviewVideoUidByOrderId(@RequestParam("orderId") Integer orderId);

    /**
     * 获取个人授权状态
     *
     * @return
     */
    @PostMapping(value = "/fdd/user/get")
    Result<Boolean> getUserAuthStatus();

    /**
     * 同步补签用户签署状态及完成文件
     *
     * @return
     */
    @PostMapping(value = "/api/v1/restart/contract/fdd/download/file")
    Result<Boolean> downloadSignedFile();

    /**
     * 同步补签用户签署状态及完成文件
     *
     * @return
     */
    @PostMapping(value = "/fdd/retry/downloadFile")
    Result<Boolean> retryDownloadFile();

    /**
     * 合同签署任务补偿
     *
     * @return
     */
    @PostMapping(value = "/fdd/contract/sign/task")
    Result<Boolean> contractSignTaskResult();


    /**
     * 更新订单还款相关信息
     *
     * @param orderId 订单id
     */
    @PostMapping(value = "/api/v1/order/updateOrderFundRepayment")
    Result<Boolean> updateOrderFundRepayment(@RequestParam("orderId") Integer orderId);
//    /**
//     * 富民合同保存
//     * @return
//     */
//    @PostMapping(value = "/api/v1/contract/fuMin/preview/contract")
//    Result<Boolean> fuMinPreviewContract(@RequestBody FuMinPreviewContractDTO dto);

    /**
     * 更新抵押状态
     *
     * @return
     */
    @PostMapping(value = "/api/v1/mortgage/updateMortgageState")
    Result<Boolean> updateMortgageState(@RequestBody MortgageStateUpdateDTO mortgageStateUpdateDTO);


    /**
     * 法大大合同签署文件同步
     *
     * @return
     */
    @PostMapping(value = "/fdd/contract/sync/file")
    Result<Boolean> syncFddContractFile();

    /**
     * 法大大合同签署文件同步
     *
     * @return
     */
    @PostMapping(value = "/fdd/contract/sync/file/{orderId}")
    Result<Boolean> syncFddContractFileByOrderId(@PathVariable("orderId") Integer orderId);


    /**
     * 获取资方签约状态
     *
     * @return
     */
    @PostMapping(value = "/api/v1/contract/fund/sign/resul")
    Result<ContractFundSignResulVO> getFundSignVOResult(ContractFundSignResulDTO resultDTO);

    /**
     * 绩效审核自动提交
     *
     * @return {@link Result }<{@link String }>
     */
    @PostMapping(value = "/api/v1/payApplication/automaticSubmit")
    Result<String> automaticSubmit();

    /**
     * 流程自动终止
     */
    @ApiOperation(value = "流程自动终止")
    @PostMapping("/api/v1/order/process/terminal")
    Result<Boolean> processTerminal();

    /**
     * 客服派单
     */
    @ApiOperation(value = "客服派单")
    @PostMapping("/api/v1/orderApproveDistribute/distributeOrder")
    Result<Boolean> distributeOrder();

    /**
     * 保存交易明细
     */
    @ApiOperation(value = "保存交易明细")
    @PostMapping("/api/v1/order/orderFeeDetail/save")
    Result<Boolean> saveOrderFeeDetail(@RequestBody OrderFeeDetailSaveDTO orderFeeDetailSaveDTO);

    /**
     * 当法大大授权结果为空且更新时间大于一天时，更新为失效
     */
    @PostMapping("/updateAuthStatus")
    @ApiOperation(value = "更改法大大认证状态")
    Result<Boolean> updateAuthStatus();

    /**
     * 自动提交赎回审核定时任务
     *
     * @return
     */
    @PostMapping("/api/v1/payApplication/auto/repurchaseRepay")
    Result<Boolean> automatedRepurchaseRepay();


    @ApiOperation(value = "钉钉费用申请审批")
    @GetMapping("/api/v1/payApplication/approvePayApplicationDingTaskBatch")
    Result<Boolean> approvePayApplicationDingTask();

    @ApiOperation(value = "费用申请钉钉发起失败重试")
    @GetMapping("/api/v1/payApplication/retryDingTask")
    Result<Boolean> approvePayApplicationRetryDingTask();

    /**
     * 赎回结果处理
     *
     * @return
     */
    @PostMapping("/api/v1/repurchase/queryRepurchaseRepayment")
    Result<Boolean> queryRepurchaseRepayment(@RequestBody List<FundRepurchaseResultDTO> list);

    /**
     * 通联通还款
     *
     * @return
     */
    @PostMapping("/api/v1/tong_lian_tong/repay")
    Result<Boolean> tongLianTongRepay(@RequestParam("orderId") Integer orderId, @RequestParam("term") Integer term);

    /**
     * 通联通还款查询
     *
     * @return
     */
    @PostMapping("/api/v1/tong_lian_tong/repayQuery")
    Result<FundDeductQueryVO> tongLianTongRepayQuery(@RequestParam("orderId") Integer orderId, @RequestParam("term") Integer term);

    /**
     * 下载赎回合同
     *
     * @return
     */
    @PostMapping("/api/v1/repurchase/repurchase/contract")
    Result<Boolean> downloadRepurchaseContract();

    /**
     * 定时查询解抵结果状态
     */
    @PostMapping("/api/v1/finalFundInfo/updateMortgageCancelQuery")
    Result<Boolean> updateMortgageCancelQuery();

    @ApiOperation(value = "GPS安装回调定时任务")
    @GetMapping("/api/v1/gps/gpsInstallCallbackScheduledTasks")
    Result<Boolean> gpsInstallCallbackScheduledTasks();

    /**
     * 逾期90天的自动到案件表
     */
    @PostMapping("/api/v1/caseInfo/addCaseInfo")
    Result<Boolean> addCaseInfo();

    /**
     * 推送至众信
     */
    @PostMapping("/api/v1/Product/ZhongXin/pushCaseToZhongXin")
    Result<Boolean> pushCaseToZhongXin();

    @ApiOperation(value = "绑卡验证码发送")
    @PostMapping("/api/v1/tong_lian_tong/SMSTriggered")
    Result<String> sMSTriggered(@RequestBody TongLianTongSMSTriggeredDTO tongLianTongSMSTriggeredDTO);

    @ApiOperation(value = "通联绑卡验证")
    @PostMapping("/api/v1/tong_lian_tong/paymentSign")
    Result<String> paymentSign(@RequestBody TongLianTongSignDTO tongLianTongSignDTO);

    @ApiOperation(value = "协议支付解约")
    @PostMapping("/api/v1/tong_lian_tong/protocolPayCancel")
    Result<Boolean> protocolPayCancel(@RequestBody TongLianTongProtocolPayCancelDTO tongLianTongProtocolPayCancelDTO);

    @ApiOperation(value = "协议支付")
    @PostMapping("/api/v1/tong_lian_tong/negotiatedPayments")
    Result<TongLianTongProtocolPayVO> negotiatedPayments(@RequestBody TongLianTongNegotiatedPaymentsDTO tongLianTongNegotiatedPaymentsDTO);

    @ApiOperation(value = "协议支付结果查询")
    @PostMapping("/api/v1/tong_lian_tong/transactionResultInquiry")
    Result<List<TongLianTongQtransreqVO>> transactionResultInquiry(@RequestBody TongLianTongQtransreqDTO tongLianTongQtransreqDTO);

    @ApiOperation(value = "赎回成功的订单添加到法诉任务池")
    @PostMapping("/api/v1/caseInfo/add/lawsuitTaskPool")
    Result<Boolean> addLawsuitTaskPool();

    @ApiOperation(value = "贷后补件材料上传资方文件任务")
    @GetMapping("/api/v1/patches/uploadFundFileTask")
    Result<Boolean> orderPayAfterUploadFundFileTask();

    /**
     * 添加钉钉审批完成的任务进入委外任务池
     */
    @ApiOperation("添加钉钉审批完成的任务进入委外任务池")
    @GetMapping("/api/v1/caseInfo/add/updatePreservationStateAndTime")
    Result<Boolean> updatePreservationStateAndTime();

    /**
     * 违约金结清结果
     */
    @GetMapping("/scanCodePaymentResult")
    void scanCodePaymentResult();

    /**
     * 获取减免违约金定时
     */
    @PostMapping("/api/v1/fundDeduct/getPenaltyWaiver")
    Result<Boolean> getPenaltyWaiver();

    /**
     * 更新委外订单是否需求重新委派
     */
    @PostMapping("/api/v1/caseInfo/update/isNeedReDelegate")
    Result<Boolean> updateIsNeedReDelegate();

    /**
     * 钉钉提前委外申请审批
     */
    @GetMapping("/api/v1/caseInfo/outsourcingAdvanceApplicationDingTaskBatch")
    Result<Boolean> outsourcingAdvanceApplicationDingTaskBatch();

    /**
     * 钉钉减免申请审批
     */
    @GetMapping("/api/v1/caseInfo/reductionApplicationDingTaskBatch")
    Result<Boolean> reductionApplicationDingTaskBatch();

    /**
     * 钉钉减免申请审批
     */
    @GetMapping("/api/v1/caseInfo/assetReductionApplicationDingTaskBatch")
    Result<Boolean> assetReductionApplicationDingTaskBatch();
    /**
     * 钉钉副总减免申请审批
     */
    @GetMapping("/api/v1/caseInfo/assetDeputyReductionApplicationDingTaskBatch")
    Result<Boolean> assetDeputyReductionApplicationDingTaskBatch();


    /**
     * 钉钉合同申请审批
     */
    @GetMapping("/api/v1/caseInfo/contractApplicationDingTaskBatch")
    Result<Boolean> contractApplicationDingTaskBatch();

    @ApiOperation(value = "客服定时下线")
    @PostMapping("/api/v1/orderApproveDistribute/theCustomerServiceIsAutomaticallyOffline")
    public Result<Boolean> theCustomerServiceIsAutomaticallyOffline();

    @ApiOperation(value = "委外撤销钉钉实例")
    @PostMapping("/api/v1/caseInfo/cancel/dingTask")
    public Result<Boolean> cancelDingTask();

    @GetMapping("/kingdee/isOnlineOrder")
    public Result<Boolean> isOnlineOrder(@RequestParam("preId") Integer preId);

    /**
     * 获取预审批申请详情
     */
    @GetMapping("/kingdee/getPreApprovalDetails/{preId}")
    public Result<PreApprovalApplyInfoVO> getPreApprovalDetails(@PathVariable("preId") Integer preId);


    /**
     * 汇丰主体法大大合同签署文件同步
     *
     * @return
     */
    @ApiOperation(value = "汇丰主体法大大合同签署文件同步")
    @PostMapping("/api/v1/hf/fdd/fdd/contract/sync/file")
    Result<Boolean> downLoadFddContractFile();


    @ApiOperation(value = "数字化逾期180天订单流转众信")
    @PostMapping("/api/v1/caseInfo/digitalize/overdueList")
    Result<Boolean> digitalizeOverdueList();


    /**
     * 更新赎回还款计划
     */
    @ApiOperation(value = "更新赎回还款计划")
    @GetMapping("/api/v1/repurchaseRepayment/updateRepurchaseRepayment")
    Result<Boolean> updateRepurchaseRepayment();

    /**
     * 回款推送众信
     */
    @ApiOperation(value = "回款推送众信")
    @GetMapping("/api/v1/Product/ZhongXin/pushToZhongXinAsRetrieveAccept")
    Result<Boolean> pushToZhongXinAsRetrieveAccept(@RequestBody ReceivePaymentDTO dto);

    /**
     * 金蝶逾期210天以上数据推送到众信
     */
    @ApiOperation(value = "金蝶逾期210天以上数据推送到众信")
    @PostMapping("/api/v1/caseInfo/kingdeeorder/addCaseInfo")
    Result<Boolean> kingdeeorderAddCaseInfo();

    /**
     * 放款后合同下载
     */
    @PostMapping("/api/v1/contract/fund/LoanPassFundDownLoad")
    Result<Boolean> contractLoanPassFundDownLoad(@RequestBody ContractFundSignResulDTO resultDTO);

    /**
     * 长银预代偿生成申请记录
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/v1/payApplication/changyinPreIndemnityBatchApply")
    Result<Boolean> changyinPreIndemnityBatchApply(@RequestBody List<FundIndemnityDTO> dto);

    /**
     * 众信状态更新
     */

    @ApiOperation(value = "定时更新众信流转状态")
    @PostMapping("/api/v1/Product/ZhongXin/updateZhongXinStatus")
    Result<Boolean> updateZhongXinStatus();

    @ApiOperation(value = "富民赎回自动提交审核")
    @PostMapping("/api/v1/payApplication/fuMinRepurchaseCallback")
    Result<Boolean> fuMinRepurchaseCallback(@RequestBody FuMinRepurchaseCallbackDTO dto);

    @ApiOperation(value = "数字化终止订单")
    @GetMapping("/api/v1/approval/digitize/stopCredit/{preId}")
    Result<Boolean> stopCredit(@PathVariable("preId") Integer preId);

    /**
     * 获取智能合同第一阶段定时任务
     */
    @GetMapping("/api/v1/contract/getIntelligentContractFirstStageTask")
    Result<Boolean> getIntelligentContractFirstStageTask();

    /**
     * 钉钉以资抵债主动申请审批
     */
    @GetMapping("/api/v1/caseInfo/usingAssetsApplicationDingTaskBatch")
    Result<Boolean> usingAssetsApplicationDingTaskBatch();

    /**
     * 钉钉以资抵债被动申请审批
     */
    @GetMapping("/api/v1/caseInfo/usingAssetsPassiveApplicationDingTaskBatch")
    Result<Boolean> usingAssetsPassiveApplicationDingTaskBatch();

    /**
     * 结清计算
     */
    @PostMapping("/api/v1/settle/settleCalculation")
    Result<SettleCalculationVO> settleCalculation(@RequestBody SettleCalculationContext dto);

    @ApiOperation(value = "面签未处理通知")
    @PostMapping("/api/v1/review/notice")
    Result<Boolean> notice();

    @ApiOperation(value = "划扣申请")
    @PostMapping("/api/v1/bank/deduct/apply")
    Result<BankDeductVO> bankDeductApply(@RequestBody BankPayDeductDTO dto);

    @ApiOperation(value = "划扣查询")
    @PostMapping("/api/v1/bank/deduct/query")
    Result<BankDeductQueryVO> bankDeductQuery(@RequestBody BankPayDeductQueryDTO dto);

    @PostMapping("/api/v1/settle/settleCalculationByOrderId")
    Result<SettleCalculationVO> settleCalculationByOrderId(@RequestBody LHSettleCalculationDTO dto);

    /**
     * 定时更新Gps还款计划
     */
    @ApiOperation(value = "定时更新Gps还款计划")
    @PostMapping("/api/v1/gps/pay/updateGpsRepaymentPlan")
    Result<String> updateGpsRepaymentPlan();

    @ApiOperation("阿里云面签处理频道内没有用户的任务")
    @GetMapping("/api/v1/faceSignerLiveRecord/handelChannelNoUserListTask")
    Result<Boolean> handelChannelNoUserListTask(@RequestParam(name = "roomId", required = false) String roomId);

    @ApiOperation(value = "逾期70天以上的订单无处理中的法诉任务，无处理中的委外任务 添加到案件信息表")
    @PostMapping("/api/v1/caseInfo/automatic/addCaseInfo")
    Result<Boolean> automaticAddCaseInfo();

    @ApiOperation(value = "数字化逾期70天以上的订单无处理中的法诉任务，无处理中的委外任务 添加到案件信息表")
    @PostMapping("/api/v1/caseInfo/digitalize/automaticAddCaseInfo")
    Result<Boolean> digitalizeAutomaticAddCaseInfo();

    @ApiOperation(value = "接收贷后延迟委外数据")
    @PostMapping("/api/v1/caseInfo/receive/delay/outsourcing/data")
    Result<Boolean> receiveDelayOutsourcingData(@RequestBody DelayOutsourcingDTO delayOutsourcingDTO);

    @ApiOperation(value = "自动提交线上抵押待办")
    @PostMapping("/api/v1/mortgage/autoSubmitMortgage")
    Result<Boolean> autoSubmitMortgage();

    @ApiOperation(value = "创建富民-融资担保服务合同签署任务")
    @PostMapping("/api/v1/hf/fdd/create/signV2")
    Result<Boolean> createSignTaskV2(@RequestBody List<String> resourceIdList);

    @GetMapping("/api/v1/finalFundInfo/dingDingMessagePush")
    Result<Boolean> dingDingMessagePush();

    @GetMapping("/api/v1/manual/callback/sendFinish")
    void sendFinish(@RequestParam("states")States state,@RequestParam("orderId") Integer orderId, @RequestParam("userId")Integer userId, @RequestParam("event")Events event);

    @ApiOperation(value = "AI智能审核主动查询")
    @GetMapping("/api/v1/riskAiIntelligentAudit/autoQueryAndPushErrorIntelligentRisk")
    Result<Boolean> autoQueryAndPushErrorIntelligentRisk();

    @ApiOperation(value = "AI智能审核推送第二次")
    @GetMapping("/api/v1/riskAiIntelligentAudit/pushAlRequestTwo")
    Result<Boolean> pushAlRequestTwo();

    @ApiOperation(value = "回传签署完成的富民-融资担保服务合同")
    @PostMapping("/api/v1/hf/fdd/returnSignContract")
    Result<List<String>> returnSignContract();

	@ApiOperation(value = "根据收款方类型和当前节点确定下一个节点")
	@PostMapping("/api/v1/payApplication/getNextNode")
	Result<PayApplicationNodeEnums> getNextNode(@RequestBody GetNextNodeDTO dto);

	@ApiOperation(value = "保存审批节点")
	@PostMapping("/api/v1/payApplication/saveAuditNode")
	Result<Void> saveAuditNode(@RequestBody SaveAuditNodeDTO dto);

    @ApiOperation(value = "门店额度控制进行处理")
    @PostMapping("/api/v1/storeQuota/failureTimeStoreQuota")
    Result<Boolean> failureTimeStoreQuota();

    @ApiOperation(value = "失效规时间则进行放款")
    @PostMapping("/api/v1/loanReservoirRules/failureTimeRuleToLoan")
    Result<Boolean> failureTimeRuleToLoan();

    /**
     * 定时新增黑名单每日执行
     */
    @ApiOperation(value = "定时新增黑名单")
    @GetMapping("/api/v1/blacklist/timingBlacklist")
    Result<Boolean> timingBlacklist();
}
