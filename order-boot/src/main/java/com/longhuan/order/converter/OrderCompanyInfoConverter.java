package com.longhuan.order.converter;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.longhuan.order.pojo.dto.EnterpriseLicenseDTO;
import com.longhuan.order.pojo.entity.OrderCompanyInfoEntity;
import com.longhuan.order.pojo.vo.EnterpriseLicenseVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;

/**
 * 次序公司信息转换
 *
 * <AUTHOR>
 * @date 2024/10/23
 */
@Mapper(componentModel = "spring")
public interface OrderCompanyInfoConverter {
    /**
     * dto2实体
     *
     * @param dto DTO
     * @return {@link OrderCompanyInfoEntity }
     */
    @Mapping(target = "paidCapital", qualifiedByName = "amountConversion")
    @Mapping(target = "registeredCapital", qualifiedByName = "amountConversion")
    @Mapping(target = "validityStartDate", qualifiedByName = "dateConversion")
    @Mapping(target = "validityEndDate", qualifiedByName = "dateConversion")
    @Mapping(target = "approvalDate", qualifiedByName = "dateConversion")
    @Mapping(target = "establishmentDate", qualifiedByName = "dateConversion")
    OrderCompanyInfoEntity dto2entity(EnterpriseLicenseDTO dto);

    /**
     * 金额转换
     *
     * @param amount 金额
     * @return {@link BigDecimal }
     */
    @Named("amountConversion")
    default BigDecimal amountConversion(String amount) {
//        if (Objects.equals(amount, "无")) {
//            return BigDecimal.ZERO;
//        }
        //由于前端会拼接上单位 "万元"，所以需要判断是否包含 "无"，则返回 0
        if (amount.contains("无")) {
            return BigDecimal.ZERO;
        }
        // 去除单位 "万元"
        String numericPart = StrUtil.removeSuffix(amount, "万元");
        // 将字符串转换成 BigDecimal
        BigDecimal bigDecimal = Convert.toBigDecimal(numericPart);
        if (bigDecimal == null) {
            return BigDecimal.ZERO;
        }
        return bigDecimal.multiply(BigDecimal.TEN.pow(4));
    }

    /**
     * 日期转换
     *
     * @param date 日期
     * @return {@link BigDecimal }
     */
    @Named("dateConversion")
    default LocalDate dateConversion(String date) {
        if (Objects.equals(date, "长期") || Objects.equals(date, "无")) {
            // 创建 LocalDate 实例
            return LocalDate.of(2999, 12, 30);
        }
        LocalDateTime localDateTime = Convert.toLocalDateTime(date);
        if (ObjUtil.isNotNull(localDateTime)){
            return localDateTime.toLocalDate();
        }
        return null;
    }
    /**
     * 实体 2vo
     *
     * @param entity 实体
     * @return {@link EnterpriseLicenseVO }
     */
    @Mapping(target = "paidCapital", qualifiedByName = "amountConversionStr")
    @Mapping(target = "registeredCapital", qualifiedByName = "amountConversionStr")
    @Mapping(target = "validityStartDate", qualifiedByName = "dateConversionStr")
    @Mapping(target = "validityEndDate", qualifiedByName = "dateConversionStr")
    @Mapping(target = "approvalDate", qualifiedByName = "dateConversionStr")
    @Mapping(target = "establishmentDate", qualifiedByName = "dateConversionStr")
    EnterpriseLicenseVO entity2vo(OrderCompanyInfoEntity entity);

    @Named("amountConversionStr")
    default String amountConversionStr(BigDecimal amount) {
        if (Objects.isNull(amount) || new BigDecimal(0).compareTo(amount) == 0) {
            return "无";
        }
        // 将 BigDecimal 转换为万元单位
        BigDecimal valueInWan = amount.divide(BigDecimal.TEN.pow(4), 2, BigDecimal.ROUND_HALF_UP);
        // 将 BigDecimal 转换为字符串，并加上 "万元" 单位
        return valueInWan.toString() + "万元";
    }

    /**
     * 日期转换
     *
     * @param date 日期
     * @return {@link BigDecimal }
     */
    @Named("dateConversionStr")
    default String dateConversionStr(Date date) {
        if (Objects.isNull(date)) {
            return "无";
        }
        if (isDate29991230(date)) {
            return "长期";
        }
        return DateUtil.format(date, DatePattern.CHINESE_DATE_PATTERN);
    }

    static boolean isDate29991230(Date date) {
        // 将 Date 转换为 LocalDate
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 判断是否是 2999-12-30
        return localDate.equals(LocalDate.of(2999, 12, 30));
    }
}
