package com.longhuan.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.GlobalConstants;
import com.longhuan.common.core.enums.FundPaymentStatusEnum;
import com.longhuan.common.core.enums.OrderAmountEnum;
import com.longhuan.common.core.enums.PreApplyInfoFundStatus;
import com.longhuan.common.core.enums.RoleEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.order.converter.ProductFundMappingConverter;
import com.longhuan.order.enums.ApprovalApplyInfoBusinessStatus;
import com.longhuan.order.enums.PreApplyInfoManagerStatus;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.feign.ResourceFeign;
import com.longhuan.order.feign.RiskFeign;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.kingdee.Kingdee;
import com.longhuan.order.kingdee.feign.KingdeeFeign;
import com.longhuan.order.kingdee.pojo.ProductSaveDTO;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.OrderDetailsVo;
import com.longhuan.order.pojo.vo.ProductFundMappingVO;
import com.longhuan.order.service.*;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.order.util.OrderNumberGenerator;
import com.longhuan.order.util.SignatureUtils;
import com.longhuan.resource.pojo.vo.FileResourceInfoResultVO;
import com.longhuan.resource.pojo.vo.FileResourceResultVO;
import com.longhuan.risk.pojo.dto.GetUserMarriageDTO;
import com.longhuan.risk.pojo.dto.xueli.QueryVerifyV4DTO;
import com.longhuan.risk.pojo.vo.CreditReportVO;
import com.longhuan.user.pojo.vo.UserStoreVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 预审批申请信息表业务接口实现
 *
 * <AUTHOR>
 * @Date 2024/7/4
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PreApprovalApplyInfoServiceImpl extends ServiceImpl<PreApprovalApplyInfoMapper, PreApprovalApplyInfoEntity> implements PreApprovalApplyInfoService {
    private final PreApprovalApplyInfoMapper preApprovalApplyInfoMapper;
    private final EnvUtil envUtil;
    private final PreFundInfoMapper preFundInfoMapper;
    private final PreOcrVehicleInfoMapper preOcrVehicleInfoMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final PreOcrIdentityCardMapper preOcrIdentityCardMapper;
    private final OrderFileMapper orderFileMapper;
    private final OrderFileService orderFileService;
    private final AmountServiceImpl amountService;
    private final FundInfoMapper fundInfoMapper;
    private final ProductInfoMapper productInfoMapper;
    private final WeChatOrderMapper wechatOrderMapper;
    private final UserFeign userFeign;
    private final OrderVehicleInfoMapper orderVehicleInfoMapper;
    private final OrderCustomerInfoMapper orderCustomerInfoMapper;
    private final DictService dictService;
    private final DataAreaMapper dataAreaMapper;
    private final PreVinRecordMapper preVinRecordMapper;
    private final OrderContactPersonMapper orderContactPersonMapper;
    private final FundProductMappingMapper fundProductMappingMapper;
    private final ObjectMapper objectMapper;
    private final OrderCompanyInfoMapper orderCompanyInfoMapper;
    private final OrderRestoreService orderRestoreService;
    private final ProductDeptMappingMapper productDeptMappingMapper;
    private final ProductFundMappingConverter productFundMappingConverter;
    private final OrderFeeDetailMapper orderFeeDetailMapper;
    private final OtherSysPreInfoMapper otherSysPreInfoMapper;
    private final KingdeeFeign kingdeeFeign;
    private final CustomerMortgageInfoMapper customerMortgageInfoMapper;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final FundUndoMortgageInfoEntityMapper fundUndoMortgageInfoEntityMapper;
    private final XueliService xueliService;
    private final Car300DataMapper car300DataMapper;
    private final ApproveFeign approveFeign;
    private final ResourceFeign resourceFeign;
    private final PreApprovalFddAuthMapper preApprovalFddAuthMapper;
    private final OrderServicePrivateMethod orderServicePrivateMethod;
    private final RiskFeign riskFeign;
    @Value("${kingdee.datapush.signature.bodySecret}")
    private String bodySecret;
    @Value("${kingdee.datapush.signature.key}")
    private String key;

    /**
     * 预审信息提交接口
     *
     * @param applyInfoDTO 预审信息提交参数
     * @return 预审信息实体
     */
    @Override
    @Transactional
    @Kingdee
    public PreApprovalApplyInfoEntity submit(PreApprovalSubmitInfoDTO applyInfoDTO) {
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = Optional.ofNullable(preApprovalApplyInfoMapper.selectById(applyInfoDTO.getPreId())).orElseThrow(() -> new BusinessException("预审批申请信息不存在"));
        log.info("PreApprovalApplyInfoServiceImpl.submit preApprovalApplyInfoEntity:{}", JSONUtil.toJsonStr(preApprovalApplyInfoEntity));
//        if (preApprovalApplyInfoEntity.getBusinessStatus() == ApprovalApplyInfoBusinessStatus.SUBMITTED_PRELIMINARY_APPROVAL) {
//            return preApprovalApplyInfoEntity;
//        }
        Integer preId = preApprovalApplyInfoEntity.getId();
        log.info("PreApprovalApplyInfoServiceImpl.submit preId:{} preApprovalApplyInfoEntity:{}", preId, JSONUtil.toJsonStr(preApprovalApplyInfoEntity));
        preApprovalApplyInfoEntity.setLoanAmount(applyInfoDTO.getLoanAmount()).setLoanPeriod(applyInfoDTO.getLoanPeriod())
                .setCustomerType(applyInfoDTO.getCustomerType())
                .setCreditInquiryTime(LocalDateTime.now());
        preApprovalApplyInfoEntity.setBusinessStatus(ApprovalApplyInfoBusinessStatus.SUBMITTED_PRELIMINARY_APPROVAL);
        preApprovalApplyInfoEntity.setSubmitTime(LocalDateTime.now());
        log.info("PreApprovalApplyInfoServiceImpl.submit preId:{} preApprovalApplyInfoEntity:{}", preId, JSONUtil.toJsonStr(preApprovalApplyInfoEntity));
        preApprovalApplyInfoMapper.updateById(preApprovalApplyInfoEntity);
        log.info("PreApprovalApplyInfoServiceImpl.submit preId:{} preApprovalApplyInfoEntity:{}", preId, JSONUtil.toJsonStr(preApprovalApplyInfoEntity));
        verifyPreInfoUpdate(preId);
        return preApprovalApplyInfoEntity;
    }

    @Override
    @Kingdee
    public Boolean resetManager(ResetManagerDTO resetManagerDTO, LoginUser loginUser) {
        List<Integer> roleIds = loginUser.getRoleIds();
        if (RoleEnum.SYS_ADMIN.hasRole(roleIds) || RoleEnum.BIZ_SUPER_ADMIN.hasRole(roleIds)|| RoleEnum.STORE_MANAGER.hasRole(roleIds) || RoleEnum.ONLINE_MARKET_MANAGER.hasRole(roleIds)) {

            log.info("ApprovalServiceImpl.resetManager preId:{}", resetManagerDTO.getPreId());
            //如果该预审信息已生成订单，需要终止流程
            List<OrderInfoEntity> orderInfoEntities = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
                    .eq(OrderInfoEntity::getPreId, resetManagerDTO.getPreId()));
            //数字化资方通知数字化终止
            DigitalizeService digitalizeService = SpringContentUtils.getBean(DigitalizeService.class);
            digitalizeService.stopCredit(resetManagerDTO.getPreId());
            log.info("ApprovalServiceImpl.resetManager orderInfoEntities.size():{}", orderInfoEntities.size());
            if (CollUtil.isNotEmpty(orderInfoEntities)) {
                for (OrderInfoEntity orderInfo : orderInfoEntities) {
                    log.info("orderId :{}, orderInfo.getCurrentNode() :{}", orderInfo.getId(), orderInfo.getCurrentNode());
                    OrderSubmitDTO submitDTO = new OrderSubmitDTO();
                    submitDTO.setOrderId(orderInfo.getId());
                    submitDTO.setRemark("重置业务员，取消在途订单");
                    ApprovalService approvalService = SpringUtil.getBean(ApprovalService.class);
                    if (!Objects.equals(orderInfo.getCurrentNode(), States.SYSTEM_TERMINAL.getNode()) &&
                            !Objects.equals(orderInfo.getCurrentNode(), States.PROCESS_TERMINAL.getNode())) {
                        approvalService.orderCancel(submitDTO, loginUser);
                    }
                }
            }
            PreApprovalApplyInfoEntity applyInfoEntity = preApprovalApplyInfoMapper.selectById(resetManagerDTO.getPreId());

            PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                    .eq(PreFundInfoEntity::getPreId, resetManagerDTO.getPreId())
                    .eq(PreFundInfoEntity::getDeleteFlag, 0)
                    .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                    .eq(PreFundInfoEntity::getFundResult, 2)
                    .orderByDesc(PreFundInfoEntity::getCreateTime)
                    .last("limit 1")
            );
            if (ObjUtil.isNotNull(preFundInfoEntity)){
                if (envUtil.isPrd()){
                    approveFeign.creditPreEnd(resetManagerDTO.getPreId());
                }
            }


            //姓名、身份证号相同的信息
            List<PreApprovalApplyInfoEntity> preApprovalApplyInfoEntities = preApprovalApplyInfoMapper.selectList(new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
                    .eq(PreApprovalApplyInfoEntity::getIdNumber, applyInfoEntity.getIdNumber())
                    .eq(PreApprovalApplyInfoEntity::getName, applyInfoEntity.getName())
                    .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0));
            for (PreApprovalApplyInfoEntity approvalApplyInfo : preApprovalApplyInfoEntities) {
                PreApprovalApplyInfoEntity entity = new PreApprovalApplyInfoEntity();
                //重置标识
                entity.setIsReset(1);
                //记录修改人信息
                entity.setUpdateBy(loginUser.getUserId());
                //将信息置为无效
                entity.setDeleteFlag(1);
                int count = preApprovalApplyInfoMapper.update(entity, new LambdaUpdateWrapper<PreApprovalApplyInfoEntity>()
                        .eq(PreApprovalApplyInfoEntity::getId, approvalApplyInfo.getId()));
                log.info("ApprovalServiceImpl.resetManager count:{}", count);

            }
        } else {
            throw new BusinessException("用户无数据权限");
        }

        return true;
    }

    @Override
    public List<ProductFundMappingVO> storeProductList(StoreProductDTO infoDTO, LoginUser loginUser) {
        Integer fundId = infoDTO.getFundId();
        Assert.notNull(fundId, () -> {
            throw new BusinessException("未选择资方");
        });
        Integer preId = infoDTO.getPreId();
        Integer orderId = infoDTO.getOrderId();
        Integer deptId;
        if (preId != null) {
            deptId = getApplyInfoStoreId(preId);
        } else {
            if (orderId != null) {
                deptId = getOrderInfoStoreId(orderId);
            } else {
                throw new BusinessException("缺少请求参数");
            }
        }


        log.info("ApprovalServiceImpl.selectProductList fundId:{} deptId:{}", fundId, deptId);

        Assert.notNull(deptId, () -> {
            throw new BusinessException("未配置客户经理所属门店");
        });
        Integer businessType = getBusinessType(preId, orderId, fundId);
        log.info("ApprovalServiceImpl.selectProductList businessType:{}", businessType);
        List<Integer> storeProductIds = productDeptMappingMapper.selectList(new LambdaQueryWrapper<ProductDeptMappingEntity>()
                .eq(ProductDeptMappingEntity::getDeptId, deptId)
                .eq(ProductDeptMappingEntity::getDeleteFlag, 0)
                .eq(ProductDeptMappingEntity::getEnable, 0)

        ).stream().map(ProductDeptMappingEntity::getProductId).toList();

        if (CollUtil.isEmpty(storeProductIds)) {
            return List.of();
        }

//        LambdaQueryWrapper<FundProductMappingEntity> lqw = new LambdaQueryWrapper<>();
//        lqw.eq(FundProductMappingEntity::getFundId, fundId);
//        lqw.in(FundProductMappingEntity::getProductId, storeProductIds);
//        lqw.eq(FundProductMappingEntity::getStatus, 0);
//        lqw.eq(Objects.nonNull(businessType), FundProductMappingEntity::getBusinessType, businessType);
//        lqw.eq(FundProductMappingEntity::getDeleteFlag, 0);
//
//        List<FundProductMappingEntity> productInfoEntities = fundProductMappingMapper.selectList(lqw);
//
//        return productFundMappingConverter.entityListToVoList(productInfoEntities);
        //        MPJLambdaQueryWrapper<FundProductMappingEntity> lqw = new MPJLambdaQueryWrapper<FundProductMappingEntity>();
//        lqw.eq(FundProductMappingEntity::getFundId, fundId);
//        lqw.in(FundProductMappingEntity::getProductId, storeProductIds);
//        lqw.eq(FundProductMappingEntity::getStatus, 0);
//        lqw.eq(Objects.nonNull(businessType), FundProductMappingEntity::getBusinessType, businessType);
//        lqw.eq(FundProductMappingEntity::getDeleteFlag, 0);
        boolean flag = false;
        int customerLevel = 0;
        if (ObjUtil.isNotEmpty(infoDTO.getPreId())){
            PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(preId);
            if (ObjUtil.isEmpty(preApprovalApplyInfoEntity)){
                throw new BusinessException("未找到预审批信息");
            }
            flag=Objects.equals(preApprovalApplyInfoEntity.getSourceType(),1) && (Objects.equals(preApprovalApplyInfoEntity.getRegionId(),24) || Objects.equals(preApprovalApplyInfoEntity.getRegionId(),56));
            if (ObjUtil.isNotEmpty(preApprovalApplyInfoEntity.getCustomerLevel())){
                customerLevel = preApprovalApplyInfoEntity.getCustomerLevel();
            }
        }
        if (ObjUtil.isNotEmpty(infoDTO.getOrderId())){
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
            if (ObjUtil.isEmpty(orderInfoEntity)){
                throw new BusinessException("未找到订单信息");
            }
            flag = Objects.equals(orderInfoEntity.getSourceType(),1) && (Objects.equals(orderInfoEntity.getRegionId(),24) || Objects.equals(orderInfoEntity.getRegionId(),56));
            OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectById(orderInfoEntity.getCustomerId());
            if (ObjUtil.isNotEmpty(orderCustomerInfoEntity)){
                if (ObjUtil.isNotEmpty(orderCustomerInfoEntity.getCustomerLevel())){
                    customerLevel = orderCustomerInfoEntity.getCustomerLevel();
                }else {
                    PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(orderInfoEntity.getPreId());
                    if (ObjUtil.isEmpty(preApprovalApplyInfoEntity)){
                        throw new BusinessException("未找到预审批信息");
                    }
                    flag=Objects.equals(preApprovalApplyInfoEntity.getSourceType(),1) && (Objects.equals(preApprovalApplyInfoEntity.getRegionId(),24) || Objects.equals(preApprovalApplyInfoEntity.getRegionId(),56));
                    if (ObjUtil.isNotEmpty(preApprovalApplyInfoEntity.getCustomerLevel())){
                        customerLevel = preApprovalApplyInfoEntity.getCustomerLevel();
                    }
                }

            }
        }

        if (flag){
            MPJLambdaWrapper<FundProductMappingEntity> wrapper = new MPJLambdaWrapper<FundProductMappingEntity>()
                    .selectAll(FundProductMappingEntity.class)
                    .leftJoin(ProductInfoEntity.class, ProductInfoEntity::getId, FundProductMappingEntity::getProductId)
                    .eq(FundProductMappingEntity::getFundId, fundId)
                    .in(FundProductMappingEntity::getProductId, storeProductIds)
                    .eq(FundProductMappingEntity::getStatus, 0)
//                    .eq(Objects.nonNull(businessType), FundProductMappingEntity::getBusinessType, businessType)
                    .eq(FundProductMappingEntity::getDeleteFlag, 0);
            if (Objects.equals(fundId,FundEnum.LAN_HAI.getValue())){
                BigDecimal executeRate = BigDecimal.ZERO;
                if (ObjUtil.isNotNull(orderId)){
                    FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(
                            new LambdaQueryWrapper<FinalFundInfoEntity>()
                                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                                    .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                                    .orderByDesc(FinalFundInfoEntity::getCreateTime)
                                    .last("limit 1")
                    );
                    if (ObjUtil.isNotNull(finalFundInfoEntity) && ObjUtil.isNotNull(finalFundInfoEntity.getExecuteRate())){
                        executeRate = finalFundInfoEntity.getExecuteRate();
                    }else {
                        PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(
                                new LambdaQueryWrapper<PreFundInfoEntity>()
                                        .eq(PreFundInfoEntity::getPreId, preId)
                                        .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                                        .eq(PreFundInfoEntity::getDeleteFlag, 0)
                                        .orderByDesc(PreFundInfoEntity::getId)
                                        .last("limit 1")
                        );
                        if (ObjUtil.isNotNull(preFundInfoEntity) && ObjUtil.isNotNull(preFundInfoEntity.getExecuteRate())){
                            executeRate = preFundInfoEntity.getExecuteRate();
                        }
                    }
                }else {
                    PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(
                            new LambdaQueryWrapper<PreFundInfoEntity>()
                                    .eq(PreFundInfoEntity::getPreId, preId)
                                    .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                                    .eq(PreFundInfoEntity::getDeleteFlag, 0)
                                    .orderByDesc(PreFundInfoEntity::getId)
                                    .last("limit 1")
                    );
                    if (ObjUtil.isNotNull(preFundInfoEntity) && ObjUtil.isNotNull(preFundInfoEntity.getExecuteRate())){
                        executeRate = preFundInfoEntity.getExecuteRate();
                    }
                }
                if (!Objects.equals(executeRate.compareTo(BigDecimal.ZERO),0)){
                    wrapper.ge(ProductInfoEntity::getIrr, executeRate);
                }
            }
            List<FundProductMappingEntity> productInfoEntities = fundProductMappingMapper.selectJoinList(FundProductMappingEntity.class,wrapper);
            return productFundMappingConverter.entityListToVoList(productInfoEntities);
        }else {
            MPJLambdaWrapper<FundProductMappingEntity> wrapper = new MPJLambdaWrapper<FundProductMappingEntity>()
                    .selectAll(FundProductMappingEntity.class)
                    .leftJoin(ProductRatingMappingEntity.class, ProductRatingMappingEntity::getProductId, FundProductMappingEntity::getProductId)
                    .leftJoin(ProductInfoEntity.class, ProductInfoEntity::getId, ProductRatingMappingEntity::getProductId)
                    .eq(FundProductMappingEntity::getFundId, fundId)
                    .in(FundProductMappingEntity::getProductId, storeProductIds)
                    .eq(FundProductMappingEntity::getStatus, 0)
//                    .eq(Objects.nonNull(businessType), FundProductMappingEntity::getBusinessType, businessType)
                    .eq(FundProductMappingEntity::getDeleteFlag, 0)
                    .eq(ProductInfoEntity::getDeleteFlag, 0)
                    .eq(ProductRatingMappingEntity::getDeleteFlag, 0);
            if (!Objects.equals(customerLevel,1)){
                wrapper.notLike(ProductRatingMappingEntity::getCustomerLevel, 1);
            }
            if (Objects.equals(fundId,FundEnum.LAN_HAI.getValue())){
                BigDecimal executeRate = BigDecimal.ZERO;
                if (ObjUtil.isNotNull(orderId)){
                    FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(
                            new LambdaQueryWrapper<FinalFundInfoEntity>()
                                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                                    .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                                    .orderByDesc(FinalFundInfoEntity::getCreateTime)
                                    .last("limit 1")
                    );
                    if (ObjUtil.isNotNull(finalFundInfoEntity) && ObjUtil.isNotNull(finalFundInfoEntity.getExecuteRate())){
                        executeRate = finalFundInfoEntity.getExecuteRate();
                    }else {
                        PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(
                                new LambdaQueryWrapper<PreFundInfoEntity>()
                                        .eq(PreFundInfoEntity::getPreId, preId)
                                        .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                                        .eq(PreFundInfoEntity::getDeleteFlag, 0)
                                        .orderByDesc(PreFundInfoEntity::getId)
                                        .last("limit 1")
                        );
                        if (ObjUtil.isNotNull(preFundInfoEntity) && ObjUtil.isNotNull(preFundInfoEntity.getExecuteRate())){
                            executeRate = preFundInfoEntity.getExecuteRate();
                        }
                    }
                }else {
                    PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(
                            new LambdaQueryWrapper<PreFundInfoEntity>()
                                    .eq(PreFundInfoEntity::getPreId, preId)
                                    .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                                    .eq(PreFundInfoEntity::getDeleteFlag, 0)
                                    .orderByDesc(PreFundInfoEntity::getId)
                                    .last("limit 1")
                    );
                    if (ObjUtil.isNotNull(preFundInfoEntity) && ObjUtil.isNotNull(preFundInfoEntity.getExecuteRate())){
                        executeRate = preFundInfoEntity.getExecuteRate();
                    }
                }
                if (!Objects.equals(executeRate.compareTo(BigDecimal.ZERO),0)){
                    wrapper.ge(ProductInfoEntity::getIrr, executeRate);
                }
            }
            List<FundProductMappingEntity> productInfoEntities = fundProductMappingMapper.selectJoinList(FundProductMappingEntity.class,wrapper);
            return productFundMappingConverter.entityListToVoList(productInfoEntities);
        }
    }

    /**
     * 获取支持更新预审信息标识
     *
     */
    @Override
    public Boolean getSupportUpdateFlag(Integer preId) {
        if (preId == null) {
            return false;
        }

        // 判断是否存在存量订单
        Long orderProcessingCount = orderInfoMapper.selectCount(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getPreId, preId)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .ge(OrderInfoEntity::getState, States.BUSINESS_ADDED_INFO.getNode())
        );

        return orderProcessingCount <= 0;
    }

    /**
     * 获取人脸照片
     */
    @Override
    public String getFaceByPreId(Integer preId) {
        //获取预审信息
        PreApprovalApplyInfoEntity applyInfo = preApprovalApplyInfoMapper.selectById(preId);
        if (ObjUtil.isNull(applyInfo)) {
            throw new BusinessException("未找到预审批信息");
        }
        if (StrUtil.isNotBlank(applyInfo.getFaceId())) {
            return applyInfo.getFaceId();
        }
        //通过fdd_auth获取
        Optional<PreApprovalFddAuthEntity> authPicture = Optional.ofNullable(preApprovalFddAuthMapper.selectOne(
                new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                        .eq(PreApprovalFddAuthEntity::getPhone, applyInfo.getPhone())
                        .eq(PreApprovalFddAuthEntity::getIdNumber, applyInfo.getIdNumber())
                        .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                        .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                        .orderByDesc(PreApprovalFddAuthEntity::getCreateTime)
                        .last("limit 1")));
        return authPicture.map(PreApprovalFddAuthEntity::getFacePictureId).orElse(null);
    }

    /**
     * 更新人脸照片
     *
     */
    @Override
    public String updateFaceByPreId(Integer preId, String facePicture) {
        PreApprovalApplyInfoEntity applyInfo = preApprovalApplyInfoMapper.selectById(preId);
        if (ObjUtil.isNull(applyInfo)) {
            throw new BusinessException("未找到预审批信息");
        }
        LambdaUpdateWrapper<PreApprovalApplyInfoEntity> luw = new LambdaUpdateWrapper<>();
        luw.set(PreApprovalApplyInfoEntity::getFaceId, facePicture)
               .eq(PreApprovalApplyInfoEntity::getId, preId);
        preApprovalApplyInfoMapper.update(luw);
        log.info("PreApprovalApplyInfoServiceImpl.updateFaceByPreId start preId:{} facePicture:{}", preId, facePicture);
        this.clearRiskInfo(preId);
        //更细法大大人脸底图(预审资方查的这个)
        Optional<PreApprovalFddAuthEntity> authPicture = Optional.ofNullable(preApprovalFddAuthMapper.selectOne(
                new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                        .eq(PreApprovalFddAuthEntity::getPhone, applyInfo.getPhone())
                        .eq(PreApprovalFddAuthEntity::getIdNumber, applyInfo.getIdNumber())
                        .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                        .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                        .orderByDesc(PreApprovalFddAuthEntity::getCreateTime)
                        .last("limit 1")));

        authPicture.ifPresent(auth -> {
            log.info("PreApprovalApplyInfoServiceImpl.updateFaceByPreId fdd_auth start  preId:{} facePicture:{}", preId, facePicture);
            PreApprovalFddAuthEntity authPictureUpdate = new PreApprovalFddAuthEntity();
            authPictureUpdate.setFacePictureId(facePicture);
            preApprovalFddAuthMapper.update(authPictureUpdate, new LambdaUpdateWrapper<PreApprovalFddAuthEntity>()
                    .eq(PreApprovalFddAuthEntity::getId, auth.getId()));
            log.info("PreApprovalApplyInfoServiceImpl.updateFaceByPreId fdd_auth end preId:{} facePicture:{}", preId, facePicture);
        });
        log.info("PreApprovalApplyInfoServiceImpl.updateFaceByPreId end preId:{} facePicture:{}", preId, facePicture);


        return facePicture;
    }

    @Override
    public Map<String,Object> fillRiskCreditFeature(Integer preId,LoginUser loginUser) {
        OrderDetailsVo orderDetailsVo = new OrderDetailsVo();
        Map<String, Object> map = new HashMap<>();
        if (ObjUtil.isNotNull(loginUser) && ObjUtil.isNotNull(loginUser.getRoleIds())){
            List<Integer> roleIds = loginUser.getRoleIds();
            boolean flag = RoleEnum.RISK_ROLE.hasRole(roleIds) || RoleEnum.SYS_ADMIN.hasRole(roleIds) || RoleEnum.BIZ_SUPER_ADMIN.hasRole(roleIds)
                    || RoleEnum.RISK_AMOUNT_APPROVE.hasRole(roleIds)
                    || RoleEnum.RISK_AMOUNT_APPROVE_FINAL.hasRole(roleIds)
                    || RoleEnum.AMOUNT_AUDIT_SPECIALIST.hasRole(roleIds)
                    || RoleEnum.AMOUNT_AUDIT_GROUP_LEADER.hasRole(roleIds)
                    || RoleEnum.AMOUNT_FIRST_MANAGER.hasRole(roleIds)
                    || RoleEnum.RISK_MANAGER.hasRole(roleIds)
                    || RoleEnum.RISK_DEPUTY_MANAGER.hasRole(roleIds)
                    || RoleEnum.RISK_AMOUNT_APPROVE_ONLINE.hasRole(roleIds);
            if (flag){
                PreApprovalApplyInfoEntity applyInfo = preApprovalApplyInfoMapper.selectById(preId);
                if (ObjUtil.isNotNull(applyInfo) && ObjUtil.isNotEmpty(applyInfo.getIdNumber())
                        && ObjUtil.isNotNull(applyInfo.getRiskStatus()) && applyInfo.getRiskStatus() > 2 ){
                    orderDetailsVo.setIdNumber(applyInfo.getIdNumber());
                    orderServicePrivateMethod.fillRiskCreditFeature(orderDetailsVo);
                    orderDetailsVo.setIdNumber(null);
                    map = BeanUtil.beanToMap(orderDetailsVo);
                    Result<CreditReportVO> result = riskFeign.creditReportSearch(applyInfo.getIdNumber());
                    if (Result.isSuccess(result)){
                        map.put("creditReport",result.getData());
                    }
                }
            }
        }
        return map;
    }

    /**
     * 获取 Business Type
     *
     * @param preId   前 ID
     * @param orderId 订单 ID
     * @param fundId  基金 ID
     * @return {@link Integer }
     */
    private Integer getBusinessType(Integer preId, Integer orderId, Integer fundId) {
        if (preId != null) {
            if (fundId == FundEnum.FU_MIN.getValue()) {
                PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                        .select(PreFundInfoEntity::getApplyType, PreFundInfoEntity::getPreId)
                        .eq(PreFundInfoEntity::getPreId, preId)
                        .eq(PreFundInfoEntity::getFundId, fundId)
                        .eq(PreFundInfoEntity::getDeleteFlag, 0)
                );

                if (preFundInfoEntity != null) {

                    Integer applyType = preFundInfoEntity.getApplyType();

                    if (Objects.equals(applyType, 1)) {
                        return 0;
                    } else {
                        return 1;
                    }
                } else {
                    return 1;
                }
            }
        } else if (orderId != null) {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                    .select(OrderInfoEntity::getOrderType)
                    .eq(OrderInfoEntity::getId, orderId));
            if (orderInfoEntity != null) {
                return orderInfoEntity.getOrderType();
            } else {
                return null;
            }
        }
        return null;
    }

    private Integer getOrderInfoStoreId(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getDeptId)
                .eq(OrderInfoEntity::getId, orderId));
        if (orderInfoEntity == null) {
            return null;
        }
        return orderInfoEntity.getDeptId();
    }

    private Integer getApplyInfoStoreId(Integer preId) {
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectOne(new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
                .select(PreApprovalApplyInfoEntity::getStoreId)
                .eq(PreApprovalApplyInfoEntity::getId, preId));
        if (preApprovalApplyInfoEntity == null) {
            return null;
        }
        return preApprovalApplyInfoEntity.getStoreId();
    }

    /**
     * 验证 pre info update
     *
     * @param preId 前id
     */
    public void verifyPreInfoUpdate(Integer preId) {
        this.verifyOrderProcessing(preId);
        DigitalizeService digitalizeService = SpringContentUtils.getBean(DigitalizeService.class);
        digitalizeService.stopCredit(preId);
        //清除风控信息
        this.clearRiskInfo(preId);
    }


    /**
     * 验证订单处理
     *
     * @param preId 前id
     */
    public void verifyOrderProcessing(Integer preId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getPreId, preId)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .ne(OrderInfoEntity::getCurrentNode, States.PROCESS_TERMINAL.getNode())
                .orderByDesc(OrderInfoEntity::getCreateTime)
                .last("limit 1")
        );

        if (ObjUtil.isNotNull(orderInfoEntity) && !States.PROCESS_TERMINAL.getNode().equals(orderInfoEntity.getCurrentNode())) {
            throw new BusinessException("该客户有流程中订单，请勿重复提交！\n 如需发起请终止流程中订单");
        }
    }

    /**
     * 清除风险信息
     *
     * @param preId 前id
     */
    public void clearRiskInfo(Integer preId) {
        //将风控状态置为未发起
        PreApprovalApplyInfoEntity preApprovalApplyInfo = preApprovalApplyInfoMapper.selectById(preId);
        Assert.notNull(preApprovalApplyInfo, "预审批申请信息不存在");
        log.info("RiskServiceImpl riskLaunchOnPreApprovalAgain preApprovalApplyInfoEntity:{}", preApprovalApplyInfo);
        preApprovalApplyInfoMapper.update(new LambdaUpdateWrapper<PreApprovalApplyInfoEntity>()
                .set(PreApprovalApplyInfoEntity::getFundId, null)
                .set(PreApprovalApplyInfoEntity::getRiskStatus, 0)
                .set(PreApprovalApplyInfoEntity::getFundStatus, PreApplyInfoFundStatus.SUBMITTED_NOT)
                .set(PreApprovalApplyInfoEntity::getManagerState, PreApplyInfoManagerStatus.APPROVAL_NO)
                .eq(PreApprovalApplyInfoEntity::getId, preId)
        );
        //修改预审状态
        preFundInfoMapper.update(new LambdaUpdateWrapper<PreFundInfoEntity>()
                .set(PreFundInfoEntity::getDeleteFlag, 1)
                .eq(PreFundInfoEntity::getPreId, preId)
                .eq(PreFundInfoEntity::getDeleteFlag, 0)
        );
        //清楚数字化资方产品信息
        otherSysPreInfoMapper.update(new LambdaUpdateWrapper<OtherSysPreInfoEntity>()
                .set(OtherSysPreInfoEntity::getDeleteFlag, 1)
                .set(OtherSysPreInfoEntity::getProductId, null)
                .set(OtherSysPreInfoEntity::getProductName, null)
                .eq(OtherSysPreInfoEntity::getPreId, preId));
    }

    /**
     * 预购
     *
     * @param preId 前id
     * @param dto   DTO
     */
    @Override
    public void preToOrder(Integer preId, PreManagerApprovalSubmitDTO dto) {
        //获取当前预审批信息
        PreApprovalApplyInfoEntity approvalApplyInfo = preApprovalApplyInfoMapper.selectById(preId);
        Assert.notNull(approvalApplyInfo, "预审批信息不存在");
        //预审批车辆信息
        PreOcrVehicleInfoEntity preOcrVehicleInfoEntity = preOcrVehicleInfoMapper.selectOne(new LambdaQueryWrapper<PreOcrVehicleInfoEntity>()
                .eq(PreOcrVehicleInfoEntity::getPreId, preId)
                .eq(PreOcrVehicleInfoEntity::getDeleteFlag, 0)
        );
        Assert.notNull(preOcrVehicleInfoEntity, "预审批车辆信息不存在");
        // 预审客户信息
        PreOcrIdentityCardEntity preOcrIdentityCardEntity = preOcrIdentityCardMapper.selectOne(new LambdaQueryWrapper<PreOcrIdentityCardEntity>()
                .eq(PreOcrIdentityCardEntity::getPreId, preId)
                .eq(PreOcrIdentityCardEntity::getDeleteFlag, 0)
        );
        Assert.notNull(preOcrIdentityCardEntity, "预审批身份信息不存在");
        // 查询对应产品信息
        ProductInfoEntity productInfoEntity = productInfoMapper.selectOne(new LambdaQueryWrapper<ProductInfoEntity>()
                .eq(ProductInfoEntity::getId, dto.getProductId())
                .eq(ProductInfoEntity::getDeleteFlag, 0)
        );
        Assert.notNull(productInfoEntity, "当前选择产品不存在");

        List<PreFundInfoEntity> preFundInfoEntityList = preFundInfoMapper.selectList(new LambdaQueryWrapper<PreFundInfoEntity>()
                .eq(PreFundInfoEntity::getPreId, preId)
                .eq(PreFundInfoEntity::getFundId, dto.getFundId())
                .eq(PreFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(PreFundInfoEntity::getCreateTime)
        );

        if (CollUtil.isEmpty(preFundInfoEntityList)) {
            throw new BusinessException("预审资方匹配不存在");
        }
        PreFundInfoEntity preFundInfoEntity = preFundInfoEntityList.get(0);
        //  TODO 风控大于350000走风控终审
        if (envUtil.isDev() || envUtil.isSit()) {
//            preFundInfoEntity.setFundCreditAmount(BigDecimal.valueOf(360000));
        }
        //根据 preId 查询历史上是否存在相同 preId 的订单数据如果存在则获取最新的一条数据
        Integer oldOrderId = dto.getOldOrderId();
        OrderInfoEntity oldOrderInfoEntity = null;
        if (Objects.nonNull(oldOrderId)) {
            oldOrderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                    .eq(OrderInfoEntity::getId, oldOrderId)
            );
        }

        Integer oldCustomerId = null;
        if (Objects.nonNull(oldOrderInfoEntity)) {
            oldOrderId = oldOrderInfoEntity.getId();
            oldCustomerId = oldOrderInfoEntity.getCustomerId();
        }
        log.info("PreApprovalApplyInfoServiceImpl.preToOrder preId:{} oldOrderInfoEntity:{}", preId, JSONUtil.toJsonStr(oldOrderInfoEntity));
        approvalApplyInfo.setFundId(dto.getFundId()).setProductId(dto.getProductId());
        preApprovalApplyInfoMapper.updateById(approvalApplyInfo);
        // 订单信息入库
        OrderInfoEntity entity = orderToOrderInfo(preId, oldOrderInfoEntity, dto, approvalApplyInfo, productInfoEntity, preFundInfoEntity, preOcrVehicleInfoEntity);
        //订单Id
        Integer orderId = entity.getId();
        if (Objects.nonNull(oldOrderInfoEntity)) {
            //同步历史订单文件数据
            syncOrderFile(orderId, oldOrderId);
            //同步客户联系人信息
            syncOrderContact(orderId, oldOrderId);
            //同步营业执照信息
            syncEnterpriseLicense(orderId, oldOrderId);
            //同步长银放款失败抵押信息
            syncChangYinPaymentFailMortgageInfo(orderId, oldOrderId);
            //同步预授信基础文件
            syncPreBaseFileByPreId(preId, orderId);
        } else {
            //同步进件时上传过的文件
            syncPreFileToOrder(orderId, preId);
        }
        // 客户信息
        Integer customerId = preToOrderCustomer(orderId, oldCustomerId, preOcrIdentityCardEntity);
        // 客户评分
        preToOrderCustomerScore(customerId,approvalApplyInfo.getCustomerLevel());
        // 车辆信息
        preToOrderVehicle(orderId, oldOrderId, preOcrVehicleInfoEntity);
        //计算订单额度
        calOrderAmount(orderId, preFundInfoEntity, dto, approvalApplyInfo);
        //保存微信订单关联信息
        saveWeChatOrderInfo(preId, orderId);
        //gps信息
        preToOrderGps(orderId);
        //保存订单交易明细信息
        saveOrderFeeDetail(preId, orderId);
    }

    private void preToOrderCustomerScore(Integer customerId, Integer customerLevel) {
        orderCustomerInfoMapper.update(new LambdaUpdateWrapper<OrderCustomerInfoEntity>()
                .set(OrderCustomerInfoEntity::getPreCustomerLevel, customerLevel)
                .eq(OrderCustomerInfoEntity::getId, customerId));
    }

    private void saveOrderFeeDetail(Integer preId, Integer orderId) {
        //复制交易明细
        List<OrderInfoEntity> oldOrderInfoEntities = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getPreId, preId)
                .eq(OrderInfoEntity::getDeleteFlag, 0));
        List<Integer> orderIds = oldOrderInfoEntities.stream().map(OrderInfoEntity::getId).toList();
        if (CollUtil.isEmpty(orderIds)) {
            return;
        }
        List<OrderFeeDetailEntity> orderFeeDetailEntities = orderFeeDetailMapper.selectList(new LambdaQueryWrapper<OrderFeeDetailEntity>()
                .in(OrderFeeDetailEntity::getOrderId, orderIds)
                .eq(OrderFeeDetailEntity::getDeleteFlag, 0));
        orderFeeDetailEntities.forEach(orderFeeDetailEntity -> {
            OrderFeeDetailEntity orderFeeDetailNew = BeanUtil.copyProperties(orderFeeDetailEntity, OrderFeeDetailEntity.class);
            orderFeeDetailNew.setId(null);
            orderFeeDetailNew.setOrderId(orderId);
            orderFeeDetailMapper.insert(orderFeeDetailNew);
            orderFeeDetailEntity.setDeleteFlag(1);
            orderFeeDetailMapper.updateById(orderFeeDetailEntity);
        });
    }

    /**
     * 同步长银放款失败抵押信息
     * @param orderId    订单ID
     * @param oldOrderId 老次序id
     */
    public void syncChangYinPaymentFailMortgageInfo(Integer orderId, Integer oldOrderId) {
        if (ObjUtil.isNull(oldOrderId)) {
            log.info("syncChangYinPaymentFailMortgageInfo oldOrderId is null orderId:{}", orderId);
            return;
        }
        //判断订单是否为长银
        Long count = orderInfoMapper.selectCount(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(OrderInfoEntity::getId, orderId)
        );
        if (count < 1) {
            log.info("syncChangYinPaymentFailMortgageInfo orderId:{} is not changYin", orderId);
            return;
        }
        Long oldCount = orderInfoMapper.selectCount(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(OrderInfoEntity::getId, oldOrderId)
        );
        if (oldCount < 1) {
            log.info("syncChangYinPaymentFailMortgageInfo oldOrderId:{} is not changYin", oldOrderId);
            return;
        }

        OrderVehicleInfoEntity oldOrderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                .eq(OrderVehicleInfoEntity::getOrderId, oldOrderId)
                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                .orderByDesc(OrderVehicleInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(oldOrderVehicleInfoEntity)) {
            log.info("syncChangYinPaymentFailMortgageInfo oldOrderVehicleInfoEntity is null orderId:{}", oldOrderId);
            return;
        }
        //获取最新vin订单
        OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                .eq(OrderVehicleInfoEntity::getVin, oldOrderVehicleInfoEntity.getVin())
                .ne(OrderVehicleInfoEntity::getOrderId, orderId)
                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                .orderByDesc(OrderVehicleInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderVehicleInfoEntity)) {
            log.info("syncChangYinPaymentFailMortgageInfo orderVehicleInfoEntity is null orderId:{}", oldOrderId);
            return;
        }
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderVehicleInfoEntity.getOrderId());
        if (ObjUtil.isNull(orderInfo)) {
            log.info("syncChangYinPaymentFailMortgageInfo orderInfo is null orderId:{}", orderVehicleInfoEntity.getOrderId());
            return;
        }
        //判断是否为长银
        if (FundEnum.CHANG_YIN.getValue() != orderInfo.getFundId()) {
            log.info("syncChangYinPaymentFailMortgageInfo orderInfo is not changYin orderId:{}", orderId);
            return;
        }
        //判断是否为放款失败订单
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
                .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(finalFundInfo)) {
            log.info("syncChangYinPaymentFailMortgageInfo finalFundInfo is null orderId:{}", orderId);
            return;
        }
        //判断是否放款拒绝
        if (FundPaymentStatusEnum.FAIL != finalFundInfo.getPaymentStatus()) {
            log.info("syncChangYinPaymentFailMortgageInfo finalFundInfo is not FAIL orderId:{}", orderId);
            return;
        }

        //获取客户抵押信息
        CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                .eq(CustomerMortgageInfoEntity::getOrderId, oldOrderVehicleInfoEntity.getOrderId())
                .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(customerMortgageInfoEntity)) {
            return;
        }
        customerMortgageInfoEntity.setId(null);
        customerMortgageInfoEntity.setOrderId(orderId);
        customerMortgageInfoEntity.setCreateTime(null);
        customerMortgageInfoEntity.setUpdateTime(null);
        customerMortgageInfoMapper.insert(customerMortgageInfoEntity);
        log.info("PreApprovalApplyInfoServiceImpl.syncMortgageInfo customerMortgageInfoEntity:{}", JSONUtil.toJsonStr(customerMortgageInfoEntity));
        //更新客户抵押状态 放款方式
        OrderInfoEntity orderInfoUpdate = new OrderInfoEntity();
        orderInfoUpdate.setMortgageState(orderInfo.getMortgageState());
        orderInfoUpdate.setPaymentType(orderInfo.getPaymentType());
        orderInfoMapper.update(orderInfoUpdate, new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getId, orderId)
        );
        //如果为线上抵押同步解抵表
        List<FundUndoMortgageInfoEntity> fundUndoMortgageInfoEntities = fundUndoMortgageInfoEntityMapper.selectList(new LambdaQueryWrapper<FundUndoMortgageInfoEntity>()
                .eq(FundUndoMortgageInfoEntity::getOrderId, oldOrderVehicleInfoEntity.getOrderId())
        );
        fundUndoMortgageInfoEntities.forEach(fundUndoMortgageInfoEntity -> {
            fundUndoMortgageInfoEntity.setId(null);
            fundUndoMortgageInfoEntity.setOrderId(orderId);
            fundUndoMortgageInfoEntity.setCreateTime(null);
            fundUndoMortgageInfoEntity.setUpdateTime(null);

            fundUndoMortgageInfoEntityMapper.insert(fundUndoMortgageInfoEntity);
        });
    }

    /**
     * 同步预申请基础文件
     */
    public void syncPreBaseFileByPreId(Integer preId, Integer orderId) {
        PreApprovalApplyInfoEntity preApprovalApplyInfo = preApprovalApplyInfoMapper.selectById(preId);
        if (ObjUtil.isNull(preApprovalApplyInfo)) {
            log.info("syncPreBaseFileByPreId preApprovalApplyInfo is null preId:{}", preId);
            throw new BusinessException("预申请信息不存在");
        }

        Optional<PreOcrVehicleInfoEntity> preOcrVehicleInfo = Optional.ofNullable(preOcrVehicleInfoMapper.selectOne(
                new LambdaQueryWrapper<PreOcrVehicleInfoEntity>().eq(PreOcrVehicleInfoEntity::getPreId, preId)));
        Optional<PreOcrIdentityCardEntity> preOcrIdentityCard = Optional.ofNullable(preOcrIdentityCardMapper.selectOne(
                new LambdaQueryWrapper<PreOcrIdentityCardEntity>().eq(PreOcrIdentityCardEntity::getPreId, preId)));
        Optional<PreApprovalFddAuthEntity> authPicture = Optional.ofNullable(preApprovalFddAuthMapper.selectOne(
                new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                        .eq(PreApprovalFddAuthEntity::getPhone, preApprovalApplyInfo.getPhone())
                        .eq(PreApprovalFddAuthEntity::getIdNumber, preApprovalApplyInfo.getIdNumber())
                        .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                        .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                        .orderByDesc(PreApprovalFddAuthEntity::getCreateTime)
                        .last("limit 1")));
        Map<String, String> imageCodeMap = new HashMap<>();
        preOcrVehicleInfo.ifPresent(info -> {
            imageCodeMap.put(info.getDrivingLicenseImage(), "DRV_LIC_HO");
            imageCodeMap.put(info.getDrivingLicenseDeputyImage(), "DRV_LIC_ATT");
        });
        preOcrIdentityCard.ifPresent(card -> {
            imageCodeMap.put(card.getIdCardBackImage(), "CARD_BACK");
            imageCodeMap.put(card.getIdCardFrontImage(), "CARD_FRONT");
        });
        if (StrUtil.isNotBlank(preApprovalApplyInfo.getFaceId())) {
            imageCodeMap.put(preApprovalApplyInfo.getFaceId(), "FACE_RECOGNITION");
        } else {
            authPicture.ifPresent(picture -> {
                imageCodeMap.put(picture.getFacePictureId(), "FACE_RECOGNITION");
            });
        }
        List<String> resourceIds = new ArrayList<>(imageCodeMap.keySet());
        List<FileResourceResultVO> fileResourceResults = resourceFeign.queryFileByIds(resourceIds).getData();

        FileResourceInfoResultVO faceResourceInfo = null;
        if (ObjUtil.isNotNull(imageCodeMap) && imageCodeMap.containsKey("FACE_RECOGNITION")) {
            Result<FileResourceInfoResultVO> faceRecognition = resourceFeign.queryInfoByResourceId(imageCodeMap.get("FACE_RECOGNITION"));
            if (Result.isSuccess(faceRecognition)) {
                faceResourceInfo = faceRecognition.getData();
            }
        }

        for (FileResourceResultVO fileResource : fileResourceResults) {
            OrderFileEntity orderFile = new OrderFileEntity();
            String code = imageCodeMap.getOrDefault(fileResource.getFileUid(), "UNKNOWN");
            Integer fileConfigId = resourceFeign.selectFileConfigByCode(code).getData();

            //判断订单是否存在如果存在更新不存在新增
            OrderFileEntity orderFileEntity = orderFileMapper.selectOne(new LambdaQueryWrapper<OrderFileEntity>()
                    .eq(OrderFileEntity::getOrderId, orderId)
                    .eq(OrderFileEntity::getFileId, fileConfigId)
                    .eq(OrderFileEntity::getDeleteFlag, 0)
            );

            orderFile.setFileId(fileConfigId);
            orderFile.setOrderId(orderId);
            orderFile.setFileName(fileResource.getFileOldName());
            orderFile.setResourceId(fileResource.getFileUid());
            orderFile.setResourceName(fileResource.getFileName());

            //人脸识别文件
            if (ObjUtil.isNotNull(faceResourceInfo) && StrUtil.equals(code, "FACE_RECOGNITION")) {
                if (orderFileEntity.getUpdateTime().isBefore(faceResourceInfo.getCreateTime())) {
                    //更新最新的resource
                    orderFile.setFileName(faceResourceInfo.getFileOldName());
                    orderFile.setResourceId(faceResourceInfo.getFileUid());
                    orderFile.setResourceName(faceResourceInfo.getFileName());
                }
            }

            if (orderFileEntity != null) {
                orderFile.setId(orderFileEntity.getId());
                orderFileMapper.updateById(orderFile);
            } else {
                orderFileMapper.insert(orderFile);
            }
        }
    }

    /**
     * 同步企业许可证
     *
     * @param orderId    订单ID
     * @param oldOrderId 老次序id
     */
    public void syncEnterpriseLicense(Integer orderId, Integer oldOrderId) {
        OrderCompanyInfoEntity oldOrderCompanyInfoEntity = orderCompanyInfoMapper.selectOne(new LambdaQueryWrapper<OrderCompanyInfoEntity>()
                .eq(OrderCompanyInfoEntity::getOrderId, oldOrderId)
        );
        OrderCompanyInfoEntity orderCompanyInfoEntity = orderCompanyInfoMapper.selectOne(new LambdaQueryWrapper<OrderCompanyInfoEntity>()
                .eq(OrderCompanyInfoEntity::getOrderId, orderId)
        );
        if (Objects.nonNull(oldOrderCompanyInfoEntity) && Objects.isNull(orderCompanyInfoEntity)) {
            oldOrderCompanyInfoEntity.setId(null);
            oldOrderCompanyInfoEntity.setOrderId(orderId);
            orderCompanyInfoMapper.insert(oldOrderCompanyInfoEntity);
        }
    }

    /**
     * 同步订单客户
     *
     * @param orderId    订单ID
     * @param oldOrderId 老次序id
     */
    private void syncOrderContact(Integer orderId, Integer oldOrderId) {
        log.info("PreApprovalApplyInfoServiceImpl.syncOrderContact orderId:{} oldOrderId:{}", orderId, oldOrderId);
        List<OrderContactPersonEntity> orderCustomerEntities = orderContactPersonMapper.selectList(new LambdaQueryWrapper<OrderContactPersonEntity>()
                .eq(OrderContactPersonEntity::getOrderId, oldOrderId).eq(OrderContactPersonEntity::getDeleteFlag, 0)
        );
        log.info("PreApprovalApplyInfoServiceImpl.syncOrderContact orderCustomerEntities:{}", JSONUtil.toJsonStr(orderCustomerEntities));
        if (CollUtil.isNotEmpty(orderCustomerEntities)) {
            orderCustomerEntities.forEach(item -> {
                item.setId(null);
                item.setOrderId(orderId);
                orderContactPersonMapper.insert(item);
            });
        }
    }

    /**
     * 同步订单文件
     *
     * @param orderId    订单ID
     * @param oldOrderId 老次序id
     */
    public void syncOrderFile(Integer orderId, Integer oldOrderId) {
//        MenuFileDTO menuFileDTO = new MenuFileDTO().setOrderId(oldOrderId).setType(1);
//        log.info("PreApprovalApplyInfoServiceImpl.syncOrderFile menuFileDTO:{}", JSONUtil.toJsonStr(menuFileDTO));
//        List<Tree<Integer>> trees = orderFileMenuService.menuFile(menuFileDTO);
        List<OrderFileEntity> orderFileEntities = orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                .eq(OrderFileEntity::getOrderId, oldOrderId)
                .eq(OrderFileEntity::getDeleteFlag, 0)
        );
        log.info("PreApprovalApplyInfoServiceImpl.syncOrderFile orderFileEntities:{}", JSONUtil.toJsonStr(orderFileEntities));
        //复制历史订单文件信息到新订单中
        orderFileEntities.forEach(item -> {
            item.setId(null);
            item.setOrderId(orderId);
            orderFileMapper.insert(item);
        });
    }

    /**
     * 保存我们聊天订单信息
     *
     * @param preId   前id
     * @param orderId 订单id
     */
    private void saveWeChatOrderInfo(Integer preId, Integer orderId) {
        List<WeChatOrderEntity> weChatOrderEntities = wechatOrderMapper.selectList(new LambdaQueryWrapper<WeChatOrderEntity>()
                .eq(WeChatOrderEntity::getPreId, preId)
                .eq(WeChatOrderEntity::getDeleteFlag, 0)
        );
        log.info("PreApprovalApplyInfoServiceImpl.preToOrder orderId:{} weChatOrderEntities:{}", orderId, JSONUtil.toJsonStr(weChatOrderEntities));
        if (CollUtil.isNotEmpty(weChatOrderEntities)) {
            WeChatOrderEntity weChatOrderEntity = weChatOrderEntities.get(0);
            Integer id = weChatOrderEntity.getId();
            wechatOrderMapper.update(new LambdaUpdateWrapper<WeChatOrderEntity>()
                    .set(WeChatOrderEntity::getOrderId, orderId)
                    .eq(WeChatOrderEntity::getId, id)
            );
            log.info("PreApprovalApplyInfoServiceImpl.preToOrder orderId:{} id:{}", orderId, id);
        }
    }

    /**
     * Cal订单金额
     *
     * @param dto               DTO
     * @param orderId           订单id
     * @param preFundInfoEntity 基金前信息实体
     * @param approvalApplyInfo 审批申请信息
     */
    private void calOrderAmount(Integer orderId, PreFundInfoEntity preFundInfoEntity, PreManagerApprovalSubmitDTO dto, PreApprovalApplyInfoEntity approvalApplyInfo) {
        BigDecimal hopeAmount = approvalApplyInfo.getLoanAmount();
        //计算订单额度
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, "订单不存在");
        BigDecimal fundCreditAmount = preFundInfoEntity.getFundCreditAmount();
        BigDecimal evaluationAmount = preFundInfoEntity.getEvaluationAmount();
        BigDecimal creditAmount = preFundInfoEntity.getCreditAmount();
//        BigDecimal riskAmount = creditAmount.min(hopeAmount);
        //风控额度 = 授信额度 和 资方授信额度中的较小值
        BigDecimal riskAmount = creditAmount.min(fundCreditAmount);
        BigDecimal fundPreAmount = riskAmount.min(creditAmount);
        FundInfoEntity fundInfoEntity = fundInfoMapper.selectById(dto.getFundId());
        BigDecimal maxMortgage = fundInfoEntity.getMaxMortgage().multiply(BigDecimal.valueOf(100));
        BigDecimal customerManagerAmount = dto.getCustomerManagerAmount();
        amountService.updateOrderAmount(orderId, hopeAmount, OrderAmountEnum.HOPE_AMOUNT);
        amountService.updateOrderAmount(orderId, evaluationAmount, OrderAmountEnum.SOFT_REVIEW_AMOUNT);
        amountService.updateOrderAmount(orderId, creditAmount, OrderAmountEnum.PRE_AMOUNT);
        amountService.updateOrderAmount(orderId, customerManagerAmount, OrderAmountEnum.LOAN_AMOUNT);
        amountService.updateOrderAmount(orderId, creditAmount, OrderAmountEnum.TOTAL_AMOUNT);
        amountService.updateOrderAmount(orderId, amountService.formatToThousand(riskAmount), OrderAmountEnum.RISK_AMOUNT);
        amountService.updateOrderAmount(orderId, fundCreditAmount, OrderAmountEnum.FUND_PRE_AMOUNT);
        amountService.updateOrderAmount(orderId, amountService.calculateAssessmentAmount(orderId, riskAmount, evaluationAmount), OrderAmountEnum.APPRAISER_AMOUNT);
        amountService.updateOrderAmount(orderId, fundCreditAmount, OrderAmountEnum.CUSTOMER_CONFIRM_AMOUNT);
    }

    /**
     * 预订购客户
     *
     * @param orderId                  订单ID
     * @param preOcrIdentityCardEntity OCR 之前身份证实体
     */
    private Integer preToOrderCustomer(Integer orderId, Integer oldCustomerId, PreOcrIdentityCardEntity preOcrIdentityCardEntity) {
        OrderCustomerInfoEntity customerInfo = null;
        if (Objects.nonNull(oldCustomerId)) {
            customerInfo = orderCustomerInfoMapper.selectById(oldCustomerId);
            customerInfo.setId(null);
            log.info("PreApprovalApplyInfoServiceImpl.preToOrderCustomer old customerInfo:{}", JSONUtil.toJsonStr(customerInfo));
        }
        if (Objects.isNull(customerInfo)) {
            customerInfo = new OrderCustomerInfoEntity();
        }
        customerInfo.setName(preOcrIdentityCardEntity.getUpdateName());
        String updateIdNumber = preOcrIdentityCardEntity.getUpdateIdNumber();
        customerInfo.setIdNumber(updateIdNumber);
        customerInfo.setBirthDate(IdcardUtil.getBirthDate(updateIdNumber));
        customerInfo.setAge(IdcardUtil.getAgeByIdCard(updateIdNumber));
        customerInfo.setPhone(preOcrIdentityCardEntity.getPhone());
        customerInfo.setGender(dictService.getDictValue(GlobalConstants.DictType.SEX, preOcrIdentityCardEntity.getUpdateGender()));
        customerInfo.setNation(dictService.getDictValue(GlobalConstants.DictType.NATION, preOcrIdentityCardEntity.getUpdateNation()));
        customerInfo.setIdType(1);
        customerInfo.setIdCardDetailedAddress(preOcrIdentityCardEntity.getUpdateAddress());
        customerInfo.setIssuingAuthority(preOcrIdentityCardEntity.getUpdateIssuingAuthority());
        customerInfo.setIdProvinceName(preOcrIdentityCardEntity.getProvinceName());
        customerInfo.setIdCityName(preOcrIdentityCardEntity.getCityName());
        customerInfo.setIdCityId(preOcrIdentityCardEntity.getCityId());
        customerInfo.setIdProvinceId(preOcrIdentityCardEntity.getProvinceId());
        customerInfo.setValidityStartDate(preOcrIdentityCardEntity.getUpdateValidityStart());
        customerInfo.setValidityEnd(preOcrIdentityCardEntity.getUpdateValidityEnd());

        if (ObjUtil.isEmpty(customerInfo.getEducationalBackground())){
            String degree = xueliService.getEducation(new QueryVerifyV4DTO().setIdNo(preOcrIdentityCardEntity.getIdNumber()).setName(preOcrIdentityCardEntity.getName()));
            if(StringUtils.isNotEmpty(degree)){
                if (degree.contains("博士")){
                    customerInfo.setEducationalBackground(1);
                }else if (degree.contains("硕士")){
                    customerInfo.setEducationalBackground(10);
                }else if (degree.contains("本科")){
                    customerInfo.setEducationalBackground(20);
                }else if (degree.contains("专科")){
                    customerInfo.setEducationalBackground(30);
                }else if (degree.contains("中专")){
                    customerInfo.setEducationalBackground(40);
                } else if (degree.contains("高中")){
                    customerInfo.setEducationalBackground(50);
                }else if (degree.contains("初中")){
                    customerInfo.setEducationalBackground(60);
                }
            }else {
                customerInfo.setEducationalBackground(50);
            }
        }
        if (ObjUtil.isEmpty(customerInfo.getMaritalStatus())){
            String userMarriage = xueliService.getUserMarriage(new GetUserMarriageDTO().setIdNumber(preOcrIdentityCardEntity.getIdNumber()).setName(preOcrIdentityCardEntity.getName()));
            if (StringUtils.isNotEmpty(userMarriage)){
                if (userMarriage.contains("未")){
                    customerInfo.setMaritalStatus(0);
                }else if (userMarriage.contains("已婚")){
                    customerInfo.setMaritalStatus(2);
                }else if (userMarriage.contains("离")){
                    customerInfo.setMaritalStatus(4);
                }
            }
        }
        log.info("PreApprovalApplyInfoServiceImpl.preToOrderCustomer new customerInfo:{}", JSONUtil.toJsonStr(customerInfo));
        orderCustomerInfoMapper.insert(customerInfo);
        orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                .set(OrderInfoEntity::getCustomerId, customerInfo.getId())
                .eq(OrderInfoEntity::getId, orderId)
        );

        return customerInfo.getId();
    }

    /**
     * 预购车辆
     *
     * @param orderId                 订单ID
     * @param preOcrVehicleInfoEntity OCR 之前 Vehicle Info 实体
     */
    private void preToOrderVehicle(Integer orderId, Integer oldOrderId, PreOcrVehicleInfoEntity preOcrVehicleInfoEntity) {
        //订单车辆信息中没有数据，进行添加操作
        OrderVehicleInfoEntity vehicleInfo = null;
        OrderVehicleInfoEntity oldVehicleInfo = null;
        if (Objects.nonNull(oldOrderId)) {
            oldVehicleInfo = orderVehicleInfoMapper.selectOne(new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                    .eq(OrderVehicleInfoEntity::getOrderId, oldOrderId)
            );
            vehicleInfo = oldVehicleInfo;
            log.info("PreApprovalApplyInfoServiceImpl.preToOrderVehicle old vehicleInfo:{}", JSONUtil.toJsonStr(vehicleInfo));
        }
        if (Objects.isNull(vehicleInfo)) {
            vehicleInfo = new OrderVehicleInfoEntity();
        }
        vehicleInfo.setOrderId(orderId);
        vehicleInfo.setIndex(1);
        vehicleInfo.setVin(preOcrVehicleInfoEntity.getUpdateVin());
        vehicleInfo.setVehicleNumber(preOcrVehicleInfoEntity.getUpdateVehicleNumber());
        vehicleInfo.setVehicleType(preOcrVehicleInfoEntity.getUpdateVehicleType());
        vehicleInfo.setNatureOfUse(preOcrVehicleInfoEntity.getUpdateNatureOfUse());
        vehicleInfo.setEngineNumber(preOcrVehicleInfoEntity.getUpdateEngineNumber());
        vehicleInfo.setHolder(preOcrVehicleInfoEntity.getUpdateHolder());
        vehicleInfo.setBrand(preOcrVehicleInfoEntity.getBrand());
        vehicleInfo.setVehicleSeries(preOcrVehicleInfoEntity.getVehicleSeries());
        vehicleInfo.setVehicleModel(preOcrVehicleInfoEntity.getVehicleModel());
        vehicleInfo.setGuidePrice(preOcrVehicleInfoEntity.getGuidePrice());
        vehicleInfo.setVehicleAge(Convert.toInt(preOcrVehicleInfoEntity.getVehicleAge()));
        vehicleInfo.setSeatCount(preOcrVehicleInfoEntity.getSeatCount());
        vehicleInfo.setSealInformation(preOcrVehicleInfoEntity.getUpdateSealInformation());
        vehicleInfo.setVehicleYear(preOcrVehicleInfoEntity.getVehicleYear());
        vehicleInfo.setRegisterDate(preOcrVehicleInfoEntity.getUpdateRegisterDate());
        vehicleInfo.setIssueDate(preOcrVehicleInfoEntity.getUpdateIssueDate());
        vehicleInfo.setMileage(preOcrVehicleInfoEntity.getMileage());
        vehicleInfo.setTransferTimes(preOcrVehicleInfoEntity.getTransferTimes());
        vehicleInfo.setAddress(preOcrVehicleInfoEntity.getUpdateAddress());
        getCar300Data(vehicleInfo);
        log.info("PreApprovalApplyInfoServiceImpl.preToOrderVehicle.old.oldVehicleInfo:{}", JSONUtil.toJsonStr(oldVehicleInfo));
        if (Objects.isNull(oldVehicleInfo)) {
            try {
                //通过车牌号查询车牌省份、城市信息
                String city = vehicleInfo.getVehicleNumber().substring(0, 2);
                DataAreaEntity licenseCity = dataAreaMapper.selectOne(new LambdaQueryWrapper<DataAreaEntity>()
                        .like(DataAreaEntity::getCarRegion, city));
                DataAreaEntity licenseProvince = dataAreaMapper.selectOne(new LambdaQueryWrapper<DataAreaEntity>()
                        .eq(DataAreaEntity::getId, licenseCity.getFatherId()));
                vehicleInfo.setLicenseProvince(licenseProvince.getAreaName())
                        .setLicenseCity(licenseCity.getAreaName());
            } catch (Exception e) {
                log.error("PreApprovalApplyInfoServiceImpl.preToOrderVehicle license province city error:{}", e.getMessage(), e);
                throw new BusinessException("获取车牌省份、城市信息失败");
            }
            getCar300Data(vehicleInfo);
            log.info("PreApprovalApplyInfoServiceImpl.preToOrderVehicle new vehicleInfo:{}", JSONUtil.toJsonStr(vehicleInfo));
        }
        orderVehicleInfoMapper.insert(vehicleInfo);
    }

    private void getCar300Data(OrderVehicleInfoEntity vehicleInfo){
        try {
            PreVinRecordEntity preVinRecordEntity = preVinRecordMapper.selectOne(new LambdaQueryWrapper<PreVinRecordEntity>()
                    .eq(PreVinRecordEntity::getVin, vehicleInfo.getVin())
                    .orderByDesc(PreVinRecordEntity::getCreateTime)
                    .last("LIMIT 1"));
            Car300DataEntity car300DataEntity = car300DataMapper.selectOne(
                    new LambdaQueryWrapper<Car300DataEntity>()
                            .eq(Car300DataEntity::getVin, vehicleInfo.getVin())
                            .eq(Car300DataEntity::getDeleteFlag, 0)
                            .orderByDesc(Car300DataEntity::getCreateTime)
                            .last("LIMIT 1")
            );
            if (ObjUtil.isNotEmpty(preVinRecordEntity) && ObjUtil.isNotEmpty(car300DataEntity)){
                //字符串转为本服务接收车辆信息类
                CarModelInfoResDTO carModelInfoResDTO = objectMapper.readValue(preVinRecordEntity.getResponseBody(), CarModelInfoResDTO.class);
                CarModelInfoResDTO.CarBaseInfo carBaseInfo = carModelInfoResDTO.getData().get(0);

                BigDecimal estimatedValue = null == carBaseInfo.getUsedPrice() ? null : carBaseInfo.getUsedPrice().getBuyPrice();

                vehicleInfo.setDisplacement(carBaseInfo.getModel().getDisplacement())
                        .setVehicleModel(ObjUtil.isNotEmpty(car300DataEntity.getModelName()) ? car300DataEntity.getModelName() : carBaseInfo.getModel().getName())
                        .setEmission(carBaseInfo.getDetail().getBasic().getEmission())
                        .setFuelType(carBaseInfo.getDetail().getEngine().getFuelType())
                        .setTransmissionType(carBaseInfo.getModel().getTransmissionType())
                        .setEstimatedValue(estimatedValue)
                        .setVehicleColor(carBaseInfo.getColor())
                        .setProductionDate(carBaseInfo.getProductionDate());
            }else {
                if (ObjUtil.isNotEmpty(car300DataEntity)){
//                        AtomicReference<BigDecimal> estimatedValue = new AtomicReference<>(BigDecimal.ZERO);
//                        Stream.of(car300DataEntity.getDealerHighSoldPriceExcellent(),
//                                        car300DataEntity.getIndividualLowSoldPriceExcellent(),
//                                        car300DataEntity.getDealerPriceExcellent(),
//                                        car300DataEntity.getDealerHighBuyPriceExcellent(),
//                                        car300DataEntity.getDealerHighSoldPriceGood(),
//                                        car300DataEntity.getIndividualLowSoldPriceGood(),
//                                        car300DataEntity.getDealerPriceGood(),
//                                        car300DataEntity.getDealerHighBuyPriceGood(),
//                                        car300DataEntity.getDealerPriceNormal(),
//                                        car300DataEntity.getDealerHighSoldPriceNormal(),
//                                        car300DataEntity.getIndividualLowSoldPriceNormal(),
//                                        car300DataEntity.getDealerHighBuyPriceNormal()
//                                ).filter(Objects::nonNull)
//                                .max(BigDecimal::compareTo)
//                                        .ifPresent(estimatedValue::set);
                    if (ObjUtil.isNotEmpty(car300DataEntity.getGreenType()) && !Objects.equals(car300DataEntity.getGreenType(),"0") && ObjUtil.isNotEmpty(car300DataEntity.getVehicleType()) && Objects.equals(car300DataEntity.getVehicleType(), 1)){
                        switch (car300DataEntity.getGreenType()){
                            case "0":
                                vehicleInfo.setFuelType("不适用");
                                break;
                            case "1":
                                vehicleInfo.setFuelType("纯电动");
                                break;
                            case "2":
                                vehicleInfo.setFuelType("插电式混动");
                                break;
                            case "4":
                                vehicleInfo.setFuelType("增程式混动");
                                break;
                            case "8":
                                vehicleInfo.setFuelType("燃料电池");
                                break;
                        }
                    }else {
                        switch (car300DataEntity.getFuelType()){
                            case "0":
                                vehicleInfo.setFuelType("汽油");
                                break;
                            case "1":
                                vehicleInfo.setFuelType("柴油");
                                break;
                            case "2":
                                vehicleInfo.setFuelType("电力");
                                break;
                            case "3":
                                vehicleInfo.setFuelType("油电");
                                break;
                            case "4":
                                vehicleInfo.setFuelType("油气");
                                break;
                            case "5":
                                vehicleInfo.setFuelType("天然气");
                                break;
                            case "6":
                                vehicleInfo.setFuelType("氢能源");
                                break;
                            case "7":
                                vehicleInfo.setFuelType("甲醇");
                                break;
                            default: vehicleInfo.setFuelType("未知");
                        }
                    }
                    vehicleInfo.setDisplacement(car300DataEntity.getModelLiter())
                            .setVehicleModel(car300DataEntity.getModelName())
                            .setEmission(car300DataEntity.getModelEmissionStandard())
                            .setTransmissionType(car300DataEntity.getModelGear())
                            .setEstimatedValue(car300DataEntity.getEvaluationAmount())
                            .setVehicleColor(car300DataEntity.getColor())
                            .setProductionDate(LocalDate.now().minusYears(car300DataEntity.getVehicleAge()));
                }
            }

        } catch (Exception e) {
            log.error("PreApprovalApplyInfoServiceImpl.preToOrderVehicle vin detail error:{}", e.getMessage(), e);
            throw new BusinessException("获取车辆信息失败");
        }
    }

    /**
     * 使用历史取消订单gps
     */
    private void preToOrderGps(Integer orderId) {
        try {
            orderRestoreService.restoreSubmitOrderGps(orderId);
        } catch (Exception e) {
            log.error("PreApprovalApplyInfoServiceImpl.preToOrderGps error:{}", e.getMessage(), e);
            throw new BusinessException("使用历史取消订单gps失败");
        }
    }

    /**
     * 订单到订单信息
     *
     * @param preId                   前id
     * @param oldOrderInfoEntity      旧 Order Info 实体
     * @param dto                     DTO
     * @param approvalApplyInfo       审批申请信息
     * @param productInfoEntity       产品信息实体
     * @param preFundInfoEntity       前资方信息entity
     * @param preOcrVehicleInfoEntity OCR 之前 Vehicle Info 实体
     * @return {@link OrderInfoEntity }
     */
    private OrderInfoEntity orderToOrderInfo(Integer preId, OrderInfoEntity oldOrderInfoEntity, PreManagerApprovalSubmitDTO dto,
                                             PreApprovalApplyInfoEntity approvalApplyInfo, ProductInfoEntity productInfoEntity,
                                             PreFundInfoEntity preFundInfoEntity, PreOcrVehicleInfoEntity preOcrVehicleInfoEntity) {
        UserStoreVO userStoreVO = new UserStoreVO();
        Result<List<UserStoreVO>> listResult = userFeign.searchUserStoreBatch(Collections.singletonList(approvalApplyInfo.getAccountManagerId()));
        if (Result.isSuccess(listResult)) {
            List<UserStoreVO> userStoreVOS = listResult.getData();
            userStoreVO = userStoreVOS.get(0);
        }
        BigDecimal customerManagerAmount = dto.getCustomerManagerAmount();

        OrderInfoEntity entity = null;
//        if (Objects.nonNull(oldOrderInfoEntity)) {
//            entity = oldOrderInfoEntity;
//            entity.setId(null);
//            entity.setCreateTime(null);
//            entity.setUpdateTime(null);
//        }
        if (Objects.isNull(entity)) {
            entity = new OrderInfoEntity();
        }
        Integer productId = dto.getProductId();
        Integer fundId = dto.getFundId();
        String fundName = fundInfoMapper.selectById(fundId).getName();
        //预审批id
        entity.setPreId(preId)
                //门店名称
                .setStoreName(userStoreVO.getStore())
                //订单编号
                .setOrderNumber(OrderNumberGenerator.generateOrderNumber(1))
                //业务类型
                .setBusinessType(1)
                //客户姓名
                .setCustomerName(approvalApplyInfo.getName())
                //贷款金额
                .setApplyAmount(customerManagerAmount)
                //贷款期限
                .setTerm(productInfoEntity.getTerm())
                //产品ID
                .setProductId(productInfoEntity.getId())
                .setProductName(productInfoEntity.getName())
                .setRate(productInfoEntity.getYearRate())
                //客户手机号
                .setCustomerPhone(approvalApplyInfo.getPhone())
                //客户经理id
                .setManagerId(approvalApplyInfo.getAccountManagerId())
                //客户经理门店部门id
                .setDeptId(userStoreVO.getStoreId())
                .setRegionId(userStoreVO.getAreaId())
                .setRegionName(userStoreVO.getArea())
                .setTeamId(userStoreVO.getTeamId())
                .setTeamName(userStoreVO.getTeam())
                //风控额度
                .setRiskAmount(preFundInfoEntity.getFundCreditAmount())
                // 评估额度
                .setEstimateAmount(preFundInfoEntity.getEvaluationAmount())
                //数据来源
                .setSource(approvalApplyInfo.getSourceType() == 1 ? "电销" : "门店")
                //预审批申请时间
                .setPreApplyTime(ObjUtil.isNotNull(oldOrderInfoEntity) ? oldOrderInfoEntity.getCreateTime() : approvalApplyInfo.getCreateTime())
                //车牌号
                .setVehicleNumber(preOcrVehicleInfoEntity.getUpdateVehicleNumber())
                // 资方ID
                .setFundId(preFundInfoEntity.getFundId())
                .setFundName(fundName)
                //数据来源
                .setSourceType(approvalApplyInfo.getSourceType())
                .setState(States.BUSINESS_ADDED_INFO.getNode())
                .setCurrentNode(States.BUSINESS_ADDED_INFO.getNode())
                .setLastNodeFinishTime(LocalDateTime.now())
                .setRiskUserId(entity.getRiskUserId())
                .setOrderType(getPreBusinessType(preFundInfoEntity.getApplyType(), productId, fundId));
        orderInfoMapper.insert(entity);

        ProductSaveDTO productSaveDTO = new ProductSaveDTO();
        productSaveDTO.setPreId(entity.getPreId());
        productSaveDTO.setOrderId(entity.getId());
        productSaveDTO.setCapitalId(entity.getFundId());
        productSaveDTO.setCapitalName(entity.getFundName());
        productSaveDTO.setProductId(entity.getProductId());
        productSaveDTO.setProductName(entity.getProductName());
        log.info("kingdeeProductSave dto = {}", JSONUtil.toJsonStr(productSaveDTO));
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(preId);
        if (Objects.equals(preApprovalApplyInfoEntity.getSourceType(), 1) && (Objects.equals(preApprovalApplyInfoEntity.getRegionId(), 24) || Objects.equals(preApprovalApplyInfoEntity.getRegionId(), 56))){
            kingdeeProductSave(productSaveDTO);
        }
        return entity;
    }

    @Async
    public void kingdeeProductSave(ProductSaveDTO dto) {
        String jsonPrettyStr = JSONUtil.toJsonStr(dto, new JSONConfig().setIgnoreNullValue(false));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        String s = kingdeeFeign.productSave(body, sign, iv, timestamp);
        log.info("kingdeeProductSave s = {}", s);
    }

    private Integer getPreBusinessType(Integer orderType, Integer productId, Integer fundId) {
        if (orderType == null) {
            return getProductBusinessType(productId, fundId);
        } else {
            if (Objects.equals(orderType, 2)) {
                return 1;
            } else {
                return 0;
            }
        }
    }

    public Integer getProductBusinessType(Integer productId, Integer fundId) {
        List<FundProductMappingEntity> fundProductMappingEntities = fundProductMappingMapper.selectList(new LambdaQueryWrapper<FundProductMappingEntity>()
                .eq(FundProductMappingEntity::getFundId, fundId)
                .eq(FundProductMappingEntity::getProductId, productId)
                .eq(FundProductMappingEntity::getStatus, 0)
        );
        if (CollUtil.isNotEmpty(fundProductMappingEntities)) {
            FundProductMappingEntity fundProductMappingEntity = fundProductMappingEntities.get(0);
            return fundProductMappingEntity.getBusinessType();
        }
        return 0;
    }

    /**
     * 添加订单文件
     *
     * @param preId 前id
     */
    private void syncPreFileToOrder(Integer orderId, Integer preId) {
        orderFileService.orderFileTransfer(orderId, preId);
    }
}
