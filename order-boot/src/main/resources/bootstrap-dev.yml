server:
  port: 9600

spring:
  main:
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    driver-class-name: org.postgresql.Driver


mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
app:
  hostname: "https://guanjia.longhuanhuifeng.com"

logging:
  level:
    com.longhuan.order.api.com.longhuan.order.api.OrderApi: debug
    com.longhuan.order.feign.TongLianPayFeign: debug
    com.longhuan.order.feign.GPSFeign: debug
    com.longhuan.order.feign.TuQiangOLFeign: debug
    com.longhuan.order.kingdee.feign.KingdeeFeign: debug
    com.longhuan.order.feign.user.UserFeign: debug
    com.longhuan.order.feign.ESignFeign: debug
    com.longhuan.order.feign.ZhongXinFeign: debug
    com.longhuan.order.feign.DigitalizeFeign: debug