package com.longhuan.order.pojo.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.longhuan.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 门店GPS分期和收费策略配置表
 * @TableName lh_store_gps_config
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "门店GPS分期和收费策略配置表")
@TableName("lh_store_gps_config")
@KeySequence("lh_store_gps_config_id_seq")
@EqualsAndHashCode(callSuper = true)
public class StoreGpsConfigEntity extends BaseEntity {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId
    private Integer id;

    /**
     * 门店ID，关联lh_dept表
     */
    @ApiModelProperty(value = "门店ID，关联lh_dept表")
    private Integer deptId;

    /**
     * 是否支持分期（0：不支持，1：支持）
     */
    @ApiModelProperty(value = "是否支持分期（0：不支持，1：支持）")
    private Integer isInstallment;

    /**
     * 是否支持免费（0：不支持，1：支持）
     */
    @ApiModelProperty(value = "是否支持免费（0：不支持，1：支持）")
    private Integer isFree;
}
