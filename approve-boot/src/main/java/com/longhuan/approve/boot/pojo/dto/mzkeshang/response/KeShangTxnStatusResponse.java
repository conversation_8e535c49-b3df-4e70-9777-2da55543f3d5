package com.longhuan.approve.boot.pojo.dto.mzkeshang.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 040200 申贷放款结果查询
 */
@Data
@Accessors(chain = true)
public class KeShangTxnStatusResponse {

    /**
     * 处理结果
     * 00-订单不存在
     * 11-进件审批处理中
     * 12-进件审批拒绝
     * 13-进件审批通过
     * 23-进件已撤销
     * 25-人工审核中
     * 26-人工审核驳回
     * 27-人工审核通过
     * 31-放款处理中
     * 32-放款失败
     * 33-放款成功
     * 99-其他
     */
    @JsonProperty("TxnSt")
    private String txnSt;

    /**
     * 授信额度
     * 13-进件审批通过时返回
     */
    @JsonProperty("CrLmt")
    private String crLmt;

    /**
     * 处理意见
     */
    @JsonProperty("TxnFailRsn")
    private String txnFailRsn;

    /**
     * 银行贷款号
     * 33-放款成功时返回
     */
    @JsonProperty("BnkLoanNO")
    private String bnkLoanNO;
}
