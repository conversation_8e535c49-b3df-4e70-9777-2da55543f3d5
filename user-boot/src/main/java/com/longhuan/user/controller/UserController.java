package com.longhuan.user.controller;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.constant.MsgConstants;
import com.longhuan.common.core.result.PageResult;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.core.result.ResultCode;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.user.api.DeptDetailDTO;
import com.longhuan.user.api.DeptDetailVO;
import com.longhuan.user.api.RoleListPlatformDTO;
import com.longhuan.user.api.UserApi;
import com.longhuan.user.converter.UserInfoConverter;
import com.longhuan.user.enums.UserTypeEnum;
import com.longhuan.user.pojo.dto.*;
import com.longhuan.user.pojo.entity.RoleEntity;
import com.longhuan.user.pojo.entity.UserEntity;
import com.longhuan.user.pojo.entity.UserSyncInfoEntity;
import com.longhuan.user.pojo.vo.*;
import com.longhuan.user.service.*;
import com.longhuan.user.sse.service.SseRemoteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 用户管理
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Api("用户管理")
public class UserController implements UserApi {

    private final MessageService messageService;

    private final UserService userService;
    private final DeptSyncInfoService deptSyncInfoService;
    private final UserSyncInfoService userSyncInfoService;
    private final UserRoleMappingService userRoleMappingService;
    private final QrCodeFileService lhQrCodeFileService;
    private final UserLoginRecordService userLoginRecordService;
    private final QrCodeFileService qrCodeFileService;
    private final DeptService deptService;
    private final UserInfoConverter userInfoConverter;
    private final WeChatService weChatService;
    private final RoleService roleService;
    private final UserTodoInfoService userTodoInfoService;
    private final DingHrmInfoService dingHrmInfoService;
    private final SseRemoteService sseRemoteService;
    @Override
    public Result<UserDetailInfoVO> validateUserPassword(UserPasswordDTO userPasswordDTO) {


        throw new BusinessException(ResultCode.RESOURCE_NOT_FOUND);

    }

    @Override
    public Result<UserDetailInfoVO> searchUserDetailByUsername(String username) {

        UserDetailInfoVO userDetailInfoVO = userService.searchDetailByUsername(username);

        return Result.success(userDetailInfoVO);
    }

    @GetMapping("/user/info")
    @ApiOperation("当前登录用户的用户信息")
    @ApiImplicitParam()
    public Result<UserRolePermissionVO> userMenus(@CurrentUser LoginUser loginUser) {
        log.info("getUserInfo loginUser:{}", loginUser);
        Integer userType = loginUser.getUserType();
        UserRolePermissionVO userInfo = new UserRolePermissionVO();
        if (Objects.equals(UserTypeEnum.EMPLOYEE.getType(), userType)) {

            userInfo = userService.getUserRolePermission(loginUser);
        } else if (Objects.equals(UserTypeEnum.WECHAT.getType(), userType)) {
            userInfo = weChatService.getUserInfo(loginUser);
        }
        return Result.success(userInfo);
    }

    @PostMapping("/user/menu")
    @ApiOperation("按平台查询用户菜单")
    @ApiImplicitParam()
    public Result<List<MenuVO>> userMenus(@RequestBody @Validated PlatFormDTO platFormDTO, @CurrentUser LoginUser loginUser) {

        List<Integer> roleIds = loginUser.getRoleIds();

        return Result.success(userService.getMenuVO(roleIds, platFormDTO.getPlatform()));
    }

    @PostMapping("/page")
    @ApiOperation("分页查询")
    PageResult<UserListVO> queryPage(@RequestBody UserPageDTO queryDTO) {
        Page<UserEntity> pageResult = userService.queryPage(queryDTO);

        return PageResult.success(userInfoConverter.entityPage2VO(pageResult));
    }

    /**
     * 保存用户角色
     *
     * @param dto 用户角色 DTO
     */
    @ApiOperation("保存用户角色")
    @PostMapping("/saveUserRole")
    public Result<Boolean> saveUserRole(@RequestBody UserRoleDTO dto) {
        return Result.success(userRoleMappingService.saveUserRole(dto.getUserId(), dto.getRoleIdList()));
    }

    /**
     * 根据用户id查询角色id
     *
     * @param
     * @return {@link Result< List< Integer>>}
     */
    @GetMapping("/queryRoleIdListByUser/{id}")
    Result<List<Integer>> queryRoleIdListByUser(@PathVariable("id") Integer id) {
        return Result.success(userRoleMappingService.queryRoleIdListByUser(id));
    }

    /**
     * 按手机号查询用户
     *
     * @param mobile 手机号
     * @return {@link Result }<{@link UserDetailInfoVO }>
     */
    @Override
    public Result<UserDetailInfoVO> searchUserDetailByMobile(String mobile) {
        UserDetailInfoVO userDetailInfoVO = userService.searchDetailByMobile(mobile);

        return Result.success(userDetailInfoVO);
    }

    @Override
    public Result<UserDetailInfoVO> searchUserDetailById(Integer userId) {
        UserDetailInfoVO userDetailInfoVO = userService.searchDetailByUserId(userId);

        return Result.success(userDetailInfoVO);
    }

    @Override
    public Result<UserAndDeptUsersVO> selectUsersStore(UserStoreDTO userStoreDTO) {
        return Result.success(userService.getUserInfoAndDeptUser(userStoreDTO));
    }

    @Override
    public Result<List<UserInfoVO>> mobile2UserId(@RequestBody List<String> mobiles) {
        return Result.success(userService.mobile2UserId(mobiles));
    }

    @Override
    public Result<String> syncDigitizationUsers(List<UsersDTO> usersDTOList) {
        return Result.success(userSyncInfoService.syncDigitizationUsers(usersDTOList));
    }

    @Override
    public Result<List<UserInfoVO>> jobNumber2UserId(List<String> jobNumbers) {
        return Result.success(userService.jobNumber2UserId(jobNumbers));
    }

    /**
     * 按团队 ID 划分用户 ID
     *
     * @param userIdByTeamIdDTO 按团队 ID DTO 划分用户 ID
     * @return {@link Result }<{@link List }<{@link Integer }>>
     */

    @Override
    @PostMapping("/userIdsByTeamIds")
    public Result<List<Integer>> searchUserIdsByTeamIds(@RequestBody UserIdByTeamIdDTO userIdByTeamIdDTO) {
        return Result.success(userService.userIdsByTeamIds(userIdByTeamIdDTO));
    }

    @Override
    public Result<Integer> saveUserLoginRecordInfo(LoginRecordDto loginInfo) {


        return Result.success(userLoginRecordService.saveLoginRecord(loginInfo));
    }

    @Override
    public Result<String> sendLoginValidateCode(String phone, String code) {

        // 特殊处理 无钉钉架构情况
        String s = userService.notExistDingDing(phone);

        if (StringUtils.isNotEmpty(s)) {
            return Result.success(s);
        }


        MessageContent content = new MessageContent();
        content.setSendType(MsgConstants.SEND_DD_NOTICE);
        content.setReceiver(phone);
        content.setMsgType(MsgConstants.MSG_MARKDOWN);
        content.setTitle("登录验证码");
        content.setContent("您的登录验证码是：\n # " + code);

        try {
            messageService.sendMessage(content);
        } catch (BusinessException e) {
            return Result.failed(e.getMessage());
        }
        return Result.success(null);
    }


    @Override
    public Result<List<Integer>> searchUserByRoleId(Integer roleId) {
        return Result.success(userRoleMappingService.userRoleMappingEntityList(roleId));
    }


    @Override
    public Result<String> sendMessage(String phone, String code) {
        MessageContent messageContent = new MessageContent();
        messageContent.setSendType(MsgConstants.SEND_DD_NOTICE);
        messageContent.setReceiver(phone);
        messageContent.setMsgType(MsgConstants.MSG_TEXT);
        messageContent.setContent(code);
        return Result.success(messageService.sendMessage(messageContent));
    }

    @Override
    public Result<UserQrCodeVO> searchQrCodeByUuid(String uuid) {
        return Result.success(qrCodeFileService.searchByUuid(uuid));
    }

    @Override
    public Result<QrCodeFileVO> getQrCodeFileByUuid(String uuid) {
        return Result.success(qrCodeFileService.getQrCodeFileByUuid(uuid));
    }

    @Override
    public Result<Boolean> insertQrCodeFile(QrCodeFileDTO qrCodeFileDTO) {
        return Result.success(lhQrCodeFileService.insertQrCodeFile(qrCodeFileDTO));
    }

    @Override
    public Result<Boolean> codeInvalid(Integer userId) {
        return Result.success(lhQrCodeFileService.codeInvalid(userId));
    }

    @Override
    public Result<UserInfoVO> searchByUserId(Integer userId) {
        return Result.success(userService.searchByUserId(userId));
    }

    @Override
    public Result<UserInfoVO> searchUserName(Integer userId) {
        return Result.success(userService.searchUserName(userId));
    }

    @Override
    public Result<List<UserInfoVO>> searchUserNameByUserIds(List<Integer> userIds) {
        return Result.success(userService.searchUserNameByUserIds(userIds));
    }

    @Override
    public Result<List<UserInfoVO>> searchUserNameBatch(List<Integer> userIds) {


        return Result.success(userService.searchUserNameBatch(userIds));
    }

    /**
     * 批量查询用户所属门店信息
     *
     * @param userIds 用户 ID
     * @return {@link Result }<{@link List }<{@link UserStoreVO }>>
     */
    @Override
    @ApiOperation("批量查询用户所属门店信息")
    public Result<List<UserStoreVO>> searchUserStoreBatch(List<Integer> userIds) {
        return Result.success(userService.searchUserStoreBatch(userIds));
    }


    /**
     * 按部门名称获取部门id
     *
     * @param deptName 部门名称
     * @return {@link Result }<{@link Integer }>
     */
    @Override
    public Result<Integer> getDeptByName(String deptName) {
        return Result.success(deptService.getDeptByName(deptName));
    }


    /**
     * 按部门id获取部门名称
     *
     * @param deptId 部门 ID
     * @return {@link Result }<{@link String }>
     */
    @Override
    public Result<String> getDeptById(Integer deptId) {
        return Result.success(deptService.getDeptById(deptId));
    }

    /**
     * 按部门id获取子部门Id列表
     *
     * @param deptId 区域 ID
     * @return {@link Result }<{@link List }<{@link Integer }>>
     */
    @Override
    public Result<List<Integer>> getChildDeptById(Integer deptId) {
        return Result.success(deptService.getChildDeptById(deptId));
    }

    @Override
    public Result<String> getAreaByUserId(Integer userId) {
        return Result.success(deptService.getAreaByUserId(userId));
    }

    @Override
    public ResponseEntity<byte[]> generateCodeFile(GenerateCodeDTO generateCodeDTO) {
        return qrCodeFileService.generateCode(generateCodeDTO);
    }
    @Override
    public Result<String> generateCode(GenerateCodeDTO generateCodeDTO) {
        return Result.success(qrCodeFileService.getQrCodeUrl(generateCodeDTO));
    }

    @Override
    public String generateQrCodeUrl(@RequestBody @Validated QrCodeDTO qrCodeDTO) {
        return lhQrCodeFileService.generateQrCodeUrl(qrCodeDTO);
    }

    ;

    @ApiOperation("系统管理员查询人员列表")
    @PostMapping("/getStorePersonnelList")
    public Result<Page<StorePersonnelVO>> getStorePersonnelList(@RequestBody StorePersonnelDTO storePersonnelDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(userService.getStorePersonnelList(storePersonnelDTO, loginUser));
    }

    @ApiOperation("管理员批量修改用户启用状态")
    @PostMapping("/updateUserEnabled")
    public Result<Integer> updateUserEnabled(@RequestBody List<UserEnabledDTO> usersEnableDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(userService.updateUserStatus(usersEnableDTO, loginUser));
    }

    /**
     * 按部门名称模糊查询部门Id列表
     *
     * @param deptName 部门名称
     * @return {@link List }<{@link Integer }>
     */
    @Override
    public Result<List<Integer>> getDeptIdByLikeName(String deptName) {
        return Result.success(deptService.getDeptIdByLikeName(deptName));
    }

    /**
     * 根据部门ids获取用户信息
     *
     * @param deptIds 部门 ID
     * @return {@link List }<{@link UserListVO }>
     */
    @Override
    public Result<List<UserStoreVO>> getUserStoreVoByDeptIds(List<Integer> deptIds) {
        return Result.success(userService.getUserStoreVoByDeptIds(deptIds));
    }

    @Override
    @ApiOperation("根据部门id获取区域信息")
    public Result<RegionInfoVO> getRegionInfoByDeptId(Integer deptId) {
        return Result.success(deptService.getRegionInfoByDeptId(deptId));
    }

    @Override
    @ApiOperation("根据部门id获取区域信息")
    public Result<StoreInfoVO> getStoreInfoByDeptId(Integer deptId) {
        return Result.success(deptService.getStoreInfoByDeptId(deptId));
    }


    @Override
    @ApiOperation("根据团队获取上级门店信息")
    public Result<StoreInfoVO> getStoreInfoByTeamId(@RequestBody SearchStoreInfoByTeamIdDTO dto) {
        return Result.success(deptService.getStoreInfoByDeptId(dto.getDeptId()));
    }

    /**
     * 推送待办消息
     *
     * @param todoInfoMessageDTO 待办事项信息消息dto
     * @return {@link Result }<{@link TodoInfoMessageVO }>
     */
    @Override
    public Result<TodoInfoMessageVO> dealMessage(TodoInfoMessageDTO todoInfoMessageDTO) {
        return Result.success(userTodoInfoService.dealMessage(todoInfoMessageDTO));
    }

    @Override
    public Result<List<UserInfoVO>> getDeptUsersByRoleIds(DeptUsersByRoleIdsDTO deptUsersByRoleIdsDTO) {
        return Result.success(userService.getDeptUsersByRoleIds(deptUsersByRoleIdsDTO));
    }

    @Override
    public Result<UserInfoVO> getUserInfoById(Integer userId) {
        return Result.success(userService.getUserInfoById(userId));
    }

    @Override
    public Result<List<UserInfoVO>> getUserInfoByRoleId(Integer roleId) {
        return Result.success(userService.getUserInfoByRoleId(roleId));
    }

    @Override
    public Result<UserInfoVO> getTeamManagerByTeamId(Integer teamId) {
        return Result.success(userService.getTeamManagerByTeamId(teamId));
    }

    @Override
    public Result<Page<TodoListVO>> todoList(TodoListDTO todoListDTO) {
        return Result.success(userTodoInfoService.todoList(todoListDTO, null));
    }


    @PostMapping("/todoListByLoginUser")
    public Result<Page<TodoListVO>> todoListByLoginUser(@RequestBody TodoListDTO todoListDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(userTodoInfoService.todoList(todoListDTO, loginUser));
    }

    @Override
    public Result<List<Integer>> getUserIdByStoreIdAndRoleId(getUserIdByStoreIdAndRoleIdDTO dto) {
        return Result.success(userService.getUserIdByStoreIdAndRoleId(dto));
    }

    @Override
    public Result<List<Integer>> getUserIdByRoleIds(SearchUserIdByRoleIdsDTO dto) {
        return Result.success(userService.getUserIdByRoleIds(dto));
    }

    @Override
    public Result<List<DeptSyncInfoVO>> getSyncDeptByLhDeptIds(@RequestBody SearchDeptSyncInfoDTO dto) {
        return Result.success(deptSyncInfoService.getDeptSyncInfoList(dto));
    }

    @Override
    public Result<List<DeptInfoVO>> getTheBranchNameBasedOnTheTeamId(List<Integer> teamIds) {
        return Result.success(deptService.getTheBranchNameBasedOnTheTeamId(teamIds));
    }

    @Override
    public Result<List<Integer>> getDeptUsers(List<Integer> deptIds) {
        return Result.success(deptService.getDeptUsers(deptIds));
    }

    @Override
    public Result<List<UserSyncInfoListVO>> queryUserSyncInfoList(@RequestBody SearchUserSyncInfoDTO dto) {
        List<UserSyncInfoEntity> userSyncInfoList = userSyncInfoService.queryUserSyncInfoList(dto);
        return Result.success(userInfoConverter.entityListByEntityVOList(userSyncInfoList));
    }

    @Override
    public Result<List<Integer>> getUserIdByLikeNameList(String userName) {
        return Result.success(userService.getUserIdByLikeNameList(userName));
    }

    @Override
    public Result<UserInfoVO> saveOrUpdateUser(SaveOrUpdateUserDTO dto) {
        return Result.success(userService.saveOrUpdateUser(dto));
    }

    @Override
    public Result<List<Tree<Integer>>> getDeptListByDeptType(DeptListDTO deptListDTO) {
        return Result.success(deptService.getDeptListByDeptType(deptListDTO));
    }

    @Override
    public Result<Page<DeptPageVO>> getDeptPage(DeptListDTO deptListDTO) {
        return Result.success(deptService.getDeptPage(deptListDTO));
    }

    @Override
    public Result<Boolean> saveOrUpdateDept(SaveOrUpdateDeptDTO saveOrUpdateDeptDTO) {
        return Result.success(deptService.saveOrUpdateDept(saveOrUpdateDeptDTO));
    }

    @Override
    public Result<Page<UserBasicPageVO>> searchUserBasicPage(UserBasicPageDTO dto) {
        return Result.success(userService.searchUserBasicPage(dto));
    }

    @Override
    public Result<UserInfoVO> saveOrUpdateUser(UserDetailSaveOrUpdateDTO dto) {
        return Result.success(userService.saveOrUpdateUser(dto));
    }

    @Override
    public Result<List<RoleVO>> getRoleList(RoleListPlatformDTO platform) {
        List<RoleEntity> roleEntities = roleService.list(new LambdaQueryWrapper<RoleEntity>()
                .eq(RoleEntity::getPlatform, platform.getPlatform())
                .eq(RoleEntity::getDeleteFlag, 0));
        List<RoleVO> roleVOS = roleEntities.stream().map(roleEntity -> {
            RoleVO roleVO = new RoleVO();
            roleVO.setId(roleEntity.getId());
            roleVO.setRole(roleEntity.getRole());
            roleVO.setName(roleEntity.getRoleName());
            roleVO.setDescribe(roleEntity.getDescription());
            return roleVO;
        }).toList();
        return Result.success(roleVOS);
    }

    @Override
    public Result<DeptDetailVO> getDeptDetail(DeptDetailDTO detailDTO) {
        return Result.success(deptService.getDeptDetail(detailDTO.getDeptId()));
    }

    @Override
    public Result<List<DeptDetailVO>> getDeptDetailVO(String deptName) {
        return Result.success(deptService.getDeptDetailVO(deptName));
    }

    @Override
    public List<RegionInfoVO> regionList() {

        return deptService.regionList();

    }

    @Override
    public List<UserDingDeptMappingVO> getUserDingDeptMapping(UserDingDeptMappingDTO userDingDeptMappingDTO) {
        return userService.getUserDingDeptMapping(userDingDeptMappingDTO);

    }

    /**
     * 通过部门名称获取部门id
     */
    @Override
    public Result<DeptDetailVO> getDeptDetailVOByDeptName(String deptName) {
        DeptDetailVO deptDetailVO = deptService.getDeptIdByDeptName(deptName);
        return Result.success(deptDetailVO);
    }

    @Override
    public Result<List<DeptDetailVO>> getDeptDetailListVOByDeptName(List<String> deptNames) {
        List<DeptDetailVO> deptDetailVOs = deptService.getDeptIdByDeptNames(deptNames);
        return Result.success(deptDetailVOs);
    }

    /**
     * 按部门id获取子部门Id列表
     *
     * @param deptIds@return {@link Result }<{@link List }<{@link Integer }>>
     */
    @Override
    public Result<List<DeptEntityVO>> getChildNameDeptById(List<Integer> deptIds) {
        return Result.success(deptService.getChildNameDeptById(deptIds));
    }

    @Override
    public Result<DeptDetailVO> getDeptDetailVOByjdDeptId(String deptId) {
        return Result.success(deptService.getDeptDetailVOByjdDeptId(deptId));
    }

    @Override
    public Result<List<DeptSyncInfoVO>> getJinDieSyncDeptByLhDeptIds(SearchDeptSyncInfoDTO searchDeptSyncInfoDTO) {
        return Result.success(deptService.getJinDieSyncDeptByLhDeptIds(searchDeptSyncInfoDTO));
    }

    @Override
    public Result<UserDetailInfoVO> searchUserDetailByName(String name) {
        UserDetailInfoVO userDetailInfoVO = userService.searchDetailByName(name);
        return Result.success(userDetailInfoVO);
    }

    @Override
    public Result<List<DeptSyncInfoVO>> getSyncDeptByUserId(@RequestBody SearchDeptSyncInfoDTO dto) {
        return Result.success(deptService.getSyncDeptByUserId(dto));
    }

    /**
     * 部门列表
     *
     * @return 实例对象
     */
    @Override
    public Result<List<Tree<Integer>>> getRegionListInfo(@RequestBody RegionListDTO regionListDTO) {
        return Result.success(deptService.getRegionList(regionListDTO));
    }

    /**
     * 贷后获取部门列表
     *
     * @return 实例对象
     */
    @Override
    public Result<List<Tree<Integer>>> getRegionListInfos() {
        return Result.success(deptService.getRegionLists());
    }

    /**
     * 根据用户获取部门权限
     *
     * @return 实例对象
     */
    @Override
    public Result<List<Integer>> getUserDeptAssignmentAll(@RequestParam("userId") Integer userId) {
        return Result.success(userService.getUserDeptAssignmentAll(userId));
    }

    /**
     * 根据部门id获取用户信息
     */
    @Override
    public Result<List<UserDeptRoleVO>> getUserDeptAssignmentAllByDeptId(@RequestBody DeptAssetUserInfoByDeptIdDTO dto) {
        return Result.success(userService.getUsersByDeptIdsAndRoleIds(dto.getDeptIdList(), dto.getRoleIdList()));
    }


    @GetMapping("/infoByRoleId")
    @ApiOperation("按角色 ID 获取用户信息")
    public Result<List<UserInfoVO>> infoByRoleId(@RequestParam("roleId") Integer roleId) {
        return Result.success(userService.getUserInfoByRoleId(roleId));
    }

    @Override
    @GetMapping("/user/getIdNumberCardByUserId")
    @ApiOperation("按角色 ID 获取用户信息")
    public Result<String> getIdNumberCardByUserId(@RequestParam("userId") Integer userId) {
        return Result.success(userService.getIdNumberCardByUserId(userId));
    }

    @Override
    public Result<Page<GetDutyLoginVO>> getDutyLogin(GetDutyLoginDTO dto) {
        return Result.success(userService.getDutyLogin(dto));
    }

    @Override
    public Result<Boolean> isDuty(Integer userId) {
       return Result.success(userService.isDuty(userId));
    }
    /**
     * 三方处置公司列表
     *
     * @param threePartyDeptDTO
     * @return
     */
    @PostMapping("/getThreePartyDeptList")
    public Result<Page<DisposalCompanyVO>> getThreePartyDeptList(@RequestBody ThreePartyDeptDTO threePartyDeptDTO) {
        return Result.success(deptService.getThreePartyDeptList(threePartyDeptDTO));
    }


    @Override
    public Result<List<Integer>> exitUserByRoleIds(@RequestBody SearchUserExitRoleIdListDTO dto) {
        return Result.success(userService.exitUserByRoleIds(dto));
    }

    @Override
    public Result<List<Integer>> getDeptIdListByType(@RequestBody SearchDeptListByTypeDTO dto) {
        return Result.success(deptService.getDeptIdListByType(dto));
    }
    @Override
    public Result<Map<Integer,List<UserInfoVO>>> getUserIdsByRoleIdsAndDeptIds(DeptUsersByRoleIdsDTO deptUsersByRoleIdsDTO){
        return Result.success(deptService.getUserIdsByRoleIdsAndDeptIds(deptUsersByRoleIdsDTO));
    };
    //电催专员添加委外专员
    @PostMapping("/addoutsideUser")
    public void  addoutsideUser(Integer role1,Integer role2){
        userService.addoutsideUser(role1,role2);
       return;
    }

    @Override
    public Result<List<Integer>> getBranchDeptById(Integer deptId) {
        return Result.success(deptService.getBranchDeptById(deptId));
    }

    @Override
    public Result<List<Integer>> getChildStoreById(Integer deptId) {
        return Result.success(deptService.getChildStoreById(deptId));
    }

    @Override
    public Result<Boolean> sendByUserId(Integer userId, String message) {
        String prefix="userId:"+userId+"_";
        return Result.success(sseRemoteService.sendByPrefix(prefix, message));
    }

    @Override
    public Result<Boolean> existsByUserId(Integer userId) {
        String prefix="userId:"+userId+"_";
        return Result.success(sseRemoteService.existsByPrefix(prefix));
    }

    @Override
    public Result<Boolean> sendEventByUserId(Integer userId, String event) {
        String prefix="userId:"+userId+"_";
        return Result.success(sseRemoteService.sendEvent(prefix,event));
    }

    @Override
    public Result<Boolean> sendEventWithDataByUserId(Integer userId, String event, String message) {
        String prefix="userId:"+userId+"_";
        return Result.success(sseRemoteService.sendEventWithDataByPrefix(prefix,event,message));
    }

    @Override
    public Result<Boolean> updateUserDeleteFlag(Integer userId, Integer deleteFlag) {
        return Result.success(userService.updateUserDeleteFlag(userId, deleteFlag));
    }
}
