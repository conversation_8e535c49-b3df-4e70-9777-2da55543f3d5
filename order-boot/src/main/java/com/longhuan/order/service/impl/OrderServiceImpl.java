package com.longhuan.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.query.MPJLambdaQueryWrapper;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import com.longhuan.approve.api.pojo.dto.FundAmountChangeDTO;
import com.longhuan.approve.api.pojo.dto.FundApproveCancelDTO;
import com.longhuan.approve.api.pojo.dto.FundApproveFinalDTO;
import com.longhuan.auto.loan.pojo.dto.SearchAssetLoanInfoLimitDTO;
import com.longhuan.auto.loan.pojo.dto.gps.GpsDataDTO;
import com.longhuan.auto.loan.pojo.vo.AssetLoanInfoLimitVO;
import com.longhuan.auto.loan.pojo.vo.gps.GpsDataVO;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.MsgConstants;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.core.util.QrCodeUtils;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.redis.util.DictUtils;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.data.api.pojo.dto.PlatformRelatedOrderDTO;
import com.longhuan.data.api.pojo.vo.PlatformRelatedOrderVO;
import com.longhuan.order.constants.DingTaskConstants;
import com.longhuan.order.converter.*;
import com.longhuan.order.enums.*;
import com.longhuan.order.feign.*;
import com.longhuan.order.kingdee.Kingdee;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.MenuVO;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.*;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.resource.pojo.dto.ResourceFileCodeDTO;
import com.longhuan.risk.pojo.dto.GetUserMarriageDTO;
import com.longhuan.risk.pojo.dto.xueli.QueryPolicyVerifyV4DTO;
import com.longhuan.risk.pojo.dto.xueli.QueryVerifyV4DTO;
import com.longhuan.risk.pojo.vo.FactorVO;
import com.longhuan.risk.pojo.vo.FhldDataVO;
import com.longhuan.risk.pojo.vo.RiskCreditFeatureVO;
import com.longhuan.user.enums.DingTaskConclusionEnum;
import com.longhuan.user.enums.UserTypeEnum;
import com.longhuan.user.pojo.dto.*;
import com.longhuan.user.pojo.vo.*;
import feign.FeignException;
import io.micrometer.common.util.StringUtils;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.longhuan.common.core.result.ResultCode.SUCCESS;
import static com.longhuan.order.constants.ContractApprovalConstants.*;

/**
 * 订单表业务接口实现
 *
 * <AUTHOR>
 * @Date 2024/7/4
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderServiceImpl extends ServiceImpl<OrderInfoMapper, OrderInfoEntity> implements OrderService {
    private static final String LOCK_KEY_PREFIX = "order:distribution:lock:";
    private static final long LOCK_TIMEOUT = 10000;
    private final OrderInfoMapper orderInfoMapper;
    private final OrderConverter orderConverter;
    private final OrderPageInfoService orderPageInfoService;
    private final UserFeign userFeign;
    private final OrderCustomerInfoMapper orderCustomerInfoMapper;
    private final FundInfoMapper fundInfoMapper;
    private final PreApprovalFddAuthMapper preApprovalFddAuthMapper;
    private final RiskFeign riskFeign;
    private final OrderNodeRecordMapper orderNodeRecordMapper;
    private final XueliService xueliService;
    private final PreApprovalApplyInfoMapper preApprovalApplyInfoMapper;
    private final RiskCustomerBlacklistService riskCustomerBlacklistService;
    private final PreFundInfoService preFundInfoService;
    private final ManageBankAccountSignMapper manageBankAccountSignMapper;
    private final DataPermissionService dataPermissionService;
    private final FinalFundInfoService finalFundInfoService;
    private final EnvUtil envUtil;
    private final OrderAmountMapper orderAmountMapper;
    private final FundRepaymentInfoService fundRepaymentInfoService;
    private final FundRepaymentDeductService fundRepaymentDeductService;
    private final BankAccountSignMapper bankAccountSignMapper;
    private final OrderSendMessageImpl orderSendMessageImpl;
    private final OrderVehicleInfoMapper orderVehicleInfoMapper;
    private final OrderVehicleInfoConverter orderVehicleInfoConverter;
    private final OrderCustomerInfoConverter orderCustomerInfoConverter;
    private final BankAccountSignConverter bankAccountSignConverter;
    private final OrderContactPersonMapper orderContactPersonMapper;
    private final ProductInfoMapper productInfoMapper;
    private final OrderCompanyInfoConverter orderCompanyInfoConverter;
    private final OrderCompanyInfoMapper orderCompanyInfoMapper;
    private final ApproveFeign approveFeign;
    private final OrderContractMapper orderContractMapper;
    private final OrderFileMenuServiceImpl orderFileMenuServiceImpl;
    private final ContractFileService contractFileService;
    private final OrderFeeDetailService orderFeeDetailService;
    private final RiskCreditFeatureMapper riskCreditFeatureMapper;
    private final OverdueWarnConfigService overdueWarnConfigService;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final RepaymentService repaymentService;
    private final OrderServicePrivateMethod orderServicePrivateMethod;
    private final RedisService redisService;
    private final OrderVehicleInfoService orderVehicleInfoService;
    private final OrderDetailsInfoService orderDetailsInfoService;
    private final FundSignInfoService fundSignInfoService;
    private final OrderTerminalConfigMapper orderTerminalConfigMapper;
    private final CustomerInfoMaintainMapper customerInfoMaintainMapper;
    private final DataFeign dataFeign;
    private final PreOcrIdentityCardService preOcrIdentityCardService;
    private final CustomerSignInfoMapper customerSignInfoMapper;
    private final ResourceFeign resourceFeign;
    private final OrderFileMapper orderFileMapper;
    private final OrderContractUserPermissionMapper orderContractUserPermissionMapper;
    private final DingTaskFeign dingTaskFeign;
    private final SwitchUtils switchUtils;
    private final OrderApprovalReasonStagingMapper orderApprovalReasonStagingMapper;
    private final RepurchaseRepaymentInfoMapper repurchaseRepaymentInfoMapper;
    private final CaseInfoEntityMapper caseInfoEntityMapper;
    private final OrderFeeInfoMapper orderFeeInfoMapper;
    private final DictUtils dictUtils;
    private final OrderCompanyInfoService orderCompanyInfoService;
    private final MessageFeign messageFeign;
    private final LoanRepaymentFeign loanRepaymentFeign;
    private final OrderProcessService orderProcessService;
    private final OrderCopyService orderCopyService;
    private final LoanRepaymentFeign autoLoanRepaymentFeign;


    @Value("${wechat.message.base-url}")
    private String baseUrl;
    @Value("${wechat.message.customer-confirm-url}")
    private String customerConfirmUrl;
    @Value("${wechat.message.customer-bankcard-url}")
    private String reBindCardPath;
    @Value("${wechat.message.customer-appointment-url}")
    private String reSignPath;
    @Value("${wechat.message.customer-transfer-url}")
    private String reTransferPagePath;
    @Value("${wechat.message.customer-supplemental-url}")
    private String supplementalUrl;
    @Value("${app.hostname}")
    private String hostname;


    private static LocalDateTime getTimeOfEnd(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.MAX);
    }

    private static LocalDateTime getTimeOfStart(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.MIN);
    }

    private static LocalDateTime getTimeOfEnd(OrderInfoDTO orderInfoDTO) {
        return LocalDateTime.of(orderInfoDTO.getApplyEndDate(), LocalTime.MAX);
    }

    private static LocalDateTime getTimeOfStart(OrderInfoDTO orderInfoDTO) {
        return LocalDateTime.of(orderInfoDTO.getApplyEndDate(), LocalTime.MIN);
    }

    @Override
    public Page<OrderApproveListVO> pageApproveList(OrderApproveDTO orderApproveDTO, LoginUser loginUser) {
        log.info("pageApproveList loginUser = {}", JSONUtil.toJsonStr(loginUser));

        //查询签约客服
        List<Integer> orderIdBySignCustomerIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(orderApproveDTO.getSignCustomerName())) {
            //根据名称匹配签约客服id
            List<Integer> customerIdList = userFeign.getUserIdByLikeNameList(orderApproveDTO.getSignCustomerName()).getData();

            List<OrderNodeRecordEntity> orderBySignCustomerIdList = null;
            if (CollUtil.isNotEmpty(customerIdList)) {
                orderBySignCustomerIdList = orderNodeRecordMapper.selectList(
                        new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                .select(OrderNodeRecordEntity::getOrderId, OrderNodeRecordEntity::getUpdateBy, OrderNodeRecordEntity::getCreateTime)
                                .apply("id IN (SELECT id FROM (SELECT order_id,id, ROW_NUMBER() OVER (PARTITION BY order_id ORDER BY create_time DESC) as rn FROM lh_order_node_record WHERE current_node = {0}) subquery WHERE rn = 1)",
                                        States.PAYMENT_APPLY_INFORMATION.getNode())
                                .in(OrderNodeRecordEntity::getUpdateBy, customerIdList));
            }
            if (CollUtil.isEmpty(orderBySignCustomerIdList)) {
                return new Page<>(orderApproveDTO.getPageNum(), orderApproveDTO.getPageSize(), 0);
            }
            //获取每个的最新一条
            orderIdBySignCustomerIdList = orderBySignCustomerIdList.stream().map(OrderNodeRecordEntity::getOrderId).toList();
        }

        //客户经理名称获取客户经理id
        List<Integer> name2ManagerIdList = new ArrayList<>();
        if (StrUtil.isNotBlank(orderApproveDTO.getManagerName())) {
            name2ManagerIdList = userFeign.getUserIdByLikeNameList(orderApproveDTO.getManagerName()).getData();
            if (CollUtil.isEmpty(name2ManagerIdList)) {
                return new Page<>(orderApproveDTO.getPageNum(), orderApproveDTO.getPageSize(), 0);
            }
        }

        List<Integer> repaymentDateOrderIdList = Lists.newArrayList();
        //还款日期在指定日期区间的订单id
        if (null != orderApproveDTO.getRepaymentStartDate()
                && null != orderApproveDTO.getRepaymentEndDate()) {
            repaymentDateOrderIdList = repaymentService.betweenRepaymentDateOrderIdList(orderApproveDTO.getRepaymentStartDate(), orderApproveDTO.getRepaymentEndDate());
            //为空的话,直接返回空
            if (CollUtil.isEmpty(repaymentDateOrderIdList)) {
                return new Page<>(orderApproveDTO.getPageNum(), orderApproveDTO.getPageSize(), 0);
            }
        }

        //贷后查询资管客服条件需要添加条件
        List<AssetLoanInfoLimitVO> assetLoanInfo = null;
        List<Integer> assetLoanInfoOrderIdLimitList = new ArrayList<>();
        if (ObjUtil.isNotNull(orderApproveDTO.getCurrentNode()) && (ObjUtil.equals(orderApproveDTO.getCurrentNode(), States.PAYMENT_SUCCESS.getNode()) || ObjUtil.equals(orderApproveDTO.getCurrentNode(), States.SETTLED.getNode()))
                && StrUtil.isNotBlank(orderApproveDTO.getAssetManagerSearchKey())) {
            SearchAssetLoanInfoLimitDTO searchAssetLoanInfoLimitDTO = new SearchAssetLoanInfoLimitDTO();
            searchAssetLoanInfoLimitDTO.setDataSource(1);
            searchAssetLoanInfoLimitDTO.setSearchKey(orderApproveDTO.getAssetManagerSearchKey());
            assetLoanInfo = loanRepaymentFeign.searchAssetLoanInfoLimit(searchAssetLoanInfoLimitDTO).getData();
            if (CollUtil.isEmpty(assetLoanInfo)) {
                return new Page<>(orderApproveDTO.getPageNum(), orderApproveDTO.getPageSize(), 0);
            } else {
                assetLoanInfoOrderIdLimitList = assetLoanInfo.stream()
                        .map(AssetLoanInfoLimitVO::getSourceOrderId)
                        .map(Math::toIntExact)
                        .toList();
            }
        }
        Map<Long, AssetLoanInfoLimitVO> assetLoanInfoMap = null;
        if (CollUtil.isNotEmpty(assetLoanInfo)) {
            assetLoanInfoMap = assetLoanInfo.stream().collect(Collectors.toMap(
                    AssetLoanInfoLimitVO::getSourceOrderId,
                    item -> item,
                    (existing, replacement) -> existing
            ));

        }


        //组装筛选器
        MPJLambdaWrapper<OrderInfoEntity> queryWrapper = orderProcessService.getOrderInfoEntityMPJLambdaWrapper(orderApproveDTO, name2ManagerIdList, repaymentDateOrderIdList, orderIdBySignCustomerIdList, assetLoanInfoOrderIdLimitList);

        // 权限控制
        dataPermissionService.limitOrder(loginUser, orderApproveDTO.getCurrentNode(), queryWrapper);
        log.info("pageApproveList 查询条件SQL：{}", queryWrapper.getSqlSelect());
        Page<OrderApproveListVO> pageList = orderInfoMapper.selectJoinPage(
                new Page<>(orderApproveDTO.getPageNum(), orderApproveDTO.getPageSize()),
                OrderApproveListVO.class,
                queryWrapper
        );


        List<OrderApproveListVO> records = pageList.getRecords();
        List<Integer> teamIds = records.stream().map(OrderApproveListVO::getTeamId).distinct().toList();
        Map<Integer, String> branchNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(teamIds)) {
            try {
                branchNameMap = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData().stream().filter(deptInfoVO -> deptInfoVO.getName() != null).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
            } catch (Exception e) {
                log.error("OrderServiceImpl.pageApproveList error for teamIds:{} e:{}", teamIds, e.getMessage());
            }
        }
        Map<Integer, String> finalBranchNameMap = branchNameMap;

        // 查询经办人信息
        List<Integer> manageIds = records.stream().map(OrderApproveListVO::getManagerId).filter(Objects::nonNull).toList();
        Result<List<UserStoreVO>> listResult = userFeign.searchUserStoreBatch(manageIds);
        Map<Integer, UserStoreVO> managerInfos;
        if (Result.isSuccess(listResult)) {
            managerInfos =
                    listResult.getData().stream().collect(Collectors.toMap(UserStoreVO::getUserId, item -> item));
        } else {
            managerInfos = null;
        }

        List<Integer> paymentOrSettledOrderIds = records.stream()
                .filter(record -> Objects.equals(record.getCurrentNode(), States.PAYMENT_SUCCESS.getNode()) ||
                        Objects.equals(record.getCurrentNode(), States.SETTLED.getNode()))
                .map(OrderApproveListVO::getOrderId)
                .distinct()
                .toList();

        Map<Integer, Integer> orderIdToRepayTermMap = new HashMap<>();

        if (CollUtil.isNotEmpty(paymentOrSettledOrderIds)) {
            List<FundRepaymentInfoEntity> repaymentInfoList = fundRepaymentInfoMapper.selectJoinList(FundRepaymentInfoEntity.class,
                    new MPJLambdaQueryWrapper<FundRepaymentInfoEntity>()
                            .select(FundRepaymentInfoEntity::getOrderId, FundRepaymentInfoEntity::getTerm)
                            .in(FundRepaymentInfoEntity::getOrderId, paymentOrSettledOrderIds)
                            .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                            .orderByAsc(FundRepaymentInfoEntity::getTerm)
            );

            Map<Integer, Integer> tempMap = repaymentInfoList.stream()
                    .collect(Collectors.groupingBy(FundRepaymentInfoEntity::getOrderId, Collectors.mapping(
                            FundRepaymentInfoEntity::getTerm, Collectors.reducing((a, b) -> a))
                    )).entrySet().stream().filter(e -> e.getValue().isPresent())
                    .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().get()));
            orderIdToRepayTermMap.putAll(tempMap);
        }

        // 收集所有需要查询的 orderId
        Set<Integer> orderIds = records.stream().map(OrderApproveListVO::getOrderId).collect(Collectors.toSet());
        Map<Integer, List<OrderAmountEntity>> orderAmountMap;
        if (CollUtil.isNotEmpty(records)) {

            orderAmountMap = orderAmountMapper.selectList(new LambdaQueryWrapper<OrderAmountEntity>()
                            .in(OrderAmountEntity::getOrderId, orderIds)
                            .orderByDesc(OrderAmountEntity::getCreateTime))
                    .stream()
                    .collect(Collectors.groupingBy(OrderAmountEntity::getOrderId,
                            Collectors.collectingAndThen(Collectors.toList(), list -> list.isEmpty() ? Collections.emptyList() : Collections.singletonList(list.get(0)))));
        } else {
            orderAmountMap = new HashMap<>();
        }


        //客户预约节点 ，贷款金额为客户确认金额
        //if (Objects.equals(States.CUSTOMER_APPOINTMENT.getNode(), orderApproveDTO.getCurrentNode()) && !records.isEmpty()) {

        //贷后节点 需要查询资管客服
        if (ObjUtil.isNotNull(orderApproveDTO.getCurrentNode()) && (ObjUtil.equals(orderApproveDTO.getCurrentNode(), States.PAYMENT_SUCCESS.getNode()) || ObjUtil.equals(orderApproveDTO.getCurrentNode(), States.SETTLED.getNode()))) {
            //调用资管客服查询
            SearchAssetLoanInfoLimitDTO searchAssetLoanInfoLimitDTO = new SearchAssetLoanInfoLimitDTO();
            searchAssetLoanInfoLimitDTO.setDataSource(1);
            searchAssetLoanInfoLimitDTO.setSourceOrderIdList(orderIds.stream().map(Long::valueOf).toList());
            List<AssetLoanInfoLimitVO> data = loanRepaymentFeign.searchAssetLoanInfoLimit(searchAssetLoanInfoLimitDTO).getData();
            if (CollUtil.isNotEmpty(data)) {
                assetLoanInfoMap = data.stream().collect(Collectors.toMap(
                        AssetLoanInfoLimitVO::getSourceOrderId,
                        item -> item,
                        (existing, replacement) -> existing
                ));
            }
        }


        Map<Long, AssetLoanInfoLimitVO> finalAssetLoanInfoMap = assetLoanInfoMap;
        records.forEach(record -> {

            Long sourceOrderId = Long.valueOf(record.getOrderId());
            if (finalAssetLoanInfoMap != null && finalAssetLoanInfoMap.containsKey(sourceOrderId)) {
                AssetLoanInfoLimitVO assetLoanInfoLimitVO = finalAssetLoanInfoMap.get(sourceOrderId);
                if (null != assetLoanInfoLimitVO) {
                    record.setAssetManagerName(assetLoanInfoLimitVO.getAssetManagerName());
                }
            }

//            item.setCustomerPhone(StrUtil.hide(item.getCustomerPhone(), 0, 7));

//            if (record.getCurrentNode() <= States.FUNDS_FINAL_APPROVE.getNode()) {
//                record.setAppropriationTime(null);
//            }

            if (ObjUtil.isNotNull(managerInfos) && record.getManagerId() != null) {
                UserStoreVO userInfoVOS = managerInfos.get(record.getManagerId());
                if (ObjUtil.isNotNull(userInfoVOS)) {
                    record.setManagerName(userInfoVOS.getName());
                    record.setStoreName(userInfoVOS.getStore());
                    record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(finalBranchNameMap.get(record.getTeamId()), ""));
                }

            }


            List<OrderAmountEntity> orderAmountEntities = orderAmountMap.getOrDefault(record.getOrderId(), Collections.emptyList());
            if (CollUtil.isNotEmpty(orderAmountEntities)) {
                OrderAmountEntity orderAmountEntity = orderAmountEntities.get(0);
                record.setCustomerAmount(orderAmountEntity.getCustomerConfirmAmount());
                record.setApplyAmount(orderAmountEntity.getCustomerConfirmAmount());
            }

            if (Objects.equals(record.getSourceType(), 1) && (Objects.equals(record.getRegionId(), 24) || Objects.equals(record.getRegionId(), 56))) {
                record.setOnlineOrder(1);
            } else {
                record.setOnlineOrder(0);
            }
            if (Objects.equals(record.getCurrentNode(), States.PAYMENT_SUCCESS.getNode()) || Objects.equals(record.getCurrentNode(), States.SETTLED.getNode())) {

                if (ObjUtil.isNotNull(orderIdToRepayTermMap)) {
                    Integer term = orderIdToRepayTermMap.get(record.getOrderId());
                    if (term != null) {
                        record.setRepayTerm(term);
                    }
                }
            }
            if (ObjUtil.isNotNull(record.getState()) && ObjUtil.isNotNull(record.getCurrentNode())
                    && Objects.equals(record.getState(), States.SETTLED.getNode()) && Objects.equals(record.getCurrentNode(), States.SETTLED.getNode())) {
                record.setOrderStatus(8);
            } else {
                Integer planState = record.getPlanState();
                if (planState == null) {
                    planState = 0;
                }
                if (0 == planState && 0 == record.getIsOverdue()) {
                    record.setOrderStatus(0);
                } else if (0 == planState && 1 == record.getIsOverdue()) {
                    record.setOrderStatus(1);
                }
            }
        });
        //}
        // 补录状态订单展示退回详情
        if (CollUtil.isNotEmpty(records)) {
            List<Integer> orderIdList = records.stream().filter(record -> Objects.equals(States.BUSINESS_ADDED_INFO.getNode(), record.getCurrentNode()))
                    .map(OrderApproveListVO::getOrderId).toList();
            if (CollUtil.isNotEmpty(orderIdList)) {
                Map<Integer, List<OrderNodeRecordEntity>> returnReasonMap = orderNodeRecordMapper.selectList(
                                new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                        .in(OrderNodeRecordEntity::getOrderId, orderIdList)
                                        .eq(OrderNodeRecordEntity::getNextNode, States.BUSINESS_ADDED_INFO.getNode())
                                        .orderByDesc(OrderNodeRecordEntity::getCreateTime)).stream()
                        .collect(Collectors.groupingBy(OrderNodeRecordEntity::getOrderId));
                log.info("pageApproveList returnReason order {}", returnReasonMap.keySet());
                records.stream().filter(record -> Objects.equals(States.BUSINESS_ADDED_INFO.getNode(), record.getCurrentNode()))
                        .filter(record -> returnReasonMap.containsKey(record.getOrderId()))
                        .forEach(
                                record -> returnReasonMap.get(record.getOrderId()).stream()
                                        .findFirst().ifPresent(reason -> {
                                                    record.setReturnReason(reason.getRemarkExternal());
                                                    record.setIsReturn(1);
                                                }
                                        )
                        );
            }
        }


        if (Objects.equals(States.RISK_FIRST_APPROVE_ASSIGN.getNode(), orderApproveDTO.getCurrentNode())) {
            List<OrderApproveListVO> records1 = pageList.getRecords();
            for (OrderApproveListVO orderApproveListVO : records1) {
                //                OrderNodeRecordEntity orderNodeRecordEntity = orderNodeRecordMapper.selectOne(new LambdaQueryWrapper<OrderNodeRecordEntity>()
                //                        .eq(OrderNodeRecordEntity::getOrderId, orderApproveListVO.getOrderId())
                //                        .eq(OrderNodeRecordEntity::getNextNode, orderApproveListVO.getCurrentNode())
                //                        .eq(OrderNodeRecordEntity::getDeleteFlag, 0)
                //                        .orderByDesc(OrderNodeRecordEntity::getCreateTime)
                //                        .last("limit 1"));
                //
                //                if(orderNodeRecordEntity != null){
                //                    orderApproveListVO.setApprovalSubmitTime(orderNodeRecordEntity.getCreateTime());
                //                }
                if (orderApproveListVO.getRisiUserId() != null && ObjUtil.equals(orderApproveListVO.getCurrentNode(), States.RISK_FIRST_APPROVE.getNode())) {
                    Result<UserDetailInfoVO> userDetailInfoVOResult = userFeign.searchUserDetailById(orderApproveListVO.getRisiUserId());
                    if (Result.isSuccess(userDetailInfoVOResult) && userDetailInfoVOResult.getData() != null) {
                        UserDetailInfoVO userDetailInfoVO = userDetailInfoVOResult.getData();
                        orderApproveListVO.setRisiUserName(userDetailInfoVO.getName());
                    }
                }
            }

            // 按照审批人员（如果有）以及提交审批时间升序排序
            pageList.setRecords(records.stream()
                    .sorted(Comparator
                            // 1. 首先按照是否有审批人员排序（有审批人员的在前）
                            .<OrderApproveListVO, Boolean>comparing(order -> order.getRisiUserName() != null, Comparator.reverseOrder())
                            // 2. 最后按照提交审批时间升序排序
                            .thenComparing(OrderApproveListVO::getApprovalSubmitTime, Comparator.nullsFirst(Comparator.naturalOrder()))
                    )
                    .toList());


        } else if (Objects.equals(orderApproveDTO.getCurrentNode(), States.PAYMENT_CONTRACT_APPROVAL.getNode())) {
            log.info("pageApproveList returnReason order {}", records);
            // 按照审批人员（如果有）以及提交审批时间升序排序
            pageList.setRecords(records.stream()
                    .sorted(Comparator.comparing(OrderApproveListVO::getApprovalSubmitTime))
                    .toList());


            /*List<Integer> list = records.stream().map(OrderApproveListVO::getOrderId).toList();
            List<OrderNodeRecordEntity> orderNodeRecordEntities = orderNodeRecordMapper.selectList(
                    Wrappers.<OrderNodeRecordEntity>lambdaQuery()
                            .in(OrderNodeRecordEntity::getOrderId, list)
                            .eq(OrderNodeRecordEntity::getLastNode, States.PAYMENT_CONTRACT_APPROVAL.getNode())
                            .eq(OrderNodeRecordEntity::getDeleteFlag, 0)
                            .groupBy(OrderNodeRecordEntity::getOrderId)
                            .orderByDesc(OrderNodeRecordEntity::getUpdateTime)
            );*/
            //todo 历史审批人
            /*log.info("pageApproveList returnReason order {}", orderNodeRecordEntities);*/


            //获取上一个合同审批节点审批人（历史审批人）
            records.stream().forEach(i -> {
                OrderNodeRecordEntity orderNodeRecordEntity = orderNodeRecordMapper.selectOne(
                        Wrappers.<OrderNodeRecordEntity>lambdaQuery()
                                .eq(OrderNodeRecordEntity::getLastNode, States.PAYMENT_CONTRACT_APPROVAL.getNode())
                                .eq(OrderNodeRecordEntity::getOrderId, i.getOrderId())
                                .eq(OrderNodeRecordEntity::getDeleteFlag, 0)
                                .orderByDesc(OrderNodeRecordEntity::getUpdateTime)
                                .last("limit 1"));
                if (orderNodeRecordEntity != null) {
                    Result<UserDetailInfoVO> userDetailInfoVOResult = userFeign.searchUserDetailById(orderNodeRecordEntity.getUpdateBy());
                    if (userDetailInfoVOResult.getData() != null) {
                        String name = userDetailInfoVOResult.getData().getName();
                        i.setHistoryApprover(name);
                    }
                }
            });


        }

        // 订单展示签约客服
        if (CollUtil.isNotEmpty(records)) {
            if (CollUtil.isNotEmpty(orderIds)) {
                Map<Integer, List<OrderNodeRecordEntity>> returnReasonMap = orderNodeRecordMapper.selectList(
                                new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                        .in(OrderNodeRecordEntity::getOrderId, orderIds)
                                        .eq(OrderNodeRecordEntity::getCurrentNode, States.PAYMENT_APPLY_INFORMATION.getNode())
                                        .orderByDesc(OrderNodeRecordEntity::getCreateTime)).stream()
                        .collect(Collectors.groupingBy(OrderNodeRecordEntity::getOrderId));
                List<Integer> signCustomerIdList = new ArrayList<>();
                records.stream()
                        .filter(record -> returnReasonMap.containsKey(record.getOrderId()))
                        .forEach(
                                record -> returnReasonMap.get(record.getOrderId()).stream()
                                        .findFirst().ifPresent(reason -> {
                                                    record.setSignCustomerId(reason.getUpdateBy());
                                                    signCustomerIdList.add(reason.getUpdateBy());
                                                }
                                        )
                        );
                List<UserInfoVO> data = userFeign.searchUserNameBatch(signCustomerIdList).getData();
                Map<Integer, String> signCustomerNameMap = data.stream().collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getName, (v1, v2) -> v1));


                records.forEach(record -> record.setSignCustomer(signCustomerNameMap.get(record.getSignCustomerId()))
                );
            }
        }

        //因订单状态需要进行逻辑判断,所以需舍弃分页,否则没办法进行筛选,故做如下操作。
//        List<OrderApproveListVO> resultRecordsList = orderInfoMapper.selectJoinList(OrderApproveListVO.class, queryWrapper);
//        //填充订单状态
//        resultRecordsList.forEach(this::setOrderStatus);
//        //过滤订单状态
//        if (null != orderApproveDTO.getOrderStatus()) {
//
//            int offset = (orderApproveDTO.getPageNum() - 1) * orderApproveDTO.getPageSize();
//            int resultListSize = resultRecordsList.stream().filter(v ->
//                    Objects.equals(orderApproveDTO.getOrderStatus(), v.getOrderStatus())).toList().size();
//            //模拟分页
//            List<OrderApproveListVO> filterList = resultRecordsList.stream().filter(v ->
//                            Objects.equals(orderApproveDTO.getOrderStatus(), v.getOrderStatus()))
//                    .skip(offset)
//                    .limit(orderApproveDTO.getPageSize()).toList();
//            pageList.setRecords(filterList);
//            pageList.setTotal(resultListSize);
//        }
        return pageList;
    }


    /**
     * 填充订单状态
     *
     * @param v
     */
    private void setOrderStatus(OrderApproveListVO v) {
        //还款中
        if (null != v.getPlanState() && 0 == v.getPlanState() && 0 == v.getIsOverdue()) {
            v.setOrderStatus(0);
        } else if (null != v.getPlanState() && 0 == v.getPlanState() && 1 == v.getIsOverdue()) {
            v.setOrderStatus(1);
        } else {
            //非委外
            if (0 == v.getIsPushThirdParty()) {
                //未赎回,查询资方还款计划表
                if (0 == v.getIsRepurchase()) {
                    //查询结清数据
                    FundRepaymentInfoEntity fundRepaymentInfoEntity = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                            .eq(FundRepaymentInfoEntity::getOrderId, v.getOrderId())
                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                            .eq(FundRepaymentInfoEntity::getFundId, v.getFundId())
                            .eq(FundRepaymentInfoEntity::getEventStatus, 1)
                            .orderByAsc(FundRepaymentInfoEntity::getTerm)
                            .last("limit 1"));

                    if (null != fundRepaymentInfoEntity) {
                        //提前结清
                        if (fundRepaymentInfoEntity.getTerm() < v.getTerm()
                                && 0 == fundRepaymentInfoEntity.getIsOverdue()) {
                            v.setOrderStatus(2);
                        }

                        //逾期结清
                        if (fundRepaymentInfoEntity.getTerm() <= v.getTerm()
                                && 1 == fundRepaymentInfoEntity.getIsOverdue()) {
                            v.setOrderStatus(3);
                        }

                        //到期结清
                        if (Objects.equals(v.getTerm(), fundRepaymentInfoEntity.getTerm())
                                && 0 == fundRepaymentInfoEntity.getIsOverdue()) {
                            v.setOrderStatus(4);
                        }
                    }

                }
                //全部赎回
                else if (1 == v.getIsRepurchase()) {
                    //查询结清数据
                    RepurchaseRepaymentInfoEntity repurchaseRepaymentInfoEntity = repurchaseRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                            .eq(RepurchaseRepaymentInfoEntity::getOrderId, v.getOrderId())
                            .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                            .eq(RepurchaseRepaymentInfoEntity::getFundId, v.getFundId())
                            .eq(RepurchaseRepaymentInfoEntity::getEventStatus, 1)
                            .orderByAsc(RepurchaseRepaymentInfoEntity::getTerm)
                            .last("limit 1"));

                    if (null != repurchaseRepaymentInfoEntity) {
                        //提前结清
                        if (v.getTerm() < repurchaseRepaymentInfoEntity.getTerm()
                                && 0 == repurchaseRepaymentInfoEntity.getIsOverdue()) {
                            v.setOrderStatus(2);
                        }

                        //逾期结清
                        if (v.getTerm() <= repurchaseRepaymentInfoEntity.getTerm()
                                && 1 == repurchaseRepaymentInfoEntity.getIsOverdue()) {
                            v.setOrderStatus(3);
                        }

                        //到期结清
                        if (Objects.equals(v.getTerm(), repurchaseRepaymentInfoEntity.getTerm())
                                && 0 == repurchaseRepaymentInfoEntity.getIsOverdue()) {
                            v.setOrderStatus(4);
                        }
                    }
                }
            }
            //委外单
            else {
                CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                        .eq(CaseInfoEntity::getOrderId, v.getOrderId())
                        .eq(CaseInfoEntity::getCurrentNode, 30)
                        .eq(CaseInfoEntity::getDeleteFlag, 0));
                if (null != caseInfoEntity) {
                    //2=出售结清
                    Integer transferStatus = caseInfoEntity.getTransferStatus();
                    //3=保全结清
                    Integer preservationResult = caseInfoEntity.getPreservationResult();

                    //保全结清
                    if (null != transferStatus && 2 == transferStatus) {
                        v.setOrderStatus(6);
                    }
                    //出售结清
                    if (null != preservationResult && 3 == preservationResult) {
                        v.setOrderStatus(7);
                    }
                    //todo:委外结清
                }
            }
        }
    }


    @Override
    public IPage<OrderInfoListVO> pageOrderList(OrderInfoDTO orderInfoDTO, LoginUser loginUser) {
        Page<OrderInfoListVO> page = new Page<>(orderInfoDTO.getPageNum(), orderInfoDTO.getPageSize());
        MPJLambdaWrapper<OrderInfoEntity> wrapper = new MPJLambdaWrapper<>();

        // 通过参数化避免SQL注入
        wrapper.selectAll(OrderInfoEntity.class)
                .selectAs(OrderCustomerInfoEntity::getAge, OrderInfoListVO::getAge)
                .selectAs(OrderCustomerInfoEntity::getGender, OrderInfoListVO::getGender)
                .selectAs(OrderVehicleInfoEntity::getVehicleNumber, OrderInfoListVO::getVehicleNumber)
                .selectAs(OrderInfoEntity::getEstimateAmount, OrderInfoListVO::getEstimateAmount)
                .selectAs(OrderInfoEntity::getContractState, OrderInfoListVO::getContractState)
                .selectAs(OrderInfoEntity::getCreateTime, OrderInfoListVO::getApplyDate)
                .selectAs(OrderInfoEntity::getId, OrderInfoListVO::getOrderId)
                .selectAs(OrderInfoEntity::getManagerId, OrderInfoListVO::getManagerId)
                .selectAs(OrderAmountEntity::getCustomerConfirmAmount, OrderInfoListVO::getApplyAmount)
                .selectAs(CustomerMortgageInfoEntity::getMortgageType, OrderInfoListVO::getMortgageType)
                .selectAs(FundUndoMortgageInfoEntity::getMortgageStatus, OrderInfoListVO::getReleaseMortgageState)
                .selectAs(OrderFeeInfoEntity::getGpsFeeStatus, OrderInfoListVO::getGpsFeeStatus)
                .selectAs(OrderInfoEntity::getRegionId, OrderInfoListVO::getRegionId)
                .selectAs(OrderInfoEntity::getSourceType, OrderInfoListVO::getSourceType)
                .selectAs(OrderInfoEntity::getRegionName, OrderInfoListVO::getRegionName)
                .selectAs(PreFundInfoEntity::getFundCreditTime, OrderInfoListVO::getFundCreditTime)
                .leftJoin(PreFundInfoEntity.class, on ->
                        on.eq(PreFundInfoEntity::getPreId, OrderInfoEntity::getPreId)
                                .eq(PreFundInfoEntity::getFundId, OrderInfoEntity::getFundId)
                                .eq(PreFundInfoEntity::getDeleteFlag, 0)
                )
                .leftJoin(OrderCustomerInfoEntity.class, OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId)
                .leftJoin(OrderVehicleInfoEntity.class, OrderVehicleInfoEntity::getOrderId, OrderInfoEntity::getId)
                .leftJoin(OrderAmountEntity.class, OrderAmountEntity::getOrderId, OrderInfoEntity::getId)
                .leftJoin(FundUndoMortgageInfoEntity.class, on ->
                        on.eq(FundUndoMortgageInfoEntity::getOrderId, OrderInfoEntity::getId)
                                .and(qw -> {
                                    qw.eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                                            .or()
                                            .isNull(FundUndoMortgageInfoEntity::getDeleteFlag);
                                })
                )
                .leftJoin(CustomerMortgageInfoEntity.class, on ->
                        on.eq(CustomerMortgageInfoEntity::getOrderId, OrderInfoEntity::getId)
                                .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0))
                .leftJoin(OrderFeeInfoEntity.class, on ->
                        on.eq(OrderFeeInfoEntity::getOrderId, OrderInfoEntity::getId)
                                .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                                .eq(OrderFeeInfoEntity::getFeeType, 1))
//                .ne(OrderInfoEntity::getCurrentNode, States.BUSINESS_ADDED_INFO.getNode())
                .ne(ObjUtil.isNotEmpty(orderInfoDTO.getOrderState()) && orderInfoDTO.getOrderState() == 1,
                        OrderInfoEntity::getCurrentNode, States.PAYMENT_SUCCESS.getNode())
                .eq(ObjUtil.isNotEmpty(orderInfoDTO.getOrderState()) && orderInfoDTO.getOrderState() == 2,
                        OrderInfoEntity::getCurrentNode, States.PAYMENT_SUCCESS.getNode());

        if (orderInfoDTO.getApplyStartDate() != null && orderInfoDTO.getApplyEndDate() == null) {
            wrapper.ge(OrderInfoEntity::getCreateTime, getTimeOfStart(orderInfoDTO.getApplyStartDate()));
        }
        if (orderInfoDTO.getApplyStartDate() == null && orderInfoDTO.getApplyEndDate() != null) {
            wrapper.le(OrderInfoEntity::getCreateTime, getTimeOfEnd(orderInfoDTO.getApplyEndDate()));
        }
        if (orderInfoDTO.getApplyStartDate() != null && orderInfoDTO.getApplyEndDate() != null) {
            wrapper.between(OrderInfoEntity::getCreateTime,
                    getTimeOfStart(orderInfoDTO.getApplyStartDate()),
                    getTimeOfEnd(orderInfoDTO.getApplyEndDate()));
        }

        // 数据权限
        dataPermissionService.limitOrder(loginUser, wrapper);


        if (StrUtil.isNotBlank(orderInfoDTO.getCustomerName())) {
            wrapper.like(OrderInfoEntity::getCustomerName, orderInfoDTO.getCustomerName());
        }

        //姓名、手机号、车牌号、订单号
        if (StrUtil.isNotBlank(orderInfoDTO.getQueryMore())) {
            String queryMore = orderInfoDTO.getQueryMore().trim();
            String small = queryMore.toUpperCase();
            wrapper.and(wp -> wp.like(OrderInfoEntity::getCustomerName, queryMore)
                    .or()
                    .like(OrderInfoEntity::getCustomerPhone, queryMore)
                    .or()
                    .like(OrderInfoEntity::getOrderNumber, queryMore)
                    .or()
                    .like(OrderVehicleInfoEntity::getVehicleNumber, small)
            );
        }
        if (ObjUtil.isNotNull(orderInfoDTO.getCurrentNode())) {
            wrapper.eq(OrderInfoEntity::getCurrentNode, orderInfoDTO.getCurrentNode());
        }

        wrapper.orderByDesc(OrderInfoEntity::getCreateTime);

        IPage<OrderInfoListVO> pageList = orderInfoMapper.selectJoinPage(page, OrderInfoListVO.class, wrapper);
        if (CollUtil.isNotEmpty(pageList.getRecords())) {
            List<Integer> list = pageList.getRecords().stream().map(OrderInfoListVO::getOrderId).toList();
            Map<Integer, OrderNodeRecordEntity> nodeRecordEntityMap = orderNodeRecordMapper.selectList(
                    new MPJLambdaWrapper<OrderNodeRecordEntity>()
                            .in(OrderNodeRecordEntity::getOrderId, list)
                            .groupBy(OrderNodeRecordEntity::getOrderId)
                            .having("create_time = MAX(create_time)")
            ).stream().collect(Collectors.toMap(OrderNodeRecordEntity::getOrderId, Function.identity()));

            pageList.getRecords().forEach(orderServicePrivateMethod::processOrderItems);
            Map<Integer, String> managerNameMap = userFeign.searchUserNameBatch(pageList.getRecords().stream().map(OrderInfoListVO::getManagerId).toList())
                    .getData()
                    .stream().collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getName, (e1, e2) -> e1));

            // 批量检测订单复制条件，提高性能
            List<Integer> orderIds = pageList.getRecords().stream().map(OrderInfoListVO::getOrderId).toList();
            Map<Integer, Boolean> copyStatusMap;
            try {
                copyStatusMap = orderCopyService.batchCheckCopyCondition(orderIds);
                log.debug("批量检测订单复制条件完成，订单数量：{}", orderIds.size());
            } catch (Exception e) {
                log.warn("批量检测订单复制条件失败，将使用默认值false：{}", e.getMessage());
                copyStatusMap = new HashMap<>();
                // 使用传统 for 循环避免 lambda 表达式中修改外部变量的编译错误
                for (Integer orderId : orderIds) {
                    copyStatusMap.put(orderId, false);
                }
            }

            // 使用传统 for 循环避免 lambda 表达式中使用非 final 变量的编译错误
            for (OrderInfoListVO record : pageList.getRecords()) {
                record.setManagerName(managerNameMap.get(record.getManagerId()));
                record.setGpsFeeStatus(ObjectUtil.isEmpty(record.getGpsFeeStatus()) ? 0 : record.getGpsFeeStatus());
                record.setIsOnlineOrder(Objects.equals(record.getSourceType(), 1) && (Objects.equals(record.getRegionId(), 24) || Objects.equals(record.getRegionId(), 56)) ? 1 : 0);
                if (record.getFundCreditTime() != null &&
                        record.getCurrentNode() != 5000 &&
                        record.getCurrentNode() != -1000
                ) {
                    LocalDateTime fundCreditTime = record.getFundCreditTime();
                    LocalDateTime fifteenDaysLater = fundCreditTime.plusDays(15);
                    record.setEffectiveTime(fifteenDaysLater);
                }

                // 设置订单复制状态
                record.setCanCopy(copyStatusMap.getOrDefault(record.getOrderId(), false));
                OrderNodeRecordEntity orderNodeRecordEntity = nodeRecordEntityMap.get(record.getOrderId());
                // 设置订单状态
                if (ObjUtil.isNotNull(orderNodeRecordEntity)) {
                    DisplayStatusEnum status = DisplayStatusEnum.fromState(
                            States.getByNode(record.getCurrentNode()),
                            orderNodeRecordEntity.getNextNode()
                    );
                    if (status != null) {
                        record.setStateDesc(status.getDisplayValue());
                    }
                }
            }
        }
        return pageList;
    }


    /**
     * 根据订单ID查询订单
     *
     * @param orderId 订单ID
     * @return 订单
     */
    @Override
    public GetOrderByOrderIdVo getOrderByOrderId(Integer orderId) {
        return orderInfoMapper.selectJoinOne(GetOrderByOrderIdVo.class, new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getCurrentNode)
                .selectAs(OrderInfoEntity::getId, GetOrderByOrderIdVo::getOrderId)
                .selectAs(OrderInfoEntity::getApplyAmount, GetOrderByOrderIdVo::getApplyAmount)
                .selectAs(OrderInfoEntity::getApplyPurpose, GetOrderByOrderIdVo::getApplyPurpose)
                .selectAs(OrderInfoEntity::getRepayMethod, GetOrderByOrderIdVo::getRepayMethod)
                .selectAs(OrderInfoEntity::getTerm, GetOrderByOrderIdVo::getTerm)
                .selectAs(OrderInfoEntity::getFundId, GetOrderByOrderIdVo::getFundId)
                .selectAs(OrderInfoEntity::getProductId, GetOrderByOrderIdVo::getProductId)
                .selectAs(OrderInfoEntity::getProductName, GetOrderByOrderIdVo::getProductName)

                .selectAs(ProductInfoEntity::getGuaranteeFee, GetOrderByOrderIdVo::getGuaranteeFee)
                .selectAs(ProductInfoEntity::getDeposit, GetOrderByOrderIdVo::getDeposit)
                .selectAs(ProductInfoEntity::getMonthlyRate, GetOrderByOrderIdVo::getMonthlyRate)
                .selectAs(ProductInfoEntity::getTerm, GetOrderByOrderIdVo::getGuaranteeFeeTerm)
                .selectAs(ProductInfoEntity::getIrr, GetOrderByOrderIdVo::getYearRate)

                .selectAs(PreApprovalApplyInfoEntity::getLoanAmount, GetOrderByOrderIdVo::getLoanAmount)

                .selectAs(OrderAmountEntity::getFundPreAmount, GetOrderByOrderIdVo::getFundPreAmount)
                .select("t4.remark_external as remarkExternal")

                .leftJoin(ProductInfoEntity.class, ProductInfoEntity::getId, OrderInfoEntity::getProductId)
                .leftJoin(OrderAmountEntity.class, OrderAmountEntity::getOrderId, OrderInfoEntity::getId)
                .leftJoin(PreApprovalApplyInfoEntity.class, PreApprovalApplyInfoEntity::getId,
                        OrderInfoEntity::getPreId)
                .leftJoin(
                        "(SELECT * FROM lh_order_node_record WHERE delete_flag = 0 AND order_id = " + orderId + " AND current_node = "
                                + States.RISK_FIRST_APPROVE.getNode() + " ORDER BY ID DESC LIMIT 1) t4 on t4.order_id = t.id")//资方终审肯定会有初审时的录入信息

                .eq(OrderInfoEntity::getId, orderId)
        );
    }

    private final FundProductMappingMapper fundProductMappingMapper;

    /**
     * 保存订单信息
     *
     * @param saveOrderInfoDTO 保存订单信息DTO
     * @return 订单实体
     */
    @Override
    @Kingdee
    public OrderInfoEntity saveOrderInfo(SaveOrderInfoDTO saveOrderInfoDTO) {
        OrderInfoEntity orderInfoEntity = orderConverter.saveOrderInfoDtoToEntity(saveOrderInfoDTO);
        OrderInfoEntity orderInfo = Optional.ofNullable(orderInfoMapper.selectById(orderInfoEntity.getId())).orElseThrow(() -> new BusinessException("订单不存在"));


        //        if (saveOrderInfoDTO.getLoanAmount().compareTo(orderInfo.getApplyAmount()) > 0) {
        //            throw new BusinessException("希望金额不能超过申请金额");
        //        }

        OrderAmountEntity orderAmount = orderAmountMapper.selectOne(
                new LambdaQueryWrapper<OrderAmountEntity>()
                        .eq(OrderAmountEntity::getOrderId, orderInfoEntity.getId())
                        .eq(OrderAmountEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderAmountEntity::getCreateTime)
                , false
        );
        BigDecimal fundPreAmount = orderAmount.getFundPreAmount();
        if (fundPreAmount.compareTo(saveOrderInfoDTO.getLoanAmount()) < 0) {
            throw new BusinessException("申请金额不能高于预审金额 " + fundPreAmount + "元");
        }
        //验证富民消费贷金额
        Integer fundId = orderInfo.getFundId();
        //订单类型（0：消费贷，1：经营贷
        Integer orderType = orderInfo.getOrderType();
        if (ObjUtil.equals(FundEnum.FU_MIN.getValue(), fundId)
                && ObjUtil.equals(orderType, 0)
                && BigDecimal.valueOf(300000).compareTo(saveOrderInfoDTO.getLoanAmount()) < 0) {
            throw new BusinessException("消费贷申请金额不能超过30万");
        }
        //处理贷款类型
        finalFundInfoService.handleOrderType(fundId, orderInfoEntity, saveOrderInfoDTO.getLoanAmount());

        FundProductMappingEntity fundProductMappingEntity = fundProductMappingMapper.selectOne(
                new LambdaQueryWrapper<FundProductMappingEntity>()
                        .eq(FundProductMappingEntity::getProductId, saveOrderInfoDTO.getProductId())
                        .eq(FundProductMappingEntity::getFundId, orderInfo.getFundId())
                        .eq(FundProductMappingEntity::getDeleteFlag, 0)
                        .eq(FundProductMappingEntity::getStatus, 0)
                        .orderByDesc(FundProductMappingEntity::getCreateTime)
                        .last("limit 1"));

        BigDecimal minLoanAmount = fundProductMappingEntity.getMinLoanAmount();

        if (saveOrderInfoDTO.getLoanAmount().compareTo(minLoanAmount) < 0) {
            throw new BusinessException("希望金额不能小于" + minLoanAmount.toString() + "元");
        }

        if (Objects.nonNull(saveOrderInfoDTO.getProductId())
                && !Objects.equals(orderInfo.getProductId(), saveOrderInfoDTO.getProductId())
                && Objects.equals(orderInfo.getManagementConclusion(), 1)
                && Objects.equals(orderInfo.getFundId(), FundEnum.FU_MIN.getValue())) {
            throw new BusinessException("富民订单不支持资方终审后修改产品");
        }

        //修改产品期数
        ProductInfoEntity productInfo = productInfoMapper.selectById(saveOrderInfoDTO.getProductId());
        orderInfoEntity.setTerm(productInfo.getTerm());
        orderInfoMapper.updateById(orderInfoEntity);

        //修改预审批订单希望金额
        preApprovalApplyInfoMapper.update(new LambdaUpdateWrapper<PreApprovalApplyInfoEntity>()
                        .set(PreApprovalApplyInfoEntity::getLoanAmount, saveOrderInfoDTO.getLoanAmount())
                        //增加备注信息
//                .set(PreApprovalApplyInfoEntity::getNotes,saveOrderInfoDTO.getNotes())
                        .eq(PreApprovalApplyInfoEntity::getId, orderInfo.getPreId())
        );
        orderAmountMapper.update(new LambdaUpdateWrapper<OrderAmountEntity>()
                .set(OrderAmountEntity::getHopeAmount, saveOrderInfoDTO.getLoanAmount())
                .eq(OrderAmountEntity::getOrderId, orderInfoEntity.getId())
        );

        orderPageInfoService.updateOrderPageInfo(orderInfoEntity.getId(), States.ADDED_BUSINESS_ORDER, 1);
        return orderInfoEntity;
    }

    @Override
    public OrderHomeDataVO selectHomeData(LoginUser loginUser) {
        return orderDetailsInfoService.selectHomeData(loginUser);

    }

    @Override
    public String resetCddSequence() {
        orderInfoMapper.resetCddSequence();
        return "success";
    }

    @Override
    public String resetFddSequence() {
        orderInfoMapper.resetFddSequence();
        return "success";
    }

    @Override
    public OrderDetailsVo detailsByOrderId(Integer orderId) {
        OrderDetailsVo orderDetailsVo = orderInfoMapper.selectJoinOne(OrderDetailsVo.class, new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getOrderNumber, OrderInfoEntity::getManagerId, OrderInfoEntity::getProductName, OrderInfoEntity::getApplyAmount, OrderInfoEntity::getTerm,
                        OrderInfoEntity::getPaymentType, OrderInfoEntity::getCreateTime, OrderInfoEntity::getCustomerPhone, OrderInfoEntity::getFundId, OrderInfoEntity::getPreId,
                        OrderInfoEntity::getCurrentNode, OrderInfoEntity::getRepayMethod, OrderInfoEntity::getApprovalUpdateAmount, OrderInfoEntity::getCustomerId
                )

                .selectAs(OrderInfoEntity::getId, OrderDetailsVo::getOrderId)
                .selectAs(OrderInfoEntity::getApprovalAmount, OrderDetailsVo::getConfirmAmount)
                .selectAs(OrderInfoEntity::getFundId, OrderDetailsVo::getFundingPartyId)
                .selectAs(OrderInfoEntity::getRiskAmount, OrderDetailsVo::getRiskControlLimit)
                .selectAs(OrderInfoEntity::getRegionName, OrderDetailsVo::getAreaName)
                .selectAs(OrderInfoEntity::getSourceType, OrderDetailsVo::getSourceType)

                .select(PreApprovalApplyInfoEntity::getLoanAmount, PreApprovalApplyInfoEntity::getLoanPeriod, PreApprovalApplyInfoEntity::getType)

                .select(OrderCustomerInfoEntity::getId, OrderCustomerInfoEntity::getName, OrderCustomerInfoEntity::getIdNumber, OrderCustomerInfoEntity::getNormalAddress)

                .select(OrderCustomerInfoEntity::getId, OrderCustomerInfoEntity::getName, OrderCustomerInfoEntity::getIdNumber, OrderCustomerInfoEntity::getNormalAddress)

                .select(OrderVehicleInfoEntity::getVehicleNumber, OrderVehicleInfoEntity::getBrand, OrderVehicleInfoEntity::getVehicleSeries,
                        OrderVehicleInfoEntity::getVehicleModel, OrderVehicleInfoEntity::getVin, OrderVehicleInfoEntity::getRegisterDate,
                        OrderVehicleInfoEntity::getMileage, OrderVehicleInfoEntity::getEstimatedValue, OrderVehicleInfoEntity::getProductionDate,
                        OrderVehicleInfoEntity::getBuyDate)

                .select(ProductInfoEntity::getDeposit)
                .selectAs(ProductInfoEntity::getGuaranteeFee, OrderDetailsVo::getGuaranteeRate)
                .selectAs(ProductInfoEntity::getTerm, OrderDetailsVo::getGuaranteePeriod)
                .selectAs(ProductInfoEntity::getIrr, OrderDetailsVo::getAnnualInterestRate)
                .selectAs(ProductInfoEntity::getDeposit, OrderDetailsVo::getDeposit)
                .selectAs(ProductRongdanEntity::getCompanyName, OrderDetailsVo::getGuaranteeName)

                .leftJoin(PreApprovalApplyInfoEntity.class, PreApprovalApplyInfoEntity::getId, OrderInfoEntity::getPreId)
                .leftJoin(OrderCustomerInfoEntity.class, OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId)
                .leftJoin(OrderVehicleInfoEntity.class, OrderVehicleInfoEntity::getOrderId, OrderInfoEntity::getId)
                .leftJoin(ProductInfoEntity.class, ProductInfoEntity::getId, OrderInfoEntity::getProductId)
                .leftJoin(ProductRongdanEntity.class, ProductRongdanEntity::getId, OrderInfoEntity::getRongdanId)

                .eq(OrderInfoEntity::getId, orderId)
        );
        if (ObjUtil.isNull(orderDetailsVo)) {
            return null;
        }
        //查出指定节点remark
        OrderNodeRecordEntity latestBusinessRecord = orderNodeRecordMapper.selectOne(new LambdaQueryWrapper<OrderNodeRecordEntity>()
                .eq(OrderNodeRecordEntity::getOrderId, orderId)
                .eq(OrderNodeRecordEntity::getCurrentNode, States.BUSINESS_ADDED_INFO.getNode())
                .eq(OrderNodeRecordEntity::getNextNode, States.QUALITY_INSPECTION.getNode())
                .orderByDesc(OrderNodeRecordEntity::getCreateTime).last("limit 1"));
        if (latestBusinessRecord != null) {
            orderDetailsVo.setBusinessAddedInfoRemark(latestBusinessRecord.getRemark());
        }
        // 资方名称和抵押成数
        Integer fundId = orderDetailsVo.getFundingPartyId();
        FundInfoEntity fundInfoEntity = fundInfoMapper.selectById(fundId);
        if (fundInfoEntity != null) {
            orderDetailsVo.setFundingPartyName(fundInfoEntity.getName());
            orderDetailsVo.setMaxMortgage(fundInfoEntity.getMaxMortgage());
        }

        // 风控终审显示风控初审结论
        if (Objects.equals(orderDetailsVo.getCurrentNode(), States.RISK_FINAL_APPROVE.getNode())) {
            //审批结论、审批意见、外部意见
            OrderNodeRecordEntity latestRecord = orderNodeRecordMapper.selectOne(new LambdaQueryWrapper<OrderNodeRecordEntity>()
                    .eq(OrderNodeRecordEntity::getCurrentNode, States.RISK_FIRST_APPROVE.getNode())
                    .eq(OrderNodeRecordEntity::getNextNode, States.RISK_FINAL_APPROVE.getNode())
                    .eq(OrderNodeRecordEntity::getOrderId, orderId)
                    .orderByDesc(OrderNodeRecordEntity::getCreateTime).last("limit 1"));
            if (latestRecord != null) {
                orderDetailsVo.setRemark(latestRecord.getRemark());
                orderDetailsVo.setRemarkExternal(latestRecord.getRemarkExternal());
                orderDetailsVo.setEvent(latestRecord.getEvent().getDesc());
            }
        }

        Integer managerId = orderDetailsVo.getManagerId();
//        try {
//            UserAndDeptUsersVO userAndDeptUsersVO = userFeign.selectUsersStore(new UserStoreDTO().setUserId(managerId)).getData();
//            orderDetailsVo.setStoreName(userAndDeptUsersVO.getStore());
//            orderDetailsVo.setAreaName(userAndDeptUsersVO.getArea());
//        } catch (FeignException e) {
//            log.error("OrderServiceImpl.detailsByOrderId 查询订单的经办人信息失败，订单ID：{}，原因：{}", orderDetailsVo.getOrderId(), e.getMessage());
//        }
        try {
            UserDetailInfoVO userDetailInfoVO = userFeign.searchUserDetailById(managerId).getData();
            orderDetailsVo.setAccountManagerName(userDetailInfoVO.getName());
        } catch (FeignException e) {
            log.error("OrderServiceImpl.detailsByOrderId 查询订单的经办人信息失败，订单ID：{}，原因：{}", orderDetailsVo.getOrderId(), e.getMessage());
        }
        //获取 法大大实名信息
        long identProcessStatusCount = preApprovalFddAuthMapper.selectCount(new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                //                .eq(PreApprovalFddAuthEntity::getPreId, orderDetailsVo.getPreId())
                .eq(PreApprovalFddAuthEntity::getPhone, orderDetailsVo.getCustomerPhone())
                .eq(PreApprovalFddAuthEntity::getIdNumber, orderDetailsVo.getIdNumber())
                .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
        );
        //实名认证状态
        orderDetailsVo.setIdentProcessStatus(identProcessStatusCount > 0L ? 1 : 0);

        //是否存在黑名单
        boolean isBlacklist = this.verifyOrderBlacklistById(orderId);
        //主申请人是否存在黑名单
        boolean isCustomerBlacklist = riskCustomerBlacklistService.isBlacklistByCustomerId(orderDetailsVo.getCustomerId());

        orderDetailsVo.setIsBlacklist(isBlacklist ? "1" : "0");
        orderDetailsVo.setIsCustomerBlacklist(isCustomerBlacklist);

        //资方授信信息
        PreFundInfoEntity preFundInfo = preFundInfoService.getByPreIdAndFundId(orderDetailsVo.getPreId(), fundId);
        if (ObjUtil.isNotNull(preFundInfo)) {

            orderDetailsVo.setSoftwareCredit(preFundInfo.getEvaluationAmount());
            orderDetailsVo.setApprovalAmount(preFundInfo.getCreditAmount());

            orderDetailsVo.setPreCreditAmount(preFundInfo.getFundCreditAmount());
            orderDetailsVo.setPreCreditTime(preFundInfo.getFundCreditTime());
            //增加预审时间
            if (preFundInfo.getFundCreditTime() != null &&
                    orderDetailsVo.getCurrentNode() != 5000 &&
                    orderDetailsVo.getCurrentNode() != -1000
            ) {
                LocalDateTime fundCreditTime = preFundInfo.getFundCreditTime();
                LocalDateTime fifteenDaysLater = fundCreditTime.plusDays(15);
                orderDetailsVo.setEffectiveTime(fifteenDaysLater);
            }


            //  预审授信额度
            orderDetailsVo.setPreCreditAmount(
                    ObjUtil.isNotNull(orderDetailsVo.getPreCreditAmount()) ?
                            orderDetailsVo.getPreCreditAmount() :
                            preFundInfo.getEvaluationAmount()
            );

        }

        orderDetailsVo.setRiskControlLimit(
                ObjUtil.isNotNull(orderDetailsVo.getRiskControlLimit()) ?
                        orderDetailsVo.getRiskControlLimit() :
                        orderDetailsVo.getPreCreditAmount()
        );


        // 根据风控额度自动倒推出评估师额度(蓝海产品、长安新生8成倒推，中关村、盈峰、苏商、客商按照9成倒推
        //
        //        if (Arrays.asList(FundEnum.LAN_HAI, FundEnum.CHANG_AN).contains(FundEnum.getFundEnum(fundId))) {
        //            orderDetailsVo.setAppraiserAmount(orderDetailsVo.getRiskControlLimit().multiply(BigDecimal.valueOf(0.8)));
        //        } else {
        //            orderDetailsVo.setAppraiserAmount(orderDetailsVo.getRiskControlLimit().multiply(BigDecimal.valueOf(0.9)));
        //        }

        // 资方终审额度
        finalFundInfoService.getFundByOrderIdAndFundId(orderId, fundId).ifPresent(fundInfo -> {
            orderDetailsVo.setFundCreditAmount(fundInfo.getFundCreditAmount());
            orderDetailsVo.setFundCreditTime(fundInfo.getFundCreditTime());
        });


        orderDetailsVo.setOnlineAssessment(null);
        orderDetailsVo.setAssessmentReport(null);


        // 填充入网时长和三要素验证
        orderServicePrivateMethod.fillThreeFactorFeature(orderDetailsVo);
        // 征信特性信息
        orderServicePrivateMethod.fillRiskCreditFeature(orderDetailsVo);
        // 模型评分
        orderServicePrivateMethod.fillModelScore(orderId, orderDetailsVo);
        orderDetailsInfoService.detailsByOrderId(orderDetailsVo);
        // 风控初审外部原因
        OrderApprovalReasonStagingEntity orderApprovalReasonStagingEntity = orderApprovalReasonStagingMapper.selectOne(new MPJLambdaWrapper<OrderApprovalReasonStagingEntity>()
                .eq(OrderApprovalReasonStagingEntity::getOrderId, orderId)
                .eq(OrderApprovalReasonStagingEntity::getDeleteFlag, 0)
                .orderByDesc(OrderApprovalReasonStagingEntity::getCreateTime)
                .last("limit 1"));
        orderDetailsVo.setTrialRemarkExternal(orderApprovalReasonStagingEntity != null ? orderApprovalReasonStagingEntity.getRemarkExternal() : null);

        return orderDetailsVo;
    }

    /**
     * 按身份证 id_number查询详细信息
     */
    @Override
    public OrderDetailsVo detailsByIdNumber(String idNumber) {
        OrderDetailsVo orderDetailsVo = new OrderDetailsVo();
        RiskCreditFeatureEntity riskCreditFeatureEntity = riskCreditFeatureMapper.selectOne(new LambdaQueryWrapper<RiskCreditFeatureEntity>()
                        .eq(RiskCreditFeatureEntity::getIdNumber, idNumber)
                        .eq(RiskCreditFeatureEntity::getDeleteFlag, 0)
                , false
        );
        if (ObjUtil.isNotEmpty(riskCreditFeatureEntity)) {
            BeanUtil.copyProperties(riskCreditFeatureEntity, orderDetailsVo);
        }
        return orderDetailsVo;
    }

    /**
     * 征信特性信息
     *
     * @param orderDetailsVo
     */
    @Override
    public void fillRiskCreditFeature(OrderDetailsVo orderDetailsVo) {
        Result<RiskCreditFeatureVO> creditFeatureResult = riskFeign.getCreditFeature(orderDetailsVo.getIdNumber());

        if (Result.isSuccess(creditFeatureResult) && creditFeatureResult.getData() != null) {
            RiskCreditFeatureVO creditFeature = creditFeatureResult.getData();
            // 是否逾期
            orderDetailsVo.setIsOverdue(creditFeature.getIsOverdue());

            // 近两年内所有账户累计逾期期数
            orderDetailsVo.setAllCumulative24Month(creditFeature.getAllCumulative24Month());

            // 近两年内单笔最高连续逾期期数
            orderDetailsVo.setSingleContinuous24Month(creditFeature.getSingleContinuous24Month());

            // 近两年内单笔最高累计逾期期数
            orderDetailsVo.setOverdueCumulative24Month(creditFeature.getOverdueCumulative24Month());

            // 是否白户
            orderDetailsVo.setIsBlankAccount(creditFeature.getIsBlankAccount());

            // 在还贷款笔数
            orderDetailsVo.setRepaymentNum(creditFeature.getRepaymentNum());

            // 在还贷款金额
            orderDetailsVo.setRepaymentAmount(creditFeature.getRepaymentAmount());

            // 信用卡授信额度
            orderDetailsVo.setCreditLimit(creditFeature.getCreditLimit());

            // 信用卡已用额度
            orderDetailsVo.setCreditUsedAmount(creditFeature.getCreditUsedAmount());

            // 3个月内历史查询次数
            orderDetailsVo.setSearchNum(creditFeature.getSearchNum());

            // 当前单笔最高逾期期数
            orderDetailsVo.setOverdueMaxNum(creditFeature.getOverdueMaxNum());

            // 当前逾期总笔数
            orderDetailsVo.setOverdueTotalNum(creditFeature.getOverdueTotalNum());

            // 当前逾期总金额
            orderDetailsVo.setNowOverdueAmount(creditFeature.getNowOverdueAmount());

            // 不良资产处置次数
            orderDetailsVo.setBadAsset(creditFeature.getBadAsset());

            // 客户所有状态
            orderDetailsVo.setCustomerAllStatus(creditFeature.getCustomerAllStatus());

            // 征信负债月还
            orderDetailsVo.setCreditDebtMonthRepayment(creditFeature.getCreditDebtMonthRepayment());
        }
    }


    /**
     * 确认详情
     *
     * @param orderId 订单 ID
     * @return {@link AmountDetailVO }
     */
    @Override
    public AmountDetailVO confirmDetail(Integer orderId) {
        //获取确认详情
        AmountDetailVO amountDetailVO = orderServicePrivateMethod.getCoreOrderDetails(orderId);

        if (ObjUtil.isNotNull(amountDetailVO)) {
            //确认金额
            amountDetailVO.setApprovalAmount(ObjUtil.isNull(amountDetailVO.getApprovalAmount()) ?
                    amountDetailVO.getApplyAmount() :
                    amountDetailVO.getApprovalAmount());
            //资方授信额度
            amountDetailVO.setFundCreditAmount(amountDetailVO.getFundCreditAmount());
            amountDetailVO.setCustomerAmountMax(amountDetailVO.getRiskAmount().min(amountDetailVO.getFundCreditAmount()));
        }
        return amountDetailVO;
    }


    /**
     * 确认提交
     *
     * @param amountConfirmDTO 金额确认 DTO
     * @param loginUser
     * @return {@link Boolean }
     */
    @Override
    public Boolean confirmSubmit(AmountConfirmDTO amountConfirmDTO, LoginUser loginUser) {

        List<OrderAmountEntity> orderAmountEntities = orderAmountMapper.selectList(new LambdaQueryWrapper<OrderAmountEntity>()
                .eq(OrderAmountEntity::getOrderId, amountConfirmDTO.getOrderId())
                .eq(OrderAmountEntity::getDeleteFlag, 0));
        if (CollUtil.isEmpty(orderAmountEntities)) {
            throw new BusinessException("当前订单不存在金额信息");
        }
        OrderAmountEntity orderAmountEntity = orderAmountEntities.get(0);
        BigDecimal fundPreAmount = orderAmountEntity.getFundPreAmount();
        if (fundPreAmount == null) {
            throw new BusinessException("当前订单不存在预授信金额");
        }
        Integer orderId = amountConfirmDTO.getOrderId();
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, "当前订单不存在");
        BigDecimal approvalAmount = amountConfirmDTO.getApprovalAmount();
        BigDecimal customerAmountMax = orderAmountEntity.getRiskAmount().min(orderAmountEntity.getFundPreAmount());
        // 判断是否小于或者等于授信金额
        if (approvalAmount.compareTo(customerAmountMax) > 0) {
            throw new BusinessException("确认金额不能大于授信金额");
        }
        // 更新资方额度
        Result<String> fundAmountChangeResult = approveFeign.fundAmountChange(new FundAmountChangeDTO()
                .setLinkId(orderId).setType(2).setChangeAmount(approvalAmount));
        if (!Result.isSuccess(fundAmountChangeResult)) {
            throw new BusinessException(fundAmountChangeResult.getMsg());
        }
        //更新订单状态和信息
        LambdaUpdateWrapper<OrderInfoEntity> oiUw = new LambdaUpdateWrapper<>();
        oiUw.set(OrderInfoEntity::getApprovalAmount, approvalAmount);
        oiUw.set(OrderInfoEntity::getConfirmState, 2);
        oiUw.eq(OrderInfoEntity::getId, orderId);
        //修改客户银行卡
        orderCustomerInfoMapper.update(new OrderCustomerInfoEntity().setBankCardNumber(amountConfirmDTO.getBankCardNumber()),
                new LambdaUpdateWrapper<OrderCustomerInfoEntity>().eq(OrderCustomerInfoEntity::getId, orderInfoEntity.getCustomerId()));

        OrderStateService orderStateService = SpringContentUtils.getBean(OrderStateService.class);

        boolean result = orderInfoMapper.update(oiUw) > 0;

        Integer userId = loginUser.getUserId();
        if (!Objects.equals(loginUser.getUserType(), UserTypeEnum.EMPLOYEE.getType())) {
            userId = -2;
        }
        SubmitResultVO resultVO = orderStateService.sendEvent(States.CUSTOMER_CONFIRM, Events.AGREES, orderId, userId);
        if (resultVO.getNode().equals(resultVO.getTargetNode())) {
            throw new BusinessException("额度不足,无法提交");
        }
        ;
        //存入面签表
        if (ObjUtil.notEqual(orderInfoEntity.getRegionId(), 56)) {
            customerSignInfoMapper.insert(new CustomerSignInfoEntity().setOrderId(orderId).setNotice(0));
        }
        //        if(orderInfoEntity.getFundId()== FundEnum.FU_MIN.getValue()){
        //            //获取富民合同
        //            Result<List<FMContractPreviewVO>> fuMinContractPreviewAllResult = approveFeign.fuMinContractPreviewAll(orderId);
        //            if(!Result.isSuccess(fuMinContractPreviewAllResult)||CollUtil.isEmpty(fuMinContractPreviewAllResult.getData())) {
        //                log.error("ContractServiceImpl.generateContractSignList 富民个人贷款借款合同获取失败 orderId:{}", orderId);
        //            }else{
        //                List<FMContractPreviewVO> fuMinContracList = fuMinContractPreviewAllResult.getData();
        //                for(FMContractPreviewVO fuMinContract:fuMinContracList){
        //                    if(fuMinContract.getResourceId() == null){
        //                        continue;
        //                    }
        //                    OrderContractEntity orderContractEntity = new OrderContractEntity()
        //                            .setOrderId(orderId)
        //                            .setContractFlag(1)
        //                            .setName(fuMinContract.getFileName())
        //                            .setSignStatus(0)
        //                            .setNumber(NumberUtils.getContractNumber())
        //                            .setTemplateId(fuMinContract.getTemplateId())
        //                            .setResource(fuMinContract.getResourceId());
        //                    log.info("ContractServiceImpl.fuMinContractPreviewAllResult orderContractEntity:{}", JSONUtil.toJsonStr(orderContractEntity));
        //                    orderContractMapper.insert(orderContractEntity);
        //                }
        //            }
        //        }

        return result;
    }

//    @Override
//    @Synchronized
//    public boolean orderDistributionAuto(Integer orderId) {
//        String lockKey = LOCK_KEY_PREFIX + orderId;
//        String lockValue = UUID.randomUUID().toString();
//
//        try {
//            // 尝试获取分布式锁
//            Boolean acquired = redisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, LOCK_TIMEOUT, TimeUnit.MILLISECONDS);
//            if (Boolean.TRUE.equals(acquired)) {
//                // 获取锁成功，执行业务逻辑
//                return executeOrderDistribution(orderId);
//            } else {
//                // 获取锁失败
//                return false;
//            }
//        } finally {
//            // 释放锁
//            releaseLock(lockKey, lockValue);
//        }
//    }

//    @Override
//    public List<Integer> orderDistributionAutoTest() {
//        List<Integer> userIdList = new ArrayList<>();
//        for (int i = 0; i < 10; i++) {
//            Integer randomRiskUserId = getRandomRiskUserId();
//            userIdList.add(randomRiskUserId);
//            log.info("randomRiskUserId: {}", randomRiskUserId);
//        }
//        return userIdList;
//    }

//    private boolean executeOrderDistribution(Integer orderId) {
//        //先判断是否为驳回件
//        OrderInfoEntity orderInfo = super.getById(orderId);
//        if (orderInfo.getRiskUserId() != null) {
//            RiskUserStateEntity riskUserStateEntity = riskStateMapper.selectOne(new LambdaQueryWrapper<RiskUserStateEntity>()
//                    .eq(RiskUserStateEntity::getUserId, orderInfo.getRiskUserId())
//                    .eq(RiskUserStateEntity::getUserType, 1)
//                    .eq(RiskUserStateEntity::getDeleteFlag, 0));
//
//            log.info("riskUserStateEntity: {}", riskUserStateEntity);
//
//            return riskUserStateEntity.getState() != 2;
//        }
//
//        Integer assignUserId = getRandomRiskUserId();
//
//        log.info("OrderServiceImpl executeOrderDistribution assignUserId:{}", assignUserId);
//
//        if (assignUserId == null) {
//            return false;
//        }
//
//        LambdaUpdateWrapper<OrderInfoEntity> wrapper = new LambdaUpdateWrapper<>();
//        wrapper.eq(OrderInfoEntity::getId, orderId)
//                .set(OrderInfoEntity::getRiskUserId, assignUserId);
//        return super.update(wrapper);
//    }

//    @Override
//    @Nullable
//    public Integer getRandomRiskUserId() {
//        // 在线人员ID
//        List<Integer> userIdList = riskStateService.selectOnlineUser(1,1).stream()
//                .map(RiskUserStateEntity::getUserId).toList();
//
//        log.info("OrderServiceImpl getRandomRiskUserId userIdList:{}", userIdList);
//
//        Result<List<Integer>> listResult = userFeign.searchUserByRoleId(RoleEnum.RISK_AMOUNT_APPROVE.getId());
//        if (!Result.isSuccess(listResult) || listResult.getData() == null) {
//            log.error("未获取到风控人员信息");
//            return null;
//        }
//
//        //获取所有风控人员ID
//        List<Integer> integerList = listResult.getData();
//
//        log.info("OrderServiceImpl getRandomRiskUserId integerList:{}", integerList);
//
//        //取交集
//        List<Integer> riskUserIdList = userIdList.stream()
//                .filter(integerList::contains).toList();
//
//        log.info("OrderServiceImpl getRandomRiskUserId riskUserIdList:{}", riskUserIdList);
//
//        // 手中有业务的风控人员ID
//        List<Integer> businessUserIdList = getRiskFirstNodeUserIdList();
//
//        log.info("OrderServiceImpl getRandomRiskUserId businessUserIdList:{}", businessUserIdList);
//
//        //  k
//        List<Integer> noBusinessUserIdList = riskUserIdList.stream()
//                .filter(userId -> !businessUserIdList.contains(userId)).toList();
//
//        log.info("OrderServiceImpl getRandomRiskUserId noBusinessUserIdList:{}", noBusinessUserIdList);
//
//        if (CollUtil.isEmpty(noBusinessUserIdList)) {
//            return null;
//        }
//
//        // 订单分配
//        int randomIndex = random.nextInt(noBusinessUserIdList.size());
//        return noBusinessUserIdList.get(randomIndex);
//    }
//
//    private void releaseLock(String lockKey, String lockValue) {
//        // 使用Lua脚本确保原子性释放锁
//        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
//        redisTemplate.execute(new DefaultRedisScript<>(script, Long.class), Collections.singletonList(lockKey), lockValue);
//    }

//    @Override
//    public Integer orderDistributionManual(Integer riskUserId) {
//        log.info("orderServiceImpl orderDistributionManual start，riskUserId：{}", riskUserId);
//
//        List<RiskUserStateEntity> riskUserStateEntities = riskStateMapper.selectList(new LambdaQueryWrapper<RiskUserStateEntity>()
//                .eq(RiskUserStateEntity::getUserId, riskUserId)
//                .eq(RiskUserStateEntity::getDeleteFlag, 0));
//
//        if (CollUtil.isEmpty(riskUserStateEntities)) {
//            throw new BusinessException("风控人员不存在");
//        }
//
//        RiskUserStateEntity riskUserStateEntity = riskUserStateEntities.get(0);
//
//        //若风控人员状态为下线，则不分配订单
//        if (riskUserStateEntity.getState() != 1) {
//            return null;
//        }
//
//        Result<UserDetailInfoVO> userDetailInfoVOResult = userFeign.searchUserDetailById(riskUserId);
//        if (!Result.isSuccess(userDetailInfoVOResult) || userDetailInfoVOResult.getData() == null) {
//            log.error("未获取到用户角色，riskUserId：{}", riskUserId);
//            return null;
//        }
//
//        UserDetailInfoVO userDetailInfoVO = userDetailInfoVOResult.getData();
//        List<Integer> roleIds = userDetailInfoVO.getRoleIds();
//        if (!RoleEnum.RISK_AMOUNT_APPROVE.hasRole(roleIds)) {
//            log.error("用户角色不匹配，riskUserId：{}", roleIds);
//            return null;
//        }
//
//        OrderInfoEntity newOrderEntity = super.getOne(new LambdaQueryWrapper<OrderInfoEntity>()
//                .select(OrderInfoEntity::getId)
//                .eq(OrderInfoEntity::getCurrentNode, States.RISK_FIRST_APPROVE_ASSIGN.getNode())
//                .eq(OrderInfoEntity::getDeleteFlag, 0)
//                .orderByAsc(OrderInfoEntity::getQualityTestCommitTime, OrderInfoEntity::getApplyAmount)
//                .last("ORDER BY CASE WHEN risk_user_id IS NOT NULL THEN 0 ELSE 1 END, risk_user_id DESC, id DESC LIMIT 1")
//
//        );
//        if (newOrderEntity == null) {
//            return null;
//        }
//        Integer orderId = newOrderEntity.getId();
//        log.info("待初审分配任务池订单：{}", orderId);
//
//        LambdaUpdateWrapper<OrderInfoEntity> wrapper = new LambdaUpdateWrapper<>();
//        wrapper.eq(OrderInfoEntity::getId, orderId)
//                .set(OrderInfoEntity::getRiskUserId, riskUserId);
//        super.update(wrapper);
//        log.info("订单分配成功，orderId：{}, riskUserId：{}", orderId, riskUserId);
//        return orderId;
//    }

    @Override
    public boolean updateOrder(UpdateOrderDTO updateOrderDTO) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(updateOrderDTO.getOrderId());
        if (orderInfoEntity == null) {
            throw new BusinessException("未获取到相关订单");
        }

        if (!Objects.isNull(updateOrderDTO.getFundId())) {
            orderInfoEntity.setFundId(updateOrderDTO.getFundId());
        }

        if (!Objects.isNull(updateOrderDTO.getApprovalUpdateAmount())) {
            PreFundInfoEntity preFundInfo = preFundInfoService.getByPreIdAndFundId(orderInfoEntity.getPreId(), orderInfoEntity.getFundId());
            if (preFundInfo == null) {
                throw new BusinessException("未获取到相关订单资方预审信息");
            }
            if (preFundInfo.getCreditAmount().compareTo(updateOrderDTO.getApprovalUpdateAmount()) <= 0) {
                throw new BusinessException("降额不能等于或大于授信金额");
            }
            orderInfoEntity.setApprovalUpdateAmount(updateOrderDTO.getApprovalUpdateAmount());
        }

        // 5. 更新订单信息到数据库
        return orderInfoMapper.updateById(orderInfoEntity) > 0;
    }


    @Override
    public FhldDataVO getFhldInfo(Integer orderId) {
        log.info("orderServiceImpl getFhldInfo start，orderId：{}", orderId);
        List<RelatedOrderVO> infoList = orderInfoMapper.selectJoinList(RelatedOrderVO.class, new MPJLambdaWrapper<OrderInfoEntity>()
                .selectAs(OrderCustomerInfoEntity::getIdNumber, RelatedOrderVO::getIdNumber)
                .leftJoin(OrderCustomerInfoEntity.class, OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId)
                .eq(OrderInfoEntity::getId, orderId)
                .orderByDesc(OrderInfoEntity::getId));
        if (CollUtil.isEmpty(infoList)) {
            throw new BusinessException("未获取到相关订单");
        }
        RelatedOrderVO relatedOrderVO = infoList.get(0);
        Result<FhldDataVO> result = riskFeign.getDetailByOrderId(relatedOrderVO.getIdNumber());
        FhldDataVO fhldDataVO = new FhldDataVO();
        if (SUCCESS.getCode().equals(result.getCode())) {
            fhldDataVO = result.getData();
        }
        return fhldDataVO;
    }

    /**
     * 验证订单是否存在黑名单
     *
     * @param orderId
     * @return {@link boolean}
     */
    @Override
    public boolean verifyOrderBlacklistById(Integer orderId) {
        MPJLambdaWrapper<OrderInfoEntity> mpjLqw = new MPJLambdaWrapper<>();
        mpjLqw.leftJoin(OrderCustomerInfoEntity.class, OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId);
        mpjLqw.leftJoin(OrderContactPersonEntity.class, on -> on
                .eq(OrderInfoEntity::getId, OrderContactPersonEntity::getOrderId)
                .eq(OrderContactPersonEntity::getDeleteFlag, 0)
        );
        mpjLqw.innerJoin(RiskCustomerBlacklistEntity.class, on -> on
                .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0)
                .and(qw -> qw
                        //对比 订单客户信息
                        .eq(RiskCustomerBlacklistEntity::getPhoneNumber, OrderInfoEntity::getCustomerPhone)
                        .or()
                        //对比 客户信息
                        .eq(RiskCustomerBlacklistEntity::getPhoneNumber, OrderCustomerInfoEntity::getPhone)
                        .or()
                        .eq(RiskCustomerBlacklistEntity::getIdNumber, OrderCustomerInfoEntity::getIdNumber)
                        //对比 联系人信息
                        .or()
                        .eq(RiskCustomerBlacklistEntity::getIdNumber, OrderContactPersonEntity::getIdNumber)
                        .or()
                        .eq(RiskCustomerBlacklistEntity::getPhoneNumber, OrderContactPersonEntity::getPhone)

                )
        );
        mpjLqw.eq(OrderInfoEntity::getId, orderId);
        return this.count(mpjLqw) > 0;
    }

    /**
     * 获取相关信息
     *
     * @param relatedOrderDTO 相关订单 DTO
     * @return {@link Page }<{@link RelatedOrderVO }>
     */
    @Override
    public Page<RelatedOrderVO> getRelatedInfo(RelatedOrderDTO relatedOrderDTO) {
        Integer type = relatedOrderDTO.getType();
        String typeData = relatedOrderDTO.getTypeData();
        if (StrUtil.isBlank(typeData)) {
            return new Page<>();
        }
        MPJLambdaWrapper<OrderInfoEntity> wrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .selectAs(OrderInfoEntity::getId, RelatedOrderVO::getOrderId)
                .selectAs(OrderInfoEntity::getOrderNumber, RelatedOrderVO::getOrderNumber)
                .selectAs(OrderInfoEntity::getCustomerPhone, RelatedOrderVO::getPhone)
                .selectAs(OrderInfoEntity::getApplyAmount, RelatedOrderVO::getApplyAmount)
                .selectAs(OrderInfoEntity::getProductName, RelatedOrderVO::getProductName)
                .selectAs(OrderInfoEntity::getManagerId, RelatedOrderVO::getManageId)
                .selectAs(OrderInfoEntity::getStoreName, RelatedOrderVO::getStore)
                .selectAs(OrderVehicleInfoEntity::getVin, RelatedOrderVO::getVin)
                .selectAs(OrderInfoEntity::getVehicleNumber, RelatedOrderVO::getVehicleNumber)
                .selectAs(OrderCustomerInfoEntity::getIdNumber, RelatedOrderVO::getIdNumber)
                .selectAs(OrderCustomerInfoEntity::getName, RelatedOrderVO::getName)
                .selectAs(OrderInfoEntity::getCurrentNode, RelatedOrderVO::getCurrentNode)
                .leftJoin(OrderCustomerInfoEntity.class, OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId)
                .leftJoin(OrderVehicleInfoEntity.class, OrderVehicleInfoEntity::getOrderId, OrderInfoEntity::getId)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .ne(OrderInfoEntity::getId, relatedOrderDTO.getOrderId())
                .eq((Objects.equals(type, 1) && !StrUtil.isEmpty(typeData)), OrderCustomerInfoEntity::getIdNumber, typeData)
                .eq((Objects.equals(type, 2) && !StrUtil.isEmpty(typeData)), OrderCustomerInfoEntity::getPhone, typeData)
                .eq((Objects.equals(type, 3) && !StrUtil.isEmpty(typeData)), OrderVehicleInfoEntity::getVin, typeData)
                .eq((Objects.equals(type, 4) && !StrUtil.isEmpty(typeData)), OrderVehicleInfoEntity::getVehicleNumber, typeData)
                .eq((Objects.equals(type, 5) && !StrUtil.isEmpty(typeData)), OrderCustomerInfoEntity::getEnterpriseName, typeData);
        if (Objects.equals(type, 6) && !StringUtils.isEmpty(typeData)) {
            wrapper.apply("1 - levenshtein(t1.enterprise_details_address, {0})::float / greatest(length(t1.enterprise_details_address), length({0})) >= 0.85", typeData);
        }
        if (Objects.equals(type, 7) && !StringUtils.isEmpty(typeData)) {
            wrapper.apply("1 - levenshtein(t1.residential_detailed_address, {0})::float / greatest(length(t1.residential_detailed_address), length({0})) >= 0.85", typeData);
        }
        Page<RelatedOrderVO> relatedOrderVOPage = orderInfoMapper.selectJoinPage(new Page<>(relatedOrderDTO.getPageNum(), relatedOrderDTO.getPageSize()), RelatedOrderVO.class, wrapper);
        List<RelatedOrderVO> records = relatedOrderVOPage.getRecords();
        List<RelatedOrderVO> list = new ArrayList<>(records.stream().peek(order -> {
            Result<UserAndDeptUsersVO> userAndDeptUsersVOResult = userFeign.selectUsersStore(new UserStoreDTO().setUserId(order.getManageId()));
            String store = Optional.ofNullable(userAndDeptUsersVOResult.getData()).map(UserAndDeptUsersVO::getStore).orElse("");
            String accountManagerNames = Optional.ofNullable(userAndDeptUsersVOResult.getData()).map(UserAndDeptUsersVO::getName).orElse("");
            order.setStore(store);
            order.setAccountManagerName(accountManagerNames);
            order.setSysId(1);
            order.setCurrentNodeName(States.getByNode(order.getCurrentNode()).getDesc());
        }).toList());

        List<PlatformRelatedOrderVO> platformRelatedOrderVOS = dataFeign.getPlatformRelatedOrder(new PlatformRelatedOrderDTO().setType(type).setTypeData(typeData)).getData();
        list.addAll(platformRelatedOrderVOS.stream().map(platformRelatedOrderVO ->
                BeanUtil.toBean(platformRelatedOrderVO, RelatedOrderVO.class)
        ).toList());
        relatedOrderVOPage.setRecords(list);
        relatedOrderVOPage.setTotal(list.size());
        relatedOrderVOPage.setSize(list.size());
        return relatedOrderVOPage;
    }


    /**
     * 更新订单资方放款状态
     *
     * @param fundStatusDTO 资方放款结论
     * @return {@link Boolean}
     */

    @Override
    public Boolean updateFundStatus(OrderApproveFundPaymentStatusDTO fundStatusDTO) {
        log.info("OrderServiceImpl.updateFundStatus begin fundStatusDTO: {}", JSONUtil.toJsonStr(fundStatusDTO));

        try {
            //验证订单状态
            orderServicePrivateMethod.verifyOrderPayment(fundStatusDTO);
            //更新订单资方信息
            finalFundInfoService.updateOrderFundId(fundStatusDTO.getOrderId(), fundStatusDTO.getFundId());

            OrderFundPaymentEnum status = fundStatusDTO.getStatus();
            boolean update = orderServicePrivateMethod.updateOrderPaymentStatus(fundStatusDTO);

            if (update) {
                log.info("Fund pay status: {}", status);
                if (OrderFundPaymentEnum.PASS == status) {
                    log.info("Fund pay status {} update order {} node", status, fundStatusDTO.getOrderId());
                    OrderStateService orderStateService = SpringContentUtils.getBean(OrderStateService.class);
                    orderStateService.sendEvent(States.FUNDS_PAYMENT_APPROVAL, Events.AGREES, fundStatusDTO.getOrderId(),
                            1);
                    OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(fundStatusDTO.getOrderId());
                    OrderAmountEntity orderAmount = orderAmountMapper.selectOne(new LambdaQueryWrapper<>(OrderAmountEntity.class)
                            .eq(OrderAmountEntity::getOrderId, fundStatusDTO.getOrderId()), false);
                    orderFeeDetailService.saveOrderFeeDetail(fundStatusDTO.getOrderId(),
                            fundStatusDTO.getLoanApplyNo(),
                            orderAmount.getCustomerConfirmAmount(),
                            OrderFeeDetailTradingMethodsEnum.FUND_LENDING,
                            FundEnum.getFundEnum(fundStatusDTO.getFundId()).getFundName(),
                            orderInfoEntity.getCustomerName(),
                            OrderFeeDetailExpandTypeEnum.FUND_LENDING,
                            OrderFeeDetailStatusEnum.SPENDING,
                            orderInfoEntity.getPaymentTime(),
                            fundStatusDTO.getFailReason(), null);
                }
                // 失败
                if (OrderFundPaymentEnum.FAIL == status) {
                    log.info("Fund pay status {} update order {} node", status, fundStatusDTO.getOrderId());

                    OrderStateService orderStateService = SpringContentUtils.getBean(OrderStateService.class);
                    // 长银失败走拒绝流程
//                    if (Objects.equals(FundEnum.CHANG_YIN.getValue(), fundStatusDTO.getFundId())) {
//                        orderStateService.sendEvent(States.FUNDS_PAYMENT_APPROVAL, Events.REJECT, fundStatusDTO.getOrderId(),
//                                1, new ApprovalSubmitDTO().setRemarkExternal(fundStatusDTO.getFailReason()));
//                    } else {
                    orderStateService.sendEvent(States.FUNDS_PAYMENT_APPROVAL, Events.FAIL, fundStatusDTO.getOrderId(),
                            1, new ApprovalSubmitDTO().setRemarkExternal(fundStatusDTO.getFailReason()));

                    orderStateService.sendEvent(States.FUNDS_PAYMENT_FAIL, Events.RE_PAYMENT_PAY, fundStatusDTO.getOrderId(),
                            1, new ApprovalSubmitDTO().setRemarkExternal("付款失败后重新请款"));
//                    }
                }


            }
            return update;
        } catch (Exception e) {
            log.error("OrderServiceImpl.updateFundStatus error e {}:", e.getMessage(), e);
            throw new BusinessException("更新订单放款状态异常");
        }
    }


    /**
     * 更新订单资方放款状态
     *
     * @return {@link Boolean}
     */
    @Override
    public Boolean updateFundStatus(FinalApproveFundStatusDTO fundStatusDTO) {
        log.info("OrderServiceImpl.updateFundStatus begin with fundStatusDTO: {}", JSONUtil.toJsonStr(fundStatusDTO));

        // 更新最终资方信息
        orderServicePrivateMethod.updateFinalFundInfo(fundStatusDTO);

        // 更新订单信息
        try {
            log.info("OrderServiceImpl.updateFundStatus update order {} payment state to {}", fundStatusDTO.getOrderId(), fundStatusDTO.getStatus());
            updateOrderInfo(fundStatusDTO);
            log.info("OrderServiceImpl.updateFundStatus end SUCCESS order {}", fundStatusDTO.getOrderId());
        } catch (Exception e) {
            log.error("OrderServiceImpl.updateFundStatus err:{}", e.getMessage(), e);
        }


        return true;
    }

    /**
     * 更新资方还款计划状态
     *
     * @param fundStatusDTO 订单资方还款计划状态DTO
     */

    @Override
    public Boolean updateFundPlanStatus(OrderApproveFundPlanStatusDTO fundStatusDTO) {
        log.info("OrderServiceImpl.updateFundPlanStatus begin with fundStatusDTO: {}", JSONUtil.toJsonStr(fundStatusDTO));

        Integer orderId = fundStatusDTO.getOrderId();
        OrderInfoEntity orderInfo = super.getById(orderId);
        log.info("OrderServiceImpl.updateFundPlanStatus fetched orderInfo: {}", JSONUtil.toJsonStr(orderInfo));
        Assert.notNull(orderInfo, "订单不存在!");

        //本金是否一致
        boolean isFullyRepaid = false;

        // 获取订单资方还款计划
        List<FundRepaymentInfoEntity> fundRepaymentInfoList = fundRepaymentInfoService.queryByOrderIdList(orderId, orderInfo.getFundId());
        log.info("OrderServiceImpl.updateFundPlanStatus fundRepaymentInfoList: {}", JSONUtil.toJsonStr(fundRepaymentInfoList));
        if (CollUtil.isEmpty(fundRepaymentInfoList)) {
            return false;
        }

        // 判断是否所有期次的还款计划都已经还清
        BigDecimal totalActuallyAmount = fundRepaymentInfoList.stream()
                .map(FundRepaymentInfoEntity::getActuallyPrincipal)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal repaymentPrincipalTotal = fundRepaymentInfoList.stream()
                .map(FundRepaymentInfoEntity::getRepaymentPrincipal)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        isFullyRepaid = totalActuallyAmount.compareTo(repaymentPrincipalTotal) >= 0;

        log.info("OrderServiceImpl.updateFundPlanStatus isFullyRepaid: {}", isFullyRepaid);

        //如果为已结清设置还款计划表中为已结清状态
        if (isFullyRepaid) {
            fundRepaymentInfoService.updateFundRepaymentInfoSettle(orderId, orderInfo.getFundId());
            log.info("OrderServiceImpl.updateFundPlanStatus orderSettled orderId: {}", orderId);
        }
        List<FundRepaymentDeductEntity> list1 = fundRepaymentDeductService.list(
                new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                        .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                        .in(FundRepaymentDeductEntity::getBizType,
                                Arrays.asList(
                                        FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                        FundDeductBizTypeEnums.PAYMENT,
                                        FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                        FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                        FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE
                                )
                        )
                        .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                        .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                        .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isNotEmpty(list1)) {
            List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                            .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(FundDeductBizTypeEnums.PENALTY_DEDUCTION, FundDeductBizTypeEnums.PENALTY_INITIATE_REPAYMENT_AFTER_PAYMENT, FundDeductBizTypeEnums.PENALTY_PAYMENT))
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                            .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                            .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
            );
            if (CollUtil.isEmpty(list)) {
                isFullyRepaid = false;
            }
        }
        // 更新订单状态
        if (isFullyRepaid) {
            orderInfo.setPlanState(2); // 已结清
        } else {
            orderInfo.setPlanState(0); // 还款中
        }

        LambdaUpdateWrapper<OrderInfoEntity> luw = new LambdaUpdateWrapper<>();
        luw.set(OrderInfoEntity::getPlanState, orderInfo.getPlanState());
        luw.eq(OrderInfoEntity::getId, orderId);

        boolean result = super.update(luw);
        log.info("OrderServiceImpl.updateFundPlanStatus update result: {}", result);

        if (isFullyRepaid) {
            try {
                //不为赎回可操作结清
                if (!ObjUtil.equals(orderInfo.getIsRepurchase(), 1)) {
                    ApprovalService approvalService = SpringUtil.getBean(ApprovalService.class);
                    OrderIdDTO orderIdDTO = new OrderIdDTO();
                    orderIdDTO.setOrderId(orderId);
                    approvalService.orderSettled(orderIdDTO);
                }
            } catch (Exception e) {
                log.error("OrderServiceImpl.updateFundPlanStatus orderSettled error: {}", e.getMessage(), e);
            }
        }
        return result;
    }

    @Override
    public ResponseEntity<byte[]> generateCodeFile(GenerateCodeDTO generateCodeDTO, LoginUser loginUser) {
        Integer qrCodeType = generateCodeDTO.getQrCodeType();
        Integer userId = loginUser.getUserId();
//                Integer userId = 10334;

        // 中转页
        if (Objects.equals(qrCodeType, 7)) {
            String imageText = "";

            if (ObjUtil.isNotNull(generateCodeDTO.getOrderId())) {
                OrderInfoEntity orderInfo = orderInfoMapper.selectById(generateCodeDTO.getOrderId());
                if (ObjUtil.isNull(orderInfo)) {
                    throw new BusinessException("订单不存在");
                }
                imageText = orderInfo.getCustomerName();
            } else if (ObjUtil.isNotNull(generateCodeDTO.getPreId())) {
                PreApprovalApplyInfoEntity applyInfo = preApprovalApplyInfoMapper.selectById(generateCodeDTO.getPreId());
                if (ObjUtil.isNull(applyInfo)) {
                    throw new BusinessException("预审信息不存在");
                }
                imageText = applyInfo.getName();
            }
            try {
                // 设置header
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.IMAGE_PNG);
                headers.setContentDisposition(ContentDisposition.builder("attachment")
                        .filename(userId + ".png")
                        .build());
                String url = reTransferPagePath;
                if (StrUtil.isNotBlank(generateCodeDTO.getSceneParams())) {
                    //preId=%s&apply_type=%s
                    url = url + "&" + generateCodeDTO.getSceneParams();
                }
                url = orderSendMessageImpl.replaceUrl(url);
                byte[] imageBytes = QrCodeUtils.generateQrCode(imageText, url);
                return new ResponseEntity<>(imageBytes, headers, HttpStatus.OK);
            } catch (Exception e) {
                log.error("OrderServiceImpl.generateCodeFile error e:", e);
                throw new BusinessException("生成二维码失败");
            }
        }

        if (Objects.equals(qrCodeType, 3) || Objects.equals(qrCodeType, 5) || Objects.equals(qrCodeType, 6)) {
            Integer orderId = generateCodeDTO.getOrderId();
            // 通用验证
            Assert.notNull(orderId, "请输入订单ID");
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
            Assert.notNull(orderInfoEntity, "订单不存在");

            // 设置header
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.setContentDisposition(ContentDisposition.builder("attachment")
                    .filename(userId + ".png")
                    .build());

            try {
                // 根据不同类型生成不同的二维码内容
                String url;
                String content = "";
                if (Objects.equals(qrCodeType, 3)) {
                    url = String.format(customerConfirmUrl, orderId);
                    content = "客户姓名：" + orderInfoEntity.getCustomerName();
                } else if (Objects.equals(qrCodeType, 5)) {
                    url = String.format(reBindCardPath, orderId);
                    content = orderInfoEntity.getCustomerName();
                } else if (Objects.equals(qrCodeType, 6)) {
                    //重新生成合同
                    //                    contractFileService.generateContractSignList(orderId);
                    content = orderInfoEntity.getCustomerName();
                    url = String.format(reSignPath, orderId);
                } else {
                    throw new BusinessException("不支持的二维码类型");
                }

                url = orderSendMessageImpl.replaceUrl(url);
                byte[] imageBytes = QrCodeUtils.generateQrCode(content, url);
                return new ResponseEntity<>(imageBytes, headers, HttpStatus.OK);
            } catch (Exception e) {
                log.error("OrderServiceImpl.generateCodeFile error e:", e);
                throw new BusinessException("生成二维码失败");
            }
        }
        generateCodeDTO.setUserId(userId);
        if (envUtil.isPrd() && qrCodeType == 1) {
            Long wagesCount = manageBankAccountSignMapper.selectCount(new LambdaQueryWrapper<ManageBankAccountSignEntity>()
                    .eq(ManageBankAccountSignEntity::getUserId, userId)
                    .eq(ManageBankAccountSignEntity::getDeleteFlag, 0)
                    .eq(ManageBankAccountSignEntity::getCardType, 1)
                    .eq(ManageBankAccountSignEntity::getSignState, 1)
            );
            Long cashbackCount = manageBankAccountSignMapper.selectCount(new LambdaQueryWrapper<ManageBankAccountSignEntity>()
                    .eq(ManageBankAccountSignEntity::getUserId, userId)
                    .eq(ManageBankAccountSignEntity::getCardType, 2)
                    .eq(ManageBankAccountSignEntity::getDeleteFlag, 0)
                    .eq(ManageBankAccountSignEntity::getSignState, 1)
            );
            Integer cardNum = Convert.toInt(wagesCount + cashbackCount);
            if (cardNum < 3) {
                throw new BusinessException("绑卡数量不足");
            }
        }
        return userFeign.generateCodeFile(generateCodeDTO);
    }

    /**
     * 获取二维码URL
     *
     * @param generateCodeDTO 生成编码DTO
     * @param loginUser       登录用户信息
     * @return 二维码URL
     */
    @Override
    public String getQrCodeUrl(GenerateCodeDTO generateCodeDTO, LoginUser loginUser) {
        Integer qrCodeType = generateCodeDTO.getQrCodeType();
        Integer userId = loginUser.getUserId();

        // 中转页
        if (Objects.equals(qrCodeType, 7)) {
            String url = reTransferPagePath;
            if (StrUtil.isNotBlank(generateCodeDTO.getSceneParams())) {
                //preId=%s&apply_type=%s
                url = url + "&" + generateCodeDTO.getSceneParams();
            }
            return orderSendMessageImpl.replaceUrl(url);
        }

        if (Objects.equals(qrCodeType, 3) || Objects.equals(qrCodeType, 5) || Objects.equals(qrCodeType, 6)) {
            Integer orderId = generateCodeDTO.getOrderId();
            // 通用验证
            Assert.notNull(orderId, "请输入订单ID");
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
            Assert.notNull(orderInfoEntity, "订单不存在");

            // 根据不同类型生成不同的二维码内容
            String url;
            if (Objects.equals(qrCodeType, 3)) {
                url = String.format(customerConfirmUrl, orderId);
            } else if (Objects.equals(qrCodeType, 5)) {
                url = String.format(reBindCardPath, orderId);
            } else if (Objects.equals(qrCodeType, 6)) {
                url = String.format(reSignPath, orderId);
            } else {
                throw new BusinessException("不支持的二维码类型");
            }

            return orderSendMessageImpl.replaceUrl(url);
        }

        generateCodeDTO.setUserId(userId);

        return userFeign.generateCode(generateCodeDTO).getData();
    }

    @Override
    public ContractStateVO getContractState(ContractStateDTO generateCodeDTO, LoginUser loginUser) {
        log.info("OrderServiceImpl.getContractStateDTO generateCodeDTO: {}", JSONUtil.toJsonStr(generateCodeDTO));
        ContractStateVO contractStateVO = new ContractStateVO();
        Integer orderId = generateCodeDTO.getOrderId();
        OrderInfoEntity oldOrderInfoEntity = orderInfoMapper.selectById(orderId);
        if (ObjUtil.equal(oldOrderInfoEntity.getRegionId(), 56) || ObjUtil.equals(oldOrderInfoEntity.getRegionId(), 24)) {
            OrderFeeInfoEntity orderFeeInfoEntity = orderFeeInfoMapper.selectOne(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                    .eq(OrderFeeInfoEntity::getOrderId, orderId)
                    .eq(OrderFeeInfoEntity::getFeeType, 1)
                    .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                    .eq(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                    .orderByDesc(OrderFeeInfoEntity::getCreateTime).last("limit 1"), false);
            if (ObjUtil.isNull(orderFeeInfoEntity)) {
                throw new BusinessException("请先完成GPS费用支付");
            }
        }
        String lockKey = "order:contract:getContractState:" + orderId;
        String requestId = orderId + "";

        if (ObjUtil.equals(generateCodeDTO.getBusinessType(), 3)) {
            Boolean tryLock = redisService.tryLock(lockKey, requestId, 120);
            if (!tryLock) {
                log.info("ContractServiceImpl.getContractState, lock failed, orderId = {}", lockKey);
                return contractStateVO.setContractState(0);
            }
        }
        log.info("OrderServiceImpl.getContractStateDTO, tryLock success, orderId = {}", lockKey);

        if (Objects.equals(generateCodeDTO.getBusinessType(), 2)) {
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
            if (orderInfo.getContractState() == 0) {
                throw new BusinessException("合同正在生成中，请耐心等待");
            }
            // 重置资方签署状态和我司合同状态
            contractFileService.resetFundSignStatus(orderId);

            //生成合同
            contractFileService.generateContractSignList(orderId);

            //  重新进行合同签署
            if (Arrays.asList(States.CUSTOMER_APPOINTMENT.getNode(),
                    States.PAYMENT_APPLY_INFORMATION.getNode(),
                    States.PAYMENT_CONTRACT_APPROVAL.getNode(),
                    States.FUNDS_PAYMENT_FAIL.getNode()).contains(orderInfo.getCurrentNode())) {
                //如果订单状态大于2700，则调用状态机修改状态
                Integer userId = loginUser.getUserId();
                if (!Objects.equals(loginUser.getUserType(), UserTypeEnum.EMPLOYEE.getType())) {
                    userId = -2;
                }
                OrderStateService orderStateService = SpringContentUtils.getBean(OrderStateService.class);
                orderStateService.sendEvent(States.getByNode(orderInfo.getCurrentNode()), Events.RESIGN_CONTRACT, orderId, userId);
                //如果是请款资料将派单取消
                if (Objects.equals(orderInfo.getCurrentNode(), States.PAYMENT_APPLY_INFORMATION.getNode())) {
                    OrderApproveDistributeService orderApproveDistributeService = SpringContentUtils.getBean(OrderApproveDistributeService.class);
                    orderApproveDistributeService.updateOrderApproveDistribute(new UpdateOrderApproveDistributeDTO()
                            .setOrderId(orderId)
                            .setOrderNumber(orderInfo.getOrderNumber())
                            .setSource(0)
                            .setBusinessType(3)
                            .setCurrentNode(orderInfo.getCurrentNode()));
                }
            }
        }
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, "订单不存在");
        log.info("OrderServiceImpl.getContractStateDTO orderInfoEntity: {}", JSONUtil.toJsonStr(orderInfoEntity));
        Integer contractState = orderInfoEntity.getContractState();

        // 资方签署完成生成合同
        try {
            if (Objects.equals(generateCodeDTO.getBusinessType(), 3)) {
                if (contractState == 0) {
                    log.info("ContractServiceImpl.getContractState.generateContractSignList, contract not generated yet, orderId = {}", orderId);
                    fundSignInfoService.verifyFundSignInfo(orderInfoEntity);
                    log.info("ContractServiceImpl.getContractState.generateContractSignList, check fund signed, orderId = {}", orderId);
                    // 检查资方是否完成
                    //                Long signedCount = fundSignInfoMapper.selectCount(new LambdaQueryWrapper<FundSignInfoEntity>()
                    //                        .eq(FundSignInfoEntity::getOrderId, orderId)
                    //                        .eq(FundSignInfoEntity::getFundId, orderInfoEntity.getFundId())
                    //                        .eq(FundSignInfoEntity::getSignStatus, FundSignStatusEnum.SUCCESS.getCode())
                    //                        .eq(FundSignInfoEntity::getDeleteFlag, 0));
                    //
                    //                if (signedCount == 0) {
                    //                    log.info("ContractServiceImpl.generateContractSignList, fund not signed yet, orderId = {}", orderId);
                    //                    throw new BusinessException("请先完成资方合同签署");
                    //                }

                    log.info("ContractServiceImpl.getContractState.generateContractSignList, fund signed, orderId = {}", orderId);
                    contractFileService.generateContractSignList(orderId);
                    log.info("ContractServiceImpl.getContractState.generateContractSignList, contract generated, orderId = {}", orderId);

                }
            }
        } catch (Exception e) {
            log.info("ContractServiceImpl.getContractState, fund not signed yet, orderId = {} e:{}", orderId, e.getMessage(), e);
        } finally {
            redisService.releaseLock(lockKey, requestId);
        }

        return contractStateVO.setContractState(contractState);
    }


    /**
     * 详情信息
     *
     * @param orderDetailDTO 次序详情信息DTO
     * @param loginUser
     * @return {@link OrderDetailVO }
     */
    @Override
    public OrderDetailVO detail(OrderDetailDTO orderDetailDTO, LoginUser loginUser) {
        try {
            Integer orderId = orderDetailDTO.getOrderId();
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                    .eq(OrderInfoEntity::getId, orderId)
                    .eq(OrderInfoEntity::getDeleteFlag, 0)
            );
            if (Objects.isNull(orderInfoEntity)) {
                return null;
            }
            Integer customerId = orderInfoEntity.getCustomerId();
            OrderDetailVO orderDetailVO = new OrderDetailVO();
            //获取订单信息
            orderDetailVO.setOrderId(orderId);
            //订单信息
            orderDetailVO.setOrderInfo(getOrderInfo(orderInfoEntity));
            //获取客户信息
            orderDetailVO.setCustomerInfo(getCustomerInfo(orderId, customerId, orderInfoEntity.getPreId()));
            //获取车辆基础信息
            orderDetailVO.setOrderVehicleInfo(getVehicleInfo(orderId));
            //车辆补充信息
            orderDetailVO.setVehicleSupplementInfo(getVehicleSupplementInfo(orderId));
            //营业执照
            orderDetailVO.setEnterpriseLicenseInfo(getEnterpriseLicenseInfo(orderId));
            //门店车评信息
            orderDetailVO.setStoreAppraiserInfo(orderServicePrivateMethod.getStoreAppraiserInfo(orderId));
            //获取合同签约信息
            orderDetailVO.setSignContractInfoVO(getSignContractInfo(orderId, loginUser));
            //获取进件实名人
            orderDetailVO.setOrderContactPersonPartsVo(orderServicePrivateMethod.getOrderContactPersonParts(orderId));
            //风险提示
            orderDetailVO.setRiskPromptVOS(orderServicePrivateMethod.getRiskPromptVOS(orderInfoEntity));

            GpsDataDTO gpsDataDTO = new GpsDataDTO();
            gpsDataDTO.setOrderId(orderId);
            gpsDataDTO.setDataSource(0);
            //获取GPS还款计划
            Result<GpsDataVO> gpsRepaymentInfo = autoLoanRepaymentFeign.getGpsRepaymentInfo(gpsDataDTO);
            orderDetailVO.setGpsDataVO(null != gpsRepaymentInfo.getData()
                    ? gpsRepaymentInfo.getData()
                    : new GpsDataVO());

            return orderDetailVO;
        } catch (Exception e) {
            log.error("OrderServiceImpl.detail error e:", e);
            throw new BusinessException("订单详情异常");
        }
    }

    /**
     * 获取合同签约信息
     *
     * @param orderId
     * @return
     */
    private SignContractInfoVO getSignContractInfo(Integer orderId, LoginUser loginUser) {
        Assert.notNull(loginUser, "用户信息为空");

        List<Integer> roleIdList = loginUser.getRoleIds();
        List<Integer> deptIdList = loginUser.getDeptIds();
        SignContractInfoVO signContractInfoVO = new SignContractInfoVO();
        //合同信息
        List<SignContractInfoVO.SignContractDetailVO> signContractDetailVOList = Lists.newArrayList();
        //资方结清证明信息
        SignContractInfoVO.ZfjqzmVO zfjqzmVO = new SignContractInfoVO.ZfjqzmVO();
        //赎回证明信息
        SignContractInfoVO.ShzmVO shzmVO = new SignContractInfoVO.ShzmVO();
        List<String> zfjqzmFileUrlList = Lists.newArrayList();
        List<String> shzmFileUrlList = Lists.newArrayList();
        //查询合同信息
        List<OrderContractEntity> orderContractEntityList = orderContractMapper.selectList(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .orderByDesc(OrderContractEntity::getCreateTime)
        );

        //查询文件配置id
        ResourceFileCodeDTO resourceFileCodeDTO = new ResourceFileCodeDTO();
        resourceFileCodeDTO.setCodes(Lists.newArrayList(FileConfigEnums.FUND_SETTLEMENT_VOUCHER.getCode(), "赎回证明"));
        Result<Map<String, Integer>> fileConfigData = resourceFeign.selectFileConfigByCodeList(resourceFileCodeDTO);
        if (!Result.isSuccess(fileConfigData)) {
            throw new BusinessException("获取文件配置信息异常");
        }

        if (CollUtil.isNotEmpty(orderContractEntityList)) {

            //资管副总、资管主管、资管部全员且非资管客服 默认拥有查看和下载合同的权限
            boolean condition = RoleEnum.ASSET_AREA_VICE_PRESIDENT.hasRole(roleIdList)
                    || RoleEnum.ASSET_MANAGER.hasRole(roleIdList)
                    || (DeptEnum.ZI_GUAN_BU.hasRole(deptIdList) && !RoleEnum.ASSET_CUSTOMER_SERVICE.hasRole(roleIdList));

            //查询资管客服合同拥有权限
            List<OrderContractUserPermissionEntity> orderContractUserPermissionEntityList = orderContractUserPermissionMapper.selectList(new LambdaQueryWrapper<OrderContractUserPermissionEntity>()
                    .eq(OrderContractUserPermissionEntity::getOrderId, orderId)
                    .eq(OrderContractUserPermissionEntity::getDeleteFlag, 0)
                    .eq(OrderContractUserPermissionEntity::getUserId, loginUser.getUserId())
                    .eq(OrderContractUserPermissionEntity::getLimitFlag, 1));
            //拥有权限的合同id集合
            Set<String[]> contractPermissionIdSet = orderContractUserPermissionEntityList.stream().map(v ->
                    v.getContractId().split(",")).collect(Collectors.toSet());

            orderContractEntityList.forEach(v -> {
                SignContractInfoVO.SignContractDetailVO signContractDetailVO = new SignContractInfoVO.SignContractDetailVO();
                signContractDetailVO.setOrderId(orderId);
                signContractDetailVO.setContractId(v.getId());
                signContractDetailVO.setContractType(v.getContractFlag());
                signContractDetailVO.setSignStatus(v.getSignStatus());
                signContractDetailVO.setContractName(v.getName());
                signContractDetailVO.setResource(v.getResource());

                if (condition) {
                    signContractDetailVO.setLimitFlag(1);
                } else {
                    if (CollUtil.isNotEmpty(contractPermissionIdSet)) {
                        contractPermissionIdSet.forEach(setIdArray -> {
                            if (Arrays.asList(setIdArray).contains(v.getId().toString())) {
                                //有权限
                                signContractDetailVO.setLimitFlag(1);
                            } else {
                                //无权限
                                signContractDetailVO.setLimitFlag(0);
                            }
                        });
                    } else {
                        //无权限
                        signContractDetailVO.setLimitFlag(0);
                    }
                }
                signContractDetailVOList.add(signContractDetailVO);
            });
        }


        //文件配置id集合
        List<Integer> fileConfigIdList = fileConfigData.getData().values().stream().toList();
        //查询其他文件信息
        List<OrderFileEntity> orderFileEntityList = orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                .eq(OrderFileEntity::getOrderId, orderId)
                .eq(OrderFileEntity::getDeleteFlag, 0)
                .in(CollUtil.isNotEmpty(fileConfigIdList), OrderFileEntity::getFileId, fileConfigIdList)
                .orderByDesc(OrderFileEntity::getCreateTime));


        zfjqzmVO.setFileName("资方结清证明");
        shzmVO.setFileName("赎回证明");
        if (CollUtil.isNotEmpty(orderFileEntityList)) {

            String zfjqzmSuffix = orderFileEntityList.stream().filter(v ->
                    FileConfigEnums.FUND_SETTLEMENT_VOUCHER.getFileId() == v.getFileId()).map(v ->
                    v.getFileName().substring(v.getFileName().indexOf("."))).distinct().collect(Collectors.joining());

            String shzmSuffix = orderFileEntityList.stream().filter(v ->
                    FileConfigEnums.SSZM.getFileId() == v.getFileId()).map(v ->
                    v.getFileName().substring(v.getFileName().indexOf("."))).distinct().collect(Collectors.joining());

            //后缀
            zfjqzmVO.setFileSuffix(zfjqzmSuffix);
            shzmVO.setFileSuffix(shzmSuffix);

            orderFileEntityList.forEach(v -> {
                //资方结清证明
                if (FileConfigEnums.FUND_SETTLEMENT_VOUCHER.getFileId() == v.getFileId()) {
                    zfjqzmFileUrlList.add(v.getResourceId());

                }
                //赎回证明
                if (FileConfigEnums.SSZM.getFileId() == v.getFileId()) {
                    shzmFileUrlList.add(v.getResourceId());
                }
            });
        }

        zfjqzmVO.setFileUrlList(zfjqzmFileUrlList);
        shzmVO.setFileUrlList(shzmFileUrlList);

        //资管部全员/资管主管/资管副总 拥有权限
        if (DeptEnum.ZI_GUAN_BU.hasRole(deptIdList)
                || RoleEnum.ASSET_MANAGER.hasRole(roleIdList)
                || RoleEnum.ASSET_AREA_VICE_PRESIDENT.hasRole(roleIdList)) {
            signContractInfoVO.setSignContractDetailVOList(signContractDetailVOList);
            signContractInfoVO.setShzmVO(shzmVO);
        }
        signContractInfoVO.setZfjqzmVO(zfjqzmVO);
        return signContractInfoVO;
    }


    /**
     * 获取 Enterprise 许可证信息
     *
     * @param orderId 订单ID
     * @return {@link EnterpriseLicenseVO }
     */
    public EnterpriseLicenseVO getEnterpriseLicenseInfo(Integer orderId) {
        OrderCompanyInfoEntity orderCompanyInfoEntity = orderCompanyInfoMapper.selectOne(new LambdaQueryWrapper<OrderCompanyInfoEntity>()
                .eq(OrderCompanyInfoEntity::getOrderId, orderId)
                .eq(OrderCompanyInfoEntity::getDeleteFlag, 0)
                .orderByDesc(OrderCompanyInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (Objects.isNull(orderCompanyInfoEntity)) {
            EnterpriseLicenseVO enterpriseLicenseVO = new EnterpriseLicenseVO();
            enterpriseLicenseVO.setExistFlag(2);
            return enterpriseLicenseVO;
        }

        EnterpriseLicenseVO enterpriseLicense = orderCompanyInfoConverter.entity2vo(orderCompanyInfoEntity);
        MenuVO enterpriseLicenseFile = getEnterpriseLicenseFile(orderId);
        enterpriseLicense.setExistFlag(1);
        if (Objects.nonNull(enterpriseLicenseFile) && CollUtil.isNotEmpty(enterpriseLicenseFile.getOrderFileInfo())) {
            MenuVO.OrderFileInfo orderFileInfo = enterpriseLicenseFile.getOrderFileInfo().get(0);
            enterpriseLicense.setResourceName(orderFileInfo.getResourceName());
            enterpriseLicense.setFileName(orderFileInfo.getFileName());
        }
        return enterpriseLicense;
    }

    /**
     * 获取订单信息
     *
     * @param orderInfoEntity 订单信息实体
     * @return {@link OrderInfoVO }
     */
    public OrderInfoVO getOrderInfo(OrderInfoEntity orderInfoEntity) {
        OrderInfoVO orderInfoVO = orderConverter.entity2vo(orderInfoEntity);
        ProductInfoEntity productInfoEntity = productInfoMapper.selectOne(new LambdaQueryWrapper<ProductInfoEntity>()
                .eq(ProductInfoEntity::getId, orderInfoEntity.getProductId())
                .eq(ProductInfoEntity::getDeleteFlag, 0)
        );
        if (Objects.nonNull(productInfoEntity)) {
            orderInfoVO.setYearRate(productInfoEntity.getYearRate());
            orderInfoVO.setGuaranteeFeeRate(productInfoEntity.getGuaranteeFee());
//            orderInfoVO.setGuaranteeFeeTerm(productInfoEntity.get);
        }
        if (Objects.nonNull(orderInfoEntity.getTeamId())) {
            List<DeptInfoVO> data = userFeign.getTheBranchNameBasedOnTheTeamId(List.of(orderInfoEntity.getTeamId())).getData();
            if (CollUtil.isNotEmpty(data)) {
                data.forEach(branchName -> {
                    orderInfoVO.setBranchName(ObjUtil.defaultIfNull(orderInfoVO.getStoreName(), "") + ObjUtil.defaultIfNull(branchName.getName(), ""));
                });
            } else {
                orderInfoVO.setBranchName(orderInfoVO.getStoreName());
            }
        } else {
            orderInfoVO.setBranchName(orderInfoVO.getStoreName());
        }
        if (Objects.equals(orderInfoEntity.getSourceType(), 1) && ((Objects.equals(orderInfoEntity.getRegionId(), 24)) || Objects.equals(orderInfoEntity.getRegionId(), 56))) {
            orderInfoVO.setIsOnlineOrder(1);
        } else {
            orderInfoVO.setIsOnlineOrder(2);
        }
        orderInfoVO.setDeptId(orderInfoEntity.getDeptId());
        orderDetailsInfoService.riskAIReportStatus(orderInfoVO, orderInfoEntity);
        return orderInfoVO;
    }

    /**
     * 获取客户信息
     *
     * @param orderId    订单ID
     * @param customerId 客户id
     * @param preId
     * @return {@link OrderCustomerInfoVo }
     */
    public CustomerInfoVO getCustomerInfo(Integer orderId, Integer customerId, Integer preId) {
        OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectOne(new LambdaQueryWrapper<OrderCustomerInfoEntity>()
                .eq(OrderCustomerInfoEntity::getId, customerId)
                .eq(OrderCustomerInfoEntity::getDeleteFlag, 0)
        );
        List<OrderContactPersonEntity> orderContactPersonEntityList = orderContactPersonMapper.selectList(new LambdaQueryWrapper<OrderContactPersonEntity>()
                .eq(OrderContactPersonEntity::getOrderId, orderId)
                .eq(OrderContactPersonEntity::getDeleteFlag, 0)
                .orderByAsc(OrderContactPersonEntity::getIndex)
        );
        CustomerInfoVO customerInfoVO = orderCustomerInfoConverter.orderEntity2vo(orderCustomerInfoEntity, orderContactPersonEntityList);
        List<OrderContactPersonVo> orderContactPersonVoList = getOrderContactPersonVos(customerInfoVO);
        customerInfoVO.setOrderContactPersonVoList(orderContactPersonVoList);
        log.info("OrderServiceImpl.getCustomerInfo orderContactPersonVoList:{}", JSONUtil.toJsonStr(orderContactPersonVoList));
        customerInfoVO.setBankCardInfo(getBankCardInfo(orderId));

        //查询客户信息维护表
        List<CustomerInfoMaintainEntity> customerInfoMaintains = customerInfoMaintainMapper.selectList(new LambdaQueryWrapper<CustomerInfoMaintainEntity>()
                .eq(CustomerInfoMaintainEntity::getOrderId, orderId)
                .eq(CustomerInfoMaintainEntity::getDeleteFlag, 0)
                .orderByDesc(CustomerInfoMaintainEntity::getIndex));
        if (CollUtil.isNotEmpty(customerInfoMaintains)) {
            CustomerInfoMaintainEntity customerInfoMaintain = new CustomerInfoMaintainEntity();
            BeanUtils.copyProperties(customerInfoVO, customerInfoMaintain, "id");
            BeanUtils.copyProperties(customerInfoMaintains.get(0), customerInfoVO, "id");
            customerInfoMaintain.setOrderId(orderId)
                    .setIndex(0);
            List<CustomerInfoMaintainEntity> customerInfoMaintainList = new ArrayList<>(customerInfoMaintains);

            customerInfoMaintainList.add(customerInfoMaintain);

            // 移除第一条记录
//            customerInfoMaintainList.remove(0);

            // 使用 Stream 对集合进行处理，将每个 CustomerInfoMaintainEntity 的 index 字段加 1
            customerInfoMaintainList.forEach(customerInfoMaintainEntity ->
                    customerInfoMaintainEntity.setIndex(customerInfoMaintainEntity.getIndex() + 1)
            );


            customerInfoVO.setCustomerInfoMaintainList(customerInfoMaintainList);
        }
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(preId);
        PreOcrIdentityCardEntity preOcrIdentityCardEntity = preOcrIdentityCardService.getDetailsByApplyInfoId(preId);
        String name = "";
        String idNumber = "";
        String province = "";
        String city = "";
        Integer proId = 0;
        Integer cityId = 0;
        if (ObjUtil.isNotEmpty(customerInfoVO.getName())) {
            name = customerInfoVO.getName();
        } else {
            name = preApprovalApplyInfoEntity.getName();
        }
        if (ObjUtil.isNotEmpty(customerInfoVO.getIdNumber())) {
            idNumber = customerInfoVO.getIdNumber();
        } else {
            idNumber = preApprovalApplyInfoEntity.getIdNumber();
        }
        if (ObjUtil.isNotEmpty(customerInfoVO.getNormalAddressPro())) {
            proId = customerInfoVO.getNormalAddressPro();
            province = customerInfoVO.getNormalAddressProName();
            cityId = customerInfoVO.getNormalAddressCity();
            city = customerInfoVO.getNormalAddressCityName();
        } else {
            proId = preOcrIdentityCardEntity.getProvinceId();
            province = preOcrIdentityCardEntity.getProvinceName();
            cityId = preOcrIdentityCardEntity.getCityId();
            city = preOcrIdentityCardEntity.getCityName();
        }
        parameterFilling(name, idNumber, province, city, proId, cityId, customerInfoVO);
        return customerInfoVO;
    }

    private void parameterFilling(String name, String idNumber, String province, String city, Integer
            proId, Integer cityId, CustomerInfoVO orderCustomerInfoVo) {
        if (ObjUtil.isEmpty(orderCustomerInfoVo.getEducationalBackground())) {
            String degree = xueliService.getEducation(new QueryVerifyV4DTO().setIdNo(idNumber).setName(name));
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(degree)) {
                if (degree.contains("博士")) {
                    orderCustomerInfoVo.setEducationalBackground(1);
                } else if (degree.contains("硕士")) {
                    orderCustomerInfoVo.setEducationalBackground(10);
                } else if (degree.contains("本科")) {
                    orderCustomerInfoVo.setEducationalBackground(20);
                } else if (degree.contains("专科")) {
                    orderCustomerInfoVo.setEducationalBackground(30);
                } else if (degree.contains("中专")) {
                    orderCustomerInfoVo.setEducationalBackground(40);
                } else if (degree.contains("高中")) {
                    orderCustomerInfoVo.setEducationalBackground(50);
                } else if (degree.contains("初中")) {
                    orderCustomerInfoVo.setEducationalBackground(60);
                }
            } else {
                orderCustomerInfoVo.setEducationalBackground(50);
            }
        }

        if (ObjUtil.isEmpty(orderCustomerInfoVo.getMaritalStatus())) {
            String userMarriage = null;
            boolean flag = false;
            try {
                userMarriage = xueliService.getUserMarriage(new GetUserMarriageDTO().setIdNumber(idNumber).setName(name));
                flag = true;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            if (StrUtil.isNotEmpty(userMarriage) && flag) {
                if (userMarriage.contains("未")) {
                    orderCustomerInfoVo.setMaritalStatus(0);
                } else if (userMarriage.contains("已婚")) {
                    orderCustomerInfoVo.setMaritalStatus(2);
                } else if (userMarriage.contains("离")) {
                    orderCustomerInfoVo.setMaritalStatus(4);
                }
            }
        }
        if (ObjUtil.isEmpty(orderCustomerInfoVo.getNormalAddress())) {
            orderCustomerInfoVo.setNormalAddress(province + city);
            orderCustomerInfoVo.setNormalAddressCityName(city);
            orderCustomerInfoVo.setNormalAddressProName(province);
            orderCustomerInfoVo.setNormalAddressCity(cityId);
            orderCustomerInfoVo.setNormalAddressPro(proId);
        }
        if (ObjUtil.isEmpty(orderCustomerInfoVo.getEnterpriseNature())) {
            orderCustomerInfoVo.setEnterpriseNature(7);
        }
        if (ObjUtil.isEmpty(orderCustomerInfoVo.getWorkType())) {
            orderCustomerInfoVo.setWorkType(2);
        }
    }

    /**
     * 获取订单联系人 VOS
     *
     * @param customerInfoVO 客户信息 VO
     * @return {@link List }<{@link OrderContactPersonVo }>
     */
    private @NotNull List<OrderContactPersonVo> getOrderContactPersonVos(CustomerInfoVO customerInfoVO) {
        if (Objects.nonNull(customerInfoVO) && CollUtil.isNotEmpty(customerInfoVO.getOrderContactPersonVoList())) {
            List<OrderContactPersonVo> orderContactPersonVoList = Optional.ofNullable(customerInfoVO.getOrderContactPersonVoList()).orElseGet(ArrayList::new);
            // 使用 stream 遍历并塞入值返回
            orderContactPersonVoList.stream()
                    .filter(item -> Objects.equals(1, item.getIndex()))
                    .forEach(this::updateContactPerson);
            log.info("OrderServiceImpl.getOrderContactPersonVos orderContactPersonVoList:{}", JSONUtil.toJsonStr(orderContactPersonVoList));
            return orderContactPersonVoList;
        }
        return new ArrayList<>();
    }

    /**
     * 更新联系人
     *
     * @param item 项目
     */
    private void updateContactPerson(OrderContactPersonVo item) {
        String name = item.getName();
        String idNumber = item.getIdNumber();
        String phone = item.getPhone();
        log.info("OrderServiceImpl.updateContactPerson name:{} idNumber:{} phone:{}", name, idNumber, phone);
        FactorVO factor = orderServicePrivateMethod.getFactor(name, phone, idNumber);
        log.info("OrderServiceImpl.updateContactPerson factor:{}", JSONUtil.toJsonStr(factor));
        if (factor != null) {
            item.setNetworkDuration(PhoneOnlineEnum.getDescriptionByCode(factor.getNetworkDuration()));
            item.setThreeFactorVerify(factor.getThreeFactorVerify());
            String twoFactorVerify = "";
            if (!factor.getFactorVerify()) {
                OrderContactPersonEntity orderContactPersonEntity = orderContactPersonMapper.selectById(item.getId());
                log.info("OrderServiceImpl.updateContactPerson orderContactPersonEntity:{}", JSONUtil.toJsonStr(orderContactPersonEntity));
                if (orderContactPersonEntity != null && orderContactPersonEntity.getTwoFactorVerify() != null) {
                    if (("1").equals(orderContactPersonEntity.getTwoFactorVerify())) {
                        twoFactorVerify = "二要素认证通过";
                    } else if (("-1").equals(orderContactPersonEntity.getTwoFactorVerify())) {
                        twoFactorVerify = "查无记录";
                    } else {
                        twoFactorVerify = "二要素认证不通过";
                    }
                }
            }
            item.setTwoFactorVerify(twoFactorVerify);
        }
    }

    /**
     * 获取银行卡信息
     *
     * @param orderId 订单ID
     * @return {@link BankCardInfoVO }
     */
    public BankCardInfoVO getBankCardInfo(Integer orderId) {
        BankAccountSignEntity bankAccountSignEntity = bankAccountSignMapper.selectOne(new LambdaQueryWrapper<BankAccountSignEntity>()
                        .eq(BankAccountSignEntity::getOrderId, orderId)
                        .eq(BankAccountSignEntity::getSignState, 1)
                        .eq(BankAccountSignEntity::getDeleteFlag, 0)
                        .orderByDesc(BankAccountSignEntity::getCreateTime)
                , false
        );
        return bankAccountSignConverter.entity2vo(bankAccountSignEntity);
    }

    /**
     * 获取车辆信息
     *
     * @param orderId 订单id
     * @return {@link OrderVehicleInfoVO}
     */
    public OrderVehicleInfoVO getVehicleInfo(Integer orderId) {
        return orderVehicleInfoService.getVehicleInfo1(orderId);

    }

    /**
     * 获取车辆补充信息
     *
     * @param orderId 订单ID
     * @return {@link VehicleSupplementInfoVO }
     */
    public VehicleSupplementInfoVO getVehicleSupplementInfo(Integer orderId) {
        OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(new MPJLambdaWrapper<OrderVehicleInfoEntity>()
                .eq(OrderVehicleInfoEntity::getOrderId, orderId)
                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
        );
        log.info("OrderServiceImpl.getVehicleSupplementInfo orderVehicleInfoEntity: {}", JSONUtil.toJsonStr(orderVehicleInfoEntity));
        if (Objects.isNull(orderVehicleInfoEntity)) {
            return null;
        }
        orderDetailsInfoService.getVehicleSupplementInfo(orderVehicleInfoEntity);

        return orderVehicleInfoConverter.entity2SupplementVo(orderVehicleInfoEntity);
    }


    /**
     * 更新订单还款信息
     *
     * @param orderId 订单id
     */
    @Override
    public Boolean updateOrderFundRepayment(Integer orderId) {
        OrderInfoEntity orderInfo = super.getById(orderId);
        if (orderInfo == null) {
            throw new BusinessException("订单不存在");
        }

        //是否结清
        boolean planState = orderInfo.getPlanState() != null && orderInfo.getPlanState() == 1;

        // 获取资方还款计划列表
        List<FundRepaymentInfoEntity> fundRepaymentInfoList = fundRepaymentInfoService.queryByOrderIdList(orderId, orderInfo.getFundId());
        boolean isOverdue = false;
        int maxOverdueDays = 0;
        BigDecimal totalOverdueAmount = BigDecimal.ZERO;

        if (!planState && CollectionUtil.isNotEmpty(fundRepaymentInfoList)) {

            // 计算当前是否逾期、最大逾期天数和当前逾期总额
            for (FundRepaymentInfoEntity info : fundRepaymentInfoList) {
                LocalDate now = LocalDate.now();
                LocalDate repaymentDate = info.getRepaymentDate();
                LocalDate actuallyDate = info.getActuallyDate();
                BigDecimal repaymentAmountTotal = ObjUtil.defaultIfNull(info.getRepaymentAmountTotal(), BigDecimal.ZERO);
                BigDecimal actuallyAmountTotal = ObjUtil.defaultIfNull(info.getActuallyAmountTotal(), BigDecimal.ZERO);

                FundRepayStatusEnum repaymentStatus = info.getRepaymentStatus();

                if (repaymentDate != null
                        && repaymentStatus != FundRepayStatusEnum.SETTLED
                        && repaymentDate.isBefore(now)) {
                    int overdueDays = 0;
                    // 应还日期 < 当前日期
                    if (actuallyDate == null || repaymentAmountTotal.compareTo(actuallyAmountTotal) != 0) {
                        // 无实还日期或应还总额不等于实还总额
                        isOverdue = true;
                        overdueDays = (int) ChronoUnit.DAYS.between(repaymentDate, now);


                        maxOverdueDays = Math.max(maxOverdueDays, overdueDays);
                        totalOverdueAmount = totalOverdueAmount.add(repaymentAmountTotal.subtract(actuallyAmountTotal));
                    }
                }
            }

        }
        LambdaUpdateWrapper<OrderInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderInfoEntity::getId, orderId)
                .set(OrderInfoEntity::getIsOverdue, isOverdue ? 1 : 0)
                .set(OrderInfoEntity::getOverdueDays, maxOverdueDays)
                .set(OrderInfoEntity::getTotalOverdueAmount, totalOverdueAmount);

        super.update(updateWrapper);
        // todo 如果 isOverdue为true 更新caseinfo delete=1


        return true;
    }

    @Override
    public Page<OverdueOrdersListVO> overdueOrdersList(OverdueOrdersDTO overdueOrdersDTO, LoginUser loginUser) {
        MPJLambdaWrapper<OrderInfoEntity> wrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .selectAll(OrderInfoEntity.class)
                .selectAs(OrderInfoEntity::getId, OrderApproveListVO::getOrderId)
                .selectAs(OrderInfoEntity::getRiskUserId, OrderApproveListVO::getRisiUserId)
                .selectAs(FundInfoEntity::getName, OrderApproveListVO::getFundName)
                .selectAs(OrderAmountEntity::getCustomerConfirmAmount, OverdueOrdersListVO::getCustomerAmount)

                .leftJoin(FundInfoEntity.class, FundInfoEntity::getId, OrderInfoEntity::getFundId)
                .leftJoin(OrderAmountEntity.class, OrderAmountEntity::getOrderId, OrderInfoEntity::getId)
                .like(StrUtil.isNotBlank(overdueOrdersDTO.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, overdueOrdersDTO.getVehicleNumber())
                .like(StrUtil.isNotBlank(overdueOrdersDTO.getOrderNumber()), OrderInfoEntity::getOrderNumber, overdueOrdersDTO.getOrderNumber())
                .like(StrUtil.isNotBlank(overdueOrdersDTO.getCustomerName()), OrderInfoEntity::getCustomerName, overdueOrdersDTO.getCustomerName())
                .like(StrUtil.isNotBlank(overdueOrdersDTO.getCustomerPhone()), OrderInfoEntity::getCustomerPhone, overdueOrdersDTO.getCustomerPhone())
                .eq(OrderInfoEntity::getState, States.PAYMENT_SUCCESS.getNode())
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .orderByAsc(OrderInfoEntity::getPaymentTime);
        if (StrUtil.isNotBlank(overdueOrdersDTO.getTaskPoolsName())) {
//            if (overdueOrdersDTO.getLevel() == 1) {
//                wrapper.between(OrderInfoEntity::getOverdueDays, 1, 3);
//            } else if (overdueOrdersDTO.getLevel() == 2) {
//                wrapper.between(OrderInfoEntity::getOverdueDays, 4, 45);
//            } else if (overdueOrdersDTO.getLevel() != 0) {
//                throw new BusinessException("暂无该任务池");
//            }
            OverdueWarnConfigEntity overdueWarnConfigEntity = overdueWarnConfigService.getOne(new LambdaQueryWrapper<OverdueWarnConfigEntity>()
                    .eq(OverdueWarnConfigEntity::getOverdueName, overdueOrdersDTO.getTaskPoolsName())
                    .orderByDesc(OverdueWarnConfigEntity::getCreateTime), false);
            if (ObjUtil.isNull(overdueWarnConfigEntity)) {
                throw new BusinessException("暂无该任务池");
            }
            if (ObjUtil.equals(overdueWarnConfigEntity.getEnable(), 1)) {
                throw new BusinessException("该任务池未启用");
            }
            wrapper.between(OrderInfoEntity::getOverdueDays, overdueWarnConfigEntity.getOverdueStartWarnDays(), overdueWarnConfigEntity.getOverdueEndWarnDays());
        }
        // 数据权限
        dataPermissionService.limitOverdueOrder(loginUser, wrapper);

        Page<OverdueOrdersListVO> pageList = orderInfoMapper.selectJoinPage(new Page<>(overdueOrdersDTO.getPageNum(), overdueOrdersDTO.getPageSize()), OverdueOrdersListVO.class, wrapper);
        List<OverdueOrdersListVO> records = pageList.getRecords();
//        records.forEach(item -> {
//            item.setCustomerPhone(StrUtil.hide(item.getCustomerPhone(), 0, 7));
//        });
        // 查询经办人信息
        List<Integer> manageIds = records.stream().map(OverdueOrdersListVO::getManagerId).filter(Objects::nonNull).toList();
        Result<List<UserStoreVO>> listResult = userFeign.searchUserStoreBatch(manageIds);
        List<Integer> teamIds = records.stream().map(OverdueOrdersListVO::getTeamId).filter(Objects::nonNull).toList();
        List<DeptInfoVO> branchNameVOS = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData();
        Map<Integer, String> branchNameMap = branchNameVOS.stream().filter(e -> ObjUtil.isNotNull(e.getName())).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
        if (Result.isSuccess(listResult)) {

            Map<Integer, UserStoreVO> managerInfos =
                    listResult.getData().stream().collect(Collectors.toMap(UserStoreVO::getUserId, item -> item));


            records.stream().filter(item -> item.getManagerId() != null).forEach(record -> {
                        UserStoreVO userInfoVOS = managerInfos.get(record.getManagerId());
                        record.setManagerName(userInfoVOS.getName());
                        record.setStoreName(userInfoVOS.getStore());
                        record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(branchNameMap.get(record.getTeamId()), ""));
                    }
            );

        }
        return pageList;
    }


    /**
     * 保存企业许可证
     *
     * @param enterpriseLicenseDTO 企业许可证 DTO
     * @return {@link Boolean }
     */
    @Override

    public Boolean saveEnterpriseLicense(EnterpriseLicenseDTO enterpriseLicenseDTO) {
        Integer orderId = enterpriseLicenseDTO.getOrderId();
        Integer existFlag = enterpriseLicenseDTO.getExistFlag();

        if (Objects.equals(existFlag, 1)) {
            OrderCompanyInfoEntity orderCompanyInfoEntity = null;
            if (Objects.nonNull(orderId)) {
                orderCompanyInfoEntity = orderCompanyInfoMapper.selectOne(new LambdaQueryWrapper<OrderCompanyInfoEntity>()
                        .eq(OrderCompanyInfoEntity::getOrderId, orderId)
                        .eq(OrderCompanyInfoEntity::getDeleteFlag, 0)
                );
            }
            //处理营业执照文件
            this.updateEnterpriseLicenseFile(enterpriseLicenseDTO);

            OrderCompanyInfoEntity orderCompanyInfo = orderCompanyInfoConverter.dto2entity(enterpriseLicenseDTO);
            orderCompanyInfoService.convertCompanyIndustry(orderCompanyInfo);

            if (Objects.nonNull(orderCompanyInfoEntity)) {
                //将enterpriseLicenseDTO的信息更新到orderCompanyInfoEntity
                orderCompanyInfo.setId(orderCompanyInfoEntity.getId());
                orderCompanyInfoMapper.updateById(orderCompanyInfo);
                return true;
            } else {
                orderCompanyInfoMapper.insert(orderCompanyInfo);
                return true;
            }

        } else if (Objects.equals(existFlag, 2)) {
            orderCompanyInfoMapper.update(new LambdaUpdateWrapper<OrderCompanyInfoEntity>()
                    .set(OrderCompanyInfoEntity::getDeleteFlag, 1)
                    .eq(OrderCompanyInfoEntity::getOrderId, orderId)
                    .eq(OrderCompanyInfoEntity::getDeleteFlag, 0)
            );
        }
        return true;

    }


    @Override
    public Boolean updateFundContract(OrderFundContractDTO fundStatusDTO) {
        OrderContractEntity orderContractEntity = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, fundStatusDTO.getOrderId())
                .eq(OrderContractEntity::getContractFlag, 1)
                .eq(OrderContractEntity::getName, fundStatusDTO.getContractName())
                .eq(OrderContractEntity::getDeleteFlag, 0)
        );
        if (orderContractEntity != null) {
            orderContractEntity.setResource(fundStatusDTO.getResourceId());
            orderContractEntity.setSignStatus(2);
            orderContractMapper.updateById(orderContractEntity);
            return true;
        } else {
            orderContractEntity = new OrderContractEntity();
            orderContractEntity.setOrderId(fundStatusDTO.getOrderId())
                    .setContractFlag(1)
                    .setName(fundStatusDTO.getContractName())
                    .setTemplateId(fundStatusDTO.getTemplateId())
                    .setSignStatus(2)
                    .setFundGenerateFlag(1)
                    .setResource(fundStatusDTO.getResourceId());
            orderContractMapper.insert(orderContractEntity);
        }
        return null;
    }

    /**
     * 历史订单
     *
     * @param historyOrderDTO 历史记录顺序 DTO
     * @return {@link List }<{@link HistoryOrderVO }>
     */
    @Override
    public List<HistoryOrderVO> historyOrder(HistoryOrderDTO historyOrderDTO) {
        Integer originalPreId = historyOrderDTO.getPreId();

        // 获取最终使用的预审批ID
        Integer finalPreId = getFinalPreId(originalPreId);

        List<HistoryOrderVO> orderFileInfoList = orderInfoMapper.selectJoinList(HistoryOrderVO.class, new MPJLambdaWrapper<OrderInfoEntity>()
                .selectAs(OrderInfoEntity::getId, HistoryOrderVO::getOrderId)
                .selectAs(OrderInfoEntity::getOrderNumber, HistoryOrderVO::getOrderNumber)
                .selectAs(OrderInfoEntity::getCustomerName, HistoryOrderVO::getName)
                .selectAs(OrderInfoEntity::getState, HistoryOrderVO::getState)
                .selectAs(OrderInfoEntity::getCreateTime, HistoryOrderVO::getCreateTime)
                .eq(OrderInfoEntity::getPreId, finalPreId)
                .orderByDesc(OrderInfoEntity::getCreateTime)
        );
        orderFileInfoList.forEach(historyOrderVO -> {
            historyOrderVO.setStateName(States.getNode(historyOrderVO.getState()).getDesc());
        });
        return orderFileInfoList;
    }

    /**
     * 获取最终使用的预审批ID
     * 如果预审批记录中存在oldPreId且不为空，则使用oldPreId；否则使用原始preId
     *
     * @param preId 原始预审批ID
     * @return 最终使用的预审批ID
     */
    private Integer getFinalPreId(Integer preId) {
        if (preId == null) {
            log.warn("getFinalPreId: 传入的preId为空");
            return preId;
        }

        try {
            // 查询预审批申请信息
            PreApprovalApplyInfoEntity preApprovalInfo = preApprovalApplyInfoMapper.selectOne(
                    new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
                            .eq(PreApprovalApplyInfoEntity::getId, preId)
                            .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
            );

            if (preApprovalInfo == null) {
                log.warn("getFinalPreId: 未找到preId={}对应的预审批记录", preId);
                return preId;
            }

            // 检查oldPreId字段
            Integer oldPreId = preApprovalInfo.getOldPreId();
            if (oldPreId != null && oldPreId > 0) {
                log.info("getFinalPreId: 使用oldPreId={} 替换原始preId={}", oldPreId, preId);
                return oldPreId;
            } else {
                log.debug("getFinalPreId: oldPreId为空或无效，继续使用原始preId={}", preId);
                return preId;
            }

        } catch (Exception e) {
            log.error("getFinalPreId: 查询预审批记录异常，preId={}", preId, e);
            // 发生异常时返回原始preId，确保业务不中断
            return preId;
        }
    }

    @Override
    public List<CheckRelatedInfoVO> checkRelatedInfo(Integer orderId) {
        log.info("OrderServiceImpl.checkRelatedInfo orderId :{}", orderId);
        if (ObjUtil.isEmpty(orderId)) {
            throw new BusinessException("订单ID不能为空");
        }
        //获取订单信息
        RelatedOrderVO orderInfoEntity = orderInfoMapper.selectJoinOne(RelatedOrderVO.class,
                new MPJLambdaWrapper<OrderInfoEntity>()
                        .selectAs(OrderInfoEntity::getId, RelatedOrderVO::getOrderId)
                        .selectAs(OrderVehicleInfoEntity::getVin, RelatedOrderVO::getVin)
                        .selectAs(OrderCustomerInfoEntity::getIdNumber, RelatedOrderVO::getIdNumber)
                        .leftJoin(OrderCustomerInfoEntity.class, OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId)
                        .leftJoin(OrderVehicleInfoEntity.class, OrderVehicleInfoEntity::getOrderId, OrderInfoEntity::getId)
                        .eq(OrderInfoEntity::getId, orderId)
        );
        log.info("OrderServiceImpl.checkRelatedInfo orderInfoEntity :{}", orderInfoEntity);
        if (ObjUtil.isEmpty(orderInfoEntity)) {
            throw new BusinessException("未查询到订单信息");
        }
        List<CheckRelatedInfoVO> checkRelatedInfoVOList = new ArrayList<>();

        // 一次查询获取所有需要的数据
        List<RelatedOrderVO> allRelatedOrders = orderInfoMapper.selectJoinList(RelatedOrderVO.class,
                new MPJLambdaWrapper<OrderInfoEntity>()
                        .selectAs(OrderInfoEntity::getId, RelatedOrderVO::getOrderId)
                        .selectAs(OrderInfoEntity::getOrderNumber, RelatedOrderVO::getOrderNumber)
                        .selectAs(OrderVehicleInfoEntity::getVin, RelatedOrderVO::getVin)
                        .selectAs(OrderVehicleInfoEntity::getVehicleNumber, RelatedOrderVO::getVehicleNumber)
                        .selectAs(OrderCustomerInfoEntity::getIdNumber, RelatedOrderVO::getIdNumber)
                        .selectAs(OrderInfoEntity::getCurrentNode, RelatedOrderVO::getCurrentNode)
                        .leftJoin(OrderCustomerInfoEntity.class, OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId)
                        .leftJoin(OrderVehicleInfoEntity.class, OrderVehicleInfoEntity::getOrderId, OrderInfoEntity::getId)
                        .eq(OrderInfoEntity::getDeleteFlag, 0)
                        .ne(OrderInfoEntity::getId, orderId)
                        .and(and -> and.eq(OrderCustomerInfoEntity::getIdNumber, orderInfoEntity.getIdNumber())
                                .or()
                                .eq(OrderVehicleInfoEntity::getVin, orderInfoEntity.getVin())
                        )
        );
        allRelatedOrders.forEach(relatedOrderVO -> {
            relatedOrderVO.setSysId(1);
            relatedOrderVO.setCurrentNodeName(States.getNode(relatedOrderVO.getCurrentNode()).getDesc());
        });
        List<PlatformRelatedOrderVO> platformRelatedOrderVOS = dataFeign.getPlatformRelatedOrder(new PlatformRelatedOrderDTO().setType(1).setTypeData(orderInfoEntity.getIdNumber())).getData();
        platformRelatedOrderVOS.addAll(dataFeign.getPlatformRelatedOrder(new PlatformRelatedOrderDTO().setType(3).setTypeData(orderInfoEntity.getVin())).getData());
        platformRelatedOrderVOS = platformRelatedOrderVOS.stream()
                .distinct()
                .collect(Collectors.toList());
        allRelatedOrders.addAll(platformRelatedOrderVOS.stream().map(platformRelatedOrderVO ->
                BeanUtil.toBean(platformRelatedOrderVO, RelatedOrderVO.class)
        ).toList());
        log.info("OrderServiceImpl.checkRelatedInfo allRelatedOrders.size :{}", allRelatedOrders.size());

        // 过滤出同一车架号、身份证号的订单信息
        List<RelatedOrderVO> sameVinAndIdNoOrders = allRelatedOrders.stream()
                .filter(order -> Objects.equals(order.getVin(), orderInfoEntity.getVin()) && Objects.equals(order.getIdNumber(), orderInfoEntity.getIdNumber()))
                .collect(Collectors.toList());

        CheckRelatedInfoVO checkRelatedInfoVO = createCheckRelatedInfoVO(OrderRelatedEnum.ALL_SAME.getCode(), sameVinAndIdNoOrders);
        checkRelatedInfoVOList.add(checkRelatedInfoVO);

        // 过滤出同一身份证号且不同车架号的订单信息
        List<RelatedOrderVO> sameIdNoOrders = allRelatedOrders.stream()
                .filter(order -> Objects.equals(order.getIdNumber(), orderInfoEntity.getIdNumber()) && !Objects.equals(order.getVin(), orderInfoEntity.getVin()))
                .collect(Collectors.toList());

        CheckRelatedInfoVO idNumberInfo = createCheckRelatedInfoVO(OrderRelatedEnum.SAME_ID_NUMBER.getCode(), sameIdNoOrders);
        checkRelatedInfoVOList.add(idNumberInfo);

        // 过滤出同一车架号且不同身份证号的订单信息
        List<RelatedOrderVO> sameVinOrders = allRelatedOrders.stream()
                .filter(order -> Objects.equals(order.getVin(), orderInfoEntity.getVin()) && !Objects.equals(order.getIdNumber(), orderInfoEntity.getIdNumber()))
                .collect(Collectors.toList());

        CheckRelatedInfoVO vinInfo = createCheckRelatedInfoVO(OrderRelatedEnum.SAME_VIN.getCode(), sameVinOrders);
        checkRelatedInfoVOList.add(vinInfo);
        log.info("OrderServiceImpl.checkRelatedInfo checkRelatedInfoVOList :{}", checkRelatedInfoVOList);
        return checkRelatedInfoVOList;
    }

    @Override
    public Boolean processTerminal() {
        //流程自动终止配置
        List<OrderTerminalConfigEntity> configEntityList = orderTerminalConfigMapper.selectList(new LambdaQueryWrapper<OrderTerminalConfigEntity>()
                .eq(OrderTerminalConfigEntity::getDeleteFlag, 0));

        log.info("OrderServiceImpl.processTerminal configEntityList.size:{}", configEntityList.size());
        if (CollUtil.isNotEmpty(configEntityList)) {
            Map<Integer, List<OrderTerminalConfigEntity>> groupedByProcessStage = configEntityList.stream()
                    .collect(Collectors.groupingBy(OrderTerminalConfigEntity::getProcessStage));
            groupedByProcessStage.forEach((key, value) -> {
                List<Integer> processNodeList = value.stream()
                        .map(OrderTerminalConfigEntity::getProcessNode)
                        .toList();
                OrderTerminalDTO dto = new OrderTerminalDTO();
                dto.setProcessStage(key);
                dto.setNodeList(processNodeList);
                dto.setValidityDay(value.get(0).getValidityDay());
                //根据失效规则查询订单信息
                List<OrderTerminalVO> infoList = orderTerminalConfigMapper.getTerminalInfo(dto);
                //符合条件的节点信息
                List<Integer> orderIds = new ArrayList<>();
                List<Integer> preIds = new ArrayList<>();
                if (CollUtil.isNotEmpty(infoList)) {
                    preIds = infoList.stream().map(OrderTerminalVO::getPreId)
                            .filter(Objects::nonNull)  // 过滤掉 null 值
                            .collect(Collectors.toList());
                    log.info("OrderServiceImpl.checkRelatedInfo preIds:{}", preIds);
                    if (CollUtil.isNotEmpty(preIds)) {
                        //将预审信息置为系统终止
                        int updatePre = preApprovalApplyInfoMapper.update(new LambdaUpdateWrapper<PreApprovalApplyInfoEntity>()
                                .set(PreApprovalApplyInfoEntity::getBusinessStatus, ApprovalApplyInfoBusinessStatus.PRE_SYSTEM_TERMINAL.getCode())
                                .set(PreApprovalApplyInfoEntity::getUpdateTime, LocalDateTime.now())
                                .in(PreApprovalApplyInfoEntity::getId, preIds));
                        log.info("OrderServiceImpl.checkRelatedInfo updatePre:{}", updatePre);
                    }

                    log.info("OrderServiceImpl.checkRelatedInfo orderIds:{}", orderIds);
                    OrderStateService orderStateService = SpringContentUtils.getBean(OrderStateService.class);
                    infoList.forEach(item -> {
                        try {
                            //系统自动终止，取消资方订单
                            Result<String> stringResult = approveFeign.fundApproveCancel(new FundApproveCancelDTO().setType(2).setLinkId(item.getOrderId()));
                            if (!Result.isSuccess(stringResult)) {
                                log.info("OrderServiceImpl.checkRelatedInfo approveFeign.fundApproveCancel error:{}", stringResult.getMsg());
                            }
                            orderStateService.sendEvent(States.getNode(item.getCurrentNode()),
                                    Events.SYSTEM_TERMINAL, item.getOrderId(), 1);
                            try {
                                States currentNode = States.getNode(item.getCurrentNode());
                                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(item.getOrderId());
                                if (Objects.equals(currentNode, States.RISK_FIRST_APPROVE)
                                        || Objects.equals(currentNode, States.RISK_FIRST_APPROVE_ASSIGN)
                                        || Objects.equals(currentNode, States.QUALITY_INSPECTION)
                                        || (Objects.equals(currentNode, States.CUSTOMER_APPOINTMENT) && ObjUtil.equals(orderInfoEntity.getReviewState(), 1))
                                        || Objects.equals(currentNode, States.PAYMENT_APPLY_INFORMATION)) {
                                    Integer node = currentNode.getNode();
                                    if (Objects.equals(currentNode, States.CUSTOMER_APPOINTMENT)) {
                                        node = States.MANAGER_INTERVIEW.getNode();
                                    }
                                    OrderApproveDistributeService orderApproveDistributeService = SpringContentUtils.getBean(OrderApproveDistributeService.class);
                                    orderApproveDistributeService.updateOrderApproveDistribute(new UpdateOrderApproveDistributeDTO()
                                            .setOrderId(item.getOrderId())
                                            .setOrderNumber(orderInfoEntity.getOrderNumber())
                                            .setSource(0)
                                            .setBusinessType(3)
                                            .setCurrentNode(node));

                                }
                            } catch (Exception e) {
                                log.error("OrderServiceImpl.checkRelatedInfo execute error", e);
                            }
                        } catch (Exception e) {
                            log.error("OrderServiceImpl.checkRelatedInfo order:{} error:{}", item.getOrderId(),
                                    e.getMessage(), e);
                        }
                    });

                }
            });


//            //预审通过信息
//            List<PreFundApprovalVO> approvalVOList = preApprovalApplyInfoMapper.selectJoinList(
//                    PreFundApprovalVO.class,
//                    new MPJLambdaWrapper<PreApprovalApplyInfoEntity>()
//                            .selectAs(PreApprovalApplyInfoEntity::getId, PreFundApprovalVO::getPreId)
//                            .selectAs(PreFundInfoEntity::getFundResult, PreFundApprovalVO::getPreFundStatus)
//                            .selectAs(PreFundInfoEntity::getFundCreditTime, PreFundApprovalVO::getFundCreditTime)
//                            .selectAs(OrderInfoEntity::getId, PreFundApprovalVO::getOrderId)
//                            .leftJoin(OrderInfoEntity.class, on -> on
//                                    .eq(PreApprovalApplyInfoEntity::getId, OrderInfoEntity::getPreId)
//                                    .in(OrderInfoEntity::getCurrentNode, processNodeList)
//                            )
//                            .leftJoin(PreFundInfoEntity.class, on -> on
//                                    .eq(PreFundInfoEntity::getPreId, PreApprovalApplyInfoEntity::getId)
//                                    .eq(PreFundInfoEntity::getFundResult, PreFundResultEnum.PASS.getValue()))
//                            .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
//                            .
//            );
//
//            //符合条件的节点信息
//            List<Integer> orderIds = new ArrayList<>();
//            List<Integer> preIds = new ArrayList<>();
//            List<Integer> nodeList = new ArrayList<>();
//            for (OrderTerminalConfigEntity configEntity : configEntityList){
//                OrderTerminalDTO dto = orderTerminalConfigConverter.entity2Dto(configEntity);
//                if(Objects.equals(configEntity.getProcessNode(), States.PRE_APPROVAL.getNode()) || Objects.equals(configEntity.getProcessNode(), States.RISK_CONTROL.getNode()) ||
//                Objects.equals(configEntity.getProcessNode(), States.FUNDS_PRE_APPROVAL.getNode())){
//                    dto.setProcessStage(0);
//                }else {
//                    dto.setProcessStage(1);
//                }
//                nodeList.add(configEntity.getProcessNode());
//                dto.setNodeList(nodeList);
//
//                //根据失效规则查询订单信息
//                List<OrderTerminalVO> infoList = orderTerminalConfigMapper.getTerminalInfo(dto);
//                log.info("OrderServiceImpl.checkRelatedInfo infoList.size:{}", infoList.size());
//                if(CollUtil.isNotEmpty(infoList)){
//                    preIds = infoList.stream().map(OrderTerminalVO::getPreId)
//                            .filter(Objects::nonNull)  // 过滤掉 null 值
//                            .collect(Collectors.toList());
//                    log.info("OrderServiceImpl.checkRelatedInfo preIds:{}", preIds);
//                    if(CollUtil.isNotEmpty(preIds)){
//                        //将预审信息置为系统终止
//                        int updatePre = preApprovalApplyInfoMapper.update(new LambdaUpdateWrapper<PreApprovalApplyInfoEntity>()
//                                .set(PreApprovalApplyInfoEntity::getBusinessStatus, ApprovalApplyInfoBusinessStatus.PRE_SYSTEM_TERMINAL.getCode())
//                                .set(PreApprovalApplyInfoEntity::getUpdateTime, LocalDateTime.now())
//                                .in(PreApprovalApplyInfoEntity::getId, preIds));
//                        log.info("OrderServiceImpl.checkRelatedInfo updatePre:{}", updatePre);
//                    }
//
//                    log.info("OrderServiceImpl.checkRelatedInfo orderIds:{}", orderIds);
//                    OrderStateService orderStateService = SpringContentUtils.getBean(OrderStateService.class);
//                    infoList.forEach(item -> {
//                        try {
//                            orderStateService.sendEvent(States.getNode(item.getCurrentNode()),
//                                    Events.SYSTEM_TERMINAL, item.getOrderId(), 1);
//                        } catch (Exception e) {
//                            log.error("OrderServiceImpl.checkRelatedInfo order:{} error:{}", item.getOrderId(),
//                                    e.getMessage(), e);
//                        }
//                    });
//
//                }
        }
        return true;
    }

    private CheckRelatedInfoVO createCheckRelatedInfoVO(Integer
                                                                sameType, List<RelatedOrderVO> relatedOrderVOList) {
        CheckRelatedInfoVO checkRelatedInfoVO = new CheckRelatedInfoVO();
        checkRelatedInfoVO.setSameType(sameType);
        checkRelatedInfoVO.setRelatedOrderVOList(relatedOrderVOList);
        return checkRelatedInfoVO;
    }

    /**
     * 更新企业许可证文件
     *
     * @param enterpriseLicenseDTO 企业许可证 DTO
     */
    public void updateEnterpriseLicenseFile(EnterpriseLicenseDTO enterpriseLicenseDTO) {
        Integer orderId = enterpriseLicenseDTO.getOrderId();
        if (Objects.isNull(orderId)) {
            throw new BusinessException("订单id不能为空");
        }
        MenuVO enterpriseLicenseFile = getEnterpriseLicenseFile(orderId);
        Integer fileId = enterpriseLicenseFile.getFileId();
        List<MenuVO.OrderFileInfo> orderFileInfo = enterpriseLicenseFile.getOrderFileInfo();
        if (CollUtil.isNotEmpty(orderFileInfo)) {
            List<Integer> ids = orderFileInfo.stream().map(MenuVO.OrderFileInfo::getId).toList();
            orderFileMenuServiceImpl.removeOrderFile(new RemoveOrderFileDTO().setOrderFileId(ids));
        }
        SaveOrderFileDTO saveOrderFileDTO = new SaveOrderFileDTO()
                .setOrderId(orderId)
                .setFileId(fileId)
                .setFileName(enterpriseLicenseDTO.getFileName())
                .setResourceId(enterpriseLicenseDTO.getResourceId())
                .setResourceName(enterpriseLicenseDTO.getResourceName());
        orderFileMenuServiceImpl.saveOrderFile(saveOrderFileDTO);
    }

    /**
     * 获取企业许可证文件
     *
     * @param orderId 订单ID
     * @return {@link MenuVO }
     */
    public MenuVO getEnterpriseLicenseFile(Integer orderId) {
        FileConfigVO busLic = orderFileMenuServiceImpl.getFileConfigByCode("BUS_LIC");
        Assert.notNull(busLic, "企业营业执照文件配置不存在");
        Integer id = busLic.getId();
        List<Integer> fileId = List.of(id);
        List<Integer> menuId = List.of(1);
        List<MenuVO> menu = orderFileMenuServiceImpl.getMenuFileList(orderId, menuId, fileId);
//        MenuVO menuInfo = Optional.ofNullable(menu).map(items -> items.get(0)).orElse(null);
        MenuVO menuInfo = CollUtil.isNotEmpty(menu) ? CollUtil.getFirst(menu) : null;

        if (Objects.nonNull(menuInfo)) {
            return menuInfo;
        }
        return new MenuVO().setFileId(id);
    }


    /**
     * 更新订单终审信息
     *
     * @param fundStatusDTO 资方状态信息
     */
    private void updateOrderInfo(FinalApproveFundStatusDTO fundStatusDTO) {
        // 判断资方是否通过
        if (fundStatusDTO.getStatus() == PreFundResultEnum.PASS) {
            log.info("OrderServiceImpl.updateOrderInfo fundFinal PASS orderId:{}", fundStatusDTO.getOrderId());
            updateManagementConclusion(fundStatusDTO.getOrderId(), 1);
        } else if (fundStatusDTO.getStatus() == PreFundResultEnum.REJECT) {
            log.info("OrderServiceImpl.updateOrderInfo fundFinal reject orderId:{}", fundStatusDTO.getOrderId());
            updateManagementConclusion(fundStatusDTO.getOrderId(), 2);
        } else if (fundStatusDTO.getStatus() == PreFundResultEnum.BACK) {
            log.info("OrderServiceImpl.updateOrderInfo fundFinal reject orderId:{}", fundStatusDTO.getOrderId());
            updateManagementConclusion(fundStatusDTO.getOrderId(), 3);
        } else {
            log.info("OrderServiceImpl.updateOrderInfo fundFinal wait orderId:{}", fundStatusDTO.getOrderId());
            return;
        }

        //        // 判断终审是否全部失败
        //        if (finalFundInfoService.isAllFinalFundFail(fundStatusDTO.getOrderId())) {
        //            updateManagementConclusion(fundStatusDTO.getOrderId(), 2);
        //        }
    }

    /**
     * 更新资方结论
     *
     * @param orderId    订单id
     * @param conclusion 结论状态
     */
    @Override
    public void updateManagementConclusion(Integer orderId, int conclusion) {
        LambdaUpdateWrapper<OrderInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OrderInfoEntity::getManagementConclusion, conclusion);
        updateWrapper.eq(OrderInfoEntity::getId, orderId);
        super.update(updateWrapper);
    }

    /**
     * 推送资方终审流程
     *
     * @param orderId 订单 ID
     */
    @Override
    @Async
    public void pushFundApprovalFinal(Integer orderId) {
        log.info("pushFundApprovalFinal orderId:{} start", orderId);
        try {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
            Integer fundId = orderInfoEntity.getFundId();
            Integer preId = orderInfoEntity.getPreId();
            FundEnum fundEnum = FundEnum.getFundEnum(fundId);
            if (fundEnum != null) {

                updateManagementConclusion(orderId, 0);

                FundApproveFinalDTO dto = new FundApproveFinalDTO();
                dto.setOrderId(orderId);
                dto.setPreId(preId);
                dto.setFund(fundEnum);
                approveFeign.fundApproveFinal(dto);

            }
        } catch (Exception e) {
            log.error("pushFundApprovalFinal orderId:{} ,error:{}", orderId, e.getMessage(), e);
        }
        log.info("pushFundApprovalFinal orderId:{} end", orderId);
    }


//    /**
//     * 查询风控初审已分配的审批人员
//     *
//     * @return
//     */
//    public List<Integer> getRiskFirstNodeUserIdList() {
//
//        LambdaQueryWrapper<OrderInfoEntity> wrapper = new LambdaQueryWrapper<>();
//        wrapper.select(OrderInfoEntity::getRiskUserId)
//                .eq(OrderInfoEntity::getDeleteFlag, 0)
//                .isNotNull(OrderInfoEntity::getRiskUserId)
//                .eq(OrderInfoEntity::getCurrentNode, States.RISK_FIRST_APPROVE.getNode());
//        return super.list(wrapper).stream()
//                .map(OrderInfoEntity::getRiskUserId).filter(Objects::nonNull)
//                .toList();
//    }

    /**
     * 暂存订单信息
     *
     * @param saveOrderInfoDTO 保存订单信息DTO
     * @return 订单实体
     */
    @Override
    public OrderInfoEntity StagingOrderInfo(SaveOrderInfoDTO saveOrderInfoDTO) {
        OrderInfoEntity orderInfoEntity = orderConverter.saveOrderInfoDtoToEntity(saveOrderInfoDTO);
        OrderInfoEntity orderInfo = Optional.ofNullable(orderInfoMapper.selectById(orderInfoEntity.getId())).orElseThrow(() -> new BusinessException("订单不存在"));

        //修改产品期数
        ProductInfoEntity productInfo = productInfoMapper.selectById(saveOrderInfoDTO.getProductId());
        orderInfoEntity.setTerm(productInfo.getTerm());
        orderInfoMapper.updateById(orderInfoEntity);

        //修改预审批订单希望金额
        preApprovalApplyInfoMapper.update(new LambdaUpdateWrapper<PreApprovalApplyInfoEntity>()
                        .set(PreApprovalApplyInfoEntity::getLoanAmount, saveOrderInfoDTO.getLoanAmount())
                        //增加备注信息
//                .set(PreApprovalApplyInfoEntity::getNotes,saveOrderInfoDTO.getNotes())
                        .eq(PreApprovalApplyInfoEntity::getId, orderInfo.getPreId())
        );
        orderAmountMapper.update(new LambdaUpdateWrapper<OrderAmountEntity>()
                .set(OrderAmountEntity::getHopeAmount, saveOrderInfoDTO.getLoanAmount())
                .eq(OrderAmountEntity::getOrderId, orderInfoEntity.getId())
        );
        orderPageInfoService.updateOrderPageInfo(orderInfoEntity.getId(), States.ADDED_BUSINESS_ORDER, 0);
        return orderInfoEntity;
    }

    @Override
    public void overdueOrdersListExport(OverdueOrdersDTO overdueOrdersDTO, LoginUser loginUser, HttpServletResponse
            response) {
        MPJLambdaWrapper<OrderInfoEntity> wrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .selectAll(OrderInfoEntity.class)
                .selectAs(OrderInfoEntity::getId, OrderApproveListVO::getOrderId)
                .selectAs(OrderInfoEntity::getRiskUserId, OrderApproveListVO::getRisiUserId)
                .selectAs(FundInfoEntity::getName, OrderApproveListVO::getFundName)
                .selectAs(OrderAmountEntity::getCustomerConfirmAmount, OverdueOrdersListVO::getCustomerAmount)

                .leftJoin(FundInfoEntity.class, FundInfoEntity::getId, OrderInfoEntity::getFundId)
                .leftJoin(OrderAmountEntity.class, OrderAmountEntity::getOrderId, OrderInfoEntity::getId)
                .like(StrUtil.isNotBlank(overdueOrdersDTO.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, overdueOrdersDTO.getVehicleNumber())
                .like(StrUtil.isNotBlank(overdueOrdersDTO.getOrderNumber()), OrderInfoEntity::getOrderNumber, overdueOrdersDTO.getOrderNumber())
                .like(StrUtil.isNotBlank(overdueOrdersDTO.getCustomerName()), OrderInfoEntity::getCustomerName, overdueOrdersDTO.getCustomerName())
                .like(StrUtil.isNotBlank(overdueOrdersDTO.getCustomerPhone()), OrderInfoEntity::getCustomerPhone, overdueOrdersDTO.getCustomerPhone())
                .eq(OrderInfoEntity::getState, States.PAYMENT_SUCCESS.getNode())
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .orderByAsc(OrderInfoEntity::getPaymentTime);
        if (StrUtil.isNotBlank(overdueOrdersDTO.getTaskPoolsName())) {
//            if (overdueOrdersDTO.getLevel() == 1) {
//                wrapper.between(OrderInfoEntity::getOverdueDays, 1, 3);
//            } else if (overdueOrdersDTO.getLevel() == 2) {
//                wrapper.between(OrderInfoEntity::getOverdueDays, 4, 45);
//            } else if (overdueOrdersDTO.getLevel() != 0) {
//                throw new BusinessException("暂无该任务池");
//            }
            OverdueWarnConfigEntity overdueWarnConfigEntity = overdueWarnConfigService.getOne(new LambdaQueryWrapper<OverdueWarnConfigEntity>()
                    .eq(OverdueWarnConfigEntity::getOverdueName, overdueOrdersDTO.getTaskPoolsName())
                    .orderByDesc(OverdueWarnConfigEntity::getCreateTime), false);
            if (ObjUtil.isNull(overdueWarnConfigEntity)) {
                throw new BusinessException("暂无该任务池");
            }
            if (ObjUtil.equals(overdueWarnConfigEntity.getEnable(), 1)) {
                throw new BusinessException("该任务池未启用");
            }
            wrapper.between(OrderInfoEntity::getOverdueDays, overdueWarnConfigEntity.getOverdueStartWarnDays(), overdueWarnConfigEntity.getOverdueEndWarnDays());
        }
        // 数据权限
        dataPermissionService.limitOverdueOrder(loginUser, wrapper);

        List<OverdueOrdersListExportVO> records = orderInfoMapper.selectJoinList(OverdueOrdersListExportVO.class, wrapper);
//        records.forEach(item -> {
//            item.setCustomerPhone(StrUtil.hide(item.getCustomerPhone(), 0, 7));
//        });
        // 查询经办人信息
//        records.forEach(item -> {
//            item.setCustomerPhone(StrUtil.hide(item.getCustomerPhone(), 0, 7));
//        });
        // 查询经办人信息
        List<Integer> manageIds = records.stream().map(OverdueOrdersListExportVO::getManagerId).filter(Objects::nonNull).toList();
        Result<List<UserStoreVO>> listResult = userFeign.searchUserStoreBatch(manageIds);
        List<Integer> teamIds = records.stream().map(OverdueOrdersListExportVO::getTeamId).filter(Objects::nonNull).toList();
        List<DeptInfoVO> branchNameVOS = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData();
        Map<Integer, String> branchNameMap = branchNameVOS.stream().filter(e -> ObjUtil.isNotNull(e.getName())).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
        if (Result.isSuccess(listResult)) {

            Map<Integer, UserStoreVO> managerInfos =
                    listResult.getData().stream().collect(Collectors.toMap(UserStoreVO::getUserId, item -> item));


            records.stream().filter(item -> item.getManagerId() != null).forEach(record -> {
                        UserStoreVO userInfoVOS = managerInfos.get(record.getManagerId());
                        record.setManagerName(userInfoVOS.getName());
                        record.setStoreName(userInfoVOS.getStore());
                        record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(branchNameMap.get(record.getTeamId()), ""));
                    }
            );

        }
        for (OverdueOrdersListExportVO record : records) {
            record.setApplyAmountToStr(record.getCustomerAmount() + "元");
            record.setIsSyncAfterSaleToStr(
                    ObjUtil.isNotEmpty(record.getIsSyncAfterSale()) ? (record.getIsSyncAfterSale() == 1 ? "已同步" : "未同步") : "未同步"
            );
            record.setPaymentTimeToStr(ObjUtil.isNotEmpty(record.getPaymentTime()) ? record.getPaymentTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
        }
//        if (CollUtil.isEmpty(records)){
//            throw new BusinessException("暂无数据");
//        }
        // 使用自定义的 WriteHandler
        String fileName = "逾期任务池报表.xlsx"; // 定义输出文件名
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream(); // 创建字节数组输出流;

        // 创建ExcelWriter对象，指定输出流、数据类和表头
        ExcelWriter excelWriter = EasyExcel.write(outputStream, OverdueOrdersListExportVO.class)
                .build();
        // 创建WriteSheet对象，指定工作表名称、数据类和表头
        WriteSheet writeSheet = EasyExcel.writerSheet(overdueOrdersDTO.getTaskPoolsName()).build();

        if (CollUtil.isEmpty(records)) {
            records = new ArrayList<>();
        } else {
            excelWriter.writeContext().writeWorkbookHolder().setWorkbook(new SXSSFWorkbook(records.size()));
        }
        excelWriter.write(records, writeSheet);
        // 获取Workbook对象
        Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();

        // 获取Sheet对象
        Sheet sheet = workbook.getSheetAt(0);
        // 执行额外的Excel创建操作（如果有）
        createExcel(sheet, workbook);
        // 完成ExcelWriter的写入操作
        excelWriter.finish();
        // 将字节数组输出流转换为字节数组
        byte[] bytes = outputStream.toByteArray();

        // 设置文件下载头
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + URLEncoder.encode(fileName, StandardCharsets.UTF_8) + "\"");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        // 获取HttpServletResponse的输出流并写入字节数组
        try (ServletOutputStream outputStream1 = response.getOutputStream()) {
            outputStream1.write(bytes);
            outputStream1.flush();
        } catch (IOException e) {
            // 处理异常
            e.printStackTrace();
        }

    }

    @Override
    public PayApplicationPageListVO approveListTotal(OrderApproveDTO dto, LoginUser loginUser) {
        log.info("approveListTotal loginUser = {}", JSONUtil.toJsonStr(loginUser));

        //查询签约客服
        List<Integer> orderIdBySignCustomerIdList = new ArrayList<>();
        if (StrUtil.isNotBlank(dto.getSignCustomerName())) {
            //根据名称匹配签约客服id
            List<Integer> customerIdList = userFeign.getUserIdByLikeNameList(dto.getSignCustomerName()).getData();

            List<OrderNodeRecordEntity> orderBySignCustomerIdList = null;
            if (CollUtil.isNotEmpty(customerIdList)) {
                orderBySignCustomerIdList = orderNodeRecordMapper.selectList(
                        new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                .select(OrderNodeRecordEntity::getOrderId, OrderNodeRecordEntity::getUpdateBy, OrderNodeRecordEntity::getCreateTime)
                                .apply("id IN (SELECT id FROM (SELECT order_id,id, ROW_NUMBER() OVER (PARTITION BY order_id ORDER BY create_time DESC) as rn FROM lh_order_node_record WHERE current_node = {0}) subquery WHERE rn = 1)",
                                        States.PAYMENT_APPLY_INFORMATION.getNode())
                                .in(OrderNodeRecordEntity::getUpdateBy, customerIdList));
            }
            if (CollUtil.isEmpty(orderBySignCustomerIdList)) {
                return new PayApplicationPageListVO().setTotalAmount(BigDecimal.ZERO);
            }
            //获取每个的最新一条
            orderIdBySignCustomerIdList = orderBySignCustomerIdList.stream().map(OrderNodeRecordEntity::getOrderId).toList();
        }

        //客户经理名称获取客户经理id
        List<Integer> name2ManagerIdList = new ArrayList<>();
        if (StrUtil.isNotBlank(dto.getManagerName())) {
            name2ManagerIdList = userFeign.getUserIdByLikeNameList(dto.getManagerName()).getData();
            if (CollUtil.isEmpty(name2ManagerIdList)) {
                return new PayApplicationPageListVO().setTotalAmount(BigDecimal.ZERO);
            }
        }

        List<Integer> repaymentDateOrderIdList = Lists.newArrayList();
        //还款日期在指定日期区间的订单id
        if (null != dto.getRepaymentStartDate()
                && null != dto.getRepaymentEndDate()) {
            repaymentDateOrderIdList = repaymentService.betweenRepaymentDateOrderIdList(dto.getRepaymentStartDate(), dto.getRepaymentEndDate());
            if (CollUtil.isEmpty(repaymentDateOrderIdList)) {
                return new PayApplicationPageListVO().setTotalAmount(BigDecimal.ZERO);
            }
        }
        //贷后查询资管客服条件需要添加条件
        List<AssetLoanInfoLimitVO> assetLoanInfo = null;
        List<Integer> assetLoanInfoOrderIdLimitList = new ArrayList<>();
        if (ObjUtil.isNotNull(dto.getCurrentNode()) && (ObjUtil.equals(dto.getCurrentNode(), States.PAYMENT_SUCCESS.getNode()) || ObjUtil.equals(dto.getCurrentNode(), States.SETTLED.getNode()))
                && StrUtil.isNotBlank(dto.getAssetManagerSearchKey())) {
            SearchAssetLoanInfoLimitDTO searchAssetLoanInfoLimitDTO = new SearchAssetLoanInfoLimitDTO();
            searchAssetLoanInfoLimitDTO.setDataSource(1);
            searchAssetLoanInfoLimitDTO.setSearchKey(dto.getAssetManagerSearchKey());
            assetLoanInfo = loanRepaymentFeign.searchAssetLoanInfoLimit(searchAssetLoanInfoLimitDTO).getData();
            if (CollUtil.isEmpty(assetLoanInfo)) {
                return new PayApplicationPageListVO().setTotalAmount(BigDecimal.ZERO);
            } else {
                assetLoanInfoOrderIdLimitList = assetLoanInfo.stream()
                        .map(AssetLoanInfoLimitVO::getSourceOrderId)
                        .map(Math::toIntExact)
                        .toList();
            }
        }
        //组装筛选器
        MPJLambdaWrapper<OrderInfoEntity> queryWrapper = orderProcessService.getOrderInfoEntityMPJLambdaWrapper(dto, name2ManagerIdList, repaymentDateOrderIdList, orderIdBySignCustomerIdList, assetLoanInfoOrderIdLimitList);

        // 权限控制
        dataPermissionService.limitOrder(loginUser, dto.getCurrentNode(), queryWrapper);
        log.info("OrderDetailsInfoServiceImpl.approveListTotal.sql:{}", queryWrapper.getSqlSelect());
        List<OrderApproveListVO> orderApproveListVOList = orderInfoMapper.selectJoinList(
                OrderApproveListVO.class,
                queryWrapper);

//        if (CollUtil.isNotEmpty(orderApproveListVOList)) {
//            orderApproveListVOList.forEach(this::setOrderStatus);
//        }
//
//        //过滤订单状态
//        if (null != dto.getOrderStatus()) {
//            orderApproveListVOList = orderApproveListVOList.stream().filter(v ->
//                    Objects.equals(dto.getOrderStatus(), v.getOrderStatus())).toList();
//        }

        List<Integer> orderIdList = orderApproveListVOList.stream()
                .map(OrderApproveListVO::getOrderId)
                .filter(ObjUtil::isNotEmpty)
                .toList();
        if (CollUtil.isNotEmpty(orderIdList)) {
            MPJLambdaWrapper<OrderAmountEntity> wrapper = new MPJLambdaWrapper<OrderAmountEntity>()
                    .selectSum(OrderAmountEntity::getCustomerConfirmAmount, PayApplicationPageListVO::getTotalAmount)
                    .in(OrderAmountEntity::getOrderId, orderIdList)
                    .eq(OrderAmountEntity::getDeleteFlag, 0);
            return new PayApplicationPageListVO().setTotalAmount(orderAmountMapper.selectJoinOne(BigDecimal.class, wrapper));
        } else {
            return new PayApplicationPageListVO().setTotalAmount(BigDecimal.ZERO);
        }
    }

    /**
     * 设置门店报表单元格样式
     *
     * @param sheet
     * @param workbook
     */
    public static void createExcel(Sheet sheet, Workbook workbook) {
        // 创建一个新的字体样式
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);

        // 创建一个新的单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setWrapText(true); // 启用自动换行
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        applyCellStyleToSheet(sheet, cellStyle);
    }

    private static void applyCellStyleToSheet(Sheet sheet, CellStyle cellStyle) {
        for (Row row : sheet) {
            for (Cell cell : row) {
                if (cell == null) {
                    cell = row.createCell(cell.getColumnIndex());
                }
                cell.setCellStyle(cellStyle);
            }
        }
    }

    /**
     * 合同签约-发起合同审批流程
     *
     * @param contractApprovalDTO
     * @param loginUser
     * @return
     */
    @Override
    @Transactional
    public Boolean process(ContractApprovalDTO contractApprovalDTO, LoginUser loginUser) {
        log.info("OrderServiceImpl process,入参:{}", JSON.toJSONString(contractApprovalDTO));

        Assert.notNull(loginUser, "用户信息为空");
        List<Integer> roleIdList = loginUser.getRoleIds();

        //资管客服拥有该权限
        if (!RoleEnum.ASSET_CUSTOMER_SERVICE.hasRole(roleIdList)) {
            throw new BusinessException("用户无权限。");
        }

        if (null == contractApprovalDTO.getOrderId() || null == contractApprovalDTO.getContractId()) {
            throw new BusinessException("参数错误");
        }


        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(contractApprovalDTO.getOrderId());
        Optional.ofNullable(orderInfoEntity).orElseThrow(() -> new BusinessException("订单信息为空"));


        List<String> contractIdList = Arrays.asList(contractApprovalDTO.getContractId().split(","));

        //根据订单id确定申请记录是否存在
        List<OrderContractUserPermissionEntity> existRecordList = orderContractUserPermissionMapper.selectList(new LambdaQueryWrapper<OrderContractUserPermissionEntity>()
                .eq(OrderContractUserPermissionEntity::getOrderId, contractApprovalDTO.getOrderId())
                .eq(OrderContractUserPermissionEntity::getDeleteFlag, 0)
                .eq(OrderContractUserPermissionEntity::getUserId, loginUser.getUserId())
                .ne(OrderContractUserPermissionEntity::getCurrentNode, OutsourcingContractsApprovalEnum.FAIL.getCode()));

        if (CollUtil.isNotEmpty(existRecordList)) {
            List<String> existContractIdList = existRecordList.stream().map(OrderContractUserPermissionEntity::getContractId).toList();
            existContractIdList.forEach(existContractId ->
                    contractIdList.forEach(contractId -> {
                        if (Arrays.asList(existContractId.split(",")).contains(contractId)) {
                            throw new BusinessException("申请记录已存在或正在审批中。");
                        }
                    }));
        }

        //发起审批流程
        String processId = startDingProcess(packageContractApprovalDingDTO(contractApprovalDTO, orderInfoEntity), loginUser);
        if (StringUtils.isBlank(processId)) {
            throw new BusinessException("processId为空,发起失败。");
        }

        //发起成功后新增记录
        OrderContractUserPermissionEntity insertRecord = packageInsertRecord(contractApprovalDTO, processId, loginUser, orderInfoEntity);
        orderContractUserPermissionMapper.insert(insertRecord);
        return true;
    }

    /**
     * 组装新增实体OrderContractUserPermissionEntity
     *
     * @param contractApprovalDTO
     * @param processId
     * @param loginUser
     * @param orderInfoEntity
     * @return
     */
    private OrderContractUserPermissionEntity packageInsertRecord(ContractApprovalDTO contractApprovalDTO,
                                                                  String processId,
                                                                  LoginUser loginUser,
                                                                  OrderInfoEntity orderInfoEntity) {
        OrderContractUserPermissionEntity insertRecord = new OrderContractUserPermissionEntity();
        insertRecord.setApplyReason(contractApprovalDTO.getApplyReason());
        insertRecord.setOrderId(contractApprovalDTO.getOrderId());
        insertRecord.setProcessId(processId);
        if (ObjectUtil.equal(DeptEnum.CHUANG_XIN_DA_QU.getId(), orderInfoEntity.getRegionId())) {
            //大区品质主管审核
            insertRecord.setCurrentNode(OutsourcingContractsApprovalEnum.REGION_QUALITY_MANAGER_APPROVED.getCode());
        } else {
            //门店经理审核
            insertRecord.setCurrentNode(OutsourcingContractsApprovalEnum.STORE_MANAGER_APPROVAL.getCode());
        }
        insertRecord.setApplyTime(LocalDateTime.now());
        insertRecord.setUserId(loginUser.getUserId());
        insertRecord.setContractId(contractApprovalDTO.getContractId());
        insertRecord.setContractName(contractApprovalDTO.getContractName());
        insertRecord.setApplyPurpose(contractApprovalDTO.getApplyPurpose());
        insertRecord.setLimitFlag(0);
        return insertRecord;
    }

    /**
     * 组装钉钉审批参数
     *
     * @param contractApprovalDTO
     * @param orderInfoEntity
     * @return
     */
    private ContractApprovalDingDTO packageContractApprovalDingDTO(ContractApprovalDTO contractApprovalDTO,
                                                                   OrderInfoEntity orderInfoEntity) {
        ContractApprovalDingDTO contractApprovalDingDTO = new ContractApprovalDingDTO();
        contractApprovalDingDTO.setStoreName(orderInfoEntity.getStoreName());
        contractApprovalDingDTO.setCustomerName(orderInfoEntity.getCustomerName());
        contractApprovalDingDTO.setVehicleNumber(orderInfoEntity.getVehicleNumber());
        contractApprovalDingDTO.setContractType(orderInfoEntity.getFundName());
        contractApprovalDingDTO.setProductName(orderInfoEntity.getProductName());
        contractApprovalDingDTO.setContractName(contractApprovalDTO.getContractName());
        contractApprovalDingDTO.setApplyPurpose(contractApprovalDTO.getApplyPurpose());
        contractApprovalDingDTO.setApplyReason(contractApprovalDTO.getApplyReason());
        contractApprovalDingDTO.setOrderId(contractApprovalDTO.getOrderId());
        return contractApprovalDingDTO;
    }

    /**
     * 发起钉钉合同查看审批流程
     *
     * @param contractApprovalDingDTO
     * @param loginUser
     * @return
     */
    private String startDingProcess(ContractApprovalDingDTO contractApprovalDingDTO, LoginUser loginUser) {
        String processCode;
        //生产环境
        if (envUtil.isPrd()) {
            processCode = dingTaskFeign.getWorkFlowSchemaId(DingTaskConstants.CONTRACT_LIMIT_PROCESS_PRODUCTION).getData();
        }
        //测试环境
        else {
            processCode = dingTaskFeign.getWorkFlowSchemaId(DingTaskConstants.CONTRACT_LIMIT_PROCESS_TEST).getData();
        }

        if (StringUtils.isBlank(processCode)) {
            log.error("OrderServiceImpl startDingProcess processCode为空");
            throw new BusinessException("流程不存在");
        }
        log.info("OrderServiceImpl startDingProcess processCode:{}", processCode);

        // 获取当前人员钉钉userId
        Result<List<UserSyncInfoListVO>> userSyncInfoListVOListResult = userFeign.queryUserSyncInfoList(new SearchUserSyncInfoDTO().setUserIdList(List.of(loginUser.getUserId())).setOrigin(1));
        if (!Result.isSuccess(userSyncInfoListVOListResult)) {
            log.error("OrderServiceImpl startDingProcess,userFeign 调用queryUserSyncInfoList方法 error:{}", JSON.toJSONString(userSyncInfoListVOListResult));
            throw new BusinessException("当前人员未同步到钉钉,无法发起审批");
        }
        List<UserSyncInfoListVO> userSyncInfoListVOList = userSyncInfoListVOListResult.getData();
        if (CollUtil.isEmpty(userSyncInfoListVOList)) {
            log.error("OrderServiceImpl startDingProcess, userSyncInfoListVOList为空");
            throw new BusinessException("当前人员未同步到钉钉,无法发起审批");
        }
        UserSyncInfoListVO currentUserSyncInfo = userSyncInfoListVOList.get(0);
        String userNumber = currentUserSyncInfo.getUserNumber();
        if (StringUtils.isBlank(userNumber)) {
            log.error("OrderServiceImpl startDingProcess, userNumber为空");
            throw new BusinessException("当前人员未同步到钉钉,无法发起审批");
        }
        log.info("OrderServiceImpl startDingProcess current userNumber:{}", userNumber);

        //获取钉钉部门id
        Result<UserDetailInfoVO> userDetailInfoVOResult = userFeign.searchUserDetailById(loginUser.getUserId());
        if (!Result.isSuccess(userDetailInfoVOResult)) {
            log.error("OrderServiceImpl startDingProcess,userFeign 调用searchUserDetailById方法 error :{}", JSON.toJSONString(userDetailInfoVOResult));
            throw new BusinessException("查询用户信息失败;");
        }
        UserDetailInfoVO userDetailInfoVO = userDetailInfoVOResult.getData();
        List<Integer> currentUserDeptIds = userDetailInfoVO.getDeptIds();
        if (CollUtil.isEmpty(currentUserDeptIds)) {
            log.error("OrderServiceImpl startDingProcess, currentUserDeptIds为空");
            throw new BusinessException("当前人员未同步到钉钉,无法发起审批");
        }

        //组装获取部门同步信息列表请求参数
        SearchDeptSyncInfoDTO searchDeptSyncInfoDTO = new SearchDeptSyncInfoDTO();
        searchDeptSyncInfoDTO.setLhDeptIdList(currentUserDeptIds);
        searchDeptSyncInfoDTO.setOrigin(1);
        Result<List<DeptSyncInfoVO>> deptSyncInfoVOListResult = userFeign.getSyncDeptByLhDeptIds(searchDeptSyncInfoDTO);
        if (!Result.isSuccess(deptSyncInfoVOListResult)) {
            log.error("OrderServiceImpl startDingProcess, userFeign 调用getSyncDeptByLhDeptIds方法 error :{}", JSON.toJSONString(deptSyncInfoVOListResult));
            throw new BusinessException("查询部门同步信息失败;");
        }
        List<DeptSyncInfoVO> deptSyncInfoVOList = deptSyncInfoVOListResult.getData();
        if (CollUtil.isEmpty(deptSyncInfoVOList)) {
            log.error("OrderServiceImpl startDingProcess, deptSyncInfoVOList为空");
            throw new BusinessException("当前人员未同步到钉钉,无法发起审批");
        }
        log.info("OrderServiceImpl startDingProcess current deptSyncInfoVOList:{}", deptSyncInfoVOList);

        List<DingCreateProcessCmdDTO.FormComponentValue> formComponentValueList = new ArrayList<>(10);
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("门店名称")
                .setValue(contractApprovalDingDTO.getStoreName()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("客户姓名")
                .setValue(contractApprovalDingDTO.getCustomerName()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("车牌号")
                .setValue(contractApprovalDingDTO.getVehicleNumber()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("签约类型")
                .setValue(contractApprovalDingDTO.getContractType()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("产品名称")
                .setValue(contractApprovalDingDTO.getProductName()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("合同名称")
                .setValue(contractApprovalDingDTO.getContractName()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("申请用途")
                .setValue(contractApprovalDingDTO.getApplyPurpose()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("申请原因")
                .setValue(contractApprovalDingDTO.getApplyReason()));


        DingCreateProcessCmdDTO processCmdDTO = new DingCreateProcessCmdDTO();
        List<DingCreateProcessCmdDTO.TargetSelectActioner> targetSelectActionerList = new ArrayList<>();

        //生产
        if (envUtil.isPrd()) {
            processCmdDTO.setProcessCode(processCode)
                    .setOriginatorUserId(userNumber)
                    .setDeptId(Convert.toLong(deptSyncInfoVOList.get(0).getDeptId()))
                    .setFormComponentValues(formComponentValueList)
                    .setTargetSelectActioners(targetSelectActionerList);
        }
        //测试
        else {
            String[] originatorUserIds = {"1747186444398587"};
            Random random = new Random();
            String randomOriginatorUserId = originatorUserIds[random.nextInt(originatorUserIds.length)];
            processCmdDTO.setProcessCode(processCode)
                    .setOriginatorUserId(randomOriginatorUserId)
                    .setDeptId(926741152L)
                    .setFormComponentValues(formComponentValueList);
        }
        log.info("OrderServiceImpl startDingProcess processCmdDTO {}", JSON.toJSONString(processCmdDTO));

        //创建审批流程实例
        Result<String> workflowProcessInstances = dingTaskFeign.createWorkflowProcessInstances(processCmdDTO);
        if (!Result.isSuccess(workflowProcessInstances)) {
            log.error("OrderServiceImpl startDingProcess dingTaskFeign 调用createWorkflowProcessInstances方法 error:{}", JSON.toJSONString(workflowProcessInstances));
            throw new BusinessException("发起钉钉审批实例失败");
        }
        log.info("OrderServiceImpl startDingProcess create workflowProcessInstances:{}", JSON.toJSONString(workflowProcessInstances));
        //返回实例id
        return workflowProcessInstances.getData();
    }

    /**
     * 合同签约-更新合同审批流程节点
     *
     * @return
     */
    @Override
    @Transactional
    public Boolean updateProcess() {
        // 获取钉钉节点的审批列表 资管副总、区总审批 资管主管审批、总裁审批
        List<OrderContractUserPermissionEntity> orderContractUserPermissionEntityList = orderContractUserPermissionMapper.selectList(new LambdaQueryWrapper<OrderContractUserPermissionEntity>()
                .in(OrderContractUserPermissionEntity::getCurrentNode,
                        Arrays.asList(OutsourcingContractsApprovalEnum.STORE_MANAGER_APPROVAL.getCode(),
                                OutsourcingContractsApprovalEnum.REGION_QUALITY_MANAGER_APPROVED.getCode(),
                                OutsourcingContractsApprovalEnum.REGION_GENERAL_APPROVED.getCode(),
                                OutsourcingContractsApprovalEnum.REGION_CONTRACTS_APPROVAL.getCode()))
                .isNotNull(OrderContractUserPermissionEntity::getProcessId)
                .eq(OrderContractUserPermissionEntity::getDeleteFlag, 0));

        if (CollUtil.isNotEmpty(orderContractUserPermissionEntityList)) {
            log.info("OrderServiceImpl updateProcess orderContractUserPermissionEntityList size:{}", orderContractUserPermissionEntityList.size());
            // 钉钉审批实例id集合
            List<String> processIdList = orderContractUserPermissionEntityList.stream().map(OrderContractUserPermissionEntity::getProcessId).filter(StrUtil::isNotBlank).toList();
            //根据processId查询审批详情
            Result<Map<String, List<DingTaskApproveVO>>> processDetailMapResult = dingTaskFeign.queryDetailByProcessIdList(processIdList);
            if (!Result.isSuccess(processDetailMapResult)) {
                log.error("OrderServiceImpl updateProcess dingTaskFeign 调用queryDetailByProcessIdList error:{}", JSON.toJSONString(processDetailMapResult));
                throw new BusinessException("获取钉钉审批实例详情失败");
            }
            Map<String, List<DingTaskApproveVO>> processDetailMap = processDetailMapResult.getData();
            log.info("OrderServiceImpl updateProcess processDetailMap size:{}", processDetailMap.size());

            orderContractUserPermissionEntityList.forEach(v -> {
                String processId = v.getProcessId();
                List<DingTaskApproveVO> dingTaskApproveVOList = processDetailMap.get(processId);
                if (CollUtil.isNotEmpty(dingTaskApproveVOList)) {
                    //获取大区信息
                    OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                            .eq(OrderInfoEntity::getId, v.getOrderId())
                            .eq(OrderInfoEntity::getDeleteFlag, 0));


                    if (null != orderInfoEntity) {
                        Integer regionId = orderInfoEntity.getRegionId();
                        List<String> sortList;
                        String regionManager = switchUtils.getStrValue(CX_REGION_MANAGER_APPROVAL_ID);
                        String general = switchUtils.getStrValue(CX_REGION_GENERAL_APPROVAL_ID);
                        String headQuarters = switchUtils.getStrValue(CX_HEADQUARTERS_APPROVAL_ID);

                        if (Objects.equals(DeptEnum.CHUANG_XIN_DA_QU.getId(), regionId)) {
                            sortList = Arrays.asList(regionManager, general, headQuarters);
                        } else {
                            String managerVice = switchUtils.getStrValue(STORE_MANAGER_APPROVAL_ID);
                            sortList = Arrays.asList(managerVice, regionManager, general, headQuarters);
                        }
                        //processId排序 决定执行顺序
                        dingTaskApproveVOList.sort(Comparator.comparingInt(a -> sortList.indexOf(a.getActivityId())));
                    }


                    dingTaskApproveVOList.forEach(dingTaskApproveVO -> {
                        String activityId = dingTaskApproveVO.getActivityId();
                        String userNumberApprove = dingTaskApproveVO.getUserId();
                        LoginUser loginUser = new LoginUser();
                        if (StringUtils.isNotBlank(userNumberApprove)) {
                            SearchUserSyncInfoDTO searchUserSyncInfoDTO = new SearchUserSyncInfoDTO();
                            searchUserSyncInfoDTO.setUserNumberList(List.of(userNumberApprove));
                            Result<List<UserSyncInfoListVO>> userSyncInfoListVOListResult = userFeign.queryUserSyncInfoList(searchUserSyncInfoDTO);
                            if (!Result.isSuccess(userSyncInfoListVOListResult)) {
                                log.error("OrderServiceImpl updateProcess userFeign 调用queryUserSyncInfoList方法 error:{}", JSON.toJSONString(userSyncInfoListVOListResult));
                            }
                            List<UserSyncInfoListVO> syncInfoListVOList = userSyncInfoListVOListResult.getData();
                            if (CollUtil.isNotEmpty(syncInfoListVOList)) {
                                UserSyncInfoListVO userSyncInfoListVO = syncInfoListVOList.get(0);
                                loginUser.setUserId(userSyncInfoListVO.getLhUserId());
                                loginUser.setUserName(userSyncInfoListVO.getName());
                            }
                        }

                        //门店经理审核
                        if (switchUtils.getStrValue(STORE_MANAGER_APPROVAL_ID).equals(activityId)
                                && OutsourcingContractsApprovalEnum.STORE_MANAGER_APPROVAL.getCode() == v.getCurrentNode()) {
                            if (DingTaskConclusionEnum.AGREE.getCode().equals(dingTaskApproveVO.getResult())) {
                                v.setCurrentNode(OutsourcingContractsApprovalEnum.REGION_QUALITY_MANAGER_APPROVED.getCode());
                            } else if (DingTaskConclusionEnum.REFUSE.getCode().equals(dingTaskApproveVO.getResult())) {
                                v.setCurrentNode(OutsourcingContractsApprovalEnum.CASE_CONTRACTS_APPLY.getCode());
                            }
                            orderContractUserPermissionMapper.updateById(v);
                        }

                        //大区品质主管审批
                        if (Objects.equals(switchUtils.getStrValue(REGION_MANAGER_APPROVAL_ID), activityId)
                                && OutsourcingContractsApprovalEnum.REGION_QUALITY_MANAGER_APPROVED.getCode() == v.getCurrentNode()) {
                            if (DingTaskConclusionEnum.AGREE.getCode().equals(dingTaskApproveVO.getResult())) {
                                v.setCurrentNode(OutsourcingContractsApprovalEnum.REGION_GENERAL_APPROVED.getCode());
                            } else if (DingTaskConclusionEnum.REFUSE.getCode().equals(dingTaskApproveVO.getResult())) {
                                v.setCurrentNode(OutsourcingContractsApprovalEnum.STORE_MANAGER_APPROVAL.getCode());
                            }
                            orderContractUserPermissionMapper.updateById(v);
                        }

                        //大区总审核
                        if (Objects.equals(switchUtils.getStrValue(REGION_GENERAL_APPROVAL_ID), activityId)
                                && OutsourcingContractsApprovalEnum.REGION_GENERAL_APPROVED.getCode() == v.getCurrentNode()) {
                            if (DingTaskConclusionEnum.AGREE.getCode().equals(dingTaskApproveVO.getResult())) {
                                v.setCurrentNode(OutsourcingContractsApprovalEnum.REGION_CONTRACTS_APPROVAL.getCode());
                            } else if (DingTaskConclusionEnum.REFUSE.getCode().equals(dingTaskApproveVO.getResult())) {
                                v.setCurrentNode(OutsourcingContractsApprovalEnum.REGION_QUALITY_MANAGER_APPROVED.getCode());
                            }
                            orderContractUserPermissionMapper.updateById(v);
                        }

                        //总部合同岗审核
                        if (Objects.equals(switchUtils.getStrValue(HEADQUARTERS_APPROVAL_ID), activityId)
                                && OutsourcingContractsApprovalEnum.REGION_CONTRACTS_APPROVAL.getCode() == v.getCurrentNode()) {
                            if (DingTaskConclusionEnum.AGREE.getCode().equals(dingTaskApproveVO.getResult())) {
                                v.setCurrentNode(OutsourcingContractsApprovalEnum.SUCCESS.getCode());
                                v.setTransitTime(LocalDateTime.now());
                                v.setLimitFlag(1);
                            } else if (DingTaskConclusionEnum.REFUSE.getCode().equals(dingTaskApproveVO.getResult())) {
                                v.setCurrentNode(OutsourcingContractsApprovalEnum.REGION_GENERAL_APPROVED.getCode());
                            }
                            orderContractUserPermissionMapper.updateById(v);
                        }
                    });
                }
            });
        }
        return true;
    }

    @Override
    public Boolean getIsInsurance(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, "当前订单不存在");
        OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectById(orderInfoEntity.getCustomerId());
        Assert.notNull(orderCustomerInfoEntity, "当前客户不存在");
        //查询是否购买车损险  查询时间查过30个自然日  调取三方获取
        OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                .eq(OrderVehicleInfoEntity::getOrderId, orderId)
                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                .orderByDesc(OrderVehicleInfoEntity::getCreateTime)
                .last("limit 1"));
        QueryPolicyVerifyV4DTO policyVerifyV4DTO = new QueryPolicyVerifyV4DTO();
        policyVerifyV4DTO.setUserCode(orderCustomerInfoEntity.getIdNumber());
        policyVerifyV4DTO.setLicenseNo(orderInfoEntity.getVehicleNumber());
        if (orderVehicleInfoEntity == null) {
            try {
                Integer data = riskFeign.insurance(policyVerifyV4DTO).getData();
                return data == 1;
            } catch (Exception e) {
                log.error("OrderServiceImpl.getIsInsurance orderId:{} error:{}", orderId, e.getMessage(), e);
                throw new RuntimeException("车损险查询服务暂不可用，请稍后重试");
            }
        } else {
            if (orderVehicleInfoEntity.getInsuranceDate() == null || orderVehicleInfoEntity.getInsuranceDate().plusDays(30).isBefore(LocalDate.now())) {
                try {
                    Integer data = riskFeign.insurance(policyVerifyV4DTO).getData();
                    OrderVehicleInfoEntity orderVehicleInfo = new OrderVehicleInfoEntity();
                    orderVehicleInfo.setId(orderVehicleInfoEntity.getId());
                    orderVehicleInfo.setInsuranceDate(LocalDate.now());
                    orderVehicleInfo.setIsInsurance(data);
                    orderVehicleInfoMapper.updateById(orderVehicleInfo);
                    return data == 1;
                } catch (Exception e) {
                    log.error("OrderServiceImpl.getIsInsurance orderId:{} error:{}", orderId, e.getMessage(), e);
                    throw new RuntimeException("车损险查询服务暂不可用，请稍后重试");
                }
            } else {
                return orderVehicleInfoEntity.getIsInsurance() == 1;
            }
        }
    }


    /**
     * 生成短链
     */
    @Override
    public String generateShortUrl(ShortUrlDTO shortUrlDTO, LoginUser loginUser) {
        String redisShortUrl = hostname + "/service/auth/s/";
        String originalUrl = null;
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(shortUrlDTO.getOrderId());
        //判断订单是否存在
        if (ObjUtil.isNull(orderInfoEntity)) {
            throw new RuntimeException("订单不存在");
        }

        if (shortUrlDTO.getNode().equals(States.BUSINESS_ADDED_INFO.getNode())) {
            String sourceType = Objects.equals(orderInfoEntity.getSourceType(), 1) && (Objects.equals(orderInfoEntity.getRegionId(), 24) || Objects.equals(orderInfoEntity.getRegionId(), 56)) ? "1" : "0";
            Integer fundId = orderInfoEntity.getFundId();
            originalUrl = String.format(supplementalUrl, shortUrlDTO.getOrderId(), sourceType, fundId);
        } else if (shortUrlDTO.getNode().equals(States.CUSTOMER_CONFIRM.getNode())) {
            originalUrl = customerConfirmUrl;
        } else if (shortUrlDTO.getNode().equals(States.CONTRACT_SIGNING.getNode())) {
            originalUrl = reSignPath;
        }
        if (originalUrl == null) {
            throw new RuntimeException("生成短链失败");
        }
        originalUrl = String.format(originalUrl, shortUrlDTO.getOrderId());
        if (shortUrlDTO.getNode().equals(States.CUSTOMER_CONFIRM.getNode()) || shortUrlDTO.getNode().equals(States.CONTRACT_SIGNING.getNode())) {
            originalUrl = orderSendMessageImpl.replaceUrl(originalUrl);
        }
        log.info("生成短链，原始链接：{}", originalUrl);
        // 生成短链的key前缀
        final String SHORT_URL_KEY_PREFIX = "SHORT_URL:";
        final String SHORT_URL_UID_PREFIX = "SHORT_URL_UID:";
        // 短链有效期（小时）
        final long EXPIRE_HOURS = 3;

        // 生成短链ID
        String shortId = IdUtil.simpleUUID();
        String shortUrl = redisShortUrl + shortId;

        Integer userId = 1;
        if (ObjUtil.isNotNull(loginUser) && ObjUtil.isNotNull(loginUser.getUserId()) && shortUrlDTO.getNode().equals(States.BUSINESS_ADDED_INFO.getNode())) {
            userId = loginUser.getUserId();
        } else {
            userId = null;
        }
        redisService.set(SHORT_URL_KEY_PREFIX + shortId, originalUrl, EXPIRE_HOURS, TimeUnit.HOURS);
        redisService.set(SHORT_URL_UID_PREFIX + shortId, userId, EXPIRE_HOURS, TimeUnit.HOURS);

        log.info("短链生成成功，短链：{}，原始链接：{}", shortUrl, originalUrl);
        return shortUrl;
    }

    @Override
    public Boolean sendMessageEditInfo(OrderInfoEntity orderInfo) {
        Integer orderId = orderInfo.getId();
        log.info("OrderServiceImpl.sendMessageEditInfo:orderId:{}", orderId);
        Boolean flag = false;
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isNull(orderInfoEntity)) {
            throw new BusinessException("订单不存在");
        }
        if (ObjUtil.isNull(orderInfoEntity.getState()) || orderInfoEntity.getState() < States.PAYMENT_SUCCESS.getNode()) {
            throw new BusinessException("当前节点不支持修改抵押");
        }
        Result<UserInfoVO> userInfoVOResult = userFeign.searchByUserId(orderInfoEntity.getManagerId());
        if (!Result.isSuccess(userInfoVOResult) || ObjUtil.isNull(userInfoVOResult.getData())) {
            throw new BusinessException("获取用户信息失败");
        }
        UserInfoVO receiverInfo = userInfoVOResult.getData();
        log.info("OrderServiceImpl.sendMessageEditInfo  receiverInfo:{}", JSONUtil.toJsonStr(receiverInfo));
        String mobile = receiverInfo.getMobile();
        //1.获取二维码
        GenerateCodeDTO generateCodeDTO = new GenerateCodeDTO().setQrCodeType(7).setOrderId(orderId);
        generateCodeDTO.setUserId(orderInfoEntity.getManagerId());
        String sceneParams = "orderId=" + orderId + "&urlType=蓝海办抵";
        generateCodeDTO.setSceneParams(sceneParams);
        ResponseEntity<byte[]> responseEntity = generateCodeFile(generateCodeDTO, new LoginUser().setUserId(orderInfoEntity.getManagerId()));
        String base64 = Base64Encoder.encode(responseEntity.getBody());
        Result<String> mediaResult = messageFeign.mediaUpload(new MessageContent().setMediaContent(base64));
        if (Result.isSuccess(mediaResult) && ObjUtil.isNotNull(mediaResult.getData())) {
            String mediaId = mediaResult.getData();
            //3.发送钉钉消息 MSG_MARKDOWN 发送文本加图案消息
            String txt = "&zwnj;**蓝海抵押修改**&zwnj;\n\n" +
                    "&zwnj;**请客户微信扫描二维码进行抵押修改**&zwnj;:\n" +
                    "![](" + mediaId + ")\n\n";
            Result<String> result = messageFeign.sendMessage(new MessageContent()
                    .setSendType(MsgConstants.SEND_DD_NOTICE)
                    .setMsgType(MsgConstants.MSG_MARKDOWN)
                    .setTitle("蓝海抵押修改")
                    .setContent(txt)
                    .setReceiver(mobile)
            );
            log.info("线上面签发送钉钉消息结果: {}", result);
            if (Result.isSuccess(result)) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 发送订单拒绝事件
     *
     * @param orderId      订单ID
     * @param rejectReason 拒绝原因
     * @return 是否发送成功
     */
    @Override
    public Boolean sendOrderRejectEvent(Integer orderId, String rejectReason) {
        log.info("发送订单拒绝事件，订单ID：{}，拒绝原因：{}", orderId, rejectReason);
        // 1. 验证订单是否存在
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isNull(orderInfoEntity)) {
            log.error("订单不存在，订单ID：{}", orderId);
            throw new BusinessException("订单不存在");
        }

        try {
            OrderStateService orderStateService = SpringContentUtils.getBean(OrderStateService.class);
            orderStateService.sendEvent(States.QUALITY_INSPECTION, Events.REJECT, orderId, 1, null, rejectReason, rejectReason);
            log.info("订单拒绝事件发送成功，订单ID：{}", orderId);
            return true;
        } catch (Exception e) {
            log.error("订单拒绝事件发送失败，订单ID：{}，错误信息：{}", orderId, e.getMessage(), e);
            throw new BusinessException("订单拒绝事件发送失败：" + e.getMessage());
        }
    }
}
