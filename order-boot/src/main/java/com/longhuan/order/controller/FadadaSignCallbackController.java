package com.longhuan.order.controller;

import com.fasc.open.api.utils.crypt.FddCryptUtil;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.service.FddSignCallbackService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@Api("法大大回调接口")
public class FadadaSignCallbackController {
    @Value("${FDD.appSecret}")
    private String appSecret;
    private final FddSignCallbackService fddSignCallbackService;

    @PostMapping("/fddSign/callback")
    public String handleFddEventCallback(
            @RequestHeader HttpHeaders headers,
            @RequestParam("bizContent") String bizContent) {
        //获取请求头参数
        String appId = headers.getFirst("X-FASC-App-Id");
        String signType = headers.getFirst("X-FASC-Sign-Type");
        String sign = headers.getFirst("X-FASC-Sign");
        String timestamp = headers.getFirst("X-FASC-Timestamp");
        //事件名称，开发者可以根据不同事件名称去解析bizContent的值，实现不同的逻辑
        String event = headers.getFirst("X-FASC-Event");
        String nonce = headers.getFirst("X-FASC-Nonce");
        //验签
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("X-FASC-App-Id", appId);
        paramMap.put("X-FASC-Sign-Type", "HMAC-SHA256");
        paramMap.put("X-FASC-Timestamp", timestamp);
        paramMap.put("X-FASC-Nonce", nonce);
        paramMap.put("X-FASC-Event", event);
        paramMap.put("bizContent", bizContent);
        //参数排序，ascii码排序
        String sortParam = FddCryptUtil.sortParameters(paramMap);
        //生成签名后可以进行校验
        try {
            String signature =  FddCryptUtil.sign(sortParam, timestamp, appSecret);
            if(!signature.equals(sign)) {
                //log.error("日志记录，签名失败");
                //为了不重复接收该请求，建议这里返回success，返回success后这条消息法大大将中断重试回调机制
                return "{\"msg\":\"success\"}";
            }
        } catch (Exception e) {
            log.info("法大大回调接口验签失败");
            throw new BusinessException("法大大回调接口验签失败");
        }
        return fddSignCallbackService.handleFddEventCallback(event, bizContent);
    }


}
