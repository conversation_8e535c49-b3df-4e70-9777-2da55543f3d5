package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.RiskPromptDTO;
import com.longhuan.order.pojo.entity.RiskPromptEntity;
import com.longhuan.order.pojo.vo.RiskPromptVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface RiskPromptConverter {

    RiskPromptVO entityToVO(RiskPromptEntity riskPromptEntity);

    RiskPromptEntity dtoToEntity(RiskPromptDTO riskPromptDTO);
}
