package com.longhuan.order.statemachine.aguard;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.service.CustomerAppointmentService;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.guard.Guard;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 状态保护机制，对一个状态转移进行评估，评估值为true允许状态转移，评估值为false禁止转移
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomerAppointmentFinishGuard implements Guard<States, Events> {

    private final OrderInfoMapper orderInfoMapper;
    private final CustomerSignInfoMapper customerSignInfoMapper;
    private final OrderGpsInfoMapper orderGpsInfoMapper;
    private final CustomerMortgageInfoMapper customerMortgageInfoMapper;
    private final OrderFeeInfoMapper orderFeeInfoMapper;
    private final OrderNodeRecordMapper orderNodeRecordMapper;

    @Override
    public boolean evaluate(StateContext<States, Events> context) {
        Integer orderId = context.getMessageHeaders().get(StateConstants.ORDER_ID, Integer.class);

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId,
                        OrderInfoEntity::getGpsState,
                        OrderInfoEntity::getReviewState,
                        OrderInfoEntity::getContractState,
                        OrderInfoEntity::getMortgageState,
                        OrderInfoEntity::getPaymentType,
                        OrderInfoEntity::getFundId)
                .eq(OrderInfoEntity::getId, orderId));

        Integer gpsState = Convert.toInt(orderInfoEntity.getGpsState(), 0);

        Integer reviewState = Convert.toInt(orderInfoEntity.getReviewState(), 0);

        Integer contractState = Convert.toInt(orderInfoEntity.getContractState(), 0);

        Integer mortgageState = Convert.toInt(orderInfoEntity.getMortgageState(), 0);

        Integer paymentType = Convert.toInt(orderInfoEntity.getPaymentType(), 0);

        //客户新签约信息
        Long signInfoCount = customerSignInfoMapper.selectCount(new LambdaQueryWrapper<CustomerSignInfoEntity>()
                .eq(CustomerSignInfoEntity::getOrderId, orderId));

        //GPS信息
        Long gpsInfoCount = orderGpsInfoMapper.selectCount(new LambdaQueryWrapper<OrderGpsInfoEntity>()
                .eq(OrderGpsInfoEntity::getOrderId, orderId)
                .eq(OrderGpsInfoEntity::getDeleteFlag, 0));
        //贷款抵押信息
        Long mortgageInfoCount = customerMortgageInfoMapper.selectCount(
                new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                        .eq(CustomerMortgageInfoEntity::getOrderId, orderId));

        //GPS费用信息
        int gpsFeeStatus = 0;
        List<OrderFeeInfoEntity> orderFeeInfoEntities = orderFeeInfoMapper.selectList(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                .select(OrderFeeInfoEntity::getOrderId, OrderFeeInfoEntity::getGpsFeeStatus)
                .eq(OrderFeeInfoEntity::getOrderId, orderId)
                .eq(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                .orderByDesc(OrderFeeInfoEntity::getCreateTime));

        if (!CollUtil.isEmpty(orderFeeInfoEntities)) {
            gpsFeeStatus = 2;
        }

        log.info("CustomerAppointmentFinishGuard orderId：{} ,gpsState:{} ,reviewState:{} ,contractState:{} ," +
                        "gpsFeeStatus:{} ,signInfoCount:{} ,gpsInfoCount:{} ,mortgageInfoCount:{} ," +
                        "paymentType:{} ,mortgageState:{}",
                orderId, gpsState,
                reviewState, contractState, gpsFeeStatus, signInfoCount, gpsInfoCount, mortgageInfoCount,
                paymentType, mortgageState
        );
        // 凭抵押回执放款，预加押状态=是（已完成），才到请款资料
        // 抵押后放款，抵押状态=3，才到请款资料
        // 1:先抵押后放款,2:抵押回执放款）
        int mortgageFinish = 0;

        if (paymentType == 1 && mortgageState == 3) {
            mortgageFinish = 1;
        } else if (paymentType == 2 && (mortgageState == 2 || mortgageState == 3)) {
            mortgageFinish = 1;
        }
        //判断是否提交抵押代办
        Long submitMortgageCount = orderNodeRecordMapper.selectCount(new LambdaQueryWrapper<OrderNodeRecordEntity>()
                .eq(OrderNodeRecordEntity::getOrderId, orderId)
                .eq(OrderNodeRecordEntity::getCurrentNode, States.CUSTOMER_APPOINTMENT.getNode())
                .in(OrderNodeRecordEntity::getEvent, List.of(Events.MORTGAGE_LOAN_FINISH, Events.MORTGAGE_PRE_STAKE_FINISH))
                .eq(OrderNodeRecordEntity::getDeleteFlag, 0)
        );

        // 线上订单不校验费用状态和GPS状态
        CustomerAppointmentService customerAppointmentService = SpringContentUtils.getBean(CustomerAppointmentService.class);
        Boolean onlineOrder = customerAppointmentService.isOnlineOrder(orderId);
        log.info("CustomerAppointmentFinishGuard orderId {}  online order {}", orderId, onlineOrder);
        if (onlineOrder) {
            return  reviewState == 2 && gpsState == 2 &&(gpsInfoCount > 0|| ObjUtil.equals(orderInfoEntity.getFundId(), FundEnum.CHANG_YIN.getValue())) &&
                    contractState == 2 && signInfoCount > 0 && (mortgageInfoCount > 0 && mortgageFinish == 1) && submitMortgageCount > 0;
        } else {
            return gpsState == 2 && reviewState == 2 &&
                    contractState == 2 && signInfoCount > 0 &&
                    gpsInfoCount > 0 && (mortgageInfoCount > 0 && mortgageFinish == 1) &&
                    gpsFeeStatus == 2 && submitMortgageCount > 0;
        }


    }
}