package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.ArrivedAddressDTO;
import com.longhuan.order.pojo.entity.ArrivedAddressEntity;
import com.longhuan.order.pojo.vo.ArrivedAddressVO;
import com.longhuan.order.service.ArrivedAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 抵押收件地址信息控制层
 *
 * <AUTHOR> css
 * @date : 2025-05-23
 */
@Api(tags = "抵押收件地址信息功能接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/arrived/address")
public class ArrivedAddressController {
    private final ArrivedAddressService arrivedAddressService;

    /**
     * 查询列表
     *
     * @return 实例对象
     */
    @ApiOperation(value = "分页查询抵押收件地址")
    @PostMapping("/page/arrivedAddressList")
    public Result<Page<ArrivedAddressVO>> selectArrivedAddressList(@RequestBody ArrivedAddressDTO dto) {
        return Result.success(arrivedAddressService.selectArrivedAddressList(dto));
    }

    /**
     * 通过抵押收件地址id获取详情
     *
     * @param id 抵押收件地址id
     * @return {@link Result< ArrivedAddressEntity >}
     */
    @ApiModelProperty("通过抵押收件地址id获取详情")
    @GetMapping("/queryInfoById/{id}")
    public Result<ArrivedAddressEntity> queryInfo(@PathVariable("id") Integer id) {
        return Result.success(arrivedAddressService.queryById(id));
    }

    /**
     * 新增抵押收件地址
     *
     * @param dto 抵押收件地址信息
     */
    @PostMapping("/insertArrivedAddressInfo")
    public Result<Boolean> insertArrivedAddressInfo(@RequestBody @Validated ArrivedAddressDTO dto) {
        return Result.success(arrivedAddressService.insertArrivedAddressInfo(dto));
    }

    /**
     * 编辑抵押收件地址
     *
     * @param dto 抵押收件地址信息
     */
    @PostMapping("/updateArrivedAddressInfo")
    public Result<Boolean> updateArrivedAddressInfo(@RequestBody @Validated ArrivedAddressDTO dto) {
        return Result.success(arrivedAddressService.updateArrivedAddressInfo(dto));
    }

    /**
     * 删除抵押收件地址
     *
     * @param id 主键
     * @return 是否成功
     */
    @ApiOperation("删除抵押收件地址")
    @PostMapping("/deleteById/{id}")
    public Result<Boolean> deleteById(@PathVariable("id") Integer id) {
        return Result.success(arrivedAddressService.deleteById(id));
    }
}