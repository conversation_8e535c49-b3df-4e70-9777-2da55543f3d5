package com.longhuan.approve.api.pojo.vo.changyin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.List;

/**
 * 贷款试算响应实体类
 * 描述：用于贷前贷款试算结果返回。
 */
@Data
@Accessors(chain = true)
public class ChangYinLoanTrialResponseDTO {

    /**
     * 外部试算流水号
     * 是否必填：是
     */
    @JsonProperty("outTrailSeq")
    private String outTrailSeq;

    /**
     * 对客总还款额
     * 是否必填：否
     * 描述：长银的本息不包含担保费
     */
    @JsonProperty("totalAmt")
    private BigDecimal totalAmt;

    /**
     * 总利息金额
     * 是否必填：否
     */
    @JsonProperty("totalNormInt")
    private BigDecimal totalNormInt;

    /**
     * 总费用金额
     * 是否必填：否
     */
    @JsonProperty("totalFeeAmt")
    private BigDecimal totalFeeAmt;

    /**
     * 执行利率
     * 是否必填：否
     */
    @JsonProperty("intRate")
    private BigDecimal intRate;

    /**
     * 合同终止日期
     * 是否必填：否
     * 描述：格式：yyyy-MM-dd
     */
    @JsonProperty("contEndDt")
    private String contEndDt;

    /**
     * LPR利率
     * 是否必填：否
     */
    @JsonProperty("lprRate")
    private BigDecimal lprRate;

    /**
     * LPR发布日期
     * 是否必填：否
     * 描述：格式：yyyy-MM-dd
     */
    @JsonProperty("lprDate")
    private String lprDate;

    /**
     * 上浮点数
     * 是否必填：否
     * 描述：例如:14.3% 仅在输入存在定价利率时返回
     */
    @JsonProperty("floatPoint")
    private String floatPoint;

    /**
     * 账单日
     * 是否必填：否
     * 描述：格式：yyyy-MM-dd
     */
    @JsonProperty("billDay")
    private String billDay;

    /**
     * 还款日
     * 是否必填：否
     * 描述：格式：yyyy-MM-dd
     */
    @JsonProperty("dueDay")
    private String dueDay;

    /**
     * 账单列表信息
     * 是否必填：是
     */
    @JsonProperty("payShdTryListAll")
    private List<RepayPlan> payShdTryListAll;

    /**
     * 还款计划明细实体类
     */
    @Data
    @Accessors(chain = true)
    public static class RepayPlan {

        /**
         * 期号
         * 是否必填：否
         */
        @JsonProperty("perdNo")
        private Integer term;

        /**
         * 应归还期供
         * 是否必填：否
         */
        @JsonProperty("instmAmt")
        private BigDecimal pmt;

        /**
         * 应归还本金
         * 是否必填：否
         */
        @JsonProperty("prcpAmt")
        private BigDecimal prin;

        /**
         * 应归还利息
         * 是否必填：否
         */
        @JsonProperty("normAmt")
        private BigDecimal intAmt;

        /**
         * 应归还费用
         * 是否必填：否
         */
        @JsonProperty("psFeeAmt")
        private BigDecimal psFeeAmt;

        /**
         * 优惠金额
         * 是否必填：否
         */
        @JsonProperty("couponAmt")
        private BigDecimal couponAmt;

        /**
         * 剩余本金
         * 是否必填：否
         */
        @JsonProperty("psRemPrcp")
        private BigDecimal bal;

        /**
         * 执行利率
         * 是否必填：否
         */
        @JsonProperty("intRate")
        private BigDecimal intRate;

        /**
         * 罚息利率
         * 是否必填：否
         */
        @JsonProperty("odIntRate")
        private BigDecimal odIntRate;

        /**
         * 到期日
         * 是否必填：否
         * 描述：格式：yyyy-MM-dd
         */
        @JsonProperty("dueDt")
        private String payDt;

        /**
         * 担保费
         * 是否必填：否
         */
        @JsonProperty("guaFeeAmt")
        private BigDecimal guaFeeAmt;
    }
}