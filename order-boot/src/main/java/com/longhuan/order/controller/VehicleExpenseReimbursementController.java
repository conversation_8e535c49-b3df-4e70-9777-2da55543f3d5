package com.longhuan.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.VehicleExpenseBatchApproveDTO;
import com.longhuan.order.pojo.dto.VehicleExpenseBatchDeleteDTO;
import com.longhuan.order.pojo.dto.VehicleExpenseReimbursementDTO;
import com.longhuan.order.pojo.dto.VehicleExpenseReimbursementQueryDTO;
import com.longhuan.order.pojo.vo.VehicleExpenseReimbursementVO;
import com.longhuan.order.service.OrderService;
import com.longhuan.order.service.VehicleExpenseReimbursementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * 车务费用报销Controller
 *
 * <AUTHOR>
 * @date 2025/07/22
 */
@Slf4j
@RestController
@RequestMapping("/vehicle/expense")
@Api(tags = "车务费用报销接口")
@Validated
public class VehicleExpenseReimbursementController {

    @Resource
    private VehicleExpenseReimbursementService vehicleExpenseReimbursementService;

    @Resource
    private OrderService orderService;

    /**
     * 申请车务费用报销
     */
    @PostMapping("/apply")
    @ApiOperation("申请车务费用报销")
    public Result<Boolean> applyReimbursement(@RequestBody @Valid VehicleExpenseReimbursementDTO dto,@CurrentUser LoginUser loginUser) {
        return Result.success(vehicleExpenseReimbursementService.applyReimbursement(dto,loginUser));
    }

    /**
     * 分页查询车务费用报销记录
     */
    @PostMapping("/page")
    @ApiOperation("分页查询车务费用报销记录")
    public Result<IPage<VehicleExpenseReimbursementVO>> pageVehicleExpenseReimbursement(
            @RequestBody(required = false) VehicleExpenseReimbursementQueryDTO query,
            @CurrentUser LoginUser loginUser) {
        Page<VehicleExpenseReimbursementVO> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<VehicleExpenseReimbursementVO> result = vehicleExpenseReimbursementService.pageVehicleExpenseReimbursement(page, query, loginUser);
        return Result.success(result);
    }

    /**
     * 导出车务费用报销记录
     *
     * @param query 查询条件
     * @param response HTTP响应
     * @return 导出结果
     */
    @PostMapping("/export")
    @ApiOperation("导出车务费用报销记录")
    public void exportVehicleExpenseReimbursement(@RequestBody(required = false) VehicleExpenseReimbursementQueryDTO query, HttpServletResponse response,@CurrentUser LoginUser loginUser) {
        vehicleExpenseReimbursementService.exportExcel(query, response, loginUser);
    }

    /**
     * 获取车务费用报销详情
     */
    @GetMapping("/{id}")
    @ApiOperation("获取车务费用报销详情")
    public Result<VehicleExpenseReimbursementVO> getReimbursementDetail(@PathVariable Integer id) {
        return Result.success(vehicleExpenseReimbursementService.getReimbursementDetail(id));
    }

    @PostMapping("/batch-approve")
    @ApiOperation("批量审批车务费用报销")
    public Result<Boolean> batchApproveReimbursement(@RequestBody @Valid VehicleExpenseBatchApproveDTO dto,@CurrentUser LoginUser loginUser) {
        return Result.success(vehicleExpenseReimbursementService.batchApproveReimbursement(dto, loginUser));
    }

    /**
     * 删除车务费用报销记录
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除车务费用报销记录")
    public Result<Boolean> deleteReimbursement(@PathVariable Integer id, @CurrentUser LoginUser loginUser) {
        return Result.success(vehicleExpenseReimbursementService.deleteReimbursement(id));
    }
    
    /**
     * 批量删除车务费用报销记录
     */
    @PostMapping("/batch-delete")
    @ApiOperation("批量删除车务费用报销记录")
    public Result<Boolean> batchDeleteReimbursement(@RequestBody @Valid VehicleExpenseBatchDeleteDTO dto, @CurrentUser LoginUser loginUser) {
        return Result.success(vehicleExpenseReimbursementService.batchDeleteReimbursement(dto.getIds()));
    }
}
