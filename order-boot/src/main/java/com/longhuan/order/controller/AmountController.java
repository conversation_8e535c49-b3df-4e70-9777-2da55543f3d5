package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.AmountCalDTO;
import com.longhuan.order.pojo.vo.AmountCalVO;
import com.longhuan.order.service.AmountService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 金额计算接口
 *
 * <AUTHOR>
 * @date 2024/09/03
 */
@Api(tags = "金额计算接口")
@RestController
@RequestMapping("/api/v1/amount")
@RequiredArgsConstructor
public class AmountController {
    private final AmountService amountService;

    /**
     * 计算金额
     *
     * @param amountCalDTO 金额计算DTO
     * @return {@link Result }<{@link AmountCalVO }>
     */
    @PostMapping("cal")
    public Result<AmountCalVO> calAmount(@RequestBody AmountCalDTO amountCalDTO) {
        return Result.success(amountService.calAmount(amountCalDTO));
    }
}
