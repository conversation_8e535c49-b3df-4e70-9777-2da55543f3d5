package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.DictVO;
import com.longhuan.order.service.SwitchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 开关接口
 *
 * <AUTHOR>
 * @date 2024/10/31
 */
@Api(tags = "开关配置")
@RestController
@RequestMapping("/api/v1/switch")
@RequiredArgsConstructor
public class SwitchController {

    private final SwitchService switchService;

    /**
     * 刷新
     *
     * @return {@link Result }<{@link List }<{@link DictVO }>>
     */
    @ApiOperation(value = "刷新开关配置")
    @GetMapping("/refresh")
    public Result<Boolean> refresh(@RequestParam String code) {
        return Result.success(switchService.refresh(code));
    }
}
