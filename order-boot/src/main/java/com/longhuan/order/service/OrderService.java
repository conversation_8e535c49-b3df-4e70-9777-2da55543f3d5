package com.longhuan.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.OrderGuarantorEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.risk.pojo.vo.FhldDataVO;
import com.longhuan.order.pojo.dto.ContractStateDTO;
import com.longhuan.user.pojo.dto.GenerateCodeDTO;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.http.ResponseEntity;

import java.net.URI;
import java.util.List;

/**
 * 订单接口
 * Author  YangFuMin
 * Date  2024/7/4
 */
public interface OrderService extends IService<OrderInfoEntity> {
    /**
     * 业务审批数据
     */
    Page<OrderApproveListVO> pageApproveList(OrderApproveDTO orderApproveDTO, LoginUser loginUser);

    /**
     * 移动端订单列表
     */
    IPage<OrderInfoListVO> pageOrderList(OrderInfoDTO orderInfoDTO, LoginUser loginUser);

    /**
     * 根据订单ID查询订单
     *
     * @param orderId 订单ID
     * @return 订单
     */
    GetOrderByOrderIdVo getOrderByOrderId(Integer orderId);

    /**
     * 保存订单信息
     *
     * @param saveOrderInfoDTO 保存订单信息DTO
     * @return 订单实体
     */
    OrderInfoEntity saveOrderInfo(SaveOrderInfoDTO saveOrderInfoDTO);

    /**
     * 新增总放款额、进件量、放款笔数统计接口
     */
    OrderHomeDataVO selectHomeData(LoginUser loginUser);

    String resetCddSequence();

    String resetFddSequence();

    OrderDetailsVo detailsByOrderId(Integer orderId);

    /**
     * 按身份证 id_number查询详细信息
     */
    OrderDetailsVo detailsByIdNumber(String idNumber);

    void fillRiskCreditFeature(OrderDetailsVo orderDetailsVo);

    /**
     * 确认详情
     *
     * @param orderId 订单 ID
     * @return {@link AmountDetailVO }
     */
    AmountDetailVO confirmDetail(Integer orderId);

    /**
     * 确认提交
     *
     * @param amountConfirmDTO 金额确认 DTO
     * @param loginUser
     * @return {@link Boolean }
     */
    Boolean confirmSubmit(AmountConfirmDTO amountConfirmDTO, LoginUser loginUser);



//    /**
//     * 订单自动分配测试
//     *
//     * @return {@link Boolean }
//     */
//    List<Integer> orderDistributionAutoTest();
//
//    @Nullable
//    Integer getRandomRiskUserId();

//    /**
//     * 订单手动分配
//     *
//     * @return {@link Boolean }
//     */
//    Integer orderDistributionManual(Integer riskUserId);

    /**
     * 修改资方、预审批额度
     *
     * @return {@link Boolean }
     */
    boolean updateOrder(UpdateOrderDTO updateOrderDTO);

    /**
     * 查看外部数据-符号律动
     *
     * @param orderId
     */
    FhldDataVO getFhldInfo(Integer orderId);

    /**
     * 验证订单是否存在黑名单
     *
     * @param orderId
     * @return {@link boolean}
     */
    boolean verifyOrderBlacklistById(Integer orderId);

    Page<RelatedOrderVO> getRelatedInfo(RelatedOrderDTO relatedOrderDTO);

    /**
     * 更新订单资方放款状态
     * @return {@link Boolean}
     */
    Boolean updateFundStatus(OrderApproveFundPaymentStatusDTO fundStatusDTO);

    /**
     * 终审更新订单资方状态
     * @return {@link Boolean}
     */
    Boolean updateFundStatus(FinalApproveFundStatusDTO fundStatusDTO);

    /**
     * 更新资方还款计划状态
     *
     * @param fundStatusDTO 订单资方还款计划状态DTO
     */
    Boolean updateFundPlanStatus(OrderApproveFundPlanStatusDTO fundStatusDTO);

    /**
     * 生成二维码
     * @return {@link Boolean}
     */
    ResponseEntity<byte[]> generateCodeFile(GenerateCodeDTO generateCodeDTO, LoginUser loginUser);


    String getQrCodeUrl(GenerateCodeDTO generateCodeDTO, LoginUser loginUser);

    ContractStateVO getContractState(ContractStateDTO generateCodeDTO, LoginUser loginUser);

    /**
     * 详情信息
     *
     * @param orderDetailDTO 次序详情信息DTO
     * @param loginUser
     * @return {@link OrderDetailVO }
     */
    OrderDetailVO detail(OrderDetailDTO orderDetailDTO, LoginUser loginUser);

    /**
     * 更新订单还款信息
     *
     * @param orderId 订单id
     */
    Boolean updateOrderFundRepayment(Integer orderId);

    /**
     * 逾期任务池列表
     *
     * @param overdueOrdersDTO
     * @param loginUser
     * @return
     */
    Page<OverdueOrdersListVO> overdueOrdersList(OverdueOrdersDTO overdueOrdersDTO, LoginUser loginUser);

    /**
     * 保存企业许可证
     *
     * @param enterpriseLicenseDTO 企业许可证 DTO
     * @return {@link Boolean }
     */
    Boolean saveEnterpriseLicense(EnterpriseLicenseDTO enterpriseLicenseDTO);

    Boolean updateFundContract(OrderFundContractDTO fundStatusDTO);

    /**
     * 历史顺序
     *
     * @param historyOrderDTO 历史记录顺序 DTO
     * @return {@link List }<{@link HistoryOrderVO }>
     */
    List<HistoryOrderVO> historyOrder(HistoryOrderDTO historyOrderDTO);

    /**
     * 获取关联订单总览
     *
     * @return
     */
    List<CheckRelatedInfoVO> checkRelatedInfo(Integer orderId);

    /**
     * 流程自动终止
     *
     * @return
     */
    Boolean processTerminal();

    void updateManagementConclusion(Integer orderId, int conclusion);

    void pushFundApprovalFinal(Integer orderId);

    /**
     * 暂存订单信息
     *
     * @param saveOrderInfoDTO 保存订单信息DTO
     * @return 订单实体
     */
    OrderInfoEntity StagingOrderInfo(SaveOrderInfoDTO saveOrderInfoDTO);

    void overdueOrdersListExport(OverdueOrdersDTO overdueOrdersDTO, LoginUser loginUser, HttpServletResponse response);

    PayApplicationPageListVO approveListTotal(OrderApproveDTO dto, LoginUser loginUser);

    /**
     * 合同签约-发起合同审批流程
     *
     * @param contractApprovalDTO
     * @param loginUser
     * @return
     */
    Boolean process(ContractApprovalDTO contractApprovalDTO, LoginUser loginUser);

    /**
     * 合同签约-更新合同审批流程节点
     *
     * @return
     */
    Boolean updateProcess();

    Boolean getIsInsurance(Integer orderId);


    String generateShortUrl(ShortUrlDTO shortUrlDTO, LoginUser loginUser);

    Boolean sendMessageEditInfo(OrderInfoEntity orderInfo);

    /**
     * 发送订单拒绝事件
     *
     * @param orderId 订单ID
     * @param rejectReason 拒绝原因
     * @return 是否发送成功
     */
    Boolean sendOrderRejectEvent(Integer orderId, String rejectReason);

}
