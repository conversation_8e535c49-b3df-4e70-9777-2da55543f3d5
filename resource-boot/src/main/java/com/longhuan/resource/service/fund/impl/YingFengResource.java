package com.longhuan.resource.service.fund.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.ssh.JschUtil;
import cn.hutool.extra.ssh.Sftp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.resource.enums.YingFengFileTypeEnum;
import com.longhuan.resource.mapper.*;
import com.longhuan.resource.pojo.dto.FundResourceDTO;
import com.longhuan.resource.pojo.dto.FundResourceResultDTO;
import com.longhuan.resource.pojo.entity.*;
import com.longhuan.resource.service.FileResourceService;
import com.longhuan.resource.service.ThumbService;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class YingFengResource {
    private final ThumbService thumbService;
    private final OrderFileMapper orderFileMapper;
    private final FileResourceService fileResourceService;
    private final FundPushResourceConfigMapper fundPushResourceConfigMapper;
    private final FundPushResourceInfoMapper fundPushResourceInfoMapper;
    private final FileResourceMapper fileResourceMapper;
    private final FileConfigMapper fileConfigMapper;
    @Value("${yingfeng.host}")
    private String sftpHost;
    @Value("${yingfeng.port}")
    private Integer sftpPort;
    @Value("${yingfeng.username}")
    private String sftpUsername;
    @Value("${yingfeng.password}")
    private String sftpPassword;
    @Value("${yingfeng.uploadBasePath}")
    private String sftpUploadBasePath;

    @Value("${file.filePath}")
    private String baseFilePath;
    @Value("${yingfeng.repurchaseSignedContract}")
    private String repurchaseSignedPath;

    public List<FundResourceResultDTO> uploadFile(FundResourceDTO fundResourceDTO) {
        Integer type = fundResourceDTO.getType();
        return switch (type) {
            case 1 -> uploadFilePre(fundResourceDTO);
            case 2 -> uploadFileOrder(fundResourceDTO);
            case 4 -> uploadFileAfterLoan(fundResourceDTO);
            case 5 -> uploadFileMortgage(fundResourceDTO);
            default -> throw new RuntimeException("未知类型");
        };
    }

    /**
     * 盈峰上传抵押登记联
     */
    private List<FundResourceResultDTO> uploadFileMortgage(FundResourceDTO fundResourceDTO) {
        System.out.println("=============================>进来了进来了");
        Integer orderId = fundResourceDTO.getLinkId();
        List<String> pushList = fundResourceDTO.getPushList();
        //TODO 先去查询抵押登记联 如果有就直接返回 如果没有就新增登记联
        FileConfigEntity dydjl = fileConfigMapper.selectOne(new LambdaQueryWrapper<FileConfigEntity>()
                .eq(FileConfigEntity::getCode, "DYDJL")
                .eq(FileConfigEntity::getDeleteFlag, 0)
                .orderByDesc(FileConfigEntity::getCreateTime)
                .last("limit 1")
        );
        List<OrderFileEntity> orderFileEntities = orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                .eq(OrderFileEntity::getOrderId, orderId)
                .eq(OrderFileEntity::getDeleteFlag, 0)
                .eq(OrderFileEntity::getFileId, dydjl.getId())
        );
        if (CollUtil.isNotEmpty(orderFileEntities)) {
            return null;
        }



//        DYDJL

//                MORTGAGE_RECEIPT


        return null;
    }

    private List<FundResourceResultDTO> uploadFileAfterLoan(FundResourceDTO fundResourceDTO) {
        Integer orderId = fundResourceDTO.getLinkId();
        log.info("uploadFileAfterLoan orderId:{}", orderId);
        List<FundResourceResultDTO> resultList = new ArrayList<>();
        Map<String, List<FundPushResourceConfigEntity>> fundFileCodeList = fundPushResourceConfigMapper
                .selectList(new LambdaQueryWrapper<FundPushResourceConfigEntity>()
                        .eq(FundPushResourceConfigEntity::getType, fundResourceDTO.getType())
                        .eq(FundPushResourceConfigEntity::getDeleteFlag, 0)
                        .eq(FundPushResourceConfigEntity::getFundId, fundResourceDTO.getFund().getValue()))
                .stream().collect(Collectors.groupingBy(FundPushResourceConfigEntity::getFileCode));

        fundFileCodeList.keySet().forEach(fileCode -> {

            List<Integer> fileConfigIds =
                    fundFileCodeList.get(fileCode).stream().map(FundPushResourceConfigEntity::getConfigId).filter(Objects::nonNull).toList();

            if (CollUtil.isEmpty(fileConfigIds)) {
                log.warn("fileCode:{} no found file config id", fileCode);
                return;
            }

            log.info("fileCode:{},fileConfigIds:{}", fileCode, fileConfigIds);

            List<String> resourceIds = orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                            .eq(OrderFileEntity::getOrderId, orderId)
                            .eq(OrderFileEntity::getDeleteFlag, 0)
                            .in(OrderFileEntity::getFileId, fileConfigIds)).stream()
                    .map(OrderFileEntity::getResourceId).filter(Objects::nonNull).toList();

            log.info("orderId:{}, resourceIds:{}", orderId, resourceIds);


            Long count = getFileCodeIndex(fundResourceDTO, fileCode);

            AtomicInteger index = new AtomicInteger(Convert.toInt(count));
            if (CollUtil.isNotEmpty(resourceIds)) {
                resourceIds.forEach(resourceId -> resultList.add(getFundResourceResultDTO(fundResourceDTO, resourceId,
                        YingFengFileTypeEnum.valueOf(fileCode), index.getAndIncrement())));
            }


        });


        return resultList.stream().filter(Objects::nonNull).toList();
    }

    private List<FundResourceResultDTO> uploadFileOrder(FundResourceDTO fundResourceDTO) {return List.of();
    }

    private List<FundResourceResultDTO> uploadFilePre(FundResourceDTO fundResourceDTO) {return List.of();
    }

    public List<FundResourceResultDTO> downloadFile(FundResourceDTO fundResourceDTO) {
        return List.of();
    }


    /**
     * 将文件上传到 SFTP 服务器。
     *
     * @return 上传服务器存储的路径
     */
    private String batchUploadSftp(List<FundResourceDTO.SftpDTO> sftpFileDTOS) {
        try (Sftp sftp = new Sftp(JschUtil.openSession(sftpHost, sftpPort, sftpUsername, sftpPassword))) {

            for (FundResourceDTO.SftpDTO sftpFileDTO : sftpFileDTOS) {
                FileResourceEntity fileResourceEntity = fileResourceMapper.selectOne(new LambdaQueryWrapper<FileResourceEntity>()
                        .eq(FileResourceEntity::getFileUid, sftpFileDTO.getFileUid())
                        .eq(FileResourceEntity::getDeleteFlag, 0));
                log.info("uploadSftp begin batchUploadSftp:{} fileName:{}", repurchaseSignedPath, sftpFileDTO.getDestPath());
//                String filePath = sftpFileDTO.getFilePath();
                String filePath = fileResourceEntity.getFilePath();
                if (thumbService.isImages(filePath)) {
                    filePath = thumbService.getThumbFileName(filePath);
                } else {
                    filePath = Paths.get(baseFilePath, filePath).toString();
                    log.info("YingFengResource batchUploadSftp filePath:{}", filePath);
                }

                if (FileUtil.isFile(filePath)) {
                    sftp.upload(repurchaseSignedPath, sftpFileDTO.getDestPath(), FileUtil.getInputStream(filePath));
                    log.info("uploadSftp end uploadBasePath:{} fileName:{} success", repurchaseSignedPath,
                            sftpFileDTO.getDestPath());
                } else {
                    log.error("uploadSftp end uploadBasePath:{} fileName:{} failed", repurchaseSignedPath,
                            sftpFileDTO.getDestPath());
                }
            }
        } catch (Exception e) {
            log.error("uploadSftp error uploadBasePath:{} fileName:{} e:{}", repurchaseSignedPath, e.getMessage(), e);
            throw new BusinessException("文件上传失败");
        }
        return "success";
    }
    private Long getFileCodeIndex(FundResourceDTO fundResourceDTO, String fileCode) {
        return fundPushResourceInfoMapper.selectCount(new LambdaQueryWrapper<FundPushResourceInfoEntity>()
                .eq(FundPushResourceInfoEntity::getLinkId, fundResourceDTO.getLinkId())
                .eq(FundPushResourceInfoEntity::getFundId, fundResourceDTO.getFund().getValue())
                .eq(FundPushResourceInfoEntity::getType, fundResourceDTO.getType())
                .eq(FundPushResourceInfoEntity::getFileCode, fileCode)
        );
    }
    private FundResourceResultDTO getFundResourceResultDTO(FundResourceDTO fundResourceDTO, String resourceId,
                                                           YingFengFileTypeEnum typeEnum, Integer index) {
        if (StringUtils.isEmpty(resourceId)) {
            return null;
        }
        // 记录资源记录
        int pushId = addResourceRecord(fundResourceDTO, resourceId, typeEnum.name(), index);

        try {

            if(CollUtil.isEmpty(fundResourceDTO.getSftpDTOList())){
                return null;
            }

            String result = batchUploadSftp(fundResourceDTO.getSftpDTOList());

            if(!result.equals("success")){
                return null;
            }

            updateResourceRecord(pushId);

            return new FundResourceResultDTO()
                    .setResourceId(resourceId)
//                    .setFileId(fileId)
                    .setFileCode(typeEnum.name());
        } catch (Exception e) {
            log.error("upload error {}", e.getMessage(), e);
            updateFailRemark(pushId, e.getMessage());
            throw new BusinessException(e.getMessage());
            //            return  null;
        }
    }
    private int addResourceRecord(FundResourceDTO fundResourceDTO, String resourceId,
                                  String fileCode, Integer index) {
        FundPushResourceInfoEntity fundPushResourceInfoEntity = new FundPushResourceInfoEntity();
        fundPushResourceInfoEntity.setResourceId(resourceId)
                .setPushType(2)
                .setFundId(fundResourceDTO.getFund().getValue())
                .setType(fundResourceDTO.getType())
                .setLinkId(fundResourceDTO.getLinkId())
                .setFileCode(fileCode).setPushIndex(index)
                .setPushStatus(1);
        fundPushResourceInfoMapper.insert(fundPushResourceInfoEntity);
        return fundPushResourceInfoEntity.getId();
    }
    private void updateResourceRecord(int pushId) {
        fundPushResourceInfoMapper.update(new LambdaUpdateWrapper<FundPushResourceInfoEntity>()
//                .set(FundPushResourceInfoEntity::getFileName, fileId)
                .set(FundPushResourceInfoEntity::getPushStatus, 2)
                .setIncrBy(FundPushResourceInfoEntity::getRetry, 1)
                .eq(FundPushResourceInfoEntity::getId, pushId));
    }
    private void updateFailRemark(int pushId, String remark) {
        fundPushResourceInfoMapper.update(new LambdaUpdateWrapper<FundPushResourceInfoEntity>()
                .set(FundPushResourceInfoEntity::getPushRemark, remark)
                .setIncrBy(FundPushResourceInfoEntity::getRetry, 1)
                .set(FundPushResourceInfoEntity::getPushStatus, -1)
                .eq(FundPushResourceInfoEntity::getId, pushId));
    }
}
