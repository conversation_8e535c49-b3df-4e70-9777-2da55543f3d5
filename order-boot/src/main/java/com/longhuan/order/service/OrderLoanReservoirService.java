package com.longhuan.order.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.order.pojo.dto.OrderLoanReservoirDTO;
import com.longhuan.order.pojo.vo.OrderLoanReservoirListVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * OrderLoanReservoirService
 *
 * <AUTHOR>
 */
public interface OrderLoanReservoirService {
    Page<OrderLoanReservoirListVO> list(OrderLoanReservoirDTO orderLoanReservoirDTO, LoginUser loginUser);

    Boolean batchPushLoan(OrderLoanReservoirDTO orderLoanReservoirDTO, LoginUser loginUser);

    Boolean reject(Integer orderId, LoginUser loginUser);

    Map<String,Object> importUploadData(MultipartFile file, LoginUser loginUser);

    String downloadTemplate(HttpServletResponse response,LoginUser loginUser);

}
