package com.longhuan.order.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseService;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.order.pojo.dto.GpsChargeRuleDTO;
import com.longhuan.order.pojo.dto.GpsChargeRuleUpdateDTO;
import com.longhuan.order.pojo.dto.GpsNoChargePersonListDTO;
import com.longhuan.order.pojo.dto.OrderIdDTO;
import com.longhuan.order.pojo.dto.StoreGpsConfigDTO;
import com.longhuan.order.pojo.entity.GpsChargingRulesInfoEntity;
import com.longhuan.order.pojo.vo.GpsChargeRuleVO;
import com.longhuan.order.pojo.vo.GpsNoChargePersonListVO;
import com.longhuan.order.pojo.vo.GpsPayVO;
import com.longhuan.order.pojo.vo.StoreGpsConfigVO;

public interface GpsPayService extends MPJBaseService<GpsChargingRulesInfoEntity> {
    GpsPayVO queryChargeRuleAndPage(OrderIdDTO dto);
    Boolean installment(OrderIdDTO dto, LoginUser loginUser);
    Boolean thereIsNoCharge(OrderIdDTO dto, LoginUser loginUser);
    Page<GpsChargeRuleVO> queryChargeRule(GpsChargeRuleDTO dto);
    Boolean editNoChargeRule(GpsChargeRuleUpdateDTO dto);
    Page<GpsNoChargePersonListVO> useNoChargePersonList(GpsNoChargePersonListDTO dto);

    String updateGpsRepaymentPlan();

    /**
     * 批量设置门店GPS收费策略配置
     *
     * 全局重置逻辑：
     * - 如果 freeStoreIds 为空，则重置所有门店的 is_free = 0
     * - 如果 installmentStoreIds 为空，则重置所有门店的 is_installment = 0
     *
     * @param dto 门店GPS配置DTO，包含免费门店列表和分期门店列表
     * @param loginUser 当前登录用户
     * @return 是否成功
     */
    Boolean setStoreGpsConfig(StoreGpsConfigDTO dto, LoginUser loginUser);

    /**
     * 根据门店ID查询GPS配置
     *
     * @param deptId 门店ID
     * @return 门店GPS配置VO
     */
    StoreGpsConfigVO getStoreGpsConfig(Integer deptId);
}
