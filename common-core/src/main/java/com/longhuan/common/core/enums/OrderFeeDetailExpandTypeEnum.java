package com.longhuan.common.core.enums;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 订单费用详情表费用类型
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum OrderFeeDetailExpandTypeEnum {
    CAR_SERVICE_FEE(1, "车务费", OrderFeeDetailStatusEnum.SPENDING),
    CURRENT_RETURN_PERFORMANCE(2, "现返绩效", OrderFeeDetailStatusEnum.SPENDING),
    REDEMPTION_PAYMENT(3, "赎回款", OrderFeeDetailStatusEnum.SPENDING),
    MONTHLY_REPAYMENT(4, "月还款", OrderFeeDetailStatusEnum.SPENDING),
    SETTLE_REPAYMENT(5, "结清款", OrderFeeDetailStatusEnum.SPENDING),
    EARLY_SETTLEMENT_PENALTY(6, "提前结清违约金", OrderFeeDetailStatusEnum.SPENDING),
    INSTALLMENT_SECURITY_DEPOSIT(7, "分期保证金", OrderFeeDetailStatusEnum.SPENDING),
    TRANSFER_MONTHLY_REPAYMENT(8, "转付月还款", OrderFeeDetailStatusEnum.SPENDING),
    TRANSFER_SETTLE_REPAYMENT(9, "转付结清款", OrderFeeDetailStatusEnum.SPENDING),
    SINGLE_PERIOD_COMPENSATION(10, "单期代偿款", OrderFeeDetailStatusEnum.SPENDING),
    REFERRAL_SERVICE_FEE(11, "返佣服务费", OrderFeeDetailStatusEnum.SPENDING),
    DISPOSABLE_SECURITY_DEPOSIT(12, "一次性保证金", OrderFeeDetailStatusEnum.SPENDING),
    SECURITY_DEPOSIT(13, "保证金", OrderFeeDetailStatusEnum.SPENDING),
    GUARANTEE_FEE(14, "担保费", OrderFeeDetailStatusEnum.SPENDING),
    INSTALLMENT_SERVICE_FEE(15, "分期服务费", OrderFeeDetailStatusEnum.SPENDING),
    GPS_DATA_TRANSFER_FEE(16, "GPS流量费", OrderFeeDetailStatusEnum.SPENDING),
    GPS_EQUIPMENT_COMPENSATION_FEE(17, "GPS设备赔偿费", OrderFeeDetailStatusEnum.SPENDING),
    FUND_LENDING(18, "资方放款", OrderFeeDetailStatusEnum.SPENDING),
    GPS_INSTALL_FEE(19, "GPS安装费", OrderFeeDetailStatusEnum.SPENDING),
    INSTALLMENT_MONTHLY_REPAYMENT(20, "分期月还", OrderFeeDetailStatusEnum.SPENDING),
    GPS_CAR_SERVICE_FEE(21,"GPS车务费",OrderFeeDetailStatusEnum.INCOME),
    OTHER(0, "其它", OrderFeeDetailStatusEnum.SPENDING),


    ;

    @EnumValue
    @JsonValue
    private final Integer code;
    private final String description;
    private final OrderFeeDetailStatusEnum applyType;

    public static OrderFeeDetailExpandTypeEnum fromCode(int code) {
        for (OrderFeeDetailExpandTypeEnum relation : values()) {
            if (relation.getCode() == code) {
                return relation;
            }
        }
        return null;
    }

    public static OrderFeeDetailStatusEnum toApplyType(PayApplicationPayeeTypeEnum payeeType,OrderFeeDetailExpandTypeEnum feeType) {
        if (payeeType == null || feeType == null) {
            return null;
        }
        if (ObjUtil.equals(feeType, OrderFeeDetailExpandTypeEnum.TRANSFER_MONTHLY_REPAYMENT)) {
            return switch (payeeType) {
                case CUSTOMER, YING_FENG, GUARANTEE , FU_MIN, CHANGYIN_MIN,LAN_HAI,ZHONG_HENG-> OrderFeeDetailStatusEnum.SPENDING;
                case BUSINESS_PERSONNEL, HUI_FENG,HEBEIDITING,SHAN_DONG_YUAN_HENG -> OrderFeeDetailStatusEnum.INCOME;
            };
        }
        return switch (payeeType) {
            case CUSTOMER, YING_FENG, GUARANTEE , FU_MIN, CHANGYIN_MIN,LAN_HAI-> OrderFeeDetailStatusEnum.SPENDING;
            case BUSINESS_PERSONNEL, HUI_FENG,HEBEIDITING,ZHONG_HENG,SHAN_DONG_YUAN_HENG -> OrderFeeDetailStatusEnum.INCOME;
        };
    }

    public static List<OrderFeeDetailExpandTypeEnum> getOfflineFeeEnumList() {
        return List.of(OrderFeeDetailExpandTypeEnum.INSTALLMENT_SECURITY_DEPOSIT, OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT,
                OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT, OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY, OrderFeeDetailExpandTypeEnum.INSTALLMENT_SERVICE_FEE);

    }
}
