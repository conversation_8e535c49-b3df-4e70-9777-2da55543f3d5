package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.OrderSuperaddInfoQueryDTO;
import com.longhuan.order.pojo.dto.SaveOrderSuperaddInfoDTO;
import com.longhuan.order.pojo.vo.OrderSuperAddInfoVO;
import com.longhuan.order.service.OrderSuperaddInfoService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单补件
 *
 * <AUTHOR>
 * @date 2024/08/19
 */
@Api(tags = "订单补件")
@RestController
@RequestMapping("/api/v1/orderSupperAdd")
@RequiredArgsConstructor
public class OrderSupperAddController {
    private final OrderSuperaddInfoService orderSuperaddInfoService;

    /**
     * 新增补件
     *
     * @param superAddFileDTO 补件DTO
     * @return {@link Result < Boolean>}
     */

    @PostMapping("/save")
    public Result<Boolean> saveSupperAddInfo(@RequestBody @Validated SaveOrderSuperaddInfoDTO superAddFileDTO) {
        return Result.success(orderSuperaddInfoService.saveSuperAddInfo(superAddFileDTO));
    }

    /**
     * 删除补件
     *
     * @param id 补件id
     * @return {@link Result< Boolean>}
     */
    @PostMapping("/delete/{id}")
    public Result<Boolean> deleteSupperAddInfo(@PathVariable(value = "id") Integer id) {
        return Result.success(orderSuperaddInfoService.removeSupperAddById(id));
    }

    /**
     * 根据订单号获取补件信息
     * @param orderId 订单id
     * @return {@link Result< OrderSuperAddInfoVO>}
     */
    @GetMapping("/queryByOrderId/{orderId}")
    public Result<List<OrderSuperAddInfoVO>> queryByOrderId(@PathVariable("orderId") Integer orderId) {
        return Result.success(orderSuperaddInfoService.queryByOrderId(orderId));
    }

    /**
     *  列表查询
     * @param dto
     * @return {@link Result< List< OrderSuperAddInfoVO>>}
     */

    @PostMapping("/queryList")
    public Result<List<OrderSuperAddInfoVO>> queryList(@RequestBody OrderSuperaddInfoQueryDTO dto){
        return Result.success(orderSuperaddInfoService.queryList(dto));
    }


}
