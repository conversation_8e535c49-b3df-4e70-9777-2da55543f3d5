package com.longhuan.order.converter;

import com.longhuan.order.pojo.entity.BankAccountSignEntity;
import com.longhuan.order.pojo.vo.BankCardInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 岸帐户签约转换
 *
 * <AUTHOR>
 * @date 2024/10/19
 */
@Mapper(componentModel = "spring")
public interface BankAccountSignConverter {

    @Mapping(target = "openBank", source = "bankNameUpdate")
    BankCardInfoVO entity2vo(BankAccountSignEntity bankAccountSignEntity);

}
