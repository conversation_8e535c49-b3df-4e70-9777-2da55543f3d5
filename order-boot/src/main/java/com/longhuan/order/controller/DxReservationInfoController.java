package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.DxReservationInfoDTO;
import com.longhuan.order.service.DxReservationInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "电销预约信息")
@RestController
@RequestMapping("/api/v1/dx")
@RequiredArgsConstructor
public class DxReservationInfoController {

    private final DxReservationInfoService dxReservationInfoService;
    /**
     * 电销预约信息接收
     *
     */
    @ApiOperation(value = "电销预约信息接收")
    @PostMapping("/reservation/receive")
    public Result<Boolean> receive(@Validated @RequestBody DxReservationInfoDTO reservationInfoDTO) {
        return Result.success(dxReservationInfoService.receive(reservationInfoDTO));
    }
}
