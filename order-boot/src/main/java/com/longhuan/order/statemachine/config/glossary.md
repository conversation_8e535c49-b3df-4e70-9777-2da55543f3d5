# Glossary
## State Machine
The main entity that drives a collection of states, together with regions, transitions, and events.


状态机  
驱动状态集合以及区域、转换和事件的主要实体。


## State
A state models a situation during which some invariant condition holds. The state is the main entity of a state machine where state changes are driven by events.

状态  
状态模拟一种情况，在这种情况下，某些不变条件保持不变。状态是状态机的主要实体，其中状态更改由事件驱动。

## Extended State
An extended state is a special set of variables kept in a state machine to reduce the number of needed states.

扩张状态  
扩展状态是保存在状态机中的一组特殊变量，用于减少所需状态的数量。
## Transition
A transition is a relationship between a source state and a target state. It may be part of a compound transition, which takes the state machine from one state configuration to another, representing the complete response of the state machine to an occurrence of an event of a particular type.

过渡  
转换是源状态和目标状态之间的关系。它可能是复合转换的一部分，复合转换将状态机从一种状态配置转换为另一种状态配置，表示状态机对特定类型事件发生的完整响应。

## Event
An entity that is sent to a state machine and then drives a various state changes.

事件  
发送到状态机然后驱动各种状态更改的实体。
## Initial State
A special state in which the state machine starts. The initial state is always bound to a particular state machine or a region. A state machine with multiple regions may have a multiple initial states.

初始状态  
状态机启动时的一种特殊状态。初始状态总是绑定到特定的状态机或区域。具有多个区域的状态机可能具有多个初始状态。

## End State
(Also called as a final state.) A special kind of state signifying that the enclosing region is completed. If the enclosing region is directly contained in a state machine and all other regions in the state machine are also completed, the entire state machine is completed.

最终状态  
(也称为最终状态。)表示封闭区域完成的一种特殊状态。如果封闭区域直接包含在状态机中，并且状态机中的所有其他区域也已完成，则整个状态机已完成。

## History State
A pseudo state that lets a state machine remember its last active state. Two types of history state exists: shallow (which remembers only top level state) and deep (which remembers active states in sub-machines).

历史状态  
一种伪状态，允许状态机记住其最后的活动状态。存在两种类型的历史状态:浅层(只记住顶级状态)和深层(记住子机器中的活动状态)。

## Choice State
A pseudo state that allows for making a transition choice based on (for example) event headers or extended state variables.

选择状态  
一种伪状态，允许基于(例如)事件头或扩展状态变量做出转换选择。
## Junction State
A pseudo state that is relatively similar to choice state but allows multiple incoming transitions, while choice allows only one incoming transition.

连接状态  
一种伪状态，与选择状态相对类似，但允许多个传入转换，而选择只允许一个传入转换。
## Fork State
A pseudo state that gives controlled entry into a region.

叉状态  
一种伪状态，提供进入某一区域的受控入口。

## Join State
A pseudo state that gives controlled exit from a region.


连接状态  
一种伪状态，提供从某个区域的受控退出。

## Entry Point
A pseudo state that allows controlled entry into a submachine.

入口点  
一种允许受控进入子机器的伪状态。

## Exit Point
A pseudo state that allows controlled exit from a submachine.

出口点  
一种伪状态，允许从子机器中受控退出。

## Region
A region is an orthogonal part of either a composite state or a state machine. It contains states and transitions.

地区  
区域是复合状态或状态机的正交部分。它包含状态和转换。

## Guard
A boolean expression evaluated dynamically based on the value of extended state variables and event parameters. Guard conditions affect the behavior of a state machine by enabling actions or transitions only when they evaluate to TRUE and disabling them when they evaluate to FALSE.

警卫  
基于扩展状态变量和事件参数的值动态求值的布尔表达式。保护条件影响状态机的行为，只有当动作或转换的值为TRUE时才启用它们，当它们的值为FALSE时禁用它们。

## Action
A action is a behavior run during the triggering of the transition.

行动  
动作是在触发转换期间运行的行为。




## Choice
You can use the Choice pseudo state choose a dynamic conditional branch of a transition from this state.  The dynamic condition is evaluated by guards so that one branch is selected.  Usually a simple if/elseif/else structure is used to make sure that one branch is selected.  Otherwise, the state machine might end up in a deadlock, and the configuration is ill-formed.

选择  
您可以使用Choice伪状态从该状态选择转换的动态条件分支。通过警卫对动态条件进行评估，选择一个支路。通常使用一个简单的if/elseif/else结构来确保选择一个分支。否则，状态机可能会陷入死锁，并且配置格式不正确。


## Junction
The Junction pseudo state is functionally similar to choice, as both are implemented with if/elseif/else structures.  The only real difference is that junction allows multiple incoming transitions, while choice allows only one.  Thus difference is largely academic but does have some differences, such as when a state machine is designed is used with a real UI modeling framework.

链接   
Junction伪状态在功能上类似于choice，因为两者都是用if/elseif/else结构实现的。唯一真正的区别是，连接允许多个传入转换，而选择只允许一个。因此，差异在很大程度上是学术性的，但确实存在一些差异，例如什么时候设计状态机与真正的UI建模框架一起使用。




## Guard Conditions
Guard conditions are expressions which evaluates to either TRUE or FALSE, based on extended state variables and event parameters.  Guards are used with actions and transitions to dynamically choose whether a particular action or transition should be run.  The various spects of guards, event parameters, and extended state variables exist to make state machine design much more simple.

守卫条件  
保护条件是基于扩展状态变量和事件参数计算为TRUE或FALSE的表达式。保护与操作和转换一起使用，以动态选择是否应该运行特定的操作或转换。保护、事件参数和扩展状态变量的各个方面的存在使得状态机的设计更加简单。

## Events
Event is the most-used trigger behavior to drive a state machine.  There are other ways to trigger behavior in a state machine (such as a timer), but events are the ones that really let users interact with a state machine.  Events are also called “signals”.  They basically indicate something that can possibly alter a state machine state.

事件  
事件是驱动状态机最常用的触发行为。在状态机中还有其他触发行为的方法(比如定时器)，但是事件才是真正让用户与状态机交互的方法。事件也被称为“信号”。它们基本上指示可能改变状态机状态的东西。

## Transitions
A transition is a relationship between a source state and a target state.  A switch from one state to another is a state transition caused by a trigger.

转换  
转换是源状态和目标状态之间的关系。从一种状态到另一种状态的转换是由触发器引起的状态转换。

## Internal Transition
Internal transition is used when an action needs to be run without causing a state transition.  In an internal transition, the source state and the target state is always the same, and it is identical with a self-transition in the absence of state entry and exit actions.

内部转换  
当需要运行操作而不引起状态转换时，使用内部转换。在内部转换中，源状态和目标状态总是相同的，并且在没有状态进入和退出操作的情况下，它与自转换是相同的。







## Triggers
A trigger begins a transition. Triggers can be driven by either events or timers.

触发器  
触发器开始转换。触发器可以由事件或计时器驱动。

## Actions
Actions really glue state machine state changes to a user’s own code. A state machine can run an action on various changes and on the steps in a state machine (such as entering or exiting a state) or doing a state transition.

Actions usually have access to a state context, which gives running code a choice to interact with a state machine in various ways. State context exposes a whole state machine so that a user can access extended state variables, event headers (if a transition is based on an event), or an actual transition (where it is possible to see more detailed about where this state change is coming from and where it is going).

行动  
动作将状态机状态更改粘合到用户自己的代码中。状态机可以对状态机中的各种更改和步骤(例如进入或退出状态)或执行状态转换运行操作。

操作通常可以访问状态上下文，这为运行代码提供了以各种方式与状态机交互的选择。状态上下文公开了整个状态机，以便用户可以访问扩展的状态变量、事件头(如果转换是基于事件的)或实际的转换(在这种情况下，可以更详细地了解此状态更改来自何处以及将去往何处)。

## Hierarchical State Machines
The concept of a hierarchical state machine is used to simplify state design when particular states must exist together.

Hierarchical states are really an innovation in UML state machines over traditional state machines, such as Mealy or Moore machines. Hierarchical states lets you define some level of abstraction (parallel to how a Java developer might define a class structure with abstract classes). For example, with a nested state machine, you can define transition on a multiple level of states (possibly with different conditions). A state machine always tries to see if the current state is able to handle an event, together with transition guard conditions. If these conditions do not evaluate to TRUE, the state machine merely see what the super state can handle.

分层状态机  
当特定状态必须同时存在时，使用分层状态机的概念来简化状态设计。

层次状态实际上是UML状态机对传统状态机(如Mealy或Moore机)的一种创新。分层状态允许您定义某种级别的抽象(类似于Java开发人员使用抽象类定义类结构的方式)。例如，使用嵌套状态机，您可以在多个状态级别(可能具有不同的条件)上定义转换。状态机总是尝试查看当前状态是否能够处理事件以及转换保护条件。如果这些条件的值不为TRUE，状态机只会查看超级状态可以处理什么。

## Regions
Regions (which are also called as orthogonal regions) are usually viewed as exclusive OR (XOR) operations applied to states. The concept of a region in terms of a state machine is usually a little difficult to understand, but things gets a little simpler with a simple example.

Some of us have a full size keyboard with the main keys on the left side and numeric keys on the right side. You have probably noticed that both sides really have their own state, which you see if you press a “numlock” key (which alters only the behaviour of the number pad itself). If you do not have a full-size keyboard, you can buy an external USB number pad. Given that the left and right side of a keyboard can each exist without the other, they must have totally different states, which means they are operating on different state machines. In state machine terms, the main part of a keyboard is one region and the number pad is another region.

It would be a little inconvenient to handle two different state machines as totally separate entities, because they still work together in some fashion. This independence lets orthogonal regions combine together in multiple simultaneous states within a single state in a state machine.



地区  
区域(也称为正交区域)通常被视为应用于状态的异或(XOR)操作。从状态机的角度来看，区域的概念通常有点难以理解，但通过一个简单的示例，事情会变得简单一些。

有些人用的是全尺寸键盘，主键在左边，数字键在右边。你可能已经注意到，两边都有自己的状态，如果你按下“numlock”键(它只改变数字键本身的行为)，你就会看到它们。如果你没有一个全尺寸的键盘，你可以买一个外部USB数字板。考虑到键盘的左右两边可以独立存在，它们必须具有完全不同的状态，这意味着它们在不同的状态机上操作。在状态机术语中，键盘的主要部分是一个区域，数字键盘是另一个区域。

将两个不同的状态机作为完全独立的实体来处理会有点不方便，因为它们仍然以某种方式一起工作。这种独立性允许正交区域在状态机的单个状态内以多个同时状态组合在一起。