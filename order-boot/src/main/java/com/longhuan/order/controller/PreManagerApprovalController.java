package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.dto.digitalize.SysPreFundStatusDTO;
import com.longhuan.order.pojo.vo.PreFundInfoVO;
import com.longhuan.order.pojo.vo.PreFundLogVO;
import com.longhuan.order.pojo.vo.PreFundRecordVO;
import com.longhuan.order.pojo.vo.PreManagerApprovalDetailVO;
import com.longhuan.order.service.DigitalizeService;
import com.longhuan.order.service.PreManagerApprovalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/08/17
 */
@Api(tags = "客户经理预审接口")
@RestController
@RequestMapping("/api/v1/pre/managerApproval")
@RequiredArgsConstructor
public class PreManagerApprovalController {

    private final PreManagerApprovalService preManagerApprovalService;
    private final DigitalizeService digitalizeService;


    /**
     * 获取预审信息
     *
     * @param preId 预审id
     * @return 预审信息
     */
    @ApiOperation("获取预审信息")
    @GetMapping("/detail/{preId}")
    public Result<PreManagerApprovalDetailVO> getDetail(@PathVariable("preId") Integer preId) {

        return Result.success(preManagerApprovalService.queryDetailByPreId(preId));
    }

    /**
     * 获取预审资方列表
     *
     * @param preId 预审id
     * @return 预审信息
     */
    @ApiOperation("获取预审资方信息列表")
    @GetMapping("/fund/list/{preId}")
    public Result<List<PreFundInfoVO>> preFundList(@PathVariable("preId") Integer preId,@CurrentUser LoginUser loginUser) {

        return Result.success(preManagerApprovalService.preFundList(preId,loginUser));
    }

    /**
     * 更换资方
     *
     * @param dto dto
     * @return {@link Result< Integer>}
     */
    @ApiOperation("更换资方")
    @PostMapping("/updateFund")
    public Result<Integer> updateFund(@Validated @RequestBody PreManagerUpdateFundDTO dto) {
        Integer fundId = preManagerApprovalService.changePreFund(dto);
        return Result.success(fundId);
    }

    /**
     * 资方前重试
     *
     * @param dto DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation("资方预审重试")
    @PostMapping("/fund_pre_retry")
    public Result<Boolean> fundPreRetry(@Validated @RequestBody FundPreRetryDTO dto) {
        preManagerApprovalService.fundPreRetry(dto);
        return Result.success(true);
    }
    /**
     * 电销线上重新选择资方
     *
     * @param dto DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation("电销线上重新选择资方")
    @PostMapping("/onLineReSelectFund")
    public Result<Boolean> onLineReSelectFund(@Validated @RequestBody FundPreRetryDTO dto ,@CurrentUser LoginUser loginUser) {
        return Result.success(preManagerApprovalService.onLineReSelectFund(dto,loginUser));
    }
    /**
     * 提交预审
     *
     * @param dto 预审信息
     * @return {@link Result< Integer>} 订单id
     */
    @ApiOperation("提交预审")
    @PostMapping("/submit")
    public Result<Integer> submit(@Validated @RequestBody PreManagerApprovalSubmitDTO dto) {
        return Result.success(preManagerApprovalService.submitPreInfo(dto));
    }


    @ApiOperation(value = "预审更新资方状态")
    @PostMapping("/preUpdateFundStatus")
    Result<Boolean> preUpdateFundStatus(@RequestBody @Validated PreApproveFundStatusDTO fundStatusDTO) {
        return Result.success(preManagerApprovalService.updateFundStatus(fundStatusDTO));
    }

    @ApiOperation(value = "同步资方预授信状态")
    @PostMapping("/sysPreFundStatus")
    Result<Boolean> sysPreFundStatus(@RequestBody @Validated SysPreFundStatusDTO sysPreFundStatusDTO) {
        //  同步数字化资方预审状态；通过：流程与云启保持一致；拒绝：再次推送预审资方信息，数字化再次发起下一个资方预授信
        return Result.success(digitalizeService.sysPreFundStatus(sysPreFundStatusDTO));
    }


    /**
     * 获取资方发起日志
     */
    @ApiOperation("获取资方发起日志")
    @GetMapping("/fundLog/{preId}")
    public Result<List<PreFundLogVO>> getPreFundLog(@PathVariable("preId") Integer preId) {
        return Result.success(preManagerApprovalService.getPreFundLog(preId));
    }

    @ApiOperation("获取资方征信策略")
    @GetMapping("/fundCredit/{preId}")
    public Result<String> fundCredit(@PathVariable("preId") Integer preId, @CurrentUser LoginUser loginUser) {
        return Result.success(preManagerApprovalService.fundCredit(preId,loginUser));
    }

    /**
     * 获取预授信记录列表
     *
     * @param preId 预审id
     * @return 预授信记录列表
     */
    @ApiOperation("获取预授信记录列表")
    @GetMapping("/preFundRecords/{preId}")
    public Result<List<PreFundRecordVO>> getPreFundRecords(@PathVariable("preId") Integer preId) {
        return Result.success(preManagerApprovalService.getPreFundRecords(preId));
    }
    @ApiOperation("批量更新客户经理")
    @PostMapping("/batchUpdateManager")
    public Result<Boolean> batchUpdateManager(@RequestBody updateManagerDTO dto){
        return Result.success(preManagerApprovalService.batchUpdateManager(dto));
    }


}
