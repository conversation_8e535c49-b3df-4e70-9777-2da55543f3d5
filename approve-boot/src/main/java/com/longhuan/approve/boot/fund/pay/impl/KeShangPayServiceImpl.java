package com.longhuan.approve.boot.fund.pay.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.approve.api.pojo.dto.FundAmountChangeDTO;
import com.longhuan.approve.api.pojo.dto.FundPayDTO;
import com.longhuan.approve.boot.enums.FundApiDictEnum;
import com.longhuan.approve.boot.enums.FundApiDictTypeEnum;
import com.longhuan.approve.boot.enums.FundApiRecordStatusEnum;
import com.longhuan.approve.boot.fund.keshang.KeShangService;
import com.longhuan.approve.boot.fund.pay.FundPayStrategy;
import com.longhuan.approve.boot.mapper.FinalFundInfoMapper;
import com.longhuan.approve.boot.mapper.FundApiDictMapper;
import com.longhuan.approve.boot.mapper.OrderInfoMapper;
import com.longhuan.approve.boot.pojo.dto.FundApiRecordDTO;

import com.longhuan.approve.boot.pojo.dto.mzkeshang.KeShangResponseHead;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.request.KeShangAddBusinessCancelRequest;
import com.longhuan.approve.boot.pojo.entity.FinalFundInfoEntity;
import com.longhuan.approve.boot.pojo.entity.OrderInfoEntity;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.enums.OrderPaymentStateEnum;
import com.longhuan.common.web.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 梅州客商
 */
@Slf4j
@Service("PAY_MZ_KE_SHANG")
@RequiredArgsConstructor
public class KeShangPayServiceImpl implements FundPayStrategy {

    private final FinalFundInfoMapper finalFundInfoMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final FundApiDictMapper fundApiDictMapper;
    private final KeShangService keShangService;

    /**
     * 支付
     *
     * @param fundPayDTO 资方支付DTO
     * @return {@link FundApiRecordDTO }
     */
    @Override
    public FundApiRecordDTO pay(FundPayDTO fundPayDTO) {
        log.info("KeShangPayServiceImpl.pay start, orderId: {}", fundPayDTO.getOrderId());
        //todo 返回实体
        KeShangResponseHead result = null;
        try {
             result = keShangService.loanApplyByOrderId(fundPayDTO.getOrderId());
        } catch (BusinessException e) {
            log.error("KeShangPayServiceImpl.pay BusinessException, orderId: {}, error: {}", fundPayDTO.getOrderId(), e.getMessage(),e );
            return new FundApiRecordDTO().setStatus(FundApiRecordStatusEnum.CALL_FAILED).setRemark(e.getMessage());
        } catch (Exception e) {
            log.error("KeShangPayServiceImpl.pay Exception, orderId: {}, error: {}", fundPayDTO.getOrderId(), e.getMessage(),e );
            return new FundApiRecordDTO().setStatus(FundApiRecordStatusEnum.CALL_FAILED).setRemark("放款申请失败，系统异常");
        }
        log.info("KeShangPayServiceImpl.pay orderId:{} result: {}",  fundPayDTO.getOrderId(), result);
        if (!result.isSuccess(result)) {
            return new FundApiRecordDTO().setStatus(FundApiRecordStatusEnum.CALL_FAILED).setRemark(result.getRetMsg());
        }
        return new FundApiRecordDTO().setStatus(FundApiRecordStatusEnum.CALL_SUCCESS).setRemark("放款成功");
    }

    @Override
    public FundApiRecordDTO payResult(FundPayDTO fundPayDTO) {
        log.info("KeShangPayServiceImpl.payResult start, orderId: {}", fundPayDTO.getOrderId());
        KeShangResponseHead result = keShangService.applyLoanResult(fundPayDTO.getOrderId());
        log.info("KeShangPayServiceImpl.payResult orderId:{} result: {}",  fundPayDTO.getOrderId(), JSONUtil.toJsonStr(result));
        return new FundApiRecordDTO().setStatus(FundApiRecordStatusEnum.CALL_SUCCESS).setRemark("请求成功");
    }


    @Override
    public FundApiRecordDTO cancel(FundPayDTO fundPayDTO) {
        log.info("KeShangPayServiceImpl.cancel start, orderId: {}", fundPayDTO.getOrderId());
        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, fundPayDTO.getOrderId())
                .eq(FinalFundInfoEntity::getFundId, FundEnum.MZ_KE_SHANG.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0));

        if (finalFundInfoEntity == null) {
            log.info("KeShangPayServiceImpl.cancel not found finalFundInfoEntity orderId:{}", fundPayDTO.getOrderId());
            return null;
        }
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(fundPayDTO.getOrderId());
        if (orderInfo == null) {
            log.info("KeShangPayServiceImpl.cancel not found orderInfo orderId:{}", fundPayDTO.getOrderId());
            return null;
        }
        if (ObjUtil.equals(orderInfo.getPaymentState(), OrderPaymentStateEnum.PASS)) {
            log.info("KeShangPayServiceImpl.cancel orderInfo is pass orderId:{}", fundPayDTO.getOrderId());
            throw new BusinessException("订单已放款，无法取消订单");
        }

        //todo 资方取消是不是 进件撤销
        KeShangAddBusinessCancelRequest request = new KeShangAddBusinessCancelRequest();
        request.setLoanAplyNO("");//todo
        request.setRplRsn(null);//todo
        //todo 请求进件撤销的接口


        return null;
    }

    @Override
    public FundApiRecordDTO amountChange(FundAmountChangeDTO fundAmountChangeDTO) {
        return null;
    }
}
