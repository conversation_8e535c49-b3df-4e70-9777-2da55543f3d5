package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.ProductRongdanDTO;
import com.longhuan.order.pojo.entity.ProductRongdanEntity;
import com.longhuan.order.pojo.vo.ProductFundMappingVO;
import com.longhuan.order.pojo.vo.ProductRongdanVO;
import com.longhuan.order.service.ProductRongdanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 融担公司表控制层
 *
 * <AUTHOR> css
 * @date : 2025-05-16
 */
@Api(tags = "融担公司功能接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/rongdan")
public class ProductRongdanController {
    private final ProductRongdanService productRongdanService;

    /**
     * 查询列表
     *
     * @return 实例对象
     */
    @ApiOperation(value = "分页查询融担公司")
    @PostMapping("/page/productRongdanList")
    public Result<Page<ProductRongdanVO>> selectProductRongdanList(@RequestBody ProductRongdanDTO dto) {
        return Result.success(productRongdanService.selectProductRongdanList(dto));
    }

    /**
     * 通过融担公司id获取详情
     *
     * @param id 融担公司id
     * @return {@link Result< ProductRongdanEntity >}
     */
    @ApiModelProperty("通过融担公司id获取详情")
    @GetMapping("/queryInfoById/{id}")
    public Result<ProductRongdanEntity> queryInfo(@PathVariable("id") Integer id) {
        return Result.success(productRongdanService.queryById(id));
    }

    /**
     * 新增融担公司
     *
     * @param dto 融担公司信息
     */
    @PostMapping("/insertProductRongdanInfo")
    public Result<Boolean> insertProductRongdanInfo(@RequestBody @Validated ProductRongdanDTO dto) {
        return Result.success(productRongdanService.insertProductRongdanInfo(dto));
    }

    /**
     * 编辑融担公司
     *
     * @param dto 融担公司信息
     */
    @PostMapping("/updateProductRongdanInfo")
    public Result<Boolean> updateProductRongdanInfo(@RequestBody @Validated ProductRongdanDTO dto) {
        return Result.success(productRongdanService.updateProductRongdanInfo(dto));
    }

    /**
     *  根据资方id查询产品列表
     * @param id 资方id
     * @return {@link Result<  List <   ProductFundMappingVO  >>}
     */
    @ApiOperation("根据资方id查询产品列表")
    @GetMapping("/queryProductListByFundId/{id}")
    public Result<List<ProductFundMappingVO>> queryProductByFundIdList(@PathVariable("id") Integer id, Integer rongdanId) {
        return Result.success(productRongdanService.queryProductListByFundId(id, rongdanId));
    }
}