-- 门店GPS分期和收费策略配置表
-- 表名: lh_store_gps_config
-- 描述: 配置每个门店是否支持GPS分期付款以及是否支持免收费服务

-- 创建表
CREATE TABLE IF NOT EXISTS lh_store_gps_config (
                                                   id serial,
                                                   dept_id INTEGER NOT NULL,
                                                   is_installment INTEGER NOT NULL DEFAULT 0,
                                                   is_free INTEGER NOT NULL DEFAULT 0,
                                                   delete_flag INTEGER NOT NULL DEFAULT 0,
                                                   create_by INTEGER,
                                                   create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                                                   update_by INTEGER,
                                                   update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 添加表注释
COMMENT ON TABLE lh_store_gps_config IS '门店GPS分期和收费策略配置表';

-- 添加字段注释
COMMENT ON COLUMN lh_store_gps_config.id IS '主键';
COMMENT ON COLUMN lh_store_gps_config.dept_id IS '门店ID，关联lh_dept表';
COMMENT ON COLUMN lh_store_gps_config.is_installment IS '是否支持分期（0：不支持，1：支持）';
COMMENT ON COLUMN lh_store_gps_config.is_free IS '是否支持免费（0：不支持，1：支持）';
COMMENT ON COLUMN lh_store_gps_config.delete_flag IS '是否有效（0：有效，1:无效）';
COMMENT ON COLUMN lh_store_gps_config.create_by IS '创建人';
COMMENT ON COLUMN lh_store_gps_config.create_time IS '创建时间';
COMMENT ON COLUMN lh_store_gps_config.update_by IS '更新人';
COMMENT ON COLUMN lh_store_gps_config.update_time IS '更新时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_lh_store_gps_config_dept_id ON lh_store_gps_config(dept_id);
CREATE INDEX IF NOT EXISTS idx_lh_store_gps_config_is_installment ON lh_store_gps_config(is_installment);
CREATE INDEX IF NOT EXISTS idx_lh_store_gps_config_is_free ON lh_store_gps_config(is_free);
CREATE INDEX IF NOT EXISTS idx_lh_store_gps_config_delete_flag ON lh_store_gps_config(delete_flag);
CREATE INDEX IF NOT EXISTS idx_lh_store_gps_config_create_time ON lh_store_gps_config(create_time);

-- 添加唯一约束，确保每个门店只有一条配置记录
CREATE UNIQUE INDEX IF NOT EXISTS uk_lh_store_gps_config_dept_id ON lh_store_gps_config(dept_id) WHERE delete_flag = 0;
