package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.common.core.enums.OrderFundPaymentEnum;
import com.longhuan.common.core.enums.OrderPaymentStateEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.converter.OrderContactPersonPartsConverter;
import com.longhuan.order.converter.OrderVehicleInfoConverter;
import com.longhuan.order.converter.RiskPromptConverter;
import com.longhuan.common.core.enums.AfterLoanPatchesEnum;
import com.longhuan.order.enums.PhoneOnlineEnum;
import com.longhuan.order.feign.RiskFeign;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.FinalApproveFundStatusDTO;
import com.longhuan.order.pojo.dto.OrderApproveFundPaymentStatusDTO;
import com.longhuan.order.pojo.dto.RiskModelDTO;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.AfterLoanPatchesEntityService;
import com.longhuan.order.service.FinalFundInfoService;
import com.longhuan.order.service.OrderDetailsInfoService;
import com.longhuan.order.service.OrderServicePrivateMethod;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.risk.pojo.vo.FactorVO;
import com.longhuan.risk.pojo.vo.RiskCreditFeatureVO;
import com.longhuan.user.pojo.vo.UserInfoVO;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class OrderServicePrivateMethodImpl implements OrderServicePrivateMethod {
    private final OrderRiskPolicyResultMapper orderRiskPolicyResultMapper;
    private final RiskFeign riskFeign;
    private final OrderInfoMapper orderInfoMapper;
    private final AfterLoanPatchesEntityService afterLoanPatchesEntityService;
    private final FinalFundInfoService finalFundInfoService;
    private final OrderVehicleInfoMapper orderVehicleInfoMapper;
    private final OrderVehicleInfoConverter orderVehicleInfoConverter;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final OrderDetailsInfoService orderDetailsInfoService;
    private final OrderContactPersonPartsMapper orderContactPersonPartsMapper;
    private final OrderContactPersonPartsConverter orderContactPersonPartsConverter;
    private final RiskPromptMapper riskPromptMapper;
    private final RiskPromptConverter riskPromptConverter;
    private final UserFeign userFeign;

    /**
     * 填充模型分数
     *
     * @param orderId        订单 ID
     * @param orderDetailsVo 订单详情 VO
     */
    @Override
    public void fillModelScore(Integer orderId, OrderDetailsVo orderDetailsVo) {
        OrderRiskPolicyResultEntity orderRiskPolicyResultEntity = orderRiskPolicyResultMapper.selectOne(new LambdaQueryWrapper<OrderRiskPolicyResultEntity>()
                .eq(OrderRiskPolicyResultEntity::getOrderId, orderId).eq(OrderRiskPolicyResultEntity::getPolicyName, "贷前模型")
                .orderByDesc(OrderRiskPolicyResultEntity::getCreateTime).last("limit 1"));

        if (orderRiskPolicyResultEntity != null) {
            String suggestion = orderRiskPolicyResultEntity.getSuggestion();

            RiskModelDTO dto = JSONUtil.toBean(suggestion, RiskModelDTO.class);

            if ("6.0.0".equals(dto.getVersion())) {
                orderDetailsVo.setModelScore("模型分:" + dto.getScore() + "分");
                orderDetailsVo.setApprovalConclusion(dto.getLevel());
                orderDetailsVo.setRemind(dto.getRemind());
            } else {
                if ("200".equals(dto.getCode())) {
                    // 模型评分 ：650分  670分
                    orderDetailsVo.setModelScore("全量模型：" + dto.getScore1() + "分 专家模型：" + dto.getScore2() + "分");
                    // 辅助审批结论：A1B2
                    orderDetailsVo.setAssistanceApprovalConclusion(dto.getSuggestion());

                    // 审批结论：自动通过（模型等级为A1A2、A1B2、B1A2且软评额度小于等于20W）
                    // 自动通过
                    if ((dto.getSuggestion().equals("A1A2") || dto.getSuggestion().equals("A1B2") || dto.getSuggestion().equals("B1A2")
                            || dto.getSuggestion().equals("B1B2"))
                            && orderDetailsVo.getSoftwareCredit() != null
                            && orderDetailsVo.getSoftwareCredit().compareTo(BigDecimal.valueOf(200000)) <= 0) {
                        orderDetailsVo.setApprovalConclusion("自动通过");
                    } else {
                        // 人工审批
                        orderDetailsVo.setApprovalConclusion("人工审批");
                    }
                }
            }


        }

    }

    @Override
    public void fillThreeFactorFeature(OrderDetailsVo orderDetailsVo) {
        String idNumber = orderDetailsVo.getIdNumber();
        String customerPhone = orderDetailsVo.getCustomerPhone();
        String name = orderDetailsVo.getName();
        if (StringUtils.isEmpty(customerPhone)) {
            return;
        }
        if (StringUtils.isEmpty(idNumber)) {
            return;
        }

        FactorVO data = getFactor(name, customerPhone, idNumber);
        orderDetailsVo.setCustomerPhoneOnline(PhoneOnlineEnum.getDescriptionByCode(data.getNetworkDuration()));
        orderDetailsVo.setThreeFactorVerify(data.getThreeFactorVerify());
    }

    /**
     * 获取因子
     *
     * @param name          名字
     * @param customerPhone 客户电话
     * @param idNumber      id数
     * @return {@link FactorVO }
     */
    @Override
    public FactorVO getFactor(String name, String customerPhone, String idNumber) {
        Result<FactorVO> result = riskFeign.getFactorData(name, customerPhone, idNumber);
        if (Result.isSuccess(result) && result.getData() != null) {
            return result.getData();
        }
        return new FactorVO();
    }

    @Override
    public void fillRiskCreditFeature(OrderDetailsVo orderDetailsVo) {
        Result<RiskCreditFeatureVO> creditFeatureResult = riskFeign.getCreditFeature(orderDetailsVo.getIdNumber());

        if (Result.isSuccess(creditFeatureResult) && creditFeatureResult.getData() != null) {
            RiskCreditFeatureVO creditFeature = creditFeatureResult.getData();
            // 是否逾期
            orderDetailsVo.setIsOverdue(creditFeature.getIsOverdue());

            // 近两年内所有账户累计逾期期数
            orderDetailsVo.setAllCumulative24Month(creditFeature.getAllCumulative24Month());

            // 近两年内单笔最高连续逾期期数
            orderDetailsVo.setSingleContinuous24Month(creditFeature.getSingleContinuous24Month());

            // 近两年内单笔最高累计逾期期数
            orderDetailsVo.setOverdueCumulative24Month(creditFeature.getOverdueCumulative24Month());

            // 是否白户
            orderDetailsVo.setIsBlankAccount(creditFeature.getIsBlankAccount());

            // 在还贷款笔数
            orderDetailsVo.setRepaymentNum(creditFeature.getRepaymentNum());

            // 在还贷款金额
            orderDetailsVo.setRepaymentAmount(creditFeature.getRepaymentAmount());

            // 信用卡授信额度
            orderDetailsVo.setCreditLimit(creditFeature.getCreditLimit());

            // 信用卡已用额度
            orderDetailsVo.setCreditUsedAmount(creditFeature.getCreditUsedAmount());

            // 3个月内历史查询次数
            orderDetailsVo.setSearchNum(creditFeature.getSearchNum());

            // 当前单笔最高逾期期数
            orderDetailsVo.setOverdueMaxNum(creditFeature.getOverdueMaxNum());

            // 当前逾期总笔数
            orderDetailsVo.setOverdueTotalNum(creditFeature.getOverdueTotalNum());

            // 当前逾期总金额
            orderDetailsVo.setNowOverdueAmount(creditFeature.getNowOverdueAmount());

            // 不良资产处置次数
            orderDetailsVo.setBadAsset(creditFeature.getBadAsset());

            // 客户所有状态
            orderDetailsVo.setCustomerAllStatus(creditFeature.getCustomerAllStatus());

            // 征信负债月还
            orderDetailsVo.setCreditDebtMonthRepayment(creditFeature.getCreditDebtMonthRepayment());
        }
    }

    @Override
    public void processOrderItems(OrderInfoListVO item) {

        // 业务补录后，订单进入申请状态
        item.setOrderState(1);
        if (Objects.equals(item.getCurrentNode(), States.PAYMENT_SUCCESS.getNode())) {
            // 放款成功后，进入还款状态
            item.setOrderState(2);
        }
    }

    /**
     * 额度详情
     *
     * @param orderId 订单id
     * @return {@link AmountDetailVO}
     */
    @Override
    public AmountDetailVO getCoreOrderDetails(Integer orderId) {
        MPJLambdaWrapper<OrderInfoEntity> wrapper = new MPJLambdaWrapper<>();

        wrapper.select(OrderInfoEntity::getApprovalAmount)
                //获取订单信息
                .selectAs(OrderInfoEntity::getId, AmountDetailVO::getOrderId)
                .selectAs(OrderInfoEntity::getApplyAmount, AmountDetailVO::getApplyAmount)
                .selectAs(OrderInfoEntity::getTerm, AmountDetailVO::getTerm)
                .selectAs(OrderInfoEntity::getState, AmountDetailVO::getState)
                //获取产品信息
                .selectAs(ProductInfoEntity::getMonthlyRate, AmountDetailVO::getAnnualInterestRate)
                .selectAs(ProductInfoEntity::getGuaranteeFee, AmountDetailVO::getGuaranteeRate)
                .selectAs(ProductInfoEntity::getTerm, AmountDetailVO::getGuaranteePeriod)
                //获取还款方式
                .selectAs(OrderInfoEntity::getRepayMethod, AmountDetailVO::getPaymentType)
                //获取资方审批额度
                .selectAs(OrderAmountEntity::getFundPreAmount, AmountDetailVO::getFundCreditAmount)
                .selectAs(OrderAmountEntity::getRiskAmount, AmountDetailVO::getRiskAmount)
                //获取确认额度
                .selectAs(OrderAmountEntity::getCustomerConfirmAmount, AmountDetailVO::getCustomerConfirmAmount)

                .selectAs(FundInfoEntity::getDeratingLoanFlag, AmountDetailVO::getDeratingLoanFlag);

        wrapper.leftJoin(ProductInfoEntity.class, on -> on
                .eq(OrderInfoEntity::getProductId, ProductInfoEntity::getId)
                .eq(ProductInfoEntity::getDeleteFlag, 0)
        );
        wrapper.leftJoin(OrderAmountEntity.class, on -> on
                .eq(OrderInfoEntity::getId, OrderAmountEntity::getOrderId)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
        );
        wrapper.leftJoin(FundInfoEntity.class, on -> on
                .eq(OrderInfoEntity::getFundId, FundInfoEntity::getId)
        );
        wrapper.eq(OrderInfoEntity::getDeleteFlag, 0)
                .eq(OrderInfoEntity::getId, orderId);

        return orderInfoMapper.selectJoinOne(AmountDetailVO.class, wrapper);
    }

    /**
     * 更新订单放款状态
     *
     * @param fundStatusDTO 资方信息
     * @return {@link boolean}
     */
    @Override
    public boolean updateOrderPaymentStatus(OrderApproveFundPaymentStatusDTO fundStatusDTO) {
        log.info("OrderServiceImpl.updateOrderPaymentStatus begin fundStatusDTO: {}", JSONUtil.toJsonStr(fundStatusDTO));
        OrderFundPaymentEnum status = fundStatusDTO.getStatus();
        BigDecimal loanAmt = fundStatusDTO.getLoanAmt();

        LambdaUpdateWrapper<OrderInfoEntity> orderLuw = Wrappers.<OrderInfoEntity>lambdaUpdate()
                .ne(OrderInfoEntity::getPaymentState, OrderPaymentStateEnum.PASS)
                .eq(OrderInfoEntity::getId, fundStatusDTO.getOrderId());
        //订单放款状态
        log.info("OrderServiceImpl.updateOrderPaymentStatus update order {} payment state to {}", fundStatusDTO.getOrderId(), status);
        switch (status) {
            case PASS -> {
                //放款通过
                orderLuw.set(OrderInfoEntity::getPaymentState, OrderPaymentStateEnum.PASS);
                orderLuw.set(ObjUtil.isNotNull(loanAmt), OrderInfoEntity::getApplyAmount, loanAmt);
                orderLuw.set(ObjUtil.isNotNull(loanAmt), OrderInfoEntity::getApprovalAmount, loanAmt);
                orderLuw.set(OrderInfoEntity::getPaymentTime, Convert.toLocalDateTime(fundStatusDTO.getLoanPayTime(), LocalDateTime.now()));
                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(fundStatusDTO.getOrderId());
                if (ObjUtil.isNotEmpty(orderInfoEntity) && orderInfoEntity.getPaymentType() == 2) {
                    List<AfterLoanPatchesEntity> list = afterLoanPatchesEntityService.list(new LambdaQueryWrapper<AfterLoanPatchesEntity>()
                            .eq(AfterLoanPatchesEntity::getOrderId, fundStatusDTO.getOrderId())
                            .eq(AfterLoanPatchesEntity::getDeleteFlag, 0));
                    if (CollUtil.isEmpty(list)) {
                        AfterLoanPatchesEntity entity = new AfterLoanPatchesEntity();
                        entity.setOrderId(fundStatusDTO.getOrderId());
                        entity.setAfterLoanStatus(AfterLoanPatchesEnum.AFTER_LOAN_PATCHES.getCode());
                        entity.setGpsState(orderInfoEntity.getGpsState());
                        entity.setPayoffState(orderInfoEntity.getPlanState());
                        afterLoanPatchesEntityService.save(entity);
                    }
                }

            }
            case FAIL -> {
                //放款失败
                //判断是否所有资方放款失败
                if (finalFundInfoService.isAllFinalPaymentFundFail(fundStatusDTO.getOrderId())) {
                    //所有资方放款失败，订单放款状态为放款失败
                    orderLuw.set(OrderInfoEntity::getPaymentState, OrderPaymentStateEnum.FAIL);
                } else {
                    //部分资方放款失败，订单放款状态为放款中
                    orderLuw.set(OrderInfoEntity::getPaymentState, OrderPaymentStateEnum.WAIT);
                }
            }
            case WAIT -> {
                throw new BusinessException("资方放款中");
            }
            default -> throw new BusinessException("未知的放款状态");
        }
        boolean flag = orderInfoMapper.update(orderLuw) > 0;
        log.info("OrderServiceImpl.updateOrderPaymentStatus end update order {} payment state to {} flag:{}", fundStatusDTO.getOrderId(), status, flag);
        return flag;
    }

    /**
     * 订单放款验证
     *
     * @param fundStatusDTO 资方结论信息
     */
    @Override
    public void verifyOrderPayment(OrderApproveFundPaymentStatusDTO fundStatusDTO) {
        log.info("OrderServiceImpl.verifyOrderPayment begin fundStatusDTO: {}", JSONUtil.toJsonStr(fundStatusDTO));

        try {
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(fundStatusDTO.getOrderId());
            Assert.notNull(orderInfo, "订单不存在!");

            Integer currentNode = orderInfo.getCurrentNode();
            OrderFundPaymentEnum status = fundStatusDTO.getStatus();
            if (null == status) {
                throw new BusinessException("资方放款状态为空");
            }

            int fundsPaymentProcessNode = States.FUNDS_PAYMENT_PROCESS.getNode();
            int fundsPaymentApprovalNode = States.FUNDS_PAYMENT_APPROVAL.getNode();

            boolean isCurrentNodeValid = Objects.equals(currentNode, fundsPaymentProcessNode)
                    || Objects.equals(currentNode, fundsPaymentApprovalNode);

            switch (status) {
                case FAIL, PASS -> {
                    if (!isCurrentNodeValid) {
                        throw new BusinessException("业务节点异常");
                    }
                }
                case WAIT -> {
                    if (!Objects.equals(currentNode, fundsPaymentApprovalNode)) {
                        throw new BusinessException("业务节点异常");
                    }
                }
            }
            log.info("OrderServiceImpl.verifyOrderPayment end SUCCESS");
        } catch (Exception e) {
            log.error("OrderServiceImpl.verifyOrderPayment error e:", e);
            throw new BusinessException("订单放款验证异常");
        }
    }

    @Override
    public StoreAppraiserInfoVO getStoreAppraiserInfo(Integer orderId) {
        OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(new MPJLambdaWrapper<OrderVehicleInfoEntity>()
                .eq(OrderVehicleInfoEntity::getOrderId, orderId)
                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
        );
        log.info("OrderServiceImpl.getStoreAppraiserInfo orderVehicleInfoEntity: {}", JSONUtil.toJsonStr(orderVehicleInfoEntity));
        if (Objects.isNull(orderVehicleInfoEntity)) {
            return null;
        }
        return orderVehicleInfoConverter.entity2StoreAppraiser(orderVehicleInfoEntity);
    }

    /**
     * 更新最终资方终审信息
     *
     * @param fundStatusDTO 资方状态信息
     */
    @Override
    public void updateFinalFundInfo(FinalApproveFundStatusDTO fundStatusDTO) {
        finalFundInfoMapper.update(Wrappers.lambdaUpdate(FinalFundInfoEntity.class)
                        .set(FinalFundInfoEntity::getFundResult, fundStatusDTO.getStatus())
                        .set(FinalFundInfoEntity::getFundCreditAmount, fundStatusDTO.getFundCreditAmt())
                        .set(FinalFundInfoEntity::getFundRemark, fundStatusDTO.getFundRemark())
                        .set(FinalFundInfoEntity::getFundCreditTime, fundStatusDTO.getFundCreditTime())
//                .set(FinalFundInfoEntity::getCreditReqNo, fundStatusDTO.getCreditReqNo())
                        .set(StrUtil.isNotEmpty(fundStatusDTO.getLoanContractNo()), FinalFundInfoEntity::getLoanContractNo, fundStatusDTO.getLoanContractNo())
                        .set(StrUtil.isNotEmpty(fundStatusDTO.getCreditNo()), FinalFundInfoEntity::getCreditNo, fundStatusDTO.getCreditNo())
                        .set(StrUtil.isNotEmpty(fundStatusDTO.getFundUserId()), FinalFundInfoEntity::getFundUserId, fundStatusDTO.getFundUserId())
                        .eq(FinalFundInfoEntity::getOrderId, fundStatusDTO.getOrderId())
                        .eq(FinalFundInfoEntity::getFundId, fundStatusDTO.getFundId())
        );
//        orderDetailsInfoService.rejectSave(fundStatusDTO);
    }

    @Override
    public OrderContactPersonPartsVo getOrderContactPersonParts(Integer orderId) {
        OrderContactPersonPartsEntity orderContactPersonPartsEntity = orderContactPersonPartsMapper.selectOne(
                new LambdaQueryWrapper<OrderContactPersonPartsEntity>()
                        .eq(OrderContactPersonPartsEntity::getOrderId, orderId)
                        .eq(OrderContactPersonPartsEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderContactPersonPartsEntity::getCreateTime)
                        .last("LIMIT 1"));
        if (Objects.isNull(orderContactPersonPartsEntity)) {
            return null;
        }
        return orderContactPersonPartsConverter.entity2vo(orderContactPersonPartsEntity);
    }

    @Override
    public List<RiskPromptVO> getRiskPromptVOS(OrderInfoEntity orderInfoEntity) {
        List<RiskPromptVO> riskPromptVOList = new ArrayList<>();
        // 客户经理风险提示
        RiskPromptEntity ManagerRiskPromptEntity = riskPromptMapper.selectOne(new LambdaQueryWrapper<RiskPromptEntity>()
                .eq(RiskPromptEntity::getType, 2)
                .eq(RiskPromptEntity::getManagerId, orderInfoEntity.getManagerId())
                .eq(RiskPromptEntity::getDeleteFlag, 0)
                .eq(RiskPromptEntity::getEnable, 0), false);
        if (Objects.nonNull(ManagerRiskPromptEntity)) {
            riskPromptVOList.add(riskPromptConverter.entityToVO(ManagerRiskPromptEntity));
        }
        // 团队经理风险提示
        if (orderInfoEntity.getTeamId() != null) {
            UserInfoVO userInfoVO = userFeign.getTeamManagerByTeamId(orderInfoEntity.getTeamId()).getData();
            if (userInfoVO != null) {
                RiskPromptEntity teamRiskPromptEntity = riskPromptMapper.selectOne(new LambdaQueryWrapper<RiskPromptEntity>()
                        .eq(RiskPromptEntity::getType, 3)
                        .eq(RiskPromptEntity::getManagerId, userInfoVO.getUserId())
                        .eq(RiskPromptEntity::getDeleteFlag, 0)
                        .eq(RiskPromptEntity::getEnable, 0), false);
                if (Objects.nonNull(teamRiskPromptEntity)) {
                    riskPromptVOList.add(riskPromptConverter.entityToVO(teamRiskPromptEntity));
                }
            }
        }
        //门店风险提示
        RiskPromptEntity deptRiskPromptEntity = riskPromptMapper.selectOne(new LambdaQueryWrapper<RiskPromptEntity>()
                .eq(RiskPromptEntity::getType, 1)
                .eq(RiskPromptEntity::getDeptId, orderInfoEntity.getDeptId())
                .eq(RiskPromptEntity::getDeleteFlag, 0)
                .eq(RiskPromptEntity::getEnable, 0), false);
        if (Objects.nonNull(deptRiskPromptEntity)) {
            riskPromptVOList.add(riskPromptConverter.entityToVO(deptRiskPromptEntity));
        }
        log.info("OrderServiceImpl.getRiskPromptVO riskPromptVOList: {}", JSONUtil.toJsonStr(riskPromptVOList));
        return riskPromptVOList;
    }
}
