package com.longhuan.order.controller;

import cn.hutool.core.bean.BeanUtil;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.OverdueWarnConfigSaveOrUpdateDTO;
import com.longhuan.order.pojo.entity.OverdueWarnConfigEntity;
import com.longhuan.order.service.OverdueWarnConfigService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 逾期提醒任务池配置控制器
 *
 * <AUTHOR>
 * @date 2025/1/2 10:30
 */
@Api(tags = "订单车辆信息接口")
@RestController
@RequestMapping("/api/v1/order/overdue/warn")
@RequiredArgsConstructor
public class OverdueWarnConfigController {
    private final OverdueWarnConfigService overdueWarnConfigService;
    @PostMapping("/list")
    public Result<List<OverdueWarnConfigEntity>> list() {
        return Result.success(overdueWarnConfigService.list());
    }
    @PostMapping("/update")
    public Result<Boolean> saveOrUpdate(@RequestBody OverdueWarnConfigSaveOrUpdateDTO dto) {
        return Result.success(overdueWarnConfigService.updateById(BeanUtil.toBean(dto, OverdueWarnConfigEntity.class)));
    }
    @PostMapping("/getPermissionsList")
    public Result<List<OverdueWarnConfigEntity>> getPermissionsList(@CurrentUser LoginUser loginUser) {
        return Result.success(overdueWarnConfigService.getPermissionsList(loginUser));
    }
}
