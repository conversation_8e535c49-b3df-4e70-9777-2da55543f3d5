package com.longhuan.approve.boot.fund.pre.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.longhuan.approve.api.pojo.dto.changyin.ChangYinResBodyDTO;
import com.longhuan.approve.boot.config.KeShangConfig;

import com.longhuan.approve.boot.enums.FundApiDictEnum;
import com.longhuan.approve.boot.enums.FundApiDictTypeEnum;
import com.longhuan.approve.boot.fund.keshang.KeShangService;
import com.longhuan.approve.boot.fund.pre.FundPreApiStrategy;
import com.longhuan.approve.boot.mapper.FundApiDictMapper;
import com.longhuan.approve.boot.mapper.FundApproveMapper;

import com.longhuan.approve.boot.mapper.PreFundInfoMapper;
import com.longhuan.approve.boot.pojo.dto.FundPreBaseDTO;

import com.longhuan.approve.boot.pojo.dto.lanhai.LanHaiResult;
import com.longhuan.approve.boot.pojo.dto.lanhai.request.LanHaiImageUploadRequest;
import com.longhuan.approve.boot.pojo.dto.lanhai.response.LanHaiCreditPreApplyResponse;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.KeShangResponseHead;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.request.KeShangCreditPreApplyRequest;

import com.longhuan.approve.boot.pojo.entity.FundApiDictEntity;
import com.longhuan.approve.boot.pojo.entity.PreFundInfoEntity;
import com.longhuan.approve.boot.service.FundFileService;
import com.longhuan.approve.boot.service.FundResourceFileService;

import com.longhuan.common.core.enums.PreFundResultEnum;
import com.longhuan.common.web.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 梅州客商前期服务实施
 *
 */
@Slf4j
@Service("fund_MZ_KE_SHANG")
@RequiredArgsConstructor
public class KeShangPreServiceImpl implements FundPreApiStrategy {
    private final KeShangService keShangService;
    private final FundApiDictMapper fundApiDictMapper;
    private final KeShangConfig keShangConfig;
    private final FundResourceFileService fundResourceFileService;
    private final FundApproveMapper fundApproveMapper;
    private final FundFileService fundFileService;
    private final PreFundInfoMapper preFundInfoMapper;

    /**
     * 开始事件
     *
     * @param fundPreBaseDTO 基金 pre base dto
     * @return {@link Boolean }
     */
    @Override
    public Boolean beginEvent(FundPreBaseDTO fundPreBaseDTO) {
        return null;
    }

    /**
     * 发送
     *
     * @param fundPreBaseDTO 基金 pre base dto
     */
    @Override
    public void send(FundPreBaseDTO fundPreBaseDTO) {

        //根据 preFundInfoMapper
        log.info("LanHaiServiceImpl.send fundPreBaseDTO:{}", JSONUtil.toJsonStr(fundPreBaseDTO));
        FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(new LambdaQueryWrapper<>(FundApiDictEntity.class)
                .eq(FundApiDictEntity::getLinkId, fundPreBaseDTO.getPreId())
                .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.PRE)
                .eq(FundApiDictEntity::getCode, FundApiDictEnum.PRE_ORI_REQUEST_APPLY_NO)
                .eq(FundApiDictEntity::getDeleteFlag, 0)
                .orderByDesc(FundApiDictEntity::getCreateTime)
                .last("limit 1")
        );
       /* if (ObjectUtil.isNotEmpty(fundApiDictEntity) && ObjectUtil.isNotEmpty(fundApiDictEntity.getValue())){
            //调用预授信终结接口
            boolean flag = lanHaiService.lanHaiCreditPreEnd(fundPreBaseDTO.getPreId());
            if (!flag){
                throw new BusinessException("预授信终结失败");
            }
        }*/

        //调用 预授信接口
        log.info("LanHaiPreServiceImpl.send begin fundPreBaseDTO:{}", fundPreBaseDTO);
        KeShangResponseHead result = keShangService.keShangCreditPreApply(fundPreBaseDTO);

        log.info("LanHaiPreServiceImpl.send end result:{}", JSONUtil.toJsonStr(result));
        if (!KeShangResponseHead.isSuccess(result)){
            log.info("LanHaiPreServiceImpl.send fail preId:{} result: {}", fundPreBaseDTO.getPreId(), JSONUtil.toJsonStr(result));
            throw new BusinessException(result.getRetMsg());
        }
    }

    /**
     * 结束 事件
     *
     * @param fundPreBaseDTO 基金 pre base dto
     * @return {@link Boolean }
     */
    @Override
    public Boolean endEvent(FundPreBaseDTO fundPreBaseDTO) {
        return true;
    }



}
