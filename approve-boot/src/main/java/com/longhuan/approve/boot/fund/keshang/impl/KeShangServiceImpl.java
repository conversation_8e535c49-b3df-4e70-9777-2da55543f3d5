package com.longhuan.approve.boot.fund.keshang.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.longhuan.approve.boot.config.KeShangConfig;
import com.longhuan.approve.boot.enums.FundApiDictEnum;
import com.longhuan.approve.boot.enums.FundApiDictTypeEnum;
import com.longhuan.approve.boot.enums.FundPaymentStatusEnum;
import com.longhuan.approve.boot.enums.InitStatusEnums;
import com.longhuan.approve.boot.enums.keshang.KeShangDictEnum;
import com.longhuan.approve.boot.enums.lanhai.LanHaiApiEnums;
import com.longhuan.approve.boot.feign.OrderFeign;
import com.longhuan.approve.boot.feign.ResourceFeign;
import com.longhuan.approve.boot.feign.UserFeign;
import com.longhuan.approve.boot.fund.finall.FinalFundInfoService;
import com.longhuan.approve.boot.fund.keshang.KeShangRepaymentService;
import com.longhuan.approve.boot.fund.keshang.KeShangService;
import com.longhuan.approve.boot.mapper.*;
import com.longhuan.approve.boot.pojo.dto.*;
import com.longhuan.approve.boot.pojo.dto.lanhai.LanHaiResult;
import com.longhuan.approve.boot.pojo.dto.lanhai.request.*;
import com.longhuan.approve.boot.pojo.dto.lanhai.response.*;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.KeShangRequestHead;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.KeShangResponseHead;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.request.*;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.response.*;
import com.longhuan.approve.boot.pojo.entity.*;
import com.longhuan.approve.boot.service.FundApiDictService;
import com.longhuan.approve.boot.service.FundBaseInfoService;
import com.longhuan.approve.boot.service.FundRepaymentInfoService;
import com.longhuan.approve.boot.utils.PareAddressUtils;
import com.longhuan.approve.boot.utils.changyin.AddressUtil;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.core.enums.dict.*;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.pojo.dto.AmountCalDTO;
import com.longhuan.order.pojo.dto.FinalApproveFundStatusDTO;
import com.longhuan.order.pojo.dto.OrderApproveFundPaymentStatusDTO;
import com.longhuan.order.pojo.dto.PreApproveFundStatusDTO;
import com.longhuan.resource.pojo.dto.FundResourceDTO;
import com.longhuan.resource.pojo.dto.FundResourceResultDTO;
import com.longhuan.user.pojo.vo.UserInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class KeShangServiceImpl implements KeShangService {

    private KeShangConfig keshangConfig;
    private final FundBaseInfoService fundBaseInfoService;
    private final OrderInfoMapper orderInfoMapper;
    private final UserFeign userFeign;
    private final FundApproveMapper fundApproveMapper;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final OrderCustomerInfoMapper orderCustomerInfoMapper;
    private final FinalFundInfoService finalFundInfoService;
    private final FundRepaymentInfoService fundRepaymentInfoService;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final FundApiDictMapper fundApiDictMapper;
    private final PreFundInfoMapper preFundInfoMapper;
    private final OrderFeign orderFeign;
    private final FundApiDictService fundApiDictService;
    private final ProductRongdanMapper productRongdanMapper;
    private final KeShangRepaymentService keShangRepaymentService;
    private final ResourceFeign resourceFeign;
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public KeShangResponseHead keShangCreditPreApply(FundPreBaseDTO fundPreBaseDTO) {
        log.info("KeShangServiceImpl.keShangCreditPreApply start, fundPreBaseDTO: {}", JSONUtil.toJsonStr(fundPreBaseDTO));
        //平台号(4位大写字母)+渠道号(4位大写字母)+请求时间(14位yyyyMMddHHMISS)+序号(8位)
        String loanAplyNO = keshangConfig.getPlatformId() + keshangConfig.getChannelId() + DateUtil.format(new Date(), "yyyyMMddHHmmss") +  RandomUtil.randomNumbers(8);

         //影像上传
        // todo 处理不同阶段的 资方文件
        KeShangResponseHead preFileList = imageUploadPre(fundPreBaseDTO, "pre");

        KeShangCreditPreApplyRequest request = buildCreditPreApplyInfo(fundPreBaseDTO,loanAplyNO);

        // todo 请求 梅州客商预申请接口
        KeShangResponseHead result = new KeShangResponseHead();

       //todo 解析数据  result.getInfoContent();
        KeShangCreditPreApplyResponse response = new KeShangCreditPreApplyResponse();

        if (!KeShangResponseHead.isSuccess(result)) {
            throw new BusinessException("预预授信失败:" + result.getRetMsg());
        }

        //todo 根据接口返回结果，处理预授信结果
        handelCreditPreApplyResult(fundPreBaseDTO.getPreId(),response);

        //todo
        fundApiDictMapper.insert(new FundApiDictEntity()
                .setLinkId(fundPreBaseDTO.getPreId())
                .setType(FundApiDictTypeEnum.PRE)
                .setValue(loanAplyNO)
                .setCode(FundApiDictEnum.PRE_ORI_REQUEST_SERIAL_NO)
        );
       /* fundApiDictMapper.insert(new FundApiDictEntity()
                .setLinkId(fundPreBaseDTO.getPreId())
                .setType(FundApiDictTypeEnum.PRE)
                .setValue("")
                .setCode(FundApiDictEnum.PRE_ORI_REQUEST_APPLY_NO)
        );*/
        return result;
    }

    @Override
    public KeShangResponseHead keShangAddedBusiness(FundFinalBaseDTO fundFinalBaseDTO) {
        String loanAplyNO = keshangConfig.getPlatformId() + keshangConfig.getChannelId() + DateUtil.format(new Date(), "yyyyMMddHHmmss") +  RandomUtil.randomNumbers(8);
        //构建请求参数
        KeShangAddedBussinessRequest request = buildAddedBusiness(fundFinalBaseDTO,loanAplyNO);

        // todo 请求 梅州客商 进件接口

        KeShangResponseHead result = new KeShangResponseHead();


        log.info("KeShangServiceImpl.keShangAddedBusiness end orderId:{} result:{}", fundFinalBaseDTO.getOrderId(), result);
        if (!KeShangResponseHead.isSuccess(result)) {
            log.info("KeShangServiceImpl.creditApply fail orderId:{} result:{}", fundFinalBaseDTO.getOrderId(), JSONUtil.toJsonStr(result));
            fundFinalBaseDTO.setCallFundRemark(result.getRetMsg());
            throw new BusinessException(result.getRetMsg());
        }
        log.info("KeShangServiceImpl.keShangAddedBusiness success orderId:{} applyId:{}", fundFinalBaseDTO.getOrderId(), loanAplyNO);
        boolean b = fundApiDictService.saveFundApiDictByLinkId(fundFinalBaseDTO.getOrderId(),
                FundApiDictTypeEnum.CREDIT,
                FundApiDictEnum.CREDIT_REQUEST_SERIAL_NO,
                loanAplyNO);//todo result.getData().getBody().getApplyId()==》lanhai
        if (!b) {
            log.info("KeShangServiceImpl.keShangAddedBusiness save fundApiDict fail orderId:{} requestSerialNo:{}", fundFinalBaseDTO.getOrderId(), loanAplyNO);
        }
        // 更新授信流水号
        finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                .set(FinalFundInfoEntity::getCreditReqNo, loanAplyNO) //todo
                .eq(FinalFundInfoEntity::getOrderId, fundFinalBaseDTO.getOrderId())
                .eq(FinalFundInfoEntity::getFundId, fundFinalBaseDTO.getFundId())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );
        return result;
    }

    @Override
    public KeShangAddBusinessCancelRequest addedBusinessCancel(FundFinalBaseDTO fundFinalBaseDTO) {
        //todo 请求参数
        KeShangAddBusinessCancelRequest request = new KeShangAddBusinessCancelRequest();

        return null;
    }

    @Override
    public KeShangAddBusinessChangeRequest addedBusinessChange(FundFinalBaseDTO fundFinalBaseDTO) {
        //todo 请求参数
        KeShangAddBusinessChangeRequest request = new KeShangAddBusinessChangeRequest();

        KeShangMsgCodeResponse response = new KeShangMsgCodeResponse();
        return null;
    }

    @Override
    public KeShangResponseHead loanApplyByOrderId(Integer orderId) {

        log.info("KeShangServiceImpl.loanApplyByOrderId orderId:{}", orderId);
        FundFinalBaseDTO fundFinalBaseDTO = fundBaseInfoService.getFundFinalBaseApplyInfo(orderId);
        //todo 是否需要重新生成
        // 平台号(4位大写字母)+渠道号(4位大写字母)+请求时间(14位yyyyMMddHHMISS)+序号(8位)
        String loanAplyNO = keshangConfig.getPlatformId() + keshangConfig.getChannelId() +
                DateUtil.format(new Date(), "yyyyMMddHHmmss") + RandomUtil.randomNumbers(8);
        KeShangLoanRequest request = buildLoanApply(fundFinalBaseDTO,loanAplyNO);
        log.info("KeShangServiceImpl.loanApplyByOrderId :{} keshangLoanApplyRequest:{}", orderId, JSONUtil.toJsonStr(request));

        // todo 请求接口
        KeShangResponseHead result = new KeShangResponseHead();

        log.info("KeShangServiceImpl.loanApplyByOrderId orderId:{} result:{}", orderId, JSONUtil.toJsonStr(result));
        if (!KeShangResponseHead.isSuccess(result)) {
            log.info("KeShangServiceImpl.loanApplyByOrderId fail orderId:{} result:{}", orderId, JSONUtil.toJsonStr(result));
            fundFinalBaseDTO.setCallFundRemark(result.getRetMsg());
            throw new BusinessException(result.getRetMsg());
        }
        //更新放款申请编号
        finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                .set(FinalFundInfoEntity::getLoanBillNo, loanAplyNO)//todo  result.getData().getBody().getApplyId()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.MZ_KE_SHANG.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );
        //保存请求流水号
        fundApiDictService.saveFundApiDictByLinkId(orderId, FundApiDictTypeEnum.LOAN, FundApiDictEnum.LOAN_REQUEST_SERIAL_NO, loanAplyNO);
        log.info("KeShangServiceImpl.loanApply orderId:{} loanBillNo:{}", orderId, loanAplyNO);//todo result.getData().getBody().getApplyId()
        return result;
    }

    @Override
    public KeShangResponseHead applyLoanResult( Integer orderId) {
        log.info("KeShangServiceImpl.applyLoanResult orderId:{}", orderId);
        FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(new LambdaQueryWrapper<>(FundApiDictEntity.class)
                .eq(FundApiDictEntity::getLinkId, orderId)
                .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.LOAN)
                .eq(FundApiDictEntity::getCode, FundApiDictEnum.LOAN_REQUEST_SERIAL_NO)
                .orderByDesc(FundApiDictEntity::getCreateTime)
                .last("limit 1")
        );
        log.info("KeShangServiceImpl.applyLoanResult fundApiDictEntity:{}", JSONUtil.toJsonStr(fundApiDictEntity));
        if (ObjectUtil.isNull(fundApiDictEntity)) {
            log.info("KeShangServiceImpl.applyLoanResult fail orderId:{} not found loan request serial no", orderId);
            throw new BusinessException("未获取到放款申请相关信息");
        }
        String loanReqNo = fundApiDictEntity.getValue();

        KeShangLoanAplyNoRequest request = new KeShangLoanAplyNoRequest();
        request.setLoanAplyNO(loanReqNo);

        //todo 请求接口 040200 查询银行贷款申请和放款申请处理结果
        KeShangResponseHead result = new KeShangResponseHead();

        log.info("KeShangServiceImpl.applyLoanResult orderId:{} result:{}", orderId, JSONUtil.toJsonStr(result));
        if (!KeShangResponseHead.isSuccess(result)) {
            log.info("KeShangServiceImpl.applyLoanResult fail orderId:{} result: {}", orderId, JSONUtil.toJsonStr(result));
            throw new BusinessException(result.getRetMsg());
        }
        //处理放款结果
        handlerLoanApplyQuery(orderId, result);
        return result;
    }

    @Override
    public KeShangApplyLoanResponse queryCustomerApplyLoanInfo(FundFinalBaseDTO fundFinalBaseDTO) {
        //todo 请求参数
        KeShangApplyLoanRequest request = new KeShangApplyLoanRequest();
        return null;
    }

  /*  @Override
    public KeShangRepaymentResultResponse queryRepaymentResult(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangRepaymentResultRequest request = new KeShangRepaymentResultRequest();
        // todo 请求客商接口
        KeShangRepaymentResultResponse response = new KeShangRepaymentResultResponse();
        return null;
    }

    @Override
    public Boolean repaymenPlanQuery(Integer orderId) {

        KeShangResponseHead result = null;
        try {
            FinalFundInfoEntity finalFundInfoEntity = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.LAN_HAI);
            if (ObjUtil.isNull(finalFundInfoEntity)) {
                throw new BusinessException("资方申请信息不存在");
            }
            KeShangLoanAplyNoRequest request = new KeShangLoanAplyNoRequest();
            //todo 请求参数 LoanAplyNO ==>loanNo ?
            request.setLoanAplyNO(finalFundInfoEntity.getLoanNo());

            log.info("KeShangServiceImpl.repaymenPlanQuery request:{}", JSONUtil.toJsonStr(request));

            // todo 请求客商接口

            log.info("KeShangServiceImpl.repaymenPlanQuery result:{}", JSONUtil.toJsonStr(result));
            if (!KeShangResponseHead.isSuccess(result)) {
                throw new BusinessException("还款计划查询失败" + result.getRetMsg());
            }
            //todo 解析数据  result.getInfoContent();
            // 处理数据
            KeShangRepaymentPlanResponse response = new KeShangRepaymentPlanResponse();
            List<KeShangRepaymentPlanResponse.RepaymentPlanItem> repayPlanList = response.getRepaymentPlanList();
            if (CollUtil.isEmpty(repayPlanList)) {
                throw new BusinessException("还款计划查询为空");
            }
            // todo 批量更新还款计划
            //batchUpdateRepaymentPlans(orderId, repayPlanList);

        } catch (Exception e) {
            log.info("KeShangServiceImpl.queryRepayPlan orderId:{} error:{}", orderId, e.getMessage(), e);
            throw new BusinessException("还款计划更新失败");
        }
        return true;
    }*/

    @Override
    public KeShangQueryResponse proofIssueQuery(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangProofIssueQueryRequest request = new KeShangProofIssueQueryRequest();
        // todo 请求客商接口
        KeShangQueryResponse response = new KeShangQueryResponse();
        return null;
    }

    @Override
    public KeShangQueryResponse fileSignQuery(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangFileSignRequest request = new KeShangFileSignRequest();
        return null;
    }

    @Override
    public KeShangPlatformFileNoticeRequest platformFileNotice(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangPlatformFileNoticeRequest request = new KeShangPlatformFileNoticeRequest();
        // todo 返回结果见“报文头约定-响应头”
        return null;
    }

    @Override
    public KeShangResponseHead addBusinessResultNotice(KeShangRequestHead callback) {
        log.info("KeShangServiceImpl.addBusinessResultNotice callback:{}", JSONUtil.toJsonStr(callback));

        // todo 解析数据
        callback.getInfoContent();
        // todo 解析数据
        KeshangAddBusinessResultRequest callBackInfo = new KeshangAddBusinessResultRequest();
        String loanAplyNO = callBackInfo.getLoanAplyNO();

        //String applyId = callBackInfo.getApplyId();
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getCreditReqNo, loanAplyNO) //todo ==?applyId
                .eq(FinalFundInfoEntity::getFundId, FundEnum.MZ_KE_SHANG.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );

        Integer orderId = finalFundInfo.getOrderId();
        Integer fundId = finalFundInfo.getFundId();
        PreFundResultEnum preFundResultEnum = switch (callBackInfo.getAdtngCd()) {
            case "1" -> PreFundResultEnum.PASS;
            case "0" -> PreFundResultEnum.REJECT;
            default -> PreFundResultEnum.WAIT;
        };
        BigDecimal loanAmount = ObjUtil.defaultIfNull(new BigDecimal(callBackInfo.getCrLmt()), BigDecimal.ZERO);

        if (ObjUtil.equals(PreFundResultEnum.PASS, preFundResultEnum)) {
            AmountCalDTO amountCalDTO = new AmountCalDTO();
            amountCalDTO.setAmount(loanAmount);
            amountCalDTO.setCalType(OrderAmountEnum.FUND_PRE_AMOUNT);
            amountCalDTO.setOrderId(orderId);
            // 更新金额表信息
            log.info("LanHaiServiceImpl.handleCreditApplyCallback updated for orderId: {}", orderId);
            log.info("LanHaiServiceImpl.handleCreditApplyCallback AmountCalDTO created: {}", JSONUtil.toJsonStr(amountCalDTO));
            orderFeign.calAmount(amountCalDTO);
        }
        /*if (ObjUtil.isNotNull(callBackInfo.getExecuteRate())){
            finalFundInfo.setExecuteRate(callBackInfo.getExecuteRate().divide(new BigDecimal(100)));
            finalFundInfoMapper.updateById(finalFundInfo);
        }*/
        // 更新订单资方状态
        FinalApproveFundStatusDTO fundStatusDTO = new FinalApproveFundStatusDTO()
                .setOrderId(orderId)
                .setFundId(fundId)
                .setStatus(preFundResultEnum)
                .setFundCreditAmt(loanAmount)
                .setFundRemark(String.join(" ", callBackInfo.getAdtngCd(), callBackInfo.getAdtngMsg()))
               // .setFundCreditTime(callBackInfo.getStartTime()) //todo
                .setCreditReqNo(loanAplyNO);
               // .setCreditNo(callBackInfo.getCreditLimitId()) //todo
               // .setFundUserId(callBackInfo.getUserId()); //todo

        log.info("LanHaiServiceImpl.handleCreditApplyCallback FinalApproveFundStatusDTO created: {}", JSONUtil.toJsonStr(fundStatusDTO));
        orderFeign.updateFundFinalStatus(fundStatusDTO);

        //todo 处理回调数据

        // todo 返回
        KeShangResponseHead response = new KeShangResponseHead();
        return response;
    }

    @Override
    public KeShangResponseHead loanResultNotice( KeShangLoanResultRequest callback) {
        // todo 请求参数
        KeShangLoanResultRequest resultBody = new KeShangLoanResultRequest();

        String loanAplyNO = resultBody.getLoanAplyNO();
        FundNodeStatusEnum nextNodeStatus = null;
        FundPaymentStatusEnum fundPaymentStatus = null;
        String failReason = null;
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(
                Wrappers.<FinalFundInfoEntity>lambdaQuery()
                        .eq(FinalFundInfoEntity::getLoanBillNo, loanAplyNO) //todo ==?resultBody.getApplyId()
                        .eq(FinalFundInfoEntity::getStatus, InitStatusEnums.SUCCESS)
                        .eq(FinalFundInfoEntity::getFundResult, PreFundResultEnum.PASS)
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );
        if (ObjectUtil.isNull(finalFundInfo)) {
            log.error("KeShangServiceImpl.handlePayCallback on payment ： {}", "订单不存在");
            throw new BusinessException("订单不存在");
        }
        Integer orderId = finalFundInfo.getOrderId();
        log.info("KeShangServiceImpl.handlePayCallback orderId:{} applyId:{} result:{}", orderId, resultBody.getLoanAplyNO(), JSONUtil.toJsonStr(resultBody));
        String handlerStatus = resultBody.getLndngRslt();
        switch (handlerStatus) {
            case "1" -> {
                log.info("KeShangServiceImpl.handlePayCallback orderId:{} applyId:{} 放款成功", orderId, loanAplyNO);
                nextNodeStatus = FundNodeStatusEnum.FUND_PAYMENT_SUCCESS;
                fundPaymentStatus = FundPaymentStatusEnum.PASS;
            }
          /*  case "0" -> {
                log.info("KeShangServiceImpl.handlePayCallback orderId:{} applyId:{} 放款处理中", orderId, resultBody.getApplyId());
                nextNodeStatus = FundNodeStatusEnum.LOAN_APPLY_QUERY;
                fundPaymentStatus = FundPaymentStatusEnum.WAIT;
            }*/
            case "0" -> {
                log.info("KeShangServiceImpl.handlePayCallback orderId:{} applyId:{} 放款失败", orderId, loanAplyNO);
                nextNodeStatus = FundNodeStatusEnum.FUND_PAYMENT_FAIL;
                fundPaymentStatus = FundPaymentStatusEnum.FAIL;
            }
            default -> {
                log.info("KeShangServiceImpl.handlePayCallback orderId:{} applyId:{} 无此交易（非终态）", orderId, loanAplyNO);
                nextNodeStatus = FundNodeStatusEnum.LOAN_APPLY_QUERY;
                fundPaymentStatus = FundPaymentStatusEnum.WAIT;
            }
        }

        BigDecimal loanAmount = ObjUtil.isNotEmpty(resultBody.getLndngActlAmt()) ? new BigDecimal(resultBody.getLndngActlAmt()): BigDecimal.ZERO;

        String loanPayTimeStr = resultBody.getLndngDt();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDateTime loanPayTime = LocalDateTime.parse(loanPayTimeStr, formatter);

        failReason = resultBody.getLndngFailRsn();

        updateOrderPaymentStatus(orderId, FundEnum.MZ_KE_SHANG.getValue(), loanAmount,
                loanPayTime,
                failReason, fundPaymentStatus, loanAplyNO); //todo
        finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                .set(FinalFundInfoEntity::getNodeStatus, nextNodeStatus)
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.MZ_KE_SHANG.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );

        if (ObjUtil.equals(fundPaymentStatus, FundPaymentStatusEnum.PASS)) {
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
            if (ObjUtil.isNull(orderInfo)) {
                throw new BusinessException("订单不存在");
            }
            ProductRongdanEntity productRongdanInfo = productRongdanMapper.selectById(orderInfo.getRongdanId());
            if (ObjUtil.isNull(productRongdanInfo)) {
                throw new BusinessException("融担公司不存在");
            }
            try {
                keShangRepaymentService.repaymenPlanQuery(orderId);
            } catch (Exception e) {
                log.error("KeShangServiceImpl.handlePayCallback queryRepayPlan orderId:{} error: {}", orderId, e.getMessage());
            }
            /*SwitchVO newLendersPerformance = switchUtils.getSwitchInfo("ZUN_HAO_APPLY");
            if (ObjUtil.isNotNull(newLendersPerformance) && newLendersPerformance.getSwitchFlag() == 1) {
                try {
                    if (StrUtil.equals(productRongdanInfo.getMiddleCode(), ProductRongDanEnum.ZUN_HAO.getCode())) {
                        zunHaoService.projectApply(orderId);
                        zunHaoService.loanApply(orderId);
                    }

                } catch (Exception e) {
                    log.error("LanHaiServiceImpl.handlerLoanApplyQuery projectApply orderId:{} error: {}", orderId, e.getMessage(), e);
                }
            } else {

            }*/
        }

        // todo 返回结果见“报文头约定-响应头”
        KeShangResponseHead keShangResponseHead = new KeShangResponseHead();

        return keShangResponseHead;
    }

    @Override
    public KeShangVisaInterviewResultRequest visaInterviewResultNotice(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangVisaInterviewResultRequest request = new KeShangVisaInterviewResultRequest();
        // todo 返回结果见“报文头约定-响应头”
        return null;
    }

    @Override
    public KeShangFileSignResultRequest fileSignResultNotice(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangFileSignResultRequest request = new KeShangFileSignResultRequest();
        // todo 返回结果见“报文头约定-响应头”
        return null;
    }

   /* @Override
    public KeShangRepaymentPlanResponse.RepaymentPlanItem repaymentPlanInfo(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangRepaymentPlanResponse.RepaymentPlanItem repaymentPlanItem = new KeShangRepaymentPlanResponse.RepaymentPlanItem();
        return null;
    }

    @Override
    public KeShangRepaymentDetailRequest repaymentDetailInfo(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangRepaymentDetailRequest request = new KeShangRepaymentDetailRequest();

        return null;
    }*/

    @Override
    public KeShangBankCardAuthRequest bankCardAuth(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangBankCardAuthRequest request = new KeShangBankCardAuthRequest();
        // todo 请求客商接口

        KeShangBankCardAuthResponse response = new KeShangBankCardAuthResponse();
        // 获获取金额

        return null;
    }

    @Override
    public KeShangGetSignatureLinkRequest getSignatureLink(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangGetSignatureLinkRequest request = new KeShangGetSignatureLinkRequest();
        // todo 请求客商接口
        KeShangGetSignatureLinkResponse response = new KeShangGetSignatureLinkResponse();
        return null;
    }

    @Override
    public KeShangAgreeSignQueryResponse agreeSignQuery(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangAgreeSignQueryRequest request = new KeShangAgreeSignQueryRequest();
        // todo 请求客商接口
        KeShangAgreeSignQueryResponse response = new KeShangAgreeSignQueryResponse();
        return null;
    }

    @Override
    public KeShangAgreeSignSendCodeRequest agreeSignSendCode(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangAgreeSignSendCodeRequest request = new KeShangAgreeSignSendCodeRequest();
        // todo 请求客商接口
        KeShangAgreeSignSendCodeResponse response = new KeShangAgreeSignSendCodeResponse();
        return null;
    }

    @Override
    public KeShangAgreeSignRequest agreeSign(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangAgreeSignRequest request = new KeShangAgreeSignRequest();
        // todo 请求客商接口
        KeShangAgreeSignResponse response = new KeShangAgreeSignResponse();
        return null;
    }

    @Override
    public KeShangBindCardInfoResponse bindCardInfo(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangBindCardQueryRequest request = new KeShangBindCardQueryRequest();
        // todo 请求客商接口
        KeShangBindCardInfoResponse response = new KeShangBindCardInfoResponse();
        return null;
    }

    @Override
    public KeShangProofIssueRequest proofIssue(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangProofIssueRequest request = new KeShangProofIssueRequest();
        // todo 请求客商接口
        KeShangProofIssueResponse response = new KeShangProofIssueResponse();
        return null;
    }

    @Override
    public void creditApplyQueryByOrderId(Integer orderId) {
        KeShangLoanAplyNoRequest request = creditApplyLoanResultQueryBuild(orderId,FundApiDictTypeEnum.CREDIT,FundApiDictEnum.CREDIT_REQUEST_SERIAL_NO);
        //todo 构建 requestHead
        KeShangRequestHead requestHead = new KeShangRequestHead();
        // todo 请求接口 040200 申贷放款结果查询 （查询银行贷款申请和放款申请处理结果）

        KeShangResponseHead result = new KeShangResponseHead();

        log.info("KeShangServiceImpl.creditApplyQueryByOrderId orderId:{} result:{}", orderId, JSONUtil.toJsonStr(result));

        if (!KeShangResponseHead.isSuccess(result)) {
            log.info("KeShangServiceImpl.creditApplyQueryByOrderId result is fail orderId:{}", orderId);
            throw new BusinessException("获取进件结果失败");
        }
        // 处理授信结果
        handleCreditApplyQuery(orderId, result,request.getLoanAplyNO());
    }

    @Override
    public void preApproveQueryV2ByPreId(Integer preId) {
        //todo ,预授信查询
        handelCreditPreApplyResult(preId,null);
    }

    private KeShangLoanAplyNoRequest creditApplyLoanResultQueryBuild(Integer orderId, FundApiDictTypeEnum fundApiDictTypeEnum, FundApiDictEnum fundApiDictEnum) {
        KeShangLoanAplyNoRequest request = new KeShangLoanAplyNoRequest();
        log.info("LanHaiServiceImpl.creditApplyQueryBuild start, orderId: {}", orderId);

        FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(new LambdaQueryWrapper<FundApiDictEntity>()
                        .eq(FundApiDictEntity::getType, fundApiDictTypeEnum)
                        .eq(FundApiDictEntity::getCode, fundApiDictEnum)
                        .eq(FundApiDictEntity::getLinkId, orderId)
                        .eq(FundApiDictEntity::getDeleteFlag, 0)
                        .orderByDesc(FundApiDictEntity::getCreateTime)
                , false
        );
        if (ObjUtil.isNull(fundApiDictEntity)) {
            log.info("KeShangServiceImpl.creditApplyQueryBuild fundApiDictEntity is null orderId:{}", orderId);
            throw new BusinessException("获取授信结果失败");
        }
        request.setLoanAplyNO(fundApiDictEntity.getValue());
        log.info("KeShangServiceImpl.creditApplyQueryBuild creditApplyQueryRequest:{}", JSONUtil.toJsonStr(request));

        return request;
    }

    /**
     * 进件结果 处理
     */
    public void handleCreditApplyQuery(Integer orderId, KeShangResponseHead result,String loanAplyNO ) {
        log.info("KeShangServiceImpl.handleCreditApplyQuery result: {}", JSONUtil.toJsonStr(result));
        if (!KeShangResponseHead.isSuccess(result)) {
            throw new BusinessException("授信结果处理失败");
        }
        //todo 解析结果
        KeShangTxnStatusResponse txnStatusResponse = new KeShangTxnStatusResponse();
       /* //根据授信号查询
        String applyId = resultBody.getApplyId();
        if (ObjUtil.isNull(orderId) && StrUtil.isBlank(applyId)) {
            log.info("LanHaiServiceImpl.handleCreditApplyQuery orderId is null result:{}", JSONUtil.toJsonStr(result));
            throw new BusinessException("授信结果处理失败,未查询到相关信息");
        }*/
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(StrUtil.isNotBlank(loanAplyNO), FinalFundInfoEntity::getCreditReqNo, loanAplyNO)
                .eq(ObjUtil.isNotNull(orderId), FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(finalFundInfo)) {
            log.info("LanHaiServiceImpl.handleCreditApplyQuery finalFundInfo is null applyId:{}", loanAplyNO);
            throw new BusinessException("授信结果处理失败,未查询到相关信息");
        }
        //todo executeRate
       /* if (ObjUtil.isNotNull(resultBody.getExecuteRate())){
            finalFundInfo.setExecuteRate(resultBody.getExecuteRate().divide(BigDecimal.valueOf(100)));
            finalFundInfoMapper.updateById(finalFundInfo);
        }*/
        Integer fundId = finalFundInfo.getFundId();
        PreFundResultEnum preFundResultEnum = switch (txnStatusResponse.getTxnSt()) {
            case "13" -> PreFundResultEnum.PASS;
            case "12" -> PreFundResultEnum.REJECT;
            case "11" -> PreFundResultEnum.WAIT;
            //todo 其他状态
            default -> PreFundResultEnum.WAIT;
        };
        BigDecimal loanAmount = ObjUtil.defaultIfNull(new BigDecimal(txnStatusResponse.getCrLmt()), BigDecimal.ZERO);

        if (ObjUtil.equals(PreFundResultEnum.PASS, preFundResultEnum)) {
            AmountCalDTO amountCalDTO = new AmountCalDTO();
            amountCalDTO.setAmount(loanAmount);
            amountCalDTO.setCalType(OrderAmountEnum.FUND_PRE_AMOUNT);
            amountCalDTO.setOrderId(orderId);
            // 更新金额表信息
            log.info("LanHaiServiceImpl.handleCreditApplyQuery updated for orderId: {}", orderId);
            log.info("LanHaiServiceImpl.handleCreditApplyQuery AmountCalDTO created: {}", JSONUtil.toJsonStr(amountCalDTO));
            orderFeign.calAmount(amountCalDTO);
        }

        // 更新订单资方状态
        FinalApproveFundStatusDTO fundStatusDTO = new FinalApproveFundStatusDTO()
                .setOrderId(orderId)
                .setFundId(fundId)
                .setStatus(preFundResultEnum)
                .setFundCreditAmt(loanAmount)
                .setFundRemark(String.join(" ", txnStatusResponse.getTxnFailRsn(), result.getRetMsg()))
                .setFundCreditTime(null) //todo
                .setCreditReqNo(loanAplyNO)//todo
                .setCreditNo("")//todo
                .setFundUserId("");//todo

        log.info("LanHaiServiceImpl.handleCreditApplyQuery FinalApproveFundStatusDTO created: {}", JSONUtil.toJsonStr(fundStatusDTO));
        orderFeign.updateFundFinalStatus(fundStatusDTO);

    }

    /**
     * 更新订单放款结果
     */
    private void handlerLoanApplyQuery(Integer orderId, KeShangResponseHead result) {
        log.info("KeShangServiceImpl.handlerLoanApplyQuery result: {}", JSONUtil.toJsonStr(result));
        if (!KeShangResponseHead.isSuccess(result)) {
            log.info("KeShangServiceImpl.handlerLoanApplyQuery Failed: {}", JSONUtil.toJsonStr(result));
            throw new BusinessException("放款结果处理失败");
        }

        // todo 处理    result.getInfoContent(); ==> KeShangTxnStatusResponse
        KeShangTxnStatusResponse response = new KeShangTxnStatusResponse();

        FundNodeStatusEnum nextNodeStatus = null;
        FundPaymentStatusEnum fundPaymentStatus = null;
        String failReason = null;
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(
                Wrappers.<FinalFundInfoEntity>lambdaQuery()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getStatus, InitStatusEnums.SUCCESS)
                        .eq(FinalFundInfoEntity::getFundResult, PreFundResultEnum.PASS)
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );
        if (ObjectUtil.isNull(finalFundInfo)) {
            log.error("KeShangServiceImpl.handlerLoanApplyQuery on payment ： {}", "订单不存在");
            throw new BusinessException("订单不存在");
        }
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isNull(orderInfo)) {
            throw new BusinessException("订单不存在");
        }
        ProductRongdanEntity productRongdanInfo = productRongdanMapper.selectById(orderInfo.getRongdanId());
        if (ObjUtil.isNull(productRongdanInfo)) {
            throw new BusinessException("融担公司不存在");
        }
        log.info("KeShangServiceImpl.handlerLoanApplyQuery orderId:{} result:{}", orderId, JSONUtil.toJsonStr(response));
        String handlerStatus = response.getTxnSt();
        switch (handlerStatus) {
            case "33" -> {
                log.info("KeShangServiceImpl.handlerLoanApplyQuery orderId:{} 放款成功", orderId);
                nextNodeStatus = FundNodeStatusEnum.FUND_PAYMENT_SUCCESS;
                fundPaymentStatus = FundPaymentStatusEnum.PASS;
            }
            case "31" -> {
                log.info("KeShangServiceImpl.handlerLoanApplyQuery orderId:{} 放款处理中", orderId);
                nextNodeStatus = FundNodeStatusEnum.LOAN_APPLY_QUERY;
                fundPaymentStatus = FundPaymentStatusEnum.WAIT;
            }
            case "32" -> {
                log.info("KeShangServiceImpl.handlerLoanApplyQuery orderId:{} 放款失败", orderId);
                nextNodeStatus = FundNodeStatusEnum.FUND_PAYMENT_FAIL;
                fundPaymentStatus = FundPaymentStatusEnum.FAIL;
            }
            //todo 其他状态怎么处理
            default -> {
                log.info("KeShangServiceImpl.handlerLoanApplyQuery orderId:{} 无此交易（非终态）", orderId);
                nextNodeStatus = FundNodeStatusEnum.LOAN_APPLY_QUERY;
                fundPaymentStatus = FundPaymentStatusEnum.WAIT;
            }
        }

        BigDecimal loanAmount = null; //todo ObjUtil.defaultIfNull(response.getCrLmt(), BigDecimal.ZERO);
        LocalDateTime loanPayTime = null; //todo  resultBody.getLoanPayTime();
        failReason = result.getRetMsg();

        updateOrderPaymentStatus(orderId, FundEnum.LAN_HAI.getValue(), loanAmount,
                loanPayTime,
                failReason, fundPaymentStatus, null);//todo resultBody.getLoanInvoiceId()
        finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                .set(FinalFundInfoEntity::getNodeStatus, nextNodeStatus)
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );
        if (ObjUtil.equals(fundPaymentStatus, FundPaymentStatusEnum.PASS)) {
            try {
                keShangRepaymentService.repaymenPlanQuery(orderId);
            } catch (Exception e) {
                log.error("LanHaiServiceImpl.handlerLoanApplyQuery queryRepayPlan orderId:{} error: {}", orderId, e.getMessage());
            }
           /* SwitchVO newLendersPerformance = switchUtils.getSwitchInfo("ZUN_HAO_APPLY");
            if (ObjUtil.isNotNull(newLendersPerformance) && newLendersPerformance.getSwitchFlag() == 1) {
                try {
                    if (StrUtil.equals(productRongdanInfo.getMiddleCode(), ProductRongDanEnum.ZUN_HAO.getCode())) {
                        zunHaoService.projectApply(orderId);
                        zunHaoService.loanApply(orderId);
                    }
                } catch (Exception e) {
                    log.error("LanHaiServiceImpl.handlerLoanApplyQuery projectApply orderId:{} error: {}", orderId, e.getMessage(), e);
                }
            } else {

            }*/

        }


       /* if (ObjectUtil.equals(fundPaymentStatus, FundPaymentStatusEnum.PASS)) {
            SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.SYNC_LAN_HAI_TO_ShuZiHua_PAY_AFTER);
            log.info("LanHaiServiceImpl.handlerLoanApplyQuery switchInfo: {}", JSONUtil.toJsonStr(switchInfo));
            //放款成功同步数字化
            if (ObjectUtil.isNotNull(switchInfo) && ObjectUtil.equals(switchInfo.getSwitchFlag(), 1)) {
                try {
                    SyncShuZiHuaDTO syncShuziHuaDTO = new SyncShuZiHuaDTO();
                    syncShuziHuaDTO.setOrderIdList(Lists.newArrayList(orderId));
                    syncShuZiHuaService.doProcess(syncShuziHuaDTO);
                } catch (Exception e) {
                    log.error("LanHaiServiceImpl.handlerLoanApplyQuery syncLoanApply orderId:{} error: {}", orderId, e.getMessage());
                }
            }

        }*/

    }

    private void updateOrderPaymentStatus(Integer orderId, Integer fundId, BigDecimal loanAmt,
                                          LocalDateTime payTime, String remark, FundPaymentStatusEnum fundPaymentStatusEnum, String loanNo) {

        log.info("KeShangServiceImpl.updateOrderPaymentStatus begin orderId:{} fundId:{}", orderId, fundId);
        try {
            String failReason = remark + " 放款金额->" + loanAmt;

            // 设置要更新的字段
            finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .set(FinalFundInfoEntity::getRemark, failReason)
                    .set(FinalFundInfoEntity::getPaymentStatus, fundPaymentStatusEnum.getValue())
                    .set(FinalFundInfoEntity::getPaymentAmount, loanAmt)
                    .set(FinalFundInfoEntity::getLoanNo, loanNo)
                    .set(ObjUtil.isNotNull(payTime), FinalFundInfoEntity::getPaymentTime, payTime)
                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                    .eq(FinalFundInfoEntity::getFundId, fundId));

            //放款单号
            OrderFundPaymentEnum orderFundPaymentEnum = OrderFundPaymentEnum.WAIT;
            if (FundPaymentStatusEnum.PASS.equals(fundPaymentStatusEnum)) {
                orderFundPaymentEnum = OrderFundPaymentEnum.PASS;
            } else if (FundPaymentStatusEnum.FAIL.equals(fundPaymentStatusEnum)) {
                orderFundPaymentEnum = OrderFundPaymentEnum.FAIL;
            }

            OrderApproveFundPaymentStatusDTO fundPaymentStatusDTO = new OrderApproveFundPaymentStatusDTO()
                    .setOrderId(orderId)
                    .setFundId(fundId)
                    .setLoanApplyNo(null)
                    .setLoanAmt(loanAmt)
                    .setLoanPayTime(payTime)
                    .setFailReason(remark)
                    .setStatus(orderFundPaymentEnum);

            orderFeign.updateFundPaymentStatus(fundPaymentStatusDTO);
            log.info("KeShangServiceImpl.updateOrderPaymentStatus end orderId:{} fundId:{}", orderId, fundId);
        } catch (Exception e) {
            log.error("KeShangServiceImpl.updateOrderPaymentStatus orderId:{} error e:{}", orderId, e.getMessage(), e);
            throw new BusinessException("更新订单放款状态异常");
        }
    }

    /**
     * 处理预授信申请结果
     * @param preId
     * @param response
     */
    private void handelCreditPreApplyResult(Integer preId, KeShangCreditPreApplyResponse response) {
        PreFundInfoEntity preFundInfo = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                .eq(PreFundInfoEntity::getPreId, preId)
                .eq(PreFundInfoEntity::getFundId, FundEnum.MZ_KE_SHANG.getValue())
                .eq(PreFundInfoEntity::getDeleteFlag, 0)
        );
        if (ObjUtil.isNull(preFundInfo)) {
            throw new BusinessException("预授信申请信息不存在");
        }
        String message = "";
        String handlerStatus = response.getRskRslt();
        PreFundResultEnum updatePreStatus = PreFundResultEnum.WAIT;
        if (StrUtil.equals(handlerStatus, "00")) {
            updatePreStatus = PreFundResultEnum.PASS;
        }
        if (StrUtil.equals(handlerStatus, "01")) {
            updatePreStatus = PreFundResultEnum.REJECT;
        }
        if (StrUtil.equals(handlerStatus, "02")) {
            updatePreStatus = PreFundResultEnum.WAIT;
        }
       /* log.info("LanHaiServiceImpl.preApproveQueryV2ByPreId updatePreStatus:{}", updatePreStatus);
        if (StrUtil.isNotEmpty(body.getStartTime()) && StrUtil.isNotEmpty(body.getEndTime())) {
            LocalDateTime now = LocalDateTime.now();
            // 定义日期格式（根据你的字符串格式调整）
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            DateTime start = DateUtil.parse(body.getStartTime(), formatter);
            DateTime end = DateUtil.parse(body.getEndTime(), formatter);
            log.info("LanHaiServiceImpl.preApproveQueryV2ByPreId start:{} end:{}, now:{}", start, end, now);
            if (DateUtil.date(date).isBefore(end)) {
                preFundInfoMapper.update(new LambdaUpdateWrapper<PreFundInfoEntity>()
                        .eq(PreFundInfoEntity::getPreId, preId)
                        .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .set(PreFundInfoEntity::getExecuteRate, body.getExecuteRate().divide(BigDecimal.valueOf(100)))
                        .set(PreFundInfoEntity::getCreditContractEnd, end)
                        .set(PreFundInfoEntity::getUpdateTime, LocalDateTime.now())
                );
            } else {
                updatePreStatus = PreFundResultEnum.REJECT;
            }
        }*/
        if (ObjUtil.equals(PreFundResultEnum.PASS, updatePreStatus)) {
            message = "授信通过";
        } else if (ObjUtil.equals(PreFundResultEnum.REJECT, updatePreStatus)) {
            message = "授信拒绝";
        }else if (ObjUtil.equals(PreFundResultEnum.WAIT, updatePreStatus)) {
            //todo  02-风控审核中，可再次调用进行回查
            message = "授信审批中";
        }
        if (StrUtil.isNotBlank(response.getRejectRs())) {
            //码值映射对照数据字典RejectRs
            List<String> rejectRsList = Arrays.asList(response.getRejectRs().split("|"));
            for (String rejectRs : rejectRsList){
                KeShangDictEnum.RiskCheckTypeEnum  riskCheckTypeEnum = KeShangDictEnum.RiskCheckTypeEnum.fromCode(rejectRs);
                message += " " + riskCheckTypeEnum.getDescription();
            }
        }
        BigDecimal creditAmount = BigDecimal.ZERO;

        if (preFundInfo.getCreditAmount() != null) {
            creditAmount = preFundInfo.getCreditAmount();
        }
        PreApproveFundStatusDTO preApproveFundStatusDTO = new PreApproveFundStatusDTO()
                .setPreId(preId)
                .setFundId(FundEnum.MZ_KE_SHANG.getValue())
                .setStatus(updatePreStatus)
                .setCreditAmt(creditAmount)
                .setFailReason(message);
        log.info("KeShangServiceImpl.handelCreditPreApplyResult preApproveFundStatusDTO: {}", JSONUtil.toJsonStr(preApproveFundStatusDTO));
        orderFeign.preUpdateFundStatus(preApproveFundStatusDTO);
    }

    /**
     * 影像上传
     * @param fundPreBaseDTO
     * @param type
     * @return
     */
    private KeShangResponseHead imageUploadPre(FundPreBaseDTO fundPreBaseDTO, String type) {

        Integer preId = fundPreBaseDTO.getPreId();
        log.info("--- 梅州客商影像上传开始 preId: {} ---", preId);
        FundEnum keshang = FundEnum.MZ_KE_SHANG;
        FundResourceDTO fundResourceDTO = new FundResourceDTO();
        if (ObjectUtil.equals(type, "pre")) {
            fundResourceDTO
                    .setLinkId(preId)
                    .setType(1)
                    .setFund(keshang)
                    .setCustomerBaseInfo(
                            new FundResourceDTO.CustomerBaseInfo()
                                    .setCustomerIdNo(fundPreBaseDTO.getIdNumber())
                                    .setPhone(fundPreBaseDTO.getPhone())
                    )
            ;
        }
        //todo 梅州客商获取文件
        Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(fundResourceDTO);
        if (!Result.isSuccess(listResult)) {
            throw new BusinessException("获取影像文件失败");
        }
        List<FundResourceResultDTO> data = listResult.getData();
        KeShangPlatformFileNoticeRequest keShangPlatformFileNoticeRequest = new KeShangPlatformFileNoticeRequest();
        List<KeShangPlatformFileNoticeRequest.FileItem> fileList = new ArrayList<>();

        data.stream().forEach(item -> {
            KeShangPlatformFileNoticeRequest.FileItem fileInfo = new  KeShangPlatformFileNoticeRequest.FileItem();
            fileInfo.setFilePath(item.getFilePath());
            fileInfo.setFileTp("");//todo
            fileInfo.setFileNm("");//todo
            fileList.add(fileInfo);
        });
        //数据太大 分开推送
        try {
            fileList.stream().forEach(item -> {
                List<KeShangPlatformFileNoticeRequest.FileItem> fileNew = new ArrayList<>();
                fileNew.add(item);
                keShangPlatformFileNoticeRequest.setFileList(fileNew);
                log.info("KeShangServiceImpl.imageUpload keShangPlatformFileNoticeRequest:{}", keShangPlatformFileNoticeRequest);
                //todo 请求接口 050100 平台文件通知
                KeShangResponseHead result = new KeShangResponseHead();
                if (!KeShangResponseHead.isSuccess(result) ) {
                    throw new BusinessException("影像上传失败");
                }
            });
        } catch (Exception e) {
            throw new BusinessException("影像上传失败:" + e.getMessage());
        }
        return null;
    }

    /**
     * 构建放款指令参数
     * @param fundFinalBaseDTO
     * @return
     */
    private KeShangLoanRequest buildLoanApply( FundFinalBaseDTO fundFinalBaseDTO, String loanAplyNO) {
        KeShangLoanRequest request = new KeShangLoanRequest();


        request.setIdno(fundFinalBaseDTO.getIdNumber())
                .setLoanAplyNO(loanAplyNO)
                .setAcctNO(fundFinalBaseDTO.getBankCardNumber())//todo
                .setLoanCtr("");//todo

        //todo 判断是否是新车，新车必填，判断新车的条件todo
        // 构建车辆信息集合
        if (ObjUtil.isNotEmpty(fundFinalBaseDTO.getVehicleType()) ) {

            List<KeShangLoanRequest.GMCar> gmCarList = new ArrayList<>();
            KeShangLoanRequest.GMCar gmCar = new KeShangLoanRequest.GMCar();

            gmCar.setCarMd(fundFinalBaseDTO.getVehicleType())
                    .setCarBrnd(fundFinalBaseDTO.getBrand())
                    .setEvaluePrice(fundFinalBaseDTO.getEstimatedValue() != null ? fundFinalBaseDTO.getEstimatedValue().toString() : "")
                    .setFstpymtRto("") //todo
                    .setRskFincngAmt("")//todo
                    .setFinTerm("") //todo
                    .setInsuranceAmt("")//todo
                    .setPurchaseTaxAmt("") //todo
                    .setGpsAmt("") //todo
                    .setBoutiqueAmt("")//todo
                    .setOtherAmt("")//todo
                    .setVhclTpId("") //todo
                    .setVhclSeriesId("") //todo
                    .setVhclBrndId("") //todo
                    .setVhclSeries(fundFinalBaseDTO.getVehicleSeries())
                    .setVhclVIN(fundFinalBaseDTO.getVin())
                    .setVhclInsurance("") //todo
                    .setLicenceTime("")//todo
                    .setBusinessMode("")//todo
                    .setCarKind(KeShangDictEnum.CarKind.PASSENGER.getCode()) //todo
                    .setCarMileage(fundFinalBaseDTO.getMileage() != null ? fundFinalBaseDTO.getMileage().toString() : "") // 行驶里程
                    .setCarType(ObjUtil.isNotEmpty(fundFinalBaseDTO.getVehicleNumber()) ? KeShangDictEnum.CarType.matchCarType(fundFinalBaseDTO.getVehicleNumber(), fundFinalBaseDTO.getTransferTimes()): "") //todo
                    .setBayCarsCity("") //todo
                    .setPlateNumberCity("")//todo
                    .setFlapperType(null) //todo
                    .setPlateNumber(fundFinalBaseDTO.getVehicleNumber())
                    .setCarCondition("") //todo
                    .setCarRegistTime(fundFinalBaseDTO.getRegisterDate() != null ? fundFinalBaseDTO.getRegisterDate() : ""); // 车辆登记日期

            gmCarList.add(gmCar);
            request.setGmCarList(gmCarList);
        }

        return request;
    }


    /**
     * 构建预授信请求参数
     */
    private KeShangCreditPreApplyRequest buildCreditPreApplyInfo(FundPreBaseDTO fundPreBaseDTO,String loanAplyNO) {
        KeShangCreditPreApplyRequest request = new KeShangCreditPreApplyRequest();

        List<KeShangCreditPreApplyRequest.riskData_ZLHJ> riskDataZlhjList = new ArrayList<KeShangCreditPreApplyRequest.riskData_ZLHJ>();
        KeShangCreditPreApplyRequest.riskData_ZLHJ riskDataZlhj = new KeShangCreditPreApplyRequest.riskData_ZLHJ();

        riskDataZlhj.setIndivGender(StrUtil.equals(fundPreBaseDTO.getGender(),"男") ? "male" : "female")
                    //todo
                    //.setIndivMarital("")
                    //.setCarApplicantIdvalidityDate(null)
                    .setCarApplicantIdaddress(fundPreBaseDTO.getIdCardAddress())
                    .setCarApplicantIdorg(fundPreBaseDTO.getIssuingAuthority())
                    .setCarLicenseNo(fundPreBaseDTO.getVehicleNumber())

                    .setDataSrc("")//todo
                    .setPrdName("")//todo
                    .setAuthType("")//todo
                    .setBiometrics("")//todo
                    .setCarFinancingAmount(fundPreBaseDTO.getApplyAmount().floatValue())//todo
                    .setCarBrand(fundPreBaseDTO.getBrand())//todo
        ;
        //主申请人婚姻状况（0：未婚、1：已婚）
        if (ObjUtil.isNotEmpty(fundPreBaseDTO.getMaritalStatus()) && ObjUtil.equals(fundPreBaseDTO.getMaritalStatus(), 1)){
            riskDataZlhj.setIndivMarital("已婚");
        }else if (ObjUtil.isNotEmpty(fundPreBaseDTO.getMaritalStatus()) && ObjUtil.equals(fundPreBaseDTO.getMaritalStatus(), 2)){
            riskDataZlhj.setIndivMarital("未婚");
        }else {
            riskDataZlhj.setIndivMarital("未知");
        }

        String validityStartDate = DateUtil.format(fundPreBaseDTO.getValidityStartDate(), "yyyyMMdd");
        if (StrUtil.equals(fundPreBaseDTO.getValidityEnd(), "长期")){
            riskDataZlhj.setCarApplicantIdvalidityDate(validityStartDate+"_20991231");
        }else {
            String validityEnd = fundPreBaseDTO.getValidityEnd().replaceAll("-","");
            riskDataZlhj.setCarApplicantIdvalidityDate(validityStartDate+"_"+validityEnd);
        }

        riskDataZlhjList.add(riskDataZlhj);
        request.setLoanAplyNO(loanAplyNO)
                .setClntNm(fundPreBaseDTO.getName())
                .setIdTp(KeShangDictEnum.IdCardType.RESIDENT.getCode())
                .setIdno(fundPreBaseDTO.getIdNumber())
                .setMblNO(fundPreBaseDTO.getPhone())
                //TODO 预审未存手机号
                .setPyeBnkCardNO(fundPreBaseDTO.getBankCardNumber())
                .setRiskData_ZLHJ(riskDataZlhjList);
        return request;
    }

    /**
     * 构建进件请求参数
     */
    private KeShangAddedBussinessRequest buildAddedBusiness(FundFinalBaseDTO fundFinalBaseDTO, String loanAplyNO) {

        OrderInfoEntity orderInfo = orderInfoMapper.selectById(fundFinalBaseDTO.getOrderId());
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));

        OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectById(orderInfo.getCustomerId());
        Assert.notNull(orderCustomerInfoEntity, () -> new BusinessException("客户信息不存在"));

        String managerName = "";
        String managerPhone = "";
        Result<UserInfoVO> userInfoVOResult = userFeign.searchUserName(orderInfo.getManagerId());
        if (Result.isSuccess(userInfoVOResult) && ObjUtil.isNotEmpty(userInfoVOResult.getData())) {
            UserInfoVO userInfoVO = userInfoVOResult.getData();
            managerName = userInfoVO.getName();
            managerPhone = userInfoVO.getMobile();
        }


        KeShangAddedBussinessRequest request = new KeShangAddedBussinessRequest();
        request.setLoanAplyNO(loanAplyNO)
                .setPltfmID("")//todo
                .setChnlID("")//todo
                .setChnlNm("")//todo
                .setPltfmID("");//todo

        // 构建申请人基本信息
        List<KeShangAddedBussinessRequest.GMAplcBsc> gmAplcBscList = new ArrayList<>();
        KeShangAddedBussinessRequest.GMAplcBsc gmAplcBsc = new KeShangAddedBussinessRequest.GMAplcBsc();

        // 设置申请人基本信息
        gmAplcBsc.setClntNm(fundFinalBaseDTO.getName())
                .setGndCd(ObjUtil.isNotNull(fundFinalBaseDTO.getGender()) ? KeShangDictEnum.GenderType.convertGenderCode(fundFinalBaseDTO.getGender()) : KeShangDictEnum.GenderType.UNKNOWN.getCode())
                .setIdTp(KeShangDictEnum.IdCardType.RESIDENT.getCode()) // 居民身份证
                .setIdno(fundFinalBaseDTO.getIdNumber())
                .setMblNO(fundFinalBaseDTO.getPhone())
                .setMrtlStCd(ObjUtil.isNotNull(fundFinalBaseDTO.getMaritalStatus())
                        ? KeShangDictEnum.MaritalStatusType.convert(DictMaritalStatus.fromCode(fundFinalBaseDTO.getMaritalStatus())) : KeShangDictEnum.MaritalStatusType.UNKNOWN.getCode())
                .setRsdcStCd(ObjUtil.isNotNull(fundFinalBaseDTO.getResideStatus())
                        ? KeShangDictEnum.ResidenceStatusEnum.convert(DictResidentStatus.fromCode(fundFinalBaseDTO.getResideStatus())) : KeShangDictEnum.ResidenceStatusEnum.UNKNOWN.getCode())//todo
                .setHghstEdctHistCd(ObjUtil.isNotNull(fundFinalBaseDTO.getEducationalBackground())
                        ? KeShangDictEnum.EducationLevelEnum.convert(DictEducationLevel.fromCode(fundFinalBaseDTO.getEducationalBackground())): KeShangDictEnum.EducationLevelEnum.UNKNOWN.getCode())//todo
                .setRsdcAdr(fundFinalBaseDTO.getResidentialDetailedAddress())
                .setDomcKindCd("")//todo
                .setPostalAdr("")//todo
                .setUnitNm(fundFinalBaseDTO.getEnterpriseName())//todo
                .setUnitAdr(fundFinalBaseDTO.getEnterpriseDetailsAddress())//todo
                .setUnitKindCd(ObjUtil.isNotNull(fundFinalBaseDTO.getEducationalBackground())
                        ? KeShangDictEnum.CompanyType.convert(DictCompanyType.fromCode(fundFinalBaseDTO.getEnterpriseNature())) : KeShangDictEnum.CompanyType.OTHER.getCode())//todo
                .setUnitTel(fundFinalBaseDTO.getEnterprisePhone())
                .setWrkYrTrm(fundFinalBaseDTO.getCurrentUnitStartDate())
                .setMoIncm(ObjUtil.isNotNull(fundFinalBaseDTO.getMonthlyIncome()) ? fundFinalBaseDTO.getMonthlyIncome().toString() : "")//todo 为空时需要怎么出路
                .setOcpCd(KeShangDictEnum.OccupationEnum.convert(DictOccupationEnum.fromCode(fundFinalBaseDTO.getVocational())))
               /* .setRsdcAdrProvinceId(fundFinalBaseDTO.getResidenceProvinceCode())
                .setRsdcAdrCityId(fundFinalBaseDTO.getResidenceCityCode())
                .setRsdcAdrAreaId(fundFinalBaseDTO.getResidenceAreaCode())
                .setUnitAdrProvinceId(fundFinalBaseDTO.getWorkProvinceCode())
                .setUnitAdrCityId(fundFinalBaseDTO.getWorkCityCode())
                .setUnitAdrAreaId(fundFinalBaseDTO.getWorkAreaCode())
                .setDstcCd("")*/
                .setPosition(fundFinalBaseDTO.getDuties())//todo
                .setApplyCrowd(ObjUtil.isNotNull(orderCustomerInfoEntity.getWorkType()) ? orderCustomerInfoEntity.getWorkType().toString() : "")//todo
                .setRsdcAdrProvince(fundFinalBaseDTO.getResidentialProName())
                .setRsdcAdrCity(fundFinalBaseDTO.getResidentialCityName())
                .setRsdcAdrArea(fundFinalBaseDTO.getResidentialAreaName())
                .setUnitAdrProvince(fundFinalBaseDTO.getEnterpriseProName())
                .setUnitAdrCity(fundFinalBaseDTO.getEnterpriseCityName())
                .setUnitAdrArea(fundFinalBaseDTO.getEnterpriseAreaName())
                .setCstEmail(fundFinalBaseDTO.getEmail())
                .setUnitBlngIdyCd(ObjUtil.isNotNull(fundFinalBaseDTO.getEnterpriseIndustryInvolved()) ? KeShangDictEnum.IndustryTypeEnum.convert(DictIndustryEnum.fromCode(fundFinalBaseDTO.getIndustryInvolved())) : KeShangDictEnum.IndustryTypeEnum.Z.getCode())
                .setProposerIpAddress("");//todo


        // 计算年份==
        String startResideDate = fundFinalBaseDTO.getStartResideDate();
        if (StrUtil.isBlank(startResideDate)) {
            throw new BusinessException("起居时间不能为空");
        }
        Date resideStartDate = DateUtil.parse(startResideDate);
        long years = DateUtil.betweenYear(resideStartDate, new Date(), true);
        gmAplcBsc.setRsdcYear(years + "");
        // 计算年份==

        buildApplyAddressCodeInfo(fundFinalBaseDTO, gmAplcBsc);

        gmAplcBscList.add(gmAplcBsc);
        request.setGmAplcBscList(gmAplcBscList);

        // 构建联系人信息
        List<KeShangAddedBussinessRequest.GMCrsp> gmCrspList = buildApplyRelationList(fundFinalBaseDTO);
        request.setGmCrspList(gmCrspList);

        if(ObjUtil.isNotNull(fundFinalBaseDTO.getAge()) && fundFinalBaseDTO.getAge() < 20){
            List<KeShangAddedBussinessRequest.GMGnt> gmGntList = new ArrayList<>();
            KeShangAddedBussinessRequest.GMGnt gmGnt = new KeShangAddedBussinessRequest.GMGnt();

            gmGnt.setCrspNm("")//todo
                    .setCrspIDNO("")//todo
                    .setGuarantorRel("")//todo
                    .setCrspIDTP("")//todo
                    .setCrspMblNO("")//todo
                    .setCrspRsdcAdr("");//todo

            gmGntList.add(gmGnt);
            request.setGmGntList(gmGntList);
        }

        // 构建车辆信息
        List<KeShangAddedBussinessRequest.GMCar> gmCarList = new ArrayList<>();
        KeShangAddedBussinessRequest.GMCar gmCar = new KeShangAddedBussinessRequest.GMCar();

        gmCar.setCarMd(fundFinalBaseDTO.getVehicleType())
                .setCarBrnd(fundFinalBaseDTO.getBrand())
                .setEvaluePrice(ObjUtil.isNotNull(fundFinalBaseDTO.getEstimatedValue()) ? fundFinalBaseDTO.getEstimatedValue().toString() :"")//todo
                .setFstpymtRto("1")//todo
                .setRskFincngAmt("")//todo
                .setFinTerm("")//todo
                .setInsuranceAmt("")//todo
                .setPurchaseTaxAmt("")//todo
                .setGpsAmt("")//todo
                .setBoutiqueAmt("")//todo
                .setOtherAmt("")//todo
                .setVhclTpId("")//todo
                .setVhclSeriesId("")//todo
                .setVhclBrndId("")//todo
                .setVhclSeries(fundFinalBaseDTO.getVehicleSeries())
                .setVhclVIN(fundFinalBaseDTO.getVin())
                .setVhclInsurance("")//todo
                .setLicenceTime("")//todo
                .setBusinessMode("")//todo
                .setCarKind(KeShangDictEnum.CarKind.PASSENGER.getCode())//todo
                .setCarMileage(ObjUtil.isNotNull(fundFinalBaseDTO.getMileage()) ? fundFinalBaseDTO.getMileage().toString() : "")//todo
                .setCarType(KeShangDictEnum.CarType.matchCarType(fundFinalBaseDTO.getVehicleNumber(), fundFinalBaseDTO.getTransferTimes()))//todo
                .setBayCarsCity("")//todo
                .setPlateNumberCity("")//todo
                .setFlapperType("")//todo
                .setPlateNumber(fundFinalBaseDTO.getVehicleNumber())//todo
                .setCarCondition("")//todo
                .setCarRegistTime(fundFinalBaseDTO.getRegisterDate() != null ? fundFinalBaseDTO.getRegisterDate() : "")
                .setCarLicType("");//todo

        gmCarList.add(gmCar);
        request.setGmCarList(gmCarList);

        // 构建身份证信息
        List<KeShangAddedBussinessRequest.GMIdntVchr> gmIdntVchrList = new ArrayList<>();
        KeShangAddedBussinessRequest.GMIdntVchr gmIdntVchr = new KeShangAddedBussinessRequest.GMIdntVchr();

        gmIdntVchr.setIdntVchrNm(fundFinalBaseDTO.getName())
                .setIdntVchrIDNO(fundFinalBaseDTO.getIdNumber())
                .setIdntVchrIssuInst(fundFinalBaseDTO.getIssuingAuthority())
                .setIdntVchrVldtEnd(sdf.format(fundFinalBaseDTO.getValidityStartDate()))
                .setIdntVchrVldtStart(fundFinalBaseDTO.getValidityEnd());

        gmIdntVchrList.add(gmIdntVchr);
        request.setGmIdntVchrList(gmIdntVchrList);

        // 构建驾驶证信息
        List<KeShangAddedBussinessRequest.GMDrvrVchr> gmDrvrVchrList = new ArrayList<>();
        KeShangAddedBussinessRequest.GMDrvrVchr gmDrvrVchr = new KeShangAddedBussinessRequest.GMDrvrVchr();

        gmDrvrVchr.setDrvrVchrIDNO(fundFinalBaseDTO.getRegistrationCode())//todo
                .setDrvrVchrNm("")//todo
                .setDrvrVchrAdr("")//todo
                .setDrvrVchrAlwDrvCarMd("")//todo
                .setStartDate("")//todo
                .setEndDate("");//todo

        gmDrvrVchrList.add(gmDrvrVchr);
        request.setGmDrvrVchrList(gmDrvrVchrList);

        // 构建银行卡信息
        List<KeShangAddedBussinessRequest.GMAccount> gmAccountList = new ArrayList<>();
        KeShangAddedBussinessRequest.GMAccount gmAccount = new KeShangAddedBussinessRequest.GMAccount();

        gmAccount.setAcctNO(fundFinalBaseDTO.getBankCardNumber())
                .setOpenAcctBnkNm(fundFinalBaseDTO.getOpeningBank())
                .setName(fundFinalBaseDTO.getName())
                .setRsrvMblNO(fundFinalBaseDTO.getBankReservedPhone());

        gmAccountList.add(gmAccount);
        request.setGmAccountList(gmAccountList);

        // 构建贷款信息
        List<KeShangAddedBussinessRequest.GMLoanTxn> gmLoanTxnList = new ArrayList<>();
        KeShangAddedBussinessRequest.GMLoanTxn gmLoanTxn = new KeShangAddedBussinessRequest.GMLoanTxn();

        gmLoanTxn.setProductType("")//todo
                .setLoanRate(ObjUtil.isNotNull(fundFinalBaseDTO.getYearRate()) ? fundFinalBaseDTO.getYearRate().toString() :"")
                .setTlrNO("")//todo
                .setTlrName(managerName)//todo
                .setTlrPhone(managerPhone)//todo
                .setTlrCityName("")//todo
                .setTlrCity("")//todo
                .setApproveMoney("")//todo
                .setEnterpriseName("")//todo
                .setEnterpriseID("")//todo
                .setLoanCity("")//todo
                .setReviewComments("");//todo

        gmLoanTxnList.add(gmLoanTxn);
        request.setGmLoanTxnList(gmLoanTxnList);
        // 设置其他字段
        request.setCustLevel("")//todo
                .setChnlType("")//todo
                .setFdcrPymtAcctNO("")//todo
                .setFdcrPymtAcctNm("")//todo
                .setFdcrPymtAcctBnkNO("")//todo
                .setFdcrPymtAcctBnkNm("")//todo
                .setCapitalRate("");//todo

        // 构建经营贷信息 todo 判断区分经营贷还是消费贷
        List<KeShangAddedBussinessRequest.JYDccount> JYDccountList = new ArrayList<>();
        KeShangAddedBussinessRequest.JYDccount jyDccount = new KeShangAddedBussinessRequest.JYDccount();

        jyDccount.setCompanyAddress("")//todo
                .setCompanyProp("")//todo
                .setIndustry(null)//todo
                .setCompanyType("")//todo
                .setCompanyStatus("")//todo
                .setOperatorIdentity("")//todo
                .setInvestRatio("")//todo
                .setCompanyStartDate("")//todo
                .setCompanyDueDate("")//todo
                .setBusinessLicense("");//todo

        JYDccountList.add(jyDccount);
        request.setJydccountList(JYDccountList);


        return request;
    }


    /**
     * 构建地址信息
     * @param fundFinalBaseDTO
     * @param gmAplcBsc
     */
    private void buildApplyAddressCodeInfo(FundFinalBaseDTO fundFinalBaseDTO,KeShangAddedBussinessRequest.GMAplcBsc gmAplcBsc) {
        // 现住房地址
        String[] liveAddCodes = AddressUtil.getAllCodes(fundFinalBaseDTO.getResidentialProName(), fundFinalBaseDTO.getResidentialCityName(), fundFinalBaseDTO.getResidentialAreaName());
        if (liveAddCodes.length == 0) {
            throw new BusinessException("现住房地址解析失败");
        }
        try {
            gmAplcBsc.setRsdcAdrProvinceId(liveAddCodes[0]) // 省
                    .setRsdcAdrCityId(liveAddCodes[1])
                    .setDstcCd(liveAddCodes[1])/// 市
                    .setRsdcAdrAreaId(liveAddCodes[2]) // 区
                    .setRsdcAdr(fundFinalBaseDTO.getResidentialDetailedAddress()); // 详细地址
        } catch (Exception e) {
            throw new BusinessException("现住房地址解析失败");
        }

        //单位地址
        String[] enterpriseAddCodes = AddressUtil.getAllCodes(fundFinalBaseDTO.getResidentialProName(), fundFinalBaseDTO.getResidentialCityName(), fundFinalBaseDTO.getResidentialAreaName());
        if (liveAddCodes.length == 0) {
            throw new BusinessException("单位地址解析失败");
        }
        try {
            gmAplcBsc.setUnitAdrProvinceId(liveAddCodes[0]) // 省
                    .setUnitAdrCityId(liveAddCodes[1]) // 市
                    .setUnitAdrAreaId(liveAddCodes[2]) // 区
                    .setUnitAdr(fundFinalBaseDTO.getEnterpriseDetailsAddress());
        } catch (Exception e) {
            throw new BusinessException("单位地址解析失败");
        }

        // 身份证地址解析
        String idCardProvince = fundFinalBaseDTO.getIdCardProvinceName();
        String idCardCity = fundFinalBaseDTO.getIdCardCityName();
        String idCardArea = null;

        // 如果省或市为空，解析身份证地址
        if (StrUtil.isBlank(idCardProvince) || StrUtil.isBlank(idCardCity) || StrUtil.isBlank(idCardArea)) {
            String idCardAddress = PareAddressUtils.parseAddress(fundFinalBaseDTO.getIdCardAddress());
            log.info("ChangYinServiceImpl.buildFamilyInfo begin idCardAddress: {}", idCardAddress);
            String[] idCardAddressParts = idCardAddress.split("-");

            // 分别获取省、市、区
            if (StrUtil.isBlank(idCardProvince)) {
                idCardProvince = idCardAddressParts[0];
            }
            if (StrUtil.isBlank(idCardCity)) {
                idCardCity = idCardAddressParts[1];
            }
            if (StrUtil.isBlank(idCardArea)) {
                idCardArea = idCardAddressParts.length > 2 ? idCardAddressParts[2] : null;
            }
        }

        // 如果省、市都有，并且区不为空，则将区改为“市辖区”
        if (StrUtil.isNotBlank(idCardProvince) && StrUtil.isNotBlank(idCardCity)) {
            if (StrUtil.isBlank(idCardArea)) {
                idCardArea = "市辖区";
            }
        }

        // 获取身份证地址代码（允许区域为空）
        String[] idCardAddCodes = AddressUtil.getAllCodes(idCardProvince, idCardCity, idCardArea);
        if (idCardAddCodes.length == 0) {
            throw new BusinessException("身份证地区解析失败");
        }
    }

    /**
     * 构建联系人信息
     */
    private List<KeShangAddedBussinessRequest.GMCrsp> buildApplyRelationList(FundFinalBaseDTO fundFinalBaseDTO) {
        if (CollUtil.isEmpty(fundFinalBaseDTO.getContactPersonList())) {
            throw new BusinessException("未获取到联系人信息");
        }
        List<KeShangAddedBussinessRequest.GMCrsp> gmCrspList = new ArrayList<>();


      /*  for (ContactPersonDTO contactPerson : fundFinalBaseDTO.getContactPersonList()) {

            relationInfo.setRelName(contactPerson.getContactName()) // 联系人名称
                    .setRelMobile(contactPerson.getContactPhone()) // 联系人手机
                    .setRelRelation(ChangYinDictEnum.ApplicantContactRelation.convert(contactRelation)); // 联系人与申请人关系
            gmCrspList.add(gmCrsp);
        }*/

        if (CollUtil.isEmpty(fundFinalBaseDTO.getContactPersonList()) && fundFinalBaseDTO.getContactPersonList().size() == 1){
            KeShangAddedBussinessRequest.GMCrsp gmCrsp = new KeShangAddedBussinessRequest.GMCrsp();
            ContactPersonDTO contactPerson =  fundFinalBaseDTO.getContactPersonList().get(0);
            // 联系人1信息
            gmCrsp.setCrspNm1st(contactPerson.getContactName())
                    .setCrspMblNO1st(contactPerson.getContactPhone())
                    .setCrspIDNO1st(contactPerson.getIdNumber())
                    .setCrspWthAplcRlnshp1st(KeShangDictEnum.ContactRelationEnum.convert(DictContactRelation.fromCode(contactPerson.getRelation())));
            gmCrspList.add(gmCrsp);
        }

        //todo 联系人2
        if (CollUtil.isEmpty(fundFinalBaseDTO.getContactPersonList()) && fundFinalBaseDTO.getContactPersonList().size() >= 2){
            KeShangAddedBussinessRequest.GMCrsp gmCrsp = new KeShangAddedBussinessRequest.GMCrsp();
            ContactPersonDTO contactPerson =  fundFinalBaseDTO.getContactPersonList().get(0);
            // 联系人2信息
            gmCrsp.setCrspNm2nd(contactPerson.getContactName())
                    .setCrspMblNO2nd(contactPerson.getContactPhone())
                    .setCrspIDNO2nd(contactPerson.getContactPhone())
                    .setCrspWthAplcRlnshp2nd(KeShangDictEnum.ContactRelationEnum.convert(DictContactRelation.fromCode(contactPerson.getRelation())));
            gmCrspList.add(gmCrsp);
        }

        return gmCrspList;
    }

}
