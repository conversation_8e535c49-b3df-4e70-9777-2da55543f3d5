package com.longhuan.order.settleCalculation.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SettleCalculationListVO {

    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNumber;

    @ApiModelProperty(value = "资方名称")
    private String fundName;

    @ApiModelProperty(value = "所属业务员")
    private Integer managerId;

    @ApiModelProperty(value = "所属业务员")
    private String managerName;

    @ApiModelProperty(value = "所属门店")
    private String storeName;

    @ApiModelProperty(value = "所属大区")
    private String regionName;

    @ApiModelProperty(value = "产品")
    private String productName;

    @ApiModelProperty(value = "当前期数")
    private Integer nowTerm;

    @ApiModelProperty(value = "所有期数")
    private Integer totalTerm;

    @ApiModelProperty(value = "已还期数")
    private String term;

    @ApiModelProperty(value = "合同金额")
    private BigDecimal applyAmount;

    @ApiModelProperty(value = "还款方式 1:等额本息,2:等额本金,3:先息后本")
    private Integer repayMethod;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "是否逾期")
    private Integer isOverdue;

    @ApiModelProperty(value = "还款状态（0:还款中,1:资方已结清）")
    private Integer planState;

}
