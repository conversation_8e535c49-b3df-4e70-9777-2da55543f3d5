package com.longhuan.order.statemachine.action;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.order.mapper.OrderGpsInfoMapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.mapper.OrderNodeRecordMapper;
import com.longhuan.order.pojo.entity.OrderGpsInfoEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.service.GpsInstallInfoService;
import com.longhuan.order.service.impl.OrderSendMessageImpl;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 放款成功
 *
 * <AUTHOR>
 * @date 2025/1/7 10:53
 */
@Slf4j
@Component
public class PaymentSuccessAction extends OrderApproveAction implements Action<States, Events> {

    private final OrderSendMessageImpl orderSendMessage;
 private final OrderGpsInfoMapper orderGpsInfoMapper;
 private final GpsInstallInfoService gpsInstallInfoService;


 public PaymentSuccessAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper, OrderSendMessageImpl orderSendMessage, OrderGpsInfoMapper orderGpsInfoMapper,GpsInstallInfoService gpsInstallInfoService) {
        super(orderInfoMapper, orderNodeRecordMapper, orderSendMessage);
        this.orderSendMessage = orderSendMessage;
  this.orderGpsInfoMapper = orderGpsInfoMapper;
  this.gpsInstallInfoService = gpsInstallInfoService;
    }

    @Override
    public void execute(StateContext<States, Events> context) {


        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();

        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);


        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event, currentNode, nextNode);

        // 1. 修改订单表状态
        if (Boolean.TRUE.equals(target.getId().getValid())) {
            saveOrderNode(orderId, nextNode);
        }

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);

        // 3. 发送消息
        Integer currentEventCode = context.getEvent().getCode();
        if (Objects.equals(currentNode, States.RISK_FINAL_APPROVE.getNode())
                || Objects.equals(currentNode, States.PAYMENT_CONTRACT_APPROVAL.getNode())
                || (Objects.equals(currentNode, States.FUNDS_PAYMENT_APPROVAL.getNode()) && "1".equals(String.valueOf(currentEventCode)))
                || Objects.equals(currentNode, States.FUNDS_PAYMENT_FAIL.getNode())
                || Objects.equals(currentNode, States.PAYMENT_SUCCESS.getNode())) {
            orderSendMessage.sendMessage(orderId, currentNode, currentEventCode);
        }
        //4.gps车辆转移
        OrderGpsInfoEntity orderGpsInfoEntity = orderGpsInfoMapper.selectOne(new LambdaQueryWrapper<OrderGpsInfoEntity>()
                .eq(OrderGpsInfoEntity::getOrderId, orderId)
                .eq(OrderGpsInfoEntity::getDeleteFlag, 0)
                .orderByDesc(OrderGpsInfoEntity::getCreateTime), false);
        try {
            gpsInstallInfoService.updateCarInfo(orderGpsInfoEntity, orderId);
            orderGpsInfoMapper.updateById(orderGpsInfoEntity);
            //更新车派达放款时间及结清时间
            gpsInstallInfoService.updateBill(orderId);
        } catch (Exception e) {
            log.error("GpsServiceImpl.setChildUserByFuMin.error:{}", e.getMessage());
        }
    }


}
