package com.longhuan.xxl.job;

import com.longhuan.xxl.feign.OrderFeign;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class TimingBlacklistTaskJobHandler {
    private final OrderFeign orderFeign;

    @XxlJob("timingBlacklistTaskJobHandler")
    public ReturnT<String> TimingBlacklistTaskJobHandler() {
        log.info("TimingBlacklistTaskJobHandler start");
        orderFeign.timingBlacklist();
        log.info("TimingBlacklistTaskJobHandler end");
        return ReturnT.SUCCESS;

    }
}
