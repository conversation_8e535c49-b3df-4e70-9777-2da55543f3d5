package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.BatchUpdateProductDeptDTO;
import com.longhuan.order.pojo.dto.GetProductRongdanListDTO;
import com.longhuan.order.pojo.dto.SaveProductRongdanDTO;
import com.longhuan.order.pojo.dto.UpdateProductDeptDTO;
import com.longhuan.order.pojo.vo.GetProductRongdanListVO;
import com.longhuan.order.service.DeptProductRongdanDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v1/productRongdan")
@RequiredArgsConstructor
public class DeptProductRongdanDetailController {
    private final DeptProductRongdanDetailService deptProductRongdanDetailService;
    /**
     * 获取地区产品融担记录
     */
    @PostMapping("/getProductDeptList")
    public Result<Page<GetProductRongdanListVO>> getProductRongdanList(@RequestBody GetProductRongdanListDTO dto) {
        return Result.success(deptProductRongdanDetailService.getProductRongdanList(dto));
    }
    /**
     * 新增地区产品融担记录
     */
    @PostMapping("/saveProductDept")
    public Result<Boolean> saveProductRongdan(@RequestBody SaveProductRongdanDTO dto) {
        return Result.success(deptProductRongdanDetailService.saveProductRongdan(dto));
    }
    /**
     * 修改地区产品融担记录
     */
    @PostMapping("/updateProductDept")
    public Result<Boolean> updateProductDept(@RequestBody UpdateProductDeptDTO dto) {
        return Result.success(deptProductRongdanDetailService.updateProductDept(dto));
    }
    /**
     * 批量修改地区产品融担记录
     */
    @PostMapping("/batchUpdateProductDept")
    public Result<Boolean> batchUpdateProductDept(@RequestBody BatchUpdateProductDeptDTO dto) {
        return Result.success(deptProductRongdanDetailService.batchUpdateProductDept(dto));
    }
    /**
     * 根据id获取详情
     */
    @GetMapping("/getProductDeptDetail/{productDeptId}")
    public Result<GetProductRongdanListVO> getProductDeptDetail(@PathVariable("productDeptId") Integer productDeptId) {
        return Result.success(deptProductRongdanDetailService.getProductDeptDetail(productDeptId));
    }
}
