package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.github.yulichang.wrapper.MPJLambdaWrapper;

import com.longhuan.approve.api.pojo.dto.TongHuiCompensateSwitchDTO;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.data.api.pojo.vo.DigitizedOverdueOrdersVO;
import com.longhuan.order.constants.ExemptionApprovalConstants;
import com.longhuan.order.enums.*;
import com.longhuan.order.feign.*;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.dto.digitalize.DigitalizeSettleList;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.*;
import com.longhuan.order.settleCalculation.service.SettleCalculationService;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.order.util.EasyExcelUtil;
import com.longhuan.user.api.DeptDetailVO;
import com.longhuan.user.enums.DingTaskConclusionEnum;
import com.longhuan.user.pojo.dto.*;
import com.longhuan.user.pojo.dto.CaseInfoUserDTO;
import com.longhuan.user.pojo.vo.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.Asserts;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.longhuan.order.constants.AdvanceOutsourcingApplicationConstants.*;
import static com.longhuan.order.constants.AdvanceOutsourcingApplicationConstants.FSH_CX_PRESIDENT_APPROVE_ID;
import static com.longhuan.order.constants.ExemptionApprovalConstants.*;
import static com.longhuan.order.enums.CaseApproveNodeEnums.*;


/**
 * <AUTHOR>
 * @date 2025/01/07
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CaseInfoServiceImpl extends ServiceImpl<CaseInfoEntityMapper, CaseInfoEntity> implements CaseInfoService {
    private final CaseInfoEntityMapper caseInfoEntityMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final CaseInfoApproveEntityMapper caseInfoApproveEntityMapper;
    private final LawsuitApplicationEntityMapper lawsuitApplicationEntityMapper;
    private final UserFeign userFeign;
    private final LawsuitStatusRecordsMapper lawsuitStatusRecordsMapper;
    private final CaseInfoStatusRecordsMapper caseInfoStatusRecordsMapper;
    private final AfterLoanPatchesEntityMapper afterLoanPatchesEntityMapper;
    private final ZhongXinService zhongXinService;
    private final DataPermissionService dataPermissionService;
    private final RepaymentService repaymentService;
    private final RepurchaseRepayService repurchaseRepayService;
    private final CaseInfoApproveEntityMapper caseInfoNodeRecordEntityMapper;
    private final DingDrawMoneyFeign dingFeign;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final CaseCirculationDeptRecordMapper caseCirculationDeptRecordMapper;
    private final OrderFeeDetailMapper orderFeeDetailMapper;
    private final ElectricReminderRecordEntityMapper electricReminderRecordEntityMapper;
    private final OverdueWarnConfigService overdueWarnConfigService;
    private final OrderService ordersService;
    private final DingTaskFeign dingTaskFeign;
    private final EnvUtil envUtil;
    private final SwitchUtils switchUtils;
    private final OutsourcingDetailsEntityMapper outsourcingDetailsEntityMapper;
    private final OutsourcingDetailsRecordEntityMapper outsourcingDetailsRecordEntityMapper;
    private final FundRepurchaseCalcMapper fundRepurchaseCalcMapper;
    private final DigitalOutsourcingOrderEntityMapper digitalOutsourcingOrderEntityMapper;
    private final DigitalizeFeign digitalizeFeign;
    private final ZhongXinFeign zhongXinFeign;
    private final DataFeign dataFeign;
    private final OrderCustomerInfoService orderCustomerInfoService;
    private final ParamsSnapshotMapper paramsSnapshotMapper;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final OrderVehicleInfoService orderVehicleInfoService;
    private final OrderService orderService;
    private final CaseUpdateFieldRecordsMapper caseUpdateFieldRecordsMapper;
    private final OutsourcingService outsourcingService;
    private final LawsuitService lawsuitService;
    private final ExemptionApplicationEntityMapper exemptionApplicationEntityMapper;
    private final ResourceFeign resourceFeign;
    private final RepurchaseRepaymentInfoMapper repurchaseRepaymentInfoMapper;
    private final KingdeeAttachmentEntityMapper kingdeeAttachmentEntityMapper;
    private final FundProductMappingMapper fundProductMappingEntityMapper;
    private final FundDeductService fundDeductService;
    private final KingdeeOutsourcingService kingdeeOutsourcingService;
    private final CaseInfoPushRecordEntityMapper caseInfoPushRecordEntityMapper;
    private final OrderVehicleGpsLogService orderVehicleGpsLogService;
    private final OrderSettleAmountRecordMapper orderSettleAmountRecordMapper;
    private final OrderPayApplicationMapper orderPayApplicationMapper;
    private final OrderVehicleGpsLogMapper orderVehicleGpsLogMapper;
    private final OutsourcingExpansionService outsourcingExpansionService;


    private final OutsourcingSettlementTrialCalculationsService outsourcingSettlementTrialCalculationsService;


    private final ApproveFeign approveFeign;
    private final SettleCalculationService settleCalculationService;
    private final  CaseTransferGpsAccountService caseTransferGpsAccountService;
    private final DingTalkProcessConfigEntityMapper dingTalkProcessConfigEntityMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addCaseInfo() {
        MPJLambdaWrapper<OrderInfoEntity> wrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .selectAll(OrderInfoEntity.class)
                .selectAs(OrderInfoEntity::getId, OrderApproveListVO::getOrderId)
                .selectAs(OrderInfoEntity::getRiskUserId, OrderApproveListVO::getRisiUserId)
                .eq(OrderInfoEntity::getState, States.PAYMENT_SUCCESS.getNode())
                .notInSql(OrderInfoEntity::getId, "select order_id from LH_CASE_INFO where " +
                        "  delete_flag = 0 AND order_id IS NOT NULL ")
                .notInSql(OrderInfoEntity::getId, "select order_id from lh_lawsuit_application where " + "  delete_flag = 0")
                .eq(OrderInfoEntity::getDeleteFlag, 0);
        //获取逾期时间大于90天的
        wrapper.ge(OrderInfoEntity::getOverdueDays, 210);

        // 数据权限
//        dataPermissionService.limitOverdueOrder(loginUser, wrapper);
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectJoinList(OrderInfoEntity.class, wrapper);
        log.info("CaseInfoServiceImpl addCaseInfo orderInfoList:{}", orderInfoEntityList);
        if (CollUtil.isNotEmpty(orderInfoEntityList)) {
            orderInfoEntityList.forEach(orderInfo -> {
                //创建案件信息
                CaseInfoEntity caseInfoEntity = new CaseInfoEntity()
                        .setCaseNo(generateCaseNumber())
                        .setOrderId(orderInfo.getId())
                        .setCaseName(orderInfo.getCustomerName())
                        .setCaseStatus(1)
                        .setCustomerName(orderInfo.getCustomerName())
                        .setOderState(1)
                        .setDisposalCompany("众信天下")
                        .setDeptId(DeptEnum.ZHONG_XIN.getId())
                        .setAuditStatus(0)
                        .setCaseType(1)
                        .setCallState(0)
                        .setVisitState(0)
                        .setInitiator("自动推送")
                        .setCirculationType(1)
                        .setReassignTime(LocalDateTime.now())
                        .setDisposalDeadline(LocalDateTime.now().plusDays(15))
                        .setStoreId(orderInfo.getDeptId())
                        .setManagerId(orderInfo.getManagerId())
                        .setRegionId(orderInfo.getRegionId())
                        .setTeamId(orderInfo.getTeamId())
                        .setSourceType(orderInfo.getSourceType())
                        .setDataSource(1)
                        .setCurrentNode(CaseApproveNodeEnums.SUCCESS);
                caseInfoEntityMapper.insert(caseInfoEntity);
                //推送至众信
                Result<String> stringResult = zhongXinService.pushCaseToZhongXin(orderInfo.getId());
                log.info("定时推送至众信结果：{}", stringResult);

                try {
                    OrderVehicleGpsLogDTO reqVLog = new OrderVehicleGpsLogDTO().setOrderId(orderInfo.getId()).setOrderSource(1);
                    log.info("CaseInfoServiceImpl.autoGetCarLatestPosition 定时任务 委外获取定位 开始：{}", reqVLog);
                    orderVehicleGpsLogService.autoGetCarLatestPosition(reqVLog, null);
                } catch (Exception e) {
                    log.error("CaseInfoServiceImpl.autoGetCarLatestPosition 定时任务 委外获取定位失败：{}", e);
                }

                if (Objects.equals(orderInfo.getFundId(), FundEnum.ZHONG_HENG_TONG_HUI.getValue())) {
                    log.info("CaseInfoServiceImpl.addCaseInfo tongHuiCompensateSwitch start entity:{}", orderInfo);
                    approveFeign.tongHuiCompensateSwitch(new TongHuiCompensateSwitchDTO().setCompensateStatus(1).setOrderId(orderInfo.getId()));
                }
            });
        }


        return true;

    }


    /**
     * 生成案件号
     */
    private static final String ORDER_HEAD = "LJ_";

    // 获取当前时间的 yyyyMMddHHmm 格式
    private static String getCurrentTimestamp() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        return sdf.format(new Date());
    }

    public String generateCaseNumber() {
        OrderInfoMapper orderInfoMapper = SpringContentUtils.getBean(OrderInfoMapper.class);

        int sequence;

        // 根据订单类型获取相应的序列号
        sequence = orderInfoMapper.getNextFddSequence();
        // 格式化序列号为5位数，不足前面补0
        String formattedSequence = String.format("%05d", sequence);

        // 生成订单号
        String timestamp = getCurrentTimestamp();

        return ORDER_HEAD + "AJ" + "_" + timestamp + formattedSequence;
        //获取订单信息
    }

    private static LocalDateTime getTimeOfEnd(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.MAX);
    }

    private static LocalDateTime getTimeOfStart(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.MIN);
    }


    @Override
    public Page<CaseInfoOrderApplyVO> getCaseInfoOrderApplyList(CaseInfoListDTO caseInfoListDTO, LoginUser loginUser) {
        MPJLambdaWrapper<OrderInfoEntity> wrapper = new MPJLambdaWrapper<OrderInfoEntity>();
        wrapper.selectAs(OrderInfoEntity::getId, CaseInfoOrderApplyVO::getOrderId)
                .selectAs(OrderInfoEntity::getOrderNumber, CaseInfoOrderApplyVO::getOrderNumber)
                .selectAs(OrderInfoEntity::getCustomerName, CaseInfoOrderApplyVO::getCustomerName)
                .selectAs(OrderInfoEntity::getFundName, CaseInfoOrderApplyVO::getFundName)
                .selectAs(OrderInfoEntity::getProductName, CaseInfoOrderApplyVO::getProductName)
                .selectAs(OrderInfoEntity::getStoreName, CaseInfoOrderApplyVO::getStoreName)
                .selectAs(OrderInfoEntity::getRegionName, CaseInfoOrderApplyVO::getRegionName)
                .selectAs(OrderInfoEntity::getVehicleNumber, CaseInfoOrderApplyVO::getVehicleNumber)
                .selectAs(OrderInfoEntity::getIsRepurchase, CaseInfoOrderApplyVO::getIsRepurchase)
                .selectAs(OrderAmountEntity::getCustomerConfirmAmount, CaseInfoOrderApplyVO::getCustomerConfirmAmount)
//                .selectAs(OrderInfoEntity::getOverdueDays, CaseInfoOrderApplyVO::getOverdueDays)
                .select("CASE WHEN t.current_node =8000 THEN '已结清'  WHEN t.is_overdue =1 THEN '逾期' WHEN t.is_overdue =0 THEN '还款中'    ELSE '未知' END as oderState")
                .leftJoin(OrderAmountEntity.class, OrderAmountEntity::getOrderId, OrderInfoEntity::getId)
                .notInSql(OrderInfoEntity::getId, "select order_id from lh_case_info where delete_flag = 0  and current_node != -10  and data_source=1 AND order_id IS NOT NULL")
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .eq(OrderInfoEntity::getPaymentState, 2)
//                .eq(OrderInfoEntity::getIsOverdue, 1)
                .ge(OrderInfoEntity::getCurrentNode, 5000)
                .like(ObjectUtil.isNotEmpty(caseInfoListDTO.getOrderNumber()), OrderInfoEntity::getOrderNumber, caseInfoListDTO.getOrderNumber())
                .like(ObjectUtil.isNotEmpty(caseInfoListDTO.getCustomerName()), OrderInfoEntity::getCustomerName, caseInfoListDTO.getCustomerName());
        List<Integer> roleIds = loginUser.getRoleIds();
        String scopes = loginUser.getScopes();
        boolean hasRole = RoleEnum.ACCOUNTANT.hasRole(roleIds) || RoleEnum.CASHIER.hasRole(roleIds) || RoleEnum.SYS_ADMIN.hasRole(roleIds) || RoleEnum.QUALITY_MANAGER.hasRole(roleIds);
        if (!hasRole || (scopes != null && !scopes.contains("data:all"))) {
            // 数据权限

            dataPermissionService.limitCaseInfoOrderApplyWithOrder(loginUser, wrapper);
        }


//        Page<CaseInfoOrderApplyVO> caseInfoOrderApplyVOPage = caseInfoEntityMapper.selectCaseInfoOrderApplyList(new Page<>(caseInfoListDTO.getPageNum(), caseInfoListDTO.getPageSize()), caseInfoListDTO);

        Page<CaseInfoOrderApplyVO> caseInfoOrderApplyVOPage = orderInfoMapper.selectJoinPage(new Page<>(caseInfoListDTO.getPageNum(), caseInfoListDTO.getPageSize()), CaseInfoOrderApplyVO.class, wrapper);

        List<CaseInfoOrderApplyVO> records = caseInfoOrderApplyVOPage.getRecords();
        log.info("getCaseInfoOrderApplyList records:{}", records);
        records.forEach(item -> {
            if (item.getOrderId() != null) {
                RepaymentOverdueStatusVO repaymentOverdueStatusVO = repaymentService.overdueStatusByOrderId(item.getOrderId());
                if (ObjUtil.isNotNull(repaymentOverdueStatusVO)) {
                    item.setRemainPrincipal(repaymentOverdueStatusVO.getRemainingPrincipal());
                    item.setOverdueDays(repaymentOverdueStatusVO.getNumberOfDaysOverdue());
                }


                FundRepaymentInfoEntity fundRepaymentInfo = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, item.getOrderId())
                        .isNull(FundRepaymentInfoEntity::getActuallyDate)
                        .orderByAsc(FundRepaymentInfoEntity::getRepaymentDate)
                        .last("limit 1"));
                if (ObjUtil.isNotNull(fundRepaymentInfo)) {
                    item.setOverdueDate(fundRepaymentInfo.getRepaymentDate());
                }


            }

        });
        log.info("getCaseInfoOrderApplyList records22:{}", records);
        return caseInfoOrderApplyVOPage;


    }

    @Override
    public void exportAssetOutSourcing(CaseInfoListDTO caseInfoListDTO, HttpServletResponse response, LoginUser loginUser) {
        caseInfoListDTO.setPageNum(1);
        caseInfoListDTO.setPageSize(99999);


        MPJLambdaWrapper<CaseInfoEntity> queryWrapper = new MPJLambdaWrapper<CaseInfoEntity>()


                .selectAs(OrderInfoEntity::getOrderNumber, ExportAssetOutSourcingVO::getOrderNumber)
                .selectAs(OrderInfoEntity::getProductName, ExportAssetOutSourcingVO::getProductName)
                .selectAs(OrderInfoEntity::getCustomerName, ExportAssetOutSourcingVO::getCustomerName)
                .selectAs(OrderInfoEntity::getVehicleNumber, ExportAssetOutSourcingVO::getVehicleNumber)
                .selectAs(OrderInfoEntity::getStoreName, ExportAssetOutSourcingVO::getStoreName)
                .selectAs(OrderInfoEntity::getRegionName, ExportAssetOutSourcingVO::getRegionName)
                .selectAs(OrderInfoEntity::getFundName, ExportAssetOutSourcingVO::getFundName)
                .selectAs(OrderInfoEntity::getPaymentTime, ExportAssetOutSourcingVO::getPaymentTime)
                .selectAs(OrderInfoEntity::getManagerId, ExportAssetOutSourcingVO::getManagerId)


                .selectAs(OrderInfoEntity::getOverdueDays, ExportAssetOutSourcingVO::getOverdueDays)

//                .selectAs(CaseInfoEntity::getPreservationTime, ExportAssetOutSourcingVO::getPreservationTime)

                .select("CASE WHEN t.call_state = 1 THEN '电催' WHEN t.call_state = 0 THEN '未电催' ELSE '未知' END as callState")
                .select("CASE WHEN t.preservation_state = 1 THEN '保全' WHEN t.preservation_state = 0 THEN '未保全' ELSE '未知' END as preservationState")
                .select("CASE WHEN t.visit_state = 0 THEN '外访' WHEN t.visit_state = 1 THEN '外访中' WHEN t.visit_state = 2 THEN '已外访' ELSE '未知' END as visitState")
                .select("CASE WHEN t1.is_overdue =1 THEN '逾期' WHEN t1.is_overdue =0 THEN '还款中' ELSE '未知' END as oderState  ")
                .select("CASE WHEN t.circulation_status =1 THEN '流转成功' WHEN t.circulation_status =0 THEN '未流转' WHEN t.circulation_status =2 THEN '流转中' ELSE '未知' END as circulationStatus")

                .select("CASE WHEN t.case_type =1 THEN '人工下单' WHEN t.case_type =2 THEN '自主下单' ELSE '未知' END as orderWay  ")
//                .selectAs(CaseInfoEntity::getCreateTime, ExportAssetOutSourcingVO::getOrderTime)
                .selectAs(CaseInfoEntity::getDisposalCompany, ExportAssetOutSourcingVO::getDisposalCompany)

                .selectAs(CaseInfoEntity::getInitiator, ExportAssetOutSourcingVO::getInitiator)
                .selectAs(OrderInfoEntity::getFundName, ExportAssetOutSourcingVO::getFundName)

                .selectAs(OrderAmountEntity::getPreAmount, ExportAssetOutSourcingVO::getCreditAmt)
                .leftJoin(OrderInfoEntity.class, OrderInfoEntity::getId, CaseInfoEntity::getOrderId)
                .leftJoin(OrderAmountEntity.class, OrderAmountEntity::getOrderId, CaseInfoEntity::getOrderId)
                .eq(CaseInfoEntity::getDeleteFlag, 0);
        outsourcingService.buildAQueryConditionForYunqi(caseInfoListDTO, queryWrapper);
        log.info("exportAssetOutSourcing：{}", loginUser);
        List<Integer> roleIds = loginUser.getRoleIds();
        String scopes = loginUser.getScopes();
        boolean hasRole = RoleEnum.ACCOUNTANT.hasRole(roleIds) || RoleEnum.CASHIER.hasRole(roleIds) || RoleEnum.SYS_ADMIN.hasRole(roleIds) || RoleEnum.QUALITY_MANAGER.hasRole(roleIds);
        if (!hasRole || (scopes != null && !scopes.contains("data:all"))) {
            // 数据权限

            dataPermissionService.limitCaseInfolistWithOrder(loginUser, queryWrapper);
        }

        List<ExportAssetOutSourcingVO> exportAssetOutSourcingVOS = caseInfoEntityMapper.selectJoinList(ExportAssetOutSourcingVO.class, queryWrapper);

        // 查询经办人信息
        List<Integer> manageIds = exportAssetOutSourcingVOS.stream().map(ExportAssetOutSourcingVO::getManagerId).filter(Objects::nonNull).toList();
        Result<List<UserStoreVO>> listResult = userFeign.searchUserStoreBatch(manageIds);
        if (Result.isSuccess(listResult)) {

            Map<Integer, UserStoreVO> managerInfos =
                    listResult.getData().stream().collect(Collectors.toMap(UserStoreVO::getUserId, item -> item));


            exportAssetOutSourcingVOS.stream().filter(item -> item.getManagerId() != null).forEach(record -> {
                        UserStoreVO userInfoVOS = managerInfos.get(record.getManagerId());
                        record.setBusinessManager(userInfoVOS.getName());


                    }
            );

        }
        try {
            response.addHeader("charset", "utf-8");
            String fileName = String.format("资产委外-%s.xlsx", LocalDate.now());
            String encodeName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
            Optional<MediaType> mediaTypeOptional = MediaTypeFactory.getMediaType(encodeName);
            MediaType mediaType = mediaTypeOptional.orElse(MediaType.APPLICATION_OCTET_STREAM);
            log.info("LoanPerformanceServiceImpl.exportExcel mediaType = {}", JSONUtil.toJsonStr(mediaType));
            response.setContentType(String.valueOf(mediaType));
            log.info("LoanPerformanceServiceImpl.exportExcel response contentType = {}", response.getContentType());
            response.setHeader("Content-disposition", "attachment;filename=" + encodeName);
            String sheetName = "资产委外";
            EasyExcel.write(response.getOutputStream())
                    // 动态头
                    .head(ExportAssetOutSourcingVO.class)
                    .registerWriteHandler(EasyExcelUtil.getHorizontalCellStyleStrategy())
                    .registerWriteHandler(new EasyExcelUtil.CustomCellWriteWidthConfig())
                    .sheet(sheetName)
                    // 表格数据
                    .doWrite(exportAssetOutSourcingVOS);
        } catch (IOException e) {
            log.error("LoanPerformanceServiceImpl.exportExcel error", e);
        }
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Boolean manualAddCaseInfo(AddCaseInfoDTO addCaseInfoDTO, LoginUser loginUser) {
        log.info("manualAddCaseInfo：{}", addCaseInfoDTO);
        String paymentVoucherList = "";
        if (CollUtil.isNotEmpty(addCaseInfoDTO.getAttachmentList())) {
            paymentVoucherList = JSONUtil.toJsonStr(addCaseInfoDTO.getAttachmentList());
        }
        String documentInformationAttachmentList = "";
        if (CollUtil.isNotEmpty(addCaseInfoDTO.getDocumentInformationAttachmentList())) {
            documentInformationAttachmentList = JSONUtil.toJsonStr(addCaseInfoDTO.getDocumentInformationAttachmentList());
        }
        String vehiclePhotoList = "";
        if (CollUtil.isNotEmpty(addCaseInfoDTO.getVehiclePhotoList())) {
            vehiclePhotoList = JSONUtil.toJsonStr(addCaseInfoDTO.getVehiclePhotoList());
        }
        String vehicleInformationList = "";
        if (CollUtil.isNotEmpty(addCaseInfoDTO.getVehicleInformationList())) {
            vehicleInformationList = JSONUtil.toJsonStr(addCaseInfoDTO.getVehicleInformationList());
        }
        String mortgageBeforeRegistrationCertificateList = "";
        if (CollUtil.isNotEmpty(addCaseInfoDTO.getMortgageBeforeRegistrationCertificateList())) {
            mortgageBeforeRegistrationCertificateList = JSONUtil.toJsonStr(addCaseInfoDTO.getMortgageBeforeRegistrationCertificateList());
        }


        if (addCaseInfoDTO.getDataSource().equals(1)) {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(addCaseInfoDTO.getOrderId());
            log.info("manualAddCaseInfo orderId：{}", addCaseInfoDTO.getOrderId());


            Assert.notNull(orderInfoEntity, () -> new BusinessException("订单不存在"));
            Integer deleteFlag = 0;
            //  查看该订单门店是否闭店
            if (ObjUtil.isNotNull(orderInfoEntity.getSourceType()) && orderInfoEntity.getSourceType().equals(0)) {
                ThreePartyDeptDTO threePartyDeptDTO = new ThreePartyDeptDTO();
                threePartyDeptDTO.setDeptId(orderInfoEntity.getDeptId());
                RegionInfoVO regionInfoByDeptId = getRegionInfoByDeptId(threePartyDeptDTO);

                if (ObjUtil.isNotNull(regionInfoByDeptId) && ObjectUtil.isNotNull(regionInfoByDeptId.getDeleteFlag())) {
                    deleteFlag = regionInfoByDeptId.getDeleteFlag();
                }
            }


            //通过CaseId获取案件信息
            if (ObjUtil.isNotNull(addCaseInfoDTO.getCaseId())) {
                CaseInfoEntity caseInfoEntity1 = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                        .eq(CaseInfoEntity::getId, addCaseInfoDTO.getCaseId())
                        .eq(CaseInfoEntity::getDeleteFlag, 0));
                if (ObjUtil.isNotNull(caseInfoEntity1)) {
                    caseInfoEntity1.setDisposalCompany(addCaseInfoDTO.getDisposalCompany());
                    caseInfoEntity1.setRemark(addCaseInfoDTO.getRemark());
                    caseInfoEntity1.setAttachmentList(paymentVoucherList);
                    //  判断是否闭店
                    if (orderInfoEntity.getSourceType() != null && orderInfoEntity.getSourceType().equals(0) && deleteFlag.equals(1)) {
                        caseInfoEntity1.setCurrentNode(getCloseStoreNextNode(CaseApproveNodeEnums.CASE_APPLY));
                    } else if (orderInfoEntity.getSourceType() != null && orderInfoEntity.getSourceType().equals(1)&&ObjUtil.equal(orderInfoEntity.getRegionId(),DeptEnum.DIAN_XING_DA_QU.getId())) {
                        caseInfoEntity1.setCurrentNode(getElectricSalesNextNode(CaseApproveNodeEnums.CASE_APPLY));
                    } else if (orderInfoEntity.getSourceType() != null && (orderInfoEntity.getSourceType().equals(0)||(orderInfoEntity.getSourceType().equals(1)&&!ObjUtil.equal(orderInfoEntity.getRegionId(),DeptEnum.DIAN_XING_DA_QU.getId())))) {
                        caseInfoEntity1.setCurrentNode(getNextNode(CaseApproveNodeEnums.CASE_APPLY));
                    }

//                    }else if(ObjUtil.equal(addCaseInfoDTO.getCirculationType(),3)){
//                        //todo 查看发起人
//                        List<UserDingDeptMappingVO> userDingDeptMappingVOS = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setUserId(loginUser.getUserId()).setUserType(0));
//                        if(CollUtil.isNotEmpty(userDingDeptMappingVOS)&&ObjUtil.equal(userDingDeptMappingVOS.get(0).getDeptId(),DingDingDeptEnum.KEFUBU.getId())){
//                            caseInfoEntity1.setCurrentNode(CaseApproveNodeEnums.SUPERVISOR_APPROVED);
//                        }else {
//                            //  判断是否闭店
//                            if (orderInfoEntity.getSourceType() != null && orderInfoEntity.getSourceType().equals(0) && deleteFlag.equals(1)) {
//                                caseInfoEntity1.setCurrentNode(getCloseStoreNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                            } else if (orderInfoEntity.getSourceType() != null && orderInfoEntity.getSourceType().equals(1)) {
//                                caseInfoEntity1.setCurrentNode(getElectricSalesNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                            } else if (orderInfoEntity.getSourceType() != null && orderInfoEntity.getSourceType().equals(0)) {
//                                caseInfoEntity1.setCurrentNode(getNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                            }
//                        }
//                    }

                    caseInfoEntity1.setCreateBy(loginUser.getUserId());
                    caseInfoEntity1.setInitiator(loginUser.getName());
                    caseInfoEntity1.setCirculationType(addCaseInfoDTO.getCirculationType());
                    caseInfoEntity1.setCirculationStatus(0);
                    caseInfoEntityMapper.updateById(caseInfoEntity1);
                    addCaseInfoApproveRecord(caseInfoEntity1.getId(), addCaseInfoDTO.getRemark(), CaseApproveNodeEnums.CASE_APPLY, getNextNode(CaseApproveNodeEnums.CASE_APPLY), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), null, PayApplicationAuditTypeEnum.YUNQI);
                    //添加以资抵债详情
//                    if(ObjUtil.equal(addCaseInfoDTO.getCirculationType(),3)){
//                        //todo 发起门店主动接回资产处置流程
//                        addCaseInfoDTO.setOrderId(orderInfoEntity.getId());
//                        addCaseInfoOutsourcingDetails(addCaseInfoDTO,loginUser);
//                        c.initiateAssetsOutsourcingAdvanceDingTalkApproval(addCaseInfoDTO,loginUser.getUserId());
//                    }


                }
            } else {
                List<CaseInfoEntity> caseInfoEntities = caseInfoEntityMapper.selectList(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getOrderId, addCaseInfoDTO.getOrderId()).eq(CaseInfoEntity::getDeleteFlag, 0)
                        .eq(CaseInfoEntity::getDataSource, 1).ne(CaseInfoEntity::getCurrentNode, -10));
                if (CollUtil.isNotEmpty(caseInfoEntities) && !caseInfoEntities.isEmpty()) {
                    throw new BusinessException("该订单已有数据，请勿重复添加");
                }


                //生成案件
                CaseInfoEntity caseInfoEntity = new CaseInfoEntity();
                caseInfoEntity.setOrderId(addCaseInfoDTO.getOrderId());
                caseInfoEntity.setDisposalCompany(addCaseInfoDTO.getDisposalCompany());
                caseInfoEntity.setRemark(addCaseInfoDTO.getRemark());
                caseInfoEntity.setAttachmentList(paymentVoucherList);
                caseInfoEntity.setCaseNo(generateCaseNumber());
                caseInfoEntity.setName(orderInfoEntity.getCustomerName());
                if (orderInfoEntity.getSourceType() != null && orderInfoEntity.getSourceType().equals(0) && deleteFlag.equals(1)) {
                    caseInfoEntity.setCurrentNode(getCloseStoreNextNode(CaseApproveNodeEnums.CASE_APPLY));
                } else if (orderInfoEntity.getSourceType() != null && orderInfoEntity.getSourceType().equals(1)&&ObjUtil.equal(orderInfoEntity.getRegionId(),DeptEnum.DIAN_XING_DA_QU.getId())) {
                    caseInfoEntity.setCurrentNode(getElectricSalesNextNode(CaseApproveNodeEnums.CASE_APPLY));
                } else if (orderInfoEntity.getSourceType() != null && (orderInfoEntity.getSourceType().equals(0)||(orderInfoEntity.getSourceType().equals(1)&&!ObjUtil.equal(orderInfoEntity.getRegionId(),DeptEnum.DIAN_XING_DA_QU.getId())))) {
                    caseInfoEntity.setCurrentNode(getNextNode(CaseApproveNodeEnums.CASE_APPLY));
                }
//                }else if(ObjUtil.equal(addCaseInfoDTO.getCirculationType(),3)){
//                  //todo 查看发起人
//                    List<UserDingDeptMappingVO> userDingDeptMappingVOS = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setUserId(loginUser.getUserId()).setUserType(0));
//                    if(CollUtil.isNotEmpty(userDingDeptMappingVOS)&&ObjUtil.equal(userDingDeptMappingVOS.get(0).getDeptId(),DingDingDeptEnum.KEFUBU.getId())){
//                        caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.SUPERVISOR_APPROVED);
//                    }else {
//                        //  判断是否闭店
//                        if (orderInfoEntity.getSourceType() != null && orderInfoEntity.getSourceType().equals(0) && deleteFlag.equals(1)) {
//                            caseInfoEntity.setCurrentNode(getCloseStoreNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                        } else if (orderInfoEntity.getSourceType() != null && orderInfoEntity.getSourceType().equals(1)) {
//                            caseInfoEntity.setCurrentNode(getElectricSalesNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                        } else if (orderInfoEntity.getSourceType() != null && orderInfoEntity.getSourceType().equals(0)) {
//                            caseInfoEntity.setCurrentNode(getNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                        }
//                    }
//                }


                caseInfoEntity.setDeleteFlag(0);
                caseInfoEntity.setCaseType(2);
                caseInfoEntity.setCallState(0);
                caseInfoEntity.setVisitState(0);
                caseInfoEntity.setCreateBy(loginUser.getUserId());
                caseInfoEntity.setAuditStatus(0);
                caseInfoEntity.setInitiator(loginUser.getName());
                caseInfoEntity.setCirculationType(addCaseInfoDTO.getCirculationType());
                caseInfoEntity.setIsReassign(0);
                caseInfoEntity.setStoreId(orderInfoEntity.getDeptId());
                caseInfoEntity.setManagerId(orderInfoEntity.getManagerId());
                caseInfoEntity.setRegionId(orderInfoEntity.getRegionId());
                caseInfoEntity.setTeamId(orderInfoEntity.getTeamId());
                caseInfoEntity.setSourceType(orderInfoEntity.getSourceType());

                caseInfoEntity.setDataSource(addCaseInfoDTO.getDataSource());

                caseInfoEntityMapper.insert(caseInfoEntity);

                //保存记录
                addCaseInfoApproveRecord(caseInfoEntity.getId(), addCaseInfoDTO.getRemark(), CaseApproveNodeEnums.CASE_APPLY, getNextNode(CaseApproveNodeEnums.CASE_APPLY), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), null, PayApplicationAuditTypeEnum.YUNQI);
//                //添加以资抵债详情
//                if(ObjUtil.equal(addCaseInfoDTO.getCirculationType(),3)){
//                    //todo 发起门店主动接回资产处置流程
//                    addCaseInfoDTO.setOrderId(orderInfoEntity.getId());
//                    addCaseInfoDTO.setCaseId(caseInfoEntity.getId());
//                    addCaseInfoOutsourcingDetails(addCaseInfoDTO,loginUser);
////                    kingdeeOutsourcingService.initiateAssetsOutsourcingAdvanceDingTalkApproval(addCaseInfoDTO,loginUser.getUserId());
//
//                }

            }

            //流转类型1 委外外访 2 委外保全 获取车辆定位信息
            if (ObjUtil.isNotNull(addCaseInfoDTO.getCirculationType()) && (addCaseInfoDTO.getCirculationType() == 1 || addCaseInfoDTO.getCirculationType() == 2)) {
                try {
                    OrderVehicleGpsLogDTO reqVLog = new OrderVehicleGpsLogDTO().setOrderId(addCaseInfoDTO.getOrderId()).setOrderSource(1);
                    log.info("CaseInfoServiceImpl.autoGetCarLatestPosition 委外获取定位 开始：{}", reqVLog);
                    orderVehicleGpsLogService.autoGetCarLatestPosition(reqVLog, null);
                } catch (Exception e) {
                    log.error("CaseInfoServiceImpl.autoGetCarLatestPosition 委外获取定位失败：{}", e);
                }
            }
            if (Objects.equals(orderInfoEntity.getFundId(), FundEnum.ZHONG_HENG_TONG_HUI.getValue())) {
                log.info("CaseInfoServiceImpl.manualAddCaseInfo tongHuiCompensateSwitch start entity:{}", orderInfoEntity);
                approveFeign.tongHuiCompensateSwitch(new TongHuiCompensateSwitchDTO().setOrderId(orderInfoEntity.getId()).setCompensateStatus(1));
            }
        } else if (addCaseInfoDTO.getDataSource().equals(2)) {


            //通过CaseId获取案件信息
            if (ObjUtil.isNotNull(addCaseInfoDTO.getCaseId())) {
                CaseInfoEntity caseInfoEntity1 = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                        .eq(CaseInfoEntity::getId, addCaseInfoDTO.getCaseId())
                        .eq(CaseInfoEntity::getDeleteFlag, 0));
                //获取数字化订单信息
                DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderId, caseInfoEntity1.getDigitalOrderId()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0));

                if (ObjUtil.isNotNull(caseInfoEntity1)) {
                    caseInfoEntity1.setDisposalCompany(addCaseInfoDTO.getDisposalCompany());
                    caseInfoEntity1.setRemark(addCaseInfoDTO.getRemark());
                    caseInfoEntity1.setAttachmentList(paymentVoucherList);
                    //  判断是否闭店
                    if (digitalOutsourcingOrderEntity.getSourceType() != null && digitalOutsourcingOrderEntity.getSourceType().equals(0) && digitalOutsourcingOrderEntity.getStoreStatus().equals(1)) {
                        caseInfoEntity1.setCurrentNode(getCloseStoreNextNode(CaseApproveNodeEnums.CASE_APPLY));
                    } else if (digitalOutsourcingOrderEntity.getSourceType() != null && digitalOutsourcingOrderEntity.getSourceType().equals(1)&&ObjUtil.equal(digitalOutsourcingOrderEntity.getRegionId(),DeptEnum.DIAN_XING_DA_QU.getId())) {
                        caseInfoEntity1.setCurrentNode(getElectricSalesNextNode(CaseApproveNodeEnums.CASE_APPLY));
                    } else if (digitalOutsourcingOrderEntity.getSourceType() != null && (digitalOutsourcingOrderEntity.getSourceType().equals(0)||(digitalOutsourcingOrderEntity.getSourceType().equals(1)&&!ObjUtil.equal(digitalOutsourcingOrderEntity.getRegionId(),DeptEnum.DIAN_XING_DA_QU.getId())))) {
                        caseInfoEntity1.setCurrentNode(getNextNode(CaseApproveNodeEnums.CASE_APPLY));
                    }

//                    }else if(ObjUtil.equal(addCaseInfoDTO.getCirculationType(),3)){
//                        //todo 查看发起人
//                        List<UserDingDeptMappingVO> userDingDeptMappingVOS = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setUserId(loginUser.getUserId()).setUserType(0));
//                        if(CollUtil.isNotEmpty(userDingDeptMappingVOS)&&ObjUtil.equal(userDingDeptMappingVOS.get(0).getDeptId(),DingDingDeptEnum.KEFUBU.getId())){
//                            caseInfoEntity1.setCurrentNode(CaseApproveNodeEnums.SUPERVISOR_APPROVED);
//                        }else {
//                            //  判断是否闭店
//                            if (digitalOutsourcingOrderEntity.getSourceType() != null && digitalOutsourcingOrderEntity.getSourceType().equals(0) && digitalOutsourcingOrderEntity.getStoreStatus().equals(1)) {
//                                caseInfoEntity1.setCurrentNode(getCloseStoreNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                            } else if (digitalOutsourcingOrderEntity.getSourceType() != null && digitalOutsourcingOrderEntity.getSourceType().equals(1)) {
//                                caseInfoEntity1.setCurrentNode(getElectricSalesNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                            } else if (digitalOutsourcingOrderEntity.getSourceType() != null && digitalOutsourcingOrderEntity.getSourceType().equals(0)) {
//                                caseInfoEntity1.setCurrentNode(getNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                            }
//                        }
//                    }

                    caseInfoEntity1.setCreateBy(loginUser.getUserId());
                    caseInfoEntity1.setInitiator(loginUser.getName());
                    caseInfoEntity1.setInitiatId(loginUser.getUserId());
                    caseInfoEntity1.setCirculationType(addCaseInfoDTO.getCirculationType());
                    caseInfoEntity1.setCirculationStatus(0);
                    caseInfoEntityMapper.updateById(caseInfoEntity1);
                    addCaseInfoApproveRecord(caseInfoEntity1.getId(), addCaseInfoDTO.getRemark(), CaseApproveNodeEnums.CASE_APPLY, getNextNode(CaseApproveNodeEnums.CASE_APPLY), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), null, PayApplicationAuditTypeEnum.YUNQI);
//                //添加以资抵债详情
//                    if(ObjUtil.equal(addCaseInfoDTO.getCirculationType(),3)){
//                        //todo 发起门店主动接回资产处置流程
//                        addCaseInfoDTO.setCaseId(caseInfoEntity1.getId());
//                        addCaseInfoOutsourcingDetails(addCaseInfoDTO,loginUser);
//                        outsourcingExpansionService.initiateAssetsOutsourcingAdvanceDingTalkApproval(addCaseInfoDTO,loginUser.getUserId());
//                    }
//  通知数字化开始审批
                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity1.getDigitalOrderId());
                    digitalizeWeiwaiOrderStatusDTO.setStatus(1);
                    log.info("通知数字化开始审批 {}", digitalizeWeiwaiOrderStatusDTO);
                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                    log.info("通知数字化开始审批 {}", s);
                    CaseInfoPushRecordEntity caseInfoPushRecordEntity = new CaseInfoPushRecordEntity();
                    caseInfoPushRecordEntity.setCaseInfoId(caseInfoEntity1.getId());
                    caseInfoPushRecordEntity.setPushTime(LocalDateTime.now());
                    caseInfoPushRecordEntity.setPushNode(1);
                    caseInfoPushRecordEntity.setDigitalOrderId(addCaseInfoDTO.getDigitalOrderId());
                    caseInfoPushRecordEntity.setKindeePushResult(s);
                    caseInfoPushRecordEntityMapper.insert(caseInfoPushRecordEntity);
                }
            } else {
                List<CaseInfoEntity> caseInfoEntities = caseInfoEntityMapper.selectList(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getDigitalOrderId, addCaseInfoDTO.getDigitalOrderId()).eq(CaseInfoEntity::getDeleteFlag, 0)
                        .eq(CaseInfoEntity::getDataSource, 2).ne(CaseInfoEntity::getCurrentNode, -10));
                if (CollUtil.isNotEmpty(caseInfoEntities) && !caseInfoEntities.isEmpty()) {
                    throw new BusinessException("该订单已有数据，请勿重复添加");
                }
                //生成案件
                CaseInfoEntity caseInfoEntity = new CaseInfoEntity();
                caseInfoEntity.setDigitalOrderId(addCaseInfoDTO.getDigitalOrderId());
                caseInfoEntity.setDisposalCompany(addCaseInfoDTO.getDisposalCompany());
                caseInfoEntity.setRemark(addCaseInfoDTO.getRemark());
                caseInfoEntity.setAttachmentList(paymentVoucherList);
                caseInfoEntity.setCaseNo(addCaseInfoDTO.getDigitalOrderId());
                caseInfoEntity.setName(addCaseInfoDTO.getCustomerName());
                //  判断是否闭店


//                if(ObjUtil.notEqual(addCaseInfoDTO.getCirculationType(),3)){

                if (addCaseInfoDTO.getSourceType() != null && addCaseInfoDTO.getSourceType().equals(0) && addCaseInfoDTO.getStoreStatus().equals(1)) {
                    caseInfoEntity.setCurrentNode(getCloseStoreNextNode(CaseApproveNodeEnums.CASE_APPLY));
                } else if (addCaseInfoDTO.getSourceType() != null && addCaseInfoDTO.getSourceType().equals(1)&&ObjUtil.equal(addCaseInfoDTO.getRegionId(),DeptEnum.DIAN_XING_DA_QU.getId())) {
                    caseInfoEntity.setCurrentNode(getElectricSalesNextNode(CaseApproveNodeEnums.CASE_APPLY));
                } else if (addCaseInfoDTO.getSourceType() != null   && (addCaseInfoDTO.getSourceType().equals(0)||(addCaseInfoDTO.getSourceType().equals(1)&&!ObjUtil.equal(addCaseInfoDTO.getRegionId(),DeptEnum.DIAN_XING_DA_QU.getId())))) {
                    caseInfoEntity.setCurrentNode(getNextNode(CaseApproveNodeEnums.CASE_APPLY));
                }
//                }else if(ObjUtil.equal(addCaseInfoDTO.getCirculationType(),3)){
//                    //todo 查看发起人
//                    List<UserDingDeptMappingVO> userDingDeptMappingVOS = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setUserId(loginUser.getUserId()).setUserType(0));
//                    if(CollUtil.isNotEmpty(userDingDeptMappingVOS)&&ObjUtil.equal(userDingDeptMappingVOS.get(0).getDeptId(),DingDingDeptEnum.KEFUBU.getId())){
//                        caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.SUPERVISOR_APPROVED);
//                    }else {
//                        //  判断是否闭店
//                        if (addCaseInfoDTO.getSourceType() != null && addCaseInfoDTO.getSourceType().equals(0) && addCaseInfoDTO.getStoreStatus().equals(1)) {
//                            caseInfoEntity.setCurrentNode(getCloseStoreNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                        } else if (addCaseInfoDTO.getSourceType() != null && addCaseInfoDTO.getSourceType().equals(1)) {
//                            caseInfoEntity.setCurrentNode(getElectricSalesNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                        } else if (addCaseInfoDTO.getSourceType() != null && addCaseInfoDTO.getSourceType().equals(0)) {
//                            caseInfoEntity.setCurrentNode(getNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                        }
//                    }
//                }

                caseInfoEntity.setDeleteFlag(0);
                caseInfoEntity.setCaseType(2);
                caseInfoEntity.setCallState(0);
                caseInfoEntity.setVisitState(0);
                caseInfoEntity.setCreateBy(loginUser.getUserId());
                caseInfoEntity.setAuditStatus(0);
                caseInfoEntity.setInitiator(loginUser.getName());
                caseInfoEntity.setCirculationType(addCaseInfoDTO.getCirculationType());
                caseInfoEntity.setIsReassign(0);
                caseInfoEntity.setInitiatId(loginUser.getUserId());
                //设置为当前时间
//                caseInfoEntity.setReassignTime(LocalDateTime.now());
                caseInfoEntity.setStoreId(addCaseInfoDTO.getStoreId());
                caseInfoEntity.setManagerId(addCaseInfoDTO.getManagerId());
                caseInfoEntity.setRegionId(addCaseInfoDTO.getRegionId());

//                //设置截止时间如果是委外保全时效为30 委外外访时效为15天
//                if (addCaseInfoDTO.getCirculationType() != null && addCaseInfoDTO.getCirculationType().equals(1)) {
//                    caseInfoEntity.setDisposalDeadline(LocalDateTime.now().plusDays(30));
//                } else if (addCaseInfoDTO.getCirculationType() != null && addCaseInfoDTO.getCirculationType().equals(2)) {
//                    caseInfoEntity.setDisposalDeadline(LocalDateTime.now().plusDays(15));
//                }
                caseInfoEntity.setDataSource(addCaseInfoDTO.getDataSource());
                caseInfoEntity.setSourceType(addCaseInfoDTO.getSourceType());
                List<String> digitalDeptId = new ArrayList<>();
                digitalDeptId.add(addCaseInfoDTO.getStoreId().toString());
                log.info("获取到数字平台门店id:{}", digitalDeptId);
                DigitalDeptsDTO digitalDeptsDTO = new DigitalDeptsDTO();
                digitalDeptsDTO.setDigitalDeptIds(digitalDeptId);
                Result<List<DeptSyncInfoVO>> digitalStoreIdByDeptId = userFeign.getDigitalStoreIdByDeptId(digitalDeptsDTO);
                log.info("获取到数字平台门店id DeptSyncInfoVO:{}", digitalStoreIdByDeptId);
                if (ObjUtil.isNotNull(digitalStoreIdByDeptId.getData())) {
                    digitalStoreIdByDeptId.getData().forEach(deptSyncInfoVO -> {
                        if (ObjUtil.isNotNull(deptSyncInfoVO) && ObjUtil.isNotNull(deptSyncInfoVO.getLhDeptId())) {

                            caseInfoEntity.setStoreId(deptSyncInfoVO.getLhDeptId());
                            Result<RegionInfoVO> regionInfoVOResult = userFeign.getRegionInfoByDeptId(deptSyncInfoVO.getLhDeptId());
                            if (ObjUtil.isNotNull(regionInfoVOResult.getData())) {
                                caseInfoEntity.setRegionId(regionInfoVOResult.getData().getRegionId());
                                addCaseInfoDTO.setRegionName(regionInfoVOResult.getData().getRegionName());
                            }


                        }
                    });
                }
                if (ObjUtil.isNotNull(addCaseInfoDTO.getManagerMobile())) {
                    List<String> users = new ArrayList<>();
                    users.add(addCaseInfoDTO.getManagerMobile());
                    Result<List<UserInfoVO>> listResult = userFeign.mobile2UserId(users);
                    if (ObjUtil.isNotNull(listResult.getData())) {
                        listResult.getData().forEach(userInfoVO -> {
                            caseInfoEntity.setManagerId(userInfoVO.getUserId());
                            caseInfoEntity.setTeamId(userInfoVO.getTeamId());
                        });

                    }
                }


                caseInfoEntityMapper.insert(caseInfoEntity);
//                //以资抵债详情
//                if(ObjUtil.equal(addCaseInfoDTO.getCirculationType(),3)){
//                    //todo 发起门店主动接回资产处置流程
//                    addCaseInfoDTO.setCaseId( caseInfoEntity.getId());
//                    addCaseInfoOutsourcingDetails(addCaseInfoDTO,loginUser);
////                    kingdeeOutsourcingService.initiateAssetsOutsourcingAdvanceDingTalkApproval(addCaseInfoDTO,loginUser.getUserId());
//                }
                DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity1 = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderId, addCaseInfoDTO.getDigitalOrderId()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0));

                //获取数字化imei编号
                DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO1=new DigitalizeWeiwaiOrderStatusDTO();
                digitalizeWeiwaiOrderStatusDTO1.setOrder_id(addCaseInfoDTO.getOrderNumber());
                Result<DigitalizeImeiVO> digitalizeImeiVOResult= digitalizeFeign.getDigitalizeImei(digitalizeWeiwaiOrderStatusDTO1);

                if (ObjUtil.isNull(digitalOutsourcingOrderEntity1)) {
                    //添加数字化信息
                    DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = new DigitalOutsourcingOrderEntity();
                    digitalOutsourcingOrderEntity.setOrderNo(addCaseInfoDTO.getOrderNumber());
                    digitalOutsourcingOrderEntity.setCustomerName(addCaseInfoDTO.getCustomerName());
                    digitalOutsourcingOrderEntity.setFundName(addCaseInfoDTO.getFundName());
                    digitalOutsourcingOrderEntity.setProductName(addCaseInfoDTO.getProductName());
                    digitalOutsourcingOrderEntity.setVehicleNumber(addCaseInfoDTO.getVehicleNumber());
                    digitalOutsourcingOrderEntity.setStoreName(addCaseInfoDTO.getStoreName());
                    digitalOutsourcingOrderEntity.setContractAmount(addCaseInfoDTO.getContractAmount());
                    digitalOutsourcingOrderEntity.setRegionName(addCaseInfoDTO.getRegionName());
                    digitalOutsourcingOrderEntity.setRemainingPrincipal(addCaseInfoDTO.getRemainPrincipal());
                    digitalOutsourcingOrderEntity.setOverdueDate(addCaseInfoDTO.getOverdueDate());
                    // 示例字符串
                    // 定义日期格式（与字符串格式匹配）
                    DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                    // 将字符串解析为 LocalDate
                    LocalDate localDate = LocalDate.parse(addCaseInfoDTO.getPaymentTime(), dateFormatter);

                    // 将 LocalDate 转换为 LocalDateTime，时间部分设置为 00:00:00
                    LocalDateTime localDateTime = localDate.atStartOfDay();
                    digitalOutsourcingOrderEntity.setPaymentTime(localDateTime);
                    digitalOutsourcingOrderEntity.setBusinessManager(addCaseInfoDTO.getBusinessManager());
                    digitalOutsourcingOrderEntity.setRedemptionTime(addCaseInfoDTO.getRedemptionTime());
                    digitalOutsourcingOrderEntity.setRedemptionAmount(addCaseInfoDTO.getRedemptionAmount());
                    digitalOutsourcingOrderEntity.setStoreStatus(addCaseInfoDTO.getStoreStatus());
                    digitalOutsourcingOrderEntity.setDataSource(addCaseInfoDTO.getDataSource());

                    digitalOutsourcingOrderEntity.setTotalOverdueAmount(addCaseInfoDTO.getOverdueAmount());
                    digitalOutsourcingOrderEntity.setCustomerPhone(addCaseInfoDTO.getCustomerPhone());

                    digitalOutsourcingOrderEntity.setCreditAmount(addCaseInfoDTO.getCreditAmt());
                    digitalOutsourcingOrderEntity.setOverdueDays(addCaseInfoDTO.getOverdueDays());
                    digitalOutsourcingOrderEntity.setVehicleBrand(addCaseInfoDTO.getVehicleBrand());
                    digitalOutsourcingOrderEntity.setVin(addCaseInfoDTO.getVin());
                    digitalOutsourcingOrderEntity.setRegisterDate(addCaseInfoDTO.getRegisterDate());
                    digitalOutsourcingOrderEntity.setIdNumber(addCaseInfoDTO.getIdNumber());
                    digitalOutsourcingOrderEntity.setRepayMethod(addCaseInfoDTO.getRepayMethod());
                    digitalOutsourcingOrderEntity.setDeptId(addCaseInfoDTO.getStoreId().toString());
                    digitalOutsourcingOrderEntity.setManagerId(addCaseInfoDTO.getManagerId());
                    digitalOutsourcingOrderEntity.setRegionId(addCaseInfoDTO.getRegionId());
                    digitalOutsourcingOrderEntity.setVehicleType(addCaseInfoDTO.getVehicleType());
                    digitalOutsourcingOrderEntity.setOrderId(addCaseInfoDTO.getDigitalOrderId());
                    digitalOutsourcingOrderEntity.setLoanContractNumber(addCaseInfoDTO.getLoanContractNumber());
                    digitalOutsourcingOrderEntity.setPrincipalAmount(addCaseInfoDTO.getPrincipalAmount());
                    digitalOutsourcingOrderEntity.setInterestAmount(addCaseInfoDTO.getInterestAmount());
                    digitalOutsourcingOrderEntity.setSourceType(addCaseInfoDTO.getSourceType());
                    digitalOutsourcingOrderEntity.setOverdueStatus(addCaseInfoDTO.getOverdueStatus());
                    digitalOutsourcingOrderEntity.setOverdueStatusDesc(addCaseInfoDTO.getOverdueStatusDesc());
                    digitalOutsourcingOrderEntity.setApprovalAmount(addCaseInfoDTO.getApprovalAmount());
                    digitalOutsourcingOrderEntity.setTerm(addCaseInfoDTO.getTerm());
                    digitalOutsourcingOrderEntity.setHaveTerm(addCaseInfoDTO.getHaveTerm());
                    if(ObjUtil.equal(digitalizeImeiVOResult.getCode(),"000000")&&ObjUtil.isNotEmpty(digitalizeImeiVOResult.getData())){
                        digitalOutsourcingOrderEntity.setGpsNumber(digitalizeImeiVOResult.getData().getImeis());
                    }

                    digitalOutsourcingOrderEntityMapper.insert(digitalOutsourcingOrderEntity);
                }

                //保存记录
                addCaseInfoApproveRecord(caseInfoEntity.getId(), addCaseInfoDTO.getRemark(), CaseApproveNodeEnums.CASE_APPLY, getNextNode(CaseApproveNodeEnums.CASE_APPLY), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), null, PayApplicationAuditTypeEnum.YUNQI);
//  通知数字化开始审批
                DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                digitalizeWeiwaiOrderStatusDTO.setOrder_id(addCaseInfoDTO.getDigitalOrderId());
                digitalizeWeiwaiOrderStatusDTO.setStatus(1);
                log.info("通知数字化开始审批 {}", digitalizeWeiwaiOrderStatusDTO);
                String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                log.info("通知数字化开始审批 {}", s);
                CaseInfoPushRecordEntity caseInfoPushRecordEntity = new CaseInfoPushRecordEntity();
                caseInfoPushRecordEntity.setCaseInfoId(caseInfoEntity.getId());
                caseInfoPushRecordEntity.setPushTime(LocalDateTime.now());
                caseInfoPushRecordEntity.setPushNode(1);
                caseInfoPushRecordEntity.setDigitalOrderId(addCaseInfoDTO.getDigitalOrderId());
                caseInfoPushRecordEntity.setKindeePushResult(s);
                caseInfoPushRecordEntityMapper.insert(caseInfoPushRecordEntity);

            }
            //流转类型1 委外外访 2 委外保全 获取车辆定位信息
            if (ObjUtil.isNotNull(addCaseInfoDTO.getCirculationType()) && (addCaseInfoDTO.getCirculationType() == 1 || addCaseInfoDTO.getCirculationType() == 2)) {
                try {
                    OrderVehicleGpsLogDTO reqVLog = new OrderVehicleGpsLogDTO().setOrderNumber(addCaseInfoDTO.getDigitalOrderId()).setOrderSource(2);
                    log.info("CaseInfoServiceImpl.autoGetCarLatestPosition 委外获取定位 开始：{}", reqVLog);
                    orderVehicleGpsLogService.autoGetCarLatestPosition(reqVLog, null);
                } catch (Exception e) {
                    log.error("CaseInfoServiceImpl.autoGetCarLatestPosition 委外获取定位失败：{}", e);
                }
            }
        } else if (addCaseInfoDTO.getDataSource().equals(3)) {


            //通过CaseId获取案件信息
            if (ObjUtil.isNotNull(addCaseInfoDTO.getCaseId())) {
                CaseInfoEntity caseInfoEntity1 = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                        .eq(CaseInfoEntity::getId, addCaseInfoDTO.getCaseId())
                        .eq(CaseInfoEntity::getDeleteFlag, 0));
                if (ObjUtil.isNotNull(caseInfoEntity1)) {
                    //  查看金蝶信息
//                    Result<DigitizedOverdueOrdersVO> digitizedOverdueOrdersVOResult = dataFeign.getkingdeeOutsourcingOrderDetail(caseInfoEntity1.getDigitalOrderId());
//                    log.info("金蝶信息 {}", digitizedOverdueOrdersVOResult);
//                    DigitizedOverdueOrdersVO overdueOrdersVOResultData = new DigitizedOverdueOrdersVO();
//                    if (Result.isSuccess(digitizedOverdueOrdersVOResult)) {
//                        overdueOrdersVOResultData = digitizedOverdueOrdersVOResult.getData();
//                    }
//                    //通过部门名称获取部门信息
//                    DeptDetailVO deptDetailVO = new DeptDetailVO();
//                    if (ObjUtil.isNotEmpty(overdueOrdersVOResultData) && ObjUtil.isNotEmpty(overdueOrdersVOResultData.getStoreName())) {
//                        Result<DeptDetailVO> deptDetailVOByDeptName = userFeign.getDeptDetailVOByDeptName(overdueOrdersVOResultData.getStoreName());
//                        if (Result.isSuccess(deptDetailVOByDeptName)) {
//                            deptDetailVO = deptDetailVOByDeptName.getData();
//                        }
//                    }
//                    if (ObjUtil.isEmpty(deptDetailVO)) {
//                        throw new BusinessException("未查询到部门信息,暂不支持添加委外");
//                    }
                    DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderId, caseInfoEntity1.getDigitalOrderId()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0).eq(DigitalOutsourcingOrderEntity::getDataSource, 3).orderByDesc(DigitalOutsourcingOrderEntity::getCreateTime).last("limit 1"));
                    Integer deleteFlag = 0;
                    deleteFlag = digitalOutsourcingOrderEntity.getStoreStatus();
                    caseInfoEntity1.setDisposalCompany(addCaseInfoDTO.getDisposalCompany());
                    caseInfoEntity1.setRemark(addCaseInfoDTO.getRemark());
                    caseInfoEntity1.setAttachmentList(paymentVoucherList);
                    //  金蝶都是门店
                    if (deleteFlag.equals(1)) {
                        caseInfoEntity1.setCurrentNode(getCloseStoreNextNode(CaseApproveNodeEnums.CASE_APPLY));
                    } else {
                        caseInfoEntity1.setCurrentNode(getNextNode(CaseApproveNodeEnums.CASE_APPLY));
                    }


//                    }else if(ObjUtil.equal(addCaseInfoDTO.getCirculationType(),3)){
//                        //todo 查看发起人
//                        List<UserDingDeptMappingVO> userDingDeptMappingVOS = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setUserId(loginUser.getUserId()).setUserType(0));
//                        if(CollUtil.isNotEmpty(userDingDeptMappingVOS)&&ObjUtil.equal(userDingDeptMappingVOS.get(0).getDeptId(),DingDingDeptEnum.KEFUBU.getId())){
//                            caseInfoEntity1.setCurrentNode(CaseApproveNodeEnums.SUPERVISOR_APPROVED);
//                        }else {
//                            //  判断是否闭店
//                            if (deleteFlag.equals(1)) {
//                                caseInfoEntity1.setCurrentNode(getCloseStoreNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                            } else {
//                                caseInfoEntity1.setCurrentNode(getNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                            }
//
//                        }
//                    }

                    caseInfoEntity1.setCreateBy(loginUser.getUserId());
                    caseInfoEntity1.setInitiator(loginUser.getName());
                    caseInfoEntity1.setInitiatId(loginUser.getUserId());
                    caseInfoEntity1.setCirculationType(addCaseInfoDTO.getCirculationType());
                    caseInfoEntity1.setCirculationStatus(0);
                    caseInfoEntityMapper.updateById(caseInfoEntity1);
                    addCaseInfoApproveRecord(caseInfoEntity1.getId(), addCaseInfoDTO.getRemark(), CaseApproveNodeEnums.CASE_APPLY, getNextNode(CaseApproveNodeEnums.CASE_APPLY), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), null, PayApplicationAuditTypeEnum.YUNQI);
//                    //以资抵债详情
//                    if(ObjUtil.equal(addCaseInfoDTO.getCirculationType(),3)){
//                        //todo 发起门店主动接回资产处置流程
//                        addCaseInfoDTO.setCaseId( caseInfoEntity1.getId());
//                        addCaseInfoOutsourcingDetails(addCaseInfoDTO,loginUser);
//                    }


                }
                //修改附件
                if (ObjUtil.isNotEmpty(documentInformationAttachmentList)) {
                    DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderId, addCaseInfoDTO.getDigitalOrderId()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0).last("limit 1"));
                    digitalOutsourcingOrderEntity.setDocumentInformationAttachment(documentInformationAttachmentList);
                }

            } else {
                List<CaseInfoEntity> caseInfoEntities = caseInfoEntityMapper.selectList(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getDigitalOrderId, addCaseInfoDTO.getDigitalOrderId()).eq(CaseInfoEntity::getDeleteFlag, 0)
                        .eq(CaseInfoEntity::getDataSource, 3).ne(CaseInfoEntity::getCurrentNode, -10));
                if (CollUtil.isNotEmpty(caseInfoEntities) && !caseInfoEntities.isEmpty()) {
                    throw new BusinessException("该订单已有数据，请勿重复添加");
                }
                //  查看金蝶信息
                Result<DigitizedOverdueOrdersVO> digitizedOverdueOrdersVOResult = dataFeign.getkingdeeOutsourcingOrderDetail(addCaseInfoDTO.getDigitalOrderId());
                DigitizedOverdueOrdersVO overdueOrdersVOResultData = new DigitizedOverdueOrdersVO();
                if (Result.isSuccess(digitizedOverdueOrdersVOResult)) {
                    overdueOrdersVOResultData = digitizedOverdueOrdersVOResult.getData();
                }
                log.info("金蝶信息：{}", overdueOrdersVOResultData);
                //通过部门名称获取部门信息
                DeptDetailVO deptDetailVO = new DeptDetailVO();
//                if (ObjUtil.isNotEmpty(overdueOrdersVOResultData) && ObjUtil.isNotEmpty(overdueOrdersVOResultData.getStoreName())) {
//                    Result<DeptDetailVO> deptDetailVOByDeptName = userFeign.getDeptDetailVOByDeptName(overdueOrdersVOResultData.getStoreName());
//                    if (Result.isSuccess(deptDetailVOByDeptName)) {
//                        deptDetailVO = deptDetailVOByDeptName.getData();
//                    }
//                }
                int storeId = 0;
                int regionId = 0;

                if (ObjUtil.isNotEmpty(overdueOrdersVOResultData) && ObjUtil.isNotEmpty(overdueOrdersVOResultData.getDeptId())) {
                    Result<DeptDetailVO> deptDetailVOResult = userFeign.getDeptDetailVOByjdDeptId(overdueOrdersVOResultData.getDeptId());
                    if (Result.isSuccess(deptDetailVOResult)) {
                        deptDetailVO = deptDetailVOResult.getData();

                        storeId = deptDetailVO.getId();
                        Result<RegionInfoVO> regionInfoVOResult = userFeign.getRegionInfoByDeptId(deptDetailVO.getId());
                        if (ObjUtil.isNotNull(regionInfoVOResult.getData())) {
                            regionId = regionInfoVOResult.getData().getRegionId();
                            addCaseInfoDTO.setRegionName(regionInfoVOResult.getData().getRegionName());
                        }


                    } else {
                        throw new BusinessException("未查询到部门信息,暂不支持添加委外");
                    }
                }
//                if (ObjUtil.isEmpty(deptDetailVO)) {
//                    throw new BusinessException("未查询到部门信息,暂不支持添加委外");
//                }
                Integer deleteFlag = 0;
                deleteFlag = deptDetailVO.getStatus();
                //生成案件
                CaseInfoEntity caseInfoEntity = new CaseInfoEntity();
                caseInfoEntity.setDigitalOrderId(addCaseInfoDTO.getDigitalOrderId());
                caseInfoEntity.setDisposalCompany(addCaseInfoDTO.getDisposalCompany());
                caseInfoEntity.setRemark(addCaseInfoDTO.getRemark());
                caseInfoEntity.setAttachmentList(paymentVoucherList);
                caseInfoEntity.setCaseNo(addCaseInfoDTO.getDigitalOrderId());
                caseInfoEntity.setName(overdueOrdersVOResultData.getCustomerName());
                //  判断是否闭店
                //  金蝶都是门店

//                if(ObjUtil.notEqual(addCaseInfoDTO.getCirculationType(),3)){
                if (ObjUtil.equal(deleteFlag, 1)) {
                    caseInfoEntity.setCurrentNode(getCloseStoreNextNode(CaseApproveNodeEnums.CASE_APPLY));
                } else {
                    caseInfoEntity.setCurrentNode(getNextNode(CaseApproveNodeEnums.CASE_APPLY));
                }

//                }else if(ObjUtil.equal(addCaseInfoDTO.getCirculationType(),3)){
//                    //todo 查看发起人
//                    List<UserDingDeptMappingVO> userDingDeptMappingVOS = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setUserId(loginUser.getUserId()).setUserType(0));
//                    if(CollUtil.isNotEmpty(userDingDeptMappingVOS)&&ObjUtil.equal(userDingDeptMappingVOS.get(0).getDeptId(),DingDingDeptEnum.KEFUBU.getId())){
//                        caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.SUPERVISOR_APPROVED);
//                    }else {
//                        //  判断是否闭店
//                        if (deleteFlag.equals(1)) {
//                            caseInfoEntity.setCurrentNode(getCloseStoreNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                        } else {
//                            caseInfoEntity.setCurrentNode(getNextNode(CaseApproveNodeEnums.CASE_APPLY));
//                        }
//
//                    }
//                }

                caseInfoEntity.setDeleteFlag(0);
                caseInfoEntity.setCaseType(2);
                caseInfoEntity.setCallState(0);
                caseInfoEntity.setVisitState(0);
                caseInfoEntity.setCreateBy(loginUser.getUserId());
                caseInfoEntity.setAuditStatus(0);
                caseInfoEntity.setInitiator(loginUser.getName());
                caseInfoEntity.setCirculationType(addCaseInfoDTO.getCirculationType());
                caseInfoEntity.setIsReassign(0);
                caseInfoEntity.setInitiatId(loginUser.getUserId());
                //设置为当前时间
//                caseInfoEntity.setReassignTime(LocalDateTime.now());
                caseInfoEntity.setStoreId(storeId);
                //todo 金蝶门店经理id
                caseInfoEntity.setManagerId(addCaseInfoDTO.getManagerId());
                caseInfoEntity.setRegionId(regionId);

                caseInfoEntity.setDataSource(addCaseInfoDTO.getDataSource());
                caseInfoEntity.setSourceType(0);

                caseInfoEntity.setManagerId(addCaseInfoDTO.getManagerId());

                caseInfoEntityMapper.insert(caseInfoEntity);
//                //以资抵债详情
//                if(ObjUtil.equal(addCaseInfoDTO.getCirculationType(),3)){
//                    //todo 发起门店主动接回资产处置流程
//                    addCaseInfoDTO.setCaseId( caseInfoEntity.getId());
//                    addCaseInfoOutsourcingDetails(addCaseInfoDTO,loginUser);
//                }
                DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity1 = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderId, addCaseInfoDTO.getDigitalOrderId()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0).eq(DigitalOutsourcingOrderEntity::getDataSource, 3));
                if (ObjUtil.isNull(digitalOutsourcingOrderEntity1)) {
                    //添加金蝶信息
                    DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = new DigitalOutsourcingOrderEntity();
                    digitalOutsourcingOrderEntity.setOrderNo(overdueOrdersVOResultData.getOrderId());
                    digitalOutsourcingOrderEntity.setCustomerName(overdueOrdersVOResultData.getCustomerName());
                    digitalOutsourcingOrderEntity.setFundName(overdueOrdersVOResultData.getFundName());
                    digitalOutsourcingOrderEntity.setProductName(overdueOrdersVOResultData.getProductName());
                    digitalOutsourcingOrderEntity.setVehicleNumber(overdueOrdersVOResultData.getVehicleNumber());
                    digitalOutsourcingOrderEntity.setStoreName(overdueOrdersVOResultData.getStoreName());
                    digitalOutsourcingOrderEntity.setContractAmount(overdueOrdersVOResultData.getContractAmount());
//                    digitalOutsourcingOrderEntity.setRegionName(ObjUtil.equal(overdueOrdersVOResultData.getRegionName(),"直营大区")?"创新大区":overdueOrdersVOResultData.getRegionName());
                    if (ObjUtil.isNotEmpty(overdueOrdersVOResultData.getRegionName()) && "直营大区".equals(overdueOrdersVOResultData.getRegionName())) {
                        digitalOutsourcingOrderEntity.setRegionName("创新大区");
                    } else {
                        digitalOutsourcingOrderEntity.setRegionName(overdueOrdersVOResultData.getRegionName());
                    }


                    digitalOutsourcingOrderEntity.setRemainingPrincipal(overdueOrdersVOResultData.getRemainingPrincipal());
                    digitalOutsourcingOrderEntity.setOverdueDate(overdueOrdersVOResultData.getOverdueDate());
//                        // 示例字符串
//                        // 定义日期格式（与字符串格式匹配）
//                        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//
//                        // 将字符串解析为 LocalDate
//                        LocalDate localDate = LocalDate.parse(overdueOrdersVOResultData.getPaymentTime(), dateFormatter);
//
//                        // 将 LocalDate 转换为 LocalDateTime，时间部分设置为 00:00:00
//                        LocalDateTime localDateTime = localDate.atStartOfDay();
                    digitalOutsourcingOrderEntity.setPaymentTime(overdueOrdersVOResultData.getPaymentTime());
                    digitalOutsourcingOrderEntity.setBusinessManager(overdueOrdersVOResultData.getBusinessManager());
                    digitalOutsourcingOrderEntity.setRedemptionTime(overdueOrdersVOResultData.getRedemptionTime());
                    if (ObjUtil.isNotEmpty(overdueOrdersVOResultData.getRedemptionAmt())) {
                        digitalOutsourcingOrderEntity.setRedemptionAmount(overdueOrdersVOResultData.getRedemptionAmt().toString());
                    }

                    digitalOutsourcingOrderEntity.setStoreStatus(deleteFlag);
                    digitalOutsourcingOrderEntity.setDataSource(addCaseInfoDTO.getDataSource());

                    digitalOutsourcingOrderEntity.setTotalOverdueAmount(overdueOrdersVOResultData.getTotalOverdueAmount());
                    digitalOutsourcingOrderEntity.setCustomerPhone(overdueOrdersVOResultData.getCustomerPhone());

                    digitalOutsourcingOrderEntity.setCreditAmount(overdueOrdersVOResultData.getCreditAmt());
                    digitalOutsourcingOrderEntity.setOverdueDays(overdueOrdersVOResultData.getOverdueDays());
                    digitalOutsourcingOrderEntity.setVehicleBrand(overdueOrdersVOResultData.getVehicleBrand());
                    digitalOutsourcingOrderEntity.setVin(overdueOrdersVOResultData.getVin());
                    digitalOutsourcingOrderEntity.setRegisterDate(overdueOrdersVOResultData.getVehicleRegisterTime());
                    digitalOutsourcingOrderEntity.setIdNumber(overdueOrdersVOResultData.getIdNumber());
                    digitalOutsourcingOrderEntity.setRepayMethod(overdueOrdersVOResultData.getRepayMethod());
                    if (ObjUtil.isNotEmpty(deptDetailVO.getId())) {
                        digitalOutsourcingOrderEntity.setDeptId(deptDetailVO.getId().toString());
                    }
                    digitalOutsourcingOrderEntity.setManagerId(0);
                    digitalOutsourcingOrderEntity.setRegionId(deptDetailVO.getParentId());
                    digitalOutsourcingOrderEntity.setVehicleType(overdueOrdersVOResultData.getVehicleType());
                    digitalOutsourcingOrderEntity.setOrderId(addCaseInfoDTO.getDigitalOrderId());
                    digitalOutsourcingOrderEntity.setLoanContractNumber(overdueOrdersVOResultData.getLoanContractNumber());
                    if (ObjUtil.isNotEmpty(overdueOrdersVOResultData.getRemainingPrincipal())) {
                        digitalOutsourcingOrderEntity.setPrincipalAmount(overdueOrdersVOResultData.getRemainingPrincipal().toString());
                    }
                    digitalOutsourcingOrderEntity.setInterestAmount(overdueOrdersVOResultData.getInterestAmount());
                    digitalOutsourcingOrderEntity.setSourceType(0);
                    if (ObjUtil.equal(overdueOrdersVOResultData.getOverdueStatusDesc(), "逾期")) {
                        digitalOutsourcingOrderEntity.setOverdueStatus(1);
                    } else {
                        digitalOutsourcingOrderEntity.setOverdueStatus(0);
                    }

                    digitalOutsourcingOrderEntity.setOverdueStatusDesc(overdueOrdersVOResultData.getOverdueStatusDesc());
                    digitalOutsourcingOrderEntity.setApprovalAmount(overdueOrdersVOResultData.getApprovalAmount());
                    digitalOutsourcingOrderEntity.setTerm(overdueOrdersVOResultData.getTerm());
                    digitalOutsourcingOrderEntity.setHaveTerm(overdueOrdersVOResultData.getHaveTerm());
                    digitalOutsourcingOrderEntity.setDocumentInformationAttachment(documentInformationAttachmentList);
                    digitalOutsourcingOrderEntity.setLoanRate(overdueOrdersVOResultData.getLoanRate());
                    if (ObjUtil.isNotEmpty(overdueOrdersVOResultData.getLoanExpiryDate())) {
                        // 定义日期格式（与字符串格式匹配）
                        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");

                        LocalDateTime dateTime = LocalDateTime.parse(overdueOrdersVOResultData.getLoanExpiryDate(), dateFormatter);
                        digitalOutsourcingOrderEntity.setLoanExpiryDate(dateTime);
                    }
//                    digitalOutsourcingOrderEntity.setBindUserAccount(overdueOrdersVOResultData.getStoreName());
//                    digitalOutsourcingOrderEntity.setChildBindUserAccount(overdueOrdersVOResultData.getStoreName());
                    digitalOutsourcingOrderEntity.setInstallCarId(overdueOrdersVOResultData.getCarId());

                    digitalOutsourcingOrderEntityMapper.insert(digitalOutsourcingOrderEntity);
                }

                //保存记录
                addCaseInfoApproveRecord(caseInfoEntity.getId(), addCaseInfoDTO.getRemark(), CaseApproveNodeEnums.CASE_APPLY, getNextNode(CaseApproveNodeEnums.CASE_APPLY), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), null, PayApplicationAuditTypeEnum.YUNQI);
//  todo 通知金蝶开始审批
                //流转类型1 委外外访 2 委外保全 获取车辆定位信息
                if (ObjUtil.isNotNull(addCaseInfoDTO.getCirculationType()) && (addCaseInfoDTO.getCirculationType() == 1 || addCaseInfoDTO.getCirculationType() == 3)&&ObjUtil.isNotEmpty(overdueOrdersVOResultData.getCarId())) {
                    try {
                        //获取金蝶的imei数据
                        orderVehicleGpsLogService.getCarInfo(Integer.parseInt(overdueOrdersVOResultData.getCarId()), digitalOutsourcingOrderEntity1.getId());
                        OrderVehicleGpsLogDTO reqVLog = new OrderVehicleGpsLogDTO().setOrderNumber(addCaseInfoDTO.getDigitalOrderId()).setOrderSource(3);
                        log.info("CaseInfoServiceImpl.autoGetCarLatestPosition 委外获取定位 开始：{}", reqVLog);
                        orderVehicleGpsLogService.autoGetCarLatestPosition(reqVLog, null);
                    } catch (Exception e) {
                        log.error("CaseInfoServiceImpl.autoGetCarLatestPosition 委外获取定位失败：{}", e);
                    }
                }

            }
        }


        return true;
    }

    //添加以资抵债详情
    public void addCaseInfoOutsourcingDetails(AddCaseInfoDTO addCaseInfoDTO, LoginUser loginUser) {
        //todo 保存以资抵债详情

        OutsourcingDetailsEntity outsourcingDetailsEntity = outsourcingDetailsEntityMapper.selectOne(new LambdaQueryWrapper<OutsourcingDetailsEntity>().eq(OutsourcingDetailsEntity::getCaseId, addCaseInfoDTO.getCaseId()).eq(OutsourcingDetailsEntity::getDeleteFlag, 0), false);
        if (ObjUtil.isNotEmpty(outsourcingDetailsEntity)) {
            outsourcingDetailsEntity.setAreaPrice(addCaseInfoDTO.getAreaPrice());
            outsourcingDetailsEntity.setParkingAmount(addCaseInfoDTO.getParkingAmount());
            outsourcingDetailsEntity.setParkingCity(addCaseInfoDTO.getParkingCity());
            outsourcingDetailsEntity.setCollateralReason(addCaseInfoDTO.getCollateralReason());
            outsourcingDetailsEntityMapper.updateById(outsourcingDetailsEntity);

        } else {
            outsourcingDetailsEntity = new OutsourcingDetailsEntity();
            outsourcingDetailsEntity.setAreaPrice(addCaseInfoDTO.getAreaPrice());
            outsourcingDetailsEntity.setParkingAmount(addCaseInfoDTO.getParkingAmount());
            outsourcingDetailsEntity.setParkingCity(addCaseInfoDTO.getParkingCity());
            outsourcingDetailsEntity.setCollateralReason(addCaseInfoDTO.getCollateralReason());
            outsourcingDetailsEntity.setCaseId(addCaseInfoDTO.getCaseId());
            outsourcingDetailsEntity.setOrderId(addCaseInfoDTO.getOrderId());
            outsourcingDetailsEntityMapper.insert(outsourcingDetailsEntity);

        }
        OutsourcingDetailsDTO outsourcingDetailsDTO = new OutsourcingDetailsDTO();
        outsourcingDetailsDTO.setAreaPrice(addCaseInfoDTO.getAreaPrice());
        outsourcingDetailsDTO.setParkingAmount(addCaseInfoDTO.getParkingAmount());
        outsourcingDetailsDTO.setParkingCity(addCaseInfoDTO.getParkingCity());
        outsourcingDetailsDTO.setCollateralReason(addCaseInfoDTO.getCollateralReason());
        outsourcingDetailsDTO.setCaseId(addCaseInfoDTO.getCaseId());
        outsourcingDetailsDTO.setOrderId(addCaseInfoDTO.getOrderId());
        addOutsourcingDetailsReocrd(outsourcingDetailsDTO, loginUser);

    }

    //查看部门信息
    public RegionInfoVO getRegionInfoByDeptId(ThreePartyDeptDTO threePartyDeptDTO) {
        Result<RegionInfoVO> regionInfoVOResult = userFeign.getDeptInfoByDeptId(threePartyDeptDTO);
        RegionInfoVO regionInfoVO = new RegionInfoVO();
        if (Result.isSuccess(regionInfoVOResult)) {
            RegionInfoVO data = regionInfoVOResult.getData();
        }
        return regionInfoVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean approveCaseInfo(ApprovalCaseDTO approvalCaseDTO, LoginUser loginUser, boolean isDingTask) {
        log.info("approveCaseInfo approvalCaseDTO = {}", JSONUtil.toJsonStr(approvalCaseDTO));
        if (CollUtil.isNotEmpty(approvalCaseDTO.getRequestIds())) {
            approvalCaseDTO.getRequestIds().forEach(requestId -> {
                CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getId, requestId).eq(CaseInfoEntity::getDeleteFlag, 0));
                Assert.notNull(caseInfoEntity, () -> new BusinessException("资外订单不存在"));
                log.info("CaseInfoServiceImpl.approveCaseInfo caseInfoEntity = {}", caseInfoEntity);
                //获取当前节点
                CaseApproveNodeEnums currentNode = caseInfoEntity.getCurrentNode();
                log.info("CaseInfoServiceImpl.approveCaseInfo currentNode = {}", currentNode);
                if (ObjUtil.isNotNull(currentNode) && currentNode.getCode() == CaseApproveNodeEnums.SUCCESS.getCode()) {
                    throw new BusinessException("该委外已审批完成");
                }
                //查看部门信息
                Integer deleteFlag = 0;

                Integer sourceType = 0;
                Integer regionId=0;
                CaseInfoDetailVO caseInfoDetailVO = new CaseInfoDetailVO();

                String order_id = "";
                if (caseInfoEntity.getDataSource().equals(1)) {
                    //获取当前订单
                    OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>().eq(OrderInfoEntity::getId, caseInfoEntity.getOrderId()).eq(OrderInfoEntity::getDeleteFlag, 0));
                    sourceType = orderInfoEntity.getSourceType();

                    if (orderInfoEntity.getSourceType() != null && orderInfoEntity.getSourceType().equals(0)) {
                        ThreePartyDeptDTO threePartyDeptDTO = new ThreePartyDeptDTO();
                        threePartyDeptDTO.setDeptId(orderInfoEntity.getDeptId());
                        RegionInfoVO regionInfoByDeptId = getRegionInfoByDeptId(threePartyDeptDTO);

                        if (regionInfoByDeptId != null) {
                            deleteFlag = regionInfoByDeptId.getDeleteFlag();
                        }
                    }
                    regionId=orderInfoEntity.getRegionId();

                    caseInfoDetailVO.setRegionName(orderInfoEntity.getRegionName());
                    caseInfoDetailVO.setStoreName(orderInfoEntity.getStoreName());
                    caseInfoDetailVO.setCustomerName(orderInfoEntity.getCustomerName());
                    caseInfoDetailVO.setVehicleNumber(orderInfoEntity.getVehicleNumber());

                    if (ObjUtil.isNotNull(orderInfoEntity.getTotalOverdueAmount())) {
                        caseInfoDetailVO.setTotalOverdueAmount(orderInfoEntity.getTotalOverdueAmount());
                    } else {
                        caseInfoDetailVO.setTotalOverdueAmount(BigDecimal.ZERO);
                    }

                    caseInfoDetailVO.setDisposalCompany(approvalCaseDTO.getDisposalCompany());

                    caseInfoDetailVO.setCaseInfoid(caseInfoEntity.getId());
                    if (caseInfoEntity.getCirculationType().equals(1)) {
                        caseInfoDetailVO.setCirculationType("委外外访");
                    } else if (caseInfoEntity.getCirculationType().equals(2)) {
                        caseInfoDetailVO.setCirculationType("委外保全");
                    } else if (caseInfoEntity.getCirculationType().equals(3)) {
                        caseInfoDetailVO.setCirculationType("以资抵债");
                    }
                    caseInfoDetailVO.setOrderId(caseInfoEntity.getOrderId());
                    caseInfoDetailVO.setCaseInfoid(requestId);
                    caseInfoDetailVO.setOverdueDays(orderInfoEntity.getOverdueDays());
                    if(Objects.equals(orderInfoEntity.getIsRepurchase(),0)){
                        caseInfoDetailVO.setIsRedemption("否");
                    }else if(Objects.equals(orderInfoEntity.getIsRepurchase(),1)){
                        caseInfoDetailVO.setIsRedemption("是");
                    }else {
                        caseInfoDetailVO.setIsRedemption("否");
                    }

                    caseInfoDetailVO.setRemark(caseInfoEntity.getRemark());


                } else if (caseInfoEntity.getDataSource().equals(2) || caseInfoEntity.getDataSource().equals(3)) {
                    //获取当前订单
                    DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderId, caseInfoEntity.getDigitalOrderId()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0));
                    sourceType = digitalOutsourcingOrderEntity.getSourceType();

                    deleteFlag = digitalOutsourcingOrderEntity.getStoreStatus();
                    regionId=caseInfoEntity.getRegionId();


                    caseInfoDetailVO.setRegionName(digitalOutsourcingOrderEntity.getRegionName());
                    caseInfoDetailVO.setStoreName(digitalOutsourcingOrderEntity.getStoreName());
                    caseInfoDetailVO.setCustomerName(digitalOutsourcingOrderEntity.getCustomerName());
                    caseInfoDetailVO.setVehicleNumber(digitalOutsourcingOrderEntity.getVehicleNumber());
                    BigDecimal total = BigDecimal.ZERO;
                    if (ObjUtil.isNotNull(digitalOutsourcingOrderEntity.getTotalOverdueAmount())) {
                        total = new BigDecimal(digitalOutsourcingOrderEntity.getTotalOverdueAmount()).setScale(2, RoundingMode.HALF_UP);
                    }
                    caseInfoDetailVO.setTotalOverdueAmount(total);
                    caseInfoDetailVO.setDisposalCompany(approvalCaseDTO.getDisposalCompany());

                    caseInfoDetailVO.setCaseInfoid(caseInfoEntity.getId());
                    if (caseInfoEntity.getCirculationType().equals(1)) {
                        caseInfoDetailVO.setCirculationType("委外外访");
                    } else if (caseInfoEntity.getCirculationType().equals(2)) {
                        caseInfoDetailVO.setCirculationType("委外保全");
                    } else if (caseInfoEntity.getCirculationType().equals(3)) {
                        caseInfoDetailVO.setCirculationType("以资抵债");
                    }
                    caseInfoDetailVO.setDigitalOrderId(caseInfoEntity.getDigitalOrderId());
                    caseInfoDetailVO.setCaseInfoid(caseInfoEntity.getId());
                    caseInfoDetailVO.setOverdueDays(digitalOutsourcingOrderEntity.getOverdueDays());
                    if(Objects.equals(digitalOutsourcingOrderEntity.getIsRepurchase(),0)){
                        caseInfoDetailVO.setIsRedemption("否");
                    }else if(Objects.equals(digitalOutsourcingOrderEntity.getIsRepurchase(),1)){
                        caseInfoDetailVO.setIsRedemption("是");
                    }else {
                        caseInfoDetailVO.setIsRedemption("否");
                    }
                    caseInfoDetailVO.setRemark(caseInfoEntity.getRemark());

                }


                CaseApproveNodeEnums nextNode = null;
                PayApplicationEventEnums event = PayApplicationEventEnums.APPROVE_PASS;
                if (sourceType != null &&( (sourceType.equals(0))||(Objects.equals(sourceType,1)&&!Objects.equals(regionId,DeptEnum.DIAN_XING_DA_QU.getId())))) {
                    // 根据当前节点和审批结论确定下一个节点
                    switch (currentNode) {


                        case STORE_MANAGER_APPROVAL:

                            if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {
                                // 审核通过，下一个节点为区域品质主管
                                nextNode = CaseApproveNodeEnums.ASSET_MANAGER_APPROVED;
                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
                                // 审核拒绝，下一个节点为流程终止
                                nextNode = CaseApproveNodeEnums.FAIL;
                                event = PayApplicationEventEnums.APPROVE_REJECT;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化开始审批 {}", s);
                                }
                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
                                // 驳回，下一个节点为资外单申请
                                nextNode = CaseApproveNodeEnums.CASE_APPLY;
                                event = PayApplicationEventEnums.APPROVE_REVOKE;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化开始审批 {}", s);
                                }
                            }
                            break;
                        case ASSET_MANAGER_APPROVED:

                            if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {
                                //选择处置公司
                                Assert.notEmpty(approvalCaseDTO.getDisposalCompany(), "请选择处置公司");
                                caseInfoEntity.setDisposalCompany(approvalCaseDTO.getDisposalCompany());
                                caseInfoEntity.setDeptId(approvalCaseDTO.getDeptId());
                                if(Objects.equals(caseInfoEntity.getCaseType(),1)){
                                    // 审核通过，下一个节点为区域资管副总
                                    nextNode = CaseApproveNodeEnums.SUCCESS;;
                                }else {
                                    // 审核通过，下一个节点为区域资管副总
                                    nextNode = ASSET_VICE_PRESIDENT_APPROVED;
                                    //发起钉钉推送
                                    initiateOutsourcingAdvanceDingTalkApproval(caseInfoDetailVO, caseInfoEntity.getCreateBy());
                                }


                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
                                // 审核拒绝，下一个节点为门店
                                nextNode = CaseApproveNodeEnums.FAIL;
                                event = PayApplicationEventEnums.APPROVE_REJECT;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化审批拒绝 {}", s);
                                    CaseInfoPushRecordEntity caseInfoPushRecordEntity = new CaseInfoPushRecordEntity();
                                    caseInfoPushRecordEntity.setCaseInfoId(caseInfoEntity.getId());
                                    caseInfoPushRecordEntity.setPushTime(LocalDateTime.now());
                                    caseInfoPushRecordEntity.setPushNode(6);
                                    caseInfoPushRecordEntity.setKindeePushResult(s);
                                    caseInfoPushRecordEntityMapper.insert(caseInfoPushRecordEntity);
                                }else if(caseInfoEntity.getDataSource().equals(3)){
                                    //通知金蝶拒绝
                                    KingdeeStatusRequestDTO kingdeeStatusRequestDTO = new KingdeeStatusRequestDTO();
                                    kingdeeStatusRequestDTO.setCode(1);
                                    kingdeeStatusRequestDTO.setMsg("成功");
                                    KingdeeRequestData data = new KingdeeRequestData();
                                    data.setOrderNumber(caseInfoEntity.getDigitalOrderId());
                                    if(caseInfoEntity.getCirculationType().equals(1)){
                                        data.setFlowType("委外外访");
                                    }else if(caseInfoEntity.getCirculationType().equals(2)){
                                        data.setFlowType("委外保全");
                                    }else if(caseInfoEntity.getCirculationType().equals(3)){
                                        data.setFlowType("以资抵债");
                                    }
                                    data.setOutsourceOrg(caseInfoEntity.getDisposalCompany());
                                    data.setOutsourceTime(caseInfoEntity.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                                    data.setOutsourceStatus(2);
                                    kingdeeStatusRequestDTO.setData(data);
                                    String kingdeeorderStatus = kingdeeOutsourcingService.pushKingdeeorderStatus(kingdeeStatusRequestDTO);
                                    log.info("通知金蝶状态 {}", kingdeeorderStatus);
                                    CaseInfoPushRecordEntity caseInfoPushRecordEntity = new CaseInfoPushRecordEntity();
                                    caseInfoPushRecordEntity.setCaseInfoId(caseInfoEntity.getId());
                                    caseInfoPushRecordEntity.setPushTime(LocalDateTime.now());
                                    caseInfoPushRecordEntity.setPushNode(6);
                                    caseInfoPushRecordEntity.setDigitalOrderId(caseInfoEntity.getDigitalOrderId());
                                    caseInfoPushRecordEntity.setKindeePushResult(kingdeeorderStatus);
                                    caseInfoPushRecordEntityMapper.insert(caseInfoPushRecordEntity);

                                }
                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
                                // 驳回，下一个节点为门店审核
                                nextNode = CaseApproveNodeEnums.CASE_APPLY;
                                event = PayApplicationEventEnums.APPROVE_REVOKE;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化开始审批 {}", s);
                                }
                            }
                            break;


                    }
                } else if (sourceType != null && sourceType.equals(1)&&Objects.equals(regionId,DeptEnum.DIAN_XING_DA_QU.getId())) {
                    // 根据当前节点和审批结论确定下一个节点
                    switch (currentNode) {


                        case ELECTRIC_SALES_MANAGER_APPROVED:

                            if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {
                                // 审核通过，下一个节点为售后主管
                                nextNode = SALES_MANAGER_APPROVED;
                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
                                // 审核拒绝，下一个节点为流程终止
                                nextNode = CaseApproveNodeEnums.FAIL;
                                event = PayApplicationEventEnums.APPROVE_REJECT;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化开始审批 {}", s);
                                }
                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
                                // 驳回，下一个节点为资外单申请
                                nextNode = CaseApproveNodeEnums.CASE_APPLY;
                                event = PayApplicationEventEnums.APPROVE_REVOKE;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化开始审批 {}", s);
                                }
                            }
                            break;
                        case SALES_MANAGER_APPROVED:

                            if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {
                                //选择处置公司
                                Assert.notEmpty(approvalCaseDTO.getDisposalCompany(), "请选择处置公司");
                                //更新处置公司信息
                                caseInfoEntity.setDisposalCompany(approvalCaseDTO.getDisposalCompany());
                                caseInfoEntity.setDeptId(approvalCaseDTO.getDeptId());


                                // 审核通过，下一个节点为资管主管审批
                                nextNode = CaseApproveNodeEnums.ASSET_MANAGER_APPROVED;


                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
                                // 审核拒绝，下一个节点为流程终止
                                nextNode = CaseApproveNodeEnums.FAIL;
                                event = PayApplicationEventEnums.APPROVE_REJECT;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化开始审批 {}", s);
                                }
                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
                                // 驳回，下一个节点为资外单申请
                                nextNode = CaseApproveNodeEnums.ELECTRIC_SALES_MANAGER_APPROVED;
                                event = PayApplicationEventEnums.APPROVE_REVOKE;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化开始审批 {}", s);
                                }
                            }
                            break;
                        case ASSET_MANAGER_APPROVED:

                            if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {
                                //选择处置公司
                                Assert.notEmpty(approvalCaseDTO.getDisposalCompany(), "请选择处置公司");
                                caseInfoEntity.setDisposalCompany(approvalCaseDTO.getDisposalCompany());
                                caseInfoEntity.setDeptId(approvalCaseDTO.getDeptId());
                                //获取处置公司所属大区走大区流程
                                if(ObjUtil.isNotEmpty(approvalCaseDTO.getDeptId())){
                                    ThreePartyDeptDTO threePartyDeptDTO =new ThreePartyDeptDTO();
                                    threePartyDeptDTO.setDeptId(approvalCaseDTO.getDeptId());
                                    Result<Page<DisposalCompanyVO>> threePartyDeptList = userFeign.getThreePartyDeptList(threePartyDeptDTO);
                                    if(Result.isSuccess(threePartyDeptList)){
                                        if(CollUtil.isNotEmpty(threePartyDeptList.getData().getRecords())){
                                            DisposalCompanyVO deptVO = threePartyDeptList.getData().getRecords().get(0);
                                            if(ObjUtil.isNotEmpty(deptVO)&&ObjUtil.isNotEmpty(deptVO.getRegionId())){
                                                caseInfoDetailVO.setRegionName(DeptEnum.getEnum(deptVO.getRegionId()).name());
                                                caseInfoEntity.setSaleProcess(deptVO.getRegionId());

                                            }

                                        }

                                    }
                                }

                                if(Objects.equals(caseInfoEntity.getCaseType(),1)){

                                    // 审核通过，下一个节点为成功
                                    nextNode = CaseApproveNodeEnums.SUCCESS;

                                }else {
                                    if (caseInfoEntity.getDeptId() != null && caseInfoEntity.getDeptId().equals(DeptEnum.ZHONG_XIN.getId())) {
                                        // 审核通过，下一个节点为成功
                                        nextNode = CaseApproveNodeEnums.SUCCESS;
                                    } else {
                                        // 审核通过，下一个节点为区域资管副总
                                        nextNode = ASSET_VICE_PRESIDENT_APPROVED;
                                        //发起钉钉推送
                                        initiateOutsourcingAdvanceDingTalkApproval(caseInfoDetailVO, caseInfoEntity.getCreateBy());

                                    }
                                }


                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
                                // 审核拒绝，下一个节点为流程终止
                                nextNode = CaseApproveNodeEnums.FAIL;
                                event = PayApplicationEventEnums.APPROVE_REJECT;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化开始审批 {}", s);
                                }
                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
                                // 驳回，下一个节点为售后主管审批
                                nextNode = SALES_MANAGER_APPROVED;
                                event = PayApplicationEventEnums.APPROVE_REVOKE;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化开始审批 {}", s);
                                }
                            }
                            break;

//                        case QUALITY_MANAGER_APPROVED:
//
//                            if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {
//                                // 审核通过，下一个节点为区域资管副总
//                                nextNode = ASSET_VICE_PRESIDENT_APPROVED;
//                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
//                                // 审核拒绝，下一个节点为流程终止
//                                nextNode = CaseApproveNodeEnums.FAIL;
//                                event = PayApplicationEventEnums.APPROVE_REJECT;
//                                if (caseInfoEntity.getDataSource().equals(2)) {
//                                    //  通知数字化开始审批
//                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
//                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
//                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
//                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
//                                    log.info("通知数字化开始审批 {}", s);
//                                }
//                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
//                                // 驳回，下一个节点为售后主管审批
//                                nextNode = CaseApproveNodeEnums.CASE_APPLY;
//                                event = PayApplicationEventEnums.APPROVE_REVOKE;
//                                if (caseInfoEntity.getDataSource().equals(2)) {
//                                    //  通知数字化开始审批
//                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
//                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
//                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
//                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
//                                    log.info("通知数字化开始审批 {}", s);
//                                }
//                            }
//                            break;
                        default:
                            // 根据当前节点确定下一个节点
                            if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {
                                nextNode = getNextNode(currentNode);
                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
                                // 审核拒绝，下一个节点为流程终止
                                nextNode = CaseApproveNodeEnums.FAIL;
                                event = PayApplicationEventEnums.APPROVE_REJECT;
                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
                                // 驳回，下一个节点为当前节点
                                nextNode = currentNode;
                                event = PayApplicationEventEnums.APPROVE_REVOKE;
                            }
                            break;

                    }

                }

                //todo 非电销订单且闭店的
                else if (sourceType != null && sourceType.equals(0) && deleteFlag.equals(1)) {
                    // 根据当前节点和审批结论确定下一个节点
                    switch (currentNode) {

                        case SALES_MANAGER_APPROVED:
                            if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {

                                // 审核通过，下一个节点为资管主管
                                nextNode = CaseApproveNodeEnums.ASSET_MANAGER_APPROVED;

                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
                                // 审核拒绝，下一个节点为流程终止
                                nextNode = CaseApproveNodeEnums.FAIL;
                                event = PayApplicationEventEnums.APPROVE_REJECT;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化开始审批 {}", s);
                                }
                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
                                // 驳回，下一个节点为资外单申请
                                nextNode = CaseApproveNodeEnums.CASE_APPLY;
                                event = PayApplicationEventEnums.APPROVE_REVOKE;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化开始审批 {}", s);
                                }
                            }
                            break;


                        case ASSET_MANAGER_APPROVED:

                            if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {

                                // 审核通过，下一个节点为区域资管副总
                                nextNode = ASSET_VICE_PRESIDENT_APPROVED;
                                //选择处置公司
                                caseInfoEntity.setDisposalCompany(approvalCaseDTO.getDisposalCompany());
                                caseInfoEntity.setDeptId(approvalCaseDTO.getDeptId());
                                //发起钉钉推送
                                initiateOutsourcingAdvanceDingTalkApproval(caseInfoDetailVO, caseInfoEntity.getCreateBy());
                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
                                // 审核拒绝，下一个节点为流程终止
                                nextNode = CaseApproveNodeEnums.FAIL;
                                event = PayApplicationEventEnums.APPROVE_REJECT;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化开始审批 {}", s);
                                }
                            } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
                                // 驳回，下一个节点为售后主管
                                nextNode = CaseApproveNodeEnums.SALES_MANAGER_APPROVED;
                                event = PayApplicationEventEnums.APPROVE_REVOKE;
                                if (caseInfoEntity.getDataSource().equals(2)) {
                                    //  通知数字化开始审批
                                    DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                    digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                                    digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                                    String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                                    log.info("通知数字化开始审批 {}", s);
                                }
                            }
                            break;

                    }
                }


                //更新资外订单状态
                caseInfoEntity.setCurrentNode(nextNode);
                caseInfoEntityMapper.updateById(caseInfoEntity);
                //添加审批记录
                addCaseInfoApproveRecord(requestId, approvalCaseDTO.getRemark(), currentNode, nextNode, event, loginUser.getUserId(), null, PayApplicationAuditTypeEnum.YUNQI);

                // 异步推送至众信
                if (caseInfoEntity.getCurrentNode() == CaseApproveNodeEnums.SUCCESS && caseInfoEntity.getDeptId().equals(DeptEnum.ZHONG_XIN.getId()) && caseInfoEntity.getDataSource().equals(1)) {
                    CompletableFuture.runAsync(() -> {
                        try {
                            Result<String> stringResult = zhongXinService.pushCaseToZhongXin(caseInfoEntity.getOrderId());
                            log.info("审批通过推送至众信结果：{}", stringResult);

                        } catch (Exception e) {
                            log.error("推送至众信时发生异常", e);
                        }
                    });
                } else if (caseInfoEntity.getCurrentNode() == CaseApproveNodeEnums.SUCCESS && caseInfoEntity.getDeptId().equals(DeptEnum.ZHONG_XIN.getId()) && caseInfoEntity.getDataSource().equals(2)) {


                    CompletableFuture.runAsync(() -> {
                        try {
                            Result<String> stringResult = zhongXinService.digitalizePushCaseToZhongXin(caseInfoEntity);
                            log.info("审批通过推送至众信结果：{}", stringResult);

                        } catch (Exception e) {
                            log.error("推送至众信时发生异常", e);
                        }
                    });


                }


            });


        }

        return true;
    }

    /**
     * 发起钉钉审批流程。
     *
     * @param caseInfoDetailVO 提前委外流程
     * @param userId           当前登录用户信息
     * @throws BusinessException 当当前人员未同步到钉钉或流程不存在时抛出
     */
    @Override
    public boolean initiateOutsourcingAdvanceDingTalkApproval(CaseInfoDetailVO caseInfoDetailVO, Integer userId) {
        CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getId, caseInfoDetailVO.getCaseInfoid()).eq(CaseInfoEntity::getDeleteFlag, 0));
        if (caseInfoEntity != null && caseInfoEntity.getDataSource().equals(1)) {
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(caseInfoDetailVO.getOrderId());
            Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));
            log.info("OrderPayApplicationServiceImpl OrderPayApplication found orderInfo:{}", orderInfo);
        } else if (caseInfoEntity != null && caseInfoEntity.getDataSource().equals(2)) {
            DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderId, caseInfoDetailVO.getDigitalOrderId()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0));
            Assert.notNull(digitalOutsourcingOrderEntity, () -> new BusinessException("订单不存在"));
            log.info("OrderPayApplicationServiceImpl OrderPayApplication found digitalOutsourcingOrderEntity:{}", digitalOutsourcingOrderEntity);

        }


        String processCode = "";
        if (envUtil.isPrd()) {
            processCode = dingTaskFeign.getWorkFlowSchemaId("委外申请").getData();
            Assert.notBlank(processCode, () -> new BusinessException("流程不存在"));
            log.info("OrderPayApplicationServiceImpl OrderPayApplication processCode:{}", processCode);
        } else if (!envUtil.isPrd()) {
            processCode = dingTaskFeign.getWorkFlowSchemaId("委外申请（测试）").getData();
            Assert.notBlank(processCode, () -> new BusinessException("流程不存在"));
            log.info("OrderPayApplicationServiceImpl OrderPayApplication processCode:{}", processCode);
        }


        // 获取当前人员钉钉userId
        List<UserSyncInfoListVO> currentUserSyncInfoListVO = userFeign.queryUserSyncInfoList(new SearchUserSyncInfoDTO().setUserIdList(List.of(userId)).setOrigin(1)).getData();
        Assert.notEmpty(currentUserSyncInfoListVO, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        UserSyncInfoListVO currentUserSyncInfo = currentUserSyncInfoListVO.get(0);
        String userNumber = currentUserSyncInfo.getUserNumber();
        Assert.notBlank(userNumber, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        log.info("OrderPayApplicationServiceImpl OrderPayApplication current userNumber:{}", userNumber);
        //获取钉钉部门id
        UserDetailInfoVO currentUserDetailInfoVO = userFeign.searchUserDetailById(userId).getData();
        List<Integer> currentUserDeptIds = currentUserDetailInfoVO.getDeptIds();
        Assert.notEmpty(currentUserDeptIds, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        List<DeptSyncInfoVO> currentUserDeptSyncList = userFeign.getSyncDeptByLhDeptIds(new SearchDeptSyncInfoDTO().setLhDeptIdList(currentUserDeptIds).setOrigin(1)).getData();
        Assert.notEmpty(currentUserDeptSyncList, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        List<DeptSyncInfoVO> currentUserDeptSyncS=userFeign.getSyncDeptByUserId(new SearchDeptSyncInfoDTO().setUserId(userId).setOrigin(1)).getData();
        Long deptId=1l;
              if(CollUtil.isNotEmpty(currentUserDeptSyncS)){
                  deptId=Convert.toLong(currentUserDeptSyncS.get(0).getDeptId());
              }else {
                  deptId=Convert.toLong(currentUserDeptSyncList.get(0).getDeptId());
              }


        log.info("OrderPayApplicationServiceImpl OrderPayApplication current deptSyncList:{}", currentUserDeptSyncList);


        List<DingCreateProcessCmdDTO.FormComponentValue> formComponentValueList = new ArrayList<>(7);
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("区域")
                .setValue(caseInfoDetailVO.getRegionName()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("门店")
                .setValue(caseInfoDetailVO.getStoreName()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("客户姓名")
                .setValue(caseInfoDetailVO.getCustomerName()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("车牌号")
                .setValue(caseInfoDetailVO.getVehicleNumber()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("逾期金额")
                .setValue(caseInfoDetailVO.getTotalOverdueAmount().toString()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("逾期天数")
                .setValue(ObjUtil.isNotEmpty(caseInfoDetailVO.getOverdueDays()) ? caseInfoDetailVO.getOverdueDays().toString() : "0"));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("赎回状态")
                .setValue(caseInfoDetailVO.getIsRedemption()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("资产处置方")
                .setValue(ObjUtil.isNotEmpty(caseInfoDetailVO.getDisposalCompany())?caseInfoDetailVO.getDisposalCompany():"未知"));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("流转类型")
                .setValue(caseInfoDetailVO.getCirculationType()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("申请备注")
                .setValue(caseInfoDetailVO.getRemark()));


        DingCreateProcessCmdDTO processCmdDTO = new DingCreateProcessCmdDTO();

        List<DingCreateProcessCmdDTO.TargetSelectActioner> targetSelectActionerList = new ArrayList<>();


        if (envUtil.isPrd()) {
            processCmdDTO.setProcessCode(processCode)
                    .setOriginatorUserId(userNumber)
                    .setDeptId(deptId)
//                    .setDeptId(Convert.toLong(currentUserDeptSyncList.get(0).getDeptId()))
                    .setFormComponentValues(formComponentValueList)
                    .setTargetSelectActioners(targetSelectActionerList)
            ;
        } else {
            processCmdDTO.setProcessCode(processCode)
                    .setOriginatorUserId(userNumber)
                    .setDeptId(deptId)
//                    .setDeptId(Convert.toLong(currentUserDeptSyncList.get(0).getDeptId()))
                    .setFormComponentValues(formComponentValueList)
                    .setTargetSelectActioners(targetSelectActionerList)
            ;

//            String[] originatorUserIds = {"121607016836389277"};
////            String[] originatorUserIds = {"17343137193032983"};
//
//            Random random = new Random();
//
//            String randomOriginatorUserId = originatorUserIds[random.nextInt(originatorUserIds.length)];
//
//            processCmdDTO.setProcessCode(processCode)
//                    .setOriginatorUserId(randomOriginatorUserId)
//
//                    .setDeptId(976054817L)
////                    .setDeptId(926741152L)
//                    .setFormComponentValues(formComponentValueList);
        }

        log.info("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances:{}", processCmdDTO);
        Result<String> workflowProcessInstances = null;
        try {
            if (envUtil.isPrd()) {
                workflowProcessInstances = dingTaskFeign.createWorkflowProcessInstances(processCmdDTO);
                log.info("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances:{}", workflowProcessInstances);
            } else {
                workflowProcessInstances = dingTaskFeign.createWorkflowProcessInstances(processCmdDTO);
                log.info("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances:{}", workflowProcessInstances);

//                workflowProcessInstances = Result.success("U-XhRSu-QgOabcYzuHaroQ07861733829144");

            }
        } catch (Exception e) {
            log.error("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances error:{}", e.getMessage());
            throw new BusinessException("发起钉钉审批实例失败");
        }
        if (Result.isSuccess(workflowProcessInstances) && StrUtil.isNotBlank(workflowProcessInstances.getData())) {
            log.info("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances:{}", workflowProcessInstances);


//            caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
//                    .eq(CaseInfoEntity::getId, caseInfoDetailVO.getCaseInfoid())
//                    .set(CaseInfoEntity::getProcessId, workflowProcessInstances.getData()));
            caseInfoEntity.setProcessId(workflowProcessInstances.getData());
            caseInfoEntityMapper.updateById(caseInfoEntity);
            return true;
        } else {
            log.info("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances fail:{}", workflowProcessInstances);
            throw new BusinessException("发起钉钉审批实例失败");
        }
    }

    @Override
    public CaseInfoRepayCalcVO earlyRepayCalc(Integer orderId) {
       //todo 查看是否锁定
        CaseInfoRepayCalcVO lockearlyRepayCalc = outsourcingExpansionService.getLockearlyRepayCalc(orderId);
        if(ObjUtil.isNotEmpty(lockearlyRepayCalc)){
            return lockearlyRepayCalc;
        }

//        CaseInfoRepayCalcVO caseInfoRepayCalcVO = new CaseInfoRepayCalcVO();
//        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
//        LocalDate beginDate = LocalDate.now();
//        //获取产品封闭期
//        FundProductMappingEntity fundProductMappingEntity = fundProductMappingEntityMapper.selectOne(new LambdaQueryWrapper<FundProductMappingEntity>()
//                .eq(FundProductMappingEntity::getProductId, orderInfo.getProductId())
//                .eq(FundProductMappingEntity::getFundId, orderInfo.getFundId())
//                .eq(FundProductMappingEntity::getDeleteFlag, 0).last("limit 1"));
//
//        if (ObjUtil.isNotEmpty(fundProductMappingEntity)) {
//
//            FundRepayCalcEarlyDTO fundRepayCalcEarlyDTO = new FundRepayCalcEarlyDTO();
//            fundRepayCalcEarlyDTO.setOrderId(orderInfo.getId());
//            fundRepayCalcEarlyDTO.setRepayMode(FundRepayModeEnum.ONLINE);
//            log.info("calculateSettlement overdueDays={}", orderInfo.getOverdueDays());
//            fundRepayCalcEarlyDTO.setSettleFlag(2);
//
//
//            Integer closedPeriod = fundProductMappingEntity.getClosedPeriod();
//            Integer returnTerm = 0;
//            if (ObjUtil.isNotEmpty(orderInfo) && ObjUtil.equal(orderInfo.getIsRepurchase(), 1)) {
//                RepurchaseRepaymentInfoEntity repurchaseRepaymentInfoEntity = repurchaseRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>().eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId).eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0).ne(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED).orderByAsc(RepurchaseRepaymentInfoEntity::getTerm).last("limit 1"));
//                if (ObjUtil.isNotEmpty(repurchaseRepaymentInfoEntity)) {
//                    returnTerm = repurchaseRepaymentInfoEntity.getTerm();
//                }
//            } else {
//                FundRepaymentInfoEntity fundRepaymentInfo = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>().eq(FundRepaymentInfoEntity::getOrderId, orderId).eq(FundRepaymentInfoEntity::getDeleteFlag, 0).ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED).orderByAsc(FundRepaymentInfoEntity::getTerm).last("limit 1"));
//                if (ObjUtil.isNotEmpty(fundRepaymentInfo)) {
//                    returnTerm = fundRepaymentInfo.getTerm();
//                }
//            }
//
//            //封闭期外
//            if (returnTerm > closedPeriod) {
//                fundRepayCalcEarlyDTO.setLoanSettlementMethod(LoanSettlementEnum.CLOSED_PERIOD_OUTSIDE);
//                caseInfoRepayCalcVO.setLoanSettlementMethod(LoanSettlementEnum.CLOSED_PERIOD_OUTSIDE.getDesc());
//                caseInfoRepayCalcVO.setLoanSettlementEnum(LoanSettlementEnum.CLOSED_PERIOD_OUTSIDE);
//            } else {
//                fundRepayCalcEarlyDTO.setLoanSettlementMethod(LoanSettlementEnum.CLOSED_PERIOD_PENALTY_INSIDE);
//                caseInfoRepayCalcVO.setLoanSettlementMethod(LoanSettlementEnum.CLOSED_PERIOD_PENALTY_INSIDE.getDesc());
//                caseInfoRepayCalcVO.setLoanSettlementEnum(LoanSettlementEnum.CLOSED_PERIOD_PENALTY_INSIDE);
//            }
//            FundEarlyRepaymentCalcDTO repaymentCalcDTO = fundDeductService.earlyRepayCalc(fundRepayCalcEarlyDTO);
//
//            caseInfoRepayCalcVO.setLoanSettlementAmount(repaymentCalcDTO.getRepayAmountTotal());
//            caseInfoRepayCalcVO.setApplySettlementAmount(repaymentCalcDTO.getRepayAmountTotal());
////            caseInfoRepayCalcVO.setUsageDays(repaymentCalcDTO.getSettlementVO().getUseDays());
//            caseInfoRepayCalcVO.setSettlePenaltyRate(repaymentCalcDTO.getSettlePenaltyRate());
//            if (ObjUtil.isNotEmpty(orderInfo) && ObjUtil.equals(orderInfo.getIsRepurchase(),1)){
//                Long count = repurchaseRepaymentInfoMapper.selectCount(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
//                        .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
//                        .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
//                        .eq(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED));
//                caseInfoRepayCalcVO.setInstalmentsRepaid(count.intValue());
//                BigDecimal principal = repurchaseRepaymentInfoMapper.selectList(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
//                                .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
//                                .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
//                                .ne(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED))
//                        .stream().map(x -> x.getRepaymentPrincipal()
//                                .subtract(ObjUtil.defaultIfNull(x.getActuallyPrincipal(), BigDecimal.ZERO)))
//                        .reduce(BigDecimal.ZERO, BigDecimal::add);
//                caseInfoRepayCalcVO.setRemainingPrincipal(principal);
//
//            }else if (ObjUtil.isNotEmpty(orderInfo)){
//                Long count = fundRepaymentInfoMapper.selectCount(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
//                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
//                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
//                        .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED));
//                caseInfoRepayCalcVO.setInstalmentsRepaid(count.intValue());
//                BigDecimal principal = fundRepaymentInfoMapper.selectList(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
//                                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
//                                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
//                                .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED))
//                        .stream().map(x -> x.getRepaymentPrincipal()
//                                .subtract(ObjUtil.defaultIfNull(x.getActuallyPrincipal(), BigDecimal.ZERO)))
//                        .reduce(BigDecimal.ZERO, BigDecimal::add);
//                caseInfoRepayCalcVO.setRemainingPrincipal(principal);
//
//            }
//            LocalDateTime now = LocalDateTime.now();
//
//            if (ObjUtil.isNotEmpty(orderInfo) && ObjUtil.equal(orderInfo.getIsRepurchase(), 1)) {
//
//                RepurchaseRepaymentInfoEntity repurchaseRepaymentInfo = repurchaseRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
//                        .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
//                        .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
//                        .eq(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
//                        .orderByDesc(RepurchaseRepaymentInfoEntity::getTerm)
//                        .last("limit 1"));
//
//                //使用天数
//                if(ObjectUtil.isNotEmpty(repurchaseRepaymentInfo)){
//                    caseInfoRepayCalcVO.setUsageDays((int) ChronoUnit.DAYS.between(repurchaseRepaymentInfo.getRepaymentDate(), beginDate));
//                }else {
//                    caseInfoRepayCalcVO.setUsageDays((int) ChronoUnit.DAYS.between(orderInfo.getPaymentTime(),now));
//                }
//            } else {
//
//                FundRepaymentInfoEntity repaymentInfoEntity = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
//                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
//                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
//                        .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
//                        .orderByDesc(FundRepaymentInfoEntity::getTerm)
//                        .last("limit 1"));
//                //使用天数
//                if(ObjectUtil.isNotEmpty(repaymentInfoEntity)){
//                    caseInfoRepayCalcVO.setUsageDays((int) ChronoUnit.DAYS.between(repaymentInfoEntity.getRepaymentDate(), beginDate));
//                }else {
//                    caseInfoRepayCalcVO.setUsageDays((int) ChronoUnit.DAYS.between(orderInfo.getPaymentTime(),now));
//                }
//
//            }
//
//
//
//
//
//        }
            SettleCalculationVO settleCalculationVO = settleCalculationService.calculateByOrderId(orderId, null);
            CaseInfoRepayCalcVO caseInfoRepayCalcVO = new CaseInfoRepayCalcVO();
            caseInfoRepayCalcVO.setLoanSettlementAmount(settleCalculationVO.getSettlementAmount());
            caseInfoRepayCalcVO.setApplySettlementAmount(settleCalculationVO.getSettlementAmount());
            caseInfoRepayCalcVO.setRemainingPrincipal(settleCalculationVO.getSettleCalculationContext().getRemainingPrincipal());
            caseInfoRepayCalcVO.setInstalmentsRepaid(settleCalculationVO.getSettleCalculationContext().getPaidPeriods());
            caseInfoRepayCalcVO.setUsageDays(settleCalculationVO.getSettleCalculationContext().getUseDays());
            if (settleCalculationVO.getSettlePenaltyRate().compareTo(BigDecimal.ZERO) != 0) {
                caseInfoRepayCalcVO.setSettlePenaltyRate(settleCalculationVO.getSettlePenaltyRate());
                caseInfoRepayCalcVO.setLoanSettlementEnum(LoanSettlementEnum.CLOSED_PERIOD_PENALTY_INSIDE);
                caseInfoRepayCalcVO.setLoanSettlementMethod(LoanSettlementEnum.CLOSED_PERIOD_PENALTY_INSIDE.getDesc() + settleCalculationVO.getSettlePenaltyRate().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_DOWN) + "%");
            } else {
                caseInfoRepayCalcVO.setLoanSettlementEnum(LoanSettlementEnum.CLOSED_PERIOD_OUTSIDE);
                caseInfoRepayCalcVO.setLoanSettlementMethod(LoanSettlementEnum.CLOSED_PERIOD_OUTSIDE.getDesc());
            }
            return caseInfoRepayCalcVO;

    }

    @Override
    public void batchtestDingTaskApprove() {

    }



    public void addCaseInfoApproveRecord(Integer requestId, String remark, CaseApproveNodeEnums currentNode, CaseApproveNodeEnums nextNode, PayApplicationEventEnums event, Integer userId, String processId, PayApplicationAuditTypeEnum auditType) {
        CaseInfoApproveEntity caseInfoApproveEntity = new CaseInfoApproveEntity();
        caseInfoApproveEntity.setCaseInfoStatus(nextNode.getCode());
        caseInfoApproveEntity.setCurrentNode(currentNode);
        caseInfoApproveEntity.setNextNode(nextNode);
        caseInfoApproveEntity.setCaseInfoRequestId(requestId);
        caseInfoApproveEntity.setApproveStatus(event.getCode());
        caseInfoApproveEntity.setRemark(remark);
        caseInfoApproveEntity.setApproveUserId(userId);
        caseInfoApproveEntity.setApproveType(1);
        caseInfoApproveEntity.setProcessId(processId);
        caseInfoApproveEntity.setAuditType(auditType);


        caseInfoApproveEntityMapper.insert(caseInfoApproveEntity);
    }

    public void addCaseInfoReductionApproveRecord(Integer requestId, String remark, ReduceApproveNodeEnums currentNode, ReduceApproveNodeEnums nextNode, PayApplicationEventEnums event, Integer userId, String processId, PayApplicationAuditTypeEnum auditType) {
        CaseInfoApproveEntity caseInfoApproveEntity = new CaseInfoApproveEntity();
        caseInfoApproveEntity.setCaseInfoStatus(nextNode.getCode());
        caseInfoApproveEntity.setReduceCurrentNode(currentNode);
        caseInfoApproveEntity.setReduceNextNode(nextNode);
        caseInfoApproveEntity.setCaseInfoRequestId(requestId);
        caseInfoApproveEntity.setApproveStatus(event.getCode());
        caseInfoApproveEntity.setRemark(remark);
        caseInfoApproveEntity.setApproveUserId(userId);
        caseInfoApproveEntity.setApproveType(2);
        caseInfoApproveEntity.setProcessId(processId);
        caseInfoApproveEntity.setAuditType(auditType);
        caseInfoApproveEntityMapper.insert(caseInfoApproveEntity);
    }


    private CaseApproveNodeEnums getNextNode(CaseApproveNodeEnums currentNode) {


        //非客户
        return switch (currentNode) {
            case CASE_APPLY ->
                // 委外申请，下一个节点为门店经理审核
                    CaseApproveNodeEnums.STORE_MANAGER_APPROVAL;
            case STORE_MANAGER_APPROVAL ->
                // 门店经理审核，下一个节点为大区运营审核
                    CaseApproveNodeEnums.REGION_MANAGER_APPROVAL;
            case REGION_MANAGER_APPROVAL ->
                // 大区运营审核，下一个节点为总区审核
                    REGION_GENERAL_APPROVED;
            case REGION_GENERAL_APPROVED ->
                // 大区运营审核，下一个节点为质检审核
                    CaseApproveNodeEnums.ASSET_MANAGER_APPROVED;
            case ASSET_MANAGER_APPROVED ->
                // 大区运营审核，下一个节点为质检审核
                    CaseApproveNodeEnums.SUCCESS;
            default -> currentNode;
        };
    }

    private CaseApproveNodeEnums getCloseStoreNextNode(CaseApproveNodeEnums currentNode) {


        //非客户
        return switch (currentNode) {
            case CASE_APPLY ->
                // 委外申请，下一个节点为售后主管
                    SALES_MANAGER_APPROVED;
            case SALES_MANAGER_APPROVED ->
                // 售后审核，下一个节点为品质主管审核
                    CaseApproveNodeEnums.ASSET_MANAGER_APPROVED;
            case ASSET_MANAGER_APPROVED ->
                // 品质主管审核，下一个节点为资管副管
                    CaseApproveNodeEnums.ASSET_VICE_PRESIDENT_APPROVED;
            // 资管副管，区总
            case ASSET_VICE_PRESIDENT_APPROVED -> REGION_GENERAL_APPROVED;
            case REGION_GENERAL_APPROVED -> CaseApproveNodeEnums.SUCCESS;
            default -> currentNode;
        };
    }

    private CaseApproveNodeEnums getElectricSalesNextNode(CaseApproveNodeEnums currentNode) {


        //非客户
        return switch (currentNode) {
            case CASE_APPLY ->
                // 委外申请，下一个节点为电销经理审核
                    CaseApproveNodeEnums.ELECTRIC_SALES_MANAGER_APPROVED;
            case ELECTRIC_SALES_MANAGER_APPROVED ->
                // 电销经理审核，下一个节点为售后主管审核
                    SALES_MANAGER_APPROVED;
            case SALES_MANAGER_APPROVED ->
                // 大区资管主管审核，下一个节点为资管主管审核
                    CaseApproveNodeEnums.ASSET_MANAGER_APPROVED;
            // 资管主管审核，下一个节点为成功
            case ASSET_MANAGER_APPROVED ->
                // 大区运营审核，下一个节点为成功
                    CaseApproveNodeEnums.SUCCESS;
            default -> currentNode;
        };
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCaseInfoStatus(CaseInfoStatusDTO caseInfoStatusDTO, LoginUser loginUser) {
        log.info("updateCaseInfoStatus.caseInfoStatusDTO:{}", caseInfoStatusDTO);
        if (ObjectUtil.isNotEmpty(caseInfoStatusDTO.getCaseInfoIds())) {
            caseInfoStatusDTO.getCaseInfoIds().forEach(item -> {
                CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getId, item).eq(CaseInfoEntity::getDeleteFlag, 0));
                Asserts.notNull(caseInfoEntity, "资产委外不存在");

                if (caseInfoStatusDTO.getStatusType() != null) {
                    CaseStatusTypeEnum statusType = caseInfoStatusDTO.getStatusType();

                    switch (statusType) {
                        case CALL_STATE:
                            caseInfoEntity.setCallState(caseInfoStatusDTO.getStatusValue());
                            break;
                        case PRESERVATION_STATE:
                            caseInfoEntity.setPreservationState(caseInfoStatusDTO.getStatusValue());
                            break;
                        case VISIT_STATE:
                            caseInfoEntity.setVisitState(caseInfoStatusDTO.getStatusValue());
                            break;
                        case OVERDUE_ORDER_STATE:
                            //更新贷后补件状态
                            updateAfterLoanPatchesStatus(caseInfoEntity.getOrderId(), AfterLoanPatchesEnum.OVERDUE, 1);

                            break;
                        case PRESERVE_ORDER_STATE:
                            //更新贷后补件状态

                            updateAfterLoanPatchesStatus(caseInfoEntity.getOrderId(), AfterLoanPatchesEnum.PRESERVE, 1);
                            break;
                    }
                }

                caseInfoEntityMapper.updateById(caseInfoEntity);

                createAndInsertCaseInfoStatusRecord(caseInfoStatusDTO, loginUser, item);


            });

        }

        return true;
    }

    private void createAndInsertCaseInfoStatusRecord(CaseInfoStatusDTO caseInfoStatusDTO, LoginUser loginUser, Integer caseInfoId) {
        CaseInfoStatusRecordsEntity caseInfoStatusRecordsEntity = new CaseInfoStatusRecordsEntity();
        caseInfoStatusRecordsEntity.setCaseInfoId(caseInfoId);
        caseInfoStatusRecordsEntity.setStatusType(caseInfoStatusDTO.getStatusType());
        caseInfoStatusRecordsEntity.setStatusValue(caseInfoStatusDTO.getStatusValue());
        caseInfoStatusRecordsEntity.setAuditOpinion(caseInfoStatusDTO.getAuditOpinion());
        caseInfoStatusRecordsEntity.setOperatorName(loginUser.getName());
        caseInfoStatusRecordsEntity.setCreateBy(loginUser.getUserId());
        caseInfoStatusRecordsMapper.insert(caseInfoStatusRecordsEntity);
    }

    private void updateAfterLoanPatchesStatus(Integer orderId, AfterLoanPatchesEnum statusEnum, Integer payoffState) {
        AfterLoanPatchesEntity afterLoanPatchesEntity = afterLoanPatchesEntityMapper.selectOne(
                new LambdaQueryWrapper<AfterLoanPatchesEntity>()
                        .eq(AfterLoanPatchesEntity::getOrderId, orderId)
                        .eq(AfterLoanPatchesEntity::getDeleteFlag, 0)
        );
        Asserts.notNull(afterLoanPatchesEntity, "该资产委外不存在贷后补件信息");
        afterLoanPatchesEntity.setAfterLoanStatus(statusEnum.getCode());
        afterLoanPatchesEntity.setPayoffState(payoffState);
        afterLoanPatchesEntityMapper.updateById(afterLoanPatchesEntity);
    }

    @Override
    public Page<CaseInfoStatusRecordsEntity> getCaseInfoStatusRecordsList(Integer caseInfoId) {
        log.info("getCaseInfoStatusRecordsList.caseInfoId:{}", caseInfoId);
        Asserts.notNull(caseInfoId, "案件id不能为空");
        Page<CaseInfoStatusRecordsEntity> caseInfoStatusRecordsEntityPage = caseInfoStatusRecordsMapper.selectPage(new Page<>(1, 10), new LambdaQueryWrapper<CaseInfoStatusRecordsEntity>().eq(CaseInfoStatusRecordsEntity::getCaseInfoId, caseInfoId).eq(CaseInfoStatusRecordsEntity::getDeleteFlag, 0).orderByDesc(CaseInfoStatusRecordsEntity::getCreateTime));
        return caseInfoStatusRecordsEntityPage;
    }


    @Override
    public CaseInfoDetailVO getCaseInfoDetail(Integer caseInfoId) {
        log.info("getCaseInfoDetail.caseInfoId:{}", caseInfoId);
//        CaseInfoDetailVO caseInfoDetailVO = caseInfoEntityMapper.getCaseInfoDetail(caseInfoId);


        CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getId, caseInfoId).eq(CaseInfoEntity::getDeleteFlag, 0).last("limit 1"));
        CaseInfoDetailVO caseInfoDetailVO = new CaseInfoDetailVO();
        DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = new DigitalOutsourcingOrderEntity();
        //如果数字化的更新订单详情
        if (Objects.equals(2, caseInfoEntity.getDataSource())) {
            DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderDTO = new DigitalizeWeiwaiOrderStatusDTO();
            digitalizeWeiwaiOrderDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
            DigitalizeRepaymentDetailVO weiwaiOrderPayApplyDetail = digitalizeFeign.getWeiwaiOrderApplyRepayment(digitalizeWeiwaiOrderDTO);
            log.info("weiwaiOrderPayApply444444:{}", weiwaiOrderPayApplyDetail);

            digitalOutsourcingOrderEntity = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderId, caseInfoEntity.getDigitalOrderId()).eq(DigitalOutsourcingOrderEntity::getDataSource, caseInfoEntity.getDataSource()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0).orderByDesc(DigitalOutsourcingOrderEntity::getCreateTime).last("limit 1"));
            if (ObjUtil.isNotEmpty(digitalOutsourcingOrderEntity) && ObjUtil.isNotEmpty(weiwaiOrderPayApplyDetail) && ObjUtil.isNotEmpty(weiwaiOrderPayApplyDetail.getDetails())) {

                if (ObjUtil.isNotEmpty(weiwaiOrderPayApplyDetail.getDetails().getCurrent_overdue_day())) {
                    digitalOutsourcingOrderEntity.setOverdueDays((Integer) weiwaiOrderPayApplyDetail.getDetails().getCurrent_overdue_day());
                } else {
                    digitalOutsourcingOrderEntity.setOverdueDays(0);
                }
                digitalOutsourcingOrderEntity.setOverdueStatusDesc(weiwaiOrderPayApplyDetail.getDetails().getCurrent_overdue_status());
                if (ObjUtil.isNotEmpty(weiwaiOrderPayApplyDetail.getDetails().getCurrent_residue_benjin())) {
                    BigDecimal remain = new BigDecimal(weiwaiOrderPayApplyDetail.getDetails().getCurrent_residue_benjin());
                    digitalOutsourcingOrderEntity.setRemainingPrincipal(remain);
                } else {
                    digitalOutsourcingOrderEntity.setRemainingPrincipal(new BigDecimal(0));
                }

                if (ObjUtil.isNotEmpty(weiwaiOrderPayApplyDetail.getDetails().getUsage_day())) {
                    digitalOutsourcingOrderEntity.setUsageDays((Integer) weiwaiOrderPayApplyDetail.getDetails().getUsage_day());
                }
                if (ObjUtil.isNotEmpty(weiwaiOrderPayApplyDetail.getDetails().getIs_redoom())) {
                    digitalOutsourcingOrderEntity.setIsRepurchase((Integer) weiwaiOrderPayApplyDetail.getDetails().getIs_redoom());
                }
                if (ObjUtil.isNotEmpty(weiwaiOrderPayApplyDetail.getDetails().getCurrent_overdue_total_amount())) {
                    digitalOutsourcingOrderEntity.setTotalOverdueAmount(weiwaiOrderPayApplyDetail.getDetails().getCurrent_overdue_total_amount().toString());
                }


                digitalOutsourcingOrderEntityMapper.updateById(digitalOutsourcingOrderEntity);

            }

        }
        DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity1=new DigitalOutsourcingOrderEntity();
        if(Objects.equals(3, caseInfoEntity.getDataSource())){
            //查询金蝶详情更新数据
            DigitizedOverdueOrdersVO digitizedOverdueOrdersVO = kingdeeOutsourcingService.getkingdeeOutsourcingOrderDetail(caseInfoEntity.getDigitalOrderId());
            log.info("getkingdeeOutsourcingOrderDetail.digitalOrderId:{}", digitizedOverdueOrdersVO);
             digitalOutsourcingOrderEntity1 = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderId, caseInfoEntity.getDigitalOrderId()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0).eq(DigitalOutsourcingOrderEntity::getDataSource, caseInfoEntity.getDataSource()).orderByDesc(DigitalOutsourcingOrderEntity::getCreateTime).last("limit 1"));

            if (ObjUtil.isNotEmpty(digitizedOverdueOrdersVO)) {
                if (ObjUtil.isNotEmpty(digitizedOverdueOrdersVO.getOverdueDays())) {
                    digitalOutsourcingOrderEntity1.setOverdueDays(digitizedOverdueOrdersVO.getOverdueDays());
                } else {
                    digitalOutsourcingOrderEntity1.setOverdueDays(0);
                }
                if (ObjUtil.isNotEmpty(digitizedOverdueOrdersVO.getOverdueDate())) {
                    digitalOutsourcingOrderEntity1.setOverdueDate(digitizedOverdueOrdersVO.getOverdueDate());
                } else {
                    digitalOutsourcingOrderEntity1.setOverdueDate("");
                }
                if (ObjUtil.isNotEmpty(digitizedOverdueOrdersVO.getRemainingPrincipal())) {
                    digitalOutsourcingOrderEntity1.setRemainingPrincipal(digitizedOverdueOrdersVO.getRemainingPrincipal());
                    digitalOutsourcingOrderEntity1.setPrincipalAmount(digitizedOverdueOrdersVO.getRemainingPrincipal().toString());
                } else {
                    digitalOutsourcingOrderEntity1.setRemainingPrincipal(new BigDecimal(0));
                }
                if (ObjUtil.isNotEmpty(digitizedOverdueOrdersVO.getTotalOverdueAmount())) {
                    digitalOutsourcingOrderEntity1.setTotalOverdueAmount(digitizedOverdueOrdersVO.getTotalOverdueAmount());
                } else {
                    digitalOutsourcingOrderEntity1.setTotalOverdueAmount("0");
                }

                digitalOutsourcingOrderEntity1.setOverdueStatusDesc(digitizedOverdueOrdersVO.getOverdueStatusDesc());
                if (ObjUtil.isNotEmpty(digitizedOverdueOrdersVO.getUsageDays())) {
                    digitalOutsourcingOrderEntity1.setUsageDays(digitizedOverdueOrdersVO.getUsageDays());
                }
                digitalOutsourcingOrderEntity1.setIsRepurchase(digitizedOverdueOrdersVO.getIsRepurchase());

                digitalOutsourcingOrderEntityMapper.updateById(digitalOutsourcingOrderEntity1);

            }
        }


        if (Objects.equals(1, caseInfoEntity.getDataSource())) {

            List<CaseInfoDetailVO> caseInfoDetailVOS = caseInfoEntityMapper.getCaseInfoDetails(caseInfoId);
            if (CollUtil.isNotEmpty(caseInfoDetailVOS)) {
                caseInfoDetailVO = caseInfoDetailVOS.get(0);
            }
            LocalDate beginDate = LocalDate.now();
            LocalDateTime now = LocalDateTime.now();
            //赎回的再还期数
            OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>().eq(OrderInfoEntity::getId, caseInfoDetailVO.getOrderId()).eq(OrderInfoEntity::getDeleteFlag, 0));
            if (ObjUtil.isNotEmpty(orderInfo) && ObjUtil.equal(orderInfo.getIsRepurchase(), 1)) {
                RepurchaseRepaymentInfoEntity repurchaseRepaymentInfoEntity = repurchaseRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>().eq(RepurchaseRepaymentInfoEntity::getOrderId, caseInfoDetailVO.getOrderId()).eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0).ne(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED).orderByAsc(RepurchaseRepaymentInfoEntity::getTerm).last("limit 1"));
                if (ObjUtil.isNotEmpty(repurchaseRepaymentInfoEntity)) {
                    caseInfoDetailVO.setReturnTerm(repurchaseRepaymentInfoEntity.getTerm());
                }
                RepurchaseRepaymentInfoEntity repurchaseRepaymentInfo = repurchaseRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                        .eq(RepurchaseRepaymentInfoEntity::getOrderId, caseInfoDetailVO.getOrderId())
                        .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                        .eq(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .orderByDesc(RepurchaseRepaymentInfoEntity::getTerm)
                        .last("limit 1"));

                //使用天数
                if (ObjectUtil.isNotEmpty(repurchaseRepaymentInfo)) {
                    caseInfoDetailVO.setUsedDays((int) ChronoUnit.DAYS.between(repurchaseRepaymentInfo.getRepaymentDate(), beginDate));
                } else {
                    caseInfoDetailVO.setUsedDays((int) ChronoUnit.DAYS.between(orderInfo.getPaymentTime(), now));
                }

                    if(ObjUtil.isNotEmpty(repurchaseRepaymentInfoEntity)&&ObjUtil.isNotEmpty(repurchaseRepaymentInfoEntity.getRepaymentDate())){
                        caseInfoDetailVO.setOverdueDate(repurchaseRepaymentInfoEntity.getRepaymentDate().toString());
                    }



            } else {
                FundRepaymentInfoEntity fundRepaymentInfo = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>().eq(FundRepaymentInfoEntity::getOrderId, caseInfoDetailVO.getOrderId()).eq(FundRepaymentInfoEntity::getDeleteFlag, 0).ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED).orderByAsc(FundRepaymentInfoEntity::getTerm).last("limit 1"));
                if (ObjUtil.isNotEmpty(fundRepaymentInfo)) {
                    caseInfoDetailVO.setReturnTerm(fundRepaymentInfo.getTerm());
                }
                FundRepaymentInfoEntity repaymentInfoEntity = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, caseInfoDetailVO.getOrderId())
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                        .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .orderByDesc(FundRepaymentInfoEntity::getTerm)
                        .last("limit 1"));
                //使用天数
                if (ObjectUtil.isNotEmpty(repaymentInfoEntity)) {
                    caseInfoDetailVO.setUsedDays((int) ChronoUnit.DAYS.between(repaymentInfoEntity.getRepaymentDate(), beginDate));
                } else {
                    caseInfoDetailVO.setUsedDays((int) ChronoUnit.DAYS.between(orderInfo.getPaymentTime(), now));
                }

            }


            if (ObjUtil.isNotNull(caseInfoDetailVO) && StrUtil.isNotBlank(caseInfoDetailVO.getImageList())) {
                caseInfoDetailVO.setAttachmentList(JSONUtil.toList(caseInfoDetailVO.getImageList(), AttachmentListDTO.class));
            }
            if (ObjUtil.isNotNull(caseInfoDetailVO) && ObjUtil.isNotNull(caseInfoDetailVO.getOrderId())) {
                RepaymentOverdueStatusVO repaymentOverdueStatusVO = repaymentService.overdueStatusByOrderId(caseInfoDetailVO.getOrderId());
                caseInfoDetailVO.setRemainPrincipal(repaymentOverdueStatusVO.getRemainingPrincipal());
                caseInfoDetailVO.setHaveTerm(repaymentOverdueStatusVO.getAlreadyRepaidInstalments());
            }
            //查看委外结清金额
            List<ExemptionApplicationEntity> exemptionApplicationEntities = exemptionApplicationEntityMapper.selectList(new LambdaQueryWrapper<ExemptionApplicationEntity>().eq(ExemptionApplicationEntity::getCaseId, caseInfoId).eq(ExemptionApplicationEntity::getDeleteFlag, 0).eq(ExemptionApplicationEntity::getReduceCurrentNode, ReduceApproveNodeEnums.SUCCESS.getCode()));
            if (CollUtil.isNotEmpty(exemptionApplicationEntities)) {
                BigDecimal reduceSettlementAmount = exemptionApplicationEntities.stream().map(ExemptionApplicationEntity::getApplySettlementAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                caseInfoDetailVO.setApplySettleAmount(reduceSettlementAmount);
            }

            OrderVehicleGpsLogEntity entity = orderVehicleGpsLogMapper.selectOne(new LambdaQueryWrapper<OrderVehicleGpsLogEntity>().
                    eq(OrderVehicleGpsLogEntity::getOrderId, caseInfoEntity.getOrderId())
                    .eq(OrderVehicleGpsLogEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderVehicleGpsLogEntity::getCreateTime).last("limit 1"));
            if (ObjUtil.isNotNull(entity) && ObjUtil.isNotNull(entity.getAddress())) {
                caseInfoDetailVO.setAddress(entity.getAddress());
            }
            CaseInfoRepayCalcVO caseInfoRepayCalcVO = outsourcingExpansionService.getLockearlyRepayCalc(caseInfoEntity.getOrderId());
            if(ObjUtil.isNotEmpty(caseInfoRepayCalcVO)){
                caseInfoDetailVO.setUsedDays(caseInfoRepayCalcVO.getUsageDays());
            }


        } else if (Objects.equals(2, caseInfoEntity.getDataSource()) || Objects.equals(3, caseInfoEntity.getDataSource())) {
            List<CaseInfoDetailVO> digitalOutsourcingOrderEntityList = digitalOutsourcingOrderEntityMapper.selectJoinList(CaseInfoDetailVO.class,
                    new MPJLambdaWrapper<DigitalOutsourcingOrderEntity>()
                            .selectAs(DigitalOutsourcingOrderEntity::getFundName, CaseInfoDetailVO::getFundName)
                            .selectAs(DigitalOutsourcingOrderEntity::getOrderId, CaseInfoDetailVO::getOrderNumber)
                            .selectAs(DigitalOutsourcingOrderEntity::getCustomerName, CaseInfoDetailVO::getCustomerName)
                            .selectAs(DigitalOutsourcingOrderEntity::getProductName, CaseInfoDetailVO::getProductName)
                            .selectAs(DigitalOutsourcingOrderEntity::getStoreName, CaseInfoDetailVO::getStoreName)
                            .selectAs(DigitalOutsourcingOrderEntity::getContractAmount, CaseInfoDetailVO::getCustomerConfirmAmount)
                            .selectAs(DigitalOutsourcingOrderEntity::getRemainingPrincipal, CaseInfoDetailVO::getRemainPrincipal)
                            .selectAs(DigitalOutsourcingOrderEntity::getOverdueDays, CaseInfoDetailVO::getOverdueDays)
                            .selectAs(DigitalOutsourcingOrderEntity::getVehicleBrand, CaseInfoDetailVO::getVehicleBrand)
                            .selectAs(DigitalOutsourcingOrderEntity::getRegionName, CaseInfoDetailVO::getRegionName)
                            .selectAs(DigitalOutsourcingOrderEntity::getOverdueDate, CaseInfoDetailVO::getOverdueDate)
                            .selectAs(DigitalOutsourcingOrderEntity::getOverdueStatus, CaseInfoDetailVO::getOderState)
                            .selectAs(DigitalOutsourcingOrderEntity::getVehicleNumber, CaseInfoDetailVO::getVehicleNumber)
                            .selectAs(DigitalOutsourcingOrderEntity::getIdNumber, CaseInfoDetailVO::getIdNumber)
                            .selectAs(DigitalOutsourcingOrderEntity::getCustomerPhone, CaseInfoDetailVO::getCustomerPhone)
                            .selectAs(DigitalOutsourcingOrderEntity::getDocumentInformationAttachment, CaseInfoDetailVO::getDocumentInformationAttachment)
                            .selectAs(DigitalOutsourcingOrderEntity::getTerm, CaseInfoDetailVO::getReturnTerm)
                            .selectAs(DigitalOutsourcingOrderEntity::getVin, CaseInfoDetailVO::getVin)
                            .selectAs(DigitalOutsourcingOrderEntity::getOverdueStatusDesc, CaseInfoDetailVO::getOderStateDesc)
                            .selectAs(DigitalOutsourcingOrderEntity::getHaveTerm, CaseInfoDetailVO::getHaveTerm)
                            .selectAs(DigitalOutsourcingOrderEntity::getRepayMethod, CaseInfoDetailVO::getInterestType)
                            .selectAs(DigitalOutsourcingOrderEntity::getUsageDays, CaseInfoDetailVO::getUsedDays)
                            .selectAs(DigitalOutsourcingOrderEntity::getIsRepurchase, CaseInfoDetailVO::getIsRepurchase)
                            .selectAs(CaseInfoEntity::getId, CaseInfoDetailVO::getCaseInfoid)
                            .selectAs(CaseInfoEntity::getDigitalOrderId, CaseInfoDetailVO::getDigitalOrderId)
                            .selectAs(CaseInfoEntity::getDataSource, CaseInfoDetailVO::getDataSource)
                            .selectAs(CaseInfoEntity::getDisposalCompany, CaseInfoDetailVO::getDisposalCompany)
                            .selectAs(CaseInfoEntity::getDeptId, CaseInfoDetailVO::getDeptId)
                            .selectAs(CaseInfoEntity::getCirculationType, CaseInfoDetailVO::getCirculationType)
                            .selectAs(CaseInfoEntity::getRemark, CaseInfoDetailVO::getRemark)
                            .selectAs(CaseInfoEntity::getAttachmentList, CaseInfoDetailVO::getImageList)
                            .leftJoin(CaseInfoEntity.class, CaseInfoEntity::getDigitalOrderId, DigitalOutsourcingOrderEntity::getOrderId)
                            .eq(CaseInfoEntity::getId, caseInfoId)
                            .eq(CaseInfoEntity::getDeleteFlag, 0)
                            .eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0)
                            .orderByDesc(DigitalOutsourcingOrderEntity::getId)
            );
            caseInfoDetailVO = digitalOutsourcingOrderEntityList.stream().findFirst().orElse(null);
            if(ObjUtil.equal(caseInfoEntity.getDataSource(),2)){
                DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderDTO = new DigitalizeWeiwaiOrderStatusDTO();
                digitalizeWeiwaiOrderDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                DigitalizeSettleList lockdigitalizeSettleList = outsourcingExpansionService.getLockdigitalizeSettleList(digitalizeWeiwaiOrderDTO);
                if(ObjUtil.isNotEmpty(lockdigitalizeSettleList)){
                    caseInfoDetailVO.setUsedDays(lockdigitalizeSettleList.getUsageDays());
                }else {
                    caseInfoDetailVO.setUsedDays(digitalOutsourcingOrderEntity.getUsageDays());
                }
            }
            if(Objects.equals(caseInfoEntity.getDataSource(),3)){
                ReductionRecordConditionalVO reductionRecordConditionalVO = outsourcingExpansionService.getLockKingdeeEarlyRepayCalc(caseInfoEntity.getCaseNo());
                if(ObjUtil.isNotEmpty(reductionRecordConditionalVO)){
                    caseInfoDetailVO.setUsedDays(reductionRecordConditionalVO.getUsageDays());
                }else {
                    caseInfoDetailVO.setUsedDays(digitalOutsourcingOrderEntity1.getUsageDays());
                }

            }
            if (ObjUtil.isNotNull(caseInfoDetailVO) && StrUtil.isNotBlank(caseInfoDetailVO.getImageList())) {
                caseInfoDetailVO.setAttachmentList(JSONUtil.toList(caseInfoDetailVO.getImageList(), AttachmentListDTO.class));
            }
            //查看委外结清金额
            List<ExemptionApplicationEntity> exemptionApplicationEntities = exemptionApplicationEntityMapper.selectList(new LambdaQueryWrapper<ExemptionApplicationEntity>().eq(ExemptionApplicationEntity::getCaseId, caseInfoId).eq(ExemptionApplicationEntity::getDeleteFlag, 0).eq(ExemptionApplicationEntity::getReduceCurrentNode, ReduceApproveNodeEnums.SUCCESS.getCode()).orderByDesc(ExemptionApplicationEntity::getCreateTime));
            if (CollUtil.isNotEmpty(exemptionApplicationEntities)) {
                BigDecimal reduceSettlementAmount = exemptionApplicationEntities.stream().map(ExemptionApplicationEntity::getApplySettlementAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                caseInfoDetailVO.setApplySettleAmount(reduceSettlementAmount);
                //获取申请减免结清金额
                caseInfoDetailVO.setReductionSettleAmount(exemptionApplicationEntities.get(0).getReceivableSettlementAmount());
            }
        }

        return caseInfoDetailVO;
    }


    @Override
    public Boolean addLawsuitTaskPool() {
        MPJLambdaWrapper<OrderInfoEntity> queryWrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .selectAll(OrderInfoEntity.class)
                .eq(OrderInfoEntity::getIsRepurchase, 1)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .notInSql(OrderInfoEntity::getId, "select order_id from LH_Lawsuit_Application where " +
                        "  delete_flag = 0")
                .orderByDesc(OrderInfoEntity::getCreateTime);
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectJoinList(OrderInfoEntity.class, queryWrapper);
        if (CollUtil.isNotEmpty(orderInfoEntityList)) {
            orderInfoEntityList.forEach(item -> {
                // 进入诉讼池
                LawsuitApplicationEntity lawsuitApplicationEntity = new LawsuitApplicationEntity();
                lawsuitApplicationEntity.setOrderId(item.getId());
                lawsuitApplicationEntity.setLawsuitForm(0);
                lawsuitApplicationEntity.setProcessingStatus(0);
                lawsuitApplicationEntity.setDisposalCompany("爱裁");
                lawsuitApplicationEntity.setDeptId(DeptEnum.AI_CAI.getId());
                lawsuitApplicationEntity.setApprovalStatus(0);
                lawsuitApplicationEntity.setPrincipal("系统发起");

                lawsuitApplicationEntityMapper.insert(lawsuitApplicationEntity);
                //生成法诉附件
                lawsuitService.getComplaintExtraction(item.getId());
                lawsuitService.getApplicationBook(item.getId());
                LawsuitContractDTO lawsuitContractDTO = new LawsuitContractDTO();
                lawsuitContractDTO.setOrderId(item.getId());
                lawsuitService.createCompanyContract(lawsuitContractDTO);
                DebtTransferNoticeDTO debtTransferNoticeDTO = new DebtTransferNoticeDTO();
                debtTransferNoticeDTO.setOrderId(item.getId());
                lawsuitService.debtTransferNotice(debtTransferNoticeDTO);
                DebtTransferNoticeDTO debtTransferNoticeDTO1 = new DebtTransferNoticeDTO();
                debtTransferNoticeDTO1.setOrderId(item.getId());
                lawsuitService.getDeliveryAddressConfirmation(debtTransferNoticeDTO1);

            });
        }


        return true;


    }

    @Override
    public Page<CaseInfoNodeRecordListVO> getCaseInfoApplyList(CaseInfoNodeRecordDTO caseInfoNodeRecordDTO) {
//        Asserts.notNull(caseInfoNodeRecordDTO, "委外id不能为空");
        log.info("CaseInfoServiceImpl getCaseInfoApplyList.caseInfoId", caseInfoNodeRecordDTO);
        MPJLambdaWrapper<CaseInfoApproveEntity> lambdaWrapper = new MPJLambdaWrapper<>();
        lambdaWrapper.eq(CaseInfoApproveEntity::getCaseInfoRequestId, caseInfoNodeRecordDTO.getCaseInfoId());
        lambdaWrapper.eq(CaseInfoApproveEntity::getDeleteFlag, 0);
        lambdaWrapper.eq(ObjectUtil.isNotEmpty(caseInfoNodeRecordDTO.getApproveType()), CaseInfoApproveEntity::getApproveType, caseInfoNodeRecordDTO.getApproveType());
        lambdaWrapper.orderByDesc(CaseInfoApproveEntity::getCreateTime);
        Page<CaseInfoNodeRecordListVO> caseInfoNodeRecordListVOPage = caseInfoNodeRecordEntityMapper.selectJoinPage(new Page<>(caseInfoNodeRecordDTO.getPageNum(), caseInfoNodeRecordDTO.getPageSize()), CaseInfoNodeRecordListVO.class, lambdaWrapper);
        List<CaseInfoNodeRecordListVO> records = caseInfoNodeRecordListVOPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<Integer> updateUserIdList = records.stream()
                    .map(CaseInfoNodeRecordListVO::getApproveUserId)
                    .distinct() // 避免重复的 userId
                    .collect(Collectors.toList());

            Result<List<UserInfoVO>> updateUserListResult = userFeign.searchUserNameByUserIds(updateUserIdList);
            List<UserInfoVO> userInfoVOList = Result.isSuccess(updateUserListResult)
                    ? updateUserListResult.getData()
                    : Collections.emptyList();

            Map<Integer, UserInfoVO> userInfoMap = userInfoVOList.stream()
                    .collect(Collectors.toMap(UserInfoVO::getUserId, Function.identity()));

            records.forEach(record -> {
                UserInfoVO userInfo = userInfoMap.get(record.getApproveUserId());
                if (ObjectUtil.isNotNull(userInfo)) {
                    record.setApproveUserName(userInfo.getName());
                }
            });
        }

        return caseInfoNodeRecordListVOPage;
    }


    @Override
    public Boolean updatePreservationStateAndTime() {
        log.info("CaseInfoServiceImpl.updatePreservationStateAndTime start");
        try {
            Map<String, CaseInfoUserDTO> carNumberMap = dingFeign.getCarNumberList();
            List<OrderInfoEntity> orderList = new ArrayList<>();
            carNumberMap.forEach((carNumber, caseInfoUserDTO) -> {
                if (ObjectUtil.isNotNull(caseInfoUserDTO)) {
                    OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                            .eq(OrderInfoEntity::getVehicleNumber, carNumber)
                            .eq(OrderInfoEntity::getCustomerName, caseInfoUserDTO.getName())
                            .eq(OrderInfoEntity::getDeleteFlag, 0)
                            .eq(OrderInfoEntity::getCurrentNode, States.PAYMENT_SUCCESS.getNode())
                    );
                    orderList.add(orderInfoEntity);
                }
            });

            List<String> carNumberList = new ArrayList<>(carNumberMap.keySet());

            Map<String, List<OrderInfoEntity>> map = orderList.stream().collect(Collectors.groupingBy(OrderInfoEntity::getVehicleNumber));

            if (CollUtil.isEmpty(map)) {
                log.info("CaseInfoServiceImpl.updatePreservationStateAndTime orderList map :{}", map);
                log.info("CaseInfoServiceImpl.updatePreservationStateAndTime end");
                return true;
            }
            log.info("CaseInfoServiceImpl.updatePreservationStateAndTime orderList map :{}", map);
            carNumberMap.forEach((carNumber, caseInfoUserDTO) -> {
                System.out.println(map.get(carNumber));
                if (CollUtil.isNotEmpty(map.get(carNumber))) {
                    List<CaseInfoEntity> caseInfoEntity = caseInfoEntityMapper.selectList(new LambdaQueryWrapper<CaseInfoEntity>()
                            .eq(CaseInfoEntity::getOrderId, map.get(carNumber).get(0).getId())
                            .eq(CaseInfoEntity::getDeleteFlag, 0)
                    );
                    caseInfoEntity.stream().forEach(caseInfo -> {
                        caseInfo.setPreservationState(1);
                        caseInfo.setPreservationTime(carNumberMap.get(carNumber).getDate());
                        caseInfoEntityMapper.updateById(caseInfo);
                        log.info("CaseInfoServiceImpl.updatePreservationStateAndTime caseInfoEntity :{}", caseInfo);
                    });

                }
            });
            log.info("CaseInfoServiceImpl.updatePreservationStateAndTime end");
            return true;
        } catch (Exception e) {
            throw new BusinessException("更新保全状态失败");
        }
    }


    @Override
    public List<CaseCirculationDeptRecordEntity> getFlowDepartmentRecordsList(String caseNo) {
        List<CaseCirculationDeptRecordEntity> caseCirculationDeptRecordEntities = caseCirculationDeptRecordMapper.selectList(new LambdaQueryWrapper<CaseCirculationDeptRecordEntity>().eq(ObjectUtil.isNotEmpty(caseNo), CaseCirculationDeptRecordEntity::getCaseNo, caseNo).eq(CaseCirculationDeptRecordEntity::getDeleteFlag, 0).orderByDesc(CaseCirculationDeptRecordEntity::getCreateTime));
        return caseCirculationDeptRecordEntities;

    }

    @Override
    public List<OrderFeeDetailEntity> getCasePaymentRecordsList(String caseNo) {
        List<OrderFeeDetailEntity> orderFeeDetailEntities = orderFeeDetailMapper.selectList(new LambdaQueryWrapper<OrderFeeDetailEntity>().eq(OrderFeeDetailEntity::getCaseNo, caseNo).eq(OrderFeeDetailEntity::getDeleteFlag, 0).eq(OrderFeeDetailEntity::getAuditStatus, 2).orderByDesc(OrderFeeDetailEntity::getCreateTime));

        return orderFeeDetailEntities;
    }

    @Override
    public Boolean transferCaseInfo(TransferCaseInfoDTO transferCaseInfoDTO) {
        log.info("CaseInfoServiceImpl.transferCaseInfo start", transferCaseInfoDTO);
        Assert.notNull(transferCaseInfoDTO.getLawsuitIds(), () -> {
            throw new BusinessException("请选择案件");
        });
        List<Integer> lawsuitIds = transferCaseInfoDTO.getLawsuitIds();
        lawsuitIds.forEach(lawsuitId -> {
                    //查询法诉信息
                    LawsuitApplicationEntity lawsuitApplicationEntity = lawsuitApplicationEntityMapper.selectOne(new LambdaQueryWrapper<LawsuitApplicationEntity>().eq(LawsuitApplicationEntity::getId, lawsuitId).eq(LawsuitApplicationEntity::getDeleteFlag, 0));
                    lawsuitApplicationEntity.setDisposalCompany(transferCaseInfoDTO.getDisposalCompany());
                    lawsuitApplicationEntity.setDeptId(transferCaseInfoDTO.getDeptId());
                    int i = lawsuitApplicationEntityMapper.updateById(lawsuitApplicationEntity);
                    //添加转移记录
                    CaseCirculationDeptRecordEntity caseCirculationDeptRecordEntity = new CaseCirculationDeptRecordEntity();
                    caseCirculationDeptRecordEntity.setLawsuitId(lawsuitId);
                    caseCirculationDeptRecordEntity.setCirculationDept(transferCaseInfoDTO.getDisposalCompany());
                    caseCirculationDeptRecordEntity.setDeptId(transferCaseInfoDTO.getDeptId());
                    caseCirculationDeptRecordMapper.insert(caseCirculationDeptRecordEntity);

                }
        );
        return true;

    }

    @Override
    public Boolean addCallRecord(AddCallRecordDTO addCallRecordDTO, LoginUser loginUser) {
        log.info("CaseInfoServiceImpl.addCallRecord start", addCallRecordDTO);
        if (ObjUtil.isNotNull(addCallRecordDTO.getElectricReminderId())) {
            ElectricReminderRecordEntity electricReminderRecordEntity = electricReminderRecordEntityMapper.selectOne(new LambdaQueryWrapper<ElectricReminderRecordEntity>().eq(ElectricReminderRecordEntity::getId, addCallRecordDTO.getElectricReminderId()).eq(ElectricReminderRecordEntity::getDeleteFlag, 0));
            electricReminderRecordEntity.setElectricReminderRecord(addCallRecordDTO.getElectricReminderRecord());
            electricReminderRecordEntityMapper.updateById(electricReminderRecordEntity);
        } else {
            ElectricReminderRecordEntity electricReminderRecordEntity = new ElectricReminderRecordEntity();
            electricReminderRecordEntity.setOrderId(addCallRecordDTO.getOrderId());
            electricReminderRecordEntity.setElectricReminderRecord(addCallRecordDTO.getElectricReminderRecord());
            electricReminderRecordEntity.setElectricPersonId(loginUser.getUserId());
            electricReminderRecordEntity.setElectricPerson(loginUser.getName());
            electricReminderRecordEntityMapper.insert(electricReminderRecordEntity);

        }
        return null;
    }

    @Override
    public List<ElectricReminderRecordEntity> getCallRecordList(Integer orderId) {
        log.info("CaseInfoServiceImpl.getCallRecordList start", orderId);
        List<ElectricReminderRecordEntity> electricReminderRecordEntities = electricReminderRecordEntityMapper.selectList(new LambdaQueryWrapper<ElectricReminderRecordEntity>().eq(ElectricReminderRecordEntity::getOrderId, orderId).eq(ElectricReminderRecordEntity::getDeleteFlag, 0).orderByDesc(ElectricReminderRecordEntity::getCreateTime));
        return electricReminderRecordEntities;
    }

    @Override
    public Page<OverdueOrdersListVO> overdueOrdersList(OverdueOrdersDTO overdueOrdersDTO, LoginUser loginUser) {
        MPJLambdaWrapper<OrderInfoEntity> wrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .selectAll(OrderInfoEntity.class)
                .selectAs(OrderInfoEntity::getId, OrderApproveListVO::getOrderId)
                .selectAs(OrderInfoEntity::getRiskUserId, OrderApproveListVO::getRisiUserId)
                .selectAs(FundInfoEntity::getName, OrderApproveListVO::getFundName)
                .selectAs(OrderAmountEntity::getCustomerConfirmAmount, OverdueOrdersListVO::getCustomerAmount)

                .leftJoin(FundInfoEntity.class, FundInfoEntity::getId, OrderInfoEntity::getFundId)
                .leftJoin(OrderAmountEntity.class, OrderAmountEntity::getOrderId, OrderInfoEntity::getId)
                .like(StrUtil.isNotBlank(overdueOrdersDTO.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, overdueOrdersDTO.getVehicleNumber())
                .like(StrUtil.isNotBlank(overdueOrdersDTO.getOrderNumber()), OrderInfoEntity::getOrderNumber, overdueOrdersDTO.getOrderNumber())
                .like(StrUtil.isNotBlank(overdueOrdersDTO.getCustomerName()), OrderInfoEntity::getCustomerName, overdueOrdersDTO.getCustomerName())
                .like(StrUtil.isNotBlank(overdueOrdersDTO.getCustomerPhone()), OrderInfoEntity::getCustomerPhone, overdueOrdersDTO.getCustomerPhone())
                .eq(ObjUtil.isNotNull(overdueOrdersDTO.getIsSyncAfterSale()), OrderInfoEntity::getIsSyncAfterSale, overdueOrdersDTO.getIsSyncAfterSale())
                .eq(OrderInfoEntity::getState, States.PAYMENT_SUCCESS.getNode())
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .eq(OrderInfoEntity::getIsOverdue, 1)
                .orderByAsc(OrderInfoEntity::getPaymentTime);


        // 数据权限
        dataPermissionService.limitElectricOverdueOrder(loginUser, wrapper);

        Page<OverdueOrdersListVO> pageList = orderInfoMapper.selectJoinPage(new Page<>(overdueOrdersDTO.getPageNum(), overdueOrdersDTO.getPageSize()), OverdueOrdersListVO.class, wrapper);
        List<OverdueOrdersListVO> records = pageList.getRecords();
//        records.forEach(item -> {
//            item.setCustomerPhone(StrUtil.hide(item.getCustomerPhone(), 0, 7));
//        });
        // 查询经办人信息
        List<Integer> manageIds = records.stream().map(OverdueOrdersListVO::getManagerId).filter(Objects::nonNull).toList();
        Result<List<UserStoreVO>> listResult = userFeign.searchUserStoreBatch(manageIds);
        List<Integer> teamIds = records.stream().map(OverdueOrdersListVO::getTeamId).filter(Objects::nonNull).toList();
        List<DeptInfoVO> branchNameVOS = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData();
        Map<Integer, String> branchNameMap = branchNameVOS.stream().filter(e -> ObjUtil.isNotNull(e.getName())).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
        if (Result.isSuccess(listResult)) {

            Map<Integer, UserStoreVO> managerInfos =
                    listResult.getData().stream().collect(Collectors.toMap(UserStoreVO::getUserId, item -> item));


            records.stream().filter(item -> item.getManagerId() != null).forEach(record -> {
                        UserStoreVO userInfoVOS = managerInfos.get(record.getManagerId());
                        record.setManagerName(userInfoVOS.getName());
                        record.setStoreName(userInfoVOS.getStore());
                        record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(branchNameMap.get(record.getTeamId()), ""));
                    }
            );

        }
        return pageList;
    }

    @Override
    public Boolean isSyncAfterSaleCall(SyncAfterSaleCallDTO syncAfterSaleCallDTO) {
        List<Integer> orderIds = syncAfterSaleCallDTO.getOrderId();
        if (CollUtil.isNotEmpty(orderIds)) {
            orderIds.forEach(orderId -> {
                //获取订单信息
                OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>().eq(OrderInfoEntity::getId, orderId).eq(OrderInfoEntity::getDeleteFlag, 0));
                Asserts.notNull(orderInfo, "未查询到订单信息");
                //更新订单售后同步状态
                orderInfo.setIsSyncAfterSale(syncAfterSaleCallDTO.getIsSyncAfterSale());
                int i = orderInfoMapper.updateById(orderInfo);
            });
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addReduction(AddReductionDTO addReductionDTO, LoginUser loginUser) {
        log.info("CaseInfoServiceImpl.addReduction AddReductionDTO:{}", addReductionDTO);
        Assert.notNull(addReductionDTO.getCaseId(), "委外信息不存在");
        String paymentVoucherList = "";
        if (CollUtil.isNotEmpty(addReductionDTO.getResourceId())) {
            paymentVoucherList = JSONUtil.toJsonStr(addReductionDTO.getResourceId());
        }
        log.info("CaseInfoServiceImpl.addReduction loginUser:{}", loginUser);
        //查询属于发起人
//        List<UserDingDeptMappingVO> userDingDeptMappingVOS = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setUserId(loginUser.getUserId()).setUserType(0));
//        Integer processType = 0;
//        List<Integer> roleIds = loginUser.getRoleIds();
//        if (CollUtil.isNotEmpty(userDingDeptMappingVOS)) {
//            UserDingDeptMappingVO userDingDeptMappingVO = userDingDeptMappingVOS.get(0);
//            if ((ObjUtil.equal(userDingDeptMappingVO.getDeptId(), DingDingDeptEnum.ZIGUANZHUGUAN_REGION.getId()))|| loginUser.getRoleIds()) {
//                processType = 1;
//            }
//        }

        //查看委外信息
        CaseInfoEntity caseInfo = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getId, addReductionDTO.getCaseId()).eq(CaseInfoEntity::getDeleteFlag, 0));
        Assert.notNull(caseInfo, "委外信息不存在");


        caseInfo.setReduceAmount(addReductionDTO.getReduceAmount());
        if(RoleEnum.ASSET_VICE_PRESIDENT_MANAGER.hasRole(loginUser.getRoleIds())){
            caseInfo.setReduceCurrentNode(ReduceApproveNodeEnums.REGION_GENERAL_MANAGER_APPROVED);
        }else {
            caseInfo.setReduceCurrentNode(ReduceApproveNodeEnums.ASSET_VICE_PRESIDENT_APPROVED);
        }

        int i = caseInfoEntityMapper.updateById(caseInfo);
        if (addReductionDTO.getExemptionApplicationId() != null) {
            ExemptionApplicationEntity exemptionApplicationEntity1 = exemptionApplicationEntityMapper.selectOne(new LambdaQueryWrapper<ExemptionApplicationEntity>().eq(ExemptionApplicationEntity::getId, addReductionDTO.getExemptionApplicationId()).eq(ExemptionApplicationEntity::getDeleteFlag, 0).last("limit 1"));
            //添加减免申请

//            exemptionApplicationEntity1.setExemptionType(ExemptionTypeEnum.getByCode(addReductionDTO.getExemptionType()));
//            exemptionApplicationEntity1.setPayType(addReductionDTO.getPayType());
//            exemptionApplicationEntity1.setClearingPlan(addReductionDTO.getClearingPlan());
//            exemptionApplicationEntity1.setIsReturnRebate(addReductionDTO.getIsReturnRebate());
//            exemptionApplicationEntity1.setIsExcludePerformance(addReductionDTO.getIsExcludePerformance());
//            exemptionApplicationEntity1.setApplyReductionAmount(addReductionDTO.getApplyReductionAmount());
//            exemptionApplicationEntity1.setApplySettlementAmount(addReductionDTO.getApplySettlementAmount());
            exemptionApplicationEntity1.setClearingType(addReductionDTO.getClearingType());
            exemptionApplicationEntity1.setApplySettlementAmount(addReductionDTO.getApplySettlementAmount());
            exemptionApplicationEntity1.setReceivableSettlementAmount(addReductionDTO.getReceivableSettlementAmount());
            exemptionApplicationEntity1.setApplyReason(addReductionDTO.getApplyReason());
            exemptionApplicationEntity1.setAttachment(paymentVoucherList);
//            if (processType != null && processType.equals(1)) {
            if(RoleEnum.ASSET_VICE_PRESIDENT_MANAGER.hasRole(loginUser.getRoleIds())){
                caseInfo.setReduceCurrentNode(ReduceApproveNodeEnums.REGION_GENERAL_MANAGER_APPROVED);
            }else {
                caseInfo.setReduceCurrentNode(ReduceApproveNodeEnums.ASSET_VICE_PRESIDENT_APPROVED);
            }

//            } else if (processType != null && processType.equals(2)) {
//                exemptionApplicationEntity1.setReduceCurrentNode(ReduceApproveNodeEnums.ASSET_APPROVED);
//            } else {
//                exemptionApplicationEntity1.setReduceCurrentNode(ReduceApproveNodeEnums.SUCCESS);
//            }


            exemptionApplicationEntity1.setUpdateBy(loginUser.getUserId());
            exemptionApplicationEntityMapper.updateById(exemptionApplicationEntity1);
            //添加
            addCaseInfoReductionApproveRecord(caseInfo.getId(), null, ReduceApproveNodeEnums.REDUCE_APPLY, exemptionApplicationEntity1.getReduceCurrentNode(), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), null, PayApplicationAuditTypeEnum.YUNQI);
            //特殊结清发起钉钉推送
//            if (addReductionDTO.getExemptionType() != null && addReductionDTO.getExemptionType().equals(3)) {
            initiatereductionDingTalkApproval(addReductionDTO.getResourceId(), exemptionApplicationEntity1, loginUser);
//            }

        } else {
            //查询是否有未完成的流程存在
            ExemptionApplicationEntity exemptionApplicationEntity3 = exemptionApplicationEntityMapper.selectOne(new LambdaQueryWrapper<ExemptionApplicationEntity>().eq(ExemptionApplicationEntity::getCaseId, addReductionDTO.getCaseId()).eq(ExemptionApplicationEntity::getDeleteFlag, 0).notIn(ExemptionApplicationEntity::getReduceCurrentNode, ReduceApproveNodeEnums.SUCCESS.getCode(), ReduceApproveNodeEnums.FAIL.getCode()).orderByDesc(ExemptionApplicationEntity::getCreateTime).last("limit 1"));
            if (ObjUtil.isNotNull(exemptionApplicationEntity3)) {
                throw new BusinessException("该订单存在未完成的减免流程，请先完成减免流程");
            }
            //添加减免申请
            ExemptionApplicationEntity exemptionApplicationEntity = new ExemptionApplicationEntity();
//            exemptionApplicationEntity.setExemptionType(ExemptionTypeEnum.getByCode(addReductionDTO.getExemptionType()));
//            exemptionApplicationEntity.setPayType(addReductionDTO.getPayType());
//            exemptionApplicationEntity.setClearingPlan(addReductionDTO.getClearingPlan());
//            exemptionApplicationEntity.setIsReturnRebate(addReductionDTO.getIsReturnRebate());
//            exemptionApplicationEntity.setIsExcludePerformance(addReductionDTO.getIsExcludePerformance());
//            exemptionApplicationEntity.setApplyReductionAmount(addReductionDTO.getApplyReductionAmount());
            exemptionApplicationEntity.setApplySettlementAmount(addReductionDTO.getApplySettlementAmount());
            exemptionApplicationEntity.setApplyReason(addReductionDTO.getApplyReason());
            exemptionApplicationEntity.setAttachment(paymentVoucherList);
            exemptionApplicationEntity.setCaseId(caseInfo.getId());
//            if (processType != null && processType.equals(1)) {
            exemptionApplicationEntity.setReduceCurrentNode(ReduceApproveNodeEnums.ASSET_VICE_PRESIDENT_APPROVED);
//            } else if (processType != null && processType.equals(2)) {
//                exemptionApplicationEntity.setReduceCurrentNode(ReduceApproveNodeEnums.ASSET_APPROVED);
//            } else {
//                exemptionApplicationEntity.setReduceCurrentNode(ReduceApproveNodeEnums.SUCCESS);
//            }

            exemptionApplicationEntity.setCreateBy(loginUser.getUserId());
            if (caseInfo.getDataSource().equals(1)) {
                exemptionApplicationEntity.setOrderId(caseInfo.getOrderId());
                exemptionApplicationEntity.setDataSource(1);
            } else if (caseInfo.getDataSource().equals(2)) {
                exemptionApplicationEntity.setDigitalOrderId(caseInfo.getDigitalOrderId());
                exemptionApplicationEntity.setDataSource(2);
            } else if (caseInfo.getDataSource().equals(3)) {
                exemptionApplicationEntity.setDigitalOrderId(caseInfo.getDigitalOrderId());
                exemptionApplicationEntity.setDataSource(3);
            }
//            exemptionApplicationEntity.setProcessType(processType);
            exemptionApplicationEntity.setClearingType(addReductionDTO.getClearingType());
            exemptionApplicationEntity.setReceivableSettlementAmount(addReductionDTO.getReceivableSettlementAmount());
            if (ObjUtil.isNotEmpty(addReductionDTO.getReceivableSettlementAmount()) && ObjUtil.isNotEmpty(addReductionDTO.getApplySettlementAmount())) {
                exemptionApplicationEntity.setApplyReductionAmount(addReductionDTO.getReceivableSettlementAmount().subtract(addReductionDTO.getApplySettlementAmount()));
            }
            exemptionApplicationEntityMapper.insert(exemptionApplicationEntity);
//            //todo 添加结清记录
            if(ObjUtil.equal(caseInfo.getDataSource(),1)){
                int usedDays;
           OrderInfoEntity orderInfoEntity=     orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>().eq(OrderInfoEntity::getId,caseInfo.getOrderId()).eq(OrderInfoEntity::getDeleteFlag,0).orderByDesc(OrderInfoEntity::getCreateTime).last("limit 1"));
                if(ObjUtil.isNotEmpty(orderInfoEntity)&&!ObjUtil.equal(orderInfoEntity.getIsRepurchase(),1)){
                    LocalDate beginDate = LocalDate.now();
                    LocalDateTime now = LocalDateTime.now();
                    CaseInfoRepayCalcVO caseInfoRepayCalcVO= this.earlyRepayCalc(caseInfo.getOrderId());
                    FundRepaymentInfoEntity repaymentInfoEntity = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                            .eq(FundRepaymentInfoEntity::getOrderId, caseInfo.getOrderId())
                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                            .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                            .orderByDesc(FundRepaymentInfoEntity::getTerm)
                            .last("limit 1"));
                    //使用天数
                    if (ObjectUtil.isNotEmpty(repaymentInfoEntity)) {
                        usedDays=(int) ChronoUnit.DAYS.between(repaymentInfoEntity.getRepaymentDate(), beginDate);
                    } else {
                        usedDays=(int) ChronoUnit.DAYS.between(orderInfoEntity.getPaymentTime(), now);
                    }
                OrderSettleAmountRecordEntity entity = new OrderSettleAmountRecordEntity();
                entity.setOrderId(caseInfo.getOrderId());
                entity.setOrderSource(1);
                entity.setOrderNumber(orderInfoEntity.getOrderNumber());
                if(ObjUtil.isNotEmpty(caseInfoRepayCalcVO)){
                    entity.setTrialSettlementAmount(caseInfoRepayCalcVO.getLoanSettlementAmount());
                    entity.setActualSettlementAmount(caseInfoRepayCalcVO.getApplySettlementAmount());
                    entity.setSettlementMethod(caseInfoRepayCalcVO.getLoanSettlementMethod());
                }
                    entity.setUsageDays(usedDays);
                entity.setOutsourcingStatus(1);
                entity.setCommitType(1);
                entity.setInstalmentsRepaid(caseInfoRepayCalcVO.getInstalmentsRepaid());

                orderSettleAmountRecordMapper.insert(entity);}

            }else if(ObjUtil.equal(caseInfo.getDataSource(),2)){
           DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity=     digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderId,caseInfo.getDigitalOrderId()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag,0).eq(DigitalOutsourcingOrderEntity::getDataSource,2).orderByDesc(DigitalOutsourcingOrderEntity::getCreateTime).last("limit 1"));
               if(ObjUtil.isNotEmpty(digitalOutsourcingOrderEntity)&&!ObjUtil.equal(digitalOutsourcingOrderEntity.getIsRepurchase(),1)){
           DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO=   new DigitalizeWeiwaiOrderStatusDTO();
                digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfo.getDigitalOrderId());
                Result<DigitalizeSettleList> digitalizeSettleList = digitalizeFeign.getDigitalizeSettleList(digitalizeWeiwaiOrderStatusDTO);
                log.info("DigitalizeServiceImpl.getDigitalizeSettleList result->{}", JSONUtil.toJsonStr(digitalizeSettleList));
                DigitalizeSettleList data = digitalizeSettleList.getData();
                DigitalizeRepaymentDetailVO weiwaiOrderPayApplyDetail = digitalizeFeign.getWeiwaiOrderApplyRepayment(digitalizeWeiwaiOrderStatusDTO);
                log.info("weiwaiOrderPayApply444444:{}", weiwaiOrderPayApplyDetail);
                Integer useDays=0;
                if(ObjUtil.isNotEmpty(weiwaiOrderPayApplyDetail)&&ObjUtil.isNotEmpty(weiwaiOrderPayApplyDetail.getDetails())){
                    if(ObjUtil.isNotEmpty(weiwaiOrderPayApplyDetail.getDetails().getUsage_day())){
                        useDays=(Integer) weiwaiOrderPayApplyDetail.getDetails().getUsage_day();
                    }

                }
                OrderSettleAmountRecordEntity entity = new OrderSettleAmountRecordEntity();
                entity.setOrderId(caseInfo.getOrderId());
                entity.setOrderSource(2);
                entity.setOrderNumber(caseInfo.getDigitalOrderId());
                if(ObjUtil.isNotEmpty(data)){
                    entity.setTrialSettlementAmount(data.getSettleAmt());
                    entity.setActualSettlementAmount(data.getSettleAmt());
                }
                entity.setOutsourcingStatus(1);
                entity.setUsageDays(useDays);
                entity.setCommitType(1);
                //todo 已还期数未添加
                orderSettleAmountRecordMapper.insert(entity);}
            }else if(ObjUtil.equal(caseInfo.getDataSource(),3)){
                DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity=     digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderId,caseInfo.getDigitalOrderId()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag,0).eq(DigitalOutsourcingOrderEntity::getDataSource,3).orderByDesc(DigitalOutsourcingOrderEntity::getCreateTime).last("limit 1"));
              if(ObjUtil.isNotEmpty(digitalOutsourcingOrderEntity)&&!ObjUtil.equal(digitalOutsourcingOrderEntity.getIsRepurchase(),1)){
                  try {
                      ReductionRecordConditionalVO reductionRecordConditionalVO= outsourcingSettlementTrialCalculationsService.kingdeeEarlyRepayCalc(caseInfo.getDigitalOrderId());

                      if(ObjUtil.isNotEmpty(reductionRecordConditionalVO)){
                          OrderSettleAmountRecordEntity entity = new OrderSettleAmountRecordEntity();
                          entity.setOrderId(caseInfo.getOrderId());
                          entity.setOrderSource(3);
                          entity.setOrderNumber(caseInfo.getDigitalOrderId());
                          entity.setTrialSettlementAmount(reductionRecordConditionalVO.getLoanAmount());
                          entity.setActualSettlementAmount(reductionRecordConditionalVO.getLeaveAmt());
                          entity.setSettlementMethod(reductionRecordConditionalVO.getLoanSettlementMethod());

                          entity.setOutsourcingStatus(1);
                          entity.setUsageDays(ObjUtil.isNotEmpty(digitalOutsourcingOrderEntity)?digitalOutsourcingOrderEntity.getUsageDays():0);
                          entity.setCommitType(1);
                          //todo 已还期数未添加
                          orderSettleAmountRecordMapper.insert(entity);

                      }
                  }catch (Exception e){
                      throw new RuntimeException("获取金蝶结清金额数据失败");
                  }
              }



            }

            //添加
            addCaseInfoReductionApproveRecord(caseInfo.getId(), null, ReduceApproveNodeEnums.REDUCE_APPLY, exemptionApplicationEntity.getReduceCurrentNode(), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), null, PayApplicationAuditTypeEnum.YUNQI);
            //特殊结清发起钉钉推送
//            if (addReductionDTO.getExemptionType() != null && addReductionDTO.getExemptionType().equals(3)) {
            initiatereductionDingTalkApproval(addReductionDTO.getResourceId(), exemptionApplicationEntity, loginUser);
//            }
        }


        return i > 0;
    }


    private ReduceApproveNodeEnums getReduceNextNode(ReduceApproveNodeEnums currentNode, Integer payType) {

        // 减免金额不能为空
        Asserts.notNull(payType, "减免金额不能为空");

        return switch (currentNode) {
            case REDUCE_APPLY ->
                // 委外申请，下一个节点为品质主管审核
                    ReduceApproveNodeEnums.QUALITY_MANAGER_APPROVED;
            case QUALITY_MANAGER_APPROVED ->
                // 品质主管审核，下一个节点为资管副总审核
                    ReduceApproveNodeEnums.STORE_MANAGER_APPROVED;
            case STORE_MANAGER_APPROVED ->
                // 资管副总审核，下一个节点为大总区审核
                    ReduceApproveNodeEnums.REGION_ASSET_MANAGER_APPROVED;
            case REGION_ASSET_MANAGER_APPROVED -> ReduceApproveNodeEnums.QUALITY_MANAGEMENT_ROOM_APPROVED;
            case QUALITY_MANAGEMENT_ROOM_APPROVED ->
                // 资管主管审核，下一个节点为运营总监
                    ReduceApproveNodeEnums.ASSET_VICE_PRESIDENT_APPROVED;
            case ASSET_VICE_PRESIDENT_APPROVED ->
                // 运营总监，下一个节点为总裁审批
                    ReduceApproveNodeEnums.REGION_GENERAL_MANAGER_APPROVED;
            case REGION_GENERAL_MANAGER_APPROVED ->
                // 总裁审核，下一个节点为申请成功
                    ReduceApproveNodeEnums.SUCCESS;

            default -> currentNode;
        };
    }

    @Override
    public Boolean updateIsNeedReDelegate() {
        // 获取所有没有重新委派的委外订单，并且重新委派时间超过15天或30天且没有审批完成的订单关联订单表查看订单来源类型

        MPJLambdaWrapper<CaseInfoEntity> wrapper = new MPJLambdaWrapper<CaseInfoEntity>()
                .selectAs(CaseInfoEntity::getId, CaseInfoListVO::getCaseId)
                .selectAll(CaseInfoEntity.class)
                .selectAs(OrderInfoEntity::getId, "orderId")
                .selectAs(OrderInfoEntity::getSourceType, "sourceType")
                .leftJoin(OrderInfoEntity.class, OrderInfoEntity::getId, CaseInfoEntity::getOrderId)
                .and(w -> w
                        .and(branch1 -> branch1
                                .eq(CaseInfoEntity::getIsReassign, 0)
                                .eq(CaseInfoEntity::getDeleteFlag, 0)
                                .ne(CaseInfoEntity::getDeptId, DeptEnum.ZHONG_XIN.getId())
                                .and(nestedOr -> nestedOr
                                        .or(subOr1 -> subOr1
                                                .eq(CaseInfoEntity::getCirculationType, 1)
                                                .isNull(CaseInfoEntity::getVisitResult)
                                                .gt(CaseInfoEntity::getReassignTime, CaseInfoEntity::getDisposalDeadline)
                                        )
                                        .or(subOr2 -> subOr2
                                                .eq(CaseInfoEntity::getCirculationType, 2)
                                                .isNull(CaseInfoEntity::getPreservationResult)
                                                .gt(CaseInfoEntity::getReassignTime, CaseInfoEntity::getDisposalDeadline)
                                        )
                                )
                        )
                        .or(branch2 -> branch2
                                .eq(CaseInfoEntity::getDeptId, DeptEnum.ZHONG_XIN.getId())
                                .ne(CaseInfoEntity::getIsSettled, 1)
                                .gt(CaseInfoEntity::getReassignTime, CaseInfoEntity::getDisposalDeadline)
                        )
                );

        List<CaseInfoListVO> caseInfoListVOS = caseInfoEntityMapper.selectJoinList(CaseInfoListVO.class, wrapper);

        if (CollUtil.isNotEmpty(caseInfoListVOS)) {
            caseInfoListVOS.forEach(item -> {

                CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getId, item.getCaseId()).eq(CaseInfoEntity::getDeleteFlag, 0));
                caseInfoEntity.setIsReassign(1);

                // 门店
                caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.REASSIGN);
                caseInfoEntityMapper.updateById(caseInfoEntity);
            });


        }


        return true;
    }


    @Override
    public void batchDingTaskApprove() {
        // 获取钉钉节点的审批列表 资管副总、区总审批
        List<CaseInfoEntity> dingTaskApproveVOList = caseInfoEntityMapper.selectList(new LambdaQueryWrapper<CaseInfoEntity>()
                .in(CaseInfoEntity::getCurrentNode, Arrays.asList(ASSET_VICE_PRESIDENT_APPROVED, REGION_GENERAL_APPROVED, PRESIDENT_APPROVED, ASSET_DISPOSAL_APPROVED))
                .isNotNull(CaseInfoEntity::getProcessId)
                .eq(CaseInfoEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(dingTaskApproveVOList)) {
            return;
        }
        log.info("OrderPayApplicationServiceImpl batchDingTaskApprove retrieved dingTaskApproveVOList size:{}", dingTaskApproveVOList.size());
        // 钉钉审批实例id
        List<String> dingTaskProcessIdList = dingTaskApproveVOList.stream().map(CaseInfoEntity::getProcessId).filter(StrUtil::isNotBlank).toList();
        Map<String, List<DingTaskApproveVO>> processDetailMap = dingTaskFeign.queryDetailByProcessIdList(dingTaskProcessIdList).getData();
        log.info("OrderPayApplicationServiceImpl batchDingTaskApprove retrieved processDetailMap size:{}", processDetailMap.size());

        List<ApprovalCaseDTO> approvalPayApplicationDTOs = new ArrayList<>();
        List<String> managerviceActivityId = new ArrayList<>();
        if (envUtil.isPrd()) {
            managerviceActivityId.add(switchUtils.getStrValue(HB_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(HZ_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(HD_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(HN_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(XN_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(XB_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(CX_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_HB_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_HZ_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_HD_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_HN_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_XN_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_XB_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_CX_MANAGER_VICE_PRESIDENT_APPROVE_ID));
        } else {
            managerviceActivityId.add(switchUtils.getStrValue(HB_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(HZ_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(HD_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(HN_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(XN_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(XB_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(CX_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_HB_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_HZ_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_HD_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_HN_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_XN_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_XB_MANAGER_VICE_PRESIDENT_APPROVE_ID));
            managerviceActivityId.add(switchUtils.getStrValue(FSH_CX_MANAGER_VICE_PRESIDENT_APPROVE_ID));
        }

        List<String> areapersidentActivityId = new ArrayList<>();
        if (envUtil.isPrd()) {
            areapersidentActivityId.add(switchUtils.getStrValue(HB_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(HZ_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(HD_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(HN_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(XN_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(XB_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(CX_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_HB_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_HZ_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_HD_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_HN_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_XN_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_XB_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_CX_AREA_PRESIDENT_APPROVE_ID));
        } else {
            areapersidentActivityId.add(switchUtils.getStrValue(HB_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(HZ_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(HD_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(HN_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(XN_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(XB_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(CX_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_HB_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_HZ_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_HD_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_HN_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_XN_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_XB_AREA_PRESIDENT_APPROVE_ID));
            areapersidentActivityId.add(switchUtils.getStrValue(FSH_CX_AREA_PRESIDENT_APPROVE_ID));
        }


        List<String> zichanchuzhizhuanyuanActivityId = new ArrayList<>();
        if (envUtil.isPrd()) {
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(HB_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(HZ_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(HD_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(HN_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(XN_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(XB_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(CX_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_HB_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_HZ_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_HD_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_HN_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_XN_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_XB_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_CX_ASSET_DISPOSAL_APPROVE_ID));
        } else {
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(HB_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(HZ_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(HD_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(HN_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(XN_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(XB_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(CX_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_HB_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_HZ_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_HD_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_HN_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_XN_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_XB_ASSET_DISPOSAL_APPROVE_ID));
            zichanchuzhizhuanyuanActivityId.add(switchUtils.getStrValue(FSH_CX_ASSET_DISPOSAL_APPROVE_ID));
        }


        List<String> yunyingzongjianActivityId = new ArrayList<>();
        if (envUtil.isPrd()) {
            yunyingzongjianActivityId.add(switchUtils.getStrValue(HB_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(HZ_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(HD_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(HN_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(XN_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(XB_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(CX_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_HB_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_HZ_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_HD_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_HN_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_XN_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_XB_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_CX_PRESIDENT_APPROVE_ID));
        } else {
            yunyingzongjianActivityId.add(switchUtils.getStrValue(HB_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(HZ_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(HD_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(HN_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(XN_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(XB_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(CX_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_HB_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_HZ_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_HD_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_HN_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_XN_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_XB_PRESIDENT_APPROVE_ID));
            yunyingzongjianActivityId.add(switchUtils.getStrValue(FSH_CX_PRESIDENT_APPROVE_ID));
        }


        dingTaskApproveVOList.forEach(caseInfoEntity -> {
            String processId = caseInfoEntity.getProcessId();
            List<DingTaskApproveVO> dingTaskApproveVOListByProcessId = processDetailMap.get(processId);
            if (CollUtil.isEmpty(dingTaskApproveVOListByProcessId)) {
                return;
            }
//            //筛选result是CANCELED或TERMINATED的结果终止
//            List<DingTaskApproveVO> filteredList = dingTaskApproveVOListByProcessId.stream()
//                    .filter(vo -> "CANCELED".equals(vo.getResult()) || "TERMINATED".equals(vo.getResult()))
//                    .collect(Collectors.toList());
//            if(CollUtil.isNotEmpty(filteredList)){
//
//            }
         int isRepurchase=this.getIsRepurchaseFromDataSource(caseInfoEntity);


            if (envUtil.isPrd()) {

                if (ObjUtil.equal(caseInfoEntity.getSaleProcess(),DeptEnum.HUAN_BEI_DA_QU.getId())||ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.HUAN_BEI_DA_QU.getId())) {
                    String managervice = switchUtils.getStrValue(HB_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String areapersident = switchUtils.getStrValue(HB_AREA_PRESIDENT_APPROVE_ID);
                    String zichanchuzhizhuanyuan = switchUtils.getStrValue(HB_ASSET_DISPOSAL_APPROVE_ID);
                    String yunyingzongjian = switchUtils.getStrValue(HB_PRESIDENT_APPROVE_ID);
                    String fshmanagervice = switchUtils.getStrValue(FSH_HB_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String fshareapersident = switchUtils.getStrValue(FSH_HB_AREA_PRESIDENT_APPROVE_ID);
                    String fshzichanchuzhizhuanyuan = switchUtils.getStrValue(FSH_HB_ASSET_DISPOSAL_APPROVE_ID);
                    String fshyunyingzongjian = switchUtils.getStrValue(FSH_HB_PRESIDENT_APPROVE_ID);
                    if(ObjUtil.equal(isRepurchase,1)){
                        List<String> order = Arrays.asList(managervice, areapersident, yunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }else {
                        List<String> order = Arrays.asList(fshmanagervice, fshareapersident, fshzichanchuzhizhuanyuan, fshyunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }
                } else if (ObjUtil.equal(caseInfoEntity.getSaleProcess(),DeptEnum.HU_ZHONG_DA_QU.getId())||ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.HU_ZHONG_DA_QU.getId())) {
                    String managervice = switchUtils.getStrValue(HZ_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String areapersident = switchUtils.getStrValue(HZ_AREA_PRESIDENT_APPROVE_ID);
                    String zichanchuzhizhuanyuan = switchUtils.getStrValue(HZ_ASSET_DISPOSAL_APPROVE_ID);
                    String yunyingzongjian = switchUtils.getStrValue(HZ_PRESIDENT_APPROVE_ID);
                    String fshmanagervice = switchUtils.getStrValue(FSH_HZ_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String fshareapersident = switchUtils.getStrValue(FSH_HZ_AREA_PRESIDENT_APPROVE_ID);
                    String fshzichanchuzhizhuanyuan = switchUtils.getStrValue(FSH_HZ_ASSET_DISPOSAL_APPROVE_ID);
                    String fshyunyingzongjian = switchUtils.getStrValue(FSH_HZ_PRESIDENT_APPROVE_ID);
                    if(ObjUtil.equal(isRepurchase,1)){
                        List<String> order = Arrays.asList(managervice, areapersident, yunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }else {
                        List<String> order = Arrays.asList(fshmanagervice, fshareapersident, fshzichanchuzhizhuanyuan, fshyunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }

                } else if (ObjUtil.equal(caseInfoEntity.getSaleProcess(),DeptEnum.HUAN_GAN_DA_QU.getId())||ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.HUAN_GAN_DA_QU.getId())) {
                    String managervice = switchUtils.getStrValue(HD_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String areapersident = switchUtils.getStrValue(HD_AREA_PRESIDENT_APPROVE_ID);
                    String zichanchuzhizhuanyuan = switchUtils.getStrValue(HD_ASSET_DISPOSAL_APPROVE_ID);
                    String yunyingzongjian = switchUtils.getStrValue(HD_PRESIDENT_APPROVE_ID);
                    String fshmanagervice = switchUtils.getStrValue(FSH_HD_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String fshareapersident = switchUtils.getStrValue(FSH_HD_AREA_PRESIDENT_APPROVE_ID);
                    String fshzichanchuzhizhuanyuan = switchUtils.getStrValue(FSH_HD_ASSET_DISPOSAL_APPROVE_ID);
                    String fshyunyingzongjian = switchUtils.getStrValue(FSH_HD_PRESIDENT_APPROVE_ID);
                    if(ObjUtil.equal(isRepurchase,1)){
                        List<String> order = Arrays.asList(managervice, areapersident, yunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }else {
                        List<String> order = Arrays.asList(fshmanagervice, fshareapersident, fshzichanchuzhizhuanyuan, fshyunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }

                } else if (ObjUtil.equal(caseInfoEntity.getSaleProcess(),DeptEnum.HUAN_SHAN_DA_QU.getId())||ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.HUAN_SHAN_DA_QU.getId())) {
                    String managervice = switchUtils.getStrValue(HN_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String areapersident = switchUtils.getStrValue(HN_AREA_PRESIDENT_APPROVE_ID);
                    String zichanchuzhizhuanyuan = switchUtils.getStrValue(HN_ASSET_DISPOSAL_APPROVE_ID);
                    String yunyingzongjian = switchUtils.getStrValue(HN_PRESIDENT_APPROVE_ID);
                    String fshmanagervice = switchUtils.getStrValue(FSH_HN_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String fshareapersident = switchUtils.getStrValue(FSH_HN_AREA_PRESIDENT_APPROVE_ID);
                    String fshzichanchuzhizhuanyuan = switchUtils.getStrValue(FSH_HN_ASSET_DISPOSAL_APPROVE_ID);
                    String fshyunyingzongjian = switchUtils.getStrValue(FSH_HN_PRESIDENT_APPROVE_ID);
                    if(ObjUtil.equal(isRepurchase,1)){
                        List<String> order = Arrays.asList(managervice, areapersident, yunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }else {
                        List<String> order = Arrays.asList(fshmanagervice, fshareapersident, fshzichanchuzhizhuanyuan, fshyunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }

                } else if (ObjUtil.equal(caseInfoEntity.getSaleProcess(),DeptEnum.XIAN_DA_QU.getId())||ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.XIAN_DA_QU.getId())) {
                    String managervice = switchUtils.getStrValue(XN_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String areapersident = switchUtils.getStrValue(XN_AREA_PRESIDENT_APPROVE_ID);
                    String zichanchuzhizhuanyuan = switchUtils.getStrValue(XN_ASSET_DISPOSAL_APPROVE_ID);
                    String yunyingzongjian = switchUtils.getStrValue(XN_PRESIDENT_APPROVE_ID);
                    String fshmanagervice = switchUtils.getStrValue(FSH_XN_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String fshareapersident = switchUtils.getStrValue(FSH_XN_AREA_PRESIDENT_APPROVE_ID);
                    String fshzichanchuzhizhuanyuan = switchUtils.getStrValue(FSH_XN_ASSET_DISPOSAL_APPROVE_ID);
                    String fshyunyingzongjian = switchUtils.getStrValue(FSH_XN_PRESIDENT_APPROVE_ID);
                    if(ObjUtil.equal(isRepurchase,1)){
                        List<String> order = Arrays.asList(managervice, areapersident, yunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }else {
                        List<String> order = Arrays.asList(fshmanagervice, fshareapersident, fshzichanchuzhizhuanyuan, fshyunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }

                } else if (ObjUtil.equal(caseInfoEntity.getSaleProcess(),DeptEnum.XI_BEI_DA_QU.getId())||ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.XI_BEI_DA_QU.getId())) {
                    String managervice = switchUtils.getStrValue(XB_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String areapersident = switchUtils.getStrValue(XB_AREA_PRESIDENT_APPROVE_ID);
                    String zichanchuzhizhuanyuan = switchUtils.getStrValue(XB_ASSET_DISPOSAL_APPROVE_ID);
                    String yunyingzongjian = switchUtils.getStrValue(XN_PRESIDENT_APPROVE_ID);
                    String fshmanagervice = switchUtils.getStrValue(FSH_XB_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String fshareapersident = switchUtils.getStrValue(FSH_XB_AREA_PRESIDENT_APPROVE_ID);
                    String fshzichanchuzhizhuanyuan = switchUtils.getStrValue(FSH_XB_ASSET_DISPOSAL_APPROVE_ID);
                    String fshyunyingzongjian = switchUtils.getStrValue(FSH_XB_PRESIDENT_APPROVE_ID);
                    if(ObjUtil.equal(isRepurchase,1)){
                        List<String> order = Arrays.asList(managervice, areapersident, yunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }else {
                        List<String> order = Arrays.asList(fshmanagervice, fshareapersident, fshzichanchuzhizhuanyuan, fshyunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }

                } else if (ObjUtil.equal(caseInfoEntity.getSaleProcess(),DeptEnum.CHUANG_XIN_DA_QU.getId())||ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.CHUANG_XIN_DA_QU.getId())) {
                    String managervice = switchUtils.getStrValue(CX_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String areapersident = switchUtils.getStrValue(CX_AREA_PRESIDENT_APPROVE_ID);
                    String zichanchuzhizhuanyuan = switchUtils.getStrValue(CX_ASSET_DISPOSAL_APPROVE_ID);
                    String yunyingzongjian = switchUtils.getStrValue(CX_PRESIDENT_APPROVE_ID);
                    String fshmanagervice = switchUtils.getStrValue(FSH_CX_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String fshareapersident = switchUtils.getStrValue(FSH_CX_AREA_PRESIDENT_APPROVE_ID);
                    String fshzichanchuzhizhuanyuan = switchUtils.getStrValue(FSH_CX_ASSET_DISPOSAL_APPROVE_ID);
                    String fshyunyingzongjian = switchUtils.getStrValue(FSH_CX_PRESIDENT_APPROVE_ID);
                    if(ObjUtil.equal(isRepurchase,1)){
                        List<String> order = Arrays.asList(managervice, areapersident, yunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }else {
                        List<String> order = Arrays.asList(fshmanagervice, fshareapersident, fshzichanchuzhizhuanyuan, fshyunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }

                }

            } else {
                if (ObjUtil.equal(caseInfoEntity.getSaleProcess(),DeptEnum.HUAN_BEI_DA_QU.getId())||ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.HUAN_BEI_DA_QU.getId())) {
                    String managervice = switchUtils.getStrValue(HB_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String areapersident = switchUtils.getStrValue(HB_AREA_PRESIDENT_APPROVE_ID);
                    String zichanchuzhizhuanyuan = switchUtils.getStrValue(HB_ASSET_DISPOSAL_APPROVE_ID);
                    String yunyingzongjian = switchUtils.getStrValue(HB_PRESIDENT_APPROVE_ID);
                    String fshmanagervice = switchUtils.getStrValue(FSH_HB_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String fshareapersident = switchUtils.getStrValue(FSH_HB_AREA_PRESIDENT_APPROVE_ID);
                    String fshzichanchuzhizhuanyuan = switchUtils.getStrValue(FSH_HB_ASSET_DISPOSAL_APPROVE_ID);
                    String fshyunyingzongjian = switchUtils.getStrValue(FSH_HB_PRESIDENT_APPROVE_ID);
                    if(ObjUtil.equal(isRepurchase,1)){
                        List<String> order = Arrays.asList(managervice, areapersident, yunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }else {
                        List<String> order = Arrays.asList(fshmanagervice, fshareapersident, fshzichanchuzhizhuanyuan, fshyunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }
                } else if (ObjUtil.equal(caseInfoEntity.getSaleProcess(),DeptEnum.HU_ZHONG_DA_QU.getId())||ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.HU_ZHONG_DA_QU.getId())) {
                    String managervice = switchUtils.getStrValue(HZ_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String areapersident = switchUtils.getStrValue(HZ_AREA_PRESIDENT_APPROVE_ID);
                    String zichanchuzhizhuanyuan = switchUtils.getStrValue(HZ_ASSET_DISPOSAL_APPROVE_ID);
                    String yunyingzongjian = switchUtils.getStrValue(HZ_PRESIDENT_APPROVE_ID);
                    String fshmanagervice = switchUtils.getStrValue(FSH_HZ_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String fshareapersident = switchUtils.getStrValue(FSH_HZ_AREA_PRESIDENT_APPROVE_ID);
                    String fshzichanchuzhizhuanyuan = switchUtils.getStrValue(FSH_HZ_ASSET_DISPOSAL_APPROVE_ID);
                    String fshyunyingzongjian = switchUtils.getStrValue(FSH_HZ_PRESIDENT_APPROVE_ID);
                    if(ObjUtil.equal(isRepurchase,1)){
                        List<String> order = Arrays.asList(managervice, areapersident, yunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }else {
                        List<String> order = Arrays.asList(fshmanagervice, fshareapersident, fshzichanchuzhizhuanyuan, fshyunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }

                } else if (ObjUtil.equal(caseInfoEntity.getSaleProcess(),DeptEnum.HUAN_GAN_DA_QU.getId())||ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.HUAN_GAN_DA_QU.getId())) {
                    String managervice = switchUtils.getStrValue(HD_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String areapersident = switchUtils.getStrValue(HD_AREA_PRESIDENT_APPROVE_ID);
                    String zichanchuzhizhuanyuan = switchUtils.getStrValue(HD_ASSET_DISPOSAL_APPROVE_ID);
                    String yunyingzongjian = switchUtils.getStrValue(HD_PRESIDENT_APPROVE_ID);
                    String fshmanagervice = switchUtils.getStrValue(FSH_HD_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String fshareapersident = switchUtils.getStrValue(FSH_HD_AREA_PRESIDENT_APPROVE_ID);
                    String fshzichanchuzhizhuanyuan = switchUtils.getStrValue(FSH_HD_ASSET_DISPOSAL_APPROVE_ID);
                    String fshyunyingzongjian = switchUtils.getStrValue(FSH_HD_PRESIDENT_APPROVE_ID);
                    if(ObjUtil.equal(isRepurchase,1)){
                        List<String> order = Arrays.asList(managervice, areapersident, yunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }else {
                        List<String> order = Arrays.asList(fshmanagervice, fshareapersident, fshzichanchuzhizhuanyuan, fshyunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }

                } else if (ObjUtil.equal(caseInfoEntity.getSaleProcess(),DeptEnum.HUAN_SHAN_DA_QU.getId())||ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.HUAN_SHAN_DA_QU.getId())) {
                    String managervice = switchUtils.getStrValue(HN_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String areapersident = switchUtils.getStrValue(HN_AREA_PRESIDENT_APPROVE_ID);
                    String zichanchuzhizhuanyuan = switchUtils.getStrValue(HN_ASSET_DISPOSAL_APPROVE_ID);
                    String yunyingzongjian = switchUtils.getStrValue(HN_PRESIDENT_APPROVE_ID);
                    String fshmanagervice = switchUtils.getStrValue(FSH_HN_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String fshareapersident = switchUtils.getStrValue(FSH_HN_AREA_PRESIDENT_APPROVE_ID);
                    String fshzichanchuzhizhuanyuan = switchUtils.getStrValue(FSH_HN_ASSET_DISPOSAL_APPROVE_ID);
                    String fshyunyingzongjian = switchUtils.getStrValue(FSH_HN_PRESIDENT_APPROVE_ID);
                    if(ObjUtil.equal(isRepurchase,1)){
                        List<String> order = Arrays.asList(managervice, areapersident, yunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }else {
                        List<String> order = Arrays.asList(fshmanagervice, fshareapersident, fshzichanchuzhizhuanyuan, fshyunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }

                } else if (ObjUtil.equal(caseInfoEntity.getSaleProcess(),DeptEnum.XIAN_DA_QU.getId())||ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.XIAN_DA_QU.getId())) {
                    String managervice = switchUtils.getStrValue(XN_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String areapersident = switchUtils.getStrValue(XN_AREA_PRESIDENT_APPROVE_ID);
                    String zichanchuzhizhuanyuan = switchUtils.getStrValue(XN_ASSET_DISPOSAL_APPROVE_ID);
                    String yunyingzongjian = switchUtils.getStrValue(XN_PRESIDENT_APPROVE_ID);
                    String fshmanagervice = switchUtils.getStrValue(FSH_XN_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String fshareapersident = switchUtils.getStrValue(FSH_XN_AREA_PRESIDENT_APPROVE_ID);
                    String fshzichanchuzhizhuanyuan = switchUtils.getStrValue(FSH_XN_ASSET_DISPOSAL_APPROVE_ID);
                    String fshyunyingzongjian = switchUtils.getStrValue(FSH_XN_PRESIDENT_APPROVE_ID);
                    if(ObjUtil.equal(isRepurchase,1)){
                        List<String> order = Arrays.asList(managervice, areapersident, yunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }else {
                        List<String> order = Arrays.asList(fshmanagervice, fshareapersident, fshzichanchuzhizhuanyuan, fshyunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }

                } else if (ObjUtil.equal(caseInfoEntity.getSaleProcess(),DeptEnum.XI_BEI_DA_QU.getId())||ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.XI_BEI_DA_QU.getId())) {
                    String managervice = switchUtils.getStrValue(XB_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String areapersident = switchUtils.getStrValue(XB_AREA_PRESIDENT_APPROVE_ID);
                    String zichanchuzhizhuanyuan = switchUtils.getStrValue(XB_ASSET_DISPOSAL_APPROVE_ID);
                    String yunyingzongjian = switchUtils.getStrValue(XN_PRESIDENT_APPROVE_ID);
                    String fshmanagervice = switchUtils.getStrValue(FSH_XB_MANAGER_VICE_PRESIDENT_APPROVE_ID);
                    String fshareapersident = switchUtils.getStrValue(FSH_XB_AREA_PRESIDENT_APPROVE_ID);
                    String fshzichanchuzhizhuanyuan = switchUtils.getStrValue(FSH_XB_ASSET_DISPOSAL_APPROVE_ID);
                    String fshyunyingzongjian = switchUtils.getStrValue(FSH_XB_PRESIDENT_APPROVE_ID);
                    if(ObjUtil.equal(isRepurchase,1)){
                        List<String> order = Arrays.asList(managervice, areapersident, yunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }else {
                        List<String> order = Arrays.asList(fshmanagervice, fshareapersident, fshzichanchuzhizhuanyuan, fshyunyingzongjian);
                        dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> order.indexOf(a.getActivityId())));
                    }

                }


            }


            dingTaskApproveVOListByProcessId.forEach(dingTaskApproveVO -> {

                String activityId = dingTaskApproveVO.getActivityId();
                // 获取当前审批节点
                CaseApproveNodeEnums currentNode = caseInfoEntity.getCurrentNode();

                String userNumberApprove = dingTaskApproveVO.getUserId();
                Integer userId = null;
                List<Integer> roleIdList = null;

                if (StrUtil.isNotBlank(userNumberApprove)) {
                    List<UserSyncInfoListVO> syncInfoListVOList = userFeign.queryUserSyncInfoList(new SearchUserSyncInfoDTO().setUserNumberList(List.of(userNumberApprove))).getData();
                    if (CollUtil.isNotEmpty(syncInfoListVOList)) {
                        UserSyncInfoListVO userSyncInfoListVO = syncInfoListVOList.get(0);
                        userId = userSyncInfoListVO.getLhUserId();
                    }
                }


                PayApplicationEventEnums conclusion = null;
                if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())) {
                    conclusion = PayApplicationEventEnums.APPROVE_PASS;
                }
                if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())) {
                    conclusion = PayApplicationEventEnums.APPROVE_REJECT;

                } else if (ObjUtil.equal(dingTaskApproveVO.getResult(), "NONE")) {
                    return;
                }
                int isRepurchase1 = this.getIsRepurchaseFromDataSource(caseInfoEntity);


                if (caseInfoEntity.getCurrentNode().equals(ASSET_VICE_PRESIDENT_APPROVED) && managerviceActivityId.contains(activityId)) {
//                if (caseInfoEntity.getCurrentNode().equals(ASSET_VICE_PRESIDENT_APPROVED) && Objects.equals(managervice, activityId)) {
                    if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())) {
                        caseInfoEntity.setCurrentNode(REGION_GENERAL_APPROVED);
                    } else if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())) {
                        if (ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.CHUANG_XIN_DA_QU.getId())) {
                            caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.SUPERVISOR_APPROVED);
                        } else {
                            caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.ASSET_MANAGER_APPROVED);
                        }

                        if (caseInfoEntity.getDataSource().equals(2)) {
                            //  通知数字化开始审批
                            DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                            digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                            digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                            String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                            log.info("通知数字化开始审批 {}", s);
                        }
                    }


                    caseInfoEntityMapper.updateById(caseInfoEntity);
                    addCaseInfoApproveRecord(caseInfoEntity.getId(), dingTaskApproveVO.getRemark(), ASSET_VICE_PRESIDENT_APPROVED, caseInfoEntity.getCurrentNode(), conclusion, userId, caseInfoEntity.getProcessId(), PayApplicationAuditTypeEnum.DINGTALK);
                }
                if (caseInfoEntity.getCurrentNode().equals(REGION_GENERAL_APPROVED) && areapersidentActivityId.contains(activityId)) {

                    if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())) {
                         if(ObjUtil.equal(isRepurchase1,0)){
                             caseInfoEntity.setCurrentNode(ASSET_DISPOSAL_APPROVED);
                         }else if(ObjUtil.equal(isRepurchase1,1)){
                             caseInfoEntity.setCurrentNode(PRESIDENT_APPROVED);
                         }


                    } else if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())) {
                        caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.ASSET_MANAGER_APPROVED);

//                        if (ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.CHUANG_XIN_DA_QU.getId())) {
//                            caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.SUPERVISOR_APPROVED);
//                        } else {
//                            caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.ASSET_MANAGER_APPROVED);
//                        }
                        if (caseInfoEntity.getDataSource().equals(2)) {
                            //  通知数字化开始审批
                            DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                            digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                            digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                            String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                            log.info("通知数字化开始审批 {}", s);
                        }
                    }


                    caseInfoEntityMapper.updateById(caseInfoEntity);
                    addCaseInfoApproveRecord(caseInfoEntity.getId(), dingTaskApproveVO.getRemark(), REGION_GENERAL_APPROVED, caseInfoEntity.getCurrentNode(), conclusion, userId, caseInfoEntity.getProcessId(), PayApplicationAuditTypeEnum.DINGTALK);
                }
                if (caseInfoEntity.getCurrentNode().equals(ASSET_DISPOSAL_APPROVED) && zichanchuzhizhuanyuanActivityId.contains(activityId)) {

                    if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())) {
                        caseInfoEntity.setCurrentNode(PRESIDENT_APPROVED);
                    } else if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())) {
                        if (ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.CHUANG_XIN_DA_QU.getId())) {
                            caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.SUPERVISOR_APPROVED);
                        } else {
                            caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.ASSET_MANAGER_APPROVED);
                        }

                        if (caseInfoEntity.getDataSource().equals(2)) {
                            //  通知数字化开始审批
                            DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                            digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                            digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                            String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                            log.info("通知数字化开始审批 {}", s);
                        }
                    }


                    caseInfoEntityMapper.updateById(caseInfoEntity);
                    addCaseInfoApproveRecord(caseInfoEntity.getId(), dingTaskApproveVO.getRemark(), ASSET_DISPOSAL_APPROVED, caseInfoEntity.getCurrentNode(), conclusion, userId, caseInfoEntity.getProcessId(), PayApplicationAuditTypeEnum.DINGTALK);
                }

                if (caseInfoEntity.getCurrentNode().equals(PRESIDENT_APPROVED) && yunyingzongjianActivityId.contains(activityId)) {
//                if (caseInfoEntity.getCurrentNode().equals(REGION_GENERAL_APPROVED) && Objects.equals(areapersident, activityId)) {
                    if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())) {
                        caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.SUCCESS);
                        //更新最新处置时间
                        caseInfoEntity.setReassignTime(LocalDateTime.now());
                        if (caseInfoEntity.getCirculationType().equals(1)) {
                            caseInfoEntity.setDisposalDeadline(LocalDateTime.now().plusDays(15));
                        } else if (caseInfoEntity.getCirculationType().equals(2)) {
                            caseInfoEntity.setDisposalDeadline(LocalDateTime.now().plusDays(30));
                        }
                        //设置每天不跑批
                        if (caseInfoEntity.getDataSource() != null && caseInfoEntity.getDataSource().equals(1)) {
                            orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>().eq(OrderInfoEntity::getId, caseInfoEntity.getOrderId()).set(OrderInfoEntity::getIsPushThirdParty, 1));
                            //todo 转移gps账号
                            CaseCustomerInfoDTO caseCustomerInfoDTO=new CaseCustomerInfoDTO();
                            caseCustomerInfoDTO.setCaseId(caseInfoEntity.getId());
                            caseTransferGpsAccountService.transferGpsAccount(caseCustomerInfoDTO);
                        }
                        //通知金蝶状态
                        if (ObjUtil.isNotEmpty(caseInfoEntity) && caseInfoEntity.getDataSource().equals(3)) {
                            KingdeeStatusRequestDTO kingdeeStatusRequestDTO = new KingdeeStatusRequestDTO();
                            kingdeeStatusRequestDTO.setCode(1);
                            kingdeeStatusRequestDTO.setMsg("成功");
                            KingdeeRequestData data = new KingdeeRequestData();
                            data.setOrderNumber(caseInfoEntity.getDigitalOrderId());
                            if (caseInfoEntity.getCirculationType().equals(1)) {
                                data.setFlowType("委外外访");
                            } else if (caseInfoEntity.getCirculationType().equals(2)) {
                                data.setFlowType("委外保全");
                            } else if (caseInfoEntity.getCirculationType().equals(3)) {
                                data.setFlowType("以资抵债");
                            }
                            data.setOutsourceOrg(caseInfoEntity.getDisposalCompany());
                            data.setOutsourceTime(caseInfoEntity.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                            data.setOutsourceStatus(1);
                            kingdeeStatusRequestDTO.setData(data);
                            String kingdeeorderStatus = kingdeeOutsourcingService.pushKingdeeorderStatus(kingdeeStatusRequestDTO);
                            log.info("通知金蝶状态 {}", kingdeeorderStatus);

                        }


                    } else if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())) {
                        if (ObjUtil.equal(caseInfoEntity.getRegionId(), DeptEnum.CHUANG_XIN_DA_QU.getId())) {
                            caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.SUPERVISOR_APPROVED);
                        } else {
                            caseInfoEntity.setCurrentNode(CaseApproveNodeEnums.ASSET_MANAGER_APPROVED);
                        }

                        if (caseInfoEntity.getDataSource().equals(2)) {
                            //  通知数字化开始审批

                            DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                            digitalizeWeiwaiOrderStatusDTO.setOrder_id(caseInfoEntity.getDigitalOrderId());
                            digitalizeWeiwaiOrderStatusDTO.setStatus(2);
                            String s = digitalizeFeign.weiwaiStatusSynchronization(digitalizeWeiwaiOrderStatusDTO);
                            log.info("通知数字化开始审批 {}", s);
                        }
                    }

                    caseInfoEntityMapper.updateById(caseInfoEntity);
                    addCaseInfoApproveRecord(caseInfoEntity.getId(), dingTaskApproveVO.getRemark(), PRESIDENT_APPROVED, caseInfoEntity.getCurrentNode(), conclusion, userId, caseInfoEntity.getProcessId(), PayApplicationAuditTypeEnum.DINGTALK);
                }

//
//

            });
            // 异步推送至众信
            if (caseInfoEntity.getCurrentNode() == CaseApproveNodeEnums.SUCCESS && caseInfoEntity.getDeptId().equals(DeptEnum.ZHONG_XIN.getId()) && caseInfoEntity.getDataSource().equals(1)) {
                CompletableFuture.runAsync(() -> {
                    try {
                        Result<String> stringResult = zhongXinService.pushCaseToZhongXin(caseInfoEntity.getOrderId());
                        log.info("审批通过推送至众信结果：{}", stringResult);

                    } catch (Exception e) {
                        log.error("推送至众信时发生异常", e);
                    }
                });

            } else if (caseInfoEntity.getCurrentNode() == CaseApproveNodeEnums.SUCCESS && caseInfoEntity.getDeptId().equals(DeptEnum.ZHONG_XIN.getId()) && caseInfoEntity.getDataSource().equals(2)) {
                try {
                    Result<String> stringResult = zhongXinService.digitalizePushCaseToZhongXin(caseInfoEntity);
                    log.info("审批通过推送至众信结果：{}", stringResult);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            } else if (caseInfoEntity.getCurrentNode() == CaseApproveNodeEnums.SUCCESS && caseInfoEntity.getDeptId().equals(DeptEnum.ZHONG_XIN.getId()) && caseInfoEntity.getDataSource().equals(3)) {
                try {
                    Result<String> stringResult = zhongXinService.jindiePushCaseToZhongXin(caseInfoEntity.getDigitalOrderId());
                    log.info("审批通过推送至众信结果：{}", stringResult);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }

            }

        });
    }
    private int getIsRepurchaseFromDataSource(CaseInfoEntity caseInfoEntity) {
        if (caseInfoEntity == null) {
            return 0;
        }

        int dataSource = caseInfoEntity.getDataSource();
        int isRepurchase = 0;

        if (ObjUtil.equal(caseInfoEntity.getDataSource(),1) && caseInfoEntity.getOrderId() != null) {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(
                    new LambdaQueryWrapper<OrderInfoEntity>()
                            .eq(OrderInfoEntity::getId, caseInfoEntity.getOrderId())
                            .eq(OrderInfoEntity::getDeleteFlag, 0)
                            .orderByDesc(OrderInfoEntity::getCreateTime)
                            .last("LIMIT 1")
            );
            if (orderInfoEntity != null) {
                isRepurchase = orderInfoEntity.getIsRepurchase();
            }
        } else if ((ObjUtil.equal(dataSource,2) || (ObjUtil.equal(dataSource,3)) && caseInfoEntity.getDigitalOrderId() != null)) {
            DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = digitalOutsourcingOrderEntityMapper.selectOne(
                    new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>()
                            .eq(DigitalOutsourcingOrderEntity::getOrderId, caseInfoEntity.getDigitalOrderId())
                            .eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0)
                            .eq(DigitalOutsourcingOrderEntity::getDataSource, dataSource)
                            .orderByDesc(DigitalOutsourcingOrderEntity::getCreateTime)
                            .last("LIMIT 1")
            );
            if (digitalOutsourcingOrderEntity != null) {
                isRepurchase = digitalOutsourcingOrderEntity.getIsRepurchase();
            }
        }

        return isRepurchase;
    }
    @Override
    public Boolean approveReduction(ApprovalCaseDTO approvalCaseDTO, LoginUser loginUser) {
        log.info("CaseInfoServiceImpl.approveReduction approvalCaseDTO = {}", approvalCaseDTO);

        approvalCaseDTO.getRequestIds().forEach(requestId -> {
            CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getId, requestId).eq(CaseInfoEntity::getDeleteFlag, 0));
            Assert.notNull(caseInfoEntity, () -> new BusinessException("资外订单不存在"));
            log.info("CaseInfoServiceImpl.approveCaseInfo caseInfoEntity = {}", caseInfoEntity);
            //获取当前节点
//            ReduceApproveNodeEnums currentNode = caseInfoEntity.getReduceCurrentNode();
//            log.info("CaseInfoServiceImpl.approveCaseInfo currentNode = {}", currentNode);
            ExemptionApplicationEntity exemptionApplicationEntity = exemptionApplicationEntityMapper.selectOne(new LambdaQueryWrapper<ExemptionApplicationEntity>().eq(ExemptionApplicationEntity::getId, approvalCaseDTO.getExemptionApplicationId()).eq(ExemptionApplicationEntity::getDeleteFlag, 0).last("limit 1"));
            if (ObjUtil.isNotNull(exemptionApplicationEntity) && exemptionApplicationEntity.getReduceCurrentNode().getCode() == ReduceApproveNodeEnums.SUCCESS.getCode()) {
                throw new BusinessException("该减免已审批完成");
            }
            ReduceApproveNodeEnums currentNode = exemptionApplicationEntity.getReduceCurrentNode();
            log.info("CaseInfoServiceImpl.approveCaseInfo currentNode = {}", currentNode);


            ReduceApproveNodeEnums nextNode = null;
            PayApplicationEventEnums event = PayApplicationEventEnums.APPROVE_PASS;

            // 根据当前节点和审批结论确定下一个节点
            switch (currentNode) {


                case QUALITY_MANAGER_APPROVED:

                    if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {
                        // 审核通过，下一个节点为区域资管副总
                        nextNode = ReduceApproveNodeEnums.ASSET_VICE_PRESIDENT_APPROVED;

                        //发起钉钉推送
//                        initiatereductionDingTalkApproval(exemptionApplicationEntity, loginUser.getUserId());

                    } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
                        // 审核拒绝，下一个节点为门店
                        nextNode = ReduceApproveNodeEnums.REDUCE_APPLY;
                        event = PayApplicationEventEnums.APPROVE_REJECT;
                    } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
                        // 驳回，下一个节点为门店审核
                        nextNode = ReduceApproveNodeEnums.REDUCE_APPLY;
                        event = PayApplicationEventEnums.APPROVE_REVOKE;
                    }
                    break;

//                default:
//                    // 根据当前节点确定下一个节点
//                    if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {
//                        nextNode = getReduceNextNode(currentNode, caseInfoEntity.getReduceAmount());
//                    } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
//                        // 审核拒绝，下一个节点为流程终止
//                        nextNode = ReduceApproveNodeEnums.FAIL;
//                        event = PayApplicationEventEnums.APPROVE_REJECT;
//                    } else if (approvalCaseDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
//                        // 驳回，下一个节点为当前节点
//                        nextNode = currentNode;
//                        event = PayApplicationEventEnums.APPROVE_REVOKE;
//                    }
//                    break;
            }


            //更新资外减免节点
            caseInfoEntity.setReduceCurrentNode(nextNode);
            caseInfoEntityMapper.updateById(caseInfoEntity);
            //添加审批记录
            addCaseInfoReductionApproveRecord(requestId, approvalCaseDTO.getRemark(), currentNode, nextNode, event, loginUser.getUserId(), null, PayApplicationAuditTypeEnum.YUNQI);


        });


        return null;
    }


    /**
     * 发起钉钉审批流程。
     *
     * @param exemptionApplicationEntity 提前委外减免流程
     * @param userId                     当前登录用户信息
     * @throws BusinessException 当当前人员未同步到钉钉或流程不存在时抛出
     */
    private boolean initiatereductionDingTalkApproval(List<String> resourceIds, ExemptionApplicationEntity exemptionApplicationEntity, LoginUser loginUser ) {
        CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>().eq(CaseInfoEntity::getId, exemptionApplicationEntity.getCaseId()).eq(CaseInfoEntity::getDeleteFlag, 0));
        OrderInfoEntity orderInfo = new OrderInfoEntity();
        DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = new DigitalOutsourcingOrderEntity();
        RepaymentOverdueStatusVO repaymentOverdueStatusVO = new RepaymentOverdueStatusVO();
        if (caseInfoEntity != null && caseInfoEntity.getDataSource().equals(1)) {
            orderInfo = orderInfoMapper.selectById(exemptionApplicationEntity.getOrderId());
            Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));
            log.info("OrderPayApplicationServiceImpl OrderPayApplication found orderInfo:{}", orderInfo);
            repaymentOverdueStatusVO = repaymentService.overdueStatusByOrderId(orderInfo.getId());
        } else if (caseInfoEntity != null && (caseInfoEntity.getDataSource().equals(2) || caseInfoEntity.getDataSource().equals(3))) {
            digitalOutsourcingOrderEntity = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>().eq(DigitalOutsourcingOrderEntity::getOrderId, exemptionApplicationEntity.getDigitalOrderId()).eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0));
            Assert.notNull(digitalOutsourcingOrderEntity, () -> new BusinessException("订单不存在"));
            log.info("OrderPayApplicationServiceImpl OrderPayApplication found digitalOutsourcingOrderEntity:{}", digitalOutsourcingOrderEntity);

        }
        String processCode = "";
        if (envUtil.isPrd()) {
            processCode = dingTaskFeign.getWorkFlowSchemaId("减免申请").getData();
            Assert.notBlank(processCode, () -> new BusinessException("流程不存在"));
            log.info("OrderPayApplicationServiceImpl OrderPayApplication processCode:{}", processCode);
        } else {
            processCode = dingTaskFeign.getWorkFlowSchemaId("减免申请（测试）").getData();
            Assert.notBlank(processCode, () -> new BusinessException("流程不存在"));
            log.info("OrderPayApplicationServiceImpl OrderPayApplication processCode:{}", processCode);
        }


        // 获取当前人员钉钉userId
        List<UserSyncInfoListVO> currentUserSyncInfoListVO = userFeign.queryUserSyncInfoList(new SearchUserSyncInfoDTO().setUserIdList(List.of(loginUser.getUserId())).setOrigin(1)).getData();
        Assert.notEmpty(currentUserSyncInfoListVO, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        UserSyncInfoListVO currentUserSyncInfo = currentUserSyncInfoListVO.get(0);
        String userNumber = currentUserSyncInfo.getUserNumber();
        Assert.notBlank(userNumber, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        log.info("OrderPayApplicationServiceImpl OrderPayApplication current userNumber:{}", userNumber);

        //获取钉钉部门id
        UserDetailInfoVO currentUserDetailInfoVO = userFeign.searchUserDetailById(loginUser.getUserId()).getData();
        List<Integer> currentUserDeptIds = currentUserDetailInfoVO.getDeptIds();
        Assert.notEmpty(currentUserDeptIds, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        List<DeptSyncInfoVO> currentUserDeptSyncList = userFeign.getSyncDeptByLhDeptIds(new SearchDeptSyncInfoDTO().setLhDeptIdList(currentUserDeptIds).setOrigin(1)).getData();
        Assert.notEmpty(currentUserDeptSyncList, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        log.info("OrderPayApplicationServiceImpl OrderPayApplication current deptSyncList:{}", currentUserDeptSyncList);
        List<DingCreateProcessCmdDTO.FormComponentValue> formComponentValueList = new ArrayList<>();

//        if (ExemptionTypeEnum.SPECIAL_SETTLEMENT_REDUCTION.equals(exemptionApplicationEntity.getExemptionType())) {


        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("申请减免类型")
                .setValue("特殊结清"));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("结清类型")
                .setValue(exemptionApplicationEntity.getClearingType() == 1 ? "委外资产结清" : "处置变现"));

        if (caseInfoEntity != null && caseInfoEntity.getDataSource().equals(1)) {
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("区域")
                    .setValue(orderInfo.getRegionName()));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("门店")
                    .setValue(orderInfo.getStoreName()));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("客户姓名")
                    .setValue(orderInfo.getCustomerName()));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("车牌号")
                    .setValue(orderInfo.getVehicleNumber()));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("所属资方")
                    .setValue(orderInfo.getFundName()));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("产品名称")
                    .setValue(orderInfo.getProductName()));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("期数")
                    .setValue(ObjUtil.isNotEmpty(orderInfo.getTerm()) ? orderInfo.getTerm().toString() : "0"));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("已还期数")
                    .setValue(ObjUtil.isNotEmpty(repaymentOverdueStatusVO.getAlreadyRepaidInstalments()) ? repaymentOverdueStatusVO.getAlreadyRepaidInstalments().toString() : "0"));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("逾期天数")
                    .setValue(ObjUtil.isNotEmpty(repaymentOverdueStatusVO.getNumberOfDaysOverdue()) ? repaymentOverdueStatusVO.getNumberOfDaysOverdue().toString() : "0"));

        } else if (caseInfoEntity != null && (caseInfoEntity.getDataSource().equals(2) || caseInfoEntity.getDataSource().equals(3))) {
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("区域")
                    .setValue(digitalOutsourcingOrderEntity.getRegionName()));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("门店")
                    .setValue(digitalOutsourcingOrderEntity.getStoreName()));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("客户姓名")
                    .setValue(digitalOutsourcingOrderEntity.getCustomerName()));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("车牌号")
                    .setValue(digitalOutsourcingOrderEntity.getVehicleNumber()));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("所属资方")
                    .setValue(digitalOutsourcingOrderEntity.getFundName()));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("产品名称")
                    .setValue(digitalOutsourcingOrderEntity.getProductName()));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("期数")
                    .setValue(ObjUtil.isNotEmpty(digitalOutsourcingOrderEntity.getTerm()) ? digitalOutsourcingOrderEntity.getTerm().toString() : "0"));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("已还期数")
                    .setValue(ObjUtil.isNotEmpty(digitalOutsourcingOrderEntity.getHaveTerm()) ? digitalOutsourcingOrderEntity.getHaveTerm().toString() : "0"));
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("逾期天数")
                    .setValue(ObjUtil.isNotEmpty(digitalOutsourcingOrderEntity.getOverdueDays()) ? digitalOutsourcingOrderEntity.getOverdueDays().toString() : "0"));
        }


        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("应收结清金额")
                .setValue(exemptionApplicationEntity.getReceivableSettlementAmount().toString()));
        if (caseInfoEntity != null && caseInfoEntity.getDataSource().equals(1)) {
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("剩余本金")
                    .setValue(ObjUtil.isNotEmpty(repaymentOverdueStatusVO.getRemainingPrincipal()) ? repaymentOverdueStatusVO.getRemainingPrincipal().toString() : "0"));

        } else if (caseInfoEntity != null && (caseInfoEntity.getDataSource().equals(2) || caseInfoEntity.getDataSource().equals(3))) {
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("剩余本金")
                    .setValue(ObjUtil.isNotEmpty(digitalOutsourcingOrderEntity.getRemainingPrincipal()) ? digitalOutsourcingOrderEntity.getRemainingPrincipal().toString() : "0"));

        }
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("申请结清金额")
                .setValue(exemptionApplicationEntity.getApplySettlementAmount().toString()));
        formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                .setName("申请原因")
                .setValue(exemptionApplicationEntity.getApplyReason()));

        List<String> temporaryAccessRouteReqList = null;
        if (CollUtil.isNotEmpty(resourceIds)) {
            temporaryAccessRouteReqList = resourceIds.stream()
                    .map(i -> resourceFeign.temporaryAccessRouteDingDingrequest(i, 240).getData())
                    .collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(temporaryAccessRouteReqList)) {
            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
                    .setName("附件")
                    .setValue(JSONUtil.toJsonStr(temporaryAccessRouteReqList))
            );
        }


//        }

//        else {
//
//
//            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                    .setName("申请减免类型")
//                    .setValue(exemptionApplicationEntity.getExemptionType().getDescription()));
//            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                    .setName("支付类型")
//                    .setValue(exemptionApplicationEntity.getPayType() == 1 ? "区域" : "特批"));
//            if (caseInfoEntity != null && caseInfoEntity.getDataSource().equals(1)) {
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("区域")
//                        .setValue(orderInfo.getRegionName()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("门店")
//                        .setValue(orderInfo.getStoreName()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("客户姓名")
//                        .setValue(orderInfo.getCustomerName()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("车牌号")
//                        .setValue(orderInfo.getVehicleNumber()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("所属资方")
//                        .setValue(orderInfo.getFundName()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("产品名称")
//                        .setValue(orderInfo.getProductName()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("期数")
//                        .setValue(orderInfo.getTerm().toString()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("已还期数")
//                        .setValue(repaymentOverdueStatusVO.getAlreadyRepaidInstalments().toString()));
//
//
//            } else if (caseInfoEntity != null && caseInfoEntity.getDataSource().equals(2)) {
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("区域")
//                        .setValue(digitalOutsourcingOrderEntity.getRegionName()));
//
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("门店")
//                        .setValue(digitalOutsourcingOrderEntity.getStoreName()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("客户姓名")
//                        .setValue(digitalOutsourcingOrderEntity.getCustomerName()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("车牌号")
//                        .setValue(digitalOutsourcingOrderEntity.getVehicleNumber()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("所属资方")
//                        .setValue(digitalOutsourcingOrderEntity.getFundName()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("产品名称")
//                        .setValue(digitalOutsourcingOrderEntity.getProductName()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("期数")
//                        .setValue(digitalOutsourcingOrderEntity.getTerm().toString()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("已还期数")
//                        .setValue(digitalOutsourcingOrderEntity.getHaveTerm().toString()));
//
//            }
//
//            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                    .setName("结清模式")
//                    .setValue(exemptionApplicationEntity.getClearingPlan() == 1 ? "封闭期模式" : "违约金模式"));
//            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                    .setName("是否剔除业绩")
//                    .setValue(exemptionApplicationEntity.getIsExcludePerformance() == 1 ? "是" : "否"));
//            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                    .setName("是否退回返点")
//                    .setValue(exemptionApplicationEntity.getIsReturnRebate() == 1 ? "是" : "否"));
//            if (caseInfoEntity != null && caseInfoEntity.getDataSource().equals(1)) {
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("剩余本金")
//                        .setValue(repaymentOverdueStatusVO.getRemainingPrincipal().toString()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("逾期天数")
//                        .setValue(repaymentOverdueStatusVO.getNumberOfDaysOverdue().toString()));
//            } else if (caseInfoEntity != null && caseInfoEntity.getDataSource().equals(2)) {
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("剩余本金")
//                        .setValue(digitalOutsourcingOrderEntity.getRemainingPrincipal().toString()));
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("逾期天数")
//                        .setValue(digitalOutsourcingOrderEntity.getOverdueDays().toString()));
//            }
//
//
//            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                    .setName("申请减免金额（元）")
//                    .setValue(exemptionApplicationEntity.getApplyReductionAmount().toString()));
//            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                    .setName("申请结清金额")
//                    .setValue(exemptionApplicationEntity.getApplySettlementAmount().toString()));
//            formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                    .setName("申请原因")
//                    .setValue(exemptionApplicationEntity.getApplyReason()));
//            List<String> temporaryAccessRouteReqList = null;
//            if (CollUtil.isNotEmpty(resourceIds)) {
//                temporaryAccessRouteReqList = resourceIds.stream()
//                        .map(i -> resourceFeign.temporaryAccessRouteRequest(i, 24).getData())
//                        .collect(Collectors.toList());
//            }
//            if (CollUtil.isNotEmpty(temporaryAccessRouteReqList)) {
//                formComponentValueList.add(new DingCreateProcessCmdDTO.FormComponentValue()
//                        .setName("附件")
//                        .setValue(JSONUtil.toJsonStr(temporaryAccessRouteReqList))
//                );
//            }
//        }
        List<DingCreateProcessCmdDTO.TargetSelectActioner> targetSelectActionerList = new ArrayList<>();
        List<DingCreateProcessCmdDTO.Approver> approvers = new ArrayList<>();
        List<String> chaosonguserNumbers = new ArrayList<>();
//        if (envUtil.isPrd()) {

//            if (exemptionApplicationEntity.getProcessType() != null && exemptionApplicationEntity.getProcessType().equals(1)) {
//                //获取大区品质主管用户id
//                //获取部门用户id
//                List<Integer> userIdsByDeptId = userFeign.getDeptUsers(List.of(orderInfo.getDeptId())).getData();
//                Assert.notEmpty(userIdsByDeptId, () -> new BusinessException("未查询到审批人信息，无法发起审批"));
//                //获取部门下门店经理
//                List<Integer> userIdListByDeptAndRole = userFeign.getUserIdByRoleIds(new SearchUserIdByRoleIdsDTO().setRoleId(RoleEnum.STORE_MANAGER.getId()).setUserIdList(userIdsByDeptId)).getData();
//                Assert.notEmpty(userIdListByDeptAndRole, () -> new BusinessException("未查询到审批人信息，无法发起审批"));
//                //获取门店经理用户钉钉userId
//                List<UserSyncInfoListVO> storeManagerUserList = userFeign.queryUserSyncInfoList(new SearchUserSyncInfoDTO().setUserIdList(List.of(userIdListByDeptAndRole.get(0))).setOrigin(1)).getData();
//                Assert.notEmpty(storeManagerUserList, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
//                UserSyncInfoListVO storeManagerUserSyncInfo = storeManagerUserList.get(0);
//                String storeManagerUserNumber = storeManagerUserSyncInfo.getUserNumber();
//                Assert.notBlank(storeManagerUserNumber, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
//                log.info("OrderPayApplicationServiceImpl OrderPayApplication storeManagerUserNumber:{}", storeManagerUserNumber);
//
//                targetSelectActionerList.add(new DingCreateProcessCmdDTO.TargetSelectActioner()
//                        .setActionerKey(switchUtils.getStrValue(QY_COMPANY_INITIATE_STORE_MANAGER_APPROVE_KEY))
//                        .setActionerUserIds(List.of(storeManagerUserNumber)));
//                /**
//                 * 获取区域资管主管
//                 */
//                List<UserDingDeptMappingVO> userDingDeptMapping = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setRegionId(orderInfo.getRegionId()).setDeptId(DingDingDeptEnum.ZIGUANZHUGUAN_REGION.getId()).setUserType(1));
//                Assert.notEmpty(userDingDeptMapping, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
//                UserDingDeptMappingVO userDingDeptMappingVO = userDingDeptMapping.get(0);
//                String ziguanzhuguanManagerUserNumber = userDingDeptMappingVO.getUserNumber();
//                targetSelectActionerList.add(new DingCreateProcessCmdDTO.TargetSelectActioner()
//                        .setActionerKey(switchUtils.getStrValue(QY_COMPANY_INITIATE_ASSET_MANAGER_APPROVE_KEY))
//                        .setActionerUserIds(List.of(ziguanzhuguanManagerUserNumber)));
//                /**
//                 * 品质管理室
//                 */
//                List<UserDingDeptMappingVO> userDingDeptMappingList = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setDeptId(DingDingDeptEnum.PINZHI_MANAGER_REGION.getId()).setUserType(1));
//                Assert.notEmpty(userDingDeptMappingList, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
//                List<String> userNumbers = userDingDeptMappingList.stream()
//                        .map(UserDingDeptMappingVO::getUserNumber)
//                        .collect(Collectors.toList());
//                Assert.notEmpty(userNumbers, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
//                DingCreateProcessCmdDTO.Approver approver = new DingCreateProcessCmdDTO.Approver();
//                approver.setActionType("OR");
//                approver.setUserIds(userNumbers);
//                approvers.add(approver);
        //区域资管副总
        List<UserDingDeptMappingVO> ziguanfuzonguserDingDeptMapping = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setRegionId(caseInfoEntity.getRegionId()).setDeptId(DingDingDeptEnum.ZIGUANZHUFUZHANG_REGION.getId()).setUserType(1));
        Assert.notEmpty(ziguanfuzonguserDingDeptMapping, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        UserDingDeptMappingVO ziguanfuzonguserDingDeptMappingVO = ziguanfuzonguserDingDeptMapping.get(0);
        String ziguanfuzongManagerUserNumber = ziguanfuzonguserDingDeptMappingVO.getUserNumber();
        targetSelectActionerList.add(new DingCreateProcessCmdDTO.TargetSelectActioner()
                .setActionerKey(switchUtils.getStrValue(QY_COMPANY_INITIATE_ASSET_VICE_PRESIDENT_APPROVE_KEY))
                .setActionerUserIds(List.of(ziguanfuzongManagerUserNumber)));
        //区域总经理
        List<UserDingDeptMappingVO> zongjingliuserDingDeptMapping = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setRegionId(caseInfoEntity.getRegionId()).setDeptId(DingDingDeptEnum.ZONGJINGLI_REGION.getId()).setUserType(1));
        Assert.notEmpty(zongjingliuserDingDeptMapping, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
        UserDingDeptMappingVO zongjingliuserDingDeptMappingVO = zongjingliuserDingDeptMapping.get(0);
        String zongjingliManagerUserNumber = zongjingliuserDingDeptMappingVO.getUserNumber();
        if(RoleEnum.ASSET_VICE_PRESIDENT_MANAGER.hasRole(loginUser.getRoleIds())){
            targetSelectActionerList.add(new DingCreateProcessCmdDTO.TargetSelectActioner()
                    .setActionerKey(switchUtils.getStrValue(ZFQY_COMPANY_INITIATE_PRESIDENT_APPROVE_KEY))
                    .setActionerUserIds(List.of(zongjingliManagerUserNumber)));
        }else {
            targetSelectActionerList.add(new DingCreateProcessCmdDTO.TargetSelectActioner()
                    .setActionerKey(switchUtils.getStrValue(QY_COMPANY_INITIATE_PRESIDENT_APPROVE_KEY))
                    .setActionerUserIds(List.of(zongjingliManagerUserNumber)));
        }



//            } else if (exemptionApplicationEntity.getProcessType() != null && exemptionApplicationEntity.getProcessType().equals(2)) {
//                /**
//                 * 品质管理室
//                 */
//                List<UserDingDeptMappingVO> userDingDeptMappingList = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setDeptId(DingDingDeptEnum.PINZHI_MANAGER_REGION.getId()).setUserType(1));
//                Assert.notEmpty(userDingDeptMappingList, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
//                List<String> userNumbers = userDingDeptMappingList.stream()
//                        .map(UserDingDeptMappingVO::getUserNumber)
//                        .collect(Collectors.toList());
//                Assert.notEmpty(userNumbers, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
//                DingCreateProcessCmdDTO.Approver approver = new DingCreateProcessCmdDTO.Approver();
//                approver.setActionType("OR");
//                approver.setUserIds(userNumbers);
//                approvers.add(approver);
//                //区域总经理
//                List<UserDingDeptMappingVO> zongjingliuserDingDeptMapping = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setRegionId(orderInfo.getRegionId()).setDeptId(DingDingDeptEnum.ZONGJINGLI_REGION.getId()).setUserType(1));
//                Assert.notEmpty(zongjingliuserDingDeptMapping, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
//                UserDingDeptMappingVO zongjingliuserDingDeptMappingVO = zongjingliuserDingDeptMapping.get(0);
//                String zongjingliManagerUserNumber = zongjingliuserDingDeptMappingVO.getUserNumber();
//                targetSelectActionerList.add(new DingCreateProcessCmdDTO.TargetSelectActioner()
//                        .setActionerKey(switchUtils.getStrValue(ASSET_REGION_GENERAL_MANAGER_APPROVE_KEY))
//                        .setActionerUserIds(List.of(zongjingliManagerUserNumber)));
//            }

//            //抄送人
//            List<UserDingDeptMappingVO> chaosongserDingDeptMapping = userFeign.getUserDingDeptMapping(new UserDingDeptMappingDTO().setUserType(3));
//            Assert.notEmpty(chaosongserDingDeptMapping, () -> new BusinessException("当前人员未同步到钉钉，无法发起审批"));
//            chaosonguserNumbers = chaosongserDingDeptMapping.stream()
//                    .map(UserDingDeptMappingVO::getUserNumber)
//                    .collect(Collectors.toList());
//        }


        DingCreateProcessCmdDTO processCmdDTO = new DingCreateProcessCmdDTO();


        if (envUtil.isPrd()) {
            processCmdDTO.setProcessCode(processCode)
                    .setOriginatorUserId(userNumber)
                    .setDeptId(Convert.toLong(currentUserDeptSyncList.get(0).getDeptId()))
                    .setFormComponentValues(formComponentValueList)
                    .setTargetSelectActioners(targetSelectActionerList)
//                    .setCcList(chaosonguserNumbers)
//                    .setCcPosition("START_FINISH")
//                    .setApprovers(approvers);
            ;
        } else {
            processCmdDTO.setProcessCode("PROC-CCA2C2B5-3348-46DD-90C1-1732191A6F0A")
                    .setOriginatorUserId(userNumber)
                    .setDeptId(Convert.toLong(currentUserDeptSyncList.get(0).getDeptId()))
                    .setFormComponentValues(formComponentValueList)
                    .setTargetSelectActioners(targetSelectActionerList)
//                    .setCcList(chaosonguserNumbers)
//                    .setCcPosition("START_FINISH")
//                    .setApprovers(approvers);
            ;


//            String[] originatorUserIds = {"17343137193032983"};
//
//            Random random = new Random();
//
//            String randomOriginatorUserId = originatorUserIds[random.nextInt(originatorUserIds.length)];
//
//            processCmdDTO.setProcessCode("PROC-CCA2C2B5-3348-46DD-90C1-1732191A6F0A")
////            processCmdDTO.setProcessCode(processCode)
//                    .setOriginatorUserId(randomOriginatorUserId)
//
////                    .setDeptId(900502017L)
////                    .setDeptId(982416714l)
//                    .setDeptId(Convert.toLong(currentUserDeptSyncList.get(0).getDeptId()))
////                    .setDeptId(976054817l)
////                    .setDeptId(926741152L)
//                    .setFormComponentValues(formComponentValueList)
////                    .setTargetSelectActioners(targetSelectActionerList)
//            ;
        }
        log.info("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances:{}", processCmdDTO);
        Result<String> workflowProcessInstances = null;
        log.info("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances:{}", formComponentValueList);
        try {
            if (envUtil.isPrd()) {
                workflowProcessInstances = dingTaskFeign.createWorkflowProcessInstances(processCmdDTO);
            } else {
                workflowProcessInstances = dingTaskFeign.createWorkflowProcessInstances(processCmdDTO);
                log.info("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances:{}", workflowProcessInstances);
//                workflowProcessInstances = Result.success("U-XhRSu-QgOabcYzuHaroQ07861733829144");

            }
        } catch (Exception e) {
            log.error("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances error:{}", e.getMessage());
            throw new BusinessException("发起钉钉审批实例失败");
        }
        if (Result.isSuccess(workflowProcessInstances) && StrUtil.isNotBlank(workflowProcessInstances.getData())) {
            log.info("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances:{}", workflowProcessInstances);
            caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                    .eq(CaseInfoEntity::getId, caseInfoEntity.getId())
                    .set(CaseInfoEntity::getReduceProcessId, workflowProcessInstances.getData())
            );
            exemptionApplicationEntityMapper.update(new LambdaUpdateWrapper<ExemptionApplicationEntity>()
                    .eq(ExemptionApplicationEntity::getId, exemptionApplicationEntity.getId())
                    .set(ExemptionApplicationEntity::getReduceProcessId, workflowProcessInstances.getData())
            );
            return true;
        } else {
            log.info("OrderPayApplicationServiceImpl OrderPayApplication create workflowProcessInstances fail:{}", workflowProcessInstances);
            throw new BusinessException("发起钉钉审批实例失败");
        }
    }

    @Override
    public void reductionApplicationDingTaskBatch() {
        // 获取钉钉节点的审批列表 资管副总、大区总审批 资管主管审批、运营总监、总裁审批
//        List<CaseInfoEntity> dingTaskApproveVOList = caseInfoEntityMapper.selectList(new LambdaQueryWrapper<CaseInfoEntity>()
//                .in(CaseInfoEntity::getReduceCurrentNode, Arrays.asList(ReduceApproveNodeEnums.ASSET_VICE_PRESIDENT_APPROVED, REGION_DIRECTOR_APPROVED, ASSET_MANAGER_APPROVED, ReduceApproveNodeEnums.PRESIDENT_APPROVED))
//                .isNotNull(CaseInfoEntity::getReduceProcessId)
//                .eq(CaseInfoEntity::getDeleteFlag, 0)
//        );
        List<ExemptionApplicationEntity> exemptionApplicationEntities = exemptionApplicationEntityMapper.selectList(new LambdaQueryWrapper<ExemptionApplicationEntity>()
                .in(ExemptionApplicationEntity::getReduceCurrentNode, Arrays.asList(ReduceApproveNodeEnums.STORE_MANAGER_APPROVED, ReduceApproveNodeEnums.REGION_ASSET_MANAGER_APPROVED, ReduceApproveNodeEnums.QUALITY_MANAGEMENT_ROOM_APPROVED, ReduceApproveNodeEnums.ASSET_VICE_PRESIDENT_APPROVED, ReduceApproveNodeEnums.REGION_GENERAL_MANAGER_APPROVED))
                .isNotNull(ExemptionApplicationEntity::getReduceProcessId)
                .eq(ExemptionApplicationEntity::getDeleteFlag, 0)

        );
        if (CollUtil.isEmpty(exemptionApplicationEntities)) {
            return;
        }
        log.info("OrderPayApplicationServiceImpl batchDingTaskApprove retrieved dingTaskApproveVOList size:{}", exemptionApplicationEntities.size());
        // 钉钉审批实例id
        List<String> dingTaskProcessIdList = exemptionApplicationEntities.stream().map(ExemptionApplicationEntity::getReduceProcessId).filter(StrUtil::isNotBlank).toList();
        Map<String, List<DingTaskApproveVO>> processDetailMap = dingTaskFeign.queryDetailByProcessIdList(dingTaskProcessIdList).getData();
        log.info("OrderPayApplicationServiceImpl batchDingTaskApprove retrieved processDetailMap size:{}", processDetailMap.size());

        List<ApprovalCaseDTO> approvalPayApplicationDTOs = new ArrayList<>();

        exemptionApplicationEntities.forEach(exemptionApplicationEntity -> {
            String processId = exemptionApplicationEntity.getReduceProcessId();
            List<DingTaskApproveVO> dingTaskApproveVOListByProcessId = processDetailMap.get(processId);
            log.info("OrderPayApplicationServiceImpl dingTaskApproveVOListByProcessId:{}", dingTaskApproveVOListByProcessId);
            if (CollUtil.isEmpty(dingTaskApproveVOListByProcessId)) {
                return;
            }


            String gsqymendian = switchUtils.getStrValue(ExemptionApprovalConstants.QY_COMPANY_INITIATE_STORE_MANAGER_APPROVE_ID);
            String gsqyziguanzhuguan = switchUtils.getStrValue(ExemptionApprovalConstants.QY_COMPANY_INITIATE_ASSET_MANAGER_APPROVE_ID);
            String gsqypinzhiguanlishi = switchUtils.getStrValue(ExemptionApprovalConstants.QY_COMPANY_INITIATE_QUALITY_MANAGER_APPROVE_ID);
            String gsqyziguanfuzong = switchUtils.getStrValue(ExemptionApprovalConstants.QY_COMPANY_INITIATE_ASSET_VICE_PRESIDENT_APPROVE_ID);
            String gsqyzongjingli = switchUtils.getStrValue(ExemptionApprovalConstants.QY_COMPANY_INITIATE_PRESIDENT_APPROVE_ID);


            if (dingTaskApproveVOListByProcessId != null && !dingTaskApproveVOListByProcessId.isEmpty()) {

                List<String> order = Arrays.asList(gsqymendian, gsqyziguanzhuguan, gsqypinzhiguanlishi, gsqyziguanfuzong, gsqyzongjingli);
                dingTaskApproveVOListByProcessId.sort(Comparator.comparingInt(a -> {
                    int index = order.indexOf(a.getActivityId());
                    return index == -1 ? Integer.MAX_VALUE : index; // 不在 order 中的元素排在最后
                }));


            }


            AtomicBoolean isProcessed = new AtomicBoolean(false); // 标志变量

            dingTaskApproveVOListByProcessId.forEach(dingTaskApproveVO -> {
                if (isProcessed.get()) {
                    return;
                }
                String activityId = dingTaskApproveVO.getActivityId();
                // 获取当前审批节点
//                ReduceApproveNodeEnums currentNode = caseInfoEntity.getReduceCurrentNode();
                ReduceApproveNodeEnums currentNode = exemptionApplicationEntity.getReduceCurrentNode();

                String userNumberApprove = dingTaskApproveVO.getUserId();
                Integer userId = null;
                List<Integer> roleIdList = null;

                if (StrUtil.isNotBlank(userNumberApprove)) {
                    List<UserSyncInfoListVO> syncInfoListVOList = userFeign.queryUserSyncInfoList(new SearchUserSyncInfoDTO().setUserNumberList(List.of(userNumberApprove))).getData();
                    if (CollUtil.isNotEmpty(syncInfoListVOList)) {
                        UserSyncInfoListVO userSyncInfoListVO = syncInfoListVOList.get(0);
                        userId = userSyncInfoListVO.getLhUserId();
                    }
                }
                if (!envUtil.isPrd()) {
                    if (ObjUtil.isNotNull(userId)) {
                        UserDetailInfoVO userDetailInfoVO = userFeign.searchUserDetailById(userId).getData();
                        if (ObjUtil.isNotNull(userDetailInfoVO)) {
                            roleIdList = userDetailInfoVO.getRoleIds();
                        }
                    }
                }
                PayApplicationEventEnums conclusion = null;
                if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())) {
                    conclusion = PayApplicationEventEnums.APPROVE_PASS;
                }
                if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())) {
                    conclusion = PayApplicationEventEnums.APPROVE_REJECT;
                } else if (ObjUtil.equal(dingTaskApproveVO.getResult(), "NONE")) {
                    return;
                }

//
                if (Objects.equals(gsqymendian, activityId) && exemptionApplicationEntity.getReduceCurrentNode().equals(ReduceApproveNodeEnums.STORE_MANAGER_APPROVED)) {

                    if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())) {

                        exemptionApplicationEntity.setReduceCurrentNode(ReduceApproveNodeEnums.REGION_ASSET_MANAGER_APPROVED);
                    } else if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())) {
                        exemptionApplicationEntity.setReduceCurrentNode(ReduceApproveNodeEnums.REDUCE_APPLY);
                    }

                    exemptionApplicationEntityMapper.updateById(exemptionApplicationEntity);

//                    caseInfoEntityMapper.updateById(caseInfoEntity);
                    addCaseInfoReductionApproveRecord(exemptionApplicationEntity.getCaseId(), dingTaskApproveVO.getRemark(), ReduceApproveNodeEnums.STORE_MANAGER_APPROVED, exemptionApplicationEntity.getReduceCurrentNode(), conclusion, userId, exemptionApplicationEntity.getReduceProcessId(), PayApplicationAuditTypeEnum.DINGTALK);
                }
                if (Objects.equals(gsqyziguanzhuguan, activityId) && exemptionApplicationEntity.getReduceCurrentNode().equals(ReduceApproveNodeEnums.REGION_ASSET_MANAGER_APPROVED)) {

                    if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())) {

                        exemptionApplicationEntity.setReduceCurrentNode(ReduceApproveNodeEnums.QUALITY_MANAGEMENT_ROOM_APPROVED);


                    } else if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())) {
                        exemptionApplicationEntity.setReduceCurrentNode(ReduceApproveNodeEnums.QUALITY_MANAGER_APPROVED);
                    }

                    exemptionApplicationEntityMapper.updateById(exemptionApplicationEntity);

//                    caseInfoEntityMapper.updateById(caseInfoEntity);
                    addCaseInfoReductionApproveRecord(exemptionApplicationEntity.getCaseId(), dingTaskApproveVO.getRemark(), ReduceApproveNodeEnums.ASSET_VICE_PRESIDENT_APPROVED, exemptionApplicationEntity.getReduceCurrentNode(), conclusion, userId, exemptionApplicationEntity.getReduceProcessId(), PayApplicationAuditTypeEnum.DINGTALK);
                }
                if (Objects.equals(gsqypinzhiguanlishi, activityId) && exemptionApplicationEntity.getReduceCurrentNode().equals(ReduceApproveNodeEnums.QUALITY_MANAGEMENT_ROOM_APPROVED)) {

                    if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())) {

                        exemptionApplicationEntity.setReduceCurrentNode(ReduceApproveNodeEnums.ASSET_VICE_PRESIDENT_APPROVED);
                    } else if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())) {
                        exemptionApplicationEntity.setReduceCurrentNode(ReduceApproveNodeEnums.REDUCE_APPLY);
                    }

                    exemptionApplicationEntityMapper.updateById(exemptionApplicationEntity);

//                    caseInfoEntityMapper.updateById(caseInfoEntity);
                    addCaseInfoReductionApproveRecord(exemptionApplicationEntity.getCaseId(), dingTaskApproveVO.getRemark(), ReduceApproveNodeEnums.QUALITY_MANAGEMENT_ROOM_APPROVED, exemptionApplicationEntity.getReduceCurrentNode(), conclusion, userId, exemptionApplicationEntity.getReduceProcessId(), PayApplicationAuditTypeEnum.DINGTALK);
                }

                if (Objects.equals(gsqyziguanfuzong, activityId) && exemptionApplicationEntity.getReduceCurrentNode().equals(ReduceApproveNodeEnums.ASSET_VICE_PRESIDENT_APPROVED)) {

                    if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())) {

                        exemptionApplicationEntity.setReduceCurrentNode(ReduceApproveNodeEnums.REGION_GENERAL_MANAGER_APPROVED);
                    } else if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())) {
                        exemptionApplicationEntity.setReduceCurrentNode(ReduceApproveNodeEnums.REDUCE_APPLY);
                    }

                    exemptionApplicationEntityMapper.updateById(exemptionApplicationEntity);

//                    caseInfoEntityMapper.updateById(caseInfoEntity);
                    addCaseInfoReductionApproveRecord(exemptionApplicationEntity.getCaseId(), dingTaskApproveVO.getRemark(), ReduceApproveNodeEnums.ASSET_VICE_PRESIDENT_APPROVED, exemptionApplicationEntity.getReduceCurrentNode(), conclusion, userId, exemptionApplicationEntity.getReduceProcessId(), PayApplicationAuditTypeEnum.DINGTALK);
                }


                if (Objects.equals(gsqyzongjingli, activityId) && exemptionApplicationEntity.getReduceCurrentNode().equals(ReduceApproveNodeEnums.REGION_GENERAL_MANAGER_APPROVED)) {

                    if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())) {

                        exemptionApplicationEntity.setReduceCurrentNode(ReduceApproveNodeEnums.SUCCESS);
                        //同步还款计划表
                        if (exemptionApplicationEntity.getDataSource() != null && exemptionApplicationEntity.getDataSource().equals(1)) {
                            OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>().eq(OrderInfoEntity::getId, exemptionApplicationEntity.getOrderId()).eq(OrderInfoEntity::getDeleteFlag, 0));
                            if (orderInfo.getIsRepurchase().equals(1)) {
                                RepurchaseRepaymentInfoEntity repurchaseRepaymentInfoEntity = repurchaseRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>().eq(RepurchaseRepaymentInfoEntity::getOrderId, orderInfo.getId())
                                        .isNull(RepurchaseRepaymentInfoEntity::getActuallyDate)
                                        .orderByAsc(RepurchaseRepaymentInfoEntity::getRepaymentDate)
                                        .last("limit 1")
                                );
                                repurchaseRepaymentInfoEntity.setOutsourceReductionAmount(repurchaseRepaymentInfoEntity.getOutsourceReductionAmount().add(exemptionApplicationEntity.getApplyReductionAmount()));
                                repurchaseRepaymentInfoMapper.updateById(repurchaseRepaymentInfoEntity);

                            } else {
                                FundRepaymentInfoEntity fundRepaymentInfo = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                        .eq(FundRepaymentInfoEntity::getOrderId, exemptionApplicationEntity.getOrderId())
                                        .isNull(FundRepaymentInfoEntity::getActuallyDate)
                                        .orderByAsc(FundRepaymentInfoEntity::getRepaymentDate)
                                        .last("limit 1"));
                                fundRepaymentInfo.setOutsourceReductionAmount(fundRepaymentInfo.getOutsourceReductionAmount().add(exemptionApplicationEntity.getApplyReductionAmount()));
                                fundRepaymentInfoMapper.updateById(fundRepaymentInfo);
                            }


                        }


                    } else if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())) {
                        exemptionApplicationEntity.setReduceCurrentNode(ReduceApproveNodeEnums.QUALITY_MANAGER_APPROVED);
                    }

                    exemptionApplicationEntityMapper.updateById(exemptionApplicationEntity);

//                    caseInfoEntityMapper.updateById(caseInfoEntity);
                    addCaseInfoReductionApproveRecord(exemptionApplicationEntity.getCaseId(), dingTaskApproveVO.getRemark(), ReduceApproveNodeEnums.REGION_GENERAL_MANAGER_APPROVED, ReduceApproveNodeEnums.SUCCESS, conclusion, userId, exemptionApplicationEntity.getReduceProcessId(), PayApplicationAuditTypeEnum.DINGTALK);
                }


            });

        });


    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOutsourcingDetails(OutsourcingDetailsDTO outsourcingDetailsEntity, LoginUser loginUser) {
        log.info("updateOutsourcingDetails：{}", outsourcingDetailsEntity);
        addOutsourcingDetails(outsourcingDetailsEntity);
        addOutsourcingDetailsReocrd(outsourcingDetailsEntity, loginUser);
        updateCaseInfoResult(outsourcingDetailsEntity.getCaseId(), outsourcingDetailsEntity.getPreservationLatestStatus(), outsourcingDetailsEntity.getVisitLatestStatus(), outsourcingDetailsEntity.getTransferStatus());
        SaveOutsourcingMaintenanceRecordDTO saveOutsourcingMaintenanceRecordDTO=new SaveOutsourcingMaintenanceRecordDTO();
        saveOutsourcingMaintenanceRecordDTO.setCaseInfoId(outsourcingDetailsEntity.getCaseId());
        saveOutsourcingMaintenanceRecordDTO.setTitles(outsourcingDetailsEntity.getTitles());
        saveOutsourcingMaintenanceRecord(saveOutsourcingMaintenanceRecordDTO,loginUser);

        // 校验非空
        if(outsourcingDetailsEntity.getPreservationLatestStatus() != null){
            // 当保全方式 更新为面谈全额结清时  触发代偿关闭接口
            if(outsourcingDetailsEntity.getPreservationLatestStatus().equals((3))){
                OrderInfoEntity entity = orderInfoMapper.selectById(outsourcingDetailsEntity.getOrderId());
                Assert.notNull(entity, () -> new BusinessException("订单不存在"));
                if (Objects.equals(entity.getFundId(), FundEnum.ZHONG_HENG_TONG_HUI.getValue())) {
                    log.info("CaseInfoServiceImpl.updateOutsourcingDetails tongHuiCompensateSwitch start entity:{}", entity);
                    approveFeign.tongHuiCompensateSwitch(new TongHuiCompensateSwitchDTO().setCompensateStatus(2).setOrderId(entity.getId()));
                }
            }
        }
    }

    private void addOutsourcingDetails(OutsourcingDetailsDTO outsourcingDetailsEntity) {
        String attachments = "";
        if (CollUtil.isNotEmpty(outsourcingDetailsEntity.getAttachments())) {
            attachments = JSONUtil.toJsonStr(outsourcingDetailsEntity.getAttachments());
        }
        String visitReports = "";
        if (CollUtil.isNotEmpty(outsourcingDetailsEntity.getVisitReports())) {
            visitReports = JSONUtil.toJsonStr(outsourcingDetailsEntity.getVisitReports());
        }


        if (outsourcingDetailsEntity.getOutsourcingDetailsId() != null) {
            OutsourcingDetailsEntity outsourcingDetailsEntity1 = outsourcingDetailsEntityMapper.selectById(outsourcingDetailsEntity.getOutsourcingDetailsId());
            outsourcingDetailsEntity1.setCaseId(outsourcingDetailsEntity.getCaseId());
            outsourcingDetailsEntity1.setOrderId(outsourcingDetailsEntity.getOrderId());
            outsourcingDetailsEntity1.setRegionName(outsourcingDetailsEntity.getRegionName());
            outsourcingDetailsEntity1.setDrivingLicense(outsourcingDetailsEntity.getDrivingLicense());
            outsourcingDetailsEntity1.setWarehouseType(outsourcingDetailsEntity.getWarehouseType());
            outsourcingDetailsEntity1.setIdCard(outsourcingDetailsEntity.getIdCard());
            outsourcingDetailsEntity1.setParkingCost(outsourcingDetailsEntity.getParkingCost());
            outsourcingDetailsEntity1.setVehicleGoods(outsourcingDetailsEntity.getVehicleGoods());
            outsourcingDetailsEntity1.setVehicleAssessPrice(outsourcingDetailsEntity.getVehicleAssessPrice());
            outsourcingDetailsEntity1.setVehicleCondition(outsourcingDetailsEntity.getVehicleCondition());
            outsourcingDetailsEntity1.setDeliveryNature(outsourcingDetailsEntity.getDeliveryNature());
            outsourcingDetailsEntity1.setIsTransfer(outsourcingDetailsEntity.getIsTransfer());
            outsourcingDetailsEntity1.setInterestType(outsourcingDetailsEntity.getInterestType());
            outsourcingDetailsEntity1.setVehicleType(outsourcingDetailsEntity.getVehicleType());
            outsourcingDetailsEntity1.setMileage(outsourcingDetailsEntity.getMileage());
            outsourcingDetailsEntity1.setRegistrationTime(outsourcingDetailsEntity.getRegistrationTime());
            outsourcingDetailsEntity1.setKeyStatus(outsourcingDetailsEntity.getKeyStatus());
            outsourcingDetailsEntity1.setDrivingLicenseStatus(outsourcingDetailsEntity.getDrivingLicenseStatus());
            outsourcingDetailsEntity1.setCustomerSituation(outsourcingDetailsEntity.getCustomerSituation());
            outsourcingDetailsEntity1.setRecordSituation(outsourcingDetailsEntity.getRecordSituation());
            outsourcingDetailsEntity1.setParticipant(outsourcingDetailsEntity.getParticipant());
            outsourcingDetailsEntity1.setPreservationLatestStatus(outsourcingDetailsEntity.getPreservationLatestStatus());
            outsourcingDetailsEntity1.setAttachment(attachments);
            outsourcingDetailsEntity1.setVisitTime(outsourcingDetailsEntity.getVisitTime());
            outsourcingDetailsEntity1.setVisitAddress(outsourcingDetailsEntity.getVisitAddress());
            outsourcingDetailsEntity1.setVisitReport(visitReports);
            outsourcingDetailsEntity1.setVisitLatestStatus(outsourcingDetailsEntity.getVisitLatestStatus());
            outsourcingDetailsEntity1.setVehicleSituation(outsourcingDetailsEntity.getVehicleSituation());
            outsourcingDetailsEntity1.setTransferStatus(outsourcingDetailsEntity.getTransferStatus());
            outsourcingDetailsEntity1.setParkingCity(outsourcingDetailsEntity.getParkingCity());
            outsourcingDetailsEntity1.setAreaPrice(outsourcingDetailsEntity.getAreaPrice());
            outsourcingDetailsEntity1.setParkingAmount(outsourcingDetailsEntity.getParkingAmount());
            outsourcingDetailsEntity1.setFlowType(outsourcingDetailsEntity.getFlowType());
            outsourcingDetailsEntity1.setDisposalRemark(outsourcingDetailsEntity.getDisposalRemark());
            outsourcingDetailsEntity1.setWarehouseAddress(outsourcingDetailsEntity.getWarehouseAddress());
            outsourcingDetailsEntity1.setPreservationMethod(outsourcingDetailsEntity.getPreservationMethod());
            outsourcingDetailsEntity1.setExpectedProcessingPlan(outsourcingDetailsEntity.getExpectedProcessingPlan());
            outsourcingDetailsEntityMapper.updateById(outsourcingDetailsEntity1);


        } else {
            OutsourcingDetailsEntity outsourcingDetailsRecordEntity = new OutsourcingDetailsEntity();
            outsourcingDetailsRecordEntity.setCaseId(outsourcingDetailsEntity.getCaseId());
            outsourcingDetailsRecordEntity.setOrderId(outsourcingDetailsEntity.getOrderId());
            outsourcingDetailsRecordEntity.setRegionName(outsourcingDetailsEntity.getRegionName());
            outsourcingDetailsRecordEntity.setDrivingLicense(outsourcingDetailsEntity.getDrivingLicense());
            outsourcingDetailsRecordEntity.setWarehouseType(outsourcingDetailsEntity.getWarehouseType());
            outsourcingDetailsRecordEntity.setIdCard(outsourcingDetailsEntity.getIdCard());
            outsourcingDetailsRecordEntity.setParkingCost(outsourcingDetailsEntity.getParkingCost());
            outsourcingDetailsRecordEntity.setVehicleGoods(outsourcingDetailsEntity.getVehicleGoods());
            outsourcingDetailsRecordEntity.setVehicleAssessPrice(outsourcingDetailsEntity.getVehicleAssessPrice());
            outsourcingDetailsRecordEntity.setVehicleCondition(outsourcingDetailsEntity.getVehicleCondition());
            outsourcingDetailsRecordEntity.setDeliveryNature(outsourcingDetailsEntity.getDeliveryNature());
            outsourcingDetailsRecordEntity.setIsTransfer(outsourcingDetailsEntity.getIsTransfer());
            outsourcingDetailsRecordEntity.setInterestType(outsourcingDetailsEntity.getInterestType());
            outsourcingDetailsRecordEntity.setVehicleType(outsourcingDetailsEntity.getVehicleType());
            outsourcingDetailsRecordEntity.setMileage(outsourcingDetailsEntity.getMileage());
            outsourcingDetailsRecordEntity.setRegistrationTime(outsourcingDetailsEntity.getRegistrationTime());
            outsourcingDetailsRecordEntity.setKeyStatus(outsourcingDetailsEntity.getKeyStatus());
            outsourcingDetailsRecordEntity.setDrivingLicenseStatus(outsourcingDetailsEntity.getDrivingLicenseStatus());
            outsourcingDetailsRecordEntity.setCustomerSituation(outsourcingDetailsEntity.getCustomerSituation());
            outsourcingDetailsRecordEntity.setRecordSituation(outsourcingDetailsEntity.getRecordSituation());
            outsourcingDetailsRecordEntity.setParticipant(outsourcingDetailsEntity.getParticipant());
            outsourcingDetailsRecordEntity.setPreservationLatestStatus(outsourcingDetailsEntity.getPreservationLatestStatus());
            outsourcingDetailsRecordEntity.setAttachment(attachments);
            outsourcingDetailsRecordEntity.setVisitTime(outsourcingDetailsEntity.getVisitTime());
            outsourcingDetailsRecordEntity.setVisitAddress(outsourcingDetailsEntity.getVisitAddress());
            outsourcingDetailsRecordEntity.setVisitReport(visitReports);
            outsourcingDetailsRecordEntity.setVisitLatestStatus(outsourcingDetailsEntity.getVisitLatestStatus());
            outsourcingDetailsRecordEntity.setVehicleSituation(outsourcingDetailsEntity.getVehicleSituation());
            outsourcingDetailsRecordEntity.setTransferStatus(outsourcingDetailsEntity.getTransferStatus());
            outsourcingDetailsRecordEntity.setParkingCity(outsourcingDetailsEntity.getParkingCity());
            outsourcingDetailsRecordEntity.setAreaPrice(outsourcingDetailsEntity.getAreaPrice());
            outsourcingDetailsRecordEntity.setParkingAmount(outsourcingDetailsEntity.getParkingAmount());
            outsourcingDetailsRecordEntity.setFlowType(outsourcingDetailsEntity.getFlowType());
            outsourcingDetailsRecordEntity.setDisposalRemark(outsourcingDetailsEntity.getDisposalRemark());
            outsourcingDetailsRecordEntity.setWarehouseAddress(outsourcingDetailsEntity.getWarehouseAddress());
            outsourcingDetailsRecordEntity.setPreservationMethod(outsourcingDetailsEntity.getPreservationMethod());
            outsourcingDetailsRecordEntity.setExpectedProcessingPlan(outsourcingDetailsEntity.getExpectedProcessingPlan());
            outsourcingDetailsEntityMapper.insert(outsourcingDetailsRecordEntity);
        }
    }

    private void updateCaseInfoResult(Integer caseId, Integer preservationLatestStatus, Integer visitLatestStatus, Integer transferStatus) {
        if (preservationLatestStatus != null) {
            if (ObjUtil.equal(preservationLatestStatus, 6)) {
                caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                        .set(CaseInfoEntity::getPreservationResult, preservationLatestStatus)
                        .set(CaseInfoEntity::getCurrentNode, ASSET_MANAGER_APPROVED)
                        .eq(CaseInfoEntity::getId, caseId).eq(CaseInfoEntity::getDeleteFlag, 0));
            }
            //更新委外最新状态
            caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                    .set(CaseInfoEntity::getPreservationResult, preservationLatestStatus)
                    .eq(CaseInfoEntity::getId, caseId).eq(CaseInfoEntity::getDeleteFlag, 0));


        } else if (visitLatestStatus != null) {
            //更新委外最新状态
            caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                    .set(CaseInfoEntity::getVisitResult, visitLatestStatus)
                    .eq(CaseInfoEntity::getId, caseId).eq(CaseInfoEntity::getDeleteFlag, 0));
        } else if (transferStatus != null) {
            CaseInfoEntity caseInfoEntity=caseInfoEntityMapper.selectById(caseId);
            //移交法务
            if(ObjUtil.equal(transferStatus, 4)&&ObjUtil.equal(caseInfoEntity.getDataSource(),1)){
                // 进入诉讼池
                LawsuitApplicationEntity lawsuitApplicationEntity = new LawsuitApplicationEntity();
                lawsuitApplicationEntity.setOrderId(caseInfoEntity.getOrderId());
                lawsuitApplicationEntity.setLawsuitForm(0);
                lawsuitApplicationEntity.setProcessingStatus(0);
                lawsuitApplicationEntity.setDisposalCompany("爱裁");
                lawsuitApplicationEntity.setDeptId(DeptEnum.AI_CAI.getId());
                lawsuitApplicationEntity.setApprovalStatus(0);
                lawsuitApplicationEntity.setPrincipal("系统发起");

                lawsuitApplicationEntityMapper.insert(lawsuitApplicationEntity);
                //生成法诉附件
                lawsuitService.getComplaintExtraction(caseInfoEntity.getOrderId());
                lawsuitService.getApplicationBook(caseInfoEntity.getOrderId());
                LawsuitContractDTO lawsuitContractDTO = new LawsuitContractDTO();
                lawsuitContractDTO.setOrderId(caseInfoEntity.getOrderId());
                lawsuitService.createCompanyContract(lawsuitContractDTO);
                DebtTransferNoticeDTO debtTransferNoticeDTO = new DebtTransferNoticeDTO();
                debtTransferNoticeDTO.setOrderId(caseInfoEntity.getOrderId());
                lawsuitService.debtTransferNotice(debtTransferNoticeDTO);
                DebtTransferNoticeDTO debtTransferNoticeDTO1 = new DebtTransferNoticeDTO();
                debtTransferNoticeDTO1.setOrderId(caseInfoEntity.getOrderId());
                lawsuitService.getDeliveryAddressConfirmation(debtTransferNoticeDTO1);
            }


            //更新委外最新状态
            caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                    .set(CaseInfoEntity::getTransferStatus, transferStatus)
                    .eq(CaseInfoEntity::getId, caseId).eq(CaseInfoEntity::getDeleteFlag, 0));
        }
    }

    //添加更新记录
    public void addOutsourcingDetailsReocrd(OutsourcingDetailsDTO outsourcingDetailsEntity, LoginUser loginUser) {
        String attachments = "";
        if (CollUtil.isNotEmpty(outsourcingDetailsEntity.getAttachments())) {
            attachments = JSONUtil.toJsonStr(outsourcingDetailsEntity.getAttachments());
        }
        String visitReports = "";
        if (CollUtil.isNotEmpty(outsourcingDetailsEntity.getVisitReports())) {
            visitReports = JSONUtil.toJsonStr(outsourcingDetailsEntity.getVisitReports());
        }
        OutsourcingDetailsRecordEntity outsourcingDetailsRecordEntity = new OutsourcingDetailsRecordEntity();
        outsourcingDetailsRecordEntity.setCaseId(outsourcingDetailsEntity.getCaseId());
        outsourcingDetailsRecordEntity.setOrderId(outsourcingDetailsEntity.getOrderId());
        outsourcingDetailsRecordEntity.setRegionName(outsourcingDetailsEntity.getRegionName());
        outsourcingDetailsRecordEntity.setDrivingLicense(outsourcingDetailsEntity.getDrivingLicense());
        outsourcingDetailsRecordEntity.setWarehouseType(outsourcingDetailsEntity.getWarehouseType());
        outsourcingDetailsRecordEntity.setIdCard(outsourcingDetailsEntity.getIdCard());
        outsourcingDetailsRecordEntity.setParkingCost(outsourcingDetailsEntity.getParkingCost());
        outsourcingDetailsRecordEntity.setVehicleGoods(outsourcingDetailsEntity.getVehicleGoods());
        outsourcingDetailsRecordEntity.setVehicleAssessPrice(outsourcingDetailsEntity.getVehicleAssessPrice());
        outsourcingDetailsRecordEntity.setVehicleCondition(outsourcingDetailsEntity.getVehicleCondition());
        outsourcingDetailsRecordEntity.setDeliveryNature(outsourcingDetailsEntity.getDeliveryNature());
        outsourcingDetailsRecordEntity.setIsTransfer(outsourcingDetailsEntity.getIsTransfer());
        outsourcingDetailsRecordEntity.setInterestType(outsourcingDetailsEntity.getInterestType());
        outsourcingDetailsRecordEntity.setVehicleType(outsourcingDetailsEntity.getVehicleType());
        outsourcingDetailsRecordEntity.setMileage(outsourcingDetailsEntity.getMileage());
        outsourcingDetailsRecordEntity.setRegistrationTime(outsourcingDetailsEntity.getRegistrationTime());
        outsourcingDetailsRecordEntity.setKeyStatus(outsourcingDetailsEntity.getKeyStatus());
        outsourcingDetailsRecordEntity.setDrivingLicenseStatus(outsourcingDetailsEntity.getDrivingLicenseStatus());
        outsourcingDetailsRecordEntity.setCustomerSituation(outsourcingDetailsEntity.getCustomerSituation());
        outsourcingDetailsRecordEntity.setRecordSituation(outsourcingDetailsEntity.getRecordSituation());
        outsourcingDetailsRecordEntity.setParticipant(outsourcingDetailsEntity.getParticipant());
        outsourcingDetailsRecordEntity.setPreservationLatestStatus(outsourcingDetailsEntity.getPreservationLatestStatus());
        outsourcingDetailsRecordEntity.setAttachment(attachments);
        outsourcingDetailsRecordEntity.setVisitTime(outsourcingDetailsEntity.getVisitTime());
        outsourcingDetailsRecordEntity.setVisitAddress(outsourcingDetailsEntity.getVisitAddress());
        outsourcingDetailsRecordEntity.setVisitReport(visitReports);
        outsourcingDetailsRecordEntity.setVisitLatestStatus(outsourcingDetailsEntity.getVisitLatestStatus());
        outsourcingDetailsRecordEntity.setVehicleSituation(outsourcingDetailsEntity.getVehicleSituation());
        outsourcingDetailsRecordEntity.setUpdateName(loginUser.getName());
        outsourcingDetailsRecordEntity.setUpdateBy(loginUser.getUserId());
        outsourcingDetailsRecordEntity.setTransferStatus(outsourcingDetailsEntity.getTransferStatus());
        outsourcingDetailsRecordEntity.setParkingCity(outsourcingDetailsEntity.getParkingCity());
        outsourcingDetailsRecordEntity.setAreaPrice(outsourcingDetailsEntity.getAreaPrice());
        outsourcingDetailsRecordEntity.setParkingAmount(outsourcingDetailsEntity.getParkingAmount());
        outsourcingDetailsRecordEntity.setFlowType(outsourcingDetailsEntity.getFlowType());
        outsourcingDetailsRecordEntity.setDisposalRemark(outsourcingDetailsEntity.getDisposalRemark());

        outsourcingDetailsRecordEntityMapper.insert(outsourcingDetailsRecordEntity);

    }

    @Override
    public OutsourcingDetailsEntity getOutsourcingDetails(Integer caseId) {
        log.info("orderServiceImpl getOutsourcingDetails start，caseId：{}", caseId);
        OutsourcingDetailsEntity outsourcingDetailsEntity = outsourcingDetailsEntityMapper.selectOne(new LambdaQueryWrapper<OutsourcingDetailsEntity>().eq(ObjectUtil.isNotEmpty(caseId), OutsourcingDetailsEntity::getCaseId, caseId).eq(OutsourcingDetailsEntity::getDeleteFlag, 0).orderByDesc(OutsourcingDetailsEntity::getCreateTime).last("limit 1"));
        return outsourcingDetailsEntity;
    }

    @Override
    public Page<OutsourcingDetailsRecordEntity> getOutsourcingDetailsRecord(Integer caseId) {
        log.info("orderServiceImpl getOutsourcingDetailsRecord start，caseId：{}", caseId);

        Page<OutsourcingDetailsRecordEntity> outsourcingDetailsRecordEntityPage = outsourcingDetailsRecordEntityMapper.selectPage(new Page<>(1, 10), new LambdaQueryWrapper<OutsourcingDetailsRecordEntity>().eq(OutsourcingDetailsRecordEntity::getCaseId, caseId).eq(OutsourcingDetailsRecordEntity::getDeleteFlag, 0).orderByDesc(OutsourcingDetailsRecordEntity::getCreateTime));
        return outsourcingDetailsRecordEntityPage;
    }

    @Override
    public void retryDingTask() {

        // 获取钉钉节点的审批列表 资管副总、区总审批
        List<CaseInfoEntity> dingTaskApproveVOList = caseInfoEntityMapper.selectList(new LambdaQueryWrapper<CaseInfoEntity>()
                .in(CaseInfoEntity::getCurrentNode, Arrays.asList(ASSET_VICE_PRESIDENT_APPROVED, REGION_GENERAL_APPROVED))
                .isNotNull(CaseInfoEntity::getProcessId)
                .eq(CaseInfoEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(dingTaskApproveVOList)) {
            return;
        }

        //发起钉钉推送
//        initiateOutsourcingAdvanceDingTalkApproval(caseInfoDetailVO, loginUser.getUserId());
    }

    @Override
    public void exportOutsourcing(CaseInfoListDTO caseInfoListDTO, HttpServletResponse response, LoginUser loginUser) {
        MPJLambdaWrapper<CaseInfoEntity> queryWrapper = new MPJLambdaWrapper<>(CaseInfoEntity.class)
                .selectAs(OrderInfoEntity::getOrderNumber, OutsourcedExportVO::getBillNo)
                .selectAs(OrderInfoEntity::getRegionName, OutsourcedExportVO::getRegion)
                .selectAs(OrderInfoEntity::getStoreName, OutsourcedExportVO::getStore)
                .selectAs(OrderInfoEntity::getProductName, OutsourcedExportVO::getProductName)

                .selectAs(OrderInfoEntity::getTerm, OutsourcedExportVO::getProductPeriod)
//                .selectAs(OrderInfoEntity::getRepayMethod,OutsourcedExportVO::getrepaymentMethod)
                .select("CASE WHEN t1.repay_method =1 THEN '等额本息'  WHEN t1.repay_method =2 THEN '等额本金'  WHEN t1.repay_method =3 THEN '先息后本' ELSE '未知' END as repaymentMethod  ")
                .selectAs(OrderAmountEntity::getCustomerConfirmAmount, OutsourcedExportVO::getLoanAmount)
                //赎回状态
//                .selectAs(OrderInfoEntity::getIsRepurchase,OutsourcedExportVO::getRedemptionStatus)

                .select("CASE WHEN t1.is_repurchase =1 THEN '是' WHEN t1.is_repurchase =0 THEN '否' ELSE '未知' END as redemptionStatus  ")
                .selectAs(CaseInfoEntity::getInitiator, OutsourcedExportVO::getCirculationApplicant)
//                .selectAs(CaseInfoEntity::getCirculationStatus,OutsourcedExportVO::getCirculationApplicantStatus)
                .select("CASE WHEN t.circulation_status =0 THEN '未流转' WHEN t.circulation_status=1 THEN '流转成功' WHEN t.circulation_status=2 THEN '流转失败'  WHEN t.circulation_status=3 THEN '流转中'ELSE '未知' END as circulationApplicantStatus  ")
                .selectAs(CaseInfoEntity::getDisposalCompany, OutsourcedExportVO::getCirculationThirdParty)
                .select("CASE WHEN t.preservation_result = 0 THEN '未保全' WHEN t.preservation_result = 1 THEN '已保全' WHEN t.preservation_result = 2 THEN '车辆入库' WHEN t.preservation_result = 3 THEN '面谈全额结清' WHEN t.preservation_result = 4 THEN '面谈过户' WHEN t.preservation_result = 5 THEN '面谈部分结清'  ELSE '无' END AS postProcessingResult")
                .selectAs(OrderInfoEntity::getCustomerName, OutsourcedExportVO::getCustomerName)
                .selectAs(OrderCustomerInfoEntity::getIdNumber, OutsourcedExportVO::getIdCard)
                .selectAs(OrderVehicleInfoEntity::getVehicleNumber, ExportApplicationElementsVO::getCarNumber)
                .selectAs(CaseInfoEntity::getPreservationTime, OutsourcedExportVO::getPreservationTime)
                .selectAs(CaseInfoEntity::getSettledTime, OutsourcedExportVO::getSettlementDate)
                .selectAs(CaseInfoEntity::getCreateTime, OutsourcedExportVO::getCirculationApplicantDate)
                .selectAs(OrderInfoEntity::getPaymentTime, OutsourcedExportVO::getLoanDate)
                .selectAs(OrderInfoEntity::getOverdueDays, OutsourcedExportVO::getOverdueDays)
                .selectAs(CaseInfoEntity::getDataSource, OutsourcedExportVO::getDataSource)
                .selectAs("null", OutsourcedExportVO::getOverdueDate)
                .selectAs(CaseInfoEntity::getOrderId, OutsourcedExportVO::getOrderId)

                .leftJoin(OrderInfoEntity.class, OrderInfoEntity::getId, CaseInfoEntity::getOrderId)
                .leftJoin(OrderCustomerInfoEntity.class, OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId)
                .leftJoin(OrderVehicleInfoEntity.class, OrderVehicleInfoEntity::getOrderId, OrderInfoEntity::getId)
                .leftJoin(OrderAmountEntity.class, OrderAmountEntity::getOrderId, OrderInfoEntity::getId)
                .eq(OrderAmountEntity::getDeleteFlag, 0)
                .eq(CaseInfoEntity::getDeleteFlag, 0)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .eq(OrderCustomerInfoEntity::getDeleteFlag, 0)
                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0);
        outsourcingService.buildAQueryConditionForYunqi(caseInfoListDTO, queryWrapper);
        log.info("getCaseInfolist：{}", loginUser);
        List<Integer> roleIds = loginUser.getRoleIds();
        String scopes = loginUser.getScopes();
        boolean hasRole = RoleEnum.ACCOUNTANT.hasRole(roleIds) || RoleEnum.CASHIER.hasRole(roleIds) || RoleEnum.SYS_ADMIN.hasRole(roleIds) || RoleEnum.QUALITY_MANAGER.hasRole(roleIds) || RoleEnum.REGIONAL_OPERATIONS.hasRole(roleIds) || RoleEnum.OPERATION_SPECIALIST.hasRole(roleIds);
        log.info("getCaseInfolist.hasRole:{}", hasRole);
        if (!hasRole || (scopes != null && !scopes.contains("data:all"))) {
            // 数据权限

            dataPermissionService.limitCaseInfolistWithOrder(loginUser, queryWrapper);
        }
        // 修改构造方式，传入主表类 CaseInfoEntity
        MPJLambdaWrapper<CaseInfoEntity> shuzihuaqueryWrapper = new MPJLambdaWrapper<>(CaseInfoEntity.class)
                .selectAs(DigitalOutsourcingOrderEntity::getOrderNo, OutsourcedExportVO::getBillNo)
                .selectAs(DigitalOutsourcingOrderEntity::getRegionName, OutsourcedExportVO::getRegion)
                .selectAs(DigitalOutsourcingOrderEntity::getStoreName, OutsourcedExportVO::getStore)
                .selectAs(DigitalOutsourcingOrderEntity::getProductName, OutsourcedExportVO::getProductName)
                .selectAs(DigitalOutsourcingOrderEntity::getTerm, OutsourcedExportVO::getProductPeriod)//todo 期数
                .selectAs(DigitalOutsourcingOrderEntity::getRepayMethod, OutsourcedExportVO::getRepaymentMethod)
                .selectAs(DigitalOutsourcingOrderEntity::getApprovalAmount, OutsourcedExportVO::getLoanAmount)
                .select("CASE WHEN t1.redemption_amount is not null THEN '是' WHEN t1.redemption_amount is not null THEN '否' ELSE '未知' END as redemptionStatus  ")

                .selectAs(CaseInfoEntity::getInitiator, OutsourcedExportVO::getRedemptionStatus)
                .select("CASE WHEN t.circulation_status =0 THEN '未流转' WHEN t.circulation_status=1 THEN '流转成功' WHEN t.circulation_status=2 THEN '流转失败'  WHEN t.circulation_status=3 THEN '流转中'ELSE '未知' END as circulationApplicantStatus  ")
                .selectAs(CaseInfoEntity::getDisposalCompany, OutsourcedExportVO::getCirculationThirdParty)
                .select("CASE WHEN t.preservation_result = 0 THEN '未保全' WHEN t.preservation_result = 1 THEN '已保全' WHEN t.preservation_result = 2 THEN '车辆入库' WHEN t.preservation_result = 3 THEN '面谈全额结清' WHEN t.preservation_result = 4 THEN '面谈过户' WHEN t.preservation_result = 5 THEN '面谈部分结清'  ELSE '无' END AS postProcessingResult")

                .selectAs(DigitalOutsourcingOrderEntity::getCustomerName, OutsourcedExportVO::getCustomerName)
                .selectAs(DigitalOutsourcingOrderEntity::getIdNumber, OutsourcedExportVO::getIdCard)
                .selectAs(DigitalOutsourcingOrderEntity::getVehicleNumber, ExportApplicationElementsVO::getCarNumber)

                .selectAs(CaseInfoEntity::getPreservationTime, OutsourcedExportVO::getPreservationTime)
                .selectAs(CaseInfoEntity::getSettledTime, OutsourcedExportVO::getSettlementDate)
                .selectAs(CaseInfoEntity::getCreateTime, OutsourcedExportVO::getCirculationApplicantDate)
                .selectAs(DigitalOutsourcingOrderEntity::getPaymentTime, OutsourcedExportVO::getLoanDate)
                .selectAs(DigitalOutsourcingOrderEntity::getOverdueDays, OutsourcedExportVO::getOverdueDays)
                .selectAs(CaseInfoEntity::getDataSource, OutsourcedExportVO::getDataSource)
                .selectAs(DigitalOutsourcingOrderEntity::getOverdueDate, OutsourcedExportVO::getOverdueDate)
                .selectAs(CaseInfoEntity::getOrderId, OutsourcedExportVO::getOrderId)

                .leftJoin(DigitalOutsourcingOrderEntity.class, DigitalOutsourcingOrderEntity::getOrderId, CaseInfoEntity::getDigitalOrderId)
//                .leftJoin(OutsourcingContractsApprovalEntity.class, OutsourcingContractsApprovalEntity::getOutsourcingId, CaseInfoEntity::getId)
//
                .eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0)
//                .eq(OutsourcingContractsApprovalEntity::getDeleteFlag, 0)
                .eq(CaseInfoEntity::getDeleteFlag, 0);
        outsourcingService.buildAQueryConditionForDigital(caseInfoListDTO, shuzihuaqueryWrapper, queryWrapper);
        log.info("getCaseInfolist：{}", loginUser);
        roleIds = loginUser.getRoleIds();
        scopes = loginUser.getScopes();
        hasRole = RoleEnum.ACCOUNTANT.hasRole(roleIds) || RoleEnum.CASHIER.hasRole(roleIds) || RoleEnum.SYS_ADMIN.hasRole(roleIds) || RoleEnum.QUALITY_MANAGER.hasRole(roleIds) || RoleEnum.REGIONAL_OPERATIONS.hasRole(roleIds) || RoleEnum.OPERATION_SPECIALIST.hasRole(roleIds);
        log.info("getCaseInfolist.hasRole:{}", hasRole);
        if (!hasRole || (scopes != null && !scopes.contains("data:all"))) {
            // 数据权限

            dataPermissionService.limitCaseInfolistWithOrder(loginUser, shuzihuaqueryWrapper);
        }

        List<OutsourcedExportVO> outsourcedExportVOS = caseInfoEntityMapper.selectJoinList(OutsourcedExportVO.class, shuzihuaqueryWrapper);
        Map<Integer, List<OutsourcedExportVO>> collect = outsourcedExportVOS.stream().collect(Collectors.groupingBy(OutsourcedExportVO::getDataSource, Collectors.toList()));
        if (!collect.isEmpty()) {
            collect.forEach((k, v) -> {
                if (ObjUtil.equals(k, 1)) {
                    //todo 补充云启
                    for (OutsourcedExportVO outsourcedExportVO : v) {
                        //补充逾期时间
                        if (StrUtil.isNotBlank(outsourcedExportVO.getOverdueDays())) {
                            FundRepaymentInfoEntity fundRepaymentInfoEntity = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                    .eq(FundRepaymentInfoEntity::getOrderId, outsourcedExportVO.getOrderId())
                                    .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.OVERDUE)
                                    .orderByAsc(FundRepaymentInfoEntity::getTerm), false);
                            if (ObjectUtil.isNotEmpty(fundRepaymentInfoEntity) && ObjUtil.isNotNull(fundRepaymentInfoEntity.getRepaymentDate())) {
                                outsourcedExportVO.setOverdueDate(fundRepaymentInfoEntity.getRepaymentDate().toString());
                            }
                        }
                    }
                }
                if (ObjUtil.equals(k, 2)) {
                    //todo 补充数字化
                }
            });
        }
        try {
            response.addHeader("charset", "utf-8");
            String fileName = String.format("债转明细-%s.xlsx", LocalDate.now());
            String encodeName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
            Optional<MediaType> mediaTypeOptional = MediaTypeFactory.getMediaType(encodeName);
            MediaType mediaType = mediaTypeOptional.orElse(MediaType.APPLICATION_OCTET_STREAM);
            log.info("LoanPerformanceServiceImpl.exportExcel mediaType = {}", JSONUtil.toJsonStr(mediaType));
            response.setContentType(String.valueOf(mediaType));
            log.info("LoanPerformanceServiceImpl.exportExcel response contentType = {}", response.getContentType());
            response.setHeader("Content-disposition", "attachment;filename=" + encodeName);
            String sheetName = "债转明细";
            EasyExcel.write(response.getOutputStream())
                    // 动态头
                    .head(OutsourcedExportVO.class)
                    .registerWriteHandler(EasyExcelUtil.getHorizontalCellStyleStrategy())
                    .registerWriteHandler(new EasyExcelUtil.CustomCellWriteWidthConfig())
                    .sheet(sheetName)
                    // 表格数据
                    .doWrite(outsourcedExportVOS);
        } catch (IOException e) {
            log.error("LoanPerformanceServiceImpl.exportExcel error", e);
        }

    }

    @Override
    public DebtTransfersContractVO getDebtTransfersContractList(List<Integer> caseInfoListDTO, LoginUser loginUser) {
        DebtTransfersContractVO debtTransfersContractVO = new DebtTransfersContractVO();
        LocalDateTime now = LocalDateTime.now();
        List<CaseInfoEntity> caseInfoEntities = caseInfoEntityMapper.selectList(new LambdaQueryWrapper<CaseInfoEntity>()
                .in(CaseInfoEntity::getId, caseInfoListDTO));
        Map<Integer, CaseInfoEntity> caseInfoEntityMap = caseInfoEntities.stream().collect(Collectors.toMap(CaseInfoEntity::getId, Function.identity()));
        List<DebtTransfersContractListVO> debtTransfersContractListVOS = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;
        Set<String> selectCompanySet = new HashSet<>();
        for (Integer caseInfoId : caseInfoListDTO) {
            DebtTransfersContractListVO debtTransfersContractListVO = new DebtTransfersContractListVO();
            Assert.isTrue(caseInfoEntityMap.containsKey(caseInfoId), () -> new BusinessException(caseInfoId + "该案件不存在"));
            CaseInfoEntity caseInfoEntity = caseInfoEntityMap.get(caseInfoId);
            log.info("LoanPerformanceServiceImpl.getDebtTransfersContractList caseInfoEntity:{}", caseInfoEntity);
            //处理数字化
            if (ObjUtil.equals(caseInfoEntity.getDataSource(), 2)) {
                DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>()
                        .eq(DigitalOutsourcingOrderEntity::getOrderNo, caseInfoEntity.getDigitalOrderId())
                        .eq(DigitalOutsourcingOrderEntity::getDataSource, caseInfoEntity.getDataSource())
                        .eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0), false);
                Assert.isTrue(ObjUtil.isNotNull(digitalOutsourcingOrderEntity), () -> new BusinessException(caseInfoEntity.getCustomerName() + "未找到数字化订单信息"));
                if (ObjUtil.equals(caseInfoEntity.getDebtTransfersContractBuildStatus(), 2)) {
                    throw new BusinessException(digitalOutsourcingOrderEntity.getCustomerName() + "," + digitalOutsourcingOrderEntity.getVehicleNumber() + "当前任务已生电子合同，请勿重复操作生成债转合同。");
                }
                if (ObjUtil.equals(caseInfoEntity.getDebtTransfersContractBuildStatus(), 1)) {
                    throw new BusinessException(digitalOutsourcingOrderEntity.getCustomerName() + "," + digitalOutsourcingOrderEntity.getVehicleNumber() + "当前任务正在生成债转合同，请稍后。");
                }
                debtTransfersContractListVO = new DebtTransfersContractListVO()
                        .setId(caseInfoEntity.getId())
                        .setOrderNumber(digitalOutsourcingOrderEntity.getOrderNo())
                        .setLoanContractNumber(digitalOutsourcingOrderEntity.getLoanContractNumber())
                        .setBorrowerName(digitalOutsourcingOrderEntity.getCustomerName())
                        .setBorrowerCardId(digitalOutsourcingOrderEntity.getIdNumber())
                        .setVin(digitalOutsourcingOrderEntity.getVin())
                        .setRemainingPrincipal(new BigDecimal(digitalOutsourcingOrderEntity.getPrincipalAmount() == null ? "0" : digitalOutsourcingOrderEntity.getPrincipalAmount()))
                        .setRemainingInterest(new BigDecimal(digitalOutsourcingOrderEntity.getInterestAmount() == null ? "0" : digitalOutsourcingOrderEntity.getInterestAmount()))
                        .setBaseDate(now)
                        .setCompany(caseInfoEntity.getDisposalCompany())
                        .setLoanStartDate(digitalOutsourcingOrderEntity.getPaymentTime())
                        .setTransferPrice((new BigDecimal(digitalOutsourcingOrderEntity.getPrincipalAmount() == null ? "0" : digitalOutsourcingOrderEntity.getPrincipalAmount())).add(new BigDecimal(digitalOutsourcingOrderEntity.getInterestAmount() == null ? "0" : digitalOutsourcingOrderEntity.getInterestAmount())));
                debtTransfersContractListVOS.add(debtTransfersContractListVO);
                //todo 数字化待完善
                //处理云启
            }
            if (ObjUtil.equals(caseInfoEntity.getDataSource(), 3)) {
                DigitalOutsourcingOrderEntity digitalOutsourcingOrderEntity = digitalOutsourcingOrderEntityMapper.selectOne(new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>()
                        .eq(DigitalOutsourcingOrderEntity::getOrderNo, caseInfoEntity.getDigitalOrderId())
                        .eq(DigitalOutsourcingOrderEntity::getDataSource, caseInfoEntity.getDataSource())
                        .eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0), false);
                Assert.isTrue(ObjUtil.isNotNull(digitalOutsourcingOrderEntity), () -> new BusinessException(caseInfoEntity.getCustomerName() + "未找到数字化订单信息"));
                if (ObjUtil.equals(caseInfoEntity.getDebtTransfersContractBuildStatus(), 2)) {
                    throw new BusinessException(digitalOutsourcingOrderEntity.getCustomerName() + "," + digitalOutsourcingOrderEntity.getVehicleNumber() + "当前任务已生电子合同，请勿重复操作生成债转合同。");
                }
                if (ObjUtil.equals(caseInfoEntity.getDebtTransfersContractBuildStatus(), 1)) {
                    throw new BusinessException(digitalOutsourcingOrderEntity.getCustomerName() + "," + digitalOutsourcingOrderEntity.getVehicleNumber() + "当前任务正在生成债转合同，请稍后。");
                }
                debtTransfersContractListVO = new DebtTransfersContractListVO()
                        .setId(caseInfoEntity.getId())
                        .setOrderNumber(digitalOutsourcingOrderEntity.getOrderNo())
                        .setLoanContractNumber(digitalOutsourcingOrderEntity.getLoanContractNumber())
                        .setBorrowerName(digitalOutsourcingOrderEntity.getCustomerName())
                        .setBorrowerCardId(digitalOutsourcingOrderEntity.getIdNumber())
                        .setVin(digitalOutsourcingOrderEntity.getVin())
                        .setRemainingPrincipal(digitalOutsourcingOrderEntity.getRemainingPrincipal() == null ? BigDecimal.ZERO : digitalOutsourcingOrderEntity.getRemainingPrincipal())
                        .setRemainingInterest(new BigDecimal(digitalOutsourcingOrderEntity.getInterestAmount() == null ? "0" : digitalOutsourcingOrderEntity.getInterestAmount()))
                        .setBaseDate(now)
                        .setCompany(caseInfoEntity.getDisposalCompany())
                        .setLoanStartDate(digitalOutsourcingOrderEntity.getPaymentTime())
                        .setTransferPrice((digitalOutsourcingOrderEntity.getRemainingPrincipal() == null ? BigDecimal.ZERO : digitalOutsourcingOrderEntity.getRemainingPrincipal()).add(new BigDecimal(digitalOutsourcingOrderEntity.getInterestAmount() == null ? "0" : digitalOutsourcingOrderEntity.getInterestAmount())));
                debtTransfersContractListVOS.add(debtTransfersContractListVO);
                //处理云启
            } else if (ObjUtil.equals(caseInfoEntity.getDataSource(), 1)) {
                OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
                orderDetailDTO.setOrderId(caseInfoEntity.getOrderId());
                OrderDetailVO orderDetailsVo = orderService.detail(orderDetailDTO, loginUser);
                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                        .select(OrderInfoEntity::getPaymentTime, OrderInfoEntity::getId)
                        .eq(OrderInfoEntity::getId, caseInfoEntity.getOrderId())
                        .eq(OrderInfoEntity::getDeleteFlag, 0), false);
                Assert.isTrue(ObjUtil.isNotNull(orderDetailsVo), () -> new BusinessException(caseInfoEntity.getCustomerName() + "未找到云启订单信息"));
                if (ObjUtil.equals(caseInfoEntity.getDebtTransfersContractBuildStatus(), 1)) {
                    throw new BusinessException(orderDetailsVo.getCustomerInfo().getName() + "," + orderDetailsVo.getOrderVehicleInfo().getVehicleNumber() + "当前任务正在生成债转合同，请稍后。");
                }
                if (ObjUtil.equals(caseInfoEntity.getDebtTransfersContractBuildStatus(), 2)) {
                    throw new BusinessException(orderDetailsVo.getCustomerInfo().getName() + "," + orderDetailsVo.getOrderVehicleInfo().getVehicleNumber() + "当前任务已生电子合同，请勿重复操作生成债转合同。");
                }
                debtTransfersContractListVO.setOrderNumber(orderDetailsVo.getOrderInfo().getOrderNumber());

                //姓名
                debtTransfersContractListVO.setBorrowerName(orderDetailsVo.getCustomerInfo().getName());
                //身份证
                debtTransfersContractListVO.setBorrowerCardId(orderDetailsVo.getCustomerInfo().getIdNumber());

                //借款合同编号
                String loanContractNumber = "";
                //资方id
                Integer fundId = orderDetailsVo.getOrderInfo().getFundId();
                FundEnum fundEnum = FundEnum.getFundEnum(fundId);
                Assert.notNull(fundEnum, "资方不存在");
                //根据 fundEnum switch 匹配资方
                switch (Objects.requireNonNull(fundEnum)) {
                    case YING_FENG:
                        ParamsSnapshotEntity paramsSnapshot = paramsSnapshotMapper.selectOne(new LambdaQueryWrapper<ParamsSnapshotEntity>()
                                .eq(ParamsSnapshotEntity::getLinkId, caseInfoEntity.getOrderId())
                                .eq(ParamsSnapshotEntity::getCode, TemplateParamsCodeEnum.FUND_CREDIT_APPLY_NO.getCode())
                                .eq(ParamsSnapshotEntity::getType, ParamsSnapshotEnum.ORDER_SNAPSHOT.getCode())
                                .eq(ParamsSnapshotEntity::getDeleteFlag, 0));
                        loanContractNumber = ObjUtil.isNotEmpty(paramsSnapshot) ? "LHPH_" + paramsSnapshot.getValue() : "";
                        break;
                    case FU_MIN:
                        List<FinalFundInfoEntity> finalFundInfoEntityList = finalFundInfoMapper.selectList(new LambdaQueryWrapper<FinalFundInfoEntity>()
                                .eq(FinalFundInfoEntity::getOrderId, caseInfoEntity.getOrderId())
                                .eq(FinalFundInfoEntity::getFundId, fundId)
                                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(FinalFundInfoEntity::getCreateTime));

                        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoEntityList.stream().findFirst().orElse(null);
                        loanContractNumber = finalFundInfoEntity == null ? "" : finalFundInfoEntity.getLoanContractNo();
                        break;
                    case ZHONG_HENG_TONG_HUI:
                        ParamsSnapshotEntity tonghui = paramsSnapshotMapper.selectOne(new LambdaQueryWrapper<ParamsSnapshotEntity>()
                                .eq(ParamsSnapshotEntity::getLinkId, caseInfoEntity.getOrderId())
                                .eq(ParamsSnapshotEntity::getCode, TemplateParamsCodeEnum.VEHICLE_FINANCIAL_LEASE_CONTRACT_NUMBER.getCode())
                                .eq(ParamsSnapshotEntity::getType, ParamsSnapshotEnum.ORDER_SNAPSHOT.getCode())
                                .eq(ParamsSnapshotEntity::getDeleteFlag, 0));
                        loanContractNumber = ObjUtil.isNotEmpty(tonghui) ? tonghui.getValue() : "";
                }
                debtTransfersContractListVO.setLoanContractNumber(loanContractNumber);
                debtTransfersContractListVO.setLoanStartDate(orderInfoEntity.getPaymentTime());
                //车架号
                debtTransfersContractListVO.setVin(orderDetailsVo.getOrderVehicleInfo().getVin());
                debtTransfersContractListVO.setBaseDate(LocalDateTime.now());

                debtTransfersContractListVO.setCompany(caseInfoEntity.getDisposalCompany());
                DisposalCompanyEnums companyEnums = DisposalCompanyEnums.getDisposalCompanyEnums(caseInfoEntity.getDeptId());
                Assert.notNull(companyEnums, "处置公司不存在");
                //todo 赎回单
//                if (ObjUtil.equals(orderInfoEntity.getIsRepurchase(),1)){
//
//                }else {
                //正常单
                RepaymentOverdueStatusVO repaymentOverdueStatusVO = repaymentService.overdueStatusByOrderId(orderInfoEntity.getId());
                debtTransfersContractListVO.setRemainingPrincipal(repaymentOverdueStatusVO.getRemainingPrincipal() != null ? repaymentOverdueStatusVO.getRemainingPrincipal() : BigDecimal.ZERO);
                debtTransfersContractListVO.setRemainingInterest(repaymentOverdueStatusVO.getRemainingInterest() != null ? repaymentOverdueStatusVO.getRemainingInterest() : BigDecimal.ZERO);

//                }
                debtTransfersContractListVO.setTransferPrice(debtTransfersContractListVO.getRemainingPrincipal().add(debtTransfersContractListVO.getRemainingInterest()));
                debtTransfersContractListVO.setId(caseInfoId);
                debtTransfersContractListVOS.add(debtTransfersContractListVO);

            }
            totalAmount = totalAmount.add(debtTransfersContractListVO.getTransferPrice());
            selectCompanySet.add(debtTransfersContractListVO.getCompany());
        }

        debtTransfersContractVO.setListVOS(debtTransfersContractListVOS);
        debtTransfersContractVO.setCount(debtTransfersContractListVOS.size());
        debtTransfersContractVO.setTotalAmount(totalAmount);
        debtTransfersContractVO.setSelectCompany((String.join(", ", selectCompanySet)));

//        StringBuilder result = new StringBuilder();
//        for (String s : selectCompanyList) {
//            if (!result.toString().contains(s)) { // 判断是否已存在
//                if (!result.isEmpty()) {
//                    result.append(", ");
//                }
//                result.append(s);
//            }
//        }
//        debtTransfersContractVO.setSelectCompany(result.toString());
        return debtTransfersContractVO;
    }

    @Override
    public Boolean saveOutsourcingMaintenanceRecord(SaveOutsourcingMaintenanceRecordDTO dto, LoginUser loginUser) {
        Assert.isTrue(ObjUtil.isNotNull(loginUser), () -> new BusinessException("无用户信息"));
        if (CollUtil.isEmpty(dto.getTitles())) {
            return true;
        }
        for (String title : dto.getTitles()) {
            CaseUpdateFieldRecordsEntity caseUpdateFieldRecordsEntity = new CaseUpdateFieldRecordsEntity()
                    .setApprover(loginUser.getName())
                    .setCaseInfoId(dto.getCaseInfoId())
                    .setTitle(title);
            caseUpdateFieldRecordsMapper.insert(caseUpdateFieldRecordsEntity);
        }
        return true;
    }

    @Override
    public List<CaseUpdateFieldRecordsEntity> getOutsourcingMaintenanceRecord(Integer caseInfoId) {
        return caseUpdateFieldRecordsMapper.selectList(new LambdaQueryWrapper<CaseUpdateFieldRecordsEntity>()
                .eq(CaseUpdateFieldRecordsEntity::getCaseInfoId, caseInfoId)
                .eq(CaseUpdateFieldRecordsEntity::getDeleteFlag, 0)
                .orderByDesc(CaseUpdateFieldRecordsEntity::getCreateTime));
    }
    @Override
    public void testbatchtestDingTaskApprove() {
        //获取节点信息
    List<DingTalkProcessConfigEntity> dingTalkProcessConfigEntities=    dingTalkProcessConfigEntityMapper.selectList(new LambdaQueryWrapper<DingTalkProcessConfigEntity>()
                .eq(DingTalkProcessConfigEntity::getDeleteFlag, 0));
        List<CaseInfoEntity> dingTaskApproveVOList = caseInfoEntityMapper.selectList(new LambdaQueryWrapper<CaseInfoEntity>()
                .in(CaseInfoEntity::getCurrentNode, Arrays.asList(ASSET_VICE_PRESIDENT_APPROVED, REGION_GENERAL_APPROVED, PRESIDENT_APPROVED, ASSET_DISPOSAL_APPROVED))
                .isNotNull(CaseInfoEntity::getProcessId)
                .eq(CaseInfoEntity::getDeleteFlag, 0)
        );
        Map<String, List<DingTalkProcessConfigEntity>> groupedByActivityId = dingTalkProcessConfigEntities.stream()
                .filter(entity -> entity.getActivityId() != null) // 过滤掉 activityId 为 null 的记录
                .collect(Collectors.groupingBy(DingTalkProcessConfigEntity::getActivityId));
        List<String> dingTaskProcessIdList = dingTaskApproveVOList.stream().map(CaseInfoEntity::getProcessId).filter(StrUtil::isNotBlank).toList();
        Map<String, List<DingTaskApproveVO>> processDetailMap = dingTaskFeign.queryDetailByProcessIdList(dingTaskProcessIdList).getData();
    //获取钉钉节点的委外订单
        dingTaskApproveVOList.forEach(caseInfoEntity -> {
            String processId = caseInfoEntity.getProcessId();
            List<DingTaskApproveVO> dingTaskApproveVOListByProcessId = processDetailMap.get(processId);
            if(CollUtil.isNotEmpty(dingTaskApproveVOListByProcessId)){
                dingTaskApproveVOListByProcessId.forEach(dingTaskApproveVO -> {
                    String userNumberApprove = dingTaskApproveVO.getUserId();
                    Integer userId = null;
                    List<Integer> roleIdList = null;

                    if (StrUtil.isNotBlank(userNumberApprove)) {
                        List<UserSyncInfoListVO> syncInfoListVOList = userFeign.queryUserSyncInfoList(new SearchUserSyncInfoDTO().setUserNumberList(List.of(userNumberApprove))).getData();
                        if (CollUtil.isNotEmpty(syncInfoListVOList)) {
                            UserSyncInfoListVO userSyncInfoListVO = syncInfoListVOList.get(0);
                            userId = userSyncInfoListVO.getLhUserId();
                        }
                    }


                    PayApplicationEventEnums conclusion = null;
                    if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())) {
                        conclusion = PayApplicationEventEnums.APPROVE_PASS;
                    }
                    if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())) {
                        conclusion = PayApplicationEventEnums.APPROVE_REJECT;

                    } else if (ObjUtil.equal(dingTaskApproveVO.getResult(), "NONE")) {
                        return;
                    }
                    List<DingTalkProcessConfigEntity> dingTalkProcessConfigEntities1= groupedByActivityId.get(dingTaskApproveVO.getActivityId());
                    DingTalkProcessConfigEntity dingTalkProcessConfigEntity=    new DingTalkProcessConfigEntity();
                    if(CollUtil.isNotEmpty(dingTalkProcessConfigEntities1)){
                        dingTalkProcessConfigEntity=dingTalkProcessConfigEntities1.get(0);
                    }
                    if(ObjUtil.isNotEmpty(dingTalkProcessConfigEntity)){
                        if(ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())){
                            caseInfoEntity.setDingdingCurrentNode(dingTalkProcessConfigEntity.getNextActivityValue());
                        }else if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())){
                            caseInfoEntity.setDingdingCurrentNode(29);
                        }

                        caseInfoEntityMapper.updateById(caseInfoEntity);
                        adddingdingCaseInfoApproveRecord(caseInfoEntity.getId(), dingTaskApproveVO.getRemark(),    dingTalkProcessConfigEntity.getActivityValue(), caseInfoEntity.getDingdingCurrentNode(), conclusion, userId, caseInfoEntity.getProcessId(), PayApplicationAuditTypeEnum.DINGTALK);

                    }
                });
            }

        });

    }


    public void adddingdingCaseInfoApproveRecord(Integer requestId, String remark, Integer currentNode, Integer nextNode, PayApplicationEventEnums event, Integer userId, String processId, PayApplicationAuditTypeEnum auditType) {
        CaseInfoApproveEntity caseInfoApproveEntity = new CaseInfoApproveEntity();
        caseInfoApproveEntity.setCaseInfoStatus(nextNode);
        caseInfoApproveEntity.setDingdingcurrentNode(currentNode);
        caseInfoApproveEntity.setDingdingnextNode(nextNode);
        caseInfoApproveEntity.setCaseInfoRequestId(requestId);
        caseInfoApproveEntity.setApproveStatus(event.getCode());
        caseInfoApproveEntity.setRemark(remark);
        caseInfoApproveEntity.setApproveUserId(userId);
        caseInfoApproveEntity.setApproveType(1);
        caseInfoApproveEntity.setProcessId(processId);
        caseInfoApproveEntity.setAuditType(auditType);


        caseInfoApproveEntityMapper.insert(caseInfoApproveEntity);
    }

}

