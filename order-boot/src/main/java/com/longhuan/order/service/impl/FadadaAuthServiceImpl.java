package com.longhuan.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.*;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasc.open.api.bean.base.BaseRes;
import com.fasc.open.api.bean.common.*;
import com.fasc.open.api.exception.ApiException;
import com.fasc.open.api.v5_1.client.*;
import com.fasc.open.api.v5_1.req.app.GetAppOpenIdListReq;
import com.fasc.open.api.v5_1.req.corp.GetCorpAuthResourceUrlReq;
import com.fasc.open.api.v5_1.req.corp.GetCorpReq;
import com.fasc.open.api.v5_1.req.doc.FddFileUrl;
import com.fasc.open.api.v5_1.req.doc.FileProcessReq;
import com.fasc.open.api.v5_1.req.doc.GetUploadUrlReq;
import com.fasc.open.api.v5_1.req.seal.GetPersonalSealFreeSignUrlReq;
import com.fasc.open.api.v5_1.req.seal.GetPersonalSealListReq;
import com.fasc.open.api.v5_1.req.seal.GetSealListReq;
import com.fasc.open.api.v5_1.req.signtask.*;
import com.fasc.open.api.v5_1.req.user.*;
import com.fasc.open.api.v5_1.res.app.GetAppOpenIdListRes;
import com.fasc.open.api.v5_1.res.common.ECorpAuthUrlRes;
import com.fasc.open.api.v5_1.res.common.EUrlRes;
import com.fasc.open.api.v5_1.res.corp.CorpRes;
import com.fasc.open.api.v5_1.res.corp.GetIdentifiedStatusRes;
import com.fasc.open.api.v5_1.res.doc.FileId;
import com.fasc.open.api.v5_1.res.doc.FileProcessRes;
import com.fasc.open.api.v5_1.res.doc.GetUploadUrlRes;
import com.fasc.open.api.v5_1.res.seal.*;
import com.fasc.open.api.v5_1.res.signtask.*;
import com.fasc.open.api.v5_1.res.user.UserIdentityInfoRes;
import com.fasc.open.api.v5_1.res.user.UserRes;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.approve.api.pojo.vo.YingFengInfoVO;
import com.longhuan.common.core.base.RequestResponseInfoEntity;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.RequestResponseCode;
import com.longhuan.common.core.enums.ProductRongDanEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.core.result.ResultCode;
import com.longhuan.common.core.util.QrCodeUtils;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.order.config.FadadaConfig;
import com.longhuan.order.enums.*;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.feign.DigitalizeFeign;
import com.longhuan.order.feign.FadadaFeign;
import com.longhuan.order.feign.ResourceFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.pojo.vo.fdd.FreeLoginVO;
import com.longhuan.order.service.*;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.order.util.ContractCodeUtils;
import com.longhuan.order.util.FadadaUtils;
import com.longhuan.order.util.HmacSHA256Utils;
import com.longhuan.resource.pojo.dto.FundResourceDTO;
import com.longhuan.resource.pojo.dto.SignZipDTO;
import com.longhuan.resource.pojo.dto.ThirdResourceDTO;
import com.longhuan.resource.pojo.vo.DownZipFileVO;
import com.longhuan.resource.pojo.vo.FileResourceResultVO;
import com.longhuan.resource.pojo.vo.FileVO;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class FadadaAuthServiceImpl implements FadadaAuthService {
    private final FadadaFeign fadadaFeign;
    private final PreFddSignTaskMapper preFddSignTaskMapper;
    private final FileResourceMapper fileResourceMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final PreFddFinishFileMapper preFddFinishFileMapper;

    private final RiskService riskService;
    private final FadadaConfig fadadaConfig;
    private final FileToFddMapper fileToFddMapper;
    private final FileToFddService fileToFddService;
    private final FddFieldConfigMapper fddFieldConfigMapper;
    private final PreApprovalFddAuthMapper preApprovalFddAuthMapper;
    private final PreApprovalApplyInfoMapper preApprovalApplyInfoMapper;
    private final PreFddFinishFileService preFddFinishFileService;
    private final ResourceFeign resourceFeign;
    private final RequestResponseInfoMapper requestResponseInfoMapper;
    private final FundInfoMapper fundInfoMapper;
    private final FddCorpConfigMapper fddCorpConfigMapper;
    private final SignTaskMapper busiSignTaskMapper;
    private final PreFddFinishFileMapper fddFinishFileMapper;
    private final FileTemplateInfoMapper fileTemplateInfoMapper;
    private final OrderContractMapper orderContractMapper;
    private final DigitalizeFeign digitalizeFeign;
    private final OrderContractService orderContractService;
    private final ContractToFundService contractToFundService;
    private final PreOcrIdentityCardMapper preOcrIdentityCardMapper;
    private final Tracer tracer;
    private final EnvUtil envUtil;
    private final PreAuthorizationMapper preAuthorizationMapper;
    private final SignTaskMapper signTaskMapper;
    private final ContractToFundMapper contractToFundMapper;
    private final SwitchUtils switchUtils;
    private final RedisService redisService;
    private final static String FDD_TOKEN_CACHE_KEY = "order:fdd:token";
    private final OrderCustomerInfoMapper orderCustomerInfoMapper;
    private final FundSignInfoMapper fundSignInfoMapper;
    @Value("${app.hostname}")
    private String hostname;
    @Value("${digitalize.yingFengTempId}")
    private Integer yingFengTempId;
    @Value("${digitalize.HmacSHA256Key}")
    private String digitalizeKey;
    private final FileConfigMapper fileConfigMapper;
    private final OrderFileMapper orderFileMapper;
    private final ApproveFeign approveFeign;
    private final OrderSendMessageImpl orderSendMessageImpl;
    private final ArrivedDataMapper arrivedDataMapper;
    private final OrderArrivedMapper orderArrivedMapper;
    private final ProductRongdanMapper productRongdanMapper;
    private final PreNodeRecordMapper preNodeRecordMapper;

    private static @NotNull Actor getActor(ActorsInfoDTO actorsInfoDTO) {
        Actor actor = new Actor();
        actor.setActorId(actorsInfoDTO.getActorId());
        actor.setActorType("person");
        actor.setActorName(actorsInfoDTO.getActorName());
        actor.setClientUserId(actorsInfoDTO.getActorId());
        actor.setAccountName(actorsInfoDTO.getAccountName());
        actor.setPermissions(Arrays.asList("sign"));
        actor.setSendNotification(false);
//        actor.setAuthScopes(Arrays.asList("signtask_init", "signtask_info", "signtask_file"));
        actor.setAuthScopes(FadadaEnum.getAllAuthScope());
        return actor;
    }

    private static @NotNull String getDocName(FddFileResourceVO vo) {
        String number = vo.getNumber();

        String batchNO;
        if (number != null && number.split("_").length > 1) {
            batchNO = number.split("_")[2];
        } else {
            batchNO = DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss");
        }

        String fileName = vo.getFileName().split("\\.")[0];

        String docName = batchNO + "_" + fileName;
        return docName;
    }

    @Override
    @Transactional
    public UserAuthUrlVO getFddUserAuthUrl(UserAuthReqDTO userAuthReqDTO) {
        Integer preId = userAuthReqDTO.getPreId();

        List<PreApprovalApplyInfoEntity> applyInfoEntityList = preApprovalApplyInfoMapper.selectList(
                new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
                        .eq(PreApprovalApplyInfoEntity::getId, preId)
                        .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
        );

        if (CollUtil.isEmpty(applyInfoEntityList)) {
            throw new BusinessException("预审信息不存在");
        }
        PreApprovalApplyInfoEntity applyInfoEntity = applyInfoEntityList.get(0);
        //判断是否已授权或签署文档
        UserAuthUrlVO judgeVO = judgeAuth(userAuthReqDTO, applyInfoEntity);
        if (ObjUtil.isNotEmpty(judgeVO)) {
            log.info("getFddUserAuthUrl preId {} user has auth record", preId);
            return judgeVO;
        }
        String phone = applyInfoEntity.getPhone();
        String idNumber = applyInfoEntity.getIdNumber();

        BaseRes<EUrlRes> eUrlResBaseRes;
        String clientUserId = FadadaUtils.generateUserId();
        try {


            //获取token
            String accessToken = getFddToken();

            OpenApiClient apiClient = fadadaConfig.appClient();

            UserClient userClient = new UserClient(apiClient);
            GetUserAuthUrlReq fadadaAuth = new GetUserAuthUrlReq();
            fadadaAuth.setAccessToken(accessToken);
            //重定向地址
            fadadaAuth.setRedirectUrl(URLEncoder.encode(hostname + "/longhuan/h5/applicationPage?preId=" + preId, "UTF-8"));
            log.info("个人授权链接重定向地址：paramMap = {}", fadadaAuth.getRedirectUrl());
            //用户唯一标识
            fadadaAuth.setClientUserId(clientUserId);

            fadadaAuth.setAccountName(phone);
            fadadaAuth.setAuthScopes(FadadaEnum.getAllAuthScope());

            UserIdentInfoReq userIdentInfoReq = new UserIdentInfoReq();
            log.info("getFddUserAuthUrl env ={}", envUtil.isPrd());
//            if (envUtil.isPrd()) {
            //用户实名认证方式，默认人脸, 测试环境可选
            userIdentInfoReq.setIdentMethod(List.of("mobile", "face"));
//            }
            userIdentInfoReq.setUserName(applyInfoEntity.getName());
            userIdentInfoReq.setUserIdentType("id_card");
            userIdentInfoReq.setUserIdentNo(idNumber);
            userIdentInfoReq.setMobile(phone);
            fadadaAuth.setUserIdentInfo(userIdentInfoReq);
            //页面中不可编辑的个人信息
            fadadaAuth.setNonEditableInfo(Arrays.asList("accountName", "userName", "userIdentNo", "mobile", "userIdentType"));

            log.info("个人授权链接：fadadaAuth = {}", JSONUtil.toJsonStr(fadadaAuth));

            eUrlResBaseRes = userClient.getUserAuthUrl(fadadaAuth);
            saveRequestLog(preId, RequestResponseCode.FDD_AUTH_URL, "", "", JSONUtil.toJsonStr(fadadaAuth), JSONUtil.toJsonStr(eUrlResBaseRes));
            if (!FadadaEnum.SUCCESS.getCode().equals(eUrlResBaseRes.getCode())) {
                throw new BusinessException(eUrlResBaseRes.getMsg());
            }
            log.info("eUrlResBaseRes = {}", JSONUtil.toJsonStr(eUrlResBaseRes));
            //保存数据
            initFadadaAuth(fadadaAuth, preId, idNumber, phone);

        } catch (Exception e) {
            throw new BusinessException(e);
        }

        return new UserAuthUrlVO().setAuthShortUrl(eUrlResBaseRes.getData().getAuthShortUrl()).setClientUserId(clientUserId);
    }

    /**
     * 判断法大大授权状态和签署任务状态
     *
     * @param userAuthReqDTO  user auth req dto
     * @param applyInfoEntity Apply info 实体
     * @return {@link UserAuthUrlVO }
     */
    private UserAuthUrlVO judgeAuth(UserAuthReqDTO userAuthReqDTO, PreApprovalApplyInfoEntity applyInfoEntity) {
        log.info("FadadaAuthServiceImpl.judgeAuth start");
        //-----------edit by zangxx at 2023/9/7  确认场景：不再校验用户授权书签署场景，可重复签署，但是同一单不可重复签署--------
        List<PreFddSignTaskEntity> signTaskEntityList = preFddSignTaskMapper.selectList(new LambdaQueryWrapper<PreFddSignTaskEntity>()
                        .eq(PreFddSignTaskEntity::getPreId, userAuthReqDTO.getPreId())
                        .eq(PreFddSignTaskEntity::getDeleteFlag, 0)
                //                .eq(PreFddSignTaskEntity::getSignTaskStatus, "task_finished")
        );
        if (CollUtil.isNotEmpty(signTaskEntityList)) {
            for (PreFddSignTaskEntity signTaskEntity : signTaskEntityList) {
                //首次进件时场景，增加非空判断
                if (StrUtil.isNotEmpty(signTaskEntity.getSignTaskStatus())) {
                    if (signTaskEntity.getSignTaskStatus().equals("task_finished")) {
                        throw new BusinessException("该预审用户已签署征信授权书");
                    }
                }

            }
        }
        log.info("judgeAuth preId ={}, preInfoList = {}", userAuthReqDTO.getPreId(), JSONUtil.toJsonStr(signTaskEntityList.size()));
        //判断是否同一身份证号、身份证号
        PreApprovalFddAuthEntity preApprovalFddAuthEntity =
                preApprovalFddAuthMapper.selectOne(new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                        //                .eq(PreApprovalFddAuthEntity::getPreId, userAuthReqDTO.getPreId())
                        //客户重新扫码填写信息，身份证号相同不再经过授权
                        .eq(PreApprovalFddAuthEntity::getIdNumber, applyInfoEntity.getIdNumber())
                        .eq(PreApprovalFddAuthEntity::getPhone, applyInfoEntity.getPhone())
                        .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                        .orderByDesc(PreApprovalFddAuthEntity::getId)
                        .last("limit 1")
                );
        log.info("judgeAuth preApprovalFddAuthEntity = {}", JSONUtil.toJsonStr(preApprovalFddAuthEntity));

        if (preApprovalFddAuthEntity == null) {
            log.info("judgeAuth preApprovalFddAuthEntity = null");
            return null;
        }

        //  存在授权完成记录 直接返回
        if (Objects.equals("success", preApprovalFddAuthEntity.getAuthResult())) {
            return new UserAuthUrlVO().setClientUserId(preApprovalFddAuthEntity.getClientUserId()).setRespMsg("该用户已授权").setBeforeAuth(true);
        }

        // 判断是否同一预审单信息再次进入
        UserRes userResBaseRes = getUserAuthStatus(preApprovalFddAuthEntity.getClientUserId());
        if (ObjUtil.isNotNull(userResBaseRes) && FadadaEnum.AUTHORIZED.getCode().equals(userResBaseRes.getBindingStatus())) {
            return new UserAuthUrlVO().setClientUserId(userResBaseRes.getClientUserId()).setRespMsg("该用户已授权").setBeforeAuth(true);
        }
        return null;
    }


    private void initFadadaAuth(GetUserAuthUrlReq authUrlReq, Integer preId, String idNumber, String phone) {
        List<PreApprovalFddAuthEntity> preApprovalFddAuthEntityList = preApprovalFddAuthMapper.selectList(new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                        .eq(PreApprovalFddAuthEntity::getIdNumber, idNumber)
                        .eq(PreApprovalFddAuthEntity::getPhone, phone)
                        .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                        .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                //                .eq(PreApprovalFddAuthEntity::getPreId, preId)

        );
        if (CollUtil.isEmpty(preApprovalFddAuthEntityList)) {
            log.info("新增授权记录");
            preApprovalFddAuthMapper.insert(new PreApprovalFddAuthEntity()
                    .setClientUserId(authUrlReq.getClientUserId())
                    .setIdNumber(idNumber)
                    .setPreId(preId)
                    .setPhone(phone)
            );
        } else {
            log.info("更新授权记录");
            preApprovalFddAuthMapper.update(new LambdaUpdateWrapper<PreApprovalFddAuthEntity>()
                    .set(PreApprovalFddAuthEntity::getClientUserId, authUrlReq.getClientUserId())
                    .eq(PreApprovalFddAuthEntity::getPreId, preId));
        }
    }

    @Override
    public boolean uploadFile(List<FileTOFddVO> voList) {
        log.debug("uploadFile voList size:{}", voList.size());

        voList.forEach(vo -> {

            String resourceId = vo.getFileUid();

            if (vo.getFileBytes() == null || vo.getFileBytes().length == 0) {
                log.error("uploadFile resourceId:{} not found ", resourceId);
                throw new BusinessException("文件不存在");
            }

            //根据uploadUrl上传用于签署的本地文档
            GetUploadUrlRes data = getGetUploadUrlRes();

            String uploadUrl = data.getUploadUrl();
            String fddFileUrl = data.getFddFileUrl();

            log.info("uploadFile resourceId:{} uploadUrl:{}", resourceId, uploadUrl);
            OrderContractEntity contractEntity = null;

            if (Objects.equals(vo.getFileBelong(), 999)) {
                contractEntity = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                        .eq(OrderContractEntity::getResource, resourceId)
                        .eq(OrderContractEntity::getDeleteFlag, 0)
                );
            }

            FileToFddEntity fileToFddEntity = new FileToFddEntity().setFddFileUrl(fddFileUrl)
                    .setFddUploadUrl(uploadUrl)
                    .setResourceFileUid(resourceId)
                    .setFileBelong(vo.getFileBelong())
                    .setFilePurpose(Objects.equals(vo.getFileBelong(), 999) ? FadadaEnum.FILE_CONTRACT.getCode() : FadadaEnum.FILE_PURPOSE.getCode())
                    .setTemplateId(ObjUtil.isNotEmpty(contractEntity) ? Objects.requireNonNull(contractEntity).getTemplateId() : 0);

            //拿到法大大文件上传路径进行文件上传
            String s = uploadFileToFddWithUploadUrl2(vo.getFileBytes(), uploadUrl);

            fileToFddService.save(fileToFddEntity);

        });

        return false;
    }

    public String uploadFileToFddWithUploadUrl2(byte[] fileBytes, String uploadUrl) {
        try (BufferedInputStream bis = new BufferedInputStream(new ByteArrayInputStream(fileBytes))) {
            URL upload = new URL(uploadUrl);
            HttpURLConnection connection = (HttpURLConnection) upload.openConnection();
            connection.setRequestMethod("PUT");
            connection.setRequestProperty("Content-Type", "application/octet-stream");
            connection.setDoOutput(true);
            BufferedOutputStream bos = new BufferedOutputStream(connection.getOutputStream());
            int readByte = -1;
            while ((readByte = bis.read()) != -1) {
                bos.write(readByte);
            }
            bos.close();
            int responseCode = connection.getResponseCode();
            String message = connection.getResponseMessage();
            log.info("uploadFileToFddWithUploadUrl2 responseCode={},msg={}", responseCode, message);
            return message;
        } catch (Exception e) {
            throw new BusinessException(e);
        }
    }

    /**
     * 使用上传 URL 将文件上传到 FDD
     *
     * @param fileBytes fileBytes
     * @param uploadUrl 上传 URL
     */
    @Override
    public String uploadFileToFddWithUploadUrl(byte[] fileBytes, String uploadUrl) {
        for (int i = 0; i < 3; i++) {
            try {
                log.info("uploadFileToFddWithUploadUrl retry:{}", i);
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

                HttpEntity<byte[]> requestEntity = new HttpEntity<>(fileBytes, headers);
                RestTemplate restTemplate = new RestTemplate();
                ResponseEntity<String> response = restTemplate.exchange(uploadUrl, HttpMethod.PUT, requestEntity, String.class);

                HttpStatusCode statusCode = response.getStatusCode();
                String body = response.getBody();
                if (statusCode.is2xxSuccessful()) {
                    log.info("uploadFileToFddWithUploadUrl uploadFile success {}", body);
                    return body;
                } else {
                    log.error("uploadFileToFddWithUploadUrl uploadFile fail {}:{}", statusCode, body);
                }
            } catch (Exception e) {
                log.error("uploadFileToFddWithUploadUrl error msg:{}", e.getMessage(), e);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ex) {
                    log.error("uploadFileToFddWithUploadUrl sleep error msg:{}", e.getMessage(), e);
                }
            }
        }
        throw new BusinessException("上传文件到法大大失败");
    }

    /**
     * 创建预审授权书签署任务
     *
     * @param signTaskDTO 对任务 DTO 进行签名
     * @return {@link BaseRes }<{@link CreateSignTaskRes }>
     */
    @Override
    public BaseRes<CreateSignTaskRes> createSignTask(SignTaskDTO signTaskDTO) {
        log.info("FadadaAuthServiceImpl.createSignTask start");

        Assert.notNull(signTaskDTO.getPreId(), () -> {
            throw new BusinessException("预审批id不能为空");
        });

        // 0. 检查是否存在未完成的预审授权书签署任务
        Integer preId = signTaskDTO.getPreId();
        List<PreFddSignTaskEntity> preFddSignTaskEntities = preFddSignTaskMapper.selectList(new LambdaQueryWrapper<PreFddSignTaskEntity>()
                .eq(PreFddSignTaskEntity::getPreId, preId)
                .eq(PreFddSignTaskEntity::getDeleteFlag, 0)
                .ne(PreFddSignTaskEntity::getSignTaskStatus, SignTaskStatusEnums.TASK_FINISHED.getCode())
                .ge(PreFddSignTaskEntity::getCreateTime, LocalDateTime.now().minusDays(1))
                .orderByDesc(PreFddSignTaskEntity::getCreateTime)
        );

        if (CollUtil.isNotEmpty(preFddSignTaskEntities)) {
            PreFddSignTaskEntity preFddSignTaskEntity = preFddSignTaskEntities.get(0);
            String signTaskId = preFddSignTaskEntity.getSignTaskId();
            if (StrUtil.isNotEmpty(signTaskId)) {
                log.info("FadadaAuthServiceImpl.createSignTask preFddSignTaskEntity.getSignTaskId()={}", signTaskId);
                return buildCreateSignTaskResBaseRes(signTaskId);
            }
        }
        // 1. 创建授权书签署任务

        // 1.1 查询要签署的文件
        List<FddFileResourceVO> resourceVOList = fileToFddMapper.selectJoinList(FddFileResourceVO.class, new MPJLambdaWrapper<FileToFddEntity>()
                .select(FileToFddEntity::getResourceFileUid, FileToFddEntity::getFddFileId)
                .selectAs(FileResourceEntity::getFileOldName, FddFileResourceVO::getFileName)
                .innerJoin(FileResourceEntity.class, FileResourceEntity::getFileUid, FileToFddEntity::getResourceFileUid)
                .eq(FileToFddEntity::getDeleteFlag, 0)
                .ne(FileToFddEntity::getFddFileId, "")
                .in(FileToFddEntity::getFilePurpose, FadadaEnum.FILE_PURPOSE.getCode())
                .orderByAsc(FileToFddEntity::getId)
        );
        log.info("FadadaAuthServiceImpl.createSignTask resourceVOList.size() = {}", resourceVOList.size());

        BaseRes<CreateSignTaskRes> signTaskRes;
        try {


            //获取token
            String accessToken = getFddToken();
            OpenApiClient apiClient = fadadaConfig.appClient();
            SignTaskClient signTaskClient = new SignTaskClient(apiClient);
            CreateSignTaskReq signTaskReq = new CreateSignTaskReq();
            signTaskReq.setAccessToken(accessToken);

            OpenId openId = new OpenId();
            openId.setIdType("corp");
            openId.setOpenId(fadadaConfig.getOppenCorpId());
            //扣费主体
            signTaskReq.setInitiator(openId);
            //签署任务主题
            signTaskReq.setSignTaskSubject("签署授权文件");
            //免验签场景码
            signTaskReq.setBusinessId(fadadaConfig.getBusinessId());
            //文件信息
            List<AddDocInfo> docs = resourceVOList.stream().map(vo -> {
                AddDocInfo docInfo = new AddDocInfo();
                docInfo.setDocId(vo.getResourceFileUid());
                docInfo.setDocName(vo.getFileName().split("\\.")[0]);
                docInfo.setDocFileId(vo.getFddFileId());
                //文档中添加控价
                docInfo.setDocFields(getDocFieldList(vo, 2));
                return docInfo;
            }).toList();

            signTaskReq.setDocs(docs);

            List<AddActorsInfo> actors = new ArrayList<>();

            List<Integer> corpIdList = fddFieldConfigMapper.selectList(new LambdaQueryWrapper<FddFieldConfigEntity>()
                            .eq(FddFieldConfigEntity::getDeleteFlag, 0)
                            .in(FddFieldConfigEntity::getTemplateId, 10000))
                    .stream()
                    .map(FddFieldConfigEntity::getSealCorpId)
                    .toList();
            if (CollUtil.isNotEmpty(corpIdList)) {
                List<FddCorpConfigEntity> corpConfigList = fddCorpConfigMapper.selectList(new LambdaQueryWrapper<FddCorpConfigEntity>()
                        .eq(FddCorpConfigEntity::getDeleteFlag, 0)
                        .in(FddCorpConfigEntity::getId, corpIdList));
                //企业参与方
                List<AddActorsInfo> corp = handleCorpActors(docs, corpConfigList);
                actors.addAll(corp);
            }

            AddActorsInfo addActorsInfo = new AddActorsInfo();
            ActorsInfoDTO actorsInfoDTO = getActorsInfoDTO(preId, addActorsInfo, docs);

            //参与方信息-预审批提交人
            List<AddActorsInfo> actorsList = new ArrayList<>();
            actorsList.add(addActorsInfo);
            actors.addAll(actorsList);

            signTaskReq.setActors(actors);
            log.info("FadadaAuthServiceImpl.createSignTask preId ={}，signTaskReq ={}", preId, JSONUtil.toJsonStr(signTaskReq));

            signTaskRes = signTaskClient.create(signTaskReq);

            log.info("signTaskRes ={}", JSONUtil.toJsonStr(signTaskRes));

            //保存报文信息
            saveRequestLog(preId, RequestResponseCode.FDD_CREATE_SIGN_TASK, "", "", JSONUtil.toJsonStr(signTaskReq), JSONUtil.toJsonStr(signTaskRes));


            if (!FadadaEnum.SUCCESS.getCode().equals(signTaskRes.getCode())) {
                if (Objects.equals(signTaskRes.getCode(), "100011")) {
                    signTaskRes.setMsg("授权信息冲突，请先解绑");
                    return signTaskRes;
                }
                throw new BusinessException(signTaskRes.getMsg());
            }


            String signTaskId = signTaskRes.getData().getSignTaskId();
            actorsInfoDTO.setSignTaskId(signTaskId);
            actorsInfoDTO.setPreId(preId);
//            CompletableFuture.supplyAsync(() -> {
            //填写控件内容
            BaseRes<Void> fillInputFieldRes = addInputField(actorsInfoDTO, signTaskClient, docs);
            log.info("FadadaAuthServiceImpl.createSignTask fillInputFieldRes = {}",
                    JSONUtil.toJsonStr(fillInputFieldRes));

            if (!FadadaEnum.SUCCESS.getCode().equals(fillInputFieldRes.getCode())) {
                throw new BusinessException(fillInputFieldRes.getMsg());
            }
            //提交签署任务
            BaseRes<Void> submitSignTaskRes = submitSignTask(signTaskId, signTaskClient,
                    preId);
            log.info("FadadaAuthServiceImpl.createSignTask submitSignTaskRes = {}",
                    JSONUtil.toJsonStr(submitSignTaskRes));
            if (!FadadaEnum.SUCCESS.getCode().equals(submitSignTaskRes.getCode())) {
                throw new BusinessException(submitSignTaskRes.getMsg());
            }
            //更新签署任务数据
            addPreSignTask(signTaskDTO, signTaskId);
//                return "ok";
//            }).thenAccept(processResult -> log.info("更新签署任务数据: {}", processResult));


        } catch (ApiException e) {
            throw new BusinessException(e);
        }
        return signTaskRes;
    }

    private static @NotNull BaseRes<CreateSignTaskRes> buildCreateSignTaskResBaseRes(String signTaskId) {
        BaseRes<CreateSignTaskRes> resBaseRes = new BaseRes<>();
        CreateSignTaskRes createSignTaskRes = new CreateSignTaskRes();
        createSignTaskRes.setSignTaskId(signTaskId);
        resBaseRes.setData(createSignTaskRes);
        resBaseRes.setCode("100000");
        resBaseRes.setMsg("请求成功");
        resBaseRes.setHttpStatusCode(200);
        return resBaseRes;
    }

    private @NotNull ActorsInfoDTO getActorsInfoDTO(Integer preId, AddActorsInfo addActorsInfo, List<AddDocInfo> docs) {
        List<ActorsInfoDTO> actorsInfoDTOList = preApprovalFddAuthMapper.selectJoinList(ActorsInfoDTO.class,
                new MPJLambdaWrapper<PreApprovalFddAuthEntity>()
                        .selectAs(PreApprovalApplyInfoEntity::getName, ActorsInfoDTO::getActorName)
                        .selectAs(PreApprovalApplyInfoEntity::getPhone, ActorsInfoDTO::getAccountName)
                        .selectAs(PreApprovalFddAuthEntity::getClientUserId, ActorsInfoDTO::getActorId)
                        .selectAs(PreApprovalApplyInfoEntity::getIdNumber, ActorsInfoDTO::getIdNumber)
                        .selectAs(PreApprovalApplyInfoEntity::getAddress, ActorsInfoDTO::getAddress)
                        .innerJoin(PreApprovalApplyInfoEntity.class, on -> on
                                .eq(PreApprovalFddAuthEntity::getIdNumber, PreApprovalApplyInfoEntity::getIdNumber)
                                .eq(PreApprovalFddAuthEntity::getPhone, PreApprovalApplyInfoEntity::getPhone)
                        )
                        .eq(PreApprovalApplyInfoEntity::getId, preId)
//                        .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                        .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                        .orderByDesc(PreApprovalApplyInfoEntity::getId)
        );


        if (CollUtil.isEmpty(actorsInfoDTOList)) {
            throw new BusinessException("未查到预审信息");
        }
        ActorsInfoDTO actorsInfoDTO = actorsInfoDTOList.get(0);
        Actor actor = new Actor();
        actor.setActorId(actorsInfoDTO.getActorId());
        actor.setActorType("person");
        actor.setActorName(actorsInfoDTO.getActorName());
        actor.setClientUserId(actorsInfoDTO.getActorId());
        actor.setAccountName(actorsInfoDTO.getAccountName());
        actor.setPermissions(Arrays.asList("sign"));
        actor.setSendNotification(false);
        actor.setAuthScopes(FadadaEnum.getAllAuthScope());
        //配置快捷签个人参与方默认信息
        actor.setIdentNameForMatch(actorsInfoDTO.getActorName());
        actor.setCertNoForMatch(actorsInfoDTO.getIdNumber());
        actor.setCertType("id_card");//默认生身份证
        addActorsInfo.setActor(actor);
        log.info("FadadaAuthServiceImpl.createSignTask preId ={} addActorsInfo = {}", preId, JSONUtil.toJsonStr(addActorsInfo));


        //设置参与方的签章控件
        List<AddSignFieldInfo> signFields = new ArrayList<>();
        docs.forEach(doc -> {
            doc.getDocFields().forEach(field -> {
                AddSignFieldInfo signFieldInfo = new AddSignFieldInfo();
                if (!field.getFieldType().equals("id_card") && !("text_single_line").equals(field.getFieldType()) && !("fill_date").equals(field.getFieldType()) && !("corp_seal").equals(field.getFieldType())) {
                    signFieldInfo.setFieldDocId(doc.getDocId());
                    signFieldInfo.setFieldId(field.getFieldId());
                    signFields.add(signFieldInfo);
                }

            });

        });
        addActorsInfo.setSignFields(signFields);
        //签署配置信息
        AddSignConfigInfo addSignConfigInfo = new AddSignConfigInfo();
        addSignConfigInfo.setFreeLogin(true);
        addSignConfigInfo.setIdentifiedView(false);
        //标准签名
        addSignConfigInfo.setSignerSignMethod("standard");
        //强制阅读，单位秒
        addSignConfigInfo.setReadingTime("5");
        log.info("createSignTask env ={}", envUtil.isPrd());
        if (envUtil.isPrd()) {
            //face: 刷脸验证,sms: 短信验证
            addSignConfigInfo.setVerifyMethods(List.of("face"));
        }
        //个人快捷签场景配置
        addSignConfigInfo.setFreeLogin(true);
        addSignConfigInfo.setIdentifiedView(false);
        addActorsInfo.setSignConfigInfo(addSignConfigInfo);
        return actorsInfoDTO;
    }

    private void addPreSignTask(SignTaskDTO signTaskDTO, String taskId) {
        PreFddSignTaskEntity newTaskEntity = new PreFddSignTaskEntity();
        newTaskEntity.setPreId(signTaskDTO.getPreId());
        newTaskEntity.setClientUserId(signTaskDTO.getClientUserId());
        newTaskEntity.setSignTaskId(taskId);
        log.info("FadadaAuthServiceImpl.addPreSignTask newTaskEntity = {}", JSONUtil.toJsonStr(newTaskEntity));
        preFddSignTaskMapper.insert(newTaskEntity);
    }

    private BaseRes<Void> addInputField(ActorsInfoDTO actorsInfoDTO, SignTaskClient signTaskClient, List<AddDocInfo> docs) {
        log.info("-----开始补充填写控件----");
        BaseRes<Void> voidBaseRes;
        try {
            String accessToken = getFddToken();
            FillFieldValuesReq fillFieldValuesReq = new FillFieldValuesReq();
            fillFieldValuesReq.setAccessToken(accessToken);
            fillFieldValuesReq.setSignTaskId(actorsInfoDTO.getSignTaskId());
            List<DocFieldValueInfo> docFieldValueInfoList = new ArrayList<>();
            List<PreAuthorizationEntity> preAuthorizationEntityList = preAuthorizationMapper.selectList(new LambdaQueryWrapper<PreAuthorizationEntity>()
                    .eq(PreAuthorizationEntity::getPreId, actorsInfoDTO.getPreId())
                    .eq(PreAuthorizationEntity::getSignTaskId, actorsInfoDTO.getSignTaskId())
                    .eq(PreAuthorizationEntity::getSignStatus, ContractEnum.SIGNED.getCode())
            );
            log.info("addInputField docs.size() = {}", docs.size());
            docs.forEach(doc -> {
                int addCount;
                if (CollUtil.isEmpty(preAuthorizationEntityList)) {
                    addCount = preAuthorizationMapper.insert(new PreAuthorizationEntity().setPreId(actorsInfoDTO.getPreId())
                            .setName(doc.getDocName()).setSignStatus(ContractEnum.SIGNING.getCode()).setResource(doc.getDocId())
                            .setSignTaskId(actorsInfoDTO.getSignTaskId())
                    );
                } else {
                    addCount = 0;
                }

                doc.getDocFields().forEach(field -> {
                    if (!doc.getDocName().contains(FadadaEnum.getNoFillFiles())) {
                        DocFieldValueInfo docFieldValueInfo = new DocFieldValueInfo();
                        docFieldValueInfo.setDocId(doc.getDocId());
                        if (("id_card").equals(field.getFieldType())) {
                            docFieldValueInfo.setFieldId(field.getFieldId());
                            docFieldValueInfo.setFieldName(field.getFieldName());
                            docFieldValueInfo.setFieldValue(actorsInfoDTO.getIdNumber());
                            docFieldValueInfoList.add(docFieldValueInfo);
                        } else if (("fill_date").equals(field.getFieldType())) {
                            docFieldValueInfo.setFieldId(field.getFieldId());
                            docFieldValueInfo.setFieldName(field.getFieldName());
                            docFieldValueInfo.setFieldValue(LocalDate.now().format(DatePattern.createFormatter("yyyy年MM月dd日")));
                            docFieldValueInfoList.add(docFieldValueInfo);
                        }
                        if (("合同编号").equals(field.getFieldName())) {
                            docFieldValueInfo.setFieldId(field.getFieldId());
                            docFieldValueInfo.setFieldName(field.getFieldName());
                            docFieldValueInfo.setFieldValue(ContractCodeUtils.generateContractCode());
                            docFieldValueInfoList.add(docFieldValueInfo);
                            if (addCount > 0) {
                                preAuthorizationMapper.update(new LambdaUpdateWrapper<PreAuthorizationEntity>()
                                        .eq(PreAuthorizationEntity::getPreId, actorsInfoDTO.getPreId())
                                        .eq(PreAuthorizationEntity::getResource, doc.getDocId())
                                        .eq(PreAuthorizationEntity::getSignTaskId, actorsInfoDTO.getSignTaskId())
                                        .set(PreAuthorizationEntity::getNumber, docFieldValueInfo.getFieldValue()));

                            }
                        }
                        if (("证件类型").equals(field.getFieldName())) {
                            docFieldValueInfo.setFieldId(field.getFieldId());
                            docFieldValueInfo.setFieldName(field.getFieldName());
                            docFieldValueInfo.setFieldValue("身份证");
                            docFieldValueInfoList.add(docFieldValueInfo);
                        } else if (("联系电话").equals(field.getFieldName())) {
                            docFieldValueInfo.setFieldId(field.getFieldId());
                            docFieldValueInfo.setFieldName(field.getFieldName());
                            docFieldValueInfo.setFieldValue(actorsInfoDTO.getAccountName());
                            docFieldValueInfoList.add(docFieldValueInfo);
                        } else if (("联系地址").equals(field.getFieldName())) {
                            docFieldValueInfo.setFieldId(field.getFieldId());
                            docFieldValueInfo.setFieldName(field.getFieldName());
                            docFieldValueInfo.setFieldValue(actorsInfoDTO.getAddress());
                            docFieldValueInfoList.add(docFieldValueInfo);
                        } else if (("姓名").equals(field.getFieldName())) {
                            docFieldValueInfo.setFieldId(field.getFieldId());
                            docFieldValueInfo.setFieldName(field.getFieldName());
                            docFieldValueInfo.setFieldValue(actorsInfoDTO.getActorName());
                            docFieldValueInfoList.add(docFieldValueInfo);
                        }
                    }

                });
            });
            fillFieldValuesReq.setDocFieldValues(docFieldValueInfoList);
            log.info("addInputField fillFieldValuesReq = {}", JSONUtil.toJsonStr(fillFieldValuesReq));
            voidBaseRes = signTaskClient.fillFieldValues(fillFieldValuesReq);
            //保存报文信息
            saveRequestLog(actorsInfoDTO.getPreId(), RequestResponseCode.FDD_CREATE_SIGN_TASK, "", "", JSONUtil.toJsonStr(fillFieldValuesReq), JSONUtil.toJsonStr(voidBaseRes));

        } catch (Exception e) {
            log.error("addInputField error {} ", e.getMessage(), e);
            throw new BusinessException(e);
        }
        return voidBaseRes;
    }

    private BaseRes<Void> submitSignTask(String signTaskId, SignTaskClient signTaskClient, Integer preId) {

        log.info("-----submitSignTask start----");
        BaseRes<Void> submitSignTaskRes;
        try {
            String accessToken = getFddToken();
            SignTaskBaseReq signTaskBaseReq = new SignTaskBaseReq();
            signTaskBaseReq.setAccessToken(accessToken);
            signTaskBaseReq.setSignTaskId(signTaskId);
            submitSignTaskRes = signTaskClient.start(signTaskBaseReq);
            log.info("submitSignTask submitSignTaskRes ={}", JSONUtil.toJsonStr(signTaskBaseReq));
            //保存报文信息
            saveRequestLog(preId, RequestResponseCode.FDD_SUBMIT_SIGN_TASK, "", "", JSONUtil.toJsonStr(signTaskBaseReq), JSONUtil.toJsonStr(submitSignTaskRes));

        } catch (ApiException e) {
            throw new BusinessException(e);
        }
        return submitSignTaskRes;
    }

    @Override
    public BaseRes<SignTaskActorGetUrlRes> getActorSignUrl(ActorSingUrlDTO actorSingUrlDTO) {
        PreApprovalApplyInfoEntity preApprovalApplyInfo = preApprovalApplyInfoMapper.selectById(actorSingUrlDTO.getPreId());
        if (ObjUtil.isEmpty(preApprovalApplyInfo)) {
            throw new BusinessException("未找到预审信息");
        }
        //获取token
        String accessToken = getFddToken();
        OpenApiClient apiClient = fadadaConfig.appClient();
        SignTaskClient signTaskClient = new SignTaskClient(apiClient);
        BaseRes<SignTaskActorGetUrlRes> urlRes;
        try {
            SignTaskActorGetUrlReq signTaskActorGetUrlReq = new SignTaskActorGetUrlReq();
            signTaskActorGetUrlReq.setAccessToken(accessToken);
            signTaskActorGetUrlReq.setSignTaskId(actorSingUrlDTO.getSignTaskId());
            signTaskActorGetUrlReq.setActorId(actorSingUrlDTO.getClientUserId());
            signTaskActorGetUrlReq.setClientUserId(actorSingUrlDTO.getClientUserId());
            signTaskActorGetUrlReq.setRedirectUrl(hostname + "/longhuan/h5/applicationPage?flg=sign&signTaskId=" + actorSingUrlDTO.getSignTaskId() + "&preId=" + actorSingUrlDTO.getPreId());

            urlRes = signTaskClient.signTaskActorGetUrl(signTaskActorGetUrlReq);
            //保存报文信息
            saveRequestLog(actorSingUrlDTO.getPreId(), RequestResponseCode.FDD_ACTOR_SIGN_URL, "", "", JSONUtil.toJsonStr(signTaskActorGetUrlReq), JSONUtil.toJsonStr(urlRes));

            log.info("urlRes ={}", JSONUtil.toJsonStr(urlRes));
        } catch (ApiException e) {
            throw new BusinessException(e);
        }
        return urlRes;
    }

    @Override
    public Boolean userUnbind(Integer preId) {
        log.info("FadadaAuthServiceImpl.userUnbind preId :{}", preId);
        PreApprovalApplyInfoEntity preApprovalApplyInfo = preApprovalApplyInfoMapper.selectOne(new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
                .eq(PreApprovalApplyInfoEntity::getId, preId)
                .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0));
        if (preApprovalApplyInfo == null) {
            throw new BusinessException("预审批申请信息不存在");
        }
        List<PreApprovalFddAuthEntity> preApprovalFddAuthEntityList = preApprovalFddAuthMapper.selectList(new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                .eq(PreApprovalFddAuthEntity::getPhone, preApprovalApplyInfo.getPhone())
                .eq(PreApprovalFddAuthEntity::getIdNumber, preApprovalApplyInfo.getIdNumber())
//                .eq(PreApprovalFddAuthEntity::getPreId, preId)
//                .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                .orderByDesc(PreApprovalFddAuthEntity::getCreateTime));

        log.info("FadadaAuthServiceImpl.userUnbind preApprovalFddAuthEntityList.size() ={}", preApprovalFddAuthEntityList.size());
//        PreApprovalFddAuthEntity preApprovalFddAuthEntity = preApprovalFddAuthEntityList.stream().findFirst().orElse(null);
        //获取token
        String accessToken = getFddToken();

        OpenApiClient apiClient = fadadaConfig.appClient();
        BaseRes<Void> unbindRes = new BaseRes<>();
        UserClient userClient = new UserClient(apiClient);
        for (PreApprovalFddAuthEntity preApprovalFddAuthEntity : preApprovalFddAuthEntityList) {
            UserUnbindReq userUnbindReq = new UserUnbindReq();
            assert preApprovalFddAuthEntity != null;
            userUnbindReq.setOpenUserId(preApprovalFddAuthEntity.getOpenUserId());
            userUnbindReq.setAccessToken(accessToken);

            try {
                unbindRes = userClient.unbind(userUnbindReq);
                log.info("unbindRes ={}", JSONUtil.toJsonStr(unbindRes));
                //修改授权
                if (FadadaEnum.SUCCESS.getCode().equals(unbindRes.getCode())) {
                    preApprovalFddAuthMapper.update(
                            new LambdaUpdateWrapper<PreApprovalFddAuthEntity>()
                                    .eq(PreApprovalFddAuthEntity::getPhone, preApprovalApplyInfo.getPhone())
                                    .eq(PreApprovalFddAuthEntity::getIdNumber, preApprovalApplyInfo.getIdNumber())
//                                    .eq(PreApprovalFddAuthEntity::getPreId, preId)
                                    .set(PreApprovalFddAuthEntity::getIsUnbind, 1)
                                    .set(PreApprovalFddAuthEntity::getDeleteFlag, 1)
                    );
                }
            } catch (ApiException e) {
                throw new BusinessException(e);
            }
        }

        return true;
    }

    @Override
    public BaseRes<Void> deleteDoc() {
        //        //生成签名
        //        Map<String, String> paramMap = FadadaUtils.getSignature(appId, appSecret);
        //        //获取token
        //        String accessToken = getFddToken();
        //        //-------------
        //        OpenApiClient apiClient = fadadaCondig.appClient();
        //        SignTaskClient signTaskClient = new SignTaskClient(apiClient);
        //        DeleteDocReq deleteDocReq = new DeleteDocReq();
        //        deleteDocReq.setAccessToken(accessToken);
        //        deleteDocReq.setSignTaskId("1722507196851157023");
        //        deleteDocReq.setDocIds(Arrays.asList("772cc144ecbd4c04ba32240a51ac1896", "ca47ac8ae314454b9e8ab039b0c95185"));
        //        BaseRes<Void> voidBaseRes;
        //        try {
        //            voidBaseRes = signTaskClient.deleteDoc(deleteDocReq);
        //        } catch (ApiException e) {
        //            throw new BusinessException(e);
        //        }
        return null;
    }

    @Override
    public BaseRes<Void> getSignOwnerFile(Integer preId) {

        if (ObjUtil.isNotEmpty(preId)) {
            List<PreFddSignTaskEntity> fddSignTaskEntityList = preFddSignTaskMapper.selectList(new LambdaQueryWrapper<PreFddSignTaskEntity>()
                    .eq(PreFddSignTaskEntity::getPreId, preId)
                    .eq(PreFddSignTaskEntity::getDeleteFlag, 0)
                    .eq(PreFddSignTaskEntity::getSignTaskStatus, SignTaskStatusEnums.TASK_FINISHED.getCode())
                    .orderByDesc(PreFddSignTaskEntity::getId)
            );
            if (CollUtil.isNotEmpty(fddSignTaskEntityList)) {
                PreFddSignTaskEntity fddSignTaskEntity = fddSignTaskEntityList.get(0);

                String signTaskId = fddSignTaskEntity.getSignTaskId();
                String customName = signTaskId + "_授权文件";

                signTaskDownloadFile(fddSignTaskEntity.getPreId(), signTaskId, customName,
                        ContractEnum.AUTHORIZATION.getCode());

            }


        }
        return null;
    }

    @Override
    @Transactional
    public boolean updateFddUserAuthResult(UserAuthResultDTO userAuthResultDTO) {

        boolean flag = false;

        int updateCount = preApprovalFddAuthMapper.update(new LambdaUpdateWrapper<PreApprovalFddAuthEntity>()
                .set(PreApprovalFddAuthEntity::getAuthResult, userAuthResultDTO.getAuthResult())
                .set(PreApprovalFddAuthEntity::getAuthScopes, userAuthResultDTO.getAuthScope())
                .set(PreApprovalFddAuthEntity::getAuthFailedReason, userAuthResultDTO.getAuthFailedReason())
                .set(PreApprovalFddAuthEntity::getOpenUserId, userAuthResultDTO.getOpenUserId())
                .eq(PreApprovalFddAuthEntity::getClientUserId, userAuthResultDTO.getClientUserId())
                .eq(PreApprovalFddAuthEntity::getPreId, userAuthResultDTO.getPreId())
        );
        if (updateCount > 0) {
            flag = true;
            preFddSignTaskMapper.update(new LambdaUpdateWrapper<PreFddSignTaskEntity>()
                    .set(PreFddSignTaskEntity::getClientUserId, userAuthResultDTO.getClientUserId())
                    .eq(PreFddSignTaskEntity::getPreId, userAuthResultDTO.getPreId()));

        }


        return flag;
    }

    @Override
    public SignTaskDetailVO getSignTaskDetail(SignTaskDetailDTO signTaskDetailDTO) {
        log.info("FadadaAuthServiceImpl.getSignTaskDetail start");
        SignTaskDetailVO signTaskDetailVO = new SignTaskDetailVO();
        try {
            String signTaskId = signTaskDetailDTO.getSignTaskId();
            Integer busiId = signTaskDetailDTO.getBusiId();
            Integer preId = signTaskDetailDTO.getPreId();
            Integer signType = signTaskDetailDTO.getSignType();

            // 查询签署任务详情
            SignTaskDetailRes signTaskDetailRes = getSignTaskDetailRes(signTaskId, busiId, preId);
            log.info("FadadaAuthServiceImpl.getSignTaskDetail signTaskDetailRes:{}",
                    JSONUtil.toJsonStr(signTaskDetailRes));
            if (signTaskDetailRes == null) {
                throw new BusinessException("获取签署任务详情失败");
            }

            // 更新签署任务状态
            BeanUtil.copyProperties(signTaskDetailRes, signTaskDetailVO);


            if (Objects.equals(ContractEnum.AUTHORIZATION.getCode(), signType)) {
                //授权书处理
                updatePreFddSignTask(signTaskDetailRes, signTaskDetailDTO);
            } else if (Objects.equals(ContractEnum.CONTRACT.getCode(), signType)) {
                //合同处理
                updateContractSignTask(signTaskId, signTaskDetailRes);
            }

            // 判断签署任务状态-已完成
            String signTaskStatus = signTaskDetailRes.getSignTaskStatus();
            log.info("FadadaAuthServiceImpl.getSignTaskDetail signTaskId:{} signTaskStatus:{}", signTaskId,
                    signTaskStatus);

            if (SignTaskStatusEnums.TASK_FINISHED.getCode().equals(signTaskStatus)) {
                signTaskFinishEvent(signTaskDetailDTO, signTaskId, signType, preId, busiId, signTaskDetailRes);
            }
        } catch (ApiException e) {
            throw new BusinessException(e);
        }
        return signTaskDetailVO;
    }

    /**
     * 签署任务完成事件
     *
     * @param signTaskDetailDTO 对任务详细信息 DTO 进行签名
     * @param signTaskId        对任务 ID 进行签名
     * @param signType          标志类型
     * @param preId             前 ID
     * @param busiId            商业 ID
     * @param signTaskDetailRes 对任务详细信息 RES 进行签名
     */
    private void signTaskFinishEvent(SignTaskDetailDTO signTaskDetailDTO, String signTaskId, Integer signType, Integer preId, Integer busiId, SignTaskDetailRes signTaskDetailRes) {
        Span span = tracer.nextSpan().start();
        //获取签署任务文件列表
        CompletableFuture.supplyAsync(() -> {
            try (Tracer.SpanInScope spanInScope = tracer.withSpan(span.start())) {
                log.info("FadadaAuthServiceImpl.getSignTaskDetail signTaskId {} download", signTaskId);
                //判断是否已经下载过签署任务文件
                Long finishFileList = preFddFinishFileMapper.selectCount(
                        new LambdaQueryWrapper<PreFddFinishFileEntity>()
                                .in(PreFddFinishFileEntity::getSignTaskId, signTaskId)
                                .eq(PreFddFinishFileEntity::getDeleteFlag, 0));

                if (signType == 0) {
                    if (finishFileList == 0) {
                        // 下载签署文件
                        newGetSignTaskFile(preId);
                        // 获取签署参与方刷脸底图
                        List<SignTaskDetailActor> actors = signTaskDetailRes.getActors();
                        if (CollUtil.isNotEmpty(actors)) {
                            Optional<SignTaskDetailActor> actor = actors.stream().filter(act -> act.getActorInfo().getActorType().equals("person")).findAny();
                            if (actor.isPresent()) {
                                String actorId = actor.get().getActorInfo().getActorId();
//                            String actorId = actors.get(0).getActorInfo().getActorId();
                                log.info("FadadaAuthServiceImpl.getSignTaskDetail actorId:{}", actorId);
                                UserRes userRes = getUserAuthStatus(actorId);
                                if (FadadaEnum.AUTHORIZED.getCode().equals(userRes.getBindingStatus()) &&
                                        FadadaEnum.IDENTIFIED.getCode().equals(userRes.getIdentStatus())) {
                                    PreApprovalFddAuthEntity updateEntity = new PreApprovalFddAuthEntity();
                                    updateEntity.setOpenUserId(userRes.getOpenUserId())
                                            .setAuthScopes(userRes.getAuthScope().toString())
                                            .setAuthResult("success");
                                    int updateCount = preApprovalFddAuthMapper.update(updateEntity, new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                                            .eq(PreApprovalFddAuthEntity::getClientUserId, userRes.getClientUserId()));
                                }
                                getSignTaskFacePicture(signTaskDetailDTO, actorId);

                                // 插入预审节点记录
                                insertPreNodeRecord(preId);

                                // 发起风控
                                log.info("risk launch on preapproval begin preId:{}", preId);
                                riskService.riskLaunchOnPreApproval(preId);
                                log.info("risk launch on preapproval end preId:{}", preId);
                            }

                        }
                    }
                } else if (signType == 1) {

                    //下载文件有单独定时任务，先更新合同状态再下载已签署的文件，注释掉此行代码
//                        downLoadSignTaskFile(signTaskId, signType);

                    //法大大签署状态完成时更新数据库合同状态为已签署
                    updateOrderContractSignStatus(signTaskId, busiId);
                }
                log.info("FadadaAuthServiceImpl.getSignTaskDetail signTaskId:{} faceImage", signTaskId);

            } finally {
                span.end();
            }
            return "ok";
        }).thenAccept(processResult ->
                log.info("FadadaAuthServiceImpl.getSignTaskDetail async download signTaskId:{}", processResult));
    }

    /**
     * 更新订单合同签署状态
     *
     * @param signTaskId 对任务 ID 进行签名
     * @param orderId    订单 ID
     */
    private void updateOrderContractSignStatus(String signTaskId, Integer orderId) {
        int update = orderContractMapper.update(new LambdaUpdateWrapper<OrderContractEntity>()
                .set(OrderContractEntity::getSignStatus, ContractEnum.SIGNED.getCode())
                .eq(OrderContractEntity::getSignTaskId, signTaskId)
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getDeleteFlag, 0));
        if (update == 0) {
            log.info("FadadaAuthServiceImpl.getSignTaskDetail updateOrderContractSignStatus update:{}", update);
            // TODO 代码待移除
            orderContractMapper.update(new LambdaUpdateWrapper<OrderContractEntity>()
                    .set(OrderContractEntity::getSignStatus, ContractEnum.SIGNED.getCode())
                    .eq(OrderContractEntity::getOrderId, orderId)
                    .eq(OrderContractEntity::getDeleteFlag, 0));
        }
    }

    private @Nullable SignTaskDetailRes getSignTaskDetailRes(String signTaskId, Integer busiId, Integer preId) throws ApiException {
        SignTaskDetailRes signTaskDetailRes = null;
        //获取token
        String accessToken = getFddToken();
        OpenApiClient apiClient = fadadaConfig.appClient();
        SignTaskClient signTaskClient = new SignTaskClient(apiClient);
        SignTaskBaseReq signTaskBaseReq = new SignTaskBaseReq();
        signTaskBaseReq.setSignTaskId(signTaskId);
        signTaskBaseReq.setAccessToken(accessToken);
        BaseRes<SignTaskDetailRes> baseRes = signTaskClient.getDetail(signTaskBaseReq);
        if (FadadaEnum.SUCCESS.getCode().equals(baseRes.getCode())) {
            signTaskDetailRes = baseRes.getData();
            //保存报文信息
            saveRequestLog(busiId, RequestResponseCode.FDD_SIGN_TASK_DETAIL, "", "", JSONUtil.toJsonStr(signTaskBaseReq), JSONUtil.toJsonStr(baseRes));

            log.info("preId = {}, busiId = {}, signTaskDetailRes ={}", preId, busiId, JSONUtil.toJsonStr(signTaskDetailRes));
        }
        return signTaskDetailRes;
    }

    private void updatePreFddSignTask(SignTaskDetailRes signTaskDetailRes, SignTaskDetailDTO signTaskDetailDTO) {
        int updateCount = preFddSignTaskMapper.update(new LambdaUpdateWrapper<PreFddSignTaskEntity>()
                .set(PreFddSignTaskEntity::getSignTaskStatus, signTaskDetailRes.getSignTaskStatus())
                .set(PreFddSignTaskEntity::getTerminationNote, signTaskDetailRes.getTerminationNote())
                .set(PreFddSignTaskEntity::getRevokeNote, signTaskDetailRes.getRevokeNote())
                .set(PreFddSignTaskEntity::getSignCreateTime, signTaskDetailRes.getCreateTime())
                .set(PreFddSignTaskEntity::getStartTime, signTaskDetailRes.getStartTime())
                .set(PreFddSignTaskEntity::getFinishTime, signTaskDetailRes.getFinishTime())
                .set(PreFddSignTaskEntity::getSignTaskSubject, signTaskDetailRes.getSignTaskSubject())
                .eq(PreFddSignTaskEntity::getPreId, signTaskDetailDTO.getPreId())
                .eq(PreFddSignTaskEntity::getSignTaskId, signTaskDetailDTO.getSignTaskId())
        );
        int preAuthorization = preAuthorizationMapper.update(new LambdaUpdateWrapper<PreAuthorizationEntity>()
                .set(PreAuthorizationEntity::getSignStatus, ContractEnum.SIGNED.getCode())
                .eq(PreAuthorizationEntity::getPreId, signTaskDetailDTO.getPreId())
                .eq(PreAuthorizationEntity::getSignTaskId, signTaskDetailDTO.getSignTaskId()));
        log.info("FadadaAuthServiceImpl.updatePreFddSignTask preId:{}, updateCount:{},preAuthorization:{}", signTaskDetailDTO.getPreId(), updateCount, preAuthorization);
    }

    private void newGetSignTaskFile(Integer preId) {
        if (ObjUtil.isNotEmpty(preId)) {
            List<PreFddSignTaskEntity> fddSignTaskEntityList = preFddSignTaskMapper.selectList(new LambdaQueryWrapper<PreFddSignTaskEntity>()
                    .eq(PreFddSignTaskEntity::getPreId, preId)
                    .eq(PreFddSignTaskEntity::getDeleteFlag, 0)
                    .eq(PreFddSignTaskEntity::getSignTaskStatus, SignTaskStatusEnums.TASK_FINISHED.getCode())
                    .orderByDesc(PreFddSignTaskEntity::getId)
            );
            if (CollUtil.isNotEmpty(fddSignTaskEntityList)) {
                PreFddSignTaskEntity fddSignTaskEntity = fddSignTaskEntityList.get(0);

                String signTaskId = fddSignTaskEntity.getSignTaskId();
                String customName = signTaskId + "_授权文件";


                signTaskDownloadFile(fddSignTaskEntity.getPreId(), signTaskId, customName,
                        ContractEnum.AUTHORIZATION.getCode());
            }
        }
    }

    /**
     * 获取单个文件下载 URL
     *
     * @param signTaskId 对任务 ID 进行签名
     * @param customName 自定义名称
     * @return {@link String }
     */
    private @Nullable String getSingleFileDownloadUrl(String signTaskId, String customName, SignTaskClient signTaskClient) {
        String downloadUrl = null;
        log.info("ContractRestartServiceImpl.getSingleFileDownloadUrl signTaskId={}", signTaskId);
        String accessToken = getFddToken();
        GetOwnerDownloadUrlReq getOwnerDownloadUrlReq = new GetOwnerDownloadUrlReq();
        getOwnerDownloadUrlReq.setAccessToken(accessToken);
        getOwnerDownloadUrlReq.setSignTaskId(signTaskId);

        getOwnerDownloadUrlReq.setCustomName(customName);
        OpenId openId = new OpenId();
        openId.setIdType("corp");
        openId.setOpenId(fadadaConfig.getOppenCorpId());
        getOwnerDownloadUrlReq.setOwnerId(openId);
        //如果是单个文档，是否压缩为zip格式
        getOwnerDownloadUrlReq.setCompression(true);
        try {
            BaseRes<OwnerDownloadUrlRes> urlResBaseRes = signTaskClient.getOwnerDownloadUrl(getOwnerDownloadUrlReq);
            log.info("ContractRestartServiceImpl.getSingleFileDownloadUrl signTaskId={} urlResBaseRes ={}", signTaskId,
                    JSONUtil.toJsonStr(urlResBaseRes));
            if (FadadaEnum.SUCCESS.getCode().equals(urlResBaseRes.getCode())) {
                downloadUrl = urlResBaseRes.getData().getDownloadUrl();

            } else {
                log.info("ContractRestartServiceImpl.getSingleFileDownloadUrl,urlResBaseRes.getMsg()={}", urlResBaseRes.getMsg());
            }

        } catch (ApiException e) {
            log.info("ContractRestartServiceImpl.getSingleFileDownloadUrl error", e);
        }
        return downloadUrl;
    }

    /**
     * 获取单个文件下载 URL
     *
     * @param signTaskId 对任务 ID 进行签名
     * @param customName 自定义名称
     * @return {@link String }
     */
    private @Nullable String getSingleFileDownloadUrl(String signTaskId, String customName) {

        SignTaskClient signTaskClient = new SignTaskClient(fadadaConfig.appClient());
        return getSingleFileDownloadUrl(signTaskId, customName, signTaskClient);
    }

    /**
     * 下载 Sign 任务文件
     *
     * @param signTaskId 对任务 ID 进行签名
     * @param signType   标志类型
     */
    private void downLoadSignTaskFile(String signTaskId, Integer signType) {
        if (StrUtil.isNotEmpty(signTaskId)) {
            SignTaskEntity fddSignTaskEntity = busiSignTaskMapper.selectOne(new LambdaQueryWrapper<SignTaskEntity>()
                    .eq(SignTaskEntity::getSignTaskId, signTaskId)
                    .eq(99 == signType, SignTaskEntity::getSignTaskStatus, SignTaskStatusEnums.TASK_FINISHED.getCode())
                    .eq(SignTaskEntity::getDeleteFlag, 0)
            );


            String customName = signTaskId + "_合同签约";

            signTaskDownloadFile(fddSignTaskEntity.getBusiId(), signTaskId, customName,
                    ContractEnum.CONTRACT.getCode());


        }

    }

    /**
     * 获取签署参与方刷脸底图
     *
     * @param signTaskDetailDTO
     * @throws ApiException
     */
    public void getSignTaskFacePicture(SignTaskDetailDTO signTaskDetailDTO, String actorId) {
        Integer preId = signTaskDetailDTO.getPreId();
        String signTaskId = signTaskDetailDTO.getSignTaskId();


        //获取token
        String accessToken = getFddToken();

        OpenApiClient apiClient = fadadaConfig.appClient();

        try {

            SignTaskClient signTaskClient = new SignTaskClient(apiClient);
            GetSignTaskFacePictureReq req = new GetSignTaskFacePictureReq();
            req.setSignTaskId(signTaskId);
            req.setAccessToken(accessToken);
            req.setActorId(actorId);
            log.info("getSignTaskFacePicture req:{}", JSONUtil.toJsonStr(req));
            BaseRes<GetSignTaskFacePictureRes> signTaskFacePicture = signTaskClient.getSignTaskFacePicture(req);
            log.info("getSignTaskFacePicture signTaskFacePicture:{}", JSONUtil.toJsonStr(signTaskFacePicture));

            log.info("signTaskFacePicture code:{}", signTaskFacePicture.getCode());

            if (FadadaEnum.SUCCESS.getCode().equals(signTaskFacePicture.getCode())) {

                saveFacePicture(preId, signTaskFacePicture.getData().getPicture());
                return;
            }

            log.warn("获取签署参与方刷脸底图失败,{} 尝试使用认证底图", signTaskFacePicture.getMsg());

            PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectOne(new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
                    .eq(PreApprovalApplyInfoEntity::getId, preId));

            if (ObjUtil.isNotEmpty(preApprovalApplyInfoEntity)) {
                List<PreApprovalFddAuthEntity> preApprovalFddAuthEntityList = preApprovalFddAuthMapper.selectList(
                        new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                                .select(PreApprovalFddAuthEntity::getOpenUserId)
                                .eq(PreApprovalFddAuthEntity::getIdNumber, preApprovalApplyInfoEntity.getIdNumber())
                                .eq(PreApprovalFddAuthEntity::getPhone, preApprovalApplyInfoEntity.getPhone())
                                .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                                .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                                .orderByDesc(PreApprovalFddAuthEntity::getCreateTime)
                        //                                .eq(PreApprovalFddAuthEntity::getPreId, preId)
                );

                if (CollUtil.isNotEmpty(preApprovalFddAuthEntityList)) {
                    PreApprovalFddAuthEntity preApprovalFddAuthEntity = preApprovalFddAuthEntityList.get(0);
                    if (preApprovalFddAuthEntity != null) {

                        String openUserId = preApprovalFddAuthEntity.getOpenUserId();
                        log.info("openUserId:{}", openUserId);
                        UserClient userClient = new UserClient(apiClient);
                        GetUserIdentityInfoReq identityInfoReq = new GetUserIdentityInfoReq();
                        identityInfoReq.setOpenUserId(openUserId);
                        identityInfoReq.setAccessToken(accessToken);

                        BaseRes<UserIdentityInfoRes> identityInfo = userClient.getIdentityInfo(identityInfoReq);

                        log.info("getIdentityInfo code:{}", identityInfo.getCode());
                        if (FadadaEnum.SUCCESS.getCode().equals(identityInfo.getCode())) {
                            String facePicture = identityInfo.getData().getFacePicture();
                            saveFacePicture(preId, facePicture);
                            return;
                        }
                    } else {
                        log.error("获取签署参与方刷脸底图失败,未找到授权信息");
                    }
                }
            }
            log.error("获取签署参与方刷脸底图失败 {}", signTaskFacePicture.getMsg());
        } catch (ApiException e) {
            log.error("getSignTaskFacePicture error", e);
            throw new BusinessException(e);
        }
    }

    /**
     * 法大大认证信息
     *
     * @param preId
     * @param
     */
    @Override
    public int getIdentityInfo(Integer preId, String signTaskId, String actorId) {

        //获取token
        String accessToken = getFddToken();
        OpenApiClient apiClient = fadadaConfig.appClient();
        SignTaskClient signTaskClient = new SignTaskClient(apiClient);
        GetSignTaskFacePictureReq req = new GetSignTaskFacePictureReq();
        req.setSignTaskId(signTaskId);
        req.setAccessToken(accessToken);
        req.setActorId(actorId);
        BaseRes<GetSignTaskFacePictureRes> signTaskFacePicture = null;
        try {
            signTaskFacePicture = signTaskClient.getSignTaskFacePicture(req);

            GetSignTaskFacePictureRes data = signTaskFacePicture.getData();
            return saveFacePicture(preId, data.getPicture());

        } catch (ApiException e) {
            throw new BusinessException(e);
        }


    }

    @Override
    public boolean getList() {

        //获取token
        String accessToken = getFddToken();

        OpenApiClient apiClient = fadadaConfig.appClient();

        AppClient appClient = new AppClient(apiClient);
        GetAppOpenIdListReq getAppOpenIdListReq = new GetAppOpenIdListReq();
        getAppOpenIdListReq.setAccessToken(accessToken);
        getAppOpenIdListReq.setIdType("person");
        try {
            BaseRes<GetAppOpenIdListRes> baseRes = appClient.getOpenIdList(getAppOpenIdListReq);
            log.info("getList data:{}", JSONUtil.toJsonStr(baseRes.getData()));
        } catch (ApiException e) {
            throw new BusinessException(e);
        }
        return false;
    }

    /**
     * 创建订单合同签署任务
     *
     * @param contractTaskDTO 合同任务 DTO
     * @return {@link BaseRes }<{@link CreateSignTaskRes }>
     */
    @Override
    public BaseRes<CreateSignTaskRes> contractSignTask(ContractTaskDTO contractTaskDTO) {
        Integer orderId = contractTaskDTO.getOrderId();
        log.info("contractSignTask orderId:{}", orderId);
        String contractLockKey = getGenerateContractLockKey(orderId);
        String requestId = IdUtil.randomUUID();

        if (!redisService.tryLock(contractLockKey, requestId, 60)) {
            log.info("contractSignTask orderId:{} contractLockKey lock failed", orderId);
            throw new BusinessException("合同未生成完成，请稍后再试");
        }


        // 0. 检查是否存在未签署完成的合同
        SignTaskEntity signTaskEntity = busiSignTaskMapper.selectJoinOne(
                SignTaskEntity.class,
                new MPJLambdaWrapper<SignTaskEntity>()
                        .selectAll(SignTaskEntity.class)
                        .innerJoin(OrderContractEntity.class, on -> on
                                .eq(OrderContractEntity::getOrderId, SignTaskEntity::getBusiId)
                                .eq(SignTaskEntity::getSignType, 1)
                                .eq(OrderContractEntity::getDeleteFlag, 0)
                                .eq(OrderContractEntity::getContractFlag, 1))
                        .eq(SignTaskEntity::getBusiId, orderId)
                        .ne(SignTaskEntity::getSignTaskStatus, SignTaskStatusEnums.TASK_FINISHED.getCode())
                        .eq(SignTaskEntity::getSignType, 1)
                        .eq(SignTaskEntity::getDeleteFlag, 0)
                        .orderByDesc(SignTaskEntity::getId)
                        .last("limit 1"));

        if (ObjUtil.isNotEmpty(signTaskEntity)) {
            log.info("contractSignTask orderId:{} exists unsigned contracts signTaskId:{}", orderId, signTaskEntity.getSignTaskId());
            redisService.releaseLock(contractLockKey, requestId);
            return buildCreateSignTaskResBaseRes(signTaskEntity.getSignTaskId());
        }

        BaseRes<CreateSignTaskRes> signTaskRes;
        try {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                    .select(OrderInfoEntity::getPreId, OrderInfoEntity::getId)
                    .eq(OrderInfoEntity::getId, orderId));
            if (ObjUtil.isEmpty(orderInfoEntity)) {
                throw new BusinessException("订单不存在");
            }
            // 1. 创建新签署任务
            // 1.1 查询要签署的文件
            //获取线上签约开关状态
            List<FddFileResourceVO> resourceVOList = orderContractMapper.selectJoinList(
                    FddFileResourceVO.class,
                    new MPJLambdaWrapper<OrderContractEntity>()
                            .selectAs(FileToFddEntity::getResourceFileUid, FddFileResourceVO::getResourceFileUid)
                            .selectAs(FileToFddEntity::getFddFileId, FddFileResourceVO::getFddFileId)
                            .selectAs(OrderContractEntity::getName, FddFileResourceVO::getFileName)
                            .selectAs(OrderContractEntity::getTemplateId, FddFileResourceVO::getTemplateId)
                            .selectAs(OrderContractEntity::getNumber, FddFileResourceVO::getNumber)
                            .innerJoin(FileResourceEntity.class, FileResourceEntity::getFileUid, OrderContractEntity::getResource)
                            .innerJoin(FileToFddEntity.class, on -> on
                                    .eq(OrderContractEntity::getResource, FileToFddEntity::getResourceFileUid)
                                    .eq(FileToFddEntity::getDeleteFlag, 0)
                                    .isNotNull(FileToFddEntity::getFddFileId))
                            .eq(OrderContractEntity::getDeleteFlag, 0)
                            //去除抵押合同
                            .notIn(OrderContractEntity::getContractFlag, 3, 4)
                            .in(OrderContractEntity::getOrderId, orderId)
                            .notIn(OrderContractEntity::getTemplateId, 23, 24)
                            .orderByAsc(FileToFddEntity::getId)
            );

            CreateSignTaskReq signTaskReq = new CreateSignTaskReq();


            OpenId openId = new OpenId();
            openId.setIdType("corp");
            openId.setOpenId(fadadaConfig.getOppenCorpId());
            //扣费主体
            signTaskReq.setInitiator(openId);
            //签署任务主题
            signTaskReq.setSignTaskSubject("合同签约");
            signTaskReq.setBusinessId(fadadaConfig.getBusinessId());

            log.info("orderId = {},需要签署的合同列表resourceVOList.size = {}", orderId, resourceVOList.size());
            if (CollUtil.isEmpty(resourceVOList)) {
                throw new BusinessException("未查询到合同信息");
            }

            // 文件信息
            List<AddDocInfo> docs = buildAddDocInfoList(resourceVOList);

            signTaskReq.setDocs(docs);
            List<Integer> templateIdList = resourceVOList.stream().map(FddFileResourceVO::getTemplateId).toList();
            List<Integer> corpIdList = fddFieldConfigMapper.selectList(new LambdaQueryWrapper<FddFieldConfigEntity>()
                            .eq(FddFieldConfigEntity::getDeleteFlag, 0)
                            .in(FddFieldConfigEntity::getTemplateId, templateIdList))
                    .stream()
                    .map(FddFieldConfigEntity::getSealCorpId)
                    .toList();
            List<AddActorsInfo> corp = new ArrayList<>();
            if (CollUtil.isNotEmpty(corpIdList)) {
                List<FddCorpConfigEntity> corpConfigList = fddCorpConfigMapper.selectList(new LambdaQueryWrapper<FddCorpConfigEntity>()
                        .eq(FddCorpConfigEntity::getDeleteFlag, 0)
                        .in(FddCorpConfigEntity::getId, corpIdList));
                //企业参与方
                corp = handleCorpActors(docs, corpConfigList);
            }

            List<AddActorsInfo> actorsList = new ArrayList<>(corp);

            //个人参与方信息
            List<AddActorsInfo> person = handlePersonActors(contractTaskDTO, docs);
            actorsList.addAll(person);

            signTaskReq.setActors(actorsList);
            log.info("orderId = {}, 创建合同签署任务请求报文，signTaskReq ={}", orderId, JSONUtil.toJsonStr(signTaskReq));

            //获取token
            String accessToken = getFddToken();
            OpenApiClient apiClient = fadadaConfig.appClient();
            SignTaskClient signTaskClient = new SignTaskClient(apiClient);
            signTaskReq.setAccessToken(accessToken);

            signTaskRes = signTaskClient.create(signTaskReq);
            log.info("orderId = {}, 合同签署signTaskRes ={}", orderId, JSONUtil.toJsonStr(signTaskRes));
            //保存报文信息
            saveRequestLog(orderId, RequestResponseCode.FDD_CREATE_SIGN_TASK, "", "", JSONUtil.toJsonStr(signTaskReq), JSONUtil.toJsonStr(signTaskRes));

            if (!FadadaEnum.SUCCESS.getCode().equals(signTaskRes.getCode())) {
                throw new BusinessException(signTaskRes.getMsg());
            }

            String signTaskId = signTaskRes.getData().getSignTaskId();
            // 合同文件标记签署任务Id
            addOrderContractSignTaskId(signTaskId, orderId, resourceVOList);


            //提交签署任务
            BaseRes<Void> submitSignTaskRes = submitSignTask(signTaskId, signTaskClient, orderId);
            if (!FadadaEnum.SUCCESS.getCode().equals(submitSignTaskRes.getCode())) {
                throw new BusinessException(submitSignTaskRes.getMsg());
            }
            //添加签署任务数据
            initSignTask(orderId, signTaskId, 1);


        } catch (ApiException e) {
            log.info("ContractRestartServiceImpl.contractRestartSignTask error ={}", e.getMessage());
            throw new BusinessException(e);
        } finally {
            redisService.releaseLock(contractLockKey, requestId);
        }
        return signTaskRes;
    }

    /**
     * 添加订单合同的签署任务 ID
     *
     * @param signTaskId     对任务 ID 进行签名
     * @param orderId        订单 ID
     * @param resourceVOList 资源投票表
     */
    private void addOrderContractSignTaskId(String signTaskId, Integer orderId, List<FddFileResourceVO> resourceVOList) {
        orderContractMapper.update(new LambdaUpdateWrapper<OrderContractEntity>()
                .set(OrderContractEntity::getSignTaskId, signTaskId)
                .eq(OrderContractEntity::getOrderId, orderId)
//                    .isNull(OrderContractEntity::getSignTaskId)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .in(OrderContractEntity::getResource, resourceVOList.stream().map(FddFileResourceVO::getResourceFileUid).toList()));
    }

    /**
     * 电子签 签署流程
     * <p>
     * 按模板id构建签署任务
     *
     * @param contractTaskDTO 订单信息
     * @param signTaskSubject 签署任务主题
     * @param templateIdList  签署模板id
     * @return
     */
    @Override
    public BaseRes<CreateSignTaskRes> createSignTask(ContractTaskDTO contractTaskDTO,
                                                     String signTaskSubject,
                                                     List<Integer> templateIdList, Integer signType) {
        Integer orderId = contractTaskDTO.getOrderId();
        log.info("createSignTask orderId:{}", orderId);
        BaseRes<CreateSignTaskRes> signTaskRes;
        try {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                    .select(OrderInfoEntity::getPreId, OrderInfoEntity::getId)
                    .eq(OrderInfoEntity::getId, orderId));
            if (ObjUtil.isEmpty(orderInfoEntity)) {
                throw new BusinessException("订单不存在");
            }

            //查询要签署的文件
            List<FddFileResourceVO> resourceVOList = getFddFileResourceVOS(contractTaskDTO, templateIdList);
            log.info("orderId = {}, templateId:{} resourceVOList.size = {}", orderId,
                    templateIdList,
                    resourceVOList.size());

            if (CollUtil.isEmpty(resourceVOList)) {
                throw new BusinessException("未查询到待签署文件");
            }


            //获取token
            String accessToken = getFddToken();
            OpenApiClient apiClient = fadadaConfig.appClient();
            SignTaskClient signTaskClient = new SignTaskClient(apiClient);
            CreateSignTaskReq signTaskReq = new CreateSignTaskReq();
            signTaskReq.setAccessToken(accessToken);

            OpenId openId = new OpenId();
            openId.setIdType("corp");
            openId.setOpenId(fadadaConfig.getOppenCorpId());
            //扣费主体
            signTaskReq.setInitiator(openId);
            //签署任务主题
            signTaskReq.setSignTaskSubject(signTaskSubject);
            signTaskReq.setBusinessId(fadadaConfig.getBusinessId());
            // 文件信息
            List<AddDocInfo> docs = buildAddDocInfoList(resourceVOList);
            signTaskReq.setDocs(docs);

            List<AddActorsInfo> actors = new ArrayList<>();
            if (Objects.equals(contractTaskDTO.getSignCropFlag(), 0)) {
//                List<Integer> corpIds = fddFieldConfigMapper.selectList(new LambdaQueryWrapper<FddFieldConfigEntity>()
//                        .in(FddFieldConfigEntity::getTemplateId, templateIdList))
//                        .stream()
//                        .map(FddFieldConfigEntity::getSealCorpId)
//                        .toList();
                List<FddCorpConfigEntity> corpConfigList = fddCorpConfigMapper.selectList(new LambdaQueryWrapper<FddCorpConfigEntity>()
                        .eq(FddCorpConfigEntity::getDeleteFlag, 0)
                        .in(FddCorpConfigEntity::getId, fddFieldConfigMapper.selectList(new LambdaQueryWrapper<FddFieldConfigEntity>()
                                        .in(FddFieldConfigEntity::getTemplateId, templateIdList))
                                .stream()
                                .map(FddFieldConfigEntity::getSealCorpId)
                                .toList()));
                //企业参与方
                List<AddActorsInfo> corp = handleCorpActors(docs, corpConfigList);
                actors.addAll(corp);
            }
            //个人参与方信息
            List<AddActorsInfo> person = handlePersonActors(contractTaskDTO, docs);
            actors.addAll(person);

            signTaskReq.setActors(actors);
            log.info("orderId = {}, contract signing task request signTaskReq ={}", orderId,
                    JSONUtil.toJsonStr(signTaskReq));

            signTaskRes = signTaskClient.create(signTaskReq);
            log.info("orderId = {}, contract signing task signTaskRes ={}", orderId,
                    JSONUtil.toJsonStr(signTaskRes));

            //保存报文信息
            saveRequestLog(orderId, RequestResponseCode.FDD_CREATE_SIGN_TASK, "", "",
                    JSONUtil.toJsonStr(signTaskReq), JSONUtil.toJsonStr(signTaskRes));

            if (!FadadaEnum.SUCCESS.getCode().equals(signTaskRes.getCode())) {
                log.error("orderId = {}, createSignTask error, msg ={}", orderId, signTaskRes.getMsg());
                throw new BusinessException(signTaskRes.getMsg());
            }
            String signTaskId = signTaskRes.getData().getSignTaskId();

            addOrderContractSignTaskId(signTaskId, orderId, resourceVOList);

            //提交签署任务
            BaseRes<Void> submitSignTaskRes = submitSignTask(signTaskId, signTaskClient, orderId);
            if (!FadadaEnum.SUCCESS.getCode().equals(submitSignTaskRes.getCode())) {
                log.error("orderId = {}, submitSignTask error, msg ={}", orderId, submitSignTaskRes.getMsg());
                throw new BusinessException(submitSignTaskRes.getMsg());
            }

            //添加签署任务数据
            initSignTask(orderId, signTaskId, signType);


        } catch (ApiException e) {
            throw new BusinessException(e);
        }
        return signTaskRes;
    }

    /**
     * 获取签署任务链接二维码
     *
     * @param signId
     * @param signTaskId
     * @param actorId
     * @return
     */
    @Override
    public ResponseEntity<byte[]> getSignUrl(Integer signId, String signTaskId, String actorId) {

        //获取token
        String accessToken = getFddToken();
        OpenApiClient apiClient = fadadaConfig.appClient();
        SignTaskClient signTaskClient = new SignTaskClient(apiClient);
        //签署人员信息
        SignTaskActorGetUrlReq signTaskActorGetUrlReq = new SignTaskActorGetUrlReq();
        signTaskActorGetUrlReq.setAccessToken(accessToken);
        signTaskActorGetUrlReq.setSignTaskId(signTaskId);
        signTaskActorGetUrlReq.setActorId(actorId);
        //            signTaskActorGetUrlReq.setClientUserId(authEntity.getClientUserId());
        try {
            BaseRes<SignTaskActorGetUrlRes> urlRes = signTaskClient.signTaskActorGetUrl(signTaskActorGetUrlReq);

            log.info("FadadaAuthServiceImpl.getSignUrl signId = {}, signTaskId = {},  signTaskActorGetUrlReq ={}",
                    signId, signTaskId, JSONUtil.toJsonStr(signTaskActorGetUrlReq));
            //保存报文信息
            saveRequestLog(signId, RequestResponseCode.FDD_ACTOR_SIGN_URL, "", "", JSONUtil.toJsonStr(signTaskActorGetUrlReq), JSONUtil.toJsonStr(urlRes));
            log.info("FadadaAuthServiceImpl.getSignUrl urlRes ={}", JSONUtil.toJsonStr(urlRes));

            if (FadadaEnum.SUCCESS.getCode().equals(urlRes.getCode())) {
                return getSignQrCode(urlRes.getData().getActorSignTaskEmbedUrl());
            } else {
                log.error("FadadaAuthServiceImpl.getSignUrl error, msg ={}", urlRes.getMsg());
                throw new BusinessException(urlRes.getMsg());
            }
        } catch (ApiException e) {
            log.error("FadadaAuthServiceImpl.getSignUrl error {}", e.getMessage(), e);
            throw new BusinessException(e);
        }
    }

    public ResponseEntity<byte[]> getSignQrCode(String url) {
        byte[] imageBytes;
        try {
            imageBytes = QrCodeUtils.generateQrCode("", url);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.setContentDisposition(ContentDisposition.builder("attachment").filename("QrCode" + ".png").build());
            // 返回响应实体
            return new ResponseEntity<>(imageBytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("FadadaAuthServiceImpl.getSignQrCode e:", e);
            throw new BusinessException(e);
        }
    }

    private @NotNull List<AddDocInfo> buildAddDocInfoList(List<FddFileResourceVO> resourceVOList) {
        return resourceVOList.stream().map(vo -> {
            AddDocInfo docInfo = new AddDocInfo();
            docInfo.setDocId(vo.getResourceFileUid());
            docInfo.setDocName(getDocName(vo));
            docInfo.setDocFileId(vo.getFddFileId());
            //文档中添加控价
            docInfo.setDocFields(getDocFieldList(vo, 1));
            return docInfo;
        }).toList();
    }

    private List<FddFileResourceVO> getFddFileResourceVOS(ContractTaskDTO contractTaskDTO, List<Integer> templateIdList) {
        return orderContractMapper.selectJoinList(
                FddFileResourceVO.class,
                new MPJLambdaWrapper<OrderContractEntity>()
                        .selectAs(FileToFddEntity::getResourceFileUid, FddFileResourceVO::getResourceFileUid)
                        .selectAs(FileToFddEntity::getFddFileId, FddFileResourceVO::getFddFileId)
                        .selectAs(OrderContractEntity::getName, FddFileResourceVO::getFileName)
                        .selectAs(OrderContractEntity::getTemplateId, FddFileResourceVO::getTemplateId)
                        .selectAs(OrderContractEntity::getNumber, FddFileResourceVO::getNumber)
                        .innerJoin(FileResourceEntity.class, FileResourceEntity::getFileUid, OrderContractEntity::getResource)
                        .innerJoin(FileToFddEntity.class, on -> on
                                .eq(OrderContractEntity::getResource, FileToFddEntity::getResourceFileUid)
                                .eq(FileToFddEntity::getDeleteFlag, 0)
                                .isNotNull(FileToFddEntity::getFddFileId))
                        .eq(OrderContractEntity::getDeleteFlag, 0)
                        .in(OrderContractEntity::getOrderId, contractTaskDTO.getOrderId())
                        .in(OrderContractEntity::getTemplateId, templateIdList)
                        .orderByAsc(FileToFddEntity::getId)
        );
    }


    /**
     * 获取文档字段列表
     *
     * @param vo            VO
     * @param conditionType 条件类型  1: templateType 2: fileUid
     * @return {@link List }<{@link Field }>
     */
    private List<Field> getDocFieldList(FddFileResourceVO vo, Integer conditionType) {
        return fddFieldConfigMapper.selectList(
                new MPJLambdaWrapper<FddFieldConfigEntity>()
                        .eq(FddFieldConfigEntity::getDeleteFlag, 0)
                        .eq(Objects.equals(conditionType, 1), FddFieldConfigEntity::getSignFileType, 1)
                        .eq(Objects.equals(conditionType, 1), FddFieldConfigEntity::getTemplateId, vo.getTemplateId())
                        .eq(Objects.equals(conditionType, 2), FddFieldConfigEntity::getFileUid, vo.getResourceFileUid())
        ).stream().map(fieldConfig -> {
            Field field = new Field();
            field.setFieldId(fieldConfig.getFieldId());
            field.setFieldName(fieldConfig.getFieldName());
            field.setFieldType(fieldConfig.getFieldType());
            if (("corp_seal").equals(fieldConfig.getFieldType())) {
                field.setFieldKey(String.valueOf(fieldConfig.getSealCorpId()));
            }
            FieldPosition fieldPosition = new FieldPosition();
            fieldPosition.setPositionMode(fieldConfig.getFieldPositionMod());
            fieldPosition.setPositionPageNo(fieldConfig.getFieldPositionPage());
            fieldPosition.setPositionX(fieldConfig.getFieldPositionX());
            fieldPosition.setPositionY(fieldConfig.getFieldPositionY());
            field.setPosition(fieldPosition);
            if (("联系地址").equals(fieldConfig.getFieldName())) {
                //单行文本控件参数
                FieldTextSingleLine fieldTextSingleLine = new FieldTextSingleLine();
                fieldTextSingleLine.setWidth(300);
                field.setFieldTextSingleLine(fieldTextSingleLine);
            }

            return field;
        }).toList();

    }

    /**
     * 更新签名任务
     *
     * @param signTaskId 对任务 ID 进行签名
     * @param res        回复
     */
    private void updateContractSignTask(String signTaskId, SignTaskDetailRes res) {
        log.info("updateSignTask signTaskId:{},res:{}", signTaskId, JSONUtil.toJsonStr(res));
        busiSignTaskMapper.update(new LambdaUpdateWrapper<SignTaskEntity>()
                .set(SignTaskEntity::getSignTaskStatus, res.getSignTaskStatus())
                .set(SignTaskEntity::getTerminationNote, res.getTerminationNote())
                .set(SignTaskEntity::getRevokeNote, res.getRevokeNote())
                .set(SignTaskEntity::getSignCreateTime, res.getCreateTime())
                .set(SignTaskEntity::getTaskStartTime, res.getStartTime())
                .set(SignTaskEntity::getFinishTime, res.getFinishTime())
                .set(SignTaskEntity::getSignTaskSubject, res.getSignTaskSubject())
                .eq(SignTaskEntity::getSignTaskId, res.getSignTaskId())
        );
    }

    /**
     * 初始化签名任务
     *
     * @param busyId     忙 ID
     * @param signTaskId 对任务 ID 进行签名
     * @param signType   标志类型
     */
    private void initSignTask(Integer busyId, String signTaskId, Integer signType) {
        log.info("initSignTask busyId:{},signTaskId:{},signType:{}", busyId, signTaskId, signType);

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(busyId);
        OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectById(orderInfoEntity.getCustomerId());
        PreApprovalFddAuthEntity preApprovalFddAuthEntity = getPreApprovalFddAuthEntity(orderCustomerInfoEntity.getIdNumber(),
                orderCustomerInfoEntity.getPhone());

        busiSignTaskMapper.insert(new SignTaskEntity()
                .setBusiId(busyId)
                .setSignTaskId(signTaskId)
                .setMobile(orderInfoEntity.getCustomerPhone())
                .setIdNumber(orderCustomerInfoEntity.getIdNumber())
                .setClientUserId(preApprovalFddAuthEntity.getClientUserId())
                .setSignType(signType)
        );
    }

    private PreApprovalFddAuthEntity getPreApprovalFddAuthEntity(String idNumber, String phone) {
        PreApprovalFddAuthEntity preApprovalFddAuthEntity = preApprovalFddAuthMapper.selectOne(
                new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                        .eq(PreApprovalFddAuthEntity::getIdNumber, idNumber)
                        .eq(PreApprovalFddAuthEntity::getPhone, phone)
                        .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                        .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                        .orderByDesc(PreApprovalFddAuthEntity::getId)
                        .last("limit 1")
        );

        if (preApprovalFddAuthEntity == null) {
            throw new BusinessException("未查询到客户授权信息");
        }
        return preApprovalFddAuthEntity;
    }

    private List<AddActorsInfo> handleCorpActors(List<AddDocInfo> docs, List<FddCorpConfigEntity> corpConfigList) {
        List<AddActorsInfo> actorsList = new ArrayList<>();
//        List<FddCorpConfigEntity> corpConfigList = fddCorpConfigMapper.selectList(new LambdaQueryWrapper<FddCorpConfigEntity>()
//                .eq(FddCorpConfigEntity::getDeleteFlag, 0));
        corpConfigList.forEach(corpConfig -> {
            List<AddSignFieldInfo> corpSignFields = new ArrayList<>();
            //定义企业参与方信息
            AddActorsInfo adCorpActors = new AddActorsInfo();
            //企业参与方
            Actor corpActor = new Actor();
            corpActor.setActorId(corpConfig.getClientCorpId());
            corpActor.setActorType("corp");
            corpActor.setActorName(corpConfig.getCorpName());
            corpActor.setPermissions(Arrays.asList("sign"));
            corpActor.setSendNotification(false);
//            corpActor.setAuthScopes(Arrays.asList("signtask_init", "signtask_info", "signtask_file"));
            corpActor.setAuthScopes(FadadaEnum.getAllAuthScope());
            corpActor.setActorOpenId(corpConfig.getOpenCorpId());
            adCorpActors.setActor(corpActor);

            docs.forEach(doc -> {
                doc.getDocFields().forEach(field -> {
                    //设置参与方的签章控件
                    AddSignFieldInfo signFieldInfo = new AddSignFieldInfo();
                    if (field.getFieldType().equals("corp_seal")) {
                        if (Objects.equals(corpConfig.getId(), Integer.parseInt(field.getFieldKey()))) {
                            signFieldInfo.setFieldDocId(doc.getDocId());
                            signFieldInfo.setFieldId(field.getFieldId());
                            signFieldInfo.setSealId(Long.valueOf(corpConfig.getSealId()));
                            corpSignFields.add(signFieldInfo);
                        }
                    }

                });
            });

            log.info("adCorpActors = {}", JSONUtil.toJsonStr(adCorpActors));
            AddSignConfigInfo addSignConfigInfo = new AddSignConfigInfo();
            addSignConfigInfo.setRequestVerifyFree(true);
            adCorpActors.setSignConfigInfo(addSignConfigInfo);
            adCorpActors.setSignFields(corpSignFields);
            actorsList.add(adCorpActors);

        });
        return actorsList;

    }

    /**
     * 组建参与方信息
     *
     * @param contractTaskDTO
     */
    private List<AddActorsInfo> handlePersonActors(ContractTaskDTO contractTaskDTO, List<AddDocInfo> docs) {
        List<AddActorsInfo> actorsList = new ArrayList<>();
        AddActorsInfo addActorsInfo = new AddActorsInfo();
        ActorsInfoDTO actorsInfoDTO = getOrderCustomerActorId(contractTaskDTO.getOrderId());
        //个人参与方信息
        Actor actor = getActor(actorsInfoDTO);
        addActorsInfo.setActor(actor);
        //设置个人参与方的签章控件
        List<AddSignFieldInfo> signFields = new ArrayList<>();
        docs.forEach(doc -> {
            doc.getDocFields().forEach(field -> {
                AddSignFieldInfo signFieldInfo = new AddSignFieldInfo();
                if (!field.getFieldType().equals("corp_seal") && !field.getFieldName().equals("免验签个人签名")) {
                    signFieldInfo.setFieldDocId(doc.getDocId());
                    signFieldInfo.setFieldId(field.getFieldId());
                    signFields.add(signFieldInfo);
                }
            });
        });
        addActorsInfo.setSignFields(signFields);
        //个人签署配置信息
        AddSignConfigInfo addSignConfigInfo = new AddSignConfigInfo();
        addSignConfigInfo.setFreeLogin(true);
        addSignConfigInfo.setIdentifiedView(false);
        //标准签名
        addSignConfigInfo.setSignerSignMethod("standard");
        //强制阅读，单位秒
        addSignConfigInfo.setReadingTime("5");
        log.info("handlePersonActors env ={}", envUtil.isPrd());
        if (envUtil.isPrd()) {
            //face: 刷脸验证,sms: 短信验证
            addSignConfigInfo.setVerifyMethods(List.of("face"));
        }
        addActorsInfo.setSignConfigInfo(addSignConfigInfo);
        actorsList.add(addActorsInfo);
        return actorsList;
    }

    @Override
    public Boolean getFaddFileUrl(List<MultipartFile> files, Integer fundId, String filePurpose) {
        Result<List<FileVO>> fileVOList = resourceFeign.uploadFile(files);
        boolean flag = false;
        if (Result.success().getCode().equals(fileVOList.getCode())) {
            log.info("getFaddFileUrl filePurpose = {}", filePurpose);
            flag = true;
            //只有授权书直接上传至法大大服务器
            if (FileToFadadaTypeEnums.AUTHORIZATION.getType().equals(filePurpose)) {
                List<String> resourceIds = fileVOList.getData().stream().map(FileVO::getResourceId).toList();

                batchFileTOFdd(resourceIds, fundId);


            } else if (FileToFadadaTypeEnums.CONTRACT.getType().equals(filePurpose)) {
                fileVOList.getData().forEach(vo -> {
                    FileToFddEntity fileToFddEntity = new FileToFddEntity()
                            .setResourceFileUid(vo.getResourceId())
                            .setFileBelong(fundId)
                            .setFilePurpose(filePurpose);
                    fileToFddMapper.insert(fileToFddEntity);
                });

            }
        }

        return flag;
    }

    @Override
    public BaseRes<ECorpAuthUrlRes> getFaddCorpAuthUrl() {
        String clientCorpId = FadadaUtils.generateUserId();
        log.info("企业授权时生成clientCorpId ={}", clientCorpId);
        BaseRes<ECorpAuthUrlRes> baseRes;
        try {


            //获取token
            String accessToken = getFddToken();
            OpenApiClient apiClient = fadadaConfig.appClient();
            //创建服务
            CorpClient corpClient = new CorpClient(apiClient);
            GetCorpAuthResourceUrlReq req = new GetCorpAuthResourceUrlReq();
            req.setAccessToken(accessToken);
            req.setClientCorpId(clientCorpId);
            baseRes = corpClient.getCorpAuthUrl(req);

            log.info("获取企业授权链接返回结果：{}", JSONUtil.toJsonStr(baseRes));

        } catch (ApiException e) {
            throw new BusinessException(e);
        }

        return baseRes;
    }

    @Override
    public BaseRes<GetSealFreeSignUrlRes> getSealFreeSignUrl(String openCorpId) {


        BaseRes<GetSealFreeSignUrlRes> signUrlResBaseRes = new BaseRes<>();

        //获取token
        String accessToken = getFddToken();
        OpenApiClient apiClient = fadadaConfig.appClient();
        //获取签章列表
        List<GetListSealInfo> sealInfoList = getSealList(apiClient, accessToken, openCorpId);
        //            List<Long> sealIds = sealInfoList.stream().map(GetListSealInfo::getSealId).toList();
        //            SealClient sealClient = new SealClient(apiClient);
        //            GetSealFreeSignUrlReq req = new GetSealFreeSignUrlReq();
        //            req.setAccessToken(accessToken);
        //            req.setOpenCorpId("d470597d38f2449996ece26d44955b4b");
        //            req.setSealIds(sealIds);
        //            req.setBusinessId("5df5a26021903b22c027351ff69e2167");
        //            signUrlResBaseRes = sealClient.getSealFreeSignUrl(req);
        //            log.info("获取免费签章链接返回结果：{}", JSONUtil.toJsonStr(signUrlResBaseRes));


        return signUrlResBaseRes;
    }

    @Override
    public BaseRes<GetIdentifiedStatusRes> getFaddCorpAuthState() {


        BaseRes<GetSealFreeSignUrlRes> signUrlResBaseRes;
        //获取token
        String accessToken = getFddToken();
        OpenApiClient apiClient = fadadaConfig.appClient();
        //创建服务
        CorpClient corpClient = new CorpClient(apiClient);
        GetCorpReq req = new GetCorpReq();
        try {
            req.setAccessToken(accessToken);
            req.setCorpIdentNo("91110112092921607L");
            BaseRes<CorpRes> resBaseRes = corpClient.get(req);
            log.info("获取企业授权状态返回结果：{}", JSONUtil.toJsonStr(resBaseRes));
        } catch (ApiException e) {
            throw new BusinessException(e);
        }
        return null;
    }

    @Override
    public BaseRes<SignTaskActorGetUrlRes> getContractSignUrl(ContractSingUrlDTO contractSingUrlDTO) {
        log.info("FadadaAuthServiceImpl.getContractSignUrl start");
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(contractSingUrlDTO.getOrderId());
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(orderInfoEntity.getPreId());

        List<PreApprovalFddAuthEntity> preApprovalFddAuthList = preApprovalFddAuthMapper.selectList(new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                //                    .eq(PreApprovalFddAuthEntity::getPreId, orderInfoEntity.getPreId())
                .eq(PreApprovalFddAuthEntity::getIdNumber, preApprovalApplyInfoEntity.getIdNumber())
                .eq(PreApprovalFddAuthEntity::getPhone, preApprovalApplyInfoEntity.getPhone())
                .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                .orderByDesc(PreApprovalFddAuthEntity::getId)
        );
        //            PreApprovalFddAuthEntity authEntity = preApprovalFddAuthMapper.selectOne(new LambdaQueryWrapper<PreApprovalFddAuthEntity>().eq(PreApprovalFddAuthEntity::getPreId, orderInfoEntity.getPreId()));
        if (CollUtil.isEmpty(preApprovalFddAuthList)) {
            throw new BusinessException("未查询到授权信息");
        }


        //获取token
        String accessToken = getFddToken();
        OpenApiClient apiClient = fadadaConfig.appClient();
        SignTaskClient signTaskClient = new SignTaskClient(apiClient);
        BaseRes<SignTaskActorGetUrlRes> urlRes = new BaseRes<>();
        try {
            //合同数量
            List<OrderContractEntity> orderContractEntityList = orderContractMapper.selectList(new LambdaQueryWrapper<OrderContractEntity>()
                    .eq(OrderContractEntity::getOrderId, contractSingUrlDTO.getOrderId())
                    .notIn(OrderContractEntity::getContractFlag, 3, 4)
                    .eq(OrderContractEntity::getDeleteFlag, 0)
            );
            log.info("FadadaAuthServiceImpl.getContractSignUrl orderId:{}, orderContractEntityList,size:{}", contractSingUrlDTO.getOrderId(), orderContractEntityList.size());
            List<OrderContractEntity> signIng = orderContractMapper.selectList(new LambdaQueryWrapper<OrderContractEntity>()
                    .eq(OrderContractEntity::getOrderId, contractSingUrlDTO.getOrderId())
                    .notIn(OrderContractEntity::getContractFlag, 3, 4)
                    .eq(OrderContractEntity::getSignStatus, 1)
                    .eq(OrderContractEntity::getDeleteFlag, 0)
            );
            log.info("FadadaAuthServiceImpl.getContractSignUrl orderId:{}, signIng.size:{}", contractSingUrlDTO.getOrderId(), signIng.size());
            if (CollUtil.isNotEmpty(orderContractEntityList) && CollUtil.isNotEmpty(signIng)) {
                List<OrderContractEntity> filteredList = orderContractEntityList.stream()
                        .filter(entity -> entity.getSignTaskId() != null && !entity.getSignTaskId().isEmpty())
                        .toList();
                //签署中状态直接获取链接处理
                List<SignTaskEntity> signTaskEntityList = signTaskMapper.selectList(new LambdaQueryWrapper<SignTaskEntity>()
                        .eq(SignTaskEntity::getBusiId, contractSingUrlDTO.getOrderId())
                        .eq(SignTaskEntity::getSignType, 1)
                        .eq(SignTaskEntity::getSignTaskId, filteredList.get(0).getSignTaskId())
                        .eq(SignTaskEntity::getDeleteFlag, 0)
                        .orderByDesc(SignTaskEntity::getId)
                );
                log.info("FadadaAuthServiceImpl.getContractSignUrl orderId:{}, signTaskEntityList:{}", contractSingUrlDTO.getOrderId(), JSONUtil.toJsonStr(signTaskEntityList));
                if (CollUtil.isNotEmpty(signTaskEntityList) && StrUtil.isEmpty(contractSingUrlDTO.getSignTaskId())) {
                    contractSingUrlDTO.setSignTaskId(signTaskEntityList.get(0).getSignTaskId());
                }
            }

            log.info("FadadaAuthServiceImpl.getContractSignUrl contractSingUrlDTO:{}", JSONUtil.toJsonStr(contractSingUrlDTO));
            if (StrUtil.isNotEmpty(contractSingUrlDTO.getSignTaskId())) {
                PreApprovalFddAuthEntity authEntity = preApprovalFddAuthList.get(0);
                SignTaskActorGetUrlReq signTaskActorGetUrlReq = new SignTaskActorGetUrlReq();
                signTaskActorGetUrlReq.setAccessToken(accessToken);
                signTaskActorGetUrlReq.setSignTaskId(contractSingUrlDTO.getSignTaskId());
                signTaskActorGetUrlReq.setActorId(authEntity.getClientUserId());
                signTaskActorGetUrlReq.setClientUserId(authEntity.getClientUserId());
                // 合同签署完成后重定向地址
                signTaskActorGetUrlReq.setRedirectUrl(hostname + "/longhuan/h5/signResult?flg=sign&signType=contract&signTaskId=" + contractSingUrlDTO.getSignTaskId());

                urlRes = signTaskClient.signTaskActorGetUrl(signTaskActorGetUrlReq);
                log.info("getContractSignUrl orderId = {}, signTaskId = {},  urlRes ={}", contractSingUrlDTO.getOrderId(), contractSingUrlDTO.getSignTaskId(), JSONUtil.toJsonStr(urlRes));
                //保存报文信息
                saveRequestLog(contractSingUrlDTO.getOrderId(), RequestResponseCode.FDD_ACTOR_SIGN_URL, "", "", JSONUtil.toJsonStr(signTaskActorGetUrlReq), JSONUtil.toJsonStr(urlRes));

                log.info("orderId={}, urlRes ={}", contractSingUrlDTO.getOrderId(), JSONUtil.toJsonStr(urlRes));
            } else {
                log.error("FadadaAuthServiceImpl.getContractSignUrl singTaskId :{}", contractSingUrlDTO.getSignTaskId());
            }
        } catch (ApiException e) {
            throw new BusinessException(e);
        }
        return urlRes;
    }

    @Override
    public boolean signTaskResult() {
        try {
            log.info("FadadaAuthServiceImpl.signTaskResult start");


            //获取token
            String accessToken = getFddToken();
            OpenApiClient apiClient = fadadaConfig.appClient();
            SignTaskClient signTaskClient = new SignTaskClient(apiClient);
            //查询没有签署状态的授权文件
            synAuthTask(signTaskClient, accessToken);


        } catch (Exception e) {
            log.info("fadadaAuthServiceImpl signTaskResult error ={}", e.getMessage(), e);
        }
        return true;
    }

    /**
     * 批量更新身份验证状态
     *
     * @return boolean
     */
    @Override
    public boolean batchUpdateAuthStatus() {
        log.info("FadadaAuthServiceImpl.batchUpdateAuthStatus start");
        List<PreApprovalFddAuthEntity> authEntityList = preApprovalFddAuthMapper.selectList(new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                .isNull(PreApprovalFddAuthEntity::getAuthResult)
                .ge(PreApprovalFddAuthEntity::getUpdateTime, LocalDateTime.now().minusDays(7))
                .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                .orderByDesc(PreApprovalFddAuthEntity::getId));
        log.info("FadadaAuthServiceImpl.batchUpdateAuthStatus authEntityList.size ={}", authEntityList.size());

        if (CollUtil.isEmpty(authEntityList)) {
            return true;
        }

        //获取token

        for (PreApprovalFddAuthEntity authEntity : authEntityList) {
            try {
                UserRes userRes = getUserAuthStatus(authEntity.getClientUserId());

                if (userRes == null) {
                    log.info("FadadaAuthServiceImpl.batchUpdateAuthStatus userRes is null");
                    continue;
                }
                ;

                log.info("batchUpdateAuthStatus clientUserId={},userRes ={}", authEntity.getClientUserId(), JSONUtil.toJsonStr(userRes));
                //已授权且已实名认证状态
                if (FadadaEnum.AUTHORIZED.getCode().equals(userRes.getBindingStatus()) &&
                        FadadaEnum.IDENTIFIED.getCode().equals(userRes.getIdentStatus())) {
                    PreApprovalFddAuthEntity updateEntity = new PreApprovalFddAuthEntity();
                    updateEntity.setOpenUserId(userRes.getOpenUserId())
                            .setAuthScopes(userRes.getAuthScope().toString())
                            .setAuthResult("success");
                    int updateCount = preApprovalFddAuthMapper.update(updateEntity, new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                            .eq(PreApprovalFddAuthEntity::getClientUserId, userRes.getClientUserId()));

                    //获取刷脸底图
                    SignTaskDetailDTO signTaskDetailDTO = new SignTaskDetailDTO();
                    signTaskDetailDTO.setPreId(authEntity.getPreId());
                    List<PreFddSignTaskEntity> signTaskEntityList = preFddSignTaskMapper.selectList(new LambdaQueryWrapper<PreFddSignTaskEntity>()
                            .eq(PreFddSignTaskEntity::getPreId, authEntity.getPreId())
                            .eq(PreFddSignTaskEntity::getDeleteFlag, 0)
                            .orderByDesc(PreFddSignTaskEntity::getId));
                    if (ObjectUtil.isNotEmpty(signTaskEntityList)) {
                        signTaskDetailDTO.setSignTaskId(signTaskEntityList.get(0).getSignTaskId());
                    }

                    getSignTaskFacePicture(signTaskDetailDTO, authEntity.getClientUserId());

                    if (updateCount > 0 && ObjUtil.isNotEmpty(authEntity.getPreId())) {
                        log.info("batchUpdateAuthStatus preId = {}", authEntity.getPreId());
                        preFddSignTaskMapper.update(new LambdaUpdateWrapper<PreFddSignTaskEntity>()
                                .set(PreFddSignTaskEntity::getClientUserId, authEntity.getClientUserId())
                                .eq(PreFddSignTaskEntity::getPreId, authEntity.getPreId()));
                    }
                }


            } catch (Exception e) {
                log.error("batchUpdateAuthStatus error ={}", e.getMessage(), e);
            }
        }

        return true;
    }

    @Override
    public BaseRes<UserRes> getAuthStatusByClientUserId(String clientUserId) {
        log.info("getAuthStatusByClientUserId clientUserId ={}", clientUserId);

        //获取token
        String accessToken = getFddToken();
        BaseRes<UserRes> userResBaseRes = null;
        try {
            OpenApiClient apiClient = fadadaConfig.appClient();
            UserClient userClient = new UserClient(apiClient);
            GetUserReq getUserReq = new GetUserReq();
            getUserReq.setAccessToken(accessToken);
            getUserReq.setClientUserId(clientUserId);
            userResBaseRes = userClient.get(getUserReq);
            log.info("getAuthStatusByClientUserId userResBaseRes.getCode() ={}", userResBaseRes.getCode());
            if (FadadaEnum.SUCCESS.getCode().equals(userResBaseRes.getCode())) {
                log.info("getAuthStatusByClientUserId clientUserId={},userResBaseRes ={}", clientUserId, JSONUtil.toJsonStr(userResBaseRes));


            }
        } catch (ApiException e) {
            log.error("getAuthStatusByClientUserId error ={}", e.getMessage(), e);
        }

        return userResBaseRes;
    }

    @Override
    public boolean retryDownloadFile() {
        List<PreFddSignTaskEntity> fddSignTaskEntityList = preFddSignTaskMapper.selectList(new LambdaQueryWrapper<PreFddSignTaskEntity>()
                .eq(PreFddSignTaskEntity::getDeleteFlag, 0)
                .eq(PreFddSignTaskEntity::getSignTaskStatus, SignTaskStatusEnums.TASK_FINISHED.getCode())
                .orderByDesc(PreFddSignTaskEntity::getId)
        );
        if (CollUtil.isNotEmpty(fddSignTaskEntityList)) {

            for (PreFddSignTaskEntity fddSignTaskEntity : fddSignTaskEntityList) {
                //是否已成功下载文件
                String signTaskId = fddSignTaskEntity.getSignTaskId();
                List<PreFddFinishFileEntity> preFddFinishFileList = preFddFinishFileMapper.selectList(new LambdaQueryWrapper<PreFddFinishFileEntity>()
                        .eq(PreFddFinishFileEntity::getSignTaskId, signTaskId)
                );
                if (CollUtil.isEmpty(preFddFinishFileList)) {

                    String customName = signTaskId + "_授权文件";

                    signTaskDownloadFile(fddSignTaskEntity.getPreId(), signTaskId, customName,
                            ContractEnum.AUTHORIZATION.getCode());
                }


            }

        }

        return true;
    }

    private List<DownZipFileVO> downloadFileAndUnZip(Integer bizId, String downloadUrl, String signTaskId,
                                                     String customName, Integer signType) {
        log.info("FadadaAuthServiceImpl.downloadFileAndUnZip start");
        SignZipDTO signZipDTO = new SignZipDTO();
        signZipDTO.setFddUrl(downloadUrl);
        signZipDTO.setSignTaskId(signTaskId);
        signZipDTO.setPreId(bizId);
        signZipDTO.setZipName(customName);
        //增加文件签署用途-此方法用于授权书下载
        signZipDTO.setSignType(signType);
        Result<List<DownZipFileVO>> resourceList = resourceFeign.downLoadSignZip(signZipDTO);
        return resourceList.getData();
    }

    private List<DownZipFileVO> downloadFileAndUnZipAuthorization(Integer bizId, String downloadUrl, String signTaskId, String customName) {

        return downloadFileAndUnZip(bizId, downloadUrl, signTaskId, customName, ContractEnum.AUTHORIZATION.getCode());
    }

    @Override
    public boolean contractSignTaskResult() {
        log.info("FadadaAuthServiceImpl.contractSignTaskResult start");
        try {


            //获取token
            String accessToken = getFddToken();
            OpenApiClient apiClient = fadadaConfig.appClient();
            SignTaskClient signTaskClient = new SignTaskClient(apiClient);
            //查询没有签署状态的签署合同任务
            synContractTask(signTaskClient, accessToken);

        } catch (Exception e) {
            log.info("fadadaAuthServiceImpl signTaskResult error ={}", e.getMessage(), e);
        }

        return true;
    }

    @Override
    public boolean deleteSignTask() {
        //查新预审签署任务超过3天依然无签署完结状态的数据
        List<SignTaskVO> invalidInfoList = preFddSignTaskMapper.getInvalidInfo();
        if (CollUtil.isNotEmpty(invalidInfoList)) {
            List<Integer> preIds = invalidInfoList.stream().map(SignTaskVO::getPreId).toList();
            log.info("fadadaAuthServiceImpl.deleteSignTask preIds.size() = {}", preIds.size());
            PreFddSignTaskEntity preFddSignTaskEntity = new PreFddSignTaskEntity();
            preFddSignTaskEntity.setDeleteFlag(1);
            preFddSignTaskMapper.update(preFddSignTaskEntity, new LambdaUpdateWrapper<PreFddSignTaskEntity>()
                    .in(PreFddSignTaskEntity::getPreId, preIds));
        }
        return true;
    }

    /**
     * 合同文件签署
     *
     * @return
     */
    @Override
    public boolean syncFddContractFile() {
        log.info("FadadaAuthServiceImpl.syncFddContractFile start");
        //查询签署完成任务信息
        List<SignTaskEntity> signTaskList = signTaskMapper.selectJoinList(
                SignTaskEntity.class,
                new MPJLambdaWrapper<SignTaskEntity>()
                        .leftJoin(PreFddFinishFileEntity.class, PreFddFinishFileEntity::getSignTaskId, SignTaskEntity::getSignTaskId)
                        .eq(SignTaskEntity::getSignTaskStatus, SignTaskStatusEnums.TASK_FINISHED.getCode())
                        .isNull(PreFddFinishFileEntity::getSignTaskId)
                        .orderByAsc(SignTaskEntity::getCreateTime)
        );
        if (CollUtil.isEmpty(signTaskList)) {
            return true;
        }

        //下载文件
        for (SignTaskEntity signTaskEntity : signTaskList) {
            String signTaskId = signTaskEntity.getSignTaskId();
            String customName = signTaskId + "_合同签约";

//            signTaskDownloadFile(signTaskEntity.getBusiId(), signTaskId, customName,
//                    ContractEnum.CONTRACT.getCode());
            signTaskDownloadFile(signTaskEntity.getBusiId(), signTaskId, customName,
                    signTaskEntity.getSignType());

        }

        return true;
    }

    /**
     * 签名任务下载文件
     *
     * @param busiId     对任务实体进行签名
     * @param signTaskId 对任务 ID 进行签名
     * @param customName 自定义名称
     */
    private void signTaskDownloadFile(Integer busiId, String signTaskId, String customName,
                                      Integer signType) {

        log.info("signTaskDownloadFile start signTaskId:{} customName:{} signType:{}", signTaskId, customName, signType);
        String singleFileDownloadUrl = getSingleFileDownloadUrl(signTaskId, customName);

        if (StrUtil.isEmpty(singleFileDownloadUrl)) {
            log.error("signTaskDownloadFile signTaskId:{} singleFileDownloadUrl is null", signTaskId);
            return;
        }


        List<DownZipFileVO> resourceList = downloadFileAndUnZip(busiId,
                singleFileDownloadUrl, signTaskId, customName, signType);
        log.info("signTaskDownloadFile resourceList.size() = {}", resourceList.size());
        saveBatchFinishFile(resourceList, busiId, signTaskId, signType);
    }

    public String getGenerateContractLockKey(Integer orderId) {
        return "order:contract:generate:" + orderId;
    }

    @Override
    public void batchUploadFileToFdd(List<String> resourceIdList) {
        log.info("batchUploadFileToFdd resourceIdList.size() = {}", resourceIdList.size());
        List<FileTOFddVO> fileTOFddVOS = resourceIdList.parallelStream().map(resourceId ->
                new FileTOFddVO().setFileBelong(999).setFileUid(resourceId).setFileBytes(resourceFeign.fileContent(resourceId).getBody())).toList();
        log.info("fileTOFddVOS.size() = {}", fileTOFddVOS.size());

        uploadFile(fileTOFddVOS);

        // 计算需要多少个批次
        int numberOfBatches = (int) Math.ceil((double) resourceIdList.size() / 10);
        log.info("计算需要多少个批次: {}", numberOfBatches);

        List<List<String>> batchList = new ArrayList<>();
        for (int i = 0; i < numberOfBatches; i++) {
            // 计算当前批次的起始和结束索引
            int start = i * 10;
            int end = Math.min(start + 10, resourceIdList.size());

            // 获取当前批次的数据
            List<String> currentBatch = resourceIdList.subList(start, end);

            // 对当前批次的数据进行处理，这里只是打印作为示例
            batchList.add(currentBatch);

        }
        //进行文件处理获得法大大平台文件id
        batchList.parallelStream().forEach(this::processFile);

    }

    /**
     * 获取订单客户参与者 ID
     *
     * @param orderId 订单 ID
     * @return {@link String }
     */
    @Override
    public ActorsInfoDTO getOrderCustomerActorId(Integer orderId) {
        List<ActorsInfoDTO> actorsInfoDTOList = orderInfoMapper.selectJoinList(ActorsInfoDTO.class,
                new MPJLambdaWrapper<OrderInfoEntity>()
                        .selectAs(OrderInfoEntity::getCustomerName, ActorsInfoDTO::getActorName)
                        .selectAs(OrderInfoEntity::getCustomerPhone, ActorsInfoDTO::getAccountName)
                        .selectAs(PreApprovalFddAuthEntity::getClientUserId, ActorsInfoDTO::getActorId)
                        .innerJoin(OrderCustomerInfoEntity.class, on -> on.eq(OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId))
                        .innerJoin(PreApprovalFddAuthEntity.class, on -> on
                                .eq(PreApprovalFddAuthEntity::getIdNumber, OrderCustomerInfoEntity::getIdNumber)
                                .eq(PreApprovalFddAuthEntity::getPhone, OrderCustomerInfoEntity::getPhone)
                                .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                                .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                        )
                        .eq(OrderInfoEntity::getId, orderId)
                        .orderByDesc(PreApprovalFddAuthEntity::getCreateTime)
        );

        if (CollUtil.isNotEmpty(actorsInfoDTOList)) {
            return actorsInfoDTOList.get(0);
        }
        throw new BusinessException("未查询到用户授权信息");
    }

    @Override
    public String createCorpSignTask(ContractTaskDTO contractTaskDTO, List<FddFileResourceVO> resourceVOList) {
        //获取token
        String accessToken = getFddToken();
        OpenApiClient apiClient = fadadaConfig.appClient();
        SignTaskClient signTaskClient = new SignTaskClient(apiClient);
        CreateSignTaskReq signTaskReq = new CreateSignTaskReq();
        signTaskReq.setAccessToken(accessToken);

        OpenId openId = new OpenId();
        openId.setIdType("corp");
        openId.setOpenId(fadadaConfig.getOppenCorpId());
        //扣费主体
        signTaskReq.setInitiator(openId);
        //签署任务主题
        signTaskReq.setSignTaskSubject(contractTaskDTO.getOrderId() + "_回购合同");
        signTaskReq.setBusinessId(fadadaConfig.getBusinessId());
        //自动提交签署任务
//        signTaskReq.setAutoStart(true);
        log.info("FadadaAuthServiceImpl.createCorpSignTask orderId = {},resourceVOList.size = {}", contractTaskDTO.getOrderId(), resourceVOList.size());
        if (CollUtil.isEmpty(resourceVOList)) {
            log.info("FadadaAuthServiceImpl.createCorpSignTask resourceVOList is empty");
        }

        // 文件信息
        List<AddDocInfo> docs = resourceVOList.stream().map(vo -> {
            AddDocInfo docInfo = new AddDocInfo();
            docInfo.setDocId(vo.getResourceFileUid());
            docInfo.setDocName(vo.getNumber().split("_")[2] + "_" + vo.getFileName().split("\\.")[0]);
            docInfo.setDocFileId(vo.getFddFileId());
            //文档中添加控价
            docInfo.setDocFields(getDocFieldList(vo, 1));
            return docInfo;
        }).toList();
        signTaskReq.setDocs(docs);
        //参与方信息
        //1、指定企业参与方
        List<String> openCorpIdList = new ArrayList<>();
        if (!envUtil.isPrd()) {
            openCorpIdList.add("d470597d38f2449996ece26d44955b4b");
        }
        openCorpIdList.add("92ab4c7e8d594220904c903f6e4e86af");
        List<AddActorsInfo> corp = handleTheCorp(docs, openCorpIdList);
        signTaskReq.setActors(corp);
        log.info("FadadaAuthServiceImpl.createCorpSignTask orderId = {}, signTaskReq ={}", contractTaskDTO.getOrderId(), JSONUtil.toJsonStr(signTaskReq));

        BaseRes<CreateSignTaskRes> signTaskRes;
        try {
            signTaskRes = signTaskClient.create(signTaskReq);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
        log.info("FadadaAuthServiceImpl.createCorpSignTask orderId = {}, signTaskRes ={}", contractTaskDTO.getOrderId(), JSONUtil.toJsonStr(signTaskRes));
        //保存报文信息
        saveRequestLog(contractTaskDTO.getOrderId(), RequestResponseCode.FDD_CREATE_SIGN_TASK, "", "", JSONUtil.toJsonStr(signTaskReq), JSONUtil.toJsonStr(signTaskRes));
        if (!FadadaEnum.SUCCESS.getCode().equals(signTaskRes.getCode())) {
            log.error("FadadaAuthServiceImpl.createCorpSignTask error:{}", signTaskRes.getMsg());
        }

        //提交签署任务
        BaseRes<Void> submitSignTaskRes = submitSignTask(signTaskRes.getData().getSignTaskId(), signTaskClient, contractTaskDTO.getOrderId());
        if (!FadadaEnum.SUCCESS.getCode().equals(submitSignTaskRes.getCode())) {
            throw new BusinessException(submitSignTaskRes.getMsg());
        }
        //添加签署任务数据
        initSignTask(contractTaskDTO.getOrderId(), signTaskRes.getData().getSignTaskId(), 3);


        return signTaskRes.getData().getSignTaskId();
    }

    @Override
    public ResponseEntity<byte[]> getSignAuthQrCode(UserAuthReqDTO userAuthReqDTO) {
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(userAuthReqDTO.getPreId());
        PreApprovalFddAuthEntity preApprovalFddAuthEntity = preApprovalFddAuthMapper.selectOne(new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                .eq(PreApprovalFddAuthEntity::getPhone, preApprovalApplyInfoEntity.getPhone())
                .eq(PreApprovalFddAuthEntity::getIdNumber, preApprovalApplyInfoEntity.getIdNumber())
                .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                .orderByDesc(PreApprovalFddAuthEntity::getCreateTime)
                .last("limit 1")
        );
        if (preApprovalFddAuthEntity != null) {
            SignTaskDTO signTaskDTO = new SignTaskDTO();
            signTaskDTO.setPreId(userAuthReqDTO.getPreId()).setClientUserId(preApprovalFddAuthEntity.getClientUserId());
            BaseRes<CreateSignTaskRes> createSignTaskResBaseRes = createSignTask(signTaskDTO);
            ActorSingUrlDTO actorSingUrlDTO = new ActorSingUrlDTO();
            actorSingUrlDTO.setClientUserId(preApprovalFddAuthEntity.getClientUserId());
            actorSingUrlDTO.setPreId(userAuthReqDTO.getPreId());
            actorSingUrlDTO.setSignTaskId(createSignTaskResBaseRes.getData().getSignTaskId());
            BaseRes<SignTaskActorGetUrlRes> getActorSignUrlRes = getActorSignUrl(actorSingUrlDTO);
            if (FadadaEnum.SUCCESS.getCode().equals(getActorSignUrlRes.getCode())) {

                byte[] imageBytes;
                try {
                    // 通用验证
                    org.springframework.util.Assert.notNull(userAuthReqDTO.getPreId(), "预审id不能为空");

                    // 设置header
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.IMAGE_PNG);
                    headers.setContentDisposition(ContentDisposition.builder("attachment")
                            .filename(userAuthReqDTO.getPreId() + ".png")
                            .build());
                    // 路径配置
//            String url = "https://sit.baicaiec.com/longhuan/h5/transferPage/perId=%s&path=applicationSubmissionPage";
                    String url = getActorSignUrlRes.getData().getActorSignTaskEmbedUrl();
                    url = String.format(url, userAuthReqDTO.getPreId());
                    String content = "客户姓名：" + preApprovalApplyInfoEntity.getName();
                    url = orderSendMessageImpl.replaceUrl(url);

                    // 返回响应实体
                    imageBytes = QrCodeUtils.generateQrCode(content, url);
                    return new ResponseEntity<>(imageBytes, headers, HttpStatus.OK);
                } catch (Exception e) {
                    log.error("ApprovalServiceImpl.userToAuth e:", e);
                    throw new BusinessException(e);
                }

            }
        }

        return null;
    }

    @Override
    public UserAuthUrlVO agentAuthUrl(AgentAuthReqDTO userAuthReqDTO) {
        BaseRes<EUrlRes> eUrlResBaseRes;
        PreApprovalFddAuthEntity preApprovalFddAuthEntity = preApprovalFddAuthMapper.selectOne(new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                .eq(PreApprovalFddAuthEntity::getPhone, userAuthReqDTO.getUserPhone())
                .eq(PreApprovalFddAuthEntity::getIdNumber, userAuthReqDTO.getUserIdNUmber())
                .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                .orderByDesc(PreApprovalFddAuthEntity::getCreateTime)
                .last("LIMIT 1")
        );

        if (ObjUtil.isNotEmpty(preApprovalFddAuthEntity)) {
            throw new BusinessException("用户已授权");
        }
        String clientUserId = FadadaUtils.generateUserId();

        try {

            //获取token
            String accessToken = getFddToken();
            OpenApiClient apiClient = fadadaConfig.appClient();
            UserClient userClient = new UserClient(apiClient);
            GetUserAuthUrlReq fadadaAuth = new GetUserAuthUrlReq();
            fadadaAuth.setAccessToken(accessToken);
            //重定向地址
//            fadadaAuth.setRedirectUrl("");
            //用户唯一标识
            fadadaAuth.setClientUserId(clientUserId);

            fadadaAuth.setAccountName(userAuthReqDTO.getUserPhone());
            fadadaAuth.setAuthScopes(FadadaEnum.getAllAuthScope());

            UserIdentInfoReq userIdentInfoReq = new UserIdentInfoReq();
            log.info("getFddUserAuthUrl env ={}", envUtil.isPrd());
            userIdentInfoReq.setIdentMethod(List.of("face", "mobile"));
            if (envUtil.isPrd()) {
                //用户实名认证方式，默认人脸, 测试环境可选
                userIdentInfoReq.setIdentMethod(List.of("face"));
            }
            userIdentInfoReq.setUserName(userAuthReqDTO.getUserName());
            userIdentInfoReq.setUserIdentType("id_card");
            userIdentInfoReq.setUserIdentNo(userAuthReqDTO.getUserIdNUmber());
            userIdentInfoReq.setMobile(userAuthReqDTO.getUserPhone());
            fadadaAuth.setUserIdentInfo(userIdentInfoReq);
            //页面中不可编辑的个人信息
            fadadaAuth.setNonEditableInfo(Arrays.asList("accountName", "userName", "userIdentNo", "mobile", "userIdentType"));

            log.info("agentAuthUrl.fadadaAuth = {}", JSONUtil.toJsonStr(fadadaAuth));

            eUrlResBaseRes = userClient.getUserAuthUrl(fadadaAuth);
//            saveRequestLog(preId, RequestResponseCode.FDD_AUTH_URL, "", "", JSONUtil.toJsonStr(fadadaAuth), JSONUtil.toJsonStr(eUrlResBaseRes));
            if (!FadadaEnum.SUCCESS.getCode().equals(eUrlResBaseRes.getCode())) {
                throw new BusinessException(eUrlResBaseRes.getMsg());
            }
            log.info("agentAuthUrl.eUrlResBaseRes = {}", JSONUtil.toJsonStr(eUrlResBaseRes));
            //保存数据
            initFadadaAuth(fadadaAuth, null, userAuthReqDTO.getUserIdNUmber(), userAuthReqDTO.getUserPhone());

        } catch (Exception e) {
            throw new BusinessException(e);
        }

        return new UserAuthUrlVO().setAuthShortUrl(eUrlResBaseRes.getData().getAuthShortUrl()).setClientUserId(clientUserId);
    }

    @Override
    public FreeSignUrlVO userFreeSignUrl(AgentAuthReqDTO userAuthReqDTO) {
        FreeSignUrlVO freeSignUrlVO = new FreeSignUrlVO();
        PreApprovalFddAuthEntity preApprovalFddAuthEntity = preApprovalFddAuthMapper.selectOne(new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                .eq(PreApprovalFddAuthEntity::getPhone, userAuthReqDTO.getUserPhone())
                .eq(PreApprovalFddAuthEntity::getIdNumber, userAuthReqDTO.getUserIdNUmber())
                .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
        );
        Assert.notNull(preApprovalFddAuthEntity, "代理人不存在");
        //获取token
        String accessToken = getFddToken();
        OpenApiClient apiClient = fadadaConfig.appClient();
        //个人签名列表
        SealClient sealClient = new SealClient(apiClient);
        GetPersonalSealListReq getPersonalSealListReq = new GetPersonalSealListReq();
        getPersonalSealListReq.setOpenUserId(preApprovalFddAuthEntity.getOpenUserId());
        getPersonalSealListReq.setAccessToken(accessToken);

        try {
            BaseRes<GetPersonalSealListRes> personalSealList = sealClient.getPersonalSealList(getPersonalSealListReq);
            log.info("userFreeSignUrl：personalSealList = {}", JSONUtil.toJsonStr(personalSealList));


            if (personalSealList.getCode().equals(FadadaEnum.SUCCESS.getCode())) {
                List<PersonalSealInfo> sealInfos = personalSealList.getData().getSealInfos();
                sealInfos.forEach(personalSealInfo -> {
                    if (Objects.equals(personalSealInfo.getCreateMethod(), "standard")) {
                        Long sealId = personalSealInfo.getSealId();
                        GetPersonalSealFreeSignUrlReq freeSignUrlReq = new GetPersonalSealFreeSignUrlReq();
                        freeSignUrlReq.setSealIds(Collections.singletonList(sealId));
                        freeSignUrlReq.setOpenUserId(preApprovalFddAuthEntity.getOpenUserId());
                        freeSignUrlReq.setBusinessId(fadadaConfig.getBusinessId());
                        freeSignUrlReq.setAccessToken(accessToken);
                        BaseRes<GetSealFreeSignUrlRes> sealFreeSignUrl = null;
                        try {
                            sealFreeSignUrl = sealClient.getPersonalSealFreeSignUrl(freeSignUrlReq);
                            log.info("userFreeSignUrl：sealFreeSignUrl = {}", JSONUtil.toJsonStr(sealFreeSignUrl));
                            if (sealFreeSignUrl.getCode().equals(FadadaEnum.SUCCESS.getCode())) {
                                freeSignUrlVO.setFreeSignShortUrl(sealFreeSignUrl.getData().getFreeSignShortUrl());
                            }
                        } catch (ApiException e) {
                            throw new RuntimeException(e);
                        }

                    }
                });

            }
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }

        return freeSignUrlVO;
    }

    @Override
    public Boolean userSynFreeSign(AgentAuthReqDTO userAuthReqDTO) {
        PreApprovalFddAuthEntity preApprovalFddAuthEntity = preApprovalFddAuthMapper.selectOne(new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                .eq(PreApprovalFddAuthEntity::getPhone, userAuthReqDTO.getUserPhone())
                .eq(PreApprovalFddAuthEntity::getIdNumber, userAuthReqDTO.getUserIdNUmber())
                .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
        );
        Assert.notNull(preApprovalFddAuthEntity, "代理人不存在");
        //获取token
        String accessToken = getFddToken();
        OpenApiClient apiClient = fadadaConfig.appClient();
        //个人签名列表
        SealClient sealClient = new SealClient(apiClient);
        GetPersonalSealListReq getPersonalSealListReq = new GetPersonalSealListReq();
        getPersonalSealListReq.setOpenUserId(preApprovalFddAuthEntity.getOpenUserId());
        getPersonalSealListReq.setAccessToken(accessToken);

        try {
            BaseRes<GetPersonalSealListRes> personalSealList = sealClient.getPersonalSealList(getPersonalSealListReq);
            log.info("userSynFreeSign：personalSealList = {}", JSONUtil.toJsonStr(personalSealList));


            if (personalSealList.getCode().equals(FadadaEnum.SUCCESS.getCode())) {
                List<PersonalSealInfo> sealInfos = personalSealList.getData().getSealInfos();
                for (PersonalSealInfo personalSealInfo : sealInfos) {
                    List<FreeSignInfo> freeSignInfos = personalSealInfo.getFreeSignInfos();
                    if (CollUtil.isNotEmpty(freeSignInfos)) {
                        boolean flag = true;
                        for (FreeSignInfo freeSignInfo : freeSignInfos) {
                            //已免验签证 更新抵押权人状态
                            if (freeSignInfo == null || StringUtils.isEmpty(freeSignInfo.getExpiresTime())) {
                                flag = false;
                                break;
                            }
                        }
                        if (flag) {
                            ArrivedDataEntity arrivedDataEntity = new ArrivedDataEntity();
                            arrivedDataEntity.setAuthSign(1);
                            arrivedDataMapper.update(arrivedDataEntity, new LambdaQueryWrapper<ArrivedDataEntity>()
                                    .eq(ArrivedDataEntity::getMortgageAgentIdNumber, userAuthReqDTO.getUserIdNUmber()));
                            return true;
                        }
                    }
                }
            }
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
        return false;
    }

    @Override
    public Boolean authStatusSynByClientUserId(String idNumber, String phone) {
        PreApprovalFddAuthEntity preApprovalFddAuthEntity = preApprovalFddAuthMapper.selectOne(new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                .eq(PreApprovalFddAuthEntity::getIdNumber, idNumber)
                .eq(PreApprovalFddAuthEntity::getPhone, phone)
                .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                .orderByDesc(PreApprovalFddAuthEntity::getCreateTime)
                .last("LIMIT 1"));
        if (preApprovalFddAuthEntity != null && StrUtil.isNotEmpty(preApprovalFddAuthEntity.getClientUserId())) {
            String clientUserId = preApprovalFddAuthEntity.getClientUserId();
            log.info("authStatusSynByClientUserId clientUserId ={}", clientUserId);

            //获取token
            String accessToken = getFddToken();
            try {
                OpenApiClient apiClient = fadadaConfig.appClient();
                UserClient userClient = new UserClient(apiClient);
                GetUserReq getUserReq = new GetUserReq();
                getUserReq.setAccessToken(accessToken);
                getUserReq.setClientUserId(clientUserId);
                BaseRes<UserRes> userResBaseRes = userClient.get(getUserReq);
                log.info("authStatusSynByClientUserId userResBaseRes {}", userResBaseRes);
                log.info("authStatusSynByClientUserId userResBaseRes.getCode() ={}", userResBaseRes.getCode());
                if (FadadaEnum.SUCCESS.getCode().equals(userResBaseRes.getCode())) {
                    log.info("authStatusSynByClientUserId clientUserId={},userResBaseRes ={}", clientUserId, JSONUtil.toJsonStr(userResBaseRes));

                    //更新lh_arrived_data实名认证状态
                    if (FadadaEnum.IDENTIFIED.getCode().equals(userResBaseRes.getData().getIdentStatus())) {
                        ArrivedDataEntity arrivedDataEntity = new ArrivedDataEntity();
                        arrivedDataEntity.setRealSign(1);
                        arrivedDataMapper.update(arrivedDataEntity, new LambdaQueryWrapper<ArrivedDataEntity>()
                                .eq(ArrivedDataEntity::getMortgageAgentIdNumber, preApprovalFddAuthEntity.getIdNumber()));
                        return true;
                    }
                }
            } catch (ApiException e) {
                log.error("getAuthStatusByClientUserId error ={}", e.getMessage(), e);
            }
        }
        return false;
    }

    @Override
    public BaseRes<CreateSignTaskRes> freeContractSignTask(ContractTaskDTO contractTaskDTO) {
        Integer orderId = contractTaskDTO.getOrderId();
        log.info("freeContractSignTask orderId:{}", orderId);
        String contractLockKey = getGenerateContractLockKey(orderId);
        String requestId = IdUtil.randomUUID();

        if (!redisService.tryLock(contractLockKey, requestId, 60)) {
            log.info("freeContractSignTask orderId:{} contractLockKey lock failed", orderId);
            throw new BusinessException("合同未生成完成，请稍后再试");
        }


        // 0. 检查是否存在未签署完成的合同
        SignTaskEntity signTaskEntity = busiSignTaskMapper.selectJoinOne(
                SignTaskEntity.class,
                new MPJLambdaWrapper<SignTaskEntity>()
                        .selectAll(SignTaskEntity.class)
                        .innerJoin(OrderContractEntity.class, on -> on
                                .eq(OrderContractEntity::getOrderId, SignTaskEntity::getBusiId)
                                .eq(SignTaskEntity::getSignType, 1)
                                .eq(OrderContractEntity::getDeleteFlag, 0)
                                .eq(OrderContractEntity::getContractFlag, ObjUtil.equals(1, contractTaskDTO.getType()) ? 3 : 4))
                        .eq(SignTaskEntity::getBusiId, orderId)
                        .ne(SignTaskEntity::getSignTaskStatus, SignTaskStatusEnums.TASK_FINISHED.getCode())
                        .eq(SignTaskEntity::getDeleteFlag, 0)
                        .orderByDesc(SignTaskEntity::getId)
                        .last("limit 1"));

        if (ObjUtil.isNotEmpty(signTaskEntity)) {
            log.info("freeContractSignTask orderId:{} exists unsigned contracts signTaskId:{}", orderId, signTaskEntity.getSignTaskId());
            redisService.releaseLock(contractLockKey, requestId);
            return buildCreateSignTaskResBaseRes(signTaskEntity.getSignTaskId());
        }

        BaseRes<CreateSignTaskRes> signTaskRes;
        try {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                    .select(OrderInfoEntity::getPreId, OrderInfoEntity::getId)
                    .eq(OrderInfoEntity::getId, orderId));
            if (ObjUtil.isEmpty(orderInfoEntity)) {
                throw new BusinessException("订单不存在");
            }
            // 1. 创建新签署任务
            // 1.1 查询要签署的文件
            List<FddFileResourceVO> resourceVOList = orderContractMapper.selectJoinList(
                    FddFileResourceVO.class,
                    new MPJLambdaWrapper<OrderContractEntity>()
                            .selectAs(FileToFddEntity::getResourceFileUid, FddFileResourceVO::getResourceFileUid)
                            .selectAs(FileToFddEntity::getFddFileId, FddFileResourceVO::getFddFileId)
                            .selectAs(OrderContractEntity::getName, FddFileResourceVO::getFileName)
                            .selectAs(OrderContractEntity::getTemplateId, FddFileResourceVO::getTemplateId)
                            .selectAs(OrderContractEntity::getNumber, FddFileResourceVO::getNumber)
                            .innerJoin(FileResourceEntity.class, FileResourceEntity::getFileUid, OrderContractEntity::getResource)
                            .innerJoin(FileToFddEntity.class, on -> on
                                    .eq(OrderContractEntity::getResource, FileToFddEntity::getResourceFileUid)
                                    .eq(FileToFddEntity::getDeleteFlag, 0)
                                    .isNotNull(FileToFddEntity::getFddFileId))
                            .eq(OrderContractEntity::getDeleteFlag, 0)
                            .eq(OrderContractEntity::getContractFlag, ObjUtil.equals(1, contractTaskDTO.getType()) ? 3 : 4)
                            .in(OrderContractEntity::getOrderId, orderId)
                            .orderByAsc(FileToFddEntity::getId)
            );

            CreateSignTaskReq signTaskReq = new CreateSignTaskReq();


            OpenId openId = new OpenId();
            openId.setIdType("corp");
            openId.setOpenId(fadadaConfig.getOppenCorpId());
            //扣费主体
            signTaskReq.setInitiator(openId);
            //签署任务主题
            signTaskReq.setSignTaskSubject("合同签约");
            signTaskReq.setBusinessId(fadadaConfig.getBusinessId());

            if (CollUtil.isEmpty(resourceVOList)) {
                throw new BusinessException("未查询到合同信息");
            }

            // 文件信息
            List<AddDocInfo> docs = buildAddDocInfoList(resourceVOList);

            signTaskReq.setDocs(docs);

            List<AddActorsInfo> freeSignPerson = handleFreeSign(contractTaskDTO, docs);


            List<AddActorsInfo> actorsList = new ArrayList<>(freeSignPerson);

            //个人参与方信息
            List<AddActorsInfo> person = handlePersonActors(contractTaskDTO, docs);
            actorsList.addAll(person);

            signTaskReq.setActors(actorsList);
            log.info("freeContractSignTask.orderId = {},signTaskReq = {}", orderId, JSONUtil.toJsonStr(signTaskReq));

            //获取token
            String accessToken = getFddToken();
            OpenApiClient apiClient = fadadaConfig.appClient();
            SignTaskClient signTaskClient = new SignTaskClient(apiClient);
            signTaskReq.setAccessToken(accessToken);

            signTaskRes = signTaskClient.create(signTaskReq);
            log.info("freeContractSignTask.orderId = {}, signTaskRes ={}", orderId, JSONUtil.toJsonStr(signTaskRes));
            //保存报文信息
            saveRequestLog(orderId, RequestResponseCode.FDD_CREATE_SIGN_TASK, "", "", JSONUtil.toJsonStr(signTaskReq), JSONUtil.toJsonStr(signTaskRes));

            if (!FadadaEnum.SUCCESS.getCode().equals(signTaskRes.getCode())) {
                throw new BusinessException(signTaskRes.getMsg());
            }

            String signTaskId = signTaskRes.getData().getSignTaskId();
            // 合同文件标记签署任务Id
            addOrderContractSignTaskId(signTaskId, orderId, resourceVOList);


            //提交签署任务
            BaseRes<Void> submitSignTaskRes = submitSignTask(signTaskId, signTaskClient, orderId);
            if (!FadadaEnum.SUCCESS.getCode().equals(submitSignTaskRes.getCode())) {
                throw new BusinessException(submitSignTaskRes.getMsg());
            }
            //添加签署任务数据
            initSignTask(orderId, signTaskId, 1);


        } catch (ApiException e) {
            log.info("ContractRestartServiceImpl.freeContractSignTask error ={}", e.getMessage());
            throw new BusinessException(e);
        } finally {
            redisService.releaseLock(contractLockKey, requestId);
        }
        return signTaskRes;
    }

    @Override
    public FreeLoginVO fddFreeLogin(SignTaskDTO signTaskDTO) {
        String lockKey = "pre:fdd:freeSignTask:" + signTaskDTO.getPreId();
        String requestId = "fddFreeLogin";
        Boolean tryLock = redisService.tryLock(lockKey, requestId, 50);
        if (!tryLock) {
            log.info("ContractServiceImpl.fddFreeLogin, lock failed, preId = {}", lockKey);
            throw new BusinessException("您已创建授权书");
        }

        FreeLoginVO freeLoginVO = new FreeLoginVO();
        Integer preId = signTaskDTO.getPreId();
        //1、是否已完成实名认证且数据已存在
        PreApprovalFddAuthEntity authEntity = freeLoginUserInfo(preId);

        //2. 检查是否存在未完成的预审授权书签署任务
        List<PreFddSignTaskEntity> preFddSignTaskEntities = preFddSignTaskMapper.selectList(new LambdaQueryWrapper<PreFddSignTaskEntity>()
                .eq(PreFddSignTaskEntity::getPreId, signTaskDTO.getPreId())
                .eq(PreFddSignTaskEntity::getDeleteFlag, 0)
                .ne(PreFddSignTaskEntity::getSignTaskStatus, SignTaskStatusEnums.TASK_FINISHED.getCode())
                .ge(PreFddSignTaskEntity::getCreateTime, LocalDateTime.now().minusDays(1))
                .orderByDesc(PreFddSignTaskEntity::getCreateTime)
        );

        if (CollUtil.isNotEmpty(preFddSignTaskEntities)) {
            PreFddSignTaskEntity preFddSignTaskEntity = preFddSignTaskEntities.get(0);
            String signTaskId = preFddSignTaskEntity.getSignTaskId();
            if (StrUtil.isNotEmpty(signTaskId)) {
                log.info("FadadaAuthServiceImpl.fddFreeLogin preFddSignTaskEntity.getSignTaskId()={}", signTaskId);
                freeLoginVO.setSignTaskId(signTaskId);
                freeLoginVO.setClientUserId(preFddSignTaskEntity.getClientUserId());
                return freeLoginVO;
            }
        }
        // 3. 创建授权书签署任务
        List<FddFileResourceVO> resourceVOList = fileToFddMapper.selectJoinList(FddFileResourceVO.class, new MPJLambdaWrapper<FileToFddEntity>()
                .select(FileToFddEntity::getResourceFileUid, FileToFddEntity::getFddFileId)
                .selectAs(FileResourceEntity::getFileOldName, FddFileResourceVO::getFileName)
                .selectAs(FileToFddEntity::getDataSource, FddFileResourceVO::getDataSource)
                .selectAs(FileToFddEntity::getSort, FddFileResourceVO::getSort)
                .innerJoin(FileResourceEntity.class, FileResourceEntity::getFileUid, FileToFddEntity::getResourceFileUid)
                .eq(FileToFddEntity::getDeleteFlag, 0)
                .ne(FileToFddEntity::getFddFileId, "")
                .in(FileToFddEntity::getFilePurpose, FadadaEnum.FILE_PURPOSE.getCode())
                .orderByAsc(FileToFddEntity::getId)
        );
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(preId);
        Assert.notNull(preApprovalApplyInfoEntity, () -> new BusinessException("预审信息不存在"));
        if (ObjUtil.equals(preApprovalApplyInfoEntity.getRegionId(), 56) || ObjUtil.equals(preApprovalApplyInfoEntity.getRegionId(), 24)) {
            resourceVOList = resourceVOList.stream().filter(e -> ObjUtil.equal(e.getDataSource(), 1)).toList();
            // 确保严格顺序:
            // 1. "富民-个人征信授权书.docx" (优先级最高)
            // 2. "富民-个人信息授权书.docx" (优先级次高)
            // 3. 其他文件保持原顺序
            // 先按sort排序
            // 如果sort相同，再按其他字段排序（比如fileName）
            resourceVOList = resourceVOList.stream()
                    .sorted(Comparator.comparing(
                            (FddFileResourceVO vo) -> vo.getSort() != null ? vo.getSort() : Integer.MAX_VALUE,
                            Comparator.naturalOrder()
                    ).thenComparing(FddFileResourceVO::getFileName))
                    .toList();
        }
        BaseRes<CreateSignTaskRes> signTaskRes;
        try {
            //获取token
            String accessToken = getFddToken();
            OpenApiClient apiClient = fadadaConfig.appClient();
            SignTaskClient signTaskClient = new SignTaskClient(apiClient);
            CreateSignTaskReq signTaskReq = new CreateSignTaskReq();
            signTaskReq.setAccessToken(accessToken);

            OpenId openId = new OpenId();
            openId.setIdType("corp");
            openId.setOpenId(fadadaConfig.getOppenCorpId());
            //扣费主体
            signTaskReq.setInitiator(openId);
            //签署任务主题
            signTaskReq.setSignTaskSubject("签署授权文件");
            //免验签场景码
            signTaskReq.setBusinessId(fadadaConfig.getBusinessId());
            //文件信息
            List<AddDocInfo> docs = resourceVOList.stream().map(vo -> {
                AddDocInfo docInfo = new AddDocInfo();
                docInfo.setDocId(vo.getResourceFileUid());
                docInfo.setDocName(vo.getFileName().split("\\.")[0]);
                docInfo.setDocFileId(vo.getFddFileId());
                //文档中添加控价
                docInfo.setDocFields(getDocFieldList(vo, 2));
                return docInfo;
            }).toList();
            signTaskReq.setDocs(docs);

            List<AddActorsInfo> actors = new ArrayList<>();
            List<Integer> corpIdList = fddFieldConfigMapper.selectList(new LambdaQueryWrapper<FddFieldConfigEntity>()
                            .eq(FddFieldConfigEntity::getDeleteFlag, 0)
                            .in(FddFieldConfigEntity::getTemplateId, 10000))
                    .stream()
                    .map(FddFieldConfigEntity::getSealCorpId)
                    .toList();
            if (CollUtil.isNotEmpty(corpIdList)) {
                List<FddCorpConfigEntity> corpConfigList = fddCorpConfigMapper.selectList(new LambdaQueryWrapper<FddCorpConfigEntity>()
                        .eq(FddCorpConfigEntity::getDeleteFlag, 0)
                        .in(FddCorpConfigEntity::getId, corpIdList));
                //企业参与方
                List<AddActorsInfo> corp = handleCorpActors(docs, corpConfigList);
                actors.addAll(corp);
            }
            //个人参与方信息
//            List<AddActorsInfo> person = preAuthPersonActors(authEntity, docs);

            AddActorsInfo addActorsInfo = new AddActorsInfo();
            ActorsInfoDTO actorsInfoDTO = getActorsInfoDTO(preId, addActorsInfo, docs);
            Actor actor = new Actor();
            BeanUtil.copyProperties(actorsInfoDTO, actor);

            //将clientUserId填入，只有人脸认证
//            actor.setClientUserId(actorsInfoDTO.getActorId());
//            actor.setAuthScopes(FadadaEnum.getAllAuthScope());
//            actor.setIdentNameForMatch(actorsInfoDTO.getActorName());
//            actor.setCertType("id_card");
//            actor.setCertNoForMatch(actorsInfoDTO.getIdNumber());
//            actor.setActorType("person");
//            actor.setPermissions(Arrays.asList("sign"));
//            actor.setActorName(actorsInfoDTO.getActorName());
//            actor.setActorId(actorsInfoDTO.getActorId());
//            addActorsInfo.setActor(actor);
            log.info("FadadaAuthServiceImpl.fddFreeLogin actor={}", actorsInfoDTO);
            //参与方信息-预审批提交人
            List<AddActorsInfo> actorsList = new ArrayList<>();
            actorsList.add(addActorsInfo);
            actors.addAll(actorsList);

            signTaskReq.setActors(actors);
            log.info("FadadaAuthServiceImpl.fddFreeLogin preId ={}，signTaskReq ={}", preId, JSONUtil.toJsonStr(signTaskReq));

            signTaskRes = signTaskClient.create(signTaskReq);

            log.info("fddFreeLogin.signTaskRes ={}", JSONUtil.toJsonStr(signTaskRes));

            //保存报文信息
            saveRequestLog(preId, RequestResponseCode.FDD_CREATE_SIGN_TASK, "", "", JSONUtil.toJsonStr(signTaskReq), JSONUtil.toJsonStr(signTaskRes));


            if (!FadadaEnum.SUCCESS.getCode().equals(signTaskRes.getCode())) {
                if (Objects.equals(signTaskRes.getCode(), "100011")) {
                    signTaskRes.setMsg("授权信息冲突，请先注销原有账号");
//                    return signTaskRes;
                }
                throw new BusinessException(signTaskRes.getMsg());
            }


            String signTaskId = signTaskRes.getData().getSignTaskId();
            actorsInfoDTO.setSignTaskId(signTaskId);
            actorsInfoDTO.setPreId(preId);
//            CompletableFuture.supplyAsync(() -> {
            //填写控件内容
            BaseRes<Void> fillInputFieldRes = addInputField(actorsInfoDTO, signTaskClient, docs);
            log.info("FadadaAuthServiceImpl.fddFreeLogin fillInputFieldRes = {}",
                    JSONUtil.toJsonStr(fillInputFieldRes));

            if (!FadadaEnum.SUCCESS.getCode().equals(fillInputFieldRes.getCode())) {
                throw new BusinessException(fillInputFieldRes.getMsg());
            }
            //提交签署任务
            BaseRes<Void> submitSignTaskRes = submitSignTask(signTaskId, signTaskClient,
                    preId);
            log.info("FadadaAuthServiceImpl.fddFreeLogin submitSignTaskRes = {}",
                    JSONUtil.toJsonStr(submitSignTaskRes));
            if (!FadadaEnum.SUCCESS.getCode().equals(submitSignTaskRes.getCode())) {
                throw new BusinessException(submitSignTaskRes.getMsg());
            }
            signTaskDTO.setClientUserId(authEntity.getClientUserId());
            //更新签署任务数据
            addPreSignTask(signTaskDTO, signTaskId);
            freeLoginVO.setSignTaskId(signTaskId);
            freeLoginVO.setClientUserId(authEntity.getClientUserId());

        } catch (ApiException e) {
            throw new BusinessException(e);
        } finally {
            redisService.releaseLock(lockKey, requestId);
        }
        return freeLoginVO;
    }

    @Override
    public Boolean syncFddContractFileByOrderId(Integer orderId) {
        log.info("FadadaAuthServiceImpl.syncFddContractFileByOrderId orderId:{}", orderId);
        //查询签署完成任务信息
        List<SignTaskEntity> signTaskList = signTaskMapper.selectJoinList(
                SignTaskEntity.class,
                new MPJLambdaWrapper<SignTaskEntity>()
                        .leftJoin(PreFddFinishFileEntity.class, PreFddFinishFileEntity::getSignTaskId, SignTaskEntity::getSignTaskId)
                        .eq(SignTaskEntity::getSignTaskStatus, SignTaskStatusEnums.TASK_FINISHED.getCode())
                        .eq(SignTaskEntity::getBusiId, orderId)
                        .isNull(PreFddFinishFileEntity::getSignTaskId)
                        .orderByAsc(SignTaskEntity::getCreateTime)
        );
        log.info("FadadaAuthServiceImpl.syncFddContractFileByOrderId signTaskList:{}", JSONUtil.toJsonStr(signTaskList));
        if (CollUtil.isEmpty(signTaskList)) {
            return true;
        }

        //下载文件
        for (SignTaskEntity signTaskEntity : signTaskList) {
            String signTaskId = signTaskEntity.getSignTaskId();
            String customName = signTaskId + "_合同签约";

            signTaskDownloadFile(signTaskEntity.getBusiId(), signTaskId, customName,
                    signTaskEntity.getSignType());

        }
        return true;
    }

    private PreApprovalFddAuthEntity freeLoginUserInfo(Integer preId) {
        PreApprovalFddAuthEntity preApprovalFddAuthEntity = new PreApprovalFddAuthEntity();
        PreApprovalApplyInfoEntity applyInfoEntity = preApprovalApplyInfoMapper.selectById(preId);
        if (applyInfoEntity == null) {
            throw new BusinessException("未找到预审批信息");
        }
        preApprovalFddAuthEntity.setPhone(applyInfoEntity.getPhone());
        preApprovalFddAuthEntity.setIdNumber(applyInfoEntity.getIdNumber());

        String clientUserId = FadadaUtils.generateUserId();
        preApprovalFddAuthEntity.setClientUserId(clientUserId);
        PreApprovalFddAuthEntity authEntity = preApprovalFddAuthMapper.selectOne(new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                //客户重新扫码填写信息，身份证号相同不再经过授权
                .eq(PreApprovalFddAuthEntity::getIdNumber, applyInfoEntity.getIdNumber())
                .eq(PreApprovalFddAuthEntity::getPhone, applyInfoEntity.getPhone())
                .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                .orderByDesc(PreApprovalFddAuthEntity::getId)
                .last("limit 1")
        );

        log.info("freeLoginUserInfo authEntity = {}", JSONUtil.toJsonStr(authEntity));
        if (ObjectUtil.isNotEmpty(authEntity)) {
            UserRes userAuthStatus = getUserAuthStatus(authEntity.getClientUserId());
            if (ObjUtil.isNotEmpty(userAuthStatus) && Objects.equals(userAuthStatus.getIdentStatus(), "identified")) {

                preApprovalFddAuthEntity.setClientUserId(authEntity.getClientUserId());

            } else {
                preApprovalFddAuthMapper.update(new LambdaUpdateWrapper<PreApprovalFddAuthEntity>()
                        .set(PreApprovalFddAuthEntity::getDeleteFlag, 1)
                        .set(PreApprovalFddAuthEntity::getUpdateTime, LocalDateTime.now())
                        .eq(PreApprovalFddAuthEntity::getPhone, applyInfoEntity.getPhone())
                        .eq(PreApprovalFddAuthEntity::getIdNumber, applyInfoEntity.getIdNumber()));
                log.info("freeLoginUserInfo preApprovalFddAuthEntity = null");
                preApprovalFddAuthMapper.insert(new PreApprovalFddAuthEntity()
                        .setClientUserId(clientUserId)
                        .setIdNumber(applyInfoEntity.getIdNumber())
                        .setPreId(preId)
                        .setPhone(applyInfoEntity.getPhone())
                );
            }
        } else {
            preApprovalFddAuthMapper.insert(new PreApprovalFddAuthEntity()
                    .setClientUserId(clientUserId)
                    .setIdNumber(applyInfoEntity.getIdNumber())
                    .setPreId(preId)
                    .setPhone(applyInfoEntity.getPhone())
            );
        }


        return preApprovalFddAuthEntity;
    }

    private List<AddActorsInfo> handleFreeSign(ContractTaskDTO contractTaskDTO, List<AddDocInfo> docs) {
        List<AddActorsInfo> actorsList = new ArrayList<>();
        AddActorsInfo addActorsInfo = new AddActorsInfo();
        ActorsInfoDTO actorsInfoDTO = getOrderAgentInfo(contractTaskDTO);
        //个人参与方信息
        Actor actor = getActor(actorsInfoDTO);
        actor.setActorOpenId(actorsInfoDTO.getOpenUserId());
        addActorsInfo.setActor(actor);
        //设置个人参与方的签章控件
        List<AddSignFieldInfo> signFields = new ArrayList<>();
        docs.forEach(doc -> {
            doc.getDocFields().forEach(field -> {
                AddSignFieldInfo signFieldInfo = new AddSignFieldInfo();
                if (field.getFieldName().equals("免验签个人签名")) {
                    signFieldInfo.setFieldDocId(doc.getDocId());
                    signFieldInfo.setFieldId(field.getFieldId());
                    signFields.add(signFieldInfo);
                }
            });
        });
        addActorsInfo.setSignFields(signFields);
        //个人签署配置信息
        AddSignConfigInfo addSignConfigInfo = new AddSignConfigInfo();
        addSignConfigInfo.setFreeLogin(true);
        addSignConfigInfo.setIdentifiedView(false);
        //标准签名
        addSignConfigInfo.setSignerSignMethod("standard");
        //该参与方免验证签
        addSignConfigInfo.setRequestVerifyFree(true);

        if (envUtil.isPrd()) {
            //face: 刷脸验证,sms: 短信验证
            addSignConfigInfo.setVerifyMethods(List.of("face"));
        }
        addActorsInfo.setSignConfigInfo(addSignConfigInfo);
        actorsList.add(addActorsInfo);
        return actorsList;
    }

    private ActorsInfoDTO getOrderAgentInfo(ContractTaskDTO contractTaskDTO) {
        List<ActorsInfoDTO> actorsInfoDTOList = orderArrivedMapper.selectJoinList(ActorsInfoDTO.class,
                new MPJLambdaWrapper<OrderArrivedEntity>()
                        .selectAs(ArrivedDataEntity::getMortgageAgentName, ActorsInfoDTO::getActorName)
                        .selectAs(ArrivedDataEntity::getMortgageAgentPhone, ActorsInfoDTO::getAccountName)
                        .selectAs(PreApprovalFddAuthEntity::getClientUserId, ActorsInfoDTO::getActorId)
                        .selectAs(PreApprovalFddAuthEntity::getOpenUserId, ActorsInfoDTO::getOpenUserId)
                        .innerJoin(ArrivedDataEntity.class, on -> on
                                .eq(ArrivedDataEntity::getId, ObjUtil.equals(1, contractTaskDTO.getType()) ? OrderArrivedEntity::getArrivedId : OrderArrivedEntity::getRelieveArrivedId)
                                .eq(ArrivedDataEntity::getDeleteFlag, 0)
                                .eq(ArrivedDataEntity::getAuthSign, 1)
                        )
                        .innerJoin(PreApprovalFddAuthEntity.class, on -> on
                                .eq(PreApprovalFddAuthEntity::getIdNumber, ArrivedDataEntity::getMortgageAgentIdNumber)
                                .eq(PreApprovalFddAuthEntity::getPhone, ArrivedDataEntity::getMortgageAgentPhone)
                                .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                                .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                        )
                        .eq(OrderArrivedEntity::getOrderId, contractTaskDTO.getOrderId())
                        .orderByDesc(PreApprovalFddAuthEntity::getCreateTime)
        );

        if (CollUtil.isNotEmpty(actorsInfoDTOList)) {
            return actorsInfoDTOList.get(0);
        }
        throw new BusinessException("未查询到用户授权信息");
    }

    private List<AddActorsInfo> handleTheCorp(List<AddDocInfo> docs, List<String> openCorpIdList) {
        List<AddActorsInfo> actorsList = new ArrayList<>();
        List<FddCorpConfigEntity> corpConfigList = fddCorpConfigMapper.selectList(new LambdaQueryWrapper<FddCorpConfigEntity>()
                .in(FddCorpConfigEntity::getOpenCorpId, openCorpIdList)
                .eq(FddCorpConfigEntity::getDeleteFlag, 0));
        corpConfigList.forEach(corpConfig -> {
            List<AddSignFieldInfo> corpSignFields = new ArrayList<>();
            //定义企业参与方信息
            AddActorsInfo adCorpActors = new AddActorsInfo();
            //企业参与方
            Actor corpActor = new Actor();
            corpActor.setActorId(corpConfig.getClientCorpId());
            corpActor.setActorType("corp");
            corpActor.setActorName(corpConfig.getCorpName());
            corpActor.setPermissions(Arrays.asList("sign"));
            corpActor.setSendNotification(false);
//            corpActor.setAuthScopes(Arrays.asList("signtask_init", "signtask_info", "signtask_file"));
            corpActor.setAuthScopes(FadadaEnum.getAllAuthScope());
            corpActor.setActorOpenId(corpConfig.getOpenCorpId());
            adCorpActors.setActor(corpActor);

            docs.forEach(doc -> {
                doc.getDocFields().forEach(field -> {
                    //设置参与方的签章控件
                    AddSignFieldInfo signFieldInfo = new AddSignFieldInfo();
                    if (field.getFieldType().equals("corp_seal")) {
                        if (Objects.equals(corpConfig.getId(), Integer.parseInt(field.getFieldKey()))) {
                            signFieldInfo.setFieldDocId(doc.getDocId());
                            signFieldInfo.setFieldId(field.getFieldId());
                            signFieldInfo.setSealId(Long.valueOf(corpConfig.getSealId()));
                            corpSignFields.add(signFieldInfo);
                        }
                    }

                });
            });

            log.info("adCorpActors = {}", JSONUtil.toJsonStr(adCorpActors));
            AddSignConfigInfo addSignConfigInfo = new AddSignConfigInfo();
            addSignConfigInfo.setRequestVerifyFree(true);
            adCorpActors.setSignConfigInfo(addSignConfigInfo);
            adCorpActors.setSignFields(corpSignFields);
            actorsList.add(adCorpActors);

        });

        return actorsList;
    }

    private void updateFinish(PreFddFinishFileEntity finishFileEntity, List<DownZipFileVO> fileVOList, Integer busiId, String signTaskId, int i) {
        log.info("saveBatchFinishFile start, busiId ={}, signTaskId ={}", busiId, signTaskId);
        List<PreFddFinishFileEntity> fddFinishFileEntityList = new ArrayList<>();

        fileVOList.forEach(fileResourceEntity -> {
            PreFddFinishFileEntity fileEntity = new PreFddFinishFileEntity();
            fileEntity.setResourceFileUid(fileResourceEntity.getFileUid());
            //            fileEntity.setPreId(busiId);
            fileEntity.setFileName(fileResourceEntity.getFileOldName());
            //            fileEntity.setSignTaskId(signTaskId);
            fileEntity.setDeleteFlag(0);
            fddFinishFileMapper.update(fileEntity, new LambdaUpdateWrapper<PreFddFinishFileEntity>()
                    //                    .eq(PreFddFinishFileEntity::getPreId, busiId)
                    .eq(PreFddFinishFileEntity::getId, finishFileEntity.getId())
                    //                            .eq(PreFddFinishFileEntity::getSignTaskId, signTaskId)
                    .eq(PreFddFinishFileEntity::getSignFileType, 0));
        });
    }

    /**
     * 预审签署任务
     *
     * @param signTaskClient
     * @param accessToken
     */
    private void synAuthTask(SignTaskClient signTaskClient, String accessToken) {
        log.info("FadadaAuthServiceImpl.signTaskResult synAuthTask start");
        List<PreFddSignTaskEntity> signTaskList = preFddSignTaskMapper.selectList(new LambdaQueryWrapper<PreFddSignTaskEntity>()
                .ge(PreFddSignTaskEntity::getCreateTime, LocalDateTime.now().minusDays(3))
                .isNotNull(PreFddSignTaskEntity::getSignTaskId)
                .isNull(PreFddSignTaskEntity::getSignTaskStatus)
                .eq(PreFddSignTaskEntity::getDeleteFlag, 0)
        );
        log.info("查询到需要同步的预审任务数量：{}", signTaskList.size());
        if (CollUtil.isEmpty(signTaskList)) {
            return;
        }
        for (PreFddSignTaskEntity signTaskEntity : signTaskList) {
            log.info("需要下载文件的签署任务id：{}", signTaskEntity.getSignTaskId());
            SignTaskBaseReq signTaskBaseReq = new SignTaskBaseReq();
            signTaskBaseReq.setSignTaskId(signTaskEntity.getSignTaskId());
            signTaskBaseReq.setAccessToken(accessToken);

            try {
                BaseRes<SignTaskDetailRes> baseRes = signTaskClient.getDetail(signTaskBaseReq);
                log.info("synAuthTask baseRes：{}", JSONUtil.toJsonStr(baseRes));
                if (FadadaEnum.SUCCESS.getCode().equals(baseRes.getCode())) {
                    SignTaskDetailRes res = baseRes.getData();
                    //签署完成状态继续后续操作
                    if (res.getSignTaskStatus().equals(SignTaskStatusEnums.TASK_FINISHED.getCode())) {
                        preFddSignTaskMapper.update(new LambdaUpdateWrapper<PreFddSignTaskEntity>()
                                .set(PreFddSignTaskEntity::getSignTaskStatus, res.getSignTaskStatus())
                                .set(PreFddSignTaskEntity::getTerminationNote, res.getTerminationNote())
                                .set(PreFddSignTaskEntity::getRevokeNote, res.getRevokeNote())
                                .set(PreFddSignTaskEntity::getSignCreateTime, res.getCreateTime())
                                .set(PreFddSignTaskEntity::getStartTime, res.getStartTime())
                                .set(PreFddSignTaskEntity::getFinishTime, res.getFinishTime())
                                .set(PreFddSignTaskEntity::getSignTaskSubject, res.getSignTaskSubject())
                                .eq(PreFddSignTaskEntity::getSignTaskId, res.getSignTaskId())
                        );

                        //是否已成功下载文件
                        List<PreFddFinishFileEntity> preFddFinishFileList = preFddFinishFileMapper.selectList(new LambdaQueryWrapper<PreFddFinishFileEntity>()
                                .eq(PreFddFinishFileEntity::getSignTaskId, res.getSignTaskId())
                        );
                        log.info("查询到需要下载的文件数量：{}", preFddFinishFileList.size());
                        if (CollUtil.isEmpty(preFddFinishFileList)) {
                            //下载已签署的文件
                            getSignOwnerFile(signTaskEntity.getPreId());
                        }
                        UserRes userRes = getUserAuthStatus(signTaskEntity.getClientUserId());

                        //已授权且已实名认证状态
                        if (FadadaEnum.AUTHORIZED.getCode().equals(userRes.getBindingStatus()) &&
                                FadadaEnum.IDENTIFIED.getCode().equals(userRes.getIdentStatus())) {
                            PreApprovalFddAuthEntity updateEntity = new PreApprovalFddAuthEntity();
                            updateEntity.setOpenUserId(userRes.getOpenUserId())
                                    .setAuthScopes(userRes.getAuthScope().toString())
                                    .setAuthResult("success");
                            int updateCount = preApprovalFddAuthMapper.update(updateEntity, new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                                    .eq(PreApprovalFddAuthEntity::getClientUserId, userRes.getClientUserId()));
                        }
                        //获取刷脸底图
                        SignTaskDetailDTO signTaskDetailDTO = new SignTaskDetailDTO();
                        signTaskDetailDTO.setPreId(signTaskEntity.getPreId());
                        signTaskDetailDTO.setSignTaskId(res.getSignTaskId());
                        getSignTaskFacePicture(signTaskDetailDTO, signTaskEntity.getClientUserId());

                        // 发起风控
                        log.info("risk launch on preapproval begin preId:{}", signTaskDetailDTO.getPreId());
                        riskService.riskLaunchOnPreApproval(signTaskDetailDTO.getPreId());
                        log.info("risk launch on preapproval end preId:{}", signTaskDetailDTO.getPreId());


                    }
                }
            } catch (Exception e) {
                log.error("fadadaAuthServiceImpl synAuthTask error ={}", e.getMessage(), e);
            }

        }
    }

    private void synContractTask(SignTaskClient signTaskClient, String accessToken) {
        log.info("FadadaAuthServiceImpl.contractSignTaskResult synContractTask start");
        List<SignTaskEntity> signTaskList = signTaskMapper.selectList(new LambdaQueryWrapper<SignTaskEntity>()
                .ge(SignTaskEntity::getCreateTime, LocalDateTime.now().minusDays(7))
                .and(or -> or.isNull(SignTaskEntity::getSignTaskStatus)
                        .or().eq(SignTaskEntity::getSignTaskStatus, SignTaskStatusEnums.SIGN_PROGRESS.getCode()))
                .eq(SignTaskEntity::getDeleteFlag, 0)
                .orderByAsc(SignTaskEntity::getCreateTime)
        );
        log.info("FadadaAuthServiceImpl.synContractTask signTaskList.size() = {}", signTaskList.size());
        if (CollUtil.isEmpty(signTaskList)) {
            return;
        }
        for (SignTaskEntity signTaskEntity : signTaskList) {
            SignTaskBaseReq signTaskBaseReq = new SignTaskBaseReq();
            signTaskBaseReq.setSignTaskId(signTaskEntity.getSignTaskId());
            signTaskBaseReq.setAccessToken(accessToken);
            BaseRes<SignTaskDetailRes> baseRes = null;
            try {
                baseRes = signTaskClient.getDetail(signTaskBaseReq);
                log.info("FadadaAuthServiceImpl.synContractTask taskId：{}, baseRes:{}", signTaskEntity.getSignTaskId(), JSONUtil.toJsonStr(baseRes));
                if (FadadaEnum.SUCCESS.getCode().equals(baseRes.getCode()) && ObjectUtil.isNotEmpty(baseRes.getData())) {

                    SignTaskDetailRes res = baseRes.getData();
                    busiSignTaskMapper.update(new LambdaUpdateWrapper<SignTaskEntity>()
                            .set(SignTaskEntity::getSignTaskStatus, res.getSignTaskStatus())
                            .set(SignTaskEntity::getTerminationNote, res.getTerminationNote())
                            .set(SignTaskEntity::getRevokeNote, res.getRevokeNote())
                            .set(SignTaskEntity::getSignCreateTime, res.getCreateTime())
                            .set(SignTaskEntity::getTaskStartTime, res.getStartTime())
                            .set(SignTaskEntity::getFinishTime, res.getFinishTime())
                            .set(SignTaskEntity::getSignTaskSubject, res.getSignTaskSubject())
                            .eq(SignTaskEntity::getSignTaskId, res.getSignTaskId())
                    );
                    if (SignTaskStatusEnums.TASK_FINISHED.getCode().equals(res.getSignTaskStatus())) {
                        //法大大签署状态完成时更新数据库合同状态为已签署
                        orderContractMapper.update(new LambdaUpdateWrapper<OrderContractEntity>()
                                .set(OrderContractEntity::getSignStatus, ContractEnum.SIGNED.getCode())
                                .eq(OrderContractEntity::getOrderId, signTaskEntity.getBusiId())
                                .eq(OrderContractEntity::getDeleteFlag, 0)
                                .eq(OrderContractEntity::getSignTaskId, signTaskEntity.getSignTaskId())
                        );
                        //                            synFddFile(signTaskEntity, accessToken);
                        ContractFundSignSubmitDTO contractFundSignSubmitDTO = new ContractFundSignSubmitDTO();
                        contractFundSignSubmitDTO.setOrderId(signTaskEntity.getBusiId());
                        ContractService contractService = SpringUtil.getBean(ContractService.class);
                        contractService.asyncFundSignSubmit(contractFundSignSubmitDTO);
                    }

                }
            } catch (Exception e) {
                log.info("fadadaAuthServiceImpl synContractTask error ={}", e.getMessage(), e);
            }
        }
    }

    private void synFddFile(SignTaskEntity signTaskEntity, String accessToken) {
        //是否已成功下载文件
        List<PreFddFinishFileEntity> preFddFinishFileList = preFddFinishFileMapper.selectList(new LambdaQueryWrapper<PreFddFinishFileEntity>()
                .eq(PreFddFinishFileEntity::getSignTaskId, signTaskEntity.getSignTaskId())
        );
        log.info("fadadaAuthServiceImpl.synFddFile preFddFinishFileList.size()：{}", preFddFinishFileList.size());
        if (CollUtil.isEmpty(preFddFinishFileList)) {
            //下载已签署的文件
            downLoadSignTaskFile(signTaskEntity.getSignTaskId(), 1);
        }

    }


    /**
     * 获取用户身份验证状态
     *
     * @param clientUserId 客户端用户 ID
     * @return {@link UserRes }
     */
    private UserRes getUserAuthStatus(String clientUserId) {
        try {
            //获取token
            String accessToken = getFddToken();
            OpenApiClient apiClient = fadadaConfig.appClient();
            UserClient userClient = new UserClient(apiClient);
            GetUserReq getUserReq = new GetUserReq();
            getUserReq.setAccessToken(accessToken);
            getUserReq.setClientUserId(clientUserId);
            log.info("FadadaAuthServiceImpl.getUserAuthStatus getUserReq:{}", JSONUtil.toJsonStr(getUserReq));
            BaseRes<UserRes> userResBaseRes = userClient.get(getUserReq);

            log.info("FadadaAuthServiceImpl.getUserAuthStatus userResBaseRes:{}", JSONUtil.toJsonStr(userResBaseRes));
            String code = userResBaseRes.getCode();
            // {"code":"210022","msg":"个人用户不存在","httpStatusCode":200}
            if ("210022".equals(code)) {
                log.info("FadadaAuthServiceImpl.getUserAuthStatus 210022");
            }
            return userResBaseRes.getData();
        } catch (ApiException e) {
            log.error("FadadaAuthServiceImpl.getUserAuthStatus error:{}", e.getMessage(), e);
            throw new BusinessException(e);
        }
    }

    private List<GetListSealInfo> getSealList(OpenApiClient apiClient, String accessToken, String openCorpId) {
        List<GetListSealInfo> sealInfos = new ArrayList<>();
        try {
            SealClient sealClient = new SealClient(apiClient);
            GetSealListReq req = new GetSealListReq();
            req.setAccessToken(accessToken);
            req.setOpenCorpId(openCorpId);
            log.info("签章列表请求报文req ={}", JSONUtil.toJsonStr(req));
            BaseRes<GetSealListRes> sealListRes = sealClient.getSealList(req);
            log.info("签章列表getSealListRes ={}", JSONUtil.toJsonStr(sealListRes));
            if (FadadaEnum.SUCCESS.getCode().equals(sealListRes.getCode())) {
                sealInfos = sealListRes.getData().getSealInfos();

            }
        } catch (ApiException e) {
            throw new BusinessException(e);
        }
        return sealInfos;
    }

    private boolean batchFileTOFdd(List<String> resourceIds, Integer fundId) {


        log.info("batchUploadFileToFdd resourceIdList.size() = {}", resourceIds.size());
        List<FileTOFddVO> fileTOFddVOS = resourceIds.parallelStream().map(resourceId ->
                        new FileTOFddVO().setFileBelong(fundId).setFileUid(resourceId)
                                .setFileBytes(resourceFeign.fileContent(resourceId)
                                        .getBody()))
                .toList();
        log.info("fileTOFddVOS.size() = {}", fileTOFddVOS.size());

        uploadFile(fileTOFddVOS);

        return true;
    }

    /**
     * 获取 Get Upload URL res
     *
     * @return {@link GetUploadUrlRes }
     */
    private GetUploadUrlRes getGetUploadUrlRes() {
        //获取token
        String accessToken = getFddToken();
        OpenApiClient apiClient = fadadaConfig.appClient();
        DocClient docClient = new DocClient(apiClient);
        BaseRes<GetUploadUrlRes> uploadFileUrlRes;
        try {
            GetUploadUrlReq req = new GetUploadUrlReq();
            req.setFileType("doc");
            req.setStorageType("cloud");
            req.setAccessToken(accessToken);
            uploadFileUrlRes = docClient.getUploadFileUrl(req);
        } catch (ApiException e) {
            throw new BusinessException(e);
        }
        if (!FadadaEnum.SUCCESS.getCode().equals(uploadFileUrlRes.getCode())) {
            throw new BusinessException(uploadFileUrlRes.getMsg());
        }

        return uploadFileUrlRes.getData();
    }


    /**
     * 保存人脸图片
     *
     * @param preId       前 ID
     * @param facePicture 人脸图片
     * @return int
     */
    @Override
    public int saveFacePicture(Integer preId, String facePicture) {
        Objects.requireNonNull(facePicture, "人脸图片不存在");
        List<FileVO> fileVOS = updateFacePicture(facePicture, "pre_face_" + preId);
        if (CollUtil.isNotEmpty(fileVOS)) {

            String resourceId = fileVOS.get(0).getResourceId();

            if (StrUtil.isNotEmpty(resourceId)) {
                PreApprovalApplyInfoEntity applyInfoEntity = preApprovalApplyInfoMapper.selectById(preId);

                preOcrIdentityCardMapper.update(new LambdaUpdateWrapper<PreOcrIdentityCardEntity>()
                        .set(PreOcrIdentityCardEntity::getFacePictureId, resourceId)
                        .eq(PreOcrIdentityCardEntity::getPreId, preId)
                );

                preApprovalFddAuthMapper.update(new LambdaUpdateWrapper<PreApprovalFddAuthEntity>()
                        .set(PreApprovalFddAuthEntity::getFacePictureId, resourceId)
                        .eq(PreApprovalFddAuthEntity::getPhone, applyInfoEntity.getPhone())
                        .eq(PreApprovalFddAuthEntity::getIdNumber, applyInfoEntity.getIdNumber())
                        .eq(PreApprovalFddAuthEntity::getAuthResult, "success")
                        .eq(PreApprovalFddAuthEntity::getDeleteFlag, 0)
                );
            } else {
                log.error("pre {} face resourceId is null", preId);
            }
        }
        log.info("pre {} upload face picture finish", preId);
        return 1;
    }

    /**
     * 法大大认证信息
     *
     * @param preId
     * @param
     */
    @Override
    public int saveFacePicture(Integer preId) {
        log.info("pre {} getIdentityInfo.", preId);
        preApprovalFddAuthMapper.selectList(new LambdaQueryWrapper<PreApprovalFddAuthEntity>()
                .eq(PreApprovalFddAuthEntity::getPreId, preId)).forEach(preApprovalFddAuthEntity -> {

            String openUserId = preApprovalFddAuthEntity.getOpenUserId();
            log.info("pre:{} openUserId:{}", preId, openUserId);
            if (StringUtils.hasText(openUserId)) {
                UserIdentityRespDTO identityInfo = fadadaFeign.getIdentityInfo(new UserIdentityReqDTO().setOpenUserId(openUserId));

                Optional.ofNullable(identityInfo).ifPresent(info -> {
                    String code = info.getCode();
                    log.info("pre:{} res code:{}", preId, code);
                    UserIdentityRespDTO.UserIdentityInfoDTO data = info.getData();
                    if (FadadaEnum.SUCCESS.getCode().equals(code) && data != null) {
                        String facePicture = data.getFacePicture();
                        String identNo = data.getUserIdentInfo().getIdentNo();
                        String identStatus = data.getIdentStatus();
                        if ("identified".equalsIgnoreCase(identStatus) && StringUtils.hasText(facePicture)) {
                            log.info("pre {} upload face picture.", preId);
                            List<FileVO> fileVOS = updateFacePicture(facePicture, identNo);
                            if (CollUtil.isNotEmpty(fileVOS)) {
                                String resourceId = fileVOS.get(0).getResourceId();

                                Integer id = preApprovalFddAuthEntity.getId();
                                PreApprovalFddAuthEntity updateEntity = new PreApprovalFddAuthEntity();
                                updateEntity.setId(id);
                                updateEntity.setFacePictureId(resourceId);
                                preApprovalFddAuthMapper.updateById(updateEntity);

                                preOcrIdentityCardMapper.update(new LambdaUpdateWrapper<PreOcrIdentityCardEntity>()
                                        .set(PreOcrIdentityCardEntity::getFacePictureId, resourceId)
                                        .eq(PreOcrIdentityCardEntity::getPreId, preId)
                                );
                            }
                        }
                    } else {
                        log.error("fadada getIdentityInfo error: {}", info);
                    }
                });
            }
        });
        return 1;
    }

    /**
     * 上传用户刷脸底图
     *
     * @param facePicture
     * @param identNo
     * @return
     */
    public List<FileVO> updateFacePicture(String facePicture, String identNo) {
        // Base64 转字节数据

        byte[] faceBytes = Base64Decoder.decode(facePicture);

        MultipartFile excelFile = new MockMultipartFile(
                "files",
                identNo + ".jpg",
                "image/jpeg",
                faceBytes
        );

        // 调用现有的 dealFiles 方法处理生成的文件
        List<MultipartFile> files = List.of(excelFile);
        return resourceFeign.uploadFile(files).getData();
    }


    private void saveBatchFinishFile(List<DownZipFileVO> fileVOList, Integer busiId, String signTaskId, Integer signType) {
        log.info("saveBatchFinishFile start, busiId ={}, signTaskId ={}", busiId, signTaskId);
        List<PreFddFinishFileEntity> fddFinishFileEntityList = new ArrayList<>();
        fileVOList.forEach(fileResourceEntity -> {
            PreFddFinishFileEntity fileEntity = new PreFddFinishFileEntity();
            fileEntity.setResourceFileUid(fileResourceEntity.getFileUid());
            fileEntity.setPreId(busiId);
            //拆分文件名称，获取 资方名称
            String fundName = fileResourceEntity.getFileOldName().split("-")[0];
            if (fundName.contains("_")) {
                fundName = fundName.split("_")[1];
            }
            String fundCode = AuthFileToFundEnums.fromDescription(fundName);
            if (StrUtil.isNotEmpty(fundCode)) {
                FundInfoEntity fundInfo = fundInfoMapper.selectOne(new LambdaQueryWrapper<FundInfoEntity>()
                        .select(FundInfoEntity::getId)
                        .eq(FundInfoEntity::getDeleteFlag, 0)
//                    .eq(FundInfoEntity::getName, fundName)
                        .eq(FundInfoEntity::getCode, fundCode)
                        .eq(FundInfoEntity::getFundFlag, 1));
                if (ObjUtil.isNotNull(fundInfo)) {
                    fileEntity.setFundingId(fundInfo.getId());
                }
            }


            fileEntity.setFileName(fileResourceEntity.getFileOldName());
            fileEntity.setSignTaskId(signTaskId);
            fileEntity.setSignFileType(signType);
            fddFinishFileEntityList.add(fileEntity);
        });

        boolean saveBatch = preFddFinishFileService.saveBatch(fddFinishFileEntityList);
        log.info("saveBatchFinishFile end, save result: {}", saveBatch);
        //合同签署时，需要进行以下操作
        if (saveBatch && signType == 1) {
            updateOrderContract(fddFinishFileEntityList);
        } else if (Objects.equals(signType, ContractEnum.REPURCHASE_CONTRACT.getCode())) {
            addOrderFile(fddFinishFileEntityList);
        } else if (Objects.equals(signType, ContractEnum.SETTLE_MATERIAL.getCode())) {
            updateOrderContractFile(fddFinishFileEntityList);
        }
    }

    private void addOrderFile(List<PreFddFinishFileEntity> fddFinishFileEntityList) {
        for (PreFddFinishFileEntity preFddFinishFileEntity : fddFinishFileEntityList) {
            FileConfigEntity fileConfigEntity = fileConfigMapper.selectOne(new LambdaQueryWrapper<FileConfigEntity>()
                    .eq(FileConfigEntity::getCode, FileConfigEnums.TRANSFER_CONFIRM.getCode())
                    .eq(FileConfigEntity::getDeleteFlag, 0)
                    .last("limit 1")
            );
            //先将已有文件置为无效
            orderFileMapper.update(new LambdaUpdateWrapper<OrderFileEntity>()
                    .set(OrderFileEntity::getDeleteFlag, 1)
                    .eq(OrderFileEntity::getOrderId, preFddFinishFileEntity.getPreId())
                    .eq(OrderFileEntity::getFileId, fileConfigEntity.getId())
                    .eq(OrderFileEntity::getDeleteFlag, 0));

            OrderFileEntity orderFileEntity = new OrderFileEntity();
            orderFileEntity.setResourceId(preFddFinishFileEntity.getResourceFileUid());
            orderFileEntity.setOrderId(preFddFinishFileEntity.getPreId());
            orderFileEntity.setResourceName(preFddFinishFileEntity.getFileName());
            orderFileEntity.setFileId(fileConfigEntity.getId());
            orderFileEntity.setFileName(fileConfigEntity.getName());
            orderFileMapper.insert(orderFileEntity);

            //将签署好的文件上传到盈峰
            uploadYingFengSftp(orderFileEntity);

        }
    }

    private void uploadYingFengSftp(OrderFileEntity orderFileEntity) {
        Result<YingFengInfoVO> result = approveFeign.getYingFengInfoByOrderId(orderFileEntity.getOrderId());
        log.info("getYingFengInfoByOrderId result:{}", JSONUtil.toJsonStr(result));
        FundResourceDTO dto = new FundResourceDTO();
        dto.setLinkId(orderFileEntity.getOrderId())
                .setFund(FundEnum.YING_FENG)
                .setType(4)
                .setRePushType(0);
        List<FundResourceDTO.SftpDTO> sftpDTOList = new ArrayList<>();
        FundResourceDTO.SftpDTO sftpDTO = new FundResourceDTO.SftpDTO();
        String folderFileName = String.format("/TransferConfirmation_%s.pdf", result.getData().getLoanApplyNo());
        sftpDTO.setFileUid(orderFileEntity.getResourceId())
//                .setFundFileCode("transferConfirm")
                .setDestPath(folderFileName);
        sftpDTOList.add(sftpDTO);
        dto.setSftpDTOList(sftpDTOList);
        log.info("uploadYingFengSftp dto:{}", JSONUtil.toJsonStr(dto));
        resourceFeign.fundResourceUpload(dto);
    }

    private void updateOrderContract(List<PreFddFinishFileEntity> fddFinishFileEntityList) {
        log.info("updateOrderContract start, fddFinishFileEntityList.size = {}", fddFinishFileEntityList.size());
        //将法大大签署完的文件更新到订单合同信息
        updateOrderContractFile(fddFinishFileEntityList);

        try {
            //如果为蓝海资方将合同状态改为 待资方签署
            log.info("FadadaAuthServiceImpl.updateOrderContract.updateOrderContractStatus start orderId:{}", fddFinishFileEntityList.get(0).getPreId());
            OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                    .select(OrderInfoEntity::getId, OrderInfoEntity::getContractState, OrderInfoEntity::getFundId)
                    .eq(OrderInfoEntity::getId, fddFinishFileEntityList.get(0).getPreId())
                    .eq(OrderInfoEntity::getDeleteFlag, 0)
            );
            if (ObjUtil.isNotNull(orderInfo) && ObjUtil.equals(FundEnum.LAN_HAI.getValue(), orderInfo.getFundId())) {
                log.info("FadadaAuthServiceImpl.updateOrderContract.updateOrderContractStatus orderId= {}, contractState: {}", fddFinishFileEntityList.get(0).getPreId(), orderInfo.getContractState());
                //更新带签署状态
                OrderInfoEntity updateOrderInfo = new OrderInfoEntity();
                updateOrderInfo.setContractState(ContractEnum.ORDER_SIGNED_WAIT_FUND.getCode());
                int update = orderInfoMapper.update(updateOrderInfo, new LambdaQueryWrapper<OrderInfoEntity>()
                        .eq(OrderInfoEntity::getId, orderInfo.getId())
                        .eq(OrderInfoEntity::getDeleteFlag, 0)
                );
                log.info("FadadaAuthServiceImpl.updateOrderContract.updateOrderContractStatus orderId= {}, update: {}", fddFinishFileEntityList.get(0).getPreId(), update);
                CompletableFuture.supplyAsync(() -> {
                    ContractFundSignSubmitDTO submitDTO = new ContractFundSignSubmitDTO();
                    submitDTO.setOrderId(orderInfo.getId());
                    ContractFundSignVO fundSignVO = SpringUtil.getBean(ContractService.class).fundSignSubmit(submitDTO);
                    return fundSignVO;
                }).thenAccept(processResult -> log.info("请求蓝海签章: {}", processResult));
            }
        } catch (Exception e) {
            log.info("ContractServiceImpl.updateOrderContract.updateOrderContractStatus orderId= {} error: {}", fddFinishFileEntityList.get(0).getPreId(), e.getMessage(), e);
        }


        List<FundContractVO> fundContractVOList = orderContractMapper.selectJoinList(
                FundContractVO.class,
                new MPJLambdaWrapper<OrderContractEntity>()
                        .selectAs(OrderContractEntity::getOrderId, FundContractVO::getOrderId)
                        .selectAs(OrderContractEntity::getResource, FundContractVO::getFileUid)
                        .selectAs(OrderContractEntity::getTemplateId, FundContractVO::getTemplateId)
                        .selectAs(FileTemplateInfoEntity::getFundSign, FundContractVO::getFundSign)
                        .selectAs(FundTemplateAssoEntity::getFundId, FundContractVO::getFundId)
                        .selectAs(OrderContractEntity::getName, FundContractVO::getName)
                        .innerJoin(FileTemplateInfoEntity.class, on -> on
                                .eq(FileTemplateInfoEntity::getId, OrderContractEntity::getTemplateId)
                                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                        )
                        .innerJoin(FundTemplateAssoEntity.class, on -> on
                                .eq(FundTemplateAssoEntity::getTemplateId, OrderContractEntity::getTemplateId))
                        .eq(OrderContractEntity::getDeleteFlag, 0)
                        .eq(OrderContractEntity::getOrderId, fddFinishFileEntityList.get(0).getPreId())

        );
        log.info("need sign, fundContractVOList.size: {}", fundContractVOList.size());
        //数字化盖章
        if (CollUtil.isNotEmpty(fddFinishFileEntityList)) {
            sendFileToDigitalize(fddFinishFileEntityList.get(0).getPreId());
        }

        CompletableFuture.supplyAsync(() -> {
            //保存需要资方签署的合同文件
            saveFundContractFile(fundContractVOList);
            return "ok";
        }).thenAccept(processResult -> log.info("保存需要资方签署的合同文件: {}", processResult));
    }

    /**
     * 将法大大签署完的文件更新到订单合同信息
     *
     * @param fddFinishFileEntityList fdd finish file 实体列表
     */
    private void updateOrderContractFile(List<PreFddFinishFileEntity> fddFinishFileEntityList) {
        for (PreFddFinishFileEntity preFddFinishFileEntity : fddFinishFileEntityList) {
            orderContractMapper.update(new LambdaUpdateWrapper<OrderContractEntity>()
                    .set(OrderContractEntity::getResource, preFddFinishFileEntity.getResourceFileUid())
                    // update by zangxx at 2024/10/24
                    //                    .set(OrderContractEntity::getSignStatus, ContractEnum.SIGNED.getCode())
                    .like(OrderContractEntity::getNumber, preFddFinishFileEntity.getFileName().split("_")[0])
                    .eq(OrderContractEntity::getDeleteFlag, 0));

        }
    }

    private void saveFundContractFile(List<FundContractVO> fundContractVOList) {
        if (CollUtil.isNotEmpty(fundContractVOList)) {
            FundContractVO contractVO = fundContractVOList.get(0);
            //合同重复签署数据处理
            List<ContractToFundEntity> contractToFundList = contractToFundMapper.selectList(new LambdaQueryWrapper<ContractToFundEntity>()
                    .eq(ContractToFundEntity::getOrderId, contractVO.getOrderId())
                    .eq(ContractToFundEntity::getDeleteFlag, 0));
            log.info("FadadaAuthServiceImpl.updateOrderContract orderId= {}, contractToFundList: {}", contractVO.getOrderId(), contractToFundList.size());
            if (CollUtil.isNotEmpty(contractToFundList)) {
                contractToFundMapper.update(new LambdaUpdateWrapper<ContractToFundEntity>().set(ContractToFundEntity::getDeleteFlag, 1)
                        .eq(ContractToFundEntity::getOrderId, contractVO.getOrderId()));
            }
            fundContractVOList.forEach(fundContractVO -> {
                //判断是否需要资方签署
                if (Objects.equals(fundContractVO.getFundSign(), ContractEnum.NEED_SIGN.getCode())) {
                    //保存要发送三方的合同信息
                    ContractToFundEntity contractToFundEntity = new ContractToFundEntity();
                    contractToFundEntity.setOrderId(fundContractVO.getOrderId());
                    contractToFundEntity.setFundId(fundContractVO.getFundId());
                    contractToFundEntity.setOriginalFileUid(fundContractVO.getFileUid());
                    contractToFundEntity.setOriginalFileName(fundContractVO.getName());
                    //资方线上合同新接口，不需要上传合同
                    contractToFundEntity.setIsSend(0);
                    contractToFundMapper.insert(contractToFundEntity);
                }
            });
        }
    }

    public void sendFileToDigitalize(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getId, orderId)
                .eq(OrderInfoEntity::getDeleteFlag, 0));
        Integer fundId = orderInfoEntity.getFundId();
        log.info("FadadaAuthServiceImpl.sendFileToDigitalize start, orderId: {}, fundId :{}", orderId, fundId);
        List<Integer> templateIdsList = new ArrayList<>();
        //查询发送给数字化的盖章文件
        if (Objects.equals(fundId, FundEnum.YING_FENG.getValue()) || Objects.equals(fundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue()) || Objects.equals(fundId, FundEnum.CHANG_YIN.getValue())) {
            if (Objects.equals(fundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())) {
                templateIdsList = Arrays.asList(yingFengTempId, FuMinToDigitalizeEnum.ZHTH_WTJYSQS.getCode());
            } else {
                templateIdsList = Arrays.asList(yingFengTempId, FuMinToDigitalizeEnum.LH_WTJYSQS.getCode());
            }
        } else if (Objects.equals(fundId, FundEnum.FU_MIN.getValue())) {
            templateIdsList = Arrays.asList(FuMinToDigitalizeEnum.FM_DBH.getCode(), FuMinToDigitalizeEnum.FM_GRWTJKHT.getCode(), FuMinToDigitalizeEnum.FM_WTJYHT.getCode(), FuMinToDigitalizeEnum.FM_WTJYHT_NEW.getCode(), FuMinToDigitalizeEnum.LH_WTJYSQS.getCode());
        } else if (Objects.equals(fundId, FundEnum.LAN_HAI.getValue())) {
            //融担公司
            ProductRongdanEntity productRongdanEntity = productRongdanMapper.selectById(orderInfoEntity.getRongdanId());
            if (ObjectUtil.equals(productRongdanEntity.getMiddleCode(), ProductRongDanEnum.ZUN_HAO.getCode())) {
                templateIdsList = Arrays.asList(yingFengTempId, FuMinToDigitalizeEnum.LH_ZH_DBH.getCode(), FuMinToDigitalizeEnum.LH_ZH_GRWTJKHT.getCode(), FuMinToDigitalizeEnum.LH_ZH_GRWTJKHT_NEW.getCode(), FuMinToDigitalizeEnum.LH_WTJYSQS.getCode());
            } else if (ObjectUtil.equals(productRongdanEntity.getMiddleCode(), ProductRongDanEnum.YIN_DING.getCode())) {
                templateIdsList = Arrays.asList(yingFengTempId, FuMinToDigitalizeEnum.LH_YD_DBH.getCode(), FuMinToDigitalizeEnum.LH_YD_GRWTHT.getCode(), FuMinToDigitalizeEnum.LH_WTJYSQS.getCode());
            }
        } else {
            return;
        }
        log.info("FadadaAuthServiceImpl.sendFileToDigitalize templateIdsList: {}", templateIdsList);

        List<OrderContractEntity> orderContractEntityList = orderContractMapper.selectList(new LambdaQueryWrapper<OrderContractEntity>()
                .select(OrderContractEntity::getOrderId, OrderContractEntity::getNumber, OrderContractEntity::getResource, OrderContractEntity::getName, OrderContractEntity::getTemplateId)
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .in(OrderContractEntity::getTemplateId, templateIdsList)
        );
        log.info("FadadaAuthServiceImpl.sendFileToDigitalize orderContractEntityList: {}", JSONUtil.toJsonStr(orderContractEntityList));
        if (CollUtil.isNotEmpty(orderContractEntityList)) {
            for (OrderContractEntity orderContractEntity : orderContractEntityList) {
                Result<String> result = resourceFeign.temporaryAccessRouteRequest(orderContractEntity.getResource(), 3);
                log.info("FadadaAuthServiceImpl.sendFileToDigitalize result:{}", result);
                String tempUrl = null;
                if (ResultCode.SUCCESS.getCode().equals(result.getCode())) {
                    tempUrl = result.getData();
                }

                ContractDTO contractDTO = new ContractDTO();
                contractDTO.setPdfUrl(tempUrl).setPdfName(orderContractEntity.getName() + ".pdf").setApplyNo(RandomUtil.randomNumbers(19));
                String keyword = "";
                //富民担保函
                if (Objects.equals(FuMinToDigitalizeEnum.FM_DBH.getCode(), orderContractEntity.getTemplateId())) {
                    keyword = FuMinToDigitalizeEnum.FM_DBH_KEY_WORD.getDescription();
                } else if (Objects.equals(FuMinToDigitalizeEnum.FM_GRWTJKHT.getCode(), orderContractEntity.getTemplateId()) || Objects.equals(FuMinToDigitalizeEnum.FM_WTJYHT_NEW.getCode(), orderContractEntity.getTemplateId())) {
                    keyword = FuMinToDigitalizeEnum.FFM_GRWTJKHT_KEY_WORD.getDescription();
                } else if (Objects.equals(FuMinToDigitalizeEnum.LH_ZH_DBH.getCode(), orderContractEntity.getTemplateId())) {
                    keyword = FuMinToDigitalizeEnum.LH_DBH_ZH_KEY_WORD.getDescription();
                } else if (Objects.equals(FuMinToDigitalizeEnum.LH_ZH_GRWTJKHT.getCode(), orderContractEntity.getTemplateId()) || Objects.equals(FuMinToDigitalizeEnum.LH_ZH_GRWTJKHT_NEW.getCode(), orderContractEntity.getTemplateId())) {
                    keyword = FuMinToDigitalizeEnum.LH_ZH_GRWTJKHT_KEY_WORD.getDescription();
                } else if (Objects.equals(FuMinToDigitalizeEnum.LH_YD_DBH.getCode(), orderContractEntity.getTemplateId())) {
                    keyword = FuMinToDigitalizeEnum.LH_DBH_YD_KEY_WORD.getDescription();
                } else if (Objects.equals(FuMinToDigitalizeEnum.LH_YD_GRWTHT.getCode(), orderContractEntity.getTemplateId())) {
                    keyword = FuMinToDigitalizeEnum.LH_YD_GRWTHT_KEY_WORD.getDescription();
                }

                contractDTO.setSignKeyword(keyword);
                log.info("sendFileToDigitalize contractDTO:{}", JSONUtil.toJsonStr(contractDTO));
                String data = "data=" + DateUtil.format(DateUtil.date(), "yyyyMMdd") + DigitalizeEnums.SECRET_KEY.getCode();
                String token = HmacSHA256Utils.hmacsha256(data, digitalizeKey);
                String respStr = digitalizeFeign.sendSignFile(orderId, token, contractDTO);
                log.info("sendFileToDigitalize digitalizeFeign.sendSignFile resp:{}", respStr);
                DigitalizeFddUrlVo digitalizeFddUrlVo = JacksonUtils.toObj(respStr, DigitalizeFddUrlVo.class);
                if (DigitalizeEnums.SUCCESS.getCode().equals(digitalizeFddUrlVo.getCode())) {
                    ThirdResourceDTO thirdResourceDTO = new ThirdResourceDTO();
                    String responseUrl = digitalizeFddUrlVo.getData().getDownloadUrl();
                    // 替换反斜杠
                    String cleanedPath = responseUrl.replace("\\", "");
                    log.info("sendFileToDigitalize cleanedPath:{}", cleanedPath);
                    thirdResourceDTO.setUrls(Collections.singletonList(cleanedPath));
                    thirdResourceDTO.setFileName(contractDTO.getPdfName());
                    thirdResourceDTO.setThirdName("fadada");
                    Result<List<FileResourceResultVO>> fileResult = resourceFeign.downLoadThirdResource(thirdResourceDTO);
                    log.info("sendFileToDigitalize resourceFeign downLoadThirdResource resp:{}", fileResult);
                    if (ResultCode.SUCCESS.getCode().equals(fileResult.getCode())) {
                        List<FileResourceResultVO> fileVOList = fileResult.getData();
                        if (CollUtil.isNotEmpty(fileVOList)) {
                            int digitalizeFileCount = orderContractMapper.update(new LambdaUpdateWrapper<OrderContractEntity>()
                                    .set(OrderContractEntity::getResource, fileVOList.get(0).getFileUid())
                                    .eq(OrderContractEntity::getOrderId, orderId)
                                    .eq(OrderContractEntity::getTemplateId, orderContractEntity.getTemplateId())
                                    .eq(OrderContractEntity::getDeleteFlag, 0)
                            );
                            log.info("数字化更新结果digitalizeFileCount ={}", digitalizeFileCount);
                        }
                    }
                }
            }

        }

    }

    @Override
    @Transactional
    public boolean processFile(List<String> fileUids) {
        List<FddFileResourceVO> resourceVOList = fileToFddMapper.selectJoinList(FddFileResourceVO.class, new MPJLambdaWrapper<FileToFddEntity>()
                .select(FileToFddEntity::getFddFileUrl, FileToFddEntity::getResourceFileUid)
                .selectAs(FileToFddEntity::getId, FddFileResourceVO::getFddId)
                .select(FileResourceEntity::getFileName, FileResourceEntity::getFileOldName)
                .innerJoin(FileResourceEntity.class, FileResourceEntity::getFileUid, FileToFddEntity::getResourceFileUid)
                .in(FileToFddEntity::getResourceFileUid, fileUids)
                .orderByAsc(FileToFddEntity::getId)
        );

        if (CollUtil.isEmpty(resourceVOList)) {
            throw new BusinessException("未获取到文件信息");
        }

        //获取token
        String accessToken = getFddToken();
        OpenApiClient apiClient = fadadaConfig.appClient();
        DocClient docClient = new DocClient(apiClient);
        FileProcessReq fileProcessReq = new FileProcessReq();
        boolean flag = false;
        try {
            fileProcessReq.setAccessToken(accessToken);
            List<FddFileUrl> fddFileUrls = new ArrayList<>();
            resourceVOList.forEach(fddFileResourceVO -> {
                FddFileUrl fddFileUrl = new FddFileUrl();
                fddFileUrl.setFddFileUrl(fddFileResourceVO.getFddFileUrl());
                //doc:用于签署（签字盖章）的文档，后续创建签署任务时使用，需要转换成pdf或ofd。
                fddFileUrl.setFileType("doc");
                fddFileUrl.setFileName(fddFileResourceVO.getFileName());
                fddFileUrls.add(fddFileUrl);
            });
            //            log.info("fddFileUrl={}", JSONUtil.toJsonStr(fddFileUrls));
            log.info("processFile fileProcessReq = {}", JSONUtil.toJsonStr(fileProcessReq));
            fileProcessReq.setFddFileUrlList(fddFileUrls);
            BaseRes<FileProcessRes> fileProcessBaseRes = docClient.process(fileProcessReq);
            log.info("processFile fileProcessBaseRes = {}", JSONUtil.toJsonStr(fileProcessBaseRes));
            if (FadadaEnum.SUCCESS.getCode().equals(fileProcessBaseRes.getCode())) {
                //更新文件数据
                flag = this.updateFileData(fileProcessBaseRes.getData());
            } else {
                throw new BusinessException(fileProcessBaseRes.getMsg());
            }
        } catch (ApiException e) {
            throw new BusinessException(e);
        }
        return flag;
    }

    /**
     * 更新资方授权文件控件与文件对应关系
     *
     * @param fileProcessRes
     * @return
     */
    private boolean updateFileData(FileProcessRes fileProcessRes) {
        //        List<FileToFddEntity> entities = new ArrayList<>();
        List<FileId> fileIdList = fileProcessRes.getFileIdList();
        for (int i = 0; i < fileIdList.size(); i++) {
            FileResourceEntity fileResource = fileResourceMapper.selectOne(new LambdaQueryWrapper<FileResourceEntity>().eq(FileResourceEntity::getFileName, fileIdList.get(i).getFileName()));
            fileToFddService.update(new LambdaUpdateWrapper<FileToFddEntity>()
                    .set(FileToFddEntity::getFddFileId, fileProcessRes.getFileIdList().get(i).getFileId())
                    .eq(FileToFddEntity::getResourceFileUid, fileResource.getFileUid()));

            fddFieldConfigMapper.update(new LambdaUpdateWrapper<FddFieldConfigEntity>()
                    .set(FddFieldConfigEntity::getFileUid, fileResource.getFileUid())
                    .eq(FddFieldConfigEntity::getFileName, fileResource.getFileOldName().split("\\.")[0]));
        }

        return true;
    }


    private String getFddToken() {

        // 尝试从缓存读取token
        Boolean hasKey = redisService.hasKey(FDD_TOKEN_CACHE_KEY);
        if (hasKey) {
            String accessToken = redisService.get(FDD_TOKEN_CACHE_KEY, String.class);
            log.info("fdd accessToken: {}", accessToken);
            return accessToken;
        }

        //生成签名
        Map<String, String> paramMap = FadadaUtils.getSignature(fadadaConfig.getAppId(), fadadaConfig.getAppSecret());
        log.info("getFddToken fadadaConfig.getAppId()={}, fadadaConfig.getAppSecret()={}, paramMap:{}", fadadaConfig.getAppId(), fadadaConfig.getAppSecret(), JSONUtil.toJsonStr(paramMap));
        String tokenStr = fadadaFeign.getFDDAccessToken(fadadaConfig.getAppId(), fadadaConfig.getAppSecret(), paramMap.get("X-FASC-Timestamp"), paramMap.get("X-FASC-Nonce"), paramMap.get("X-FASC-Sign"));
        FadadaTokenRespDTO respDTO = JacksonUtils.toObj(tokenStr, FadadaTokenRespDTO.class);

        log.info("tokenStr:{}", tokenStr);

        if (!FadadaEnum.SUCCESS.getCode().equals(respDTO.getCode())) {
            throw new BusinessException(respDTO.getMsg());
        }

        saveRequestLog(0, RequestResponseCode.FDD_AUTH_TOKEN, "", "", JSONUtil.toJsonStr(paramMap),
                JSONUtil.toJsonStr(respDTO));

        FadadaTokenRespDTO.TokenDTO data = respDTO.getData();

        String accessToken = data.getAccessToken();
        Long expiresIn = Convert.toLong(data.getExpiresIn(), 7200L);

        redisService.set(FDD_TOKEN_CACHE_KEY, accessToken, expiresIn - 200, TimeUnit.SECONDS);


        return redisService.get(FDD_TOKEN_CACHE_KEY, String.class);

    }

    /**
     * 保存请求日志
     *
     * @param tableId 表 ID
     * @param path    路径
     * @param req     请求参数
     * @param resp    响应结果
     */
    private void saveRequestLog(Integer tableId, RequestResponseCode code, String tableName, String path, String req, String resp) {

        log.info("saveRequestLog start");
        RequestResponseInfoEntity requestResponseInfoEntity = new RequestResponseInfoEntity()
                .setTId(tableId)
                .setTName(tableName)
                .setAccessPath(path)
                .setCode(code)
                .setRequestBody(req)
                .setResponseBody(resp);
        requestResponseInfoMapper.insert(requestResponseInfoEntity);

        log.info("saveRequestLog end");

    }

    /**
     * 假设sanitizeJSON是进行脱敏处理的方法
     *
     * @param json JSON格式
     * @return {@link String }
     */
    private String sanitizeJson(String json) {
        // 敏感信息的脱敏
        return json;
    }

    @Override
    public boolean uploadFileV2(List<FileTOFddVO> voList) {
        log.debug("uploadFileV2 voList size:{}", voList.size());

        voList.forEach(vo -> {

            String resourceId = vo.getFileUid();

            if (vo.getFileBytes() == null || vo.getFileBytes().length == 0) {
                log.error("uploadFileV2 resourceId:{} not found ", resourceId);
                throw new BusinessException("文件不存在");
            }

            //根据uploadUrl上传用于签署的本地文档
            GetUploadUrlRes data = getGetUploadUrlRes();

            String uploadUrl = data.getUploadUrl();
            String fddFileUrl = data.getFddFileUrl();

            log.info("uploadFileV2 resourceId:{} uploadUrl:{}", resourceId, uploadUrl);

            FileToFddEntity fileToFddEntity = new FileToFddEntity().setFddFileUrl(fddFileUrl)
                    .setFddUploadUrl(uploadUrl)
                    .setResourceFileUid(resourceId)
                    .setFileBelong(vo.getFileBelong())
                    .setFilePurpose(FadadaEnum.FILE_CONTRACT.getCode())
                    .setTemplateId(ContractTemplateEnum.LH_FM_RZDB_CONTRACT.getCode());

            //拿到法大大文件上传路径进行文件上传
            uploadFileToFddWithUploadUrl2(vo.getFileBytes(), uploadUrl);

            fileToFddService.save(fileToFddEntity);

        });

        return true;
    }

    /**
     * 插入预审节点记录
     *
     * @param preId 预审ID
     */
    private void insertPreNodeRecord(Integer preId) {
        try {
            PreNodeRecordEntity preNodeRecord = new PreNodeRecordEntity()
                    .setPreId(preId)
                    .setCurrentNode(States.SIGN_AUTHORIZATION_CONTRACT.getNode())
                    .setNextNode(States.RISK_CONTROL.getNode())
                    .setRemark("授权书签署完成");

            preNodeRecordMapper.insert(preNodeRecord);
            log.info("FadadaAuthServiceImpl.insertPreNodeRecord success, preId:{}", preId);
        } catch (Exception e) {
            log.error("FadadaAuthServiceImpl.insertPreNodeRecord error, preId:{}, error:{}",
                    preId, e.getMessage(), e);
            throw new BusinessException("保存预审节点记录失败");
        }
    }
}
