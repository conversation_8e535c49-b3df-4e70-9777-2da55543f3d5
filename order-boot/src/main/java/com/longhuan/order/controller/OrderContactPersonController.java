package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.converter.OrderContactPersonConverter;
import com.longhuan.order.pojo.entity.OrderContactPersonEntity;
import com.longhuan.order.pojo.vo.OrderContactPersonVo;
import com.longhuan.order.service.OrderContactPersonService;
import io.swagger.annotations.ApiModelProperty;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 订单联系人控制器
 *
 * <AUTHOR>
 * @Date 2024/7/24
 */
@RestController
@RequestMapping("/orderContactPerson")
@RequiredArgsConstructor
public class OrderContactPersonController {
    private final OrderContactPersonService orderContactPersonService;
    private final OrderContactPersonConverter orderContactPersonConverter;


    /**
     * 通过订单id查询订单联系人信息
     *
     * @param orderId 订单id
     * @return {@link Result}
     */
    @ApiModelProperty(value = "通过订单id查询订单联系人信息")
    @GetMapping("/getOrderContactPersonList/{orderId}")
    public Result<List<OrderContactPersonVo>> getOrderContactPersonList(@PathVariable("orderId")Integer orderId) {
        List<OrderContactPersonEntity> personEntityList = orderContactPersonService.queryByOrderIdList(orderId);
        List<OrderContactPersonVo> contactPersonVoList = orderContactPersonConverter.personList2VoList(personEntityList);
        return Result.success(contactPersonVoList);
    }
}
