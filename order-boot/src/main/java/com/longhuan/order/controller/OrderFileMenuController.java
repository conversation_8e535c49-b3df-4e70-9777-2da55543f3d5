package com.longhuan.order.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.vo.MenuVO;
import com.longhuan.order.service.OrderFileMenuService;
import com.longhuan.order.service.OrderSuperaddFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单文件菜单控制器
 *
 * <AUTHOR>
 * @date 2024/07/26
 */
@Api(tags = "订单文件菜单")
@RestController
@RequestMapping("/api/v1/order/menu")
@RequiredArgsConstructor
public class OrderFileMenuController {
    private final OrderFileMenuService orderFileMenuService;
    private final OrderSuperaddFileService orderSuperaddFileService;

    /**
     * 菜单文件
     *
     * @param menuFileDTO 菜单文件 dto
     * @return {@link Result }<{@link List }<{@link Tree }<{@link Integer }>>>
     */
    @ApiOperation(value = "获取文件目录列表")
    @PostMapping("/menu_file")
    public Result<List<Tree<Integer>>> menuFile(@Validated @RequestBody MenuFileDTO menuFileDTO) {
        return Result.success(orderFileMenuService.menuFile(menuFileDTO));
    }

    /**
     * 订单文件
     *
     * @param orderFileDTO 订单文件 DTO
     * @return {@link Result }<{@link List }<{@link MenuVO }>>
     */
    @PostMapping("/order_file")
    public Result<List<MenuVO>> orderFile(@Validated @RequestBody OrderFileDTO orderFileDTO) {
        return Result.success(orderFileMenuService.orderFile(orderFileDTO));
    }

    /**
     * 保存订单文件
     *
     * @param saveOrderFileDTO 保存订单文件 DTO
     * @return {@link Result }<{@link Integer }>
     */
    @PostMapping("/save_order_file")
    public Result<Integer> saveOrderFile(@Validated @RequestBody SaveOrderFileDTO saveOrderFileDTO) {
        return Result.success(orderFileMenuService.saveOrderFile(saveOrderFileDTO));
    }

    /**
     * 批量保存订单文件
     */
    @PostMapping("/batchOrderFile")
    public Result<Boolean> batchOrderFile(@Validated @RequestBody BatchSaveOrderFileDTO batchSaveOrderFileDTO) {
        return Result.success(orderFileMenuService.batchSaveOrderFile(batchSaveOrderFileDTO));
    }

    /**
     * 删除订单文件
     *
     * @param removeOrderFileDTO 删除订单文件 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @PostMapping("/remove_order_file")
    public Result<Boolean> removeOrderFile(@Validated @RequestBody RemoveOrderFileDTO removeOrderFileDTO) {
        List<Integer> orderFileId = removeOrderFileDTO.getOrderFileId();
        // 订单数据
        List<Integer> positiveNumbers = orderFileId.stream()
                .filter(n -> n > 0)
                .toList();
        // 负数 补件数据
        List<Integer> negativeNumbers = orderFileId.stream()
                .filter(n -> n < 0)
                .map(Math::abs)
                .toList();


        Boolean orderFileRemove = false;
        if (CollUtil.isNotEmpty(positiveNumbers)){
            removeOrderFileDTO.setOrderFileId(positiveNumbers);
             orderFileRemove = orderFileMenuService.removeOrderFile(removeOrderFileDTO);
        }
        Boolean superAddRemove = false;
        if (CollUtil.isNotEmpty(negativeNumbers)){
             superAddRemove = orderSuperaddFileService.removeFileList(negativeNumbers);
        }
        return Result.judge(orderFileRemove ||superAddRemove);
    }

    /**
     * 暂存订单文件
     *
     * @param saveOrderFileDTO 保存订单文件 DTO
     * @return {@link Result }<{@link Integer }>
     */
    @PostMapping("/stagingOrderFile")
    public Result<Integer> stagingOrderFile(@Validated @RequestBody SaveOrderFileDTO saveOrderFileDTO) {
        return Result.success(orderFileMenuService.stagingOrderFile(saveOrderFileDTO));
    }

}
