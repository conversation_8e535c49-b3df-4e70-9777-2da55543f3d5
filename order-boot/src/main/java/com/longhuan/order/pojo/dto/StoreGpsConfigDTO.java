package com.longhuan.order.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.List;

/**
 * 门店GPS分期和收费策略配置DTO
 *
 * <AUTHOR>
 * @date 2025/01/01
 */
@Data
@Accessors(chain = true)
public class StoreGpsConfigDTO {
    /**
     * 支持免费GPS服务的门店ID列表
     */
    @ApiModelProperty(value = "支持免费GPS服务的门店ID列表")
    private List<Integer> freeStoreIds;

    /**
     * 支持分期付款的门店ID列表
     */
    @ApiModelProperty(value = "支持分期付款的门店ID列表")
    private List<Integer> installmentStoreIds;
}
