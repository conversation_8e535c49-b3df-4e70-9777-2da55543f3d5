package com.longhuan.approve.boot.pojo.dto.mzkeshang.request;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * LoanAplyNO 请求参数
 */
@Data
@Accessors(chain = true)
public class KeShangLoanAplyNoRequest {

    /**
     * 贷款申请编号
     * 1、唯一标识
     * 2、规则：平台号(4位大写字母)+渠道号(4位大写字母)+请求时间(14位yyyyMMddHHMISS)+序号(8位)
     * 最大长度：30
     */
    @JsonProperty("LoanAplyNO")
    private String loanAplyNO;


}