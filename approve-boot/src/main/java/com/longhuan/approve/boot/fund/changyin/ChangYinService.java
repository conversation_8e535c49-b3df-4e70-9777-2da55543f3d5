package com.longhuan.approve.boot.fund.changyin;

import com.longhuan.approve.api.pojo.dto.changyin.*;
import com.longhuan.approve.api.pojo.vo.changyin.ChangLPRResDTO;
import com.longhuan.approve.api.pojo.vo.changyin.ChangYinContractPreviewVO;
import com.longhuan.approve.api.pojo.vo.changyin.ChangYinLoanTrialResponseDTO;
import com.longhuan.approve.boot.enums.changyin.ChangYinDictEnum;
import com.longhuan.approve.boot.pojo.dto.FundFinalBaseDTO;
import com.longhuan.approve.boot.pojo.dto.FundPreBaseDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.ChangYinCreditQueryDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.ChangYinLimitAdjustApplyDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.ChangYinLimitAdjustQueryDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.ChangYinLoanQueryDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.response.*;

import java.time.LocalDate;
import java.util.List;

public interface ChangYinService {

    /**
     * 预授信申请
     */
    void approvePreApply(FundPreBaseDTO fundPreBaseDTO);

    /**
     * 预授信申请
     */
    void approvePreApplyQuery(Integer preId);

    /**
     * 长银预授信申请
     */
    ChangYinResBodyDTO<ChangYinPreApprovalResDTO> preApproveApplyV2(FundPreBaseDTO preBaseDTO);



    /**
     * 长银预授信申请查询
     */
    ChangYinResBodyDTO<ChangYinPreApprovalResDTO> preApproveQueryV2ByPreId(Integer preId);



    /**
     * 3.2 授信申请
     */
    ChangYinResBodyDTO<ChangYinPreApprovalInfoResDTO> approveApplyV2(FundFinalBaseDTO fundFinalBaseDTO, String fundFinalNumber);



    /**
     * 授信状态查询
     */
    ChangYinResBodyDTO<ChangYinCreditQueryResDTO>  creditQueryV2ByOrderId(Integer orderId);

    /**
     * 3.5 用信申请
     */
    ChangYinResBodyDTO<ChangYinLoanApplicationRespDTO> preLoanApplyV2ByOrderId(Integer orderId, String contractNumber);

    /**
     * 签约申请
     */
    ChangYinResBodyDTO<ChangYinSignResDTO> protocolApply(ChangYinSignInfoDTO changYinSignInfoDTO);
    /**
     * 签约确认
     */
    ChangYinResBodyDTO<ChangYinSignResDTO> protocolConfirm(ChangYinSignConfirmDTO changYinSignConfirmDTO);

    /**
     * 授信状态查询
     */
    ChangYinResBodyDTO<ChangYinCreditQueryResDTO>  creditQueryV2(ChangYinCreditQueryDTO changYinCreditQueryDTO);
    /**
     * 授信回调
     */
    ChangYinResBodyDTO<ChangYinCallbackResDTO> handleApplyCallback(String request);

    /**
     * 用信回调
     */
    ChangYinResBodyDTO<ChangYinCallbackResDTO> handlePreLoanCallback(String request);

    /**
     * 放款回调
     */
    ChangYinResBodyDTO<ChangYinCallbackResDTO> handlePayCallback(String request);

    /**
     * 放款申请
     */
    ChangYinResBodyDTO<ChangYinLoanApplyResDTO> loanApplyV2ByOrderId(Integer orderId);


    /**
     * 放款状态查询
     */
    ChangYinResBodyDTO<ChangYinLoanQueryResDTO> loanQueryV2(ChangYinLoanQueryDTO changYinLoanQueryDTO);


    /**
     * 放款状态查询
     */
    ChangYinResBodyDTO<ChangYinPreLoanQueryResDTO> preLoanQueryV2ByOrderId(Integer orderId);


    /**
     * 放款状态查询
     */
    ChangYinResBodyDTO<ChangYinPreLoanQueryResDTO> preLoanQueryV2(ChangYinPreLoanQueryDTO changYinPreLoanQueryDTOe);

    /**
     * 身份证更新查询
     */
    ChangYinResBodyDTO<ChangYinIdCardUpdateQueryResDTO> idCardUpdateQuery(Integer orderId);
    /**
     * 身份证更新申请
     */
    ChangYinResBodyDTO<ChangYinIdCardUpdateApplyResDTO> idCardUpdateApply(Integer orderId);

    /**
     * 卡号变更
     */
    ChangYinResBodyDTO<ChangYinAccountChangeResDTO> changeAccountApply(ChangYinUpdateAccountDTO changYinUpdateAccountDTO);

    /**
     * 贷款用途上传
     */
    ChangYinResBodyDTO<ChangYinLoanUploadResDTO> loanPurposeUpload(Integer orderId);


    /**
     * 更新订单支付状态
     */
    void updateOrderPaymentStatus(ChangYinResBodyDTO<ChangYinPayHandlerDTO> resultDTO);

    /**
     * 下载还款计划表
     * @param orderId
     */
    void repaymentPlanInit(Integer orderId,ChangYinDictEnum.RepaymentPlanQueryType enqTyp);


    /**
     * 更新还款计划表应还
     */
    void repaymentPlanRepay(Integer orderId,ChangYinDictEnum.RepaymentPlanQueryType enqTyp);

    /**
     * 更新还款计划表实还
     */
    Boolean repaymentPlanSettle(Integer orderId);

    /**
     * 还款计划查询
     */
    ChangYinResBodyDTO<ChangYinRepaymentPlanQueryResDTO> changYinRepaymentPlanQueryV2(Integer orderId,ChangYinDictEnum.RepaymentPlanQueryType enqTyp);

    Integer getOrderIdByOrderNo(String orderNo);
    /**
     * 下载资方合同文件
     */
    List<ChangYinContractPreviewVO> downloadFundContract(Integer orderId);

    /**
     * lpr查询
     */
    ChangYinResBodyDTO<ChangLPRResDTO> queryLPR(ChangYinLPRDTO changYinLPRDTO);

    /**
     * 还款试算
     */
    ChangYinResBodyDTO<ChangYinRepaymentTrialResDTO> repaymentTrial(Integer orderId, ChangYinDictEnum.RepaymentMode changYinRepayType,Integer term);

    /**
     * 还款申请
     */
    ChangYinResBodyDTO<ChangYinRepaymentApplyResDTO> repaymentApply(Integer orderId);

    /**
     * 线下还款账户查询
     */
    ChangYinResBodyDTO<ChangYinCustRepayAccountQueryResDTO> custRepayAccountQuery(Integer orderId);

    ChangYinResBodyDTO<ChangYinCallbackResDTO> handleRepayCallback(String requestJson);

    /**
     * 还款文件下载
     */
    void syncRepaymentUpdateBatch(LocalDate startDate);


    /**
     * 放款明细文件同步
     */
    void syncPaymentDetailFileBatch(LocalDate startDate);

    List<ChangYinContractPreviewVO> downCardChangeInOutSupply(Integer orderId);

    /**
     * 还款计划初始化
     */
    void initRepaymentPlan();


    /**
     * 还款计划文件下载
     */
    void repayPlanFileBatch();


    /**
     * 结清证明申请
     */
    ChangYinResBodyDTO<ChangYinSignatureApplyResDTO> signatureApplyByOrderId(Integer orderId);

    /**
     * 结清证明查询下载
     */
    ChangYinResBodyDTO<ChangYinSignatureQueryResDTO> signatureQueryByOrderId(Integer orderId);


    /**
     * 资产正向调额申请根据订单
     */
    boolean limitAdjustApplyV2ByOrderId(Integer orderId);

    /**
     * 资产正向调额结果查询根据订单
     */
    ChangYinResBodyDTO<ChangYinLimitAdjustQueryResDTO> limitAdjustQueryV2ByOrderId(Integer orderId);


    /**
     * 资产正向调额申请
     */
    ChangYinResBodyDTO<ChangYinLimitAdjustApplyResDTO> limitAdjustApplyV2(ChangYinLimitAdjustApplyDTO changYinLimitAdjustApplyDTO);

    /**
     * 资产正向调额结果查询
     */
    ChangYinResBodyDTO<ChangYinLimitAdjustQueryResDTO> limitAdjustQueryV2(ChangYinLimitAdjustQueryDTO changYinLimitAdjustQueryDTO);

    /**
     * 贷后预代偿文件拉取
     */
    void getPreIndemnityFile(String jobTime);

    /**
     * 贷后代偿文件拉取
     */
    void getIndemnityFile(String jobTime);

    /**
     * 根据订单贷款试算
     */
    ChangYinResBodyDTO<ChangYinLoanTrialResponseDTO> loanTrialV2ByOrder(ChangYinLoanTrialByOrderDTO changYinLoanTrialByOrderDTO);


}
