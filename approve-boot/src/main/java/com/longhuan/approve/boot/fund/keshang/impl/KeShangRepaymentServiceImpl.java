package com.longhuan.approve.boot.fund.keshang.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.longhuan.approve.boot.fund.finall.FinalFundInfoService;
import com.longhuan.approve.boot.fund.keshang.KeShangRepaymentService;
import com.longhuan.approve.boot.pojo.dto.FundFinalBaseDTO;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.KeShangResponseHead;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.request.KeShangLoanAplyNoRequest;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.request.KeShangRepaymentDetailRequest;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.request.KeShangRepaymentResultRequest;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.response.KeShangRepaymentPlanResponse;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.response.KeShangRepaymentResultResponse;
import com.longhuan.approve.boot.pojo.entity.FinalFundInfoEntity;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.web.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * KeShangRepaymentService
 *
 * @date 2025/9/2 10:11
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class KeShangRepaymentServiceImpl implements KeShangRepaymentService {

    private final FinalFundInfoService finalFundInfoService;

    @Override
    public KeShangRepaymentResultResponse queryRepaymentResult(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangRepaymentResultRequest request = new KeShangRepaymentResultRequest();
        // todo 请求客商接口
        KeShangRepaymentResultResponse response = new KeShangRepaymentResultResponse();
        return null;
    }

    @Override
    public Boolean repaymenPlanQuery(Integer orderId) {

        KeShangResponseHead result = null;
        try {
            FinalFundInfoEntity finalFundInfoEntity = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.LAN_HAI);
            if (ObjUtil.isNull(finalFundInfoEntity)) {
                throw new BusinessException("资方申请信息不存在");
            }
            KeShangLoanAplyNoRequest request = new KeShangLoanAplyNoRequest();
            //todo 请求参数 LoanAplyNO ==>loanNo ?
            request.setLoanAplyNO(finalFundInfoEntity.getLoanNo());

            log.info("KeShangServiceImpl.repaymenPlanQuery request:{}", JSONUtil.toJsonStr(request));

            // todo 请求客商接口

            log.info("KeShangServiceImpl.repaymenPlanQuery result:{}", JSONUtil.toJsonStr(result));
            if (!KeShangResponseHead.isSuccess(result)) {
                throw new BusinessException("还款计划查询失败" + result.getRetMsg());
            }
            //todo 解析数据  result.getInfoContent();
            // 处理数据
            KeShangRepaymentPlanResponse response = new KeShangRepaymentPlanResponse();
            List<KeShangRepaymentPlanResponse.RepaymentPlanItem> repayPlanList = response.getRepaymentPlanList();
            if (CollUtil.isEmpty(repayPlanList)) {
                throw new BusinessException("还款计划查询为空");
            }
            // todo 批量更新还款计划
            //batchUpdateRepaymentPlans(orderId, repayPlanList);

        } catch (Exception e) {
            log.info("KeShangServiceImpl.queryRepayPlan orderId:{} error:{}", orderId, e.getMessage(), e);
            throw new BusinessException("还款计划更新失败");
        }
        return true;
    }

    @Override
    public KeShangRepaymentPlanResponse.RepaymentPlanItem repaymentPlanInfo(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangRepaymentPlanResponse.RepaymentPlanItem repaymentPlanItem = new KeShangRepaymentPlanResponse.RepaymentPlanItem();
        return null;
    }

    @Override
    public KeShangRepaymentDetailRequest repaymentDetailInfo(FundFinalBaseDTO fundFinalBaseDTO) {
        // todo 请求参数
        KeShangRepaymentDetailRequest request = new KeShangRepaymentDetailRequest();

        return null;
    }
}
