package com.longhuan.order.statemachine.action;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.longhuan.common.core.enums.RoleEnum;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.service.OrderService;
import com.longhuan.order.service.UserApproveQueueService;
import com.longhuan.order.service.impl.OrderSendMessageImpl;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.user.enums.TodoInfoEnums;
import com.longhuan.user.pojo.dto.TodoInfoMessageDTO;
import com.longhuan.user.pojo.dto.getUserIdByStoreIdAndRoleIdDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 质检提交
 * <p>
 * 状态转移动作，在转移进行的时候触发的动作
 */
@Slf4j
@Component
public class RiskFirstApproveAction extends OrderNodeAction implements Action<States, Events> {
    private final OrderService orderService;
    private final OrderSendMessageImpl orderSendMessage;
    private final ApproveFeign approveFeign;
    private final DeptQuotaRecordMapper deptQuotaRecordMapper;
    private final DeptQuotaMapper deptQuotaMapper;
    private final OrderApproveDistributeMapper orderApproveDistributeMapper;
    private final UserFeign userFeign;
    private final DistributeAreaMapper distributeAreaMapper;
    private final RedisService redisService;
    private  final UserApproveQueueService userApproveQueueService;
    private final PreApprovalApplyInfoMapper preApprovalApplyInfoMapper;
    public RiskFirstApproveAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper,
                                  OrderService orderService, OrderSendMessageImpl orderSendMessage,
                                  ApproveFeign approveFeign, DeptQuotaRecordMapper deptQuotaRecordMapper, DeptQuotaMapper deptQuotaMapper, OrderApproveDistributeMapper orderApproveDistributeMapper, UserFeign userFeign, DistributeAreaMapper distributeAreaMapper, RedisService redisService, UserApproveQueueService userApproveQueueService,PreApprovalApplyInfoMapper preApprovalApplyInfoMapper) {
        super(orderInfoMapper, orderNodeRecordMapper);
        this.orderService = orderService;
        this.orderSendMessage = orderSendMessage;
        this.approveFeign = approveFeign;
        this.deptQuotaRecordMapper = deptQuotaRecordMapper;
        this.deptQuotaMapper = deptQuotaMapper;
        this.orderApproveDistributeMapper = orderApproveDistributeMapper;
        this.userFeign = userFeign;
        this.distributeAreaMapper = distributeAreaMapper;
        this.redisService = redisService;
        this.userApproveQueueService = userApproveQueueService;
        this.preApprovalApplyInfoMapper = preApprovalApplyInfoMapper;
    }


    @Override
    public void execute(StateContext<States, Events> context) {

        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();
        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        String remark = messageHeaders.get(StateConstants.REMARK, String.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);

        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event,
                currentNode, nextNode);

        // 1. 修改订单表状态
        if (Boolean.TRUE.equals(target.getId().getValid())) {
            saveOrderNode(orderId, nextNode);
        }

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);


        try {

//            userApproveQueueService.addUserToQueue(userId);

            // 3. 发送消息
            orderSendMessage.sendMessage(orderId, currentNode, context.getEvent().getCode());
            // 3.送入待分配任务池
            if (ObjUtil.equals(States.QUALITY_INSPECTION.getNode(), nextNode)) {
                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
                String lockValue = IdUtil.randomUUID();
                String lockKey = "OrderApproveDistributeServiceImpl:assignRiskFirstApproval:" + orderInfoEntity.getOrderNumber();
                try {
                    if (redisService.tryLock(lockKey, lockValue, 10)) {
                        DistributeAreaEntity distributeAreaEntity = distributeAreaMapper.selectOne(new LambdaQueryWrapper<DistributeAreaEntity>()
                                .eq(DistributeAreaEntity::getStoreId, orderInfoEntity.getDeptId())
                                .eq(DistributeAreaEntity::getServiceDispatch, 1)
                                .eq(DistributeAreaEntity::getDeleteFlag, 0)
                                .orderByDesc(DistributeAreaEntity::getCreateTime)
                                .last("limit 1"));
                        if (distributeAreaEntity != null) {
                            Long count = orderApproveDistributeMapper.selectCount(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                                    .eq(OrderApproveDistributeEntity::getOrderNumber, orderInfoEntity.getOrderNumber())
                                    .eq(OrderApproveDistributeEntity::getOrderId, orderId)
                                    .eq(OrderApproveDistributeEntity::getNode, nextNode)
                                    .in(OrderApproveDistributeEntity::getState, 0, 1, 2, 5));
                            if (count==0) {
                                OrderApproveDistributeEntity orderApproveDistributeEntity = new OrderApproveDistributeEntity()
                                        .setOrderNumber(orderInfoEntity.getOrderNumber())
                                        .setOrderId(orderId)
                                        .setNode(nextNode)
                                        .setNodeName(States.getByNode(nextNode).getDesc())
                                        .setSource(0)
                                        .setStoreId(orderInfoEntity.getDeptId())
                                        .setTitle(orderInfoEntity.getCustomerName() + "-" + orderInfoEntity.getVehicleNumber() + "-" + orderInfoEntity.getStoreName())
                                        .setState(0);
                                orderApproveDistributeEntity.setCustomerName(orderInfoEntity.getCustomerName())
                                        .setCustomerPhone(orderInfoEntity.getCustomerPhone())
                                        .setManagerId(orderInfoEntity.getManagerId())
                                        .setStoreName(orderInfoEntity.getStoreName())
                                        .setRegionName(orderInfoEntity.getRegionName())
                                        .setVehicleNumber(orderInfoEntity.getVehicleNumber())
                                        .setStepDispose(currentNode <= nextNode ? 0 : 1);
                                orderApproveDistributeMapper.insert(orderApproveDistributeEntity);
                            }
                        } else {
                            List<Integer> userIds = userFeign.getUserIdByStoreIdAndRoleId(new getUserIdByStoreIdAndRoleIdDTO().setDeptId(orderInfoEntity.getDeptId()).setRoleId(RoleEnum.CUSTOMER_SERVICE_SPECIALIST.getId())).getData();
                            List<TodoInfoMessageDTO.TodoUser> userDto = userFeign.searchUserNameBatch(userIds).getData().stream().map(e -> new TodoInfoMessageDTO.TodoUser().setName(e.getName()).setJobNumber(e.getJobNumber()).setPhoneNumber(e.getMobile())).toList();

                            userFeign.dealMessage(new TodoInfoMessageDTO()
                                    .setOrderNumber(orderInfoEntity.getOrderNumber())
                                    .setOrderId(orderInfoEntity.getId())
                                    .setNode(TodoInfoEnums.getByNode(nextNode))
                                    .setSourceType(0)
                                    .setRemark("订单：" + orderInfoEntity.getOrderNumber() + "待处理")
                                    .setState(0)
                                    .setTitle(orderInfoEntity.getCustomerName() + "-" + orderInfoEntity.getVehicleNumber() + "-" + orderInfoEntity.getStoreName())
                                    .setAccessUrl(null)
                                    .setTodoUserList(userDto)
                                    .setCustomerName(orderInfoEntity.getCustomerName())
                                    .setCustomerPhone(orderInfoEntity.getCustomerPhone())
                                    .setManagerId(orderInfoEntity.getManagerId())
                                    .setStoreName(orderInfoEntity.getStoreName())
                                    .setRegionName(orderInfoEntity.getRegionName())
                                    .setVehicleNumber(orderInfoEntity.getVehicleNumber()));
                        }
                    }
                } finally {
                    redisService.releaseLock(lockKey, lockValue);
                }
            }

        } catch (Exception e) {
            log.error("orderDistributionManual e {}", e.getMessage(), e);
        }

        if (Objects.equals(nextNode, States.FUNDS_FINAL_APPROVE.getNode())) {

            orderService.pushFundApprovalFinal(orderId );
        }

        if (Objects.equals(nextNode, States.PROCESS_TERMINAL.getNode()) || Objects.equals(nextNode, States.SYSTEM_TERMINAL.getNode())){
            LocalDate now = LocalDate.now();
            deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                    .set(DeptQuotaEntity::getEnable, 1)
                    .lt(DeptQuotaEntity::getEndDate, now)
                    .eq(DeptQuotaEntity::getEnable, 0)
                    .eq(DeptQuotaEntity::getDeleteFlag, 0));
            DeptQuotaRecordEntity deptQuotaRecordEntity=deptQuotaRecordMapper.selectOne(new LambdaQueryWrapper<DeptQuotaRecordEntity>()
                    .eq(DeptQuotaRecordEntity::getOrderId, orderId)
                    .eq(DeptQuotaRecordEntity::getAction, 0)
                    .eq(DeptQuotaRecordEntity::getDeleteFlag, 0)
                    .orderByDesc(DeptQuotaRecordEntity::getId),false);
            if (ObjUtil.isNotNull(deptQuotaRecordEntity)){
                int update = deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                        .setIncrBy(DeptQuotaEntity::getRemainingQuota, deptQuotaRecordEntity.getQuota())
                        .eq(DeptQuotaEntity::getId, deptQuotaRecordEntity.getDeptQuotaId())
                        .eq(DeptQuotaEntity::getDeptId, deptQuotaRecordEntity.getDeptId())
                        .eq(DeptQuotaEntity::getDeleteFlag, 0));
                if (update==0){
                    log.error("RiskFirstApproveAction execute quotaReleaseFailed DeptQuotaEntity=0，orderid:{}", orderId);
                           deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                            .set(DeptQuotaRecordEntity::getDeleteFlag, 1)
                            .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
                }else {
                    deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                            .set(DeptQuotaRecordEntity::getAction, 1)
                            .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
                }
            }
            preApprovalApplyInfoMapper.update(new LambdaUpdateWrapper<PreApprovalApplyInfoEntity>()
                    .set(PreApprovalApplyInfoEntity::getProductId, null)
                    .eq(PreApprovalApplyInfoEntity::getId, orderInfoMapper.selectById(orderId).getPreId()));
        }

    }

}