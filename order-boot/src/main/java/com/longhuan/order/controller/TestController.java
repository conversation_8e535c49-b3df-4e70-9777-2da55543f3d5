package com.longhuan.order.controller;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.feign.CheXiaoFeign;
import com.longhuan.order.mapper.OrderAmountMapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.mapper.OrderVehicleInfoMapper;
import com.longhuan.order.pojo.dto.chexiao.CreateInstallationWorkOrderDTO;
import com.longhuan.order.pojo.entity.OrderAmountEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.entity.OrderVehicleInfoEntity;
import com.longhuan.order.pojo.vo.chexiao.CheXiaoVO;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 测试车晓
 */
@RestController
@RequestMapping("/chexiao")
@RequiredArgsConstructor
public class TestController {
    @Value("${chexiao.token}")
    private String token;
    @Resource
    private CheXiaoFeign cheXiaoFeign;
    @Resource
    private OrderVehicleInfoMapper orderVehicleInfoMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private OrderAmountMapper orderAmountMapper;
    @Resource
    private ObjectMapper objectMapper;
    @PostMapping("/createInstallationWorkOrder")
    public String createInstallationWorkOrder() {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(1323);
        OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(
                new LambdaQueryWrapper<OrderAmountEntity>()
                        .eq(OrderAmountEntity::getOrderId, orderInfoEntity.getId())
                        .eq(OrderAmountEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderAmountEntity::getCreateTime)
                        .last("limit 1")
        );
        OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(
                new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                        .eq(OrderVehicleInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderVehicleInfoEntity::getCreateTime)
                        .last("limit 1")
        );
        CreateInstallationWorkOrderDTO dto=new CreateInstallationWorkOrderDTO();
        dto.setToken(token);
        dto.setName(orderVehicleInfoEntity.getHolder());
        dto.setTel("13295484447");
        dto.setBrand(orderVehicleInfoEntity.getBrand());
        dto.setModel(orderVehicleInfoEntity.getVehicleModel());
        dto.setFrame_no(orderVehicleInfoEntity.getVin());
        dto.setPlate_no(orderVehicleInfoEntity.getVehicleNumber());
        if (orderAmountEntity.getCustomerConfirmAmount().compareTo(new BigDecimal("150000.00")) >= 0){
            dto.setWire_equip("1");
            dto.setWireless_equip("3");
            dto.setObd_equip("0");
        }else {
            dto.setWire_equip("1");
            dto.setWireless_equip("2");
            dto.setObd_equip("0");
        }
        dto.setContact_name("测试");
        dto.setContact_status("测试");
        dto.setContact_tel("13295484447");
        dto.setPlan_installTime("2025-03-30 14:00:00");
        dto.setInstall_province("河北省");
        dto.setInstall_city("石家庄市");
        dto.setInstall_county("新华区");
        dto.setInstall_location("测试地址");
        dto.setCredit_period(String.valueOf(orderInfoEntity.getTerm()));
        dto.setCollectingKey("1");
        dto.setRecipient("路雨欣");
        dto.setRecipientTel("15830050600");
        dto.setRecipientAddress("河北省邯郸市邯山区启信大厦6层");
        dto.setInsured("河北省邯郸市邯山区启信大厦6层");

        String installationWorkOrder = cheXiaoFeign.createInstallationWorkOrder(
                token,
                orderVehicleInfoEntity.getHolder(),
                "13295484447",
                orderVehicleInfoEntity.getBrand(),
                orderVehicleInfoEntity.getVehicleModel(),
                orderVehicleInfoEntity.getVin(),
                orderVehicleInfoEntity.getVehicleNumber(),
                "1",
                "3",
                "0",
                "测试",
                "测试",
                "13295484447",
                "2025-03-30 14:00:00",
                "河北省",
                "石家庄市",
                "新华区",
                "测试地址",
                "1个无线不可拆",
                String.valueOf(orderInfoEntity.getTerm()),
                "1",
                "路雨欣",
                "15830050600",
                "河北省邯郸市邯山区启信大厦6层",
                "2",
                ""
        );
        try {
            CheXiaoVO cheXiaoVO = objectMapper.readValue(installationWorkOrder, CheXiaoVO.class);
            System.out.println("installationWorkOrder = " + cheXiaoVO);
            if (ObjUtil.isNotEmpty(cheXiaoVO)){
                if (Objects.equals(cheXiaoVO.getResult(), 1)){
                    if (Objects.equals(cheXiaoVO.getData().getCode(), 0)){
                        throw new BusinessException(cheXiaoVO.getData().getErrormsg());
                    }else {
                        return "成功";
                    }
                }else if (Objects.equals(cheXiaoVO.getResult(), 0)){
                    throw new BusinessException("GPS预约安装失败");
                }else {
                    throw new BusinessException("系统错误");
                }
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return "失败";
    }
}
