package com.longhuan.order.service.impl;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.github.yulichang.wrapper.segments.Fun;
import com.longhuan.approve.api.constants.FundRepayModeEnum;
import com.longhuan.approve.api.pojo.dto.*;
import com.longhuan.approve.api.pojo.vo.FundDeductCheckVO;
import com.longhuan.approve.api.pojo.vo.FundRepayCalcVO;
import com.longhuan.approve.api.pojo.vo.LoanSettlementVO;
import com.longhuan.auto.loan.pojo.dto.gps.GpsDataDTO;
import com.longhuan.auto.loan.pojo.vo.gps.GpsDataVO;
import com.longhuan.common.core.base.RequestResponseInfoEntity;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.RequestResponseCode;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.core.util.QrCodeUtils;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.order.converter.FundDeductConverter;
import com.longhuan.order.enums.CaseApproveNodeEnums;
import com.longhuan.order.enums.FundRepayStatusEnum;
import com.longhuan.order.enums.PayApplicationAuditTypeEnum;
import com.longhuan.order.enums.PayApplicationEventEnums;
import com.longhuan.order.feign.*;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.*;
import com.longhuan.order.settleCalculation.service.SettleCalculationService;
import com.longhuan.order.util.SybUtil;
import com.longhuan.user.pojo.vo.DeptInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class FundDeductServiceImpl implements FundDeductService {
    private static final String LOCK_KEY_PREFIX = "fund:deduct:lock:";
    private static final String LOCK_DEDUCT_QUERY_KEY_PREFIX = "fund:deduct:query:lock:";
    private static final String PENALTY_PAY_URL_KEY = "fund:deduct:penalty:lock:";
    private static final String RISK_NUMBER = "Pay_";
    private final CaseInfoEntityMapper caseInfoEntityMapper;
    @Value("${TongLian.SYB_CUSID}")
    private String SYB_CUSID;
    @Value("${TongLian.SYB_APPID}")
    private String SYB_APPID;
    @Value("${TongLian.SIGN_TYPE}")
    private String SIGN_TYPE;
    @Value("${TongLian.SYB_RSACUSPRIKEY}")
    private String SYB_RSACUSPRIKEY;
    @Value("${TongLian.SYB_SM2PPRIVATEKEY}")
    private String SYB_SM2PPRIVATEKEY;
    @Value("${TongLian.SYB_MD5_APPKEY}")
    private String SYB_MD5_APPKEY;
    @Value("${TongLian.TL_CALL_BACK}")
    private String TL_CALL_BACK;
    @Value("${TongLian.SYB_RSATLPUBKEY}")
    private String SYB_RSATLPUBKEY;
    @Value("${TongLian.SYB_SM2TLPUBKEY}")
    private String SYB_SM2TLPUBKEY;
    private final String TNAME = "LH_ORDER_INFO";
    private final OrderInfoMapper orderInfoMapper;
    private final ApproveFeign approveFeign;
    private final FundRepaymentDeductService fundRepaymentDeductService;
    private final DataPermissionService dataPermissionService;
    private final OrderAmountMapper orderAmountMapper;
    private final UserFeign userFeign;
    private final RedisService redisService;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final TongLianTongService tongLianTongService;
    private final EnvUtil envUtil;
    private final RequestResponseInfoMapper requestResponseInfoMapper;
    private final TongLianPayFeign tongLianPayFeign;
    private final OrderFeeDetailService orderFeeDetailService;
    private final DingDrawMoneyFeign dingDrawMoneyFeign;
    private final SprEductionUsageService sprEductionUsageService;
    private final OrderPayApplicationMapper orderPayApplicationMapper;
    private final ProductFundMappingMapper productFundMappingMapper;
    private final OrderPayApplyNodeRecordMapper orderPayApplyNodeRecordMapper;
    private final RepurchaseRepaymentInfoMapper repurchaseRepaymentInfoMapper;
    private final OrderSettleAmountRecordMapper orderSettleAmountRecordMapper;
    private final OrderCustomerInfoMapper customerInfoMapper;
    private final FundProductMappingMapper fundProductMappingMapper;
    private final FundRepaymentDeductMapper repaymentDeductMapper;
    private final SettleCalculationService settleCalculationService;
    private final LoanRepaymentFeign autoLoanRepaymentFeign;

    @Override
    public Page<OrderFundDeductListVO> page(OrderFundDeductQueryListDTO dto, LoginUser loginUser) {

        MPJLambdaWrapper<OrderInfoEntity> wrapper = new MPJLambdaWrapper<>(OrderInfoEntity.class)
                .select(OrderInfoEntity::getId,
                        OrderInfoEntity::getCustomerName,
                        OrderInfoEntity::getRegionName,
                        OrderInfoEntity::getOrderNumber,
                        OrderInfoEntity::getStoreName,
                        OrderInfoEntity::getVehicleNumber,
                        OrderInfoEntity::getFundName,
                        OrderInfoEntity::getProductName,
                        OrderInfoEntity::getTeamId)
                .selectAs(FundRepaymentDeductEntity::getTerm, OrderFundDeductListVO::getApplyTerm)
                .selectFunc("SUM(CASE WHEN %s = 20 THEN %s ELSE 0 END)", arg -> arg.accept(
                        Fun.f("frd", FundRepaymentDeductEntity::getRepayStatus),
                        Fun.f("frd", FundRepaymentDeductEntity::getDeductAmount)
                ), OrderFundDeductListVO::getActuallyAmountTotal)
                .select(FundRepaymentInfoEntity::getRepaymentStatus, FundRepaymentInfoEntity::getActuallyDate, FundRepaymentInfoEntity::getTerm, FundRepaymentInfoEntity::getIsOverdue, FundRepaymentInfoEntity::getRepaymentDate)
                .innerJoin(FundRepaymentInfoEntity.class, "fri", join -> join
                        .eq(FundRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                        .eq(FundRepaymentInfoEntity::getFundId, OrderInfoEntity::getFundId))
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                .innerJoin(FundRepaymentDeductEntity.class, "frd", join -> join
                        .eq(FundRepaymentDeductEntity::getOrderId, OrderInfoEntity::getId)
                        .eq(FundRepaymentDeductEntity::getTerm, FundRepaymentInfoEntity::getTerm)
                        .eq(FundRepaymentDeductEntity::getDeleteFlag, 0))
                .groupBy(FundRepaymentDeductEntity::getTerm)
                .groupBy(FundRepaymentInfoEntity::getRepaymentStatus, FundRepaymentInfoEntity::getActuallyDate, FundRepaymentInfoEntity::getTerm,
                        FundRepaymentInfoEntity::getIsOverdue, FundRepaymentInfoEntity::getRepaymentDate, FundRepaymentInfoEntity::getActuallyAmountTotal)

                .groupBy(OrderInfoEntity::getId, OrderInfoEntity::getCustomerName, OrderInfoEntity::getRegionName, OrderInfoEntity::getOrderNumber,
                        OrderInfoEntity::getStoreName, OrderInfoEntity::getVehicleNumber, OrderInfoEntity::getFundName,
                        OrderInfoEntity::getProductName, OrderInfoEntity::getTeamId)
                .orderByDesc(FundRepaymentInfoEntity::getRepaymentDate, FundRepaymentInfoEntity::getTerm)
                .like(StrUtil.isNotBlank(dto.getOrderNumber()), OrderInfoEntity::getOrderNumber, dto.getOrderNumber())
                .like(StrUtil.isNotBlank(dto.getCustomerName()), OrderInfoEntity::getCustomerName, dto.getCustomerName())
                .like(StrUtil.isNotBlank(dto.getCustomerPhone()), OrderInfoEntity::getCustomerPhone, dto.getCustomerPhone())
                .like(StrUtil.isNotBlank(dto.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, dto.getVehicleNumber());


        //权限控制
        dataPermissionService.limitOrderRepaymentDeduct(loginUser, wrapper);
        //应还日期筛选
        wrapper.ge(ObjUtil.isNotNull(dto.getRepaymentStartTime()), FundRepaymentInfoEntity::getRepaymentDate, dto.getRepaymentStartTime())
                .le(ObjUtil.isNotNull(dto.getRepaymentEndTime()), FundRepaymentInfoEntity::getRepaymentDate, dto.getRepaymentEndTime());

        // 实际日期条件
        wrapper.ge(ObjUtil.isNotNull(dto.getActuallyStartTime()), FundRepaymentInfoEntity::getActuallyDate, dto.getActuallyStartTime())
                .le(ObjUtil.isNotNull(dto.getActuallyEndTime()), FundRepaymentInfoEntity::getActuallyDate, dto.getActuallyEndTime());

        Page<OrderFundDeductListVO> orderFundDeductListVOPage = orderInfoMapper.selectJoinPage(Page.of(dto.getPageNum(), dto.getPageSize()), OrderFundDeductListVO.class, wrapper);
        List<Integer> teamIds = orderFundDeductListVOPage.getRecords().stream().map(OrderFundDeductListVO::getTeamId).filter(Objects::nonNull).toList();
        List<DeptInfoVO> branchNameVOS = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData();
        Map<Integer, String> branchNameMap = branchNameVOS.stream().filter(e -> ObjUtil.isNotNull(e.getName())).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
        orderFundDeductListVOPage.getRecords().stream().filter(item -> item.getTeamId() != null).forEach(record -> {
            record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(branchNameMap.get(record.getTeamId()), ""));
        });
        return orderFundDeductListVOPage;
    }


    @Override
    public Page<OrderFundDeductDetailListVO> detailPage(OrderFundDeductDetailQueryListDTO dto) {
        MPJLambdaWrapper<FundRepaymentDeductEntity> wrapper = new MPJLambdaWrapper<>(FundRepaymentDeductEntity.class);
        wrapper.select(FundRepaymentDeductEntity::getId,
                        FundRepaymentDeductEntity::getTerm,
                        FundRepaymentDeductEntity::getDeductReqNo,
                        FundRepaymentDeductEntity::getRepayDate,
                        FundRepaymentDeductEntity::getRepayType,
                        FundRepaymentDeductEntity::getBizType,
                        FundRepaymentDeductEntity::getRepayStatus,
                        FundRepaymentDeductEntity::getFailReason,
                        FundRepaymentDeductEntity::getCreateTime,
                        FundRepaymentDeductEntity::getDeductAmount
                )
                .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                .eq(ObjUtil.isNotNull(dto.getTerm()), FundRepaymentDeductEntity::getTerm, dto.getTerm())
                .ne(FundRepaymentDeductEntity::getBizType, FundDeductBizTypeEnums.PAYMENT)
                .eq(ObjUtil.isNotNull(dto.getRepayStatus()), FundRepaymentDeductEntity::getRepayStatus, dto.getRepayStatus())
                .ge(ObjUtil.isNotNull(dto.getRepaymentStartTime()), FundRepaymentDeductEntity::getRepayDate, dto.getRepaymentStartTime())
                .le(ObjUtil.isNotNull(dto.getRepaymentEndTime()), FundRepaymentDeductEntity::getRepayDate, dto.getRepaymentEndTime())
                .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                .orderByDesc(FundRepaymentDeductEntity::getCreateTime);
        return fundRepaymentDeductService.selectJoinListPage(Page.of(dto.getPageNum(), dto.getPageSize()), OrderFundDeductDetailListVO.class, wrapper);
    }

    @Override
    public ScanCodePaymentVO scanCodePayment(ScanCodePaymentDTO dto) {
        List<FundRepaymentDeductEntity> fundRepaymentDeductList = fundRepaymentDeductService.list(
                new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                        .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                        .eq(FundRepaymentDeductEntity::getBizType, FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION)
                        .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)
                        .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isNotEmpty(fundRepaymentDeductList)) {
            throw new BusinessException("当前订单的结清违约金已有正在处理中的操作，请耐心等待");
        }
        FundDeductRepayStatusEnums fundDeductRepayStatusEnums = tongLianTongService.huiFengDeductQuery(dto.getOrderId());
        if (Objects.equals(fundDeductRepayStatusEnums, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)) {
            throw new BusinessException("当前订单的结清违约金已有正在处理中的操作，请耐心等待");
        }
        if (Objects.equals(fundDeductRepayStatusEnums, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)) {
            throw new BusinessException("当前订单的结清违约金已结清，无需重复操作");
        }
        FundDeductRepayStatusEnums fundDeductRepayStatusEnums1 = searchTranx(dto.getOrderId());
        if (Objects.equals(fundDeductRepayStatusEnums1, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)) {
            throw new BusinessException("当前订单的结清违约金已结清，无需重复操作");
        }
        if (Objects.equals(fundDeductRepayStatusEnums1, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)) {
            throw new BusinessException("当前订单的结清违约金已有正在处理中的操作，请耐心等待");
        }
        List<FundRepaymentDeductEntity> fundRepaymentDeductEntityList = fundRepaymentDeductService.list(
                new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                        .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                        .eq(FundRepaymentDeductEntity::getBizType, FundDeductBizTypeEnums.PENALTY_PAYMENT)
                        .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                        .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isNotEmpty(fundRepaymentDeductEntityList)) {
            FundRepaymentDeductEntity fundRepaymentDeductEntity = fundRepaymentDeductEntityList.get(0);
            ScanCodePaymentVO scanCodePaymentVO = new ScanCodePaymentVO();
            scanCodePaymentVO.setOrderId(dto.getOrderId())
                    .setId(fundRepaymentDeductEntity.getId())
                    .setDeductAmount(fundRepaymentDeductEntity.getDeductAmount())
                    .setFeeStatus(fundRepaymentDeductEntity.getRepayStatus().getCode());
            return scanCodePaymentVO;
        }
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(dto.getOrderId());
        Assert.notNull(orderInfo, "订单" + dto.getOrderId() + "信息不存在");
        List<FundRepaymentDeductEntity> fundRepaymentDeductEntityList1 = fundRepaymentDeductService.list(
                new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                        .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                        .eq(FundRepaymentDeductEntity::getBizType, FundDeductBizTypeEnums.PENALTY_PAYMENT)
                        .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)
                        .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                        .orderByDesc(FundRepaymentDeductEntity::getCreateTime)
        );
        ScanCodePaymentVO scanCodePaymentVO = new ScanCodePaymentVO();
        FundRepaymentDeductEntity fundRepaymentDeductEntity = new FundRepaymentDeductEntity();
        if (CollUtil.isNotEmpty(fundRepaymentDeductEntityList1)) {
            fundRepaymentDeductEntity = fundRepaymentDeductEntityList1.get(0);
            scanCodePaymentVO.setOrderId(dto.getOrderId())
                    .setDeductAmount(fundRepaymentDeductEntity.getDeductAmount())
                    .setFeeStatus(fundRepaymentDeductEntity.getRepayStatus().getCode());

        }
        redisService.delete(PENALTY_PAY_URL_KEY + dto.getOrderId());
        // 从 Redis 中获取支付信息
        String payInfoVal = redisService.getString(PENALTY_PAY_URL_KEY + dto.getOrderId());
        if (payInfoVal != null) {
            try {
                // 生成二维码
                byte[] imageBytes = QrCodeUtils.generateQrCode(orderInfo.getCustomerName(), payInfoVal);
                scanCodePaymentVO.setResponseEntity(Base64Encoder.encode(imageBytes))
                        .setPayUrl(payInfoVal);
            } catch (Exception e) {
                throw new BusinessException("生成二维码失败", e);
            }
            return scanCodePaymentVO;
        }
//
//        //若Redis中没有支付信息，则先把最近一笔置为失效再生成新的支付信息
        String paySnNum = fundRepaymentDeductEntity.getDeductReqNo();
        // 关闭订单
        tlCloseTransaction(paySnNum, dto.getOrderId());
        //将之前订单全部置为失效
        fundRepaymentDeductService.update(new LambdaUpdateWrapper<FundRepaymentDeductEntity>()
                .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                .eq(FundRepaymentDeductEntity::getBizType, FundDeductBizTypeEnums.PENALTY_PAYMENT)
                .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)
                .set(FundRepaymentDeductEntity::getDeleteFlag, 1));

//        // 调用通联支付接口生成支付链接
        Integer orderId = dto.getOrderId();
        // 调用通联支付接口生成支付链接
        String reqsn = getPayNumber();
        // 计算金额并调用通联支付接口
        BigDecimal trxamt = (dto.getPayAmount().subtract(ObjUtil.isNotEmpty(dto.getReductionAmount()) ? dto.getReductionAmount() : BigDecimal.ZERO))
                .multiply(new BigDecimal("100"))
                .setScale(0, RoundingMode.HALF_UP);
        TongLianPayResultVO tongLianPayResultVO = tongLianPaySend(orderId, Objects.toString(trxamt, ""), reqsn);

        if (!Objects.equals(tongLianPayResultVO.getRetcode(), "SUCCESS")) {
            throw new BusinessException(tongLianPayResultVO.getRetmsg());
        }

        // 记录支付编号并判断是否插入或更新
        if (fundRepaymentDeductEntity.getDeductReqNo() != null) {
            List<FundRepaymentInfoEntity> repaymentInfoList = fundRepaymentInfoMapper.selectList(
                    new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                            .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                            .eq(FundRepaymentInfoEntity::getFundId, fundRepaymentDeductEntity.getFundId())
                            .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
            );
            log.info("RepaymentServiceImpl.buildLoanSettlementFactoryDTO - repaymentInfoList: {}", repaymentInfoList);
            BigDecimal remainingPrincipal;
            Integer term = repaymentInfoList.stream().map(FundRepaymentInfoEntity::getTerm).min(Integer::compareTo).orElse(0);
            long index = fundRepaymentDeductService.count(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                    .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                    .eq(FundRepaymentDeductEntity::getTerm, term));
            // 创建新支付记录
            log.info("CustomerAppointmentServiceImpl orderId {} create new pay info", orderId);
            FundRepaymentDeductEntity entity = new FundRepaymentDeductEntity()
                    .setOrderId(orderId)
                    .setDeductReqNo(reqsn)
                    .setIndex(Math.toIntExact(index))
                    .setRepayDate(LocalDateTime.now().toLocalDate())
                    .setFundId(fundRepaymentDeductEntity.getFundId())
                    .setRespContent(JSONUtil.toJsonStr(tongLianPayResultVO))
                    .setReqContent(JSONUtil.toJsonStr(orderId + "/" + Objects.toString(trxamt, "") + "/" + reqsn))
                    .setRepayType(FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                    .setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)
                    .setTerm(term)
                    .setBizType(FundDeductBizTypeEnums.PENALTY_PAYMENT)
                    .setDeductAmount(dto.getPayAmount());

            fundRepaymentDeductService.save(entity);
            // 保存订单费用明细
            orderFeeDetailService.saveOrderFeeDetail(orderId,
                    fundRepaymentDeductEntity.getDeductReqNo(),
                    dto.getPayAmount(),
                    OrderFeeDetailTradingMethodsEnum.SCAN_THE_QR_CODE_TO_PAY,
                    orderInfo.getCustomerName(),
                    PayApplicationPayeeTypeEnum.HUI_FENG.getDesc(),
                    OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY,
                    OrderFeeDetailStatusEnum.INCOME,
                    term,
                    LocalDateTime.now(),
                    tongLianPayResultVO.getRetmsg(),
                    ObjUtil.isNotEmpty(dto.getReductionAmount()) ? dto.getReductionAmount() : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP)
            );
            tongLianTongService.updateRepayment(orderId);
        }
        // 支付链接
        String payinfo = tongLianPayResultVO.getPayinfo();

        // 设置 Redis 缓存
        redisService.set(PENALTY_PAY_URL_KEY + orderId, payinfo, 60 * 4);

        try {
            // 生成二维码
            byte[] imageBytes = QrCodeUtils.generateQrCode(orderInfo.getCustomerName(), payinfo);
            scanCodePaymentVO.setResponseEntity(Base64Encoder.encode(imageBytes))
                    .setPayUrl(payinfo);
        } catch (Exception e) {
            throw new BusinessException("生成二维码失败", e);
        }
        log.info("FundDeductServiceImpl.scanCodePayment:scanCodePaymentVO:{}", scanCodePaymentVO);

        return scanCodePaymentVO;
    }

    @Override
    public FundDeductRepayStatusEnums searchTranx(Integer orderId) {
//        if(!envUtil.isPrd()){
//            return FundDeductRepayStatusEnums.REPAYMENT_SUCCESS;
//        }

        List<FundRepaymentDeductEntity> fundRepaymentDeductEntityList = fundRepaymentDeductService.list(
                new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                        .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                        .eq(FundRepaymentDeductEntity::getBizType, FundDeductBizTypeEnums.PENALTY_PAYMENT)
                        .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)
                        .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isNotEmpty(fundRepaymentDeductEntityList)) {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
            FundRepaymentDeductEntity fundRepaymentDeductEntity = fundRepaymentDeductEntityList.get(0);
            //调用通联支付查询交易
            TongLianSearchVO tongLianSearchVO = searchTranxSend(orderId, fundRepaymentDeductEntity.getDeductReqNo());
            String trxstatus = tongLianSearchVO.getTrxstatus();
            String resultMsg = "";
            FundDeductRepayStatusEnums repayStatus;
            if ("0000".equals(trxstatus)) {
                // 交易成功
                resultMsg = "支付成功";
                fundRepaymentDeductEntity.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_SUCCESS);
                orderFeeDetailService.saveOrderFeeDetail(orderId,
                        fundRepaymentDeductEntity.getDeductReqNo(),
                        fundRepaymentDeductEntity.getDeductAmount(),
                        OrderFeeDetailTradingMethodsEnum.LINKED_BUCKLES,
                        orderInfoEntity.getCustomerName(),
                        PayApplicationPayeeTypeEnum.HUI_FENG.getDesc(),
                        OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY,
                        OrderFeeDetailStatusEnum.INCOME,
                        fundRepaymentDeductEntity.getTerm(),
                        Convert.toLocalDateTime(fundRepaymentDeductEntity.getRepayDate(), LocalDateTime.now()),
                        resultMsg,
                        null
                );
                tongLianTongService.updateRepayment(orderId);
                sprEductionUsageService.update(
                        new LambdaUpdateWrapper<SprEductionUsageEntity>()
                                .eq(SprEductionUsageEntity::getVehicleNumber, orderInfoEntity.getVehicleNumber())
                                .eq(SprEductionUsageEntity::getDeleteFlag, 0)
                                .set(SprEductionUsageEntity::getUsageStatus, 1)
                );
                repayStatus = FundDeductRepayStatusEnums.REPAYMENT_SUCCESS;
            } else if ("1001".equals(trxstatus)) {
                resultMsg = "交易不存在";
                fundRepaymentDeductEntity.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_FAILED);
                log.info("通联支付查询，交易不存在");
                repayStatus = FundDeductRepayStatusEnums.REPAYMENT_FAILED;
            } else if ("2008".equals(trxstatus) || "2000".equals(trxstatus)) {
                resultMsg = "支付处理中,请再次查询支付";
                fundRepaymentDeductEntity.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_PROCESSING);
                log.info("通联支付查询，交易处理中,请再次查询交易");
                repayStatus = FundDeductRepayStatusEnums.REPAYMENT_PROCESSING;
            } else {
                resultMsg = "支付失败";
                fundRepaymentDeductEntity.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_FAILED);
                log.info("交易失败");
                repayStatus = FundDeductRepayStatusEnums.REPAYMENT_FAILED;
            }
            if (!envUtil.isPrd()) {
                //目前测试阶段，直接修改订单状态为已支付
                fundRepaymentDeductEntity.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_SUCCESS);
                repayStatus = FundDeductRepayStatusEnums.REPAYMENT_SUCCESS;
            }
            fundRepaymentDeductService.updateById(fundRepaymentDeductEntity);
//            throw new BusinessException(resultMsg);
            return repayStatus;
        } else {
            return FundDeductRepayStatusEnums.REPAYMENT_NONE;
        }
    }

    @Override
    public Result<Boolean> corporateTransfer(CorporateTransferDTO dto, LoginUser loginUser) {
        if (Objects.equals(dto.getFeeType(), 6)) {
            List<FundRepaymentDeductEntity> fundRepaymentDeductEntityList = fundRepaymentDeductService.list(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                            .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                    FundDeductBizTypeEnums.PAYMENT,
                                    FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                    FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                    FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE
                            ))
                            .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                            .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                            .orderByDesc(FundRepaymentDeductEntity::getUpdateTime)
            );
            if (CollUtil.isNotEmpty(fundRepaymentDeductEntityList) && ObjUtil.isNotEmpty(dto.getTerm())) {
                if (!Objects.equals(dto.getTerm(), fundRepaymentDeductEntityList.get(0).getTerm())) {
                    throw new BusinessException("填写的期数与提前结清期数不一致");
                }
            }

            FundRepaymentInfoEntity fundRepaymentInfoEntity = fundRepaymentInfoMapper.selectOne(
                    new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                            .eq(FundRepaymentInfoEntity::getOrderId, dto.getOrderId())
                            .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.NONE)
                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                            .orderByAsc(FundRepaymentInfoEntity::getTerm)
                            .last("limit 1")
            );
            if (ObjUtil.isEmpty(dto.getTerm())) {
                if (ObjUtil.isNotNull(fundRepaymentInfoEntity)) {
                    dto.setTerm(fundRepaymentInfoEntity.getTerm());
                } else {
                    List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(
                            new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                                    .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                                    .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                            FundDeductBizTypeEnums.PENALTY_DEDUCTION,
                                            FundDeductBizTypeEnums.PENALTY_PAYMENT,
                                            FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION,
                                            FundDeductBizTypeEnums.PENALTY_INITIATE_REPAYMENT_AFTER_PAYMENT
                                    ))
                                    .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                                    .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                                    .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                    );
                    if (CollUtil.isNotEmpty(list)) {
                        dto.setTerm(list.get(list.size() - 1).getTerm());
                    } else {
                        throw new BusinessException("请选择要结清的期数");
                    }
                }
            }
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(dto.getOrderId());
            if (ObjUtil.isNotEmpty(dto.getId())) {
                FundRepaymentDeductEntity one = fundRepaymentDeductService.getOne(
                        new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                                .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                                .eq(FundRepaymentDeductEntity::getBizType, FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION)
                                .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_FAILED)
                                .orderByDesc(FundRepaymentDeductEntity::getUpdateTime)
                                .last("limit 1")
                );
                if (ObjUtil.isNotEmpty(one)) {
                    one.setFundId(orderInfo.getFundId());
                    one.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_PROCESSING);
                    fundRepaymentDeductService.updateById(one);
                }
                OrderPayApplicationInfoEntity entity = orderPayApplicationMapper.selectById(dto.getId());
                entity.setPaymentDetails(dto.getPaymentDetails());
                entity.setPayAccount(dto.getTransferUserName());
                entity.setPayAccountName(dto.getPayAccountName());
                entity.setCurrentNode(PayApplicationNodeEnums.CASHIER_APPROVAL);
                entity.setPayAccountNumber(dto.getPayAccountNumber());
                entity.setPayeeAccount(dto.getPayeeAccount());
                entity.setPayeeAccountName(dto.getPayeeAccountName());
                entity.setPayeeAccountNumber(dto.getPayeeAccountNumber());
                entity.setPayeeAmount(dto.getPayeeAmount());
                entity.setPaymentVoucherList(JSONUtil.toJsonStr(dto.getResourceId()));
                entity.setPaymentTime(dto.getPaymentTime());
                entity.setRemark(dto.getRemark());
                entity.setRepaymentTerm(dto.getTerm());
                entity.setLoanSettlementMethod(dto.getLoanSettlementMethod());
                entity.setReductionAmount(dto.getReductionAmount());
                entity.setRepaymentShowAmount(dto.getRepaymentShowAmount());
                int update = orderPayApplicationMapper.updateById(entity);
                saveNodeRecord(entity.getId(), PayApplicationNodeEnums.ACCOUNTANT_APPLY, entity.getCurrentNode(), PayApplicationAuditTypeEnum.YUNQI
                        , null, entity.getRemark(), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), LocalDateTime.now());

                return Result.success(update > 0);
            }
            long count = fundRepaymentDeductService.count(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                            .in(FundRepaymentDeductEntity::getBizType,
                                    Arrays.asList(
                                            FundDeductBizTypeEnums.PENALTY_PAYMENT,
                                            FundDeductBizTypeEnums.PENALTY_DEDUCTION,
                                            FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION)
                            )
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)
                            .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
            );
            if (count > 0) {
                throw new BusinessException("当前订单的结清违约金已有正在处理中的操作，请耐心等待");
            }

//        sprEductionUsageService.update(
//                new LambdaUpdateWrapper<SprEductionUsageEntity>()
//                        .eq(SprEductionUsageEntity::getVehicleNumber, orderInfo.getVehicleNumber())
//                        .eq(SprEductionUsageEntity::getDeleteFlag,0)
//                        .set(SprEductionUsageEntity::getUsageStatus,1)
//        );
//        List<SprEductionUsageEntity> list1 = sprEductionUsageService.list(
//                new LambdaQueryWrapper<SprEductionUsageEntity>()
//                        .eq(SprEductionUsageEntity::getVehicleNumber, orderInfo.getVehicleNumber())
//                        .eq(SprEductionUsageEntity::getDeleteFlag,0)
//                        .eq(SprEductionUsageEntity::getUsageStatus,2)
//        );
            FundRepaymentDeductEntity deductEntity = new FundRepaymentDeductEntity();
            deductEntity.setOrderId(dto.getOrderId());
            deductEntity.setFundId(orderInfo.getFundId());
            deductEntity.setDeductReqNo(dto.getPaymentDetails());
            List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                    .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                    .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                            FundDeductBizTypeEnums.PENALTY_DEDUCTION,
                            FundDeductBizTypeEnums.PENALTY_PAYMENT,
                            FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION
                    )));
            deductEntity.setIndex(list.size());
            deductEntity.setRepayDate(dto.getPaymentTime().toLocalDate());
            deductEntity.setRepayType(FundDeductRepayTypeEnums.EARLY_SETTLEMENT);
            deductEntity.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_PROCESSING);
            deductEntity.setBizType(FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION);
            deductEntity.setDeductAmount(dto.getPayeeAmount());
            deductEntity.setTerm(dto.getTerm());
            fundRepaymentDeductService.save(deductEntity);
            BigDecimal reduce = list.stream().
                    filter(deductEntity1 -> deductEntity1.getBizType().equals(FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION))
                    .map(FundRepaymentDeductEntity::getDeductAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            OrderPayApplicationInfoEntity entity = new OrderPayApplicationInfoEntity();
            entity.setOrderId(dto.getOrderId());
            entity.setPayeeType(PayApplicationPayeeTypeEnum.fromCode(dto.getPayeeType()));
            entity.setFeeType(OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY);
            entity.setPaymentDetails(dto.getPaymentDetails());
            entity.setPayAccount(dto.getTransferUserName());
            entity.setPayAccountName(dto.getPayAccountName());
            entity.setPayAccountNumber(dto.getPayAccountNumber());
            entity.setPayeeAccount(dto.getPayeeAccount());
            entity.setPayeeAccountName(dto.getPayeeAccountName());
            entity.setPayeeAccountNumber(dto.getPayeeAccountNumber());
            entity.setPayeeAmount(dto.getPayeeAmount());
            entity.setPaymentVoucherList(JSONUtil.toJsonStr(dto.getResourceId()));
            entity.setPaymentTime(dto.getPaymentTime());
            entity.setRemark(dto.getRemark());
            entity.setRepaymentTerm(dto.getTerm());
            entity.setReductionAmount(ObjUtil.isNotEmpty(reductionAmount(orderInfo)) ? reductionAmount(orderInfo) : BigDecimal.ZERO);
            entity.setCurrentNode(PayApplicationNodeEnums.CASHIER_APPROVAL);
            entity.setApplyUserId(loginUser.getUserId());
            entity.setApplyType(OrderFeeDetailStatusEnum.INCOME);
            entity.setLoanSettlementMethod(dto.getLoanSettlementMethod());
            entity.setReductionAmount(dto.getReductionAmount());
            entity.setRepaymentShowAmount(dto.getRepaymentShowAmount());
            entity.setFeeDetails(2);
            int insert = orderPayApplicationMapper.insert(entity);
            saveNodeRecord(entity.getId(), PayApplicationNodeEnums.ACCOUNTANT_APPLY, entity.getCurrentNode(), PayApplicationAuditTypeEnum.YUNQI
                    , null, entity.getRemark(), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), LocalDateTime.now());
            FundEarlyRepaymentCalcDTO fundEarlyRepaymentCalcDTO1 = earlyRepayCalc(new FundRepayCalcEarlyDTO().setOrderId(dto.getOrderId()).setLoanSettlementMethod(dto.getLoanSettlementMethod()));
            entity.setRepaymentShowAmount(fundEarlyRepaymentCalcDTO1.getFirmRepayAmountTotal());
            orderPayApplicationMapper.updateById(entity);
            FundEarlyRepaymentCalcDTO fundEarlyRepaymentCalcDTO = earlyRepayCalc(new FundRepayCalcEarlyDTO().setOrderId(dto.getOrderId()).setLoanSettlementMethod(dto.getLoanSettlementMethod()));
            if (reduce.compareTo(fundEarlyRepaymentCalcDTO.getFirmRepayAmountTotal()) >= 0) {
                deductEntity.setIndex(list.size());
                deductEntity.setFundId(orderInfo.getFundId());
                deductEntity.setRepayDate(dto.getPaymentTime().toLocalDate());
                deductEntity.setRepayType(FundDeductRepayTypeEnums.EARLY_SETTLEMENT);
                deductEntity.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_PROCESSING);
                deductEntity.setBizType(FundDeductBizTypeEnums.PENALTY_INITIATE_REPAYMENT_AFTER_PAYMENT);
                deductEntity.setDeductAmount(dto.getPayeeAmount());
                deductEntity.setTerm(dto.getTerm());
                fundRepaymentDeductService.save(deductEntity);
            }
            return Result.success(insert > 0);
        }
        if (Objects.equals(dto.getFeeType(), 16) || Objects.equals(dto.getFeeType(), 17)) {
            if (ObjUtil.isNotEmpty(dto.getId())) {
                OrderPayApplicationInfoEntity entity = orderPayApplicationMapper.selectById(dto.getId());
                entity.setPaymentDetails(dto.getPaymentDetails());
                entity.setPayAccount(dto.getTransferUserName());
                entity.setPayAccountName(dto.getPayAccountName());
                entity.setCurrentNode(PayApplicationNodeEnums.CASHIER_APPROVAL);
                entity.setPayAccountNumber(dto.getPayAccountNumber());
                entity.setPayeeAccount(dto.getPayeeAccount());
                entity.setPayeeAccountName(dto.getPayeeAccountName());
                entity.setPayeeAccountNumber(dto.getPayeeAccountNumber());
                entity.setPayeeAmount(dto.getPayeeAmount());
                entity.setPaymentVoucherList(JSONUtil.toJsonStr(dto.getResourceId()));
                entity.setPaymentTime(dto.getPaymentTime());
                entity.setRemark(dto.getRemark());
                entity.setRepaymentTerm(dto.getTerm());
                entity.setLoanSettlementMethod(dto.getLoanSettlementMethod());
                entity.setReductionAmount(dto.getReductionAmount());
                entity.setRepaymentShowAmount(dto.getRepaymentShowAmount());
                int update = orderPayApplicationMapper.updateById(entity);
                saveNodeRecord(entity.getId(), PayApplicationNodeEnums.ACCOUNTANT_APPLY, entity.getCurrentNode(), PayApplicationAuditTypeEnum.YUNQI
                        , null, entity.getRemark(), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), LocalDateTime.now());

                return Result.success(update > 0);
            }
            OrderPayApplicationInfoEntity entity = new OrderPayApplicationInfoEntity();
            entity.setOrderId(dto.getOrderId());
            entity.setPayeeType(PayApplicationPayeeTypeEnum.fromCode(dto.getPayeeType()));
            entity.setFeeType(Objects.equals(dto.getFeeType(), OrderFeeDetailExpandTypeEnum.GPS_DATA_TRANSFER_FEE.getCode()) ? OrderFeeDetailExpandTypeEnum.GPS_DATA_TRANSFER_FEE : OrderFeeDetailExpandTypeEnum.GPS_EQUIPMENT_COMPENSATION_FEE);
            entity.setPaymentDetails(dto.getPaymentDetails());
            entity.setPayAccount(dto.getTransferUserName());
            entity.setPayAccountName(dto.getPayAccountName());
            entity.setPayAccountNumber(dto.getPayAccountNumber());
            entity.setPayeeAccount(dto.getPayeeAccount());
            entity.setPayeeAccountName(dto.getPayeeAccountName());
            entity.setPayeeAccountNumber(dto.getPayeeAccountNumber());
            entity.setPayeeAmount(dto.getPayeeAmount());
            entity.setPaymentVoucherList(JSONUtil.toJsonStr(dto.getResourceId()));
            entity.setPaymentTime(dto.getPaymentTime());
            entity.setRemark(dto.getRemark());
            entity.setRepaymentTerm(dto.getTerm());
            entity.setReductionAmount(dto.getReductionAmount());
            entity.setCurrentNode(PayApplicationNodeEnums.CASHIER_APPROVAL);
            entity.setApplyUserId(loginUser.getUserId());
            entity.setApplyType(OrderFeeDetailStatusEnum.INCOME);
            entity.setLoanSettlementMethod(dto.getLoanSettlementMethod());
            entity.setReductionAmount(dto.getReductionAmount());
            entity.setRepaymentShowAmount(dto.getRepaymentShowAmount());
            entity.setFeeDetails(2);
            int insert = orderPayApplicationMapper.insert(entity);
            saveNodeRecord(entity.getId(), PayApplicationNodeEnums.ACCOUNTANT_APPLY, entity.getCurrentNode(), PayApplicationAuditTypeEnum.YUNQI
                    , null, entity.getRemark(), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), LocalDateTime.now());
            return Result.success(insert > 0);
        }
        throw new RuntimeException("暂不支持该类型");
    }

    private BigDecimal reductionAmount(OrderInfoEntity orderInfoEntity) {
        List<FundRepaymentInfoEntity> repaymentInfoList = fundRepaymentInfoMapper.selectList(
                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(FundRepaymentInfoEntity::getFundId, orderInfoEntity.getFundId())
                        .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
        );
        BigDecimal remainingPrincipal = repaymentInfoList.stream()
                .map(FundRepaymentInfoEntity::getRepaymentPrincipal)
                .reduce(BigDecimal.ZERO, (a, b) -> a.add(b == null ? BigDecimal.ZERO : b))
                .subtract(repaymentInfoList.stream()
                        .map(FundRepaymentInfoEntity::getActuallyPrincipal)
                        .reduce(BigDecimal.ZERO, (a, b) -> a.add(b == null ? BigDecimal.ZERO : b)));
        OrderAmountEntity orderAmount = orderAmountMapper.selectOne(
                new LambdaQueryWrapper<OrderAmountEntity>()
                        .eq(OrderAmountEntity::getOrderId, orderInfoEntity.getId())
                        .eq(OrderAmountEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderAmountEntity::getCreateTime),
                false
        );
        LoanSettlementDTO settlementDTO = new LoanSettlementDTO();
        settlementDTO.setOrderId(orderInfoEntity.getId()); // 设置订单ID
        settlementDTO.setLoanAmount(orderAmount.getCustomerConfirmAmount()); // 设置放款金额
        settlementDTO.setFundSettlementAmount(remainingPrincipal); // 设置资金结算金额
        settlementDTO.setLoanSettlementMethod(LoanSettlementEnum.CLOSED_PERIOD_PENALTY_INSIDE); // 设置贷款结算方式
        BigDecimal firmRepayAmountTotal = BigDecimal.ZERO;
        BigDecimal penaltyAmount = BigDecimal.ZERO;
        // 调用外部服务进行结清计算
        LoanSettlementVO settlementVO = approveFeign.calLoanSettlement(settlementDTO).getData();
        if (settlementVO != null) {
            // 计算还款总额
            BigDecimal settlementAmount = settlementVO.getSettlementAmount();
            if (ObjUtil.isNotNull(settlementAmount) && settlementAmount.compareTo(BigDecimal.ZERO) > 0) {
                firmRepayAmountTotal = settlementAmount.subtract(remainingPrincipal);
                penaltyAmount = settlementVO.getPenaltyAmount();
            }
        }

        List<SprEductionUsageEntity> list = sprEductionUsageService.list(
                new LambdaQueryWrapper<SprEductionUsageEntity>()
                        .eq(SprEductionUsageEntity::getVehicleNumber, orderInfoEntity.getVehicleNumber())
                        .eq(SprEductionUsageEntity::getUsageStatus, 2)
                        .eq(SprEductionUsageEntity::getDeleteFlag, 0)
        );
        BigDecimal ourCompanySettles = firmRepayAmountTotal.add(penaltyAmount);
//        //所有剩余本金
        BigDecimal totalRepaymentPrincipal = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(repaymentInfoList)) {
            totalRepaymentPrincipal = repaymentInfoList.stream()
                    .map(FundRepaymentInfoEntity::getRepaymentPrincipal)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        BigDecimal reductionAmount = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(list)) {
            if (list.stream().anyMatch(entity -> entity.getApprovalTemplate() == 1)) {
                reductionAmount = list.stream()
                        .filter(entity -> entity.getApprovalTemplate() == 1)
                        .map(SprEductionUsageEntity::getReductionAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

            } else {
                if (list.stream().anyMatch(entity -> entity.getApprovalTemplate() == 2)) {
                    long count = list.stream()
                            .filter(entity -> entity.getApprovalTemplate() == 1)
                            .count();
                    if (count == 1) {
                        BigDecimal reductionAmount1 = list.stream()
                                .filter(entity -> entity.getApprovalTemplate() == 2)
                                .findFirst()
                                .get()
                                .getReductionAmount();
                        BigDecimal multiply = totalRepaymentPrincipal.multiply(reductionAmount1);
                        reductionAmount = ourCompanySettles.subtract(multiply);
                    }
                }
            }
        }
        return reductionAmount;
    }

    /**
     * 记录审核节点
     *
     * @param applyInfoId 付款申请信息ID
     * @param currentNode 当前节点
     * @param nextNode    下一个节点
     * @param auditType   审批类型
     * @param processId   钉钉审批流程ID
     * @param remark      备注
     * @param event       事件类型（1:通过，2：拒绝， 3:驳回）
     * @param approveTime 审核时间
     */
    private void saveNodeRecord(int applyInfoId, PayApplicationNodeEnums currentNode, PayApplicationNodeEnums nextNode,
                                PayApplicationAuditTypeEnum auditType, String processId,
                                String remark, PayApplicationEventEnums event, Integer currentUserId, LocalDateTime approveTime) {
        try {
            OrderPayApplyNodeRecordEntity recordEntity = new OrderPayApplyNodeRecordEntity()
                    .setApplyInfoId(applyInfoId)
                    .setCurrentNode(currentNode)
                    .setRemark(remark)
                    .setEvent(event)
                    .setAuditType(auditType)
                    .setProcessId(processId)
                    .setNextNode(nextNode);
            recordEntity.setCreateBy(currentUserId);
            recordEntity.setUpdateBy(currentUserId);
            recordEntity.setUpdateTime(approveTime);
            orderPayApplyNodeRecordMapper.insert(recordEntity);
        } catch (Exception e) {
            log.error("OrderPayApplicationServiceImpl.saveNodeRecord applyInfoId:{} err:{}", applyInfoId, e.getMessage(), e);
        }
    }

    @Override
    public void scanCodePaymentResult() {
        List<FundRepaymentDeductEntity> fundRepaymentDeductEntityList = fundRepaymentDeductService.list(
                new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                        .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                FundDeductBizTypeEnums.PENALTY_PAYMENT,
                                FundDeductBizTypeEnums.PENALTY_DEDUCTION))
                        .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)
                        .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isNotEmpty(fundRepaymentDeductEntityList)) {
            for (FundRepaymentDeductEntity fundRepaymentDeductEntity : fundRepaymentDeductEntityList) {
                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(fundRepaymentDeductEntity.getOrderId());
                if (Objects.equals(fundRepaymentDeductEntity.getBizType(), FundDeductBizTypeEnums.PENALTY_PAYMENT)) {
                    FundDeductRepayStatusEnums fundDeductRepayStatusEnums = searchTranx(fundRepaymentDeductEntity.getOrderId());
                    if (Objects.equals(fundDeductRepayStatusEnums, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)) {
                        sprEductionUsageService.update(
                                new LambdaUpdateWrapper<SprEductionUsageEntity>()
                                        .eq(SprEductionUsageEntity::getVehicleNumber, orderInfoEntity.getVehicleNumber())
                                        .eq(SprEductionUsageEntity::getDeleteFlag, 0)
                                        .set(SprEductionUsageEntity::getUsageStatus, 1)
                        );
                    }
                }
                if (Objects.equals(fundRepaymentDeductEntity.getBizType(), FundDeductBizTypeEnums.PENALTY_DEDUCTION)) {
                    FundDeductRepayStatusEnums fundDeductRepayStatusEnums = tongLianTongService.huiFengDeductQuery(fundRepaymentDeductEntity.getOrderId());
                    if (Objects.equals(fundDeductRepayStatusEnums, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)) {
                        sprEductionUsageService.update(
                                new LambdaUpdateWrapper<SprEductionUsageEntity>()
                                        .eq(SprEductionUsageEntity::getVehicleNumber, orderInfoEntity.getVehicleNumber())
                                        .eq(SprEductionUsageEntity::getDeleteFlag, 0)
                                        .set(SprEductionUsageEntity::getUsageStatus, 1)
                        );
                    }
                }
            }
        }

    }

    @Override
    public ReductionAmountVO reductionAmount(ReductionAmountDTO dto) {

        return null;
    }

    @Override
    public Boolean getPenaltyWaiver() {
        List<SprEductionUsageEntity> entityList = sprEductionUsageService.list(
                new LambdaQueryWrapper<SprEductionUsageEntity>()
                        .eq(SprEductionUsageEntity::getDeleteFlag, 0)
        );
        List<Integer> idList = entityList.stream().map(SprEductionUsageEntity::getInstanceId).toList();
        String approvalName1 = "大区减免违约金申请";
        String approvalName2 = "封闭期内违约金模式结清申请";
        String approvalName3 = "减免申请";
        String approvalName4 = "";
        if (envUtil.isPrd()) {
            approvalName4 = "特殊退款申请";
        } else {
            approvalName4 = "特殊退款申请（测试）";
        }

        List<SprEductionUsageEntity> list = new ArrayList<>();
        Map<Integer, Map<String, String>> integerMapMap1 = dingDrawMoneyFeign.updateDingDrawInfo(approvalName1);
        log.info("FundDeductServiceImpl.getPenaltyWaiver.integerMapMap1 {}", JSONUtil.toJsonStr(integerMapMap1));
        Map<Integer, Map<String, String>> integerMapMap2 = dingDrawMoneyFeign.updateDingDrawInfo(approvalName2);
        log.info("FundDeductServiceImpl.getPenaltyWaiver.integerMapMap2 {}", JSONUtil.toJsonStr(integerMapMap2));
        Map<Integer, Map<String, String>> integerMapMap3 = dingDrawMoneyFeign.updateDingDrawInfo(approvalName3);
        log.info("FundDeductServiceImpl.getPenaltyWaiver.integerMapMap3 {}", JSONUtil.toJsonStr(integerMapMap3));
        Map<Integer, Map<String, String>> integerMapMap4 = dingDrawMoneyFeign.updateDingDrawInfo(approvalName4);
        log.info("FundDeductServiceImpl.getPenaltyWaiver.integerMapMap3 {}", JSONUtil.toJsonStr(integerMapMap4));
        integerMapMap1.forEach((k, v) -> {
            if (!idList.contains(k)) {
                SprEductionUsageEntity sprEductionUsageEntity = new SprEductionUsageEntity();
                sprEductionUsageEntity.setInstanceId(k);
                sprEductionUsageEntity.setVehicleNumber(v.getOrDefault("TextField-JMBEQWRP", ""));
                sprEductionUsageEntity.setReductionAmount(v.containsKey("TextField-K0XJ0G3V") ? BigDecimal.valueOf(Double.parseDouble(v.get("TextField-K0XJ0G3V").replaceAll("[^\\d.-]", ""))) : BigDecimal.ZERO);
                sprEductionUsageEntity.setApprovalTemplate(1);
                sprEductionUsageEntity.setUsageStatus(2);
                sprEductionUsageEntity.setApprovalDetails(JSONUtil.toJsonStr(v));
                list.add(sprEductionUsageEntity);
            }
        });
        integerMapMap2.forEach((k, v) -> {
            if (!idList.contains(k)) {
                SprEductionUsageEntity sprEductionUsageEntity = new SprEductionUsageEntity();
                sprEductionUsageEntity.setInstanceId(k);
                sprEductionUsageEntity.setVehicleNumber(v.getOrDefault("TextField_2026KBJNA3B40", ""));
                sprEductionUsageEntity.setReductionAmount(v.containsKey("NumberField_1G564LH6CWCG0") ? BigDecimal.valueOf(Double.parseDouble(v.get("NumberField_1G564LH6CWCG0").replaceAll("[^\\d.-]", ""))).divide(BigDecimal.valueOf(100)) : BigDecimal.ZERO);
                sprEductionUsageEntity.setApprovalTemplate(2);
                sprEductionUsageEntity.setUsageStatus(2);
                sprEductionUsageEntity.setApprovalDetails(JSONUtil.toJsonStr(v));
                list.add(sprEductionUsageEntity);
            }
        });
        integerMapMap3.forEach((k, v) -> {
            if (!idList.contains(k)) {
                SprEductionUsageEntity sprEductionUsageEntity = new SprEductionUsageEntity();
                sprEductionUsageEntity.setInstanceId(k);
                sprEductionUsageEntity.setVehicleNumber(v.getOrDefault("TextField-JMBEQWRP", ""));
                sprEductionUsageEntity.setReductionAmount(v.containsKey("TextField-K0XJ0G3V") ? BigDecimal.valueOf(Double.parseDouble(v.get("TextField-K0XJ0G3V").replaceAll("[^\\d.-]", ""))) : BigDecimal.ZERO);
                sprEductionUsageEntity.setApprovalTemplate(3);
                sprEductionUsageEntity.setUsageStatus(2);
                sprEductionUsageEntity.setApprovalDetails(JSONUtil.toJsonStr(v));
                list.add(sprEductionUsageEntity);
            }
        });
        integerMapMap4.forEach((k, v) -> {
            if (!idList.contains(k)) {
                SprEductionUsageEntity sprEductionUsageEntity = new SprEductionUsageEntity();
                sprEductionUsageEntity.setInstanceId(k);
                sprEductionUsageEntity.setVehicleNumber(v.getOrDefault("TextField_IR3ANOW44VC0", ""));
                sprEductionUsageEntity.setReductionAmount(v.containsKey("NumberField-JMBEQWRQ") ? BigDecimal.valueOf(Double.parseDouble(v.get("NumberField-JMBEQWRQ").replaceAll("[^\\d.-]", ""))) : BigDecimal.ZERO);
                sprEductionUsageEntity.setApprovalTemplate(6);
                sprEductionUsageEntity.setApprovalDetails(JSONUtil.toJsonStr(v));
                sprEductionUsageEntity.setUsageStatus(2);
                list.add(sprEductionUsageEntity);
            }
        });
        log.info("FundDeductServiceImpl.getPenaltyWaiver.list {}", JSONUtil.toJsonStr(list));
        return sprEductionUsageService.saveBatch(list);
//        return null;
    }

    @Override
    public Boolean getProductInfo(ProductInfoDTO dto) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
        FundProductMappingEntity fundProductMappingEntity = fundProductMappingMapper.selectOne(
                new LambdaQueryWrapper<FundProductMappingEntity>()
                        .eq(FundProductMappingEntity::getProductId, orderInfoEntity.getProductId())
                        .eq(FundProductMappingEntity::getFundId, orderInfoEntity.getFundId())
                        .eq(FundProductMappingEntity::getDeleteFlag, 0)
                        .orderByDesc(FundProductMappingEntity::getCreateTime)
                        .last("limit 1")
        );
        return Objects.equals(fundProductMappingEntity.getClosedPeriod(), 6);
    }


    private TongLianSearchVO searchTranxSend(Integer orderId, String paySnNum) {
        TreeMap<String, String> params = new TreeMap<String, String>();
        //        if (!SybUtil.isEmpty(SybConstants.SYB_ORGID)) {
        //            params.put("orgid", SybConstants.SYB_ORGID);
        //        }
        params.put("orgid", "");
        //商户号 实际交易的商户号(必传)
        params.put("cusid", SYB_CUSID);
        //应用ID 平台分配的APPID(必传)
        params.put("appid", SYB_APPID);
        //接口版本号 默认填11
        params.put("version", "11");
        params.put("reqsn", paySnNum);
        //随机字符串(必传)
        String validatecode = SybUtil.getValidatecode(8);
        params.put("randomstr", validatecode);
        //签名方式(必传)
        params.put("signtype", SIGN_TYPE);
        log.info("通联支付查询验签参数:{}", params.toString());
        String appkey = "";
        if (SIGN_TYPE.equals("RSA")) {
            appkey = SYB_RSACUSPRIKEY;
        } else if (SIGN_TYPE.equals("SM2")) {
            appkey = SYB_SM2PPRIVATEKEY;
        } else {
            appkey = SYB_MD5_APPKEY;
        }
        String result = null;
        RequestResponseInfoEntity requestResponseInfoEntity = new RequestResponseInfoEntity();
        try {
            String sign = SybUtil.unionSign(params, appkey, SIGN_TYPE);
            params.put("sign", sign);
            requestResponseInfoEntity.setRequestBody(params.toString());
            requestResponseInfoEntity.setTId(orderId);
            requestResponseInfoEntity.setTName(TNAME);
            requestResponseInfoEntity.setCode(RequestResponseCode.TONGLIAN_PAY_QUERY);
            requestResponseInfoMapper.insert(requestResponseInfoEntity);
            log.info("通联支付查询验签参数:{}", params);
            log.info("CustomerAppointmentServiceImpl searchTranxSend: SYB_CUSID:{},SYB_APPID:{},version:{},reqsn:{},validatecode:{},SIGN_TYPE:{},sign:{}", SYB_CUSID, SYB_APPID, "11", paySnNum, validatecode, SIGN_TYPE, sign);
            result = tongLianPayFeign.searchTranx(SYB_CUSID, SYB_APPID, "11", paySnNum, validatecode,
                    SIGN_TYPE, sign);
            requestResponseInfoEntity.setResponseBody(result);
            requestResponseInfoMapper.updateById(requestResponseInfoEntity);
        } catch (Exception e) {
            throw new BusinessException("查询交易sign签名失败,e{}", e);
        }
        log.info("通联支付查询交易返回结果:{}", result);
        Map<String, String> map = new HashMap<>();
        try {
            map = handleResult(result);
            log.info("通联支付查询交易返回的参数，map:{}", map.toString());
        } catch (Exception e) {
            throw new BusinessException("通联支付查询交易|||验证签名失败|||,e{}", e);
        }
        ObjectMapper objectMapper = new ObjectMapper();
        TongLianSearchVO tongLianSearchVO = new TongLianSearchVO();
        try {
            // 将 map 转换为 TongLianPayVO 对象
            tongLianSearchVO = objectMapper.convertValue(map, TongLianSearchVO.class);
        } catch (Exception e) {
            log.error("将 map 转换为对象时出错: {}", e.getMessage(), e);
        }
        return tongLianSearchVO;
    }

    private TongLianPayResultVO tongLianPaySend(Integer orderId, String trxamt, String reqsn) {
        TreeMap<String, String> params = new TreeMap<String, String>();
        //商户号 实际交易的商户号(必传)
        params.put("cusid", SYB_CUSID);
        //应用ID 平台分配的APPID(必传)
        params.put("appid", SYB_APPID);
        //交易金额(必传)
        params.put("trxamt", trxamt);
        //商户交易单号
        params.put("reqsn", reqsn);
        //交易方式(必传)
        params.put("paytype", CustomerPayEnum.WXSM.getCode());
        //随机字符串(必传)
        String validatecode = SybUtil.getValidatecode(8);
        params.put("randomstr", validatecode);
        //签名方式(必传)
        params.put("signtype", SIGN_TYPE);
        //交易结果通知地址 接收交易结果的通知回调地址，通知url必须为直接可访问的url，不能携带参数。https只支持默认端口
        params.put("notify_url", TL_CALL_BACK);
        String appkey = "";
        if (SIGN_TYPE.equals("RSA")) {
            appkey = SYB_RSACUSPRIKEY;
        } else if (SIGN_TYPE.equals("SM2")) {
            appkey = SYB_SM2PPRIVATEKEY;
        } else {
            appkey = SYB_MD5_APPKEY;
        }
        String result = null;
        RequestResponseInfoEntity requestResponseInfoEntity = new RequestResponseInfoEntity();
        try {
            String sign = SybUtil.unionSign(params, appkey, SIGN_TYPE);
            requestResponseInfoEntity.setRequestBody(sign);
            requestResponseInfoEntity.setTId(orderId);
            requestResponseInfoEntity.setTName(TNAME);
            requestResponseInfoEntity.setCode(RequestResponseCode.TONGLIAN_PAY);
            requestResponseInfoMapper.insert(requestResponseInfoEntity);

            log.info("CustomerAppointmentServiceImpl tongLianPaySend: SYB_CUSID:{},SYB_APPID:{},trxamt:{},reqsn:{},validatecode:{},SIGN_TYPE:{},sign:{}", SYB_CUSID, SYB_APPID, trxamt, reqsn, validatecode, SIGN_TYPE, sign);
            result = tongLianPayFeign.pay(SYB_CUSID, SYB_APPID, trxamt,
                    reqsn, CustomerPayEnum.WXSM.getCode(), validatecode,
                    SIGN_TYPE, TL_CALL_BACK, sign);
            requestResponseInfoEntity.setResponseBody(result);
            requestResponseInfoMapper.updateById(requestResponseInfoEntity);
            log.info(result);
        } catch (Exception e) {
            throw new BusinessException("支付sign签名失败,e{}", e);
        }
        log.info("通联支付返回结果:{}", result);

        Map<String, String> map = new HashMap<>();
        try {
            map = handleResult(result);
            log.info("通联支付返回的参数，map:{}", map);
        } catch (Exception e) {
            log.error("通联支付|||验证签名失败|||,e{}", e.getMessage(), e);
            throw new BusinessException("支付|||验证签名失败|||");
        }

        ObjectMapper objectMapper = new ObjectMapper();
        TongLianPayResultVO tongLianPayVO = null;

        try {
            // 将 map 转换为 TongLianPayVO 对象
            tongLianPayVO = objectMapper.convertValue(map, TongLianPayResultVO.class);
        } catch (Exception e) {
            log.error("将 map 转换为对象时出错: {}", e.getMessage(), e);
        }


        return tongLianPayVO;
    }

    public Map<String, String> handleResult(String result) throws Exception {
        log.info("ret:" + result);
        Map map = SybUtil.json2Obj(result, Map.class);
        if (map == null) {
            throw new Exception("返回数据错误");
        }
        if ("SUCCESS".equals(map.get("retcode"))) {
            TreeMap tmap = new TreeMap(map);
            String appkey = "";
            if (SIGN_TYPE.equals("RSA")) {
                appkey = SYB_RSATLPUBKEY;
            } else if (SIGN_TYPE.equals("SM2")) {
                appkey = SYB_SM2TLPUBKEY;
            } else {
                appkey = SYB_MD5_APPKEY;
            }
            if (SybUtil.validSign(tmap, appkey, SIGN_TYPE)) {
                log.info("签名成功");
                return map;
            } else {
                throw new Exception("|||验证签名失败|||");
            }

        } else {
            return map;
        }
    }

    public TlCloseTransactionVO tlCloseTransaction(String reqsn, Integer orderId) {
        log.info("CustomerAppointmentServiceImpl tlCloseTransaction: reqsn:{},orderId:{}", reqsn, orderId);
        TreeMap<String, String> params = new TreeMap<String, String>();
        //params.put("orgid", SYB_ORGID);
        //商户号 实际交易的商户号(必传)
        params.put("cusid", SYB_CUSID);
        //应用ID 平台分配的APPID(必传)
        params.put("appid", SYB_APPID);
        params.put("oldreqsn", reqsn);
        //随机字符串(必传)
        String validatecode = SybUtil.getValidatecode(8);
        params.put("randomstr", validatecode);
        //签名方式(必传)
        params.put("signtype", SIGN_TYPE);
        log.info("通联关闭订单验签参数:{}", params);
        String appkey = "";
        if (SIGN_TYPE.equals("RSA")) {
            appkey = SYB_RSACUSPRIKEY;
        } else if (SIGN_TYPE.equals("SM2")) {
            appkey = SYB_SM2PPRIVATEKEY;
        } else {
            appkey = SYB_MD5_APPKEY;
        }
        String result = null;
        RequestResponseInfoEntity requestResponseInfoEntity = new RequestResponseInfoEntity();
        try {
            String sign = SybUtil.unionSign(params, appkey, SIGN_TYPE);
            params.put("sign", sign);
            requestResponseInfoEntity.setRequestBody(params.toString());
            requestResponseInfoEntity.setTId(orderId);
            requestResponseInfoEntity.setTName(TNAME);
            requestResponseInfoEntity.setCode(RequestResponseCode.TONGLIAN_PAY_QUERY);
            requestResponseInfoMapper.insert(requestResponseInfoEntity);
            log.info("通联支付查询验签参数:{}", params);
            log.info("CustomerAppointmentServiceImpl searchTranxSend: SYB_CUSID:{},SYB_APPID:{},reqsn:{},validatecode:{},SIGN_TYPE:{},sign:{}", SYB_CUSID, SYB_APPID, "11", validatecode, SIGN_TYPE, sign);
            result = tongLianPayFeign.closeTransaction(SYB_CUSID, SYB_APPID, reqsn, validatecode, SIGN_TYPE, sign);
            log.info("CustomerAppointmentServiceImpl searchTranxSend: result:{}", result);
            requestResponseInfoEntity.setResponseBody(result);
            requestResponseInfoMapper.updateById(requestResponseInfoEntity);
        } catch (Exception e) {
            throw new BusinessException("查询交易sign签名失败,e{}", e);
        }
        log.info("通联支付查询交易返回结果:{}", result);
        Map<String, String> map = new HashMap<>();
        try {
            map = handleResult(result);
            log.info("通联支付查询交易返回的参数，map:{}", map.toString());
        } catch (Exception e) {
            throw new BusinessException("通联支付查询交易|||验证签名失败|||,e{}", e);
        }
        ObjectMapper objectMapper = new ObjectMapper();
        TlCloseTransactionVO tongLianSearchVO = new TlCloseTransactionVO();
        try {
            // 将 map 转换为 TongLianPayVO 对象
            tongLianSearchVO = objectMapper.convertValue(map, TlCloseTransactionVO.class);
            log.info("通联支付查询交易返回对象:{}", JSONUtil.toJsonStr(tongLianSearchVO));
        } catch (Exception e) {
            log.error("将 map 转换为对象时出错: {}", e.getMessage(), e);
        }
        return tongLianSearchVO;
    }

    public static String getPayNumber() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        String formattedDateTime = now.format(formatter);
        return RISK_NUMBER + formattedDateTime;
    }

    /**
     * 提前还款计算
     */
    @Override
    public FundEarlyRepaymentCalcDTO earlyRepayCalc(FundRepayCalcEarlyDTO dto) {
        log.info("FundDeductServiceImpl.earlyRepayCalc - begin, orderId: {}", dto.getOrderId());
        // 获取订单信息
        OrderInfoEntity orderInfo = getOrderInfo(dto.getOrderId());
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));


        FundEarlyRepaymentCalcDTO calcDTO = new FundEarlyRepaymentCalcDTO();

        /*log.info("FundDeductServiceImpl.earlyRepayCalc - begin, orderId: {}", dto.getOrderId());

        // 获取订单信息
        OrderInfoEntity orderInfo = getOrderInfo(dto.getOrderId());
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));

        // 初始化变量
        BigDecimal fundRepayAmt = BigDecimal.ZERO;
        FundEarlyRepaymentCalcDTO calcDTO = new FundEarlyRepaymentCalcDTO();
        BigDecimal amountTotal = BigDecimal.ZERO;
        LoanSettlementDTO settlementDTO = new LoanSettlementDTO();
        settlementDTO.setIsReturnCash(dto.getIsReturnCash());
        OrderCustomerInfoEntity orderCustomerInfoEntity = customerInfoMapper.selectById(orderInfo.getCustomerId());
        if (orderCustomerInfoEntity != null){
            settlementDTO.setCustomerRating(ObjUtil.defaultIfNull(orderCustomerInfoEntity.getCustomerLevel(),2));
        }
        try {
            List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                            .eq(FundRepaymentDeductEntity::getFundId, orderInfo.getFundId())
                            .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                            .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                    FundDeductBizTypeEnums.PAYMENT,
                                    FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                    FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                    FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE
                            ))
                            .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                            .orderByAsc(FundRepaymentDeductEntity::getUpdateTime)
            );
            // 获取资方类型并处理资方提前还款计算
            FundEnum fundEnum = getFundEnum(orderInfo.getFundId());
            if (ObjUtil.equals(orderInfo.getIsRepurchase(), 1)) {
                boolean flag = list.stream().anyMatch(e -> e.getIsRepurchase() != null && e.getIsRepurchase() == 1);
                if (!flag){
                    // 获取剩余本金和已还款期数
                    List<RepurchaseRepaymentInfoEntity> repaymentInfoList = repurchaseRepaymentInfoMapper.selectList(
                            new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                                    .eq(RepurchaseRepaymentInfoEntity::getOrderId, dto.getOrderId())
                                    .eq(RepurchaseRepaymentInfoEntity::getFundId, orderInfo.getFundId())
                                    .ne(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                                    .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                    );
                    BigDecimal remainingPrincipal = repaymentInfoList.stream()
                            .map(RepurchaseRepaymentInfoEntity::getRepaymentPrincipal)
                            .reduce(BigDecimal.ZERO, (a, b) -> a.add(b == null ? BigDecimal.ZERO : b))
                            .subtract(repaymentInfoList.stream()
                                    .map(RepurchaseRepaymentInfoEntity::getActuallyPrincipal)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, (a, b) -> a.add(b == null ? BigDecimal.ZERO : b)));
                    calcDTO.setFundSettleDTO(new FundEarlyRepaymentCalcDTO.FundSettleDTO()
                            .setPrincipal(remainingPrincipal)
                            .setPenalty(BigDecimal.ZERO)
                            .setInterest(BigDecimal.ZERO)
                            .setFine(BigDecimal.ZERO)
                            .setComInterest(BigDecimal.ZERO)
                            .setRepayAmt(remainingPrincipal));
                }else {
                    // 获取剩余本金和已还款期数
                    List<RepurchaseRepaymentInfoEntity> repaymentInfoList = repurchaseRepaymentInfoMapper.selectList(
                            new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                                    .eq(RepurchaseRepaymentInfoEntity::getOrderId, dto.getOrderId())
                                    .eq(RepurchaseRepaymentInfoEntity::getFundId, orderInfo.getFundId())
                                    .ge(RepurchaseRepaymentInfoEntity::getTerm, list.get(0).getTerm())
                                    .eq(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                                    .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                    );
                    BigDecimal repaymentPrincipal = repaymentInfoList.stream()
                            .map(RepurchaseRepaymentInfoEntity::getRepaymentPrincipal)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal actuallyPrincipal = repaymentInfoList.stream()
                            .map(RepurchaseRepaymentInfoEntity::getActuallyPrincipal)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (Objects.equals(repaymentPrincipal.compareTo(actuallyPrincipal),0)){
                        if (Objects.equals(list.size(),1)){
                            BigDecimal principal = repaymentInfoList.stream()
                                    .map(RepurchaseRepaymentInfoEntity::getActuallyPrincipal)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            amountTotal = repaymentInfoList.stream()
                                    .map(RepurchaseRepaymentInfoEntity::getActuallyAmountTotal)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal interestAmount = repaymentInfoList.stream()
                                    .map(RepurchaseRepaymentInfoEntity::getActuallyInterest)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal penaltyInterest = repaymentInfoList.stream()
                                    .map(RepurchaseRepaymentInfoEntity::getActuallyPenaltyInterest)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            calcDTO.setFundSettleDTO(new FundEarlyRepaymentCalcDTO.FundSettleDTO()
                                    .setPrincipal(principal)
                                    .setPenalty(BigDecimal.ZERO)
                                    .setInterest(interestAmount)
                                    .setFine(penaltyInterest)
                                    .setComInterest(BigDecimal.ZERO)
                                    .setRepayAmt(amountTotal));
                            settlementDTO.setFundSettleFlag(1);
                        }else {
                            BigDecimal principal = repaymentInfoList.stream()
                                    .filter(entity -> entity.getTerm() >= list.get(list.size()-1).getTerm())
                                    .map(RepurchaseRepaymentInfoEntity::getActuallyPrincipal)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            amountTotal = repaymentInfoList.stream()
                                    .filter(entity -> entity.getTerm() >= list.get(list.size()-1).getTerm())
                                    .map(RepurchaseRepaymentInfoEntity::getActuallyAmountTotal)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal interestAmount = repaymentInfoList.stream()
                                    .map(RepurchaseRepaymentInfoEntity::getActuallyInterest)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal penaltyInterest = repaymentInfoList.stream()
                                    .map(RepurchaseRepaymentInfoEntity::getActuallyPenaltyInterest)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            calcDTO.setFundSettleDTO(new FundEarlyRepaymentCalcDTO.FundSettleDTO()
                                    .setPrincipal(principal)
                                    .setPenalty(BigDecimal.ZERO)
                                    .setInterest(interestAmount)
                                    .setFine(penaltyInterest)
                                    .setComInterest(BigDecimal.ZERO)
                                    .setRepayAmt(amountTotal));
                            settlementDTO.setFundSettleFlag(1);
                        }
                    }
                }


            } else {
                log.info("FundDeductServiceImpl.earlyRepayCalc dto: {}", JSONUtil.toJsonStr(dto));
                //委外逾期
                if(Objects.equals(dto.getSettleFlag(), 2) && Objects.equals(FundEnum.YING_FENG.getValue(), orderInfo.getFundId()) && orderInfo.getOverdueDays() >0){
                    calcDTO = overdueRepayCalc(dto, orderInfo);
                }else {
                    if (CollUtil.isNotEmpty(list)){
                        List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList = fundRepaymentInfoMapper.selectList(
                                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                        .eq(FundRepaymentInfoEntity::getOrderId, dto.getOrderId())
                                        .ge(FundRepaymentInfoEntity::getTerm, list.get(0).getTerm())
                                        .eq(FundRepaymentInfoEntity::getFundId, orderInfo.getFundId())
                                        .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                                        .eq(FundRepaymentInfoEntity::getDeleteFlag,0)
                        );
                        BigDecimal repaymentPrincipal = fundRepaymentInfoEntityList.stream()
                                .map(FundRepaymentInfoEntity::getRepaymentPrincipal)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal actuallyPrincipal = fundRepaymentInfoEntityList.stream()
                                .map(FundRepaymentInfoEntity::getActuallyPrincipal)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
//                        if (fundRepaymentInfoEntityList.stream()
//                                .anyMatch(entity -> Objects.equals(entity.getRepaymentStatus() , FundRepayStatusEnum.SETTLED))){
                        if (Objects.equals(repaymentPrincipal.compareTo(actuallyPrincipal),0)){
                            if (Objects.equals(list.size(),1)){
                                //本金
                                BigDecimal principal = fundRepaymentInfoEntityList.stream()
                                        .filter(entity -> entity.getTerm() >= list.get(0).getTerm())
                                        .map(FundRepaymentInfoEntity::getActuallyPrincipal)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                amountTotal = fundRepaymentInfoEntityList.stream()
                                        .filter(entity -> entity.getTerm() >= list.get(0).getTerm())
                                        .map(FundRepaymentInfoEntity::getActuallyAmountTotal)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal interestAmount = fundRepaymentInfoEntityList.stream()
                                        .map(FundRepaymentInfoEntity::getActuallyInterest)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal penaltyInterest = fundRepaymentInfoEntityList.stream()
                                        .map(FundRepaymentInfoEntity::getActuallyPenaltyInterest)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                *//*BigDecimal guaraFeeAmount = fundRepaymentInfoEntityList.stream()
                                        .map(FundRepaymentInfoEntity::getActuallyGuaraFeeAmount)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal guaraFeeOdAmount = fundRepaymentInfoEntityList.stream()
                                        .map(FundRepaymentInfoEntity::getActuallyGuaraFeeOdAmount)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);*//*
                                calcDTO.setFundSettleDTO(new FundEarlyRepaymentCalcDTO.FundSettleDTO()
                                        .setPrincipal(principal)
                                        .setPenalty(BigDecimal.ZERO)
                                        .setInterest(interestAmount)
                                        .setFine(penaltyInterest)
                                        .setComInterest(BigDecimal.ZERO)
//                                        .setGuaraFeeAmt(guaraFeeAmount)
//                                        .setGuaraFeeOdAmt(guaraFeeOdAmount)
                                        .setRepayAmt(amountTotal));
                                settlementDTO.setFundSettleFlag(1);
                            }else {
                                //本金
                                BigDecimal principal = fundRepaymentInfoEntityList.stream()
                                        .filter(entity -> entity.getTerm() >= list.get(list.size()-1).getTerm())
                                        .map(FundRepaymentInfoEntity::getActuallyPrincipal)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                amountTotal = fundRepaymentInfoEntityList.stream()
                                        .filter(entity -> entity.getTerm() >= list.get(list.size()-1).getTerm())
                                        .map(FundRepaymentInfoEntity::getActuallyAmountTotal)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal interestAmount = fundRepaymentInfoEntityList.stream()
                                        .map(FundRepaymentInfoEntity::getActuallyInterest)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal penaltyInterest = fundRepaymentInfoEntityList.stream()
                                        .map(FundRepaymentInfoEntity::getActuallyPenaltyInterest)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            *//*    BigDecimal guaraFeeAmount = fundRepaymentInfoEntityList.stream()
                                        .map(FundRepaymentInfoEntity::getActuallyGuaraFeeAmount)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal guaraFeeOdAmount = fundRepaymentInfoEntityList.stream()
                                        .map(FundRepaymentInfoEntity::getActuallyGuaraFeeOdAmount)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);*//*
                                calcDTO.setFundSettleDTO(new FundEarlyRepaymentCalcDTO.FundSettleDTO()
                                        .setPrincipal(principal)
                                        .setPenalty(BigDecimal.ZERO)
                                        .setInterest(interestAmount)
                                        .setFine(penaltyInterest)
                                        .setComInterest(BigDecimal.ZERO)
//                                        .setGuaraFeeAmt(guaraFeeAmount)
//                                        .setGuaraFeeOdAmt(guaraFeeOdAmount)
                                        .setRepayAmt(amountTotal));
                                settlementDTO.setFundSettleFlag(1);
                            }
                        }
                    }else {
                        calcDTO = handleRepayCalc(dto);
                    }
                }


            }
            // 设置资方名称
            calcDTO.setFundName(fundEnum.getFundName());

            // 获取资方结清金额
            fundRepayAmt = calcDTO.getFundSettleDTO().getRepayAmt();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("FundDeductServiceImpl.earlyRepayCalc - error, orderId: {}", dto.getOrderId(), e);
            throw new BusinessException("提前还款计算异常");
        }

        // 初始化我司还款金额
        BigDecimal firmRepayAmountTotal = BigDecimal.ZERO;
        BigDecimal penaltyAmount = BigDecimal.ZERO;
        try {
            // 查询订单金额信息
            OrderAmountEntity orderAmount = orderAmountMapper.selectOne(
                    new LambdaQueryWrapper<OrderAmountEntity>()
                            .eq(OrderAmountEntity::getOrderId, dto.getOrderId())
                            .eq(OrderAmountEntity::getDeleteFlag, 0)
                            .orderByDesc(OrderAmountEntity::getCreateTime),
                    false
            );
            Assert.notNull(orderAmount, () -> new BusinessException("订单金额不存在"));

            // 创建结清计算DTO

            settlementDTO.setOrderId(dto.getOrderId()); // 设置订单ID
            settlementDTO.setLoanAmount(orderAmount.getCustomerConfirmAmount()); // 设置放款金额
            settlementDTO.setFundSettlementAmount(fundRepayAmt); // 设置资金结算金额
            settlementDTO.setLoanSettlementMethod(dto.getLoanSettlementMethod()); // 设置贷款结算方式
            if(Objects.equals(dto.getSettleFlag(), 2)){
                settlementDTO.setSettleFlag(2); //委外结清
            }

            // 调用外部服务进行结清计算
            LoanSettlementVO settlementVO = approveFeign.calLoanSettlement(settlementDTO).getData();
            if (settlementVO != null) {
                // 设置结算信息
                calcDTO.setSettlementVO(settlementVO);

                // 计算还款总额
                BigDecimal settlementAmount = calcDTO.getSettlementVO().getSettlementAmount();
                if (ObjUtil.isNotNull(settlementAmount) && settlementAmount.compareTo(BigDecimal.ZERO) > 0) {
                    firmRepayAmountTotal = settlementAmount.subtract(fundRepayAmt);
                    penaltyAmount=settlementVO.getPenaltyAmount();
                    calcDTO.getSettlementVO().setSettlementAmount(firmRepayAmountTotal);
                }
                calcDTO.setSettlePenaltyRate(settlementVO.getSettlePenaltyRate());
            }
        } catch (Exception e) {
            log.error("FundDeductServiceImpl.earlyRepayCalc calLoanSettlement, orderId: {} err:{}", dto.getOrderId(), e.getMessage(), e);
        }

        // 设置结清总额
//        calcDTO.setFundRepayAmt(fundRepayAmt);
        calcDTO.setFundRepayAmt(fundRepayAmt);
*//*        calcDTO.setRepayAmountTotal(fundRepayAmt.add(firmRepayAmountTotal));
        if (ObjUtil.equals(orderInfo.getIsRepurchase(), 1)) {
            LoanSettlementVO loanSettlementVO = calcDTO.getSettlementVO();

            calcDTO.setFundRepayAmt(fundRepayAmt.add(firmRepayAmountTotal)
                    .add(ObjUtil.defaultIfNull(loanSettlementVO.getPenaltyAmount(), BigDecimal.ZERO))
                    .add(ObjUtil.defaultIfNull(loanSettlementVO.getLatePenaltyAmount(), BigDecimal.ZERO))
//            calcDTO.setRepayAmountTotal(fundRepayAmt.add(firmRepayAmountTotal)
//                            .add(ObjUtil.defaultIfNull(loanSettlementVO.getPenaltyAmount(), BigDecimal.ZERO))
//                            .add(ObjUtil.defaultIfNull(loanSettlementVO.getLatePenaltyAmount(), BigDecimal.ZERO))
//                    //TODO 暂无大区减免输入入口不做减免计算
////                    .subtract(ObjUtil.defaultIfNull(loanSettlementVO.getReductionAmount(), BigDecimal.ZERO))
            );
        }*//*
        log.info("FundDeductServiceImpl.earlyRepayCalc - SUCCESS, orderID: {}", dto.getOrderId());

        BigDecimal ourCompanySettles =firmRepayAmountTotal.add(penaltyAmount);
//        //所有剩余本金
        BigDecimal totalRepaymentPrincipal = BigDecimal.ZERO;
        //我司结清
        //获取剩余本金
        if (ObjUtil.equals(orderInfo.getIsRepurchase(), 1)){
            List<RepurchaseRepaymentInfoEntity> fundRepaymentInfoEntityList = repurchaseRepaymentInfoMapper.selectList(
                    new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                            .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderInfo.getId())
                            .eq(RepurchaseRepaymentInfoEntity::getFundId, orderInfo.getFundId())
                            .ne(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                            .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
            );
            if (CollUtil.isNotEmpty(fundRepaymentInfoEntityList)) {
                totalRepaymentPrincipal = fundRepaymentInfoEntityList.stream()
                        .map(RepurchaseRepaymentInfoEntity::getRepaymentPrincipal)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
        }else {
            List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList = fundRepaymentInfoMapper.selectList(
                    new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                            .eq(FundRepaymentInfoEntity::getOrderId, orderInfo.getId())
                            .eq(FundRepaymentInfoEntity::getFundId, orderInfo.getFundId())
                            .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
            );
            if (CollUtil.isNotEmpty(fundRepaymentInfoEntityList)) {
                totalRepaymentPrincipal = fundRepaymentInfoEntityList.stream()
                        .map(FundRepaymentInfoEntity::getRepaymentPrincipal)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
        }*/
        // 违约金
        BigDecimal ourCompanySettles = BigDecimal.ZERO;
        //所有剩余本金
        BigDecimal totalRepaymentPrincipal = BigDecimal.ZERO;
        SettleCalculationVO settleCalculationVO = settleCalculationService.calculateByOrderId(
                dto.getOrderId(),
                switch (dto.getLoanSettlementMethod()) {
                    case CLOSED_PERIOD_OUTSIDE -> 0;
                    case CLOSED_PERIOD_INSIDE -> 1;
                    case CLOSED_PERIOD_PENALTY_INSIDE -> 2;
                });
        ourCompanySettles = settleCalculationVO.getPenaltyAmount();
        if (Objects.equals(dto.getLoanSettlementMethod(), LoanSettlementEnum.CLOSED_PERIOD_INSIDE)) {
            ourCompanySettles = ourCompanySettles.add(settleCalculationVO.getInterestAmount());
        }
        totalRepaymentPrincipal = settleCalculationVO.getSettleCalculationContext().getRemainingPrincipal();
        FundEarlyRepaymentCalcDTO.FundSettleDTO fundSettleDTO = new FundEarlyRepaymentCalcDTO.FundSettleDTO();
        fundSettleDTO.setPrincipal(settleCalculationVO.getSettleCalculationContext().getRemainingPrincipal());

        fundSettleDTO.setFine(settleCalculationVO.getLatePenaltyAmount());
        fundSettleDTO.setGuaraFeeAmt(settleCalculationVO.getSettleCalculationContext().getGuaraFeeAmt());
        fundSettleDTO.setGuaraFeeOdAmt(settleCalculationVO.getSettleCalculationContext().getGuaraFeeOdAmt());
        calcDTO.setFundName(settleCalculationVO.getSettleCalculationContext().getFundName());
        calcDTO.setInstalmentsRepaid(settleCalculationVO.getSettleCalculationContext().getPaidPeriods());
        LoanSettlementVO vo = new LoanSettlementVO();


        if (orderInfo.getIsRepurchase() == 1) {
            Long outsourcing = caseInfoEntityMapper.selectCount(new LambdaQueryWrapper<CaseInfoEntity>()
                    .eq(CaseInfoEntity::getOrderId, orderInfo.getId())
                    .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS)
                    .eq(CaseInfoEntity::getDeleteFlag, 0));
            BigDecimal fundSettlementAmount;
            if (outsourcing > 0) {
                fundSettlementAmount = settleCalculationVO.getSettleCalculationContext().getRedeemAmount()
                        .add(settleCalculationVO.getSettleCalculationContext().getRedeemSingleRepay())
                        .subtract(settleCalculationVO.getSettleCalculationContext().getRedeemPaidAmount());
            } else {
                fundSettlementAmount = settleCalculationVO.getSettleCalculationContext().getRemainingPrincipal();
            }
            fundSettleDTO.setRepayAmt(fundSettlementAmount.add(settleCalculationVO.getLatePenaltyAmount()));
            vo.setSettlementAmount(fundSettlementAmount.add(settleCalculationVO.getLatePenaltyAmount()));
            calcDTO.setFundRepayAmt(fundSettlementAmount.add(settleCalculationVO.getLatePenaltyAmount()));
            if (!Objects.equals(dto.getLoanSettlementMethod(), LoanSettlementEnum.CLOSED_PERIOD_INSIDE) || outsourcing > 0) {
                fundSettleDTO.setRepayAmt(fundSettlementAmount.add(settleCalculationVO.getLatePenaltyAmount()).add(settleCalculationVO.getInterestAmount()));
                vo.setSettlementAmount(fundSettlementAmount.add(settleCalculationVO.getLatePenaltyAmount()).add(settleCalculationVO.getInterestAmount()));
                calcDTO.setFundRepayAmt(fundSettlementAmount.add(settleCalculationVO.getLatePenaltyAmount()).add(settleCalculationVO.getInterestAmount()));
                fundSettleDTO.setInterest(settleCalculationVO.getInterestAmount());
            }
        } else {
            fundSettleDTO.setInterest(settleCalculationVO.getSettleCalculationContext().getFundSettlementAmount().subtract(settleCalculationVO.getSettleCalculationContext().getRemainingPrincipal()));
            fundSettleDTO.setRepayAmt(settleCalculationVO.getSettleCalculationContext().getFundSettlementAmount());
            vo.setSettlementAmount(settleCalculationVO.getSettleCalculationContext().getFundSettlementAmount());
            calcDTO.setFundRepayAmt(settleCalculationVO.getSettleCalculationContext().getFundSettlementAmount());
        }

        vo.setLatePenaltyAmount(settleCalculationVO.getLatePenaltyAmount());
        vo.setReductionAmount(settleCalculationVO.getReductionAmount());
        vo.setUseDays(settleCalculationVO.getUseDays());
        vo.setSettlePenaltyRate(settleCalculationVO.getSettlePenaltyRate());
        calcDTO.setSettlementVO(vo);
        calcDTO.setFundSettleDTO(fundSettleDTO);
        calcDTO.setSettlePenaltyRate(settleCalculationVO.getSettlePenaltyRate());
        if (ObjUtil.isNotNull(dto.getReductionFlag())) {
            List<SprEductionUsageEntity> list = sprEductionUsageService.list(
                    new LambdaQueryWrapper<SprEductionUsageEntity>()
                            .eq(SprEductionUsageEntity::getVehicleNumber, orderInfo.getVehicleNumber())
                            .eq(SprEductionUsageEntity::getUsageStatus, 2)
                            .eq(SprEductionUsageEntity::getDeleteFlag, 0)
                            .orderByDesc(SprEductionUsageEntity::getCreateTime)
            );
            if (CollUtil.isNotEmpty(list)) {
                if (list.stream().anyMatch(entity -> entity.getApprovalTemplate() == 1)) {
                    BigDecimal reductionAmount = list.stream()
                            .filter(entity -> entity.getApprovalTemplate() == 1)
                            .map(SprEductionUsageEntity::getReductionAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    calcDTO.setDeductionAmount(reductionAmount);
                } else if (list.stream().anyMatch(entity -> entity.getApprovalTemplate() == 2)) {
                    long count = list.stream()
                            .filter(entity -> entity.getApprovalTemplate() == 2)
                            .count();
                    if (count == 1) {
                        BigDecimal reductionAmount = list.stream()
                                .filter(entity -> entity.getApprovalTemplate() == 2)
                                .findFirst()
                                .get()
                                .getReductionAmount();
                        BigDecimal multiply = totalRepaymentPrincipal.multiply(reductionAmount);
                        BigDecimal subtract = ourCompanySettles.subtract(multiply);
                        calcDTO.setDeductionAmount(subtract);

                    } else if (list.stream().anyMatch(entity -> entity.getApprovalTemplate() == 3)) {
                        BigDecimal reductionAmount = list.stream()
                                .filter(entity -> entity.getApprovalTemplate() == 3)
                                .map(SprEductionUsageEntity::getReductionAmount)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        calcDTO.setDeductionAmount(reductionAmount);
                    }
                }
            }
        }
        //获取对公转账金额
        List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(
                new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                        .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                        .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                        .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                        .in(FundRepaymentDeductEntity::getBizType,
                                Arrays.asList(
                                        FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION,
                                        FundDeductBizTypeEnums.PENALTY_PAYMENT,
                                        FundDeductBizTypeEnums.PENALTY_DEDUCTION,
                                        FundDeductBizTypeEnums.PENALTY_INITIATE_REPAYMENT_AFTER_PAYMENT
                                )
                        )
                        .orderByDesc(FundRepaymentDeductEntity::getCreateTime)
        );
        //TODO 待确认合计金额是否需要减去违约金
        if (CollUtil.isNotEmpty(list)) {
            if (
                    list.stream().anyMatch(entity -> Objects.equals(entity.getBizType(), FundDeductBizTypeEnums.PENALTY_PAYMENT))
                            ||
                            list.stream().anyMatch(entity -> Objects.equals(entity.getBizType(), FundDeductBizTypeEnums.PENALTY_DEDUCTION))
                            ||
                            list.stream().anyMatch(entity -> Objects.equals(entity.getBizType(), FundDeductBizTypeEnums.PENALTY_INITIATE_REPAYMENT_AFTER_PAYMENT))
            ) {
                ourCompanySettles = BigDecimal.ZERO;
            } else {
                //获取对公转账金额
                BigDecimal totalDeductAmount = list.stream()
                        .filter(entity -> entity.getBizType() == FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION)
                        .filter(entity -> Objects.equals(entity.getRepayStatus(), FundDeductRepayStatusEnums.REPAYMENT_SUCCESS))
                        .map(FundRepaymentDeductEntity::getDeductAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                ourCompanySettles = ourCompanySettles.subtract(ObjUtil.isNotNull(totalDeductAmount) ? totalDeductAmount : BigDecimal.ZERO);
                //获取已使用的违约金减免
                List<SprEductionUsageEntity> list1 = sprEductionUsageService.list(
                        new LambdaQueryWrapper<SprEductionUsageEntity>()
                                .eq(SprEductionUsageEntity::getVehicleNumber, orderInfo.getVehicleNumber())
                                .eq(SprEductionUsageEntity::getDeleteFlag, 0)
                                .eq(SprEductionUsageEntity::getUsageStatus, 1)
                );
                if (CollUtil.isNotEmpty(list1)) {
                    //获取已使用的违约金减免金额
                    BigDecimal DeductAmount = list1.stream()
                            .map(SprEductionUsageEntity::getReductionAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal subtract = ourCompanySettles.subtract(ObjUtil.isNotNull(DeductAmount) ? DeductAmount : BigDecimal.ZERO);
                    if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                        ourCompanySettles = ourCompanySettles.subtract(ObjUtil.isNotNull(DeductAmount) ? DeductAmount : BigDecimal.ZERO);
                    } else {
                        ourCompanySettles = BigDecimal.ZERO;
                    }
                    calcDTO.setDeductionAmount(BigDecimal.ZERO);
                }
                if (Objects.equals(ourCompanySettles, BigDecimal.ZERO) &&
                        list.stream().anyMatch(entity -> Objects.equals(entity.getBizType(), FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION))) {
                    FundRepaymentDeductEntity one = fundRepaymentDeductService.getOne(
                            new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                                    .eq(FundRepaymentDeductEntity::getOrderId, dto.getOrderId())
                                    .eq(FundRepaymentDeductEntity::getBizType, FundDeductBizTypeEnums.PENALTY_INITIATE_REPAYMENT_AFTER_PAYMENT)
                                    .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                    );
                    if (ObjUtil.isEmpty(one)) {
                        FundRepaymentDeductEntity deductEntity = new FundRepaymentDeductEntity();
                        deductEntity.setOrderId(dto.getOrderId());
                        deductEntity.setRepayDate(LocalDate.now());
                        deductEntity.setRepayType(FundDeductRepayTypeEnums.EARLY_SETTLEMENT);
                        deductEntity.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_SUCCESS);
                        deductEntity.setBizType(FundDeductBizTypeEnums.PENALTY_INITIATE_REPAYMENT_AFTER_PAYMENT);
                        deductEntity.setDeductAmount(BigDecimal.ZERO);
                        deductEntity.setTerm(dto.getTerm());
                        fundRepaymentDeductService.save(deductEntity);
                    }
                }
            }
        }

        log.info("FundDeductServiceImpl.earlyRepayCalc - SUCCESS, orderID: {}", dto.getOrderId());

        if ((ObjUtil.equals(orderInfo.getPlanState(), 1) && ObjUtil.equals(orderInfo.getIsRepurchase(), 0))
                || (ObjUtil.equals(orderInfo.getCompanyPlanState(), 1) && ObjUtil.equals(orderInfo.getIsRepurchase(), 1))) {
            ourCompanySettles = BigDecimal.ZERO;
        }
        calcDTO.setFirmRepayAmountTotal(ObjUtil.isNotEmpty(ourCompanySettles) ? ourCompanySettles.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
//        calcDTO.setFundRepayAmt(fundRepayAmt);
        calcDTO.setDeductionAmount(ObjUtil.isNotEmpty(calcDTO.getDeductionAmount())
                ? calcDTO.getDeductionAmount().abs().setScale(2, RoundingMode.HALF_UP)
                : BigDecimal.ZERO);
        calcDTO.setRepayAmountTotal(calcDTO.getFundRepayAmt().add(calcDTO.getFirmRepayAmountTotal()));
        if (ObjUtil.isEmpty(calcDTO.getSettlementVO())) {
            calcDTO.setSettlementVO(new LoanSettlementVO());
        }

        GpsDataDTO gpsDataDTO = new GpsDataDTO();
        gpsDataDTO.setOrderId(orderInfo.getId());
        gpsDataDTO.setDataSource(0);

        //查询gps分期车务费信息
        try {
            Result<GpsDataVO> gpsRepaymentInfo = autoLoanRepaymentFeign.getGpsRepaymentInfo(gpsDataDTO);
            GpsDataVO gpsDataVO = gpsRepaymentInfo.getData();
            calcDTO.setRemainingGpsAmount(gpsDataVO.getRemainingGpsAmount());
        } catch (Exception e) {
            log.error("FundDeductServiceImpl.earlyRepayCalc,getGpsRepaymentInfo,error:{}", e.getMessage(), e);
        }

        return calcDTO;
    }


    private FundEarlyRepaymentCalcDTO overdueRepayCalc(FundRepayCalcEarlyDTO dto, OrderInfoEntity orderInfo) {
        log.info("FundDeductServiceImpl.overdueRepayCalc - START, orderId: {}", dto.getOrderId());
        FundEarlyRepaymentCalcDTO calcDTO = new FundEarlyRepaymentCalcDTO();
        List<FundRepaymentInfoEntity> repaymentInfoList = fundRepaymentInfoMapper.selectList(
                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, dto.getOrderId())
                        .eq(FundRepaymentInfoEntity::getFundId, orderInfo.getFundId())
                        .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
        );
        BigDecimal remainingPrincipal = repaymentInfoList.stream()
                .map(FundRepaymentInfoEntity::getRepaymentPrincipal)
                .reduce(BigDecimal.ZERO, (a, b) -> a.add(b == null ? BigDecimal.ZERO : b))
                .subtract(repaymentInfoList.stream()
                        .map(FundRepaymentInfoEntity::getActuallyPrincipal)
                        .reduce(BigDecimal.ZERO, (a, b) -> a.add(b == null ? BigDecimal.ZERO : b)));
        calcDTO.setFundSettleDTO(new FundEarlyRepaymentCalcDTO.FundSettleDTO()
                .setPrincipal(remainingPrincipal)
                .setPenalty(BigDecimal.ZERO)
                .setInterest(BigDecimal.ZERO)
                .setFine(BigDecimal.ZERO)
                .setComInterest(BigDecimal.ZERO)
                .setRepayAmt(remainingPrincipal));
        return calcDTO;
    }


    /**
     * 匹配资方申请代扣
     */
    @Override
    public boolean matchDeductApply(FundDeductApplyDTO dto) {
        LoanSettlementEnum loanSettlementMethod = dto.getLoanSettlementMethod();
        Boolean settleFlag = dto.getSettleFlag();
        Integer term = dto.getTerm();
        Integer orderId = dto.getOrderId();
        log.info("FundDeductServiceImpl.matchDeductApply - Matching deduction apply start, orderId: {}, fundRepaymentId: {}, settleFlag: {}", orderId, term, settleFlag);
        //如果不是整笔结清并且term为空 默认取最近应还一期
        if (!settleFlag && ObjUtil.isNull(term)) {
            FundRepaymentInfoEntity fundRepaymentInfo = fundRepaymentInfoMapper.selectOne(new MPJLambdaWrapper<FundRepaymentInfoEntity>()
                    .select(FundRepaymentInfoEntity::getTerm)
                    .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                    .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED.getValue())
                    .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                    .orderByAsc(FundRepaymentInfoEntity::getTerm)
                    .last("limit 1")
            );
            Assert.notNull(fundRepaymentInfo, () -> new BusinessException("未找到符合条件的期次"));

            term = fundRepaymentInfo.getTerm();
        }
        //上边逻辑最终划扣期数已经获取为term，查询当前订单和这一期的划扣状态
        FundRepaymentDeductEntity fundRepaymentDeduct = repaymentDeductMapper.selectOne(new MPJLambdaWrapper<FundRepaymentDeductEntity>()
                .select(FundRepaymentDeductEntity::getRepayStatus)
                .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                .eq(FundRepaymentDeductEntity::getTerm, term)
                .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                .orderByDesc(FundRepaymentDeductEntity::getCreateTime) // 按创建时间降序排序
                .last("limit 1") // 限制结果为最新的一条记录
        );
        if (ObjUtil.isNotEmpty(fundRepaymentDeduct) && ObjUtil.equals(fundRepaymentDeduct.getRepayStatus(), FundDeductRepayStatusEnums.REPAYMENT_PROCESSING.getCode())) {
            throw new BusinessException("请耐心等待资方还款结果通知，请勿重复操作！");
        }
        // 尝试获取分布式锁
        String lockKey = LOCK_KEY_PREFIX + orderId;
        String lockValue = Convert.toStr(orderId);
        try {
            Boolean lockSuccess = redisService.tryLock(lockKey, lockValue, 30);
            if (Objects.equals(Boolean.FALSE, lockSuccess)) {
                log.error("FundDeductServiceImpl.matchDeductApply - Failed to obtain lock, orderId: {}, fundRepaymentId: {}, settleFlag: {}", orderId, term, settleFlag);
                throw new BusinessException("已发起划扣，请稍后再试");
            }
            // 获取锁成功
            OrderInfoEntity orderInfo = getOrderInfo(orderId);

            Result<Boolean> result = null;
            if (ObjUtil.equals(orderInfo.getIsRepurchase(), 1)) {
                //赎回通联划扣
                result = tongLianTongService.matchDeductApply(orderId, term, settleFlag, loanSettlementMethod);
            } else {
                //划扣资方
                FundRepayDTO fundRepayDTO = new FundRepayDTO();
                fundRepayDTO.setOrderId(orderId)
                        .setTerm(term)
                        .setSettleFlag(settleFlag)
                        .setRepayMode(dto.getRepayMode())
                        .setRepayYingFengTransferDTO(dto.getRepayYingFengTransferDTO())
                ;
                result = approveFeign.repay(fundRepayDTO);
            }

            if (!Result.isSuccess(result)) {
                log.info("FundDeductServiceImpl.matchDeductApply - matching deduction apply fail, orderId: {}, fundRepaymentId: {}", orderId, term);
                throw new BusinessException(result.getMsg());
            }
            if (settleFlag) {
                FundRepayCalcEarlyDTO fundRepayCalcEarlyDTO = new FundRepayCalcEarlyDTO();
                fundRepayCalcEarlyDTO.setOrderId(orderId);
                fundRepayCalcEarlyDTO.setLoanSettlementMethod(dto.getLoanSettlementMethod());
                fundRepayCalcEarlyDTO.setRepayMode(dto.getRepayMode());
                FundEarlyRepaymentCalcDTO fundEarlyRepaymentCalcDTO = earlyRepayCalc(fundRepayCalcEarlyDTO);
                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                        .eq(OrderInfoEntity::getId, orderId)
                        .eq(OrderInfoEntity::getDeleteFlag, 0), false);
                orderSettleAmountRecordMapper.update(new LambdaUpdateWrapper<OrderSettleAmountRecordEntity>()
                        .set(OrderSettleAmountRecordEntity::getDeleteFlag, 1)
                        .eq(OrderSettleAmountRecordEntity::getOrderId, orderId)
                        .eq(OrderSettleAmountRecordEntity::getDeleteFlag, 0));
                OrderSettleAmountRecordEntity entity = new OrderSettleAmountRecordEntity()
                        .setOrderId(orderId)
                        .setOrderSource(1)
                        .setOrderNumber(orderInfoEntity.getOrderNumber())
                        .setTrialSettlementAmount(fundEarlyRepaymentCalcDTO.getRepayAmountTotal())
//                        .setApplySettlementAmount(fundEarlyRepaymentCalcDTO.getRepayAmountTotal())
                        .setActualSettlementAmount(fundEarlyRepaymentCalcDTO.getRepayAmountTotal())
                        .setSettlementMethod(loanSettlementMethod.getDesc())
                        .setOutsourcingStatus(0)
                        .setUsageDays(fundEarlyRepaymentCalcDTO.getSettlementVO().getUseDays())
                        .setInstalmentsRepaid(fundEarlyRepaymentCalcDTO.getInstalmentsRepaid());
                if (ObjUtil.equals(loanSettlementMethod, LoanSettlementEnum.CLOSED_PERIOD_PENALTY_INSIDE)) {
                    entity.setSettlementMethod(entity.getSettlementMethod() + fundEarlyRepaymentCalcDTO.getSettlementVO().getSettlePenaltyRate().multiply(new BigDecimal("100")) + "%");
                }
                orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                                .eq(OrderPayApplicationInfoEntity::getOrderId, orderId)
                                .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                                .eq(OrderPayApplicationInfoEntity::getApplyType, 1)
                                .in(OrderPayApplicationInfoEntity::getFeeType, List.of(OrderFeeDetailExpandTypeEnum.OTHER,
                                        OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE, OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT,
                                        OrderFeeDetailExpandTypeEnum.DISPOSABLE_SECURITY_DEPOSIT, OrderFeeDetailExpandTypeEnum.INSTALLMENT_SERVICE_FEE,
                                        OrderFeeDetailExpandTypeEnum.GPS_DATA_TRANSFER_FEE, OrderFeeDetailExpandTypeEnum.GPS_EQUIPMENT_COMPENSATION_FEE))
                                .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)).stream().map(OrderPayApplicationInfoEntity::getPayeeAmount)
                        .reduce(BigDecimal::add).ifPresentOrElse(entity::setRefundsTotalAmount, () -> entity.setRefundsTotalAmount(BigDecimal.ZERO));
                orderSettleAmountRecordMapper.insert(entity);
            }
            return true;
        } catch (BusinessException e) {
            log.error("FundDeductServiceImpl.matchDeductApply - Business exception during deduction apply match", e);
            throw e;
        } catch (Exception e) {
            log.error("FundDeductServiceImpl.matchDeductApply - Error during deduction apply match", e);
            throw new BusinessException("代扣申请异常");
        } finally {
            redisService.releaseLock(lockKey, lockValue);
        }
    }

    @Override
    public boolean penaltyDeductApply(PenaltyDeductApplyDTO dto) {

        LoanSettlementEnum loanSettlementMethod = dto.getLoanSettlementMethod();
        Integer orderId = dto.getOrderId();
        log.info("FundDeductServiceImpl.matchDeductApply - Matching deduction apply start, orderId: {}", orderId);
        // 尝试获取分布式锁
        String lockKey = LOCK_KEY_PREFIX + orderId + "penalty";
        String lockValue = Convert.toStr(orderId);
        try {
            Boolean lockSuccess = redisService.tryLock(lockKey, lockValue, 30);
            if (Objects.equals(Boolean.FALSE, lockSuccess)) {
                log.error("FundDeductServiceImpl.matchDeductApply - Failed to obtain lock, orderId: {}", orderId);
                throw new BusinessException("已发起划扣，请稍后再试");
            }
            // 获取锁成功
            OrderInfoEntity orderInfo = getOrderInfo(orderId);

            Result<Boolean> result = null;
            // 1.判断是否为结清划扣 划扣汇丰结清金额  走通联划扣（先判断是否存在一笔划扣中的，存在查询结果）
            //2. 在汇丰结清通联划扣结果查询方法判断 是否成功如果成功 则走资方结清划扣
            //3. 汇丰划扣成功 执行资方结清
            //是否需要重新发起划

            List<FundRepaymentDeductEntity> fundRepaymentDeductEntityList = fundRepaymentDeductService.list(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                    .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                    .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                    .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                            FundDeductBizTypeEnums.PENALTY_DEDUCTION,
                            FundDeductBizTypeEnums.PENALTY_PAYMENT,
                            FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION))
            );
            boolean flag = fundRepaymentDeductEntityList.stream().anyMatch(
                    entity ->
                            entity.getRepayType() == FundDeductRepayTypeEnums.EARLY_SETTLEMENT
                                    &&
                                    entity.getRepayStatus() == FundDeductRepayStatusEnums.REPAYMENT_PROCESSING
                                    &&
                                    entity.getBizType() == FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION
            );
            if (flag) {
                throw new BusinessException("当前订单的结清违约金已有正在处理中的操作，请耐心等待");
            }
            if (CollectionUtils.isNotEmpty(fundRepaymentDeductEntityList)) {
                FundDeductRepayStatusEnums fundDeductRepayStatusEnums = tongLianTongService.huiFengDeductQuery(orderId);
                if (Objects.equals(fundDeductRepayStatusEnums, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)) {
                    throw new BusinessException("当前订单的结清违约金已结清，无需重复操作");
                }
                if (Objects.equals(fundDeductRepayStatusEnums, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)) {
                    throw new BusinessException("当前订单的结清违约金已有正在处理中的操作，请耐心等待");
                }
                FundDeductRepayStatusEnums fundDeductRepayStatusEnums1 = searchTranx(dto.getOrderId());
                if (Objects.equals(fundDeductRepayStatusEnums1, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)) {
                    throw new BusinessException("当前订单的结清违约金已结清，无需重复操作");
                }
                if (Objects.equals(fundDeductRepayStatusEnums1, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)) {
                    throw new BusinessException("当前订单的结清违约金已有正在处理中的操作，请耐心等待");
                }
//                    if (Objects.equals(fundDeductRepayStatusEnums, FundDeductRepayStatusEnums.REPAYMENT_FAILED)){
                //汇丰通联划扣
                result = tongLianTongService.huiFengMatchDeductApply(orderId, loanSettlementMethod, dto);
//                     }

            } else {
                result = tongLianTongService.huiFengMatchDeductApply(orderId, loanSettlementMethod, dto);
            }

            if (!Result.isSuccess(result)) {
                log.info("FundDeductServiceImpl.matchDeductApply - matching deduction apply fail, orderId: {}", orderId);
                throw new BusinessException(result.getMsg());
            }

            return true;
        } catch (BusinessException e) {
            log.error("FundDeductServiceImpl.matchDeductApply - Business exception during deduction apply match", e);
            throw e;
        } catch (Exception e) {
            log.error("FundDeductServiceImpl.matchDeductApply - Error during deduction apply match", e);
            throw new BusinessException("代扣申请异常");
        } finally {
            redisService.releaseLock(lockKey, lockValue);
        }

    }

    @Override
    public byte[] applyQrCode(FundRepaymentDTO fundRepaymentDTO) {
        log.info("FundDeductServiceImpl.applyQrCode begin orderId:{} term:{}", fundRepaymentDTO.getOrderId(), fundRepaymentDTO.getTerm());
        try {
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(fundRepaymentDTO.getOrderId());
            FundEnum fundEnum = FundEnum.getFundEnum(orderInfo.getFundId());
            if (fundEnum == null) {
                log.warn("FundDeductServiceImpl.applyQrCode invalid fundEnum or fundRepaymentId");
                throw new BusinessException("无效的资方");
            }

            Result<String> result;
            switch (fundEnum) {
                case YING_FENG -> result = approveFeign.yingFengApplyQrCode(fundRepaymentDTO);
                default -> throw new BusinessException("不支持的资方类型: " + fundEnum);
            }

            log.info("FundDeductServiceImpl.applyQrCode end orderId:{} term:{}", fundRepaymentDTO.getOrderId(), fundRepaymentDTO.getTerm());
            if (!Result.isSuccess(result)) {
                throw new BusinessException(result.getMsg());
            }
            return QrCodeUtils.generateQrCode("", result.getData());
        } catch (BusinessException e) {
            log.error("FundDeductServiceImpl.applyQrCode error e:", e);
            throw e;
        } catch (Exception e) {
            log.error("FundDeductServiceImpl.applyQrCode error e:", e);
            throw new BusinessException("获取资方付款二维码异常");
        }
    }

    @Override
    public Integer qrPayResult(FundRepaymentDTO fundRepaymentDTO) {
        log.info("FundDeductServiceImpl.qrPayResult begin orderId:{} term:{}", fundRepaymentDTO.getOrderId(), fundRepaymentDTO.getTerm());
        try {
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(fundRepaymentDTO.getOrderId());
            FundEnum fundEnum = FundEnum.getFundEnum(orderInfo.getFundId());
            if (fundEnum == null) {
                log.warn("FundDeductServiceImpl.qrPayResult invalid fundEnum or fundRepaymentId");
                throw new BusinessException("无效的资方");
            }

            Result<Integer> result;
            switch (fundEnum) {
                case YING_FENG -> result = approveFeign.yingFengQrPayResult(fundRepaymentDTO);
                default -> throw new BusinessException("不支持的资方类型: " + fundEnum);
            }

            log.info("FundDeductServiceImpl.qrPayResult end orderId:{} term:{}", fundRepaymentDTO.getOrderId(), fundRepaymentDTO.getTerm());
            if (!Result.isSuccess(result)) {
                throw new BusinessException(result.getMsg());
            }
            return result.getData();
        } catch (BusinessException e) {
            log.error("FundDeductServiceImpl.qrPayResult error e:", e);
            throw e;
        } catch (Exception e) {
            log.error("FundDeductServiceImpl.qrPayResult error e:", e);
            throw new BusinessException("获取资方二维码付款结果异常");
        }
    }


    @Override
    public Boolean repayApply(FundRepaymentDTO fundRepaymentDTO) {
        log.info("FundDeductServiceImpl.repayApply begin orderId:{} term:{}", fundRepaymentDTO.getOrderId(), fundRepaymentDTO.getTerm());
        try {
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(fundRepaymentDTO.getOrderId());
            FundEnum fundEnum = FundEnum.getFundEnum(orderInfo.getFundId());
            if (fundEnum == null) {
                log.warn("FundDeductServiceImpl.repayApply invalid fundEnum or fundRepaymentId");
                return false;
            }

            Result<Boolean> result;
            switch (fundEnum) {
                case YING_FENG -> result = approveFeign.yingFengRepayApply(fundRepaymentDTO);
                default -> throw new BusinessException("不支持的资方类型: " + fundEnum);
            }

            log.info("FundDeductServiceImpl.repayApply end orderId:{} term:{}", fundRepaymentDTO.getOrderId(), fundRepaymentDTO.getTerm());
            if (!Result.isSuccess(result)) {
                throw new BusinessException(result.getMsg());
            }
            return result.getData();
        } catch (BusinessException e) {
            log.error("FundDeductServiceImpl.repayApply error e:", e);
            throw e;
        } catch (Exception e) {
            log.error("FundDeductServiceImpl.repayApply error e:", e);
            throw new BusinessException("发起资方还款异常");
        }
    }

    @Override
    public Boolean queryRepayResult(FundRepaymentDTO fundRepaymentDTO) {
        log.info("FundDeductServiceImpl.queryRepayResult begin orderId:{} term:{}", fundRepaymentDTO.getOrderId(), fundRepaymentDTO.getTerm());
        try {
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(fundRepaymentDTO.getOrderId());
            FundEnum fundEnum = FundEnum.getFundEnum(orderInfo.getFundId());
            if (fundEnum == null) {
                log.warn("FundDeductServiceImpl.queryRepayResult invalid fundEnum or fundRepaymentId");
                return false;
            }

            Result<Boolean> result;
            switch (fundEnum) {
                case YING_FENG -> result = approveFeign.yingFengQueryRepayResult(fundRepaymentDTO);
                default -> throw new BusinessException("不支持的资方类型: " + fundEnum);
            }
            log.info("FundDeductServiceImpl.queryRepayResult end orderId:{} term:{}", fundRepaymentDTO.getOrderId(), fundRepaymentDTO.getTerm());
            if (!Result.isSuccess(result)) {
                throw new BusinessException(result.getMsg());
            }
            return result.getData();
        } catch (BusinessException e) {
            log.error("FundDeductServiceImpl.queryRepayResult error e:", e);
            throw e;
        } catch (Exception e) {
            log.error("FundDeductServiceImpl.queryRepayResult error e:", e);
            throw new BusinessException("获取资方还款结果异常");
        }
    }

    @Override
    public FundDeductCheckVO checkThePeriod(FundRepaymentDeductCheckDTO dto) {
        Result<FundDeductCheckVO> result = approveFeign.checkThePeriod(dto);
        if (!Result.isSuccess(result)) {
            throw new BusinessException(result.getMsg());
        }
        return result.getData();
    }

    @Override
    public FundRepaymentDeductVO repaymentDeductInfo(FundRepaymentDeductQueryInfoDTO dto) {
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(dto.getOrderId());

        return fundRepaymentDeductService.queryInfoByRepayType(dto.getOrderId(), orderInfo.getFundId(), dto.getRepayType(), null, dto.getTerm(), dto.getType());
    }

    /**
     * 资方代扣查询
     *
     * @param orderId 订单id
     * @param term    期数
     */
    @Override
    public FundDeductQueryVO deductQuery(Integer orderId, Integer term, boolean settleFlag) {
        log.info("FundDeductServiceImpl.deductQuery - Deduct query start, orderId: {}, term: {}", orderId, term);

        String lockKey = LOCK_DEDUCT_QUERY_KEY_PREFIX + orderId;
        String lockValue = Convert.toStr(orderId);
        FundDeductQueryVO vo = null;
        try {
            Boolean lockSuccess = redisService.tryLock(lockKey, lockValue, 30);
            if (Objects.equals(Boolean.FALSE, lockSuccess)) {
                log.error("FundDeductServiceImpl.deductQuery - Failed to obtain lock, orderId: {}, fundRepaymentId: {}, settleFlag: {}", orderId, term, settleFlag);
                throw new BusinessException("结果查询中,请稍后再试");
            }
            OrderInfoEntity orderInfo = getOrderInfo(orderId);
            if (ObjUtil.equals(orderInfo.getIsRepurchase(), 1)) {
                vo = tongLianTongService.deductQuery(orderId, term, settleFlag);
            } else {
                vo = handleDeductQuery(orderId, term, settleFlag);
            }
        } catch (BusinessException e) {
            log.error("FundDeductServiceImpl.deductQuery - Business exception during deduct query", e);
            throw e;
        } catch (Exception e) {
            log.error("FundDeductServiceImpl.deductQuery - Error during deduct query", e);
            throw new BusinessException("代扣查询异常");
        } finally {
            redisService.releaseLock(lockKey, lockValue);
        }
        log.info("FundDeductServiceImpl.deductQuery - Deduct query success, orderId: {}, term: {}", orderId, term);
        return vo;
    }


    /**
     * 获取订单信息
     */
    private OrderInfoEntity getOrderInfo(Integer orderId) {
        log.info("FundDeductServiceImpl.getOrderInfo - Fetching order info, orderId: {}", orderId);
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, "订单不存在");
        return orderInfo;
    }

    /**
     * 获取资方枚举
     */
    private FundEnum getFundEnum(Integer fundId) {
        log.info("FundDeductServiceImpl.getFundEnum - Fetching fund enum, fundId: {}", fundId);
        FundEnum fundEnum = FundEnum.getFundEnum(fundId);
        if (fundEnum == null) {
            throw new BusinessException("无效的资方类型");
        }
        return fundEnum;
    }

    /**
     * 处理还款计算
     */
    private FundEarlyRepaymentCalcDTO handleRepayCalc(FundRepayCalcEarlyDTO dto) {
        Integer orderId = dto.getOrderId();
        log.info("FundDeductServiceImpl.handleRepayCalc - Processing repayment calculation, orderId: {}", orderId);

        FundRepayCalcDTO reqDTO = new FundRepayCalcDTO();
        reqDTO.setOrderId(orderId);
        reqDTO.setTerm(dto.getTerm());
        reqDTO.setRepayMode(dto.getRepayMode());

        Result<FundRepayCalcVO> result = approveFeign.repayCalc(reqDTO);
        if (!Result.isSuccess(result)) {
            throw new BusinessException(result.getMsg());
        }

        log.info("FundDeductServiceImpl.handleRepayCalc - repayment calculation success, orderId: {}", orderId);
        return FundDeductConverter.INSTANCE.fundCalc2FundDeductCalc(result.getData());
    }


    /**
     * 处理代扣查询
     */
    private FundDeductQueryVO handleDeductQuery(Integer orderId, Integer term, boolean settleFlag) {
        log.info("FundDeductServiceImpl.handleDeductQuery - Processing  deduction query, orderId: {}, term: {}", orderId, term);

        FundRepayQueryDTO dto = new FundRepayQueryDTO();
        dto.setOrderId(orderId);

        Result<Boolean> result = approveFeign.repayResult(dto);
        if (!Result.isSuccess(result)) {
            throw new BusinessException(result.getMsg());
        }
        FundRepaymentDeductEntity fundRepaymentDeduct = null;
        if (settleFlag) {
            fundRepaymentDeduct = fundRepaymentDeductService.getOne(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                    .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                    .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                    .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                    .orderByDesc(FundRepaymentDeductEntity::getCreateTime)
                    .last("limit 1")
            );
        } else {
            fundRepaymentDeduct = fundRepaymentDeductService.getOne(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                    .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                    .eq(FundRepaymentDeductEntity::getTerm, term)
                    .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                    .orderByDesc(FundRepaymentDeductEntity::getCreateTime)
                    .last("limit 1")
            );
        }

        FundDeductQueryVO queryVO = new FundDeductQueryVO();
        if (ObjUtil.isNull(fundRepaymentDeduct)) {
            queryVO.setStatus(FundDeductRepayStatusEnums.REPAYMENT_NONE);
        } else {
            queryVO.setStatus(fundRepaymentDeduct.getRepayStatus());
            queryVO.setFailReason(fundRepaymentDeduct.getFailReason());
        }
        queryVO.setAmount(fundRepaymentDeduct.getDeductAmount())
                .setDeductTime(fundRepaymentDeduct.getRepayDate());

        log.info("FundDeductServiceImpl.handleDeductQuery  deduction query processed successfully, orderId: {}, term: {}", orderId, term);
        return queryVO;
    }

    @Override
    public FundEarlyRepaymentCalcDTO getSingleDeductData(FundDeductApplyDTO fundDeductApplyDTO) {

        FundRepayCalcEarlyDTO fundRepayCalcEarlyDTO = new FundRepayCalcEarlyDTO();
        fundRepayCalcEarlyDTO.setOrderId(fundDeductApplyDTO.getOrderId());
        fundRepayCalcEarlyDTO.setTerm(fundRepayCalcEarlyDTO.getTerm());
        //默认线下
        fundRepayCalcEarlyDTO.setRepayMode(FundRepayModeEnum.OFFLINE);
        //处理还款计算
        return handleRepayCalc(fundRepayCalcEarlyDTO);
    }

    @Override
    public Boolean isClosedPeriod(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        log.info("FundDeductServiceImpl.isClosedPeriod orderInfoEntity:{}", orderInfoEntity);
        int closedPeriod = 0;
        int term = 0;
        FundProductMappingEntity fundProductMappingEntity = productFundMappingMapper.selectOne(
                new LambdaQueryWrapper<FundProductMappingEntity>()
                        .eq(FundProductMappingEntity::getProductId, orderInfoEntity.getProductId())
                        .eq(FundProductMappingEntity::getFundId, orderInfoEntity.getFundId())
                        .orderByDesc(FundProductMappingEntity::getCreateTime)
                        .last("limit 1")
        );
        if (ObjUtil.isNotNull(fundProductMappingEntity)) {
            log.info("FundDeductServiceImpl.isClosedPeriod fundProductMappingEntity:{}", fundProductMappingEntity);
            closedPeriod = fundProductMappingEntity.getClosedPeriod();
        }
        List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(
                new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                        .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                        .in(FundRepaymentDeductEntity::getBizType,
                                Arrays.asList(
                                        FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                        FundDeductBizTypeEnums.PAYMENT,
                                        FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                        FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                        FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE,
                                        FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION
                                ))
                        .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                        .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                        .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
        );
        log.info("FundDeductServiceImpl.isClosedPeriod list:{}", list);
        if (Objects.equals(orderInfoEntity.getIsRepurchase(), 0)) {
            List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList = fundRepaymentInfoMapper.selectList(
                    new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                            .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                            .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                            .orderByAsc(FundRepaymentInfoEntity::getTerm)
                            .last("limit 1")
            );
            if (CollUtil.isNotEmpty(list)) {
                List<FundRepaymentDeductEntity> list1 = list.stream().filter(item -> Objects.equals(item.getIsRepurchase(), 0)).toList();
                if (CollUtil.isNotEmpty(list1)) {
                    term = list1.get(0).getTerm();
                    log.info("FundDeductServiceImpl.isClosedPeriod isRepurchase0 term:{}", term);
                }
            }
            if (CollUtil.isNotEmpty(fundRepaymentInfoEntityList)) {
                log.info("FundDeductServiceImpl.isClosedPeriod fundRepaymentInfoEntityList:{}", fundRepaymentInfoEntityList);
                term = fundRepaymentInfoEntityList.get(0).getTerm();
                log.info("FundDeductServiceImpl.isClosedPeriod fundRepaymentInfoEntityList.get(0).getTerm():{}", term);
            }
        } else {
            List<RepurchaseRepaymentInfoEntity> repurchaseRepaymentInfoEntityList = repurchaseRepaymentInfoMapper.selectList(
                    new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                            .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                            .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                            .ne(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                            .orderByAsc(RepurchaseRepaymentInfoEntity::getTerm)
                            .last("limit 1")
            );
            if (CollUtil.isNotEmpty(list)) {
                List<FundRepaymentDeductEntity> list1 = list.stream().filter(item -> Objects.equals(item.getIsRepurchase(), 1)).toList();
                if (CollUtil.isNotEmpty(list1)) {
                    term = list1.get(0).getTerm();
                    log.info("FundDeductServiceImpl.isClosedPeriod isRepurchase1 term:{}", term);
                }
            }
            if (CollUtil.isNotEmpty(repurchaseRepaymentInfoEntityList)) {
                log.info("FundDeductServiceImpl.isClosedPeriod repurchaseRepaymentInfoEntityList:{}", repurchaseRepaymentInfoEntityList);
                term = repurchaseRepaymentInfoEntityList.get(0).getTerm();
                log.info("FundDeductServiceImpl.isClosedPeriod repurchaseRepaymentInfoEntityList.get(0).getTerm():{}", term);
            }
        }
        log.info("FundDeductServiceImpl.isClosedPeriod term:{} closedPeriod:{}", term, closedPeriod);
        return term < closedPeriod;
    }
}