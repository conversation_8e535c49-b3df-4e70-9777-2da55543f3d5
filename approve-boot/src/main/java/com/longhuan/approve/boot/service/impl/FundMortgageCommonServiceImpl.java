package com.longhuan.approve.boot.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.approve.api.constants.ContractTypeEnums;
import com.longhuan.approve.boot.enums.FundApplyMortgageTypeEnum;
import com.longhuan.approve.boot.enums.FundApplyNodeStatusEnum;
import com.longhuan.approve.boot.enums.FundMortgageStatusEnum;
import com.longhuan.approve.boot.fund.mortgage.FundMortgageStrategy;
import com.longhuan.approve.boot.fund.mortgage.FundMortgageStrategyFactory;
import com.longhuan.approve.boot.mapper.CustomerMortgageInfoMapper;
import com.longhuan.approve.boot.mapper.FinalFundInfoMapper;
import com.longhuan.approve.boot.mapper.OrderContractMapper;
import com.longhuan.approve.boot.mapper.OrderInfoMapper;
import com.longhuan.approve.boot.pojo.entity.CustomerMortgageInfoEntity;
import com.longhuan.approve.boot.pojo.entity.FinalFundInfoEntity;
import com.longhuan.approve.boot.pojo.entity.OrderContractEntity;
import com.longhuan.approve.boot.pojo.entity.OrderInfoEntity;
import com.longhuan.approve.boot.pojo.dto.FundMortgageCommonDTO;
import com.longhuan.approve.boot.pojo.vo.FundMortgageCommonVO;
import com.longhuan.approve.boot.service.FundMortgageCommonService;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.web.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class FundMortgageCommonServiceImpl implements FundMortgageCommonService {

    private final OrderContractMapper orderContractMapper;

    private final FinalFundInfoMapper finalFundInfoMapper;

    private final OrderInfoMapper orderInfoMapper;

    private final CustomerMortgageInfoMapper customerMortgageInfoMapper;

    private final FundMortgageStrategyFactory fundMortgageStrategyFactory;

    @Override
    public FundMortgageCommonVO createMortgage(FundMortgageCommonDTO fundMortgageCommonDTO) {
        log.info("FundMortgageCommonServiceImpl.createMortgage fundMortgageCommonVO:{}", JSONUtil.toJsonStr(fundMortgageCommonDTO));
        Integer orderId = fundMortgageCommonDTO.getOrderId();

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);

        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, orderInfoEntity.getFundId())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("LIMIT 1"));
        if (ObjUtil.isNull(finalFundInfo)) {
            log.info("FundMortgageCommonServiceImpl.mortgageStatus not found finalFundInfoEntity orderId:{}", orderId);
            throw new BusinessException("未找到该订单");
        }

        if (ObjUtil.isNull(orderInfoEntity)) {
            log.info("FundMortgageCommonServiceImpl.createMortgage not found orderInfoEntity orderId:{}", orderId);
            throw new BusinessException("未找到该订单");
        }
        //查询订单表是否放款
        if (ObjUtil.isNotNull(orderInfoEntity)) {
            if (ObjUtil.isNotNull(orderInfoEntity.getCurrentNode())) {
                if (orderInfoEntity.getCurrentNode() >= 4500) {
                    throw new BusinessException("当前订单已放款");
                }
            }
        }
        // 查询抵押表是否有数据
        CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                .eq(CustomerMortgageInfoEntity::getMortgageType, 0)
                .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                .last("LIMIT 1"));

        if (Objects.isNull(customerMortgageInfoEntity)) {
            customerMortgageInfoEntity = new CustomerMortgageInfoEntity()
                    .setOrderId(orderId)
                    .setMortgageType(0);
            customerMortgageInfoMapper.insert(customerMortgageInfoEntity);
        }
        fundMortgageCommonDTO.setPreId(orderInfoEntity.getPreId());
        fundMortgageCommonDTO.setMortgageName(orderInfoEntity.getCustomerName());
        fundMortgageCommonDTO.setMortgagePhone(orderInfoEntity.getCustomerPhone());
        //进入工厂
        Integer fundId = orderInfoEntity.getFundId();
        FundEnum fundEnum = FundEnum.getFundEnum(fundId);
        String fundCode = "MORTGAGE_" + fundEnum.getFundCode();
        FundMortgageStrategy fundMortgageStrategy = fundMortgageStrategyFactory.getFundMortgageStrategy(fundCode);
        FundMortgageCommonVO  fundMortgageCommonVO =  fundMortgageStrategy.mortgageCreate(fundMortgageCommonDTO);

        finalFundInfo.setFundNodeStatus(FundApplyNodeStatusEnum.SUCCESS);
        finalFundInfo.setMortgageType(FundApplyMortgageTypeEnum.ONLINE);
        finalFundInfoMapper.updateById(finalFundInfo);

        orderInfoEntity.setMortgageState(1);
        orderInfoMapper.updateById(orderInfoEntity);

        return fundMortgageCommonVO;
    }

    @Override
    public FundMortgageCommonVO mortgageStateQuery(FundMortgageCommonDTO fundMortgageCommonDTO) {
        Integer orderId = fundMortgageCommonDTO.getOrderId();

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);

        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, orderInfoEntity.getFundId())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("LIMIT 1"));
        if (ObjUtil.isNull(finalFundInfo)) {
            log.info("FundMortgageCommonServiceImpl.mortgageStateQuery not found finalFundInfoEntity orderId:{}", orderId);
            throw new BusinessException("未找到该订单");
        }

        if (ObjUtil.isNull(orderInfoEntity)) {
            log.info("FundMortgageCommonServiceImpl.mortgageStateQuery not found orderInfoEntity orderId:{}", orderId);
            throw new BusinessException("未找到该订单");
        }
        fundMortgageCommonDTO.setPreId(orderInfoEntity.getPreId());
        Integer fundId = orderInfoEntity.getFundId();
        FundEnum fundEnum = FundEnum.getFundEnum(fundId);
        String fundCode = "MORTGAGE_" + fundEnum.getFundCode();
        FundMortgageStrategy fundMortgageStrategy = fundMortgageStrategyFactory.getFundMortgageStrategy(fundCode);

        FundMortgageCommonVO fundMortgageCommonVO = fundMortgageStrategy.mortgageQuery(fundMortgageCommonDTO);

        return fundMortgageCommonVO;
    }
}