package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.RiskPromptDTO;
import com.longhuan.order.pojo.entity.RiskPromptEntity;
import com.longhuan.order.pojo.vo.RiskPromptVO;
import com.longhuan.order.service.RiskPromptService;
import com.longhuan.user.pojo.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 风控风险提示
 *
 * <AUTHOR> css
 * @date : 2025-07-30
 */
@Api(tags = "风控风险提示功能接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/riskPrompt")
public class RiskPromptController {
    private final RiskPromptService riskPromptService;

    /**
     * 查询列表
     *
     * @return 实例对象
     */
    @ApiOperation(value = "分页查询风控风险提示")
    @PostMapping("/page/RiskPromptList")
    public Result<Page<RiskPromptVO>> selectRiskPromptList(@RequestBody RiskPromptDTO dto) {
        return Result.success(riskPromptService.selectRiskPromptList(dto));
    }

    /**
     * 通过风控风险提示id获取详情
     *
     * @param id 风控风险提示id
     * @return {@link Result< RiskPromptEntity >}
     */
    @ApiModelProperty("通过风控风险提示id获取详情")
    @GetMapping("/queryInfoById/{id}")
    public Result<RiskPromptEntity> queryInfo(@PathVariable("id") Integer id) {
        return Result.success(riskPromptService.queryById(id));
    }

    /**
     * 新增风控风险提示
     *
     * @param dto 风控风险提示信息
     */
    @PostMapping("/insertRiskPromptInfo")
    public Result<Boolean> insertRiskPromptInfo(@RequestBody @Validated RiskPromptDTO dto) {
        return Result.success(riskPromptService.insertRiskPromptInfo(dto));
    }

    /**
     * 编辑风控风险提示
     *
     * @param dto 风控风险提示信息
     */
    @PostMapping("/updateRiskPromptInfo")
    public Result<Boolean> updateRiskPromptInfo(@RequestBody @Validated RiskPromptDTO dto) {
        return Result.success(riskPromptService.updateRiskPromptInfo(dto));
    }

    /**
     * 删除数据风控风险提示
     *
     * @param id 风控风险提示id
     */
    @ApiOperation("删除数据")
    @GetMapping({"/delRiskPrompt/{id}"})
    public Result<Boolean> delRiskPrompt(@PathVariable("id") Integer id) {
        return Result.success(riskPromptService.delRiskPrompt(id));
    }


    /**
     * 根据门店id获取人员
     *
     * @param dept 门店id
     * @param type 人员类型
     */
    @ApiOperation("根据门店id获取人员")
    @PostMapping({"/getMangerByDeptId"})
    public Result<List<UserInfoVO>> getMangerByDeptId(@RequestParam("dept") Integer dept, @RequestParam("type") Integer type) {
        return Result.success(riskPromptService.getMangerByDeptId(dept, type));
    }

}