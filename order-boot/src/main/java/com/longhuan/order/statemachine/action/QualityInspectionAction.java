package com.longhuan.order.statemachine.action;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.longhuan.common.core.enums.RoleEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.entity.OrderApproveDistributeEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.entity.RiskPromptEntity;
import com.longhuan.order.pojo.entity.RiskUserStateEntity;
import com.longhuan.order.service.OrderApproveDistributeService;
import com.longhuan.order.service.OrderService;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.user.enums.TodoInfoEnums;
import com.longhuan.user.pojo.dto.TodoInfoMessageDTO;
import com.longhuan.user.pojo.vo.UserDetailInfoVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.StateMachineEventResult;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Random;

/**
 * 客服质检
 * <p>
 * 状态转移动作，在转移进行的时候触发的动作
 */
@Slf4j
@Component
public class QualityInspectionAction extends OrderNodeAction implements Action<States, Events> {

    private final OrderService orderService;
    private final OrderApproveDistributeMapper orderApproveDistributeMapper;
    private final RiskStateMapper riskStateMapper;
    private final UserFeign userFeign;
    private final RedisService redisService;
    private final RiskPromptMapper riskPromptMapper;
    public QualityInspectionAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper, OrderService orderService, OrderApproveDistributeMapper orderApproveDistributeMapper, RiskStateMapper riskStateMapper, UserFeign userFeign, RedisService redisService,RiskPromptMapper riskPromptMapper) {
        super(orderInfoMapper, orderNodeRecordMapper);
        this.orderService = orderService;
        this.orderApproveDistributeMapper = orderApproveDistributeMapper;
        this.riskStateMapper = riskStateMapper;
        this.userFeign = userFeign;
        this.redisService = redisService;
        this.riskPromptMapper = riskPromptMapper;
    }


    @Override
    public void execute(StateContext<States, Events> context) {
        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();
        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);
        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event, currentNode, nextNode);

        // 1. 修改订单表状态
        if (Boolean.TRUE.equals(target.getId().getValid())) {
            saveOrderNode(orderId, nextNode);
        }

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        if (Objects.equals(nextNode, States.RISK_FIRST_APPROVE_ASSIGN.getNode())){
            try {
                RiskPromptEntity riskPromptEntity = riskPromptMapper.selectOne(
                        new LambdaQueryWrapper<RiskPromptEntity>()
                                .eq(RiskPromptEntity::getDeleteFlag, 0)
                                .eq(RiskPromptEntity::getEnable, 0)
                                .eq(RiskPromptEntity::getType, 1)
                                .eq(RiskPromptEntity::getDeptId, orderInfoEntity.getDeptId())
                                .orderByDesc(RiskPromptEntity::getCreateTime)
                                .last("limit 1")
                );
                if (ObjUtil.isNotNull(riskPromptEntity) && riskPromptEntity.getMessage().contains("综合资质不足拒绝")){
                    log.info("CustomerAppointmentAction order {} try CustomerAppointmentFinish", orderId);
                    context.getStateMachine().sendEvent(Mono.just(MessageBuilder.withPayload(Events.AGREES).
                                    setHeader(StateConstants.ORDER_ID, orderId)
                                    .setHeader(StateConstants.REMARK, "自动通过")
                                    .setHeader(StateConstants.USER_ID, 1).build()))
                            .subscribe(result -> {
                                log.info("CustomerAppointmentAction order {} try CustomerAppointmentFinish result:{}", orderId,
                                        result.getResultType());
                                // 1. 修改订单表状态
                                if (Boolean.TRUE.equals(target.getId().getValid())) {
                                    saveOrderNode(orderId, target.getId().getNode());
                                }
                                // 2. 保存状态变更记录
                                saveOrderNodeRecord(context.getEvent(), orderId, source.getId().getNode(), target.getId().getNode(), context.getMessage().getHeaders());
                                // 在第一个事件完成后发送第二个事件
                                if (Objects.equals(result.getResultType(),StateMachineEventResult.ResultType.ACCEPTED)) {
                                    context.getStateMachine().sendEvent(Mono.just(MessageBuilder.withPayload(Events.REJECT)
                                                    .setHeader(StateConstants.ORDER_ID, orderId)
                                                    .setHeader(StateConstants.REMARK, "综合资质不足拒绝")
                                                    .setHeader(StateConstants.USER_ID, 1).build()))
                                            .subscribe(result2 -> {
                                                log.info("QualityInspectionAction order {} try QualityInspectionAction result2:{}",orderId, result2.getResultType());
                                                // 1. 修改订单表状态
                                                if (Boolean.TRUE.equals(target.getId().getValid())) {
                                                    saveOrderNode(orderId, target.getId().getNode());
                                                }
                                                // 2. 保存状态变更记录
                                                saveOrderNodeRecord(context.getEvent(), orderId, source.getId().getNode(), target.getId().getNode(), context.getMessage().getHeaders());
                                            });
                                }
                            });
                    return;
                }
            } catch (Exception e) {
                log.info("riskPromptMapper.REJECT error:{}",e.getMessage());
            }
        }
        try {
            // 3.送入待分配任务池
            Long count = orderApproveDistributeMapper.selectCount(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                    .eq(OrderApproveDistributeEntity::getOrderNumber, orderInfoEntity.getOrderNumber())
                    .eq(OrderApproveDistributeEntity::getOrderId, orderId)
                    .eq(OrderApproveDistributeEntity::getNode, nextNode)
                    .in(OrderApproveDistributeEntity::getState, 0, 1, 2, 5));
            if (count==0) {
                OrderApproveDistributeEntity orderApproveDistributeEntity = new OrderApproveDistributeEntity()
                        .setOrderNumber(orderInfoEntity.getOrderNumber())
                        .setOrderId(orderId)
                        .setNode(nextNode)
                        .setNodeName(States.RISK_FIRST_APPROVE.getDesc())
                        .setSource(0)
                        .setStoreId(orderInfoEntity.getDeptId())
                        .setTitle(orderInfoEntity.getCustomerName() + "-" + orderInfoEntity.getVehicleNumber() + "-" + orderInfoEntity.getStoreName())
                        .setState(0);
                orderApproveDistributeEntity.setCustomerName(orderInfoEntity.getCustomerName())
                        .setCustomerPhone(orderInfoEntity.getCustomerPhone())
                        .setManagerId(orderInfoEntity.getManagerId())
                        .setStoreName(orderInfoEntity.getStoreName())
                        .setRegionName(orderInfoEntity.getRegionName())
                        .setVehicleNumber(orderInfoEntity.getVehicleNumber())
                        .setStepDispose(currentNode <= nextNode ? 0 : 1);
                orderApproveDistributeMapper.insert(orderApproveDistributeEntity);
//            OrderApproveDistributeService orderApproveDistributeService= SpringContentUtils.getBean(OrderApproveDistributeService.class);
//            orderApproveDistributeService.assignRiskFirstApproval(orderApproveDistributeEntity);
            }
        } catch (Exception e){
            log.error("send todo info error", e);
        }

    }

    /**
     * 修改订单表状态
     *
     * @param orderId
     * @param nextNode
     */
    @Override
    protected void saveOrderNode(Integer orderId, Integer nextNode) {
        log.info("order {} update  current_node {}", orderId, nextNode);
        OrderInfoEntity updateEntity = new OrderInfoEntity();

        updateEntity.setId(orderId);
        updateEntity.setCurrentNode(nextNode);
        updateEntity.setLastNodeFinishTime(LocalDateTime.now());
        updateEntity.setQualityTestCommitTime(LocalDateTimeUtil.now());

        orderInfoMapper.updateById(updateEntity);
    }


}