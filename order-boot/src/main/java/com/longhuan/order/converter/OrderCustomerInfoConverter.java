package com.longhuan.order.converter;

import cn.hutool.core.date.DatePattern;
import com.longhuan.order.pojo.dto.OrderCustomerInfoDTO;
import com.longhuan.order.pojo.entity.OrderContactPersonEntity;
import com.longhuan.order.pojo.entity.OrderCustomerInfoEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.entity.PreOcrIdentityCardEntity;
import com.longhuan.order.pojo.vo.CustomerInfoVO;
import com.longhuan.order.pojo.vo.OrderCustomerInfoVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/25
 */
@Mapper(componentModel = "spring")
public interface OrderCustomerInfoConverter {

    @Mapping(source = "orderInfoEntity.id", target = "orderId")
    @Mapping(source = "preOcrIdentityCardEntity.updateName", target = "name")
    @Mapping(source = "preOcrIdentityCardEntity.phone", target = "phone")
    @Mapping(source = "preOcrIdentityCardEntity.updateIdNumber", target = "idNumber")
    @Mapping(source = "preOcrIdentityCardEntity.updateValidityStart", target = "validityStartDate")
    @Mapping(source = "preOcrIdentityCardEntity.updateValidityEnd", target = "validityEnd")
    @Mapping(source = "preOcrIdentityCardEntity.updateIssuingAuthority", target = "issuingAuthority")
    @Mapping(source = "preOcrIdentityCardEntity.updateAddress", target = "idCardDetailedAddress")
    @Mapping(target = "gender", ignore = true)
    @Mapping(target = "nation", ignore = true)
    @Mapping(target = "idCardAddress", ignore = true)
    OrderCustomerInfoVo ocrIdentityCardEntity2OrderCustomerInfoVo(PreOcrIdentityCardEntity preOcrIdentityCardEntity, OrderInfoEntity orderInfoEntity);

    @Mapping(source = "orderContactPersonEntityList", target = "orderContactPersonVoList")
    OrderCustomerInfoVo entity2vo(OrderCustomerInfoEntity orderCustomerInfoEntity, List<OrderContactPersonEntity> orderContactPersonEntityList);

    OrderCustomerInfoEntity dto2Entity(OrderCustomerInfoDTO orderCustomerInfoDTO);

    @Mapping(source = "orderContactPersonEntityList", target = "orderContactPersonVoList")
    @Mapping(target = "birthDate", dateFormat = DatePattern.NORM_DATE_PATTERN)
    CustomerInfoVO orderEntity2vo(OrderCustomerInfoEntity orderCustomerInfoEntity, List<OrderContactPersonEntity> orderContactPersonEntityList);

}
