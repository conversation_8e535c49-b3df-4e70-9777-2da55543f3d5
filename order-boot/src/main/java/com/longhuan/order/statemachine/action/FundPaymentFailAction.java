package com.longhuan.order.statemachine.action;

import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.mapper.OrderNodeRecordMapper;
import com.longhuan.order.pojo.dto.ApprovalSubmitDTO;
import com.longhuan.order.service.impl.OrderSendMessageImpl;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.StateMachineEventResult;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Objects;

/**
 * 状态转移动作，在转移进行的时候触发的动作
 */
@Slf4j
@Component
public class FundPaymentFailAction extends OrderNodeAction implements Action<States, Events> {

    private final OrderSendMessageImpl orderSendMessage;


    public FundPaymentFailAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper, OrderSendMessageImpl orderSendMessage) {
        super(orderInfoMapper, orderNodeRecordMapper);
        this.orderSendMessage = orderSendMessage;
    }

    @Override
    public void execute(StateContext<States, Events> context) {


        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();

        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);


        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event, currentNode, nextNode);

        // 1. 修改订单表状态
        if (Boolean.TRUE.equals(target.getId().getValid())) {
            saveOrderNode(orderId, nextNode);
        }

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);

        // 3. 发送消息
        Integer currentEventCode = context.getEvent().getCode();

        orderSendMessage.sendMessage(orderId, currentNode, currentEventCode);


    }


}