package com.longhuan.approve.boot.pojo.vo;


import com.longhuan.approve.boot.enums.yingfeng.YingFengDictEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 *盈峰线上抵押查询实请求体类
 */
@Data
@Accessors(chain = true)
public class OnlineMortgageGetOnlineMortgageInfoVO {

    /**
     *合同状态（0：待车主签署;20：待代理人签署;30：待金融机构签署;40：签署完成）
     */
    private YingFengDictEnum.ZrContractStatus zrContractStatus;

    /**
     *交科所状态(O：未受理;A：预受理成功;B：验证成功;D：受理成功;S：已归档;F：退办（验证失败）)
     */
    private YingFengDictEnum.ZrChannelStatus zrChannelStatus;

    /**
     *交科所失败原因
     */
    private String jksFailedReason;

    /**
     *代理人名称
     */
    private String agentName;

    /**
     *代理人手机号
     */
    private String agentPhoneNo;

    /**
     *代理人收件地址
     */
    private String agentRecipientAddress;

    /**
     *代理人邮编
     */
    private String agentPostalCode;

    /**
     *办抵绿本照片
     */
    private List<String> imgList;



}
