package com.longhuan.order.settleCalculation.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import com.longhuan.common.core.enums.LoanSettlementEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.enums.CaseApproveNodeEnums;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.LHSettleCalculationDTO;
import com.longhuan.order.pojo.dto.OrderSettleDTO;
import com.longhuan.order.pojo.dto.SettleCalculationContext;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.SettleCalculationVO;
import com.longhuan.order.settleCalculation.service.SettleCalculationService;
import com.longhuan.order.settleCalculation.service.impl.strategy.SettleCalculationRouter;
import com.longhuan.order.settleCalculation.service.impl.strategy.impl.enums.SettleType;
import com.longhuan.order.settleCalculation.vo.SettleCalculationListVO;
import com.longhuan.user.pojo.vo.UserInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.longhuan.common.core.enums.LoanSettlementEnum.values;

@Service
@RequiredArgsConstructor
@Slf4j
public class SettleCalculationServiceImpl implements SettleCalculationService {
    private final SettleCalculationRouter settleCalculationRouter;
    private final OrderInfoMapper orderInfoMapper;
    private final FundProductMappingMapper fundProductMappingMapper;
    private final ProductInfoMapper productInfoMapper;
    private final OrderCustomerInfoMapper orderCustomerInfoMapper;
    private final UserFeign userFeign;
    private final CaseInfoEntityMapper caseInfoEntityMapper;
    private final OrderSettleAmountRecordMapper orderSettleAmountRecordMapper;


    @Override
    public SettleCalculationVO calculate(SettleCalculationContext params) {
        return settleCalculationRouter.route(params).calculate(params);
    }

    @Override
    public SettleCalculationVO calculateByOrderId(Integer orderId, Integer closedPeriodRepayMode) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        if (orderInfoEntity != null) {
            SettleCalculationContext settleCalculationContext = buildLHContext(orderInfoEntity, closedPeriodRepayMode, null);
            log.info("orderId:{} settleCalculationContext:{}",orderId,settleCalculationContext);
            SettleCalculationVO calculate = calculate(settleCalculationContext);
            calculate.setSettleCalculationContext(settleCalculationContext);
            log.info("orderId:{} calculate:{}",orderId,calculate);
            OrderSettleAmountRecordEntity entity = new OrderSettleAmountRecordEntity();
            entity.setOrderId(orderId);
            entity.setOrderSource(1);
            entity.setOrderNumber(orderInfoEntity.getOrderNumber());
                entity.setTrialSettlementAmount(calculate.getSettlementAmount());
                entity.setActualSettlementAmount(calculate.getSettlementAmount());
                entity.setSettlementMethod(calculate.getSettleMode());
                entity.setUsageDays(calculate.getSettleCalculationContext().getUseDays());
                entity.setSettleModeType(calculate.getSettleModeType());
                entity.setInstalmentsRepaid(calculate.getSettleCalculationContext().getPaidPeriods());
            orderSettleAmountRecordMapper.insert(entity);
            return calculate;
        }
        return new SettleCalculationVO();
    }

    /**
     * @param orderInfoEntity
     * @param closedPeriodRepayMode
     * @param payDate  结清结算器用
     * @return
     */
    private SettleCalculationContext buildLHContext(OrderInfoEntity orderInfoEntity,
                                                    Integer closedPeriodRepayMode,
                                                    LocalDate payDate) {
        SettleCalculationContext settleCalculationContext = new SettleCalculationContext();
        List<FundProductMappingEntity> fundProductMappingEntityList = fundProductMappingMapper.selectList(
                new LambdaQueryWrapper<FundProductMappingEntity>()
                        .eq(FundProductMappingEntity::getFundId, orderInfoEntity.getFundId())
                        .eq(FundProductMappingEntity::getProductId, orderInfoEntity.getProductId())
//                        .eq(FundProductMappingEntity::getStatus, 0)
//                        .eq(FundProductMappingEntity::getDeleteFlag, 0)
                        .orderByAsc(FundProductMappingEntity::getCreateTime)
        );
        if (CollUtil.isEmpty(fundProductMappingEntityList)) {
            throw new BusinessException("获取资方产品失败");
        }
        ProductInfoEntity productInfoEntity = productInfoMapper.selectById(orderInfoEntity.getProductId());
        OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectById(orderInfoEntity.getCustomerId());
        settleCalculationContext.setClosedPeriod(fundProductMappingEntityList.get(0).getClosedPeriod());
        settleCalculationContext.setProductRate(productInfoEntity.getMonthlyRate());
        settleCalculationContext.setFundName(orderInfoEntity.getFundName());
        settleCalculationContext.setCustomerRating(orderCustomerInfoEntity.getCustomerLevel());
        settleCalculationContext.setIRRInterest(productInfoEntity.getIrr());
        Long outsourcing = caseInfoEntityMapper.selectCount(new LambdaQueryWrapper<CaseInfoEntity>()
                .eq(CaseInfoEntity::getOrderId,orderInfoEntity.getId())
                .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS)
                .eq(CaseInfoEntity::getDeleteFlag, 0));
        settleCalculationContext.setOutsourcing(outsourcing>0);
        settleCalculationContext.setRedeem(orderInfoEntity.getIsRepurchase() == 1);
        settleCalculationContext.setClosedPeriodRepayMode(closedPeriodRepayMode);
        SettleType type = SettleType.getType(settleCalculationContext);
        //构建LH上下文
        settleCalculationRouter.route(settleCalculationContext).buildLHContext(settleCalculationContext, orderInfoEntity, payDate);
        return settleCalculationContext;
    }

    /**
     * 结清计算器列表
     *
     * @param orderSettleDTO
     * @return
     */
    @Override
    public Page<SettleCalculationListVO> list(OrderSettleDTO orderSettleDTO) {

        MPJLambdaWrapper<OrderInfoEntity> queryWrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getOrderNumber,
                        OrderInfoEntity::getCustomerName,
                        OrderInfoEntity::getVehicleNumber,
                        OrderInfoEntity::getFundName,
                        OrderInfoEntity::getManagerId,
                        OrderInfoEntity::getStoreName,
                        OrderInfoEntity::getRegionName,
                        OrderInfoEntity::getProductName,
                        OrderInfoEntity::getApplyAmount,
                        OrderInfoEntity::getRepayMethod,
                        OrderInfoEntity::getIsOverdue,
                        OrderInfoEntity::getPlanState)
                .select("max(t1.term) as nowTerm")
                .selectAs(OrderInfoEntity::getId, SettleCalculationListVO::getOrderId)
                .selectAs(OrderInfoEntity::getTerm, SettleCalculationListVO::getTotalTerm)
                .leftJoin(FundRepaymentInfoEntity.class, FundRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                .eq(StringUtils.isNotBlank(orderSettleDTO.getOrderNumber()), OrderInfoEntity::getOrderNumber, orderSettleDTO.getOrderNumber())
                //已还
                .eq(FundRepaymentInfoEntity::getRepaymentStatus, 3)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                .eq(StringUtils.isNotBlank(orderSettleDTO.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, orderSettleDTO.getVehicleNumber())
                .and(v ->
                        v.eq(OrderInfoEntity::getPlanState, 0).or().eq(OrderInfoEntity::getIsOverdue, 1))
                .groupBy(OrderInfoEntity::getId,
                        OrderInfoEntity::getCustomerName,
                        OrderInfoEntity::getVehicleNumber,
                        OrderInfoEntity::getFundName,
                        OrderInfoEntity::getManagerId,
                        OrderInfoEntity::getStoreName,
                        OrderInfoEntity::getRegionName,
                        OrderInfoEntity::getProductName,
                        OrderInfoEntity::getApplyAmount,
                        OrderInfoEntity::getRepayMethod,
                        OrderInfoEntity::getIsOverdue,
                        OrderInfoEntity::getPlanState,
                        OrderInfoEntity::getTerm)
                .groupBy(FundRepaymentInfoEntity::getOrderId)
                .orderByDesc(OrderInfoEntity::getOrderNumber);

        Page<SettleCalculationListVO> result = orderInfoMapper.selectJoinPage(new Page<>(orderSettleDTO.getPageNum(), orderSettleDTO.getPageSize()),
                SettleCalculationListVO.class,
                queryWrapper);


        if (CollUtil.isNotEmpty(result.getRecords())) {

            List<SettleCalculationListVO> records = result.getRecords();
            List<Integer> managerIdList = records.stream().map(SettleCalculationListVO::getManagerId).toList();
            //查询业务员信息
            Result<List<UserInfoVO>> userInfoVOResult = userFeign.searchUserNameByUserIds(managerIdList);
            if (!Result.isSuccess(userInfoVOResult)) {
                throw new BusinessException("查询业务员信息失败");
            }
            //以<managerId,UserInfoVO>为格式的map
            Map<Integer, UserInfoVO> userInfoVOMap = userInfoVOResult.getData().stream()
                    .collect(Collectors.toMap(UserInfoVO::getUserId, v -> v));

            records.forEach(v -> {
                UserInfoVO userInfoVO = userInfoVOMap.get(v.getManagerId());
                if (null != userInfoVO) {
                    v.setManagerName(userInfoVO.getName());
                }

                if (1 == v.getIsOverdue()) {
                    v.setOrderStatus("逾期");
                } else {
                    v.setOrderStatus("还款中");
                }

                v.setTerm(v.getNowTerm() + "/" + v.getTotalTerm());
            });
        }
        return result;

    }

    /**
     * 结清结算器-计算结清
     *
     * @param dto
     * @return
     */
    @Override
        public List<SettleCalculationVO> compute(LHSettleCalculationDTO dto) {

        if (null == dto.getOrderId() || null == dto.getPayDate()) {
            throw new BusinessException("参数错误。");
        }

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
        if (null == orderInfoEntity) {
            throw new BusinessException("订单信息为空。");
        }

        List<SettleCalculationVO> resultList = Lists.newArrayList();
        for (LoanSettlementEnum loanSettlementEnum : values()) {
            Integer closedPeriodRepayMode = null;
            switch (loanSettlementEnum) {
                case CLOSED_PERIOD_OUTSIDE -> closedPeriodRepayMode = 0;
                case CLOSED_PERIOD_INSIDE -> closedPeriodRepayMode = 1;
                case CLOSED_PERIOD_PENALTY_INSIDE -> closedPeriodRepayMode = 2;
            }

            SettleCalculationContext settleCalculationContext = buildLHContext(orderInfoEntity, closedPeriodRepayMode, dto.getPayDate());
            SettleCalculationVO calculate = calculate(settleCalculationContext);
            calculate.setSettleCalculationContext(settleCalculationContext);
            resultList.add(calculate);
        }
        return resultList;
    }
}
