<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.longhuan.order.mapper.OrderContactPersonMapper">

    <resultMap id="contactResultMap" type="com.longhuan.order.pojo.vo.OrderContactVO">
        <result property="name" column="name"/>
        <result property="relationship" column="relationship"/>
        <result property="mobile" column="mobile"/>
        <result property="idcard" column="idcard"/>
        <result property="job_name" column="job_name"/>
        <result property="unit_post" column="unit_post"/>
        <result property="sex" column="sex"/>
        <result property="education" column="education"/>
        <result property="address" column="address"/>
        <result property="birth_address" column="birth_address"/>
        <result property="job_mobile" column="job_mobile"/>
        <result property="job_address" column="job_address"/>
    </resultMap>
    <select id="getContactselectJoinList" resultMap="contactResultMap">
        SELECT t.name AS name,
               case
                   when t.relation = 1 then '配偶'
                   when t.relation = 2 then '父亲'
                   when t.relation = 3 then '子女'
                   when t.relation = 4 then '其他直系亲属'
                   when t.relation = 5 then '朋友'
                   when t.relation = 6 then '同事'
                   when t.relation = 7 then '母亲'
                   else '未知'
                   end              as relationship,
               t.phone              AS mobile,
               t.id_number          AS idcard,
               t.enterprise_name    AS job_name,
               ' '                  AS unit_post,
               ' '                  AS sex,
               ' '                  AS education,
               ' '                  AS address,
               ' '                  AS birth_address,
               ' '                  AS job_mobile,
               t.enterprise_address AS job_address
        FROM LH_ORDER_CONTACT_PERSON t
        WHERE t.order_id = #{orderId} and t.delete_flag =0
        order by t.index
    </select>
</mapper>