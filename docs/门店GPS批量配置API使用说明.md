# 门店GPS批量配置API使用说明

## API概述

**接口地址**: `POST /api/v1/gps/pay/setStoreGpsConfig`

**功能描述**: 批量设置门店GPS收费策略配置，支持同时配置多个门店的免费GPS服务和分期付款策略。

## 请求参数

### StoreGpsConfigDTO

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| freeStoreIds | List<Integer> | 否 | 支持免费GPS服务的门店ID列表 |
| installmentStoreIds | List<Integer> | 否 | 支持分期付款的门店ID列表 |

**注意**: 至少需要提供一个非空列表。

## 业务逻辑

1. **免费GPS服务门店** (`freeStoreIds`):
   - 设置 `isFree = 1`
   - 设置 `isInstallment = 0`

2. **分期付款门店** (`installmentStoreIds`):
   - 设置 `isFree = 0`
   - 设置 `isInstallment = 1`

3. **冲突处理**:
   - 如果同一个门店ID同时出现在两个列表中，该门店将同时支持免费和分期服务
   - 最终配置：`isFree = 1, isInstallment = 1`
   - 系统会记录信息日志但不会抛出异常

4. **数据完整性**:
   - 每个门店在数据库中只保留一条有效记录
   - 存在记录则更新，不存在则插入

## 请求示例

### 示例1: 只配置免费门店
```json
{
    "freeStoreIds": [101, 102, 103],
    "installmentStoreIds": null
}
```

### 示例2: 只配置分期门店
```json
{
    "freeStoreIds": null,
    "installmentStoreIds": [201, 202, 203]
}
```

### 示例3: 同时配置两种类型
```json
{
    "freeStoreIds": [101, 102, 103],
    "installmentStoreIds": [201, 202, 203]
}
```

### 示例4: 同时支持两种服务的配置
```json
{
    "freeStoreIds": [101, 102, 103],
    "installmentStoreIds": [102, 201, 202]
}
```
**结果**:
- 门店101, 103: 仅支持免费服务 (`isFree=1, isInstallment=0`)
- 门店102: 同时支持免费和分期服务 (`isFree=1, isInstallment=1`)
- 门店201, 202: 仅支持分期服务 (`isFree=0, isInstallment=1`)

## 响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "操作成功",
    "data": true
}
```

### 失败响应
```json
{
    "code": 500,
    "message": "批量设置门店GPS配置失败: 具体错误信息",
    "data": false
}
```

## 错误处理

1. **参数验证错误**:
   - 两个列表都为空: "至少需要配置一种类型的门店"

2. **数据库操作错误**:
   - 单个门店配置失败会抛出具体的错误信息
   - 整个事务会回滚，保证数据一致性

3. **空值处理**:
   - 列表中的null值会被跳过，不会影响其他门店的配置

## 事务特性

- 整个批量操作在一个事务中执行
- 任何一个门店配置失败都会导致整个事务回滚
- 保证数据的原子性和一致性

## 日志记录

系统会记录以下关键信息：
- 批量操作的开始和结束
- 每个门店的配置结果
- 冲突门店的警告信息
- 异常情况的详细错误日志

## 性能考虑

- 建议单次批量操作的门店数量不超过1000个
- 大批量操作建议分批进行，避免长时间锁定数据库资源
