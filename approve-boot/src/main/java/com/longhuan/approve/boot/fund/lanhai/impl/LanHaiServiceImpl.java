package com.longhuan.approve.boot.fund.lanhai.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import com.longhuan.approve.api.constants.ContractTypeEnums;
import com.longhuan.approve.api.constants.LanHaiMortgageEnums;
import com.longhuan.approve.api.pojo.dto.RegularlyQueryPostLoanSupplementsDTO;
import com.longhuan.approve.api.pojo.dto.lanhai.LanHaiContractSignFileDTO;
import com.longhuan.approve.api.pojo.dto.lanhai.LanHaiCreditLimitInfoDTO;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiAfterReplenishVerifyResponse;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiContractSignApplyResponse;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiRepayCalcResponse;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiTrialRepayResponse;
import com.longhuan.approve.boot.client.LanHaiClient;
import com.longhuan.approve.boot.client.ThunderAtmClient;
import com.longhuan.approve.boot.config.LanHaiConfig;
import com.longhuan.approve.boot.constants.FundConstant;
import com.longhuan.approve.boot.constants.LanHaiApproveConstants;
import com.longhuan.approve.boot.enums.FundPaymentStatusEnum;
import com.longhuan.approve.boot.enums.*;
import com.longhuan.approve.boot.enums.fumin.MortgageEnums;
import com.longhuan.approve.boot.enums.lanhai.LanHaiApiEnums;
import com.longhuan.approve.boot.enums.lanhai.LanHaiDictEnum;
import com.longhuan.approve.boot.feign.*;
import com.longhuan.approve.boot.fund.finall.FinalFundInfoService;
import com.longhuan.approve.boot.fund.lanhai.LanHaiService;
import com.longhuan.approve.boot.mapper.*;
import com.longhuan.approve.boot.pojo.dto.*;
import com.longhuan.approve.boot.pojo.dto.lanhai.LanHaiResult;
import com.longhuan.approve.boot.pojo.dto.lanhai.request.*;
import com.longhuan.approve.boot.pojo.dto.lanhai.response.*;
import com.longhuan.approve.boot.pojo.dto.thunder.AtmResponse;
import com.longhuan.approve.boot.pojo.dto.thunder.AtmStatus;
import com.longhuan.approve.boot.pojo.entity.*;
import com.longhuan.approve.boot.pojo.vo.FundProductMappingVO;
import com.longhuan.approve.boot.service.*;
import com.longhuan.approve.boot.utils.lanhaiyh.RandomShandongIPUtils;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.GlobalConstants;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.core.enums.dict.DictApplyPurposeEnum;
import com.longhuan.common.core.enums.dict.DictContactRelation;
import com.longhuan.common.core.enums.dict.DictMaritalStatus;
import com.longhuan.common.core.enums.dict.DictOccupationEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.constants.SwitchConstants;
import com.longhuan.common.redis.pojo.SwitchVO;
import com.longhuan.common.redis.util.DictUtils;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.resource.pojo.dto.FundResourceDTO;
import com.longhuan.resource.pojo.dto.FundResourceResultDTO;
import com.longhuan.resource.pojo.dto.UploadVerticalTxtDTO;
import com.longhuan.resource.pojo.vo.FileVO;
import com.longhuan.resource.pojo.vo.LanHaiLprVo;
import com.longhuan.risk.pojo.vo.Car300DataVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class LanHaiServiceImpl implements LanHaiService {

    private final FundApproveMapper fundApproveMapper;
    private final LanHaiClient lanHaiClient;
    private final FundBaseInfoService fundBaseInfoService;
    private final ResourceFeign resourceFeign;
    private final LanHaiConfig lanHaiConfig;
    private final PreFundInfoMapper preFundInfoMapper;
    private final OrderFeign orderFeign;
    private final OrderInfoMapper orderInfoMapper;
    private final UserFeign userFeign;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final ObjectMapper objectMapper;
    private final FundApiDictMapper fundApiDictMapper;
    private final FundApiDictService fundApiDictService;
    private final OrderArrivedMapper orderArrivedMapper;
    private final CustomerMortgageInfoMapper customerMortgageInfoMapper;
    private final DictUtils dictUtils;
    private final OrderContractMapper orderContractMapper;
    private final FileTemplateInfoMapper fileTemplateInfoMapper;
    private final FinalFundInfoService finalFundInfoService;
    private final FundUndoMortgageInfoMapper fundUndoMortgageInfoMapper;
    private final ProductRongdanMapper productRongdanMapper;
    private final FundRepaymentInfoService fundRepaymentInfoService;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final FundPushResourceConfigMapper fundPushResourceConfigMapper;
    private final OrderArrivedAddressMapper orderArrivedAddressMapper;
    private final ArrivedDataMapper arrivedDataMapper;
    private final Car300Feign car300Feign;
    private final ProductFundMappingMapper productFundMappingMapper;
    private final PreApprovalApplyInfoMapper preApprovalApplyInfoMapper;
    private final FundSignInfoMapper fundSignInfoMapper;
    private final FileResourceMapper fileResourceMapper;
    private final EnvUtil envUtil;
    private final ProductInfoMapper productInfoMapper;
    private final OrderFileMapper orderFileMapper;
    private final ZunHaoService zunHaoService;
    private final AfterLoanPatchesService afterLoanPatchesService;
    private final ThunderAtmClient thunderAtmClient;
    private final ParamsSnapshotEntityMapper paramsSnapshotEntityMapper;
    private final FundRepaymentDeductMapper fundRepaymentDeductMapper;
    private final SwitchUtils switchUtils;
    private final SyncShuZiHuaService syncShuZiHuaService;
    private final DataFeign dataFeign;
    private final  RiskAiIntelligentAuditMapper riskAiIntelligentAuditMapper;
    private final MessageFeign messageFeign;
    private final LanHaiExpandService lanHaiExpandService;


    /**
     * 预授信
     *
     * @param fundPreBaseDTO
     * @return
     */
    @Override
    public LanHaiResult<LanHaiCreditPreApplyResponse> lanHaiCreditPreApply(FundPreBaseDTO fundPreBaseDTO, List<LanHaiImageUploadRequest.FileInfo> preFileList) {
        LanHaiCreditPreApplyRequest bean = buildLanHaiCreditPreApplyDTO(fundPreBaseDTO, preFileList);
        String oriRequestSerialNo = getOriRequestSerialNo();
        LanHaiResult<LanHaiCreditPreApplyResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_REPAY_DETAIL_QUERY, bean, LanHaiCreditPreApplyResponse.class, oriRequestSerialNo);
        if (!LanHaiResult.isSuccess(result)) {
            throw new BusinessException(result.getMsg());
        }
        fundApiDictMapper.insert(new FundApiDictEntity()
                .setLinkId(fundPreBaseDTO.getPreId())
                .setType(FundApiDictTypeEnum.PRE)
                .setValue(oriRequestSerialNo)
                .setCode(FundApiDictEnum.PRE_ORI_REQUEST_SERIAL_NO)
        );
        fundApiDictMapper.insert(new FundApiDictEntity()
                .setLinkId(fundPreBaseDTO.getPreId())
                .setType(FundApiDictTypeEnum.PRE)
                .setValue(result.getData().getBody().getApplyId())
                .setCode(FundApiDictEnum.PRE_ORI_REQUEST_APPLY_NO)
        );
        return result;
    }

    /**
     * 构建蓝海预授信上传请求
     *
     * @param fundPreBaseDTO
     * @return
     */
    private LanHaiCreditPreApplyRequest buildLanHaiCreditPreApplyDTO(FundPreBaseDTO fundPreBaseDTO, List<LanHaiImageUploadRequest.FileInfo> preFileList) {
        LanHaiCreditPreApplyRequest request = new LanHaiCreditPreApplyRequest();
        List<LanHaiCreditPreApplyRequest.VehicleInfo> vehicleInfoList = new ArrayList<>();
        DataAreaDTO cityByVehicleNumber = new DataAreaDTO();
        if (fundPreBaseDTO.getVehicleNumber() != null) {
            cityByVehicleNumber = fundApproveMapper.getCityByVehicleNumber(fundPreBaseDTO.getVehicleNumber().substring(0, 2));
        }
        vehicleInfoList.add(
                new LanHaiCreditPreApplyRequest.VehicleInfo()
                        .setVehNumber(fundPreBaseDTO.getVehicleNumber())
                        .setFrameNumber(fundPreBaseDTO.getVin())
                        .setInitRegDate(fundPreBaseDTO.getRegisterDate())
                        .setVehMileage(fundPreBaseDTO.getMileage().toString())
                        .setLicenseLocal(cityByVehicleNumber.getAreaName())
                        .setVehOwner(fundPreBaseDTO.getName())
        );

        List<LanHaiCreditPreApplyRequest.Attachment> attachmentList = new ArrayList<>();

        preFileList.stream().forEach(item -> {
            LanHaiCreditPreApplyRequest.Attachment attachment = new LanHaiCreditPreApplyRequest.Attachment();
            attachment.setFileKind(item.getFileKind())
                    .setFileName(item.getFileName());
            attachmentList.add(attachment);
        });

        request.setOperateType("1")
                .setBusiType("1")
                .setProductId(lanHaiConfig.getProductId())
                .setApplyForLimit("Y")
                .setPersonalBaseInfo(
                        new LanHaiCreditPreApplyRequest.PersonalBaseInfo()
                                .setUserName(fundPreBaseDTO.getName())
                                .setCertificateKind("110001")
                                .setCertificateNo(fundPreBaseDTO.getIdNumber())
                                .setMobileNo(fundPreBaseDTO.getPhone())
                )
                .setVehicleInfo(vehicleInfoList)
                .setAttachments(attachmentList)
        ;
        return request;
    }

    @Override
    public LanHaiResult<LanHaiCreditApplyResponse> creditApply(FundFinalBaseDTO fundFinalBaseDTO) {
        log.info("LanHaiServiceImpl.creditApply start orderId:{}", fundFinalBaseDTO.getOrderId());
        // 请求交易流水号
        String requestSerialNo = getOriRequestSerialNo();
        LanHaiCreditApplyRequest creditApplyRequest = buildCreditApply(fundFinalBaseDTO);
        LanHaiResult<LanHaiCreditApplyResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_CREDIT_APPLY, creditApplyRequest, LanHaiCreditApplyResponse.class, requestSerialNo);
        log.info("LanHaiServiceImpl.creditApply end orderId:{} result:{}", fundFinalBaseDTO.getOrderId(), result);
        if (!LanHaiResult.isSuccess(result)) {
            log.info("LanHaiServiceImpl.creditApply fail orderId:{} result:{}", fundFinalBaseDTO.getOrderId(), JSONUtil.toJsonStr(result));
            fundFinalBaseDTO.setCallFundRemark(result.getData().getHead().getReturnMessage());
            throw new BusinessException(result.getData().getHead().getReturnMessage());
        }
        log.info("LanHaiServiceImpl.creditApply success orderId:{} applyId:{}", fundFinalBaseDTO.getOrderId(), result.getData().getBody().getApplyId());
        boolean b = fundApiDictService.saveFundApiDictByLinkId(fundFinalBaseDTO.getOrderId(),
                FundApiDictTypeEnum.CREDIT,
                FundApiDictEnum.CREDIT_REQUEST_SERIAL_NO,
                requestSerialNo);
        if (!b) {
            log.info("LanHaiServiceImpl.creditApply save fundApiDict fail orderId:{} requestSerialNo:{}", fundFinalBaseDTO.getOrderId(), requestSerialNo);
        }
        // 更新授信流水号
        finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                .set(FinalFundInfoEntity::getCreditReqNo, result.getData().getBody().getApplyId())
                .eq(FinalFundInfoEntity::getOrderId, fundFinalBaseDTO.getOrderId())
                .eq(FinalFundInfoEntity::getFundId, fundFinalBaseDTO.getFundId())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );
        return result;

    }


    /**
     * 授信申请查询
     */
    @Override
    public void creditApplyQueryByOrderId(Integer orderId) {
        LanHaiCreditApplyQueryRequest creditApplyQueryRequest = creditApplyQueryBuild(orderId);
        LanHaiResult<LanHaiCreditApplyQueryResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_CREDIT_APPLY_VERIFY, creditApplyQueryRequest, LanHaiCreditApplyQueryResponse.class, getOriRequestSerialNo());
        log.info("LanHaiServiceImpl.creditApplyQueryByOrderId orderId:{} result:{}", orderId, JSONUtil.toJsonStr(result));
        if (!LanHaiResult.isSuccess(result)) {
            log.info("LanHaiServiceImpl.creditApplyQueryByOrderId result is fail orderId:{}", orderId);
            throw new BusinessException("获取授信结果失败");
        }
        // 处理授信结果
        handleCreditApplyQuery(orderId, result);
    }

    /**
     * 查询授信状态不处理结果
     */
    @Override
    public LanHaiResult<LanHaiCreditApplyQueryResponse> creditApplyQueryByOrderIdNoResult(Integer orderId) {
        LanHaiCreditApplyQueryRequest creditApplyQueryRequest = creditApplyQueryBuild(orderId);
        LanHaiResult<LanHaiCreditApplyQueryResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_CREDIT_APPLY_VERIFY, creditApplyQueryRequest, LanHaiCreditApplyQueryResponse.class, getOriRequestSerialNo());
        log.info("LanHaiServiceImpl.creditApplyQueryByOrderIdNoResult orderId:{} result:{}", orderId, JSONUtil.toJsonStr(result));
        if (!LanHaiResult.isSuccess(result)) {
            log.info("LanHaiServiceImpl.creditApplyQueryByOrderIdNoResult result is fail orderId:{}", orderId);
            throw new BusinessException("获取授信结果失败");
        }
        return result;
    }


    /**
     * 授信终结
     *
     * @param orderId
     */
    @Override
    public LanHaiResult<LanHaiCreditLimitTerminateResponse> creditLimitTerminateByOrderId(Integer orderId) {
        LanHaiCreditLimitTerminateRequest creditLimitTerminateRequest = creditLimitTerminateBuild(orderId);
        LanHaiResult<LanHaiCreditLimitTerminateResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_CREDIT_END, creditLimitTerminateRequest, LanHaiCreditLimitTerminateResponse.class, getOriRequestSerialNo());
        log.info("LanHaiServiceImpl.creditLimitTerminateByOrderId orderId:{} result:{}", orderId, JSONUtil.toJsonStr(result));
        if (!LanHaiResult.isSuccess(result)) {
            log.info("LanHaiServiceImpl.creditLimitTerminateByOrderId fail orderId:{} result:{}", orderId, JSONUtil.toJsonStr(result));
            throw new BusinessException("终止授信失败");
        }
        return result;
    }

    /**
     * 合同签署申请
     */
    @Override
    public LanHaiResult<LanHaiContractSignApplyResponse> contractSignApplyByOrderId(Integer orderId) {
        log.info("LanHaiServiceImpl.contractSignApplyByOrderId orderId:{}", orderId);

        LanHaiContractSignApplyRequest contractSignApplyRequest = contractSignApplyBuild(orderId);
        LanHaiResult<LanHaiContractSignApplyResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_CONTRACT_SIGN, contractSignApplyRequest, LanHaiContractSignApplyResponse.class, getOriRequestSerialNo());
        log.info("LanHaiServiceImpl.contractSignApplyByOrderId orderId:{} result:{}", orderId, JSONUtil.toJsonStr(result));
        if (!LanHaiResult.isSuccess(result)) {
            log.info("LanHaiServiceImpl.contractSignApplyByOrderId fail orderId:{} result:{}", orderId, JSONUtil.toJsonStr(result));
            throw new BusinessException(result.getData().getHead().getReturnMessage());
        }
        LanHaiContractSignApplyResponse body = result.getData().getBody();
        if (StrUtil.equals(body.getDealFlag(), "2")) {
            log.info("LanHaiServiceImpl.contractSignApplyByOrderId orderId:{} dealFlag:{}", orderId, body.getDealFlag());
            //更新授信状态为成功
            finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .set(FinalFundInfoEntity::getPrePaymentStatus, FundPrePaymentStatusEnum.FAIL)
                    .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
            );
            throw new BusinessException("合同签署申请失败");
        } else {
            //更新授信状态为成功
            finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .set(FinalFundInfoEntity::getPrePaymentStatus, FundPrePaymentStatusEnum.PASS)
                    .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
            );
        }

        List<LanHaiContractSignApplyResponse.SignatureFile> signatureFiles = body.getSignatureFiles();
        if (CollUtil.isEmpty(signatureFiles)) {
            log.info("LanHaiServiceImpl.contractSignApplyByOrderId orderId:{} signatureFiles is empty", orderId);
            throw new BusinessException("合同签署申请结果为空");
        }
        return result;
    }


    /**
     * 获取下载合同签署文件列表
     */
    @Override
    public List<LanHaiContractSignFileDTO> getContractSignFileList(Integer orderId, List<String> signatureFiles, LocalDate signTime) {
        if (CollUtil.isEmpty(signatureFiles)) {
            log.info("LanHaiServiceImpl.getContractSignFileList orderId:{} signatureFiles is empty", orderId);
            return List.of();
        }
        log.info("LanHaiServiceImpl.getContractSignFileList orderId:{} signatureFiles:{}", orderId, JSONUtil.toJsonStr(signatureFiles));

        FundResourceDTO fundResourceDTO = new FundResourceDTO();
        List<FundResourceDTO.Resource> resourceList = new ArrayList<>();
        for (String signatureFile : signatureFiles) {
            log.info("LanHaiServiceImpl.getContractSignFileList orderId:{} signatureFile:{}", orderId, JSONUtil.toJsonStr(signatureFile));


            FundResourceDTO.Resource resource = new FundResourceDTO.Resource();
            String path = "/$rootdirectory/$merchantid/$yyyymmdd/bobtochannel/image/$image_kind";
            //字符串替换
            path = path.replace("$rootdirectory", "app");
            path = path.replace("$merchantid", lanHaiConfig.getMerchantId());
            path = path.replace("$yyyymmdd", signTime.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            //176_1716773910_000CA202407170000018057_01.pdf
            //根据文件名称_前面数字为$image_kind
            String fileKind = signatureFile.split("_")[0];
            path = path.replace("$image_kind", fileKind);
            path += "/" + signatureFile;
            resource.setFilePath(path);
            resource.setFundFileCode(fileKind);
            resource.setFileName(signatureFile);
            resourceList.add(resource);
        }
        fundResourceDTO.setResourceList(resourceList);
        fundResourceDTO.setFund(FundEnum.LAN_HAI);
        Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceDownload(fundResourceDTO);
        if (!Result.isSuccess(listResult)) {
            log.info("LanHaiServiceImpl.getContractSignFileList orderId:{} resourceFeign fail", orderId);
            throw new BusinessException("获取合同签署文件失败");
        }
        List<FundResourceResultDTO> dataList = listResult.getData();
        if (CollUtil.isEmpty(dataList)) {
            log.info("LanHaiServiceImpl.getContractSignFileList orderId:{} data is empty", orderId);
            return List.of();
        }

        List<LanHaiContractSignFileDTO> contractSignFileDTOList = new ArrayList<>();
        for (FundResourceResultDTO resultDTO : dataList) {
            log.info("LanHaiServiceImpl.getContractSignFileList orderId:{} resultDTO:{}", orderId, JSONUtil.toJsonStr(resultDTO));
            LanHaiContractSignFileDTO lanHaiContractSignFileDTO = new LanHaiContractSignFileDTO();
            lanHaiContractSignFileDTO.setFileName(resultDTO.getResourceName());
            lanHaiContractSignFileDTO.setResourceId(resultDTO.getResourceId());
            lanHaiContractSignFileDTO.setOriginalFileName(resultDTO.getOriginFileName());

            if (StrUtil.isNotBlank(resultDTO.getFileCode())) {
                FundPushResourceConfigEntity fundPushResourceConfigEntity = fundPushResourceConfigMapper.selectOne(new LambdaQueryWrapper<FundPushResourceConfigEntity>()
                        .eq(FundPushResourceConfigEntity::getType, 5)
                        .eq(FundPushResourceConfigEntity::getFileCode, resultDTO.getFileCode())
                        .eq(FundPushResourceConfigEntity::getDeleteFlag, 0)
                        .eq(FundPushResourceConfigEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .orderByDesc(FundPushResourceConfigEntity::getId)
                        .last("limit 1")
                );
                if (ObjUtil.isNull(fundPushResourceConfigEntity)) {
                    log.info("LanHaiServiceImpl.getContractSignFileList orderId:{} not found fundPushResourceConfigEntity fileKind:{}", orderId, resultDTO.getFileCode());
                    continue;
                }
                lanHaiContractSignFileDTO.setContractTemplateId(fundPushResourceConfigEntity.getConfigId());
            }

            contractSignFileDTOList.add(lanHaiContractSignFileDTO);
        }

        return contractSignFileDTOList;
    }

    @Override
    public List<LanHaiImageUploadRequest.FileInfo> imageUploadPre(FundPreBaseDTO fundPreBaseDTO, String type) {
        Integer preId = fundPreBaseDTO.getPreId();
        log.info("--- 蓝海影像上传开始 preId: {} ---", preId);
        FundEnum lanHai = FundEnum.LAN_HAI;
        FundResourceDTO fundResourceDTO = new FundResourceDTO();
        if (ObjectUtil.equals(type, "pre")) {
            fundResourceDTO
                    .setLinkId(preId)
                    .setType(1)
                    .setFund(lanHai)
                    .setCustomerBaseInfo(
                            new FundResourceDTO.CustomerBaseInfo()
                                    .setCustomerIdNo(fundPreBaseDTO.getIdNumber())
                                    .setPhone(fundPreBaseDTO.getPhone())
                    )
            ;
        }
        Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(fundResourceDTO);
        if (!Result.isSuccess(listResult)) {
            throw new BusinessException("获取影像文件失败");
        }
        List<FundResourceResultDTO> data = listResult.getData();
        LanHaiImageUploadRequest lanHaiImageUploadRequest = new LanHaiImageUploadRequest();
        List<LanHaiImageUploadRequest.FileInfo> fileList = new ArrayList<>();

        data.stream().forEach(item -> {
            LanHaiImageUploadRequest.FileInfo fileInfo = new LanHaiImageUploadRequest.FileInfo();
            fileInfo.setFileData(item.getFilePath());
            fileInfo.setFileKind(item.getFileCode());
            fileInfo.setFileName(item.getFileId());
            fileList.add(fileInfo);
        });
        //数据太大 分开推送
        try {
            fileList.stream().forEach(item -> {
                List<LanHaiImageUploadRequest.FileInfo> fileNew = new ArrayList<>();
                fileNew.add(item);
                lanHaiImageUploadRequest.setFileList(fileNew);
                log.info("LanHaiServiceImpl.imageUpload lanHaiImageUploadRequest:{}", lanHaiImageUploadRequest);
                LanHaiResult<LanHaiImageUploadResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_IMAGE_UPLOAD, lanHaiImageUploadRequest, LanHaiImageUploadResponse.class, getOriRequestSerialNo());
                if (!LanHaiResult.isSuccess(result) || !LanHaiResult.isSuccess(result)) {
                    throw new BusinessException("影像上传失败");
                }
            });
        } catch (Exception e) {
            throw new BusinessException("影像上传失败:" + e.getMessage());
        }
        return fileList;
    }


    @Override
    public boolean lanHaiCreditPreEnd(Integer preId) {
        log.info("LanHaiServiceImpl.lanHaiCreditPreEnd preId:{}", preId);
        boolean flag = false;

        PreApprovalApplyInfoEntity preApprovalApplyInfo = preApprovalApplyInfoMapper.selectOne(new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
                .eq(PreApprovalApplyInfoEntity::getId, preId)
                .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0));

        List<PreApprovalApplyInfoEntity> preApprovalApplyInfoEntities = preApprovalApplyInfoMapper.selectList(
                new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
                        .eq(PreApprovalApplyInfoEntity::getName, preApprovalApplyInfo.getName())
                        .eq(PreApprovalApplyInfoEntity::getIdNumber, preApprovalApplyInfo.getIdNumber())
                        .eq(PreApprovalApplyInfoEntity::getPhone, preApprovalApplyInfo.getPhone())
//                        .eq(PreApprovalApplyInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
        );
        if (CollUtil.isEmpty(preApprovalApplyInfoEntities)) {
            log.info("LanHaiServiceImpl.lanHaiCreditPreEnd preApprovalApplyInfoEntities is empty");
            return true;
        }
        List<Integer> preIds = preApprovalApplyInfoEntities.stream()
                .map(PreApprovalApplyInfoEntity::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        log.info("LanHaiServiceImpl.lanHaiCreditPreEnd preIds:{}", preIds);

        List<FundApiDictEntity> list = fundApiDictMapper.selectList(new LambdaQueryWrapper<>(FundApiDictEntity.class)
                .in(FundApiDictEntity::getLinkId, preIds)
                .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.PRE)
                .eq(FundApiDictEntity::getCode, FundApiDictEnum.PRE_ORI_REQUEST_APPLY_NO)
        );
        log.info("LanHaiServiceImpl.lanHaiCreditPreEnd list:{}", JSONUtil.toJsonStr(list));
        try {
            list.stream().forEach(fundApiDictEntity -> {
                LanHaiCreditPreEndRequest request = new LanHaiCreditPreEndRequest();
                request.setApplyId(fundApiDictEntity.getValue())
                        .setCreditOperateType("008")
                        .setOperateReason("预审失败，重新发起");
                LanHaiResult<LanHaiCreditPreEndResponse> lanHaiCreditPreEndResponse = null;
                try {
                    log.info("LanHaiServiceImpl.lanHaiCreditPreEnd request:{}", JSONUtil.toJsonStr(request));
                    lanHaiCreditPreEndResponse = lanHaiClient.execute(LanHaiApiEnums.LH_REPAY_DETAIL_QUERY_END, request, LanHaiCreditPreEndResponse.class, getOriRequestSerialNo());
                    log.info("LanHaiServiceImpl.lanHaiCreditPreEnd response:{}", JSONUtil.toJsonStr(lanHaiCreditPreEndResponse));
                    if (!LanHaiResult.isSuccess(lanHaiCreditPreEndResponse)) {
                        throw new BusinessException("预授信终结失败" + lanHaiCreditPreEndResponse.getData().getHead().getReturnMessage());
                    }

                } catch (Exception e) {
                    throw new BusinessException("预授信终结失败:" + e.getMessage());
                }
            });
            flag = true;
        } catch (Exception e) {
            throw new BusinessException("预授信终结失败");
        } finally {
            //清除预审保存的 apidict
            if (flag) {
                fundApiDictMapper.update(new LambdaUpdateWrapper<FundApiDictEntity>()
                        .set(FundApiDictEntity::getDeleteFlag, 1)
                        .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.PRE)
                        .eq(FundApiDictEntity::getLinkId, preId)
                );
            }
        }
        return flag;
    }

    /**
     * 放款申请
     */
    @Override
    public LanHaiResult<LanHaiLoanApplyResponse> loanApplyByOrderId(Integer orderId) {
        log.info("LanHaiServiceImpl.loanApply orderId:{}", orderId);
        FundFinalBaseDTO fundFinalBaseDTO = fundBaseInfoService.getFundFinalBaseApplyInfo(orderId);
        LanHaiLoanApplyRequest lanHaiLoanApplyRequest = loanApplyBuild(fundFinalBaseDTO);
        log.info("LanHaiServiceImpl.loanApply :{} lanHaiLoanApplyRequest:{}", orderId, JSONUtil.toJsonStr(lanHaiLoanApplyRequest));
        String oriRequestSerialNo = getOriRequestSerialNo();
        LanHaiResult<LanHaiLoanApplyResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_LOAN_APPLY, lanHaiLoanApplyRequest, LanHaiLoanApplyResponse.class, oriRequestSerialNo);
        log.info("LanHaiServiceImpl.loanApply orderId:{} result:{}", orderId, JSONUtil.toJsonStr(result));
        if (!LanHaiResult.isSuccess(result)) {
            log.info("LanHaiServiceImpl.loanApply fail orderId:{} result:{}", orderId, JSONUtil.toJsonStr(result));
            fundFinalBaseDTO.setCallFundRemark(result.getData().getHead().getReturnMessage());
            throw new BusinessException(result.getData().getHead().getReturnMessage());
        }
        //更新放款申请编号
        finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                .set(FinalFundInfoEntity::getLoanBillNo, result.getData().getBody().getApplyId())
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );
        //保存请求流水号
        fundApiDictService.saveFundApiDictByLinkId(orderId, FundApiDictTypeEnum.LOAN, FundApiDictEnum.LOAN_REQUEST_SERIAL_NO, oriRequestSerialNo);
        log.info("LanHaiServiceImpl.loanApply orderId:{} loanBillNo:{}", orderId, result.getData().getBody().getApplyId());
        return result;
    }

    /**
     * 放款申请查证
     */
    @Override
    public LanHaiResult<LanHaiLoanApplyVerifyResponse> loanApplyVerifyByOrderId(Integer orderId) {
        log.info("LanHaiServiceImpl.loanApplyVerify orderId:{}", orderId);
        FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(new LambdaQueryWrapper<>(FundApiDictEntity.class)
                .eq(FundApiDictEntity::getLinkId, orderId)
                .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.LOAN)
                .eq(FundApiDictEntity::getCode, FundApiDictEnum.LOAN_REQUEST_SERIAL_NO)
                .orderByDesc(FundApiDictEntity::getCreateTime)
                .last("limit 1")
        );
        log.info("LanHaiServiceImpl.loanApplyVerify fundApiDictEntity:{}", JSONUtil.toJsonStr(fundApiDictEntity));
        if (ObjectUtil.isNull(fundApiDictEntity)) {
            log.info("LanHaiServiceImpl.loanApplyVerify fail orderId:{} not found loan request serial no", orderId);
            throw new BusinessException("未获取到放款申请相关信息");
        }
        String loanReqNo = fundApiDictEntity.getValue();
        LanHaiLoanApplyVerifyRequest verifyRequest = new LanHaiLoanApplyVerifyRequest();
        verifyRequest.setOriRequestSerialNo(loanReqNo);
        LanHaiResult<LanHaiLoanApplyVerifyResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_LOAN_APPLY_VERIFY, verifyRequest, LanHaiLoanApplyVerifyResponse.class, getOriRequestSerialNo());
        log.info("LanHaiServiceImpl.loanApplyVerify orderId:{} result:{}", orderId, JSONUtil.toJsonStr(result));
        if (!LanHaiResult.isSuccess(result)) {
            log.info("LanHaiServiceImpl.loanApplyVerify fail orderId:{} result: {}", orderId, JSONUtil.toJsonStr(result));
            throw new BusinessException(result.getData().getHead().getReturnMessage());
        }
        //处理放款结果
        handlerLoanApplyQuery(orderId, result);
        return result;
    }

    /**
     * 处理授信回调结果
     */
    @Override
    public List<LanHaiCallBackRequest> handleCreditApplyCallback(List<LanHaiCallBackResponse> callback) {
        log.info("LanHaiServiceImpl.handleCreditApplyCallback callback:{}", JSONUtil.toJsonStr(callback));
        if (CollUtil.isEmpty(callback)) {
            log.info("LanHaiServiceImpl.handleCreditApplyCallback callback is empty");
            return List.of();
        }
        List<LanHaiCallBackRequest> callBackResultList = new ArrayList<>();
        for (LanHaiCallBackResponse callDto : callback) {
            String arguments = callDto.getArguments();
            LanHaiResult.Result<LanHaiCreditApplyCallBackResponse> callBackInfoResult = null;
            try {
                JavaType javaType = objectMapper.getTypeFactory().constructParametricType(LanHaiResult.Result.class, LanHaiCreditApplyCallBackResponse.class);
                callBackInfoResult = objectMapper.readValue(arguments, javaType);
            } catch (Exception e) {
                log.info("LanHaiServiceImpl.handleCreditApplyCallback error parsing response: {}", e.getMessage(), e);
                LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                callResultDTO.setId(callDto.getId());
                callResultDTO.setStatus("2");
                callResultDTO.setRemark("参数解析失败");
                callBackResultList.add(callResultDTO);
                continue;
            }
            if (ObjectUtil.isNull(callBackInfoResult)) {
                log.info("LanHaiServiceImpl.handleCreditApplyCallback callBackInfo is null");
                LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                callResultDTO.setId(callDto.getId());
                callResultDTO.setStatus("2");
                callResultDTO.setRemark("未获取到回调参数信息");
                callBackResultList.add(callResultDTO);
                continue;
            }
            try {
                LanHaiCreditApplyCallBackResponse callBackInfo = callBackInfoResult.getBody();
                //根据授信号查询
                String applyId = callBackInfo.getApplyId();
                FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getCreditReqNo, applyId)
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                        .last("limit 1")
                );
                if (ObjUtil.isNull(finalFundInfo)) {
                    log.info("LanHaiServiceImpl.handleCreditApplyCallback finalFundInfo is null applyId:{}", applyId);
                    LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                    callResultDTO.setId(callDto.getId());
                    callResultDTO.setStatus("2");
                    callResultDTO.setRemark("授信号不存在,未查询到相关信息");
                    callBackResultList.add(callResultDTO);
                    continue;
                }

                Integer orderId = finalFundInfo.getOrderId();
                Integer fundId = finalFundInfo.getFundId();
                PreFundResultEnum preFundResultEnum = switch (callBackInfo.getHandlerStatus()) {
                    case "001" -> PreFundResultEnum.PASS;
                    case "003" -> PreFundResultEnum.REJECT;
                    default -> PreFundResultEnum.WAIT;
                };
                BigDecimal loanAmount = ObjUtil.defaultIfNull(callBackInfo.getLoanAmount(), BigDecimal.ZERO);

                if (ObjUtil.equals(PreFundResultEnum.PASS, preFundResultEnum)) {
                    AmountCalDTO amountCalDTO = new AmountCalDTO();
                    amountCalDTO.setAmount(loanAmount);
                    amountCalDTO.setCalType(OrderAmountEnum.FUND_PRE_AMOUNT);
                    amountCalDTO.setOrderId(orderId);
                    // 更新金额表信息
                    log.info("LanHaiServiceImpl.handleCreditApplyCallback updated for orderId: {}", orderId);
                    log.info("LanHaiServiceImpl.handleCreditApplyCallback AmountCalDTO created: {}", JSONUtil.toJsonStr(amountCalDTO));
                    orderFeign.calAmount(amountCalDTO);
                }
                if (ObjUtil.isNotNull(callBackInfo.getExecuteRate())){
                    finalFundInfo.setExecuteRate(callBackInfo.getExecuteRate().divide(new BigDecimal(100)));
                    finalFundInfoMapper.updateById(finalFundInfo);
                }
                // 更新订单资方状态
                FinalApproveFundStatusDTO fundStatusDTO = new FinalApproveFundStatusDTO()
                        .setOrderId(orderId)
                        .setFundId(fundId)
                        .setStatus(preFundResultEnum)
                        .setFundCreditAmt(loanAmount)
                        .setFundRemark(String.join(" ", callBackInfo.getRemark(), callBackInfo.getErrMsg()))
                        .setFundCreditTime(callBackInfo.getStartTime())
                        .setCreditReqNo(applyId)
                        .setCreditNo(callBackInfo.getCreditLimitId())
                        .setFundUserId(callBackInfo.getUserId());

                log.info("LanHaiServiceImpl.handleCreditApplyCallback FinalApproveFundStatusDTO created: {}", JSONUtil.toJsonStr(fundStatusDTO));
                orderFeign.updateFundFinalStatus(fundStatusDTO);

                LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                callResultDTO.setId(callDto.getId());
                callResultDTO.setStatus("1");
                callResultDTO.setRemark("处理成功");
                callBackResultList.add(callResultDTO);
            } catch (Exception e) {
                log.info("LanHaiServiceImpl.handleCreditApplyCallback error: {}", e.getMessage(), e);
                LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                callResultDTO.setId(callDto.getId());
                callResultDTO.setStatus("2");
                callResultDTO.setRemark("业务处理失败");
                callBackResultList.add(callResultDTO);
            }

        }


        return callBackResultList;
    }


    @Override
    public List<LanHaiCallBackRequest> handlePayCallback(List<LanHaiCallBackResponse> callback) {
        log.info("LanHaiServiceImpl.handlePayCallback callback:{}", JSONUtil.toJsonStr(callback));
        if (CollUtil.isEmpty(callback)) {
            log.info("LanHaiServiceImpl.handlePayCallback callback is empty");
            return List.of();
        }
        List<LanHaiCallBackRequest> callBackResultList = new ArrayList<>();
        for (LanHaiCallBackResponse callDto : callback) {
            String arguments = callDto.getArguments();
            LanHaiResult.Result<LanHaiLoanCallbackResponse> callBackInfoResult = null;
            try {
                JavaType javaType = objectMapper.getTypeFactory().constructParametricType(LanHaiResult.Result.class, LanHaiLoanCallbackResponse.class);
                callBackInfoResult = objectMapper.readValue(arguments, javaType);
            } catch (Exception e) {
                log.info("LanHaiServiceImpl.handlePayCallback error parsing response: {}", e.getMessage(), e);
                LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                callResultDTO.setId(callDto.getId());
                callResultDTO.setStatus("2");
                callResultDTO.setRemark("参数解析失败");
                callBackResultList.add(callResultDTO);
                continue;
            }
            if (ObjectUtil.isNull(callBackInfoResult)) {
                log.info("LanHaiServiceImpl.handlePayCallback callBackInfo is null");
                LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                callResultDTO.setId(callDto.getId());
                callResultDTO.setStatus("2");
                callResultDTO.setRemark("未获取到回调参数信息");
                callBackResultList.add(callResultDTO);
                continue;
            }
            try {
                LanHaiLoanCallbackResponse resultBody = callBackInfoResult.getBody();

                FundNodeStatusEnum nextNodeStatus = null;
                FundPaymentStatusEnum fundPaymentStatus = null;
                String failReason = null;
                FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(
                        Wrappers.<FinalFundInfoEntity>lambdaQuery()
                                .eq(FinalFundInfoEntity::getLoanBillNo, resultBody.getApplyId())
                                .eq(FinalFundInfoEntity::getStatus, InitStatusEnums.SUCCESS)
                                .eq(FinalFundInfoEntity::getFundResult, PreFundResultEnum.PASS)
                                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                        , false
                );
                if (ObjectUtil.isNull(finalFundInfo)) {
                    log.error("LanHaiServiceImpl.handlePayCallback on payment ： {}", "订单不存在");
                    throw new BusinessException("订单不存在");
                }
                Integer orderId = finalFundInfo.getOrderId();
                log.info("LanHaiServiceImpl.handlePayCallback orderId:{} applyId:{} result:{}", orderId, resultBody.getApplyId(), JSONUtil.toJsonStr(resultBody));
                String handlerStatus = resultBody.getHandlerStatus();
                switch (handlerStatus) {
                    case "001" -> {
                        log.info("LanHaiServiceImpl.handlePayCallback orderId:{} applyId:{} 放款成功", orderId, resultBody.getApplyId());
                        nextNodeStatus = FundNodeStatusEnum.FUND_PAYMENT_SUCCESS;
                        fundPaymentStatus = FundPaymentStatusEnum.PASS;
                    }
                    case "002" -> {
                        log.info("LanHaiServiceImpl.handlePayCallback orderId:{} applyId:{} 放款处理中", orderId, resultBody.getApplyId());
                        nextNodeStatus = FundNodeStatusEnum.LOAN_APPLY_QUERY;
                        fundPaymentStatus = FundPaymentStatusEnum.WAIT;
                    }
                    case "003" -> {
                        log.info("LanHaiServiceImpl.handlePayCallback orderId:{} applyId:{} 放款失败", orderId, resultBody.getApplyId());
                        nextNodeStatus = FundNodeStatusEnum.FUND_PAYMENT_FAIL;
                        fundPaymentStatus = FundPaymentStatusEnum.FAIL;
                    }
                    default -> {
                        log.info("LanHaiServiceImpl.handlePayCallback orderId:{} applyId:{} 无此交易（非终态）", orderId, resultBody.getApplyId());
                        nextNodeStatus = FundNodeStatusEnum.LOAN_APPLY_QUERY;
                        fundPaymentStatus = FundPaymentStatusEnum.WAIT;
                    }
                }

                BigDecimal loanAmount = ObjUtil.defaultIfNull(resultBody.getLoanAmt(), BigDecimal.ZERO);
                LocalDateTime loanPayTime = resultBody.getLoanPayTime();
                failReason = resultBody.getErrMsg();

                updateOrderPaymentStatus(orderId, FundEnum.LAN_HAI.getValue(), loanAmount,
                        loanPayTime,
                        failReason, fundPaymentStatus, resultBody.getLoanInvoiceId());
                finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                        .set(FinalFundInfoEntity::getNodeStatus, nextNodeStatus)
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                );

                if (ObjUtil.equals(fundPaymentStatus, FundPaymentStatusEnum.PASS)) {
                    OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
                    if (ObjUtil.isNull(orderInfo)) {
                        throw new BusinessException("订单不存在");
                    }
                    ProductRongdanEntity productRongdanInfo = productRongdanMapper.selectById(orderInfo.getRongdanId());
                    if (ObjUtil.isNull(productRongdanInfo)) {
                        throw new BusinessException("融担公司不存在");
                    }
                    try {
                        queryRepayPlan(orderId);
                    } catch (Exception e) {
                        log.error("LanHaiServiceImpl.handlePayCallback queryRepayPlan orderId:{} error: {}", orderId, e.getMessage());
                    }
                    SwitchVO newLendersPerformance = switchUtils.getSwitchInfo("ZUN_HAO_APPLY");
                    if (ObjUtil.isNotNull(newLendersPerformance) && newLendersPerformance.getSwitchFlag() == 1) {
                        try {
                            if (StrUtil.equals(productRongdanInfo.getMiddleCode(), ProductRongDanEnum.ZUN_HAO.getCode())) {
                                zunHaoService.projectApply(orderId);
                                zunHaoService.loanApply(orderId);
                            }

                        } catch (Exception e) {
                            log.error("LanHaiServiceImpl.handlerLoanApplyQuery projectApply orderId:{} error: {}", orderId, e.getMessage(), e);
                        }
                    } else {

                    }

                }

                LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                callResultDTO.setId(callDto.getId());
                callResultDTO.setStatus("1");
                callResultDTO.setRemark("处理成功");
                callBackResultList.add(callResultDTO);
            } catch (BusinessException e) {
                log.info("LanHaiServiceImpl.handlePayCallback BusinessException error: {}", e.getMessage(), e);
                LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                callResultDTO.setId(callDto.getId());
                callResultDTO.setStatus("2");
                callResultDTO.setRemark(e.getMessage());
                callBackResultList.add(callResultDTO);
            } catch (Exception e) {
                log.info("LanHaiServiceImpl.handlePayCallback Exception error: {}", e.getMessage(), e);
                LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                callResultDTO.setId(callDto.getId());
                callResultDTO.setStatus("2");
                callResultDTO.setRemark("业务处理失败");
                callBackResultList.add(callResultDTO);
            }
        }
        return callBackResultList;
    }

    /**
     * 授信结果处理
     */
    public void handleCreditApplyQuery(Integer orderId, LanHaiResult<LanHaiCreditApplyQueryResponse> result) {
        log.info("LanHaiServiceImpl.handleCreditApplyQuery result: {}", JSONUtil.toJsonStr(result));
        if (!LanHaiResult.isSuccess(result)) {
            throw new BusinessException("授信结果处理失败");
        }

        LanHaiResult.Result<LanHaiCreditApplyQueryResponse> data = result.getData();
        LanHaiCreditApplyQueryResponse resultBody = data.getBody();
        //根据授信号查询
        String applyId = resultBody.getApplyId();
        if (ObjUtil.isNull(orderId) && StrUtil.isBlank(applyId)) {
            log.info("LanHaiServiceImpl.handleCreditApplyQuery orderId is null result:{}", JSONUtil.toJsonStr(result));
            throw new BusinessException("授信结果处理失败,未查询到相关信息");
        }
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(StrUtil.isNotBlank(applyId), FinalFundInfoEntity::getCreditReqNo, applyId)
                .eq(ObjUtil.isNotNull(orderId), FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(finalFundInfo)) {
            log.info("LanHaiServiceImpl.handleCreditApplyQuery finalFundInfo is null applyId:{}", applyId);
            throw new BusinessException("授信结果处理失败,未查询到相关信息");
        }
        if (ObjUtil.isNotNull(resultBody.getExecuteRate())){
            finalFundInfo.setExecuteRate(resultBody.getExecuteRate().divide(BigDecimal.valueOf(100)));
            finalFundInfoMapper.updateById(finalFundInfo);
        }
        Integer fundId = finalFundInfo.getFundId();
        PreFundResultEnum preFundResultEnum = switch (resultBody.getHandlerStatus()) {
            case "001" -> PreFundResultEnum.PASS;
            case "003" -> PreFundResultEnum.REJECT;
            default -> PreFundResultEnum.WAIT;
        };
        BigDecimal loanAmount = ObjUtil.defaultIfNull(resultBody.getLoanAmount(), BigDecimal.ZERO);

        if (ObjUtil.equals(PreFundResultEnum.PASS, preFundResultEnum)) {
            AmountCalDTO amountCalDTO = new AmountCalDTO();
            amountCalDTO.setAmount(loanAmount);
            amountCalDTO.setCalType(OrderAmountEnum.FUND_PRE_AMOUNT);
            amountCalDTO.setOrderId(orderId);
            // 更新金额表信息
            log.info("LanHaiServiceImpl.handleCreditApplyQuery updated for orderId: {}", orderId);
            log.info("LanHaiServiceImpl.handleCreditApplyQuery AmountCalDTO created: {}", JSONUtil.toJsonStr(amountCalDTO));
            orderFeign.calAmount(amountCalDTO);
        }

        // 更新订单资方状态
        FinalApproveFundStatusDTO fundStatusDTO = new FinalApproveFundStatusDTO()
                .setOrderId(orderId)
                .setFundId(fundId)
                .setStatus(preFundResultEnum)
                .setFundCreditAmt(loanAmount)
                .setFundRemark(String.join(" ", resultBody.getRemark(), resultBody.getErrMsg()))
                .setFundCreditTime(resultBody.getStartTime())
                .setCreditReqNo(applyId)
                .setCreditNo(resultBody.getCreditLimitId())
                .setFundUserId(resultBody.getUserId());

        log.info("LanHaiServiceImpl.handleCreditApplyQuery FinalApproveFundStatusDTO created: {}", JSONUtil.toJsonStr(fundStatusDTO));
        orderFeign.updateFundFinalStatus(fundStatusDTO);

    }


    /**
     * 更新订单放款结果
     */
    private void handlerLoanApplyQuery(Integer orderId, LanHaiResult<LanHaiLoanApplyVerifyResponse> result) {
        log.info("LanHaiServiceImpl.handlerLoanApplyQuery result: {}", JSONUtil.toJsonStr(result));
        if (!LanHaiResult.isSuccess(result)) {
            log.info("LanHaiServiceImpl.handlerLoanApplyQuery Failed: {}", JSONUtil.toJsonStr(result));
            throw new BusinessException("放款结果处理失败");
        }
        LanHaiLoanApplyVerifyResponse resultBody = result.getData().getBody();

        FundNodeStatusEnum nextNodeStatus = null;
        FundPaymentStatusEnum fundPaymentStatus = null;
        String failReason = null;
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(
                Wrappers.<FinalFundInfoEntity>lambdaQuery()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getStatus, InitStatusEnums.SUCCESS)
                        .eq(FinalFundInfoEntity::getFundResult, PreFundResultEnum.PASS)
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );
        if (ObjectUtil.isNull(finalFundInfo)) {
            log.error("LanHaiServiceImpl.handlerLoanApplyQuery on payment ： {}", "订单不存在");
            throw new BusinessException("订单不存在");
        }
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isNull(orderInfo)) {
            throw new BusinessException("订单不存在");
        }
        ProductRongdanEntity productRongdanInfo = productRongdanMapper.selectById(orderInfo.getRongdanId());
        if (ObjUtil.isNull(productRongdanInfo)) {
            throw new BusinessException("融担公司不存在");
        }
        log.info("LanHaiServiceImpl.handlerLoanApplyQuery orderId:{} result:{}", orderId, JSONUtil.toJsonStr(resultBody));
        String handlerStatus = resultBody.getHandlerStatus();
        switch (handlerStatus) {
            case "001" -> {
                log.info("LanHaiServiceImpl.handlerLoanApplyQuery orderId:{} 放款成功", orderId);
                nextNodeStatus = FundNodeStatusEnum.FUND_PAYMENT_SUCCESS;
                fundPaymentStatus = FundPaymentStatusEnum.PASS;
            }
            case "002" -> {
                log.info("LanHaiServiceImpl.handlerLoanApplyQuery orderId:{} 放款处理中", orderId);
                nextNodeStatus = FundNodeStatusEnum.LOAN_APPLY_QUERY;
                fundPaymentStatus = FundPaymentStatusEnum.WAIT;
            }
            case "003" -> {
                log.info("LanHaiServiceImpl.handlerLoanApplyQuery orderId:{} 放款失败", orderId);
                nextNodeStatus = FundNodeStatusEnum.FUND_PAYMENT_FAIL;
                fundPaymentStatus = FundPaymentStatusEnum.FAIL;
            }
            default -> {
                log.info("LanHaiServiceImpl.handlerLoanApplyQuery orderId:{} 无此交易（非终态）", orderId);
                nextNodeStatus = FundNodeStatusEnum.LOAN_APPLY_QUERY;
                fundPaymentStatus = FundPaymentStatusEnum.WAIT;
            }
        }

        BigDecimal loanAmount = ObjUtil.defaultIfNull(resultBody.getLoanAmt(), BigDecimal.ZERO);
        LocalDateTime loanPayTime = resultBody.getLoanPayTime();
        failReason = resultBody.getErrMsg();

        updateOrderPaymentStatus(orderId, FundEnum.LAN_HAI.getValue(), loanAmount,
                loanPayTime,
                failReason, fundPaymentStatus, resultBody.getLoanInvoiceId());
        finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                .set(FinalFundInfoEntity::getNodeStatus, nextNodeStatus)
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );
        if (ObjUtil.equals(fundPaymentStatus, FundPaymentStatusEnum.PASS)) {
            try {
                queryRepayPlan(orderId);
            } catch (Exception e) {
                log.error("LanHaiServiceImpl.handlerLoanApplyQuery queryRepayPlan orderId:{} error: {}", orderId, e.getMessage());
            }
            SwitchVO newLendersPerformance = switchUtils.getSwitchInfo("ZUN_HAO_APPLY");
            if (ObjUtil.isNotNull(newLendersPerformance) && newLendersPerformance.getSwitchFlag() == 1) {
                try {
                    if (StrUtil.equals(productRongdanInfo.getMiddleCode(), ProductRongDanEnum.ZUN_HAO.getCode())) {
                        zunHaoService.projectApply(orderId);
                        zunHaoService.loanApply(orderId);
                    }
                } catch (Exception e) {
                    log.error("LanHaiServiceImpl.handlerLoanApplyQuery projectApply orderId:{} error: {}", orderId, e.getMessage(), e);
                }
            } else {

            }

        }


        if (ObjectUtil.equals(fundPaymentStatus, FundPaymentStatusEnum.PASS)) {
            SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.SYNC_LAN_HAI_TO_ShuZiHua_PAY_AFTER);
            log.info("LanHaiServiceImpl.handlerLoanApplyQuery switchInfo: {}", JSONUtil.toJsonStr(switchInfo));
            //放款成功同步数字化
            if (ObjectUtil.isNotNull(switchInfo) && ObjectUtil.equals(switchInfo.getSwitchFlag(), 1)) {
                try {
                    SyncShuZiHuaDTO syncShuziHuaDTO = new SyncShuZiHuaDTO();
                    syncShuziHuaDTO.setOrderIdList(Lists.newArrayList(orderId));
                    syncShuZiHuaService.doProcess(syncShuziHuaDTO);
                } catch (Exception e) {
                    log.error("LanHaiServiceImpl.handlerLoanApplyQuery syncLoanApply orderId:{} error: {}", orderId, e.getMessage());
                }
            }

        }

    }

    private void updateOrderPaymentStatus(Integer orderId, Integer fundId, BigDecimal loanAmt,
                                          LocalDateTime payTime, String remark, FundPaymentStatusEnum fundPaymentStatusEnum, String loanNo) {

        log.info("LanHaiServiceImpl.updateOrderPaymentStatus begin orderId:{} fundId:{}", orderId, fundId);
        try {
            String failReason = remark + " 放款金额->" + loanAmt;

            // 设置要更新的字段
            finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .set(FinalFundInfoEntity::getRemark, failReason)
                    .set(FinalFundInfoEntity::getPaymentStatus, fundPaymentStatusEnum.getValue())
                    .set(FinalFundInfoEntity::getPaymentAmount, loanAmt)
                    .set(FinalFundInfoEntity::getLoanNo, loanNo)
                    .set(ObjUtil.isNotNull(payTime), FinalFundInfoEntity::getPaymentTime, payTime)
                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                    .eq(FinalFundInfoEntity::getFundId, fundId));

            //放款单号
            OrderFundPaymentEnum orderFundPaymentEnum = OrderFundPaymentEnum.WAIT;
            if (FundPaymentStatusEnum.PASS.equals(fundPaymentStatusEnum)) {
                orderFundPaymentEnum = OrderFundPaymentEnum.PASS;
            } else if (FundPaymentStatusEnum.FAIL.equals(fundPaymentStatusEnum)) {
                orderFundPaymentEnum = OrderFundPaymentEnum.FAIL;
            }

            OrderApproveFundPaymentStatusDTO fundPaymentStatusDTO = new OrderApproveFundPaymentStatusDTO()
                    .setOrderId(orderId)
                    .setFundId(fundId)
                    .setLoanApplyNo(null)
                    .setLoanAmt(loanAmt)
                    .setLoanPayTime(payTime)
                    .setFailReason(remark)
                    .setStatus(orderFundPaymentEnum);

            orderFeign.updateFundPaymentStatus(fundPaymentStatusDTO);
            log.info("LanHaiServiceImpl.updateOrderPaymentStatus end orderId:{} fundId:{}", orderId, fundId);
        } catch (Exception e) {
            log.error("LanHaiServiceImpl.updateOrderPaymentStatus orderId:{} error e:{}", orderId, e.getMessage(), e);
            throw new BusinessException("更新订单放款状态异常");
        }
    }

    /**
     * 预授信回调
     *
     * @param body
     * @return
     */
    @Override
    public List<LanHaiCallBackRequest> preCreditCallBack(List<LanHaiCallBackResponse> body) {
        log.info("LanHaiServiceImpl.preCreditCallBack body:{}", JSONUtil.toJsonStr(body));
        List<LanHaiCallBackRequest> callBacks = new ArrayList<>();
        body.stream().forEach(
                lanHaiCallBackResponse -> {
                    //蓝海预授信参数
                    LanHaiCallBackRequest response = preCreditCallBackByInfo(lanHaiCallBackResponse.getArguments());
                    response.setId(lanHaiCallBackResponse.getId());
                    callBacks.add(response);
                }
        );
        return callBacks;
    }


    private LanHaiCallBackRequest preCreditCallBackByInfo(String arguments) {
        LanHaiResult.Result<LanHaiCreditPreCallBackResponse> callBackInfoResult = null;
        LanHaiCallBackRequest request = new LanHaiCallBackRequest();
        PreApproveFundStatusDTO preApproveFundStatusDTO = null;
        boolean flag = false;
        try {
            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(LanHaiResult.Result.class, LanHaiCreditPreCallBackResponse.class);
            callBackInfoResult = objectMapper.readValue(arguments, javaType);
            LanHaiCreditPreCallBackResponse info = callBackInfoResult.getBody();

            log.info("LanHaiServiceImpl.preCreditCallBackByInfo info:{}", info);
            FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(new LambdaQueryWrapper<>(FundApiDictEntity.class)
                    .eq(FundApiDictEntity::getValue, info.getApplyId())
                    .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.PRE)
                    .eq(FundApiDictEntity::getCode, FundApiDictEnum.PRE_ORI_REQUEST_APPLY_NO)
                    .orderByDesc(FundApiDictEntity::getCreateTime)
                    .last("limit 1")
            );
            PreFundInfoEntity preFundInfo = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                    .eq(PreFundInfoEntity::getPreId, fundApiDictEntity.getLinkId())
                    .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                    .eq(PreFundInfoEntity::getDeleteFlag, 0)
            );
            log.info("LanHaiServiceImpl.preCreditCallBackByInfo fundApiDictEntity:{}", JSONUtil.toJsonStr(fundApiDictEntity));
            if (ObjUtil.isNull(fundApiDictEntity) && StrUtil.isEmpty(fundApiDictEntity.getValue())) {
                return request.setStatus("2");
            }
            Integer preId = fundApiDictEntity.getLinkId();

            String handlerStatus = info.getHandlerStatus();
            String message = "";
            PreFundResultEnum updatePreStatus = PreFundResultEnum.WAIT;
            if (StrUtil.equals(handlerStatus, "001")) {
                updatePreStatus = PreFundResultEnum.PASS;
            }
            if (StrUtil.equals(handlerStatus, "003") || StrUtil.equals(handlerStatus, "004")) {
                updatePreStatus = PreFundResultEnum.REJECT;
            }
            if (StrUtil.equals(handlerStatus, "002")) {
                updatePreStatus = PreFundResultEnum.WAIT;
            }
            log.info("LanHaiServiceImpl.preCreditCallBackByInfo updatePreStatus:{}", updatePreStatus);
            if (StrUtil.isNotEmpty(info.getStartTime()) && StrUtil.isNotEmpty(info.getEndTime())) {
                LocalDateTime now = LocalDateTime.now();
                // 定义日期格式（根据你的字符串格式调整）
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                Date date = new Date();
                DateTime start = DateUtil.parse(info.getStartTime(), formatter);
                DateTime end = DateUtil.parse(info.getEndTime(), formatter);
                log.info("LanHaiServiceImpl.preCreditCallBackByInfo start:{} end:{}, now:{}", start, end, now);
                if (DateUtil.date(date).isBefore(end)) {
                    preFundInfoMapper.update(new LambdaUpdateWrapper<PreFundInfoEntity>()
                            .eq(PreFundInfoEntity::getPreId, preId)
                            .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                            .eq(PreFundInfoEntity::getDeleteFlag, 0)
                            .set(PreFundInfoEntity::getExecuteRate, info.getExecuteRate().divide(BigDecimal.valueOf(100)))
                            .set(PreFundInfoEntity::getCreditContractEnd, end)
                            .set(PreFundInfoEntity::getUpdateTime, LocalDateTime.now())
                    );
                } else {
                    updatePreStatus = PreFundResultEnum.REJECT;
                }
            }
            if (ObjUtil.equals(PreFundResultEnum.PASS, updatePreStatus)) {
                message = "授信通过";
            } else if (ObjUtil.equals(PreFundResultEnum.REJECT, updatePreStatus)) {
                message = "授信拒绝";
            }
            if (StrUtil.isNotBlank(info.getErrMsg())) {
                message += " " + info.getErrMsg();
            }

            BigDecimal creditAmount = BigDecimal.ZERO;

            if (preFundInfo.getCreditAmount() != null) {
                creditAmount = preFundInfo.getCreditAmount();
            }

            preApproveFundStatusDTO = new PreApproveFundStatusDTO()
                    .setPreId(preId)
                    .setFundId(FundEnum.LAN_HAI.getValue())
                    .setStatus(updatePreStatus)
                    .setCreditAmt(creditAmount)
                    .setFailReason(message);
            log.info("LanHaiServiceImpl.preCreditCallBackByInfo preApproveFundStatusDTO: {}", JSONUtil.toJsonStr(preApproveFundStatusDTO));
            orderFeign.preUpdateFundStatus(preApproveFundStatusDTO);
            flag = true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            request.setStatus(flag ? "1" : "2");
        }

        return request;
    }

    /**
     * 构建授信申请
     *
     * @param fundFinalBaseDTO 基础信息
     */
    private LanHaiCreditApplyRequest buildCreditApply(FundFinalBaseDTO fundFinalBaseDTO) {
        log.info("LanHaiServiceImpl.buildCreditApply begin fundFinalNumber:{}", fundFinalBaseDTO.getFundFinalNumber());
        OrderAmountCalDTO amountCalDTO = fundBaseInfoService.getApplyAmountByOrderId(fundFinalBaseDTO.getOrderId());
        Assert.notNull(amountCalDTO, "订单金额不能为空");
        //获取门店信息
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(fundFinalBaseDTO.getOrderId());
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));

        CustomerBaseDTO customerPreBaseInfo = fundApproveMapper.getCustomerPreBaseInfo(fundFinalBaseDTO.getPreId());
        Assert.notNull(customerPreBaseInfo, () -> new BusinessException("客户信息不存在"));

        BigDecimal totalAmount = amountCalDTO.getTotalAmount();
        BigDecimal riskAmount = amountCalDTO.getRiskAmount();
        // 申请额度 = 总评额度和风控额度 最小值
        BigDecimal applyAmount = totalAmount.min(riskAmount);

        //取出返回的智能al报告的最终金额 取最小值
        RiskAiIntelligentAuditEntity riskAiIntelligentAudit = riskAiIntelligentAuditMapper.selectOne(new LambdaQueryWrapper<RiskAiIntelligentAuditEntity>()
                .eq(RiskAiIntelligentAuditEntity::getOrderId, fundFinalBaseDTO.getOrderId())
                .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
                .orderByDesc(RiskAiIntelligentAuditEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNotNull(riskAiIntelligentAudit) && ObjUtil.isNotNull(riskAiIntelligentAudit.getFinalAmount())){
            log.info("LanHaiServiceImpl.buildCreditApply riskAiIntelligentAudit.getFinalAmount:{} ", riskAiIntelligentAudit.getFinalAmount());
             applyAmount = totalAmount.min(riskAiIntelligentAudit.getFinalAmount());
        }
        LanHaiCreditApplyRequest creditApplyRequest = new LanHaiCreditApplyRequest();

        // 设置授信申请基础字段
        creditApplyRequest.setOperateType("1"); // 操作类型：新增
        creditApplyRequest.setBusiType("1"); // 业务类型
        creditApplyRequest.setProductId(lanHaiConfig.getProductId());
        creditApplyRequest.setLoanPurpose(LanHaiDictEnum.LoanPurposeEnum.getByCode(DictApplyPurposeEnum.fromCode(fundFinalBaseDTO.getApplyPurpose()), applyAmount)); //借款用途（汽车金融）
        creditApplyRequest.setUserType(ObjUtil.equals(fundFinalBaseDTO.getOrderType(), 0) ? LanHaiDictEnum.UserTypeEnum.PERSONAL : LanHaiDictEnum.UserTypeEnum.ENTERPRISE); // 客户类型（个人）
        creditApplyRequest.setGuaranteeType(LanHaiDictEnum.GuaranteeTypeEnum.OTHER_MORTGAGE_LOAN); //默认担保类别

        creditApplyRequest.setApplyAmount(applyAmount.intValue()); // 申请金额
        PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(
                new LambdaQueryWrapper<PreFundInfoEntity>()
                        .eq(PreFundInfoEntity::getPreId, orderInfo.getPreId())
                        .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .eq(PreFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(PreFundInfoEntity::getCreateTime)
                        .last("limit 1")
        );
        if (ObjUtil.isNull(preFundInfoEntity)){
            throw new BusinessException("未找到授信信息");
        }
//        creditApplyRequest.setCreditRate(fundFinalBaseDTO.getIrr());
        if(ObjUtil.isNotNull(preFundInfoEntity.getExecuteRate())){
            creditApplyRequest.setCreditRate(preFundInfoEntity.getExecuteRate());
        }
        creditApplyRequest.setApplyTerm(fundFinalBaseDTO.getTerm());
        creditApplyRequest.setApplyTermUnit(LanHaiDictEnum.TermUnitEnum.PERIOD); // 期限单位
        creditApplyRequest.setCreditContractNo(""); //附件类型中传“105”时必传查询合同编号

        LanHaiDictEnum.UserTypeEnum userType = creditApplyRequest.getUserType();
        // 设置个人客户信息
        LanHaiCreditApplyRequest.PersonalBaseInfo personalInfo = new LanHaiCreditApplyRequest.PersonalBaseInfo();
        personalInfo.setUserName(fundFinalBaseDTO.getName());
        personalInfo.setCertificateKind(LanHaiDictEnum.PersonalCertificateKindEnum.ID_CARD); // 证件类型（身份证）
        personalInfo.setCertificateNo(fundFinalBaseDTO.getIdNumber());
        personalInfo.setNation(dictUtils.getDictLabel(GlobalConstants.DictType.NATION.name(), fundFinalBaseDTO.getNation()));
        personalInfo.setBirthday(fundFinalBaseDTO.getBirthDate().replace("-", "")); // 格式化为yyyyMMdd
        personalInfo.setIssueDate(DateUtil.format(fundFinalBaseDTO.getValidityStartDate(), DatePattern.PURE_DATE_PATTERN));
        personalInfo.setExpireDate(StrUtil.equals("长期", fundFinalBaseDTO.getValidityEnd()) ? "99991231" : fundFinalBaseDTO.getValidityEnd().replace("-", ""));
        personalInfo.setMobileNo(fundFinalBaseDTO.getPhone());
        personalInfo.setGender(LanHaiDictEnum.GenderEnum.getByCode(fundFinalBaseDTO.getGender()));
        personalInfo.setMarriageStatus(LanHaiDictEnum.MarriageStatusEnum.getByCode(DictMaritalStatus.fromCode(fundFinalBaseDTO.getMaritalStatus())));
        personalInfo.setLiveAddress(fundFinalBaseDTO.getResidentialAddress() + fundFinalBaseDTO.getResidentialDetailedAddress());
        personalInfo.setNationality("中国");//固定 中国
        creditApplyRequest.setPersonalBaseInfo(personalInfo);

        // 设置职业信息
        LanHaiCreditApplyRequest.UserProfessionalInfo professionalInfo = new LanHaiCreditApplyRequest.UserProfessionalInfo();
        professionalInfo.setVocation(LanHaiDictEnum.VocationEnum.getByCode(DictOccupationEnum.fromCode(fundFinalBaseDTO.getVocational())));
        professionalInfo.setIncome(fundFinalBaseDTO.getMonthlyIncome());
        professionalInfo.setWorkName(fundFinalBaseDTO.getEnterpriseName());
        creditApplyRequest.setUserProfessionalInfo(professionalInfo);
        if (ObjUtil.equals(userType, LanHaiDictEnum.UserTypeEnum.ENTERPRISE)) {
            // 设置企业客户信息
            LanHaiCreditApplyRequest.EntBaseInfo entBaseInfo = new LanHaiCreditApplyRequest.EntBaseInfo();
            CompanyInfo companyInfo = fundBaseInfoService.getCompanyInfo(fundFinalBaseDTO.getOrderId());
            if (ObjUtil.isNull(companyInfo)) {
                log.info("LanHaiServiceImpl.buildCreditApply companyInfo is null fundFinalNumber:{}", fundFinalBaseDTO.getFundFinalNumber());
                throw new BusinessException("企业信息为空");
            }
            //企业信息不能为空
            entBaseInfo.setEnterpriseName(companyInfo.getCompanyName()); // 企业名称
            entBaseInfo.setEntCertificateKind(LanHaiDictEnum.OrgCertificateTypeEnum.UNIFIED_SOCIAL_CREDIT_CODE);
            entBaseInfo.setEntCertificateNo(companyInfo.getSocialCreditCode()); // 企业证件号码
            entBaseInfo.setLegalName(companyInfo.getLegalPerson()); // 法定代表人姓名
            entBaseInfo.setLegalCertificateKind(LanHaiDictEnum.PersonalCertificateKindEnum.ID_CARD); // 法人证件类型
            entBaseInfo.setLegalCertificateNo(companyInfo.getLegalCertificateNo()); // 法人证件号码
            entBaseInfo.setLegalMobileNo(companyInfo.getLegalMobileNo()); // 法人手机号码
            creditApplyRequest.setEntBaseInfo(entBaseInfo);

        }


        // 设置联系人信息（至少两个）
        List<LanHaiCreditApplyRequest.Contact> contacts = fundFinalBaseDTO.getContactPersonList()
                .stream()
                .map(contact -> {
                    LanHaiCreditApplyRequest.Contact c = new LanHaiCreditApplyRequest.Contact();
                    c.setName(contact.getContactName());
                    c.setRelation(LanHaiDictEnum.RelationTypeEnum.getByCode(DictContactRelation.fromCode(contact.getRelation()))); // 关系类型（如亲属、同事）
                    c.setMobileNo(contact.getContactPhone());
//                    c.setCertificateNo(contact.getIdNumber());
                    return c;
                })
                .toList();
        creditApplyRequest.setContacts(contacts);

        // 设置绑定账户信息（银行卡）
        List<Integer> signCardIds = new ArrayList<>();
        signCardIds.add(7);
        signCardIds.add(9);
        List<BankAccountSignEntity> signCards = fundApproveMapper.getSignCardList(fundFinalBaseDTO.getOrderId(), signCardIds);
        if (CollUtil.isEmpty(signCards)) {
            log.info("LanHaiServiceImpl.buildCreditApply signCards is null fundFinalNumber:{}", fundFinalBaseDTO.getFundFinalNumber());
            throw new BusinessException("未找到绑定银行卡信息");
        }
        BankAccountSignEntity bankAccountSign = signCards.get(0);
        LanHaiCreditApplyRequest.BindAcctInfo bindAcctInfo = new LanHaiCreditApplyRequest.BindAcctInfo();
        bindAcctInfo.setBindAcctNo(bankAccountSign.getBankCardNumber());
        bindAcctInfo.setBindAcctName(bankAccountSign.getName());
        bindAcctInfo.setBindAcctBankName(bankAccountSign.getBankNameUpdate());
        bindAcctInfo.setBankMobile(bankAccountSign.getPhone());
        bindAcctInfo.setBindAcctFlag(LanHaiDictEnum.AccountFlagEnum.PRIVATE_ACCOUNT);//账户类型
        bindAcctInfo.setBankCardVerifyResult("1");//银行卡四要素结果
        creditApplyRequest.setBindAcctInfo(bindAcctInfo);

        // 设置车辆信息
        LanHaiCreditApplyRequest.VehicleInfo vehicleInfo = new LanHaiCreditApplyRequest.VehicleInfo();
        vehicleInfo.setVehBrand(fundFinalBaseDTO.getBrand());
        vehicleInfo.setVehType(fundFinalBaseDTO.getVehicleType());
        vehicleInfo.setVehColor(fundFinalBaseDTO.getVehicleColor());
        vehicleInfo.setVehOperation(LanHaiDictEnum.VehOperationTypeEnum.convert(fundFinalBaseDTO.getNatureOfUse()));
        vehicleInfo.setVehNumber(fundFinalBaseDTO.getVehicleNumber());
        vehicleInfo.setFrameNumber(fundFinalBaseDTO.getVin());
        vehicleInfo.setVehOwner(fundFinalBaseDTO.getName());
        vehicleInfo.setInitRegDate(fundFinalBaseDTO.getRegisterDate() != null ? DateUtil.parse(fundFinalBaseDTO.getRegisterDate()) : null);
        vehicleInfo.setVehMileage(new BigDecimal(fundFinalBaseDTO.getMileage()));
        vehicleInfo.setEvaInstitution("龙环汇丰");// 评估机构 固定 龙环汇丰
        vehicleInfo.setAssessDate(fundFinalBaseDTO.getUpdateTime());// 评估日期
        vehicleInfo.setValuePrice(fundFinalBaseDTO.getEstimatedValue()); //评估价
        vehicleInfo.setPurchAmount(fundFinalBaseDTO.getGuidePrice().multiply(BigDecimal.valueOf(10000))); //购买金额

        BigDecimal braeakRate = amountCalDTO.getSoftReviewAmount().divide(fundFinalBaseDTO.getGuidePrice().multiply(BigDecimal.valueOf(10000)), 4, RoundingMode.HALF_UP);
        vehicleInfo.setBraeakRate(braeakRate.toString());//折损率 软评额度/厂商指导价
        vehicleInfo.setLicenseLocal(fundFinalBaseDTO.getLicenseProvince() + fundFinalBaseDTO.getLicenseCity());
        String storeName = userFeign.getDeptById(orderInfo.getDeptId()).getData();
        StoreAddressInfoEntity storeAddressInfo = fundApproveMapper.getStoreAddressInfo(storeName);
        Assert.notNull(storeAddressInfo, () -> new BusinessException("门店地址信息不存在"));
        vehicleInfo.setAcceptPlace(storeAddressInfo.getProvinceName() + storeAddressInfo.getCityName() + storeAddressInfo.getDetail());//受理地 门店地址
        vehicleInfo.setIfUsedCar("0");// 是否为二手车辆 固定为0
        vehicleInfo.setIfMortgage("1");//是否按揭 固定为0
        vehicleInfo.setPayRatio("100");// 首付比例 100%
        vehicleInfo.setTransferCnt(new BigDecimal(fundFinalBaseDTO.getTransferTimes()));
        vehicleInfo.setCarAge(fundFinalBaseDTO.getVehicleAge().toString());
        vehicleInfo.setCarLimit(fundFinalBaseDTO.getSeatCount().toString());//车辆准载限制 座位数
        creditApplyRequest.setVehicleInfo(List.of(vehicleInfo));

        //合作方审批信息 固定通过
        LanHaiCreditApplyRequest.ApproveInfo approveInfo = new LanHaiCreditApplyRequest.ApproveInfo();
        approveInfo.setApproveResult("0");
        approveInfo.setApproveOpini("通过");
        approveInfo.setApproveAmount(applyAmount);
        approveInfo.setApproveDate(DateUtil.date());
        creditApplyRequest.setApproveInfo(approveInfo);


        //附件列表
        List<LanHaiCreditApplyRequest.Attachments> attachmentsList = new ArrayList<>();
        List<LanHaiImageUploadResponse.FileList> fileList = imageUpload(new FundResourceDTO()
                .setRongDanId(orderInfo.getRongdanId())
                .setFund(FundEnum.LAN_HAI)
                .setType(2)
                .setLinkId(fundFinalBaseDTO.getOrderId())
                .setPreId(fundFinalBaseDTO.getPreId())
                .setCustomerBaseInfo(new FundResourceDTO.CustomerBaseInfo().setPhone(fundFinalBaseDTO.getPhone())
                        .setCustomerIdNo(fundFinalBaseDTO.getIdNumber())
                )
        );

        for (LanHaiImageUploadResponse.FileList item : fileList) {
            LanHaiCreditApplyRequest.Attachments attachments = new LanHaiCreditApplyRequest.Attachments();
            attachments.setFileKind(item.getFileKind());
            attachments.setFileName(item.getFileName());
            attachmentsList.add(attachments);
        }
        creditApplyRequest.setAttachments(attachmentsList);

        //设备信息
        LanHaiCreditApplyRequest.DeviceInfo deviceInfo = new LanHaiCreditApplyRequest.DeviceInfo();
        deviceInfo.setDeviceIp(RandomShandongIPUtils.getIp()); //设备IP 对应随机山东ip
        creditApplyRequest.setDeviceInfo(deviceInfo);

        //扩展信息
        LanHaiCreditApplyRequest.ExtraData extraData = new LanHaiCreditApplyRequest.ExtraData();
//        extraData.setLoanRate(fundFinalBaseDTO.getYearRate());s
        extraData.setLoanRate(fundFinalBaseDTO.getIrr());
        //还款来源：个人：30万以下包含30万传1
        //还款来源：个体工商、企业传3
        String customerType = customerPreBaseInfo.getCustomerType();
        LanHaiDictEnum.RepaySourceEnum repaymentResource = StrUtil.equals(customerType, "1") && ObjUtil.equals(fundFinalBaseDTO.getOrderType(), 0) ? LanHaiDictEnum.RepaySourceEnum.SALARY_BONUS : LanHaiDictEnum.RepaySourceEnum.OPERATING_INCOME;
        extraData.setRepaymentResource(repaymentResource);
        String residenceTime = null;
        String startResideDate = fundFinalBaseDTO.getStartResideDate();
        if (StrUtil.isBlank(startResideDate)) {
            throw new BusinessException("起居时间不能为空");
        }
        Date resideStartDate = DateUtil.parse(startResideDate);
        // 计算居住时长（月）
        long months = DateUtil.betweenMonth(resideStartDate, new Date(), true);

        if (months >= 12) {
            // 居住时间已满一年
            residenceTime = "Y";
        } else {
            residenceTime = "N";
        }
        extraData.setResidenceTime(residenceTime);// 根据起居时间判断是否满一年
        extraData.setCreditBusiKind(ObjUtil.equals(fundFinalBaseDTO.getOrderType(), 0) ? "1" : "2");
        creditApplyRequest.setExtraData(extraData);

        LanHaiCreditApplyRequest.AddressLabelInfo addressLabelInfo = new LanHaiCreditApplyRequest.AddressLabelInfo();
        addressLabelInfo.setCustomerAreaName("山东省");
        addressLabelInfo.setCustomerAreaResult("370000");
        addressLabelInfo.setCustomerAreaType(LanHaiDictEnum.JudgmentDimensionEnum.IP_ADDRESS);
        addressLabelInfo.setCustomerAreaValue(RandomShandongIPUtils.getIp());

        creditApplyRequest.setAddressLabelInfoList(List.of(addressLabelInfo));


        log.info("LanHaiServiceImpl.buildCreditApply creditApplyRequest:{}", JSONUtil.toJsonStr(creditApplyRequest));
        return creditApplyRequest;
    }


    /**
     * 构建贷款申请
     */
    private LanHaiLoanApplyRequest loanApplyBuild(FundFinalBaseDTO fundFinalBaseDTO) {
        Integer orderId = fundFinalBaseDTO.getOrderId();
        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );

        if (finalFundInfoEntity == null) {
            log.info("LanHaiServiceImpl.loanApplyBuild finalFundInfoEntity is null orderId:{}", orderId);
            throw new BusinessException("获取授信信息失败");
        }

        OrderAmountCalDTO amountCalDTO = fundBaseInfoService.getApplyAmountByOrderId(fundFinalBaseDTO.getOrderId());
        Assert.notNull(amountCalDTO, "订单金额不能为空");

        CustomerBaseDTO customerPreBaseInfo = fundApproveMapper.getCustomerPreBaseInfo(fundFinalBaseDTO.getPreId());
        Assert.notNull(customerPreBaseInfo, () -> new BusinessException("客户信息不存在"));
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));


        String templateCodeLoan = "LH_LH_0004";//蓝海-汽车金融借款合同
        FileTemplateInfoEntity fileTemplateInfoLoan = fileTemplateInfoMapper.selectOne(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getTemplateNumber, templateCodeLoan)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .eq(FileTemplateInfoEntity::getType, 1)
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FileTemplateInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(fileTemplateInfoLoan)) {
            log.info("LanHaiServiceImpl.loanApplyBuild orderId:{} templateCodeLoan:{}", orderId, templateCodeLoan);
            throw new BusinessException("合同模板不存在");
        }
        OrderContractEntity orderContractEntityLoan = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getTemplateId, fileTemplateInfoLoan.getId())
                .eq(OrderContractEntity::getSignStatus, 2)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .orderByDesc(OrderContractEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderContractEntityLoan)) {
            log.info("LanHaiServiceImpl.loanApplyBuild orderId:{} fileTemplateInfoLoan:{}", orderId, JSONUtil.toJsonStr(fileTemplateInfoLoan));
            throw new BusinessException("未查询到合同相关信息");
        }

        BigDecimal customerConfirmAmount = amountCalDTO.getCustomerConfirmAmount();

        LanHaiLoanApplyRequest response = new LanHaiLoanApplyRequest();

        // 必填字段赋值
        response.setUserId(finalFundInfoEntity.getFundUserId());
        response.setOperateType("1"); // 操作类型(1-提交)
        response.setCreditLimitId(finalFundInfoEntity.getCreditNo());
        response.setLoanPurpose(LanHaiDictEnum.LoanPurposeEnum.getByCode(DictApplyPurposeEnum.fromCode(fundFinalBaseDTO.getApplyPurpose()), customerConfirmAmount)); //借款用途（汽车金融）
        response.setLoanPeriod(fundFinalBaseDTO.getTerm());
        response.setLoanUnit(LanHaiDictEnum.TermUnitEnum.PERIOD);
        response.setLoanAmount(customerConfirmAmount);
        response.setRepayment(LanHaiDictEnum.RepayMethodEnum.EQUAL_INSTALLMENTS);// 还款方式
        response.setLoanContractNo(orderContractEntityLoan.getNumber());//合同编号
        response.setPayFlag("1");//支付标识

        String customerType = customerPreBaseInfo.getCustomerType();
        response.setIndividualBusiness(LanHaiDictEnum.EnumBooleanEnum.NO);//个体工商户标识

        // 设置借款账户信息
        List<Integer> signCardIds = new ArrayList<>();
        signCardIds.add(7);
        signCardIds.add(9);
        List<BankAccountSignEntity> signCards = fundApproveMapper.getSignCardList(fundFinalBaseDTO.getOrderId(), signCardIds);
        if (CollUtil.isEmpty(signCards)) {
            log.info("LanHaiServiceImpl.loanApplyBuild signCards is null fundFinalNumber:{}", fundFinalBaseDTO.getFundFinalNumber());
            throw new BusinessException("未找到绑定银行卡信息");
        }
        BankAccountSignEntity bankAccountSign = signCards.get(0);
        LanHaiLoanApplyRequest.LoanAcctInfo loanAcctInfo = new LanHaiLoanApplyRequest.LoanAcctInfo();
        loanAcctInfo.setLoanAcctNo(bankAccountSign.getBankCardNumber());
        loanAcctInfo.setLoanAcctName(bankAccountSign.getName());
        loanAcctInfo.setLoanAcctBankName(bankAccountSign.getBankNameUpdate());
        loanAcctInfo.setLoanAcctFlag(LanHaiDictEnum.AccountFlagEnum.PRIVATE_ACCOUNT);
        loanAcctInfo.setBankMobile(bankAccountSign.getPhone());
        loanAcctInfo.setBankCardVerifyResult("1");
        response.setLoanAcctInfo(loanAcctInfo);

        // 设置还款账户信息对象(非必填字段)
        LanHaiLoanApplyRequest.RepayAcctInfo repayAcctInfo = new LanHaiLoanApplyRequest.RepayAcctInfo();
        repayAcctInfo.setRepayAcctId(null);
        repayAcctInfo.setRepayAcctNo(bankAccountSign.getBankCardNumber());
        repayAcctInfo.setRepayAcctName(bankAccountSign.getName());
        repayAcctInfo.setRepayAcctBankName(bankAccountSign.getBankNameUpdate());
        repayAcctInfo.setRepayAcctFlag(LanHaiDictEnum.AccountFlagEnum.PRIVATE_ACCOUNT);
        response.setRepayAcctInfo(repayAcctInfo);

        // 设置受托收款账户信息对象
        if (StrUtil.equals(response.getPayFlag(), "2")) {
            LanHaiLoanApplyRequest.RcvAcctInfo rcvAcctInfo = new LanHaiLoanApplyRequest.RcvAcctInfo();
            rcvAcctInfo.setRcvAcctNo(bankAccountSign.getBankCardNumber());
            rcvAcctInfo.setRcvAcctName(bankAccountSign.getName());
            rcvAcctInfo.setRcvAcctBankNo(bankAccountSign.getOpenBankNumber());
            rcvAcctInfo.setRcvAcctBankName(bankAccountSign.getBankName());
            rcvAcctInfo.setRcvAcctFlag(LanHaiDictEnum.AccountFlagEnum.PRIVATE_ACCOUNT);
            response.setRcvAcctInfo(rcvAcctInfo);
        }

        // 设置附件列表
        List<LanHaiLoanApplyRequest.Attachment> attachmentsList = new ArrayList<>();
        List<LanHaiImageUploadResponse.FileList> fileList = imageUpload(new FundResourceDTO().setFund(FundEnum.LAN_HAI)
                .setType(3).setLinkId(fundFinalBaseDTO.getOrderId()).setPreId(fundFinalBaseDTO.getPreId())
                .setRongDanId(orderInfo.getRongdanId())
                .setCustomerBaseInfo(new FundResourceDTO.CustomerBaseInfo().setPhone(fundFinalBaseDTO.getPhone())
                        .setCustomerIdNo(fundFinalBaseDTO.getIdNumber())
                )
        );

        for (LanHaiImageUploadResponse.FileList item : fileList) {
            LanHaiLoanApplyRequest.Attachment attachments = new LanHaiLoanApplyRequest.Attachment();
            attachments.setFileKind(item.getFileKind());
            attachments.setFileName(item.getFileName());
            attachmentsList.add(attachments);
        }
        response.setAttachments(attachmentsList);

        // 设置设备信息对象
        LanHaiLoanApplyRequest.DeviceInfo deviceInfo = new LanHaiLoanApplyRequest.DeviceInfo();
        deviceInfo.setDeviceIp(RandomShandongIPUtils.getIp());
        response.setDeviceInfo(deviceInfo);

        // 设置扩展信息对象
        BankAccountSignEntity bankAccountSignYiBao = signCards.stream().filter(item -> ObjUtil.equals(item.getSignPlate(), SignPlateEnum.YI_BAO)).findFirst().orElse( null);

        LanHaiLoanApplyRequest.ExtraData extraData = new LanHaiLoanApplyRequest.ExtraData();
//        extraData.setCreditRate(fundFinalBaseDTO.getIrr());
//        extraData.setLoanRate(fundFinalBaseDTO.getYearRate());
        if(ObjUtil.isNotNull(finalFundInfoEntity.getExecuteRate())){
            extraData.setCreditRate(finalFundInfoEntity.getExecuteRate());
            extraData.setLoanRate(fundFinalBaseDTO.getIrr());
        }else {
            PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(
                    new LambdaQueryWrapper<PreFundInfoEntity>()
                            .eq(PreFundInfoEntity::getPreId, orderInfo.getPreId())
                            .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                            .eq(PreFundInfoEntity::getDeleteFlag, 0)
                            .orderByDesc(PreFundInfoEntity::getCreateTime)
                            .last("limit 1")
            );
            if (ObjUtil.isNull(preFundInfoEntity)){
                throw new BusinessException("未找到授信信息");
            }
            extraData.setCreditRate(preFundInfoEntity.getExecuteRate());
            extraData.setLoanRate(fundFinalBaseDTO.getIrr());
        }
        extraData.setMobileNo(fundFinalBaseDTO.getPhone());

        //易宝
        if (ObjUtil.isNotNull(bankAccountSignYiBao)) {
            extraData.setDeductionMode("2");
            List<LanHaiLoanApplyRequest.DeductionInfo> deductionInfoList = new ArrayList<>();

            LanHaiLoanApplyRequest.DeductionInfo deductionInfo = new LanHaiLoanApplyRequest.DeductionInfo();
            deductionInfo.setFixDepartmentId("yeepayHbzf-WithHold");
            deductionInfo.setContractNo(bankAccountSignYiBao.getSignProtocolNo());
            deductionInfoList.add(deductionInfo);
            response.setDeductionInfo(deductionInfoList);
        }
        CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                .last("LIMIT 1"));
        //判断是否为线上抵押
        if (ObjUtil.isNotNull(customerMortgageInfoEntity) && Objects.equals(customerMortgageInfoEntity.getMortgageType(), 0)) {
            OrderArrivedEntity orderArrivedEntity = orderArrivedMapper.selectOne(new LambdaQueryWrapper<OrderArrivedEntity>()
                    .eq(OrderArrivedEntity::getOrderId, orderId)
                    .eq(OrderArrivedEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderArrivedEntity::getCreateTime)
                    .last("LIMIT 1")
            );
            if (ObjUtil.isNull(orderArrivedEntity)) {
                throw new BusinessException("未找到订单办抵信息");
            }
            if (ObjUtil.isNull(orderArrivedEntity.getArrivedId())) {
                throw new BusinessException("未找到订单抵押权人信息");
            }
            ArrivedDataEntity arrivedDataEntity = arrivedDataMapper.selectById(orderArrivedEntity.getArrivedId());
            if (ObjUtil.isNull(arrivedDataEntity)) {
                throw new BusinessException("未查询到订单抵押权人信息");
            }
            Integer managerId = orderInfo.getManagerId();
            Result<String> idNumberCardByUserId = userFeign.getIdNumberCardByUserId(managerId);
            if (!Result.isSuccess(idNumberCardByUserId) || StrUtil.isBlank(idNumberCardByUserId.getData())){
                throw new BusinessException("未查询到业务员身份证");
            }
            extraData.setAgentCertNo(idNumberCardByUserId.getData());
            extraData.setMortgageType("1");
        } else {
            extraData.setMortgageType("2");

        }
        response.setExtraData(extraData);

        // 设置地址标签信息对象
        LanHaiLoanApplyRequest.AddressLabelInfo addressLabelInfo = new LanHaiLoanApplyRequest.AddressLabelInfo();
        addressLabelInfo.setCustomerAreaName("山东省");
        addressLabelInfo.setCustomerAreaResult("370000");
        addressLabelInfo.setCustomerAreaType(LanHaiDictEnum.JudgmentDimensionEnum.IP_ADDRESS);
        addressLabelInfo.setCustomerAreaValue(RandomShandongIPUtils.getIp());
        response.setAddressLabelInfoList(List.of(addressLabelInfo));

        //设置共借人信息
        LanHaiLoanApplyRequest.Coborrower coborrower = new LanHaiLoanApplyRequest.Coborrower();
        coborrower.setCoborrowerFlag("1");
        response.setCoborrower(coborrower);

        // 企业客户信息
        LanHaiLoanApplyRequest.EntBaseInfo entBaseInfo = new LanHaiLoanApplyRequest.EntBaseInfo();
        if (ObjUtil.equals(fundFinalBaseDTO.getOrderType(), 1)) {
            // 设置企业客户信息
            CompanyInfo companyInfo = fundBaseInfoService.getCompanyInfo(fundFinalBaseDTO.getOrderId());
            if (ObjUtil.isNull(companyInfo)) {
                log.info("LanHaiServiceImpl.loanApplyBuild companyInfo is null fundFinalNumber:{}", fundFinalBaseDTO.getFundFinalNumber());
                throw new BusinessException("企业信息为空");
            }
            //企业信息不能为空
            entBaseInfo.setEnterpriseName(companyInfo.getCompanyName()); // 企业名称
            entBaseInfo.setBusinessCode(companyInfo.getSocialCreditCode());
            entBaseInfo.setIndustryCode(companyInfo.getIndustryCode());

            //判断前两位是否为 92
            if (StrUtil.startWith(companyInfo.getSocialCreditCode(), "92")) {
                response.setIndividualBusiness(LanHaiDictEnum.EnumBooleanEnum.YES);//个体工商户标识
            } else {
                response.setIndividualBusiness(LanHaiDictEnum.EnumBooleanEnum.NO);//其他
            }

        }
        response.setEntBaseInfo(entBaseInfo);

        // 融担公司
        ProductRongdanEntity productRongdanEntity = productRongdanMapper.selectById(orderInfo.getRongdanId());
        if (ObjUtil.isNull(productRongdanEntity)) {
            log.info("LanHaiServiceImpl.loanApplyBuild orderId:{} productRongdanEntity:{}", orderId, JSONUtil.toJsonStr(productRongdanEntity));
            throw new BusinessException("未查询到融担公司相关信息");
        }
        // 设置担保公司明细列表

        List<String> templateCodeRongDan = new ArrayList<>();
        if (StrUtil.equals(ProductRongDanEnum.ZUN_HAO.getCode(), productRongdanEntity.getMiddleCode())) {
            templateCodeRongDan.add("LH_LH_0007");//蓝海-个人借款委托担保合同-樽昊
            templateCodeRongDan.add("LH_LH_0011");//蓝海-个人借款委托担保合同-樽昊-新产品
        }
        if (StrUtil.equals(ProductRongDanEnum.YIN_DING.getCode(), productRongdanEntity.getMiddleCode())) {
            templateCodeRongDan.add("LH_LH_0015");//蓝海-个人借款委托担保合同-樽昊-新产品
        }
        if (CollUtil.isEmpty(templateCodeRongDan)) {
            throw new BusinessException("未查询到融担公司相关模板");
        }
        List<FileTemplateInfoEntity> fileTemplateInfoRongDanList = fileTemplateInfoMapper.selectList(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .in(FileTemplateInfoEntity::getTemplateNumber, templateCodeRongDan)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .eq(FileTemplateInfoEntity::getType, 1)
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FileTemplateInfoEntity::getCreateTime)
        );
        if (CollUtil.isEmpty(fileTemplateInfoRongDanList)) {
            log.info("ContractServiceImpl.LoanFailDownLoad orderId:{} templateCodeRongDan:{}", orderId, templateCodeRongDan);
            throw new BusinessException("合同模板不存在");
        }
        //获取idList
        List<Integer> idList = fileTemplateInfoRongDanList.stream().map(FileTemplateInfoEntity::getId).collect(Collectors.toList());
        OrderContractEntity orderContractEntityRongDan = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, orderId)
                .in(OrderContractEntity::getTemplateId, idList)
                .eq(OrderContractEntity::getSignStatus, 2)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .orderByDesc(OrderContractEntity::getCreateTime)
                .last("LIMIT 1")
        );
        if (ObjUtil.isNull(orderContractEntityRongDan)) {
            log.info("LanHaiServiceImpl.loanApplyBuild orderId:{} fileTemplateInfoRongDanList:{}", orderId, JSONUtil.toJsonStr(fileTemplateInfoRongDanList));
            throw new BusinessException("未查询到合同相关信息");
        }

        String templateCodeMortgage = null;
        templateCodeMortgage = "LH_LH_0003";// 蓝海-汽车金融抵押合同
        FileTemplateInfoEntity fileTemplateInfoMortgage = fileTemplateInfoMapper.selectOne(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getTemplateNumber, templateCodeMortgage)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .eq(FileTemplateInfoEntity::getType, 1)
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FileTemplateInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(fileTemplateInfoMortgage)) {
            log.info("ContractServiceImpl.LoanFailDownLoad orderId:{} templateCodeMortgage:{}", orderId, templateCodeMortgage);
            throw new BusinessException("合同模板不存在");
        }
        OrderContractEntity orderContractEntityMortgage = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getTemplateId, fileTemplateInfoMortgage.getId())
                .eq(OrderContractEntity::getSignStatus, 2)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .orderByDesc(OrderContractEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderContractEntityMortgage)) {
            log.info("LanHaiServiceImpl.loanApplyBuild orderId:{} fileTemplateInfoMortgage:{}", orderId, JSONUtil.toJsonStr(fileTemplateInfoMortgage));
            throw new BusinessException("未查询到合同相关信息");
        }


        //融担公司担保合同签署时间
        LocalDate signDateRongDan = orderContractEntityRongDan.getUpdateTime().toLocalDate();
        //借款人担保合同签署时间
        LocalDate signDateMortgage = orderContractEntityMortgage.getUpdateTime().toLocalDate();


        LanHaiLoanApplyRequest.GuaranteeCompanyInfo guaranteeCompanyInfo = new LanHaiLoanApplyRequest.GuaranteeCompanyInfo();
        guaranteeCompanyInfo.setGuaranteeAccount(productRongdanEntity.getAccount());
        guaranteeCompanyInfo.setGuaranteeAccountName(productRongdanEntity.getCompanyName());
        response.setGuaranteeCompanyInfo(guaranteeCompanyInfo);

        //融担公司配置
        String configJson = productRongdanEntity.getConfigJson();
        JSONObject configJsonObject = JSON.parseObject(configJson);
        Optional.ofNullable(configJsonObject).orElseThrow(() -> new BusinessException("未查询到融单公司配置信息"));
        // 创建担保公司明细对象（保证合同）
        LanHaiLoanApplyRequest.GuaranteeCompayDetail guaranteeCompanyDetail = new LanHaiLoanApplyRequest.GuaranteeCompayDetail();

        guaranteeCompanyDetail.setGuaranteeName(productRongdanEntity.getCompanyName()); //融担公司名称

        guaranteeCompanyDetail.setGuaranteeCertificateKind(configJsonObject.getString("guaranteeCertificateKind")); // 担保人证件类型

        guaranteeCompanyDetail.setGuaranteeCertificateNo(productRongdanEntity.getSocialCode()); // 担保人证件号码：统一社会信用代码（固定值）

        guaranteeCompanyDetail.setGuaranteeRatio(configJsonObject.getString("guaranteeRatio")); // 担保比例：

        guaranteeCompanyDetail.setGuaranteeContractKind("001"); // 担保合同类型：001 一般担保合同（固定值）

        guaranteeCompanyDetail.setGuaranteeKind("003"); // 担保类型：003 保证（固定值）


        guaranteeCompanyDetail.setGuaranteeContractNo(orderContractEntityRongDan.getNumber()); // 合同编号

        guaranteeCompanyDetail.setGuaranteeStartDate(signDateRongDan); // 担保起始日期9

        guaranteeCompanyDetail.setGuaranteeEndDate(signDateRongDan
                .plusMonths(fundFinalBaseDTO.getTerm())             // 加贷款期限（月）
                .plusDays(5)                      // 加5天宽限期
                .plusYears(2)); // 担保到期日期 申款日+贷款期限(月)+5天宽期限+2年

        guaranteeCompanyDetail.setGuaranteeOrg("C01"); // 担保人国民经济部门：C01（保证合同填）（固定值）

        guaranteeCompanyDetail.setGuaranteeIndustryCode("J69"); // 担保人行业：J69（保证合同填）（固定值）

        guaranteeCompanyDetail.setGuaranteeAreaCode(configJsonObject.getString("guaranteeAreaCode")); // 担保人地区代码

        guaranteeCompanyDetail.setGuaranteeEnterpriseScale(configJsonObject.getString("guaranteeEnterpriseScale")); // 担保人企业规模：

        guaranteeCompanyDetail.setGuaranteeComKind("001"); // 担保人类型：001 专业担保机构（固定值）

        guaranteeCompanyDetail.setGuaranteeAmt(amountCalDTO.getCustomerConfirmAmount().toString()); // 担保金额

        guaranteeCompanyDetail.setGuaranteeStatus("有效"); // 担保合同状态：有效（固定值）

        guaranteeCompanyDetail.setJointGuarantee("0"); // 联保标志：0（固定值）


        // 创建抵押合同信息对象（抵押合同）
        LanHaiLoanApplyRequest.GuaranteeCompayDetail mortgageInfo = new LanHaiLoanApplyRequest.GuaranteeCompayDetail();

        mortgageInfo.setGuaranteeName(fundFinalBaseDTO.getName()); // 借款人姓名

        mortgageInfo.setGuaranteeCertificateKind("110001"); // 担保人证件类型：110001（身份证）（固定值）

        mortgageInfo.setGuaranteeCertificateNo(fundFinalBaseDTO.getIdNumber()); // 担保人证件号码：身份证号（

        mortgageInfo.setGuaranteeRatio("1"); // 担保比例：1（固定值）

        mortgageInfo.setGuaranteeContractKind("001"); // 担保合同类型：001 一般担保合同（固定值）

        mortgageInfo.setGuaranteeKind("001"); // 担保类型：001 抵押（固定值）

        mortgageInfo.setGuaranteeCurrency("CNY"); // 担保币种：CNY（固定值）


        mortgageInfo.setGuaranteeContractNo(orderContractEntityMortgage.getNumber()); // 合同编号（非固定值，动态赋值）

        mortgageInfo.setGuaranteeStartDate(signDateMortgage); // 担保起始日期 合同签署日期

        mortgageInfo.setGuaranteeEndDate(signDateMortgage
                .plusMonths(fundFinalBaseDTO.getTerm())             // 加贷款期限（月）
                .plusDays(5)                      // 加5天宽限期
                .plusYears(2));// 担保到期日期（非固定值，动态赋值）

        mortgageInfo.setGuaranteeOrg("D01"); // 担保人国民经济部门：D01（抵押合同填）（固定值）

        mortgageInfo.setGuaranteeIndustryCode("100"); // 担保人行业：100（抵押合同填）（固定值）

        mortgageInfo.setGuaranteeComKind("002"); // 担保人类型：002 保证人（固定值）

        mortgageInfo.setGuaranteeAmt(amountCalDTO.getCustomerConfirmAmount().toString()); // 担保金额

        mortgageInfo.setGuaranteeStatus("有效"); // 担保合同状态：有效（固定值）

        mortgageInfo.setJointGuarantee("0"); // 联保标志：0（固定值）

        mortgageInfo.setCollateralOwer(fundFinalBaseDTO.getIdNumber()); // 抵质押所属人

        mortgageInfo.setCollateralCount(1); // 抵押物个数：1（固定值）

        mortgageInfo.setCollateralKind("13"); // 抵押物种类：13 交通运输设备（固定值）

        mortgageInfo.setCollateralNoKind("4"); // 抵押物识别号类型：4 交通工具发动机号（固定值）

        mortgageInfo.setCollateralUniqueNo(fundFinalBaseDTO.getEngineNumber()); // 抵押物唯一识别号

        mortgageInfo.setCollateralEstimateAmt(getCollateralCarAmt(fundFinalBaseDTO.getVehicleNumber(),amountCalDTO).toString()); // 抵押物评估价值

        mortgageInfo.setEstimateKind("2"); // 评估机构类型：2 第三方机构（固定值）

        mortgageInfo.setCollateralEstimateDate(fundFinalBaseDTO.getUpdateTime()); // 抵押物评估日期

        mortgageInfo.setMortgagorKind("1"); // 抵押人身份类别：1 自然人（固定值）

        mortgageInfo.setCollateralNo(lanHaiConfig.getProductId() + DateUtil.currentSeconds()); // 押品编号 按产品名称缩写+放款时间戳格式生成，参考格式如下：“CXDSQCDD2023070300001”

        mortgageInfo.setCollateralType("4.3交通运输设备"); // 押品类型：4.3交通运输设备（固定值）

        mortgageInfo.setCollateralName("机动车辆"); // 押品名称：机动车辆（固定值）

        mortgageInfo.setCollateralStatus("正常"); // 抵质押物状态：正常（固定值）

        mortgageInfo.setCollateralCurrentAmt(getCollateralCarAmt(fundFinalBaseDTO.getVehicleNumber(),amountCalDTO).toString()); // 最新估值

        mortgageInfo.setRegistrationNo(fundFinalBaseDTO.getRegistrationCode()); // 权证登记号码
        response.setGuaranteeCompayDetail(List.of(guaranteeCompanyDetail, mortgageInfo));

        return response;
    }

    /**
     * 根据车牌取抵押车辆估值
     * 支用接口抵押类担保信息中：
     * 非京沪牌或者是京B,沪 C
     * 押品初始估值（collateralEstimateAmt）-----车300软评额度、
     * 最新估值（collateralCurrentAmt）-----车300软评额度，
     * 抵押合同中车辆价值-----车300软评额度
     * 京沪牌，京B沪 C除外
     * 押品初始估值（collateralEstimateAmt）-----预审批额度、
     * 最新估值（collateralCurrentAmt）-----预审批额度，
     * 抵押合同中车辆价值-----预审批额度
     *
     */
    private BigDecimal getCollateralCarAmt(String vehicleNumber,OrderAmountCalDTO amountCalDTO) {
        //车300软评额度
        BigDecimal softReviewAmount = amountCalDTO.getSoftReviewAmount();
        //预审批额度
        BigDecimal preApprovalAmount = amountCalDTO.getPreAmount();

        // 判断车牌归属地
        if (StringUtils.isNotBlank(vehicleNumber)) {
            String prefix = vehicleNumber.substring(0, 2);
            // 京沪牌，但排除京B和沪C
            if (("京".equals(prefix.substring(0, 1)) || "沪".equals(prefix.substring(0, 1)))
                    && !("京B".equals(prefix) || "沪C".equals(prefix))) {
                return preApprovalAmount;
            }
        }

        // 非京沪牌或者是京B,沪C
        return softReviewAmount;
    }


    /**
     * 上传影像文件
     */
    private List<LanHaiImageUploadResponse.FileList> imageUpload(FundResourceDTO dto) {
        return lanHaiExpandService.imageUpload(dto);
    }

    /**
     * 3.1.5.6. 预授信结果查证（如需）
     */
    @Override
    public void preApproveQueryV2ByPreId(Integer preId) {
        PreFundInfoEntity preFundInfo = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                .eq(PreFundInfoEntity::getPreId, preId)
                .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(PreFundInfoEntity::getDeleteFlag, 0)
        );
        if (ObjUtil.isNull(preFundInfo)) {
            throw new BusinessException("预授信申请信息不存在");
        }
        log.info("LanHaiServiceImpl.preApproveQueryV2ByPreId preId:{}", preId);
        FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(new LambdaQueryWrapper<>(FundApiDictEntity.class)
                .eq(FundApiDictEntity::getLinkId, preId)
                .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.PRE)
                .eq(FundApiDictEntity::getCode, FundApiDictEnum.PRE_ORI_REQUEST_SERIAL_NO)
                .orderByDesc(FundApiDictEntity::getCreateTime)
                .last("limit 1")
        );
        String fundNumber = fundApiDictEntity.getValue();
        LanHaiCreditPreApplyVerifyRequest request = new LanHaiCreditPreApplyVerifyRequest();
        request.setOriRequestSerialNo(fundNumber);
        LanHaiResult<LanHaiCreditPreApplyVerifyResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_REPAY_DETAIL_QUERY_VERIFY, request, LanHaiCreditPreApplyVerifyResponse.class, getOriRequestSerialNo());
        log.info("LanHaiServiceImpl.preApproveQueryV2ByPreId result:{}", JSONUtil.toJsonStr(result));
        if (!LanHaiResult.isSuccess(result)) {
            throw new BusinessException("查询预授信结果失败:" + result.getMsg());
        }
        LanHaiCreditPreApplyVerifyResponse body = result.getData().getBody();
        String handlerStatus = body.getHandlerStatus();
        String message = "";
        PreFundResultEnum updatePreStatus = PreFundResultEnum.WAIT;
        if (StrUtil.equals(handlerStatus, "001")) {
            updatePreStatus = PreFundResultEnum.PASS;
        }
        if (StrUtil.equals(handlerStatus, "003") || StrUtil.equals(handlerStatus, "004")) {
            updatePreStatus = PreFundResultEnum.REJECT;
        }
        if (StrUtil.equals(handlerStatus, "002")) {
            updatePreStatus = PreFundResultEnum.WAIT;
        }
        log.info("LanHaiServiceImpl.preApproveQueryV2ByPreId updatePreStatus:{}", updatePreStatus);
        if (StrUtil.isNotEmpty(body.getStartTime()) && StrUtil.isNotEmpty(body.getEndTime())) {
            LocalDateTime now = LocalDateTime.now();
            // 定义日期格式（根据你的字符串格式调整）
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            DateTime start = DateUtil.parse(body.getStartTime(), formatter);
            DateTime end = DateUtil.parse(body.getEndTime(), formatter);
            log.info("LanHaiServiceImpl.preApproveQueryV2ByPreId start:{} end:{}, now:{}", start, end, now);
            if (DateUtil.date(date).isBefore(end)) {
                preFundInfoMapper.update(new LambdaUpdateWrapper<PreFundInfoEntity>()
                        .eq(PreFundInfoEntity::getPreId, preId)
                        .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .set(PreFundInfoEntity::getExecuteRate, body.getExecuteRate().divide(BigDecimal.valueOf(100)))
                        .set(PreFundInfoEntity::getCreditContractEnd, end)
                        .set(PreFundInfoEntity::getUpdateTime, LocalDateTime.now())
                );
            } else {
                updatePreStatus = PreFundResultEnum.REJECT;
            }
        }
        if (ObjUtil.equals(PreFundResultEnum.PASS, updatePreStatus)) {
            message = "授信通过";
        } else if (ObjUtil.equals(PreFundResultEnum.REJECT, updatePreStatus)) {
            message = "授信拒绝";
        }
        if (StrUtil.isNotBlank(body.getErrMsg())) {
            message += " " + body.getErrMsg();
        }
        BigDecimal creditAmount = BigDecimal.ZERO;

        if (preFundInfo.getCreditAmount() != null) {
            creditAmount = preFundInfo.getCreditAmount();
        }
        PreApproveFundStatusDTO preApproveFundStatusDTO = new PreApproveFundStatusDTO()
                .setPreId(preId)
                .setFundId(FundEnum.LAN_HAI.getValue())
                .setStatus(updatePreStatus)
                .setCreditAmt(creditAmount)
                .setFailReason(message);
        log.info("LanHaiServiceImpl.preApproveQueryV2ByPreId preApproveFundStatusDTO: {}", JSONUtil.toJsonStr(preApproveFundStatusDTO));
        orderFeign.preUpdateFundStatus(preApproveFundStatusDTO);
    }


    /**
     * 生成head请求流水号
     *
     * @return
     */
    @Override
    public String getOriRequestSerialNo() {
        return lanHaiExpandService.getOriRequestSerialNo();
    }

    /**
     * 授信结果查询构建
     */
    public LanHaiCreditApplyQueryRequest creditApplyQueryBuild(Integer orderId) {
        log.info("LanHaiServiceImpl.creditApplyQueryBuild start, orderId: {}", orderId);

        FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(new LambdaQueryWrapper<FundApiDictEntity>()
                        .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.CREDIT)
                        .eq(FundApiDictEntity::getCode, FundApiDictEnum.CREDIT_REQUEST_SERIAL_NO)
                        .eq(FundApiDictEntity::getLinkId, orderId)
                        .eq(FundApiDictEntity::getDeleteFlag, 0)
                        .orderByDesc(FundApiDictEntity::getCreateTime)
                , false
        );
        if (ObjUtil.isNull(fundApiDictEntity)) {
            log.info("LanHaiServiceImpl.creditApplyQueryBuild fundApiDictEntity is null orderId:{}", orderId);
            throw new BusinessException("获取授信结果失败");
        }
        LanHaiCreditApplyQueryRequest creditApplyQueryRequest = new LanHaiCreditApplyQueryRequest();
        creditApplyQueryRequest.setOriRequestSerialNo(fundApiDictEntity.getValue());
        log.info("LanHaiServiceImpl.creditApplyQueryBuild creditApplyQueryRequest:{}", JSONUtil.toJsonStr(creditApplyQueryRequest));
        return creditApplyQueryRequest;
    }


    /**
     * 授信终结构建
     */
    private LanHaiCreditLimitTerminateRequest creditLimitTerminateBuild(Integer orderId) {
        log.info("LanHaiServiceImpl.creditLimitTerminateBuild start, orderId: {}", orderId);

        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );

        if (finalFundInfoEntity == null) {
            log.info("LanHaiServiceImpl.creditLimitTerminateBuild finalFundInfoEntity is null orderId:{}", orderId);
            throw new BusinessException("获取授信信息失败");
        }
        LanHaiCreditLimitTerminateRequest creditLimitTerminateRequest = new LanHaiCreditLimitTerminateRequest();
        creditLimitTerminateRequest.setCreditLimitId(finalFundInfoEntity.getCreditNo());
        creditLimitTerminateRequest.setOperateReason("终止授信");
        creditLimitTerminateRequest.setUserId(finalFundInfoEntity.getFundUserId());
        creditLimitTerminateRequest.setCreditOperateType("005");
        log.info("LanHaiServiceImpl.creditLimitTerminateBuild creditLimitTerminateRequest:{}", JSONUtil.toJsonStr(creditLimitTerminateRequest));
        return creditLimitTerminateRequest;
    }

    private LanHaiContractSignApplyRequest contractSignApplyBuild(Integer orderId) {
        log.info("LanHaiServiceImpl.contractSignApplyBuild start, orderId: {}", orderId);

        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );
        if (finalFundInfoEntity == null) {
            log.info("LanHaiServiceImpl.contractSignApplyBuild finalFundInfoEntity is null orderId:{}", orderId);
            throw new BusinessException("获取授信信息失败");
        }
        LanHaiContractSignApplyRequest contractSignApplyRequest = new LanHaiContractSignApplyRequest();
        contractSignApplyRequest.setCreditLimitId(finalFundInfoEntity.getCreditNo());
        contractSignApplyRequest.setUserId(finalFundInfoEntity.getFundUserId());
        contractSignApplyRequest.setOperateType("1");
        contractSignApplyRequest.setProductId(lanHaiConfig.getProductId());


        CustomerBaseDTO customerFinalBaseInfo = fundApproveMapper.getCustomerFinalBaseInfo(orderId);

        List<LanHaiContractSignApplyRequest.Attachment> attachmentsList = new ArrayList<>();
        List<LanHaiImageUploadResponse.FileList> fileList = imageUpload(new FundResourceDTO().setFund(FundEnum.LAN_HAI)
                .setType(5).setLinkId(orderId)
                .setCustomerBaseInfo(new FundResourceDTO.CustomerBaseInfo().setPhone(customerFinalBaseInfo.getPhone())
                        .setCustomerIdNo(customerFinalBaseInfo.getIdNumber())
                )
        );
        for (LanHaiImageUploadResponse.FileList item : fileList) {
            LanHaiContractSignApplyRequest.Attachment attachments = new LanHaiContractSignApplyRequest.Attachment();
            attachments.setFileKind(item.getFileKind());
            attachments.setFileName(item.getFileName());
            attachmentsList.add(attachments);
        }
        contractSignApplyRequest.setAttachments(attachmentsList);

        log.info("LanHaiServiceImpl.contractSignApplyBuild contractSignApplyRequest:{}", JSONUtil.toJsonStr(contractSignApplyRequest));
        return contractSignApplyRequest;
    }

    /**
     * 3.5.1 抵押申请
     *
     * @param orderId
     * @return
     */
    @Override
    public boolean mortgageApply(Integer orderId) {
        FinalFundInfoEntity finalFundInfoEntity = null;
        // 根据订单ID获取申请编号
        finalFundInfoEntity = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.LAN_HAI);
        LanHaiResult<LanHaiMortgageApplyResponse> result = null;
        boolean flag = false;
        log.info("LanHaiServiceImpl.mortgageApply orderId:{}", orderId);
        FundSignInfoEntity fundSignInfoEntity = fundSignInfoMapper.selectOne(new LambdaQueryWrapper<FundSignInfoEntity>()
                .eq(FundSignInfoEntity::getOrderId, orderId)
                .eq(FundSignInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FundSignInfoEntity::getSignStatus, FundSignStatusEnum.SUCCESS)
                .eq(FundSignInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FundSignInfoEntity::getCreateTime), false);
        log.info("LanHaiServiceImpl.mortgageApply fundSignInfoEntity:{}", fundSignInfoEntity);
        if (fundSignInfoEntity == null) {
            throw new BusinessException("合同未盖章");
        }
        checkProductContract(orderId);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        try {
            // 判断是否已签约，若未签约则先进行签约
//            if (!checkSignStatus(orderId)) {
//                throw new BusinessException("请先进行资方特殊合同签约");
//            }
            //判断是否抵押   从 apiDict 表查  如果 没数据 可以去抵押  如果有数据 调查询接口 判断状态  如果装态是已退办 则重新发起
            String transactionId = mortgageStatus(orderId);
            if (StrUtil.isNotEmpty(transactionId)) {
                //抵押状态查询
                LanHaiMortgageEnums mortgageApplyVerify = null;
                try {
                    mortgageApplyVerify = mortgageApplyVerify(orderId);
                    log.info("LanHaiServiceImpl.mortgageApply mortgageApplyVerify:{}", mortgageApplyVerify.getDescription());
                } catch (Exception e) {
                    throw new BusinessException(e.getMessage());
                }
                if (!ObjUtil.equals(mortgageApplyVerify, LanHaiMortgageEnums.RETURNED)) {
                    throw new BusinessException("抵押订单已创建,请勿重复创建,当前状态：" + mortgageApplyVerify.getDescription());
                }

            }
            //查询订单表是否放款

            if (ObjUtil.isNotNull(orderInfoEntity)) {
                if (ObjUtil.isNotNull(orderInfoEntity.getCurrentNode())) {
                    if (orderInfoEntity.getCurrentNode() >= 4500) {
                        throw new BusinessException("当前订单已放款");
                    }
                }
            }
            // 查询抵押表是否有数据
            CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                    .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                    .eq(CustomerMortgageInfoEntity::getMortgageType, 0)
                    .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                    .last("LIMIT 1"));

            if (Objects.isNull(customerMortgageInfoEntity)) {
                customerMortgageInfoEntity = new CustomerMortgageInfoEntity()
                        .setOrderId(orderId)
                        .setMortgageType(0);
                customerMortgageInfoMapper.insert(customerMortgageInfoEntity);
            }


            LanHaiMortgageApplyRequest request = buildMortgageApplyByOrderId(orderId);
            log.info("LanHaiServiceImpl.mortgageApply request:{}", JSONUtil.toJsonStr(request));
            result = lanHaiClient.execute(LanHaiApiEnums.LH_MORTGAGE_APPLY, request, LanHaiMortgageApplyResponse.class, getOriRequestSerialNo());
            log.info("LanHaiServiceImpl.mortgageApply result:{}", JSONUtil.toJsonStr(result));
            if (!LanHaiResult.isSuccess(result)) {
                throw new BusinessException("抵押失败:" + result.getData().getHead().getReturnMessage());
            }
            flag = true;

        } catch (Exception e) {
            log.error("LanHaiServiceImpl.mortgageApply Error during getMortgageUrl for orderId: {}", orderId, e);
            throw new BusinessException("抵押申请失败:" + e.getMessage());
        } finally {
            if (flag) {
                FinalFundInfoEntity fundApplyInfo = new FinalFundInfoEntity();
                fundApplyInfo.setId(finalFundInfoEntity.getId());
                fundApplyInfo.setFundNode(FundApplyNodeEnums.MORTGAGE_H5_PAGE);
                fundApplyInfo.setFundNodeStatus(FundApplyNodeStatusEnum.SUCCESS);
                fundApplyInfo.setMortgageState(FundMortgageStatusEnum.WAIT);
                finalFundInfoMapper.updateById(fundApplyInfo);

                //如果第二次抵押 将原先解抵和撤回完成的的记录（抵押中不更新）  变为历史记录 重新来一遍新的流程
                fundUndoMortgageInfoMapper.update(new LambdaUpdateWrapper<FundUndoMortgageInfoEntity>()
                        .set(FundUndoMortgageInfoEntity::getHistory, 1)
                        .set(FundUndoMortgageInfoEntity::getDeleteFlag, 1)
                        .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                        .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                );
                //清除原先的抵押编号
                fundApiDictMapper.update(new LambdaUpdateWrapper<FundApiDictEntity>()
                        .set(FundApiDictEntity::getDeleteFlag, 1)
                        .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.MORTGAGE)
                        .eq(FundApiDictEntity::getCode, FundApiDictEnum.MORTGAGE_REQUEST_APPLY_NO)
                        .eq(FundApiDictEntity::getLinkId, orderId)
                );
                //将抵押编号存到apidict中
                FundApiDictEntity fundApiDictEntity = new FundApiDictEntity();
                fundApiDictEntity.setLinkId(finalFundInfoEntity.getOrderId())
                        .setType(FundApiDictTypeEnum.MORTGAGE)
                        .setCode(FundApiDictEnum.MORTGAGE_REQUEST_APPLY_NO)
                        .setValue(result.getData().getBody().getTransactionId());
                fundApiDictMapper.insert(fundApiDictEntity);
                //申请更新为申请中
                orderInfoEntity.setMortgageState(1);
                orderInfoMapper.updateById(orderInfoEntity);
            } else {
                FinalFundInfoEntity fundApplyInfo = new FinalFundInfoEntity();
                fundApplyInfo.setId(finalFundInfoEntity.getId());
                fundApplyInfo.setFundNode(FundApplyNodeEnums.MORTGAGE_H5_PAGE);
                fundApplyInfo.setFundNodeStatus(FundApplyNodeStatusEnum.FAILURE);
                fundApplyInfo.setMortgageState(FundMortgageStatusEnum.NONE);
                finalFundInfoMapper.updateById(fundApplyInfo);
            }
        }
        return flag;
    }

    /**
     * 3.5.2.1 抵押修改
     *
     * @param orderId
     * @return
     */
    @Override
    public Boolean mortgageEdit(Integer orderId) {
        log.info("LanHaiServiceImpl.mortgageEdit start orderId:{}", orderId);
        FundSignInfoEntity fundSignInfoEntity = fundSignInfoMapper.selectOne(new LambdaQueryWrapper<FundSignInfoEntity>()
                .eq(FundSignInfoEntity::getOrderId, orderId)
                .eq(FundSignInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FundSignInfoEntity::getSignStatus, FundSignStatusEnum.SUCCESS)
                .eq(FundSignInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FundSignInfoEntity::getCreateTime), false);
        log.info("LanHaiServiceImpl.mortgageEdit fundSignInfoEntity:{}", fundSignInfoEntity);
        if (fundSignInfoEntity == null) {
            throw new BusinessException("合同未盖章");
        }
        checkProductContract(orderId);
        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.LAN_HAI);
        log.info("LanHaiServiceImpl.mortgageEdit finalFundInfoEntity:{}", finalFundInfoEntity);
        Assert.notNull(finalFundInfoEntity, () -> new BusinessException("资方申请信息不存在"));
        //抵押编号
        String transactionId = mortgageStatus(orderId);
        if (StrUtil.isEmpty(transactionId)) {
            throw new BusinessException("未查询到抵押编号");
        } else {
            //抵押状态查询
            LanHaiMortgageEnums mortgageApplyVerify = null;
            try {
                mortgageApplyVerify = mortgageApplyVerify(orderId);
                log.info("LanHaiServiceImpl.mortgageEdit mortgageApplyVerify:{}", mortgageApplyVerify);
            } catch (Exception e) {
                throw new BusinessException(e.getMessage());
            }
            if (!ObjUtil.equals(mortgageApplyVerify, LanHaiMortgageEnums.PRE_ACCEPT_SUCCESS)) {
                throw new BusinessException("当前状态不支持修改:：" + mortgageApplyVerify.getDescription());
            }

        }

        //查询订单表是否放款
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
//        if (ObjUtil.isNotNull(orderInfoEntity)) {
//            if (ObjUtil.isNotNull(orderInfoEntity.getCurrentNode())) {
//                if (orderInfoEntity.getCurrentNode() >= 4500) {
//                    throw new BusinessException("当前订单已放款");
//                }
//            }
//        }
        // 查询抵押表是否有数据
        CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                .eq(CustomerMortgageInfoEntity::getMortgageType, 0)
                .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                .last("LIMIT 1"));

        if (Objects.isNull(customerMortgageInfoEntity)) {
            customerMortgageInfoEntity = new CustomerMortgageInfoEntity()
                    .setOrderId(orderId)
                    .setMortgageType(0);
            customerMortgageInfoMapper.insert(customerMortgageInfoEntity);
        }
        //直接调用修改的接口 不用做处理
        boolean flag = false;
        try {
            LanHaiMortgageEditRequest request = buildMortgageEditByOrderId(orderId);
            log.info("LanHaiServiceImpl.mortgageEdit request:{}", JSONUtil.toJsonStr(request));
            LanHaiResult<LanHaiMortgageEditResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_MORTGAGE_EDIT, request, LanHaiMortgageEditResponse.class, getOriRequestSerialNo());
            log.info("LanHaiServiceImpl.mortgageEdit result:{}", JSONUtil.toJsonStr(result));
            if (!LanHaiResult.isSuccess(result)) {
                throw new BusinessException("抵押修改失败:" + result.getData().getHead().getReturnMessage());
            }
            flag = true;

        } catch (BusinessException e) {
            throw new RuntimeException(e);
        } finally {
            if (flag) {
                finalFundInfoEntity.setFundNode(FundApplyNodeEnums.MORTGAGE_H5_EDIT);
                finalFundInfoMapper.updateById(finalFundInfoEntity);
            }
        }
        return flag;
    }

    private LanHaiMortgageEditRequest buildMortgageEditByOrderId(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, () -> new BusinessException("订单不存在"));
        OrderArrivedEntity orderArrivedEntity = orderArrivedMapper.selectOne(new LambdaQueryWrapper<OrderArrivedEntity>()
                .eq(OrderArrivedEntity::getOrderId, orderId)
                .eq(OrderArrivedEntity::getDeleteFlag, 0)
                .orderByDesc(OrderArrivedEntity::getCreateTime)
                .last("LIMIT 1")
        );
        Assert.notNull(orderArrivedEntity, () -> new BusinessException("抵押信息不存在"));
        ArrivedDataEntity arrivedDataEntity = arrivedDataMapper.selectById(orderArrivedEntity.getArrivedId());
        Assert.notNull(arrivedDataEntity, () -> new BusinessException("抵押信息不存在"));
        // 获取订单关联的办抵地址信息
        OrderArrivedAddressEntity orderArrivedAddressEntity = orderArrivedAddressMapper.selectOne(new LambdaQueryWrapper<OrderArrivedAddressEntity>()
                .eq(OrderArrivedAddressEntity::getArrivedId, orderArrivedEntity.getId())
                .eq(OrderArrivedAddressEntity::getType, 1)
                .eq(OrderArrivedAddressEntity::getDeleteFlag, 0)
                .orderByDesc(OrderArrivedAddressEntity::getCreateTime)
                .last("LIMIT 1"));
        Assert.notNull(orderArrivedAddressEntity, () -> new BusinessException("抵押收件地址信息不存在"));

        FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("LIMIT 1")
        );
        Assert.notNull(fundInfo, () -> new BusinessException("资方信息不存在"));


        FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(
                new LambdaQueryWrapper<FundApiDictEntity>()
                        .eq(FundApiDictEntity::getLinkId, orderId)
                        .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.MORTGAGE)
                        .eq(FundApiDictEntity::getCode, FundApiDictEnum.MORTGAGE_REQUEST_APPLY_NO)
                        .eq(FundApiDictEntity::getDeleteFlag, 0)
                        .orderByDesc(FundApiDictEntity::getCreateTime)
                        .last("LIMIT 1")
        );
        Assert.notNull(fundApiDictEntity, () -> new BusinessException("抵押编号不存在"));

        List<LanHaiImageUploadRequest.FileInfo> fileInfos = imageUploadMortgage(orderId, orderArrivedEntity);
        List<LanHaiMortgageEditRequest.Attachment> attachmentList = new ArrayList<>();
        fileInfos.stream().forEach(item -> {
            LanHaiMortgageEditRequest.Attachment attachment = new LanHaiMortgageEditRequest.Attachment();
            attachment.setFileKind(item.getFileKind())
                    .setFileName(item.getFileName());
            attachmentList.add(attachment);
        });


        String templateCodeMortgage = "LH_LH_0003";// 蓝海-汽车金融抵押合同
        FileTemplateInfoEntity fileTemplateInfoMortgage = fileTemplateInfoMapper.selectOne(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getTemplateNumber, templateCodeMortgage)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .eq(FileTemplateInfoEntity::getType, 1)
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FileTemplateInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(fileTemplateInfoMortgage)) {
            log.info("LanHaiServiceImpl.buildMortgageEditByOrderId fileTemplateInfoMortgage:{}", fileTemplateInfoMortgage);
            throw new BusinessException("合同模板不存在");
        }
        OrderContractEntity orderContractEntity = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getTemplateId, fileTemplateInfoMortgage.getId())
                .eq(OrderContractEntity::getSignStatus, 2)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .orderByDesc(OrderContractEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderContractEntity)) {
            log.info("LanHaiServiceImpl.buildMortgageEditByOrderId orderContractEntity:{}", orderContractEntity);
            throw new BusinessException("未查询到合同相关信息");
        }

        Car300DataVO car300DataVO = car300Feign.getInfoByVehicle(orderInfoEntity.getVehicleNumber()).getData();


        LanHaiMortgageEditRequest request = new LanHaiMortgageEditRequest();
        request.setUserId(fundInfo.getFundUserId())
                .setProductId(lanHaiConfig.getProductId())
                .setCreditApplyId(fundInfo.getCreditReqNo())
                .setTransactionId(fundApiDictEntity.getValue())
                .setMtgType("E")
                .setMtgeeCode(lanHaiConfig.getMortgageCreditCode())
                .setMtgeeAgtIdType("A")
                .setMtgeeAgtIdNumber(arrivedDataEntity.getMortgageAgentIdNumber())
                .setMtgeeAgtName(arrivedDataEntity.getMortgageAgentName())
                .setMtgeeAgtMobile(arrivedDataEntity.getMortgageAgentPhone())
                .setMtgContractNo(StringUtils.isNotBlank(getMtgContractNo(orderId)) ? getMtgContractNo(orderId) : "")
                .setMtgorIdType("A")
                .setMtgorIdNumber(orderArrivedEntity.getMotorVehicleIdNumber())
                .setMtgorName(orderArrivedEntity.getMotorVehicleName())
                .setMtgorMobile(orderArrivedEntity.getMotorVehiclePhone())
                .setVin(car300DataVO.getVin())
                .setPlateType(orderInfoEntity.getVehicleNumber().length() == 7 ? "02" : "52")
                .setPlateNumber(orderInfoEntity.getVehicleNumber())
                .setRecipient(orderArrivedAddressEntity.getRecipient())
                .setPostAddress(orderArrivedAddressEntity.getPostAddress())
                .setRecipientMobile(orderArrivedAddressEntity.getRecipientMobile())
                .setApplyDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .setAttachments(attachmentList);


        return request;

    }

    /**
     * 构建抵押请求
     *
     * @param orderId
     * @return
     */
    private LanHaiMortgageApplyRequest buildMortgageApplyByOrderId(Integer orderId) {

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, () -> new BusinessException("订单不存在"));
        OrderArrivedEntity orderArrivedEntity = orderArrivedMapper.selectOne(new LambdaQueryWrapper<OrderArrivedEntity>()
                .eq(OrderArrivedEntity::getOrderId, orderId)
                .eq(OrderArrivedEntity::getDeleteFlag, 0)
                .orderByDesc(OrderArrivedEntity::getCreateTime)
                .last("LIMIT 1")
        );
        Assert.notNull(orderArrivedEntity, () -> new BusinessException("抵押信息不存在"));
        ArrivedDataEntity arrivedDataEntity = arrivedDataMapper.selectById(orderArrivedEntity.getArrivedId());
        Assert.notNull(arrivedDataEntity, () -> new BusinessException("抵押信息不存在"));
        // 获取订单关联的办抵地址信息
        OrderArrivedAddressEntity orderArrivedAddressEntity = orderArrivedAddressMapper.selectOne(new LambdaQueryWrapper<OrderArrivedAddressEntity>()
                .eq(OrderArrivedAddressEntity::getArrivedId, orderArrivedEntity.getId())
                .eq(OrderArrivedAddressEntity::getType, 1)
                .eq(OrderArrivedAddressEntity::getDeleteFlag, 0)
                .orderByDesc(OrderArrivedAddressEntity::getCreateTime)
                .last("LIMIT 1"));
        Assert.notNull(orderArrivedAddressEntity, () -> new BusinessException("抵押收件地址信息不存在"));

        FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("LIMIT 1")
        );
        Assert.notNull(fundInfo, () -> new BusinessException("资方信息不存在"));

        List<LanHaiImageUploadRequest.FileInfo> fileInfos = imageUploadMortgage(orderId, orderArrivedEntity);
        List<LanHaiMortgageApplyRequest.Attachment> attachmentList = new ArrayList<>();
        fileInfos.stream().forEach(item -> {
            LanHaiMortgageApplyRequest.Attachment attachment = new LanHaiMortgageApplyRequest.Attachment();
            attachment.setFileKind(item.getFileKind())
                    .setFileName(item.getFileName());
            attachmentList.add(attachment);
        });

        String templateCodeMortgage = "LH_LH_0003";// 蓝海-汽车金融抵押合同
        FileTemplateInfoEntity fileTemplateInfoMortgage = fileTemplateInfoMapper.selectOne(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getTemplateNumber, templateCodeMortgage)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .eq(FileTemplateInfoEntity::getType, 1)
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FileTemplateInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(fileTemplateInfoMortgage)) {
            log.info("LanHaiServiceImpl.buildMortgageApplyByOrderId fileTemplateInfoMortgage:{}", fileTemplateInfoMortgage);
            throw new BusinessException("合同模板不存在");
        }
        OrderContractEntity orderContractEntity = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getTemplateId, fileTemplateInfoMortgage.getId())
                .eq(OrderContractEntity::getSignStatus, 2)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .orderByDesc(OrderContractEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderContractEntity)) {
            log.info("LanHaiServiceImpl.buildMortgageApplyByOrderId orderContractEntity:{}", orderContractEntity);
            throw new BusinessException("未查询到合同相关信息");
        }

        Car300DataVO car300DataVO = car300Feign.getInfoByVehicle(orderInfoEntity.getVehicleNumber()).getData();

        String transactionId = lanHaiConfig.getProductId() + "DY" + System.currentTimeMillis() + RandomUtil.randomString(4);

        LanHaiMortgageApplyRequest request = new LanHaiMortgageApplyRequest();
        request.setUserId(fundInfo.getFundUserId())
                .setProductId(lanHaiConfig.getProductId())
                .setCreditApplyId(fundInfo.getCreditReqNo())
                .setTransactionId(transactionId)
                .setMtgType("E")
                .setMtgeeCode(lanHaiConfig.getMortgageCreditCode())
                .setMtgeeAgtIdType("A")
                .setMtgeeAgtIdNumber(arrivedDataEntity.getMortgageAgentIdNumber())
                .setMtgeeAgtName(arrivedDataEntity.getMortgageAgentName())
                .setMtgeeAgtMobile(arrivedDataEntity.getMortgageAgentPhone())
//                .setMtgContractNo(orderContractEntity.getNumber())
                .setMtgContractNo(StringUtils.isNotBlank(getMtgContractNo(orderId)) ? getMtgContractNo(orderId) : "")
                .setMtgorIdType("A")
                .setMtgorIdNumber(orderArrivedEntity.getMotorVehicleIdNumber())
                .setMtgorName(orderArrivedEntity.getMotorVehicleName())
                .setMtgorMobile(orderArrivedEntity.getMotorVehiclePhone())
                .setVin(car300DataVO.getVin())
                .setPlateType(orderInfoEntity.getVehicleNumber().length() == 7 ? "02" : "52")
                .setPlateNumber(orderInfoEntity.getVehicleNumber())
                .setRecipient(orderArrivedAddressEntity.getRecipient())
                .setPostAddress(orderArrivedAddressEntity.getPostAddress())
                .setRecipientMobile(orderArrivedAddressEntity.getRecipientMobile())
                .setApplyDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .setAttachments(attachmentList);


        return request;
    }

    private String getMtgContractNo(Integer linkId) {
        ParamsSnapshotEntity paramsSnapshotEntity = paramsSnapshotEntityMapper.selectOne(
                new LambdaQueryWrapper<ParamsSnapshotEntity>()
                        .eq(ParamsSnapshotEntity::getLinkId, linkId)
                        .eq(ParamsSnapshotEntity::getCode, "MORTGAGE_CONTRACT_NUMBER")
                        .eq(ParamsSnapshotEntity::getDeleteFlag, 0)
                        .orderByAsc(ParamsSnapshotEntity::getCreateTime)
                        .last("limit 1")
        );
        if (ObjUtil.isNotNull(paramsSnapshotEntity)) {
            return paramsSnapshotEntity.getValue();
        } else {
            throw new BusinessException("抵押合同编号不存在");
        }
    }

    private List<LanHaiImageUploadRequest.FileInfo> imageUploadMortgage(Integer orderId, OrderArrivedEntity orderArrivedEntity) {
        log.info("--- 蓝海影像上传开始 orderId: {} ---", orderId);
        FundEnum lanHai = FundEnum.LAN_HAI;
        FundResourceDTO fundResourceDTO = new FundResourceDTO();
        fundResourceDTO
                .setLinkId(orderId)
                .setType(6)
                .setFund(lanHai)
                .setCustomerBaseInfo(
                        new FundResourceDTO.CustomerBaseInfo()
                                .setCustomerIdNo(orderArrivedEntity.getMotorVehicleIdNumber())
                                .setPhone(orderArrivedEntity.getMotorVehiclePhone())
                )
        ;

        Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(fundResourceDTO);
        if (!Result.isSuccess(listResult)) {
            throw new BusinessException("获取影像文件失败");
        }
        List<FundResourceResultDTO> data = listResult.getData();
        LanHaiImageUploadRequest lanHaiImageUploadRequest = new LanHaiImageUploadRequest();
        List<LanHaiImageUploadRequest.FileInfo> fileList = new ArrayList<>();

        data.stream().forEach(item -> {
            LanHaiImageUploadRequest.FileInfo fileInfo = new LanHaiImageUploadRequest.FileInfo();
            fileInfo.setFileData(item.getFilePath());
            fileInfo.setFileKind(item.getFileCode());
            fileInfo.setFileName(item.getFileId());
            fileList.add(fileInfo);
        });
        //数据太大 分开推送
        List<LanHaiImageUploadRequest.FileInfo> fileListTest = new ArrayList<>();
        data.stream().forEach(item -> {
            LanHaiImageUploadRequest.FileInfo fileInfo = new LanHaiImageUploadRequest.FileInfo();
            fileInfo.setFileKind(item.getFileCode());
            fileInfo.setFileName(item.getFileId());
            fileListTest.add(fileInfo);
        });
        log.info("LanHaiServiceImpl.imageUploadMortgage fileListTest:{}", JSONUtil.toJsonStr(fileListTest));
        try {
            fileList.stream().forEach(item -> {
                List<LanHaiImageUploadRequest.FileInfo> fileNew = new ArrayList<>();
                fileNew.add(item);
                lanHaiImageUploadRequest.setFileList(fileNew);
                log.info("LanHaiServiceImpl.imageUploadMortgage item.getFileKind():{}", item.getFileKind());
                LanHaiResult<LanHaiImageUploadResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_IMAGE_UPLOAD, lanHaiImageUploadRequest, LanHaiImageUploadResponse.class, getOriRequestSerialNo());
                if (!LanHaiResult.isSuccess(result) || !LanHaiResult.isSuccess(result)) {
                    throw new BusinessException("影像上传失败");
                }
            });
        } catch (Exception e) {
            throw new BusinessException("影像上传失败:" + e.getMessage());
        }

        return fileList;
    }

    private boolean checkSignStatus(Integer orderId) {
        //判断是否已签约
        OrderContractEntity orderContractEntity = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getTemplateId, ContractTypeEnums.GRDDJKHT.getTemplateId())
                .eq(OrderContractEntity::getSignStatus, 2)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .orderByDesc(OrderContractEntity::getCreateTime)
                .last("limit 1"));
        return orderContractEntity != null;
    }

    /**
     * 获取抵押流水号
     *
     * @param orderId
     * @return
     */
    public String mortgageStatus(Integer orderId) {
        String transactionId = "";
        FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(new LambdaQueryWrapper<FundApiDictEntity>()
                .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.MORTGAGE)
                .eq(FundApiDictEntity::getCode, FundApiDictEnum.MORTGAGE_REQUEST_APPLY_NO)
                .eq(FundApiDictEntity::getLinkId, orderId)
                .eq(FundApiDictEntity::getDeleteFlag, 0)
                .orderByDesc(FundApiDictEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNotNull(fundApiDictEntity) && StrUtil.isNotEmpty(fundApiDictEntity.getValue())) {
            transactionId = fundApiDictEntity.getValue();
        }
        return transactionId;
    }


    /**
     * 3.5.2 抵押状态查询
     *
     * @param orderId
     * @return
     */
    @Override
    public LanHaiMortgageEnums mortgageApplyVerify(Integer orderId) {
        log.info("LanHaiServiceImpl.mortgageApplyVerify orderId:{}", orderId);
        //根据orderId 查询 transactionId
        String transactionId = "";

        transactionId = mortgageStatus(orderId);

        if (StrUtil.isEmpty(transactionId)) {
            throw new BusinessException("抵押申请流水号不存在");
        } else {
            LanHaiMortgageApplyVerifyRequest request = new LanHaiMortgageApplyVerifyRequest();
            request.setTransactionId(transactionId);

            LanHaiMortgageEnums reuslt = null;
            try {
                LanHaiResult<LanHaiMortgageApplyVerifyResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_MORTGAGE_VERIFY, request, LanHaiMortgageApplyVerifyResponse.class, getOriRequestSerialNo());
                log.info("LanHaiServiceImpl.mortgageApplyVerify result:{}", JSONUtil.toJsonStr(result));
                if (!LanHaiResult.isSuccess(result)) {
                    throw new BusinessException(result.getData().getHead().getReturnMessage());
                }
                LanHaiMortgageApplyVerifyResponse body = result.getData().getBody();
                //如果抵押方式是线上，才会变更抵押状态
                CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                        .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                        .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                        .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                        .last("limit 1")
                );
                if (ObjUtil.isNotNull(customerMortgageInfoEntity)&& ObjectUtil.equals(customerMortgageInfoEntity.getMortgageType(),0) ){
                    reuslt = updateMortgageApplyStatues(orderId, body.getBusinessStatus(), body.getOperationStatus(),null);
                }
            } catch (BusinessException e) {
                throw new BusinessException("抵押状态查询异常:" + e.getMessage());
            }
            return reuslt;

        }

    }

    /**
     * 更新抵押状态
     *
     * @param orderId
     * @return
     */
    private LanHaiMortgageEnums updateMortgageApplyStatues(Integer orderId, LanHaiMortgageEnums businessStatus, String operationStatus,String rejectReason) {
        log.info("LanHaiServiceImpl.updateMortgageApplyStatues start orderId:{} businessStatus:{} operationStatus:{}", orderId, businessStatus, operationStatus);

        FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        Assert.notNull(fundInfo, () -> {
            throw new BusinessException("终审信息不存在");
        });

        FundUndoMortgageInfoEntity fundUndoMortgageInfoEntity = fundUndoMortgageInfoMapper.selectOne(new LambdaQueryWrapper<FundUndoMortgageInfoEntity>()
                .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                .eq(FundUndoMortgageInfoEntity::getOperateType, "1")
                .eq(FundUndoMortgageInfoEntity::getHistory, 0)
        );
        MortgageStateUpdateDTO mortgageStateUpdateDTO = new MortgageStateUpdateDTO();
        mortgageStateUpdateDTO.setOrderId(orderId);
        Integer mortgageState = 0;
        FundMortgageStatusEnum fundNodeStatus = null;

        switch (businessStatus) {
            case PRE_ACCEPT_SUCCESS:
                mortgageState = 2;
                fundNodeStatus = FundMortgageStatusEnum.PUSH_TO_TMRI;
                break;
            case ACCEPT_SUCCESS:
            case SIGNED:
                mortgageState = 2;
                fundNodeStatus = FundMortgageStatusEnum.ACCEPT_COMPLETED;
                break;
            case ARCHIVED:
                mortgageState = 2;
                fundNodeStatus = FundMortgageStatusEnum.COMPLETED;
                break;
            case RETURNED:
                mortgageState = 4;
//                fundNodeStatus = FundMortgageStatusEnum.NONE;
                fundNodeStatus = FundMortgageStatusEnum.CANCELED;
                break;

        }
        fundInfo.setMortgageState(fundNodeStatus);
        fundInfo.setFundNode(FundApplyNodeEnums.MORTGAGE_RESULT_QUERY);
        fundInfo.setFailReason(StringUtils.isNotBlank(rejectReason) ? rejectReason : null);
        finalFundInfoService.updateById(fundInfo);

        //如果是空 说明未发起撤回  如果不是空 结果为最终时 把对象从表中删除
        if (ObjUtil.isNotNull(fundUndoMortgageInfoEntity)) {
            if (ObjUtil.isNotNull(mortgageState)) {
                if (LanHaiMortgageEnums.RETURNED.equals(businessStatus)) {
                    fundUndoMortgageInfoEntity.setDeleteFlag(1);
                    fundUndoMortgageInfoEntity.setHistory(1);
                }
            }
//            fundUndoMortgageInfoEntity.setMortgageStatus(mortgageState.toString());
            fundUndoMortgageInfoMapper.updateById(fundUndoMortgageInfoEntity);
        }
        log.info("LanHaiServiceImpl.updateMortgageApplyStatues mortgageState {}", mortgageState);
        mortgageStateUpdateDTO.setMortgageState(mortgageState);
        mortgageStateUpdateDTO.setMortgageType(0);
        orderFeign.updateMortgageState(mortgageStateUpdateDTO);
        log.info("LanHaiServiceImpl.updateMortgageApplyStatues end");
        return businessStatus;
    }

    /**
     * 3.5.3. 抵押退办
     *
     * @param orderId
     * @return
     */
    @Override
    public Boolean mortgageCancel(Integer orderId, String cancelReason) {
        log.info("LanHaiServiceImpl.mortgageCancel start,orderId:{}", orderId);
        FundSignInfoEntity fundSignInfoEntity = fundSignInfoMapper.selectOne(new LambdaQueryWrapper<FundSignInfoEntity>()
                .eq(FundSignInfoEntity::getOrderId, orderId)
                .eq(FundSignInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FundSignInfoEntity::getSignStatus, FundSignStatusEnum.SUCCESS)
                .eq(FundSignInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FundSignInfoEntity::getCreateTime), false);
        log.info("LanHaiServiceImpl.mortgageCancel fundSignInfoEntity:{}", fundSignInfoEntity);
        if (fundSignInfoEntity == null) {
            throw new BusinessException("合同未盖章");
        }
        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.LAN_HAI);
        log.info("LanHaiServiceImpl.mortgageCancel finalFundInfoEntity:{}", finalFundInfoEntity);
        Assert.notNull(finalFundInfoEntity, () -> new BusinessException("资方申请信息不存在"));
        //抵押编号
        String transactionId = mortgageStatus(orderId);
        if (StrUtil.isEmpty(transactionId)) {
            throw new BusinessException("未查询到抵押编号");
        } else {
            //抵押状态查询
            LanHaiMortgageEnums mortgageApplyVerify = null;
            try {
                mortgageApplyVerify = mortgageApplyVerify(orderId);
                log.info("LanHaiServiceImpl.mortgageCancel mortgageApplyVerify:{}", mortgageApplyVerify);
            } catch (Exception e) {
                throw new BusinessException(e.getMessage());
            }
            if (!ObjUtil.equals(mortgageApplyVerify, LanHaiMortgageEnums.PRE_ACCEPT_SUCCESS)) {
                throw new BusinessException("当前状态不支持修改:" + mortgageApplyVerify.getDescription());
            }
        }
        //根据订单ID获取申请编号
        boolean flag = false;
        try {
            LanHaiMortgageCancelRequest request = buildMortgageCancelByOrderId(orderId, cancelReason);
            log.info("LanHaiServiceImpl.mortgageCancel request:{}", JSONUtil.toJsonStr(request));
            LanHaiResult<LanHaiMortgageCancelResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_MORTGAGE_CANCEL, request, LanHaiMortgageCancelResponse.class, getOriRequestSerialNo());
            log.info("LanHaiServiceImpl.mortgageCancel result:{}", JSONUtil.toJsonStr(result));
            if (!LanHaiResult.isSuccess(result)) {
                throw new BusinessException("蓝海抵押撤销失败" + result.getData().getHead().getReturnMessage());
            }

            flag = true;
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessage());
        } finally {
            if (flag) {
                finalFundInfoEntity.setFundNode(FundApplyNodeEnums.MORTGAGE_REVOKE);

                finalFundInfoEntity.setFundNodeStatus(FundApplyNodeStatusEnum.SUCCESS);
                finalFundInfoEntity.setMortgageState(FundMortgageStatusEnum.WAIT);
                finalFundInfoMapper.updateById(finalFundInfoEntity);

                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
                FundUndoMortgageInfoEntity fundUndoMortgageInfoEntity = new FundUndoMortgageInfoEntity();

                //查询原先抵押撤销的信息  如果曾经有过抵押撤销将抵押撤销置为历史记录
                fundUndoMortgageInfoMapper.update(new LambdaUpdateWrapper<FundUndoMortgageInfoEntity>()
                        .set(FundUndoMortgageInfoEntity::getHistory, 1)
                        .set(FundUndoMortgageInfoEntity::getDeleteFlag, 1)
                        .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                        .eq(FundUndoMortgageInfoEntity::getOperateType, "1")
                        .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                        .eq(FundUndoMortgageInfoEntity::getCancel, 1)
                );
                fundUndoMortgageInfoEntity = new FundUndoMortgageInfoEntity().setFundId(FundEnum.FU_MIN.getValue())
                        .setOrderId(orderId)
                        .setApplyNo(finalFundInfoEntity.getCreditReqNo())
                        .setChannelCode(lanHaiConfig.getChannelNo())
                        .setOperateType("1")
                        .setMortgageStatus(MortgageEnums.MORTGAGE_STATUS_BACK_PROCESSING.getCode().toString())
                        .setCancel(1)
                        .setHistory(0);

                fundUndoMortgageInfoMapper.insert(fundUndoMortgageInfoEntity);
                orderInfoMapper.updateById(orderInfoEntity);
            }
        }
        return flag;
    }

    private LanHaiMortgageCancelRequest buildMortgageCancelByOrderId(Integer orderId, String cancelReason) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, () -> new BusinessException("订单不存在"));
        OrderArrivedEntity orderArrivedEntity = orderArrivedMapper.selectOne(new LambdaQueryWrapper<OrderArrivedEntity>()
                .eq(OrderArrivedEntity::getOrderId, orderId)
                .eq(OrderArrivedEntity::getDeleteFlag, 0)
                .orderByDesc(OrderArrivedEntity::getCreateTime)
                .last("LIMIT 1")
        );
        Assert.notNull(orderArrivedEntity, () -> new BusinessException("抵押信息不存在"));
        ArrivedDataEntity arrivedDataEntity = arrivedDataMapper.selectById(orderArrivedEntity.getArrivedId());
        Assert.notNull(arrivedDataEntity, () -> new BusinessException("抵押信息不存在"));
        // 获取订单关联的办抵地址信息
        OrderArrivedAddressEntity orderArrivedAddressEntity = orderArrivedAddressMapper.selectOne(new LambdaQueryWrapper<OrderArrivedAddressEntity>()
                .eq(OrderArrivedAddressEntity::getArrivedId, orderArrivedEntity.getId())
                .eq(OrderArrivedAddressEntity::getType, 1)
                .eq(OrderArrivedAddressEntity::getDeleteFlag, 0)
                .orderByDesc(OrderArrivedAddressEntity::getCreateTime)
                .last("LIMIT 1"));
        Assert.notNull(orderArrivedAddressEntity, () -> new BusinessException("抵押收件地址信息不存在"));

        FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("LIMIT 1")
        );
        Assert.notNull(fundInfo, () -> new BusinessException("资方信息不存在"));


        FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(
                new LambdaQueryWrapper<FundApiDictEntity>()
                        .eq(FundApiDictEntity::getLinkId, orderId)
                        .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.MORTGAGE)
                        .eq(FundApiDictEntity::getCode, FundApiDictEnum.MORTGAGE_REQUEST_APPLY_NO)
                        .eq(FundApiDictEntity::getDeleteFlag, 0)
                        .orderByDesc(FundApiDictEntity::getCreateTime)
                        .last("LIMIT 1")
        );
        Assert.notNull(fundApiDictEntity, () -> new BusinessException("抵押编号不存在"));

        String templateCodeMortgage = "LH_LH_0003";// 蓝海-汽车金融抵押合同
        FileTemplateInfoEntity fileTemplateInfoMortgage = fileTemplateInfoMapper.selectOne(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getTemplateNumber, templateCodeMortgage)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .eq(FileTemplateInfoEntity::getType, 1)
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FileTemplateInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(fileTemplateInfoMortgage)) {
            log.info("LanHaiServiceImpl.buildMortgageCancelByOrderId fileTemplateInfoMortgage:{}", fileTemplateInfoMortgage);
            throw new BusinessException("合同模板不存在");
        }
        OrderContractEntity orderContractEntity = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getTemplateId, fileTemplateInfoMortgage.getId())
                .eq(OrderContractEntity::getSignStatus, 2)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .orderByDesc(OrderContractEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderContractEntity)) {
            log.info("LanHaiServiceImpl.buildMortgageCancelByOrderId orderContractEntity:{}", orderContractEntity);
            throw new BusinessException("未查询到合同相关信息");
        }

        Car300DataVO car300DataVO = car300Feign.getInfoByVehicle(orderInfoEntity.getVehicleNumber()).getData();

        LanHaiMortgageCancelRequest request = new LanHaiMortgageCancelRequest();
        request.setUserId(fundInfo.getFundUserId())
                .setProductId(lanHaiConfig.getProductId())
                .setCreditApplyId(fundInfo.getCreditReqNo())
                .setTransactionId(fundApiDictEntity.getValue())
                .setMtgType("E")
                .setMtgeeCode(lanHaiConfig.getMortgageCreditCode())
                .setMtgeeAgtIdType("A")
                .setMtgeeAgtIdNumber(arrivedDataEntity.getMortgageAgentIdNumber())
                .setMtgeeAgtName(arrivedDataEntity.getMortgageAgentName())
                .setMtgeeAgtMobile(arrivedDataEntity.getMortgageAgentPhone())
                .setMtgContractNo(StringUtils.isNotBlank(getMtgContractNo(orderId)) ? getMtgContractNo(orderId) : "")
                .setMtgorIdType("A")
                .setMtgorIdNumber(orderArrivedEntity.getMotorVehicleIdNumber())
                .setMtgorName(orderArrivedEntity.getMotorVehicleName())
                .setMtgorMobile(orderArrivedEntity.getMotorVehiclePhone())
                .setVin(car300DataVO.getVin())
                .setPlateType(orderInfoEntity.getVehicleNumber().length() == 7 ? "02" : "52")
                .setPlateNumber(orderInfoEntity.getVehicleNumber())
                .setRecipient(orderArrivedAddressEntity.getRecipient())
                .setPostAddress(orderArrivedAddressEntity.getPostAddress())
                .setRecipientMobile(orderArrivedAddressEntity.getRecipientMobile())
                .setApplyDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .setRemark(cancelReason)
        ;

        return request;
    }


    /**
     * *******. 还款计划查询
     */
    @Override
    public Boolean queryRepayPlan(Integer orderId) {
        log.info("LanHaiServiceImpl.queryRepayPlan start,orderId:{}", orderId);

        LanHaiResult<LanHaiRepayPlanQueryResponse> result = null;
        try {
            FinalFundInfoEntity finalFundInfoEntity = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.LAN_HAI);
            if (ObjUtil.isNull(finalFundInfoEntity)) {
                throw new BusinessException("资方申请信息不存在");
            }
            LanHaiRepayPlanQueryRequest request = new LanHaiRepayPlanQueryRequest();
            request.setLoanInvoiceId(finalFundInfoEntity.getLoanNo());
            request.setRepayStatus(null);
            log.info("LanHaiServiceImpl.queryRepayPlan request:{}", JSONUtil.toJsonStr(request));
            result = lanHaiClient.execute(LanHaiApiEnums.LH_QUERY_REPAY_PLAN, request, LanHaiRepayPlanQueryResponse.class, getOriRequestSerialNo());
            log.info("LanHaiServiceImpl.queryRepayPlan result:{}", JSONUtil.toJsonStr(result));
            if (!LanHaiResult.isSuccess(result)) {
                throw new BusinessException("还款计划查询失败" + result.getData().getHead().getReturnMessage());
            }
            List<LanHaiRepayPlanQueryResponse.RepayPlanItem> repayPlanList = result.getData().getBody().getDataList();
            if (CollUtil.isEmpty(repayPlanList)) {
                throw new BusinessException("还款计划查询为空");
            }
            batchUpdateRepaymentPlans(orderId, repayPlanList);

        } catch (Exception e) {
            log.info("LanHaiServiceImpl.queryRepayPlan orderId:{} error:{}", orderId, e.getMessage(), e);
            throw new BusinessException("还款计划更新失败");
        }

        return null;
    }


    /**
     * *******. 还款计划查询
     */
    @Override
    public LanHaiRepayPlanQueryResponse queryRepayPlanNoUpdate(Integer orderId) {
        log.info("LanHaiServiceImpl.queryRepayPlan start,orderId:{}", orderId);

        LanHaiResult<LanHaiRepayPlanQueryResponse> result = null;
        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.LAN_HAI);
        if (ObjUtil.isNull(finalFundInfoEntity)) {
            throw new BusinessException("资方申请信息不存在");
        }
        LanHaiRepayPlanQueryRequest request = new LanHaiRepayPlanQueryRequest();
        request.setLoanInvoiceId(finalFundInfoEntity.getLoanNo());
        request.setRepayStatus(null);
        log.info("LanHaiServiceImpl.queryRepayPlan request:{}", JSONUtil.toJsonStr(request));
        result = lanHaiClient.execute(LanHaiApiEnums.LH_QUERY_REPAY_PLAN, request, LanHaiRepayPlanQueryResponse.class, getOriRequestSerialNo());
        log.info("LanHaiServiceImpl.queryRepayPlan result:{}", JSONUtil.toJsonStr(result));
        if (!LanHaiResult.isSuccess(result)) {
            throw new BusinessException("还款计划查询失败" + result.getData().getHead().getReturnMessage());
        }

        return result.getData().getBody();
    }


    /**
     * 批量更新还款计划
     */
    private void batchUpdateRepaymentPlans(Integer orderId, List<LanHaiRepayPlanQueryResponse.RepayPlanItem> planList) {
        log.info("LanHaiServiceImpl.batchUpdateRepaymentPlans start, orderId:{}, size:{}", orderId, planList.size());

        // 1. 查询所有已存在的还款记录
        Map<String, FundRepaymentInfoEntity> existingRecords = new HashMap<>();
        List<FundRepaymentInfoEntity> existingList = fundRepaymentInfoMapper.selectList(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                .eq(FundRepaymentInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0));

        for (FundRepaymentInfoEntity entity : existingList) {
            String key = buildRecordKey(entity);
            existingRecords.put(key, entity);
        }

        List<FundRepaymentInfoEntity> insertList = new ArrayList<>();
        List<FundRepaymentInfoEntity> updateList = new ArrayList<>();

        //计算所有本金
        BigDecimal totalPrincipal = BigDecimal.ZERO;
        for (LanHaiRepayPlanQueryResponse.RepayPlanItem plan : planList) {
            String key = orderId + "_" + plan.getCurrentNum() + "_" + FundEnum.LAN_HAI.getValue();
            FundRepaymentInfoEntity entity = existingRecords.get(key);

            if (entity == null) {
                entity = new FundRepaymentInfoEntity();
                updateRepaymentPlan(orderId, plan, entity);
                insertList.add(entity);
            } else {
                updateRepaymentPlan(orderId, plan, entity);
                updateList.add(entity);
            }
            totalPrincipal = totalPrincipal.add(plan.getRepayPrincipal());
        }

        // 3. 批量操作
        if (CollUtil.isNotEmpty(insertList)) {
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);


            //增加担保费
            //查询产品信息
            ProductInfoEntity productInfoEntity = productInfoMapper.selectById(orderInfo.getProductId());

            // PMT函数等效计算（返回正值）
            BigDecimal rate = productInfoEntity.getIrr().divide(new BigDecimal("12"));  // 月利率
            int nper = productInfoEntity.getTerm();          // 还款期数
            BigDecimal pv = totalPrincipal;         // 贷款本金
            // PMT函数等效计算
            BigDecimal monthlyPayment;
            if (Objects.equals(rate.compareTo(BigDecimal.ZERO), 0)) {
                monthlyPayment = pv.divide(new BigDecimal(nper), 2, RoundingMode.HALF_UP);
            } else {
                BigDecimal rateAdd = rate.add(BigDecimal.ONE);
                double pow = Math.pow(rateAdd.doubleValue(), nper);
                BigDecimal multiply = pv.multiply(rate);
                BigDecimal multiplyTotal = multiply.multiply(BigDecimal.valueOf(pow));
                double pow1 = pow - 1;
                log.info("ContractFileService.getContractSnapshot rate:{} rateAdd:{} pow:{} multiply:{} multiplyTotal:{} pow1:{}", rate, rateAdd, pow, multiply, multiplyTotal, pow1);
                monthlyPayment = multiplyTotal.divide(BigDecimal.valueOf(pow1), 2, RoundingMode.HALF_UP);
            }
            //修改应还担保费
            insertList.forEach(item -> {
                BigDecimal guaraFeeAmount = monthlyPayment.subtract(item.getRepaymentAmountTotal());
                item.setRepaymentGuaraFeeAmount(guaraFeeAmount);
            });

            fundRepaymentInfoService.saveBatch(insertList);
        }

        if (CollUtil.isNotEmpty(updateList)) {
            fundRepaymentInfoService.updateBatchById(updateList);
        }

        log.info("LanHaiServiceImpl.batchUpdateRepaymentPlans completed, inserts:{}, updates:{}", insertList.size(), updateList.size());
    }

    /**
     * 构建唯一键（用于内存匹配）
     */
    private String buildRecordKey(FundRepaymentInfoEntity entity) {
        return entity.getOrderId() + "_" + entity.getTerm() + "_" + entity.getFundId();
    }


    public void updateRepaymentPlan(Integer orderId, LanHaiRepayPlanQueryResponse.RepayPlanItem plan, FundRepaymentInfoEntity repaymentInfoEntity) {
        // 获取当前期次
        Integer term = plan.getCurrentNum();
        // 记录日志信息
        log.info("LanHaiServiceImpl.updateRepaymentPlan orderId:{}, term:{} plan:{}", orderId, term, plan);
        // 获取还款状态
        LanHaiDictEnum.RepayPlanStatusEnum repayStatus = plan.getRepayStatus();
        FundRepayStatusEnum fundRepayStatusEnum = null;

        // 根据还款状态设置资金还款状态枚举
        switch (repayStatus) {
            case TO_BE_REPAID -> fundRepayStatusEnum = FundRepayStatusEnum.NONE;
            case PART_REPAID -> fundRepayStatusEnum = FundRepayStatusEnum.PART_RETURN;
            case REPAYED -> fundRepayStatusEnum = FundRepayStatusEnum.SETTLED;
            case OVERDUE -> fundRepayStatusEnum = FundRepayStatusEnum.OVERDUE;
            case PART_OVERDUE_REPAID -> fundRepayStatusEnum = FundRepayStatusEnum.OVERDUE;
            case OVERDUE_REPAYED -> fundRepayStatusEnum = FundRepayStatusEnum.SETTLED;
            case SETTLED -> fundRepayStatusEnum = FundRepayStatusEnum.SETTLED;
        }
        // 根据逾期天数和还款状态设置实还日期
        LocalDate repaymentDate = repaymentInfoEntity.getRepaymentDate();
        Integer overdueDays = plan.getOverdueDays();

        //结清日期
        LocalDate settleDate = null;

        if (overdueDays == 0 && (repayStatus == LanHaiDictEnum.RepayPlanStatusEnum.REPAYED)) {
            //获取最近还款日期
            FundRepaymentDeductEntity deductEntity = fundRepaymentDeductMapper.selectOne(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                    .select(FundRepaymentDeductEntity::getRepayDate)
                    .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                    .eq(FundRepaymentDeductEntity::getTerm, term)
                    .eq(FundRepaymentDeductEntity::getFundId, FundEnum.LAN_HAI.getValue())
                    .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                            FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                            FundDeductBizTypeEnums.PAYMENT,
                            FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                            FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                            FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE,
                            FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION,
                            FundDeductBizTypeEnums.LIAN_LIAN_BUCKLE)
                    )
                    .ne(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                    .orderByDesc(FundRepaymentDeductEntity::getId)
                    .last("limit 1")
            );
            if (ObjUtil.isNotNull(deductEntity)) {
                repaymentInfoEntity.setActuallyDate(deductEntity.getRepayDate());
            } else {
                repaymentInfoEntity.setActuallyDate(repaymentDate);
            }
        } else if (overdueDays == 0 && repayStatus == LanHaiDictEnum.RepayPlanStatusEnum.SETTLED) {
            FundRepaymentDeductEntity deductEntity = fundRepaymentDeductMapper.selectOne(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                    .select(FundRepaymentDeductEntity::getRepayDate)
                    .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                    .eq(FundRepaymentDeductEntity::getTerm, term)
                    .eq(FundRepaymentDeductEntity::getFundId, FundEnum.LAN_HAI.getValue())
                    .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                            FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                            FundDeductBizTypeEnums.PAYMENT,
                            FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                            FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                            FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE,
                            FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION,
                            FundDeductBizTypeEnums.LIAN_LIAN_BUCKLE)
                    )
                    .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                    .orderByDesc(FundRepaymentDeductEntity::getId)
                    .last("limit 1")
            );
            if (deductEntity == null) {
                deductEntity = fundRepaymentDeductMapper.selectOne(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                        .select(FundRepaymentDeductEntity::getRepayDate)
                        .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                        .eq(FundRepaymentDeductEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                FundDeductBizTypeEnums.PAYMENT,
                                FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE,
                                FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION,
                                FundDeductBizTypeEnums.LIAN_LIAN_BUCKLE)
                        )
                        .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                        .orderByDesc(FundRepaymentDeductEntity::getId)
                        .last("limit 1")
                );
            }

            if (ObjUtil.isNotNull(deductEntity)) {
                settleDate = deductEntity.getRepayDate();
            }
            if (ObjUtil.isNotNull(settleDate)) {
                repaymentInfoEntity.setActuallyDate(settleDate);
            }

        } else if (repayStatus == LanHaiDictEnum.RepayPlanStatusEnum.OVERDUE_REPAYED || repayStatus == LanHaiDictEnum.RepayPlanStatusEnum.SETTLED) {
            // 如果还款状态为逾期已结清，实还日期等于应还款日期加上逾期天数
            repaymentInfoEntity.setActuallyDate(repaymentDate.plusDays(overdueDays));
        } else {
            // 如果有其他逻辑需求，可以在这里添加，例如逾期但尚未结清
            repaymentInfoEntity.setActuallyDate(null);
        }
        // 设置订单ID
        repaymentInfoEntity.setOrderId(orderId);
        repaymentInfoEntity.setFundId(FundEnum.LAN_HAI.getValue());

        // 设置还款期次
        repaymentInfoEntity.setTerm(term);
        // 解析应还款日期字符串为LocalDate对象
        repaymentInfoEntity.setRepaymentDate(plan.getRepayDate());


        // 设置还款状态
        repaymentInfoEntity.setRepaymentStatus(fundRepayStatusEnum);


        FundRepaymentDeductEntity deductSettleEntity = fundRepaymentDeductMapper.selectOne(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                .eq(FundRepaymentDeductEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                        FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                        FundDeductBizTypeEnums.PAYMENT,
                        FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                        FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                        FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE,
                        FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION,
                        FundDeductBizTypeEnums.LIAN_LIAN_BUCKLE)
                )
                .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                .eq(FundRepaymentDeductEntity::getChannelPayStatus, 1)
                .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                .orderByDesc(FundRepaymentDeductEntity::getId)
                .last("limit 1")
        );

        if (!ObjUtil.equals(repayStatus, LanHaiDictEnum.RepayPlanStatusEnum.SETTLED) &&
                !(ObjUtil.isNotNull(deductSettleEntity)
                        && ObjUtil.equals(deductSettleEntity.getTerm(), plan.getCurrentNum())
                        && ObjUtil.equals(deductSettleEntity.getRepayType(), FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                        && ObjUtil.equals(deductSettleEntity.getChannelPayStatus(), 1))
        ) {
            //如果存在并且状态为结清只更新实还金额

            // 设置应还总金额
            repaymentInfoEntity.setRepaymentAmountTotal(plan.getRepayAmount());
            // 设置应还本金
            repaymentInfoEntity.setRepaymentPrincipal(plan.getRepayPrincipal());
            // 设置应还利息
            repaymentInfoEntity.setRepaymentInterest(plan.getRepayInterest());
            // 设置应还费用金额
            repaymentInfoEntity.setRepaymentPsFeeAmount(plan.getRepayFee());
            // 设置应还罚息
            repaymentInfoEntity.setRepaymentPenaltyInterest(plan.getRepayOverdueFee());
            // 设置应还复利
            repaymentInfoEntity.setRepaymentPsCommOdAmount(plan.getRepayCompoundInterest());

            // 初始状态违约金为0
            repaymentInfoEntity.setActuallyPenalty(BigDecimal.ZERO);

        }

        // 判断是否逾期
        boolean isOverdue = false;
        if (ObjUtil.equals(FundRepayStatusEnum.OVERDUE, fundRepayStatusEnum)) {
            isOverdue = true;
        } else if (ObjUtil.equals(repayStatus, LanHaiDictEnum.RepayPlanStatusEnum.SETTLED) || (ObjUtil.isNotNull(deductSettleEntity)
                && ObjUtil.equals(deductSettleEntity.getTerm(), plan.getCurrentNum())
                && ObjUtil.equals(deductSettleEntity.getRepayType(), FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                && ObjUtil.equals(deductSettleEntity.getChannelPayStatus(), 1)
        )) {
            isOverdue = repaymentInfoEntity.getActuallyDate().isAfter(repaymentInfoEntity.getRepaymentDate());
            // 计算实还金额
            BigDecimal actuallyAmountTotal = plan.getRepayAmount();
            // 设置实还总金额
            repaymentInfoEntity.setActuallyAmountTotal(actuallyAmountTotal);
        } else {
            // 调用服务方法判断是否逾期
            isOverdue = fundRepaymentInfoService.determineOverdue(repaymentInfoEntity.getRepaymentAmountTotal(), repaymentInfoEntity.getActuallyAmountTotal(),
                    LocalDate.now(), repaymentInfoEntity.getRepaymentDate(), repaymentInfoEntity.getActuallyDate());
            // 计算实还金额
            BigDecimal actuallyAmountTotal = plan.getRepayAmount().subtract(plan.getLeftRepayAmount());
            // 设置实还总金额
            repaymentInfoEntity.setActuallyAmountTotal(actuallyAmountTotal);
        }

        // 设置是否逾期标志
        repaymentInfoEntity.setIsOverdue(isOverdue ? 1 : 0);


        // 计算并设置实还本金、实还利息、实还罚息、实还复利
        repaymentInfoEntity.setActuallyPrincipal(plan.getRepayPrincipal().subtract(plan.getLeftRepayPrincipal()));
        repaymentInfoEntity.setActuallyInterest(plan.getRepayInterest().subtract(plan.getLeftRepayInterest()));
        repaymentInfoEntity.setActuallyPenaltyInterest(plan.getRepayOverdueFee().subtract(plan.getLeftRepayOverdueFee()));
        repaymentInfoEntity.setActuallyCommOdAmount(plan.getRepayCompoundInterest().subtract(plan.getLeftRepayCompoundInterest()));

        //优惠费用、利息
        repaymentInfoEntity.setDiscountInterest(plan.getDiscountInterest());
        repaymentInfoEntity.setDiscountFee(plan.getDiscountFee());
        repaymentInfoEntity.setLeftDiscountFee(plan.getLeftRepayFee());
        repaymentInfoEntity.setLeftDiscountInterest(plan.getLeftRepayInterest());
    }

    /**
     * 获取LPR文件
     *
     * @return
     */
    @Override
    public Boolean getLprFile() {
        log.info("LanHaiServiceImpl.getLprFile start");
        boolean flag = false;
        try {
            FundResourceDTO fundResourceDTO = new FundResourceDTO();
            List<FundResourceDTO.Resource> resourceList = new ArrayList<>();
            FundResourceDTO.Resource resource = new FundResourceDTO.Resource();
            String path = "/$rootdirectory/$merchantid/$yyyymmdd/bobtochannel/file/cms_rate_info.txt";

            //字符串替换
            path = path.replace("$rootdirectory", "app");
            path = path.replace("$merchantid", lanHaiConfig.getMerchantId());
            // 获取当前日期的前一天日期
            Date yesterday = DateUtil.offsetDay(new Date(), -1);
            // 格式化日期为 "yyyyMMdd"
            String formattedYesterday = DateUtil.format(yesterday, "yyyyMMdd");
            // 替换路径中的 "$yyyymmdd" 为前一天的日期
            path = path.replace("$yyyymmdd", formattedYesterday);
            //根据文件名称_前面数字为$image_kind
                /*if (!envUtil.isPrd()){
                    path = "/app/longhuan/20240524/bobtochannel/file/cms_rate_info.txt";
                }*/
            log.info("LanHaiServiceImpl.getLprFile - path:{}", path);
            resource.setFilePath(path);
            resource.setFundFileCode(null);
            fundResourceDTO.setFund(FundEnum.LAN_HAI);
            resourceList.add(resource);
            fundResourceDTO.setResourceList(resourceList);
            Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceDownload(fundResourceDTO);
            if (!Result.isSuccess(listResult)) {
                throw new BusinessException("更新lpr失败");
            }
            List<FundResourceResultDTO> data = listResult.getData();
            String resourceId = data.get(0).getResourceId();

            //todo 解析 文件
            List<LanHaiLprVo> LanHaiLprList = null;

            LanHaiLprList = parseSOHTxt(resourceId);

            log.info("LanHaiServiceImpl.getLprFile - LanHaiLprList:{}", LanHaiLprList);
            if (CollUtil.isNotEmpty(LanHaiLprList) && LanHaiLprList.size() > 1) {
                LanHaiLprVo lanHaiLprVo = LanHaiLprList.get(LanHaiLprList.size() - 2);
                //更新蓝海lh_lpr
                if (lanHaiLprVo.getBaseRate() != null && StrUtil.equals(lanHaiLprVo.getStatus(), "1") && StrUtil.equals(lanHaiLprVo.getInterestRateType(), "020")) {
                    MPJLambdaWrapper<FundProductMappingEntity> wrapper = new MPJLambdaWrapper<FundProductMappingEntity>()
                            .selectAll(FundProductMappingEntity.class)
                            .selectAs(ProductInfoEntity::getYearRate, FundProductMappingVO::getYearRate)
                            .leftJoin(ProductInfoEntity.class, ProductInfoEntity::getId, FundProductMappingEntity::getProductId)
                            .eq(FundProductMappingEntity::getFundId, FundEnum.LAN_HAI.getValue())
                            .eq(FundProductMappingEntity::getDeleteFlag, 0);
                    List<FundProductMappingVO> fundProductMappingList = productFundMappingMapper.selectJoinList(FundProductMappingVO.class, wrapper);
                    if (CollUtil.isNotEmpty(fundProductMappingList)) {
                        List<Integer> ids = fundProductMappingList.stream().map(FundProductMappingVO::getId).toList();
                        FundProductMappingEntity fundProductMappingEntity = new FundProductMappingEntity();
                        fundProductMappingEntity.setLhLpr(lanHaiLprVo.getBaseRate());
                        productFundMappingMapper.update(fundProductMappingEntity, new LambdaUpdateWrapper<FundProductMappingEntity>()
                                .in(FundProductMappingEntity::getId, ids));
                    }
                        /*fundProductMappingList.stream().forEach(item -> {
                            ProductInfoEntity productInfoEntity = productInfoMapper.selectById(item.getProductId());
                            //(年利率 - 资方lpr) * 100)  保留两位小数
                            BigDecimal baseRate = lanHaiLprVo.getBaseRate().divide(new BigDecimal("100"),  4, RoundingMode.HALF_UP);
                            BigDecimal lhLpr = productInfoEntity.getIrr().subtract(baseRate).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                            fundProductMappingEntity.setId(item.getId());
                            fundProductMappingEntity.setLhLpr(lhLpr);
                            productFundMappingMapper.updateById(fundProductMappingEntity);
                        });*/
                }
            }
            flag = true;
        } catch (BusinessException e) {
            throw new RuntimeException(e);
        }
        return flag;
    }

    //    private List<LanHaiLprVo> parseSOHTxt(String fileUid) {
//        log.info("FileResourceServiceImpl.parseVerticalTxtByFileUid start: fileUid={}", fileUid);
//
//        // 查询文件资源实体
//        LambdaQueryWrapper<FileResourceEntity> resourceLqw = new LambdaQueryWrapper<>();
//        resourceLqw.eq(FileResourceEntity::getFileUid, fileUid);
//        FileResourceEntity fileResourceEntity = fileResourceMapper.selectOne(resourceLqw, false);
//        if (fileResourceEntity == null) {
//            log.warn("FileResourceServiceImpl.parseVerticalTxtByFileUid file not found: fileUid={}", fileUid);
//            return new ArrayList<>();
//        }
//        String filePath = fileResourceEntity.getPrePath() + fileResourceEntity.getFilePath();
//        List<LanHaiLprVo> vos = new ArrayList<>();
//        try {
//            vos = parseLanHaiLprVoTxt(filePath);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        return vos;
//    }
    private List<LanHaiLprVo> parseSOHTxt(String fileUid) {
        try {
            log.info("FileResourceServiceImpl.parseVerticalTxtByFileUid start: fileUid={}", fileUid);
            ResponseEntity<byte[]> responseEntity = resourceFeign.fileContent(fileUid);

            if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                byte[] fileBytes = responseEntity.getBody();

                // 将字节流转为字符串
                String content = new String(fileBytes, StandardCharsets.UTF_8);

                // 按行读取
                List<LanHaiLprVo> dataList = new ArrayList<>();
                BufferedReader reader = new BufferedReader(new StringReader(content));

                String line;
                while ((line = reader.readLine()) != null) {
                    // 使用 SOH 控制字符 \u0001 分割字段
                    String[] fields = line.trim().split("\u0001");

                    if (fields.length < 10) {
                        log.warn("字段数量不足，跳过该行: {}", line);
                        continue;
                    }

                    try {
                        LanHaiLprVo vo = new LanHaiLprVo();
                        vo.setInterestRateId(fields[0]);             // 3位
                        vo.setInterestRateType(fields[1]);           // 3位
                        vo.setCurrency(fields[2]);                   // 2位
                        vo.setEffectiveDate(fields[3]);              // 8位
                        vo.setTermUnit(fields[4]);                   // 1位
                        vo.setTermCount(new BigDecimal(fields[5]));  // 5位整数
                        vo.setBaseRate(new BigDecimal(fields[6]));   // 12位，保留8位小数
                        vo.setStatus(fields[7]);                     // 1位
                        vo.setRateCategory(fields[8]);               // 25位（未使用）
                        vo.setEntryUserId(fields[9]);                // 20位（未使用）

                        dataList.add(vo);
                    } catch (NumberFormatException e) {
                        log.error("数值转换失败: {}", line, e);
                    }
                }

                // 使用 dataList 做后续处理
                return dataList;

            } else {
                throw new BusinessException("下载文件失败，HTTP 状态码：" + responseEntity.getStatusCodeValue());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    public static List<LanHaiLprVo> parseLanHaiLprVoTxt(String filePath) throws FileNotFoundException, IOException {
        List<LanHaiLprVo> dataList = new ArrayList<>();
        File file = new File(filePath);

        if (!file.exists()) {
            throw new FileNotFoundException("文件不存在: " + filePath);
        }

        try (FileInputStream fis = new FileInputStream(file);
             BufferedReader reader = new BufferedReader(new InputStreamReader(fis, StandardCharsets.UTF_8))) {

            String line;
            while ((line = reader.readLine()) != null) {
                // 使用 SOH 控制字符 \u0001 分割字段
                String[] fields = line.trim().split("\u0001");
                // 创建 VO 并填充数据
                LanHaiLprVo vo = new LanHaiLprVo();
                vo.setInterestRateId(fields[0]);             // 3位
                vo.setInterestRateType(fields[1]);           // 3位
                vo.setCurrency(fields[2]);                   // 2位
                vo.setEffectiveDate(fields[3]);              // 8位
                vo.setTermUnit(fields[4]);                   // 1位
                vo.setTermCount(new BigDecimal(fields[5]));  // 5位整数
                vo.setBaseRate(new BigDecimal(fields[6]));   // 12位，保留8位小数
                vo.setStatus(fields[7]);                     // 1位
                vo.setRateCategory(fields[8]);               // 25位（未使用）
                vo.setEntryUserId(fields[9]);                // 20位（未使用）
                dataList.add(vo);
            }
        }

        return dataList;
    }

    @Override
    public List<OrderContractEntity> getLhContractStatus(Integer orderId) {

        // 蓝海-抵押申请表-LH_LH_0010 蓝海-抵押权人委托书LH_LH_0009
        List<FileTemplateInfoEntity> fileTemplateInfoList = fileTemplateInfoMapper.selectList(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .eq(FileTemplateInfoEntity::getType, 1)
                .in(FileTemplateInfoEntity::getTemplateNumber, "LH_LH_0010", "LH_LH_0009"));
        log.info("LanHaiServiceImpl.getLhContractStatus - fileTemplateInfoList:{}", fileTemplateInfoList);
        if (CollUtil.isNotEmpty(fileTemplateInfoList)) {
            List<Integer> templateIdIds = fileTemplateInfoList.stream().map(FileTemplateInfoEntity::getId).toList();
            List<OrderContractEntity> orderContractList = orderContractMapper.selectList(new LambdaQueryWrapper<OrderContractEntity>()
                    .eq(OrderContractEntity::getOrderId, orderId)
                    .in(OrderContractEntity::getTemplateId, templateIdIds)
                    .eq(OrderContractEntity::getDeleteFlag, 0));
            log.info("LanHaiServiceImpl.getLhContractStatus - orderContractList:{}", orderContractList);
            return orderContractList;
            /*for (FileTemplateInfoEntity fileTemplateInfoEntity : fileTemplateInfoList) {
                OrderContractEntity orderContract = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                        .eq(OrderContractEntity::getOrderId, orderId)
                        .eq(OrderContractEntity::getTemplateId, fileTemplateInfoEntity.getId())
                        .eq(OrderContractEntity::getSignStatus, 2)
                        .eq(OrderContractEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderContractEntity::getCreateTime)
                        .last("limit 1"));
                if (ObjUtil.isNull(orderContract)) {
                    return false;
                }
            }*/
        }
        return null;
    }

    @Override
    public Boolean disuseLhContract(Integer orderId) {
        // 蓝海-抵押申请表-LH_LH_0010 蓝海-抵押权人委托书LH_LH_0009
        List<FileTemplateInfoEntity> fileTemplateInfoList = fileTemplateInfoMapper.selectList(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .in(FileTemplateInfoEntity::getTemplateNumber, "LH_LH_0010", "LH_LH_0009"));
        log.info("LanHaiServiceImpl.disuseLhContract - fileTemplateInfoList:{}", fileTemplateInfoList);
        if (CollUtil.isNotEmpty(fileTemplateInfoList)) {
            List<Integer> ids = fileTemplateInfoList.stream().map(FileTemplateInfoEntity::getId).toList();
            OrderContractEntity orderContract = new OrderContractEntity();
            orderContract.setDeleteFlag(1);
            orderContractMapper.update(orderContract, new LambdaQueryWrapper<OrderContractEntity>()
                    .eq(OrderContractEntity::getOrderId, orderId)
                    .eq(OrderContractEntity::getDeleteFlag, 0)
                    .in(OrderContractEntity::getTemplateId, ids));
        }
        return true;
    }

    @Override
    public Boolean preSaveFaceId(Integer preId, String resourceId) {
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectOne(new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
                .eq(PreApprovalApplyInfoEntity::getId, preId)
                .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
                .orderByDesc(PreApprovalApplyInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNotNull(preApprovalApplyInfoEntity)) {
            preApprovalApplyInfoEntity.setFaceId(resourceId);
            preApprovalApplyInfoMapper.updateById(preApprovalApplyInfoEntity);
            return true;
        }
        return false;
    }

    @Override
    public String preQueryFaceId(Integer preId) {
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectOne(new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
                .eq(PreApprovalApplyInfoEntity::getId, preId)
                .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
                .orderByDesc(PreApprovalApplyInfoEntity::getCreateTime)
                .last("limit 1")
        );
        return preApprovalApplyInfoEntity.getFaceId();
    }

    /**
     * 抵押状态回调
     *
     * @param callback
     * @return
     */
    @Override
    public List<LanHaiCallBackRequest> mortgageCallback(List<LanHaiCallBackResponse> callback) {
        log.info("LanHaiServiceImpl.mortgageCallback body:{}", callback);
        List<LanHaiCallBackRequest> callBacks = new ArrayList<>();
        callback.stream().forEach(
                lanHaiCallBackResponse -> {
                    //蓝海抵押
                    String arguments = lanHaiCallBackResponse.getArguments();
                    LanHaiResult.Result<LanHaiCallMortgageRequest> callBackInfoResult = null;
                    LanHaiCallBackRequest response = new LanHaiCallBackRequest();
                    LanHaiCallMortgageRequest body = null;

                    JavaType javaType = objectMapper.getTypeFactory().constructParametricType(LanHaiResult.Result.class, LanHaiCallMortgageRequest.class);
                    try {
                        callBackInfoResult = objectMapper.readValue(arguments, javaType);
                        body = callBackInfoResult.getBody();

                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                    try {
                        if (ObjUtil.isNull(body)) {
                            response.setId(lanHaiCallBackResponse.getId());
                            response.setStatus("2");
                            response.setRemark("参数解析失败");
                            callBacks.add(response);
                        } else {
                            response = mortgageBackByInfo(body);
                            response.setId(lanHaiCallBackResponse.getId());
                            callBacks.add(response);
                        }
                    } catch (Exception e) {
                        response.setId(lanHaiCallBackResponse.getId());
                        response.setStatus("2");
                        response.setRemark("系统异常");
                        callBacks.add(response);
                    }
                }
        );
        return callBacks;
    }

    /**
     * 贷后补件上传
     */
    @Override
    public Boolean payAfterReplenish(Integer orderId) {
        lanHaiExpandService.payAfterReplenish(orderId);
        return true;
    }

    /**
     * 贷后补件上传查证
     */
    @Override
    public LanHaiAfterReplenishVerifyResponse payAfterReplenishQuery(Integer orderId) {
        return lanHaiExpandService.payAfterReplenishQuery(orderId);
    }

    /**
     * 支用申请试算
     */
    @Override
    public LanHaiRepayCalcResponse creditLimitInfo(LanHaiCreditLimitInfoDTO dto) {
        log.info("LanHaiServiceImpl.creditLimitTerminate - dto:{}", JSONUtil.toJsonStr(dto));
        Integer orderId = dto.getOrderId();
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(finalFundInfo)) {
            log.info("LanHaiServiceImpl.creditLimitTerminate not finalFundInfo - orderId:{} ", orderId);
            throw new BusinessException("借款信息不存在");
        }
        LanHaiCreditLimitInfoRequest request = new LanHaiCreditLimitInfoRequest();
        request.setCreditLimitId(finalFundInfo.getCreditNo());
        request.setLoanPeriod(dto.getLoanPeriod());
        request.setLoanUnit(LanHaiDictEnum.TermUnitEnum.PERIOD.getCode());
        request.setLoanAmount(dto.getLoanAmount());
        request.setRepayment(LanHaiDictEnum.RepayMethodEnum.EQUAL_INSTALLMENTS.getCode());
        request.setRepaymentDay(dto.getRepaymentDay());
        request.setProductId(lanHaiConfig.getProductId());
        request.setTrialRate(dto.getTrialRate());
        LanHaiResult<LanHaiRepayCalcResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_TRIAL_LOAN, request, LanHaiRepayCalcResponse.class, getOriRequestSerialNo());

        if (!LanHaiResult.isSuccess(result)) {
            log.info("LanHaiServiceImpl.creditLimitTerminate - orderId:{} - result:{}", orderId, JSONUtil.toJsonStr(result));
            throw new BusinessException(result.getData().getHead().getReturnMessage());
        }
        log.info("LanHaiServiceImpl.creditLimitTerminate - orderId:{} - result:{}", orderId, JSONUtil.toJsonStr(result));


        return result.getData().getBody();
    }


    /**
     * 3.1.3.1. 还款试算
     */
    @Override
    public LanHaiTrialRepayResponse trialRepayQuery(Integer orderId, BigDecimal amount, boolean isSettle) {
        log.info("LanHaiServiceImpl.trialRepayQuery - orderId:{} - amount:{} - isSettle:{}", orderId, amount, isSettle);
        //查询借款编号
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjectUtil.isEmpty(finalFundInfo)) {
            throw new BusinessException("借款编号不存在");
        }
        LanHaiTrialRepayRequest request = new LanHaiTrialRepayRequest();
        request.setLoanInvoiceId(finalFundInfo.getLoanNo());
        request.setAmount(amount);
        request.setRepayAmountType(isSettle ? LanHaiDictEnum.RepayAmtTypeEnum.PRINCIPAL : LanHaiDictEnum.RepayAmtTypeEnum.TOTAL_AMOUNT);
        request.setAssetRepayKind(isSettle ? LanHaiDictEnum.AssetRepayKindEnum.EARLY_REPAYMENT : LanHaiDictEnum.AssetRepayKindEnum.NORMAL_REPAYMENT);
        LanHaiResult<LanHaiTrialRepayResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_TRIAL_REPAY, request, LanHaiTrialRepayResponse.class, getOriRequestSerialNo());
        if (!LanHaiResult.isSuccess(result)) {
            log.info("LanHaiServiceImpl.trialRepayQuery - orderId:{} - result:{}", orderId, JSONUtil.toJsonStr(result));
            throw new BusinessException(result.getData().getHead().getReturnMessage());
        }
        log.info("LanHaiServiceImpl.trialRepayQuery - orderId:{} - result:{}", orderId, JSONUtil.toJsonStr(result));
        return result.getData().getBody();
    }

    private LanHaiCallBackRequest mortgageBackByInfo(LanHaiCallMortgageRequest preCallBackInfo) {
        log.info("LanHaiServiceImpl.mortgageBackByInfo - preCallBackInfo:{}", JSONUtil.toJsonStr(preCallBackInfo));
        LanHaiCallBackRequest request = new LanHaiCallBackRequest();
        String type = preCallBackInfo.getType();
        String transactionId = preCallBackInfo.getTransactionId();
        LanHaiMortgageEnums businessStatus = preCallBackInfo.getBusinessStatus();
        boolean flag = false;
        if (StrUtil.equals(type, "1")) {
            try {
                FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(new LambdaQueryWrapper<FundApiDictEntity>()
                        .eq(FundApiDictEntity::getValue, transactionId)
                        .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.MORTGAGE)
                        .eq(FundApiDictEntity::getCode, FundApiDictEnum.MORTGAGE_REQUEST_APPLY_NO)
                        .eq(FundApiDictEntity::getDeleteFlag, 0)
                        .orderByDesc(FundApiDictEntity::getCreateTime)
                        .last("limit 1")
                );
                if (ObjectUtil.isEmpty(fundApiDictEntity)) {
                    throw new BusinessException("抵押申请流水号不存在");
                }
                //如果抵押方式是线上，才会变更抵押状态
                CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                        .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                        .eq(CustomerMortgageInfoEntity::getOrderId, fundApiDictEntity.getLinkId())
                        .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                        .last("limit 1")
                );
                if (ObjUtil.isNotNull(customerMortgageInfoEntity)&& ObjectUtil.equals(customerMortgageInfoEntity.getMortgageType(),0) ){
                    updateMortgageApplyStatues(fundApiDictEntity.getLinkId(), businessStatus, preCallBackInfo.getOperationStatus(),preCallBackInfo.getRejectReason());
                }
                flag = true;
            } catch (BusinessException e) {
                flag = false;
            } finally {
                request.setStatus(flag ? "1" : "2");
            }
        }
        if (StrUtil.equals(type, "2")) {
            FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(new LambdaQueryWrapper<FundApiDictEntity>()
                    .eq(FundApiDictEntity::getValue, transactionId)
                    .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.UNDO_MORTGAGE)
                    .eq(FundApiDictEntity::getCode, FundApiDictEnum.UNDO_MORTGAGE_REQUEST_APPLY_NO)
                    .eq(FundApiDictEntity::getDeleteFlag, 0)
                    .orderByDesc(FundApiDictEntity::getCreateTime)
                    .last("limit 1")
            );
            try {
                if (ObjectUtil.isEmpty(fundApiDictEntity)) {
                    throw new BusinessException("抵押申请流水号不存在");
                }
                updateUndoMortgageApplyStatues(fundApiDictEntity.getLinkId(), businessStatus, preCallBackInfo.getOperationStatus());
                flag = true;
            } catch (Exception e) {
                flag = false;
            } finally {
                request.setStatus(flag ? "1" : "2");
            }
        }
        return request;
    }

    /**
     * 3.1.3.3. 还款登记
     */
    @Override
    public String repayRegister(Integer orderId, LanHaiRepayRegisterRequest request, String deductReqNo) {
        log.info("LanHaiServiceImpl.repayRegister - orderId:{} - request:{} - deductReqNo:{}", orderId, JSONUtil.toJsonStr(request), deductReqNo);
        if (StrUtil.isBlank(deductReqNo)) {
            throw new BusinessException("请传入代扣流水号");
        }
        String oriRequestSerialNo = getOriRequestSerialNo();
        LanHaiResult<LanHaiRepayRegisterResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_REPAY_REGISTER, request, LanHaiRepayRegisterResponse.class, oriRequestSerialNo);
        if (!LanHaiResult.isSuccess(result)) {
            log.info("LanHaiServiceImpl.repayRegister - orderId:{} - result:{}", orderId, JSONUtil.toJsonStr(result));
            throw new BusinessException(result.getData().getHead().getReturnMessage());
        }
        //更新还款流水号到对应的记录中
        FundRepaymentDeductEntity deductEntity = new FundRepaymentDeductEntity();
        deductEntity.setFundSyncReqNo(oriRequestSerialNo);
        deductEntity.setFundRepayNo(result.getData().getBody().getApplyId());
        deductEntity.setFundSyncFlag(2);
        fundRepaymentDeductMapper.update(deductEntity, new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                .eq(FundRepaymentDeductEntity::getDeductReqNo, deductReqNo)
                .eq(FundRepaymentDeductEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
        );

        log.info("LanHaiServiceImpl.repayRegister - orderId:{} - result:{}", orderId, JSONUtil.toJsonStr(result));
        return oriRequestSerialNo;
    }

    /**
     * 3.1.5.9. 还款结果查证
     */
    @Override
    public LanHaiRepaymentVerifyResponse repayQuery(Integer orderId, String reqNo) {
        log.info("LanHaiServiceImpl.repayQuery - orderId:{} - reqNo:{}", orderId, reqNo);
        LanHaiRepayVerifyRequest lanHaiRepayVerifyRequest = new LanHaiRepayVerifyRequest();
        lanHaiRepayVerifyRequest.setOriRequestSerialNo(reqNo);
        LanHaiResult<LanHaiRepaymentVerifyResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_REPAYMENT_VERIFY, lanHaiRepayVerifyRequest, LanHaiRepaymentVerifyResponse.class, getOriRequestSerialNo());
        if (!LanHaiResult.isSuccess(result)) {
            log.info("LanHaiServiceImpl.repayQuery - orderId:{} - result:{}", orderId, JSONUtil.toJsonStr(result));
            throw new BusinessException(result.getData().getHead().getReturnMessage());
        }
        log.info("LanHaiServiceImpl.repayQuery - orderId:{} - result:{}", orderId, JSONUtil.toJsonStr(result));
        return result.getData().getBody();
    }

    @Override
    public List<LanHaiCallBackRequest> callPayAfter(List<LanHaiCallBackResponse> list) {
        log.info("LanHaiServiceImpl.handlePayCallback callback:{}", JSONUtil.toJsonStr(list));
        if (CollUtil.isEmpty(list)) {
            log.info("LanHaiServiceImpl.handlePayCallback callback is empty");
            return List.of();
        }

        List<LanHaiCallBackRequest> requests = new ArrayList<>();
        for (LanHaiCallBackResponse lanHaiCallBackResponse : list) {
            CallPayAfterDTO dto = new CallPayAfterDTO();
            try {
                dto = objectMapper.readValue(lanHaiCallBackResponse.getArguments(), CallPayAfterDTO.class);
            } catch (JsonProcessingException e) {
                log.info("LanHaiServiceImpl.handlePayCallback callback json error:{}", e.getMessage());
                LanHaiCallBackRequest lanHaiCallBackRequest = new LanHaiCallBackRequest();
                lanHaiCallBackRequest.setId(lanHaiCallBackResponse.getId());
                lanHaiCallBackRequest.setStatus("2");
                lanHaiCallBackRequest.setRemark("参数解析失败");
                requests.add(lanHaiCallBackRequest);
                continue;
            }
            if (Objects.equals(dto.getBody().getHandlerStatus(), "001")) {
                FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(
                        new LambdaQueryWrapper<FinalFundInfoEntity>()
                                .eq(FinalFundInfoEntity::getLoanAfterReplenishNo, dto.getBody().getApplyId())
                                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                                .last("limit 1")
                );
                if (ObjUtil.isNotNull(finalFundInfoEntity)) {
                    afterLoanPatchesService.update(
                            new LambdaUpdateWrapper<AfterLoanPatchesEntity>()
                                    .set(AfterLoanPatchesEntity::getFundUploadFlag, 1)
                                    .eq(AfterLoanPatchesEntity::getOrderId, finalFundInfoEntity.getOrderId())
                                    .eq(AfterLoanPatchesEntity::getDeleteFlag, 0)
                    );
                    LanHaiCallBackRequest lanHaiCallBackRequest = new LanHaiCallBackRequest();
                    lanHaiCallBackRequest.setId(lanHaiCallBackResponse.getId());
                    lanHaiCallBackRequest.setStatus("1");
                    lanHaiCallBackRequest.setRemark("成功");
                    requests.add(lanHaiCallBackRequest);
                } else {
                    LanHaiCallBackRequest lanHaiCallBackRequest = new LanHaiCallBackRequest();
                    lanHaiCallBackRequest.setId(lanHaiCallBackResponse.getId());
                    lanHaiCallBackRequest.setStatus("2");
                    lanHaiCallBackRequest.setRemark("无订单");
                    requests.add(lanHaiCallBackRequest);
                    log.info("LanHaiServiceImpl.handlePayCallback callback finalFundInfoEntity is null:{}", lanHaiCallBackResponse);
                }
                continue;
            }
            if (Objects.equals(dto.getBody().getHandlerStatus(), "003")) {
                LanHaiCallBackRequest lanHaiCallBackRequest = new LanHaiCallBackRequest();
                lanHaiCallBackRequest.setId(lanHaiCallBackResponse.getId());
                lanHaiCallBackRequest.setStatus("2");
                lanHaiCallBackRequest.setRemark(dto.getBody().getErrMsg());
                requests.add(lanHaiCallBackRequest);
            }
        }
        return requests;
    }

    @Override
    public List<OrderContractEntity> getLhContractRelieveStatus(Integer orderId) {
        // 蓝海-解押申请表-LH_LH_0012 蓝海-抵押权人委托书（解押）-LH_LH_0013
        List<FileTemplateInfoEntity> fileTemplateInfoList = fileTemplateInfoMapper.selectList(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .eq(FileTemplateInfoEntity::getType, 1)
                .in(FileTemplateInfoEntity::getTemplateNumber, "LH_LH_0012", "LH_LH_0013"));
        log.info("LanHaiServiceImpl.getLhContractRelieveStatus - fileTemplateInfoList:{}", fileTemplateInfoList);
        if (CollUtil.isNotEmpty(fileTemplateInfoList)) {
            List<Integer> templateIdIds = fileTemplateInfoList.stream().map(FileTemplateInfoEntity::getId).toList();
            List<OrderContractEntity> orderContractList = orderContractMapper.selectList(new LambdaQueryWrapper<OrderContractEntity>()
                    .eq(OrderContractEntity::getOrderId, orderId)
                    .in(OrderContractEntity::getTemplateId, templateIdIds)
                    .eq(OrderContractEntity::getDeleteFlag, 0));
            log.info("LanHaiServiceImpl.getLhContractRelieveStatus - orderContractList:{}", orderContractList);
            return orderContractList;
        }
        return null;
    }

    @Override
    public Boolean disuseLhContractRelieve(Integer orderId) {
        // 蓝海-解押申请表-LH_LH_0012 蓝海-抵押权人委托书（解押）-LH_LH_0013
        List<FileTemplateInfoEntity> fileTemplateInfoList = fileTemplateInfoMapper.selectList(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .in(FileTemplateInfoEntity::getTemplateNumber, "LH_LH_0012", "LH_LH_0013"));
        log.info("LanHaiServiceImpl.disuseLhContractRelieve - fileTemplateInfoList:{}", fileTemplateInfoList);
        if (CollUtil.isNotEmpty(fileTemplateInfoList)) {
            List<Integer> ids = fileTemplateInfoList.stream().map(FileTemplateInfoEntity::getId).toList();
            OrderContractEntity orderContract = new OrderContractEntity();
            orderContract.setDeleteFlag(1);
            orderContractMapper.update(orderContract, new LambdaQueryWrapper<OrderContractEntity>()
                    .eq(OrderContractEntity::getOrderId, orderId)
                    .eq(OrderContractEntity::getDeleteFlag, 0)
                    .in(OrderContractEntity::getTemplateId, ids));
        }
        return true;
    }

    /**
     * 贷后补件申请构建
     */
    private LanHaiAfterReplenishRequest payAfterReplenishBuild(Integer orderId) {
        return lanHaiExpandService.payAfterReplenishBuild(orderId);
    }


    /**
     * 雷霆抵押状态查询
     *
     * @return
     */
    @Override
    public Boolean thunderGetMortgageStatus() {

        //查询文件配置id
        Integer fileConfigId = null;
        Result<Integer> fileConfigData = resourceFeign.selectFileConfigByCode(FundConstant.DYDJL);
        if (Result.isSuccess(fileConfigData)) {
            fileConfigId = fileConfigData.getData();
        }

        //查询抵押状态为抵押完成的订单信息
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getMortgageState, FundMortgageStatusEnum.PASS.getValue())
                .eq(OrderInfoEntity::getDeleteFlag, 0));

        List<Integer> orderIdList = orderInfoEntityList.stream().map(OrderInfoEntity::getId).toList();

        if (CollUtil.isNotEmpty(orderIdList)) {
            //查询订单文件信息
            List<OrderFileEntity> orderFileEntityList = orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                    .in(OrderFileEntity::getOrderId, orderIdList)
                    .eq(OrderFileEntity::getFileId, fileConfigId)
                    .eq(OrderFileEntity::getDeleteFlag, 0));

            //获取订单idSet
            Set<Integer> orderIdSet = orderFileEntityList.stream().map(OrderFileEntity::getOrderId).collect(Collectors.toSet());

            //两者取差集
            List<Integer> paramOrderIdList = orderIdList.stream().filter(item -> !orderIdSet.contains(item)).toList();
            log.info("LanHaiServiceImpl.thunderGetMortgageStatus,雷霆查询抵押状态,paramOrderIdList :{}", paramOrderIdList);

            if (CollUtil.isNotEmpty(paramOrderIdList)) {
                for (Integer orderId : paramOrderIdList) {
                    List<MultipartFile> multipartFileList = new ArrayList<>();
                    List<String> attachIdList = new ArrayList<>();

                    //根据orderId 查询 transactionId
                    String transactionId = mortgageStatus(orderId);
                    if (StringUtils.isBlank(transactionId)) {
                        log.error("LanHaiServiceImpl.thunderGetMortgageStatus 抵押申请流水号不存在:orderId:{}", orderId);
                        continue;
                    }
                    //调用雷霆查询抵押状态接口
                    AtmResponse<AtmStatus> atmStatusAtmResponse = thunderAtmClient.getMortgageStatus(transactionId);
                    if (!atmStatusAtmResponse.isSuccess()) {
                        log.error("LanHaiServiceImpl.thunderGetMortgageStatus 失败:orderId:{},错误原因:{}", orderId, atmStatusAtmResponse.getErrors().get(0).getStatement());
                        continue;
                    }
                    AtmStatus atmStatus = atmStatusAtmResponse.getData();
                    if (null != atmStatus) {
                        //获取业务状态
                        String businessStatus = atmStatus.getBusinessStatus();
                        //获取登记证书影像资料对象列表
                        List<Map<String, String>> attachments = atmStatus.getAttachments();
                        //S=业务状态:已归档
                        if ("S".equals(businessStatus)) {
                            //遍历获取所有attachId
                            attachments.forEach(attchment ->
                                    attachIdList.add(MapUtils.getString(attchment, "id")));
                        }
                    }
                    log.info("LanHaiServiceImpl.thunderGetMortgageStatus 获得附件attachIdList:{},orderId:{}", attachIdList, orderId);

                    if (CollUtil.isNotEmpty(attachIdList)) {
                        for (String v : attachIdList) {
                            //下载该附件
                            byte[] byteResult = thunderAtmClient.downloadRegCert(v);
                            if (null == byteResult || byteResult.length == 0) {
                                log.error("LanHaiServiceImpl.thunderGetMortgageStatus 下载登记证书影像件资料失败,图片返回为空:attachId{},orderId:{}", v, orderId);
                                continue;
                            }
                            MultipartFile multipartFile = new MockMultipartFile("files", v + ".jpg", "image/jpeg", byteResult);
                            multipartFileList.add(multipartFile);
                        }

                        //上传文件
                        Result<List<FileVO>> listResult = resourceFeign.uploadFile(multipartFileList);
                        log.info("LanHaiServiceImpl.thunderGetMortgageStatus 上传文件,response:{}", JSON.toJSONString(listResult));
                        if (!Result.isSuccess(listResult) || CollUtil.isEmpty(listResult.getData())) {
                            log.error("LanHaiServiceImpl.thunderGetMortgageStatus 上传文件失败:orderId:{}", orderId);
                            continue;
                        }

                        List<FileVO> data = listResult.getData();
                        for (MultipartFile multipartFile : multipartFileList) {
                            //插入订单文件记录
                            OrderFileEntity orderFileEntity = new OrderFileEntity()
                                    .setOrderId(orderId)
                                    .setFileId(fileConfigId)
                                    .setFileName(multipartFile.getOriginalFilename())
                                    .setResourceId(data.get(0).getResourceId())
                                    .setResourceName(data.get(0).getResourceName());
                            orderFileMapper.insert(orderFileEntity);
                        }
                    }
                }
            }
        }
        return true;
    }

    @Override
    public Boolean undoMortgageApply(Integer orderId) {
        log.info("LanHaiServiceImpl.undoMortgageApply start orderId:{}", orderId);
        String transactionId = mortgageStatus(orderId);
        if (StrUtil.isNotEmpty(transactionId)) {
            //抵押状态查询
            LanHaiMortgageEnums mortgageApplyVerify = null;
            try {
                mortgageApplyVerify = mortgageApplyVerify(orderId);
                log.info("LanHaiServiceImpl.undoMortgageApply mortgageApplyVerify:{}", mortgageApplyVerify.getDescription());
            } catch (Exception e) {
                throw new BusinessException(e.getMessage());
            }
            if (!ObjUtil.equals(mortgageApplyVerify, LanHaiMortgageEnums.ARCHIVED)) {
                throw new BusinessException("当前订单状态为:" + mortgageApplyVerify.getDescription() + ",请勿解抵");
            }
        } else {
            throw new BusinessException("未查询到抵押流水号");
        }
        checkProductContractRelieve(orderId);
        //进入解抵
        LanHaiResult<LanHaiUndoMortgageApplyResponse> result = null;
        try {
            LanHaiUndoMortgageApplyRequest request = buildUndoMortgageApplyByOrderId(orderId);
            log.info("LanHaiServiceImpl.LanHaiUndoMortgageApplyRequest request:{}", JSONUtil.toJsonStr(request));
            result = lanHaiClient.execute(LanHaiApiEnums.LH_RELEASE_APPLY, request, LanHaiUndoMortgageApplyResponse.class, getOriRequestSerialNo());
            log.info("LanHaiServiceImpl.LanHaiUndoMortgageApplyRequest result:{}", JSONUtil.toJsonStr(result));
        } catch (Exception e) {
            throw new BusinessException("解抵失败" + e.getMessage());
        }
        if (!LanHaiResult.isSuccess(result)) {
            throw new BusinessException("解抵失败:" + result.getData().getHead().getReturnMessage());
        }
        //将抵押编号存到apidict中
        FundApiDictEntity fundApiDictEntity = new FundApiDictEntity();
        fundApiDictEntity.setLinkId(orderId)
                .setType(FundApiDictTypeEnum.UNDO_MORTGAGE)
                .setCode(FundApiDictEnum.UNDO_MORTGAGE_REQUEST_APPLY_NO)
                .setValue(result.getData().getBody().getTransactionId());
        fundApiDictMapper.insert(fundApiDictEntity);
        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        FinalFundInfoEntity fundApplyInfo = new FinalFundInfoEntity();
        fundApplyInfo.setId(finalFundInfoEntity.getId());
        fundApplyInfo.setFundNode(FundApplyNodeEnums.MORTGAGE_H5_PAGE_REMOVE);
        fundApplyInfo.setFundNodeStatus(FundApplyNodeStatusEnum.SUCCESS);
        fundApplyInfo.setMortgageState(FundMortgageStatusEnum.REMOVE_ING);
        finalFundInfoMapper.updateById(fundApplyInfo);

        // 只要是发生解抵操作 应将将关于订单的解抵完成记录 解抵成功 失败记录全部置为历史记录
        fundUndoMortgageInfoMapper.update(new FundUndoMortgageInfoEntity(), new LambdaUpdateWrapper<FundUndoMortgageInfoEntity>()
                .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                .eq(FundUndoMortgageInfoEntity::getOperateType, "2")
                .notIn(FundUndoMortgageInfoEntity::getMortgageStatus, MortgageEnums.MORTGAGE_STATUS_CANCEL_PROCESSING.getCode().toString(), MortgageEnums.MORTGAGE_STATUS_CANCEL_PROCESSING_HAVE.getCode().toString())
                .set(FundUndoMortgageInfoEntity::getHistory, 1)
                .set(FundUndoMortgageInfoEntity::getDeleteFlag, 1)
        );

        //申请解抵创建解抵记录
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        FundUndoMortgageInfoEntity infoEntity = new FundUndoMortgageInfoEntity();
        infoEntity = fundUndoMortgageInfoMapper.selectOne((new LambdaQueryWrapper<FundUndoMortgageInfoEntity>()
                .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                .in(FundUndoMortgageInfoEntity::getMortgageStatus, MortgageEnums.MORTGAGE_STATUS_CANCEL_PROCESSING.getCode().toString(), MortgageEnums.MORTGAGE_STATUS_CANCEL_PROCESSING_HAVE.getCode().toString())
                .eq(FundUndoMortgageInfoEntity::getOperateType, "2")
                .eq(FundUndoMortgageInfoEntity::getHistory, 0)
                .last("LIMIT 1")
        ));
        if (ObjUtil.isNull(infoEntity)) {
            infoEntity = new FundUndoMortgageInfoEntity();
            infoEntity.setOrderId(orderId);
            infoEntity.setChannelCode(lanHaiConfig.getChannelNo());
            infoEntity.setApplyNo(transactionId);
            if (ObjUtil.isNotNull(orderInfoEntity)) {
                if (orderInfoEntity.getMortgageState() == MortgageEnums.MORTGAGE_STATUS_SUCCESS.getCode()) {
                    infoEntity.setMortgageStatus(MortgageEnums.MORTGAGE_STATUS_CANCEL_PROCESSING.getCode().toString());
                }
                if (orderInfoEntity.getMortgageState() == MortgageEnums.MORTGAGE_STATUS_PRE.getCode()) {
                    infoEntity.setMortgageStatus(MortgageEnums.MORTGAGE_STATUS_CANCEL_PROCESSING_HAVE.getCode().toString());
                }
            }
            infoEntity.setOperateType("2");
            infoEntity.setHistory(0);
            infoEntity.setFundId(FundEnum.FU_MIN.getValue());
            fundUndoMortgageInfoMapper.insert(infoEntity);
        }
        return true;
    }

    @Override
    public String loanFlowCertificate(Integer orderId) {
        Result<Integer> loanFlowCertificate = resourceFeign.selectFileConfigByCode(FundConstant.LAN_HAI_LOAN_FLOW_CERTIFICATE);
        if (!Result.isSuccess(loanFlowCertificate) || loanFlowCertificate.getData() == null) {
            throw new BusinessException("未查询到文件配置");
        }
        List<OrderFileEntity> orderFileEntityList = orderFileMapper.selectList(
                new LambdaQueryWrapper<OrderFileEntity>()
                        .eq(OrderFileEntity::getOrderId, orderId)
                        .eq(OrderFileEntity::getDeleteFlag, 0)
                        .eq(OrderFileEntity::getFileId, loanFlowCertificate.getData())
                        .orderByAsc(OrderFileEntity::getCreateTime)
        );
        if (CollUtil.isEmpty(orderFileEntityList)) {
            String fileName = "";
            List<FundApiDictEntity> fundApiDictEntityList = fundApiDictMapper.selectList(
                    new LambdaQueryWrapper<FundApiDictEntity>()
                            .eq(FundApiDictEntity::getLinkId, orderId)
                            .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.LOAN_FLOW_CERTIFICATE)
                            .eq(FundApiDictEntity::getDeleteFlag, 0)
                            .orderByDesc(FundApiDictEntity::getCreateTime)
            );
            if (CollUtil.isEmpty(fundApiDictEntityList)) {
                FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                        .last("LIMIT 1")
                );
                LanHAiLoanFlowCertificateRequest request = new LanHAiLoanFlowCertificateRequest();
                request.setUserId(fundInfo.getFundUserId());
                request.setLoanInvoiceId(fundInfo.getLoanNo());

                LanHaiResult<LanHaiFlowProofResponse> execute = lanHaiClient.execute(LanHaiApiEnums.LH_LOAN_STATEMENT, request, LanHaiFlowProofResponse.class, getOriRequestSerialNo());
                log.info("LanHaiServiceImpl.loanFlowCertificate end result:{}", JSONUtil.toJsonStr(execute));
                if (ObjUtil.isNull(execute.getData().getBody()) || !Objects.equals(execute.getData().getBody().getStatus(), "080")) {
                    throw new BusinessException("获取放款流水证明失败:" + execute.getData().getHead().getReturnMessage());
                }
                fileName = execute.getData().getBody().getFileName();
                fundApiDictService.save(new FundApiDictEntity()
                        .setLinkId(orderId)
                        .setType(FundApiDictTypeEnum.LOAN_FLOW_CERTIFICATE)
                        .setValue(fileName));
            } else {
                fileName = fundApiDictEntityList.get(0).getValue();
            }
            FundResourceDTO dto = new FundResourceDTO();
            dto.setFund(FundEnum.LAN_HAI);
            List<FundResourceDTO.Resource> resourceList = new ArrayList<>();
            FundResourceDTO.Resource resource = new FundResourceDTO.Resource();
            int firstUnderscoreIndex = fileName.indexOf('_');
            String prefix = (firstUnderscoreIndex != -1)
                    ? fileName.substring(0, firstUnderscoreIndex)
                    : fileName;
            String path = "/app/" + lanHaiConfig.getMerchantId() + "/" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "/bobtochannel/image/" + prefix + "/" + fileName;
            //字符串替换
            path = path.replace("$rootdirectory", "app");
            path = path.replace("$merchantid", lanHaiConfig.getMerchantId());
            // 替换路径中的 "$yyyymmdd" 为前一天的日期
            path = path.replace("$yyyymmdd", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            //根据文件名称_前面数字为$image_kind
                /*if (!envUtil.isPrd()){
                    path = "/app/longhuan/20240524/bobtochannel/file/cms_rate_info.txt";
                }*/
            log.info("LanHaiServiceImpl.loanFlowCertificate - path:{}", path);
            resource.setFilePath(path);
            resource.setFundFileCode(null);
            resourceList.add(resource);
            dto.setResourceList(resourceList);
            Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceDownload(dto);
            if (!Result.isSuccess(listResult)) {
                throw new BusinessException("获取放款流水证明失败");
            }
            orderFileMapper.insert(new OrderFileEntity()
                    .setFileId(loanFlowCertificate.getData())
                    .setResourceId(listResult.getData().get(0).getResourceId())
                    .setOrderId(orderId)
                    .setFileName(fileName)
                    .setResourceName(listResult.getData().get(0).getResourceName())
            );
            return listResult.getData().get(0).getResourceId();
        } else {
            return orderFileEntityList.get(0).getResourceId();
        }
    }

    /**
     * 代偿流水证明
     */
    @Override
    public String compensateFlowCertificate(Integer orderId) {
        Result<Integer> loanFlowCertificate = resourceFeign.selectFileConfigByCode(FundConstant.LAN_HAI_COMPENSATION_STATEMENT);
        if (!Result.isSuccess(loanFlowCertificate) || loanFlowCertificate.getData() == null) {
            throw new BusinessException("未查询到文件配置");
        }
        List<OrderFileEntity> orderFileEntityList = orderFileMapper.selectList(
                new LambdaQueryWrapper<OrderFileEntity>()
                        .eq(OrderFileEntity::getOrderId, orderId)
                        .eq(OrderFileEntity::getDeleteFlag, 0)
                        .eq(OrderFileEntity::getFileId, loanFlowCertificate.getData())
                        .orderByAsc(OrderFileEntity::getCreateTime)
        );
        if (CollUtil.isEmpty(orderFileEntityList)) {
            String fileName = "";
            List<FundApiDictEntity> fundApiDictEntityList = fundApiDictMapper.selectList(
                    new LambdaQueryWrapper<FundApiDictEntity>()
                            .eq(FundApiDictEntity::getLinkId, orderId)
                            .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.COMPENSATION_FLOW_CERTIFICATE)
                            .eq(FundApiDictEntity::getDeleteFlag, 0)
                            .orderByDesc(FundApiDictEntity::getCreateTime)
            );
            if (CollUtil.isEmpty(fundApiDictEntityList)) {
                FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                        .last("LIMIT 1")
                );
                LanHaiCompensateFlowCertificateRequest request = new LanHaiCompensateFlowCertificateRequest();
                request.setUserId(fundInfo.getFundUserId());
                request.setRequestSerialNo(fundInfo.getCompensateApplyNo());

                LanHaiResult<LanHaiFlowProofResponse> execute = lanHaiClient.execute(LanHaiApiEnums.LH_COMPENSATION_STATEMENT, request, LanHaiFlowProofResponse.class, getOriRequestSerialNo());
                log.info("LanHaiServiceImpl.compensateFlowCertificate end result:{}", JSONUtil.toJsonStr(execute));
                if (ObjUtil.isNull(execute.getData().getBody()) || !Objects.equals(execute.getData().getBody().getStatus(), "080")) {
                    throw new BusinessException("获取代偿流水证明失败:" + execute.getData().getHead().getReturnMessage());
                }
                fileName = execute.getData().getBody().getFileName();
                fundApiDictService.save(new FundApiDictEntity()
                        .setLinkId(orderId)
                        .setType(FundApiDictTypeEnum.COMPENSATION_FLOW_CERTIFICATE)
                        .setValue(fileName));
            } else {
                fileName = fundApiDictEntityList.get(0).getValue();
            }
            FundResourceDTO dto = new FundResourceDTO();
            dto.setFund(FundEnum.LAN_HAI);
            List<FundResourceDTO.Resource> resourceList = new ArrayList<>();
            FundResourceDTO.Resource resource = new FundResourceDTO.Resource();
            int firstUnderscoreIndex = fileName.indexOf('_');
            String prefix = (firstUnderscoreIndex != -1)
                    ? fileName.substring(0, firstUnderscoreIndex)
                    : fileName;
            String path = "/app/" + lanHaiConfig.getMerchantId() + "/" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "/bobtochannel/image/" + prefix + "/" + fileName;
            path = path.replace("$rootdirectory", "app");
            path = path.replace("$merchantid", lanHaiConfig.getMerchantId());
            path = path.replace("$yyyymmdd", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            log.info("LanHaiServiceImpl.compensateFlowCertificate - path:{}", path);
            resource.setFilePath(path);
            resource.setFundFileCode(null);
            resourceList.add(resource);
            dto.setResourceList(resourceList);
            Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceDownload(dto);
            if (!Result.isSuccess(listResult)) {
                throw new BusinessException("获取代偿流水证明失败");
            }
            orderFileMapper.insert(new OrderFileEntity()
                    .setFileId(loanFlowCertificate.getData())
                    .setResourceId(listResult.getData().get(0).getResourceId())
                    .setOrderId(orderId)
                    .setFileName(fileName)
                    .setResourceName(listResult.getData().get(0).getResourceName())
            );
            return listResult.getData().get(0).getResourceId();
        } else {
            return orderFileEntityList.get(0).getResourceId();
        }
    }

    /**
     * 结清证明下载
     */
    @Override
    public String settleFlowCertificate(Integer orderId) {
        Result<Integer> loanFlowCertificate = resourceFeign.selectFileConfigByCode(FundConstant.LAN_HAI_SETTLEMENT);
        if (!Result.isSuccess(loanFlowCertificate) || loanFlowCertificate.getData() == null) {
            throw new BusinessException("未查询到文件配置");
        }
        List<OrderFileEntity> orderFileEntityList = orderFileMapper.selectList(
                new LambdaQueryWrapper<OrderFileEntity>()
                        .eq(OrderFileEntity::getOrderId, orderId)
                        .eq(OrderFileEntity::getDeleteFlag, 0)
                        .eq(OrderFileEntity::getFileId, loanFlowCertificate.getData())
                        .orderByAsc(OrderFileEntity::getCreateTime)
        );
        if (CollUtil.isEmpty(orderFileEntityList)) {
            String fileName = "";
            List<FundApiDictEntity> fundApiDictEntityList = fundApiDictMapper.selectList(
                    new LambdaQueryWrapper<FundApiDictEntity>()
                            .eq(FundApiDictEntity::getLinkId, orderId)
                            .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.SETTLE_FLOW_CERTIFICATE)
                            .eq(FundApiDictEntity::getDeleteFlag, 0)
                            .orderByDesc(FundApiDictEntity::getCreateTime)
            );
            if (CollUtil.isEmpty(fundApiDictEntityList)) {
                FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                        .last("LIMIT 1")
                );
                LanHaiSettleFlowCertificateRequest request = new LanHaiSettleFlowCertificateRequest();
                request.setUserId(fundInfo.getFundUserId());
                request.setLoanInvoiceId(fundInfo.getLoanNo());

                LanHaiResult<LanHaiSettleFlowCertificateResponse> execute = lanHaiClient.execute(LanHaiApiEnums.LH_SETTLEMENT, request, LanHaiSettleFlowCertificateResponse.class, getOriRequestSerialNo());
                log.info("LanHaiServiceImpl.settleFlowCertificate end result:{}", JSONUtil.toJsonStr(execute));
                if (ObjUtil.isNull(execute.getData().getBody())) {
                    throw new BusinessException("获取结清证明失败:" + execute.getData().getHead().getReturnMessage());
                }
                fileName = execute.getData().getBody().getFileName();
                fundApiDictService.save(new FundApiDictEntity()
                        .setLinkId(orderId)
                        .setType(FundApiDictTypeEnum.SETTLE_FLOW_CERTIFICATE)
                        .setValue(fileName));
            } else {
                fileName = fundApiDictEntityList.get(0).getValue();
            }
            FundResourceDTO dto = new FundResourceDTO();
            dto.setFund(FundEnum.LAN_HAI);
            List<FundResourceDTO.Resource> resourceList = new ArrayList<>();
            FundResourceDTO.Resource resource = new FundResourceDTO.Resource();
            int firstUnderscoreIndex = fileName.indexOf('_');
            String prefix = (firstUnderscoreIndex != -1)
                    ? fileName.substring(0, firstUnderscoreIndex)
                    : fileName;
            String path = "/app/" + lanHaiConfig.getMerchantId() + "/" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "/bobtochannel/image/" + prefix + "/" + fileName;
//            path = path.replace("$rootdirectory", "app");
//            path = path.replace("$merchantid", lanHaiConfig.getMerchantId());
//            path = path.replace("$yyyymmdd", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            log.info("LanHaiServiceImpl.settleFlowCertificate - path:{}", path);
            resource.setFilePath(path);
            resource.setFundFileCode(null);
            resourceList.add(resource);
            dto.setResourceList(resourceList);
            Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceDownload(dto);
            if (!Result.isSuccess(listResult)) {
                throw new BusinessException("获取结清证明失败");
            }
            orderFileMapper.insert(new OrderFileEntity()
                    .setFileId(loanFlowCertificate.getData())
                    .setResourceId(listResult.getData().get(0).getResourceId())
                    .setOrderId(orderId)
                    .setFileName(fileName)
                    .setResourceName(listResult.getData().get(0).getResourceName())
            );
            return listResult.getData().get(0).getResourceId();
        } else {
            return orderFileEntityList.get(0).getResourceId();
        }
    }

    @Override
    public List<LanHaiCallBackRequest> handleRepayCallback(List<LanHaiCallBackResponse> callback) {
        log.info("LanHaiServiceImpl.handleRepayCallback callback:{}", JSONUtil.toJsonStr(callback));
        if (CollUtil.isEmpty(callback)) {
            log.info("LanHaiServiceImpl.handleRepayCallback callback is empty");
            return List.of();
        }
        List<LanHaiCallBackRequest> callBackResultList = new ArrayList<>();
        for (LanHaiCallBackResponse callDto : callback) {
            String arguments = callDto.getArguments();
            LanHaiResult.Result<LanHaiRepaymentCallbackResponse> callBackInfoResult = null;
            try {
                JavaType javaType = objectMapper.getTypeFactory().constructParametricType(LanHaiResult.Result.class, LanHaiRepaymentCallbackResponse.class);
                callBackInfoResult = objectMapper.readValue(arguments, javaType);
            } catch (Exception e) {
                log.info("LanHaiServiceImpl.handleRepayCallback error parsing response: {}", e.getMessage(), e);
                LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                callResultDTO.setId(callDto.getId());
                callResultDTO.setStatus("2");
                callResultDTO.setRemark("参数解析失败");
                callBackResultList.add(callResultDTO);
                continue;
            }
            if (ObjectUtil.isNull(callBackInfoResult)) {
                log.info("LanHaiServiceImpl.handleRepayCallback callBackInfo is null");
                LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                callResultDTO.setId(callDto.getId());
                callResultDTO.setStatus("2");
                callResultDTO.setRemark("未获取到回调参数信息");
                callBackResultList.add(callResultDTO);
                continue;
            }
            try {
                LanHaiRepaymentCallbackResponse resultBody = callBackInfoResult.getBody();

                FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(
                        Wrappers.<FinalFundInfoEntity>lambdaQuery()
                                .eq(FinalFundInfoEntity::getLoanNo, resultBody.getLoanInvoiceId())
                                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                        , false
                );
                if (ObjectUtil.isNull(finalFundInfo)) {
                    log.error("LanHaiServiceImpl.handleRepayCallback on payment ： {} 订单不存在", JSONUtil.toJsonStr(resultBody));
                    throw new BusinessException("订单不存在");
                }

                OrderInfoEntity orderInfo = orderInfoMapper.selectById(finalFundInfo.getOrderId());
                if (ObjUtil.isNull(orderInfo)) {
                    log.error("LanHaiServiceImpl.handleRepayCallback on payment ： {} 订单不存在", JSONUtil.toJsonStr(resultBody));
                    throw new BusinessException("订单不存在");
                }
                // 同步还款计划表
                queryRepayPlan(orderInfo.getId());


            } catch (BusinessException e) {
                log.info("LanHaiServiceImpl.handleRepayCallback BusinessException error: {}", e.getMessage(), e);
                LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                callResultDTO.setId(callDto.getId());
                callResultDTO.setStatus("2");
                callResultDTO.setRemark(e.getMessage());
                callBackResultList.add(callResultDTO);
            } catch (Exception e) {
                log.info("LanHaiServiceImpl.handleRepayCallback Exception error: {}", e.getMessage(), e);
                LanHaiCallBackRequest callResultDTO = new LanHaiCallBackRequest();
                callResultDTO.setId(callDto.getId());
                callResultDTO.setStatus("2");
                callResultDTO.setRemark("业务处理失败");
                callBackResultList.add(callResultDTO);
            }
        }


        return List.of();
    }


    /**
     * 构建解抵申请参数
     *
     * @param orderId
     * @return
     */
    private LanHaiUndoMortgageApplyRequest buildUndoMortgageApplyByOrderId(Integer orderId) {

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, () -> new BusinessException("订单不存在"));
        OrderArrivedEntity orderArrivedEntity = orderArrivedMapper.selectOne(new LambdaQueryWrapper<OrderArrivedEntity>()
                .eq(OrderArrivedEntity::getOrderId, orderId)
                .eq(OrderArrivedEntity::getDeleteFlag, 0)
                .orderByDesc(OrderArrivedEntity::getCreateTime)
                .last("LIMIT 1")
        );
        Assert.notNull(orderArrivedEntity, () -> new BusinessException("抵押信息不存在"));
        ArrivedDataEntity arrivedDataEntity = arrivedDataMapper.selectById(orderArrivedEntity.getRelieveArrivedId());
        Assert.notNull(arrivedDataEntity, () -> new BusinessException("抵押信息不存在"));
        // 获取订单关联的办抵地址信息
        OrderArrivedAddressEntity orderArrivedAddressEntity = orderArrivedAddressMapper.selectOne(new LambdaQueryWrapper<OrderArrivedAddressEntity>()
                .eq(OrderArrivedAddressEntity::getArrivedId, orderArrivedEntity.getId())
                .eq(OrderArrivedAddressEntity::getType, 2)
                .eq(OrderArrivedAddressEntity::getDeleteFlag, 0)
                .orderByDesc(OrderArrivedAddressEntity::getCreateTime)
                .last("LIMIT 1"));
        Assert.notNull(orderArrivedAddressEntity, () -> new BusinessException("抵押收件地址信息不存在"));

        FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("LIMIT 1")
        );
        Assert.notNull(fundInfo, () -> new BusinessException("资方信息不存在"));

        String templateCodeMortgage = "LH_LH_0003";// 蓝海
        FileTemplateInfoEntity fileTemplateInfoMortgage = fileTemplateInfoMapper.selectOne(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getTemplateNumber, templateCodeMortgage)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .eq(FileTemplateInfoEntity::getType, 1)
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FileTemplateInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(fileTemplateInfoMortgage)) {
            log.info("LanHaiServiceImpl.buildUndoMortgageApplyByOrderId fileTemplateInfoMortgage:{}", fileTemplateInfoMortgage);
            throw new BusinessException("合同模板不存在");
        }
        OrderContractEntity orderContractEntity = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getTemplateId, fileTemplateInfoMortgage.getId())
                .eq(OrderContractEntity::getSignStatus, 2)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .orderByDesc(OrderContractEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderContractEntity)) {
            log.info("LanHaiServiceImpl.buildUndoMortgageApplyByOrderId orderContractEntity:{}", orderContractEntity);
            throw new BusinessException("未查询到合同相关信息");
        }
        Car300DataVO car300DataVO = car300Feign.getInfoByVehicle(orderInfoEntity.getVehicleNumber()).getData();

        List<LanHaiImageUploadRequest.FileInfo> fileInfos = imageUploadUndoMortgage(orderId, orderArrivedEntity);
        List<LanHaiUndoMortgageApplyRequest.Attachment> attachmentList = new ArrayList<>();
        fileInfos.stream().forEach(item -> {
            LanHaiUndoMortgageApplyRequest.Attachment attachment = new LanHaiUndoMortgageApplyRequest.Attachment();
            attachment.setFileKind(item.getFileKind())
                    .setFileName(item.getFileName());
            attachmentList.add(attachment);
        });
        String transactionId = lanHaiConfig.getProductId() + "JY" + System.currentTimeMillis() + RandomUtil.randomString(4);
        LanHaiUndoMortgageApplyRequest request = new LanHaiUndoMortgageApplyRequest();
        request.setUserId(fundInfo.getFundUserId())
                .setProductId(lanHaiConfig.getProductId())
                .setLoanInvoiceId(fundInfo.getLoanBillNo())
                .setTransactionId(transactionId)
                .setMtgeeCode(lanHaiConfig.getMortgageCreditCode())
                .setMtgeeAgtIdType("A")
                .setMtgeeAgtIdNumber(arrivedDataEntity.getMortgageAgentIdNumber())
                .setMtgeeAgtName(arrivedDataEntity.getMortgageAgentName())
                .setMtgeeAgtMobile(arrivedDataEntity.getMortgageAgentPhone())
                .setMtgContractNo(orderContractEntity.getNumber())
                .setMtgorIdType("A")
                .setMtgorIdNumber(orderArrivedEntity.getMotorVehicleIdNumber())
                .setMtgorName(orderArrivedEntity.getMotorVehicleName())
                .setMtgorMobile(orderArrivedEntity.getMotorVehiclePhone())
                .setVin(car300DataVO.getVin())
                .setPlateType(orderInfoEntity.getVehicleNumber().length() == 7 ? "02" : "52")
                .setPlateNumber(orderInfoEntity.getVehicleNumber())
                .setRecipient(orderArrivedAddressEntity.getRecipient())
                .setPostCode(orderArrivedAddressEntity.getPostCode())
                .setPostAddress(orderArrivedAddressEntity.getPostAddress())
                .setRecipientMobile(orderArrivedAddressEntity.getRecipientMobile())
                .setApplyDate(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN))
                .setAttachments(attachmentList);
        return request;
    }

    private List<LanHaiImageUploadRequest.FileInfo> imageUploadUndoMortgage(Integer orderId, OrderArrivedEntity orderArrivedEntity) {
        log.info("--- 蓝海影像上传开始 orderId: {} ---", orderId);
        FundEnum lanHai = FundEnum.LAN_HAI;
        FundResourceDTO fundResourceDTO = new FundResourceDTO();
        fundResourceDTO
                .setLinkId(orderId)
                .setType(7)
                .setFund(lanHai)
                .setCustomerBaseInfo(
                        new FundResourceDTO.CustomerBaseInfo()
                                .setCustomerIdNo(orderArrivedEntity.getMotorVehicleIdNumber())
                                .setPhone(orderArrivedEntity.getMotorVehiclePhone())
                )
        ;

        Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(fundResourceDTO);
        if (!Result.isSuccess(listResult)) {
            throw new BusinessException("获取影像文件失败");
        }
        List<FundResourceResultDTO> data = listResult.getData();
        LanHaiImageUploadRequest lanHaiImageUploadRequest = new LanHaiImageUploadRequest();
        List<LanHaiImageUploadRequest.FileInfo> fileList = new ArrayList<>();

        data.stream().forEach(item -> {
            LanHaiImageUploadRequest.FileInfo fileInfo = new LanHaiImageUploadRequest.FileInfo();
            fileInfo.setFileData(item.getFilePath());
            fileInfo.setFileKind(item.getFileCode());
            fileInfo.setFileName(item.getFileId());
            fileList.add(fileInfo);
        });
        //数据太大 分开推送
        List<LanHaiImageUploadRequest.FileInfo> fileListTest = new ArrayList<>();
        data.stream().forEach(item -> {
            LanHaiImageUploadRequest.FileInfo fileInfo = new LanHaiImageUploadRequest.FileInfo();
            fileInfo.setFileKind(item.getFileCode());
            fileInfo.setFileName(item.getFileId());
            fileListTest.add(fileInfo);
        });
        log.info("LanHaiServiceImpl.imageUploadUndoMortgage fileListTest:{}", JSONUtil.toJsonStr(fileListTest));
        try {
            fileList.stream().forEach(item -> {
                List<LanHaiImageUploadRequest.FileInfo> fileNew = new ArrayList<>();
                fileNew.add(item);
                lanHaiImageUploadRequest.setFileList(fileNew);
                log.info("LanHaiServiceImpl.imageUploadUndoMortgage item.getFileKind():{}", item.getFileKind());
                LanHaiResult<LanHaiImageUploadResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_IMAGE_UPLOAD, lanHaiImageUploadRequest, LanHaiImageUploadResponse.class, getOriRequestSerialNo());
                if (!LanHaiResult.isSuccess(result) || !LanHaiResult.isSuccess(result)) {
                    throw new BusinessException("影像上传失败");
                }
            });
        } catch (Exception e) {
            throw new BusinessException("影像上传失败:" + e.getMessage());
        }

        return fileList;

    }

    /**
     * 解抵申请查询
     *
     * @param orderId
     * @return
     */
    @Override
    public LanHaiMortgageEnums undoMortgageApplyVerify(Integer orderId) {
        log.info("LanHaiServiceImpl.undoMortgageApplyVerify orderId:{}", orderId);
        //根据orderId 查询 transactionId
        String transactionId = "";

        transactionId = undoMortgageStatus(orderId);
        if (StrUtil.isEmpty(transactionId)) {
            throw new BusinessException("抵押申请流水号不存在");
        } else {
            LanHaiUndoMortgageApplyVerifyRequest request = new LanHaiUndoMortgageApplyVerifyRequest();
            request.setTransactionId(transactionId);

            LanHaiMortgageEnums reuslt = null;
            try {
                LanHaiResult<LanHaiUndoMortgageApplyVerifyResponse> result = lanHaiClient.execute(LanHaiApiEnums.LH_RELEASE_VERIFY, request, LanHaiUndoMortgageApplyVerifyResponse.class, getOriRequestSerialNo());
                log.info("LanHaiServiceImpl.undoMortgageApplyVerify result:{}", JSONUtil.toJsonStr(result));
                if (!LanHaiResult.isSuccess(result)) {
                    throw new BusinessException(result.getData().getHead().getReturnMessage());
                }
                LanHaiUndoMortgageApplyVerifyResponse body = result.getData().getBody();
                reuslt = updateUndoMortgageApplyStatues(orderId, body.getBusinessStatus(), body.getOperationStatus());
            } catch (BusinessException e) {
                throw new BusinessException("抵押状态查询异常:" + e.getMessage());
            }
            return reuslt;

        }

    }

    private String undoMortgageStatus(Integer orderId) {
        String transactionId = "";
        FundApiDictEntity fundApiDictEntity = fundApiDictMapper.selectOne(new LambdaQueryWrapper<FundApiDictEntity>()
                .eq(FundApiDictEntity::getType, FundApiDictTypeEnum.UNDO_MORTGAGE)
                .eq(FundApiDictEntity::getCode, FundApiDictEnum.UNDO_MORTGAGE_REQUEST_APPLY_NO)
                .eq(FundApiDictEntity::getLinkId, orderId)
                .eq(FundApiDictEntity::getDeleteFlag, 0)
                .orderByDesc(FundApiDictEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNotNull(fundApiDictEntity) && StrUtil.isNotEmpty(fundApiDictEntity.getValue())) {
            transactionId = fundApiDictEntity.getValue();
        }
        return transactionId;

    }

    private LanHaiMortgageEnums updateUndoMortgageApplyStatues(Integer orderId, LanHaiMortgageEnums businessStatus, String operationStatus) {
        log.info("LanHaiServiceImpl.updateUndoMortgageApplyStatues start orderId:{} businessStatus:{} operationStatus:{}", orderId, businessStatus, operationStatus);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        MortgageStateUpdateDTO mortgageStateUpdateDTO = new MortgageStateUpdateDTO();
        mortgageStateUpdateDTO.setOrderId(orderId);
        Integer mortgageState = orderInfoEntity.getMortgageState();
        if (ObjUtil.isNull(orderInfoEntity) || ObjUtil.isNull(orderInfoEntity.getMortgageState())) {
            throw new BusinessException("订单信息不存在");
        }
        FundUndoMortgageInfoEntity fundUndoMortgageInfoEntity = fundUndoMortgageInfoMapper.selectOne(new LambdaQueryWrapper<FundUndoMortgageInfoEntity>()
                .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                .eq(FundUndoMortgageInfoEntity::getOperateType, "2")
                .eq(FundUndoMortgageInfoEntity::getHistory, 0)
        );
        if (ObjUtil.equals(businessStatus, LanHaiMortgageEnums.PRE_ACCEPT_SUCCESS) || ObjUtil.equals(businessStatus, LanHaiMortgageEnums.ACCEPT_SUCCESS) || ObjUtil.equals(businessStatus, LanHaiMortgageEnums.SIGNED)
        ) {
            if (ObjUtil.isNotNull(orderInfoEntity)) {
                if (orderInfoEntity.getMortgageState() == MortgageEnums.MORTGAGE_STATUS_SUCCESS.getCode()) {
                    mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_PROCESSING.getCode();
                }
                if (orderInfoEntity.getMortgageState() == MortgageEnums.MORTGAGE_STATUS_PRE.getCode()) {
                    mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_PROCESSING_HAVE.getCode();
                }
            }
        } else if (ObjUtil.equals(businessStatus, LanHaiMortgageEnums.ARCHIVED)) {
            if (ObjUtil.isNotNull(orderInfoEntity)) {
                if (orderInfoEntity.getMortgageState() == MortgageEnums.MORTGAGE_STATUS_SUCCESS.getCode()) {
                    mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_SUCCESS.getCode();
                }
                if (orderInfoEntity.getMortgageState() == MortgageEnums.MORTGAGE_STATUS_PRE.getCode()) {
                    mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_SUCCESS_HAVE.getCode();
                }
            }
        } else if (ObjUtil.equals(businessStatus, LanHaiMortgageEnums.RETURNED)) {
            if (orderInfoEntity.getMortgageState() == MortgageEnums.MORTGAGE_STATUS_SUCCESS.getCode()) {
                mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_BACK_SUCCESS.getCode();
            }
            if (orderInfoEntity.getMortgageState() == MortgageEnums.MORTGAGE_STATUS_PRE.getCode()) {
                mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_BACK_SUCCESS_HAVE.getCode();
            }
            fundUndoMortgageInfoEntity.setDeleteFlag(1);
            fundUndoMortgageInfoEntity.setHistory(1);
        }
//        else if () {
//            if (ObjUtil.isNotNull(orderInfoEntity)) {
//                if (orderInfoEntity.getMortgageState() == MortgageEnums.MORTGAGE_STATUS_SUCCESS.getCode()) {
//                    mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_FAIL.getCode();
//                }
//                if (orderInfoEntity.getMortgageState() == MortgageEnums.MORTGAGE_STATUS_PRE.getCode()) {
//                    mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_FAIL_HAVE.getCode();
//                }
//            }
//        }
        else {
            //初始化的时候 删除掉解抵信息
            fundUndoMortgageInfoEntity.setDeleteFlag(1);
            fundUndoMortgageInfoEntity.setHistory(1);
            fundUndoMortgageInfoMapper.updateById(fundUndoMortgageInfoEntity);

        }
        log.info("FuMinPreServiceImpl.updateOrderMortgageSCancelStatus mortgageState {}", mortgageState);
        if (ObjUtil.isNotNull(fundUndoMortgageInfoEntity)) {
            fundUndoMortgageInfoEntity.setMortgageStatus(mortgageState.toString());
            fundUndoMortgageInfoMapper.updateById(fundUndoMortgageInfoEntity);
            log.info("FuMinPreServiceImpl.updateOrderMortgageSCancelStatus fundUndoMortgageInfoEntity {}", fundUndoMortgageInfoEntity);
        }

        return businessStatus;
    }

    @Override
    public Boolean undoMortgageEdit(Integer orderId) {
        log.info("LanHaiServiceImpl.undoMortgageEdit start orderId:{}", orderId);
        String transactionId = undoMortgageStatus(orderId);
        if (StrUtil.isNotEmpty(transactionId)) {
            //解抵状态查询
            LanHaiMortgageEnums mortgageApplyVerify = null;
            try {
                mortgageApplyVerify = undoMortgageApplyVerify(orderId);
                log.info("LanHaiServiceImpl.undoMortgageEdit mortgageApplyVerify:{}", mortgageApplyVerify.getDescription());
            } catch (Exception e) {
                throw new BusinessException(e.getMessage());
            }
            if (!ObjUtil.equals(mortgageApplyVerify, LanHaiMortgageEnums.PRE_ACCEPT_SUCCESS)) {
                throw new BusinessException("当前订单状态为:" + mortgageApplyVerify.getDescription() + ",请勿解抵");
            }
        } else {
            throw new BusinessException("未查询到解抵流水号");
        }
        checkProductContractRelieve(orderId);
        LanHaiResult<LanHaiUndoMortgageEditResponse> result = null;
        try {
            LanHaiUndoMortgageEditRequest request = buildUndoMortgageEditByOrderId(orderId);
            log.info("LanHaiServiceImpl.undoMortgageEdit request:{}", JSONUtil.toJsonStr(request));
            result = lanHaiClient.execute(LanHaiApiEnums.LH_RELEASE_EDIT, request, LanHaiUndoMortgageEditResponse.class, getOriRequestSerialNo());
            log.info("LanHaiServiceImpl.undoMortgageEdit result:{}", JSONUtil.toJsonStr(result));
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        if (!LanHaiResult.isSuccess(result)) {
            throw new BusinessException("解抵修改失败:" + result.getData().getHead().getReturnMessage());
        }
        return true;
    }

    private LanHaiUndoMortgageEditRequest buildUndoMortgageEditByOrderId(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, () -> new BusinessException("订单不存在"));
        OrderArrivedEntity orderArrivedEntity = orderArrivedMapper.selectOne(new LambdaQueryWrapper<OrderArrivedEntity>()
                .eq(OrderArrivedEntity::getOrderId, orderId)
                .eq(OrderArrivedEntity::getDeleteFlag, 0)
                .orderByDesc(OrderArrivedEntity::getCreateTime)
                .last("LIMIT 1")
        );
        Assert.notNull(orderArrivedEntity, () -> new BusinessException("抵押信息不存在"));
        ArrivedDataEntity arrivedDataEntity = arrivedDataMapper.selectById(orderArrivedEntity.getRelieveArrivedId());
        Assert.notNull(arrivedDataEntity, () -> new BusinessException("抵押信息不存在"));
        // 获取订单关联的办抵地址信息
        OrderArrivedAddressEntity orderArrivedAddressEntity = orderArrivedAddressMapper.selectOne(new LambdaQueryWrapper<OrderArrivedAddressEntity>()
                .eq(OrderArrivedAddressEntity::getArrivedId, orderArrivedEntity.getId())
                .eq(OrderArrivedAddressEntity::getType, 2)
                .eq(OrderArrivedAddressEntity::getDeleteFlag, 0)
                .orderByDesc(OrderArrivedAddressEntity::getCreateTime)
                .last("LIMIT 1"));
        Assert.notNull(orderArrivedAddressEntity, () -> new BusinessException("抵押收件地址信息不存在"));

        FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("LIMIT 1")
        );
        Assert.notNull(fundInfo, () -> new BusinessException("资方信息不存在"));

        String templateCodeMortgage = "LH_LH_0003";// 蓝海
        FileTemplateInfoEntity fileTemplateInfoMortgage = fileTemplateInfoMapper.selectOne(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getTemplateNumber, templateCodeMortgage)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .eq(FileTemplateInfoEntity::getType, 1)
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FileTemplateInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(fileTemplateInfoMortgage)) {
            log.info("LanHaiServiceImpl.buildUndoMortgageApplyByOrderId fileTemplateInfoMortgage:{}", fileTemplateInfoMortgage);
            throw new BusinessException("合同模板不存在");
        }
        OrderContractEntity orderContractEntity = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getTemplateId, fileTemplateInfoMortgage.getId())
                .eq(OrderContractEntity::getSignStatus, 2)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .orderByDesc(OrderContractEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderContractEntity)) {
            log.info("LanHaiServiceImpl.buildUndoMortgageApplyByOrderId orderContractEntity:{}", orderContractEntity);
            throw new BusinessException("未查询到合同相关信息");
        }
        Car300DataVO car300DataVO = car300Feign.getInfoByVehicle(orderInfoEntity.getVehicleNumber()).getData();

        List<LanHaiImageUploadRequest.FileInfo> fileInfos = imageUploadUndoMortgage(orderId, orderArrivedEntity);
        List<LanHaiUndoMortgageEditRequest.Attachment> attachmentList = new ArrayList<>();
        fileInfos.stream().forEach(item -> {
            LanHaiUndoMortgageEditRequest.Attachment attachment = new LanHaiUndoMortgageEditRequest.Attachment();
            attachment.setFileKind(item.getFileKind())
                    .setFileName(item.getFileName());
            attachmentList.add(attachment);
        });
        String transactionId = lanHaiConfig.getProductId() + "JY" + System.currentTimeMillis() + RandomUtil.randomString(4);
        LanHaiUndoMortgageEditRequest request = new LanHaiUndoMortgageEditRequest();
        request.setUserId(fundInfo.getFundUserId())
                .setProductId(lanHaiConfig.getProductId())
                .setLoanInvoiceId(fundInfo.getLoanBillNo())
                .setTransactionId(transactionId)
                .setMtgeeCode(lanHaiConfig.getMortgageCreditCode())
                .setMtgeeAgtIdType("A")
                .setMtgeeAgtIdNumber(arrivedDataEntity.getMortgageAgentIdNumber())
                .setMtgeeAgtName(arrivedDataEntity.getMortgageAgentName())
                .setMtgeeAgtMobile(arrivedDataEntity.getMortgageAgentPhone())
                .setMtgContractNo(orderContractEntity.getNumber())
                .setMtgorIdType("A")
                .setMtgorIdNumber(orderArrivedEntity.getMotorVehicleIdNumber())
                .setMtgorName(orderArrivedEntity.getMotorVehicleName())
                .setMtgorMobile(orderArrivedEntity.getMotorVehiclePhone())
                .setVin(car300DataVO.getVin())
                .setPlateType(orderInfoEntity.getVehicleNumber().length() == 7 ? "02" : "52")
                .setPlateNumber(orderInfoEntity.getVehicleNumber())
                .setRecipient(orderArrivedAddressEntity.getRecipient())
                .setPostCode(orderArrivedAddressEntity.getPostCode())
                .setPostAddress(orderArrivedAddressEntity.getPostAddress())
                .setRecipientMobile(orderArrivedAddressEntity.getRecipientMobile())
                .setApplyDate(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN))
                .setAttachments(attachmentList);
        return request;

    }

    /**
     * 解抵退办
     */
    @Override
    public Boolean undoMortgageCancel(Integer orderId, String remark) {
        log.info("LanHaiServiceImpl.undoMortgageCancel start orderId:{}", orderId);
        String transactionId = undoMortgageStatus(orderId);
        if (StrUtil.isNotEmpty(transactionId)) {
            //解抵状态查询
            LanHaiMortgageEnums mortgageApplyVerify = null;
            try {
                mortgageApplyVerify = undoMortgageApplyVerify(orderId);
                log.info("LanHaiServiceImpl.undoMortgageCancel mortgageApplyVerify:{}", mortgageApplyVerify.getDescription());
            } catch (Exception e) {
                throw new BusinessException(e.getMessage());
            }
            if (!ObjUtil.equals(mortgageApplyVerify, LanHaiMortgageEnums.PRE_ACCEPT_SUCCESS)) {
                throw new BusinessException("当前订单状态为:" + mortgageApplyVerify.getDescription() + ",请勿解抵");
            }
        } else {
            throw new BusinessException("未查询到解抵流水号");
        }

        LanHaiResult<LanHaiUndoMortgageCancelResponse> result = null;
        try {
            LanHaiUndoMortgageCancelRequest request = buildUndoMortgageCancelByOrderId(orderId, remark);
            log.info("LanHaiServiceImpl.undoMortgageCancel request:{}", JSONUtil.toJsonStr(request));
            result = lanHaiClient.execute(LanHaiApiEnums.LH_RELEASE_CANCEL, request, LanHaiUndoMortgageCancelResponse.class, getOriRequestSerialNo());
            log.info("LanHaiServiceImpl.undoMortgageCancel result:{}", JSONUtil.toJsonStr(result));
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        if (!LanHaiResult.isSuccess(result)) {
            throw new BusinessException("解抵退办失败:" + result.getData().getHead().getReturnMessage());
        }

        FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("LIMIT 1")
        );

        fundInfo.setFundNode(FundApplyNodeEnums.MORTGAGE_REVOKE);
        fundInfo.setFundNodeStatus(FundApplyNodeStatusEnum.SUCCESS);
        fundInfo.setMortgageState(FundMortgageStatusEnum.PASS);
        finalFundInfoMapper.updateById(fundInfo);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);


        FundUndoMortgageInfoEntity fundUndoMortgageInfoEntity = fundUndoMortgageInfoMapper.selectOne(new LambdaUpdateWrapper<FundUndoMortgageInfoEntity>()
                .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                .eq(FundUndoMortgageInfoEntity::getOperateType, "2")
                .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                .eq(FundUndoMortgageInfoEntity::getHistory, 0)
        );
        if (ObjectUtil.equals(orderInfoEntity.getMortgageState(), 2)) {
            fundUndoMortgageInfoEntity.setMortgageStatus(MortgageEnums.MORTGAGE_STATUS_CANCEL_BACK_PROCESSING.getCode().toString());
        } else {
            fundUndoMortgageInfoEntity.setMortgageStatus(MortgageEnums.MORTGAGE_STATUS_CANCEL_BACK_PROCESSING_HAVE.getCode().toString());
        }
        fundUndoMortgageInfoMapper.updateById(fundUndoMortgageInfoEntity);
        return true;
    }

    private LanHaiUndoMortgageCancelRequest buildUndoMortgageCancelByOrderId(Integer orderId, String remark) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, () -> new BusinessException("订单不存在"));
        OrderArrivedEntity orderArrivedEntity = orderArrivedMapper.selectOne(new LambdaQueryWrapper<OrderArrivedEntity>()
                .eq(OrderArrivedEntity::getOrderId, orderId)
                .eq(OrderArrivedEntity::getDeleteFlag, 0)
                .orderByDesc(OrderArrivedEntity::getCreateTime)
                .last("LIMIT 1")
        );
        Assert.notNull(orderArrivedEntity, () -> new BusinessException("抵押信息不存在"));
        ArrivedDataEntity arrivedDataEntity = arrivedDataMapper.selectById(orderArrivedEntity.getRelieveArrivedId());
        Assert.notNull(arrivedDataEntity, () -> new BusinessException("抵押信息不存在"));
        // 获取订单关联的办抵地址信息
        OrderArrivedAddressEntity orderArrivedAddressEntity = orderArrivedAddressMapper.selectOne(new LambdaQueryWrapper<OrderArrivedAddressEntity>()
                .eq(OrderArrivedAddressEntity::getArrivedId, orderArrivedEntity.getId())
                .eq(OrderArrivedAddressEntity::getType, 2)
                .eq(OrderArrivedAddressEntity::getDeleteFlag, 0)
                .orderByDesc(OrderArrivedAddressEntity::getCreateTime)
                .last("LIMIT 1"));
        Assert.notNull(orderArrivedAddressEntity, () -> new BusinessException("抵押收件地址信息不存在"));

        FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("LIMIT 1")
        );
        Assert.notNull(fundInfo, () -> new BusinessException("资方信息不存在"));

        String templateCodeMortgage = "LH_LH_0003";// 蓝海
        FileTemplateInfoEntity fileTemplateInfoMortgage = fileTemplateInfoMapper.selectOne(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getTemplateNumber, templateCodeMortgage)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .eq(FileTemplateInfoEntity::getType, 1)
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FileTemplateInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(fileTemplateInfoMortgage)) {
            log.info("LanHaiServiceImpl.buildUndoMortgageCancelByOrderId fileTemplateInfoMortgage:{}", fileTemplateInfoMortgage);
            throw new BusinessException("合同模板不存在");
        }
        OrderContractEntity orderContractEntity = orderContractMapper.selectOne(new LambdaQueryWrapper<OrderContractEntity>()
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getTemplateId, fileTemplateInfoMortgage.getId())
                .eq(OrderContractEntity::getSignStatus, 2)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .orderByDesc(OrderContractEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderContractEntity)) {
            log.info("LanHaiServiceImpl.buildUndoMortgageCancelByOrderId orderContractEntity:{}", orderContractEntity);
            throw new BusinessException("未查询到合同相关信息");
        }
        Car300DataVO car300DataVO = car300Feign.getInfoByVehicle(orderInfoEntity.getVehicleNumber()).getData();


        String transactionId = lanHaiConfig.getProductId() + "JY" + System.currentTimeMillis() + RandomUtil.randomString(4);
        LanHaiUndoMortgageCancelRequest request = new LanHaiUndoMortgageCancelRequest();
        request.setUserId(fundInfo.getFundUserId())
                .setProductId(lanHaiConfig.getProductId())
                .setLoanInvoiceId(fundInfo.getLoanBillNo())
                .setTransactionId(transactionId)
                .setMtgeeCode(lanHaiConfig.getMortgageCreditCode())
                .setMtgeeAgtIdType("A")
                .setMtgeeAgtIdNumber(arrivedDataEntity.getMortgageAgentIdNumber())
                .setMtgeeAgtName(arrivedDataEntity.getMortgageAgentName())
                .setMtgeeAgtMobile(arrivedDataEntity.getMortgageAgentPhone())
                .setMtgContractNo(orderContractEntity.getNumber())
                .setMtgorIdType("A")
                .setMtgorIdNumber(orderArrivedEntity.getMotorVehicleIdNumber())
                .setMtgorName(orderArrivedEntity.getMotorVehicleName())
                .setMtgorMobile(orderArrivedEntity.getMotorVehiclePhone())
                .setVin(car300DataVO.getVin())
                .setPlateType(orderInfoEntity.getVehicleNumber().length() == 7 ? "02" : "52")
                .setPlateNumber(orderInfoEntity.getVehicleNumber())
                .setRecipient(orderArrivedAddressEntity.getRecipient())
                .setPostCode(orderArrivedAddressEntity.getPostCode())
                .setPostAddress(orderArrivedAddressEntity.getPostAddress())
                .setRecipientMobile(orderArrivedAddressEntity.getRecipientMobile())
                .setApplyDate(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN))
                .setRemark(remark)
        ;
        return request;
    }

    @Override
    public Boolean queryUndoMortgage(Integer orderId) {
        String transactionId;
        transactionId = undoMortgageStatus(orderId);
        if (StrUtil.isNotBlank(transactionId)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean creditPreEnd(Integer preId) {
        return lanHaiCreditPreEnd(preId);
    }

    @Override
    public Boolean compensateApply(Integer orderId) {
        log.info("LanHaiServiceImpl.compensateApply start orderId:{}", orderId);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, () -> new BusinessException("订单信息不存在"));
        List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList = fundRepaymentInfoMapper.selectList(
                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                        .eq(FundRepaymentInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                        .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                        .orderByAsc(FundRepaymentInfoEntity::getTerm)
        );
        if (CollUtil.isEmpty(fundRepaymentInfoEntityList)) {
            log.info("LanHaiServiceImpl.compensateApply fundRepaymentInfoEntityList:{}", fundRepaymentInfoEntityList);
            throw new BusinessException("未查询到资方还款信息");
        }
        FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("LIMIT 1")
        );
        log.info("LanHaiServiceImpl.compensateApply fundInfo:{}", fundInfo);
        BigDecimal totalRepaymentPrincipal = fundRepaymentInfoEntityList.stream()
                .map(FundRepaymentInfoEntity::getRepaymentPrincipal)
                .filter(Objects::nonNull) // 过滤掉可能为 null 的值
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Assert.notNull(fundInfo, () -> new BusinessException("资方信息不存在"));
        LanHaiTrialRepayResponse lanHaiTrialRepayResponse = trialRepayQuery(orderId, totalRepaymentPrincipal, true);
        if (ObjUtil.isNull(lanHaiTrialRepayResponse)) {
            throw new BusinessException("未查询到试算信息");
        }
        LanHaiCompensateApplyRequest request = new LanHaiCompensateApplyRequest();
        request.setUserId(fundInfo.getFundUserId());
        request.setLoanInvoiceId(fundInfo.getLoanBillNo());
        request.setAmount(lanHaiTrialRepayResponse.getRepayAmount());
        request.setRepayPrincipal(lanHaiTrialRepayResponse.getRepayPrincipal());
        request.setRepayInterest(lanHaiTrialRepayResponse.getRepayInterest());
        request.setRepayFee(lanHaiTrialRepayResponse.getRepayFee());
        request.setRepayOverdueFee(lanHaiTrialRepayResponse.getRepayOverdueFee());
        request.setRepayCompoundInterest(lanHaiTrialRepayResponse.getRepayCompoundInterest());
        request.setBizType("006");
        request.setUserName(orderInfoEntity.getCustomerName());
        request.setRepayFlag("0");
        request.setRepayRemark("3");
        log.info("LanHaiServiceImpl.compensateApply LanHaiCompensateApplyRequest:{}", request);
        LanHaiResult<LanHaiCompensateApplyResponse> execute = lanHaiClient.execute(LanHaiApiEnums.LH_COMPENSATE_APPLY, request, LanHaiCompensateApplyResponse.class, getOriRequestSerialNo());
        if (!LanHaiResult.isSuccess(execute)) {
            throw new BusinessException("申请代偿失败" + execute.getData().getHead().getReturnMessage());
        }
        fundInfo.setCompensateApplyNo(execute.getData().getBody().getApplyId());
        finalFundInfoMapper.updateById(fundInfo);
        orderInfoEntity.setIsRepurchase(1);
        orderInfoMapper.updateById(orderInfoEntity);
        return true;
    }

    private void checkProductContract(Integer orderId) {
        List<FileTemplateInfoEntity> fileTemplateInfoList = fileTemplateInfoMapper.selectList(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .eq(FileTemplateInfoEntity::getType, 1)
                .in(FileTemplateInfoEntity::getTemplateNumber, "LH_LH_0010", "LH_LH_0009"));
        log.info("LanHaiServiceImpl.checkProductContract - fileTemplateInfoList:{}", fileTemplateInfoList);
        if (CollUtil.isNotEmpty(fileTemplateInfoList)) {
            List<Integer> templateIdIds = fileTemplateInfoList.stream().map(FileTemplateInfoEntity::getId).toList();
            List<OrderContractEntity> orderContractList = orderContractMapper.selectList(new LambdaQueryWrapper<OrderContractEntity>()
                    .eq(OrderContractEntity::getOrderId, orderId)
                    .in(OrderContractEntity::getTemplateId, templateIdIds)
                    .eq(OrderContractEntity::getSignStatus, 2)
                    .eq(OrderContractEntity::getDeleteFlag, 0));
            log.info("LanHaiServiceImpl.checkProductContract - orderContractList:{}", orderContractList);
            if (CollUtil.isEmpty(orderContractList)) {
                throw new BusinessException("等待合同签约完成");
            } else {
                try {
                    orderFeign.syncFddContractFileByOrderId(orderId);
                } catch (Exception e) {
                    throw new BusinessException("等待合同签约完成");
                }
            }
        }
    }

    private void checkProductContractRelieve(Integer orderId) {
        List<FileTemplateInfoEntity> fileTemplateInfoList = fileTemplateInfoMapper.selectList(new LambdaQueryWrapper<FileTemplateInfoEntity>()
                .eq(FileTemplateInfoEntity::getDeleteFlag, 0)
                .eq(FileTemplateInfoEntity::getStatus, 0)
                .eq(FileTemplateInfoEntity::getType, 1)
                .in(FileTemplateInfoEntity::getTemplateNumber, "LH_LH_0012", "LH_LH_0013"));
        log.info("LanHaiServiceImpl.checkProductContractRelieve - fileTemplateInfoList:{}", fileTemplateInfoList);
        if (CollUtil.isNotEmpty(fileTemplateInfoList)) {
            List<Integer> templateIdIds = fileTemplateInfoList.stream().map(FileTemplateInfoEntity::getId).toList();
            List<OrderContractEntity> orderContractList = orderContractMapper.selectList(new LambdaQueryWrapper<OrderContractEntity>()
                    .eq(OrderContractEntity::getOrderId, orderId)
                    .in(OrderContractEntity::getTemplateId, templateIdIds)
                    .eq(OrderContractEntity::getSignStatus, 2)
                    .eq(OrderContractEntity::getDeleteFlag, 0));
            log.info("LanHaiServiceImpl.checkProductContractRelieve - orderContractList:{}", orderContractList);
            if (CollUtil.isEmpty(orderContractList)) {
                throw new BusinessException("等待合同签约完成");
            } else {
                try {
                    orderFeign.syncFddContractFileByOrderId(orderId);
                } catch (Exception e) {
                    throw new BusinessException("等待合同签约完成");
                }
            }
        }
    }

    /**
     * 回传代偿后还款数据
     *
     * @return
     */
    @Override
    public Boolean repaymentData() {
        LocalDate now = LocalDate.now();
        DateTimeFormatter dtf1 = DateTimeFormatter.ofPattern(DatePattern.SIMPLE_MONTH_PATTERN);
        String fileNameSuffix = dtf1.format(now);

        //获取蓝海单条
        Result<List<List<String>>> lhSingleDataList = dataFeign.getLhSingleDataList();
        //获取蓝海多条
        Result<List<List<String>>> lhBatchDataList = dataFeign.getLhBatchDataList();

        if (!Result.isSuccess(lhSingleDataList)) {
            throw new BusinessException("获取蓝海单条失败");
        }

        if (!Result.isSuccess(lhBatchDataList)) {
            throw new BusinessException("获取蓝海多条失败");
        }

        UploadVerticalTxtDTO lhSingleDataDTOA = new UploadVerticalTxtDTO();
        lhSingleDataDTOA.setDataList(lhSingleDataList.getData());
        lhSingleDataDTOA.setFileName(fileNameSuffix + LanHaiApproveConstants.FILE_NAME_SINGLE);

        UploadVerticalTxtDTO lhSingleDataDTOB = new UploadVerticalTxtDTO();
        lhSingleDataDTOB.setDataList(Lists.newArrayList());
        lhSingleDataDTOB.setFileName(fileNameSuffix + LanHaiApproveConstants.FILE_NAME_SINGLE_OK);

        List<UploadVerticalTxtDTO> paramListA = Lists.newArrayList(lhSingleDataDTOA, lhSingleDataDTOB);

        //上传蓝海单条数据
        Result<List<FileVO>> fileVOResultA = resourceFeign.uploadVerticalTxt(paramListA);
        if (!Result.isSuccess(fileVOResultA)) {
            throw new BusinessException("上传蓝海单条数据失败");
        }

        UploadVerticalTxtDTO lhBatchDataDTOA = new UploadVerticalTxtDTO();
        lhBatchDataDTOA.setDataList(lhBatchDataList.getData());
        lhBatchDataDTOA.setFileName(fileNameSuffix + LanHaiApproveConstants.FILE_NAME_BATCH);

        UploadVerticalTxtDTO lhBatchDataDTOB = new UploadVerticalTxtDTO();
        lhBatchDataDTOB.setDataList(Lists.newArrayList());
        lhBatchDataDTOB.setFileName(fileNameSuffix + LanHaiApproveConstants.FILE_NAME_BATCH_OK);

        List<UploadVerticalTxtDTO> paramListB = Lists.newArrayList(lhBatchDataDTOA, lhBatchDataDTOB);
        //上传蓝海多条数据
        Result<List<FileVO>> fileVOResultB = resourceFeign.uploadVerticalTxt(paramListB);
        if (!Result.isSuccess(fileVOResultB)) {
            throw new BusinessException("上传蓝海多条数据失败");
        }

        List<String> resourceIdListA = fileVOResultA.getData().stream().map(FileVO::getResourceId).toList();
        List<String> resourceIdListB = fileVOResultB.getData().stream().map(FileVO::getResourceId).toList();
        //上传至sftp
        try {
            resourceFeign.uploadVerticalTxtToSftp(resourceIdListA);
            resourceFeign.uploadVerticalTxtToSftp(resourceIdListB);
        } catch (Exception e) {
            log.error("LanHaiServiceImpl,repaymentData,上传蓝海数据至sftp,error:{}", e.getMessage(), e);
            throw new BusinessException("上传蓝海数据至sftp失败");
        }

        return true;
    }

    @Override
    public void batchPorJectApply() {
        List<OrderInfoEntity> orderInfoEntities = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .in(OrderInfoEntity::getState, List.of(5000, 8000))
                .eq(OrderInfoEntity::getReconsiderState,1)
                .eq(OrderInfoEntity::getDeleteFlag, 0));
        orderInfoEntities.clear();
        orderInfoEntities.add(orderInfoMapper.selectById(2133));

        for (OrderInfoEntity orderInfoEntity : orderInfoEntities) {
            boolean flag =  false;
            try {
                //立项
                zunHaoService.projectApply(orderInfoEntity.getId());
                //放款
                zunHaoService.loanApply(orderInfoEntity.getId());
                //租金计划
                zunHaoService.createRentplanAuto(orderInfoEntity.getId());
                flag = true;
            } catch (Exception e) {
                log.error("batchProjectApply e", e);
            }finally {
                if(flag){
                    orderInfoEntity.setReconsiderState(0);
                    orderInfoMapper.updateById(orderInfoEntity);
                }
            }
        }
    }

    @Override
    public Map<Integer,String> batchPayAfterReplenish(List<Integer> orderIdList) {
        Map<Integer,String> resultMap = new HashMap<>();
        for (Integer orderId : orderIdList) {
            try {
                payAfterReplenish(orderId);
            } catch (Exception e) {
                log.error("batchPayAfterReplenish e:{}", e.getMessage());
                resultMap.put(orderId, e.getMessage());
            }
        }
        log.info("batchPayAfterReplenish result:{}", JSONUtil.toJsonStr(resultMap));
        return resultMap;
    }

    @Override
    public Map<Integer, String> batchPayAfterReplenishQuery(List<Integer> orderIdList) {
        Map<Integer, String> resultMap = new HashMap<>();
        for (Integer orderId : orderIdList) {
            try {
                LanHaiAfterReplenishVerifyResponse lanHaiAfterReplenishVerifyResponse = payAfterReplenishQuery(orderId);
                if (!Objects.equals(lanHaiAfterReplenishVerifyResponse.getHandlerStatus(),"001")){
                    resultMap.put(orderId, JSONUtil.toJsonStr(lanHaiAfterReplenishVerifyResponse));
                }
            } catch (Exception e) {
                log.error("batchPayAfterReplenish e:{}", e.getMessage());
                resultMap.put(orderId, e.getMessage());
            }
        }
        log.info("batchPayAfterReplenish result:{}", JSONUtil.toJsonStr(resultMap));
        return resultMap;
    }

    @Override
    public void regularlyPushPostLoanSupplements(Integer days) {
        lanHaiExpandService.regularlyPushPostLoanSupplements(days);
    }

    @Override
    public Map<Integer, String> regularlyQueryPostLoanSupplements(RegularlyQueryPostLoanSupplementsDTO dto) {
        return lanHaiExpandService.regularlyQueryPostLoanSupplements(dto);
    }
}
