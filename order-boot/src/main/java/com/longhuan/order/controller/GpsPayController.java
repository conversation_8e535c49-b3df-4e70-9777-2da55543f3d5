package com.longhuan.order.controller;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.GpsChargeRuleDTO;
import com.longhuan.order.pojo.dto.GpsChargeRuleUpdateDTO;
import com.longhuan.order.pojo.dto.GpsNoChargePersonListDTO;
import com.longhuan.order.pojo.dto.OrderIdDTO;
import com.longhuan.order.pojo.dto.StoreGpsConfigDTO;
import com.longhuan.order.pojo.vo.GpsChargeRuleVO;
import com.longhuan.order.pojo.vo.GpsNoChargePersonListVO;
import com.longhuan.order.pojo.vo.GpsPayVO;
import com.longhuan.order.pojo.vo.StoreGpsConfigVO;
import com.longhuan.order.service.GpsPayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.Map;

/**
 * GPS支付接口
 *
 * <AUTHOR>
 * @date 2025/06/20
 */
@Api(tags = "GPS支付接口")
@RestController
@RequestMapping("/api/v1/gps/pay")
@RequiredArgsConstructor
@Slf4j
public class GpsPayController {
    private final GpsPayService gpsPayService;
    /**
     * 检查是否可选不收费
     *
     * @body dto
     * @return
     */
    @PostMapping("/checkWhetherTheFeeIsNotCharged")
    public Result<GpsPayVO> checkWhetherTheFeeIsNotCharged(@RequestBody OrderIdDTO dto) {

        return Result.success(gpsPayService.queryChargeRuleAndPage(dto));
    }
    /**
     * GPS分期付款
     *
     * @param dto
     * @return
     */
    @PostMapping("/installment")
    public Result<Boolean> installment(@RequestBody OrderIdDTO dto, @CurrentUser LoginUser loginUser) {
        return Result.success(gpsPayService.installment(dto,loginUser));
    }

    /**
     * GPS不收费
     * @param dto
     * @return
     */
    @PostMapping("/thereIsNoCharge")
    public Result<Boolean> thereIsNoCharge(@RequestBody OrderIdDTO dto, @CurrentUser LoginUser loginUser) {
        return Result.success(gpsPayService.thereIsNoCharge(dto,loginUser));
    }
    /**
     * 查询不收费规则
     */
    @PostMapping("/queryChargeRuleAndPage")
    public Result<Page<GpsChargeRuleVO>> queryChargeRule(@RequestBody GpsChargeRuleDTO dto) {
        return Result.success(gpsPayService.queryChargeRule(dto));
    }
    /**
     * 修改不收费规则
     *
     * @param dto
     * @return
     */
    @PostMapping("/editNoChargeRule")
    public Result<Boolean> editNoChargeRule(@RequestBody GpsChargeRuleUpdateDTO dto) {
        return Result.success(gpsPayService.editNoChargeRule(dto));
    }
    /**
     * 使用不收费人员列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/useNoChargePersonList")
    public Result<Page<GpsNoChargePersonListVO>> useNoChargePersonList(@RequestBody GpsNoChargePersonListDTO dto) {
        return Result.success(gpsPayService.useNoChargePersonList(dto));
    }
    /**
     * 定时更新Gps还款计划
     */
    @ApiOperation(value = "定时更新Gps还款计划")
    @PostMapping("/updateGpsRepaymentPlan")
    public Result<String> updateGpsRepaymentPlan() {
        return Result.success(gpsPayService.updateGpsRepaymentPlan());
    }
    /**
     * 宝付安装回调
     */
    @ApiOperation(value = "宝付安装回调")
    @PostMapping("/baofuPayCallBack")
    public String baofuPayCallBack( @RequestParam Map<String, String> allParams){
        log.info("baofuPayCallBack:params:{}", JSONUtil.toJsonStr(allParams));
        return "OK";
    }

    /**
     * 批量设置支持不收费和分期的门店
     */
    @ApiOperation(value = "批量设置门店GPS收费策略配置", notes = "支持批量配置门店的免费GPS服务和分期付款策略")
    @PostMapping("/setStoreGpsConfig")
    public Result<Boolean> setStoreGpsConfig(@RequestBody StoreGpsConfigDTO dto, @CurrentUser LoginUser loginUser) {
        return Result.success(gpsPayService.setStoreGpsConfig(dto, loginUser));
    }

    /**
     * 根据门店ID查询GPS配置
     *
     * @param deptId 门店ID
     * @return GPS配置信息
     */
    @ApiOperation(value = "根据门店ID查询GPS配置")
    @GetMapping("/getStoreGpsConfig/{deptId}")
    public Result<StoreGpsConfigVO> getStoreGpsConfig(@PathVariable Integer deptId) {
        return Result.success(gpsPayService.getStoreGpsConfig(deptId));
    }

}