package com.longhuan.approve.boot.enums.keshang;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjUtil;
import com.longhuan.approve.boot.enums.fumin.FuMinDictEnum;
import com.longhuan.common.core.enums.dict.*;
import lombok.Getter;

public interface KeShangDictEnum {

    /**
     * 性别类型枚举
     */
    @Getter
    enum GenderType {
        MALE("1", "男"),
        FEMALE("2", "女"),
        UNKNOWN("9", "未说明的性别");

        private final String code;
        private final String description;

        GenderType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        // 性别转换工具方法
        public static String convertGenderCode(Integer gender) {
            if (ObjUtil.isNotNull(gender)){
                return ObjUtil.equals(gender, 1) ? KeShangDictEnum.GenderType.MALE.getCode() : KeShangDictEnum.GenderType.FEMALE.getCode();
            }else {
                return KeShangDictEnum.GenderType.UNKNOWN.getCode();
            }
        }

    }

    /**
     * 授权方式枚举
     */
    @Getter
    enum AuthorizationMethodEnum {
        OFFLINE_FACE_TO_FACE("01", "线下本人面签授权书"),
        ONLINE_BIOMETRIC_SIGNATURE("11", "线上有生物识别的电子签名授权"),
        ONLINE_NON_BIOMETRIC_SIGNATURE("12", "线上无生物识别的电子签名授权"),
        ONLINE_OTHER_AUTHORIZATION("19", "线上其他授权方式");

        private final String code;
        private final String description;

        AuthorizationMethodEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }

    /**
     * 生物识别类型枚举
     */
    @Getter
    public enum BiometricTypeEnum {
        FACE_RECOGNITION("01", "人脸识别"),
        FINGERPRINT_RECOGNITION("02", "指纹识别"),
        IRIS_RECOGNITION("03", "虹膜识别"),
        VOICE_RECOGNITION("04", "发音识别"),
        SIGNATURE_RECOGNITION("05", "签名识别"),
        RETINA_RECOGNITION("06", "视网膜识别"),
        OTHER("99", "其他生物识别");

        private final String code;
        private final String description;

        BiometricTypeEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }

    /**
     * 身份类型
     */
    @Getter
    enum IdCardType {
        RESIDENT("10100", "居民身份证"),
        TEMPORAYR("10200", "临时身份证"),
        PASSPORT("10400", "护照");

        private final String code;
        private final String description;

        IdCardType(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }

    @Getter
    public enum MaritalStatusType {
        UNMARRIED("10", "未婚"),
        MARRIED("20", "已婚"),
        FIRST_MARRIAGE("21", "初婚"),
        REMARRIAGE("22", "再婚"),
        REMARRY("23", "复婚"),
        WIDOWED("30", "丧偶"),
        DIVORCED("40", "离婚"),
        UNKNOWN("90", "未说明的婚姻状况");

        private final String code;
        private final String description;

        MaritalStatusType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static String convert(DictMaritalStatus maritalStatus) {
            if (maritalStatus == null) {
                return KeShangDictEnum.MaritalStatusType.UNKNOWN.code;
            }
            return switch (maritalStatus) {
                case UNMARRIED -> KeShangDictEnum.MaritalStatusType.UNMARRIED.code;
                case WIDOWED ->KeShangDictEnum.MaritalStatusType.WIDOWED.code;
                case MARRIED_WITH_CHILDREN -> KeShangDictEnum.MaritalStatusType.MARRIED.code;
                case MARRIED_WITHOUT_CHILDREN -> KeShangDictEnum.MaritalStatusType.MARRIED.code;
                case DIVORCED -> KeShangDictEnum.MaritalStatusType.DIVORCED.code;
                default -> KeShangDictEnum.MaritalStatusType.UNKNOWN.code;
            };
        }
    }
    @Getter
    public enum ResidenceStatusEnum {
        SELF_OWNED("1", "自置"),
        MORTGAGE("2", "按揭"),
        RELATIVE_PROPERTY("3", "亲属楼宇"),
        COLLECTIVE_DORMITORY("4", "集体宿舍"),
        RENTAL("5", "租房"),
        JOINT_OWNERSHIP("6", "共有住宅"),
        OTHER("9", "其他"),
        UNKNOWN("0", "未知");

        private final String code;
        private final String description;

        ResidenceStatusEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static String convert(DictResidentStatus residentStatus) {
            if (residentStatus == null) {
                return KeShangDictEnum.ResidenceStatusEnum.UNKNOWN.code;
            }
            return switch (residentStatus) {
                case OWNED_NO_MORTGAGE -> KeShangDictEnum.ResidenceStatusEnum.SELF_OWNED.code;
                case OWNED_WITH_MORTGAGE -> KeShangDictEnum.ResidenceStatusEnum.MORTGAGE.code;
                case RELATIVE_HOUSING -> KeShangDictEnum.ResidenceStatusEnum.RELATIVE_PROPERTY.code;
                case RENTED -> KeShangDictEnum.ResidenceStatusEnum.RENTAL.code;
                default -> KeShangDictEnum.ResidenceStatusEnum.UNKNOWN.code;
            };
        }
    }


    @Getter
    public enum CompanyType {
        GOVERNMENT_INSTITUTION("1", "机关事业"),
        MILITARY("2", "军队"),
        SPECIAL_INDUSTRY("3", "特殊行业"),
        OTHER_ENTERPRISE("4", "其他企业"),
        SELF_EMPLOYED("5", "个体经营"),
        OTHER("9", "其他");

        private final String code;
        private final String description;

        CompanyType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static String convert(DictCompanyType companyType) {
            if (companyType == null) {
                return null;
            }
            return switch (companyType) {
                case INDIVIDUAL_BUSINESS -> KeShangDictEnum.CompanyType.SELF_EMPLOYED.code; // 个体工商户归类为民营
                case PERSONAL -> KeShangDictEnum.CompanyType.OTHER.code; // 个人归类为民营
                case STATE_OWNED_ENTERPRISE -> KeShangDictEnum.CompanyType.OTHER.code; // 国有企业
                case GOVERNMENT_ORGANIZATION -> KeShangDictEnum.CompanyType.GOVERNMENT_INSTITUTION.code; // 机关单位
                case SOCIAL_ORGANIZATION -> KeShangDictEnum.CompanyType.OTHER.code; // 社会团体归类为事业单位
                case PUBLIC_INSTITUTION -> KeShangDictEnum.CompanyType.OTHER.code; // 事业单位
                case PRIVATE_ENTERPRISE -> KeShangDictEnum.CompanyType.OTHER.code; // 民营企业
                case FOREIGN_ENTERPRISE -> KeShangDictEnum.CompanyType.OTHER.code; // 外资企业归类为三资
            };
        }

    }

    /**
     * 职业信息枚举
     */
    @Getter
    public enum OccupationEnum {
        EXECUTIVE("10000", "党的机关、国家机关、群众团体和社会组织、企事业单位负责人"),
        PROFESSIONAL("20000", "专业技术人员"),
        CLERICAL("30000", "办事人员和有关人员"),
        SERVICE_WORKER("40000", "社会生产服务和生活服务人员"),
        AGRICULTURAL_WORKER("50000", "农、林、牧、渔业生产及辅助人员"),
        MANUFACTURING_WORKER("60000", "生产制造及有关人员"),
        MILITARY("70000", "军人"),
        OTHER_WORKER("80000", "不便分类的其他从业人员");

        private final String code;
        private final String description;

        OccupationEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static String convert(DictOccupationEnum job) {
            if (job == null)
                return OTHER_WORKER.getCode();
            return switch (job) {
                case ORGANIZATION -> EXECUTIVE.getCode();
                case PROFESSIONAL_TECHNICAL -> PROFESSIONAL.getCode();
                case CLERICAL -> CLERICAL.getCode();
                case SOCIAL_SERVICE -> SERVICE_WORKER.getCode();
                case FARMING -> AGRICULTURAL_WORKER.getCode();
                case MANUFACTURING -> MANUFACTURING_WORKER.getCode();
                case SOLDIER -> MILITARY.getCode();
                default -> OTHER_WORKER.getCode();
            };
        }
    }

    /**
     * 职位枚举
     */
    @Getter
    public enum PositionEnum {
        SENIOR_LEADER("1", "高级领导（行政级别局级及局级以上领导或大公司高级管理人员）"),
        MIDDLE_LEADER("2", "中级领导（行政级别局级以下领导或大公司中级管理人员）"),
        GENERAL_STAFF("3", "一般员工"),
        OTHER("9", "其他"),
        UNKNOWN("0", "未知");

        private final String code;

        private final String description;

        PositionEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }

    /**
     * 教育程度枚举
     */
    @Getter
    public enum EducationLevelEnum {

        UNKNOWN("00", "未知"),
        POSTGRADUATE("10", "研究生教育"),
        BACHELOR("20", "大学本科"),
        COLLEGE("30", "专科教育"),
        SECONDARY_VOCATIONAL("40", "中等职业教育"),
        TECHNICAL_SCHOOL("47", "技工学校毕业"),
        HIGH_SCHOOL("60", "普通高级中学教育"),
        JUNIOR_HIGH_SCHOOL("70", "初级中学教育"),
        PRIMARY_SCHOOL("80", "小学教育"),
        OTHER("90", "其他");

        private final String code;
        private final String description;

        EducationLevelEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }
        public static String convert(DictEducationLevel educationLevel) {
            if (educationLevel == null) {
                return null;
            }
            return switch (educationLevel) {
                case MASTER, DOCTOR -> KeShangDictEnum.EducationLevelEnum.POSTGRADUATE.getCode();
                case UNIVERSITY_BACHELOR -> KeShangDictEnum.EducationLevelEnum.BACHELOR.getCode();
                case UNIVERSITY_SPECIALTY -> KeShangDictEnum.EducationLevelEnum.COLLEGE.getCode();
                case TECHNICAL_SECONDARY_SCHOOL, HIGH_SCHOOL -> KeShangDictEnum.EducationLevelEnum.HIGH_SCHOOL.getCode();
                case JUNIOR_HIGH_SCHOOL_AND_BELOW -> KeShangDictEnum.EducationLevelEnum.PRIMARY_SCHOOL.getCode();
                default -> KeShangDictEnum.EducationLevelEnum.OTHER.getCode();
            };
        }

    }


    /**
     * 单位所属行业枚举
     */
    @Getter
    public enum IndustryTypeEnum {
        A("A", "农、林、牧、渔业"),
        B("B", "采掘业"),
        C("C", "制造业"),
        D("D", "电力、燃气及水的生产和供应业"),
        E("E", "建筑业"),
        F("F", "交通运输、仓储和邮政业"),
        G("G", "信息传输、计算机服务和软件业"),
        H("H", "批发和零售业"),
        I("I", "住宿和餐饮业"),
        J("J", "金融业"),
        K("K", "房地产业"),
        L("L", "租赁和商务服务业"),
        M("M", "科学研究、技术服务业和地质勘察业"),
        N("N", "水利、环境和公共设施管理业"),
        O("O", "居民服务和其他服务业"),
        P("P", "教育"),
        Q("Q", "卫生、社会保障和社会福利业"),
        R("R", "文化、体育和娱乐业"),
        S("S", "公共管理和社会组织"),
        T("T", "国际组织"),
        Z("Z", "未知");

        private final String code;
        private final String description;

        IndustryTypeEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static String convert(DictIndustryEnum dictIndustryEnum) {
            if (dictIndustryEnum == null) {
                return  KeShangDictEnum.IndustryTypeEnum.Z.getCode();
            }
            return switch (dictIndustryEnum) {
                case AGRICULTURE -> KeShangDictEnum.IndustryTypeEnum.A.getCode();
                case MINING -> KeShangDictEnum.IndustryTypeEnum.B.getCode();
                case MANUFACTURING -> KeShangDictEnum.IndustryTypeEnum.C.getCode();
                case UTILITIES -> KeShangDictEnum.IndustryTypeEnum.D.getCode();
                case CONSTRUCTION -> KeShangDictEnum.IndustryTypeEnum.E.getCode();
                case TRANSPORTATION -> KeShangDictEnum.IndustryTypeEnum.F.getCode();
                case IT_SERVICES -> KeShangDictEnum.IndustryTypeEnum.G.getCode();
                case WHOLESALE_RETAIL -> KeShangDictEnum.IndustryTypeEnum.H.getCode();
                case HOSPITALITY -> KeShangDictEnum.IndustryTypeEnum.I.getCode();
                case FINANCE -> KeShangDictEnum.IndustryTypeEnum.J.getCode();
                case REAL_ESTATE -> KeShangDictEnum.IndustryTypeEnum.K.getCode();
                case BUSINESS_SERVICES -> KeShangDictEnum.IndustryTypeEnum.L.getCode();
                case SCIENTIFIC_TECH -> KeShangDictEnum.IndustryTypeEnum.M.getCode();
                case ENVIRONMENT -> KeShangDictEnum.IndustryTypeEnum.N.getCode();
                case RESIDENTIAL_SERVICES -> KeShangDictEnum.IndustryTypeEnum.O.getCode();
                case EDUCATION -> KeShangDictEnum.IndustryTypeEnum.P.getCode();
                case HEALTHCARE -> KeShangDictEnum.IndustryTypeEnum.Q.getCode();
                case CULTURE_SPORTS -> KeShangDictEnum.IndustryTypeEnum.R.getCode();
                case PUBLIC_ADMIN -> KeShangDictEnum.IndustryTypeEnum.S.getCode();
                case INTERNATIONAL_ORG -> KeShangDictEnum.IndustryTypeEnum.Z.getCode();
                case STUDENT -> KeShangDictEnum.IndustryTypeEnum.Z.getCode();
                case OTHER -> KeShangDictEnum.IndustryTypeEnum.Z.getCode();
                default -> KeShangDictEnum.IndustryTypeEnum.Z.getCode();
            };
        }
    }

    /**
     * 联系人与申请人关系枚举
     */
    @Getter
    public enum ContactRelationEnum {
        OTHER("00", "其他"),
        PERSONAL("11", "个人关系"),
        SPOUSE("21", "夫妻关系"),
        PARENT("31", "父母"),
        CHILD("32", "子女"),
        SIBLING("43", "兄弟姐妹关系"),
        GRANDPARENT("51", "祖父母"),
        GRANDCHILD("52", "孙子女"),
        OTHER_RELATIVE("61", "其他亲属关系"),
        COLLEAGUE("71", "同事关系"),
        CLASSMATE("81", "同学"),
        FRIEND("91", "朋友关系"),
        LOVER("92", "情侣关系");

        private final String code;
        private final String description;

        ContactRelationEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static String convert(DictContactRelation dictContactRelation) {
            if (dictContactRelation == null) {
                return  KeShangDictEnum.ContactRelationEnum.OTHER.getCode();
            }
            return switch (dictContactRelation) {
                case SPOUSE -> KeShangDictEnum.ContactRelationEnum.SPOUSE.getCode();
                case CHILDREN -> KeShangDictEnum.ContactRelationEnum.CHILD.getCode();
                case FATHER -> KeShangDictEnum.ContactRelationEnum.PARENT.getCode();
                case RELATIVE -> KeShangDictEnum.ContactRelationEnum.OTHER_RELATIVE.getCode();//todo 直系亲属
                case FRIEND -> KeShangDictEnum.ContactRelationEnum.FRIEND.getCode();
                case COLLEAGUE -> KeShangDictEnum.ContactRelationEnum.COLLEAGUE.getCode();
                case MOTHER -> KeShangDictEnum.ContactRelationEnum.PARENT.getCode();
                default -> KeShangDictEnum.ContactRelationEnum.OTHER.getCode();
            };
        }
    }

    /**
     * 合作产品种类枚举
     */
    @Getter
    public enum ChnlTypeEnum {
        BUSINESS_LOAN("01", "经营贷"),
        CONSUMER_LOAN("02", "消费贷");

        private final String code;

        private final String description;

        ChnlTypeEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

    }

    /**
     * 车辆种类枚举
     */
    @Getter
    enum CarKind {
        PASSENGER("01", "乘用车"),
        COMMERCIAL("02", "商用车"),
        NETWORK("03", "网约车"),
        OPERATING("04", "运营车"),
        PERSONAL_NETWORK("05", "个人网约车");

        private final String code;
        private final String description;

        CarKind(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }

    /**
     * 车辆类型枚举
     */
    @Getter
    enum CarType {
        CONVENTIONAL_NEW_CAR("01", "常规新车"),
        NEW_ENERGY_CAR("02", "新能源车"),
        CONVENTIONAL_USED_CAR("03", "常规二手车"),
        NEW_ENERGY_USED_CAR("04", "新能源二手车");

        private final String code;
        private final String description;

        CarType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        /**
         * 匹配车辆类型
         *
         * @return {@link FuMinDictEnum.CarType}
         */
        public static String matchCarType(String vehicleNumber, Integer transferTimes) {

            boolean isGreen = false;
            int count = Convert.toInt(transferTimes, 0);
            if (vehicleNumber != null && vehicleNumber.length() == 8) {
                isGreen = true;
            }

            if (isGreen) {
                if (count == 0) {
                    return FuMinDictEnum.CarType.NEW_ENERGY_CAR.getCode();
                } else {
                    return FuMinDictEnum.CarType.NEW_ENERGY_USED_CAR.getCode();
                }
            } else {
                if (count == 0) {
                    return FuMinDictEnum.CarType.CONVENTIONAL_NEW_CAR.getCode();
                } else {
                    return FuMinDictEnum.CarType.CONVENTIONAL_NEW_CAR.getCode();
                }
            }
        }

    }

    /**
     * 人群类型枚举
     */
    @Getter
    public enum CustomerTypeEnum {
        SELF_EMPLOYED("1", "自雇人群"),
        SALARIED("2", "工薪人群");

        private final String code;
        private final String description;

        CustomerTypeEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static CustomerTypeEnum fromCode(String code) {
            for (CustomerTypeEnum type : CustomerTypeEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("未知的人群类型代码: " + code);
        }
        public static String convert(String customerType) {
            if (customerType == null) {
                return "";
            }
            return switch (customerType) {
                case "1" -> KeShangDictEnum.CustomerTypeEnum.SELF_EMPLOYED.code;
                case "2" -> KeShangDictEnum.CustomerTypeEnum.SELF_EMPLOYED.code;
                case "3" -> KeShangDictEnum.CustomerTypeEnum.SALARIED.code;
                default -> "";
            };
        }

    }


    /**
     * 拒绝原因枚举
     */
    @Getter
    public enum RejectReasonEnum {

        USER_REASON("1", "用户原因"),
        MATERIAL_REVIEW_FAILED("2", "材料审核不通过"),
        LOAN_REVIEW_FAILED("3", "放款审核不通过");

        private final String code;
        private final String description;

        RejectReasonEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }

    /**
     * 处理结果枚举
     */
    @Getter
    public enum TxnStatusEnum {
        ORDER_NOT_EXIST("00", "订单不存在"),
        APPROVAL_PROCESSING("11", "进件审批处理中"),
        APPROVAL_REJECTED("12", "进件审批拒绝"),
        APPROVAL_PASSED("13", "进件审批通过"),
        APPLICATION_REVOKED("23", "进件已撤销"),
        MANUAL_REVIEW_PROCESSING("25", "人工审核中"),
        MANUAL_REVIEW_REJECTED("26", "人工审核驳回"),
        MANUAL_REVIEW_PASSED("27", "人工审核通过"),
        LOAN_PROCESSING("31", "放款处理中"),
        LOAN_FAILED("32", "放款失败"),
        LOAN_SUCCESS("33", "放款成功"),
        OTHER("99", "其他");


        private final String code;
        private final String description;

        TxnStatusEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

    }
    @Getter
    public enum RetCdStatus {
        SUCCESS("00", "成功"),
        FAILURE("01", "失败"),
        PROCESSING("99", "处理中"),
        SYSTEM_ERROR("10", "系统异常");

        private final String code;
        private final String description;

        RetCdStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }
    /**
     * 文件类型枚举
     */
    @Getter
    public enum FileTypeEnum {

        // 身份证明文件类型
        ID_CARD("0100", "身份证"),
        DRIVER_LICENSE("0101", "驾驶证文件（正副本）"),
        BANK_CARD("0102", "银行卡"),
        BANK_STATEMENT("0103", "银行流水"),
        CREDIT_AUTHORIZATION("0104", "人行个人征信查询授权书"),
        MANAGER_PHOTO_WITH_CLIENT("0106", "客户经理与借款人及车辆合影照片"),
        COMMERCIAL_INSURANCE("0108", "商业险保单"),
        RESIDENCE_PROOF("0110", "本地常住证明"),
        VEHICLE_PHOTO("0111", "车辆照片"),
        LIFE_INSURANCE("0114", "寿险保单"),
        HOUSING_FUND_RECORD("0115", "公积金缴纳记录"),
        MEIZHOU_CUSTOMER_BANK_AUTHORIZATION("0116", "梅州客商银行个人信息使用授权书"),
        FULL_PAYMENT_HOUSE_PROOF("0112", "全款房证明"),
        HOUSE_PAYMENT_SETTLED_PROOF("0113", "房款结清证明"),
        MORTGAGE_HOUSE_PROOF("0117", "按揭房证明"),
        MORTGAGED_HOUSE_PROOF("0119", "抵押房证明"),
        CAR_LOAN_PROOF("0118", "车贷证明"),
        CLIENT_FACE_PHOTO("0120", "客户人脸照片"),
        BAIXING_CREDIT_AUTHORIZATION("0127", "百行征信授权书"),
        PUDAO_CREDIT_AUTHORIZATION("0128", "朴道征信授权书"),

        // 合同文件类型
        LOAN_CONTRACT("0200", "借款合同"),
        GUARANTEE_CONTRACT("0201", "担保合同"),
        LEASE_CONTRACT("0202", "租赁合同"),
        MORTGAGE_CONTRACT("0203", "抵押合同"),
        COMMITMENT_AUTHORIZATION("0204", "承诺与授权书"),
        VEHICLE_ASSESSMENT_REPORT("0205", "车辆评估报告"),
        MORTGAGE_DISBURSEMENT_VOUCHER("0206", "抵押联放款凭证"),
        DISBURSEMENT_VOUCHER("0207", "放款凭证"),
        MOTOR_VEHICLE_CERTIFICATE_MORTGAGE("0208", "机动车登记证抵押页"),
        GPS_INSTALLATION_CONFIRMATION("0209", "GPS安装确认书"),
        WITHDRAWAL_NOTICE("0210", "提款通知书"),
        DEDUCTION_COMMITMENT("0211", "代扣承诺书"),
        MORTGAGED_ITEM_CERTIFICATE("0212", "抵押物办妥的凭证"),
        MORTGAGED_ITEM_STORAGE_NOTICE("0213", "抵押物入库通知书"),
        LOAN_NOTE("0214", "借款借据"),
        FACE_SIGNING_VIDEO("0215", "面签视频"),

        // 其他文件类型
        VEHICLE_HANDOVER_FORM("0302", "车辆交接单"),
        REGISTRATION_CERTIFICATE("0304", "登记证"),
        VEHICLE_LICENSE("0305", "车辆行驶证"),
        OTHER_DOCUMENTS("0400", "其他相关资料");

        private final String code;
        private final String description;

        FileTypeEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static FileTypeEnum fromCode(String code) {
            for (FileTypeEnum type : FileTypeEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("未知的文件类型代码: " + code);
        }
    }
    /**
     * 审核结果枚举
     */
    @Getter
    public enum ResultEnum {
        REJECTED("0", "拒绝"),
        APPROVED("1", "通过");

        private final String code;
        private final String description;

        ResultEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static ResultEnum fromCode(String code) {
            for (ResultEnum result : ResultEnum.values()) {
                if (result.getCode().equals(code)) {
                    return result;
                }
            }
            throw new IllegalArgumentException("未知的审核结果代码: " + code);
        }
    }

    /**
     * 结果枚举
     */
    @Getter
    public enum DisbursementResultEnum {

        SUCCESS("1", "放款成功"),
        FAILED("0", "放款失败");

        private final String code;
        private final String description;

        DisbursementResultEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static DisbursementResultEnum fromCode(String code) {
            for (DisbursementResultEnum result : DisbursementResultEnum.values()) {
                if (result.getCode().equals(code)) {
                    return result;
                }
            }
            throw new IllegalArgumentException("未知的放款结果代码: " + code);
        }
    }

    /**
     * 文件签署状态枚举
     */
    @Getter
    public enum FileSignStatusEnum {
        INIT("init", "未签署"),
        SIGNING("signing", "签署中"),
        CANCELLED("cancelled", "作废"),
        REFUSED("refused", "拒签"),
        SIGNED("signed", "签署成功");

        private final String code;
        private final String description;

        FileSignStatusEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static FileSignStatusEnum fromCode(String code) {
            for (FileSignStatusEnum status : FileSignStatusEnum.values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("未知的签署状态代码: " + code);
        }
    }

    /**
     * 账号类型枚举
     */
    @Getter
    public enum AccountTypeEnum {
        TYPE_ONE("1", "一类户"),
        TYPE_TWO("2", "二类户");

        private final String code;
        private final String description;

        AccountTypeEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static AccountTypeEnum fromCode(String code) {
            for (AccountTypeEnum type : AccountTypeEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("未知的账号类型代码: " + code);
        }
    }

    /**
     * 成功标识
     */
    @Getter
    public enum BackResultEnum {

        SUCCESS("1", "成功"),
        FAILED("0", "失败");

        private final String code;
        private final String description;

        BackResultEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public static BackResultEnum fromCode(String code) {
            for (BackResultEnum result : BackResultEnum.values()) {
                if (result.getCode().equals(code)) {
                    return result;
                }
            }
            throw new IllegalArgumentException("未知的操作结果代码: " + code);
        }
    }


    /**
     * 绑卡状态枚举
     */
    @Getter
    public enum BindCardStatusEnum {
        SUCCESS("S", "成功"),
        FAIL("F", "失败");

        private final String code;
        private final String description;

        BindCardStatusEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        /**
         * 根据代码获取枚举值
         * @param code 状态代码
         * @return 对应的枚举值
         */
        public static BindCardStatusEnum fromCode(String code) {
            for (BindCardStatusEnum status : BindCardStatusEnum.values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("未知的绑卡状态代码: " + code);
        }

    }

    /**
     * 号牌类型枚举
     */
    @Getter
    public enum KeShangPlateTypeEnum {
        LARGE_VEHICLE("01", "大型汽车号牌"),
        SMALL_VEHICLE("02", "小型汽车号牌"),
        EMBASSY_VEHICLE("03", "使馆汽车号牌"),
        CONSULATE_VEHICLE("04", "领馆汽车号牌"),
        OVERSEAS_VEHICLE("05", "境外汽车号牌"),
        FOREIGN_VEHICLE("06", "外籍汽车号牌"),
        MOTORCYCLE("07", "两、三轮摩托车号牌"),
        MOPED("08", "轻便摩托车号牌"),
        EMBASSY_MOTORCYCLE("09", "使馆摩托车号牌"),
        CONSULATE_MOTORCYCLE("10", "领馆摩托车号牌"),
        OVERSEAS_MOTORCYCLE("11", "境外摩托车号牌"),
        FOREIGN_MOTORCYCLE("12", "外籍摩托车号牌"),
        AGRICULTURAL_VEHICLE("13", "农用运输车号牌"),
        TRACTOR("14", "拖拉机号牌"),
        TRAILER("15", "挂车号牌"),
        COACHING_VEHICLE("16", "教练汽车号牌"),
        COACHING_MOTORCYCLE("17", "教练摩托车号牌"),
        TEST_VEHICLE("18", "试验汽车号牌"),
        TEST_MOTORCYCLE("19", "试验摩托车号牌"),
        TEMPORARY_ENTRY_VEHICLE("20", "临时入境汽车号牌"),
        TEMPORARY_ENTRY_MOTORCYCLE("21", "临时入境摩托车号牌"),
        TEMPORARY_LICENSE("22", "临时行驶车号牌"),
        POLICE_VEHICLE("23", "公安警车号牌"),
        CIVIL_POLICE("24", "公安民用号牌"),
        ORIGINAL_AGRICULTURAL("25", "原农机号牌"),
        HONG_KONG_ENTRY("26", "香港入出境车号牌"),
        MACAU_ENTRY("27", "澳门入出境车号牌"),
        ARMED_POLICE("31", "武警号牌"),
        MILITARY("32", "军队号牌"),
        NO_PLATE("41", "无号牌"),
        FAKE_PLATE("42", "假号牌"),
        MISUSED_PLATE("43", "挪用号牌"),
        LARGE_NEW_ENERGY("51", "大型新能源汽车（新能源专用号牌）"),
        SMALL_NEW_ENERGY("52", "小型新能源汽车（新能源专用号牌）"),
        OTHER("99", "其他");

        private final String code;
        private final String description;

        KeShangPlateTypeEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        /**
         * 根据代码获取枚举值
         *
         * @param code 号牌类型代码
         * @return 对应的枚举值
         */
        public static KeShangPlateTypeEnum fromCode(String code) {
            for (KeShangPlateTypeEnum plateType : KeShangPlateTypeEnum.values()) {
                if (plateType.getCode().equals(code)) {
                    return plateType;
                }
            }
            return null;
        }

        /**
         * 根据车牌号判断号牌类型
         * @param plateNumber 车牌号
         * @return 对应的号牌类型枚举
         */
        public static KeShangPlateTypeEnum matchPlateType(String plateNumber) {
            if (plateNumber == null || plateNumber.isEmpty()) {
                return null;
            }

            // 根据车牌号特征判断号牌类型
            if (plateNumber.matches(".*[A-Z]{2}[0-9]{5,}")) {
                // 新能源车牌特征（如京AD12345）
                if (plateNumber.length() == 7) {
                    return SMALL_NEW_ENERGY;
                } else if (plateNumber.length() == 8) {
                    return LARGE_NEW_ENERGY;
                }
            } else if (plateNumber.matches(".*[A-Z]{2}[0-9]{5,}[A-Z]")) {
                // 使馆车辆（如京A12345使）
                return EMBASSY_VEHICLE;
            } else if (plateNumber.matches(".*[A-Z]{2}[0-9]{5,}[警]")) {
                // 警车（如京A12345警）
                return POLICE_VEHICLE;
            } else if (plateNumber.matches(".*[A-Z]{2}[0-9]{5,}[学]")) {
                // 教练车（如京A12345学）
                return COACHING_VEHICLE;
            } else if (plateNumber.matches(".*[A-Z]{2}[0-9]{5,}[挂]")) {
                // 挂车（如京A12345挂）
                return TRAILER;
            } else if (plateNumber.matches(".*[A-Z]{2}[0-9]{5,}[临]")) {
                // 临时车牌（如京A12345临）
                return TEMPORARY_LICENSE;
            }

            // 默认返回小型汽车号牌
            return SMALL_VEHICLE;
        }

    }


    /**
     * 风险校验类型枚举
     */
    @Getter
    public enum RiskCheckTypeEnum {
        CUSTOMER_BASIC_ADJUST("01", "客户基本准入"),
        APPLICATION_LOGIC_CHECK("02", "申请信息逻辑校验"),
        IDENTITY_VERIFICATION("03", "身份核验"),
        PEOPLE_BANK_CREDIT_HIGH_RISK("04", "人行征信高风险"),
        INTERNAL_BLACKLIST("05", "内部黑名单"),
        CREDIT_RISK_01("06", "失信风险01"),
        LITIGATION_RISK_01("07", "涉诉风险01"),
        EXTERNAL_BLACKLIST("08", "外部黑名单"),
        CREDIT_RISK_02("09", "失信风险02"),
        MULTIPLE_LOAN_RISK("10", "多头借贷风险"),
        STABILITY_RISK("11", "稳定性风险"),
        A0_1_PRE_SCREEN_REJECT("12", "A0_1前置初筛自动拒绝"),
        A0_2_PRE_SCREEN_REJECT("13", "A0_2前置初筛自动拒绝"),
        A0_3_PRE_SCREEN_REJECT("14", "A0_3前置初筛自动拒绝"),
        A0_4_PRE_SCREEN_REJECT("15", "A0_4前置初筛自动拒绝"),
        A0_5_PRE_SCREEN_REJECT("16", "A0_5前置初筛自动拒绝"),
        A0_6_PRE_SCREEN_REJECT("17", "A0_6前置初筛自动拒绝"),
        A0_7_PRE_SCREEN_REJECT("18", "A0_7前置初筛自动拒绝");

        private final String code;
        private final String description;

        RiskCheckTypeEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        /**
         * 根据代码获取枚举值
         *
         * @param code 风险校验代码
         * @return 对应的枚举值
         */
        public static RiskCheckTypeEnum fromCode(String code) {
            for (RiskCheckTypeEnum type : RiskCheckTypeEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("未知的风险校验代码: " + code);
        }
    }



}