package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.RiskAccidentCarDTO;
import com.longhuan.order.pojo.entity.RiskAccidentCarEntity;
import com.longhuan.order.pojo.vo.RiskAccidentCarVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface RiskAccidentCarConverter {

    RiskAccidentCarVO entityToVO(RiskAccidentCarEntity riskAccidentCarEntity);

    RiskAccidentCarEntity dtoToEntity(RiskAccidentCarDTO riskAccidentCarDTO);
}
