package com.longhuan.order.statemachine.aguard;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.mapper.OrderNodeRecordMapper;
import com.longhuan.order.pojo.entity.OrderNodeRecordEntity;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.guard.Guard;

/**
 * 状态保护机制，对一个状态转移进行评估，评估值为true允许状态转移，评估值为false禁止转移
 */
@Slf4j
//@Component
@RequiredArgsConstructor
public class CustomerConfirmGuard implements Guard<States, Events> {
    private final boolean exits;
    private final OrderInfoMapper orderInfoMapper;
    private final OrderNodeRecordMapper orderNodeRecordMapper;

    @Override
    public boolean evaluate(StateContext<States, Events> context) {
        Integer orderId = context.getMessageHeaders().get(StateConstants.ORDER_ID, Integer.class);
        Events event = context.getEvent();
        log.info("orderId:{}, CustomerConfirmGuard event:{}", orderId, event);
        OrderNodeRecordEntity orderNodeRecordEntity = orderNodeRecordMapper.selectOne(new LambdaQueryWrapper<OrderNodeRecordEntity>()
//                .eq(OrderNodeRecordEntity::getCurrentNode, States.CUSTOMER_CONFIRM.getNode())
                .eq(OrderNodeRecordEntity::getEvent, Events.CUSTOMER_CONFIRM)
                .eq(OrderNodeRecordEntity::getOrderId, orderId).last("limit 1"));

        boolean res = orderNodeRecordEntity != null;
        return res == exits;

    }
}