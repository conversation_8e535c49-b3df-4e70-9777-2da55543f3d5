package com.longhuan.order.converter;

import com.longhuan.order.pojo.entity.FileConfigEntity;
import com.longhuan.order.pojo.vo.FileConfigVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 订单文件转换器
 *
 * <AUTHOR>
 * @date 2024/07/24
 */
@Mapper(componentModel = "spring")
public interface FileConfigConverter {
    /**
     * 实体 2vo
     *
     * @param fileConfigEntityList 文件配置实体列表
     * @return {@link List }<{@link FileConfigVO }>
     */
    List<FileConfigVO> entity2vo(List<FileConfigEntity> fileConfigEntityList);
}
