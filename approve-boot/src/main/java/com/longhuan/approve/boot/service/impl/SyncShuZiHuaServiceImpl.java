package com.longhuan.approve.boot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.approve.boot.converter.SyncShuZiHuaConverter;
import com.longhuan.approve.boot.enums.FundRepayStatusEnum;
import com.longhuan.approve.boot.enums.lanhai.SyncShuZiHuaBankEnum;
import com.longhuan.approve.boot.enums.lanhai.SyncShuZiHuaColorEnum;
import com.longhuan.approve.boot.enums.lanhai.SyncShuZiHuaFuelTypeEnum;
import com.longhuan.approve.boot.enums.lanhai.SyncShuZiHuaProductEnum;
import com.longhuan.approve.boot.feign.Car300Feign;
import com.longhuan.approve.boot.feign.DigitalizeFeign;
import com.longhuan.approve.boot.feign.OrderFeign;
import com.longhuan.approve.boot.feign.UserFeign;
import com.longhuan.approve.boot.mapper.*;
import com.longhuan.approve.boot.pojo.dto.FundFinalBaseDTO;
import com.longhuan.approve.boot.pojo.dto.OrderAmountCalDTO;
import com.longhuan.approve.boot.pojo.dto.RepurchaseRepaymentInfoEntity;
import com.longhuan.approve.boot.pojo.dto.SyncShuZiHuaDTO;
import com.longhuan.approve.boot.pojo.dto.syncshuzihua.QueryRepayPlanDTO;
import com.longhuan.approve.boot.pojo.dto.syncshuzihua.SyncCollectPledgeDataDTO;
import com.longhuan.approve.boot.pojo.dto.syncshuzihua.SyncShuZiHuaDigitalOrderDTO;
import com.longhuan.approve.boot.pojo.dto.syncshuzihua.SyncShuZiHuaRepaymentInfoDTO;
import com.longhuan.approve.boot.pojo.entity.*;
import com.longhuan.approve.boot.pojo.vo.SyncCollectPledgeDataVO;
import com.longhuan.approve.boot.pojo.vo.SyncShuZiHuaResultVO;
import com.longhuan.approve.boot.service.FundBaseInfoService;
import com.longhuan.approve.boot.service.FundRepaymentInfoService;
import com.longhuan.approve.boot.service.RepurchaseRepaymentInfoService;
import com.longhuan.approve.boot.service.SyncShuZiHuaService;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.enums.OrderFeeDetailExpandTypeEnum;
import com.longhuan.common.core.enums.SignPlateEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.pojo.dto.OrderApproveFundPlanStatusDTO;
import com.longhuan.risk.pojo.dto.Car300AppraisalDTO;
import com.longhuan.risk.pojo.vo.Car300DataVO;
import com.longhuan.risk.pojo.vo.Car300PriceVO;
import com.longhuan.user.pojo.vo.UserInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SyncShuZiHuaServiceImpl implements SyncShuZiHuaService {

    private final FundBaseInfoService fundBaseInfoService;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final BankAccountSignMapper bankAccountSignMapper;
    private final UserFeign userFeign;
    private final ProductRongdanMapper productRongdanMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final PreFundInfoMapper preFundInfoMapper;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final DigitalizeFeign digitalizeFeign;
    private final Car300Feign car300Feign;
    private final OrderToDigitalSyncLogMapper orderToDigitalSyncLogMapper;
    private final OrderFeeDetailMapper orderFeeDetailMapper;
    private final FundRepaymentInfoService fundRepaymentInfoService;
    private final FundRepurchaseResultMapper fundRepurchaseResultMapper;
    private final RepurchaseRepaymentInfoService repurchaseRepaymentInfoService;
    private final OrderFeign orderFeign;
    private final OrderCustomerInfoMapper orderCustomerInfoMapper;
    private final SyncShuZiHuaConverter syncShuZiHuaConverter;


    @Override
    public SyncShuZiHuaResultVO doProcess(SyncShuZiHuaDTO syncShuziHuaDTO) {

        SyncShuZiHuaResultVO syncShuZiHuaResultVO = new SyncShuZiHuaResultVO();
        List<Integer> successList = Lists.newArrayList();
        List<Integer> failList = Lists.newArrayList();

        //查询同步日志表中失败的记录
        List<OrderToDigitalSyncLogEntity> orderToDigitalSyncLogEntityList = orderToDigitalSyncLogMapper.selectList(new LambdaQueryWrapper<OrderToDigitalSyncLogEntity>()
                .select(OrderToDigitalSyncLogEntity::getOrderId)
                .in(CollUtil.isNotEmpty(syncShuziHuaDTO.getOrderIdList()), OrderToDigitalSyncLogEntity::getOrderId, syncShuziHuaDTO.getOrderIdList())
                .eq(OrderToDigitalSyncLogEntity::getDeleteFlag, 0)
                .eq(OrderToDigitalSyncLogEntity::getSyncStatus, 0)
                .eq(OrderToDigitalSyncLogEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .groupBy(OrderToDigitalSyncLogEntity::getOrderId)
        );

        //若日志表中存在记录,则同步日志表中失败的记录即可
        if (CollUtil.isNotEmpty(orderToDigitalSyncLogEntityList)) {
            //先删除原纪录
            orderToDigitalSyncLogMapper.update(new LambdaUpdateWrapper<OrderToDigitalSyncLogEntity>()
                    .set(OrderToDigitalSyncLogEntity::getDeleteFlag, 1)
                    .in(CollUtil.isNotEmpty(syncShuziHuaDTO.getOrderIdList()), OrderToDigitalSyncLogEntity::getOrderId, syncShuziHuaDTO.getOrderIdList())
                    .eq(OrderToDigitalSyncLogEntity::getDeleteFlag, 0)
                    .eq(OrderToDigitalSyncLogEntity::getSyncStatus, 0)
                    .eq(OrderToDigitalSyncLogEntity::getFundId, FundEnum.LAN_HAI.getValue())
            );
            //需要更新
            List<Integer> orderIdList = orderToDigitalSyncLogEntityList.stream().map(OrderToDigitalSyncLogEntity::getOrderId).collect(Collectors.toCollection(ArrayList::new));

            List<OrderToDigitalSyncLogEntity> orderToDigitalSyncLogSuccessList = orderToDigitalSyncLogMapper.selectList(new LambdaQueryWrapper<OrderToDigitalSyncLogEntity>()
                    .select(OrderToDigitalSyncLogEntity::getOrderId)
                    .in(OrderToDigitalSyncLogEntity::getOrderId, orderIdList)
                    .eq(OrderToDigitalSyncLogEntity::getDeleteFlag, 0)
                    .eq(OrderToDigitalSyncLogEntity::getSyncStatus, 1)
            );
            if (CollUtil.isNotEmpty(orderToDigitalSyncLogSuccessList)) {
                //移除成功记录
                List<Integer> successOrderIdList = orderToDigitalSyncLogSuccessList.stream().map(OrderToDigitalSyncLogEntity::getOrderId).toList();
                orderIdList.removeAll(successOrderIdList);
            }


            if (CollUtil.isNotEmpty(orderIdList)) {
                log.info("同步蓝海放款后信息,日志表存在失败记录 ,orderIdList:{}", orderIdList);
                orderIdList.forEach(orderId -> {
                    try {
                        boolean syncFlag = syncLanHaiPayAfter(orderId);
                        if (syncFlag) {
                            successList.add(orderId);
                        } else {
                            failList.add(orderId);
                        }
                    } catch (BusinessException e) {
                        log.error("同步蓝海放款后信息,失败orderId:{}", orderId);
                        failList.add(orderId);
                        packageRecord(orderId, 0, "", "", e.getMessage());
                    }
                });
                syncShuZiHuaResultVO.setSuccessList(successList);
                syncShuZiHuaResultVO.setFailList(failList);
            }
            return syncShuZiHuaResultVO;
        }

        //查询蓝海且放款成功的订单
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectJoinList(OrderInfoEntity.class, new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId)
                .leftJoin(OrderToDigitalSyncLogEntity.class, on ->
                        on.eq(OrderToDigitalSyncLogEntity::getOrderId, OrderInfoEntity::getId)
                                .eq(OrderToDigitalSyncLogEntity::getSyncStatus, 1)
                                .eq(OrderToDigitalSyncLogEntity::getDeleteFlag, 0)
                )
                .in(CollUtil.isNotEmpty(syncShuziHuaDTO.getOrderIdList()), OrderInfoEntity::getId, syncShuziHuaDTO.getOrderIdList())
                .eq(OrderInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .ge(OrderInfoEntity::getCurrentNode, 5000)
                .isNull(OrderToDigitalSyncLogEntity::getOrderId)
                .groupBy(OrderInfoEntity::getId)
        );

        if (CollUtil.isNotEmpty(orderInfoEntityList)) {
            List<Integer> orderIdList = orderInfoEntityList.stream().map(OrderInfoEntity::getId).toList();
            log.info("同步蓝海放款后信息,首次初始化同步,orderIdList:{}", orderIdList);

            orderInfoEntityList.forEach(v -> {
                //遍历调用同步方法
                try {
                    boolean syncFlag = syncLanHaiPayAfter(v.getId());
                    if (syncFlag) {
                        successList.add(v.getId());
                    } else {
                        failList.add(v.getId());
                    }
                } catch (BusinessException e) {
                    log.error("同步蓝海放款后信息,失败orderId:{}", v.getId());
                    failList.add(v.getId());
                    packageRecord(v.getId(), 0, "", "", e.getMessage());
                }
            });
        }
        syncShuZiHuaResultVO.setSuccessList(successList);
        syncShuZiHuaResultVO.setFailList(failList);
        return syncShuZiHuaResultVO;
    }


    private void packageRecord(Integer orderId,
                               Integer syncStatus,
                               String requestJson,
                               String responseJson,
                               String failReason) {
        OrderToDigitalSyncLogEntity orderToDigitalSyncLogEntity = new OrderToDigitalSyncLogEntity();
        orderToDigitalSyncLogEntity.setOrderId(orderId);
        orderToDigitalSyncLogEntity.setSyncStatus(syncStatus);
        orderToDigitalSyncLogEntity.setRequestJson(requestJson);
        orderToDigitalSyncLogEntity.setResponseJson(responseJson);
        orderToDigitalSyncLogEntity.setFundId(FundEnum.LAN_HAI.getValue());
        orderToDigitalSyncLogEntity.setDeleteFlag(0);
        orderToDigitalSyncLogEntity.setCreateTime(LocalDateTime.now());
        orderToDigitalSyncLogEntity.setUpdateTime(LocalDateTime.now());
        orderToDigitalSyncLogEntity.setFailReason(failReason);
        orderToDigitalSyncLogMapper.insert(orderToDigitalSyncLogEntity);
    }


    /**
     * 同步蓝海放款后信息
     */

    public boolean syncLanHaiPayAfter(Integer orderId) {
        log.info("SyncShuZiHuaServiceImpl.syncLanHaiPayAfter start orderId:{}", orderId);
        FundFinalBaseDTO fundFinalBaseApplyInfo = fundBaseInfoService.getFundFinalBaseApplyInfo(orderId);
        if (ObjectUtil.isNull(fundFinalBaseApplyInfo)) {
            log.info("SyncShuZiHuaServiceImpl.syncLanHaiPayAfter fail orderId:{}", orderId);
            throw new BusinessException("未找到订单基础信息");
        }
        log.info("SyncShuZiHuaServiceImpl.syncLanHaiPayAfter orderId:{} fundFinalBaseApplyInfo:{}", orderId, JSONUtil.toJsonStr(fundFinalBaseApplyInfo));
        OrderAmountCalDTO orderAmountCalDTO = fundBaseInfoService.getApplyAmountByOrderId(orderId);
        if (ObjectUtil.isNull(orderAmountCalDTO)) {
            log.info("SyncShuZiHuaServiceImpl.syncLanHaiPayAfter fail orderId:{}", orderId);
            throw new BusinessException("未找到订单金额信息");
        }
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjectUtil.isNull(finalFundInfo)) {
            log.info("SyncShuZiHuaServiceImpl.syncLanHaiPayAfter finalFundInfo is null orderId:{}", orderId);
            throw new BusinessException("未找到资方终审信息");
        }
        BankAccountSignEntity repayBankAccountSignEntity = bankAccountSignMapper.selectOne(new MPJLambdaWrapper<BankAccountSignEntity>()
                .eq(BankAccountSignEntity::getOrderId, orderId)
                .in(BankAccountSignEntity::getSignPlate, List.of(SignPlateEnum.LIAN_LIAN.getValue(), SignPlateEnum.YI_BAO.getValue()))
                .eq(BankAccountSignEntity::getSignState, 1)
                .eq(BankAccountSignEntity::getCardType, 1)
                .eq(BankAccountSignEntity::getDeleteFlag, 0)
                .orderByDesc(BankAccountSignEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjectUtil.isNull(repayBankAccountSignEntity)) {
            repayBankAccountSignEntity = bankAccountSignMapper.selectOne(new MPJLambdaWrapper<BankAccountSignEntity>()
                    .eq(BankAccountSignEntity::getOrderId, orderId)
                    .in(BankAccountSignEntity::getSignPlate, SignPlateEnum.LIAN_LIAN.getValue(), SignPlateEnum.YI_BAO.getValue())
                    .eq(BankAccountSignEntity::getSignState, 1)
                    .eq(BankAccountSignEntity::getCardType, 0)
                    .eq(BankAccountSignEntity::getDeleteFlag, 0)
                    .orderByDesc(BankAccountSignEntity::getCreateTime)
                    .last("limit 1")
            );
        }
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        if (ObjectUtil.isNull(orderInfo)) {
            log.info("SyncShuZiHuaServiceImpl.syncLanHaiPayAfter orderInfo is null orderId:{}", orderId);
            throw new BusinessException("未找到订单信息");

        }
        OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectById(orderInfo.getCustomerId());
        Assert.notNull(orderCustomerInfoEntity, () -> new BusinessException("未找到客户信息"));

        Assert.notNull(repayBankAccountSignEntity, () -> new BusinessException("未找到签约完成的银行卡"));
        Result<UserInfoVO> userInfoVOResult = userFeign.searchByUserId(orderInfo.getManagerId());
        Assert.isTrue(Result.isSuccess(userInfoVOResult), () -> new BusinessException("未找到用户信息"));
        ProductRongdanEntity productRongdanEntity = productRongdanMapper.selectById(orderInfo.getRongdanId());
        Assert.notNull(productRongdanEntity, () -> new BusinessException("未找到融担信息"));
        PreFundInfoEntity preFundInfo = preFundInfoMapper.selectOne(new MPJLambdaWrapper<PreFundInfoEntity>()
                .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(PreFundInfoEntity::getPreId, orderInfo.getPreId())
                .eq(PreFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(PreFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        Assert.notNull(preFundInfo, () -> new BusinessException("未找到预审批信息"));
        List<FundRepaymentInfoEntity> fundRepaymentInfoEntities = fundRepaymentInfoMapper.selectList(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                .eq(FundRepaymentInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
        );
        Assert.notEmpty(fundRepaymentInfoEntities, () -> new BusinessException("未找到还款信息"));

        Result<Car300DataVO> infoByVehicle = car300Feign.getInfoByVehicle(fundFinalBaseApplyInfo.getVehicleNumber());
        if (!Result.isSuccess(infoByVehicle)) {
            log.info("SyncShuZiHuaServiceImpl.syncLanHaiPayAfter fail orderId:{}", orderId);
            throw new BusinessException("未找到车型信息");
        }
        Assert.notNull(infoByVehicle.getData(), () -> new BusinessException("未找到车型信息"));
        SyncShuZiHuaDigitalOrderDTO.MainOrder mainOrder = buildSyncDigitalOrder(
                fundFinalBaseApplyInfo,
                orderAmountCalDTO,
                finalFundInfo,
                preFundInfo,
                fundRepaymentInfoEntities,
                orderInfo,
                userInfoVOResult.getData(),
                repayBankAccountSignEntity,
                productRongdanEntity,
                infoByVehicle.getData(),
                orderCustomerInfoEntity
        );
        String jsonString = JSON.toJSONString(mainOrder);
        log.info("SyncShuZiHuaServiceImpl,syncLanHaiPayAfter 云启蓝海贷后同步数字化还款,request,{}", jsonString);
        JSONObject result = digitalizeFeign.buildSyncDigitalOrder(jsonString);
        log.info("SyncShuZiHuaServiceImpl,syncLanHaiPayAfter 云启蓝海贷后同步数字化还款,response,{}", JSON.toJSONString(result));

        if (null != result) {
            Integer code = result.getInteger("code");
            //成功
            if (000000 == code) {
                packageRecord(orderId, 1, jsonString, JSON.toJSONString(result), "");
            } else {
                String failReason = result.getString("msg");
                packageRecord(orderId, 0, jsonString, JSON.toJSONString(result), failReason);
                return false;
            }
        }
        return true;
    }


    /**
     * 构建 SyncShuZiHuaDigitalOrderDTO 对象
     */
    public SyncShuZiHuaDigitalOrderDTO.MainOrder buildSyncDigitalOrder(
            FundFinalBaseDTO fundFinalBaseDTO,
            OrderAmountCalDTO orderAmountCalDTO,
            FinalFundInfoEntity finalFundInfoEntity,
            PreFundInfoEntity preFundInfoEntity,
            List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList,
            OrderInfoEntity orderInfoEntity,
            UserInfoVO userInfoVO,
            BankAccountSignEntity bankAccountSignEntity,
            ProductRongdanEntity rongdanEntity,
            Car300DataVO car300DataVO,
            OrderCustomerInfoEntity orderCustomerInfoEntity) {

        SyncShuZiHuaDigitalOrderDTO.MainOrder mainOrder = new SyncShuZiHuaDigitalOrderDTO.MainOrder();

        // 构建订单基础信息
        SyncShuZiHuaDigitalOrderDTO.OrderBaseInfo baseInfo = new SyncShuZiHuaDigitalOrderDTO.OrderBaseInfo();
        baseInfo.setSpOrderId(orderInfoEntity.getId().toString());
        baseInfo.setSpOrderNumber(orderInfoEntity.getOrderNumber());
        baseInfo.setFirstAmount(orderAmountCalDTO.getSoftReviewAmount().toPlainString());
        baseInfo.setApplyAmount(orderAmountCalDTO.getHopeAmount().toPlainString());
        baseInfo.setApplyPeriod(fundFinalBaseDTO.getTerm());
        baseInfo.setApproveAmount(orderAmountCalDTO.getCustomerConfirmAmount().toPlainString());
        baseInfo.setCreditAmount(orderAmountCalDTO.getFundPreAmount().toPlainString());
        baseInfo.setRiskAmount(orderAmountCalDTO.getRiskAmount().toPlainString());
        baseInfo.setEvaluateAmount(orderAmountCalDTO.getAppraiserAmount().toPlainString());
        baseInfo.setRiskTime(finalFundInfoEntity.getFundCreditTime().atZone(ZoneId.systemDefault()).toEpochSecond());
        if (null != orderInfoEntity.getPaymentTime()) {
            baseInfo.setLoanTime(orderInfoEntity.getPaymentTime().atZone(ZoneId.systemDefault()).toEpochSecond());
        }
        baseInfo.setIsDianxiao(orderInfoEntity.getSourceType());
        if (ObjUtil.isNotNull(orderCustomerInfoEntity.getCustomerLevel())) {
            baseInfo.setUserModelLevel(switch (orderCustomerInfoEntity.getCustomerLevel()) {
                case 1 -> "A";
                case 2 -> "B";
                case 3 -> "C";
                case 4 -> "D";
                default -> null;
            });
        }


        // 构建用户信息
        SyncShuZiHuaDigitalOrderDTO.UserInfo userInfo = new SyncShuZiHuaDigitalOrderDTO.UserInfo();
        userInfo.setRealname(fundFinalBaseDTO.getName());
        userInfo.setSex(fundFinalBaseDTO.getGender());
        userInfo.setIdcard(fundFinalBaseDTO.getIdNumber());
        if (fundFinalBaseDTO.getValidityStartDate() != null) {
            userInfo.setIdcardStart(fundFinalBaseDTO.getValidityStartDate().toInstant()
                    .atZone(java.time.ZoneId.of("GMT+8")).toLocalDateTime().atZone(ZoneId.systemDefault()).toEpochSecond());
        }
        if (fundFinalBaseDTO.getValidityEnd() != null && !fundFinalBaseDTO.getValidityEnd().isEmpty()) {

            //特殊处理,有效期可能存在长期,转义为2099-12-31
            if (fundFinalBaseDTO.getValidityEnd().equals("长期")) {
                userInfo.setIdcardExpire(LocalDateTime.parse("2099-12-31 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault()).toEpochSecond());
            } else {
                userInfo.setIdcardExpire(LocalDateTime.parse(fundFinalBaseDTO.getValidityEnd() + " 00:00:00",
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault()).toEpochSecond());
            }
        }
        userInfo.setMobile(fundFinalBaseDTO.getPhone());
        userInfo.setMaritalStatus(fundFinalBaseDTO.getMaritalStatus());

        // 构建用户还款卡信息
        SyncShuZiHuaDigitalOrderDTO.RepaymentCardInfo repaymentCardInfo = new SyncShuZiHuaDigitalOrderDTO.RepaymentCardInfo();
        repaymentCardInfo.setCardNum(bankAccountSignEntity.getBankCardNumber());
        repaymentCardInfo.setOpenName(String.valueOf(SyncShuZiHuaBankEnum.getBankId(bankAccountSignEntity.getBankName())));
        repaymentCardInfo.setBranchName(bankAccountSignEntity.getBankNameUpdate());// 需要实际数据源
        repaymentCardInfo.setAccountHolder(bankAccountSignEntity.getName());
        repaymentCardInfo.setIdcard(bankAccountSignEntity.getIdCardNum());
        repaymentCardInfo.setMobile(bankAccountSignEntity.getPhone());

        // 构建连连签约信息
        SyncShuZiHuaDigitalOrderDTO.LianLianAgreementInfo lianLianAgreementInfo = new SyncShuZiHuaDigitalOrderDTO.LianLianAgreementInfo();
        lianLianAgreementInfo.setOidPartner(rongdanEntity.getOidPartner());
        lianLianAgreementInfo.setNoOrder(null);
        lianLianAgreementInfo.setCardNo(bankAccountSignEntity.getBankCardNumber());
        lianLianAgreementInfo.setAcctName(bankAccountSignEntity.getName());
        lianLianAgreementInfo.setBindMob(bankAccountSignEntity.getPhone());
        lianLianAgreementInfo.setIdNo(bankAccountSignEntity.getIdCardNum());
        lianLianAgreementInfo.setOpenBank(bankAccountSignEntity.getBankNameUpdate());
        lianLianAgreementInfo.setNoAgree(bankAccountSignEntity.getSignProtocolNo());
        SignPlateEnum signPlate = bankAccountSignEntity.getSignPlate();
        if (null != signPlate) {
            //连连
            if (7 == signPlate.getValue()) {
                lianLianAgreementInfo.setSignChannel(1);
            }
            //易宝
            else if (9 == signPlate.getValue()) {
                lianLianAgreementInfo.setSignChannel(2);
            }
        }

        // 构建抵押物信息
        SyncShuZiHuaDigitalOrderDTO.MortgageInfo mortgageInfo = new SyncShuZiHuaDigitalOrderDTO.MortgageInfo();
        mortgageInfo.setUsageType(mapNatureOfUse(fundFinalBaseDTO.getNatureOfUse()));
        mortgageInfo.setFirstAmount(String.valueOf(orderAmountCalDTO.getSoftReviewAmount()));
        if (fundFinalBaseDTO.getProductionDate() != null && !fundFinalBaseDTO.getProductionDate().isEmpty()) {
            mortgageInfo.setBatchDate(LocalDate.parse(fundFinalBaseDTO.getProductionDate(),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay(ZoneId.systemDefault()).toEpochSecond());
        }
        mortgageInfo.setCarPlate(fundFinalBaseDTO.getVehicleNumber());
        if (fundFinalBaseDTO.getMileage() != null) {
            mortgageInfo.setMileage(String.valueOf(BigDecimal.valueOf(fundFinalBaseDTO.getMileage()).divide(BigDecimal.valueOf(10000))));
        }
        mortgageInfo.setVinCode(fundFinalBaseDTO.getVin());
        mortgageInfo.setEngineNum(fundFinalBaseDTO.getEngineNumber());
        mortgageInfo.setRegisterCertNum(fundFinalBaseDTO.getRegistrationCode());
        mortgageInfo.setModelId(1);
        mortgageInfo.setSeriesId(1);
        mortgageInfo.setBuyPrice(fundFinalBaseDTO.getBuyPrice());
        if (null != fundFinalBaseDTO.getBuyDate()) {
            mortgageInfo.setBuyDate(LocalDate.parse(fundFinalBaseDTO.getBuyDate(),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay(ZoneId.systemDefault()).toEpochSecond());
        }

        if (null != fundFinalBaseDTO.getRegisterDate()) {
            mortgageInfo.setRegDate(LocalDate.parse(fundFinalBaseDTO.getRegisterDate(),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay(ZoneId.systemDefault()).toEpochSecond());
        }

        if (null != fundFinalBaseDTO.getAnnualDate()) {
            mortgageInfo.setVerificationDate(LocalDate.parse(fundFinalBaseDTO.getAnnualDate(),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay(ZoneId.systemDefault()).toEpochSecond());
        }

        if (null != fundFinalBaseDTO.getCompulsoryDate()) {
            mortgageInfo.setMandatoryDate(LocalDate.parse(fundFinalBaseDTO.getCompulsoryDate(),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay(ZoneId.systemDefault()).toEpochSecond());
        }

        if (null != fundFinalBaseDTO.getVehicleInsuranceDate()) {
            mortgageInfo.setDamageDate(LocalDate.parse(fundFinalBaseDTO.getVehicleInsuranceDate(),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay(ZoneId.systemDefault()).toEpochSecond());
        }

        if (null != fundFinalBaseDTO.getThirdInsuranceDate()) {
            mortgageInfo.setThirdDate(LocalDate.parse(fundFinalBaseDTO.getThirdInsuranceDate(),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay(ZoneId.systemDefault()).toEpochSecond());
        }

        mortgageInfo.setTransfersNum(fundFinalBaseDTO.getTransferTimes());
        mortgageInfo.setDisplacement(fundFinalBaseDTO.getDisplacement());
        mortgageInfo.setFuel(String.valueOf(getFuelType(car300DataVO.getFuelType(), car300DataVO.getGreenType())));
        mortgageInfo.setColor(SyncShuZiHuaColorEnum.getColorId(fundFinalBaseDTO.getVehicleColor()));
        //家庭自用=1370
        mortgageInfo.setBuyUse(1370);


        // 构建资方信息
        SyncShuZiHuaDigitalOrderDTO.CapitalInfo capitalInfo = new SyncShuZiHuaDigitalOrderDTO.CapitalInfo();
        if (finalFundInfoEntity.getPreId() != null) {
            capitalInfo.setPreApplyId(finalFundInfoEntity.getPreId().toString());
        }
        capitalInfo.setApplyId(preFundInfoEntity.getCreditReqNo());
        capitalInfo.setCreditLimitId(finalFundInfoEntity.getCreditNo());
        capitalInfo.setLoanInvoiceId(finalFundInfoEntity.getLoanNo());
        capitalInfo.setBorrowerId(finalFundInfoEntity.getFundUserId());
        capitalInfo.setUserId(finalFundInfoEntity.getFundUserId());

        // 构建订单还款信息
        ArrayList<SyncShuZiHuaDigitalOrderDTO.RepaymentInfo> repaymentInfos = new ArrayList<>();
        //根据期数排序
        fundRepaymentInfoEntityList.sort(Comparator.comparing(FundRepaymentInfoEntity::getTerm));
        //计算还款后剩余本金 初始为 还款计划本金总金额
        BigDecimal remainingPrincipal = fundRepaymentInfoEntityList.stream()
                .map(FundRepaymentInfoEntity::getRepaymentPrincipal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        for (FundRepaymentInfoEntity fundRepaymentInfoEntity : fundRepaymentInfoEntityList) {
            SyncShuZiHuaDigitalOrderDTO.RepaymentInfo repaymentInfo = new SyncShuZiHuaDigitalOrderDTO.RepaymentInfo();
            repaymentInfo.setDeadline(fundRepaymentInfoEntity.getTerm());
            //应还本息=本金+利息
            repaymentInfo.setReBx(fundRepaymentInfoEntity.getRepaymentPrincipal().add(fundRepaymentInfoEntity.getRepaymentInterest()).toPlainString());
            repaymentInfo.setReBenjin(fundRepaymentInfoEntity.getRepaymentPrincipal().toPlainString());
            repaymentInfo.setReLixi(fundRepaymentInfoEntity.getRepaymentInterest().toPlainString());
            repaymentInfo.setReFwf(fundRepaymentInfoEntity.getRepaymentGuaraFeeAmount().toPlainString()); // 示例数据
            repaymentInfo.setReTime(fundRepaymentInfoEntity.getRepaymentDate().atStartOfDay(ZoneId.systemDefault()).toEpochSecond());
            //计算还款后剩余本金
            remainingPrincipal = remainingPrincipal.subtract(fundRepaymentInfoEntity.getRepaymentPrincipal());
            repaymentInfo.setReSheng(remainingPrincipal.toPlainString());
            repaymentInfos.add(repaymentInfo);
        }


        // 构建店铺信息
        SyncShuZiHuaDigitalOrderDTO.StoreInfo storeInfo = new SyncShuZiHuaDigitalOrderDTO.StoreInfo();
        storeInfo.setStoreName(orderInfoEntity.getStoreName());

        //构建业务员信息
        SyncShuZiHuaDigitalOrderDTO.SalesmanInfo salesman = new SyncShuZiHuaDigitalOrderDTO.SalesmanInfo();
        salesman.setName(userInfoVO.getName());
        salesman.setIdcard("");
        salesman.setMobile(userInfoVO.getMobile());
        salesman.setJobNumber(userInfoVO.getJobNumber());

        //构建产品码值
        SyncShuZiHuaDigitalOrderDTO.ProductInfo productInfo = new SyncShuZiHuaDigitalOrderDTO.ProductInfo();
        productInfo.setProductId(SyncShuZiHuaProductEnum.getDigitalProductId(orderInfoEntity.getProductId()));

        //构建交易明细
        List<OrderFeeDetailEntity> orderFeeDetailEntityList = orderFeeDetailMapper.selectList(new LambdaQueryWrapper<OrderFeeDetailEntity>()
                .eq(OrderFeeDetailEntity::getOrderId, orderInfoEntity.getId())
                .in(OrderFeeDetailEntity::getExpenseType, Lists.newArrayList(OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE.getCode(),
                        OrderFeeDetailExpandTypeEnum.CURRENT_RETURN_PERFORMANCE.getCode()))
                .eq(OrderFeeDetailEntity::getDeleteFlag, 0));

        List<SyncShuZiHuaDigitalOrderDTO.TransInfo> transInfoList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(orderFeeDetailEntityList)) {
            orderFeeDetailEntityList.forEach(v -> {
                SyncShuZiHuaDigitalOrderDTO.TransInfo transInfo = new SyncShuZiHuaDigitalOrderDTO.TransInfo();
                transInfo.setAmount(v.getAmount());
                if (1 == v.getExpenseType().getCode()) {
                    //转义 云启方车务费code=数字化方32
                    transInfo.setTransType(32);
                }
                if (2 == v.getExpenseType().getCode()) {
                    //转义 云启方现返绩效code=数字化方14
                    transInfo.setTransType(14);
                }
                if (null != v.getTradingTime()) {
                    transInfo.setPayTime(v.getTradingTime().toLocalDate().atStartOfDay(ZoneId.systemDefault()).toEpochSecond());
                }
                transInfo.setSerialNumber(v.getTradingSerialNumber());
                transInfoList.add(transInfo);
            });
        }

        // 组合所有子模块
        mainOrder.setOrderInfo(baseInfo);
        mainOrder.setUserInfo(userInfo);
        mainOrder.setRepayBank(repaymentCardInfo);
        mainOrder.setLianlianSign(lianLianAgreementInfo);
        mainOrder.setPawnInfo(mortgageInfo);
        mainOrder.setOrderCapitalInfo(capitalInfo);
        mainOrder.setRepayList(repaymentInfos);
        mainOrder.setStoreInfo(storeInfo);
        mainOrder.setInviteUserInfo(salesman);
        mainOrder.setProduct(productInfo);
        mainOrder.setTrans(transInfoList);

        return mainOrder;
    }

    /**
     * 将natureOfUse转换为对应的usageType
     */
    private Integer mapNatureOfUse(String natureOfUse) {
        if (natureOfUse != null) {
            return switch (natureOfUse) {
                case "非营运" -> 1;
                case "营运" -> 2;
                default -> 1; // 默认非营运
            };
        }
        return 1; // 默认非营运
    }

    /**
     * 燃料<p>
     * * 必填	码值如下： 1:汽油    2:柴油    3:插电混动    4:纯电动 5. 油电混动 6. 增程式 CNG(天然气)
     *
     * @param fuelType
     * @param greenType
     * @return
     */
    private int getFuelType(String fuelType, String greenType) {
        Integer fuelTypeIntValue = Convert.toInt(fuelType, 0);
        Integer greenTypeIntValue = Convert.toInt(greenType, 0);

        if (greenTypeIntValue == 1) {
            return SyncShuZiHuaFuelTypeEnum.ELECTRIC.getId();
        } else if (greenTypeIntValue == 2 || greenTypeIntValue == 4 || fuelTypeIntValue == 3) {
            return SyncShuZiHuaFuelTypeEnum.PLUG_IN_HYBRID.getId();
        } else if (fuelTypeIntValue == 8 || fuelTypeIntValue == 5) {
            return SyncShuZiHuaFuelTypeEnum.RANGE_EXTENDER.getId();
        } else {
            if (fuelTypeIntValue == 1) {
                return SyncShuZiHuaFuelTypeEnum.DIESEL.getId();
            } else {
                return SyncShuZiHuaFuelTypeEnum.GASOLINE.getId();
            }
        }
    }


    /**
     * 同步蓝海还款信息
     *
     * @param orderId 订单id
     */
    @Override
    public void repayCallBack(Integer orderId) {
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isNull(orderInfo)) {
            log.info("SyncShuZiHuaServiceImpl.repayCallBack orderId:{}, order not exist", orderId);
            throw new BusinessException("订单不存在");
        }

        QueryRepayPlanDTO queryRepayPlanDTO = new QueryRepayPlanDTO();
        queryRepayPlanDTO.setSpOrderId(orderId);
        Result<List<SyncShuZiHuaRepaymentInfoDTO>> result = digitalizeFeign.queryRepayPlan(queryRepayPlanDTO);
        if (!ObjUtil.equals("000000", result.getCode())) {
            throw new BusinessException("查询数字化还款计划失败");
        }
        // 1. 查询所有已存在的还款记录
        Map<String, FundRepaymentInfoEntity> existingRecords = new HashMap<>();
        List<FundRepaymentInfoEntity> existingList = fundRepaymentInfoMapper.selectList(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                .eq(FundRepaymentInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0));

        for (FundRepaymentInfoEntity entity : existingList) {
            String key = entity.getOrderId() + "_" + entity.getTerm() + "_" + entity.getFundId();
            existingRecords.put(key, entity);
        }

        //更新还款计划

        List<SyncShuZiHuaRepaymentInfoDTO> planList = result.getData();

        // 按期数排序
        planList.sort(Comparator.comparingInt(SyncShuZiHuaRepaymentInfoDTO::getDeadline));

        List<FundRepaymentInfoEntity> insertList = new ArrayList<>();
        List<FundRepaymentInfoEntity> updateList = new ArrayList<>();

        //判断是否赎回
        boolean isRedeem = false;
        //判断最后一期是否赎回
        //发生代偿期数
        Integer redeemTerm = null;
        //代偿总额
        BigDecimal redeemAmount = BigDecimal.ZERO;

        //获取最后一期期数
        Integer lastTerm = planList.get(planList.size() - 1).getDeadline();

        for (SyncShuZiHuaRepaymentInfoDTO plan : planList) {
            String key = orderId + "_" + plan.getDeadline() + "_" + FundEnum.LAN_HAI.getValue();
            FundRepaymentInfoEntity entity = existingRecords.get(key);

            if (entity == null) {
                entity = new FundRepaymentInfoEntity();
                updateRepaymentPlan(orderId, plan, entity);
                insertList.add(entity);
            } else {
                updateRepaymentPlan(orderId, plan, entity);
                updateList.add(entity);
            }

            //判断是否赎回
            if (ObjUtil.equals(plan.getHftype(), 2)) {
                if (ObjUtil.equals(plan.getDeadline(), lastTerm)) {
                    isRedeem = true;
                }
                if (ObjUtil.isNotNull(redeemTerm)) {
                    //找出最小期数
                    redeemTerm = Math.min(redeemTerm, plan.getDeadline());
                } else {
                    redeemTerm = plan.getDeadline();
                }
                redeemAmount = redeemAmount.add(plan.getCompensation());
            }
            /*else if (ObjUtil.equals(plan.getHftype(), 3)) {
                //代偿已结清
                // 最后一期代偿已结清，赎回标识
                if (plan.getDeadline().equals(lastTerm)) {
                    isRedeem = true;
                }
            }*/

        }

        // 3. 批量操作
        if (CollUtil.isNotEmpty(insertList)) {
            fundRepaymentInfoService.saveBatch(insertList);
        }

        if (CollUtil.isNotEmpty(updateList)) {
            fundRepaymentInfoService.updateBatchById(updateList);
        }

        if (isRedeem) {
            // 1. 查询所有已存在的还款记录
            Map<String, RepurchaseRepaymentInfoEntity> existingRepurchaseRecords = new HashMap<>();
            List<RepurchaseRepaymentInfoEntity> existingRespurchaseList = repurchaseRepaymentInfoService.list(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                    .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                    .eq(RepurchaseRepaymentInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                    .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0));

            for (RepurchaseRepaymentInfoEntity entity : existingRespurchaseList) {
                String key = entity.getOrderId() + "_" + entity.getTerm() + "_" + entity.getFundId();
                existingRepurchaseRecords.put(key, entity);
            }

            //更新还款计划
            FundRepurchaseResultEntity fundRepurchaseResult = fundRepurchaseResultMapper.selectOne(new LambdaQueryWrapper<FundRepurchaseResultEntity>()
                    .eq(FundRepurchaseResultEntity::getOrderId, orderId)
                    .eq(FundRepurchaseResultEntity::getRepurchaseResult, 1)
                    .eq(FundRepurchaseResultEntity::getDeleteFlag, 0)
                    .orderByDesc(FundRepurchaseResultEntity::getCreateTime)
                    .last("limit 1")
            );

            List<RepurchaseRepaymentInfoEntity> insertRespurchaseList = new ArrayList<>();
            List<RepurchaseRepaymentInfoEntity> updateRespurchaseList = new ArrayList<>();


            for (SyncShuZiHuaRepaymentInfoDTO plan : planList) {
                String key = orderId + "_" + plan.getDeadline() + "_" + FundEnum.LAN_HAI.getValue();
                RepurchaseRepaymentInfoEntity entity = existingRepurchaseRecords.get(key);

                if (entity == null) {
                    entity = new RepurchaseRepaymentInfoEntity();
                    updateRepurchaseRepaymentPlan(orderId, plan, entity);
                    insertRespurchaseList.add(entity);
                } else {
                    updateRepurchaseRepaymentPlan(orderId, plan, entity);
                    updateRespurchaseList.add(entity);
                }

            }


            //  批量操作
            if (CollUtil.isNotEmpty(insertRespurchaseList)) {
                repurchaseRepaymentInfoService.saveBatch(insertRespurchaseList);
            }

            if (CollUtil.isNotEmpty(updateRespurchaseList)) {
                repurchaseRepaymentInfoService.updateBatchById(updateRespurchaseList);
            }

            //判断是否为整笔赎回 不在更新为未赎回状态
            // 整笔赎回失效历史赎回记录和赎回还款计划 重新生成

            if (
                    (ObjUtil.isNull(fundRepurchaseResult)) ||
                            ((ObjUtil.isNotNull(fundRepurchaseResult) && !ObjUtil.equals(fundRepurchaseResult.getEventStartTerm(), redeemTerm)))

            ) {
                if (ObjUtil.isNotNull(fundRepurchaseResult)) {
                    FundRepurchaseResultEntity repurchaseResult = new FundRepurchaseResultEntity();
                    repurchaseResult.setDeleteFlag(1);
                    fundRepurchaseResultMapper.update(repurchaseResult,
                            new LambdaUpdateWrapper<FundRepurchaseResultEntity>()
                                    .eq(FundRepurchaseResultEntity::getId, fundRepurchaseResult.getId())
                                    .eq(FundRepurchaseResultEntity::getDeleteFlag, 0));
                }

                FundRepurchaseResultEntity fundRepurchaseResultInsert = new FundRepurchaseResultEntity();
                fundRepurchaseResultInsert.setOrderId(orderId);
                fundRepurchaseResultInsert.setFundId(FundEnum.LAN_HAI.getValue());
                fundRepurchaseResultInsert.setRepurchaseResult(1);
                fundRepurchaseResultInsert.setEventStartTerm(redeemTerm);
                fundRepurchaseResultInsert.setRepurchaseDate(LocalDate.now().toString());
                fundRepurchaseResultInsert.setPreRepayAmt(redeemAmount);
                fundRepurchaseResultMapper.insert(fundRepurchaseResultInsert);
            }

            orderInfoMapper.update(new OrderInfoEntity().setIsRepurchase(1),
                    new LambdaUpdateWrapper<OrderInfoEntity>()
                            .eq(OrderInfoEntity::getIsRepurchase, 0)
                            .eq(OrderInfoEntity::getId, orderId));

        }

        //更新订单还款状态
        try {
            OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
            orderApproveFundPlanStatusDTO.setOrderId(orderId);
            orderFeign.updateFundPlanStatus(orderApproveFundPlanStatusDTO);
        } catch (Exception e) {
            log.error("SyncShuZiHuaServiceImpl.repayCallBack.orderFeign.updateFundPlanStatus orderId:{}", orderId);
        }
        try {
            orderFeign.updateOrderFundRepayment(orderId);
        } catch (Exception e) {
            log.error("SyncShuZiHuaServiceImpl.repayCallBack.orderFeign.updateOrderFundRepayment orderId:{}", orderId);
        }


        log.info("SyncShuZiHuaServiceImpl.repayCallBack orderId:{}, completed, inserts:{}, updates:{}", orderId, insertList.size(), updateList.size());


    }

    /**
     * 更新还款计划
     */
    public void updateRepaymentPlan(Integer orderId, SyncShuZiHuaRepaymentInfoDTO plan, FundRepaymentInfoEntity repaymentInfo) {
        log.info("SyncShuZiHuaServiceImpl.updateRepaymentPlan orderId:{}, plan:{} ,repaymentInfo:{}", orderId, JSONUtil.toJsonStr(plan), JSONUtil.toJsonStr(repaymentInfo));
        // 设置订单ID
        repaymentInfo.setOrderId(orderId);
        repaymentInfo.setFundId(FundEnum.LAN_HAI.getValue());

        // 获取当前期次
        Integer term = plan.getDeadline();
        // 记录日志信息

        log.info("SyncShuZiHuaServiceImpl.updateRepaymentPlan orderId:{}, term:{} plan:{}", orderId, term, plan);
        // 获取还款状态
        Integer repayStatus = plan.getStatus();
        FundRepayStatusEnum fundRepayStatusEnum;

        // 根据还款状态设置资金还款状态枚举
        fundRepayStatusEnum = switch (repayStatus) {
            case 0 -> FundRepayStatusEnum.NONE;
            case 1 -> FundRepayStatusEnum.PART_RETURN;
            case 4 -> FundRepayStatusEnum.SETTLED;
            default -> FundRepayStatusEnum.NONE;
        };
        // 解析应还款日期字符串为LocalDate对象
        Long reTime = plan.getReTime();

        if (null == reTime) {
            throw new BusinessException("应还时间不能为空");
        }


        LocalDate repaymentDate = LocalDateTime.ofInstant(Instant.ofEpochSecond(reTime), ZoneId.systemDefault())
                .toLocalDate();

        Long seTime = plan.getSeTime();
        LocalDate actuallyDate = null;
        if (ObjUtil.isNotNull(seTime) && seTime > 0) {
            actuallyDate = LocalDateTime.ofInstant(Instant.ofEpochSecond(seTime), ZoneId.systemDefault())
                    .toLocalDate();
        }


        //判断是否逾期
        boolean isOverdue = false;
        LocalDate now = LocalDate.now();
        if (now.isAfter(repaymentDate)) {
            isOverdue = true;
            if (!ObjUtil.equals(fundRepayStatusEnum, FundRepayStatusEnum.SETTLED)) {
                fundRepayStatusEnum = FundRepayStatusEnum.OVERDUE;
            }
        }
        // 设置还款期次
        repaymentInfo.setTerm(term);

        repaymentInfo.setRepaymentDate(repaymentDate);
        repaymentInfo.setActuallyDate(actuallyDate);
        repaymentInfo.setIsOverdue(isOverdue ? 1 : 0);
        // 设置还款状态
        repaymentInfo.setRepaymentStatus(fundRepayStatusEnum);

        // 设置应还本金
        repaymentInfo.setRepaymentPrincipal(plan.getReBenjin());
        // 设置应还利息
        repaymentInfo.setRepaymentInterest(plan.getReLixi());
        // 设置应还担保费金额
        repaymentInfo.setRepaymentGuaraFeeAmount(plan.getReFwf());
        // 设置应还罚息
        repaymentInfo.setRepaymentPenaltyInterest(plan.getZhina());
        // 设置应还复利
        repaymentInfo.setRepaymentPsCommOdAmount(BigDecimal.ZERO);

        // 计算应还总额
        BigDecimal repaymentAmountTotal = plan.getReBenjin().add(plan.getReLixi()).add(plan.getZhina());
        // 设置应还总金额
        repaymentInfo.setRepaymentAmountTotal(repaymentAmountTotal);


        // 初始状态违约金为0
        repaymentInfo.setActuallyPenalty(BigDecimal.ZERO);

        // 计算并设置实还本金、实还利息、实还罚息、实还复利
        repaymentInfo.setActuallyPrincipal(plan.getSeBenjin());
        repaymentInfo.setActuallyInterest(plan.getSeLixi());
        repaymentInfo.setActuallyPenaltyInterest(plan.getZhina());
        repaymentInfo.setActuallyGuaraFeeAmount(plan.getSeFwf());
        repaymentInfo.setActuallyCommOdAmount(BigDecimal.ZERO);

        //优惠费用、利息
        repaymentInfo.setDiscountInterest(BigDecimal.ZERO);
        repaymentInfo.setDiscountFee(BigDecimal.ZERO);
        repaymentInfo.setLeftDiscountFee(BigDecimal.ZERO);
        repaymentInfo.setLeftDiscountInterest(BigDecimal.ZERO);
        repaymentInfo.setActuallyAmountTotal(plan.getSeBenjin().add(plan.getSeLixi()).add(plan.getZhina()));


    }


    /**
     * 批量更新赎回还款计划
     */

    public void updateRepurchaseRepaymentPlan(Integer orderId, SyncShuZiHuaRepaymentInfoDTO plan, RepurchaseRepaymentInfoEntity repaymentInfo) {
        log.info("SyncShuZiHuaServiceImpl.updateRepurchaseRepaymentPlan orderId:{}, plan:{} ,repaymentInfo:{}", orderId, JSONUtil.toJsonStr(plan), JSONUtil.toJsonStr(repaymentInfo));
        // 设置订单ID
        repaymentInfo.setOrderId(orderId);
        repaymentInfo.setFundId(FundEnum.LAN_HAI.getValue());

        // 获取当前期次
        Integer term = plan.getDeadline();
        // 记录日志信息

        log.info("SyncShuZiHuaServiceImpl.updateRepurchaseRepaymentPlan orderId:{}, term:{} plan:{}", orderId, term, plan);
        // 获取还款状态
        Integer repayStatus = plan.getStatus();

        FundRepayStatusEnum fundRepayStatusEnum = FundRepayStatusEnum.NONE;

        // 根据还款状态设置资金还款状态枚举
        fundRepayStatusEnum = switch (repayStatus) {
            case 0 -> FundRepayStatusEnum.NONE;
            case 1 -> FundRepayStatusEnum.PART_RETURN;
            case 4 -> FundRepayStatusEnum.SETTLED;
            default -> FundRepayStatusEnum.NONE;
        };
        // 解析应还款日期字符串为LocalDate对象
        Long reTime = plan.getReTime();

        if (null == reTime) {
            throw new BusinessException("应还时间不能为空");
        }

        LocalDate repaymentDate = LocalDateTime.ofInstant(Instant.ofEpochSecond(reTime), ZoneId.systemDefault())
                .toLocalDate();

        Long seTime = plan.getSeTime();
        LocalDate actuallyDate = null;
        if (ObjUtil.isNotNull(seTime) && seTime > 0) {
            actuallyDate = LocalDateTime.ofInstant(Instant.ofEpochSecond(seTime), ZoneId.systemDefault())
                    .toLocalDate();
        }

        //判断是否逾期
        boolean isOverdue = false;
        LocalDate now = LocalDate.now();
        if (ObjUtil.equals(fundRepayStatusEnum, FundRepayStatusEnum.PART_RETURN) && now.isAfter(repaymentDate)) {
            isOverdue = true;
            fundRepayStatusEnum = FundRepayStatusEnum.OVERDUE;
        }
        // 设置还款期次
        repaymentInfo.setTerm(term);

        repaymentInfo.setRepaymentDate(repaymentDate);
        repaymentInfo.setIsOverdue(isOverdue ? 1 : 0);
        // 设置还款状态
        repaymentInfo.setRepaymentStatus(fundRepayStatusEnum);

        // 设置应还本金
        repaymentInfo.setRepaymentPrincipal(plan.getReBenjin());
        // 设置应还利息
        repaymentInfo.setRepaymentInterest(plan.getReLixi());
        // 设置应还担保费金额
        repaymentInfo.setRepaymentGuaraFeeAmount(plan.getReFwf());
        // 设置应还罚息
        repaymentInfo.setRepaymentPenaltyInterest(plan.getZhina());
        // 计算应还总额
        BigDecimal repaymentAmountTotal = plan.getReBenjin().add(plan.getReLixi()).add(plan.getZhina());
        // 设置应还总金额
        repaymentInfo.setRepaymentAmountTotal(repaymentAmountTotal);


        // 初始状态违约金为0
        repaymentInfo.setActuallyPenalty(BigDecimal.ZERO);

        // 计算并设置实还本金、实还利息、实还罚息、实还复利
        repaymentInfo.setActuallyDate(actuallyDate);
        repaymentInfo.setActuallyPrincipal(plan.getSeBenjin());
        repaymentInfo.setActuallyInterest(plan.getSeLixi());
        repaymentInfo.setActuallyPenaltyInterest(plan.getZhina());
        repaymentInfo.setActuallyGuaraFeeAmount(plan.getSeFwf());
        repaymentInfo.setActuallyAmountTotal(plan.getSeBenjin().add(plan.getSeLixi()).add(plan.getZhina()));

    }

    @Override
    public List<SyncCollectPledgeDataVO> collectPledgeData(String monthDayStart, String monthDayEnd) {
        log.info("SyncShuZiHuaServiceImpl.collectPledgeData startTime:{}, endTime:{}", monthDayStart, monthDayEnd);

        //获取月份和日期

        //查询放款且在参数时间范围内的订单数据(月份+日期在传入参数月份+日期中即可)
        List<SyncCollectPledgeDataDTO> syncCollectPledgeDataDTOList = orderInfoMapper.selectJoinList(SyncCollectPledgeDataDTO.class, new MPJLambdaWrapper<OrderInfoEntity>()
                .selectAs(OrderInfoEntity::getId, SyncCollectPledgeDataDTO::getOrderId)
                .selectAs(OrderAmountEntity::getCustomerConfirmAmount, SyncCollectPledgeDataDTO::getApproveAmount)
                .selectAs(OrderCustomerInfoEntity::getName, SyncCollectPledgeDataDTO::getRealName)
                .selectAs(OrderCustomerInfoEntity::getIdNumber, SyncCollectPledgeDataDTO::getIdCard)
                .selectAs(FinalFundInfoEntity::getLoanNo, SyncCollectPledgeDataDTO::getLoanInvoiceId)
                .selectAs(OrderVehicleInfoEntity::getRegistrationCode, SyncCollectPledgeDataDTO::getRegisterCertNum)
                .selectAs(OrderVehicleInfoEntity::getVin, SyncCollectPledgeDataDTO::getVin)
                .selectAs(OrderVehicleInfoEntity::getVehicleNumber, SyncCollectPledgeDataDTO::getVehicleNumber)
                .selectAs(OrderVehicleInfoEntity::getMileage, SyncCollectPledgeDataDTO::getMileage)
                .selectAs(OrderVehicleInfoEntity::getRegisterDate, SyncCollectPledgeDataDTO::getRegisterDate)
                .selectAs(ParamsSnapshotEntity::getValue, SyncCollectPledgeDataDTO::getContactNumber)
                .leftJoin(OrderAmountEntity.class, on ->
                        on.eq(OrderInfoEntity::getId, OrderAmountEntity::getOrderId)
                                .eq(OrderInfoEntity::getDeleteFlag, 0)
                )
                .leftJoin(OrderVehicleInfoEntity.class, on ->
                        on.eq(OrderVehicleInfoEntity::getOrderId, OrderInfoEntity::getId)
                                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                )
                .leftJoin(OrderCustomerInfoEntity.class, on ->
                        on.eq(OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId)
                                .eq(OrderCustomerInfoEntity::getDeleteFlag, 0)
                )
                .leftJoin(FinalFundInfoEntity.class, on ->
                        on.eq(FinalFundInfoEntity::getOrderId, OrderInfoEntity::getId)
                                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                )
                .leftJoin(ParamsSnapshotEntity.class, on ->
                        on.eq(ParamsSnapshotEntity::getLinkId, OrderInfoEntity::getId)
                                .eq(ParamsSnapshotEntity::getCode, "MORTGAGE_CONTRACT_NUMBER")
                                .eq(ParamsSnapshotEntity::getDeleteFlag, 0))
                .apply("TO_CHAR(t.payment_time, 'MM-DD') >= {0}", monthDayStart)
                .apply("TO_CHAR(t.payment_time, 'MM-DD') <= {0}", monthDayEnd)
                .eq(OrderInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                .ge(OrderInfoEntity::getState, 5000)
                .eq(OrderInfoEntity::getDeleteFlag, 0))
                ;

        if (CollUtil.isEmpty(syncCollectPledgeDataDTOList)) {
            return Lists.newArrayList();
        }

        syncCollectPledgeDataDTOList.forEach(v -> {

            //查询车300最新额度
            try {
                Result<Car300PriceVO> car300PriceVOResult = car300Feign.vehicleAppraisalPrice(
                        new Car300AppraisalDTO()
                                .setCarNo(v.getVehicleNumber())
                                .setVin(v.getVin())
                                .setMile(v.getMileage())
                                .setRegDate(v.getRegisterDate())
                );
                if (Result.isSuccess(car300PriceVOResult)) {
                    //取整舍小数
                    // 取整（四舍五入）
                    BigDecimal evaluationAmount = car300PriceVOResult.getData().getEvaluationAmount();
                    if (evaluationAmount != null) {
                        v.setFirstAmount(evaluationAmount.setScale(0, RoundingMode.HALF_UP));
                    } else {
                        v.setFirstAmount(BigDecimal.ZERO);
                    }

                }
            } catch (Exception e) {
                log.info("SyncShuZiHuaServiceImpl.collectPledgeData car300Feign.vehicleAppraisalPrice error:{}", e.getMessage(),e);
            }

        });
        return syncShuZiHuaConverter.dto2VOList(syncCollectPledgeDataDTOList);
    }

    public static void main(String[] args) {
        // 获取2025年8月1日00:00:00的时间戳（毫秒）
        LocalDateTime specificDateTime = LocalDateTime.of(2025, 8, 1, 0, 0, 0);
        long specificTimestamp = specificDateTime.atZone(ZoneId.systemDefault())
                .toInstant().toEpochMilli();

        // 获取今日00:00:00的时间戳（毫秒）
        LocalDateTime todayStart = LocalDateTime.now()
                .with(ChronoField.HOUR_OF_DAY, 0)
                .with(ChronoField.MINUTE_OF_HOUR, 0)
                .with(ChronoField.SECOND_OF_MINUTE, 0)
                .with(ChronoField.MILLI_OF_SECOND, 0);
        long todayStartTimestamp = todayStart.atZone(ZoneId.systemDefault())
                .toInstant().toEpochMilli();

        System.out.println("2025年8月1日00:00:00的时间戳: " + specificTimestamp);
        System.out.println("今日00:00:00的时间戳: " + todayStartTimestamp);
    }
}
