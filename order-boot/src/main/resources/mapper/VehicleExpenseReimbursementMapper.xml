<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.longhuan.order.mapper.VehicleExpenseReimbursementMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.longhuan.order.pojo.entity.VehicleExpenseReimbursementEntity">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="order_number" property="orderNumber"/>
        <result column="applicant_id" property="applicantId"/>
        <result column="applicant_name" property="applicantName"/>
        <result column="apply_time" property="applyTime"/>
        <result column="reimbursement_status" property="reimbursementStatus"/>
        <result column="handle_type" property="handleType"/>
        <result column="vehicle_admin_fee" property="vehicleAdminFee"/>
        <result column="agency_fee" property="agencyFee"/>
        <result column="total_fee" property="totalFee"/>
        <result column="is_remote" property="isRemote"/>
        <result column="approver_id" property="approverId"/>
        <result column="approver_name" property="approverName"/>
        <result column="approve_time" property="approveTime"/>
        <result column="approve_status" property="approveStatus"/>
        <result column="approve_comment" property="approveComment"/>
        <result column="payment_time" property="paymentTime"/>
        <result column="payment_serial_number" property="paymentSerialNumber"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="delete_flag" property="deleteFlag"/>
    </resultMap>

    <!-- 分页查询车务费用报销记录 -->
    <select id="pageVehicleExpenseReimbursement" resultType="com.longhuan.order.pojo.vo.VehicleExpenseReimbursementVO">
        SELECT
            t.id,
            t.order_id,
            t.order_number,
            t.applicant_id,
            t.applicant_name,
            t.apply_time,
            t.reimbursement_status,
            CASE t.reimbursement_status
                WHEN 0 THEN '待付款'
                WHEN 1 THEN '已报销'
                WHEN 2 THEN '付款失败'
                ELSE ''
            END AS reimbursement_status_name,
            t.handle_type,
            CASE t.handle_type
                WHEN 0 THEN '自行办抵'
                WHEN 1 THEN '三方办抵'
                ELSE ''
            END AS handle_type_name,
            t.vehicle_admin_fee,
            t.agency_fee,
            t.total_fee,
            t.is_remote,
            CASE t.is_remote
                WHEN 0 THEN '否'
                WHEN 1 THEN '是'
                ELSE ''
            END AS is_remote_name,
            t.approver_id,
            t.approver_name,
            t.approve_time,
            t.approve_status,
            CASE t.approve_status
                WHEN 0 THEN '待审批'
                WHEN 1 THEN '已通过'
                WHEN 2 THEN '已拒绝'
                ELSE ''
            END AS approve_status_name,
            t.approve_comment,
            t.payment_time,
            t.payment_serial_number,
            t.remark,
            t.create_time
        FROM
            lh_vehicle_expense_reimbursement t
        WHERE
            t.delete_flag = 0
        <if test="query.orderNumber != null and query.orderNumber != ''">
            AND t.order_number LIKE CONCAT('%', #{query.orderNumber}, '%')
        </if>
        <if test="query.applicantName != null and query.applicantName != ''">
            AND t.applicant_name LIKE CONCAT('%', #{query.applicantName}, '%')
        </if>
        <if test="query.reimbursementStatus != null">
            AND t.reimbursement_status = #{query.reimbursementStatus}
        </if>
        <if test="query.handleType != null">
            AND t.handle_type = #{query.handleType}
        </if>
        <if test="query.isRemote != null">
            AND t.is_remote = #{query.isRemote}
        </if>
        <if test="query.approveStatus != null">
            AND t.approve_status = #{query.approveStatus}
        </if>
        ORDER BY t.create_time DESC
    </select>

</mapper>
