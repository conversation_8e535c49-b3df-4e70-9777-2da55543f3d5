package com.longhuan.order.statemachine.action;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.common.core.enums.RoleEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.kingdee.pojo.KingdeeOrderFlowDTO;
import com.longhuan.order.kingdee.service.KingdeeService;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.mapper.OrderNodeRecordMapper;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.entity.OrderNodeRecordEntity;
import com.longhuan.order.service.RiskAccidentCarService;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.user.pojo.dto.DeptUsersByRoleIdsDTO;
import com.longhuan.user.pojo.dto.TodoInfoMessageDTO;
import com.longhuan.user.pojo.vo.UserInfoVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 订单节点变更动作
 */
@Slf4j
//@Component
public class OrderNodeAction {
    protected final OrderInfoMapper orderInfoMapper;
    protected final OrderNodeRecordMapper orderNodeRecordMapper;
    @Resource
    protected RiskAccidentCarService riskAccidentCarService;
    @Resource
    private KingdeeService kingdeeService;
    @Resource
    private UserFeign userFeign;

    public OrderNodeAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper) {
        this.orderInfoMapper = orderInfoMapper;
        this.orderNodeRecordMapper = orderNodeRecordMapper;
    }

    /**
     * 修改订单表状态
     *
     * @param orderId
     * @param nextNode
     */
    protected void saveOrderNode(Integer orderId, Integer nextNode) {
        OrderInfoEntity updateEntity = new OrderInfoEntity();

        updateEntity.setId(orderId);
        updateEntity.setCurrentNode(nextNode);
        updateEntity.setLastNodeFinishTime(LocalDateTime.now());
        log.info("update order {} node {}", orderId, nextNode);
        orderInfoMapper.updateById(updateEntity);

        try {
//            // 同步系统待办信息
//            TodoMessageEnums type = null;
//            if (Objects.equals(nextNode, States.QUALITY_INSPECTION.getNode())
//                    || (Objects.equals(nextNode, States.MANAGER_INTERVIEW.getNode()))
//                    || Objects.equals(nextNode, States.MORTGAGE_PENDING.getNode())) {
//                type = TodoMessageEnums.CUSTOMER_SERVICE_MESSAGE;
//            } else if (Objects.equals(nextNode, States.RISK_FIRST_APPROVE.getNode())
//                    || Objects.equals(nextNode, States.RISK_FINAL_APPROVE.getNode())
//                    || Objects.equals(nextNode, States.PAYMENT_CONTRACT_APPROVAL.getNode())) {
//                type = TodoMessageEnums.RISK_MESSAGE;
//            } else {
//                return;
//            }
//            States node = getNode(nextNode);
//            List<TodoInfoMessageDTO.TodoUser> todoUserList = this.getTodoUserList(orderId, nextNode);
//            if (CollUtil.isNotEmpty(todoUserList)) {
//                OrderInfoEntity orderInfoEntity=orderInfoMapper.selectById(orderId);
//                TodoInfoMessageDTO todoInfoMessageDTO = new TodoInfoMessageDTO()
//                        .setOrderId(orderId)
//                        .setOrderNumber(orderInfoEntity.getOrderNumber())
//                        .setSourceType(0)
//                        .setRemark("订单：" + orderInfoEntity.getOrderNumber() + "待处理")
//                        //待处理
//                        .setState(0)
//                        .setTitle(orderInfoEntity.getCustomerName() + " " + orderInfoEntity.getVehicleNumber() + " " + orderInfoEntity.getCustomerPhone() + " " + States.getDescriptionByCode(orderInfoEntity.getCurrentNode()))
//                        .setNode(TodoInfoEnums.getByNode(nextNode))
//                        .setTodoUserList(todoUserList);
//                log.info("todoMessage data push {}", JSONUtil.toJsonStr(todoInfoMessageDTO));
//                userFeign.dealMessage(todoInfoMessageDTO);
//            }


        } catch (Exception e) {

            log.error("todoMessage data push error ", e);
        }


    }

    /**
     * 获取todo用户列表
     *
     * @param orderId 订单id
     * @return {@link List }<{@link TodoInfoMessageDTO.TodoUser }>
     */
    private List<TodoInfoMessageDTO.TodoUser> getTodoUserList(Integer orderId, Integer nextNode) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getDeptId, OrderInfoEntity::getRiskUserId, OrderInfoEntity::getTeamId)
                .eq(OrderInfoEntity::getId, orderId));

        if (Objects.equals(nextNode, States.QUALITY_INSPECTION.getNode())
                || (Objects.equals(nextNode, States.PAYMENT_APPLY_INFORMATION.getNode()))
                || Objects.equals(nextNode, States.MORTGAGE_PENDING.getNode())) {
            //获取客服用户信息

            List<Integer> roleIds = Arrays.asList(RoleEnum.CUSTOMER_SERVICE_SPECIALIST.getId(), RoleEnum.CUSTOMER_SERVICE_SUPERVISOR.getId());
            List<Integer> deptIds = Arrays.asList(orderInfoEntity.getDeptId(), orderInfoEntity.getTeamId());

            Result<List<UserInfoVO>> deptUsersByRoleIds = userFeign.getDeptUsersByRoleIds(new DeptUsersByRoleIdsDTO()
                    .setRoleIds(roleIds).setDeptIds(deptIds));
            if (Result.isSuccess(deptUsersByRoleIds)) {
                List<UserInfoVO> userInfoVOList = deptUsersByRoleIds.getData();
                return userInfoVOList.stream().map(userInfoVO -> new TodoInfoMessageDTO.TodoUser()
                        .setName(userInfoVO.getName())
                        .setPhoneNumber(userInfoVO.getMobile())
                        .setJobNumber(userInfoVO.getJobNumber())
                ).toList();
            }

        } else if (Objects.equals(nextNode, States.RISK_FIRST_APPROVE.getNode())) {
            Integer riskUserId = orderInfoEntity.getRiskUserId();
            Result<UserInfoVO> userInfoVO = userFeign.getUserInfoById(riskUserId);
            if (Result.isSuccess(userInfoVO) && userInfoVO.getData() != null) {
                return List.of(new TodoInfoMessageDTO.TodoUser()
                        .setName(userInfoVO.getData().getName())
                        .setPhoneNumber(userInfoVO.getData().getMobile())
                        .setJobNumber(userInfoVO.getData().getJobNumber()));
            }
        } else if (Objects.equals(nextNode, States.OVERALL_REVIEW.getNode())) {
            //获取风控用户信息
            Result<List<UserInfoVO>> userIdByRoleIds = userFeign.getUserInfoByRoleId(RoleEnum.RISK_VEHICLE_ASSESSOR.getId());
            if (Result.isSuccess(userIdByRoleIds) && userIdByRoleIds.getData() != null) {
                return userIdByRoleIds.getData().stream().map(userInfoVO -> new TodoInfoMessageDTO.TodoUser()
                        .setName(userInfoVO.getName())
                        .setPhoneNumber(userInfoVO.getMobile())
                        .setJobNumber(userInfoVO.getJobNumber())
                ).toList();
            }
        } else if (Objects.equals(nextNode, States.RISK_FINAL_APPROVE.getNode())) {
            //获取风控用户信息
            Result<List<UserInfoVO>> userIdByRoleIds = userFeign.getUserInfoByRoleId(RoleEnum.RISK_AMOUNT_APPROVE_FINAL.getId());
            if (Result.isSuccess(userIdByRoleIds) && userIdByRoleIds.getData() != null) {
                return userIdByRoleIds.getData().stream().map(userInfoVO -> new TodoInfoMessageDTO.TodoUser()
                        .setName(userInfoVO.getName())
                        .setPhoneNumber(userInfoVO.getMobile())
                        .setJobNumber(userInfoVO.getJobNumber())
                ).toList();
            }
        } else if (Objects.equals(nextNode, States.PAYMENT_CONTRACT_APPROVAL.getNode())) {
            //获取风控用户信息
            Result<List<UserInfoVO>> userIdByRoleIds = userFeign.getUserInfoByRoleId(RoleEnum.RISK_CONTRACT_APPROVE.getId());
            if (Result.isSuccess(userIdByRoleIds) && userIdByRoleIds.getData() != null) {
                return userIdByRoleIds.getData().stream().map(userInfoVO -> new TodoInfoMessageDTO.TodoUser()
                        .setName(userInfoVO.getName())
                        .setPhoneNumber(userInfoVO.getMobile())
                        .setJobNumber(userInfoVO.getJobNumber())
                ).toList();
            }
        }
        List<TodoInfoMessageDTO.TodoUser> todoUserList = null;
        return todoUserList;
    }

    /**
     * 保存状态变更记录
     *
     * @param orderId
     * @param currentNode
     * @param nextNode
     * @param messageHeaders
     */
    protected void saveOrderNodeRecord(Events event, Integer orderId, Integer currentNode, Integer nextNode,
                                       MessageHeaders messageHeaders) {

        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        String remark = messageHeaders.get(StateConstants.REMARK, String.class);
        String remarkExternal = messageHeaders.get(StateConstants.REMARK_EXTERNAL, String.class);
        String rejectReason = messageHeaders.get(StateConstants.REJECT_REASON, String.class);
        Integer rejectReasonSecond = messageHeaders.get(StateConstants.REJECT_REASON_SECOND, Integer.class);

        saveOrderNodeRecord(event, orderId, currentNode, nextNode, remark, remarkExternal, rejectReason, userId, rejectReasonSecond);
        // 风控初审、风控终审、总评复核审核：凡是命中“事故车”和“车辆残值不足”拒绝的客户，自动转入事故车数据库
        if (Objects.equals(currentNode, States.RISK_FIRST_APPROVE.getNode()) || Objects.equals(currentNode, States.RISK_FINAL_APPROVE.getNode()) || Objects.equals(currentNode, States.OVERALL_REVIEW.getNode())) {
            // 流程终止 并且为“事故车”和“车辆残值不足”
            if (Objects.equals(nextNode, States.PROCESS_TERMINAL.getNode()) && (Objects.equals(rejectReason, "4110") || Objects.equals(rejectReason, "418"))) {
                log.info("saveOrderNodeRecord.insertByOrderId orderId={},remarkExternal={}", orderId, rejectReason);
                try {
                    riskAccidentCarService.insertByOrderId(orderId, rejectReason);
                } catch (Exception e) {
                    log.error("saveOrderNodeRecord.insertByOrderId error={}", e.getMessage());
                }
            }
        }
        try {
            log.warn("orderId {} push data to kingdee ", orderId);
            kingdeeService.submitOrderFlow(new KingdeeOrderFlowDTO()
                    .setOrderId(orderId)
                    .setFromNode(currentNode)
                    .setToNode(nextNode).setState(event.getCode())
                    .setRemark(remarkExternal)
            );
            //4. 同步系统待办信息
//            if (Objects.equals(currentNode, States.QUALITY_INSPECTION.getNode())
//                    || Objects.equals(currentNode, States.RISK_FIRST_APPROVE.getNode())
//                    || Objects.equals(currentNode, States.RISK_FINAL_APPROVE.getNode())
//                    || Objects.equals(currentNode, States.PAYMENT_CONTRACT_APPROVAL.getNode())
//                    || (Objects.equals(currentNode, States.MANAGER_INTERVIEW.getNode()))
//                    || Objects.equals(currentNode, States.MORTGAGE_PENDING.getNode())) {
//
//                userFeign.dealMessage(new TodoInfoMessageDTO()
//                        .setTitle(States.getDescriptionByCode(currentNode))
//                        //已处理
//                        .setState(2)
//                        .setOrderId(orderId)
//                        .setUserId(userId)
//                );
//            }
        } catch (Exception e) {
            log.warn("Kingdee data push error ", e);
        }
    }


    /**
     * 保存订单节点记录
     *
     * @param event          事件
     * @param orderId        订单 ID
     * @param currentNode    当前节点
     * @param nextNode       下一个节点
     * @param remark         备注
     * @param remarkExternal 备注外部
     * @param rejectReason   拒绝原因
     * @param userId         用户 ID
     */
    protected void saveOrderNodeRecord(Events event, Integer orderId, Integer currentNode, Integer nextNode,
                                       String remark, String remarkExternal, String rejectReason, Integer userId,
                                       Integer rejectReasonSecond) {
        // 2.1 查询最近次记录
        Integer lastNode = null;
        LocalDateTime lastTime = null;
        OrderNodeRecordEntity orderNodeRecordEntity =
                orderNodeRecordMapper.selectOne(new LambdaQueryWrapper<OrderNodeRecordEntity>()
                        .eq(OrderNodeRecordEntity::getOrderId, orderId)
                        .orderByDesc(OrderNodeRecordEntity::getCreateTime)
                        .last("LIMIT 1")
                );

        if (orderNodeRecordEntity != null) {
            lastNode = orderNodeRecordEntity.getCurrentNode();
            lastTime = orderNodeRecordEntity.getCreateTime();
        }

        log.info("insert order {} node {} record", orderId, currentNode);
        // 2.2 生成记录
        OrderNodeRecordEntity insertEntity = new OrderNodeRecordEntity();

        insertEntity.setOrderId(orderId);

        insertEntity.setCurrentNode(currentNode);
        insertEntity.setNextNode(nextNode);
        insertEntity.setLastNode(lastNode);

        if (lastTime != null) {
            insertEntity.setApprovalCost(DateUtil.between(
                    Date.from(lastTime.atZone(ZoneId.systemDefault()).toInstant()),
                    new Date(System.currentTimeMillis()), DateUnit.SECOND));
        } else {
            insertEntity.setApprovalCost(0L);
        }

        insertEntity.setRemark(remark);
        insertEntity.setRemarkExternal(remarkExternal);


        insertEntity.setRejectReason(rejectReason);
        insertEntity.setRejectReasonSecond(rejectReasonSecond);


        insertEntity.setEvent(event.eventTransform());
        insertEntity.setCreateBy(userId);
        insertEntity.setUpdateBy(userId);
        log.info("save order {} record {}", orderId, JSONUtil.toJsonStr(insertEntity));
        orderNodeRecordMapper.insert(insertEntity);
    }

    /**
     * 修改订单表状态
     *
     * @param orderId
     * @param nextNode
     */
    protected void saveOrderNodeState(Integer orderId, Integer nextNode) {
        OrderInfoEntity updateEntity = new OrderInfoEntity();

        updateEntity.setId(orderId);
        updateEntity.setCurrentNode(nextNode);
        updateEntity.setLastNodeFinishTime(LocalDateTime.now());
        updateEntity.setState(nextNode);

        orderInfoMapper.updateById(updateEntity);
    }

}