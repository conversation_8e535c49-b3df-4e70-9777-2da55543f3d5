package com.longhuan.order.pojo.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class ContractInfoVO {
    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 订单编号
     */
    private String orderNumber;
    /**
     * 客户姓名
     */
    private String customerName;
    /**
     * 手机号
     */
    private String customerPhone;
    /**
     * 资方id
     */
    private Integer fundId;

    /**
     * 资方名称
     */
    private String fundName;
    /**
     * 车牌号
     */
    private String vehicleNumber;
    /**
     * 申请时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime applyDate;

    /**
     * 贷款金额
     */
    private BigDecimal applyAmount;
    /**
     * 贷款期限
     */
    private Integer term;

    /**
     * 产品名称
     */
    private String productName;
    /**
     * 年化利率
     */
    @ApiModelProperty(value = "年化利率")
    private BigDecimal yearRate;
    /**
     * 还款方式
     */
    private Integer repayMethod;
    /**
     * 申请用途(所有人性质字典值)
     */
    private Integer applyPurpose;

    /**
     * 是否需要资方签署合同
     */
    private Boolean fundSign;

    /**
     * 资方签署状态
     * 0 未发起  1:成功  2：失败
     */
    private Integer fundSignStatus;

    /**
     * 合同状态
     * 0：生成中，1：签署中，2：签署完成
     */
    private Integer contractState;
    /**
     * 合同签署时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime contractSignTime;
    /**
     * 绑卡时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime bindCardTime;
}
