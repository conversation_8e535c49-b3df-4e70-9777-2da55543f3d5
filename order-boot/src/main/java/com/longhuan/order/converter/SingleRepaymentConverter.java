package com.longhuan.order.converter;

import com.longhuan.order.pojo.vo.singleRepayment.SingleRepaymentListExportVO;
import com.longhuan.order.pojo.vo.singleRepayment.SingleRepaymentListVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SingleRepaymentConverter {

   SingleRepaymentListExportVO VOToExportVO(SingleRepaymentListVO singleRepaymentListVO);

   List<SingleRepaymentListExportVO> listVOToExportVO(List<SingleRepaymentListVO> singleRepaymentListVO);
}
