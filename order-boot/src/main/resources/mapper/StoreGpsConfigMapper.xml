<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.longhuan.order.mapper.StoreGpsConfigMapper">

    <resultMap id="BaseResultMap" type="com.longhuan.order.pojo.entity.StoreGpsConfigEntity">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="deptId" column="dept_id" jdbcType="INTEGER"/>
        <result property="isInstallment" column="is_installment" jdbcType="INTEGER"/>
        <result property="isFree" column="is_free" jdbcType="INTEGER"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, dept_id, is_installment, is_free,
        delete_flag, create_by, create_time,
        update_by, update_time
    </sql>

    <!-- 根据门店ID查询配置 -->
    <select id="selectByDeptId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lh_store_gps_config
        WHERE dept_id = #{deptId}
        AND delete_flag = 0
        LIMIT 1
    </select>

</mapper>
