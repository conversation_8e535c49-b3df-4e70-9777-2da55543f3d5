package com.longhuan.order.settleCalculation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.LHSettleCalculationDTO;
import com.longhuan.order.pojo.dto.OrderSettleDTO;
import com.longhuan.order.pojo.dto.SettleCalculationContext;
import com.longhuan.order.pojo.vo.SettleCalculationVO;
import com.longhuan.order.settleCalculation.service.SettleCalculationService;
import com.longhuan.order.settleCalculation.vo.SettleCalculationListVO;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 结清试算接口
 *
 * <AUTHOR>
 * @date 2025/06/12
 */
@Api(tags = "结清试算接口")
@RestController
@RequestMapping("/api/v1/settle")
@RequiredArgsConstructor
public class SettleCalculationController {
    private final SettleCalculationService settleCalculationService;

    @PostMapping("/settleCalculation")
    Result<SettleCalculationVO> settleCalculation(@RequestBody SettleCalculationContext dto) {
        return Result.success(settleCalculationService.calculate(dto));
    }

    @PostMapping("/settleCalculationByOrderId")
    Result<SettleCalculationVO> settleCalculationByOrderId(@RequestBody LHSettleCalculationDTO dto) {
        return Result.success(settleCalculationService.calculateByOrderId(dto.getOrderId(), dto.getClosedPeriod()));
    }


    /**
     * 结清结算器列表
     *
     * @param orderSettleDTO
     * @return
     */
    @PostMapping("/list")
    Result<Page<SettleCalculationListVO>> list(@RequestBody OrderSettleDTO orderSettleDTO) {
        return Result.success(settleCalculationService.list(orderSettleDTO));
    }

    /**
     * 结清结算器-计算结清
     *
     * @param dto
     * @return
     */
    @PostMapping("/compute")
    Result<List<SettleCalculationVO>> compute(@RequestBody LHSettleCalculationDTO dto) {
        return Result.success(settleCalculationService.compute(dto));
    }
}
