package com.longhuan.common.core.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 贷后
 */
@Getter
@AllArgsConstructor
public enum AfterLoanPatchesEnum {
    AFTER_LOAN_PATCHES(10000, "贷后补件"),
    CONTRACT_POST_REVIEW(15000, "合同岗审核"),
    CONTRACT_APPROVED(20000, "合同岗审核通过"),
    CONTRACT_OVERRULE(17500, "合同岗审核驳回"),
    OVERDUE(18000, "逾期"),
    PRESERVE(19000, "保全"),
    FUNDS_WAIT_AUDIT(20500, "资方待审核"),
    FUNDS_REJECT(21000, "资方审批拒绝"),
    FUNDS_APPROVED(22000, "资方审批通过"),
    ;
    @EnumValue
    @JsonValue
    private final Integer code;
    private final String value;
}
