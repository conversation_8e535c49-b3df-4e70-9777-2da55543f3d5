package com.longhuan.data.pojo.entity.order;

import cn.hutool.core.date.DatePattern;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.longhuan.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 风控黑名单
 * @TableName lh_risk_customer_blacklist
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "订单信息表")
@TableName("lh_risk_customer_blacklist")
@KeySequence("lh_risk_customer_blacklist_id_seq")
@EqualsAndHashCode(callSuper = true)
@DS("order")
public class RiskCustomerBlacklistEntity extends BaseEntity {
/**
     * 主键
     */
    @ApiModelProperty(value = "ID", notes = "ID")
    @TableId
    private Integer id;

    /**
     * 客户姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idNumber;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 黑名单原因
     */
    private String blackReason;

    /**
     * 
     */
    private Integer infos;

    /**
     * 
     */
    private Integer status;
    /**
     * 数据来源 0云启 1数字化
     */
    private Integer infoSource;
    /**
     * 黑名单解除时间
     */
    private LocalDateTime releaseTime;
    /**
     * 创建人名称
     */
    private String createName;
    /**
     * 删除人员名称
     */
    private String releaseName;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 解除黑名单原因
     */
    private String releaseReason;
}