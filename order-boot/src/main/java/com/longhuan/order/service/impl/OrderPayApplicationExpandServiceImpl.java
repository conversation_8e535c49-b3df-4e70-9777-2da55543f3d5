package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.longhuan.common.core.constant.MsgConstants;
import com.longhuan.common.core.enums.OrderFeeDetailExpandTypeEnum;
import com.longhuan.common.core.enums.PayApplicationNodeEnums;
import com.longhuan.common.core.enums.PayApplicationPayeeTypeEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.feign.MessageFeign;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.OrderAmountMapper;
import com.longhuan.order.mapper.OrderPayApplyNodeRecordMapper;
import com.longhuan.order.mapper.ProductInfoMapper;
import com.longhuan.order.pojo.dto.ExpenseApplicationExportDTO;
import com.longhuan.order.pojo.entity.OrderAmountEntity;
import com.longhuan.order.pojo.entity.OrderPayApplyNodeRecordEntity;
import com.longhuan.order.pojo.entity.ProductInfoEntity;
import com.longhuan.order.pojo.vo.CashBackPerformanceExportVO;
import com.longhuan.order.pojo.vo.ExpenseApplicationExportVO;
import com.longhuan.order.pojo.vo.OrderPayApplyListVO;
import com.longhuan.order.pojo.vo.PayApplicationPageListExportVO;
import com.longhuan.order.service.OrderPayApplicationExpandService;
import com.longhuan.order.util.CostDisclosureUtil;
import com.longhuan.user.pojo.dto.MessageContent;
import com.longhuan.user.pojo.vo.DeptInfoVO;
import com.longhuan.user.pojo.vo.UserInfoVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
@Slf4j
@Service
@AllArgsConstructor
public class OrderPayApplicationExpandServiceImpl implements OrderPayApplicationExpandService {
    private final RedisService redisService;
    private final UserFeign userFeign;
    private final OrderPayApplyNodeRecordMapper orderPayApplyNodeRecordMapper;
    private final MessageFeign messageFeign;
    private final ProductInfoMapper productInfoMapper;
    private final OrderAmountMapper orderAmountMapper;

    @Override
    @Async
    public void dingTaskExport(LoginUser loginUser,
                               ExpenseApplicationExportDTO dto,
                               List<OrderPayApplyListVO> records
//                                ,
//                                Map<Integer, UserInfoVO> finalAccountantCheckerMap,
//                                Map<Integer, UserInfoVO> finalCashierCheckerMap,
//                                Map<Integer, OrderPayApplyNodeRecordEntity> finalCollect
    ) {

        String lockKey = "OrderPayApplicationController:getSpecialRefundApply"+loginUser.getUserId();
        String requestId = IdUtil.randomUUID();
        Boolean tryLock = redisService.tryLock(lockKey, requestId, 30, TimeUnit.MINUTES);
        if (tryLock){
            try {
                if (CollUtil.isNotEmpty(records)) {
                    BigDecimal totalAmount = BigDecimal.ZERO;
// 客户经理id 提交人id List
                    List<Integer> userIds = Stream.concat(
                                    records.stream().filter(orderPayApplyListVO -> ObjUtil.isNotEmpty(orderPayApplyListVO.getManagerId()))
                                            .map(OrderPayApplyListVO::getManagerId),
                                    records.stream().filter(orderPayApplyListVO -> ObjUtil.isNotEmpty(orderPayApplyListVO.getUserId()))
                                            .map(OrderPayApplyListVO::getUserId)
                            )
                            .distinct()
                            .toList();
                    Map<Integer, UserInfoVO> userInfoMap = new HashMap<>();
                    if (CollUtil.isNotEmpty(userIds)) {
                        // 获取用户列表
                        Result<List<UserInfoVO>> userInfoResult = userFeign.searchUserNameByUserIds(userIds);
                        if (Result.isSuccess(userInfoResult) && ObjUtil.isNotEmpty(userInfoResult.getData())) {
                            userInfoMap = userInfoResult.getData().stream()
                                    .collect(Collectors.toMap(UserInfoVO::getUserId, userInfoVO -> userInfoVO));
                        }
                    }
                    for (OrderPayApplyListVO orderPayApplyListVO : records) {
//                        .selectAs(ProductInfoEntity::getId, OrderPayApplyListVO::getProductId)
//                                .selectAs(ProductInfoEntity::getName, OrderPayApplyListVO::getProductName)
//                                .selectAs(ProductInfoEntity::getCashPerformance, OrderPayApplyListVO::getCashPerformance)
//                                .selectAs(ProductInfoEntity::getTerm, OrderPayApplyListVO::getPeriod)
//                                .selectAs(OrderAmountEntity::getCustomerConfirmAmount, OrderPayApplyListVO::getCustomerConfirmAmount)
                        ProductInfoEntity productInfoEntity = productInfoMapper.selectById(orderPayApplyListVO.getProductId());
                        orderPayApplyListVO.setProductName(productInfoEntity.getName());
                        orderPayApplyListVO.setPeriod(productInfoEntity.getTerm());
                        orderPayApplyListVO.setCashPerformance(productInfoEntity.getCashPerformance());
                        OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(
                                new LambdaQueryWrapper<OrderAmountEntity>()
                                        .eq(OrderAmountEntity::getOrderId, orderPayApplyListVO.getOrderId())
                                        .orderByDesc(OrderAmountEntity::getCreateTime)
                                .last("limit 1")
                        );
                        orderPayApplyListVO.setCustomerConfirmAmount(orderAmountEntity.getCustomerConfirmAmount());
                        if (ObjUtil.isNotNull(orderPayApplyListVO.getPayeeAmount())) {
                            totalAmount = totalAmount.add(orderPayApplyListVO.getPayeeAmount());
                        }
                        if (orderPayApplyListVO.getUserId() != null && orderPayApplyListVO.getUserId() != 1) {
                            UserInfoVO userInfoVO = userInfoMap.get(orderPayApplyListVO.getUserId());
                            if (ObjUtil.isNotNull(userInfoVO)) {
                                orderPayApplyListVO.setUserName(userInfoVO.getName());
                            }
                        } else {
                            orderPayApplyListVO.setUserName("系统自动提交");
                        }
                        if (StrUtil.isNotBlank(orderPayApplyListVO.getPaymentVoucherList())) {
                            // 将 JSON 字符串转换为 List<String>
                            List<String> paymentVoucherList = JSON.parseArray(orderPayApplyListVO.getPaymentVoucherList(), String.class);
                            orderPayApplyListVO.setResourceId(paymentVoucherList);
                        }
                        if (Objects.equals(orderPayApplyListVO.getFeeType(), OrderFeeDetailExpandTypeEnum.CURRENT_RETURN_PERFORMANCE.getCode())) {
                            if (ObjUtil.isNotEmpty(orderPayApplyListVO.getManagerId())) {
                                Integer managerId = orderPayApplyListVO.getManagerId();
                                UserInfoVO userInfoVO = userInfoMap.get(managerId);
                                if (ObjUtil.isNotNull(userInfoVO)) {
                                    orderPayApplyListVO.setPayeeName(userInfoVO.getName());
                                }
                            }
                        }
                        orderPayApplyListVO.setBranchName(ObjUtil.defaultIfNull(orderPayApplyListVO.getStoreName(), ""));
                    }
                    List<Integer> teamIds = records.stream().map(OrderPayApplyListVO::getTeamId).filter(Objects::nonNull).toList();
                    if (CollUtil.isNotEmpty(teamIds)) {
                        List<DeptInfoVO> branchNameVOS = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData();
                        Map<Integer, String> branchNameMap = branchNameVOS.stream().filter(e -> ObjUtil.isNotNull(e.getName())).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
                        records.stream().filter(item -> item.getTeamId() != null).forEach(record -> {
                            record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(branchNameMap.get(record.getTeamId()), ""));
                        });
                    }
                    //==========================================
                    //会计审核人
                    List<Integer> accountantCheckerList = records.stream()
                            .map(OrderPayApplyListVO::getAccountantChecker).filter(ObjUtil::isNotNull).toList();
                    Map<Integer, UserInfoVO> accountantCheckerMap = new HashMap<>();
                    if (CollUtil.isNotEmpty(accountantCheckerList)) {
                        Result<List<UserInfoVO>> listResult = userFeign.searchUserNameByUserIds(accountantCheckerList);
                        if (Result.isSuccess(listResult) && CollUtil.isNotEmpty(listResult.getData())) {
                            accountantCheckerMap = listResult.getData().stream()
                                    .collect(Collectors.toMap(UserInfoVO::getUserId, Function.identity(), (existing, replacement) -> existing));
                        }
                    }
                    Map<Integer, UserInfoVO> finalAccountantCheckerMap = accountantCheckerMap;
//出纳审核人
                    List<Integer> cashierCheckerList = records.stream()
                            .map(OrderPayApplyListVO::getCashierChecker).filter(ObjUtil::isNotNull).toList();
                    Map<Integer, UserInfoVO> cashierCheckerMap = new HashMap<>();
                    if (CollUtil.isNotEmpty(cashierCheckerList)) {
                        Result<List<UserInfoVO>> listResult = userFeign.searchUserNameByUserIds(cashierCheckerList);
                        if (Result.isSuccess(listResult) && CollUtil.isNotEmpty(listResult.getData())) {
                            cashierCheckerMap = listResult.getData().stream()
                                    .collect(Collectors.toMap(UserInfoVO::getUserId, Function.identity(), (existing, replacement) -> existing));
                        }
                    }
                    Map<Integer, UserInfoVO> finalCashierCheckerMap = cashierCheckerMap;
//收款主体是客户的行号
//        List<String> customerList = records.stream()
//                .filter(record -> Objects.equals(record.getPayeeType(), PayApplicationPayeeTypeEnum.CUSTOMER.getCode()))
//                .map(OrderPayApplyListVO::getPayeeAccountNumber).filter(ObjUtil::isNotNull).toList();
//        Map<String, BankAccountSignEntity> customerCollectMap = new HashMap<>();
//        if (CollUtil.isNotEmpty(customerList)){
//            customerCollectMap = bankAccountSignMapper.selectList(
//                            new LambdaQueryWrapper<BankAccountSignEntity>()
//                                    .in(BankAccountSignEntity::getBankCardNumber, customerList)
//                                    .eq(BankAccountSignEntity::getDeleteFlag, 0)).stream()
//                    .collect(Collectors.toMap(BankAccountSignEntity::getBankCardNumber, Function.identity(), (existing, replacement) -> existing));
//        }
//        Map<String, BankAccountSignEntity> finalCustomerCollectMap = customerCollectMap;
//收款主体是汇丰员工的行号
//        List<String> businessPersonnelList = records.stream()
//                .filter(record -> Objects.equals(record.getPayeeType(), PayApplicationPayeeTypeEnum.BUSINESS_PERSONNEL.getCode()) && ObjectUtil.isNotEmpty(record.getManagerId()))
//                .map(OrderPayApplyListVO::getPayeeAccountNumber).filter(ObjUtil::isNotNull).toList();
//        Map<String, ManageBankAccountSignEntity> businessPersonnelCollectMap = new HashMap<>();
//        if (CollUtil.isNotEmpty(businessPersonnelList)){
//            businessPersonnelCollectMap = manageBankAccountSignMapper.selectList(
//                            new LambdaQueryWrapper<ManageBankAccountSignEntity>()
//                                    .in(ManageBankAccountSignEntity::getBankCardNumber, businessPersonnelList)
//                                    .eq(ManageBankAccountSignEntity::getDeleteFlag, 0)).stream()
//                    .collect(Collectors.toMap(ManageBankAccountSignEntity::getBankCardNumber, Function.identity(), (existing, replacement) -> existing));
//        }
//        Map<String, ManageBankAccountSignEntity> finalBusinessPersonnelCollectMap = businessPersonnelCollectMap;
// 出纳审核状态
                    List<Integer> applyInfoIdList = records.stream()
                            .map(OrderPayApplyListVO::getId).filter(ObjUtil::isNotNull).toList();
                    Map<Integer, OrderPayApplyNodeRecordEntity> collect = new HashMap<>();
                    if (CollUtil.isNotEmpty(applyInfoIdList)) {
                        collect = orderPayApplyNodeRecordMapper.selectList(
                                        new LambdaQueryWrapper<OrderPayApplyNodeRecordEntity>()
                                                .in(OrderPayApplyNodeRecordEntity::getApplyInfoId, applyInfoIdList)
                                                .eq(OrderPayApplyNodeRecordEntity::getDeleteFlag, 0)
                                                .eq(OrderPayApplyNodeRecordEntity::getCurrentNode, PayApplicationNodeEnums.CASHIER_APPROVAL.getCode())).stream()
                                .collect(Collectors.toMap(OrderPayApplyNodeRecordEntity::getApplyInfoId, Function.identity(),
                                        BinaryOperator.maxBy(Comparator.comparing(OrderPayApplyNodeRecordEntity::getCreateTime))));
                    }
                    Map<Integer, OrderPayApplyNodeRecordEntity> finalCollect = collect;
                    // 创建字节数组输出流
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
// 创建ExcelWriter对象，指定输出流
                    ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
                    if (Objects.equals(dto.getFeeDetails(),1)){
                        if (Objects.equals(dto.getFeeType().size(),1) && Objects.equals(dto.getFeeType().get(0), 2)) {
                            List<CashBackPerformanceExportVO> list = records.stream()
                                    .map(exportVO -> {
                                        CashBackPerformanceExportVO vo = new CashBackPerformanceExportVO();
                                        vo.setOrderNumber(exportVO.getOrderNumber());
                                        vo.setFeeType(exportVO.getFeeType());
                                        vo.setFeeTypeToStr(ObjUtil.isNotEmpty(OrderFeeDetailExpandTypeEnum.fromCode(exportVO.getFeeType())) ? OrderFeeDetailExpandTypeEnum.fromCode(exportVO.getFeeType()).getDescription() : "");
                                        vo.setVehicleNumber(exportVO.getVehicleNumber());
                                        vo.setTransferTime(exportVO.getPaymentTime());
                                        vo.setTransferTimeToStr(ObjUtil.isNotNull(exportVO.getPaymentTime()) ? exportVO.getPaymentTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                        vo.setCashierApproveTime(exportVO.getCashierApproveTime());
                                        vo.setCashierApproveTimeToStr(ObjUtil.isNotNull(exportVO.getCashierApproveTime()) ? exportVO.getCashierApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                        vo.setCustomerName(exportVO.getCustomerName());
                                        vo.setRegionName(exportVO.getRegionName());
                                        vo.setStoreName(exportVO.getBranchName());
                                        vo.setPaymentDetails(exportVO.getPaymentDetails());
                                        vo.setFundName(exportVO.getFundName());
                                        vo.setCashPerformance(exportVO.getCashPerformance() != null ? exportVO.getCashPerformance().multiply(new BigDecimal(100)) + "%" : "");
                                        vo.setLoanTime(exportVO.getLoanTime());
                                        vo.setLoanTimeToStr(ObjUtil.isNotNull(exportVO.getLoanTime()) ? exportVO.getLoanTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                        vo.setProductName(exportVO.getProductName());
                                        vo.setTerm(exportVO.getPeriod());
                                        vo.setCustomerConfirmAmountToStr(ObjUtil.isNotNull(exportVO.getCustomerConfirmAmount()) ? String.valueOf(exportVO.getCustomerConfirmAmount()) : null);
                                        vo.setTradeAmountToStr(ObjUtil.isNotNull(exportVO.getPayeeAmount()) ? String.valueOf(exportVO.getPayeeAmount()) : null);
                                        vo.setTradeAmountToStr(ObjUtil.isNotNull(exportVO.getPayeeAmount()) ? exportVO.getPayeeAmount().toString() : null);
                                        vo.setRemark(exportVO.getRemark());
                                        vo.setApplyType(exportVO.getApplyType());
                                        vo.setApplyTypeToStr(exportVO.getApplyType() == 1 ? "付款单" : "收款单");
                                        vo.setPayeeAccount(exportVO.getPayeeAccount());
                                        vo.setPayeeAccountName(exportVO.getPayeeAccountName());
                                        vo.setPayeeAccountNumber(exportVO.getPayeeAccountNumber());
                                        vo.setPayeePhone(exportVO.getPayeePhone());
                                        vo.setPayeeCardNumber(exportVO.getPayeeCardNumber());
                                        vo.setPayAccount(exportVO.getPayAccount());
                                        vo.setPayAccountNumber(exportVO.getPayAccountNumber());
                                        vo.setPayAccountName(exportVO.getPayAccountName());
                                        vo.setAuditStatus(PayApplicationNodeEnums.getDescriptionByCode(exportVO.getCurrentNode().getCode()));
                                        vo.setSubmitTime(exportVO.getSubmitTime());
                                        vo.setApplyRemark(exportVO.getRemark());
                                        vo.setSubmitTimeToStr(exportVO.getSubmitTime() != null ? exportVO.getSubmitTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                        vo.setAccountantUserName(finalAccountantCheckerMap.containsKey(exportVO.getAccountantChecker()) ? finalAccountantCheckerMap.get(exportVO.getAccountantChecker()).getName() : "");
                                        vo.setAccountantApproveTime(exportVO.getAccountantApproveTime());
                                        vo.setAccountantApproveTimeToStr(ObjUtil.isNotNull(exportVO.getAccountantApproveTime()) ? exportVO.getAccountantApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                        vo.setAccountantRemark(exportVO.getAccountantRemark());
                                        vo.setCashierChecker(finalCashierCheckerMap.containsKey(exportVO.getCashierChecker()) ? finalCashierCheckerMap.get(exportVO.getCashierChecker()).getName() : "");
                                        vo.setCashierRemark(exportVO.getCashierRemark());
                                        vo.setUserName(exportVO.getUserName());
                                        vo.setUserRole(ObjUtil.isNotNull(exportVO.getUserId()) ? (exportVO.getUserId() == 1 ? "系统自动提交" : "线上运营人员") : "线上运营人员");
                                        //                                        vo.setSummary(exportVO.getSummary());
                                        return vo;
                                    })
                                    .toList();
                            // 创建WriteSheet对象，指定工作表名称、数据类和表头
                            WriteSheet writeSheet = EasyExcel.writerSheet("费用申请报表.xlsx")
                                    .head(CashBackPerformanceExportVO.class)
                                    .build();
                            // 写入实际数据到工作表
                            excelWriter.write(list, writeSheet);
//                        if (CollUtil.isNotEmpty(list)) {
//                            // 创建合计行
//                            CashBackPerformanceExportVO totalRow = new CashBackPerformanceExportVO();
//                            totalRow.setOrderNumber("合计");
//                            // 计算金额合计
//                            BigDecimal tradeAmount = list.stream()
//                                    .map(CashBackPerformanceExportVO::getTradeAmount)
//                                    .filter(Objects::nonNull)
//                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//                            totalRow.setTradeAmountToStr(String.valueOf(tradeAmount));
//                            // 写入合计行
//                            excelWriter.write(Collections.singletonList(totalRow), writeSheet);
//                        }
                            int windowSize = CollUtil.isNotEmpty(list) ? list.size() : 100;
                            SXSSFWorkbook workbook = new SXSSFWorkbook(windowSize);
                            SXSSFSheet sheet;
                            if (workbook.getNumberOfSheets() > 0) {
                                sheet = workbook.getSheetAt(0);
                            } else {
                                sheet = workbook.createSheet("现返绩效");
                            }
                            adjustSheet1(sheet, windowSize,workbook);
                            workbook.dispose();
                        }else {
//                        ExpenseApplicationExportVO
                            List<ExpenseApplicationExportVO> list = records.stream()
                                    .map(exportVO -> {
                                        ExpenseApplicationExportVO vo = new ExpenseApplicationExportVO();
//                                    vo.setOrderNumber(exportVO.getOrderNumber());
                                        vo.setFeeType(exportVO.getFeeType());
                                        vo.setFeeTypeToStr(ObjUtil.isNotEmpty(OrderFeeDetailExpandTypeEnum.fromCode(exportVO.getFeeType())) ? OrderFeeDetailExpandTypeEnum.fromCode(exportVO.getFeeType()).getDescription() : "");
                                        vo.setVehicleNumber(exportVO.getVehicleNumber());
                                        vo.setTransferTime(exportVO.getPaymentTime());
                                        vo.setTransferTimeToStr(ObjUtil.isNotNull(exportVO.getPaymentTime()) ? exportVO.getPaymentTime().toLocalDate().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)) : "");
                                        vo.setCashierApproveTime(exportVO.getCashierApproveTime());
                                        vo.setCashierApproveTimeToStr(ObjUtil.isNotNull(exportVO.getCashierApproveTime()) ? exportVO.getCashierApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                        vo.setCustomerName(exportVO.getCustomerName());
//                                    vo.setRegionName(exportVO.getRegionName());
//                                    vo.setStoreName(exportVO.getStoreName());
                                        vo.setPaymentDetails(exportVO.getPaymentDetails());
                                        vo.setFundName(exportVO.getFundName());
//                                    vo.setCashPerformance(exportVO.getCashPerformance() != null ? exportVO.getCashPerformance().multiply(new BigDecimal(100)) + "%" : "");
//                                    vo.setLoanTime(exportVO.getLoanTime());
//                                    vo.setLoanTimeToStr(ObjUtil.isNotNull(exportVO.getLoanTime()) ? exportVO.getLoanTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
//                                    vo.setProductName(exportVO.getProductName());
//                                    vo.setTerm(exportVO.getPeriod());
//                                    vo.setCustomerConfirmAmount(exportVO.getCustomerConfirmAmount());
                                        vo.setTradeAmountToStr(ObjUtil.isNotNull(exportVO.getPayeeAmount()) ? String.valueOf(exportVO.getPayeeAmount()) : null);
                                        vo.setTradeAmount(exportVO.getPayeeAmount());
                                        vo.setRemark(exportVO.getRemark());
                                        vo.setApplyType(exportVO.getApplyType());
                                        vo.setApplyTypeToStr(exportVO.getApplyType() == 1 ? "付款单" : "收款单");
                                        vo.setPayeeAccount(exportVO.getPayeeAccount());
                                        vo.setPayeeAccountName(exportVO.getPayeeAccountName());
                                        vo.setPayeeAccountNumber(exportVO.getPayeeAccountNumber());
                                        vo.setPayeeAccountOpeningBranch(exportVO.getPayeeAccountName());
//                                    vo.setPayeePhone(exportVO.getPayeePhone());
//                                    vo.setPayeeCardNumber(exportVO.getPayeeCardNumber());
                                        vo.setPayAccount(exportVO.getPayAccount());
                                        vo.setPayAccountNumber(exportVO.getPayAccountNumber());
                                        vo.setPayAccountName(exportVO.getPayAccountName());
//                                    vo.setAuditStatus(PayApplicationNodeEnums.getDescriptionByCode(exportVO.getCurrentNode().getCode()));
                                        vo.setAuditStatus(finalCollect.containsKey(exportVO.getId()) ? (
                                                switch (finalCollect.get(exportVO.getId()).getEvent()){
                                                    case APPROVE_PASS -> "通过";
                                                    case APPROVE_REJECT -> "拒绝";
                                                    case APPROVE_REVOKE -> "驳回";
                                                case REDIRECTED -> "转交";
                                                }) : "");
                                        vo.setSubmitTime(exportVO.getSubmitTime());
                                        vo.setApplyRemark(exportVO.getRemark());
                                        vo.setSubmitTimeToStr(exportVO.getSubmitTime() != null ? exportVO.getSubmitTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                        vo.setAccountantUserName(finalAccountantCheckerMap.containsKey(exportVO.getAccountantChecker()) ? finalAccountantCheckerMap.get(exportVO.getAccountantChecker()).getName() : "");
                                        vo.setAccountantApproveTime(exportVO.getAccountantApproveTime());
                                        vo.setAccountantApproveTimeToStr(ObjUtil.isNotNull(exportVO.getAccountantApproveTime()) ? exportVO.getAccountantApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                        vo.setAccountantRemark(exportVO.getAccountantRemark());
                                        vo.setCashierChecker(finalCashierCheckerMap.containsKey(exportVO.getCashierChecker()) ? finalCashierCheckerMap.get(exportVO.getCashierChecker()).getName() : "");
                                        vo.setCashierRemark(exportVO.getCashierRemark());
                                        vo.setUserName(exportVO.getUserName());
                                        vo.setUserRole(ObjUtil.isNotNull(exportVO.getUserId()) ? (exportVO.getUserId() == 1 ? "系统自动提交" : "线上运营人员") : "线上运营人员");
                                        vo.setSummary(exportVO.getSummary());
                                        return vo;
                                    })
                                    .toList();
                            // 创建WriteSheet对象，指定工作表名称、数据类和表头
                            WriteSheet writeSheet = EasyExcel.writerSheet("费用申请报表.xlsx")
                                    .head(ExpenseApplicationExportVO.class)
                                    .build();
                            // 写入实际数据到工作表
                            excelWriter.write(list, writeSheet);
                            if (CollUtil.isNotEmpty(list)) {
                                // 创建合计行
                                ExpenseApplicationExportVO totalRow = new ExpenseApplicationExportVO();
                                totalRow.setPaymentDetails("合计");
                                // 计算金额合计
                                BigDecimal tradeAmount = list.stream()
                                        .map(ExpenseApplicationExportVO::getTradeAmount)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                totalRow.setTradeAmountToStr(String.valueOf(tradeAmount));
                                // 写入合计行
                                excelWriter.write(Collections.singletonList(totalRow), writeSheet);
                            }
                            int windowSize = CollUtil.isNotEmpty(list) ? list.size() : 100;
                            SXSSFWorkbook workbook = new SXSSFWorkbook(windowSize);
                            SXSSFSheet sheet;
                            if (workbook.getNumberOfSheets() > 0) {
                                sheet = workbook.getSheetAt(0);
                            } else {
                                sheet = workbook.createSheet("费用申请");
                            }
                            adjustSheet1(sheet, windowSize,workbook);
                            workbook.dispose();
                        }
                    }
                    if (Objects.equals(dto.getFeeDetails(),2)) {
                        List<PayApplicationPageListExportVO> list = records.stream()
                                .map(exportVO -> {
                                    PayApplicationPageListExportVO vo = new PayApplicationPageListExportVO();
                                    vo.setId(exportVO.getId());
                                    vo.setOrderId(exportVO.getOrderId());
                                    vo.setPayeeAccount(exportVO.getPayeeAccount());
                                    vo.setOrderNumber(exportVO.getOrderNumber());
                                    vo.setCustomerName(exportVO.getCustomerName());
                                    vo.setVehicleNumber(exportVO.getVehicleNumber());
                                    vo.setRegionName(exportVO.getRegionName());
                                    vo.setStoreName(exportVO.getBranchName());
                                    vo.setUserName(exportVO.getUserName());
                                    vo.setFeeType(exportVO.getFeeType());
                                    vo.setFeeTypeToStr(ObjUtil.isNotEmpty(OrderFeeDetailExpandTypeEnum.fromCode(exportVO.getFeeType())) ? OrderFeeDetailExpandTypeEnum.fromCode(exportVO.getFeeType()).getDescription() : "");
                                    vo.setPayeeAmount(exportVO.getPayeeAmount());
                                    vo.setPayeeAmountToStr(String.valueOf(exportVO.getPayeeAmount()));
                                    vo.setPayeeType(exportVO.getPayeeType());
                                    vo.setPayeeTypeToStr(ObjUtil.defaultIfNull(PayApplicationPayeeTypeEnum.fromCode(exportVO.getPayeeType()).getDesc(), ""));
                                    vo.setPayAccount(exportVO.getPayAccount());
                                    vo.setPayeeAccountNumber(exportVO.getPayeeAccountNumber());
                                    vo.setRemark(exportVO.getRemark());
                                    vo.setCurrentNode(exportVO.getCurrentNode());
                                    vo.setCurrentNodeToStr(PayApplicationNodeEnums.getDescriptionByCode(exportVO.getCurrentNode().getCode()));
                                    vo.setPaymentTime(exportVO.getPaymentTime());
                                    vo.setPaymentTimeToStr(ObjUtil.isNotEmpty(exportVO.getPaymentTime()) ? exportVO.getPaymentTime().toLocalDate().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)) : "");
                                    vo.setCashierApproveTime(exportVO.getCashierApproveTime());
                                    vo.setCashierApproveTimeToStr(ObjUtil.isNotEmpty(exportVO.getCashierApproveTime()) ? exportVO.getCashierApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                    vo.setSubmitTime(exportVO.getSubmitTime());
                                    vo.setSubmitTimeToStr(ObjUtil.isNotEmpty(exportVO.getSubmitTime()) ? exportVO.getSubmitTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                                    vo.setTeamId(exportVO.getTeamId());
                                    vo.setBranchName(exportVO.getBranchName());
                                    vo.setPaySnNumBack(exportVO.getPaySnNumBack());
                                    vo.setManagerId(exportVO.getManagerId());
                                    vo.setManagerName(exportVO.getManagerName());
                                    vo.setPayeeAccountName(exportVO.getPayeeAccountName());
                                    vo.setProductId(exportVO.getProductId());
                                    vo.setPaymentDetails(exportVO.getPaymentDetails());
                                    vo.setUserId(exportVO.getUserId());
                                    vo.setPayAccountName(exportVO.getPayAccountName());
                                    vo.setPayAccountNumber(exportVO.getPayAccountNumber());
                                    vo.setResourceId(exportVO.getResourceId());
                                    vo.setPaymentVoucherList(exportVO.getPaymentVoucherList());
                                    vo.setAccountantRemark(exportVO.getAccountantRemark());
                                    vo.setCashierRemark(exportVO.getCashierRemark());
                                    vo.setFundName(exportVO.getFundName());
                                    vo.setPayeeAccountBranchName(exportVO.getPayeeAccountBranchName());
                                    vo.setPayeePhone(exportVO.getPayeePhone());
                                    vo.setPayeeName(exportVO.getPayeeName());
                                    vo.setPayeeCardNumber(exportVO.getPayeeCardNumber());
                                    vo.setAccountantApproveTime(exportVO.getAccountantApproveTime());
                                    vo.setAccountantChecker(exportVO.getAccountantChecker());
                                    vo.setCashierChecker(exportVO.getCashierChecker());
                                    vo.setApplyType(exportVO.getApplyType());
                                    vo.setFundId(exportVO.getFundId());
                                    vo.setSummary(exportVO.getSummary());
                                    vo.setFeeDetails(exportVO.getFeeDetails());
                                    vo.setProductName(exportVO.getProductName());
                                    vo.setOrderSource(exportVO.getOrderSource());
                                    vo.setOrderApplicationSource(exportVO.getOrderApplicationSource());
                                    vo.setRepaymentTerm(exportVO.getRepaymentTerm());
                                    return vo;
                                })
                                .toList();
                        // 创建WriteSheet对象，指定工作表名称、数据类和表头
                        WriteSheet writeSheet = EasyExcel.writerSheet("查账申请报表.xlsx")
                                .head(PayApplicationPageListExportVO.class)
                                .build();
                        // 写入实际数据到工作表
                        excelWriter.write(list, writeSheet);
                        if (CollUtil.isNotEmpty(list)) {
                            // 创建合计行
                            PayApplicationPageListExportVO totalRow = new PayApplicationPageListExportVO();
                            totalRow.setOrderNumber("合计");
                            // 计算金额合计
                            BigDecimal amountTotal = list.stream()
                                    .map(PayApplicationPageListExportVO::getPayeeAmount)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            totalRow.setPayeeAmountToStr(String.valueOf(amountTotal));
                            excelWriter.write(Collections.singletonList(totalRow), writeSheet);
                        }
                        int windowSize = CollUtil.isNotEmpty(list) ? list.size() : 100;
                        SXSSFWorkbook workbook = new SXSSFWorkbook(windowSize);
                        SXSSFSheet sheet;
                        if (workbook.getNumberOfSheets() > 0) {
                            sheet = workbook.getSheetAt(0);
                        } else {
                            sheet = workbook.createSheet("查账申请");
                        }
                        adjustSheet1(sheet, windowSize,workbook);
                        workbook.dispose();
                    }
                    excelWriter.finish();
// 将字节数组输出流转换为字节数组
                    byte[] bytes = outputStream.toByteArray();
//            // 设置文件下载头
//            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + URLEncoder.encode("费用申请导出.xlsx", StandardCharsets.UTF_8) + "\"");
//            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
//
//            // 获取HttpServletResponse的输出流并写入字节数组
//            try (ServletOutputStream outputStream1 = response.getOutputStream()) {
//                outputStream1.write(bytes);
//                outputStream1.flush();
//            } catch (IOException e) {
//                // 处理异常
//                e.printStackTrace();
//            }

                    String base64String = Base64.getEncoder().encodeToString(bytes);
                    messageFeign.sendMessage(new MessageContent()
                            .setMsgType(MsgConstants.MSG_FILE)
                            .setSendType(MsgConstants.SEND_DD_NOTICE)
                            .setTitle((Objects.equals(dto.getFeeDetails(),1) ? "费用申请" : "查账申请") +"报表-" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN))
                            .setContent("导出完成，请查收")
                            .setMediaContent(base64String)
                            .setReceiver(loginUser.getMobile())
                            .setFileType("xlsx")
                    );

                }
            } catch (BusinessException e) {
                log.info("OrderPayApplicationPriveteMethodImpl.expenseApplicationExport error:{}", e.getMessage());
            }finally {
                redisService.releaseLock(lockKey, requestId);
            }
        }
    }

    private void adjustSheet1(Sheet sheet, Integer size, SXSSFWorkbook workbook){
        if (sheet instanceof SXSSFSheet) {
            ((SXSSFSheet) sheet).setRandomAccessWindowSize(size);
            ((SXSSFSheet) sheet).trackAllColumnsForAutoSizing();
        }
        // 获取最大行数
        int rowCount = sheet.getLastRowNum();
        // 创建一个数组来存储每列的最大宽度
        int maxCellCount = 0;
        for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row currentRow = sheet.getRow(rowIndex);
            if (currentRow != null) {
                int lastCellNum = currentRow.getLastCellNum();
                if (lastCellNum > maxCellCount) {
                    maxCellCount = lastCellNum;
                }
            }
        }
        int[] maxWidths = new int[maxCellCount];
        // 遍历每一行，计算每列的最大宽度
        for (int rowIndex = 0; rowIndex <= rowCount; rowIndex++) {
            Row currentRow = sheet.getRow(rowIndex);
            if (currentRow != null) {
                for (int cellIndex = 0; cellIndex < currentRow.getLastCellNum(); cellIndex++) {
                    Cell cell = currentRow.getCell(cellIndex);
                    if (cell != null) {
                        // 获取单元格的值
                        String cellValue = CostDisclosureUtil.getCellValue(cell);
                        // 计算单元格宽度，并更新最大宽度数组
                        int cellWidth = (cellValue.length() + 8) * 256; // 256 units is approximately 1 character width
                        maxWidths[cellIndex] = Math.max(maxWidths[cellIndex], cellWidth);
                    }
                }
            }
        }

        // 设置每列的宽度
        for (int i = 0; i < maxWidths.length; i++) {
            sheet.setColumnWidth(i, maxWidths[i]);
        }
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);

        // 创建一个新的单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setWrapText(true); // 启用自动换行
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        int rowHeight = 25;
        int lastRowNum = sheet.getLastRowNum();
        for (int i = 0; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            // 设置行高
            row.setHeightInPoints(i == 0 ? rowHeight + 15 : rowHeight);
            row.setRowStyle(cellStyle);
        }
    }

//        }else {
//            throw new BusinessException("暂无数据");
//        }
//        VehicleServiceFeesExportVO  车务费
//        CashBackPerformanceExportVO  现返绩效
//        RedemptionExportVO  赎回
//        ExpenseApplicationExportVO  通用

//    }

}
