package com.longhuan.order.controller;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.dto.chexiao.CancelGongdanDTO;
import com.longhuan.order.pojo.dto.chexiao.UpdateGongdanDTO;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.pojo.vo.chexiao.PushGongdanProgressVO;
import com.longhuan.order.service.GpsInstallInfoService;
import com.longhuan.order.service.GpsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


/**
 * GPS系统接口
 *
 * <AUTHOR>
 * @date 2024/08/13
 */
@Api(tags = "GPS系统接口")
@RestController
@RequestMapping("/api/v1/gps")
@RequiredArgsConstructor
@Slf4j
public class GpsController {
    private final GpsService gpsService;
    private final GpsInstallInfoService gpsInstallInfoService;

    /**
     * 安装
     *
     * @param installGpsDTO 安装 GPS DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "安装")
    @PostMapping("/install")
    public Result<Boolean> install(@RequestBody @Validated InstallGpsDTO installGpsDTO) {
        return Result.success(gpsService.install(installGpsDTO));
    }


    /**
     * 列表
     *
     * @param gpsListDTO GPS列表DTO
     * @return {@link Result }<{@link Page }<{@link GpsListVO }>>
     */
    @ApiOperation(value = "GPS列表")
    @PostMapping("/list")
    public Result<Page<GpsListVO>> list(@RequestBody @Validated GpsListDTO gpsListDTO) {
        return Result.success(gpsService.list(gpsListDTO));
    }

    /**
     * 上传gps图片
     *
     * @param uploadGpsImageVO 上传gps图片
     */
    @ApiOperation(value = "上传gps图片")
    @PostMapping("/uploadGpsImage")
    public Result<String> list(@RequestBody @Validated UploadGpsImageDTO uploadGpsImageVO) {
        return Result.success(gpsService.uploadGpsImage(uploadGpsImageVO));
    }

    /**
     * 详情信息
     *
     * @param orderId 订单 ID
     * @return {@link Result }<{@link GpsDetailVO }>
     */
    @ApiOperation(value = "GPS详情")
    @GetMapping("/detail")
    public Result<GpsDetailVO> detail(@RequestParam @Validated Integer orderId) {
        return Result.success(gpsService.detail(orderId));
    }

    /**
     * 详情信息
     *
     * @param orderId 订单 ID
     * @return {@link Result }<{@link GpsDetailVO }>
     */
    @ApiOperation(value = "GPS详情")
    @GetMapping("/detailByOrderId/{orderId}")
    public Result<GpsDetailVO> detailByOrderId(@PathVariable("orderId") Integer orderId) {
        return Result.success(gpsService.detail(orderId));
    }

    @ApiOperation(value = "根据订单id获取GPS信息")
    @GetMapping("/getInfoByOrderId/{orderId}")
    public Result<OrderGpsInfoVO> orderGpsInfo(@PathVariable("orderId") @Validated Integer orderId) {
        return Result.success(gpsService.getOrderGpsInfoByOrderId(orderId));
    }

//    /**
//     * 预约安装GPS
//     *
//     * @param orderId 订单 ID
//     * @return {@link Result }<{@link GpsDetailVO }>
//     */
//    @ApiOperation(value = "安装GPS")
//    @GetMapping("/setOpenInstallOrder")
//    public Result<String> setOpenInstallOrder(@RequestParam @Validated Integer orderId) {
//        return Result.success(gpsService.setOpenInstallOrder(orderId));
//    }

    /**
     * 预约拆除GPS
     *
     * @param afterSaleOrderDTO 订单 ID
     * @return {@link Result }<{@link GpsDetailVO }>
     */
    @ApiOperation(value = "预约拆除GPS")
    @PostMapping("/setAfterSaleOrder")
    public Result<String> setAfterSaleOrder(@RequestBody @Validated GPSAfterSaleOrderDTO afterSaleOrderDTO) {
        return Result.success(gpsService.setAfterSaleOrder(afterSaleOrderDTO));
    }

    /**
     * GPS安装回调
     *
     * @param gpsInstallCallbackDTO 回调参数
     * @return {@link Result }<{@link GpsDetailVO }>
     */
    @ApiOperation(value = "GPS安装回调")
    @PostMapping(value = "/gpsInstallCallback")
    public Result<String> gpsInstallCallback(@Validated @RequestBody GpsInstallCallbackVO gpsInstallCallbackDTO) {
        return Result.success(gpsService.gpsInstallCallback(gpsInstallCallbackDTO));
    }
    /**
      * GPS更新车辆信息
      */
    @ApiOperation(value = "GPS更新车辆信息")
    @PostMapping("/updateCarInfo")
    public Result<String> updateCarInfo(@RequestBody @Validated List<Integer> orderIds) {
        gpsInstallInfoService.updateCarInfo(orderIds);
        return Result.success("success");
    }
    /**
     * GPS安装回调定时任务
     */
    @ApiOperation(value = "GPS安装回调定时任务")
    @GetMapping("/gpsInstallCallbackScheduledTasks")
    public Result<Boolean> gpsInstallCallbackScheduledTasks() {
        return Result.success(gpsService.gpsInstallCallbackScheduledTasks());
    }
    /**
     * GPS GPS拆除回调
     *
     * @param code 码值
     * @param hcxx HCXX
     * @param djbh DJBH
     * @return {@link Result }<{@link String }>
     */
    @ApiOperation(value = "GPS拆除回调")
    @PostMapping(value = "/gpsDismantleCallback", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Result<String> gpsDismantleCallback(@RequestParam("code") String code,
                                               @RequestParam("hcxx") String hcxx,
                                               @RequestParam("djbh") String djbh) {
        log.info("GpsController.gpsDismantleCallback: code={}, hcxx={}, djbh={}", code, hcxx, djbh);
        // 手动解析 hcxx JSON 字符串
        GpsDismantleCallbackVO gpsDismantleCallbackVO = new GpsDismantleCallbackVO();

        ObjectMapper objectMapper = new ObjectMapper();
        GpsDismantleCallbackVO.Hcxx hcxxVO = null;
        try {
            hcxxVO = objectMapper.readValue(hcxx, GpsDismantleCallbackVO.Hcxx.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("解析 hcxx 失败", e);
        }
        gpsDismantleCallbackVO.setHcxx(hcxxVO);
        if(code!= null && !code.isEmpty()){
            gpsDismantleCallbackVO.setCode(Integer.valueOf(code));
        }
        gpsDismantleCallbackVO.setDjbh(djbh);
        log.info("GpsController.gpsDismantleCallback: gpsDismantleCallbackVO: {}", JSONUtil.toJsonStr(gpsDismantleCallbackVO));
        return Result.success(gpsService.gpsDismantleCallback(gpsDismantleCallbackVO));
    }
    @ApiOperation(value = "谛听补充数据")
    @PostMapping("/ditingSupplementalData")
    public Result<List<Integer>> ditingSupplementalData(@RequestBody List<Integer> orderIds) {
        return Result.success(gpsInstallInfoService.supplementalData(orderIds));
    }

    /**
     * 车晓GPS安装回调
     */
    @ApiOperation(value = "车晓GPS安装/拆除回调")
    @PostMapping(value = "/cheXiaoGpsCallback", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Result<PushGongdanProgressVO> cheXiaoGpsCallback(@RequestBody String json) {
        return Result.success(gpsService.cheXiaoGpsCallback(json));
    }
    /**
     * 车晓GPS修改工单
     */
    @ApiOperation(value = "车晓GPS修改工单")
    @PostMapping(value = "/updateCheXiaoGongdan")
    public Result<Boolean> updateCheXiaoGongdan(@RequestBody UpdateGongdanDTO dto ,@CurrentUser LoginUser loginUser) {
        return Result.success(gpsService.updateCheXiaoGongdan(dto,loginUser));
    }
    /**
     * 车晓GPS取消工单
     */
    @ApiOperation(value = "车晓GPS取消工单")
    @PostMapping(value = "/cancelCheXiaoGongdan")
    public Result<Boolean> cancelCheXiaoGongdan(@RequestBody CancelGongdanDTO dto) {
        return Result.success(gpsService.CancelCheXiaoGongdanDTO(dto));
    }
    /**
     * 车派达取消安装
     */
    @ApiOperation(value = "车派达取消安装")
    @PostMapping(value = "/cancelCarPaiDaInstall")
    public Result<Boolean> cancelCarPaiDaInstall(@RequestBody CancelCarPaiDaInstallDTO dto) {
        return Result.success(gpsService.cancelCarPaiDaInstall(dto));
    }
//    /**
//     * GPS补充子账号数据
//     */
//    @ApiOperation(value = "GPS补充子账号数据")
//    @PostMapping("/gpsSupplementalData")
//    public Result<List<Integer>> gpsSupplementalData(@RequestBody List<Integer> orderIds) {
//        return Result.success(gpsInstallInfoService.gpsSupplementalData(orderIds));
//    }
    /**
     * GPS补充子账号数据
     */
    @ApiOperation(value = "GPS委外补充子账号数据")
    @PostMapping("/outsourcing/gpsSupplementalData")
    public Result<List<Integer>> outsourcingGpsSupplementalData(@RequestBody List<Integer> orderIds) {
        return Result.success(gpsInstallInfoService.outsourcingGpsSupplementalData(orderIds));
    }
    /**
     * GPS安装回调获取图片数据
     */
    @ApiOperation(value = "GPS安装回调获取图片数据")
    @PostMapping("/gpsInstallCallbackForPics")
    public Result<GpsPicVO> gpsInstallCallbackForPics(@RequestBody GpsInstallCallbackVO gpsInstallCallbackDTO) {
        return Result.success(gpsService.gpsInstallCallbackForPics(gpsInstallCallbackDTO));
    }
    @PostMapping("/updateBill")
    public Result<Boolean> updateBill(@RequestBody Integer orderId) {
        return Result.success(gpsInstallInfoService.updateBill(orderId));
    }
}
