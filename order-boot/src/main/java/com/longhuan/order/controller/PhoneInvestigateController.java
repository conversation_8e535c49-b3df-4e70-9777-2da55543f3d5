package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.AddPhoneInvestigateDTO;
import com.longhuan.order.pojo.vo.PhoneInvestigateVO;
import com.longhuan.order.service.PhoneInvestigateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "电调记录")
@RestController
@RequestMapping("/api/v1/phone/investigate")
@RequiredArgsConstructor
public class PhoneInvestigateController {

    private final PhoneInvestigateService phoneInvestigateService;

    @ApiOperation(value = "添加电调记录")
    @PostMapping("/add")
    Result<Boolean> savePhoneInvestigate(@RequestBody @Validated AddPhoneInvestigateDTO addPhoneInvestigateDTO) {
        return Result.success(phoneInvestigateService.savePhoneInvestigate(addPhoneInvestigateDTO));
    }

    @ApiOperation(value = "电调记录列表")
    @GetMapping("/list")
    Result<List<PhoneInvestigateVO>> getPhoneInvestigateList(@RequestParam("orderId") Integer orderId) {
        return Result.success(phoneInvestigateService.getPhoneInvestigateList(orderId));
    }
}
