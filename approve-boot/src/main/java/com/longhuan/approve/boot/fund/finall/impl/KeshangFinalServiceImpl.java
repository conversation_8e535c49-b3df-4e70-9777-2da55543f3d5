package com.longhuan.approve.boot.fund.finall.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.longhuan.approve.boot.fund.finall.FundFinalApiStrategy;
import com.longhuan.approve.boot.fund.keshang.KeShangService;
import com.longhuan.approve.boot.fund.lanhai.LanHaiService;
import com.longhuan.approve.boot.mapper.FinalFundInfoMapper;
import com.longhuan.approve.boot.pojo.dto.FundFinalBaseDTO;
import com.longhuan.approve.boot.pojo.dto.lanhai.LanHaiResult;
import com.longhuan.approve.boot.pojo.dto.lanhai.response.LanHaiCreditApplyResponse;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.KeShangResponseHead;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.request.KeShangAddedBussinessRequest;
import com.longhuan.approve.boot.pojo.entity.FinalFundInfoEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 梅州客商前期服务实施
 *
 */
@Slf4j
@Service("fund_final_MZ_KE_SHANG")
@RequiredArgsConstructor
public class KeshangFinalServiceImpl implements FundFinalApiStrategy {

    private final KeShangService keShangService;
    private final FinalFundInfoMapper finalFundInfoMapper;

    /**
     * 开始事件
     *
     * @param fundFinalBaseDTO 基金 final base dto
     * @return {@link Boolean }
     */
    @Override
    public Boolean beginEvent(FundFinalBaseDTO fundFinalBaseDTO) {
        return true;
    }

    /**
     * 发送
     *
     * @param fundFinalBaseDTO 基金 final base dto
     */
    @Override
    public void send(FundFinalBaseDTO fundFinalBaseDTO) {
        log.info("KeshangFinalServiceImpl.send begin orderId:{} fundFinalBaseDTO:{}", fundFinalBaseDTO.getOrderId(), JSONUtil.toJsonStr(fundFinalBaseDTO));
        KeShangResponseHead result = keShangService.keShangAddedBusiness(fundFinalBaseDTO);
        log.info("KeshangFinalServiceImpl.send end orderId:{} result:{}", fundFinalBaseDTO.getOrderId(), JSONUtil.toJsonStr(result));

    }


    /**
     * 结束 事件
     *
     * @param fundFinalBaseDTO 基金 final base dto
     * @return {@link Boolean }
     */
    @Override
    public Boolean endEvent(FundFinalBaseDTO fundFinalBaseDTO) {

        return true;
    }
}
