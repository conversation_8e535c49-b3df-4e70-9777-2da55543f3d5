package com.longhuan.approve.boot.controller;


import com.longhuan.approve.api.pojo.dto.OnlineMortgageGetAreaInfoDTO;
import com.longhuan.approve.api.pojo.vo.OnlineMortgageGetAreaInfoVO;
import com.longhuan.approve.api.pojo.vo.YingFengApiResult;
import com.longhuan.approve.boot.fund.yingfeng.YingFengService;
import com.longhuan.approve.boot.pojo.dto.FundMortgageCommonDTO;
import com.longhuan.approve.boot.pojo.vo.FundMortgageCommonVO;
import com.longhuan.approve.boot.service.FundMortgageCommonService;
import com.longhuan.common.core.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Api(tags = "资方抵押公共")
@RestController
@RequestMapping("/api/v1/fund/mortgageCommon")
@RequiredArgsConstructor
public class FundMortgageCommonController {

    private final FundMortgageCommonService fundMortgageCommonService;
    private final YingFengService yingFengService;


    /**
     * 查询盈峰线上抵押枚举值
     */
    @ApiOperation("查询盈峰线上抵押地区")
    @PostMapping("/queryMortgageArea")
    public Result<YingFengApiResult<List<OnlineMortgageGetAreaInfoVO>>> queryMortgageArea(@RequestBody OnlineMortgageGetAreaInfoDTO dto) {
        return   Result.success(yingFengService.queryMortgageArea(dto));
    }

    /**
     * 抵押下单
     */
    @PostMapping("createMortgage")
    public Result<FundMortgageCommonVO> mortgageStateCreate(@RequestBody FundMortgageCommonDTO fundMortgageCommonDTO) {
        return Result.success(fundMortgageCommonService.createMortgage(fundMortgageCommonDTO));
    }



    /**
     * 抵押查询
     */
    @PostMapping("queryById")
    public Result<FundMortgageCommonVO> mortgageStateQuery(@RequestBody FundMortgageCommonDTO fundMortgageCommonDTO) {
        return Result.success(fundMortgageCommonService.mortgageStateQuery(fundMortgageCommonDTO));
    }
}
