package com.longhuan.order;

import com.longhuan.risk.pojo.dto.xueli.QueryVerifyV4DTO;

import com.longhuan.order.service.XueliService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = OrderApplication.class)
public class XueliTest {

    @Resource
    private XueliService xueliService;
    @Test
    public void test() {
        String resResults = xueliService.getEducation(new QueryVerifyV4DTO().setName("王元青").setIdNo("330122198102212239"));

        System.out.println("queryVerifyV4VO = " + resResults);
    }
}
