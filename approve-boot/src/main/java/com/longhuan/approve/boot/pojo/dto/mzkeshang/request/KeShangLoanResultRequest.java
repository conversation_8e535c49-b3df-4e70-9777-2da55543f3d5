package com.longhuan.approve.boot.pojo.dto.mzkeshang.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 050300 放款结果通知
 */
@Data
@Accessors(chain = true)
public class KeShangLoanResultRequest {

    /**
     * 贷款申请编号
     * 规则：平台号（4位大写字母，不足4位补X）+渠道号（4位大写字母，不足4位补X）+
     * 请求时间（14位yyyyMMddHHMISS）+序号（8位）
     */
    @JsonProperty("LoanAplyNO")
    private String loanAplyNO;

    /**
     * 放款结果
     * 1-放款成功
     * 0-放款失败
     */
    @JsonProperty("LndngRslt")
    private String lndngRslt;

    /**
     * 银行贷款号
     */
    @JsonProperty("BnkLoanNO")
    private String bnkLoanNO;

    /**
     * 放款时间
     * 格式：YYYYMMDD，放款成功才提供
     * 是否必填：否
     */
    @JsonProperty("LndngDt")
    private String lndngDt;

    /**
     * 实际放款金额
     * 放款成功才提供
     *   是否必填：否
     */
    @JsonProperty("LndngActlAmt")
    private String lndngActlAmt;

    /**
     * 放款失败原因
     * 是否必填：否
     */
    @JsonProperty("LndngFailRsn")
    private String lndngFailRsn;

    /**
     * 一次性手续费
     * 是否必填：否
     */
    @JsonProperty("LndngPcdrFee")
    private String lndngPcdrFee;
}
