package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.DictTreeVO;
import com.longhuan.common.redis.pojo.DictVO;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.service.DictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * Dict 控制器
 *
 * <AUTHOR>
 * @date 2024/07/24
 */
@Api(tags = "词典接口")
@RestController
@RequestMapping("/api/v1/dict")
@RequiredArgsConstructor
public class DictController {
    private final DictService dictService;

    /**
     * 刷新
     *
     * @param dictDTO 字典 dto
     * @return {@link Result }<{@link List }<{@link DictVO }>>
     */
    @ApiOperation(value = "获取词典列表")
    @PostMapping("/refresh")
    public Result<List<DictVO>> refresh(@Validated @RequestBody DictRefreshDTO dictDTO) {
        return Result.success(dictService.refresh(dictDTO));
    }

    /**
     * 获取 Dict 信息
     *
     * @param dictDTO 字典 dto
     * @return {@link Result }<{@link DictVO }>
     */
    @ApiOperation(value = "获取词典信息")
    @PostMapping("/info")
    public Result<Map<String, DictVO>> getDictInfo(@Validated @RequestBody DictDTO dictDTO) {
        return Result.success(dictService.getDictInfo(dictDTO));
    }

    /**
     * 获取 dict 标签
     *
     * @param dictLabelDTO dict 标签 dto
     * @return {@link Result }<{@link String }>
     */
    @ApiOperation(value = "获取词典标签")
    @PostMapping("/label")
    public Result<String> getDictLabel(@Validated @RequestBody DictLabelDTO dictLabelDTO) {
        return Result.success(dictService.getDictLabel(dictLabelDTO.getCode(), dictLabelDTO.getValue()));
    }

    /**
     * 导入词典
     *
     * @param importDictDTO import dict dto
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "导入词典")
    @PostMapping("/import")
    public Result<Boolean> importDict(@Validated @RequestBody List<ImportDictDTO> importDictDTO) {
        return Result.success(dictService.importDict(importDictDTO));
    }

    /**
     * 获取 dict tree
     *
     * @param dictDTO dict dto
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "获取词典列表")
    @PostMapping("/getDictTree")
    public Result<List<DictTreeVO>> getDictTree(@Validated @RequestBody DictRefreshDTO dictDTO) {
        return Result.success(dictService.getDictTree(dictDTO));
    }

    /**
     * 更新 dict
     *
     * @param dictUpdateDTO dict update dto
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "更新词典")
    @PostMapping("/updateDict")
    public Result<Boolean> updateDict(@Validated @RequestBody DictUpdateDTO dictUpdateDTO) {
        return Result.success(dictService.updateDict(dictUpdateDTO));
    }

    /**
     * 添加字典 dict
     *
     * @param dictInsertDTO dict insert dto
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "添加字典")
    @PostMapping("/insertDict")
    public Result<Boolean> insertDict(@Validated @RequestBody DictInsertDTO dictInsertDTO) {
        return Result.success(dictService.insertDict(dictInsertDTO));
    }

}
