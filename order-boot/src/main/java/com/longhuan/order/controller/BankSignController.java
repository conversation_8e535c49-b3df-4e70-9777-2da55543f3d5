package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.vo.accountBank.BankDeductQueryVO;
import com.longhuan.order.pojo.vo.accountBank.BankDeductVO;
import com.longhuan.order.pojo.dto.accountBank.BankPayDeductQueryDTO;
import com.longhuan.order.pojo.dto.accountBank.BankPayDeductDTO;
import com.longhuan.order.service.BankSignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Api(tags = "银行划扣接口")
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/api/v1/bank")
public class BankSignController {

    private final BankSignService bankSignService;

    @ApiOperation(value = "划扣申请")
    @PostMapping("/deduct/apply")
    public Result<BankDeductVO> deductApply(@RequestBody BankPayDeductDTO dto) {
        log.info("BankSignController.deductApply start dto : {}", dto);
        BankDeductVO result = bankSignService.deductApply(dto);
        log.info("BankSignController.deductApply end result : {}", result);
        return Result.success(result);
    }

    @ApiOperation(value = "划扣查询")
    @PostMapping("/deduct/query")
    public Result<BankDeductQueryVO> deductQuery(@RequestBody BankPayDeductQueryDTO dto) {
        log.info("BankSignController.deductQuery start dto : {}", dto);
        BankDeductQueryVO result = bankSignService.deductQuery(dto);
        log.info("BankSignController.deductQuery end result : {}", result);
        return Result.success(result);
    }
} 