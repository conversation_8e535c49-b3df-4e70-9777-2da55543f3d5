package com.longhuan.order.converter;

import com.longhuan.approve.api.pojo.vo.FundRepayCalcVO;
import com.longhuan.order.pojo.dto.FundEarlyRepaymentCalcDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;


@Mapper(componentModel = "spring")
public interface FundDeductConverter {

    FundDeductConverter INSTANCE = Mappers.getMapper(FundDeductConverter.class);

    @Mapping(target = "fundSettleDTO.principal", source = "principal")
    @Mapping(target = "fundSettleDTO.penalty", source = "penalty")
    @Mapping(target = "fundSettleDTO.interest", source = "interest")
    @Mapping(target = "fundSettleDTO.fine", source = "fine")
    @Mapping(target = "fundSettleDTO.repayAmt", source = "repayAmt")
    @Mapping(target = "fundSettleDTO.comInterest", source = "comInterest")
    @Mapping(target = "fundSettleDTO.period", source = "period")
    @Mapping(target = "fundSettleDTO.fundRepayAcct", source = "fundRepayAcct")
    @Mapping(target = "fundSettleDTO.odFeeAmt", source = "odFeeAmt")
    @Mapping(target = "fundSettleDTO.guaraFeeAmt", source = "guaraFeeAmt")
    @Mapping(target = "fundSettleDTO.guaraFeeOdAmt", source = "guaraFeeOdAmt")
    FundEarlyRepaymentCalcDTO fundCalc2FundDeductCalc(FundRepayCalcVO calcVO);

}
