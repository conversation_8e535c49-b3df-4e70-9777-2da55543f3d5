package com.longhuan.order.statemachine.action;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.common.core.enums.RoleEnum;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.DistributeAreaMapper;
import com.longhuan.order.mapper.OrderApproveDistributeMapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.mapper.OrderNodeRecordMapper;
import com.longhuan.order.pojo.entity.DistributeAreaEntity;
import com.longhuan.order.pojo.entity.OrderApproveDistributeEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.service.OrderSendMessage;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.user.enums.TodoInfoEnums;
import com.longhuan.user.pojo.dto.TodoInfoMessageDTO;
import com.longhuan.user.pojo.dto.getUserIdByStoreIdAndRoleIdDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 状态转移动作，在转移进行的时候触发的动作
 */
@Slf4j
@Component
public class CustomerAppointmentFinishAction extends OrderNodeAction implements Action<States, Events> {

private final UserFeign userFeign;
private final OrderApproveDistributeMapper orderApproveDistributeMapper;
private final DistributeAreaMapper distributeAreaMapper;

    public CustomerAppointmentFinishAction(OrderInfoMapper orderInfoMapper,
                                           OrderNodeRecordMapper orderNodeRecordMapper, UserFeign userFeign, OrderApproveDistributeMapper orderApproveDistributeMapper, DistributeAreaMapper distributeAreaMapper
    ) {
        super(orderInfoMapper, orderNodeRecordMapper);

        this.userFeign = userFeign;
        this.orderApproveDistributeMapper = orderApproveDistributeMapper;
        this.distributeAreaMapper = distributeAreaMapper;
    }

    @Override
    public void execute(StateContext<States, Events> context) {


        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();

        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);


        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event,
                currentNode, nextNode);




        // 1. 修改订单表状态
        if (Boolean.TRUE.equals(target.getId().getValid())) {
            saveOrderNode(orderId, nextNode);
        }

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);
        // 3.送入待分配任务池
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        DistributeAreaEntity distributeAreaEntity = distributeAreaMapper.selectOne(new LambdaQueryWrapper<DistributeAreaEntity>()
                .eq(DistributeAreaEntity::getStoreId, orderInfoEntity.getDeptId())
                .eq(DistributeAreaEntity::getServiceDispatch, 1)
                .eq(DistributeAreaEntity::getDeleteFlag, 0)
                .orderByDesc(DistributeAreaEntity::getCreateTime)
                .last("limit 1"));
        if (distributeAreaEntity != null) {
            Long count = orderApproveDistributeMapper.selectCount(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                    .eq(OrderApproveDistributeEntity::getOrderNumber, orderInfoEntity.getOrderNumber())
                    .eq(OrderApproveDistributeEntity::getOrderId, orderId)
                    .eq(OrderApproveDistributeEntity::getNode, nextNode)
                    .in(OrderApproveDistributeEntity::getState, 0, 1, 2, 5));
            if (count==0) {
                orderApproveDistributeMapper.insert(new OrderApproveDistributeEntity()
                        .setOrderNumber(orderInfoEntity.getOrderNumber())
                        .setOrderId(orderId)
                        .setNode(nextNode)
                        .setNodeName(States.getByNode(nextNode).getDesc())
                        .setSource(0)
                        .setStoreId(orderInfoEntity.getDeptId())
                        .setTitle(orderInfoEntity.getCustomerName() + "-" + orderInfoEntity.getVehicleNumber() + "-" + orderInfoEntity.getStoreName())
                        .setState(0)
                        .setCustomerName(orderInfoEntity.getCustomerName())
                        .setCustomerPhone(orderInfoEntity.getCustomerPhone())
                        .setManagerId(orderInfoEntity.getManagerId())
                        .setStoreName(orderInfoEntity.getStoreName())
                        .setRegionName(orderInfoEntity.getRegionName())
                        .setVehicleNumber(orderInfoEntity.getVehicleNumber())
                        .setStepDispose(currentNode <= nextNode ? 0 : 1));
            }
        } else {
            List<Integer> userIds = userFeign.getUserIdByStoreIdAndRoleId(new getUserIdByStoreIdAndRoleIdDTO().setDeptId(orderInfoEntity.getDeptId()).setRoleId(RoleEnum.CUSTOMER_SERVICE_SPECIALIST.getId())).getData();
            List<TodoInfoMessageDTO.TodoUser> userDto = userFeign.searchUserNameBatch(userIds).getData().stream().map(e -> new TodoInfoMessageDTO.TodoUser().setName(e.getName()).setJobNumber(e.getJobNumber()).setPhoneNumber(e.getMobile())).toList();

            TodoInfoMessageDTO todoInfoMessageDTO = new TodoInfoMessageDTO()
                    .setOrderNumber(orderInfoEntity.getOrderNumber())
                    .setOrderId(orderInfoEntity.getId())
                    .setNode(TodoInfoEnums.getByNode(nextNode))
                    .setSourceType(0)
                    .setRemark("订单：" + orderInfoEntity.getOrderNumber() + "待处理")
                    .setState(0)
                    .setTitle(orderInfoEntity.getCustomerName() + "-" + orderInfoEntity.getVehicleNumber() + "-" + orderInfoEntity.getStoreName())
                    .setAccessUrl(null)
                    .setTodoUserList(userDto)
                    .setCustomerName(orderInfoEntity.getCustomerName())
                    .setCustomerPhone(orderInfoEntity.getCustomerPhone())
                    .setManagerId(orderInfoEntity.getManagerId())
                    .setStoreName(orderInfoEntity.getStoreName())
                    .setRegionName(orderInfoEntity.getRegionName())
                    .setVehicleNumber(orderInfoEntity.getVehicleNumber());
            log.info("CustomerAppointmentFinishAction send todoMessage:{}", todoInfoMessageDTO);
            userFeign.dealMessage(todoInfoMessageDTO);
        }
    }


}