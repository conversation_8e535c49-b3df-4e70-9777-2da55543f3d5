package com.longhuan.order.settleCalculation.service.impl.strategy.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.common.core.enums.OrderFeeDetailExpandTypeEnum;
import com.longhuan.common.core.enums.PayApplicationNodeEnums;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.enums.FundRepayStatusEnum;
import com.longhuan.order.mapper.OrderPayApplicationMapper;
import com.longhuan.order.mapper.RepurchaseRepaymentInfoMapper;
import com.longhuan.order.pojo.dto.SettleCalculationContext;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.entity.OrderPayApplicationInfoEntity;
import com.longhuan.order.pojo.entity.RepurchaseRepaymentInfoEntity;
import com.longhuan.order.pojo.vo.SettleCalculationVO;
import com.longhuan.order.settleCalculation.service.impl.strategy.SettleCalculationStrategy;
import com.longhuan.order.settleCalculation.service.impl.strategy.impl.Annotation.SupportSettleType;
import com.longhuan.order.settleCalculation.service.impl.strategy.impl.enums.SettleType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 委外赎回策略
 */
@Component
@SupportSettleType(SettleType.OUTSOURCING_REDEEM)
@RequiredArgsConstructor
public class OutsourcingRedeemStrategy implements SettleCalculationStrategy {
    private final OrderPayApplicationMapper orderPayApplicationMapper;
    private final RepurchaseRepaymentInfoMapper repurchaseRepaymentInfoMapper;

    @Override
    public SettleCalculationVO calculate(SettleCalculationContext context) {
        //委外赎回封闭期内仅适用于违约金模式
        context.setClosedPeriodRepayMode(2);
        //委外赎回用赎回金额-单期代偿金额-已还部分作为还款金额
        Assert.notNull(context.getRedeemAmount(), () -> new BusinessException("赎回金额不能为空"));
        Assert.notNull(context.getRedeemSingleRepay(), () -> new BusinessException("单期代偿金额不能为空"));
        Assert.notNull(context.getRedeemPaidAmount(), () -> new BusinessException("已还部分不能为空"));
        BigDecimal fundSettlementAmount = context.getFundSettlementAmount();
        context.setFundSettlementAmount(context.getRedeemAmount().add(context.getRedeemSingleRepay()).subtract(context.getRedeemPaidAmount()));
        //委外封闭期内特殊策略封闭期不等于六期情况下结清金额等价于通用结清金额策略封闭期内违约金模式还款期数大于封闭期一半时策略
        int paidPeriods = context.getPaidPeriods();
        if (context.getClosedPeriod() != 6 && !context.getFundName().equals("长安新生") && context.getPaidPeriods() < context.getClosedPeriod()) {
            //直接设置已还期数等于封闭期
            context.setPaidPeriods(context.getClosedPeriod()-1);
        }
        SettleCalculationVO settleCalculationVO;
        if (Objects.equals(context.getClosedPeriodRepayMode(), 0)) {
            // 结清金额=资方结清金额
             settleCalculationVO = SettleCalculationMethod.directSettlement(context.getFundSettlementAmount());
        }else {        //调用通用结算策略
            settleCalculationVO = SettleCalculationMethod.defaultSettlementPolicy(context);
        }
        context.setPaidPeriods(paidPeriods);
        context.setFundSettlementAmount(fundSettlementAmount);
        return settleCalculationVO;
    }

    @Override
    public void buildLHContext(SettleCalculationContext context, OrderInfoEntity orderInfoEntity, LocalDate payDate) {
        context.setLoanAmount(orderInfoEntity.getApprovalAmount());
        //赎回金额
        orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                        .eq(OrderPayApplicationInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)
                        .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                        .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS))
                .stream().map(OrderPayApplicationInfoEntity::getPayeeAmount)
                .reduce(BigDecimal::add).ifPresentOrElse(context::setRedeemAmount, () -> context.setRedeemAmount(BigDecimal.ZERO));
        //todo 单期代偿金额
        orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                        .eq(OrderPayApplicationInfoEntity::getOrderId, orderInfoEntity.getId())
                        .in(OrderPayApplicationInfoEntity::getFeeType, List.of(OrderFeeDetailExpandTypeEnum.SINGLE_PERIOD_COMPENSATION))
                        .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                        .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS))
                .stream().map(OrderPayApplicationInfoEntity::getPayeeAmount)
                .reduce(BigDecimal::add).ifPresentOrElse(context::setRedeemSingleRepay, () -> context.setRedeemSingleRepay(BigDecimal.ZERO));
        //已还金额
//        orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
//                        .eq(OrderPayApplicationInfoEntity::getOrderId, orderInfoEntity.getId())
//                        .in(OrderPayApplicationInfoEntity::getFeeType, List.of(OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT))
//                        .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
//                        .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)
//                        .eq(OrderPayApplicationInfoEntity::getPayeeType, 1))
//                .stream().map(e -> e.getPayeeAmount().add(ObjUtil.defaultIfNull(e.getReductionAmount(), BigDecimal.ZERO)).add(ObjUtil.defaultIfNull(e.getDeposit(), BigDecimal.ZERO)))
//                .reduce(BigDecimal::add).ifPresentOrElse(context::setRedeemPaidAmount, () -> context.setRedeemPaidAmount(BigDecimal.ZERO));
        List<RepurchaseRepaymentInfoEntity> repurchaseRepaymentInfoEntities = repurchaseRepaymentInfoMapper.selectList(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderInfoEntity.getId())
                .eq(RepurchaseRepaymentInfoEntity::getFundId, orderInfoEntity.getFundId())
                .eq(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0));
        //实还总额
        repurchaseRepaymentInfoEntities.stream().map(RepurchaseRepaymentInfoEntity::getActuallyPrincipal)
                .reduce(BigDecimal::add).ifPresentOrElse(e -> context.setRemainingPrincipal(context.getLoanAmount().subtract(e)), () -> context.setRemainingPrincipal(context.getLoanAmount()));
        //实还期数
        context.setPaidPeriods(repurchaseRepaymentInfoEntities.size());
        //当期期数
        context.setSettlePaidPeriods(repurchaseRepaymentInfoEntities.size()+1);
    }
}
