package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.FundSignDetailDTO;
import com.longhuan.order.pojo.dto.FundSignInfoDTO;
import com.longhuan.order.pojo.entity.FundSignDetailEntity;
import com.longhuan.order.pojo.entity.FundSignInfoEntity;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 资方签约信息
 *
 */
@Mapper(componentModel = "spring")
public interface FundSignInfoConverter {

    FundSignInfoEntity dto2Entity(FundSignInfoDTO dto);

    List<FundSignDetailEntity> detailDtoList2DetailEntityList (List<FundSignDetailDTO> dto);
}
