package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.data.api.pojo.dto.KingdeeOutsourcingOrderDTO;
import com.longhuan.data.api.pojo.vo.DigitizedOverdueOrdersVO;
//import com.longhuan.data.api.pojo.vo.KindeeOrderCaseBaseInfoVO;
import com.longhuan.data.api.pojo.vo.RepaymentInfoVO;
import com.longhuan.data.api.pojo.vo.RepaymentOverdueStatusVO;
import com.longhuan.order.kingdee.pojo.KingdeeOrderInfoDTO;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.dto.digitalize.DigitalizeSettleList;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.checkerframework.checker.units.qual.C;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.fasterxml.jackson.databind.cfg.CoercionInputShape.Array;

/**
 * <AUTHOR>
 * @date 2025/01/07
 */
@Api(tags = "委外、法诉相关接口")
@RestController
@RequestMapping("/api/v1/caseInfo")
@RequiredArgsConstructor
public class CaseInfoController {
    private final CaseInfoService caseInfoService;
    private final LawsuitService lawsuitService;
    private final OutsourcingService outsourcingService;
    private final OutsourcingAdvanceService outsourcingAdvanceService;
    private final OutsourcingReductionService outsourcingReductionService;
    private final OutsourcingContractService outsourcingContractService;
    private final KingdeeOutsourcingService kingdeeOutsourcingService;
    private final OutsourcingSettlementTrialCalculationsService outsourcingSettlementTrialCalculationsService;
    private final CaseUsingAssetsService caseUsingAssetsService;
    private final CaseTransferGpsAccountService caseTransferGpsAccountService;

    @ApiOperation("获取审批申请委外的订单列表")
    @PostMapping("/approveList")
    public Result<Page<CaseInfoListVO>> getApproveCaseInfolist(@RequestBody CaseInfoListDTO caseInfoListDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(outsourcingAdvanceService.getApproveCaseInfolist(caseInfoListDTO, loginUser));
    }
//    @ApiOperation("获取审批申请委外的订单列表")
//    @PostMapping("/approveList")
//    public Result<Page<CaseInfoListVO>> getApproveCaseInfolist(@RequestBody CaseInfoListDTO caseInfoListDTO, @CurrentUser LoginUser loginUser) {

    /// /        LoginUser newLoginUser = new LoginUser();
    /// /        newLoginUser.setUserId(2015);
    /// /        newLoginUser.setUserName("***********");
    /// /        newLoginUser.setName("路红历");
    /// /        newLoginUser.setMobile("***********");
    /// /        newLoginUser.setJobNumber("LH310329");
    /// /        newLoginUser.setDeptIds(Arrays.asList(108, 48));
    /// /        newLoginUser.setRoleIds(Arrays.asList(118));
    /// /        newLoginUser.setProxyUserIds(Arrays.asList());
    /// /        newLoginUser.setRoleId(118);
    /// /        newLoginUser.setScopes("system:pc;data:region;system:vehicle");
    /// /        newLoginUser.setStatus(0);
    /// /        newLoginUser.setDeleteFlag(0);
    /// /        newLoginUser.setUserType(5);
    /// /        newLoginUser.setToken("64de88af-358f-4476-9c42-49fd99226c4f");
    /// /        newLoginUser.setOrigin("pc");
    /// /        newLoginUser.setRenewal(0);
//        LoginUser loginUser1 = new LoginUser();
//        loginUser1.setUserId(1989);
//        loginUser1.setUserName("13509424972");
//        loginUser1.setName("陶艳");
//        loginUser1.setMobile("13509424972");
//        loginUser1.setJobNumber("LH100491");
//        loginUser1.setDeptIds(Arrays.asList(70, 45));
//        loginUser1.setRoleIds(Arrays.asList(118));
//        loginUser1.setProxyUserIds(Arrays.asList());
//        loginUser1.setRoleId(118);
//        loginUser1.setScopes("system:pc;data:region;system:vehicle");
//        loginUser1.setStatus(0);
//        loginUser1.setDeleteFlag(0);
//        loginUser1.setUserType(5);
//        loginUser1.setToken("d2d57eff-372a-44ec-a72c-f6c8b5d9bbc2");
//        loginUser1.setOrigin("pc");
//        loginUser1.setRenewal(0);
//        return Result.success(outsourcingAdvanceService.getApproveCaseInfolist(caseInfoListDTO, loginUser1));
//    }
    @ApiOperation("逾期180天以上的订单无处理中的法诉任务，无处理中的委外任务 添加到案件信息表")
    @PostMapping("/addCaseInfo")
    public Result<Boolean> addCaseInfo() {
        return Result.success(caseInfoService.addCaseInfo());
    }

    @ApiOperation("获取申请委外的订单列表")
    @PostMapping("/list")
    public Result<Page<CaseInfoListVO>> getCaseInfolist(@RequestBody CaseInfoListDTO caseInfoListDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(outsourcingService.getCaseInfolist(caseInfoListDTO, loginUser));
    }
//    @ApiOperation("获取申请委外的订单列表")
//    @PostMapping("/list")
//    public Result<Page<CaseInfoListVO>> getCaseInfolist(@RequestBody CaseInfoListDTO caseInfoListDTO, @CurrentUser LoginUser loginUser) {
//        LoginUser loginUser1 = new LoginUser();
//        loginUser1.setUserId(2081);
//        loginUser1.setUserName("18701878777");
//        loginUser1.setName("申永");
//        loginUser1.setMobile("18701878777");
//        loginUser1.setJobNumber("LH300980");
//        loginUser1.setDeptIds(Arrays.asList(130));
//        loginUser1.setRoleIds(Arrays.asList(29));
//        loginUser1.setProxyUserIds(Arrays.asList(2081, 10835, 1293, 10792, 1295, 3799, 1317, 10455, 1291, 1309, 3416, 1305, 1311, 1307, 10529, 1848, 1304, 10725, 1312, 1299, 10650, 3265, 1843, 1315, 1318, 1316, 1303, 10531, 1847, 1294, 1302, 1290, 10561, 1320, 1296, 1844, 1306, 1301, 1842, 1322, 1297, 1845, 1298, 1321, 10532, 1313, 1846, 1292, 10791, 3564, 1300, 1319, 9422, 9895, 10211, 10232, 10234, 10240, 10250, 10251, 3543, 3170, 314, 313, 11116, 11182, 11183, 11184, 11185, 11046, 11091, 11361, 11346, 10334, 11586, 11587, 11588, 1029));
//        loginUser1.setRoleId(29);
//        loginUser1.setScopes("system:common;system:h5;system:pc;system:vehicle;system:tel");
//        loginUser1.setStatus(0);
//        loginUser1.setDeleteFlag(0);
//        loginUser1.setUserType(5);
//        loginUser1.setToken("bef6a299-c643-46d1-a2ad-c50e86a27d55");
//        loginUser1.setOrigin("pc");
//        loginUser1.setRenewal(0);
//        return Result.success(outsourcingService.getCaseInfolist(caseInfoListDTO, loginUser1));
//    }


    @ApiOperation("获取申请委外的维护订单列表")
    @PostMapping("/maintenance/list")
    public Result<Page<CaseInfoListVO>> getMaintenancelist(@RequestBody CaseInfoListDTO caseInfoListDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(outsourcingAdvanceService.getMaintenancelist(caseInfoListDTO, loginUser));
    }


    //获取放款成功未申请委外的订单列表
    @ApiOperation("获取未申请委外的订单列表")
    @PostMapping("/list/orderPayApply")
    public Result<Page<CaseInfoOrderApplyVO>> getCaseInfoOrderApplyList(@RequestBody CaseInfoListDTO caseInfoListDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(caseInfoService.getCaseInfoOrderApplyList(caseInfoListDTO, loginUser));
    }


    @ApiOperation(value = "资产委外导出")
    @PostMapping("/exportAssetOutSourcing")
    public void exportAssetOutSourcing(@RequestBody CaseInfoListDTO caseInfoListDTO, HttpServletResponse response, @CurrentUser LoginUser loginUser) {
        outsourcingContractService.exportAssetOutSourcing(caseInfoListDTO, response, loginUser);
    }


    //手动添加委外订单
    @ApiOperation(value = "手动添加委外订单")
    @PostMapping("/manualAddCaseInfo")
    public Result<Boolean> manualAddCaseInfo(@RequestBody @Validated AddCaseInfoDTO addCaseInfoDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(caseInfoService.manualAddCaseInfo(addCaseInfoDTO, loginUser));
    }


    /**
     * 资外订单审批
     */

    @ApiOperation(value = "资产委外订单审批")
    @PostMapping("/approveCaseInfo")
    public Result<Boolean> approveCaseInfo(@RequestBody @Validated ApprovalCaseDTO approvalCaseDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(caseInfoService.approveCaseInfo(approvalCaseDTO, loginUser, false));
    }


    /**
     * 资外申请节点信息列表
     */
    @ApiOperation(value = "资产委外申请节点信息列表")
    @PostMapping("/list/caseInfoNodeRecordList")
    public Result<Page<CaseInfoNodeRecordListVO>> getCaseInfoApplyList(@RequestBody @Validated CaseInfoNodeRecordDTO caseInfoNodeRecordDTO) {
        return Result.success(caseInfoService.getCaseInfoApplyList(caseInfoNodeRecordDTO));
    }


    /**
     * 状态更新
     */
    @ApiOperation(value = "资产委外订单状态更新")
    @PostMapping("/updateCaseInfoStatus")
    public Result<Boolean> updateCaseInfoStatus(@RequestBody @Validated CaseInfoStatusDTO caseInfoStatusDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(caseInfoService.updateCaseInfoStatus(caseInfoStatusDTO, loginUser));
    }


    /**
     * 资产委外状态更新记录列表
     */
    @ApiOperation(value = "资产委外状态更新记录列表")
    @GetMapping("/list/caseInfoStatusRecords")
    public Result<Page<CaseInfoStatusRecordsEntity>> getCaseInfoStatusRecordsList(@RequestParam("caseInfoId") Integer caseInfoId) {
        return Result.success(caseInfoService.getCaseInfoStatusRecordsList(caseInfoId));
    }

    /**
     * 资产委外详情
     */
    @ApiOperation(value = "资产委外详情")
    @GetMapping("/detail/caseInfo")
    public Result<CaseInfoDetailVO> getCaseInfoDetail(@RequestParam("caseInfoId") Integer caseInfoId) {
        return Result.success(caseInfoService.getCaseInfoDetail(caseInfoId));
    }


    /**
     * 资产法诉列表
     */
    @ApiOperation(value = "售后法诉诉讼列表")
    @PostMapping("/list/lawsuit")
    public Result<Page<LawsuitApplicationVO>> getLawsuitList(@RequestBody CaseInfoListDTO caseInfoListDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(lawsuitService.getLawsuitList(caseInfoListDTO, loginUser));

    }

    /**
     * 资产法诉综合查询列表
     */
    @ApiOperation(value = "资产法诉综合查询列表")
    @PostMapping("/queryList/lawsuit")
    public Result<Page<LawsuitApplicationVO>> getLawsuitQueryList(@RequestBody CaseInfoListDTO caseInfoListDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(lawsuitService.getLawsuitQueryList(caseInfoListDTO, loginUser));

    }


    /**
     * 资产法诉导出
     */
    @ApiOperation(value = "资产法诉导出")
    @PostMapping("/export/lawsuit")
    public void exportLawsuitList(@RequestBody CaseInfoListDTO caseInfoListDTO, HttpServletResponse response, @CurrentUser LoginUser loginUser) {
        lawsuitService.exportLawsuitList(caseInfoListDTO, response, loginUser);
    }


    /**
     * 法诉状态更新
     */
    @ApiOperation(value = "资产法诉状态更新")
    @PostMapping("/update/lawsuitStatus")
    public Result<Boolean> updateLawsuitStatus(@RequestBody @Validated LawsuitStatusDTO lawsuitStatusDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(lawsuitService.updateLawsuitStatus(lawsuitStatusDTO, loginUser));
    }


    /**
     * 法诉状态更新记录列表
     */
    @ApiOperation(value = "资产法诉状态更新记录列表")
    @GetMapping("/list/lawsuitStatusRecords")
    public Result<Page<LawsuitStatusRecordsEntity>> getLawsuitStatusRecordsList(@RequestParam("lawsuitId") Integer lawsuitId) {
        return Result.success(lawsuitService.getLawsuitStatusRecordsList(lawsuitId));
    }

    /**
     * 售后法诉审批
     */
    @ApiOperation(value = "售后审批")
    @PostMapping("/approve/afterSale")
    public Result<Boolean> approveAfterSale(@RequestBody @Validated ApprovalCaseDTO approvalCaseDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(lawsuitService.approveAfterSale(approvalCaseDTO, loginUser));
    }


    /**
     * 第三方处置公司查看列表
     */
    @ApiOperation(value = "第三方爱财查看列表")
    @PostMapping("/list/thirdParty")
    public Result<Page<LawsuitApplicationVO>> getThirdPartyList(@RequestBody CaseInfoListDTO caseInfoListDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(lawsuitService.getThirdPartyList(caseInfoListDTO, loginUser));
    }

    /**
     * 赎回成功的订单添加到法诉任务池
     */
    @ApiOperation("赎回成功的订单添加到法诉任务池")
    @PostMapping("/add/lawsuitTaskPool")
    public Result<Boolean> addLawsuitTaskPool() {
        return Result.success(caseInfoService.addLawsuitTaskPool());
    }


    /**
     * 添加钉钉审批完成的任务进入委外任务池
     */
    @ApiOperation("添加钉钉审批完成的任务进入委外任务池")
    @GetMapping("/add/updatePreservationStateAndTime")
    public Result<Boolean> updatePreservationStateAndTime() {
        return Result.success(caseInfoService.updatePreservationStateAndTime());
    }

    /**
     * 获取流转部门记录
     */
    @ApiOperation(value = "获取流转部门记录")
    @GetMapping("/list/flowDepartmentRecords")
    public Result<List<CaseCirculationDeptRecordEntity>> getFlowDepartmentRecordsList(@RequestParam("caseNo") String caseNo) {
        return Result.success(caseInfoService.getFlowDepartmentRecordsList(caseNo));
    }

    /**
     * 获取案件回款记录
     */
    @ApiOperation(value = "获取案件回款记录")
    @GetMapping("/list/casePaymentRecords")
    public Result<List<OrderFeeDetailEntity>> getCasePaymentRecordsList(@RequestParam("caseNo") String caseNo) {
        return Result.success(caseInfoService.getCasePaymentRecordsList(caseNo));
    }

    /**
     * 案件转移
     */
    @ApiOperation(value = "案件转移")
    @PostMapping("/transfer/caseInfo")
    public Result<Boolean> transferCaseInfo(@RequestBody @Validated TransferCaseInfoDTO transferCaseInfoDTO) {
        return Result.success(caseInfoService.transferCaseInfo(transferCaseInfoDTO));
    }

    /**
     * 添加电催记录
     */
    @ApiOperation(value = "添加电催记录")
    @PostMapping("/add/callRecord")
    public Result<Boolean> addCallRecord(@RequestBody @Validated AddCallRecordDTO addCallRecordDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(caseInfoService.addCallRecord(addCallRecordDTO, loginUser));
    }

    /**
     * 获取电催记录
     */
    @ApiOperation(value = "获取电催记录")
    @GetMapping("/list/callRecord")
    public Result<List<ElectricReminderRecordEntity>> getCallRecordList(@RequestParam("orderId") Integer orderId) {
        return Result.success(caseInfoService.getCallRecordList(orderId));
    }

    /**
     * 电催坐席获取同步电催售后数据
     *
     * @param overdueOrdersDTO 订单id
     */
    @PostMapping(value = "/overdueOrdersList")
    public Result<Page<OverdueOrdersListVO>> overdueOrdersList(@RequestBody OverdueOrdersDTO overdueOrdersDTO,
                                                               @CurrentUser LoginUser loginUser) {
        return Result.success(caseInfoService.overdueOrdersList(overdueOrdersDTO, loginUser));
    }

    /**
     * 是否同步售后电催
     */
    @ApiOperation(value = "是否同步售后电催")
    @PostMapping("/isSyncAfterSaleCall")
    public Result<Boolean> isSyncAfterSaleCall(@RequestBody @Validated SyncAfterSaleCallDTO syncAfterSaleCallDTO) {
        return Result.success(caseInfoService.isSyncAfterSaleCall(syncAfterSaleCallDTO));
    }

    /**
     * 发起减免
     */
    @ApiOperation(value = "发起减免")
    @PostMapping("/add/reduction")
    public Result<Boolean> addReduction(@RequestBody @Validated AddReductionDTO addReductionDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(caseInfoService.addReduction(addReductionDTO, loginUser));
    }

//    /**
//     * 发起减免
//     */
//    @ApiOperation(value = "发起减免")
//    @PostMapping("/add/reduction")
//    public Result<Boolean> addReduction(@RequestBody @Validated AddReductionDTO addReductionDTO, @CurrentUser LoginUser loginUser) {
//        return Result.success(caseInfoService.addReduction(addReductionDTO, new LoginUser().setUserId(2015)));
//    }


    /**
     * 更新委外订单是否需求重新委派
     */
    @ApiOperation(value = "更新委外订单是否需求重新委派")
    @PostMapping("/update/isNeedReDelegate")
    public Result<Boolean> updateIsNeedReDelegate() {
        return Result.success(caseInfoService.updateIsNeedReDelegate());
    }

    /**
     * 重新委派委外订单
     */
    @ApiOperation(value = "重新委派委外订单")
    @PostMapping("/update/reDelegate")
    public Result<Boolean> updateReDelegate(@RequestBody @Validated UpdateReDelegateDTO reDelegateDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(outsourcingContractService.updateReDelegate(reDelegateDTO, loginUser));
    }


    @ApiOperation(value = "钉钉提前委外申请审批")
    @GetMapping("/outsourcingAdvanceApplicationDingTaskBatch")
    Result<Boolean> outsourcingAdvanceApplicationDingTaskBatch() {
        caseInfoService.batchDingTaskApprove();
        return Result.success(true);
    }

    @ApiOperation(value = "钉钉提前委外申请审批")
    @GetMapping("/deptoutsourcingAdvanceApplicationDingTaskBatch")
    Result<Boolean> deptoutsourcingAdvanceApplicationDingTaskBatch() {
        caseInfoService.testbatchtestDingTaskApprove();
        return Result.success(true);
    }

    /**
     * 减免审批
     */
    @ApiOperation(value = "减免审批")
    @PostMapping("/approve/reduction")
    public Result<Boolean> approveReduction(@RequestBody @Validated ApprovalCaseDTO approvalCaseDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(outsourcingService.approveReduction(approvalCaseDTO, loginUser));
    }


    @ApiOperation(value = "钉钉减免申请审批")
    @GetMapping("/reductionApplicationDingTaskBatch")
    Result<Boolean> reductionApplicationDingTaskBatch() {
        caseInfoService.reductionApplicationDingTaskBatch();
        return Result.success(true);
    }

    @ApiOperation(value = "钉钉资管部减免申请审批")
    @GetMapping("/assetReductionApplicationDingTaskBatch")
    Result<Boolean> assetReductionApplicationDingTaskBatch() {
        outsourcingReductionService.assetReductionApplicationDingTaskBatch();
        return Result.success(true);
    }
    @ApiOperation(value = "钉钉资管部副总减免申请审批")
    @GetMapping("/assetDeputyReductionApplicationDingTaskBatch")
    Result<Boolean> assetDeputyReductionApplicationDingTaskBatch() {
        outsourcingReductionService.assetDeputyReductionApplicationDingTaskBatch();
        return Result.success(true);
    }

    /**
     * 委外维护状态信息更新
     */
    @ApiOperation(value = "委外维护状态信息更新")
    @PostMapping("/update/outsourcingDetails")
    public Result<Boolean> updateOutsourcingDetails(@RequestBody OutsourcingDetailsDTO outsourcingDetailsEntity, @CurrentUser LoginUser loginUser) {
        caseInfoService.updateOutsourcingDetails(outsourcingDetailsEntity, loginUser);
        return Result.success(true);
    }

    /**
     * 获取委外维护信息
     */
    @ApiOperation(value = "获取委外维护信息")
    @GetMapping("/get/outsourcingDetails")
    public Result<OutsourcingDetailsEntity> getOutsourcingDetails(@RequestParam("caseId") Integer caseId) {

        return Result.success(caseInfoService.getOutsourcingDetails(caseId));
    }

    /***
     * 获取委外维护信息记录
     */
    @ApiOperation(value = "获取委外维护信息记录")
    @GetMapping("/get/outsourcingDetailsRecord")
    public Result<Page<OutsourcingDetailsRecordEntity>> getOutsourcingDetailsRecord(@RequestParam("caseId") Integer caseId) {
        return Result.success(caseInfoService.getOutsourcingDetailsRecord(caseId));
    }

    /**
     * 诉讼要素表
     */
    @ApiOperation(value = "诉讼要素表生成")
    @GetMapping("/get/complaintExtraction")
    public Result<Boolean> getComplaintExtraction(@RequestParam("orderId") Integer orderId, HttpServletResponse response) {
        return Result.success(lawsuitService.getComplaintExtraction(orderId));
    }

    /**
     * 申请书要诉表
     */
    @ApiOperation(value = "申请书要诉表生成")
    @GetMapping("/get/applicationBook")
    public Result<Boolean> getApplicationBook(@RequestParam("orderId") Integer orderId, HttpServletResponse response) {
        return Result.success(lawsuitService.getApplicationBook(orderId));
    }


    @ApiOperation(value = "生成法诉合同")
    @PostMapping("/fs/contract")
    ResponseEntity<byte[]> getContract(@RequestBody @Validated LawsuitContractDTO lawsuitContractDTO) {
        return lawsuitService.getContract(lawsuitContractDTO);
    }

    /**
     * 获取处置公司流转记录
     */
    @ApiOperation(value = "获取处置公司流转记录")
    @PostMapping("/get/caseInfoFlowList")
    public Result<Page<CaseCirculationDeptRecordEntity>> getCaseInfoFlowList(@RequestBody @Validated CaseInfoFlowListDTO caseInfoFlowListDTO) {
        return Result.success(lawsuitService.getCaseInfoFlowList(caseInfoFlowListDTO));
    }

    /**
     * 债转通知
     */
    @ApiOperation(value = "债转通知要素表生成")
    @PostMapping("/debtTransferNotice")
    public Result<Boolean> debtTransferNotice(@RequestBody @Validated DebtTransferNoticeDTO debtTransferNoticeDTO) {
        return Result.success(lawsuitService.debtTransferNotice(debtTransferNoticeDTO));
    }

    /**
     * 送达地址确认书
     */
    @ApiOperation(value = "送达地址确认书生成")
    @PostMapping("/deliveryAddressConfirmation")
    public Result<Boolean> getDeliveryAddressConfirmation(@RequestBody @Validated DebtTransferNoticeDTO debtTransferNoticeDTO) {
        return Result.success(lawsuitService.getDeliveryAddressConfirmation(debtTransferNoticeDTO));
    }

    @ApiOperation(value = "钉钉发起失败重试")
    @GetMapping("/retryDingTask")
    Result<Boolean> outsourcingAdvanceApplication() {
        caseInfoService.retryDingTask();
        return Result.success(true);
    }

    @ApiOperation(value = "法诉平台合同")
    @PostMapping("/fs/company/contract")
    Result<Boolean> createCompanyContract(@RequestBody @Validated LawsuitContractDTO lawsuitContractDTO) {
        return Result.success(lawsuitService.createCompanyContract(lawsuitContractDTO));
    }

    /**
     * 委外导出
     */
    @ApiOperation(value = "委外导出")
    @PostMapping("/export/outsourcing")
    public void exportOutsourcing(@RequestBody CaseInfoListDTO caseInfoListDTO, HttpServletResponse response, @CurrentUser LoginUser loginUser1) {


        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(314);
        loginUser.setUserName("17717870517");
        loginUser.setName("陈新凯");
        loginUser.setMobile("17717870517");
        loginUser.setJobNumber("LH312109");
        loginUser.setDeptIds(Arrays.asList(49, 130, 54));
        loginUser.setRoleIds(Arrays.asList(1, 2001, 108));
        loginUser.setProxyUserIds(Collections.emptyList());
        loginUser.setRoleId(108);
        loginUser.setScopes("order:promote");
        loginUser.setStatus(0);
        loginUser.setDeleteFlag(0);
        loginUser.setUserType(5);
        loginUser.setToken("a4a4eaf1-bb69-468b-b65d-dbac34648dc9");
        loginUser.setOrigin(null);
        loginUser.setRenewal(0);

        caseInfoService.exportOutsourcing(caseInfoListDTO, response, loginUser);
    }

//    /**
//     * 法诉统计导出
//     */
//    @ApiOperation(value = "法诉统计导出")
//    @PostMapping("/export/lawsuitStatistics")
//    public void exportLawsuitStatistics(@RequestBody CaseInfoListDTO caseInfoListDTO, HttpServletResponse response, @CurrentUser LoginUser loginUser) {
//        lawsuitService.exportLawsuitStatistics(caseInfoListDTO, response, loginUser);
//    }

    /**
     * 法诉统计导出
     */
    @ApiOperation(value = "法诉统计导出")
    @PostMapping("/export/lawsuitStatistics")
    public void exportLawsuitStatistics(@RequestBody CaseInfoListDTO caseInfoListDTO, HttpServletResponse response, @CurrentUser LoginUser loginUser) {
        lawsuitService.exportLawsuitStatistics(caseInfoListDTO, response, loginUser);
    }

    /**
     * 法诉附件
     */
    @ApiOperation(value = "法诉附件")
    @PostMapping("/fs/attachment")
    public Result<List<AnnexLawsuitVO>> getAttachment(@RequestBody @Validated AnnexLawsuitDTO lawsuitAttachmentDTO) {
        return Result.success(lawsuitService.getAttachment(lawsuitAttachmentDTO));
    }

    /**
     * 撤销钉钉实例
     */
    @ApiOperation(value = "委外撤销钉钉实例")
    @PostMapping("/cancel/dingTask")
    public Result<Boolean> cancelDingTask() {
        return Result.success(outsourcingService.cancelOutsourcing());
    }

    @ApiOperation("获取减免审批申请委外的订单列表")
    @PostMapping("/reduction/approveList")
    public Result<Page<CaseInfoListVO>> getReductionApproveCaseInfolist(@RequestBody CaseInfoListDTO caseInfoListDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(outsourcingReductionService.getReductionApproveCaseInfolist(caseInfoListDTO, loginUser));
    }


    /**
     * 合同审批
     */
    @ApiOperation(value = "添加合同审批")
    @PostMapping("/contract/approval")
    public Result<Boolean> contractApproval(@RequestBody @Validated ContractApprovalDTO contractApprovalDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(outsourcingService.contractApproval(contractApprovalDTO, loginUser));
    }


    /**
     * 获取合同审批流程记录
     */
    @ApiOperation(value = "获取合同审批流程记录")
    @PostMapping("/contract/approval/record")
    public Result<Page<OutsourcingContractsApprovalRecordVO>> getContractApprovalRecord(@RequestBody @Validated OutsourcingContractsApprovalRecordDTO outsourcingContractsApprovalRecordDTO) {
        return Result.success(outsourcingService.getContractApprovalRecord(outsourcingContractsApprovalRecordDTO));
    }

    @ApiOperation(value = "钉钉合同申请审批")
    @GetMapping("/contractApplicationDingTaskBatch")
    Result<Boolean> contractApplicationDingTaskBatch() {
        outsourcingService.contractApplicationDingTaskBatch();
        return Result.success(true);
    }

    /**
     * 获取订单合同列表
     */
    @ApiOperation(value = "获取订单合同列表")
    @PostMapping("/order/contract/list")
    public Result<List<OrderContractListVO>> getOrderContractList(@RequestBody @Validated OrderContractListDTO orderContractListDTO) {
        return Result.success(outsourcingService.getOrderContractList(orderContractListDTO));
    }

    @ApiOperation("获取申请委外的订单列表")
    @PostMapping("/getAllCaseInfolist")
    public Result<Page<CaseInfoListVO>> getAllCaseInfolist(@RequestBody CaseInfoListDTO caseInfoListDTO, @CurrentUser LoginUser loginUser) {

        return Result.success(outsourcingService.getAllCaseInfolist(caseInfoListDTO, loginUser));
    }

    /**
     * 获取委外申请的合同列表
     */
    @ApiOperation(value = "获取委外申请的合同列表")
    @PostMapping("/outsourcing/contract/list")
    public Result<List<OrderContractListVO>> getOutsourcingContractList(@RequestBody @Validated OrderContractListDTO orderContractListDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(outsourcingService.getOutsourcingContractList(orderContractListDTO, loginUser));
    }

    /**
     * 获取数字化未申请委外列表
     */
    @ApiOperation(value = "获取数字化未申请委外列表")
    @PostMapping("/get/digitalize/order/list")
    public Object getOutsourcingList(@RequestBody DigitalizeWeiwaiOrderDTO digitalizeWeiwaiOrderDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(outsourcingService.getDigitalizeOderList(digitalizeWeiwaiOrderDTO, loginUser));
    }
//    /**
//     * 获取数字化未申请委外列表
//     */
//    @ApiOperation(value = "获取数字化未申请委外列表")
//    @PostMapping("/get/digitalize/order/list")
//    public Object getOutsourcingList(@RequestBody DigitalizeWeiwaiOrderDTO digitalizeWeiwaiOrderDTO, @CurrentUser LoginUser loginUser) {
//               LoginUser newLoginUser = new LoginUser();
//        newLoginUser.setUserId(2015);
//        newLoginUser.setUserName("***********");
//        newLoginUser.setName("路红历");
//        newLoginUser.setMobile("***********");
//        newLoginUser.setJobNumber("LH310329");
//        newLoginUser.setDeptIds(Arrays.asList(108, 48));
//        newLoginUser.setRoleIds(Arrays.asList(118));
//       newLoginUser.setProxyUserIds(Arrays.asList());
//       newLoginUser.setRoleId(118);
//        newLoginUser.setScopes("system:pc;data:region;system:vehicle");
//        newLoginUser.setStatus(0);
//        newLoginUser.setDeleteFlag(0);
//      newLoginUser.setUserType(5);
//        newLoginUser.setToken("64de88af-358f-4476-9c42-49fd99226c4f");
//       newLoginUser.setOrigin("pc");
//       newLoginUser.setRenewal(0);
//        return Result.success(outsourcingService.getDigitalizeOderList(digitalizeWeiwaiOrderDTO, newLoginUser));
//    }


    /**
     * 获取数字化订单逾期详情
     */

    @ApiOperation(value = "获取数字化订单逾期详情")
    @PostMapping("/get/digitalize/orderRepayment/list")
    public Object getOutsourcingOrderRepaymentList(@RequestBody DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO) {
        return Result.success(outsourcingService.getOutsourcingOrderRepaymentList(digitalizeWeiwaiOrderStatusDTO));
    }

    /**
     * 获取数字化订单众信信息
     */
    @ApiOperation(value = "获取数字化订单众信信息")
    @PostMapping("/get/digitalize/zhongxin/list")
    public Object getOutsourcingOrderZhongxinList(DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO) {
        return Result.success(outsourcingService.getOutsourcingOrderZhongxinList(digitalizeWeiwaiOrderStatusDTO));
    }

    @ApiOperation(value = "钉钉合同申请审批")
    @GetMapping("/contraicationDingTaskBatch")
    Result<Boolean> conationDingTaskBatch() {
        outsourcingService.contpplicationDingTaskBatch();
        return Result.success(true);
    }

    @ApiOperation(value = "生成债转合同选择列表")
    @PostMapping("/getDebtTransfersContractList")
    Result<DebtTransfersContractVO> getDebtTransfersContractList(@RequestBody List<Integer> caseInfoListDTO,
                                                                 @CurrentUser LoginUser loginUser) {
        return Result.success(caseInfoService.getDebtTransfersContractList(caseInfoListDTO, loginUser));
    }

    @ApiOperation(value = "委外平台合同")
    @PostMapping("/outSourcing/company/contract")
    Result<Boolean> createCompanySign(@RequestBody List<DebtTransfersContractListVO> contractListVOList) {

        return Result.success(lawsuitService.createCompanySign(contractListVOList));
    }

    /**
     * 获取数字化订单合同信息
     */

    @ApiOperation(value = "获取数字化订单合同信息")
    @PostMapping("/get/digitalize/orderContractList")
    public String getDigitalizeOrderContractList(@RequestBody DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO) {
        return outsourcingContractService.getDigitalizeOrderContractList(digitalizeWeiwaiOrderStatusDTO);
    }

    /**
     * 数字化逾期180天以上的订单
     */
    @ApiOperation(value = "数字化逾期180天以上的订单")
    @GetMapping("/digitalize/overdueList")
    Result<Boolean> digitalizeOverdueList() {
        outsourcingContractService.digitalizeOverdueList();
        return Result.success(true);
    }

    /**
     * 接收数字化结清状态
     */
    @ApiOperation(value = "接收数字化结清状态")
    @PostMapping("/receive/digitalize/settle/status")
    Result<Boolean> digitalizeReceipt(@RequestBody DigitalizeWeiwaiOrderStatusDTO digitalizeReceiptDTO) {
        return Result.success(kingdeeOutsourcingService.digitalizeReceipt(digitalizeReceiptDTO));
    }


    @ApiOperation(value = "保存委外维护更新记录")
    @PostMapping("/save/outsourcing/maintenance/record")
    Result<Boolean> saveOutsourcingMaintenanceRecord(@RequestBody SaveOutsourcingMaintenanceRecordDTO dto, @CurrentUser LoginUser loginUser) {
        return Result.success(caseInfoService.saveOutsourcingMaintenanceRecord(dto, loginUser));
    }

    @ApiOperation(value = "获取委外维护更新记录")
    @GetMapping("/get/outsourcing/maintenance/record")
    Result<List<CaseUpdateFieldRecordsEntity>> getOutsourcingMaintenanceRecord(@RequestParam Integer caseInfoId) {
        return Result.success(caseInfoService.getOutsourcingMaintenanceRecord(caseInfoId));
    }

    /**
     * 主动撤回
     */
    @ApiOperation(value = "主动撤回")
    @PostMapping("/proactively/withdraw")
    Result<String> cancelOutsourcing(@RequestBody CaseProactivelyWithdrawDTO caseProactivelyWithdrawDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(outsourcingContractService.proactivelyWithdraw(caseProactivelyWithdrawDTO, loginUser));
    }

    /**
     * 获取数字化结清金额
     */
    @ApiOperation(value = "获取数字化结清金额")
    @PostMapping("/get/digitalize/settle/list")
    public Result<DigitalizeSettleList> getDigitalizeSettleList(@RequestBody DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderDTO) {
        try {
            return Result.success(outsourcingContractService.getDigitalizeSettleList(digitalizeWeiwaiOrderDTO));
        }catch (Exception e){
            return Result.success(new DigitalizeSettleList());
        }
    }

    /**
     * 获取申请减免记录
     */
    @ApiOperation(value = "获取申请减免记录")
    @PostMapping("/get/reduction/record")
    public Result<Page<ExemptionApplicationEntityVO>> getReductionRecord(@RequestBody ExemptionApplicationEntityDTO exemptionApplicationEntityDTO) {
        return Result.success(outsourcingService.getReductionRecord(exemptionApplicationEntityDTO, null, null));
    }

    /**
     * 获取审批记录
     */
    /**
     * 资外申请节点信息列表
     */
    @ApiOperation(value = "资产委外申请节点信息列表")
    @GetMapping("/list/caseInfoReductionNodeRecordList")
    public Result<Page<CaseInfoNodeRecordListVO>> getCaseInfoReductionNodeRecordList(@RequestParam("caseInfoId") Integer caseInfoId, @RequestParam("approveType") Integer approveType, @RequestParam("processId") String processId) {
        return Result.success(kingdeeOutsourcingService.getCaseInfoReductionNodeRecordList(caseInfoId, approveType, processId));
    }

//    /**
//     * 金蝶获取申请委外的订单列表
//     */
//    @ApiOperation(value = "金蝶获取申请委外的订单列表")
//    @PostMapping("/get/kingdeeOutsourcing/order/list")
//    public Result<DigitizedOverdueOrdersTotalVO> getkingdeeOutsourcingOrderList(@RequestBody KingdeeOutsourcingOrderDTO kingdeeOutsourcingOrderDTO, @CurrentUser LoginUser loginUser1) {
//        LoginUser loginUser = new LoginUser();
//        loginUser.setUserId(521);
//        loginUser.setUserName("17633183549");
//        loginUser.setName("翟安然");
//        loginUser.setMobile("17633183549");
//        loginUser.setJobNumber("LH307424");
//        loginUser.setDeptIds(Arrays.asList(284));
//        loginUser.setRoleIds(Arrays.asList(102));
//        loginUser.setProxyUserIds(Arrays.asList(515, 533, 546, 542, 545, 511, 509, 514, 507, 528, 508, 512, 10416, 540, 530, 522, 524, 3268, 538, 541, 537, 10722, 529, 535, 544, 521, 548, 527, 2022, 519, 518, 543, 536, 517, 534, 10729, 510, 513, 526, 523, 520, 9846, 9956, 532, 531, 525, 516, 3654, 3272, 11645, 10646, 11757));
//        loginUser.setRoleId(102);
//        loginUser.setScopes("system:common;system:pc;system:vehicle");
//        loginUser.setStatus(0);
//        loginUser.setDeleteFlag(0);
//        loginUser.setUserType(5);
//        loginUser.setToken("2bc02d0c-c929-416a-ad11-c928e0d7c5db");
//        loginUser.setOrigin("pc");
//        loginUser.setRenewal(0);
////        List<DigitizedOverdueOrdersVO> digitizedOverdueOrdersVOS = kingdeeOutsourcingService.getkingdeeOutsourcingOrderList(kingdeeOutsourcingOrderDTO, loginUser);
////        DigitizedOverdueOrdersTotalVO digitizedOverdueOrdersTotalVO = new DigitizedOverdueOrdersTotalVO();
////        digitizedOverdueOrdersTotalVO.setRecords(digitizedOverdueOrdersVOS);
////        Integer totalCount = kingdeeOutsourcingService.getkingdeeOutsourcingOrderListCount(kingdeeOutsourcingOrderDTO, loginUser);
//
//        return Result.success(kingdeeOutsourcingService.getkingdeeOutsourcingOrderList(kingdeeOutsourcingOrderDTO, loginUser));
//    }

    /**
     * 金蝶获取申请委外的订单列表
     */
    @ApiOperation(value = "金蝶获取申请委外的订单列表")
    @PostMapping("/get/kingdeeOutsourcing/order/list")
    public Result<DigitizedOverdueOrdersTotalVO> getkingdeeOutsourcingOrderList(@RequestBody KingdeeOutsourcingOrderDTO kingdeeOutsourcingOrderDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(kingdeeOutsourcingService.getkingdeeOutsourcingOrderList(kingdeeOutsourcingOrderDTO, loginUser));
    }

    /**
     * 获取金蝶订单详情
     */
    @ApiOperation(value = "获取金蝶订单详情")
    @GetMapping("/get/kingdeeOutsourcing/order/detail")
    public Result<DigitizedOverdueOrdersVO> getkingdeeOutsourcingOrderDetail(@RequestParam("orderId") String orderId) {
        return Result.success(kingdeeOutsourcingService.getkingdeeOutsourcingOrderDetail(orderId));
    }

    /**
     * 获取数字化订单还款计划
     */
    @ApiOperation(value = "获取金蝶的订单还款计划")
    @GetMapping("/get/kingdeeOutsourcing/order/repayment/plan")
    public Result<List<RepaymentInfoVO>> getDigitalizeOrderRepaymentPlan(@RequestParam("digitalOrderId") String digitalOrderId) {
        return Result.success(kingdeeOutsourcingService.getDigitalizeOrderRepaymentPlan(digitalOrderId));
    }

    /**
     * 获取金蝶订单还款计划
     */
    @ApiOperation(value = "获取金蝶订单还款计划")
    @GetMapping("/get/kingdeeOutsourcing/repayment/plan/detail")
    public Result<RepaymentOverdueStatusVO> getkingdeeRepaymentPlanDetail(@RequestParam("digitalOrderId") String digitalOrderId) {
        return Result.success(kingdeeOutsourcingService.getkingdeeRepaymentPlanDetail(digitalOrderId));
    }

    /**
     * 通知金蝶状态
     */
    @ApiOperation(value = "通知金蝶状态")
    @PostMapping("/notice/kingdee/status")
    public Result<KingdeeStatusVO> debtTransferNotice() {
        return Result.success(kingdeeOutsourcingService.debtTransferNotice());
    }

    /**
     * 获取金蝶附件信息
     */
    @ApiOperation(value = "获取金蝶附件信息")
    @GetMapping("/get/kingdeeOutsourcing/attachment")
    public Result<KingdeeAttachmentEntity> getkingdeeAttachment(@RequestParam("orderId") String orderId) {
        return Result.success(kingdeeOutsourcingService.getkingdeeAttachment(orderId));
    }

    /**
     * 获取金蝶订单详情
     */
    @ApiOperation(value = "获取金蝶订单详情")
    @GetMapping("/get/kingdee/order/detail")
    public Result<KingdeeOrderDetailVO> getkingdeeOrderDetail(@RequestParam("digitalOrderId") String digitalOrderId) {
        return Result.success(kingdeeOutsourcingService.getkingdeeOrderDetail(digitalOrderId));
    }

    /**
     * 委外结清还款试算
     */
    @GetMapping("/earlyRepayCalc")
    public Result<CaseInfoRepayCalcVO> earlyRepayCalc(@RequestParam("orderId") Integer orderId) {
        try {
            return Result.success(caseInfoService.earlyRepayCalc(orderId));
        }catch (Exception e){
            return Result.success(new CaseInfoRepayCalcVO());
        }
    }


    /**
     * 定时更新数字化详情信息
     */
    @ApiOperation(value = "定时更新数字化详情信息")
    @GetMapping("/update/digitalize/order/detail")
    public Result<Boolean> updateDigitalizeOrderDetail() {
        return Result.success(kingdeeOutsourcingService.updateDigitalizeOrderDetail());
    }

    /**
     * 推送状态到金蝶
     */
    @ApiOperation(value = "推送状态到金蝶")
    @PostMapping("/push/Kingdeeorder/status")
    public Result<String> pushKingdeeorderStatus(@RequestBody KingdeeStatusRequestDTO kingdeeStatusRequestDTO) {
        return Result.success(kingdeeOutsourcingService.pushKingdeeorderStatus(kingdeeStatusRequestDTO));
    }

    /**
     * 根据条件获取减免申请记录
     */
    @ApiOperation(value = "根据条件获取减免申请记录")
    @PostMapping("/get/reduction/record/conditional")
    public Result<ReductionRecordConditionalVO> getReductionRecordConditional(@RequestBody ReductionRecordConditionalDTO dto) {
        return Result.success(outsourcingSettlementTrialCalculationsService.getReductionRecordConditional(dto));
    }

    /**
     * 获取金蝶结清试算
     */
    @ApiOperation(value = "获取金蝶结清试算")
    @GetMapping("/kingdee/order/earlyRepayCalc")
    public Result<ReductionRecordConditionalVO> kingdeeEarlyRepayCalc(@RequestParam String orderId) {
        try {
            return Result.success(outsourcingSettlementTrialCalculationsService.kingdeeEarlyRepayCalc(orderId));
        }catch (Exception e){
            return Result.success(new ReductionRecordConditionalVO());
        }
    }

    /**
     * 金蝶逾期210天以上数据推送到众信
     */
    @ApiOperation(value = "金蝶逾期210天以上数据推送到众信")
    @PostMapping("/kingdeeorder/addCaseInfo")
    public Result<Boolean> kingdeeorderAddCaseInfo() {
        return Result.success(kingdeeOutsourcingService.kingdeeorderAddCaseInfo());
    }

    /**
     * 委外终止
     */
    @ApiOperation(value = "委外终止")
    @PostMapping("/termination/outsourcing")
    public Result<Boolean> terminationOutsourcing(@RequestBody TerminationOutsourcingDTO terminationOutsourcingDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(outsourcingContractService.terminationOutsourcing(terminationOutsourcingDTO, loginUser));
    }

    /**
     * 委外详情
     *
     * @param orderId 订单 ID
     * @return {@link Result }<{@link OrderDetailsVo }>
     */
    @GetMapping("/detailsByOrderId")
    public Result<CaseOrderDetailsVo> detailsByOrderId(@RequestParam Integer orderId) {
        return Result.success(outsourcingContractService.detailsByOrderId(orderId));
    }

    /**
     * 数字化订单保证金是否被锁定
     */
    @ApiOperation(value = "数字化订单保证金是否被锁定")
    @PostMapping("/digitalizeBond/lockStatus")
    public Result<DigitalizeBondLockStatusVO> digitalizeBondLockStatus(@RequestBody DigitalizeWeiwaiOrderStatusDTO digitalizeReceiptDTO) {
        return Result.success(kingdeeOutsourcingService.digitalizeBondLockStatus(digitalizeReceiptDTO));
    }

    /**
     * 推送数字化保证金使用状态
     */
    @ApiOperation(value = "推送数字化保证金使用状态")
    @PostMapping("/digitalizeBond/pushStatus")
    public Result<Boolean> digitalizeBondPushStatus(@RequestBody DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO) {
        return Result.success(kingdeeOutsourcingService.digitalizeBondPushStatus(digitalizeWeiwaiOrderStatusDTO));
    }

    /**
     * 数字化保证金
     */
    @ApiOperation(value = "数字化保证金")
    @PostMapping("/digitalizeSettle")
    public Result<DigitalizeSettleList> digitalizeSettle(@RequestBody DigitalizeWeiwaiOrderStatusDTO digitalizeReceiptDTO) {
        return Result.success(outsourcingContractService.getDigitalizeSettleList(new DigitalizeWeiwaiOrderStatusDTO().setOrder_id(digitalizeReceiptDTO.getOrder_id())));
    }

    /**
     * 以资抵债列表
     */
    @ApiOperation("获取申请委外以资抵债的订单列表")
    @PostMapping("/usingAssets/list")
    public Result<Page<CaseInfoListVO>> getUsingAssetsCaseInfolist(@RequestBody CaseInfoListDTO caseInfoListDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(caseUsingAssetsService.getUsingAssetsCaseInfolist(caseInfoListDTO, loginUser));
    }

    @ApiOperation(value = "钉钉以资抵债主动审批")
    @GetMapping("/usingAssetsApplicationDingTaskBatch")
    Result<Boolean> usingAssetsApplicationDingTaskBatch() {
        caseUsingAssetsService.usingAssetsApplicationDingTaskBatch();
        return Result.success(true);
    }

    @ApiOperation(value = "钉钉以资抵债被动审批")
    @GetMapping("/usingAssetsPassiveApplicationDingTaskBatch")
    Result<Boolean> usingAssetsPassiveApplicationDingTaskBatch() {
        caseUsingAssetsService.usingAssetsPassiveApplicationDingTaskBatch();
        return Result.success(true);
    }

    /**
     * 委外获取客户详情
     */
    @ApiOperation(value = "委外获取客户详情")
    @PostMapping("/get/outsourcing/customer/detail")
    public Result<CaseCustomerInfoVO> getOutsourcingCustomerDetail(@RequestBody CaseCustomerInfoDTO caseCustomerInfoDTO) {
        return Result.success(caseUsingAssetsService.getOutsourcingCustomerDetail(caseCustomerInfoDTO));
    }
//
//    /**
//     * 获取金蝶/数字化客户信息
//     */
//    @ApiOperation(value = "获取金蝶/数字化客户信息")
//    @PostMapping("/get/kingdee/customer/detail")
//    public Result<KindeeOrderCaseBaseInfoVO> getKingdeeCustomerDetail(@RequestBody CaseCustomerInfoDTO caseCustomerInfoDTO) {
//        return Result.success(kingdeeOutsourcingService.getKingdeeCustomerDetail(caseCustomerInfoDTO));
//    }
//
//    /**
//     * 获取数字化客户信息
//     */
//    @ApiOperation(value = "获取数字化客户信息")
//    @PostMapping("/get/digitalize/customer/detail")
//    public Result<KindeeOrderCaseBaseInfoVO> getDigitalizeCustomerDetail(@RequestBody CaseCustomerInfoDTO caseCustomerInfoDTO) {
//        return Result.success(kingdeeOutsourcingService.getDigitalizeCustomerDetail(caseCustomerInfoDTO));
//    }

    /**
     * 流转三方gps账号转移
     */
    @ApiOperation(value = "流转三方gps账号转移")
    @PostMapping("/transfer/gpsAccount")
    public Result<Boolean> transferGpsAccount(@RequestBody CaseCustomerInfoDTO transferGpsAccountDTO) {
        return Result.success(caseTransferGpsAccountService.transferGpsAccount(transferGpsAccountDTO));
    }

    /**
     * 主动撤回gps转移
     */
    @ApiOperation(value = "主动撤回gps转移")
    @PostMapping("/transfer/gpsAccount/cancel")
    public Result<Boolean> cancelTransferGpsAccount(@RequestBody CaseCustomerInfoDTO cancelTransferGpsAccountDTO) {
        return Result.success(caseTransferGpsAccountService.cancelTransferGpsAccount(cancelTransferGpsAccountDTO));
    }

    /**
     * 筛选金蝶有问题数据
     */
    @ApiOperation(value = "筛选金蝶有问题数据")
    @PostMapping("/kingdee/problem/data")
    public Result<Boolean> kingdeeProblemData() {
        return Result.success(caseUsingAssetsService.kingdeeProblemData());
    }
    @ApiOperation("逾期70天以上的订单无处理中的法诉任务，无处理中的委外任务 添加到案件信息表")
    @PostMapping("/automatic/addCaseInfo")
    public Result<Boolean> automaticAddCaseInfo() {
        return Result.success(outsourcingAdvanceService.automaticAddCaseInfo());
    }
    /**
     * 数字化逾期70天以上的订单
     */
    @ApiOperation(value = "数字化逾期70天以上的订单自动加入委外")
    @PostMapping("/digitalize/automaticAddCaseInfo")
    Result<Boolean> digitalizeAutomaticAddCaseInfo() {
        outsourcingAdvanceService.digitalizeAutomaticAddCaseInfo();
        return Result.success(true);
    }
    /**
     * 获取数字化imei
     */
    @ApiOperation(value = "获取数字化imei")
    @PostMapping("/get/digitalize/imei")
    public Result<DigitalizeImeiVO> getDigitalizeImei(@RequestBody DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO) {
        return outsourcingAdvanceService.getDigitalizeImei(digitalizeWeiwaiOrderStatusDTO);
    }
    /**
     * 获取数字化已经委外的订单
     */
    @ApiOperation(value = "获取数字化已经委外的订单")
    @PostMapping("/get/digitalize/outsourcing/order/list")
    public Result<List<String>> getDigitalizeOutsourcingOderList() {
        return Result.success(caseUsingAssetsService.getDigitalizeOutsourcingOderList());
    }
/**
 * 接收贷后延迟委外数据
 */
@ApiOperation(value = "接收贷后延迟委外数据")
    @PostMapping("/receive/delay/outsourcing/data")
    public Result<Boolean> receiveDelayOutsourcingData(@RequestBody DelayOutsourcingDTO delayOutsourcingDTO) {
        return Result.success(outsourcingAdvanceService.delayOutsourcingData(delayOutsourcingDTO));
    }

}
