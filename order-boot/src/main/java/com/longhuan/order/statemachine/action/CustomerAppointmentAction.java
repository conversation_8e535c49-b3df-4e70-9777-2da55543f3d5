package com.longhuan.order.statemachine.action;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.mapper.OrderNodeRecordMapper;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.entity.OrderNodeRecordEntity;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * 状态转移动作，在转移进行的时候触发的动作
 */
@Slf4j
@Component

public class CustomerAppointmentAction extends OrderNodeAction implements Action<States, Events> {


    public CustomerAppointmentAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper) {
        super(orderInfoMapper, orderNodeRecordMapper);
    }

    @Override
    public void execute(StateContext<States, Events> context) {


        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();
        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);

        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event, currentNode, nextNode);


        // 1. 修改订单表状态
        //        updateStates(orderId);
        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);

        log.info("CustomerAppointmentAction order {} try CustomerAppointmentFinish", orderId);
        context.getStateMachine().sendEvent(Mono.just(MessageBuilder.withPayload(Events.AGREES).
                        setHeader(StateConstants.ORDER_ID, orderId)
                        .setHeader(StateConstants.REMARK, "客户预约完成")
                        .setHeader(StateConstants.USER_ID, userId).build()))
                .subscribe(result -> {
                    log.info("CustomerAppointmentAction order {} try CustomerAppointmentFinish result:{}", orderId,
                            result.getResultType());
                });
    }

    /**
     * 修改订单表状态
     *
     * @param orderId
     */
    private void updateStates(Integer orderId) {
        OrderInfoEntity updateEntity = new OrderInfoEntity();

        updateEntity.setId(orderId);
        updateEntity.setGpsState(2);

        orderInfoMapper.updateById(updateEntity);
    }


    /**
     * 保存订单节点记录
     *
     * @param event          事件
     * @param orderId        订单 ID
     * @param currentNode    当前节点
     * @param nextNode       下一个节点
     * @param remark         备注
     * @param remarkExternal 备注外部
     * @param rejectReason   拒绝原因
     * @param userId         用户 ID
     */
    @Override
    protected void saveOrderNodeRecord(Events event, Integer orderId, Integer currentNode, Integer nextNode, String remark, String remarkExternal,
                                       String rejectReason, Integer userId, Integer rejectReasonSecond) {
        // 2.1 查询最近次记录
        Integer lastNode = null;
        LocalDateTime lastTime = null;
        OrderNodeRecordEntity orderNodeRecordEntity = orderNodeRecordMapper.selectOne(new LambdaQueryWrapper<OrderNodeRecordEntity>()
                .eq(OrderNodeRecordEntity::getOrderId, orderId)
                .eq(OrderNodeRecordEntity::getEvent, Events.AGREES)
                .eq(OrderNodeRecordEntity::getNextNode, States.CUSTOMER_APPOINTMENT)
                .orderByDesc(OrderNodeRecordEntity::getCreateTime)
                .last("LIMIT 1")
        );

        if (orderNodeRecordEntity != null) {
            lastNode = orderNodeRecordEntity.getCurrentNode();
            lastTime = orderNodeRecordEntity.getCreateTime();
        }

        log.info("insert order node record");
        // 2.2 生成记录
        OrderNodeRecordEntity insertEntity = new OrderNodeRecordEntity();

        insertEntity.setOrderId(orderId);

        insertEntity.setCurrentNode(currentNode);
        insertEntity.setNextNode(nextNode);
        insertEntity.setLastNode(lastNode);

        if (lastTime != null) {
            insertEntity.setApprovalCost(DateUtil.between(
                    Date.from(lastTime.atZone(ZoneId.systemDefault()).toInstant()),
                    new Date(System.currentTimeMillis()), DateUnit.SECOND));
        } else {
            insertEntity.setApprovalCost(0L);
        }

        insertEntity.setRemark(remark);
        insertEntity.setRemarkExternal(remarkExternal);

        insertEntity.setRejectReason(rejectReason);
        insertEntity.setRejectReasonSecond(rejectReasonSecond);


        insertEntity.setEvent(event.eventTransform());
        insertEntity.setCreateBy(userId);
        insertEntity.setUpdateBy(userId);

        orderNodeRecordMapper.insert(insertEntity);
    }
}