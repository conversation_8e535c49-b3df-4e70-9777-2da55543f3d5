package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.IdentityCardDTO;
import com.longhuan.order.pojo.entity.PreOcrIdentityCardEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * 光学字符识别身份卡转换
 *
 * <AUTHOR>
 * @date 2024/10/22
 */
@Mapper(componentModel = "spring")
public interface OcrIdentityCardConverter {
    @Mapping(target = "updateNation", qualifiedByName = "formatNation")
    PreOcrIdentityCardEntity dtoToEntity(IdentityCardDTO dto);

    @Named("formatNation")
    default String formatNation(String nation) {
        if (nation != null && !nation.endsWith("族")) {
            return nation + "族";
        }
        return nation;
    }
}
