package com.longhuan.approve.boot.fund.pre.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.approve.api.pojo.dto.*;
import com.longhuan.approve.api.pojo.vo.FundRepayCalcVO;
import com.longhuan.approve.boot.constants.FundConstant;
import com.longhuan.approve.boot.enums.*;
import com.longhuan.approve.boot.enums.FundPaymentStatusEnum;
import com.longhuan.approve.boot.feign.AutoLoanRepaymentFeign;
import com.longhuan.approve.boot.feign.OrderFeign;
import com.longhuan.approve.boot.feign.ResourceFeign;
import com.longhuan.approve.boot.fund.finall.FundFinalApiStrategy;
import com.longhuan.approve.boot.fund.finall.FundFinalStrategyFactory;
import com.longhuan.approve.boot.fund.hengtong.HengTongService;
import com.longhuan.approve.boot.fund.pay.FundPayStrategy;
import com.longhuan.approve.boot.fund.pay.FundPayStrategyFactory;
import com.longhuan.approve.boot.fund.post.FundPostStrategy;
import com.longhuan.approve.boot.fund.post.FundPostStrategyFactory;
import com.longhuan.approve.boot.fund.pre.FundApproveService;
import com.longhuan.approve.boot.fund.pre.FundPreApiStrategy;
import com.longhuan.approve.boot.fund.pre.FundPreStrategyFactory;
import com.longhuan.approve.boot.fund.repay.FundRepayStrategy;
import com.longhuan.approve.boot.fund.repay.FundRepayStrategyFactory;
import com.longhuan.approve.boot.fund.util.FundApiRecordUtils;
import com.longhuan.approve.boot.mapper.*;
import com.longhuan.approve.boot.pojo.dto.CommuteAndPayDTO;
import com.longhuan.approve.boot.pojo.dto.*;
import com.longhuan.approve.boot.pojo.entity.*;
import com.longhuan.approve.boot.pojo.vo.OrderTimeoutAutomaticVO;
import com.longhuan.approve.boot.service.processing.FundMessageProcess;
import com.longhuan.auto.loan.common.enums.BankPayResultCodeEnum;
import com.longhuan.auto.loan.pojo.dto.BankPayDeductDTO;
import com.longhuan.auto.loan.pojo.vo.BankPaySendVO;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.core.util.NumberUtils;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.enums.Events;
import com.longhuan.order.enums.States;
import com.longhuan.order.pojo.dto.FinalApproveFundStatusDTO;
import com.longhuan.order.pojo.dto.OrderApproveFundPaymentStatusDTO;
import com.longhuan.order.pojo.dto.PreApproveFundStatusDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.longhuan.approve.boot.enums.InitStatusEnums.*;

/**
 * 资金审批服务实施
 *
 * <AUTHOR>
 * @date 2024/08/09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FundApproveServiceImpl implements FundApproveService {
    private final static String PRE_PREFIX = "fund_";
    private final static String FINAL_PREFIX = "fund_final_";
    private static final String LOCK_KEY_PREFIX = "approve:fundPayment:lock:";
    private static final long LOCK_TIMEOUT = 270;
    private final RedisService redisService;

    private final HengTongService hengTongService;

    // 最大重试次数
    private final FundPreStrategyFactory fundPreStrategyFactory;
    private final FundFinalStrategyFactory fundFinalStrategyFactory;
    private final FundPayStrategyFactory fundPayStrategyFactory;
    private final FundPostStrategyFactory fundPostStrategyFactory;
    private final FundRepayStrategyFactory fundRepayStrategyFactory;
    private final FundApproveMapper fundApproveMapper;
    private final PreFundInfoMapper preFundInfoMapper;
    private final PreFundBaseInfoMapper preFundBaseInfoMapper;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final FinalFundBaseInfoMapper finalFundBaseInfoMapper;
    private final FundInfoMapper fundInfoMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final FundApiRecordUtils fundApiRecordUtils;
    private final OrderFeign orderFeign;
    private final AutoLoanRepaymentFeign autoLoanRepaymentFeign;
    private final OrderCompanyInfoMapper orderCompanyInfoMapper;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final FundRepaymentDeductMapper fundRepaymentDeductMapper;
    private final OrderPayApplicationMapper orderPayApplicationMapper;

    private final ResourceFeign resourceFeign;
    private final RepurchaseRepaymentInfoMapper repurchaseRepaymentInfoMapper;
    private final OrderVehicleInfoMapper orderVehicleInfoMapper;
    private final FundMessageProcess fundMessageProcess;
    private final OrderNodeRecordMapper orderNodeRecordMapper;
    private final OrderFeeDetailMapper orderFeeDetailMapper;


    /**
     * 预审批
     *
     * @param fundApprovePreDTO 资方预审批
     * @return {@link Boolean }
     */
    @Override
    public String pre(FundApprovePreDTO fundApprovePreDTO) {
        log.info("FundApproveServiceImpl.pre begin fundApprovePreDTO:{}", fundApprovePreDTO);
        //生成资方预审批编号
        String fundNumber = NumberUtils.getFundPreNumber();
        log.info("FundApproveServiceImpl.pre fundNumber:{}", fundNumber);
        //获取资方Id
        Integer fundId = fundApprovePreDTO.getFund().getValue();
        //获取预审Id
        Integer preId = fundApprovePreDTO.getPreId();
        //终止数字化
        orderFeign.stopCredit(fundApprovePreDTO.getPreId());
        //初始化资方预审批数据
        FundPreBaseDTO fundPreBaseDTO = this.copyFundApprovePreData(fundApprovePreDTO);
        fundPreBaseDTO.setFundCode(PRE_PREFIX + fundApprovePreDTO.getFund().getFundCode());
        fundPreBaseDTO.setFundId(fundId);
        this.initFundPreBaseData(fundPreBaseDTO);
        //initFundPreBaseData 拷贝preId会为空
        fundPreBaseDTO.setPreId(preId);
        //记录资方预审批数据
        fundPreBaseDTO.setFundPreNumber(fundNumber);
        //记录预审资方匹配信息
        Boolean saveFundPreData = this.saveFundPreData(fundPreBaseDTO, fundApprovePreDTO, fundId);
        //调用资方预审批接口
        Boolean callFundPreApi = this.callFundPreApi(fundPreBaseDTO);
        //更新资方匹配信息
        Boolean updateFundPreDate = this.updateFundPreDate(fundPreBaseDTO, fundId);
        log.info("FundApproveServiceImpl.pre end fundApprovePreDTO:{}", fundApprovePreDTO);
        return fundNumber;
    }

    /**
     * 最终批准
     *
     * @param fundApproveFinalDTO 基金批准最终 DTO
     * @return {@link String }
     */
    @Override
    public String finalApproval(FundApproveFinalDTO fundApproveFinalDTO) {
        log.info("FundApproveServiceImpl.finalApproval begin fundApproveFinalDTO:{}", fundApproveFinalDTO);
        //生成资方预审批编号
        String fundNumber = NumberUtils.getFundFinalNumber();
        //获取资方Id
        Integer fundId = fundApproveFinalDTO.getFund().getValue();
        log.info("FundApproveServiceImpl.finalApproval fundNumber:{}", fundNumber);
        //初始化资方预审批数据
        FundFinalBaseDTO fundFinalBaseDTO = this.copyFundApproveFinalData(fundApproveFinalDTO);
        fundFinalBaseDTO.setFundCode(FINAL_PREFIX + fundApproveFinalDTO.getFund().getFundCode());
        this.initFundFinalBaseData(fundFinalBaseDTO);
        fundFinalBaseDTO.setFundId(fundApproveFinalDTO.getFund().getValue());
        //记录资方预审批数据
        Boolean saveFundPreData = this.saveFundFinalData(fundFinalBaseDTO, fundApproveFinalDTO, fundId);
        fundFinalBaseDTO.setFundFinalNumber(fundNumber);
        //调用资方预审批接口
        Boolean callFundPreApi = this.callFundFinalApi(fundFinalBaseDTO);
        //更新资方匹配信息
        Boolean updateFundPreDate = this.updateFundFinalDate(fundFinalBaseDTO, fundId);
        log.info("FundApproveServiceImpl.finalApproval end fundApproveFinalDTO:{}", fundApproveFinalDTO);
        return fundNumber;
    }

    /**
     * 支付
     *
     * @return {@link Boolean }
     */
    @Override
    public Boolean pay() {
        log.info("FundApproveServiceImpl.pay begin");
        String lockKey = LOCK_KEY_PREFIX + "pay";
        String lockValue = "pay";
        Boolean lockSuccess = redisService.tryLock(lockKey, lockValue, LOCK_TIMEOUT);
        if (!lockSuccess) {
            log.warn("FundApproveServiceImpl.pay Another instance is already running pay task.");
            return false;
        }
        try {
            List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(new MPJLambdaWrapper<OrderInfoEntity>()
                    .innerJoin(FinalFundInfoEntity.class, on -> on
                            .eq(OrderInfoEntity::getId, FinalFundInfoEntity::getOrderId)
                            .eq(OrderInfoEntity::getFundId, FinalFundInfoEntity::getFundId)
                            .eq(OrderInfoEntity::getDeleteFlag, 0))
                    .and(qw -> {
                        qw.eq(OrderInfoEntity::getPaymentState, OrderPaymentStateEnum.WAIT)
                                .or().isNull(OrderInfoEntity::getPaymentState);
                    })
                    .leftJoin(OrderLoanReservoirEntity.class, on -> on
                            .eq(OrderInfoEntity::getId, OrderLoanReservoirEntity::getOrderId)
                            .eq(OrderLoanReservoirEntity::getDeleteFlag, 0))
                    .in(OrderInfoEntity::getState, Arrays.asList(4500, 4550))
                    .eq(FinalFundInfoEntity::getFundResult, PreFundResultEnum.PASS)
                    .lt(FinalFundInfoEntity::getRetry, FundConstant.MAX_PAYMENT_RETRY_COUNT)
                    .and(qw -> {
                        qw.eq(FinalFundInfoEntity::getPaymentStatus, FundPaymentStatusEnum.NONE)
                                .or().isNull(FinalFundInfoEntity::getPaymentStatus);
                    })
                    .and(qw -> {
                        qw.in(OrderLoanReservoirEntity::getReservoirStatus, List.of(2, 3))
                                .or().isNull(OrderLoanReservoirEntity::getId);
                    })
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(FinalFundInfoEntity::getCreateTime));


            log.info("FundApproveServiceImpl.pay orderInfoEntityList orderInfoEntityList:{}", JSONUtil.toJsonStr(orderInfoEntityList));
            for (OrderInfoEntity orderInfoEntity : orderInfoEntityList) {
                log.info("FundApproveServiceImpl.pay orderInfoEntityList orderInfoEntity:{}", JSONUtil.toJsonStr(orderInfoEntity));
                Integer orderId = orderInfoEntity.getId();
                Integer state = orderInfoEntity.getState();
                //判断当前订单节点是否符合放款
                boolean contains = List.of(4500, 4550).contains(state);
                Assert.isTrue(contains, "订单状态不符合放款流程");
                Integer fundId = orderInfoEntity.getFundId();
                FundEnum fundEnum = FundEnum.getFundEnum(fundId);
                String fundCode = "PAY_" + fundEnum.getFundCode();
                FundPayStrategy fundPayStrategy = fundPayStrategyFactory.getFundPayStrategy(fundCode);
                FundApiRecordDTO fundApiRecordDTO = null;
                String remark = null;
                FundApiRecordStatusEnum status = null;
                // 尝试获取分布式锁

                try {
                    fundApiRecordDTO = fundPayStrategy.pay(new FundPayDTO().setOrderId(orderId));
                    if (null != fundApiRecordDTO) {
                        remark = fundApiRecordDTO.getRemark();
                        status = fundApiRecordDTO.getStatus();
                    }
                    log.info("FundApproveServiceImpl.pay fundApiRecordDTO:{}", fundApiRecordDTO);
                    if (FundApiRecordStatusEnum.REJECTED == status || FundApiRecordStatusEnum.CALL_FAILED == status) {

                        updateFinalFundPaymentStatue(orderId, fundId, 3);
                        //更新订单放款状态为拒绝
                        this.updateOrderPaymentStatusThrowFail(orderId, fundId, remark);
                    } else if (FundApiRecordStatusEnum.CALL_SUCCESS == status || FundApiRecordStatusEnum.APPROVED == status) {

                        updateFinalFundPaymentStatue(orderId, fundId, 1);
                    } else {
                        updateFinalFundPaymentStatue(orderId, fundId, 0);
                    }

                } catch (Exception e) {
                    log.error("FundApproveServiceImpl.pay Exception e:{}", e.getMessage(), e);
                    //更新订单放款状态为拒绝
                    updateFinalFundPaymentStatue(orderId, fundId, 3);
                    this.updateOrderPaymentStatusThrowFail(orderId, fundId, remark);
                } finally {
                    log.info("FundApproveServiceImpl.pay finally orderId:{} fundApiRecordDTO:{}", orderId, JSONUtil.toJsonStr(fundApiRecordDTO));
                }
            }

        } catch (Exception e) {
            log.error("FundApproveServiceImpl.pay error processing fundInfoEntity with ID: {}. Exception: {}", lockKey, e.getMessage(), e);
        } finally {
            // 释放锁
            redisService.releaseLock(lockKey, lockValue);
            log.info("FundApproveServiceImpl.pay Released lock for fundInfoEntity with ID: {}", lockKey);
        }
        return true;
    }

    /**
     * 资方取消接口
     * <h3>盈峰</h3>
     *
     * <h3>富民</h3>
     * 未撤销不能再重新进件，额度有效期是30天，30天后额度会过期 <br/>
     * 如果已经办理线上抵押，需要发起撤销，已经办抵成功的客户需要发起线上解押；避免客户车辆被富民锁定
     *
     * @param fundApproveCancelDTO
     * @return
     */
    @Override
    public String finalCancel(FundApproveCancelDTO fundApproveCancelDTO) {


        log.info("FundApproveServiceImpl.finalCancel finalCancel fundApproveCancelDTO:{}", JSONUtil.toJsonStr(fundApproveCancelDTO));
        Integer orderId = fundApproveCancelDTO.getLinkId();
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId, OrderInfoEntity::getState, OrderInfoEntity::getFundId)
                .eq(OrderInfoEntity::getId, orderId));

        Integer state = orderInfoEntity.getState();
        Integer fundId = orderInfoEntity.getFundId();

        // 判断当前订单节点是否符合放款
        boolean contains = !List.of(4500, 5000).contains(state);

        Assert.isTrue(contains, () -> {
            throw new BusinessException("该订单状态不允许取消");
        });

        FundEnum fundEnum = FundEnum.getFundEnum(fundId);


        String fundCode = "PAY_" + fundEnum.getFundCode();
        FundPayStrategy fundPayStrategy = fundPayStrategyFactory.getFundPayStrategy(fundCode);
        FundApiRecordDTO fundApiRecordDTO = null;
        String remark = null;
        FundApiRecordStatusEnum status = null;
        try {
            fundApiRecordDTO = fundPayStrategy.cancel(new FundPayDTO().setOrderId(orderId));


        } catch (Exception e) {
            log.error("FundApproveServiceImpl.finalCancel Exception e:{}", e.getMessage(), e);
            throw new BusinessException("资方取消失败: " + e.getMessage());
        } finally {
            log.info("FuMinPayServiceImpl.finalCancel finally orderId:{} fundApiRecordDTO:{}", orderId, JSONUtil.toJsonStr(fundApiRecordDTO));

        }

        return "success";

    }

    @Override
    public Boolean orderTimeoutAutomaticTermination() {
        MPJLambdaWrapper<OrderInfoEntity> wrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .distinct()
                .selectAll(OrderInfoEntity.class)
                .selectAs(PreFundInfoEntity::getFundCreditTime, OrderTimeoutAutomaticVO::getFundCreditTime)
                .leftJoin(PreFundInfoEntity.class, PreFundInfoEntity::getPreId, OrderInfoEntity::getPreId)
                .leftJoin(CustomerMortgageInfoEntity.class, CustomerMortgageInfoEntity::getOrderId, OrderInfoEntity::getId)
                .eq(OrderInfoEntity::getFundId, PreFundInfoEntity::getFundId)
                .gt(OrderInfoEntity::getState, 0)
                .lt(OrderInfoEntity::getState, 5000)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .eq(PreFundInfoEntity::getResult, 1)
                .eq(PreFundInfoEntity::getFundResult, 2)
                .eq(PreFundInfoEntity::getDeleteFlag, 0)
                .and(i ->
                        i.eq(CustomerMortgageInfoEntity::getMortgageType, 0)
                                .or()
                                .isNull(CustomerMortgageInfoEntity::getMortgageType)
                                .or()
                                .eq(CustomerMortgageInfoEntity::getMortgageType, 1)
                );
        List<OrderTimeoutAutomaticVO> ordersTimeoutAutomaticVO = orderInfoMapper.selectJoinList(OrderTimeoutAutomaticVO.class, wrapper);
        log.info("orderTimeoutAutomaticTermination finalFundInfoEntities:{}", ordersTimeoutAutomaticVO);
        ordersTimeoutAutomaticVO.stream().forEach(orderInfo -> {
            try {
                log.info("orderTimeoutAutomaticTermination finalFundInfoEntity:{}", JSONUtil.toJsonStr(orderInfo));
                LocalDateTime fundCreditTime = orderInfo.getFundCreditTime();
                Boolean flag = judgeOrderContractAudit(orderInfo.getId());
                //进行过合同审核通过则不进行订单终止
                if (flag) {
                    if (fundCreditTime != null) {
                        LocalDateTime fifteen15Date = fundCreditTime.plusDays(15);
                        LocalDateTime currentTime = LocalDateTime.now();
                        if (currentTime.isAfter(fifteen15Date)) {
                            FundApproveCancelDTO fundApproveCancelDTO = new FundApproveCancelDTO();
                            fundApproveCancelDTO.setFund(FundEnum.getFundEnum(orderInfo.getFundId()))
                                    .setLinkId(orderInfo.getId())
                                    .setType(2);
                            finalCancelStatus(fundApproveCancelDTO);
                        }
                    }
                }
            } catch (Exception e) {
                log.info("com.longhuan.approve.boot.fund.pre.impl.FundApproveServiceImpl.finalCancelStatus 处理失败的orderid：{}", orderInfo.getId());
            }
        });
        return true;
    }

    /**
     * 判断订单是否进行过合同审核，审核通过
     *
     * @param orderId
     * @return
     */
    private Boolean judgeOrderContractAudit(Integer orderId) {
        log.info("FundApproveServiceImpl judgeOrderContractAudit 查询是否经过合同审核通过 orderId:{}", orderId);
        List<OrderNodeRecordEntity> contractNodeList = orderNodeRecordMapper.selectList(new LambdaQueryWrapper<OrderNodeRecordEntity>()
                .eq(OrderNodeRecordEntity::getOrderId, orderId)
                .eq(OrderNodeRecordEntity::getDeleteFlag, 0)
                .eq(OrderNodeRecordEntity::getNextNode, 4500)
        );
        if (CollUtil.isEmpty(contractNodeList)) {
            return true;
        } else {
            log.info("FundApproveServiceImpl judgeOrderContractAudit 已经过合同审核通过 的订单 orderId:{}", orderId);
            return false;
        }
    }

    public void finalCancelStatus(FundApproveCancelDTO fundApproveCancelDTO) {
        log.info("FundApproveServiceImpl.finalCancel finalCancel fundApproveCancelDTO:{}", JSONUtil.toJsonStr(fundApproveCancelDTO));
        Integer orderId = fundApproveCancelDTO.getLinkId();
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId, OrderInfoEntity::getState, OrderInfoEntity::getFundId, OrderInfoEntity::getManagerId)
                .eq(OrderInfoEntity::getId, orderId));
        Integer fundId = orderInfoEntity.getFundId();
        // 判断当前订单节点是否符合放款
        FundEnum fundEnum = FundEnum.getFundEnum(fundId);
        String fundCode = "PAY_" + fundEnum.getFundCode();
        FundPayStrategy fundPayStrategy = fundPayStrategyFactory.getFundPayStrategy(fundCode);
        try {
            fundPayStrategy.cancel(new FundPayDTO().setOrderId(orderId));
            orderFeign.sendFinish(States.getByNode(orderInfoEntity.getState()), orderId, orderInfoEntity.getManagerId(), Events.CANCEL);
        } catch (Exception e) {
            log.error("FundApproveServiceImpl.finalCancel Exception e:{}", e.getMessage(), e);
        }
    }

    @Override
    public Boolean payResult() {
        log.info("FundApproveServiceImpl.payResult begin");
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(new MPJLambdaWrapper<OrderInfoEntity>()
                .innerJoin(FinalFundInfoEntity.class, on -> on
                        .eq(OrderInfoEntity::getId, FinalFundInfoEntity::getOrderId)
                        .eq(OrderInfoEntity::getFundId, FinalFundInfoEntity::getFundId)
                        .eq(OrderInfoEntity::getDeleteFlag, 0))
                .and(qw -> {
                    qw.eq(OrderInfoEntity::getPaymentState, OrderPaymentStateEnum.WAIT)
                            .or().isNull(OrderInfoEntity::getPaymentState);
                })
                .eq(OrderInfoEntity::getState, 4500)
                .eq(FinalFundInfoEntity::getFundResult, PreFundResultEnum.PASS)
                .lt(FinalFundInfoEntity::getRetry, FundConstant.MAX_PAYMENT_RETRY_COUNT)
                .eq(FinalFundInfoEntity::getPaymentStatus, FundPaymentStatusEnum.WAIT)
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime));
        log.info("FundApproveServiceImpl.payResult orderInfoEntityList orderInfoEntityList:{}", JSONUtil.toJsonStr(orderInfoEntityList));
        if (CollUtil.isEmpty(orderInfoEntityList)) {
            return true;
        }
        for (OrderInfoEntity orderInfoEntity : orderInfoEntityList) {
            Integer orderId = orderInfoEntity.getId();
            Integer fundId = orderInfoEntity.getFundId();
            FundEnum fundEnum = FundEnum.getFundEnum(fundId);
            if (ObjUtil.equals(fundEnum, FundEnum.ZHONG_HENG)) {
                fundEnum = FundEnum.ZHONG_HENG_TONG_HUI;
            }
            String fundCode = "PAY_" + fundEnum.getFundCode();
            FundPayStrategy fundPayStrategy = fundPayStrategyFactory.getFundPayStrategy(fundCode);
            try {
                fundPayStrategy.payResult(new FundPayDTO().setOrderId(orderId));
            } catch (Exception e) {
                log.error("FundApproveServiceImpl.payResult Exception e:{}", e.getMessage(), e);
            }
        }
        return null;
    }

    @Override
    public String fundAmountChange(FundAmountChangeDTO fundAmountChangeDTO) {

        log.info("FundApproveServiceImpl.fundAmountChange fundAmountChangeDTO:{}",
                JSONUtil.toJsonStr(fundAmountChangeDTO));
        Integer orderId = fundAmountChangeDTO.getLinkId();
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId, OrderInfoEntity::getState, OrderInfoEntity::getFundId)
                .eq(OrderInfoEntity::getId, orderId));

        Integer fundId = orderInfoEntity.getFundId();

        FundEnum fundEnum = FundEnum.getFundEnum(fundId);


        String fundCode = "PAY_" + fundEnum.getFundCode();
        FundPayStrategy fundPayStrategy = fundPayStrategyFactory.getFundPayStrategy(fundCode);
        FundApiRecordDTO fundApiRecordDTO = null;
        String remark = null;
        FundApiRecordStatusEnum status = null;
        try {
            fundApiRecordDTO = fundPayStrategy.amountChange(fundAmountChangeDTO);


        } catch (BusinessException e) {
            log.error("FundApproveServiceImpl.fundAmountChange Exception e:{}", e.getMessage(), e);
            throw new BusinessException("资方额度变更: " + e.getMessage());
        } finally {
            log.info("FuMinPayServiceImpl.fundAmountChange finally orderId:{} fundApiRecordDTO:{}", orderId, JSONUtil.toJsonStr(fundApiRecordDTO));

        }

        return "success";
    }

    @Override
    public FundRepayCalcVO repayCalc(FundRepayCalcDTO fundRepayDTO) {
        Integer orderId = fundRepayDTO.getOrderId();
        log.info("FundApproveServiceImpl.repayCalc begin orderId:{}", orderId);

        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));

        FundRepayCalcVO calcVO = null;
        try {
            Integer fundId = orderInfo.getFundId();
            FundEnum fundEnum = FundEnum.getFundEnum(fundId);
            if (ObjUtil.equals(fundEnum, FundEnum.ZHONG_HENG)) {
                fundEnum = FundEnum.ZHONG_HENG_TONG_HUI;
            }
            String fundCode = "REPAY_" + fundEnum.getFundCode();

            FundRepayStrategy fundRepayStrategy = fundRepayStrategyFactory.getFundRepayStrategy(fundCode);
            calcVO = fundRepayStrategy.calc(fundRepayDTO);
        } catch (BusinessException e) {
            log.error("FundApproveServiceImpl.repayCalc BusinessException error orderId:{} msg:{}", orderId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("FundApproveServiceImpl.repayCalc Exception error orderId:{} msg:{}", orderId, e.getMessage());
            throw new BusinessException("资方还款试算异常");
        }

        return calcVO;
    }

    @Override
    public Boolean repay(FundRepayDTO fundRepayDTO) {
        log.info("FundApproveServiceImpl.repay begin");

        //是否整笔结清
        boolean settleFlag = false;
        if (ObjUtil.isNotNull(fundRepayDTO)) {
            settleFlag = fundRepayDTO.getSettleFlag();
        }

        Integer orderId = fundRepayDTO.getOrderId();
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));

        List<RepayBatchDTO> repaymentInfoList = new ArrayList<>();
        //是否按传入订单期数还款
        boolean isByOrderDeduct = false;
        if (settleFlag) {
            //结清还款 取最近应还一期
            //按传入订单还款
            isByOrderDeduct = true;

            List<RepayBatchDTO> fundRepaymentList = fundRepaymentInfoMapper.selectJoinList(RepayBatchDTO.class,
                    JoinWrappers.lambda(FundRepaymentInfoEntity.class)
                            .select(FundRepaymentInfoEntity::getOrderId, FundRepaymentInfoEntity::getTerm, FundRepaymentInfoEntity::getFundId, FundRepaymentInfoEntity::getIsdaichang)
                            .select(OrderInfoEntity::getIsRepurchase)
                            .innerJoin(OrderInfoEntity.class, on ->
                                    on.eq(FundRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                                            .eq(FundRepaymentInfoEntity::getFundId, OrderInfoEntity::getFundId)
                                            .eq(OrderInfoEntity::getIsRepurchase, 0)

                                            .eq(OrderInfoEntity::getId, orderId)
                            )
                            .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                            .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                            .eq(OrderInfoEntity::getCurrentNode, 5000)
                            .orderByAsc(FundRepaymentInfoEntity::getTerm)
                            .last("LIMIT 1")
            );

            List<RepayBatchDTO> repurchaseRepaymentList = repurchaseRepaymentInfoMapper.selectJoinList(RepayBatchDTO.class,
                    JoinWrappers.lambda(RepurchaseRepaymentInfoEntity.class)
                            .select(RepurchaseRepaymentInfoEntity::getOrderId, RepurchaseRepaymentInfoEntity::getTerm, RepurchaseRepaymentInfoEntity::getFundId)
                            .select(OrderInfoEntity::getIsRepurchase)
                            .innerJoin(OrderInfoEntity.class, on ->
                                    on.eq(RepurchaseRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                                            .eq(RepurchaseRepaymentInfoEntity::getFundId, OrderInfoEntity::getFundId)
                                            .eq(OrderInfoEntity::getIsRepurchase, 1)
                                            .eq(OrderInfoEntity::getId, orderId)
                            )
                            .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                            .in(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.NONE, FundRepayStatusEnum.OVERDUE, FundRepayStatusEnum.PART_RETURN)
                            .eq(OrderInfoEntity::getCurrentNode, 5000)
                            .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                            .orderByAsc(RepurchaseRepaymentInfoEntity::getTerm)
                            .last("LIMIT 1")
            );

            repaymentInfoList.addAll(fundRepaymentList);
            repaymentInfoList.addAll(repurchaseRepaymentList);

            Assert.notEmpty(repaymentInfoList, () -> new BusinessException("未查询到还款计划信息"));

        } else if (ObjUtil.isNotNull(fundRepayDTO) && ObjUtil.isNotNull(fundRepayDTO.getOrderId())) {
            // 正常还款 逾期还款
            //按传入订单期数还款
            isByOrderDeduct = true;

            Integer term = fundRepayDTO.getTerm();

            List<RepayBatchDTO> fundRepaymentList = fundRepaymentInfoMapper.selectJoinList(RepayBatchDTO.class,
                    JoinWrappers.lambda(FundRepaymentInfoEntity.class)
                            .select(FundRepaymentInfoEntity::getOrderId, FundRepaymentInfoEntity::getTerm, FundRepaymentInfoEntity::getFundId, FundRepaymentInfoEntity::getIsdaichang)
                            .select(OrderInfoEntity::getIsRepurchase)
                            .innerJoin(OrderInfoEntity.class, on ->
                                    on.eq(FundRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                                            .eq(FundRepaymentInfoEntity::getFundId, OrderInfoEntity::getFundId)
                                            .eq(OrderInfoEntity::getIsRepurchase, 0)
                                            .eq(OrderInfoEntity::getId, orderId)
                            )
                            .eq(FundRepaymentInfoEntity::getTerm, term)
                            .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                            .in(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.NONE, FundRepayStatusEnum.OVERDUE, FundRepayStatusEnum.PART_RETURN)
                            .eq(OrderInfoEntity::getCurrentNode, 5000)
                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                            .orderByDesc(FundRepaymentInfoEntity::getCreateTime)
            );

            List<RepayBatchDTO> repurchaseRepaymentList = repurchaseRepaymentInfoMapper.selectJoinList(RepayBatchDTO.class,
                    JoinWrappers.lambda(RepurchaseRepaymentInfoEntity.class)
                            .select(RepurchaseRepaymentInfoEntity::getOrderId, RepurchaseRepaymentInfoEntity::getTerm, RepurchaseRepaymentInfoEntity::getFundId)
                            .select(OrderInfoEntity::getIsRepurchase)
                            .innerJoin(OrderInfoEntity.class, on ->
                                    on.eq(RepurchaseRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                                            .eq(RepurchaseRepaymentInfoEntity::getFundId, OrderInfoEntity::getFundId)
                                            .eq(OrderInfoEntity::getIsRepurchase, 1)
                                            .eq(OrderInfoEntity::getId, orderId)
                            )
                            .eq(RepurchaseRepaymentInfoEntity::getTerm, term)
                            .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderId)
                            .in(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.NONE, FundRepayStatusEnum.OVERDUE, FundRepayStatusEnum.PART_RETURN)
                            .eq(OrderInfoEntity::getCurrentNode, 5000)
                            .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                            .orderByDesc(RepurchaseRepaymentInfoEntity::getCreateTime)

            );
            repaymentInfoList.addAll(fundRepaymentList);
            repaymentInfoList.addAll(repurchaseRepaymentList);

            Assert.notEmpty(repaymentInfoList, () -> new BusinessException("未查询到还款计划信息"));

        } else {

            List<RepayBatchDTO> fundRepaymentList = fundRepaymentInfoMapper.selectJoinList(RepayBatchDTO.class,
                    JoinWrappers.lambda(FundRepaymentInfoEntity.class)
                            .select(FundRepaymentInfoEntity::getOrderId, FundRepaymentInfoEntity::getTerm, FundRepaymentInfoEntity::getFundId, FundRepaymentInfoEntity::getIsdaichang)
                            .select(OrderInfoEntity::getIsRepurchase)
                            .innerJoin(OrderInfoEntity.class, on ->
                                    on.eq(FundRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                                            .eq(FundRepaymentInfoEntity::getFundId, OrderInfoEntity::getFundId)
                                            .eq(OrderInfoEntity::getIsRepurchase, 0)
                                            .ne(OrderInfoEntity::getIsPushThirdParty, 1)
                            )
                            .le(FundRepaymentInfoEntity::getRepaymentDate, LocalDate.now())
                            .in(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.NONE, FundRepayStatusEnum.OVERDUE, FundRepayStatusEnum.PART_RETURN)
                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                            .eq(OrderInfoEntity::getCurrentNode, 5000)
                            .ne(FundRepaymentInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                            .notIn(FundRepaymentInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                            .orderByDesc(FundRepaymentInfoEntity::getCreateTime)
            );

            List<RepayBatchDTO> repurchaseRepaymentList = repurchaseRepaymentInfoMapper.selectJoinList(RepayBatchDTO.class,
                    JoinWrappers.lambda(RepurchaseRepaymentInfoEntity.class)
                            .select(RepurchaseRepaymentInfoEntity::getOrderId, RepurchaseRepaymentInfoEntity::getTerm, RepurchaseRepaymentInfoEntity::getFundId)
                            .select(OrderInfoEntity::getIsRepurchase)
                            .innerJoin(OrderInfoEntity.class, on ->
                                    on.eq(RepurchaseRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                                            .eq(RepurchaseRepaymentInfoEntity::getFundId, OrderInfoEntity::getFundId)
                                            .eq(OrderInfoEntity::getIsRepurchase, 1)
                                            .ne(OrderInfoEntity::getIsPushThirdParty, 1)
                            )
                            .le(RepurchaseRepaymentInfoEntity::getRepaymentDate, LocalDate.now())
                            .in(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.NONE, FundRepayStatusEnum.OVERDUE, FundRepayStatusEnum.PART_RETURN)
                            .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                            .eq(OrderInfoEntity::getCurrentNode, 5000)
                            .ne(FundRepaymentInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                            .notIn(FundRepaymentInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                            .orderByDesc(RepurchaseRepaymentInfoEntity::getCreateTime)

            );

            repaymentInfoList.addAll(fundRepaymentList);
            repaymentInfoList.addAll(repurchaseRepaymentList);

        }
        log.info("FundApproveServiceImpl.repay repaymentInfoEntities repaymentInfoEntities:{}", JSONUtil.toJsonStr(repaymentInfoList));

        BankPayDeductDTO bankPayDeductDTO = new BankPayDeductDTO()
                .setOrderId(fundRepayDTO.getOrderId())
                .setTerm(fundRepayDTO.getTerm())
                .setIsSettle(fundRepayDTO.getSettleFlag())
                .setDataSource(0)
                .setSignPlate(SignPlateEnum.BAO_FU);

        //月供还款前先进行gps车务费划扣
        Result<BankPaySendVO> bankPaySendVOResult = null;
        //gps车务费划扣
        try {
            bankPaySendVOResult = autoLoanRepaymentFeign.gpsFeeDeduct(bankPayDeductDTO);
        } catch (Exception e) {
            log.error("FundApproveServiceImpl.repay,gps车务费划扣,error:{}", e.getMessage(), e);
        }

        if (null != bankPaySendVOResult) {
            BankPaySendVO bankPaySendVO = bankPaySendVOResult.getData();
            BankPayResultCodeEnum resultCode = bankPaySendVO.getResultCode();
            if (BankPayResultCodeEnum.SUCCESS.equals(resultCode)) {
                orderFeeDetailMapper.insert(new OrderFeeDetailEntity()
                        .setOrderId(fundRepayDTO.getOrderId())
                        .setTradingSerialNumber(bankPaySendVO.getPayReqNo())
                        .setAmount(bankPaySendVO.getPayAmount())
                        .setTradingMethods(OrderFeeDetailTradingMethodsEnum.BAO_FU_DEDUCT)
                        .setPayer(orderInfo.getCustomerName())
                        .setPayee("河北龙环谛听网络科技有限公司")
                        .setExpenseType(OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE)
                        .setStatus(OrderFeeDetailStatusEnum.INCOME)
                        .setTerm(fundRepayDTO.getTerm())
                        .setTradingTime(bankPaySendVO.getPayTime())
                        .setOrderApplicationSource(0)
                        .setOrderSource(1));
            }
        }

        List<Integer> orderIds = new ArrayList<>();
        for (RepayBatchDTO repayDTO : repaymentInfoList) {
            log.info("FundApproveServiceImpl.repay repaymentInfoEntities fundRepaymentInfoEntity:{}", JSONUtil.toJsonStr(repayDTO));
            Integer repayOrderId = repayDTO.getOrderId();
            Integer fundId = repayDTO.getFundId();
            FundEnum fundEnum = FundEnum.getFundEnum(fundId);
            if (ObjUtil.equals(fundEnum, FundEnum.ZHONG_HENG)) {
                fundEnum = FundEnum.ZHONG_HENG_TONG_HUI;
            }
//            if (ObjUtil.equals(fundEnum, FundEnum.ZHONG_HENG_TONG_HUI)){
//                continue;
//            }
            String fundCode = "REPAY_" + fundEnum.getFundCode();
            Integer isDC = 0;
            if (repayDTO.getIsdaichang() != null) {
                isDC = repayDTO.getIsdaichang();
            }
            // 增加判断 或者 fund那个表新增的字段=1时 触发云启划扣
            if (ObjUtil.equals(repayDTO.getIsRepurchase(), 1) || isDC.equals((1))) {
                fundCode = "REPAY_TONG_LIAN_TONG";
            }
            FundRepayStrategy fundRepayStrategy = fundRepayStrategyFactory.getFundRepayStrategy(fundCode);
            FundApiRecordDTO fundApiRecordDTO = null;
            String remark = null;
            FundApiRecordStatusEnum status = null;
            try {
                Long offlineFeeCount = orderPayApplicationMapper.selectCount(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                                .eq(OrderPayApplicationInfoEntity::getOrderId, repayDTO.getOrderId())
//                        .ne(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY)
                                .notIn(OrderPayApplicationInfoEntity::getCurrentNode, List.of(PayApplicationNodeEnums.FAIL, PayApplicationNodeEnums.SUCCESS))
//                        .in(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.getOfflineFeeEnumList())
                                .in(OrderPayApplicationInfoEntity::getFeeType, List.of(
                                                OrderFeeDetailExpandTypeEnum.INSTALLMENT_SECURITY_DEPOSIT,
                                                OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT,
                                                OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT,
//                                        OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY,
                                                OrderFeeDetailExpandTypeEnum.INSTALLMENT_SERVICE_FEE)
                                )
                                .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                );
                if (offlineFeeCount > 0) {
                    throw new BusinessException("该订单存在待审核的转对公申请单，无法发起资方划扣");
                }

                //资方划扣
                fundApiRecordDTO = fundRepayStrategy.repay(new FundRepayDTO()
                        .setOrderId(repayOrderId)
                        .setTerm(repayDTO.getTerm())
                        .setSettleFlag(settleFlag)
                        .setRepayMode(fundRepayDTO.getRepayMode())
                        .setRepayYingFengTransferDTO(fundRepayDTO.getRepayYingFengTransferDTO())
                );

                if (null != fundApiRecordDTO) {
                    remark = fundApiRecordDTO.getRemark();
                    status = fundApiRecordDTO.getStatus();

                    if (status.equals(FundApiRecordStatusEnum.APPROVED) && fundCode.equals("REPAY_TONG_LIAN_TONG") && fundEnum == FundEnum.ZHONG_HENG_TONG_HUI) {
                        orderIds.add(repayOrderId);
                    }
                }
            } catch (BusinessException e) {
                log.error("FundApproveServiceImpl.repay repayOrderId:{} term:{} BusinessException e:{}", repayOrderId, repayDTO.getTerm(), e.getMessage(), e);
                if (isByOrderDeduct) {
                    throw e;
                }
            } catch (Exception e) {
                log.error("FundApproveServiceImpl.repay Exception e:{}", e.getMessage(), e);
                if (isByOrderDeduct) {
                    throw new BusinessException("划扣异常");
                }
            } finally {
                log.info("FundApproveServiceImpl.repay finally repayOrderId:{} fundApiRecordDTO:{}", repayOrderId, JSONUtil.toJsonStr(fundApiRecordDTO));
            }
        }

        if (!orderIds.isEmpty()) {
            for (Integer tempOrderId : orderIds) {
                OrderInfoEntity entity = orderInfoMapper.selectById(tempOrderId);
                if (entity != null) {
                    FinalFundInfoEntity fundInfoEntity = finalFundInfoMapper.selectById(entity.getFundId());
                    FundRepaymentInfoEntity fundRepaymentInfoEntity = fundRepaymentInfoMapper.selectOne(
                            new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
                                    .eq(FundRepaymentInfoEntity::getOrderId, tempOrderId)
                                    .eq(FundRepaymentInfoEntity::getFundId, fundInfoEntity.getFundId())
                                    .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                    .last("LIMIT 1")
                    );

                    if (fundRepaymentInfoEntity != null) {
                        CommuteAndPayDTO commuteAndPayDTO = new CommuteAndPayDTO();
                        commuteAndPayDTO.setOrderNum(fundInfoEntity.getLoanBillNo());
                        commuteAndPayDTO.setSPOrderNum(entity.getOrderNumber());
                        commuteAndPayDTO.setQiShu(String.valueOf(fundRepaymentInfoEntity.getTerm()));
                        commuteAndPayDTO.setHKJinE(String.valueOf(fundRepaymentInfoEntity.getRepaymentAmountTotal()));
                        commuteAndPayDTO.setShouKuanTime(String.valueOf(fundRepaymentInfoEntity.getRepaymentDate()));

                        hengTongService.daiChangHouHKTZ(commuteAndPayDTO);
                    }
                }
            }
        }


        return true;

    }

    @Override
    public Boolean repayResult(FundRepayQueryDTO fundRepayQueryDTO) {

        log.info("FundApproveServiceImpl.repayResult begin");
        //是否按传入订单期数还款
        boolean isByOrderQuery = false;

        List<FundRepaymentDeductEntity> repaymentDeductList = null;
        if (ObjUtil.isNotNull(fundRepayQueryDTO) && ObjUtil.isNotNull(fundRepayQueryDTO.getOrderId())) {
            isByOrderQuery = true;
            repaymentDeductList = fundRepaymentDeductMapper.selectList(new MPJLambdaWrapper<FundRepaymentDeductEntity>()
                    .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)
                    .eq(FundRepaymentDeductEntity::getOrderId, fundRepayQueryDTO.getOrderId())
                    .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                    .notIn(FundRepaymentDeductEntity::getBizType,
                            FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION,
                            FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION,
                            FundDeductBizTypeEnums.PENALTY_INITIATE_REPAYMENT_AFTER_PAYMENT
                    )
                    .orderByDesc(FinalFundInfoEntity::getCreateTime));
        } else {
            repaymentDeductList = fundRepaymentDeductMapper.selectList(new MPJLambdaWrapper<FundRepaymentDeductEntity>()
                    .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING.getCode())
                    .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                    .notIn(FundRepaymentDeductEntity::getBizType,
                            FundDeductBizTypeEnums.PUBLIC_ACCOUNT_APPLICATION,
                            FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION,
                            FundDeductBizTypeEnums.PENALTY_INITIATE_REPAYMENT_AFTER_PAYMENT
                    )
                    .orderByDesc(FinalFundInfoEntity::getCreateTime));
        }
        log.info("FundApproveServiceImpl.repayResult orderInfoEntityList orderInfoEntityList:{}", JSONUtil.toJsonStr(repaymentDeductList));
        Set<Integer> redemptionNumber = new HashSet<>();
        try {
            redemptionNumber = repaymentDeductList.stream().filter(e -> ObjUtil.equals(e.getBizType(), FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE)).map(FundRepaymentDeductEntity::getOrderId).collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("FundApproveServiceImpl.repay Exception e:{}", e.getMessage(), e);
        }
        for (FundRepaymentDeductEntity fundRepaymentInfoEntity : repaymentDeductList) {
            log.info("FundApproveServiceImpl.repayResult orderInfoEntityList fundRepaymentInfoEntity:{}", JSONUtil.toJsonStr(fundRepaymentInfoEntity));
            Integer orderId = fundRepaymentInfoEntity.getOrderId();
            Integer fundId = fundRepaymentInfoEntity.getFundId();
            FundEnum fundEnum = FundEnum.getFundEnum(fundId);
            if (ObjUtil.equals(fundEnum, FundEnum.ZHONG_HENG)) {
                fundEnum = FundEnum.ZHONG_HENG_TONG_HUI;
            }
            String fundCode = "REPAY_" + fundEnum.getFundCode();
            if (redemptionNumber.contains(orderId)) {
                fundCode = "REPAY_TONG_LIAN_TONG";
            }
            FundRepayStrategy fundRepayStrategy = fundRepayStrategyFactory.getFundRepayStrategy(fundCode);
            FundApiRecordDTO fundApiRecordDTO = null;
            String remark = null;
            FundApiRecordStatusEnum status = null;
            try {
                fundApiRecordDTO = fundRepayStrategy.repayResult(new FundRepayResultDTO()
                        .setOrderId(orderId)
                        .setTerm(fundRepaymentInfoEntity.getTerm())
                        .setRepayOrderNo(fundRepaymentInfoEntity.getDeductReqNo())
                        .setRepayType(fundRepaymentInfoEntity.getRepayType())
                        .setBizType(fundRepaymentInfoEntity.getBizType())
                        .setRepayDeductId(fundRepaymentInfoEntity.getId())
                );

                if (null != fundApiRecordDTO) {
                    remark = fundApiRecordDTO.getRemark();
                    status = fundApiRecordDTO.getStatus();
                }
            } catch (BusinessException e) {
                log.error("FundApproveServiceImpl.repayResult orderId:{} term:{} BusinessException e:{}", orderId, fundRepaymentInfoEntity.getTerm(), e.getMessage(), e);
                if (isByOrderQuery) {
                    throw e;
                }
            } catch (Exception e) {
                log.error("FundApproveServiceImpl.repayResult Exception e:{}", e.getMessage(), e);
                if (isByOrderQuery) {
                    throw new BusinessException("划扣查询异常");
                }
            } finally {
                log.info("FuMinPayServiceImpl.repayResult finally orderId:{} fundApiRecordDTO:{}", orderId, JSONUtil.toJsonStr(fundApiRecordDTO));
            }
        }

        // 发送还款消息
        List<String> deductNoList = repaymentDeductList.stream().map(FundRepaymentDeductEntity::getDeductReqNo).distinct().toList();
        try {
            ThreadUtil.execAsync(() -> fundMessageProcess.sendRepayMessage(deductNoList));
        } catch (Exception e) {
            log.error("FundApproveServiceImpl.repayResult sendRepayMessage deductNoList:{} Exception e:{}", deductNoList, e.getMessage(), e);
        }
        //推送众信
        return true;

    }

    @Override
    public Boolean postOrderInformation() {

        //资方结清文件凭证fileId
        Integer fundSettlementFileId = resourceFeign.selectFileConfigByCode(FundConstant.FUND_SETTLEMENT_VOUCHER).getData();
        Integer lanHaiLoanFlowCertificateFileId = resourceFeign.selectFileConfigByCode(FundConstant.LAN_HAI_LOAN_FLOW_CERTIFICATE).getData();
        log.info("FundApproveServiceImpl.postOrderInformation begin");
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(new MPJLambdaWrapper<OrderInfoEntity>()
                .leftJoin(OrderFileEntity.class, on ->
                        on.eq(OrderInfoEntity::getId, OrderFileEntity::getOrderId)
                                .eq(OrderFileEntity::getDeleteFlag, 0)
                                .in(OrderFileEntity::getFileId, Arrays.asList(
                                        fundSettlementFileId,
                                        lanHaiLoanFlowCertificateFileId
                                ))
                )
                // 盈峰没有
                .in(OrderInfoEntity::getFundId, Arrays.asList(
                        FundEnum.ZHONG_HENG.getValue(), FundEnum.ZHONG_HENG_TONG_HUI.getValue(),
                        FundEnum.CHANG_YIN.getValue(), FundEnum.FU_MIN.getValue(), FundEnum.LAN_HAI.getValue()))
                .eq(OrderInfoEntity::getPaymentState, OrderPaymentStateEnum.PASS)
                .in(OrderInfoEntity::getState, Arrays.asList(5000, 8000))
                .isNull(OrderFileEntity::getFileId)
        );
        log.info("FundApproveServiceImpl.postOrderInformation orderInfoEntityList orderInfoEntityList:{}", JSONUtil.toJsonStr(orderInfoEntityList));
        for (OrderInfoEntity orderInfoEntity : orderInfoEntityList) {
            log.info("FundApproveServiceImpl.postOrderInformation orderInfoEntityList orderInfoEntity:{}", JSONUtil.toJsonStr(orderInfoEntity));
            Integer orderId = orderInfoEntity.getId();
            Integer state = orderInfoEntity.getState();
            //判断当前订单节点是否符合放款
            boolean contains = List.of(5000, 8000).contains(state);
            Assert.isTrue(contains, "订单状态不符合放款流程");
            Integer fundId = orderInfoEntity.getFundId();
            FundEnum fundEnum = FundEnum.getFundEnum(fundId);
            if (ObjUtil.equals(fundEnum, FundEnum.ZHONG_HENG)) {
                fundEnum = FundEnum.ZHONG_HENG_TONG_HUI;
            }
            String fundCode = "POST_" + fundEnum.getFundCode();
            FundPostStrategy fundPostStrategy = fundPostStrategyFactory.getFundPostStrategy(fundCode);

            FundApiRecordDTO fundApiRecordDTO = null;
            String remark = null;
            FundApiRecordStatusEnum status = null;
            try {
                fundApiRecordDTO = fundPostStrategy.postOrderInformation(new FundPayDTO().setOrderId(orderId));
                if (null != fundApiRecordDTO) {
                    remark = fundApiRecordDTO.getRemark();
                    status = fundApiRecordDTO.getStatus();
                }
                log.info("FundApproveServiceImpl.postOrderInformation fundApiRecordDTO:{}", fundApiRecordDTO);

            } catch (Exception e) {
                log.error("FundApproveServiceImpl.postOrderInformation Exception e:{}", e.getMessage(), e);
            } finally {
                log.info("FuMinPayServiceImpl.postOrderInformation finally orderId:{} fundApiRecordDTO:{}", orderId, JSONUtil.toJsonStr(fundApiRecordDTO));
            }
        }
        return true;

    }

    /**
     * 判断是否需要重新加载资方预审批数据
     *
     * @param fundApprovePreDTO 基金批准 pre dto
     * @return {@link FundPreBaseDTO }
     */
    public FundPreBaseDTO checkInitFundPreData(FundApprovePreDTO fundApprovePreDTO) {
        PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                .eq(PreFundInfoEntity::getPreId, fundApprovePreDTO.getPreId())
                .eq(PreFundInfoEntity::getDeleteFlag, 0)
        );
        if (Objects.isNull(preFundInfoEntity)) {
            return null;
        }
        Integer id = preFundInfoEntity.getId();
        PreFundBaseInfoEntity preFundBaseInfoEntity = preFundBaseInfoMapper.selectOne(new LambdaQueryWrapper<PreFundBaseInfoEntity>()
                .eq(PreFundBaseInfoEntity::getPreId, id)
                .eq(PreFundBaseInfoEntity::getDeleteFlag, 0)
        );
        if (Objects.isNull(preFundBaseInfoEntity)) {
            return null;
        }
        return JSONUtil.toBean(preFundBaseInfoEntity.getBaseData(), FundPreBaseDTO.class);
    }


    /**
     * 复制基金批准前数据
     *
     * @param fundApprovePreDTO 基金批准 pre dto
     * @return {@link FundPreBaseDTO }
     */
    public FundPreBaseDTO copyFundApprovePreData(FundApprovePreDTO fundApprovePreDTO) {
        return new FundPreBaseDTO().setFundCode(fundApprovePreDTO.getFund().name())
                .setPreId(fundApprovePreDTO.getPreId());
    }

    /**
     * 复制基金批准最终数据
     *
     * @param fundApproveFinalDTO 基金批准最终 DTO
     * @return {@link FundFinalBaseDTO }
     */
    public FundFinalBaseDTO copyFundApproveFinalData(FundApproveFinalDTO fundApproveFinalDTO) {
        return new FundFinalBaseDTO().setFundCode(fundApproveFinalDTO.getFund().name())
                .setOrderId(fundApproveFinalDTO.getOrderId())
                .setPreId(fundApproveFinalDTO.getPreId())
                ;
    }

    /**
     * 保存基金前数据
     *
     * @param fundPreBaseDTO    基金 pre base dto
     * @param fundApprovePreDTO 基金批准 pre dto
     * @return {@link Boolean }
     */
    public Boolean saveFundPreData(FundPreBaseDTO fundPreBaseDTO, FundApprovePreDTO fundApprovePreDTO, Integer fundId) {
        log.info("FundApproveServiceImpl.saveFundPreBaseDTO begin fundPreBaseDTO:{} fundApprovePreDTO:{}", fundPreBaseDTO, fundApprovePreDTO);
        //查询预审匹配资方信息
        PreFundInfoEntity preFundInfo = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                        .eq(PreFundInfoEntity::getPreId, fundApprovePreDTO.getPreId())
                        .eq(PreFundInfoEntity::getFundId, fundId)
                        .eq(PreFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(PreFundInfoEntity::getCreateTime)
                , false);
        //保存请求信息和基础信息
        preFundBaseInfoMapper.insert(new PreFundBaseInfoEntity()
                .setPreId(null != preFundInfo ? preFundInfo.getId() : null)
                .setRequestData(JSONUtil.toJsonStr(fundApprovePreDTO))
                .setBaseData(JSONUtil.toJsonStr(fundPreBaseDTO))
        );
//        fundPreBaseDTO.setPreFundId(preFundInfo.getId());
        // 更新资方申请编号
        if (preFundInfo != null && StringUtils.hasText(preFundInfo.getCreditReqNo())) {
            fundPreBaseDTO.setFundPreNumber(preFundInfo.getCreditReqNo());
        }
        log.info("FundApproveServiceImpl.saveFundPreBaseDTO end fundPreBaseDTO:{} fundApprovePreDTO:{}", fundPreBaseDTO, fundApprovePreDTO);
        return true;
    }


    /**
     * 保存基金最终数据
     *
     * @param fundFinalBaseDTO    基金最终基数 DTO
     * @param fundApproveFinalDTO 基金批准最终 DTO
     * @return {@link Boolean }
     */
    public Boolean saveFundFinalData(FundFinalBaseDTO fundFinalBaseDTO, FundApproveFinalDTO fundApproveFinalDTO, Integer fundId) {
        log.info("FundApproveServiceImpl.saveFundFinalData begin fundFinalBaseDTO:{} fundApproveFinalDTO:{}", fundFinalBaseDTO, fundApproveFinalDTO);
        FinalFundInfoEntity finalFundInfoEntity = getFinalFundInfoByPreIdAndFundId(fundApproveFinalDTO.getOrderId(),
                fundId);
        if (finalFundInfoEntity == null) {

            PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                    .eq(PreFundInfoEntity::getPreId, fundApproveFinalDTO.getPreId())
                    .eq(PreFundInfoEntity::getFundId, fundId)
                    .eq(PreFundInfoEntity::getDeleteFlag, 0)
            );

            finalFundInfoEntity = new FinalFundInfoEntity()
                    .setFundResult(PreFundResultEnum.INIT)
                    .setFundId(fundId)
                    .setOrderId(fundFinalBaseDTO.getOrderId())
                    .setCreditReqNo(preFundInfoEntity.getCreditReqNo());
            finalFundInfoMapper.insert(finalFundInfoEntity);
        }

        // 保存记录
        Integer preId = fundApproveFinalDTO.getPreId();
        if (null != preId) {
            finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .set(FinalFundInfoEntity::getOrderId, fundApproveFinalDTO.getOrderId())
                    .eq(FinalFundInfoEntity::getPreId, preId).isNull(FinalFundInfoEntity::getOrderId)
                    .eq(FinalFundInfoEntity::getFundId, fundId)
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
            );
        }

        //同步长银放款失败抵押信息
        syncChangYinPaymentFailHandle(fundApproveFinalDTO.getOrderId());

        //保存请求信息和基础信息
        finalFundBaseInfoMapper.insert(new FinalFundBaseInfoEntity()
                .setFundId(finalFundInfoEntity.getId())
                .setRequestData(JSONUtil.toJsonStr(fundApproveFinalDTO))
                .setBaseData(JSONUtil.toJsonStr(fundFinalBaseDTO))
        );

        log.info("FundApproveServiceImpl.saveFundFinalData end fundFinalBaseDTO:{} fundApproveFinalDTO:{}", fundFinalBaseDTO, fundApproveFinalDTO);
        return true;
    }

    /**
     * Init Fund 前置基础数据
     *
     * @param fundPreBaseDTO 基金 pre base dto
     */
    public void initFundPreBaseData(FundPreBaseDTO fundPreBaseDTO) {
        log.info("FundApproveServiceImpl.initFundPreBaseData begin fundPreBaseDTO:{}", JSONUtil.toJsonStr(fundPreBaseDTO));
        Instant start = Instant.now();
        try {
            CompletableFuture<Integer> mainApplyInfo = CompletableFuture.supplyAsync(() -> getMainApplyInfo(fundPreBaseDTO));
            CompletableFuture<Integer> carInfo = CompletableFuture.supplyAsync(() -> getCarInfo(fundPreBaseDTO));
            CompletableFuture<List<Integer>> futureResult = CompletableFuture.allOf(mainApplyInfo, carInfo)
                    .thenApply(v -> Arrays.asList(
                                    mainApplyInfo.join(),
                                    carInfo.join()
                            )
                    );

            log.info("FundApproveServiceImpl.initFundPreBaseData fundPreBaseDTO:{}", JSONUtil.toJsonStr(fundPreBaseDTO));
            log.info("FundApproveServiceImpl.initFundPreBaseData futureResult:{}", futureResult);
            try {
                List<Integer> resultList = futureResult.get();
                Integer dataStatus = resultList.contains(FAIL.getStatus()) ? FAIL.getStatus() : SUCCESS.getStatus();
                log.info("FundApproveServiceImpl.initFundPreBaseData resultList:{}", JSONUtil.toJsonStr(resultList));
                log.info("FundApproveServiceImpl.initFundPreBaseData dataStatus:{}", dataStatus);
            } catch (InterruptedException | ExecutionException e) {
                log.error("FundApproveServiceImpl.initFundPreBaseData future e:", e);
                throw new BusinessException(e);
            }
        } catch (Exception e) {
            log.error("FundApproveServiceImpl.initFundPreBaseData e:", e);
            throw new BusinessException("初始化资方预审批信息失败");
        }
        // 记录任务完成后的事件
        Instant end = Instant.now();
        // 计算并打印耗时
        Duration duration = Duration.between(start, end);
        log.info("FundApproveServiceImpl.initFundPreBaseData Task took:{} ms", duration.toMillis());
        log.info("FundApproveServiceImpl.initFundPreBaseData end fundPreBaseDTO:{}", JSONUtil.toJsonStr(fundPreBaseDTO));
    }

    /**
     * Init Fund 最终基础数据
     *
     * @param fundFinalBaseDTO 基金最终基数 DTO
     */
    public void initFundFinalBaseData(FundFinalBaseDTO fundFinalBaseDTO) {
        log.info("FundApproveServiceImpl.initFundFinalBaseData begin fundFinalBaseDTO:{}", JSONUtil.toJsonStr(fundFinalBaseDTO));
        Instant start = Instant.now();
        try {
            CompletableFuture<Integer> mainApplyInfo = CompletableFuture.supplyAsync(() -> getFinalMainApplyInfo(fundFinalBaseDTO));
            CompletableFuture<Integer> carInfo = CompletableFuture.supplyAsync(() -> getFinalCarInfo(fundFinalBaseDTO));
            CompletableFuture<List<Integer>> futureResult = CompletableFuture.allOf(mainApplyInfo, carInfo)
                    .thenApply(v -> Arrays.asList(
                                    mainApplyInfo.join(),
                                    carInfo.join()
                            )
                    );
            log.info("FundApproveServiceImpl.initFundFinalBaseData fundFinalBaseDTO:{}", JSONUtil.toJsonStr(fundFinalBaseDTO));
            log.info("FundApproveServiceImpl.initFundFinalBaseData futureResult:{}", futureResult);
            try {
                List<Integer> resultList = futureResult.get();
                Integer dataStatus = resultList.contains(FAIL.getStatus()) ? FAIL.getStatus() : SUCCESS.getStatus();
                log.info("FundApproveServiceImpl.initFundFinalBaseData resultList:{}", JSONUtil.toJsonStr(resultList));
                log.info("FundApproveServiceImpl.initFundFinalBaseData dataStatus:{}", dataStatus);
            } catch (InterruptedException | ExecutionException e) {
                log.error("FundApproveServiceImpl.initFundFinalBaseData future e:", e);
                throw new BusinessException(e);
            }
        } catch (Exception e) {
            log.error("FundApproveServiceImpl.initFundFinalBaseData e:", e);
            throw new BusinessException("初始化资方预审批信息失败");
        }
        // 记录任务完成后的事件
        Instant end = Instant.now();
        // 计算并打印耗时
        Duration duration = Duration.between(start, end);
        log.info("FundApproveServiceImpl.initFundFinalBaseData Task took:{} ms", duration.toMillis());
        log.info("FundApproveServiceImpl.initFundFinalBaseData end fundPreBaseDTO:{}", JSONUtil.toJsonStr(fundFinalBaseDTO));
    }


    /**
     * 调用基金 Pre API
     *
     * @param fundPreBaseDTO 基金 pre base dto
     * @return {@link Boolean }
     */
    public Boolean callFundPreApi(FundPreBaseDTO fundPreBaseDTO) {
        Instant start = Instant.now();
        log.info("FundApproveServiceImpl.callFundPreApi begin fundPreBaseDTO:{} ", fundPreBaseDTO);
        try {
            //调用资方预审批接口每个资方都是一个具体的实现了并统一实现FundPreApiStrategy接口
            String fundCode = fundPreBaseDTO.getFundCode();
            //根据调用方传来的资方编码调用对应资方预审批接口
            FundPreApiStrategy fundPreStrategy = fundPreStrategyFactory.getFundPreStrategy(fundCode);
            try {
                Boolean beginEvent = fundPreStrategy.beginEvent(fundPreBaseDTO);
                if (Objects.nonNull(beginEvent) && beginEvent) {
                    fundPreStrategy.send(fundPreBaseDTO);
                    fundPreBaseDTO.setCallFundPreStatus(SUCCESS);
                } else {
                    fundPreBaseDTO.setCallFundPreStatus(NOT_CALLED);
                }

            } catch (BusinessException e) {
                fundPreBaseDTO.setCallFundPreStatus(FAIL);
                fundPreBaseDTO.setCallFundRemark(e.getMessage());
                log.error("FundApproveServiceImpl.callFundPreApi e:", e);
            } catch (Exception e) {
                fundPreBaseDTO.setCallFundPreStatus(FAIL);
                log.error("FundApproveServiceImpl.callFundPreApi e:", e);
            } finally {
                fundPreStrategy.endEvent(fundPreBaseDTO);
            }
        } catch (Exception e) {
            log.error("FundApproveServiceImpl.callFundPreApi e:", e);
            throw new BusinessException(e);
        }
        Instant end = Instant.now();
        // 计算并打印耗时
        Duration duration = Duration.between(start, end);
        log.info("FundApproveServiceImpl.callFundPreApi Task took:{} ms", duration.toMillis());
        log.info("FundApproveServiceImpl.callFundPreApi end fundPreBaseDTO:{} ", fundPreBaseDTO);
        return true;
    }

    private FundInfoEntity getFundInfoByFundCode(FundEnum fundCode) {
        return fundInfoMapper.selectOne(new LambdaQueryWrapper<FundInfoEntity>()
                .eq(FundInfoEntity::getCode, fundCode.name())
                .eq(FundInfoEntity::getDeleteFlag, 0)
        );
    }

    private Boolean updateFundPreDate(FundPreBaseDTO fundPreBaseDTO, Integer fundId) {
        InitStatusEnums callFundPreStatus = fundPreBaseDTO.getCallFundPreStatus();
        // 未调用直接返回
        if (callFundPreStatus == NOT_CALLED) {
            return false;
        }
        PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                .select(PreFundInfoEntity::getId, PreFundInfoEntity::getRetry)
                .eq(PreFundInfoEntity::getPreId, fundPreBaseDTO.getPreId())
                .eq(PreFundInfoEntity::getFundId, fundId)
                .eq(PreFundInfoEntity::getDeleteFlag, 0));
        Integer retry = Convert.toInt(preFundInfoEntity.getRetry(), 0) + 1;
        //判断是否达到重试次数限制
        String callFundRemark = fundPreBaseDTO.getCallFundRemark();
        if (retry >= FundConstant.MAX_PRE_RETRY_COUNT && callFundPreStatus == FAIL) {
            callFundRemark = callFundRemark + "-" + StrUtil.blankToDefault(callFundRemark, "失败重试次数达到上限");
        }
        preFundInfoMapper.update(new LambdaUpdateWrapper<PreFundInfoEntity>()
                .setIncrBy(PreFundInfoEntity::getRetry, 1)
                .set(PreFundInfoEntity::getStatus, callFundPreStatus)

                .set(PreFundInfoEntity::getFundRemark, callFundRemark)
                .set(PreFundInfoEntity::getFundResult, ObjUtil.defaultIfNull(fundPreBaseDTO.getCallFundPreResult(), PreFundResultEnum.WAIT))
                .eq(PreFundInfoEntity::getPreId, fundPreBaseDTO.getPreId())

                .eq(PreFundInfoEntity::getFundId, fundId)
                .eq(PreFundInfoEntity::getDeleteFlag, 0));
        //预审拒绝重新发起预审
        if (ObjUtil.equals(fundPreBaseDTO.getCallFundPreResult(), PreFundResultEnum.REJECT)) {
            PreApproveFundStatusDTO preApproveFundStatusDTO = new PreApproveFundStatusDTO()
                    .setPreId(fundPreBaseDTO.getPreId())
                    .setFundId(fundPreBaseDTO.getFundId())
                    .setStatus(PreFundResultEnum.REJECT)
                    .setCreditAmt(BigDecimal.ZERO)
                    .setFailReason(callFundRemark);
            log.info("FundApproveServiceImpl.updateFundPreDate preId:{} created: {}", fundPreBaseDTO.getPreId(), JSONUtil.toJsonStr(preApproveFundStatusDTO));
            orderFeign.preUpdateFundStatus(preApproveFundStatusDTO);
            log.info("FundApproveServiceImpl.updateFundPreDate preId:{} end", fundPreBaseDTO.getPreId());
        }
        return true;
    }


    private Boolean updateFundFinalDate(FundFinalBaseDTO fundFinalBaseDTO, Integer fundId) {
        InitStatusEnums callFundFinalStatus = fundFinalBaseDTO.getCallFundFinalStatus();

        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .select(FinalFundInfoEntity::getId, FinalFundInfoEntity::getRetry)
                .eq(FinalFundInfoEntity::getOrderId, fundFinalBaseDTO.getOrderId())
                .eq(FinalFundInfoEntity::getFundId, fundId)
                .eq(FinalFundInfoEntity::getDeleteFlag, 0));
        Integer retry = Convert.toInt(finalFundInfoEntity.getRetry(), 0) + 1;
        //判断是否达到重试次数限制
        String callFundRemark = fundFinalBaseDTO.getCallFundRemark();
        //判断是否达到重试次数限制


        if (retry >= FundConstant.MAX_FINAL_RETRY_COUNT && callFundFinalStatus == FAIL) {
            callFundRemark = StrUtil.blankToDefault(fundFinalBaseDTO.getCallFundRemark(), "失败重试次数达到上限");

            // 重试大于上线将订单驳回到补录
            FinalApproveFundStatusDTO fundStatusDTO = new FinalApproveFundStatusDTO();
            fundStatusDTO.setOrderId(fundFinalBaseDTO.getOrderId());
            fundStatusDTO.setFundId(fundId);
            fundStatusDTO.setStatus(PreFundResultEnum.BACK);
            fundStatusDTO.setFundCreditAmt(BigDecimal.ZERO);
            fundStatusDTO.setFundRemark(callFundRemark);
            orderFeign.updateFundFinalStatus(fundStatusDTO);

        }
        finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                .setIncrBy(FinalFundInfoEntity::getRetry, 1)
                .set(FinalFundInfoEntity::getStatus, callFundFinalStatus)
                .set(FinalFundInfoEntity::getFundRemark, callFundRemark)
                .set(FinalFundInfoEntity::getFundResult, PreFundResultEnum.WAIT)
                .eq(FinalFundInfoEntity::getOrderId, fundFinalBaseDTO.getOrderId())
                .eq(FinalFundInfoEntity::getFundId, fundId)
                .eq(FinalFundInfoEntity::getDeleteFlag, 0));

        return true;
    }

    //    private PreFundInfoEntity getPreFundInfoByPreIdAndFundId(Integer preId, Integer fundId) {
    //        return preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
    //                        .eq(PreFundInfoEntity::getPreId, preId)
    //                        .eq(PreFundInfoEntity::getFundId, fundId)
    //                        .eq(PreFundInfoEntity::getDeleteFlag, 0)
    //                        .orderByDesc(PreFundInfoEntity::getCreateTime)
    //                , false
    //        );
    //    }
    //
    private FinalFundInfoEntity getFinalFundInfoByPreIdAndFundId(Integer orderId, Integer fundId) {
        return finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, fundId)
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false
        );
    }


    /**
     * Call Fund Final API
     *
     * @param fundFinalBaseDTO 基金最终基数 DTO
     * @return {@link Boolean }
     */
    public Boolean callFundFinalApi(FundFinalBaseDTO fundFinalBaseDTO) {
        Instant start = Instant.now();
        log.info("FundApproveServiceImpl.callFundFinalApi begin fundFinalBaseDTO:{} ", fundFinalBaseDTO);
        try {
            //调用资方预审批接口每个资方都是一个具体的实现了并统一实现FundPreApiStrategy接口
            String fundCode = fundFinalBaseDTO.getFundCode();
            //根据调用方传来的资方编码调用对应资方预审批接口
            FundFinalApiStrategy fundFinalStrategy = fundFinalStrategyFactory.getFundFinalStrategy(fundCode);
            try {
                Boolean beginEvent = fundFinalStrategy.beginEvent(fundFinalBaseDTO);
                if (beginEvent) {
                    fundFinalStrategy.send(fundFinalBaseDTO);
                }
                fundFinalBaseDTO.setCallFundFinalStatus(SUCCESS);
            } catch (BusinessException e) {
                fundFinalBaseDTO.setCallFundFinalStatus(FAIL);
                fundFinalBaseDTO.setCallFundRemark(e.getMessage());
                log.error("FundApproveServiceImpl.callFundFinalApi orderId:{} e:{}", fundFinalBaseDTO.getOrderId(), e.getMessage());
            } catch (Exception e) {
                fundFinalBaseDTO.setCallFundFinalStatus(FAIL);
                log.error("FundApproveServiceImpl.callFundFinalApi orderId:{} e:{}", fundFinalBaseDTO.getOrderId(), e.getMessage());
            } finally {
                fundFinalStrategy.endEvent(fundFinalBaseDTO);
            }
        } catch (Exception e) {
            log.error("FundApproveServiceImpl.callFundFinalApi orderId:{} e:", fundFinalBaseDTO.getOrderId(), e);
            throw new BusinessException(e);
        }
        Instant end = Instant.now();
        // 计算并打印耗时
        Duration duration = Duration.between(start, end);
        log.info("FundApproveServiceImpl.callFundFinalApi Task took:{} ms", duration.toMillis());
        log.info("FundApproveServiceImpl.callFundFinalApi end fundFinalBaseDTO:{} ", fundFinalBaseDTO);
        return true;
    }

    /**
     * 获取主要申请信息
     *
     * @param fundPreBaseDTO 基金 pre base dto
     * @return {@link Integer }
     */
    public Integer getMainApplyInfo(FundPreBaseDTO fundPreBaseDTO) {
        log.info("FundApproveServiceImpl.getMainApplyInfo begin fundPreBaseDTO:{} ", fundPreBaseDTO);
        try {
            //直接往fundPreBaseDTO set 数据无需创建新对象
            //用户基本信息
            CustomerBaseDTO customerBaseDTO = fundApproveMapper.getCustomerPreBaseInfo(fundPreBaseDTO.getPreId());
            if (ObjUtil.isNotEmpty(customerBaseDTO)) {
                BeanUtil.copyProperties(customerBaseDTO, fundPreBaseDTO);
            }

            fundPreBaseDTO.setContactPersonList(fundApproveMapper.getPreContactPerson(fundPreBaseDTO.getPreId()));

        } catch (Exception e) {
            log.error("FundApproveServiceImpl.getMainApplyInfo e:", e);
            throw new BusinessException("获取主申请人信息失败");
        }
        log.info("FundApproveServiceImpl.getMainApplyInfo end fundPreBaseDTO:{} ", fundPreBaseDTO);
        return SUCCESS.getStatus();
    }

    /**
     * 获取汽车信息
     *
     * @param fundPreBaseDTO 基金 pre base dto
     * @return {@link Integer }
     */
    @Override
    public Integer getCarInfo(FundPreBaseDTO fundPreBaseDTO) {
        log.info("FundApproveServiceImpl.getCarInfo begin fundPreBaseDTO:{} ", fundPreBaseDTO);
        try {
            //直接往fundPreBaseDTO set 数据无需创建新对象
            //车辆基本信息
            log.info("FundApproveServiceImpl.getCarInfo fundPreBaseDTO preId:{} ", fundPreBaseDTO.getPreId());
            FundPreCarBaseDTO fundPreCarBaseDTO = fundApproveMapper.getCarPreBaseInfo(fundPreBaseDTO.getPreId());
            if (ObjUtil.isNotEmpty(fundPreCarBaseDTO)) {
                BeanUtil.copyProperties(fundPreCarBaseDTO, fundPreBaseDTO);
            }
        } catch (Exception e) {
            log.error("FundApproveServiceImpl.getCarInfo e:", e);
            throw new BusinessException("获取车辆信息失败");
        }
        log.info("FundApproveServiceImpl.getCarInfo end fundPreBaseDTO:{} ", fundPreBaseDTO);
        return SUCCESS.getStatus();
    }

    /**
     * 获取最终主要申请信息
     *
     * @param fundFinalBaseDTO 基金最终基数 DTO
     * @return {@link Integer }
     */
    @Override
    public Integer getFinalMainApplyInfo(FundFinalBaseDTO fundFinalBaseDTO) {
        log.info("FundApproveServiceImpl.getFinalMainApplyInfo begin fundFinalBaseDTO:{} ", fundFinalBaseDTO);
        try {
            //直接往fundPreBaseDTO set 数据无需创建新对象
            //用户基本信息
            CustomerBaseDTO customerBaseDTO = getCustomerBaseDTO(fundFinalBaseDTO.getOrderId());
            BeanUtil.copyProperties(customerBaseDTO, fundFinalBaseDTO);
            fundFinalBaseDTO.setContactPersonList(fundApproveMapper.getContactPerson(customerBaseDTO.getOrderId()));
//            fundFinalBaseDTO.setCompanyInfo(getCompanyInfo(customerBaseDTO.getOrderId()));
            log.info("FundApproveServiceImpl.getFinalMainApplyInfo fundFinalBaseDTO:{}", JSONUtil.toJsonStr(fundFinalBaseDTO));
        } catch (Exception e) {
            log.error("FundApproveServiceImpl.getFinalMainApplyInfo e:", e);
            throw new BusinessException("获取主申请人信息失败");
        }
        log.info("FundApproveServiceImpl.getFinalMainApplyInfo end fundFinalBaseDTO:{} ", fundFinalBaseDTO);
        return SUCCESS.getStatus();
    }

    @Override
    public CustomerBaseDTO getCustomerBaseDTO(Integer orderId) {
        return fundApproveMapper.getCustomerFinalBaseInfo(orderId);
    }

    @Override
    public CompanyInfo getCompanyInfo(Integer orderId) {
        OrderCompanyInfoEntity orderCompanyInfoEntity = orderCompanyInfoMapper.selectOne(new LambdaQueryWrapper<OrderCompanyInfoEntity>()
                .eq(OrderCompanyInfoEntity::getOrderId, orderId).eq(OrderCompanyInfoEntity::getDeleteFlag, 0));

        if (orderCompanyInfoEntity == null) {
            return null;
        }

        String industry = orderCompanyInfoEntity.getIndustry();
        if (industry == null) {
            log.info("order {} companyInfo not found industry", orderId);
            CompanyInfo companyIndustryInfo = fundApproveMapper.getCompanyIndustry(orderId);
            if (companyIndustryInfo != null && companyIndustryInfo.getIndustry() != null) {
                industry = companyIndustryInfo.getIndustry();
            }
        }
        return new CompanyInfo()
                .setCompanyType(orderCompanyInfoEntity.getCompanyType())
                .setCompanyDetailAddress(orderCompanyInfoEntity.getCompanyDetailAddress())
                .setApprovalDate(orderCompanyInfoEntity.getEstablishmentDate())
                .setIndustry(industry)
                .setValidityEndDate(orderCompanyInfoEntity.getValidityEndDate())
                .setValidityStartDate(orderCompanyInfoEntity.getValidityStartDate())
                .setSocialCreditCode(orderCompanyInfoEntity.getSocialCreditCode())
                .setCompanyName(orderCompanyInfoEntity.getCompanyName())
                .setLegalPerson(orderCompanyInfoEntity.getLegalPerson())
                .setCertificateNumber(orderCompanyInfoEntity.getCertificateNumber())
                ;
    }


    @Override
    public Boolean fundSyncOverdueRepayment(List<FundEnum> fundEnumList) {
        if (CollectionUtil.isEmpty(fundEnumList)) {
            return false;
        }
        for (FundEnum fundEnum : fundEnumList) {
            String fundCode = "REPAY_" + fundEnum.getFundCode();
            FundRepayStrategy fundRepayStrategy = fundRepayStrategyFactory.getFundRepayStrategy(fundCode);
            try {
                fundRepayStrategy.syncOverdueRepayment();
            } catch (BusinessException e) {
                log.error("FundApproveServiceImpl.fundSyncOverdueRepayment BusinessException e:{}", e.getMessage(), e);
            } catch (Exception e) {
                log.error("FundApproveServiceImpl.fundSyncOverdueRepayment Exception e:{}", e.getMessage(), e);
            }
        }
        return true;
    }

    /**
     * 获取最终汽车信息
     *
     * @param fundFinalBaseDTO 基金最终基数 DTO
     * @return {@link Integer }
     */
    public Integer getFinalCarInfo(FundFinalBaseDTO fundFinalBaseDTO) {
        log.info("FundApproveServiceImpl.getFinalCarInfo begin fundFinalBaseDTO:{} ", fundFinalBaseDTO);
        try {
            //直接往fundPreBaseDTO set 数据无需创建新对象
            //车辆基本信息
            FundPreCarBaseDTO fundPreCarBaseDTO = fundApproveMapper.getCarFinalBaseInfo(fundFinalBaseDTO.getOrderId());
            if (ObjUtil.isNotEmpty(fundPreCarBaseDTO)) {
                BeanUtil.copyProperties(fundPreCarBaseDTO, fundFinalBaseDTO);
            }
        } catch (Exception e) {
            log.error("FundApproveServiceImpl.getFinalCarInfo e:", e);
            throw new BusinessException("获取车辆信息失败");
        }
        log.info("FundApproveServiceImpl.getFinalCarInfo end fundFinalBaseDTO:{} ", fundFinalBaseDTO);
        return SUCCESS.getStatus();
    }


    /**
     * 更新订单放款状态为拒绝
     *
     * @param orderId 订单id
     * @param fundId  资方id
     */
    private void updateOrderPaymentStatusThrowFail(Integer orderId, Integer fundId, String remark) {
        log.info("YingFengTaskProcess.updateOrderPaymentStatusThrowFail begin orderId:{} fundId:{}", orderId, fundId);

        try {

            //放款单号
            OrderApproveFundPaymentStatusDTO fundPaymentStatusDTO = new OrderApproveFundPaymentStatusDTO()
                    .setOrderId(orderId)
                    .setFundId(fundId)
                    .setLoanApplyNo(null)
                    .setLoanAmt(null)
                    .setLoanPayTime(LocalDateTime.now())
                    .setFailReason(remark);

            fundPaymentStatusDTO.setStatus(OrderFundPaymentEnum.FAIL);


            orderFeign.updateFundPaymentStatus(fundPaymentStatusDTO);

            log.info("FundApproveServiceImpl.updateOrderPaymentStatusThrowFail end orderId:{} fundId:{}", orderId, fundId);
        } catch (Exception e) {
            log.error("FundApproveServiceImpl.updateOrderPaymentStatusThrowFail error e:{}", e.getMessage(), e);
            throw new BusinessException("更新订单放款状态异常");
        }
    }

    private void updateFinalFundPaymentStatue(Integer orderId, Integer fundId, Integer paymentStatus) {
        finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                .set(FinalFundInfoEntity::getPaymentStatus, paymentStatus)
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, fundId)
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );
    }


    /**
     * 长银放款失败同步处理
     */
    private void syncChangYinPaymentFailHandle(Integer orderId) {
        //判断订单是否为长银
        Long count = orderInfoMapper.selectCount(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(OrderInfoEntity::getId, orderId)
        );
        if (count < 1) {
            log.info("HengTongCopperCarDeptServiceImpl.syncChangYinPaymentFailMortgageInfo orderId:{} is not changYin", orderId);
            return;
        }
        OrderVehicleInfoEntity oldOrderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                .eq(OrderVehicleInfoEntity::getOrderId, orderId)
                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                .orderByDesc(OrderVehicleInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(oldOrderVehicleInfoEntity)) {
            log.info("HengTongCopperCarDeptServiceImpl.syncChangYinPaymentFailMortgageInfo oldOrderVehicleInfoEntity is null orderId:{}", orderId);
            return;
        }
        //获取最新vin订单
        OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                .eq(OrderVehicleInfoEntity::getVin, oldOrderVehicleInfoEntity.getVin())
                .ne(OrderVehicleInfoEntity::getOrderId, orderId)
                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                .orderByDesc(OrderVehicleInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderVehicleInfoEntity)) {
            log.info("HengTongCopperCarDeptServiceImpl.syncChangYinPaymentFailMortgageInfo orderVehicleInfoEntity is null orderId:{}", orderId);
            return;
        }
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderVehicleInfoEntity.getOrderId());
        if (ObjUtil.isNull(orderInfo)) {
            log.info("HengTongCopperCarDeptServiceImpl.syncChangYinPaymentFailMortgageInfo orderInfo is null orderId:{}", orderVehicleInfoEntity.getOrderId());
            return;
        }
        //判断是否为长银
        if (FundEnum.CHANG_YIN.getValue() != orderInfo.getFundId()) {
            log.info("HengTongCopperCarDeptServiceImpl.syncChangYinPaymentFailMortgageInfo orderInfo is not changYin orderId:{}", orderVehicleInfoEntity.getOrderId());
            return;
        }
        //判断是否为放款失败订单
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderVehicleInfoEntity.getOrderId())
                .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(finalFundInfo)) {
            log.info("HengTongCopperCarDeptServiceImpl.syncChangYinPaymentFailMortgageInfo finalFundInfo is null orderId:{}", orderId);
            return;
        }
        //判断是否放款拒绝
        if (FundPaymentStatusEnum.FAIL != finalFundInfo.getPaymentStatus()) {
            log.info("HengTongCopperCarDeptServiceImpl.syncChangYinPaymentFailMortgageInfo finalFundInfo is not FAIL orderId:{}", orderId);
            return;
        }
        //判断抵押链接是否为null
        if (StrUtil.isBlank(finalFundInfo.getRedirectUrl())) {
            log.info("HengTongCopperCarDeptServiceImpl.syncChangYinPaymentFailMortgageInfo finalFundInfo is null orderId:{}", orderId);
            return;
        }
        //更新对应orderId抵押信息
        FinalFundInfoEntity finalFundInfoUpdate = new FinalFundInfoEntity();
        finalFundInfoUpdate.setEntrustReceiptNo(finalFundInfo.getEntrustReceiptNo());
        finalFundInfoUpdate.setMortgageState(finalFundInfo.getMortgageState());
        finalFundInfoUpdate.setRedirectUrl(finalFundInfo.getRedirectUrl());
        finalFundInfoUpdate.setMortgageContractNo(finalFundInfo.getMortgageContractNo());
        finalFundInfoUpdate.setAgentRadio(finalFundInfo.getAgentRadio());
        finalFundInfoMapper.update(finalFundInfoUpdate, new LambdaUpdateWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );


    }
}
