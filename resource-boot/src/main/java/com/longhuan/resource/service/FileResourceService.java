package com.longhuan.resource.service;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.IService;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.resource.pojo.dto.*;
import com.longhuan.resource.pojo.entity.FileResourceEntity;
import com.longhuan.resource.pojo.vo.*;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface FileResourceService extends IService<FileResourceEntity> {

    List<FileVO> handlerResourceFile(List<MultipartFile> files, Integer fileBelong);

    String batchDownloadFiles(List<String> resourceIds, HttpServletResponse response);

    String downloadFile(String resourceId, HttpServletResponse response);

    ResponseEntity<byte[]> previewImg(String resourceId);

    ResponseEntity<byte[]> previewImg(String resourceId, String thumbnail);

    /**
     * 上传本地文件到法大大服务器
     * @param resourceIds
     * @return
     */
    //    List<String> batchFileTOFdd(List<String> resourceIds);

    /**
     * 法大大文件处理
     *
     * @param resourceIds
     * @return
     */
    boolean processFile(List<String> resourceIds);

    /**
     * 下载签署完成文件
     *
     * @param signZipDTO
     * @return
     */
    List<DownZipFileVO> downLoadSignZip(SignZipDTO signZipDTO);

    List<CarImageVO> getCarImage(Integer orderId, String code);

    /**
     * 查询文件内容列表
     *
     * @param resourceIds 文件ids
     * @return
     */
    List<FileResourceResultVO> getFileByResourceIds(List<String> resourceIds);

    /**
     * 下载第三方文件
     *
     * @param dto
     * @return
     */
    List<FileResourceResultVO> downLoadThirdResource(ThirdResourceDTO dto);

    boolean uploadFadd(List<String> resourceIds);

    /**
     * 解析竖排txt文件
     *
     * @param fileUid 文件uid
     * @return 一个二维字符串列表，其中每个内部列表表示文件中的一行数据，
     * 每个元素表示该行中的一个字段。
     * @throws FileNotFoundException 如果指定的文件不存在。
     * @throws IOException           如果读取文件时发生输入/输出错误。
     */
    List<List<String>> parseVerticalTxtByFileUid(String fileUid) throws FileNotFoundException, IOException;

    /**
     * 将文件保存到资源
     *
     * @param fileName 文件名
     * @param filePath 文件路径
     * @param suffix   后缀
     * @return {@link String }
     */
    default String saveFileToResource(String fileName, String filePath, String suffix) {
        FileResourceEntity fileResourceEntity = new FileResourceEntity();
        String fileUid = IdUtil.simpleUUID();
        fileResourceEntity.setFileName(fileName)
                .setFilePath(filePath)
                .setFileUid(fileUid)
                .setFileType(suffix)
                .setFileOldName(fileName);
        save(fileResourceEntity);
        return fileUid;
    }

    /**
     * 获取文件流及文件信息
     *
     * @param resourceIds
     * @return
     */
    List<FileResourceBytesVO> queryFileBytes(List<String> resourceIds);

    /**
     * 将签署后文件流写入文件
     *
     * @param dto
     * @return
     */
    FileResourceResultVO bytesWriteFile(WriteFileDTO dto);

    ResponseEntity<byte[]> fileContent(String resourceId);

    Result<FileInfoVO> fileInfo(String resourceId);

    FileVO uploadFileByUuid(String uuid, MultipartFile file);

    byte[] mergeImageToPdf(List<String> resourceIds);

    String getBase64ByResourceId(String resourceId);

    Map<String, List<List<String>>> parseCsv(Integer titleLine, List<String> fileUidList);

    void downloadFile(String resourceId, HttpServletResponse response, String range, Integer thumbnail);

    List<FileVO> uploadVerticalTxt(List<UploadVerticalTxtDTO> dtoList);

    /**
     * 上传竖线分割文本至sftp
     *
     * @param resourceIdList
     * @return
     */
    void uploadVerticalTxtToSftp(List<String> resourceIdList);

    String deleteFile(FileDelDTO fileDelDTO, LoginUser loginUser);

    /**
     * 根据文件地址从阿里云下载视频文件上传到服务器
     *
     * @param liveFileDTO
     * @return
     */
//    List<FileVO> getAliyunUploadResource(LiveFileDTO liveFileDTO);

    /**
     * 批量上传文件到 SFTP 服务器
     *
     * @param batchUploadSftpDTO sftp基本信息
     * @return {@link String}
     */
    void batchUploadSftp(BatchUploadSftpDTO batchUploadSftpDTO);

    /**
     * 批量下载zip包
     */
    void batchDownloadByNameZip(BatchDownloadByNameZipDTO batchDownloadByNameZipDTO, HttpServletResponse response);
}
