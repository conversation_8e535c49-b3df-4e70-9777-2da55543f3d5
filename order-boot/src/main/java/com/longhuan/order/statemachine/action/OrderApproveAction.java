package com.longhuan.order.statemachine.action;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.longhuan.common.core.enums.RoleEnum;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.DistributeAreaMapper;
import com.longhuan.order.mapper.OrderApproveDistributeMapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.mapper.OrderNodeRecordMapper;
import com.longhuan.order.pojo.entity.DistributeAreaEntity;
import com.longhuan.order.pojo.entity.OrderApproveDistributeEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.service.GpsInstallInfoService;
import com.longhuan.order.service.OrderSettleApplyInfoService;
import com.longhuan.order.service.impl.OrderSendMessageImpl;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.user.enums.TodoInfoEnums;
import com.longhuan.user.pojo.dto.TodoInfoMessageDTO;
import com.longhuan.user.pojo.dto.getUserIdByStoreIdAndRoleIdDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessageHeaders;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 状态转移动作，在转移进行的时候触发的动作
 */
@Slf4j
@Component
public class OrderApproveAction extends OrderNodeAction implements Action<States, Events> {

    private final OrderSendMessageImpl orderSendMessage;
    @Autowired
    private OrderApproveDistributeMapper orderApproveDistributeMapper;
    @Autowired
    private UserFeign userFeign;
    @Autowired
    private DistributeAreaMapper distributeAreaMapper;
    @Autowired
    private GpsInstallInfoService gpsInstallInfoService;

    public OrderApproveAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper, OrderSendMessageImpl orderSendMessage) {
        super(orderInfoMapper, orderNodeRecordMapper);
        this.orderSendMessage = orderSendMessage;
    }

    @Override
    public void execute(StateContext<States, Events> context) {


        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();

        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);


        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event, currentNode, nextNode);

        // 1. 修改订单表状态
        if (Boolean.TRUE.equals(target.getId().getValid())) {
            saveOrderNode(orderId, nextNode);
        }

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);

        // 3. 发送消息
        Integer currentEventCode = context.getEvent().getCode();
        if (Objects.equals(currentNode, States.RISK_FINAL_APPROVE.getNode())
                || Objects.equals(currentNode, States.PAYMENT_CONTRACT_APPROVAL.getNode())
                || (Objects.equals(currentNode, States.FUNDS_PAYMENT_APPROVAL.getNode()) && "1".equals(String.valueOf(currentEventCode)))
                || Objects.equals(currentNode, States.FUNDS_PAYMENT_FAIL.getNode())
                || Objects.equals(currentNode, States.PAYMENT_SUCCESS.getNode())
                ||  Objects.equals(currentNode, States.FUNDS_FINAL_APPROVE.getNode())&&ObjUtil.equals(currentNode,3)){
            orderSendMessage.sendMessage(orderId, currentNode, currentEventCode);
        }
        // 4.送入待分配任务池
        if (ObjUtil.equals(States.QUALITY_INSPECTION.getNode(), nextNode)
        || ObjUtil.equals(States.PAYMENT_APPLY_INFORMATION.getNode(), nextNode)) {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
            DistributeAreaEntity distributeAreaEntity = distributeAreaMapper.selectOne(new LambdaQueryWrapper<DistributeAreaEntity>()
                    .eq(DistributeAreaEntity::getStoreId, orderInfoEntity.getDeptId())
                    .eq(DistributeAreaEntity::getServiceDispatch, 1)
                    .eq(DistributeAreaEntity::getDeleteFlag, 0)
                    .orderByDesc(DistributeAreaEntity::getCreateTime)
                    .last("limit 1"));
            if (distributeAreaEntity != null) {
                Long count = orderApproveDistributeMapper.selectCount(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                        .eq(OrderApproveDistributeEntity::getOrderNumber, orderInfoEntity.getOrderNumber())
                        .eq(OrderApproveDistributeEntity::getOrderId, orderId)
                        .eq(OrderApproveDistributeEntity::getNode, nextNode)
                        .in(OrderApproveDistributeEntity::getState, 0, 1, 2, 5));
                if (count==0) {
                    OrderApproveDistributeEntity orderApproveDistributeEntity = new OrderApproveDistributeEntity()
                            .setOrderNumber(orderInfoEntity.getOrderNumber())
                            .setOrderId(orderId)
                            .setNode(nextNode)
                            .setNodeName(States.getByNode(nextNode).getDesc())
                            .setSource(0)
                            .setStoreId(orderInfoEntity.getDeptId())
                            .setTitle(orderInfoEntity.getCustomerName() + "-" + orderInfoEntity.getVehicleNumber() + "-" + orderInfoEntity.getStoreName())
                            .setState(0);
                    orderApproveDistributeEntity.setCustomerName(orderInfoEntity.getCustomerName())
                            .setCustomerPhone(orderInfoEntity.getCustomerPhone())
                            .setManagerId(orderInfoEntity.getManagerId())
                            .setStoreName(orderInfoEntity.getStoreName())
                            .setRegionName(orderInfoEntity.getRegionName())
                            .setVehicleNumber(orderInfoEntity.getVehicleNumber())
                            .setStepDispose(currentNode <= nextNode ? 0 : 1);
                    orderApproveDistributeMapper.insert(orderApproveDistributeEntity);
                }
            } else {
                List<Integer> userIds = userFeign.getUserIdByStoreIdAndRoleId(new getUserIdByStoreIdAndRoleIdDTO().setDeptId(orderInfoEntity.getDeptId()).setRoleId(RoleEnum.CUSTOMER_SERVICE_SPECIALIST.getId())).getData();
                List<TodoInfoMessageDTO.TodoUser> userDto = userFeign.searchUserNameBatch(userIds).getData().stream().map(e -> new TodoInfoMessageDTO.TodoUser().setName(e.getName()).setJobNumber(e.getJobNumber()).setPhoneNumber(e.getMobile())).toList();

                userFeign.dealMessage(new TodoInfoMessageDTO()
                        .setOrderNumber(orderInfoEntity.getOrderNumber())
                        .setOrderId(orderInfoEntity.getId())
                        .setNode(TodoInfoEnums.getByNode(nextNode))
                        .setSourceType(0)
                        .setRemark("订单：" + orderInfoEntity.getOrderNumber() + "待处理")
                        .setState(0)
                        .setTitle(orderInfoEntity.getCustomerName() + "-" + orderInfoEntity.getVehicleNumber() + "-" + orderInfoEntity.getStoreName())
                        .setAccessUrl(null)
                        .setTodoUserList(userDto)
                        .setCustomerName(orderInfoEntity.getCustomerName())
                        .setCustomerPhone(orderInfoEntity.getCustomerPhone())
                        .setManagerId(orderInfoEntity.getManagerId())
                        .setStoreName(orderInfoEntity.getStoreName())
                        .setRegionName(orderInfoEntity.getRegionName())
                        .setVehicleNumber(orderInfoEntity.getVehicleNumber()));
            }
            //结清状态自动生成结清单
        }else if (ObjUtil.equals(States.SETTLED.getNode(), nextNode)){
            try {
                OrderSettleApplyInfoService orderSettleApplyInfoService = SpringContentUtils.getBean(OrderSettleApplyInfoService.class);
                orderSettleApplyInfoService.addSettleApplyInfo(1,null,1,orderInfoMapper.selectById(orderId));
                //更新车派达放款时间及结清时间
                gpsInstallInfoService.updateBill(orderId);
            }catch (Exception e){
                log.error("OrderApproveAction generateSettleDocumentsFailed:",e);
            }
        }
    }



}