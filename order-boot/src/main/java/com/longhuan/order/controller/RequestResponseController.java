package com.longhuan.order.controller;

import com.longhuan.common.core.base.RequestResponseInfoEntity;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.mapper.RequestResponseInfoMapper;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 请求响应控制器
 *
 * <AUTHOR>
 * @date 2024/07/30
 */
@Api(tags = "请求响应信息接口")
@RestController
@RequestMapping("/api/v1/call/request/log")
@RequiredArgsConstructor
public class RequestResponseController {
    private final RequestResponseInfoMapper requestResponseInfoMapper;

    /**
     * 保存请求响应信息
     *
     * @param requestResponseInfoEntity 请求响应信息实体
     * @return {@link Result }<{@link Boolean }>
     */
    @PostMapping("/save")
    public Result<Boolean> save(@RequestBody RequestResponseInfoEntity requestResponseInfoEntity) {
        return Result.judge(requestResponseInfoMapper.insert(requestResponseInfoEntity) > 0);
    }
}
