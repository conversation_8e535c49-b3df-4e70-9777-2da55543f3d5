package com.longhuan.order.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.ProductInfoEntity;
import com.longhuan.order.pojo.entity.RiskCustomerBlacklistEntity;
import com.longhuan.order.pojo.entity.RiskCustomerBlacklistLogEntity;
import com.longhuan.order.pojo.vo.OrderVehicleGpsLogVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface RiskCustomerBlacklistService extends IService<RiskCustomerBlacklistEntity> {

    /**
     * 查询黑名单列表
     *
     * @param dto
     * @return {@link List< RiskCustomerBlacklistEntity>}
     */
    List<RiskCustomerBlacklistEntity> queryBlacklist(RiskCustomerBlacklistQueryDTO dto);

    /**
     * 根据客户id加入和名单
     *
     * @param customerId  客户id
     * @param blackReason 黑名单原因
     * @return {@link Boolean}
     */

    Boolean saveByCustomerId(Integer customerId, List<String> blackReason,String remarks);

    /**
     * 是否在黑名单中
     *
     * @param name     名称
     * @param phone    电话
     * @param idNumber 身份证号
     * @return {@link boolean}
     */
    boolean isBlacklist(String name, String phone, String idNumber);

    /**
     *  根据客户id查询黑名单
     * @param customerId 客户id
     * @return {@link boolean}
     */
    boolean isBlacklistByCustomerId(Integer customerId);

    /**
     * 根据客户id删除黑名单
     *
     * @param customerId 客户id
     * @return {@link Boolean}
     */
    Boolean deleteByCustomerId(Integer customerId);

    Result<Boolean> addBlacklist(RiskCustomerBlacklistAddDTO riskCustomerBlacklistAddDTO);

    Page<RiskCustomerBlacklistEntity> pageBlacklistlist(RiskCustomerBlacklistPageDTO riskCustomerBlacklistPageDTO);

    void downloadTemplate(HttpServletResponse response);

    Object importBlacklistlist(MultipartFile file);

    RiskCustomerBlacklistEntity details(RiskCustomerBlacklistAddDTO riskCustomerBlacklistAddDTO);

    Boolean edit(RiskCustomerBlacklistAddDTO riskCustomerBlacklistAddDTO);

    Page<RiskCustomerBlacklistLogEntity> pageBlacklistlistLog(RiskCustomerBlacklistAddDTO riskCustomerBlacklistAddDTO);

    Object importest(MultipartFile file, Integer type);

    Boolean timingBlacklist();

    Boolean updateBlacklist();

    Boolean policeBad(PoliceBadDTO policeBadDTO);

    Boolean historyAddBlacklis();
}
