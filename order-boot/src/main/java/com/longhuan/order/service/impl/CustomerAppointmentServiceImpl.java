package com.longhuan.order.service.impl;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.XML;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.approve.api.ApproveApi;
import com.longhuan.approve.api.constants.PreFileTypeEnums;
import com.longhuan.approve.api.pojo.dto.*;
import com.longhuan.approve.api.pojo.dto.changyin.*;
import com.longhuan.approve.api.pojo.dto.zhongheng.hengtong.HengTongATMCardInfoSyncDTO;
import com.longhuan.approve.api.pojo.vo.YingFengBindCardApplyVO;
import com.longhuan.approve.api.pojo.vo.YingFengBindCardVerifyCodeVO;
import com.longhuan.approve.api.pojo.vo.ZhongHengApiResult;
import com.longhuan.approve.api.pojo.vo.zhongheng.HengTongATMCardInfoSyncVO;
import com.longhuan.approve.api.pojo.vo.zhongheng.ZhongHengTYTLFaSongYZMVO;
import com.longhuan.approve.api.pojo.vo.zhongheng.ZhongHengTYTLJieYueVO;
import com.longhuan.approve.api.pojo.vo.zhongheng.ZhongHengTYTLQianYueVO;
import com.longhuan.common.core.base.RequestResponseInfoEntity;
import com.longhuan.common.core.constant.BankNameEnum;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.GlobalConstants;
import com.longhuan.common.core.constant.RequestResponseCode;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.core.util.BankNameUtils;
import com.longhuan.common.core.util.QrCodeUtils;
import com.longhuan.common.redis.pojo.DictVO;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.redis.util.DictUtils;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.order.converter.CustomerMortgageInfoConverter;
import com.longhuan.order.converter.CustomerSignInfoConverter;
import com.longhuan.order.converter.OrderGpsInfoConverter;
import com.longhuan.order.enums.*;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.feign.TongLianPayFeign;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.feign.ZhongRuiFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.*;
import com.longhuan.order.signBank.SignBankStrategy;
import com.longhuan.order.signBank.SignBankStrategyFactory;
import com.longhuan.order.signBank.vo.SignBankSendMessageVO;
import com.longhuan.order.signBank.vo.SignBankVerifyMessageVO;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.order.util.AESUtils;
import com.longhuan.order.util.SybUtil;
import com.longhuan.order.util.TlSignUtil;
import com.longhuan.order.util.TongLianReqUtils;
import com.longhuan.user.enums.TodoInfoEnums;
import com.longhuan.user.enums.UserTypeEnum;
import com.longhuan.user.pojo.dto.TodoInfoMessageDTO;
import com.longhuan.user.pojo.dto.UserStoreDTO;
import com.longhuan.user.pojo.dto.getUserIdByStoreIdAndRoleIdDTO;
import com.longhuan.user.pojo.vo.UserAndDeptUsersVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 客户预约服务实施
 *
 * <AUTHOR>
 * @date 2024/08/16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerAppointmentServiceImpl implements CustomerAppointmentService {
    private static final String SAVE_GPS_APPOINTMENT_INSTALLATION_LOCK = "setOpenInstallOrder:gps_installation_appointment:lock:";
    private final static String LICENSE_PLATE_NUMBER = "LICENSE_PLATE_NUMBER";
    private static final String RISK_NUMBER = "Pay_";
    private static final String Pay_URL_KEY = "TL_REQSN_";
    private final CustomerSignInfoMapper customerSignInfoMapper;
    private final CustomerSignInfoConverter customerSignInfoConverter;
    private final CustomerMortgageInfoMapper customerMortgageInfoMapper;
    private final CustomerMortgageInfoConverter customerMortgageInfoConverter;
    private final OrderFeeInfoMapper orderFeeInfoMapper;
    private final TongLianPayFeign tongLianPayFeign;
    private final OrderInfoMapper orderInfoMapper;
    private final DictUtils dictUtils;
    private final ProductInfoMapper productInfoMapper;
    private final OrderGpsInfoMapper orderGpsInfoMapper;
    private final OrderGpsInfoConverter orderGpsInfoConverter;
    private final OrderVehicleInfoMapper orderVehicleInfoMapper;
    private final RequestResponseInfoMapper requestResponseInfoMapper;
    private final BankAccountSignMapper bankAccountSignMapper;
    private final OrderStateService orderStateService;
    private final ManageBankAccountSignMapper manageBankAccountSignMapper;
    private final UserFeign userFeign;
    private final ApproveFeign approveFeign;
    private final FundMortgageModelConfigMapper fundMortgageModelConfigMapper;
    private final DataAreaMapper dataAreaMapper;
    private final StoreAddressInfoMapper storeAddressInfoMapper;
    private final ReviewMapper reviewMapper;
    private final TongLianReqUtils tongLianReqUtils;
    private final OrderAmountMapper orderAmountMapper;
    private final EnvUtil envUtil;
    private final GpsService gpsService;
    private final RedisService redisService;
    private final String TNAME = "LH_ORDER_INFO";
    private final ZhongRuiFeign zhongRuiFeign;
    private final FundMortgageInfoMapper fundMortgageInfoMapper;
    private final OrderPageInfoService orderPageInfoService;
    private final OrderFileMapper orderFileMapper;
    private final ContractFileService contractFileService;
    private final SwitchUtils switchUtils;
    private final FundSignInfoMapper fundSignInfoMapper;
    private final OrderFeeDetailService orderFeeDetailService;
    private final ContractService contractService;
    private final TlSignUtil tlSignUtil;
    private final AfterLoanPatchesEntityService afterLoanPatchesEntityService;
    private final OrderApproveDistributeService orderApproveDistributeService;
    private final FundUndoMortgageInfoEntityMapper fundUndoMortgageInfoEntityMapper;
    private final DistributeAreaMapper distributeAreaMapper;
    private final ApproveApi approveApi;
    private final OrderCustomerInfoMapper orderCustomerInfoMapper;
    private final OrderContractService orderContractService;
    private final SignTaskMapper signTaskMapper;
    private final SignBankStrategyFactory signBankStrategyFactory;
    private final FinalFundInfoMapper finalFundInfoMapper;
    @Value("${TongLian.SYB_ORGID}")
    private String SYB_ORGID;
    @Value("${TongLian.SYB_CUSID}")
    private String SYB_CUSID;
    @Value("${TongLian.SYB_APPID}")
    private String SYB_APPID;
    @Value("${TongLian.SIGN_TYPE}")
    private String SIGN_TYPE;
    @Value("${TongLian.SYB_RSACUSPRIKEY}")
    private String SYB_RSACUSPRIKEY;
    @Value("${TongLian.SYB_SM2PPRIVATEKEY}")
    private String SYB_SM2PPRIVATEKEY;
    @Value("${TongLian.SYB_MD5_APPKEY}")
    private String SYB_MD5_APPKEY;
    @Value("${TongLian.SYB_RSATLPUBKEY}")
    private String SYB_RSATLPUBKEY;
    @Value("${TongLian.SYB_SM2TLPUBKEY}")
    private String SYB_SM2TLPUBKEY;
    @Value("${TongLian.TL_CALL_BACK}")
    private String TL_CALL_BACK;
    @Value("${zhongrui.AES_KEY}")
    private String aesKey;
    @Value("${zhongrui.AES_IV}")
    private String aesIv;

    private final static String SAVE_MORTGAGE_INFO_KEY = "order:saveMortgageInfo:";

    private static @NotNull OrderFeeInfoEntity getNewEntity(OrderFeeInfoEntity orderFeeInfoEntity, String reqsn, Integer orderId) {
        OrderFeeInfoEntity newEntity = new OrderFeeInfoEntity();

        newEntity.setPaySnNum(reqsn);
        newEntity.setOrderId(orderId);

        newEntity.setGpsFee(orderFeeInfoEntity.getGpsFee());
        newEntity.setFeeType(orderFeeInfoEntity.getFeeType());
        newEntity.setGpsFeeStatus(orderFeeInfoEntity.getGpsFeeStatus());

        newEntity.setGpsTotalFee(orderFeeInfoEntity.getGpsTotalFee());
        newEntity.setGpsFee(orderFeeInfoEntity.getGpsFee());
        newEntity.setCarServiceFee(orderFeeInfoEntity.getCarServiceFee());

        newEntity.setPaymentPerformance(orderFeeInfoEntity.getPaymentPerformance());
        newEntity.setCashBackPerformance(orderFeeInfoEntity.getCashBackPerformance());
        newEntity.setOnceServiceFee(orderFeeInfoEntity.getOnceServiceFee());
        return newEntity;
    }

    public static String getPayNumber() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        String formattedDateTime = now.format(formatter);
        return RISK_NUMBER + formattedDateTime;
    }

    /**
     * 业务详情信息
     *
     * @param orderId 次序id
     * @return {@link BusinessDetailVO }
     */
    @Override
    public BusinessDetailVO businessDetail(Integer orderId) {
        BusinessDetailVO businessDetailVO = new BusinessDetailVO();
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, "订单信息不存在");
        businessDetailVO
                .setOrderId(orderInfoEntity.getId())
                .setOrderNumber(orderInfoEntity.getOrderNumber())
                .setApplyName(orderInfoEntity.getCustomerName())
                .setFundName(orderInfoEntity.getFundName())
                .setProductName(orderInfoEntity.getProductName())
                .setAppropriationAmount(orderInfoEntity.getApprovalAmount())
                .setAppropriationTerm(orderInfoEntity.getTerm())
                .setStore(orderInfoEntity.getStoreName())
                .setRegionId(orderInfoEntity.getRegionId())
                .setTeamId(orderInfoEntity.getTeamId())
                .setDeptId(orderInfoEntity.getDeptId());
        ProductInfoEntity productInfoEntity = productInfoMapper.selectById(orderInfoEntity.getProductId());
        if (ObjectUtil.isNotNull(productInfoEntity)) {
            businessDetailVO.setAnnualInterestRate(productInfoEntity.getIrr())
                    .setGuaranteeFeeTerm(productInfoEntity.getTerm())
                    .setGuaranteeFeeRate(productInfoEntity.getGuaranteeFee())
                    .setRepaymentType(orderInfoEntity.getRepayMethod());
        }
        List<OrderAmountEntity> orderAmountEntities = orderAmountMapper.selectList(new LambdaQueryWrapper<OrderAmountEntity>()
                .eq(OrderAmountEntity::getOrderId, orderId)
                .eq(OrderAmountEntity::getDeleteFlag, 0));
        if (CollUtil.isNotEmpty(orderAmountEntities)) {
            OrderAmountEntity orderAmountEntity = orderAmountEntities.get(0);
            businessDetailVO.setCustomerConfirmAmount(orderAmountEntity.getCustomerConfirmAmount());
        }

        return businessDetailVO;
    }

    /**
     * 签约详情信息
     *
     * @return {@link AppointmentSignDetailVO }
     */
    @Override
    public AppointmentSignDetailVO signDetail(Integer orderId) {
        List<AppointmentSignDetailVO> appointmentSignDetailVO = customerSignInfoMapper.selectJoinList(AppointmentSignDetailVO.class,
                new MPJLambdaWrapper<CustomerSignInfoEntity>()
                        .eq(CustomerSignInfoEntity::getOrderId, orderId)
                        .eq(CustomerSignInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
        );
        if (CollUtil.isEmpty(appointmentSignDetailVO)||ObjUtil.isNull(appointmentSignDetailVO.get(0))||ObjUtil.isNull(appointmentSignDetailVO.get(0).getSignType())) {
            AppointmentSignDetailVO addressVO = new AppointmentSignDetailVO();
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
            OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectById(orderInfoEntity.getCustomerId());
            if (ObjectUtil.isNotNull(orderCustomerInfoEntity)) {
                //居住地址
                addressVO.setSignProvinceName(orderCustomerInfoEntity.getResidentialProName());
                addressVO.setSignProvince(orderCustomerInfoEntity.getResidentialProvince());
                addressVO.setSignCityName(orderCustomerInfoEntity.getResidentialCityName());
                addressVO.setSignCity(orderCustomerInfoEntity.getResidentialCity());
                addressVO.setSignAreaName(orderCustomerInfoEntity.getResidentialAreaName());
                addressVO.setSignArea(orderCustomerInfoEntity.getResidentialArea());
                addressVO.setSignDetail(orderCustomerInfoEntity.getResidentialDetailedAddress());
            }
            addressVO.setReviewState(orderInfoEntity.getReviewState());
            return addressVO;
        }
        AppointmentSignDetailVO result = appointmentSignDetailVO.get(0);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        result.setReviewState(orderInfoEntity.getReviewState());
        result.setRegionId(orderInfoEntity.getRegionId());
        return result;
    }

    private DataAreaEntity getDataAreaEntity(String addressName, Integer type) {
        DataAreaEntity dataAreaEntity = dataAreaMapper.selectOne(new LambdaQueryWrapper<DataAreaEntity>()
                .eq(DataAreaEntity::getAreaName, addressName)
                .eq(DataAreaEntity::getDeleteFlag, 0)
                .eq(DataAreaEntity::getAreaType, type)
                .last("limit 1"));
        return ObjectUtil.isNotEmpty(dataAreaEntity) ? dataAreaEntity : null;
    }
    //修改签约方式
    @Override
    public Boolean updateSignType(Integer orderId, Integer signType) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        if(ObjectUtil.isEmpty(orderInfoEntity)){
            throw new BusinessException("订单信息不存在");
        }
        if(orderInfoEntity.getCurrentNode() != 3000){
            throw new BusinessException("节点不支持修改签约方式");
        }
        customerSignInfoMapper.update(new LambdaUpdateWrapper<CustomerSignInfoEntity>()
                .set(CustomerSignInfoEntity::getSignType, signType)
                .eq(CustomerSignInfoEntity::getOrderId, orderId)
                .eq(CustomerSignInfoEntity::getDeleteFlag, 0));
        return true;
    }
    /**
     * 救签约信息
     *
     * @param appointmentSignDetailVO 约会签约详情信息VO
     * @return {@link Boolean }
     */
    @Override
    public Boolean saveSignInfo(AppointmentSignDetailVO appointmentSignDetailVO, LoginUser loginUser) {
        String lockValue = IdUtil.randomUUID();
        String orderNumber = String.valueOf(appointmentSignDetailVO.getOrderId());
        String lockKey = "order:assign:saveSignInfo:" + orderNumber;
        try {
            log.info("CustomerAppointmentServiceImpl saveSignInfo appointmentSignDetailVO:{}", JSONUtil.toJsonStr(appointmentSignDetailVO));
            if (redisService.tryLock(lockKey, lockValue, 60)) {
                Integer id = appointmentSignDetailVO.getId();
                if (ObjectUtil.isEmpty(appointmentSignDetailVO.getSignType())) {
                    throw new BusinessException("签约类型不能为空");
                }
                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(appointmentSignDetailVO.getOrderId());
                if (ObjectUtil.isEmpty(orderInfoEntity)) {
                    throw new BusinessException("订单信息不存在");
                }
                if(ObjectUtil.isNotNull(orderInfoEntity.getReviewState()) && List.of(1,2,3).contains(orderInfoEntity.getReviewState())){
                    throw new BusinessException("改订单已提交面签审核，不能提交");
                }

                CustomerSignInfoEntity customerSignInfoEntity = customerSignInfoConverter.dtoToEntity(appointmentSignDetailVO);
                CustomerSignInfoEntity oldCustomerSignInfoEntity = customerSignInfoMapper.selectOne(new LambdaQueryWrapper<CustomerSignInfoEntity>()
                        .eq(CustomerSignInfoEntity::getOrderId, appointmentSignDetailVO.getOrderId())
                        .eq(CustomerSignInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(CustomerSignInfoEntity::getCreateTime), false);
                if (ObjUtil.isNotNull(oldCustomerSignInfoEntity)) {
                    customerSignInfoEntity.setId(oldCustomerSignInfoEntity.getId());
                    customerSignInfoMapper.updateById(customerSignInfoEntity);
                } else {
                    customerSignInfoMapper.insert(customerSignInfoEntity);
                }
                orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                        .set(OrderInfoEntity::getReviewState, 1)
                        .eq(OrderInfoEntity::getId, appointmentSignDetailVO.getOrderId())
                        .ne(OrderInfoEntity::getReviewState, 2)
                );
                orderPageInfoService.updateOrderPageInfo(appointmentSignDetailVO.getOrderId(), States.MANAGER_INTERVIEW, 1);
                sendAppointmentEvent(appointmentSignDetailVO.getOrderId(), loginUser.getUserId(),
                        loginUser.getUserType(), Events.REVIEW_APPOINTMENT_START);
                //送入待分配任务池
                if (orderInfoEntity.getReviewState()!=1&&ObjUtil.equals(1,appointmentSignDetailVO.getIsDispatch())) {
                    faceToFaceDispatch(appointmentSignDetailVO.getSignType(), orderInfoEntity);
                }
            } else {
                throw new BusinessException("正在提交中,请勿重复点击");
            }
        }catch (BusinessException e){
            throw e;
        } catch (Exception e) {
            log.error("CustomerAppointmentServiceImpl.saveSignInfo error processing  with ID: {}. Exception: {}", lockKey, e.getMessage(), e);
        } finally {
            // 释放锁
            redisService.releaseLock(lockKey, lockValue);
            log.info("CustomerAppointmentServiceImpl.saveSignInfo Released lock with ID: {}", lockKey);
        }
        return true;
    }

    private void faceToFaceDispatch(Integer signType, OrderInfoEntity orderInfoEntity) {
        DistributeAreaEntity distributeAreaEntity = distributeAreaMapper.selectOne(new LambdaQueryWrapper<DistributeAreaEntity>()
                .eq(DistributeAreaEntity::getStoreId, orderInfoEntity.getDeptId())
                .eq(DistributeAreaEntity::getServiceDispatch, 1)
                .eq(DistributeAreaEntity::getDeleteFlag, 0)
                .orderByDesc(DistributeAreaEntity::getCreateTime)
                .last("limit 1"));
        if (distributeAreaEntity != null) {
            OrderApproveDistributeEntity orderApproveDistributeEntity = new OrderApproveDistributeEntity()
                    .setOrderId(orderInfoEntity.getId())
                    .setOrderNumber(orderInfoEntity.getOrderNumber())
                    .setNode(States.MANAGER_INTERVIEW.getNode())
                    .setNodeName(States.MANAGER_INTERVIEW.getDesc())
                    .setTitle(orderInfoEntity.getCustomerName() + "-" + orderInfoEntity.getVehicleNumber() + "-" + orderInfoEntity.getStoreName())
                    .setSource(0)
                    .setSignType(signType)
                    .setStoreId(orderInfoEntity.getDeptId())
                    .setState(0);
            orderApproveDistributeEntity.setCustomerName(orderInfoEntity.getCustomerName())
                    .setCustomerPhone(orderInfoEntity.getCustomerPhone())
                    .setManagerId(orderInfoEntity.getManagerId())
                    .setStoreName(orderInfoEntity.getStoreName())
                    .setRegionName(orderInfoEntity.getRegionName())
                    .setVehicleNumber(orderInfoEntity.getVehicleNumber());

            OrderApproveDistributeEntity oldOrderApproveDistributeEntity = orderApproveDistributeService.getOne(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                    .eq(OrderApproveDistributeEntity::getOrderId, orderInfoEntity.getId())
                    .eq(OrderApproveDistributeEntity::getNode, States.MANAGER_INTERVIEW.getNode())
                    .in(OrderApproveDistributeEntity::getState, 0,1,2,5)
                    .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderApproveDistributeEntity::getCreateTime),false
            );
            if (oldOrderApproveDistributeEntity != null) {
                if (ObjUtil.isNull(oldOrderApproveDistributeEntity.getUserId())) {
                    orderApproveDistributeService.assignInterviews(orderApproveDistributeEntity);
                }
            }else {
                orderApproveDistributeService.save(orderApproveDistributeEntity);
                OrderApproveDistributeEntity byId = orderApproveDistributeService.getById(orderApproveDistributeEntity.getId());
                if (ObjUtil.equals(0,signType)){
                    orderApproveDistributeService.assignInterviews(byId);
                }
            }
        } else {
            List<Integer> userIds = userFeign.getUserIdByStoreIdAndRoleId(new getUserIdByStoreIdAndRoleIdDTO().setDeptId(orderInfoEntity.getDeptId()).setRoleId(RoleEnum.CUSTOMER_SERVICE_SPECIALIST.getId())).getData();
            List<TodoInfoMessageDTO.TodoUser> userDto = userFeign.searchUserNameBatch(userIds).getData().stream().map(e -> new TodoInfoMessageDTO.TodoUser().setName(e.getName()).setJobNumber(e.getJobNumber()).setPhoneNumber(e.getMobile())).toList();
            TodoInfoMessageDTO todoInfoMessageDTO = new TodoInfoMessageDTO()
                    .setOrderNumber(orderInfoEntity.getOrderNumber())
                    .setOrderId(orderInfoEntity.getId())
                    .setNode(TodoInfoEnums.MANAGER_INTERVIEW)
                    .setSourceType(0)
                    .setRemark("订单：" + orderInfoEntity.getOrderNumber() + "待处理")
                    .setState(0)
                    .setTitle(orderInfoEntity.getCustomerName() + "-" + orderInfoEntity.getVehicleNumber() + "-" + orderInfoEntity.getStoreName())
                    .setAccessUrl(null)
                    .setTodoUserList(userDto)
                    .setCustomerName(orderInfoEntity.getCustomerName())
                    .setCustomerPhone(orderInfoEntity.getCustomerPhone())
                    .setManagerId(orderInfoEntity.getManagerId())
                    .setStoreName(orderInfoEntity.getStoreName())
                    .setRegionName(orderInfoEntity.getRegionName())
                    .setVehicleNumber(orderInfoEntity.getVehicleNumber());
            log.info("CustomerAppointment send todoMessage:{}", todoInfoMessageDTO);
//            if (userFeign.dealMessage(todoInfoMessageDTO).getData().getIsUpdate()){
//                for (Integer userId : userIds) {
//                    userFeign.sendEventWithDataByUserId(userId, "MANAGER_INTERVIEW:orderId:"+orderInfoEntity.getId(),
//                            LocalDateTimeUtil.format(LocalDateTime.now(), DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss")) + "\n"
//                                    + orderInfoEntity.getStoreName() + "-" + orderInfoEntity.getCustomerName()
//                                    + "-" + orderInfoEntity.getVehicleNumber() + "-" + States.getDescriptionByCode(States.MANAGER_INTERVIEW.getNode())
//                                    + "\n当前节点已指派至您处理，请注意处理");
//                }
//            }
        }

    }
    @Override
    public Boolean onlineSigningAssignments(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, ()->new BusinessException("当前订单不存在"));
        Assert.isFalse(ObjUtil.equals(orderInfoEntity.getReviewState(),2),()->new BusinessException("签约视频已录制完成"));
        Assert.isFalse(ObjUtil.equals(orderInfoEntity.getReviewState(),0),()->new BusinessException("未发起面签"));
        faceToFaceDispatch(0, orderInfoEntity);
        return true;
    }
    /**
     * 抵押详情信息
     *
     * @param orderId 订单 ID
     * @return {@link Objects }
     */
    @Override
    public AppointmentMortgageDetailVO mortgageDetail(Integer orderId) {
        log.info("mortgageDetail orderId = {}", orderId);
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectJoinList(OrderInfoEntity.class,
                new MPJLambdaWrapper<OrderInfoEntity>()
                        .selectAll(OrderInfoEntity.class)
                        .selectAs(OrderVehicleInfoEntity::getVehicleNumber, OrderInfoEntity::getVehicleNumber)
                        .innerJoin(OrderVehicleInfoEntity.class, on ->
                                on.eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                                        .eq(OrderVehicleInfoEntity::getOrderId, OrderInfoEntity::getId))
                        .eq(OrderInfoEntity::getId, orderId)
                        .orderByDesc(OrderVehicleInfoEntity::getId)
        );

//        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
//        Assert.notNull(orderInfoEntity, "订单信息不存在");
        if (CollUtil.isEmpty(orderInfoEntityList)) {
            throw new BusinessException("订单信息不存在");
        }
        OrderInfoEntity orderInfoEntity = orderInfoEntityList.get(0);
        //查询订单车辆信息
        String vehicleNumber = orderInfoEntity.getVehicleNumber();
        Integer paymentType = orderInfoEntity.getPaymentType();
        Integer fundId = orderInfoEntity.getFundId();
        String fundName = orderInfoEntity.getFundName();
        String source = orderInfoEntity.getSource();

        List<AppointmentMortgageDetailVO> appointmentMortgageDetailVOList = customerMortgageInfoMapper.selectJoinList(AppointmentMortgageDetailVO.class,
                new MPJLambdaWrapper<CustomerMortgageInfoEntity>()
                        .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                        .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
        );
        log.info("appointmentMortgageDetailVOList.size = {}", appointmentMortgageDetailVOList.size());
        AppointmentMortgageDetailVO appointmentMortgageDetailVO = new AppointmentMortgageDetailVO();
        if (CollUtil.isNotEmpty(appointmentMortgageDetailVOList)) {
            appointmentMortgageDetailVO = appointmentMortgageDetailVOList.get(0);
        }
        if (Objects.isNull(paymentType) || paymentType <= 0) {
            DictVO dict = dictUtils.getDictInfo(LICENSE_PLATE_NUMBER);
            List<DictVO.DictItemInfo> dictItemInfo = dict.getDictItemInfo();
            DictVO.DictItemInfo dictItem = dictItemInfo.stream().filter(item -> vehicleNumber.contains(item.getName())).findFirst().orElse(null);
            paymentType = Objects.nonNull(dictItem) ? 2 : 1;
            orderInfoEntity.setPaymentType(paymentType);
            orderInfoMapper.updateById(orderInfoEntity);
        }

        //--------------edit by zangxx at 2023/08/08  start---------------
        //配置
        List<FundMortgageModelConfigEntity> configEntityList = fundMortgageModelConfigMapper.selectList(new LambdaQueryWrapper<FundMortgageModelConfigEntity>()
                .eq(FundMortgageModelConfigEntity::getFundId, fundId)
                .eq(FundMortgageModelConfigEntity::getDeleteFlag, 0)
        );
        log.info("CustomerAppointmentServiceImpl configEntityList.size = {}", configEntityList.size());
        if (CollUtil.isNotEmpty(configEntityList)) {
            //抵押方式可选
            List<Integer> mortgageTypeScope = new ArrayList<>();
            //办理渠道可选
            List<Integer> mortgageChannelScope = new ArrayList<>();
            configEntityList.forEach(item -> {
                mortgageTypeScope.add(item.getMortgageType());
                if (ObjUtil.equals(item.getMortgageType(), 1)) {
                    mortgageChannelScope.add(item.getMortgageChannel());
                }
            });
            //            appointmentMortgageDetailVO.setMortgageType(configEntityList.get(0).getMortgageType());
            appointmentMortgageDetailVO.setMortgageTypeScope(mortgageTypeScope);
            appointmentMortgageDetailVO.setMortgageChannelScope(mortgageChannelScope);
            appointmentMortgageDetailVO.setChannelType(configEntityList.get(0).getChannelType());
        }
        String city = vehicleNumber.substring(0, 2);
        log.info("CustomerAppointmentServiceImpl city = {}", city);
        DataAreaEntity dataAreaEntity = dataAreaMapper.selectOne(new LambdaQueryWrapper<DataAreaEntity>()
                .like(DataAreaEntity::getCarRegion, city));
        if (ObjectUtil.isNotEmpty(dataAreaEntity)) {
            //抵押城市
            appointmentMortgageDetailVO.setVehicleMortgageCity(dataAreaEntity.getAreaName());
        }
        StoreAddressInfoEntity storeAddressInfo = storeAddressInfoMapper.selectById(appointmentMortgageDetailVO.getStoreId());
        if (ObjectUtil.isNotEmpty(storeAddressInfo)) {
            appointmentMortgageDetailVO.setStoreAreaName(storeAddressInfo.getAreaName());
            appointmentMortgageDetailVO.setStoreCityName(storeAddressInfo.getCityName());
            appointmentMortgageDetailVO.setStoreProvinceName(storeAddressInfo.getProvinceName());
        }
        //抵押待办-返回抵押相关状态
        appointmentMortgageDetailVO.setAdvanceMortgageState(orderInfoEntity.getAdvanceMortgageState())
                .setMortgageState(orderInfoEntity.getMortgageState());
        appointmentMortgageDetailVO.setCurrentNode(orderInfoEntity.getCurrentNode());

        //--------------edit by zangxx at 2023/08/08  end---------------

        appointmentMortgageDetailVO.setVehicleNumber(vehicleNumber)
                .setPaymentType(paymentType)
                .setFundId(fundId)
                .setFundName(fundName)
                .setMortgageChannelStr(ObjectUtil.isNotNull(appointmentMortgageDetailVO.getMortgageChannel()) ? dictUtils.getDictLabel(GlobalConstants.DictType.MORTGAGE_CHANNEL.name(), appointmentMortgageDetailVO.getMortgageChannel()) : "")
        ;
        FundUndoMortgageInfoEntity fundUndoMortgageInfoEntity = fundUndoMortgageInfoEntityMapper.selectOne(new LambdaQueryWrapper<FundUndoMortgageInfoEntity>()
                .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                .eq(FundUndoMortgageInfoEntity::getOperateType, "2")
                .eq(FundUndoMortgageInfoEntity::getHistory, 0)
                .in(FundUndoMortgageInfoEntity::getMortgageStatus, MortgageEnums.MORTGAGE_STATUS_CANCEL_SUCCESS.getCode().toString(), MortgageEnums.MORTGAGE_STATUS_CANCEL_SUCCESS_HAVE.getCode().toString())
        );
        if (ObjUtil.isNotNull(fundUndoMortgageInfoEntity)) {
            appointmentMortgageDetailVO.setReleaseMortgageState(fundUndoMortgageInfoEntity.getMortgageStatus());
        }
        appointmentMortgageDetailVO.setSource(source);
        //增加代理人姓名、代理人手机号、邮寄地址
        List<FinalFundInfoEntity> finalFundInfoEntity = finalFundInfoMapper.selectList(Wrappers.<FinalFundInfoEntity>lambdaQuery().eq(FinalFundInfoEntity::getOrderId, orderId));
        if (CollUtil.isNotEmpty(finalFundInfoEntity)) {
            FinalFundInfoEntity finalFundInfoEntitys = finalFundInfoEntity.get(0);
            appointmentMortgageDetailVO.setProxyName(finalFundInfoEntitys.getProxyName());
            appointmentMortgageDetailVO.setProxyPhone(finalFundInfoEntitys.getProxyPhone());
            appointmentMortgageDetailVO.setRecAddress(finalFundInfoEntitys.getRecipientAddress());
            appointmentMortgageDetailVO.setFailReason(finalFundInfoEntitys.getFailReason());
            appointmentMortgageDetailVO.setSpName(finalFundInfoEntitys.getSpName());
        }
        //查询车辆抵押省市城市
        CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                .orderByDesc(CustomerMortgageInfoEntity::getUpdateTime)
                .last("LIMIT 1"));
        if (customerMortgageInfoEntity!=null){
            appointmentMortgageDetailVO.setProvincialCode(String.valueOf(customerMortgageInfoEntity.getMortgageProvince()));
            appointmentMortgageDetailVO.setProvinceName(customerMortgageInfoEntity.getMortgageProvinceName());
            appointmentMortgageDetailVO.setCityCode(String.valueOf(customerMortgageInfoEntity.getMortgageCity()));
            appointmentMortgageDetailVO.setCityName(customerMortgageInfoEntity.getMortgageCityName());
        }
        return appointmentMortgageDetailVO;
    }

    /**
     * 保存抵押贷款信息
     *
     * @param appointmentMortgageDetailDTO 预约抵押贷款详情 DTO
     * @param loginUser                    登录用户
     * @return {@link Boolean }
     */
    @Override
    public Boolean saveMortgageInfo(AppointmentMortgageDetailDTO appointmentMortgageDetailDTO, LoginUser loginUser) {
        log.info("CustomerAppointmentServiceImpl.saveMortgageInfo orderId = {}", appointmentMortgageDetailDTO.getOrderId());
        String lockKey = SAVE_MORTGAGE_INFO_KEY + appointmentMortgageDetailDTO.getOrderId();
        String lockValue = UUID.randomUUID().toString();
        try {
            Boolean lockSuccess = redisService.tryLock(lockKey, lockValue, 5);
            if (!lockSuccess) {
                log.info("CustomerAppointmentServiceImpl.saveMortgageInfo, lock failed, orderId = {}", lockKey);
                throw new BusinessException("操作频繁，请稍后再试");
            }

            CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoConverter.dtoToEntity(appointmentMortgageDetailDTO);

            List<CustomerMortgageInfoEntity> customerMortgageInfoEntityList = customerMortgageInfoMapper.selectList(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                    .eq(CustomerMortgageInfoEntity::getOrderId, appointmentMortgageDetailDTO.getOrderId())
                    .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                    .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
            );

            log.info("CustomerAppointmentServiceImpl.saveMortgageInfo, customerMortgageInfoEntityList.size = {}", customerMortgageInfoEntityList.size());
            if (CollUtil.isNotEmpty(customerMortgageInfoEntityList)) {
                customerMortgageInfoEntity.setId(customerMortgageInfoEntityList.get(0).getId());
                customerMortgageInfoMapper.updateById(customerMortgageInfoEntity);
            } else {
                customerMortgageInfoMapper.insert(customerMortgageInfoEntity);
            }

            Integer advanceMortgageState = null;
            if (Objects.equals(MortgageEnums.PRE_PLEDGE_POST_DISBURSEMENT.getCode(), appointmentMortgageDetailDTO.getPaymentType())) {
                advanceMortgageState = MortgageEnums.ADVANCE_NO.getCode();
            } else if (Objects.equals(MortgageEnums.PLEDGE_RECEIPT_DISBURSEMENT.getCode(), appointmentMortgageDetailDTO.getPaymentType())) {
                advanceMortgageState = MortgageEnums.ADVANCE__UNFINISHED.getCode();
            }
            orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                    .set(OrderInfoEntity::getMortgageState, 1)
                    .set(OrderInfoEntity::getAdvanceMortgageState, advanceMortgageState)
                    .eq(OrderInfoEntity::getId, appointmentMortgageDetailDTO.getOrderId())
                    .eq(OrderInfoEntity::getMortgageState, 0)
            );
            orderPageInfoService.updateOrderPageInfo(appointmentMortgageDetailDTO.getOrderId(), States.MORTGAGE_PENDING, 1);

            sendAppointmentEvent(appointmentMortgageDetailDTO.getOrderId(), loginUser.getUserId(),
                    loginUser.getUserType(), Events.MORTGAGE_LOAN_START);
            return true;
        } finally {
            redisService.releaseLock(lockKey, lockValue);
        }
    }

    private void zhongRuiMortgageOrder(AppointmentMortgageDetailDTO submitMortgageDTO) {
        AddOrderReqDTO addOrderReqDTO = new AddOrderReqDTO();
        CustomerMortgageInfoEntity mortgageInfo = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                .eq(CustomerMortgageInfoEntity::getOrderId, submitMortgageDTO.getOrderId()));
        if (ObjUtil.isEmpty(mortgageInfo)) {
            throw new BusinessException("未查询到抵押预约信息");
        }
        //获取办理省份城市码值
        String code = "";
        List<ProvinceCityResVO.ProvinceCityInfo> provinceInfoList = getProvinceCity(code);
        if (CollUtil.isNotEmpty(provinceInfoList)) {
            provinceInfoList.forEach(provinceCityInfo -> {
                //省份码值
                if (Objects.equals(mortgageInfo.getMortgageProvinceName(), provinceCityInfo.getLabel())) {
                    addOrderReqDTO.setAttendProCode(provinceCityInfo.getValue());
                }
            });
            List<ProvinceCityResVO.ProvinceCityInfo> cityInfoList = getProvinceCity(addOrderReqDTO.getAttendProCode());
            cityInfoList.forEach(cityInfo -> {
                if (Objects.equals(mortgageInfo.getMortgageCityName(), cityInfo.getLabel())) {
                    addOrderReqDTO.setAttendCityCode(cityInfo.getValue());
                }
            });
        }

        BusinessInfoDTO businessInfoDTO = reviewMapper.getBusinessInfo(submitMortgageDTO.getOrderId());
        //抵押预约信息
        CustomerMortgageInfoEntity customerMortgageInfo = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                .select(CustomerMortgageInfoEntity::getProcessingTime, CustomerMortgageInfoEntity::getFileDeliveryType, CustomerMortgageInfoEntity::getFileReturnType)
                .eq(CustomerMortgageInfoEntity::getOrderId, submitMortgageDTO.getOrderId()));

        addOrderReqDTO.setOrderType(ZhongRuiEnum.VEHICLE_PLEDGE.getCode())
                .setContactName(businessInfoDTO.getCustomerName())
                .setContactMobile(businessInfoDTO.getPhone())
                .setGetFileType(customerMortgageInfo.getFileDeliveryType())
                .setReturnFileType(customerMortgageInfo.getFileReturnType())
                .setPreAttendTime(DateUtil.format(customerMortgageInfo.getProcessingTime(), DatePattern.NORM_DATETIME_PATTERN));
        //附加数据
        AddOrderReqDTO.AttachOrderInfo attachOrderInfo = new AddOrderReqDTO.AttachOrderInfo();
        attachOrderInfo.setVinNumber(businessInfoDTO.getVin())
                .setHolderName(businessInfoDTO.getHolder())
                //todo
                .setHolderMobile(businessInfoDTO.getPhone());
        //根据资方判断抵押权人
        if (Objects.equals(MortgageFundEnums.MORTGAGE_ZHONGHENG.getCode(), businessInfoDTO.getFundId())) {
            attachOrderInfo.setMortgagee(ZhongRuiEnum.ZHONG_HENG.getDescription());
        } else if (Objects.equals(MortgageFundEnums.MORTGAGE_YINGFENG.getCode(), businessInfoDTO.getFundId())) {
            attachOrderInfo.setMortgagee(ZhongRuiEnum.YING_FENG.getDescription());
        }

        if (ZhongRuiEnum.OWNER_TYPE_PRIVATE.getDescription().equals(dictUtils.getDictLabel(GlobalConstants.DictType.OWNER_TYPE.name(), businessInfoDTO.getFundId()))) {
            attachOrderInfo.setHolderNature(ZhongRuiEnum.OWNER_TYPE_PRIVATE.getCode());
        } else {
            attachOrderInfo.setHolderNature(ZhongRuiEnum.OWNER_TYPE_COMPANY.getCode());
        }
        addOrderReqDTO.setAttachOrderInfo(attachOrderInfo);

        //快递邮寄-资料归还信息
        AddOrderReqDTO.FileReturnBackInfo fileReturnBackInfo = new AddOrderReqDTO.FileReturnBackInfo();

        //根据客户经理查询文件归还门店地址
        UserAndDeptUsersVO userAndDeptUsersVO = userFeign.selectUsersStore(new UserStoreDTO().setUserId(businessInfoDTO.getAccountManagerId())).getData();
        String storeName = userAndDeptUsersVO.getStore();
        //精确查询
        StoreAddressInfoEntity storeAddressInfo = storeAddressInfoMapper.selectOne(new LambdaQueryWrapper<StoreAddressInfoEntity>()
                .eq(StoreAddressInfoEntity::getStoreName, storeName));
        fileReturnBackInfo.setContractName(userAndDeptUsersVO.getName())
                .setContractMobile(userAndDeptUsersVO.getMobile())
                .setAddressDesc(storeAddressInfo.getAddress() + storeAddressInfo.getDetail());

        //获取归还地址码值
        List<ProvinceCityResVO.ProvinceCityInfo> returnProList = getProvinceCity(code);
        returnProList.forEach(returnProInfo -> {
            //省份码值
            if (Objects.equals(storeAddressInfo.getProvinceName(), returnProInfo.getLabel())) {
                fileReturnBackInfo.setProCode(returnProInfo.getValue());
            }
        });
        List<ProvinceCityResVO.ProvinceCityInfo> returnCityList = getProvinceCity(fileReturnBackInfo.getProCode());
        returnCityList.forEach(returnCityInfo -> {
            if (Objects.equals(storeAddressInfo.getCityName(), returnCityInfo.getLabel())) {
                fileReturnBackInfo.setCityCode(returnCityInfo.getValue());
            }
        });
        addOrderReqDTO.setFileReturnBackInfo(fileReturnBackInfo);

        ZhongRuiAESReqDTO aesReqDTO = new ZhongRuiAESReqDTO();
        String jsonStr = JSONUtil.toJsonStr(addOrderReqDTO);
        log.info("中瑞请求信息：{}", jsonStr);
        aesReqDTO.setPlain(AESUtils.cbcEncrypt(jsonStr, aesKey, aesIv));
        MortgageeOrderResVO orderResVO = zhongRuiFeign.addWorkOrder(aesReqDTO);
        log.info("中瑞返回信息：{}", JSONUtil.toJsonStr(orderResVO));
        if (orderResVO.getSucceeded()) {
            //将返回订单号解密
            String applyNo = AESUtils.cbcDecrypt(orderResVO.getData(), aesKey, aesIv);

            log.info("解密后的订单号：{}", applyNo);
            fundMortgageInfoMapper.insert(new FundMortgageInfoEntity()
                    .setFundMortgageState(orderResVO.getResultType())
                    .setOrderId(submitMortgageDTO.getOrderId())
                    .setInstitutionId(customerMortgageInfo.getInstitutionId())
                    .setInstitutionName(customerMortgageInfo.getInstitutionName())
                    .setApplyNo(applyNo)
            );

        } else {
            throw new BusinessException(orderResVO.getMessage());
        }
    }

    private List<ProvinceCityResVO.ProvinceCityInfo> getProvinceCity(String code) {
        ProvinceCityResVO proVilleResVO = zhongRuiFeign.provinceCityList(code);
        log.info("省份区域码值数据结果：{}", proVilleResVO.getSucceeded());
        if (proVilleResVO.getSucceeded()) {
            return proVilleResVO.getData();
        }
        return List.of();
    }

//    /**
//     * GPS费详情信息
//     *
//     * @param orderId 订单 ID
//     * @return {@link AppointmentGpsFeeDetailVO }
//     */
//    @Override
//    public AppointmentGpsFeeDetailVO gpsFeeDetail(Integer orderId) {
//        List<AppointmentGpsFeeDetailVO> feeDetailList = orderFeeInfoMapper.selectJoinList(
//                AppointmentGpsFeeDetailVO.class,
//                new MPJLambdaWrapper<OrderFeeInfoEntity>()
//                        .eq(OrderFeeInfoEntity::getOrderId, orderId)
//                        .eq(OrderFeeInfoEntity::getFeeType, 1)
//                        .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
//        );
//        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
//        if (CollUtil.isEmpty(feeDetailList)) {
//            ProductInfoEntity productInfoEntity = productInfoMapper.selectById(orderInfo.getProductId());
//            Assert.notNull(productInfoEntity, "订单" + orderId + "产品信息不存在");
//
//            OrderFeeInfoEntity orderFeeInfoEntity = new OrderFeeInfoEntity()
//                    .setOrderId(orderId)
//                    .setGpsFeeStatus(0)
//                    .setGpsFee(productInfoEntity.getGpsFee())
//                    .setFeeType(1)
//                    .setCarServiceFee(productInfoEntity.getCarServiceFee())
//                    .setGpsTotalFee(productInfoEntity.getGpsFee().add(productInfoEntity.getCarServiceFee()));
//
//            orderFeeInfoMapper.insert(orderFeeInfoEntity);
//            AppointmentGpsFeeDetailVO appointmentGpsFeeDetailVO = new AppointmentGpsFeeDetailVO()
//                    .setOrderId(orderId)
//                    .setCarServiceFee(orderFeeInfoEntity.getCarServiceFee())
//                    .setGpsFee(orderFeeInfoEntity.getGpsFee())
//                    .setId(orderFeeInfoEntity.getId())
//                    .setGpsTotalFee(orderFeeInfoEntity.getGpsTotalFee());
//            feeDetailList.add(appointmentGpsFeeDetailVO);
//        }
//
//        Assert.notEmpty(feeDetailList, "订单" + orderId + "费用信息不存在");
//
//        return feeDetailList.get(0);
//    }

//    /**
//     * 保存GPS费
//     *
//     * @param appointmentGpsFeeDetailVO 预约GPS费详情信息VO
//     * @return {@link Boolean }
//     */
//    @Override
//    public Boolean saveGpsFee(AppointmentGpsFeeDetailVO appointmentGpsFeeDetailVO, LoginUser loginUser) {
//
//        Integer id = appointmentGpsFeeDetailVO.getId();
//        OrderFeeInfoEntity orderFeeInfoEntity = new OrderFeeInfoEntity()
//                .setOrderId(appointmentGpsFeeDetailVO.getOrderId())
//                .setCarServiceFee(appointmentGpsFeeDetailVO.getCarServiceFee())
//                .setGpsFee(appointmentGpsFeeDetailVO.getGpsFee())
//                .setFeeType(1)
//                .setGpsTotalFee(appointmentGpsFeeDetailVO.getGpsTotalFee());
//        if (!Objects.equals(id, null) && id != 0) {
//            OrderFeeInfoEntity orderFeeInfo = orderFeeInfoMapper.selectById(id);
//            Assert.notNull(orderFeeInfo, "订单费用信息不存在");
//            orderFeeInfoEntity.setId(id);
//            orderFeeInfoMapper.updateById(orderFeeInfoEntity);
//        } else {
//            orderFeeInfoMapper.insert(orderFeeInfoEntity);
//        }
//
//        return true;
//    }

    /**
     * GPS详情信息
     *
     * @param orderId 次序id
     * @return {@link AppointmentGpsDetailVO }
     */
    @Override
    public AppointmentGpsDetailVO gpsDetail(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isEmpty(orderInfoEntity)){
            throw new BusinessException("订单不存在");
        }
        OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                .eq(OrderVehicleInfoEntity::getOrderId, orderId)
                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
        );
        Assert.notNull(orderVehicleInfoEntity, "车辆信息不存在");

        AppointmentGpsDetailVO appointmentGpsDetailVO = new AppointmentGpsDetailVO();
        OrderGpsInfoEntity orderGpsInfoEntity = orderGpsInfoMapper.selectOne(
                new MPJLambdaWrapper<OrderGpsInfoEntity>()
                        .eq(OrderGpsInfoEntity::getOrderId, orderId)
                        .eq(OrderGpsInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderGpsInfoEntity::getCreateTime)
                        .last("limit 1")
        );
        OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectById(orderInfoEntity.getCustomerId());
        if (ObjUtil.isEmpty(orderCustomerInfoEntity)){
            throw new BusinessException("客户信息不存在");
        }
        Boolean onlineOrder = isOnlineOrder(orderId);
        if (onlineOrder&&ObjUtil.equals(orderInfoEntity.getFundId(),FundEnum.CHANG_YIN.getValue())){
            if (ObjUtil.equals(orderInfoEntity.getGpsState(),2)&&(ObjUtil.isNull(orderGpsInfoEntity)||ObjUtil.notEqual(orderGpsInfoEntity.getGpsStatus(),2))){
                return appointmentGpsDetailVO.setInstallType(2);
            }
            OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new MPJLambdaWrapper<OrderAmountEntity>()
                    .eq(OrderAmountEntity::getOrderId, orderId)
                    .eq(OrderAmountEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderAmountEntity::getCreateTime)
                    .last("limit 1"));
            if ((orderAmountEntity.getCustomerConfirmAmount().compareTo(new BigDecimal("30000.00")) <= 0)){
                orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                        .set(OrderInfoEntity::getGpsState, 2)
                        .eq(OrderInfoEntity::getId, orderId));
                orderPageInfoService.updateOrderPageInfo(orderId, States.GPS_INSTALL_APPLY, 1);
                return appointmentGpsDetailVO.setInstallType(2);
            }
        }
        if (ObjUtil.isNotEmpty(orderGpsInfoEntity)) {
            appointmentGpsDetailVO = orderGpsInfoConverter.entityToAppointmentGpsDetailVO(orderGpsInfoEntity);
            appointmentGpsDetailVO.setReservationInstallTime(ObjUtil.isNotEmpty(orderGpsInfoEntity.getPlanInstallTime()) ? orderGpsInfoEntity.getPlanInstallTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
            appointmentGpsDetailVO.setIdentity(orderGpsInfoEntity.getContactStatus());
            appointmentGpsDetailVO.setTel(orderGpsInfoEntity.getContactTel());
            appointmentGpsDetailVO.setName(orderGpsInfoEntity.getContactName());
            appointmentGpsDetailVO.setInstallDetail(orderGpsInfoEntity.getInstallDetail());
            appointmentGpsDetailVO.setThirdParty(orderGpsInfoEntity.getThirdParty());
        }else {
            appointmentGpsDetailVO.setInstallProvince(orderCustomerInfoEntity.getResidentialProvince());
            appointmentGpsDetailVO.setInstallProvinceName(orderCustomerInfoEntity.getResidentialProName());
            appointmentGpsDetailVO.setInstallCity(orderCustomerInfoEntity.getResidentialCity());
            appointmentGpsDetailVO.setInstallCityName(orderCustomerInfoEntity.getResidentialCityName());
            appointmentGpsDetailVO.setInstallArea(orderCustomerInfoEntity.getResidentialArea());
            appointmentGpsDetailVO.setInstallAreaName(orderCustomerInfoEntity.getResidentialAreaName());
            appointmentGpsDetailVO.setInstallDetail(orderCustomerInfoEntity.getResidentialDetailedAddress());
            appointmentGpsDetailVO.setThirdParty(onlineOrder?2:1);
        }
        appointmentGpsDetailVO.setName(ObjUtil.isNotEmpty(appointmentGpsDetailVO.getName()) ? appointmentGpsDetailVO.getName() : orderCustomerInfoEntity.getName());
        appointmentGpsDetailVO.setTel(ObjUtil.isNotEmpty(appointmentGpsDetailVO.getTel()) ? appointmentGpsDetailVO.getTel() : orderCustomerInfoEntity.getPhone());
        appointmentGpsDetailVO.setIdentity(ObjUtil.isNotEmpty(appointmentGpsDetailVO.getIdentity()) ? appointmentGpsDetailVO.getIdentity() : "本人");
        appointmentGpsDetailVO.setOrderId(orderId).setCarId(orderVehicleInfoEntity.getId());
        return appointmentGpsDetailVO;
    }

    /**
     * 保存GPS信息
     *
     * @param appointmentGpsDetailDTO 预约GPS详情信息DTO
     * @return {@link Boolean }
     */
    @Override
    public Boolean saveGpsInfo(AppointmentGpsDetailDTO appointmentGpsDetailDTO, LoginUser loginUser) {
        String lockKey = SAVE_GPS_APPOINTMENT_INSTALLATION_LOCK + appointmentGpsDetailDTO.getOrderId();
        String lockValue = UUID.randomUUID().toString();
        Boolean lock = redisService.tryLock(lockKey, lockValue, 120);
        if (!lock) {
            log.info("GpsServiceImpl setOpenInstallOrder, lock failed, orderId = {}", lockKey);
            throw new BusinessException("操作频繁，请稍后再试");
        }
        try {
            Integer orderId = appointmentGpsDetailDTO.getOrderId();
            OrderGpsInfoEntity orderGpsInfoEntity = orderGpsInfoConverter.dtoToEntity(appointmentGpsDetailDTO);
            Assert.notNull(orderId, "订单ID不能为空");
            OrderGpsInfoEntity orderGpsInfo = orderGpsInfoMapper.selectOne(new LambdaQueryWrapper<OrderGpsInfoEntity>()
                    .eq(OrderGpsInfoEntity::getOrderId, orderId)
                    .eq(OrderGpsInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderGpsInfoEntity::getUpdateTime), false);
            if (orderGpsInfo != null && orderGpsInfo.getGpsStatus() != null) {
                switch (orderGpsInfo.getGpsStatus()) {
                    case 1:
                        throw new BusinessException("当前订单已预约安装，请勿重复预约！");
                    case 2:
                        throw new BusinessException("当前订单已安装，请勿重复预约！");
                    case 3:
                        throw new BusinessException("该订单尚未拆除完成，请拆除完成后再进行预约！");
                }
            }
            if (Objects.isNull(orderGpsInfo)) {
                orderGpsInfoMapper.insert(orderGpsInfoEntity);
            } else {
                orderGpsInfoMapper.update(
                        new LambdaUpdateWrapper<OrderGpsInfoEntity>()
                                .set(OrderGpsInfoEntity::getDismantleType, null)
                                .set(OrderGpsInfoEntity::getDismantleStoreId, null)
                                .set(OrderGpsInfoEntity::getDismantleStoreName,null)
                                .set(OrderGpsInfoEntity::getDismantleDetail, null)
                                .set(OrderGpsInfoEntity::getDismantleProvince, null)
                                .set(OrderGpsInfoEntity::getDismantleCity, null)
                                .set(OrderGpsInfoEntity::getDismantleProvinceName, null)
                                .set(OrderGpsInfoEntity::getDismantleCityName, null)
                                .set(OrderGpsInfoEntity::getDismantleArea, null)
                                .set(OrderGpsInfoEntity::getDismantleAreaName, null)
                                .set(OrderGpsInfoEntity::getPlanRemoveTime,null)
                                .set(OrderGpsInfoEntity::getRemoveContactName, null)
                                .set(OrderGpsInfoEntity::getRemoveContactTel, null)
                                .set(OrderGpsInfoEntity::getRemoveContactStatus, null)
                                .set(OrderGpsInfoEntity::getInstallType, appointmentGpsDetailDTO.getInstallType())
                                .set(OrderGpsInfoEntity::getInstallStoreId, appointmentGpsDetailDTO.getInstallStoreId())
                                .set(OrderGpsInfoEntity::getInstallStoreName, appointmentGpsDetailDTO.getInstallStoreName())
                                .set(OrderGpsInfoEntity::getInstallProvince, appointmentGpsDetailDTO.getInstallProvince())
                                .set(OrderGpsInfoEntity::getInstallProvinceName, appointmentGpsDetailDTO.getInstallProvinceName())
                                .set(OrderGpsInfoEntity::getInstallCity, appointmentGpsDetailDTO.getInstallCity())
                                .set(OrderGpsInfoEntity::getInstallCityName, appointmentGpsDetailDTO.getInstallCityName())
                                .set(OrderGpsInfoEntity::getInstallArea, appointmentGpsDetailDTO.getInstallArea())
                                .set(OrderGpsInfoEntity::getInstallAreaName, appointmentGpsDetailDTO.getInstallAreaName())
                                .set(OrderGpsInfoEntity::getInstallDetail, appointmentGpsDetailDTO.getInstallDetail())
                                .set(OrderGpsInfoEntity::getPlanInstallTime, ObjUtil.isNotEmpty(appointmentGpsDetailDTO.getReservationInstallTime()) ? LocalDateTime.parse(appointmentGpsDetailDTO.getReservationInstallTime(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : null)
                                .set(OrderGpsInfoEntity::getCarId, appointmentGpsDetailDTO.getCarId())
                                .set(OrderGpsInfoEntity::getContactTel, appointmentGpsDetailDTO.getTel())
                                .set(OrderGpsInfoEntity::getContactName, appointmentGpsDetailDTO.getName())
                                .set(OrderGpsInfoEntity::getContactStatus, appointmentGpsDetailDTO.getIdentity())
                                .set(OrderGpsInfoEntity::getThirdParty, appointmentGpsDetailDTO.getThirdParty())
                                .eq(OrderGpsInfoEntity::getOrderId, orderId)
                );
            }

            Boolean onlineOrder = isOnlineOrder(orderId);



            //如果是电销线上并且选择不到店安装
            if (onlineOrder && Objects.equals(appointmentGpsDetailDTO.getInstallType(),0) && Objects.equals(appointmentGpsDetailDTO.getThirdParty(),2)){
                gpsService.createInstallationWorkOrder(appointmentGpsDetailDTO, loginUser);
            }else {
                //预约安装GPS
                gpsService.setOpenInstallOrder(appointmentGpsDetailDTO,onlineOrder);
                orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                        .set(OrderInfoEntity::getGpsState, 1)
                        .eq(OrderInfoEntity::getId, appointmentGpsDetailDTO.getOrderId())
                        .in(OrderInfoEntity::getGpsState, Arrays.asList(0,5))
                );
                orderPageInfoService.updateOrderPageInfo(appointmentGpsDetailDTO.getOrderId(), States.GPS_INSTALL_APPLY, 1);
                sendAppointmentEvent(appointmentGpsDetailDTO.getOrderId(), loginUser.getUserId(), loginUser.getUserType(),
                        Events.GPS_INSTALL_APPLY);
            }
        } finally {
            redisService.releaseLock(lockKey,lockValue);
        }
        return true;
    }

    @Override
    public TongLianPayVO customerPay(Integer orderId) {
        // 查询订单信息
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, "订单" + orderId + "信息不存在");
        int isOnlineOrder = isOnlineOrder(orderId) ? 1 : 2;
        List<OrderFeeInfoEntity> orderFeeInfoEntities = orderFeeInfoMapper.selectList(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                .eq(OrderFeeInfoEntity::getOrderId, orderId)
                .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                .eq(OrderFeeInfoEntity::getFeeType, 1)
                .eq(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                .orderByDesc(OrderFeeInfoEntity::getCreateTime));
        if (CollUtil.isNotEmpty(orderFeeInfoEntities)) {
            OrderFeeInfoEntity orderFeeInfoEntity = orderFeeInfoEntities.get(0);
            TongLianPayVO tongLianPayVO = new TongLianPayVO();
            tongLianPayVO.setOrderId(orderId)
                    .setGpsFee(orderFeeInfoEntity.getGpsFee())
                    .setCarServiceFee(orderFeeInfoEntity.getCarServiceFee())
                    .setId(orderFeeInfoEntity.getId())
                    .setGpsTotalFee(orderFeeInfoEntity.getGpsTotalFee())
                    .setGpsFeeStatus(orderFeeInfoEntity.getGpsFeeStatus())
                    .setGpsPayType(ObjUtil.defaultIfNull(orderFeeInfoEntity.getGpsPayType(),2));
            tongLianPayVO.setIsOnlineOrder(isOnlineOrder);
            return tongLianPayVO;
        }


        OrderFeeInfoEntity orderFeeInfoEntity = searchFeeInfo(orderId);
//        ProductInfoEntity productInfoEntity = productInfoMapper.selectOne(
//                new LambdaQueryWrapper<ProductInfoEntity>()
//                        .eq(ProductInfoEntity::getId, orderInfo.getProductId())
//                        .eq(ProductInfoEntity::getDeleteFlag, 0)
//        );
//        BigDecimal addGpsFee = (ObjUtil.isNotNull(productInfoEntity.getGpsFee()) ? productInfoEntity.getGpsFee() : BigDecimal.ZERO)
//                .add(
//                        (ObjUtil.isNotNull(productInfoEntity.getCarServiceFee()) ? productInfoEntity.getCarServiceFee() : BigDecimal.ZERO)
//                );
//        if (Objects.equals(addGpsFee.compareTo(BigDecimal.ZERO), 0)) {
//            TongLianPayVO tongLianPayVO = new TongLianPayVO();
//            tongLianPayVO.setOrderId(orderId)
//                    .setGpsFee(productInfoEntity.getGpsFee())
//                    .setCarServiceFee(productInfoEntity.getCarServiceFee())
//                    .setId(orderFeeInfoEntity.getId())
//                    .setGpsTotalFee(
//                            (ObjUtil.isNotNull(productInfoEntity.getGpsFee()) ? productInfoEntity.getGpsFee() : BigDecimal.ZERO)
//                                    .add(
//                                            (ObjUtil.isNotNull(productInfoEntity.getCarServiceFee()) ? productInfoEntity.getCarServiceFee() : BigDecimal.ZERO)
//                                    )
//                    )
//                    .setGpsFeeStatus(orderFeeInfoEntity.getGpsFeeStatus());
//            tongLianPayVO.setIsOnlineOrder(1);
//            tongLianPayVO.setGpsFeeStatus(2);
//            orderFeeInfoMapper.update(
//                    new LambdaUpdateWrapper<OrderFeeInfoEntity>()
//                            .eq(OrderFeeInfoEntity::getOrderId, orderId)
//                            .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
//                            .set(OrderFeeInfoEntity::getDeleteFlag, 1)
//            );
//            OrderFeeInfoEntity entity = new OrderFeeInfoEntity();
//            entity.setOrderId(orderId);
//            entity.setGpsFeeStatus(2);
//            entity.setGpsFee(productInfoEntity.getGpsFee());
//            entity.setCarServiceFee(productInfoEntity.getCarServiceFee());
//            entity.setGpsTotalFee(
//                    (ObjUtil.isNotNull(productInfoEntity.getGpsFee()) ? productInfoEntity.getGpsFee() : BigDecimal.ZERO)
//                            .add(
//                                    (ObjUtil.isNotNull(productInfoEntity.getCarServiceFee()) ? productInfoEntity.getCarServiceFee() : BigDecimal.ZERO)
//                            )
//            );
//            entity.setFeeType(1);
//            entity.setPaySnNumBack(String.valueOf(Instant.now().toEpochMilli()));
//            orderFeeInfoMapper.insert(entity);
//            orderFeeDetailService.saveOrderFeeDetail(orderId,
//                    entity.getPaySnNumBack(),
//                    entity.getGpsTotalFee(),
//                    OrderFeeDetailTradingMethodsEnum.SCAN_THE_QR_CODE_TO_PAY,
//                    orderInfo.getCustomerName(),
//                    "河北龙环谛听网络科技有限公司",
//                    OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE,
//                    OrderFeeDetailStatusEnum.INCOME,
//                    entity.getUpdateTime(),
//                    null,null);
//            return tongLianPayVO;
//
//        }


//        if (!envUtil.isPrd()) {
//            return payTestBaffle(orderId);
//        }

        TongLianPayVO tongLianPayVO = new TongLianPayVO();
        tongLianPayVO.setOrderId(orderId)
                .setGpsFee(orderFeeInfoEntity.getGpsFee())
                .setCarServiceFee(orderFeeInfoEntity.getCarServiceFee())
                .setId(orderFeeInfoEntity.getId())
                .setGpsTotalFee(orderFeeInfoEntity.getGpsTotalFee())
                .setGpsFeeStatus(orderFeeInfoEntity.getGpsFeeStatus())
                .setGpsPayType(ObjUtil.defaultIfNull(orderFeeInfoEntity.getGpsPayType(),2));
        if (Objects.isNull(orderFeeInfoEntity.getPaySnNum())) {
            return getPayUrl(orderInfo, orderFeeInfoEntity, tongLianPayVO);
        }

        // 从 Redis 中获取支付信息
        String payInfoVal = redisService.getString(Pay_URL_KEY + orderId);
        if (payInfoVal != null) {
            try {
                // 生成二维码
                byte[] imageBytes = QrCodeUtils.generateQrCode(orderInfo.getCustomerName(), payInfoVal);
                tongLianPayVO.setResponseEntity(Base64Encoder.encode(imageBytes))
                        .setPayUrl(payInfoVal);
            } catch (Exception e) {
                throw new BusinessException("生成二维码失败", e);
            }
            tongLianPayVO.setIsOnlineOrder(isOnlineOrder);
            return tongLianPayVO;
        }

        //若Redis中没有支付信息，则先把最近一笔置为失效再生成新的支付信息
        String paySnNum = orderFeeInfoEntity.getPaySnNum();
        // 关闭订单
        tlCloseTransaction(paySnNum, orderId);
        //将之前订单全部置为失效
        orderFeeInfoMapper.update(new LambdaUpdateWrapper<OrderFeeInfoEntity>()
                .eq(OrderFeeInfoEntity::getOrderId, orderId)
                .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                .eq(OrderFeeInfoEntity::getFeeType, 1)
                .set(OrderFeeInfoEntity::getDeleteFlag, 1));
        // 调用通联支付接口生成支付链接
        return getPayUrl(orderInfo, orderFeeInfoEntity, tongLianPayVO);

        //        // 调用通联支付接口生成支付链接
        //        String reqsn = getPayNumber();
        //        // 计算金额并调用通联支付接口
        //        BigDecimal trxamt = orderFeeInfoEntity.getGpsTotalFee()
        //                .multiply(new BigDecimal("100"))
        //                .setScale(0, RoundingMode.HALF_UP);
        //        TongLianPayResultVO tongLianPayResultVO = tongLianPaySend(orderId, Objects.toString(trxamt, ""), reqsn);
        //
        //        if (!Objects.equals(tongLianPayResultVO.getRetcode(), "SUCCESS")) {
        //            throw new BusinessException(tongLianPayResultVO.getRetmsg());
        //        }
        //
        //        // 记录支付编号并判断是否插入或更新
        //        if (orderFeeInfoEntity.getPaySnNum() != null) {
        //            orderFeeInfoEntity.setPaySnNum(reqsn);
        //            orderFeeInfoEntity.setId(null);
        //            orderFeeInfoMapper.insert(orderFeeInfoEntity);
        //        } else {
        //            orderFeeInfoEntity.setPaySnNum(reqsn);
        //            orderFeeInfoMapper.updateById(orderFeeInfoEntity);
        //        }
        //
        //        if (orderFeeInfoEntity.getGpsTotalFee() == null) {
        //            log.info("订单{}费用信息不存在", orderId);
        //            throw new BusinessException("订单" + orderId + "费用信息不存在");
        //        }
        //
        //        // 支付链接
        //        String payinfo = tongLianPayResultVO.getPayinfo();
        //
        //        // 设置 Redis 缓存
        //        redisService.set(Pay_URL_KEY + orderId, payinfo, 60 * 4);
        //
        //        try {
        //            // 生成二维码
        //            byte[] imageBytes = QrCodeUtils.generateQrCode(orderInfo.getCustomerName(), payinfo);
        //            tongLianPayVO.setResponseEntity(Base64Encoder.encode(imageBytes))
        //                    .setPayUrl(payinfo);
        //        } catch (Exception e) {
        //            throw new BusinessException("生成二维码失败", e);
        //        }

        //        return tongLianPayVO;
    }

    private @NotNull TongLianPayVO payTestBaffle(Integer orderId) {
        OrderFeeInfoEntity orderFeeInfoEntity = orderFeeInfoMapper.selectOne(new MPJLambdaWrapper<>(OrderFeeInfoEntity.class)
                .eq(OrderFeeInfoEntity::getOrderId, orderId)
                .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .orderByDesc(OrderFeeInfoEntity::getCreateTime)
                .last("limit 1"));
        orderFeeInfoMapper.updateById(new OrderFeeInfoEntity().setId(orderFeeInfoEntity.getId())
                .setGpsFeeStatus(2));
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        orderFeeDetailService.saveOrderFeeDetail(orderId,
                orderFeeInfoEntity.getPaySnNumBack(),
                orderFeeInfoEntity.getGpsTotalFee(),
                OrderFeeDetailTradingMethodsEnum.SCAN_THE_QR_CODE_TO_PAY,
                orderInfoEntity.getCustomerName(),
                "河北龙环谛听网络科技有限公司",
                OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE,
                OrderFeeDetailStatusEnum.INCOME,
                orderFeeInfoEntity.getUpdateTime(),
                null,null);
        TongLianPayVO tongLianPayVO = new TongLianPayVO();
        tongLianPayVO.setOrderId(orderId)
                .setGpsFee(orderFeeInfoEntity.getGpsFee())
                .setCarServiceFee(orderFeeInfoEntity.getCarServiceFee())
                .setId(orderFeeInfoEntity.getId())
                .setGpsTotalFee(orderFeeInfoEntity.getGpsTotalFee())
                .setGpsFeeStatus(orderFeeInfoEntity.getGpsFeeStatus());
        sendAppointmentEvent(orderId, 1, UserTypeEnum.EMPLOYEE.getType(), Events.GPS_FEE_PAY_FINISH);
        tongLianPayVO.setIsOnlineOrder(isOnlineOrder(orderId)?1:2);
        return tongLianPayVO;
    }

    public OrderFeeInfoEntity searchFeeInfo(Integer orderId) {
        // 查询订单费用信息
        List<OrderFeeInfoEntity> orderFeeInfoEntities = orderFeeInfoMapper.selectList(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                .eq(OrderFeeInfoEntity::getOrderId, orderId)
                .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                .eq(OrderFeeInfoEntity::getFeeType, 1)
                .orderByDesc(OrderFeeInfoEntity::getCreateTime));

        // 查询订单信息
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, "订单" + orderId + "信息不存在");

        // 如果订单费用信息为空，插入产品信息作为默认费用信息
        if (CollUtil.isEmpty(orderFeeInfoEntities)) {
            ProductInfoEntity productInfoEntity = productInfoMapper.selectById(orderInfo.getProductId());
            Assert.notNull(productInfoEntity, "产品信息不存在");

            OrderFeeInfoEntity orderFeeInfoEntity = new OrderFeeInfoEntity()
                    .setOrderId(orderId)
                    .setGpsFeeStatus(0)
                    .setGpsFee(productInfoEntity.getGpsFee())
                    .setFeeType(1)
                    .setCarServiceFee(productInfoEntity.getCarServiceFee())
                    .setGpsTotalFee(productInfoEntity.getGpsFee().add(productInfoEntity.getCarServiceFee()))
                    .setGpsPayType(2);
            if (isOnlineOrder(orderId)){
                orderFeeInfoEntity.setGpsTotalFee(new BigDecimal("500"))
                        .setCarServiceFee(new BigDecimal("500"))
                        .setGpsFee(null);
            }
            //todo
            if (ObjUtil.equals(orderInfo.getProductId(),67)){
                OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new MPJLambdaWrapper<OrderAmountEntity>()
                        .eq(OrderAmountEntity::getOrderId, orderId)
                        .eq(OrderAmountEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderAmountEntity::getCreateTime)
                        .last("limit 1"));
                if (orderAmountEntity.getCustomerConfirmAmount().compareTo(new BigDecimal("30000"))<0){
                    orderFeeInfoEntity.setGpsTotalFee(new BigDecimal("1000"))
                        .setCarServiceFee(new BigDecimal("1000"))
                        .setGpsFee(null);
                }else {
                    orderFeeInfoEntity.setGpsTotalFee(new BigDecimal("1500"))
                            .setCarServiceFee(new BigDecimal("1500"))
                            .setGpsFee(null);
                }
            }
            orderFeeInfoMapper.insert(orderFeeInfoEntity);
            orderFeeInfoEntities.add(orderFeeInfoEntity);
        }

        return orderFeeInfoEntities.get(0);
    }

    public TongLianPayVO getPayUrl(OrderInfoEntity orderInfo, OrderFeeInfoEntity orderFeeInfoEntity, TongLianPayVO tongLianPayVO) {
        log.info("CustomerAppointmentServiceImpl.getPayUrl:orderInfo:{},orderInfo:{},tonglianPayVO:{}", orderInfo, orderInfo, tongLianPayVO);
        Integer orderId = orderInfo.getId();
        // 调用通联支付接口生成支付链接
        String reqsn = getPayNumber();
        // 计算金额并调用通联支付接口
        BigDecimal trxamt = orderFeeInfoEntity.getGpsTotalFee()
                .multiply(new BigDecimal("100"))
                .setScale(0, RoundingMode.HALF_UP);
        TongLianPayResultVO tongLianPayResultVO = tongLianPaySend(orderId, Objects.toString(trxamt, ""), reqsn);

        if (!Objects.equals(tongLianPayResultVO.getRetcode(), "SUCCESS")) {
            throw new BusinessException(tongLianPayResultVO.getRetmsg());
        }

        // 记录支付编号并判断是否插入或更新
        if (orderFeeInfoEntity.getPaySnNum() != null) {
            // 创建新支付记录
            log.info("CustomerAppointmentServiceImpl orderId {} create new pay info", orderId);
            OrderFeeInfoEntity newEntity = getNewEntity(orderFeeInfoEntity, reqsn, orderId);

            orderFeeInfoMapper.insert(newEntity);
        } else {
            orderFeeInfoEntity.setPaySnNum(reqsn);
            orderFeeInfoMapper.updateById(orderFeeInfoEntity);
        }

        if (orderFeeInfoEntity.getGpsTotalFee() == null) {
            log.info("订单{}费用信息不存在", orderId);
            throw new BusinessException("订单" + orderId + "费用信息不存在");
        }

        // 支付链接
        String payinfo = tongLianPayResultVO.getPayinfo();

        // 设置 Redis 缓存
        redisService.set(Pay_URL_KEY + orderId, payinfo, 60 * 4);

        try {
            // 生成二维码
            byte[] imageBytes = QrCodeUtils.generateQrCode(orderInfo.getCustomerName(), payinfo);
            tongLianPayVO.setResponseEntity(Base64Encoder.encode(imageBytes))
                    .setPayUrl(payinfo);
        } catch (Exception e) {
            throw new BusinessException("生成二维码失败", e);
        }
        log.info("CustomerAppointmentServiceImpl.getPayUrl:tongLianPayVO:{}", tongLianPayVO);
        tongLianPayVO.setIsOnlineOrder(2);
        return tongLianPayVO;
    }

    @Override
    public TongLianPayResultVO kingDeePay(TongLianPayDTO tongLianPayDTO) {
        return tongLianPaySend(tongLianPayDTO.getSourceType(), tongLianPayDTO.getTrxamt(), tongLianPayDTO.getReqsn());
    }

    private TongLianPayResultVO tongLianPaySend(Integer orderId, String trxamt, String reqsn) {
        TreeMap<String, String> params = new TreeMap<String, String>();
        //商户号 实际交易的商户号(必传)
        params.put("cusid", SYB_CUSID);
        //应用ID 平台分配的APPID(必传)
        params.put("appid", SYB_APPID);
        //交易金额(必传)
        params.put("trxamt", trxamt);
        //商户交易单号
        params.put("reqsn", reqsn);
        //交易方式(必传)
        params.put("paytype", CustomerPayEnum.WXSM.getCode());
        //随机字符串(必传)
        String validatecode = SybUtil.getValidatecode(8);
        params.put("randomstr", validatecode);
        //签名方式(必传)
        params.put("signtype", SIGN_TYPE);
        //交易结果通知地址 接收交易结果的通知回调地址，通知url必须为直接可访问的url，不能携带参数。https只支持默认端口
        params.put("notify_url", TL_CALL_BACK);
        String appkey = "";
        if (SIGN_TYPE.equals("RSA")) {
            appkey = SYB_RSACUSPRIKEY;
        } else if (SIGN_TYPE.equals("SM2")) {
            appkey = SYB_SM2PPRIVATEKEY;
        } else {
            appkey = SYB_MD5_APPKEY;
        }
        String result = null;
        RequestResponseInfoEntity requestResponseInfoEntity = new RequestResponseInfoEntity();
        try {
            String sign = SybUtil.unionSign(params, appkey, SIGN_TYPE);
            requestResponseInfoEntity.setRequestBody(sign);
            requestResponseInfoEntity.setTId(orderId);
            requestResponseInfoEntity.setTName(TNAME);
            requestResponseInfoEntity.setCode(RequestResponseCode.TONGLIAN_PAY);
            requestResponseInfoMapper.insert(requestResponseInfoEntity);

            log.info("CustomerAppointmentServiceImpl tongLianPaySend: SYB_CUSID:{},SYB_APPID:{},trxamt:{},reqsn:{},validatecode:{},SIGN_TYPE:{},sign:{}", SYB_CUSID, SYB_APPID, trxamt, reqsn, validatecode, SIGN_TYPE, sign);
            result = tongLianPayFeign.pay(SYB_CUSID, SYB_APPID, trxamt,
                    reqsn, CustomerPayEnum.WXSM.getCode(), validatecode,
                    SIGN_TYPE, TL_CALL_BACK, sign);
            requestResponseInfoEntity.setResponseBody(result);
            requestResponseInfoMapper.updateById(requestResponseInfoEntity);
            log.info(result);
        } catch (Exception e) {
            throw new BusinessException("支付sign签名失败,e{}", e);
        }
        log.info("通联支付返回结果:{}", result);

        Map<String, String> map = new HashMap<>();
        try {
            map = handleResult(result);
            log.info("通联支付返回的参数，map:{}", map);
        } catch (Exception e) {
            log.error("通联支付|||验证签名失败|||,e{}", e.getMessage(), e);
            throw new BusinessException("支付|||验证签名失败|||");
        }

        ObjectMapper objectMapper = new ObjectMapper();
        TongLianPayResultVO tongLianPayVO = null;

        try {
            // 将 map 转换为 TongLianPayVO 对象
            tongLianPayVO = objectMapper.convertValue(map, TongLianPayResultVO.class);
        } catch (Exception e) {
            log.error("将 map 转换为对象时出错: {}", e.getMessage(), e);
        }


        return tongLianPayVO;
    }

    @Override
    public String searchTranx(Integer orderId, LoginUser loginUser) {
//        if (!envUtil.isPrd()) {
//            return "支付成功";
//        }
        Integer userId = loginUser.getUserId();

        //        Integer userId = 10334;
        LambdaQueryWrapper<OrderFeeInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderFeeInfoEntity::getOrderId, orderId);
        queryWrapper.eq(OrderFeeInfoEntity::getDeleteFlag, 0);
        queryWrapper.eq(OrderFeeInfoEntity::getFeeType, 1);
        queryWrapper.orderByDesc(OrderFeeInfoEntity::getCreateTime);
        List<OrderFeeInfoEntity> orderFeeInfoEntities = orderFeeInfoMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(orderFeeInfoEntities)) {
            OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
            ProductInfoEntity productInfoEntity = productInfoMapper.selectById(orderInfo.getProductId());
            OrderFeeInfoEntity orderFeeInfo = new OrderFeeInfoEntity();
            orderFeeInfo.setOrderId(orderId);
            orderFeeInfo.setGpsFeeStatus(0);
            orderFeeInfo.setCarServiceFee(productInfoEntity.getCarServiceFee());
            orderFeeInfo.setGpsFee(productInfoEntity.getGpsFee());
            orderFeeInfo.setGpsTotalFee(productInfoEntity.getGpsFee().add(productInfoEntity.getCarServiceFee()))
                    .setGpsPayType(2);
            if (isOnlineOrder(orderId)){
                orderFeeInfo.setCarServiceFee(new BigDecimal("500")).setGpsFee( null).setGpsTotalFee(new BigDecimal("500"));
            }
            //todo
            if (ObjUtil.equals(orderInfo.getProductId(),67)){
                OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new MPJLambdaWrapper<OrderAmountEntity>()
                        .eq(OrderAmountEntity::getOrderId, orderId)
                        .eq(OrderAmountEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderAmountEntity::getCreateTime)
                        .last("limit 1"));
                if (orderAmountEntity.getCustomerConfirmAmount().compareTo(new BigDecimal("30000"))<0){
                    orderFeeInfo.setGpsTotalFee(new BigDecimal("1000"))
                            .setCarServiceFee(new BigDecimal("1000"))
                            .setGpsFee(null);
                }else {
                    orderFeeInfo.setGpsTotalFee(new BigDecimal("1500"))
                            .setCarServiceFee(new BigDecimal("1500"))
                            .setGpsFee(null);
                }
            }
            orderFeeInfoEntities.add(orderFeeInfo);
            orderFeeInfoMapper.insert(orderFeeInfo);
        }
        OrderFeeInfoEntity orderFeeInfoEntity = orderFeeInfoEntities.get(0);
        if (orderFeeInfoEntity.getGpsFeeStatus() != null && orderFeeInfoEntity.getGpsFeeStatus() == 2) {
            return "支付成功";
        }
        //调用通联支付查询交易
        TongLianSearchVO tongLianSearchVO = searchTranxSend(orderId, orderFeeInfoEntity.getPaySnNum());
        String trxstatus = tongLianSearchVO.getTrxstatus();
        String resultMsg = "";
        //todo 测试用后续正常判断状态
        if ("0000".equals(trxstatus)) {
            // 交易成功
            resultMsg = "支付成功";
            orderFeeInfoEntity.setGpsFeeStatus(2);

            updateFeeInfo(orderFeeInfoEntity.getOrderId(), userId, orderFeeInfoEntity.getId(), 2, tongLianSearchVO.getTrxid());
            return resultMsg;
        } else if ("1001".equals(trxstatus)) {
            resultMsg = "交易不存在";
            orderFeeInfoEntity.setGpsFeeStatus(-1);
            log.info("通联支付查询，交易不存在");
        } else if ("2008".equals(trxstatus) || "2000".equals(trxstatus)) {
            resultMsg = "支付处理中,请再次查询支付";
            orderFeeInfoEntity.setGpsFeeStatus(1);
            log.info("通联支付查询，交易处理中,请再次查询交易");
        } else {
            resultMsg = "支付失败";
            orderFeeInfoEntity.setGpsFeeStatus(-1);
            log.info("交易失败");
        }
        if (!envUtil.isPrd()) {
            //目前测试阶段，直接修改订单状态为已支付
            orderFeeInfoEntity.setGpsFeeStatus(2);
        }
        updateFeeInfo(orderFeeInfoEntity.getOrderId(), userId, orderFeeInfoEntity.getId(), orderFeeInfoEntity.getGpsFeeStatus(), tongLianSearchVO.getTrxid());
        throw new BusinessException(resultMsg);
    }

    @Override
    public TongLianSearchVO kingDeeSearchTranx(KingDeeSearchDTO kingDeeSearchDTO) {
        return searchTranxSend(kingDeeSearchDTO.getSourceType(), kingDeeSearchDTO.getReqsn());
    }

    @Override
    public TlCloseTransactionVO tlCloseTransaction(String reqsn, Integer orderId) {
        log.info("CustomerAppointmentServiceImpl tlCloseTransaction: reqsn:{},orderId:{}", reqsn, orderId);
        TreeMap<String, String> params = new TreeMap<String, String>();
        //params.put("orgid", SYB_ORGID);
        //商户号 实际交易的商户号(必传)
        params.put("cusid", SYB_CUSID);
        //应用ID 平台分配的APPID(必传)
        params.put("appid", SYB_APPID);
        params.put("oldreqsn", reqsn);
        //随机字符串(必传)
        String validatecode = SybUtil.getValidatecode(8);
        params.put("randomstr", validatecode);
        //签名方式(必传)
        params.put("signtype", SIGN_TYPE);
        log.info("通联关闭订单验签参数:{}", params);
        String appkey = "";
        if (SIGN_TYPE.equals("RSA")) {
            appkey = SYB_RSACUSPRIKEY;
        } else if (SIGN_TYPE.equals("SM2")) {
            appkey = SYB_SM2PPRIVATEKEY;
        } else {
            appkey = SYB_MD5_APPKEY;
        }
        String result = null;
        RequestResponseInfoEntity requestResponseInfoEntity = new RequestResponseInfoEntity();
        try {
            String sign = SybUtil.unionSign(params, appkey, SIGN_TYPE);
            params.put("sign", sign);
            requestResponseInfoEntity.setRequestBody(params.toString());
            requestResponseInfoEntity.setTId(orderId);
            requestResponseInfoEntity.setTName(TNAME);
            requestResponseInfoEntity.setCode(RequestResponseCode.TONGLIAN_PAY_QUERY);
            requestResponseInfoMapper.insert(requestResponseInfoEntity);
            log.info("通联支付查询验签参数:{}", params);
            log.info("CustomerAppointmentServiceImpl searchTranxSend: SYB_CUSID:{},SYB_APPID:{},reqsn:{},validatecode:{},SIGN_TYPE:{},sign:{}", SYB_CUSID, SYB_APPID, "11", validatecode, SIGN_TYPE, sign);
            result = tongLianPayFeign.closeTransaction(SYB_CUSID, SYB_APPID, reqsn, validatecode, SIGN_TYPE, sign);
            log.info("CustomerAppointmentServiceImpl searchTranxSend: result:{}", result);
            requestResponseInfoEntity.setResponseBody(result);
            requestResponseInfoMapper.updateById(requestResponseInfoEntity);
        } catch (Exception e) {
            throw new BusinessException("查询交易sign签名失败,e{}", e);
        }
        log.info("通联支付查询交易返回结果:{}", result);
        Map<String, String> map = new HashMap<>();
        try {
            map = handleResult(result);
            log.info("通联支付查询交易返回的参数，map:{}", map.toString());
        } catch (Exception e) {
            throw new BusinessException("通联支付查询交易|||验证签名失败|||,e{}", e);
        }
        ObjectMapper objectMapper = new ObjectMapper();
        TlCloseTransactionVO tongLianSearchVO = new TlCloseTransactionVO();
        try {
            // 将 map 转换为 TongLianPayVO 对象
            tongLianSearchVO = objectMapper.convertValue(map, TlCloseTransactionVO.class);
            log.info("通联支付查询交易返回对象:{}", JSONUtil.toJsonStr(tongLianSearchVO));
        } catch (Exception e) {
            log.error("将 map 转换为对象时出错: {}", e.getMessage(), e);
        }
        return tongLianSearchVO;
    }

    private TongLianSearchVO searchTranxSend(Integer orderId, String paySnNum) {
        TreeMap<String, String> params = new TreeMap<String, String>();
        //        if (!SybUtil.isEmpty(SybConstants.SYB_ORGID)) {
        //            params.put("orgid", SybConstants.SYB_ORGID);
        //        }
        params.put("orgid", "");
        //商户号 实际交易的商户号(必传)
        params.put("cusid", SYB_CUSID);
        //应用ID 平台分配的APPID(必传)
        params.put("appid", SYB_APPID);
        //接口版本号 默认填11
        params.put("version", "11");
        params.put("reqsn", paySnNum);
        //随机字符串(必传)
        String validatecode = SybUtil.getValidatecode(8);
        params.put("randomstr", validatecode);
        //签名方式(必传)
        params.put("signtype", SIGN_TYPE);
        log.info("通联支付查询验签参数:{}", params.toString());
        String appkey = "";
        if (SIGN_TYPE.equals("RSA")) {
            appkey = SYB_RSACUSPRIKEY;
        } else if (SIGN_TYPE.equals("SM2")) {
            appkey = SYB_SM2PPRIVATEKEY;
        } else {
            appkey = SYB_MD5_APPKEY;
        }
        String result = null;
        RequestResponseInfoEntity requestResponseInfoEntity = new RequestResponseInfoEntity();
        try {
            String sign = SybUtil.unionSign(params, appkey, SIGN_TYPE);
            params.put("sign", sign);
            requestResponseInfoEntity.setRequestBody(params.toString());
            requestResponseInfoEntity.setTId(orderId);
            requestResponseInfoEntity.setTName(TNAME);
            requestResponseInfoEntity.setCode(RequestResponseCode.TONGLIAN_PAY_QUERY);
            requestResponseInfoMapper.insert(requestResponseInfoEntity);
            log.info("通联支付查询验签参数:{}", params);
            log.info("CustomerAppointmentServiceImpl searchTranxSend: SYB_CUSID:{},SYB_APPID:{},version:{},reqsn:{},validatecode:{},SIGN_TYPE:{},sign:{}", SYB_CUSID, SYB_APPID, "11", paySnNum, validatecode, SIGN_TYPE, sign);
            result = tongLianPayFeign.searchTranx(SYB_CUSID, SYB_APPID, "11", paySnNum, validatecode,
                    SIGN_TYPE, sign);
            requestResponseInfoEntity.setResponseBody(result);
            requestResponseInfoMapper.updateById(requestResponseInfoEntity);
        } catch (Exception e) {
            throw new BusinessException("查询交易sign签名失败,e{}", e);
        }
        log.info("通联支付查询交易返回结果:{}", result);
        Map<String, String> map = new HashMap<>();
        try {
            map = handleResult(result);
            log.info("通联支付查询交易返回的参数，map:{}", map.toString());
        } catch (Exception e) {
            throw new BusinessException("通联支付查询交易|||验证签名失败|||,e{}", e);
        }
        ObjectMapper objectMapper = new ObjectMapper();
        TongLianSearchVO tongLianSearchVO = new TongLianSearchVO();
        try {
            // 将 map 转换为 TongLianPayVO 对象
            tongLianSearchVO = objectMapper.convertValue(map, TongLianSearchVO.class);
        } catch (Exception e) {
            log.error("将 map 转换为对象时出错: {}", e.getMessage(), e);
        }
        return tongLianSearchVO;
    }

    @Override
    public String payCallback(TlPatCallbackDTO tlPatCallbackDTO) {
        log.info("CustomerAppointmentServiceImpl payCallback: tlPatCallbackDTO:{}", JSONUtil.toJsonStr(tlPatCallbackDTO));
        List<OrderFeeInfoEntity> orderFeeInfoEntities = new ArrayList<>();
        try {
            // 处理交易结果
            String trxstatus = tlPatCallbackDTO.getTrxstatus();
            String cusorderid = tlPatCallbackDTO.getCusorderid();
            orderFeeInfoEntities = orderFeeInfoMapper.selectList(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                    .eq(OrderFeeInfoEntity::getPaySnNum, cusorderid)
                    .eq(OrderFeeInfoEntity::getDeleteFlag, 0));
            log.info("orderFeeInfoEntities:{}", orderFeeInfoEntities);
            if (CollUtil.isEmpty(orderFeeInfoEntities)) {
                //todo 如果不存在，则转发给金蝶
                return "success";
            }
            OrderFeeInfoEntity orderFeeInfoEntity = orderFeeInfoEntities.get(0);
            log.info("orderFeeInfoEntity:{}", JSONUtil.toJsonStr(orderFeeInfoEntity));

            // 根据交易状态进行相应处理
            if ("0000".equals(trxstatus)) {
                // 交易成功
                orderFeeInfoEntity.setGpsFeeStatus(2);
            } else if ("1001".equals(trxstatus)) {
                orderFeeInfoEntity.setGpsFeeStatus(-1);
                log.info("交易不存在");
            } else if ("2008".equals(trxstatus) || "2000".equals(trxstatus)) {
                orderFeeInfoEntity.setGpsFeeStatus(1);
                log.info("交易处理中,请查询交易,");
            } else {
                orderFeeInfoEntity.setGpsFeeStatus(-1);
                log.info("交易失败");
            }

            updateFeeInfo(orderFeeInfoEntity.getOrderId(), 1, orderFeeInfoEntity.getId(), orderFeeInfoEntity.getGpsFeeStatus(), tlPatCallbackDTO.getTrxid());
            // 返回成功响应
            return "success";
        } catch (Exception e) {
            // 记录异常日志
            log.error("交易回调异常", e);
            return "error";
        }
    }

    @Override
    public String smsTriggered(SMSTriggeredDTO smsTriggeredDTO) {
//        if(!envUtil.isPrd()&&smsTriggeredDTO.getBingType()== 1){
//            BankAccountSignEntity bankAccountSignEntity = new BankAccountSignEntity()
//                    .setBankCardNumber(smsTriggeredDTO.getBankCardNumber())
//                    .setBankName(smsTriggeredDTO.getBankName())
//                    .setBankNameUpdate(smsTriggeredDTO.getBankNameUpdate())
//                    .setOrderId(smsTriggeredDTO.getOrderId())
//                    .setPhone(smsTriggeredDTO.getPhone())
//                    .setSignPlate(smsTriggeredDTO.getBingType())
//                    .setName(smsTriggeredDTO.getName())
//                    .setSignState(0)
//                    .setIdCardNum(smsTriggeredDTO.getIdCardNum())
//                    .setReqSn("6601270727700DV-****************")
//                    .setSignProtocolNo("AIP9089240911C80E7112D")
//                    .setOpenBankNumber(smsTriggeredDTO.getOpenBankNumber());
//            bankAccountSignMapper.insert(bankAccountSignEntity);
//            return "success";
//        }

        //若资方为盈峰则判断银行卡是否支持
        Integer orderId = smsTriggeredDTO.getOrderId();
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, "订单不存在");
        if (orderInfo.getFundId() == FundEnum.YING_FENG.getValue() && ObjectUtil.isNull(YingFengBankCodeEnum.getBankCode(smsTriggeredDTO.getBankName()))) {
            throw new BusinessException("该银行暂不支持绑定");
        }
        SignPlateEnum signPlateEnum = null;
        try {
            // 构建并发送请求
            if (smsTriggeredDTO.getBingType() == 1) {
                String respText = tongLianReqUtils.sendRequestToTongLian(smsTriggeredDTO);
                // 处理响应
                return handleTongLianResponse(respText, smsTriggeredDTO);
            } else if (smsTriggeredDTO.getBingType() == 2) {
                return yingFengBindCardApply(smsTriggeredDTO);
            } else if (smsTriggeredDTO.getBingType() == 3) {
                return zhongHengBindCardApply(smsTriggeredDTO);
            } else if (smsTriggeredDTO.getBingType() == 4) {
                return hengTongBindCardApply(smsTriggeredDTO, "1");
            } else if (smsTriggeredDTO.getBingType() == 5) {
                return hengTongBindCardApply(smsTriggeredDTO, "2");
            } else if (smsTriggeredDTO.getBingType() == 6) {
                return changYinBindCardApply(smsTriggeredDTO);
            }else if (smsTriggeredDTO.getBingType() == 7){
                signPlateEnum = SignPlateEnum.LIAN_LIAN;
            } else if(smsTriggeredDTO.getBingType() == 8) {
                signPlateEnum = SignPlateEnum.BAO_FU;
            } else if (smsTriggeredDTO.getBingType() == 9){
                signPlateEnum = SignPlateEnum.YI_BAO;
            } else {
                throw new BusinessException("短信触发接口调用失败,未知的绑定类型");
            }
            SignBankStrategy signBankLianlian = signBankStrategyFactory.getBankStrategy(signPlateEnum);

            SignBankSendMessageVO signBankSendMessageVO = signBankLianlian.sendMessage(smsTriggeredDTO);
            return StrUtil.isNotBlank(signBankSendMessageVO.getReqNo())?"success":"fail";
        } catch (Exception e) {
            log.error("获取验证码异常{}", e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
    }


    @Override
    public String manageSMSTriggered(SMSTriggeredDTO smsTriggeredDTO, LoginUser loginUser) {
//        if(!envUtil.isPrd()){
//            ManageBankAccountSignEntity managerBankAccountSignEntity = new ManageBankAccountSignEntity()
//                    .setBankName(smsTriggeredDTO.getBankName())
//                    .setBankNameUpdate(smsTriggeredDTO.getBankNameUpdate())
//                    .setBankCardNumber(smsTriggeredDTO.getBankCardNumber())
//                    .setPhone(smsTriggeredDTO.getPhone())
//                    .setUserId(loginUser.getUserId())
//                    .setSignState(1)
//                    .setCardType(smsTriggeredDTO.getCardType())
//                    .setName(smsTriggeredDTO.getName())
//                    .setIdCardNum(smsTriggeredDTO.getIdCardNum())
//                    .setReqSn("6601270727700DV-****************")
//                    .setSignProtocolNo("AIP9089240911C80E7112D")
//                    .setOpenBankNumber(smsTriggeredDTO.getOpenBankNumber());
//            manageBankAccountSignMapper.insert(managerBankAccountSignEntity);
//        }

        log.info("CustomerAppointmentServiceImpl manageSMSTriggered: smsTriggeredDTO:{}", JSONUtil.toJsonStr(smsTriggeredDTO));
        Integer userId = loginUser.getUserId();
        Integer cardType = smsTriggeredDTO.getCardType();
        Long count = manageBankAccountSignMapper.selectCount(new LambdaQueryWrapper<ManageBankAccountSignEntity>()
                .eq(ManageBankAccountSignEntity::getUserId, userId)
                .eq(ManageBankAccountSignEntity::getCardType, cardType)
                .eq(ManageBankAccountSignEntity::getSignState, 1)
                .eq(ManageBankAccountSignEntity::getDeleteFlag, 0));
        log.info("CustomerAppointmentServiceImpl manageSMSTriggered: count:{},cardType:{}", count, cardType);
        // 判断卡片类型并处理逻辑
        switch (cardType) {
            case 1:
                if (count > 0) {
                    throw new BusinessException("工资卡已绑定完成");
                }
                break;
            case 2:
                if (count >= 2) {
                    throw new BusinessException("返现卡已绑定完成");
                }
                break;
            default:
                throw new BusinessException("卡类型错误");
        }
        // 构建并发送请求
        String respText = tongLianReqUtils.sendRequestToTongLian(smsTriggeredDTO);
        try {
            if (!tlSignUtil.verifyXml(respText)) {
                log.info("====================================================>客户经理通联短信触发验签失败");
                throw new BusinessException("获取验证码");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        JSONObject jsonObj = XML.toJSONObject(respText);
        log.info("jsonObj:{}", jsonObj);

        TlSsmVo tlSsmVo = JSON.parseObject(jsonObj.toString(), TlSsmVo.class);

        TlSsmVo.Aipg aipg = tlSsmVo.getAipg();
        TlSsmVo.Info info = aipg.getInfo();
        TlSsmVo.Fagraret fagraret = aipg.getFagraret();
        if ("0000".equals(info.getRetCode())) {
            if ("0000".equals(fagraret.getRetCode())) {
                ManageBankAccountSignEntity managerBankAccountSignEntity = new ManageBankAccountSignEntity()
                        .setBankName(smsTriggeredDTO.getBankName())
                        .setBankNameUpdate(smsTriggeredDTO.getBankNameUpdate())
                        .setBankCardNumber(smsTriggeredDTO.getBankCardNumber())
                        .setPhone(smsTriggeredDTO.getPhone())
                        .setUserId(loginUser.getUserId())
                        .setSignState(0)
                        .setCardType(smsTriggeredDTO.getCardType())
                        .setName(smsTriggeredDTO.getName())
                        .setIdCardNum(smsTriggeredDTO.getIdCardNum())
                        .setReqSn(info.getReqSn())
                        .setOpenBankNumber(smsTriggeredDTO.getOpenBankNumber());
                manageBankAccountSignMapper.insert(managerBankAccountSignEntity);
                return "success";
            } else {
                throw new BusinessException(fagraret.getErrMsg());
            }
        }
        throw new BusinessException("获取验证码失败" + info.getErrMsg());
    }

    @Override
    public Boolean setDefaultCard(SMSTriggeredDTO smsTriggeredDTO, LoginUser loginUser) {
        ManageBankAccountSignEntity manageBankAccountSignEntity = manageBankAccountSignMapper.selectOne(new LambdaQueryWrapper<ManageBankAccountSignEntity>()
                .eq(ManageBankAccountSignEntity::getUserId, loginUser.getUserId())
                .eq(ManageBankAccountSignEntity::getBankCardNumber, smsTriggeredDTO.getBankCardNumber())
                .eq(ManageBankAccountSignEntity::getSignState, 1)
                .eq(ManageBankAccountSignEntity::getCardType, smsTriggeredDTO.getCardType())
                .eq(ManageBankAccountSignEntity::getDeleteFlag, 0));
        Assert.notNull(manageBankAccountSignEntity, "卡片不存在");
        //先将之前绑定的卡片设置为非默认
        manageBankAccountSignMapper.update(new LambdaUpdateWrapper<ManageBankAccountSignEntity>()
                .set(ManageBankAccountSignEntity::getIsDefault, 0)
                .eq(ManageBankAccountSignEntity::getUserId, loginUser.getUserId())
                .eq(ManageBankAccountSignEntity::getDeleteFlag, 0));
        //设置新的卡片为默认
        ManageBankAccountSignEntity manageBankAccountSignEntity1 = new ManageBankAccountSignEntity()
                .setId(manageBankAccountSignEntity.getId())
                .setIsDefault(1);
        return manageBankAccountSignMapper.updateById(manageBankAccountSignEntity1) > 0;
    }

    private String zhongHengBindCardApply(SMSTriggeredDTO smsTriggeredDTO) {
        if ("招商银行".equals(smsTriggeredDTO.getBankName())) {
            throw new BusinessException("暂不支持招商卡");
        }
        ZhongHengTYTLFaSongYzmDTO zhongHengTYTLFaSongYzmDTO = new ZhongHengTYTLFaSongYzmDTO()
                .setXingMing(smsTriggeredDTO.getName())
                .setKaiHuHang(smsTriggeredDTO.getBankName())
                .setYinHangKaNum(smsTriggeredDTO.getBankCardNumber())
                .setYuLiuShouJiNum(smsTriggeredDTO.getPhone())
                .setShenFengZhengNum(smsTriggeredDTO.getIdCardNum());
        ZhongHengApiResult<ZhongHengTYTLFaSongYZMVO> yingFengTYTLFaSongYZMVOZhongHengApiResult = approveFeign.zhongHengTYTLFaSongYZM(zhongHengTYTLFaSongYzmDTO);
        if ("000000".equals(yingFengTYTLFaSongYZMVOZhongHengApiResult.getCode())) {
            ZhongHengTYTLFaSongYZMVO responseData = yingFengTYTLFaSongYZMVOZhongHengApiResult.getResponseData();
            if ("T".equals(responseData.getState())) {
                BankAccountSignEntity bankAccountSignEntity = new BankAccountSignEntity()
                        .setOrderId(smsTriggeredDTO.getOrderId())
                        .setPhone(smsTriggeredDTO.getPhone())
                        .setBankCardNumber(smsTriggeredDTO.getBankCardNumber())
                        .setBankName(smsTriggeredDTO.getBankName())
                        .setBankNameUpdate(smsTriggeredDTO.getBankNameUpdate())
                        .setSignState(0)
                        .setReqSn(responseData.getLiuShuiOrderNum())
                        .setName(smsTriggeredDTO.getName())
                        .setIdCardNum(smsTriggeredDTO.getIdCardNum())
                        .setSignPlate(SignPlateEnum.ZHONG_HENG)
                        .setOpenBankNumber(smsTriggeredDTO.getOpenBankNumber());
                bankAccountSignMapper.insert(bankAccountSignEntity);
                return "success";
            } else {
                throw new BusinessException("获取验证码失败" + yingFengTYTLFaSongYZMVOZhongHengApiResult.getMsg());
            }
        } else {
            throw new BusinessException("获取验证码失败" + yingFengTYTLFaSongYZMVOZhongHengApiResult.getMsg());
        }
    }

    /**
     * 恒通绑卡申请
     *
     * @param smsTriggeredDTO 绑卡信息
     * @param signType        码值如下： 1：通联  2：嘉泰
     */
    private String hengTongBindCardApply(SMSTriggeredDTO smsTriggeredDTO, String signType) {
        if ("招商银行".equals(smsTriggeredDTO.getBankName())) {
            throw new BusinessException("暂不支持招商卡");
        }
        HengTongBankCardSMSCodeDTO bankCardSMSCodeDTO = new HengTongBankCardSMSCodeDTO()
                .setXingMing(smsTriggeredDTO.getName())
                .setKaiHuHang(smsTriggeredDTO.getBankName())
                .setYinHangKaNum(smsTriggeredDTO.getBankCardNumber())
                .setYuLiuShouJiNum(smsTriggeredDTO.getPhone())
                .setShenFengZhengNum(smsTriggeredDTO.getIdCardNum())
                .setQianYueType(signType);
        ZhongHengApiResult<ZhongHengTYTLFaSongYZMVO> hengTongBankCardSMSCodeResult = approveFeign.hengTongBankCardSMSCode(bankCardSMSCodeDTO);
        if (ZhongHengApiResult.isSuccess(hengTongBankCardSMSCodeResult)) {
            ZhongHengTYTLFaSongYZMVO responseData = hengTongBankCardSMSCodeResult.getResponseData();
            if ("T".equals(responseData.getState())) {
                BankAccountSignEntity bankAccountSignEntity = new BankAccountSignEntity()
                        .setOrderId(smsTriggeredDTO.getOrderId())
                        .setPhone(smsTriggeredDTO.getPhone())
                        .setBankCardNumber(smsTriggeredDTO.getBankCardNumber())
                        .setBankName(smsTriggeredDTO.getBankName())
                        .setBankNameUpdate(smsTriggeredDTO.getBankNameUpdate())
                        .setSignState(0)
                        .setReqSn(responseData.getLiuShuiOrderNum())
                        .setName(smsTriggeredDTO.getName())
                        .setIdCardNum(smsTriggeredDTO.getIdCardNum())
                        .setSignPlate(ObjUtil.equals(signType, "1") ? SignPlateEnum.HENG_TONG_TONG_LIAN  : SignPlateEnum.HENG_TONG_JIA_TAI)
                        .setOpenBankNumber(smsTriggeredDTO.getOpenBankNumber());
                bankAccountSignMapper.insert(bankAccountSignEntity);
                return "success";
            } else {
                throw new BusinessException("获取验证码失败" + hengTongBankCardSMSCodeResult.getResponseData().getNote());
            }
        } else {
            throw new BusinessException("获取验证码失败" + hengTongBankCardSMSCodeResult.getMsg());
        }
    }

    private String yingFengBindCardApply(SMSTriggeredDTO smsTriggeredDTO) {
        String custId = getPayNumber();
        YingFengBindCardApplyDTO yingFengBindCardApplyDTO = new YingFengBindCardApplyDTO();
        yingFengBindCardApplyDTO.setName(smsTriggeredDTO.getName())
                .setMobile(smsTriggeredDTO.getPhone())
                .setIdNo(smsTriggeredDTO.getIdCardNum())
                .setCardNo(smsTriggeredDTO.getBankCardNumber())
                .setCustId(custId)
                .setBankCode(YingFengBankCodeEnum.getBankCode(smsTriggeredDTO.getBankName()));
        //推给盈峰
        Result<YingFengBindCardApplyVO> yingFengBindCardApplyVOResult = approveFeign.yingFengBindCardApply(yingFengBindCardApplyDTO);
        log.info("盈峰触发验证码返回报文:{}", yingFengBindCardApplyVOResult);
        if ("00000".equals(yingFengBindCardApplyVOResult.getCode())) {
            BankAccountSignEntity bankAccountSignEntity = new BankAccountSignEntity()
                    .setOrderId(smsTriggeredDTO.getOrderId())
                    .setPhone(smsTriggeredDTO.getPhone())
                    .setBankCardNumber(smsTriggeredDTO.getBankCardNumber())
                    .setBankName(smsTriggeredDTO.getBankName())
                    .setBankNameUpdate(smsTriggeredDTO.getBankNameUpdate())
                    .setSignState(0)
                    .setReqSn(yingFengBindCardApplyVOResult.getData().getAuthId())
                    .setCustId(custId)
                    .setName(smsTriggeredDTO.getName())
                    .setIdCardNum(smsTriggeredDTO.getIdCardNum())
                    .setSignPlate(SignPlateEnum.YING_FENG)
                    .setOpenBankNumber(smsTriggeredDTO.getOpenBankNumber());
            bankAccountSignMapper.insert(bankAccountSignEntity);
            return "success";
        } else {
            log.info("盈峰触发验证码失败:{}", yingFengBindCardApplyVOResult.getMsg());
            throw new BusinessException(yingFengBindCardApplyVOResult.getMsg());
        }
    }

    private String changYinBindCardApply(SMSTriggeredDTO smsTriggeredDTO) {
        log.info("CustomerAppointmentServiceImpl.changYinBindCardApply: smsTriggeredDTO{}", smsTriggeredDTO);
        ChangYinSignInfoDTO changYinSignInfoDTO = new ChangYinSignInfoDTO();
        String sign = "LH" + RandomUtil.randomString(3)+DateUtil.format(new Date(), "yyyyMMddHHmmss");
        changYinSignInfoDTO.setSignSeq(sign)
                .setOperateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"))
                .setAcctNo(smsTriggeredDTO.getBankCardNumber())
                .setAcctName(smsTriggeredDTO.getName())
                .setIdTyp("20")
                .setIdNo(smsTriggeredDTO.getIdCardNum())
                .setAcctPhone(smsTriggeredDTO.getPhone())
                .setAcctBankCode(BankNameEnum.getCodeByName(smsTriggeredDTO.getBankName()));
        log.info("CustomerAppointmentServiceImpl.changYinBindCardApply: ChangYinSignInfoDTO{}", changYinSignInfoDTO);
        ChangYinResBodyDTO<ChangYinSignResDTO> result = approveFeign.protocolApply(changYinSignInfoDTO);
        log.info("CustomerAppointmentServiceImpl.changYinBindCardApply: resultResult{}", result);
        if (ChangYinResBodyDTO.isSuccess(result) && result.getBody().getResultCode().equals("0000")) {
            BankAccountSignEntity bankAccountSignEntity = new BankAccountSignEntity()
                    .setOrderId(smsTriggeredDTO.getOrderId())
                    .setPhone(smsTriggeredDTO.getPhone())
                    .setBankCardNumber(smsTriggeredDTO.getBankCardNumber())
                    .setBankName(smsTriggeredDTO.getBankName())
                    .setBankNameUpdate(smsTriggeredDTO.getBankNameUpdate())
                    .setSignState(0)
                    .setReqSn(result.getBody().getAgrSeq())
                    .setCustId(null)
                    .setName(smsTriggeredDTO.getName())
                    .setIdCardNum(smsTriggeredDTO.getIdCardNum())
                    .setSignPlate(SignPlateEnum.CHANG_YIN)
                    .setOpenBankNumber(smsTriggeredDTO.getOpenBankNumber());
            bankAccountSignMapper.insert(bankAccountSignEntity);
            return "success";
        } else if (ChangYinResBodyDTO.isSuccess(result) && (result.getBody().getResultCode().equals("1000") || result.getBody().getResultCode().equals("1001"))) {
            //获取客户以前的相同绑卡信息
            BankAccountSignEntity oldBankAccountSignEntity = bankAccountSignMapper.selectOne(new LambdaQueryWrapper<BankAccountSignEntity>()
                    .eq(BankAccountSignEntity::getPhone, smsTriggeredDTO.getPhone())
                    .eq(BankAccountSignEntity::getBankCardNumber, smsTriggeredDTO.getBankCardNumber())
                    .eq(BankAccountSignEntity::getIdCardNum, smsTriggeredDTO.getIdCardNum())
                    .eq(BankAccountSignEntity::getSignState, 1)
                    .eq(BankAccountSignEntity::getSignPlate, SignPlateEnum.CHANG_YIN)
                    .eq(BankAccountSignEntity::getDeleteFlag, 0)
                    .orderByDesc(BankAccountSignEntity::getCreateTime)
                    .last("limit 1"));
            if (oldBankAccountSignEntity != null) {
                log.info("CustomerAppointmentServiceImpl.changYinBindCardApply: oldBankAccountSignEntity{}", oldBankAccountSignEntity);
                oldBankAccountSignEntity.setId(null);
                oldBankAccountSignEntity.setCreateTime(null);
                oldBankAccountSignEntity.setUpdateTime(null);
                oldBankAccountSignEntity.setOrderId(smsTriggeredDTO.getOrderId());
                bankAccountSignMapper.insert(oldBankAccountSignEntity);
                return BankBindEnum.BIND_SUCCESS.getCode();
            }
            return BankBindEnum.BIND_FAIL.getCode();
        } else{
            if (ObjUtil.isNull(result.getBody())){
                log.info("长银触发验证码失败:{}", result.getHead().getRespMsg());
                throw new BusinessException(result.getHead().getRespMsg());
            }
            log.info("长银触发验证码失败:{}", result.getBody().getResultMsg());
            throw new BusinessException(result.getBody().getResultMsg());
        }
    }

    private String handleTongLianResponse(String respText, SMSTriggeredDTO smsTriggeredDTO) throws Exception {
        if (!tlSignUtil.verifyXml(respText)) {
            log.info("====================================================>通联短信触发验签失败");
            throw new BusinessException("验签失败");
        }

        JSONObject jsonObj = XML.toJSONObject(respText);

        TlSsmVo tlSsmVo = JSON.parseObject(jsonObj.toString(), TlSsmVo.class);

        log.info("tlSsmVo:{}", JSON.toJSONString(tlSsmVo));

        TlSsmVo.Aipg aipg = tlSsmVo.getAipg();
        TlSsmVo.Info info = aipg.getInfo();
        TlSsmVo.Fagraret fagraret = aipg.getFagraret();

        log.info("InfoRsp:{}", JSON.toJSONString(info));

        if ("0000".equals(info.getRetCode())) {
            if ("0000".equals(fagraret.getRetCode())) {
                BankAccountSignEntity bankAccountSignEntity = new BankAccountSignEntity()
                        .setBankCardNumber(smsTriggeredDTO.getBankCardNumber())
                        .setBankName(smsTriggeredDTO.getBankName())
                        .setBankNameUpdate(smsTriggeredDTO.getBankNameUpdate())
                        .setOrderId(smsTriggeredDTO.getOrderId())
                        .setPhone(smsTriggeredDTO.getPhone())
                        .setSignPlate(SignPlateEnum.getSignPlateEnum(smsTriggeredDTO.getBingType()))
                        .setName(smsTriggeredDTO.getName())
                        .setIdCardNum(smsTriggeredDTO.getIdCardNum())
                        .setReqSn(info.getReqSn())
                        .setOpenBankNumber(smsTriggeredDTO.getOpenBankNumber());
                bankAccountSignMapper.insert(bankAccountSignEntity);

                return "success";
            } else {
                throw new BusinessException(fagraret.getErrMsg());
            }
        }
        throw new BusinessException(info.getErrMsg());
    }

    @Override
    public String bankCardSign(TLSignDTO tlSignDTO) {
//        if(!envUtil.isPrd()&&tlSignDTO.getBingType()== 1){
//            BankAccountSignEntity bankAccountSignEntity = getSignCard(tlSignDTO.getBingType(), tlSignDTO.getOrderId());
//            delCard(tlSignDTO.getOrderId(),bankAccountSignEntity);
//            //再修改新的卡
//            bankAccountSignEntity.setSignState(1);
//            bankAccountSignMapper.updateById(bankAccountSignEntity);
//            return "success";
//        }

        log.info("CustomerAppointmentServiceImpl bankCardSign: tlSignDTO:{}", JSONUtil.toJsonStr(tlSignDTO));
        Integer orderId = tlSignDTO.getOrderId();
        String reqSn = "";
        String phone = "";
        String name = "";
        String idCardNum = "";
        BankAccountSignEntity bankAccountSignEntity = null;
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        SignPlateEnum signPlateEnum = null;
        if (tlSignDTO.getBingType() == 1) {
            bankAccountSignEntity = getSignCard(tlSignDTO.getBingType(), orderId);
            reqSn = bankAccountSignEntity.getReqSn();
            phone = bankAccountSignEntity.getPhone();
            name = bankAccountSignEntity.getName();
            idCardNum = bankAccountSignEntity.getIdCardNum();

            String signReq = tongLianReqUtils.cardSign(reqSn, phone, name, idCardNum, orderId, tlSignDTO.getVerCode());
            String msg = handleResponse(signReq, tlSignDTO, null, bankAccountSignEntity);

            //富民 设置自动扣款卡 放款成功后
            if (ObjUtil.equals(FundEnum.FU_MIN.getValue(), orderInfo.getFundId()) && ObjectUtil.equals(orderInfo.getCurrentNode(), States.PAYMENT_SUCCESS.getNode())) {
                log.info("CustomerAppointmentServiceImpl bankCardSign FuMin orderId:{}", orderId);
                Result<Boolean> fuMinWithholdCardResult = approveFeign.fuMinWithholdCard(orderId);

                if (!Result.isSuccess(fuMinWithholdCardResult)) {
                    log.info("CustomerAppointmentServiceImpl bankCardSign FuMinWithholdCard orderId:{} error:{}", orderId, fuMinWithholdCardResult.getMsg());
                    throw new BusinessException(fuMinWithholdCardResult.getMsg());
                }

                if (!fuMinWithholdCardResult.getData()) {
                    throw new BusinessException("富民绑卡失败");
                }
            } else if (ObjUtil.equals(FundEnum.CHANG_YIN.getValue(), orderInfo.getFundId()) && ObjectUtil.isNotNull(bankAccountSignEntity)) {
                log.info("CustomerAppointmentServiceImpl bankCardSign ChangYin orderId:{}", orderId);
                Boolean isBindChangYin = verifyChangYinBindCard(orderId, bankAccountSignEntity.getId());
                if (isBindChangYin) {
                    return BankBindEnum.BIND_ALL_SUCCESS.getCode();
                }
                //换卡操作
                boolean flag = ObjectUtil.equals(orderInfo.getCurrentNode(), States.PAYMENT_SUCCESS.getNode()) ||
                        ObjUtil.equals(orderInfo.getCurrentNode(), States.SETTLED.getNode());
                if (flag) {
                    Boolean b = modifyAccount(bankAccountSignEntity.getId());
                    if (b) {
                        return BankBindEnum.BIND_ALL_SUCCESS.getCode();
                    }
                }

            }
            return msg;

        } else if (tlSignDTO.getBingType() == 2) {

            List<BankAccountSignEntity> bankAccountSignEntities = bankAccountSignMapper.selectList(
                    new LambdaQueryWrapper<BankAccountSignEntity>()
                            .eq(BankAccountSignEntity::getOrderId, orderId)
                            .eq(BankAccountSignEntity::getDeleteFlag, 0)
                            .eq(BankAccountSignEntity::getBankCardNumber, tlSignDTO.getBankCardNumber())
                            .eq(BankAccountSignEntity::getSignPlate, tlSignDTO.getBingType())
                            .orderByDesc(BankAccountSignEntity::getCreateTime)
            );

            if (CollUtil.isEmpty(bankAccountSignEntities)) {
                log.info("客户未获取短信验证码");
                throw new BusinessException("未获取短信验证码");
            }

            bankAccountSignEntity = bankAccountSignEntities.get(0);

            YingFengBindCardVerifyCodeDTO yingFengBindCardVerifyCodeDTO = new YingFengBindCardVerifyCodeDTO()
                    .setCustId(bankAccountSignEntity.getCustId())
                    .setAuthId(bankAccountSignEntity.getReqSn())
                    .setCardNo(tlSignDTO.getBankCardNumber())
                    .setMobile(bankAccountSignEntity.getPhone())
                    .setValidCode(tlSignDTO.getVerCode());

            Result<YingFengBindCardVerifyCodeVO> yingFengBindCardVerifyCodeVOResult = null;
            try {
                yingFengBindCardVerifyCodeVOResult = approveFeign.yingFengBindCardVerifyCode(yingFengBindCardVerifyCodeDTO);
            } catch (Exception e) {
                log.error("CustomerAppointmentServiceImpl.bankCardSign bindCardVerifyCode e{}", e.getMessage(), e);
                throw new BusinessException("银行卡绑定失败");
            }
            log.info("盈峰支付签约请求报文:{}", yingFengBindCardVerifyCodeVOResult);
            if ("00000".equals(yingFengBindCardVerifyCodeVOResult.getCode())) {
                YingFengBindCardVerifyCodeVO data = yingFengBindCardVerifyCodeVOResult.getData();
                if (data == null || data.getSignProtocolNo() == null) {
                    throw new BusinessException("银行卡绑定失败");
                }
                bankAccountSignEntity.setSignState(1);
                bankAccountSignEntity.setSignProtocolNo(data.getSignProtocolNo());
                if (ObjectUtil.equals(orderInfo.getCurrentNode(), States.PAYMENT_SUCCESS.getNode())) {
                    bankAccountSignEntity.setCardType(1);
                }
                bankAccountSignMapper.updateById(bankAccountSignEntity);
                return "success";
            }
            log.info(yingFengBindCardVerifyCodeVOResult.getMsg());
            log.info(yingFengBindCardVerifyCodeVOResult.getMsg());
            throw new BusinessException(yingFengBindCardVerifyCodeVOResult.getMsg());
        } else if (tlSignDTO.getBingType() == 3) {
            List<BankAccountSignEntity> bankAccountSignEntities = bankAccountSignMapper.selectList(
                    new LambdaQueryWrapper<BankAccountSignEntity>()
                            .eq(BankAccountSignEntity::getOrderId, orderId)
                            .eq(BankAccountSignEntity::getBankCardNumber, tlSignDTO.getBankCardNumber())
                            .eq(BankAccountSignEntity::getDeleteFlag, 0)
                            .eq(BankAccountSignEntity::getSignPlate, tlSignDTO.getBingType())
                            .orderByDesc(BankAccountSignEntity::getCreateTime)
            );

            if (CollUtil.isEmpty(bankAccountSignEntities)) {
                log.info("该客户没有获取短信验证码");
                throw new BusinessException("没有获取短信验证码");
            }

            bankAccountSignEntity = bankAccountSignEntities.get(0);
            ZhongHengTYTongLianContractendDto zhongHengTYTongLianContractendDto = new ZhongHengTYTongLianContractendDto()
                    .setYanZhengMa(tlSignDTO.getVerCode())
                    .setLiuShuiOrderNum(bankAccountSignEntity.getReqSn());
            ZhongHengApiResult<ZhongHengTYTLQianYueVO> yingFengTYTLQianYueVOZhongHengApiResult =
                    approveFeign.zhongHengTonglianContracted(zhongHengTYTongLianContractendDto);
            if ("000000".equals(yingFengTYTLQianYueVOZhongHengApiResult.getCode())) {
                ZhongHengTYTLQianYueVO data = yingFengTYTLQianYueVOZhongHengApiResult.getResponseData();
                if ("T".equals(data.getState())) {
                    bankAccountSignEntity.setSignState(1)
                            .setSignProtocolNo(data.getYhkQianYueNum());
                    bankAccountSignMapper.updateById(bankAccountSignEntity);
                    return "success";
                }
            }
            throw new BusinessException(yingFengTYTLQianYueVOZhongHengApiResult.getMsg());
        } else if (tlSignDTO.getBingType() == 4 || tlSignDTO.getBingType() == 5) {
            //首次绑卡解绑其他卡信息
            /*if (ObjUtil.equals(tlSignDTO.getBingType(), 4)) {
                hengTongDelCard(orderId);
            }*/
            // 恒通绑卡
            bankAccountSignEntity = bankAccountSignMapper.selectOne(
                    new LambdaQueryWrapper<BankAccountSignEntity>()
                            .eq(BankAccountSignEntity::getOrderId, orderId)
                            .eq(BankAccountSignEntity::getBankCardNumber, tlSignDTO.getBankCardNumber())
                            .eq(BankAccountSignEntity::getDeleteFlag, 0)
                            .eq(BankAccountSignEntity::getSignPlate, tlSignDTO.getBingType())
                            .orderByDesc(BankAccountSignEntity::getCreateTime)
                    , false
            );

            if (ObjUtil.isNull(bankAccountSignEntity)) {
                log.info("该客户没有获取短信验证码");
                throw new BusinessException("没有获取短信验证码");
            }
            HengTongBankSignDTO hengTongBankSignDTO = new HengTongBankSignDTO()
                    .setYanZhengMa(tlSignDTO.getVerCode())
                    .setLiuShuiOrderNum(bankAccountSignEntity.getReqSn())
                    .setQianYueType(ObjUtil.equals(tlSignDTO.getBingType(), 4) ? "1" : "2");
            ZhongHengApiResult<ZhongHengTYTLQianYueVO> hengTongBankCardSignResult =
                    approveFeign.hengTongBankCardSign(hengTongBankSignDTO);
            if (ZhongHengApiResult.isSuccess(hengTongBankCardSignResult)) {
                ZhongHengTYTLQianYueVO data = hengTongBankCardSignResult.getResponseData();
                if ("T".equals(data.getState())) {
                    bankAccountSignEntity.setSignState(1)
                            .setSignProtocolNo(data.getYhkQianYueNum());

                    if (ObjectUtil.equals(orderInfo.getCurrentNode(), States.PAYMENT_SUCCESS.getNode()) ||
                            ObjUtil.equals(orderInfo.getCurrentNode(), States.SETTLED.getNode())) {
                        bankAccountSignEntity.setCardType(1);
                    }
                    bankAccountSignMapper.updateById(bankAccountSignEntity);
                    if (ObjectUtil.equals(orderInfo.getCurrentNode(), States.PAYMENT_SUCCESS.getNode())) {
                        hengTongATMCardInfoSync(bankAccountSignEntity, orderId, orderInfo);
                    }

                    return "success";
                } else {
                    throw new BusinessException(data.getNote());
                }
            }
            throw new BusinessException(hengTongBankCardSignResult.getMsg());
        } else if (tlSignDTO.getBingType() == 6){
            List<BankAccountSignEntity> bankAccountSignEntities = bankAccountSignMapper.selectList(
                    new LambdaQueryWrapper<BankAccountSignEntity>()
                            .eq(BankAccountSignEntity::getOrderId, orderId)
                            .eq(BankAccountSignEntity::getBankCardNumber, tlSignDTO.getBankCardNumber())
                            .eq(BankAccountSignEntity::getDeleteFlag, 0)
                            .eq(BankAccountSignEntity::getSignPlate, tlSignDTO.getBingType())
                            .orderByDesc(BankAccountSignEntity::getCreateTime)
            );

            if (CollUtil.isEmpty(bankAccountSignEntities)) {
                log.info("该客户没有获取短信验证码");
                throw new BusinessException("没有获取短信验证码");
            }

            bankAccountSignEntity = bankAccountSignEntities.get(0);

            ChangYinSignConfirmDTO changYinSignConfirmDTO = new ChangYinSignConfirmDTO();
            changYinSignConfirmDTO.setSignSeq(bankAccountSignEntity.getReqSn()).setOperateTime(DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_PATTERN)).setSmsNo(tlSignDTO.getVerCode());
            ChangYinResBodyDTO<ChangYinSignResDTO> result = approveFeign.protocolConfirm(changYinSignConfirmDTO);
            if (ChangYinResBodyDTO.isSuccess(result) && result.getBody().getResultCode().equals("1002")) {
                bankAccountSignEntity.setSignState(1)
                        .setSignProtocolNo(result.getBody().getAgrSeq());
                if (ObjectUtil.equals(orderInfo.getCurrentNode(), States.PAYMENT_SUCCESS.getNode()) ||
                        ObjUtil.equals(orderInfo.getCurrentNode(), States.SETTLED.getNode())) {
                    bankAccountSignEntity.setCardType(1);
                }
                bankAccountSignMapper.updateById(bankAccountSignEntity);
                return "success";
            }else {
                if (ObjUtil.isNull(result.getBody())){
                    log.info("签约失败:{}", result.getHead().getRespMsg());
                    throw new BusinessException(result.getHead().getRespMsg());
                }
                throw new BusinessException("签约失败"+result.getBody().getResultMsg());
            }
        }else if (tlSignDTO.getBingType() == 7){
            signPlateEnum = SignPlateEnum.LIAN_LIAN;
        }else if (tlSignDTO.getBingType() == 8){
            signPlateEnum = SignPlateEnum.BAO_FU;
        }else if (tlSignDTO.getBingType() == 9){
            signPlateEnum = SignPlateEnum.YI_BAO;
        } else {
            throw new UnsupportedOperationException("暂不支绑卡类型");
        }
        SignBankStrategy signBankLianlian = signBankStrategyFactory.getBankStrategy(signPlateEnum);
        SignBankVerifyMessageVO signBankSendMessageVO = signBankLianlian.verifyMessage(tlSignDTO);
        return StrUtil.isNotBlank(signBankSendMessageVO.getProtocolNo())?"success":"fail";
    }

    /**
     * 中恒绑卡同步
     * @param bankAccountSignEntity
     * @param orderId
     * @param orderInfo
     */
    private void hengTongATMCardInfoSync(BankAccountSignEntity bankAccountSignEntity, Integer orderId, OrderInfoEntity orderInfo) {
        BankAccountSignEntity newAccount = bankAccountSignMapper.selectById(bankAccountSignEntity.getId());

        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(
                new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .in(FinalFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue(),FundEnum.ZHONG_HENG.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .last("limit 1")
        );
        if (ObjUtil.isNull(finalFundInfoEntity)){
            throw new BusinessException("资方终审列表为空");
        }
        HengTongATMCardInfoSyncDTO hengTongATMCardInfoSyncDTO = new HengTongATMCardInfoSyncDTO();
        List<HengTongATMCardInfoSyncDTO.ATMCardList> atmCardList = new ArrayList<>();
        HengTongATMCardInfoSyncDTO.ATMCardList atmCardInfo = new HengTongATMCardInfoSyncDTO.ATMCardList();

        atmCardInfo.setBankCard(newAccount.getBankCardNumber())
                .setBankName(newAccount.getBankName())
                .setMobilePhone(newAccount.getPhone())
                .setSignNum(newAccount.getSignProtocolNo())
                .setSignType(ObjUtil.equals(newAccount.getSignPlate() ,SignPlateEnum.HENG_TONG_TONG_LIAN) ? 1 : 2);
        atmCardList.add(atmCardInfo);
        hengTongATMCardInfoSyncDTO
                .setSpOrderNum(orderInfo.getOrderNumber())
                .setOrderNum(finalFundInfoEntity.getLoanBillNo())
                .setAtmCardList(atmCardList);
        try {
            ZhongHengApiResult<HengTongATMCardInfoSyncVO> result = approveFeign.hengTongATMCardInfoSync(hengTongATMCardInfoSyncDTO);
            if (!ZhongHengApiResult.isSuccess(result)) {
                throw new BusinessException("银行卡信息同步失败:"+ result.getMsg());
            }
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessage());
        }
    }

    private void sendFinishEvent(Integer orderId, Integer userId, Events event) {
        orderStateService.sendEvent(States.CUSTOMER_APPOINTMENT, event, orderId, userId);
    }

    private BankAccountSignEntity getSignCard(Integer bingType, Integer orderId) {
        BankAccountSignEntity bankAccountSignEntity;
        List<BankAccountSignEntity> bankAccountSignEntities = bankAccountSignMapper.selectList(
                new LambdaQueryWrapper<BankAccountSignEntity>()
                        .eq(BankAccountSignEntity::getOrderId, orderId)
                        .eq(BankAccountSignEntity::getDeleteFlag, 0)
                        .eq(BankAccountSignEntity::getSignPlate, bingType)
                        .orderByDesc(BankAccountSignEntity::getCreateTime)
        );

        if (CollUtil.isEmpty(bankAccountSignEntities)) {
            log.info("客户没有获取短信验证码");
            throw new BusinessException("未获取短信验证码");
        }

        bankAccountSignEntity = bankAccountSignEntities.get(0);
        return bankAccountSignEntity;
    }

    @Override
    public Boolean updateOrderFile(UpdateOrderFileDTO updateOrderFileDTO, LoginUser loginUser) {
        log.info("OrderFileServiceImpl.updateOrderFile start updateOrderFileDTO: {}", JSONUtil.toJsonStr(updateOrderFileDTO));
        Integer orderId = updateOrderFileDTO.getOrderId();
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);

        if (Objects.equals(orderInfo.getCurrentNode(), States.PAYMENT_SUCCESS.getNode())) {
            updateOrderBankCardFile(updateOrderFileDTO);
            return true;
        }

        // 执行更新操作
        orderFileMapper.update(new LambdaUpdateWrapper<OrderFileEntity>()
                .eq(OrderFileEntity::getOrderId, updateOrderFileDTO.getOrderId())
                .in(OrderFileEntity::getFileId, Arrays.asList(19, 20))
                .set(OrderFileEntity::getDeleteFlag, 1));
        //重新插入
        updateOrderBankCardFile(updateOrderFileDTO);


        if (Objects.equals(orderInfo.getFundId(), FundEnum.YING_FENG.getValue())) {
            //重新推送
            UploadDesignateTypeDTO uploadDesignateTypeDTO = new UploadDesignateTypeDTO();
            uploadDesignateTypeDTO.setFundId(FundEnum.YING_FENG.getValue());
            uploadDesignateTypeDTO.setPreId(orderInfo.getPreId());
            uploadDesignateTypeDTO.setType(2);
            uploadDesignateTypeDTO.setPreFileTypeCodeList(new ArrayList<>(List.of(PreFileTypeEnums.BANK_CARD.getCode(), PreFileTypeEnums.BANK_CARD_BACK.getCode())));
            log.info("OrderFileServiceImpl.updateOrderFile uploadDesignateTypeDTO: {}", JSONUtil.toJsonStr(uploadDesignateTypeDTO));
            approveFeign.uploadDesignateType(uploadDesignateTypeDTO);
        } else if (Objects.equals(orderInfo.getFundId(), FundEnum.FU_MIN.getValue())) {
            // 更换富民收款卡
            updateFuMinReceiveCard(orderId);

        }
        // 重置合同签署状态和资方签署状态
        contractFileService.resetFundSignStatus(orderId);
        orderContractService.update(new LambdaUpdateWrapper<OrderContractEntity>()
                .set(OrderContractEntity::getDeleteFlag, 1)
                .eq(OrderContractEntity::getOrderId, orderId)
                .ne(OrderContractEntity::getFundGenerateFlag, 1)
                .eq(OrderContractEntity::getDeleteFlag, 0)
        );
        signTaskMapper.update(new LambdaUpdateWrapper<SignTaskEntity>()
                .set(SignTaskEntity::getDeleteFlag, 1)
                .eq(SignTaskEntity::getBusiId, orderId)
                .eq(SignTaskEntity::getSignType, 1)
                .eq(SignTaskEntity::getDeleteFlag, 0));
        // 重新生成合同
//        contractFileService.generateContractSignList(orderId);

        // 更换银行卡后重新进行合同签署
        if (Arrays.asList(States.CUSTOMER_APPOINTMENT.getNode(),
                States.PAYMENT_APPLY_INFORMATION.getNode(),
                States.PAYMENT_CONTRACT_APPROVAL.getNode(),
                States.FUNDS_PAYMENT_FAIL.getNode()).contains(orderInfo.getCurrentNode())) {
            //如果订单状态大于2700，则调用状态机修改状态
            Integer userId = loginUser.getUserId();
            if (!Objects.equals(loginUser.getUserType(), UserTypeEnum.EMPLOYEE.getType())) {
                userId = -2;
            }
            orderStateService.sendEvent(States.getByNode(orderInfo.getCurrentNode()), Events.REBIND_BLANK_CARD, orderId, userId);

        }

        return true;
    }

    /**
     * 更换富民收款卡
     *
     * @param orderId 订单 ID
     */
    private void updateFuMinReceiveCard(Integer orderId) {

        // 检查签约状态
        Long signCount = fundSignInfoMapper.selectCount(new LambdaQueryWrapper<FundSignInfoEntity>()
                .eq(FundSignInfoEntity::getOrderId, orderId)
                .eq(FundSignInfoEntity::getDeleteFlag, 0)
                .eq(FundSignInfoEntity::getSignStatus, FundSignStatusEnum.SUCCESS.getCode())
        );
        if (signCount == 0) {
            log.info("updateFuMinReceiveCard orderId {} not found signInfo", orderId);
            return;
        }

        bankAccountSignMapper.selectList(new LambdaQueryWrapper<BankAccountSignEntity>()
                .eq(BankAccountSignEntity::getOrderId, orderId)
                .eq(BankAccountSignEntity::getDeleteFlag, 0)
                .eq(BankAccountSignEntity::getSignState, 1)
                .eq(BankAccountSignEntity::getSignPlate, SignPlateEnum.TONG_LIAN)
                .eq(BankAccountSignEntity::getCardType, 0)
                .orderByDesc(BankAccountSignEntity::getCreateTime)
        ).stream().findFirst().ifPresent(bankAccountSignEntity -> {
            // 校验卡号是否变更
            List<BankAccountSignEntity> bankAccountSignEntities = bankAccountSignMapper.selectList(new LambdaQueryWrapper<BankAccountSignEntity>()
                    .eq(BankAccountSignEntity::getOrderId, orderId)
                    .eq(BankAccountSignEntity::getDeleteFlag, 1)
                    .eq(BankAccountSignEntity::getSignState, 1)
                    .eq(BankAccountSignEntity::getSignPlate, SignPlateEnum.TONG_LIAN)
                    .eq(BankAccountSignEntity::getCardType, 0)
                    .orderByDesc(BankAccountSignEntity::getCreateTime));
            if (CollUtil.isNotEmpty(bankAccountSignEntities)) {
                BankAccountSignEntity lastRecord = bankAccountSignEntities.get(0);

                if (bankAccountSignEntity.getSignProtocolNo().equals(lastRecord.getSignProtocolNo())
                        && bankAccountSignEntity.getBankCardNumber().equals(lastRecord.getBankCardNumber())) {
                    // 协议号相同 不变更
                    log.warn("updateFuMinReceiveCard order {} signProtocolNo or bankCardNumber not change", orderId);
                    return;
                }
            }

            BankCardInfoDTO bankCardInfoDTO = new BankCardInfoDTO();
            bankCardInfoDTO.setBankName(bankAccountSignEntity.getBankNameUpdate());
            bankCardInfoDTO.setBindMobile(bankAccountSignEntity.getPhone());
            bankCardInfoDTO.setProtocolNo(bankAccountSignEntity.getSignProtocolNo());
            bankCardInfoDTO.setCardNo(bankAccountSignEntity.getBankCardNumber());
            bankCardInfoDTO.setLinkId(orderId);
            bankCardInfoDTO.setType(2);
            Result<FuMinPreviewContractDTO> fuMinPreviewContractDTOResult = approveFeign.fuMinReceiveCardChange(bankCardInfoDTO);

            if (Result.isSuccess(fuMinPreviewContractDTOResult)) {
                FuMinPreviewContractDTO data = fuMinPreviewContractDTOResult.getData();
                contractService.saveFuMinContract(data);
            } else {
                throw new BusinessException("富民更换付款卡失败:" + fuMinPreviewContractDTOResult.getMsg());
            }

        });
    }

    /**
     * 更新 Order银行卡文件
     *
     * @param updateOrderFileDTO 更新订单文件 DTO
     */
    private void updateOrderBankCardFile(UpdateOrderFileDTO updateOrderFileDTO) {
        OrderFileEntity cardFront = new OrderFileEntity();
        cardFront.setOrderId(updateOrderFileDTO.getOrderId());
        cardFront.setFileId(19);
        cardFront.setFileName(updateOrderFileDTO.getBankCardFrontName());
        cardFront.setResourceId(updateOrderFileDTO.getBankCardFront());
        cardFront.setResourceName(updateOrderFileDTO.getBankCardFrontResourceName());
        log.info("OrderFileServiceImpl.updateOrderFile cardFront: {}", JSONUtil.toJsonStr(cardFront));
        orderFileMapper.insert(cardFront);

        OrderFileEntity cardBack = new OrderFileEntity();
        cardBack.setOrderId(updateOrderFileDTO.getOrderId());
        cardBack.setFileId(20);
        cardBack.setFileName(updateOrderFileDTO.getBankCardBackName());
        cardBack.setResourceId(updateOrderFileDTO.getBankCardBack());
        cardBack.setResourceName(updateOrderFileDTO.getBankCardBackResourceName());
        log.info("OrderFileServiceImpl.updateOrderFile cardBack: {}", JSONUtil.toJsonStr(cardFront));
        orderFileMapper.insert(cardBack);
    }

    @Override
    public Boolean replaceCard(Integer orderId) {
        return bankAccountSignMapper.update(new LambdaUpdateWrapper<BankAccountSignEntity>()
                .eq(BankAccountSignEntity::getOrderId, orderId)
                .eq(BankAccountSignEntity::getDeleteFlag, 0)
                .set(BankAccountSignEntity::getDeleteFlag, 1)) > 0;
    }

    @Override
    public String manageBankCardSign(TLSignDTO tlSignDTO, LoginUser loginUser) {
//        if(!envUtil.isPrd()){
//            return "success";
//        }
        log.info("CustomerAppointmentServiceImpl manageBankCardSign: tlSignDTO:{}", JSONUtil.toJsonStr(tlSignDTO));
        Integer orderId = tlSignDTO.getOrderId();
        Integer userId = loginUser.getUserId();
        String reqSn = "";
        String phone = "";
        String name = "";
        String idCardNum = "";
        ManageBankAccountSignEntity manageBankAccountSignEntity = null;
        List<ManageBankAccountSignEntity> manageBankAccountSignEntities = manageBankAccountSignMapper.selectList(
                new LambdaQueryWrapper<ManageBankAccountSignEntity>()
                        .eq(ManageBankAccountSignEntity::getUserId, userId)
                        .eq(ManageBankAccountSignEntity::getSignState, 1)
                        .eq(ManageBankAccountSignEntity::getBankCardNumber, tlSignDTO.getBankCardNumber())
        );

        log.info("CustomerAppointmentServiceImpl manageBankCardSign: manageBankAccountSignEntities:{}", manageBankAccountSignEntities);

        if (CollUtil.isNotEmpty(manageBankAccountSignEntities)) {
            throw new BusinessException("不能重复绑定同一张银行卡");
        }

        List<ManageBankAccountSignEntity> manageBankAccountSignEntities2 = manageBankAccountSignMapper.selectList(
                new LambdaQueryWrapper<ManageBankAccountSignEntity>()
                        .eq(ManageBankAccountSignEntity::getUserId, userId)
                        .eq(ManageBankAccountSignEntity::getSignState, 0)
                        .eq(ManageBankAccountSignEntity::getBankCardNumber, tlSignDTO.getBankCardNumber())
                        .orderByDesc(ManageBankAccountSignEntity::getCreateTime)
        );

        log.info("CustomerAppointmentServiceImpl manageBankCardSign: manageBankAccountSignEntities2:{}", manageBankAccountSignEntities2);

        if (CollUtil.isEmpty(manageBankAccountSignEntities2)) {
            log.info("未获取短信验证码");
            throw new BusinessException("未获取短信验证码");
        }

        manageBankAccountSignEntity = manageBankAccountSignEntities2.get(0);
        reqSn = manageBankAccountSignEntity.getReqSn();
        phone = manageBankAccountSignEntity.getPhone();
        name = manageBankAccountSignEntity.getName();
        idCardNum = manageBankAccountSignEntity.getIdCardNum();

        log.info("CustomerAppointmentServiceImpl manageBankCardSign: reqSn:{}, phone:{}, name:{}, idCardNum:{}", reqSn, phone, name, idCardNum);
        String signReq = tongLianReqUtils.cardSign(reqSn, phone, name, idCardNum, orderId, tlSignDTO.getVerCode());
        return handleResponse(signReq, tlSignDTO, manageBankAccountSignEntity, null);
    }

    @Override
    public SearchCardListVO searchCardList(Integer orderId) {
        List<BankAccountSignEntity> paymentCardList = getBankAccountSigns(orderId, 0);
        List<BankAccountSignEntity> repaymentCardList = getBankAccountSigns(orderId, 1);

        //查询图片信息
        List<OrderFileEntity> orderFileEntities = orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                .eq(OrderFileEntity::getOrderId, orderId)
                .in(OrderFileEntity::getFileId, Arrays.asList(19, 20))
                .eq(OrderFileEntity::getDeleteFlag, 0));

        //查询订单节点
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Integer currentNode = orderInfoEntity.getCurrentNode();
        return new SearchCardListVO().setPaymentCardList(paymentCardList)
                .setRepaymentCardList(repaymentCardList)
                .setOrderFileEntities(orderFileEntities)
                .setCurrentNode(currentNode)
                .setFundId(orderInfoEntity.getFundId());
    }

    private List<BankAccountSignEntity> getBankAccountSigns(Integer orderId, Integer cardType) {
        return bankAccountSignMapper.selectList(
                new LambdaQueryWrapper<BankAccountSignEntity>()
                        .select(BankAccountSignEntity::getBankCardNumber,
                                BankAccountSignEntity::getBankName,
                                BankAccountSignEntity::getBankNameUpdate,
                                BankAccountSignEntity::getPhone,
                                BankAccountSignEntity::getName,
                                BankAccountSignEntity::getIdCardNum,
                                BankAccountSignEntity::getSignPlate,
                                BankAccountSignEntity::getCardType,
                                BankAccountSignEntity::getOpenBankNumber)
                        .eq(BankAccountSignEntity::getOrderId, orderId)
                        .eq(BankAccountSignEntity::getSignState, 1)
                        .eq(BankAccountSignEntity::getCardType, cardType)
                        .eq(BankAccountSignEntity::getDeleteFlag, 0)
        );
    }

    private String handleResponse(String respText, TLSignDTO tlSignDTO, ManageBankAccountSignEntity manageBankAccountSignEntity, BankAccountSignEntity bankAccountSignEntity) {
        log.info("CustomerAppointmentServiceImpl handleResponse respText:{}, tlSignDTO:{}, manageBankAccountSignEntity:{}, bankAccountSignEntity:{}",
                respText, tlSignDTO, manageBankAccountSignEntity, bankAccountSignEntity);
        JSONObject jsonObj = XML.toJSONObject(respText);
        log.info("签约jsonObj:{}", jsonObj.toString());

        TlSignVo tlSsmVo = JSON.parseObject(jsonObj.toString(), TlSignVo.class);

        TlSignVo.Aipg aipg = tlSsmVo.getAipg();
        log.info("aipg:{}", JSON.toJSONString(aipg));
        TlSignVo.Info info = aipg.getInfo();
        TlSignVo.Fagrcret fagrcret = aipg.getFagrcret();

        if (!"0000".equals(info.getRetCode())) {
            throw new BusinessException(info.getErrMsg());
        }

        if ("0000".equals(fagrcret.getRetCode())) {
            if (tlSignDTO.getBankCardNumber() != null && manageBankAccountSignEntity != null) {
                manageBankAccountSignEntity.setSignState(1);
                manageBankAccountSignEntity.setSignProtocolNo(fagrcret.getAgrmno());
                manageBankAccountSignMapper.updateById(manageBankAccountSignEntity);
                log.info("客户经理{}签约调用通联支付签约成功", manageBankAccountSignEntity.getName());
            } else if (bankAccountSignEntity != null) {
                delCard(tlSignDTO.getOrderId(), bankAccountSignEntity);

                //再修改新的卡
                bankAccountSignEntity.setSignState(1);
                bankAccountSignEntity.setSignProtocolNo(fagrcret.getAgrmno());
                bankAccountSignMapper.updateById(bankAccountSignEntity);
                log.info("客户{}签约调用通联支付签约成功", bankAccountSignEntity.getName());
            }
            return fagrcret.getErrMsg();
        } else {
            throw new BusinessException(fagrcret.getErrMsg());
        }
    }

    private void delCard(Integer orderId, BankAccountSignEntity bankAccountSignEntity) {
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);

        Long count = bankAccountSignMapper.selectCount(new LambdaQueryWrapper<BankAccountSignEntity>()
                .eq(BankAccountSignEntity::getOrderId, orderId)
                .eq(BankAccountSignEntity::getSignState, 1)
                .eq(BankAccountSignEntity::getCardType, 0)
                .eq(BankAccountSignEntity::getDeleteFlag, 0));
        if (count > 0) {
            if (ObjectUtil.equals(orderInfo.getCurrentNode(), States.PAYMENT_SUCCESS.getNode())) {
                //先将原来的卡置为失效
                bankAccountSignMapper.update(new LambdaUpdateWrapper<BankAccountSignEntity>()
                        .eq(BankAccountSignEntity::getOrderId, orderId)
                        .eq(BankAccountSignEntity::getSignState, 1)
                        .eq(BankAccountSignEntity::getDeleteFlag, 0)
                        .eq(BankAccountSignEntity::getCardType, 1)
                        .set(BankAccountSignEntity::getDeleteFlag, 1));
                bankAccountSignEntity.setCardType(1);
            } else {
                //先将原来的卡置为失效
                bankAccountSignMapper.update(new LambdaUpdateWrapper<BankAccountSignEntity>()
                        .eq(BankAccountSignEntity::getOrderId, orderId)
                        .eq(BankAccountSignEntity::getSignState, 1)
                        .eq(BankAccountSignEntity::getDeleteFlag, 0)
                        .eq(BankAccountSignEntity::getCardType, 0)
                        .set(BankAccountSignEntity::getDeleteFlag, 1));
            }
        }
    }

    /**
     * 恒通解绑卡
     *
     * @param orderId
     */
    private void hengTongDelCard(Integer orderId) {
        Long count = orderInfoMapper.selectCount(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getId, orderId)
                .in(OrderInfoEntity::getCurrentNode, States.PAYMENT_SUCCESS.getNode(), States.SETTLED.getNode())
        );
        //放款前重新绑卡
        if (count == 0) {
            List<BankAccountSignEntity> paymentBankList = bankAccountSignMapper.selectList(new LambdaQueryWrapper<BankAccountSignEntity>()
                    .eq(BankAccountSignEntity::getOrderId, orderId)
                    .eq(BankAccountSignEntity::getSignState, 1)
                    .in(BankAccountSignEntity::getCardType, List.of(4,5))
                    .eq(BankAccountSignEntity::getCardType, 0)
                    .eq(BankAccountSignEntity::getDeleteFlag, 0));
            if (CollUtil.isNotEmpty(paymentBankList)) {
                for (BankAccountSignEntity bankAccountSign : paymentBankList) {
                    //解约银行卡
                    HengTongBankCancelSignDTO hengTongBankCancelSignDTO = new HengTongBankCancelSignDTO()
                            .setYHKQianYueNum(bankAccountSign.getSignProtocolNo())
                            .setQianYueType(ObjUtil.equals(bankAccountSign.getSignPlate(), SignPlateEnum.HENG_TONG_TONG_LIAN) ? "1" : "2");
                    try {
                        ZhongHengApiResult<ZhongHengTYTLJieYueVO> result = approveApi.hengTongBankCardCancelSign(hengTongBankCancelSignDTO);
                        if (ZhongHengApiResult.isSuccess(result) && ObjUtil.equals(result.getResponseData().getState(), "T")) {
                            bankAccountSignMapper.update(new LambdaUpdateWrapper<BankAccountSignEntity>()
                                    .eq(BankAccountSignEntity::getOrderId, orderId)
                                    .eq(BankAccountSignEntity::getSignState, 1)
                                    .eq(BankAccountSignEntity::getDeleteFlag, 0)
                                    .eq(BankAccountSignEntity::getCardType, 0)
                                    .set(BankAccountSignEntity::getDeleteFlag, 1)
                                    .set(BankAccountSignEntity::getSignState, 2));
                        }
                    } catch (Exception e) {
                        log.error("CustomerAppointmentServiceImpl.bankCardSign bingType cancelSign e{}", e.getMessage(), e);
                    }
                }
                bankAccountSignMapper.update(new LambdaUpdateWrapper<BankAccountSignEntity>()
                        .eq(BankAccountSignEntity::getOrderId, orderId)
                        .eq(BankAccountSignEntity::getSignState, 1)
                        .eq(BankAccountSignEntity::getDeleteFlag, 0)
                        .eq(BankAccountSignEntity::getCardType, 0)
                        .set(BankAccountSignEntity::getDeleteFlag, 1));

            }
        }
    }

    @Override
    public String bankCardTermination(TLSignDTO tlSignDTO, LoginUser loginUser) {
        String bankCardNumber = tlSignDTO.getBankCardNumber();
        Integer userId = loginUser.getUserId();
        tlSignDTO.setUserId(userId);
        ManageBankAccountSignEntity manageBankAccountSignEntity = new ManageBankAccountSignEntity();
        List<ManageBankAccountSignEntity> manageBankAccountSignEntitys = manageBankAccountSignMapper.selectList(new LambdaQueryWrapper<ManageBankAccountSignEntity>()
                .eq(ManageBankAccountSignEntity::getUserId, userId)
                .eq(ManageBankAccountSignEntity::getSignState, 1)
                .eq(ManageBankAccountSignEntity::getBankCardNumber, tlSignDTO.getBankCardNumber()));
        if (CollUtil.isEmpty(manageBankAccountSignEntitys)) {
            throw new BusinessException("该银行卡未绑定");
        }
        manageBankAccountSignEntity = manageBankAccountSignEntitys.get(0);
        JSONObject jsonObj = tongLianReqUtils.cardTermination(bankCardNumber, userId);
        JSONObject aipgRsp = jsonObj.getJSONObject("AIPG");
        JSONObject infoRsp = aipgRsp.getJSONObject("INFO");
        JSONObject ftRsp = aipgRsp.getJSONObject("FAGRCNLRET");
        log.info("客户解约infoRsp:{}", infoRsp.toString());
        if ("0000".equals(infoRsp.get("RET_CODE"))) {
            if ("0000".equals(ftRsp.get("RET_CODE"))) {
                manageBankAccountSignEntity.setSignState(2);
                manageBankAccountSignMapper.updateById(manageBankAccountSignEntity);
                log.info("客户解约通联支付解约接口调用成功");
            }
        }
        throw new BusinessException(ftRsp.get("ERR_MSG").toString());
    }

    @Override
    public String customerPaymentTermination(CustomerCardTerminationDTO customerCardTerminationDTO, LoginUser loginUser) {
//        if(!envUtil.isPrd()){
//            return "success";
//        }
        String bankCardNumber = customerCardTerminationDTO.getBankCardNumber();
        Integer orderId = customerCardTerminationDTO.getOrderId();
        Integer userId = loginUser.getUserId();
        List<BankAccountSignEntity> bankAccountSignEntities = bankAccountSignMapper.selectList(new LambdaQueryWrapper<BankAccountSignEntity>()
                .eq(BankAccountSignEntity::getBankCardNumber, bankCardNumber)
                .eq(BankAccountSignEntity::getDeleteFlag, 0)
                .eq(BankAccountSignEntity::getSignState, 1)
                .eq(BankAccountSignEntity::getSignPlate, SignPlateEnum.TONG_LIAN)
                .eq(BankAccountSignEntity::getOrderId, orderId));
        if (CollUtil.isEmpty(bankAccountSignEntities)) {
            throw new BusinessException("该卡号未绑定");
        }
        BankAccountSignEntity bankAccountSignEntity = new BankAccountSignEntity();
        bankAccountSignEntity.setId(bankAccountSignEntities.get(0).getId());
        JSONObject jsonObj = tongLianReqUtils.cardTermination(bankCardNumber, userId);
        JSONObject aipgRsp = jsonObj.getJSONObject("AIPG");
        JSONObject infoRsp = aipgRsp.getJSONObject("INFO");
        JSONObject ftRsp = aipgRsp.getJSONObject("FAGRCNLRET");
        log.info("解约infoRsp:{}", infoRsp.toString());
        if ("0000".equals(infoRsp.get("RET_CODE"))) {
            if ("0000".equals(ftRsp.get("RET_CODE"))) {
                bankAccountSignEntity.setSignState(2);
                bankAccountSignMapper.updateById(bankAccountSignEntity);
                log.info("客户经理解约通联支付解约接口调用成功");
            }
            return ftRsp.get("ERR_MSG").toString();
        }
        throw new BusinessException(ftRsp.get("ERR_MSG").toString());
    }

    @Override
    public List<ManageBankAccountSignEntity> searchBankCardNum(LoginUser loginUser) {
        Integer userId = loginUser.getUserId();
        return manageBankAccountSignMapper.selectList(new LambdaQueryWrapper<ManageBankAccountSignEntity>()
                .eq(ManageBankAccountSignEntity::getUserId, userId)
                .eq(ManageBankAccountSignEntity::getDeleteFlag, 0)
                .eq(ManageBankAccountSignEntity::getSignState, 1));
    }

    @Override
    public Integer searchCardNum(Integer userId) {
        Long count = manageBankAccountSignMapper.selectCount(new LambdaQueryWrapper<ManageBankAccountSignEntity>()
                .eq(ManageBankAccountSignEntity::getUserId, userId)
                .eq(ManageBankAccountSignEntity::getDeleteFlag, 0)
                .eq(ManageBankAccountSignEntity::getSignState, 1));
        return Convert.toInt(count);
    }

    /**
     * 获取银行名称
     *
     * @param bankCardNum 银行卡 Num
     * @return {@link String }
     */
    @Override
    public String getBankCardName(String bankCardNum) {
        try {
            return BankNameUtils.getbankName(bankCardNum);
        } catch (Exception e) {
            log.error("getBankCardName bankCardNum:{} error {}", bankCardNum, e.getMessage(), e);
            throw new BusinessException("银行卡号解析失败，请填写开户银行名称");
        }
    }

    @Override
    public void updateFeeInfo(Integer orderId, Integer userId, Integer dataId, Integer gpsFeeStatus, String paySnNumBack) {
        log.info("CustomerAppointmentServiceImpl.updateFeeInfo orderId:{},dataId:{},gpsFeeStatus:{}", orderId, dataId, gpsFeeStatus);

        OrderFeeInfoEntity updateEntity = new OrderFeeInfoEntity();
        updateEntity.setId(dataId);
        updateEntity.setGpsFeeStatus(gpsFeeStatus);
        if (paySnNumBack != null && !paySnNumBack.isEmpty()) {
            updateEntity.setPaySnNumBack(paySnNumBack);
        }
        if (gpsFeeStatus==2){
            updateEntity.setGpsPayType(1);
        }

        orderFeeInfoMapper.updateById(updateEntity);

        orderPageInfoService.updateOrderPageInfo(orderId, States.GPS_FEE_PAYMENT, 1);

        if (gpsFeeStatus == 2) {
            OrderFeeInfoEntity orderFeeInfoEntity = orderFeeInfoMapper.selectById(updateEntity.getId());
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
            orderFeeDetailService.saveOrderFeeDetail(orderId,
                    orderFeeInfoEntity.getPaySnNumBack(),
                    orderFeeInfoEntity.getGpsTotalFee(),
                    OrderFeeDetailTradingMethodsEnum.SCAN_THE_QR_CODE_TO_PAY,
                    orderInfoEntity.getCustomerName(),
                    "河北龙环谛听网络科技有限公司",
                    OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE,
                    OrderFeeDetailStatusEnum.INCOME,
                    orderFeeInfoEntity.getUpdateTime(),
                    null,null);
            sendAppointmentEvent(orderId, userId, UserTypeEnum.EMPLOYEE.getType(), Events.GPS_FEE_PAY_FINISH);
        }
    }

    private void sendAppointmentEvent(Integer orderId, Integer userId, Integer userType, Events event) {
        if (!Objects.equals(userType, UserTypeEnum.EMPLOYEE.getType())) {
            userId = -2;
        }
        orderStateService.sendEvent(States.CUSTOMER_APPOINTMENT, event, orderId, userId);
    }

    @Override
    public String searchTranxScheduled() {
        List<OrderFeeInfoEntity> orderFeeInfoEntities = orderFeeInfoMapper.selectList(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                .eq(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                .eq(OrderFeeInfoEntity::getDeleteFlag, 0));
        if (CollUtil.isEmpty(orderFeeInfoEntities)) {
            return null;
        }
        for (OrderFeeInfoEntity orderFeeInfoEntity : orderFeeInfoEntities) {
            searchTranx(orderFeeInfoEntity.getOrderId(), null);
        }
        return "success";
    }

    @Override
    public Boolean modifyTheLoanMethod(ModifyTheLoanMethodDTO modifyTheLoanMethodDTO) {
        int update = orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                .set(OrderInfoEntity::getPaymentType, modifyTheLoanMethodDTO.getPaymentType())
                .eq(OrderInfoEntity::getId, modifyTheLoanMethodDTO.getOrderId())
                .eq(OrderInfoEntity::getDeleteFlag, 0)
        );
//        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(modifyTheLoanMethodDTO.getOrderId());
//        if (Objects.equals(orderInfoEntity.getCurrentNode(), States.PAYMENT_SUCCESS.getNode())
//                &&
//                orderInfoEntity.getPaymentState()==OrderPaymentStateEnum.PASS
//        ){
//            if (modifyTheLoanMethodDTO.getPaymentType()==2){
//                if (ObjUtil.isNotEmpty(orderInfoEntity)&& orderInfoEntity.getPaymentType()==2){
//                    List<AfterLoanPatchesEntity> list = afterLoanPatchesEntityService.list(new LambdaQueryWrapper<AfterLoanPatchesEntity>()
//                            .eq(AfterLoanPatchesEntity::getOrderId, modifyTheLoanMethodDTO.getOrderId()));
//                    if (CollUtil.isEmpty(list)){
//                        AfterLoanPatchesEntity entity=new AfterLoanPatchesEntity();
//                        entity.setOrderId(modifyTheLoanMethodDTO.getOrderId());
//                        entity.setAfterLoanStatus(AfterLoanPatchesEnum.AFTER_LOAN_PATCHES.getCode());
//                        entity.setGpsState(orderInfoEntity.getGpsState());
//                        entity.setPayoffState(orderInfoEntity.getPlanState());
//                        afterLoanPatchesEntityService.save(entity);
//                    }else {
//                        list.forEach(afterLoanPatchesEntity -> {
//                            afterLoanPatchesEntityService.update(new LambdaUpdateWrapper<AfterLoanPatchesEntity>()
//                                    .eq(AfterLoanPatchesEntity::getId, afterLoanPatchesEntity.getId())
//                                    .set(AfterLoanPatchesEntity::getDeleteFlag, 0));
//                        });
//                    }
//                }
//            }
//            if (modifyTheLoanMethodDTO.getPaymentType()==1){
//                List<AfterLoanPatchesEntity> list = afterLoanPatchesEntityService.list(new LambdaQueryWrapper<AfterLoanPatchesEntity>()
//                        .eq(AfterLoanPatchesEntity::getOrderId, modifyTheLoanMethodDTO.getOrderId()));
//                if (CollUtil.isNotEmpty(list)){
//                    list.forEach(afterLoanPatchesEntity -> {
//                        afterLoanPatchesEntityService.update(new LambdaUpdateWrapper<AfterLoanPatchesEntity>()
//                                .eq(AfterLoanPatchesEntity::getId, afterLoanPatchesEntity.getId())
//                                .set(AfterLoanPatchesEntity::getDeleteFlag, 1));
//                    });
//                }
//            }
//        }
        return update > 0;
    }

    @Override
    public Boolean isOnlineOrder(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        return Objects.equals(orderInfoEntity.getSourceType(), 1) && (Objects.equals(orderInfoEntity.getRegionId(), 24) || Objects.equals(orderInfoEntity.getRegionId(), 56));
    }

    public Map<String, String> handleResult(String result) throws Exception {
        log.info("ret:" + result);
        Map map = SybUtil.json2Obj(result, Map.class);
        if (map == null) {
            throw new Exception("返回数据错误");
        }
        if ("SUCCESS".equals(map.get("retcode"))) {
            TreeMap tmap = new TreeMap(map);
            String appkey = "";
            if (SIGN_TYPE.equals("RSA")) {
                appkey = SYB_RSATLPUBKEY;
            } else if (SIGN_TYPE.equals("SM2")) {
                appkey = SYB_SM2TLPUBKEY;
            } else {
                appkey = SYB_MD5_APPKEY;
            }
            if (SybUtil.validSign(tmap, appkey, SIGN_TYPE)) {
                log.info("签名成功");
                return map;
            } else {
                throw new Exception("|||验证签名失败|||");
            }

        } else {
            return map;
        }
    }

    @Override
    public Boolean modifyAccount(Integer Id) {
        //查询通联的绑卡信息
        BankAccountSignEntity tongLianAccount = bankAccountSignMapper.selectById(Id);
        if (ObjUtil.isNull(tongLianAccount)) {
            return false;
        }
        Integer orderId = tongLianAccount.getOrderId();
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isNull(orderInfo)){
            return false;
        }
        //构建请求
        ChangYinUpdateAccountDTO changYinUpdateAccountDTO = new ChangYinUpdateAccountDTO();
        changYinUpdateAccountDTO.setName(tongLianAccount.getBankName())
                .setIdCardNum(tongLianAccount.getIdCardNum())
                .setName(tongLianAccount.getName())
                .setBankName(tongLianAccount.getBankName())
                .setBankCardNumber(tongLianAccount.getBankCardNumber())
                .setPhone(tongLianAccount.getPhone());
        log.info("ChangYinServiceImpl.changeAccountApply begin fundPreBaseDTO: {}", changYinUpdateAccountDTO);
        ChangYinResBodyDTO<ChangYinAccountChangeResDTO> result  = null;

        try {
             result = approveFeign.changeAccountApply(changYinUpdateAccountDTO);
        } catch (Exception e) {
            return false;
        }
        if (ChangYinResBodyDTO.isSuccess(result) || ObjUtil.isNotNull(result.getBody())){
            ChangYinAccountChangeResDTO body = result.getBody();
            if("01".equals(body.getStatus())){
                //更新历史数据
//                bankAccountSignMapper.update(new LambdaUpdateWrapper<BankAccountSignEntity>()
//                        .eq(BankAccountSignEntity::getOrderId, changYinUpdateAccountDTO.getOrderId())
//                        .eq(BankAccountSignEntity::getDeleteFlag, 0)
//                        .eq(BankAccountSignEntity::getSignPlate,SignPlateEnum.CHANG_YIN)
//                        .eq(BankAccountSignEntity::getSignState, 1)
//                        .eq(BankAccountSignEntity::getCardType, 1)
//                        .set(BankAccountSignEntity::getDeleteFlag, 1)) ;
                //新增绑卡数据
                BankAccountSignEntity bankAccountSignEntity = new BankAccountSignEntity();
                bankAccountSignEntity.setOrderId(changYinUpdateAccountDTO.getOrderId())
                        .setBankCardNumber(changYinUpdateAccountDTO.getBankCardNumber())
                        .setSignState(1)
                        .setSignPlate(SignPlateEnum.CHANG_YIN)
                        .setReqSn(body.getOutAcctChgSeq())
                        .setPhone(changYinUpdateAccountDTO.getPhone())
                        .setBankName(changYinUpdateAccountDTO.getBankNameUpdate())
                        .setCardType(tongLianAccount.getCardType())
                        .setName(tongLianAccount.getName())
                        .setIdCardNum(tongLianAccount.getIdCardNum())
                        .setOpenBankNumber(tongLianAccount.getOpenBankNumber())
                        .setSignProtocolNo(body.getOutAcctChgSeq());
                bankAccountSignMapper.insert(bankAccountSignEntity);
                return true;
            }else {
                return false;
            }
        }else {
            return false;
        }

    }

    /**
     * 验证长银资方通联绑卡后是否需要再次绑定银行卡
     * @return true:需要再次绑卡，false:不需要再次绑卡
     */
    private Boolean verifyChangYinBindCard(Integer orderId, Integer bankCardId) {
        Long count = orderInfoMapper.selectCount(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getId, orderId)
                .eq(OrderInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
        );
        if (count < 1) {
            log.info("verifyChangYinBindCard orderId:{} is not changYin", orderId);
            return false;
        }

        BankAccountSignEntity bankAccountSign = bankAccountSignMapper.selectOne(new LambdaQueryWrapper<BankAccountSignEntity>()
                .eq(BankAccountSignEntity::getId, bankCardId)
                .eq(BankAccountSignEntity::getSignState, 1)
                .eq(BankAccountSignEntity::getDeleteFlag, 0)
                , false
        );
        if (ObjUtil.isNull(bankAccountSign)) {
            return false;
        }

        //获取长银已绑定银行卡
        BankAccountSignEntity oldBankAccountSign = bankAccountSignMapper.selectOne(new LambdaQueryWrapper<BankAccountSignEntity>()
                .eq(BankAccountSignEntity::getPhone, bankAccountSign.getPhone())
                .eq(BankAccountSignEntity::getBankCardNumber, bankAccountSign.getBankCardNumber())
                .eq(BankAccountSignEntity::getIdCardNum, bankAccountSign.getIdCardNum())
                .eq(BankAccountSignEntity::getSignState, 1)
                .eq(BankAccountSignEntity::getSignPlate, SignPlateEnum.CHANG_YIN)
                .orderByDesc(BankAccountSignEntity::getCreateTime)
                .last("limit 1"));
        if (ObjUtil.isNull(oldBankAccountSign)) {
            return false;
        }

        //新增绑卡数据
        // 新增绑卡数据
        BankAccountSignEntity bankAccountSignEntity = new BankAccountSignEntity();
        bankAccountSignEntity.setOrderId(orderId)
                .setBankCardNumber(oldBankAccountSign.getBankCardNumber())
                .setSignState(1)
                .setSignPlate(SignPlateEnum.CHANG_YIN)
                .setReqSn(oldBankAccountSign.getSignProtocolNo())
                .setIdCardNum(oldBankAccountSign.getIdCardNum())
                .setPhone(oldBankAccountSign.getPhone())
                .setBankNameUpdate(oldBankAccountSign.getBankNameUpdate())
                .setCardType(bankAccountSign.getCardType())
                .setOpenBankNumber(oldBankAccountSign.getOpenBankNumber())
                .setName(oldBankAccountSign.getName())
                .setCustId(oldBankAccountSign.getCustId())
                .setBankName(oldBankAccountSign.getBankName())
                .setSignProtocolNo(oldBankAccountSign.getSignProtocolNo())
        ;

        int insert = bankAccountSignMapper.insert(bankAccountSignEntity);
        return insert > 0;
    }


}
