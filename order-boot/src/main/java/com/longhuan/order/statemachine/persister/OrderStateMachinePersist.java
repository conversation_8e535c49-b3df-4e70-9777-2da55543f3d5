package com.longhuan.order.statemachine.persister;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderStateMachinePersist implements StateMachinePersist<States, Events, Integer> {

    public static final String MACHINE_ID = "进件审批";
    // 用 map 来模拟持久化存储，可替换成数据库
    static Map<Integer, States> cache = new HashMap<>(16);

    private final OrderInfoMapper orderInfoMapper;

    @Override
    public void write(StateMachineContext<States, Events> stateMachineContext, Integer orderId) {
        Integer node = stateMachineContext.getState().getNode();
        log.info("OrderStateMachinePersist write orderId:{} ,node:{}", orderId, node);

        OrderInfoEntity orderInfoEntity = new OrderInfoEntity();
        orderInfoEntity.setId(orderId);
        orderInfoEntity.setState(node);

        orderInfoMapper.updateById(orderInfoEntity);

    }

    @Override
    public StateMachineContext<States, Events> read(Integer orderId) {
        Objects.requireNonNull(orderId, "订单号不存在!");
        log.info("OrderStateMachinePersist read orderId={}", orderId);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId, OrderInfoEntity::getState).eq(OrderInfoEntity::getId, orderId));

        Objects.requireNonNull(orderInfoEntity, "订单不存在!");
        Integer state = orderInfoEntity.getState();

        return state != null ?
                new DefaultStateMachineContext<>(States.getNode(state), null, null, null, null, MACHINE_ID) :
                new DefaultStateMachineContext<>(States.BUSINESS_ADDED_INFO, null, null, null, null, MACHINE_ID);

    }
}
