package com.longhuan.order.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasc.open.api.bean.base.BaseRes;
import com.fasc.open.api.v5_1.res.signtask.CreateSignTaskRes;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.vo.ContractRestartFileInfoVO;
import com.longhuan.order.pojo.vo.ContractRestartListVO;
import com.longhuan.order.service.ContractRestartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@Api("合同补签接口")
@RequestMapping("/api/v1/restart/contract")
public class ContractRestartController {

    private final ContractRestartService contractRestartService;

    @ApiOperation(value = "创建补签合同任务")
    @PostMapping("/fdd/create/task")
    BaseRes<CreateSignTaskRes> contractRestartSignTask(@RequestBody RestartContactDTO restartContactDTO) {
        return contractRestartService.contractRestartSignTask(restartContactDTO);
    }
    @ApiOperation(value = "合同补签列表")
    @PostMapping("/restart/page")
    public Result<Page<ContractRestartListVO>> contractRestartPage(@RequestBody ContractRestartListDTO contractRestartListDTO, @CurrentUser LoginUser loginUser){
        return Result.success(contractRestartService.contractRestartPage(contractRestartListDTO,loginUser));
    }
    @ApiOperation(value = "获取签署二维码链接")
    @PostMapping("/restart/QrCode")
    public ResponseEntity<byte[]> getRestartQrCode(@RequestParam String url){
        return contractRestartService.getRestartQrCode(url);
    }

    @ApiOperation(value = "获取补签合同签署链接")
    @PostMapping("/fdd/sign/url")
    ResponseEntity<byte[]> getRestartSignUrl(@RequestBody RestartUrlDTO restartUrlDTO) {
        return contractRestartService.getRestartSignUrl(restartUrlDTO);
    }

    @ApiOperation(value = "同步补签用户签署状态及完成文件")
    @PostMapping("/fdd/download/file")
    Result<Boolean> downloadSignedFile() {
        return Result.success(contractRestartService.downloadSignedFile());
    }

    @ApiOperation(value = "获取补签签署完成文件信息")
    @GetMapping("/file/detail")
    Result<List<ContractRestartFileInfoVO>> getFileDetail(@RequestParam("signId") Integer signId) {
        return Result.success(contractRestartService.getFileDetail(signId));
    }
    @ApiOperation(value = "导出补签数据到excel")
    @PostMapping("/export")
    public void exportExcel(@RequestBody ContractRestartListDTO contractRestartListDTO, @CurrentUser LoginUser loginUser, HttpServletResponse response){
        contractRestartService.exportExcel(contractRestartListDTO,loginUser, response);
    }
    @ApiOperation(value = "修改补签合同客户数据")
    @PostMapping("/update/customer")
    public Result<Boolean> updateCustomer(@RequestBody ContractRestartUpdateDTO contractRestartUpdateDTO, @CurrentUser LoginUser loginUser){
        return Result.success(contractRestartService.updateCustomer(contractRestartUpdateDTO,loginUser));
    }

    @ApiOperation(value = "合同补签同步签署信息")
    @GetMapping("/fdd/restart/file")
    Result<Boolean> restartDownloadSignedFile(@RequestParam("signTaskId") String signTaskId) {
        return Result.success(contractRestartService.restartDownloadSignedFile(signTaskId));
    }
}
