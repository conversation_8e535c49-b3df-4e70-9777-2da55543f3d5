package com.longhuan.order.settleCalculation.service.impl.strategy.impl.Annotation;

/**
 * SupportSettleType
 *
 * <AUTHOR>
 * @date 2025/6/15 下午9:59
 */

import com.longhuan.order.settleCalculation.service.impl.strategy.impl.enums.SettleType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * SupportSettleType
 *
 * <AUTHOR>
 * @date 2025/6/15 下午9:59
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface SupportSettleType {
    SettleType value();
}
