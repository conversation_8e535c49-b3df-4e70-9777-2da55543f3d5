package com.longhuan.order.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.OrderFileEntity;
import com.longhuan.order.pojo.entity.OrderIdCardUpdateEntity;
import com.longhuan.order.pojo.vo.*;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

public interface PostLoanSupplementReviewService {
    Page<PagePatchesVO> pagePatches(PagePatchesDTO dto, LoginUser currentUser);

    Boolean patchesSubmit(PatchesSubmitDTO dto, LoginUser currentUser);

    Page<PagePatchesVO> pagePatchesApproval(PagePatchesDTO dto, LoginUser currentUser);

    Boolean patchesApproval(PatchesApprovalDTO dto, LoginUser currentUser);

    AppointmentMortgageDetailVO patchesDetail(Integer patchesId);

    List<PatchworkProcessVO> patchworkProcess(PatchworkProcessDTO dto);

    List<EmployerProductListVO> employerProductList();

    void uploadFundFileTask();

    GetOrderIdVO getOrderId(String vin);

    GetManagerIdVO getManagerId(String vin);

    /**
     * 贷款用途证明上传列表
     */
    Page<PagePatchesVO> loanUploadList(PagePatchesDTO dto, LoginUser currentUser);


    Boolean loanUploadInfo(Integer orderId);


    Boolean idCardUpdateApply(Integer orderId);

    Boolean saveUpdateIdCardInfo(OrderIdCardUpdateEntity entity);

    String idCardUpdateQuery(Integer orderId);

    OrderIdCardUpdateEntity idCardQueryByOrderId(Integer orderId);

    void exportExcel(PagePatchesDTO pagePatchesDTO, LoginUser loginUser, HttpServletResponse response);

    String loanProveDownload(MenuFileDTO menuFileDTO);

    void exports(PagePatchesDTO pagePatchesDTO, LoginUser loginUser, HttpServletResponse response);

    void sendPatchesMessage();

}
