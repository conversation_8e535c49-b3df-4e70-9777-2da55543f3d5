package com.longhuan.order.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.order.mapper.OrderFeeInfoMapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.pojo.entity.OrderFeeInfoEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.service.RiskService;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

import static com.longhuan.order.statemachine.enums.States.CUSTOMER_APPOINTMENT;

/**
 * 手动回调接口
 *
 * <AUTHOR>
 * @date 2024/09/01
 */
@Api(tags = "手动回调接口")
@RestController
@Slf4j
@RequestMapping("/api/v1/manual/callback")
@RequiredArgsConstructor
public class ManualCallbackController {
    private final OrderInfoMapper orderInfoMapper;
    private final OrderFeeInfoMapper orderFeeInfoMapper;
    private final RiskService riskService;
    private final OrderStateService orderStateService;
    private final EnvUtil envUtil;

    private void sendFinishEvent(Integer orderId, Integer userId, Events event) {
        orderStateService.sendEvent(States.CUSTOMER_APPOINTMENT, event, orderId, userId);
    }

    /**
     * 增加状态机调用
     */
    @ApiOperation(value = "状态机")
    @GetMapping("/sendFinish")
    public void sendFinish(@RequestParam("states")States states,
                           @RequestParam("orderId")Integer orderId,
                           @RequestParam("userId") Integer userId,
                           @RequestParam("event")Events event) {
        orderStateService.sendEvent(states, event, orderId, userId);
    }
    /**
     * 次序
     *
     * @return {@link Result }<{@link String }>
     */
    @ApiOperation(value = "订单")
    @GetMapping("/order")
    public Result<String> order() {
        if (envUtil.isPrd()) {
            return Result.success("生产环境无法调用");
        }
        List<OrderInfoEntity> orderInfoList = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getState, CUSTOMER_APPOINTMENT.getNode())
        );
        for (OrderInfoEntity orderInfoEntity : orderInfoList) {
            try {
                Integer state = orderInfoEntity.getState();
                Integer orderId = orderInfoEntity.getId();
                if (Objects.equals(CUSTOMER_APPOINTMENT.getNode(), state)) {
//                    orderInfoEntity.setContractState(2);
//                    orderInfoMapper.updateById(orderInfoEntity);
//                    sendFinishEvent(orderId, 1, Events.CONTRACT_SIGNING_FINISH);
//                    orderInfoEntity.setMortgageState(2);
//                    orderInfoMapper.updateById(orderInfoEntity);
//                    sendFinishEvent(orderId, 1, Events.MORTGAGE_LOAN_FINISH);
                    orderInfoEntity.setGpsState(2);
                    orderInfoMapper.updateById(orderInfoEntity);

                    sendFinishEvent(orderId, 1, Events.GPS_INSTALL_FINISH);
//                    orderInfoEntity.setReviewState(2);
//                    orderInfoMapper.updateById(orderInfoEntity);
//                    sendFinishEvent(orderId, 1, Events.REVIEW_APPOINTMENT_FINISH);
                }
                List<OrderFeeInfoEntity> orderFeeInfoEntities = orderFeeInfoMapper.selectList(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                        .eq(OrderFeeInfoEntity::getOrderId, orderId)
                        .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                );
                if (!orderFeeInfoEntities.isEmpty()) {
                    OrderFeeInfoEntity orderFeeInfoEntity = orderFeeInfoEntities.get(0);
                    Assert.notNull(orderFeeInfoEntity, "订单" + orderId + "GPS费用信息不存在");
                    orderFeeInfoEntity.setGpsFeeStatus(2);
                    orderFeeInfoMapper.updateById(orderFeeInfoEntity);
                    sendFinishEvent(orderId, 1, Events.GPS_FEE_PAY_FINISH);
                }
            } catch (Exception e) {
                log.error("订单手动回调失败 e:", e);
            }
        }
        return Result.success();
    }


    /**
     * 预审发起风控
     *
     * @param preId
     * @return
     */
    @PostMapping("/manual/risk/launch")
    public Result<String> launch(@RequestParam @Validated Integer preId) {
        return Result.success(riskService.riskLaunchOnPreApproval(preId));
    }

    /**
     * 重新发起风控
     *
     * @param preId
     * @return
     */
    @PostMapping("/manual/risk/launchAgain")
    public Result<String> launchAgain(@RequestParam @Validated Integer preId) {
        return Result.success(riskService.riskLaunchOnPreApprovalAgain(preId));
    }

    /**
     * 预审发起规则调用
     *
     * @param preId
     * @return
     */
    @PostMapping("/manual/risk/engine")
    public Result<String> engine(@RequestParam @Validated Integer preId) {
        return Result.success(riskService.riskEngineOnPreApproval(preId));
    }


    /**
     * 预审发起规则调用
     *
     * @param preId 预审ID
     * @return {@link Result }<{@link String }>
     */
    @PostMapping("/manual/risk/result")
    public Result<String> riskResult(@RequestParam @Validated Integer preId) {
        return Result.success(riskService.updatePreApprovalRiskResult(preId));
    }
}
