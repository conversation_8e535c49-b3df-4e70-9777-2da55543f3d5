package com.longhuan.order.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.FundProductRongdanDetailDTO;
import com.longhuan.order.pojo.dto.RepaymentListDTO;
import com.longhuan.order.pojo.entity.FundProductRongdanDetailEntity;
import com.longhuan.order.pojo.vo.FundProductRongdanDetailVO;
import com.longhuan.order.pojo.vo.RepaymentInfoVO;
import com.longhuan.order.service.FundProductRongdanDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 资方 / 产品 - 关联表
 *
 * <AUTHOR> css
 * @date : 2025-05-16
 */
@Api(tags = "融担公司功能接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/FundProductRongdanDetail")
public class ProductRongDanDetailController {

    private final FundProductRongdanDetailService fundProductRongdanDetailService;


    /**
     * 列表
     *
     */
    @ApiOperation(value = "列表")
    @PostMapping("/list")
    public Result<Page<FundProductRongdanDetailDTO>> list(@RequestBody FundProductRongdanDetailVO fundProductRongdanDetailVO) {
        return Result.success(fundProductRongdanDetailService.selectFundProductRongdanDetailList(fundProductRongdanDetailVO));
    }

    /**
     * 详情
     *
     */
    @ApiOperation(value = "详情")
    @PostMapping("/info")
    public Result<FundProductRongdanDetailEntity> info(@RequestParam Integer id) {
        return Result.success(fundProductRongdanDetailService.selectFundProductRongdanDetailById(id));
    }

    /**
     * 新增
     */
    @ApiOperation(value = "新增")
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody FundProductRongdanDetailVO fundProductRongdanDetailVO) {
        return Result.success(fundProductRongdanDetailService.addFundProductRongdanDetailList(fundProductRongdanDetailVO));
    }

}
