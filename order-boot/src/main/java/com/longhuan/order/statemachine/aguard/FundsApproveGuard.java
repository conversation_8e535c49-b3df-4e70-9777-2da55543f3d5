package com.longhuan.order.statemachine.aguard;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.guard.Guard;

/**
 * 状态保护机制，对一个状态转移进行评估，评估值为true允许状态转移，评估值为false禁止转移
 */
@Slf4j
//@Component
@RequiredArgsConstructor
public class FundsApproveGuard implements Guard<States, Events> {
    private final boolean match;
    private final OrderInfoMapper orderInfoMapper;

    @Override
    public boolean evaluate(StateContext<States, Events> context) {
        Integer orderId = context.getMessageHeaders().get(StateConstants.ORDER_ID, Integer.class);

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId, OrderInfoEntity::getManagementConclusion)
                .eq(OrderInfoEntity::getId, orderId));

        Integer state = orderInfoEntity.getManagementConclusion();
        log.info("orderId:{},FundsApprove state:{}", orderId, state);
        boolean b = Convert.toInt(state, 0) == 1;
        return b == match;
    }
}