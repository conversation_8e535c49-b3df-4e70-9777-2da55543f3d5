package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.common.core.enums.RoleEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.OnlineUserListDTO;
import com.longhuan.order.pojo.dto.OrderDistributionDTO;
import com.longhuan.order.pojo.dto.UserRiskChangeDTO;
import com.longhuan.order.pojo.dto.UserRiskListDTO;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.service.*;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.user.enums.TodoInfoEnums;
import com.longhuan.user.pojo.dto.TodoInfoMessageDTO;
import com.longhuan.user.pojo.dto.UserStoreDTO;
import com.longhuan.user.pojo.vo.UserAndDeptUsersVO;
import com.longhuan.user.pojo.vo.UserDetailInfoVO;
import com.longhuan.user.pojo.vo.UserInfoVO;
import com.longhuan.user.pojo.vo.UserStoreVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 风控人员状态变更订单分配
 *
 * <AUTHOR>
 * @date 2024/08/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserOnlineDistributionServiceImpl implements UserOnlineDistributionService {
    private final RiskStateService riskStateService;
    private final OrderService orderService;
    private final ApprovalService approvalService;
    private final UserFeign userFeign;
    private final RiskStateMapper riskStateMapper;
    private final OrderApproveDistributeService orderApproveDistributeService;
    private final DistributeAreaMapper distributeAreaMapper;
    private final UserOnlineDistributionTimeMapper userOnlineDistributionTimeMapper;
    private final OrderApproveDistributeMapper orderApproveDistributeMapper;
    private final EnvUtil envUtil;
    private final UserApproveQueueService userApproveQueueService;
    private final OrderDistributeLogsMapper orderDistributeLogsMapper;

    @Override
    public Boolean changeRiskStatus(UserRiskChangeDTO userRiskChangeDTO, LoginUser loginUser) {
//        if (ObjUtil.equals(userRiskChangeDTO.getState(), 0)) {
//            riskStateMapper.update(new LambdaUpdateWrapper<RiskUserStateEntity>()
//                    .set(RiskUserStateEntity::getUpdateTime, LocalDateTime.now())
//                    .eq(RiskUserStateEntity::getUserId, loginUser.getUserId())
//                    .eq(RiskUserStateEntity::getDeleteFlag, 0));
//            return true;
//        }
        Integer state = userRiskChangeDTO.getState();
        if (ObjUtil.equals(userRiskChangeDTO.getState(), 4)) {
            userRiskChangeDTO.setState(1);
        }
        RiskUserStateEntity riskUserStateEntity = riskStateMapper.selectOne(new LambdaQueryWrapper<RiskUserStateEntity>()
                .eq(RiskUserStateEntity::getUserId, loginUser.getUserId())
                .eq(RiskUserStateEntity::getDeleteFlag, 0)
                .orderByDesc(RiskUserStateEntity::getCreateTime)
                .last("limit 1"));

        Assert.notNull(riskUserStateEntity, "当前用户无权限操作在线状态");

        if (ObjUtil.equals(userRiskChangeDTO.getState(), riskUserStateEntity.getState()) && ObjUtil.notEqual(userRiskChangeDTO.getState(), 3)) {
            return true;
        }

        // 处理客服上下线
        Integer userType = Convert.toInt(userRiskChangeDTO.getUserType(), 1);
        if (userType == 2) {
            Assert.notNull(riskUserStateEntity, "未查询到当前客服人员");
            if (ObjUtil.equals(userRiskChangeDTO.getState(), 1) || ObjUtil.equals(userRiskChangeDTO.getState(), 3)) {
                UserStoreVO userStoreVO = userFeign.searchUserStoreBatch(List.of(riskUserStateEntity.getUserId())).getData().get(0);
                DistributeAreaEntity distributeAreaEntity = distributeAreaMapper.selectOne(new LambdaQueryWrapper<DistributeAreaEntity>()
                        .eq(DistributeAreaEntity::getDeleteFlag, 0)
                        .in(DistributeAreaEntity::getStoreId, userStoreVO.getStoreId()), false);
                if (ObjUtil.isNull(distributeAreaEntity) || ObjUtil.equals(distributeAreaEntity.getServiceDispatch(), 0)) {
                    throw new BusinessException("该客服门店未开启客服派单功能，无法上线或者小憩");
                }
            }
            if (userRiskChangeDTO.getState() == 3) {
                startTimer(userRiskChangeDTO.getSleepTime(), riskUserStateEntity.getId(), loginUser.getUserId());
            }
            riskStateService.update(new LambdaUpdateWrapper<>(RiskUserStateEntity.class)
                    .set(RiskUserStateEntity::getState, userRiskChangeDTO.getState())
                    .set(RiskUserStateEntity::getSleepTime, ObjUtil.isNotNull(userRiskChangeDTO.getSleepTime()) && ObjUtil.equals(userRiskChangeDTO.getState(), 3) ? LocalDateTime.now().plusMinutes(userRiskChangeDTO.getSleepTime()) : null)
                    .set(RiskUserStateEntity::getUpdateTime, LocalDateTime.now())
                    .eq(RiskUserStateEntity::getUserId, loginUser.getUserId()));
            updateTheOnlineAndOfflineTimes(state, loginUser.getUserId());
            return true;
        }


        // 处理风控人员上下线
        Integer userId = loginUser.getUserId();
        List<Integer> roleIds = loginUser.getRoleIds();
        if (!RoleEnum.RISK_AMOUNT_APPROVE.hasRole(roleIds)&&!RoleEnum.RISK_AMOUNT_APPROVE_ONLINE.hasRole(loginUser.getRoleIds())) {
            throw new RuntimeException("暂无此权限");
        }
        boolean b = riskStateService.update(new LambdaUpdateWrapper<RiskUserStateEntity>()
                .eq(RiskUserStateEntity::getUserId, userId)
                .set(RiskUserStateEntity::getState, userRiskChangeDTO.getState())
                .set(RiskUserStateEntity::getUpdateTime, LocalDateTime.now())
                .set(ObjUtil.notEqual(userRiskChangeDTO.getState(), 3), RiskUserStateEntity::getSleepTime, null));

        if (Objects.equals(userType, 1)) {
            // 若更新状态为上线则按顺序排队派单
            if (b && userRiskChangeDTO.getState() == 1) {
                userApproveQueueService.addUserToQueue(userId);
//            Integer orderId = orderApproveDistributeService.orderDistributionManual(userId);
            } else if (b && userRiskChangeDTO.getState() == 2) {
                userApproveQueueService.removeUserFromQueue(userId);
            }
        }

        updateTheOnlineAndOfflineTimes(state, userId);
        return b;
    }

    private void updateTheOnlineAndOfflineTimes(Integer state, Integer userId) {
        //存入用户在线派单时间表
        if (ObjUtil.equals(state, 1) || ObjUtil.equals(state, 4)) {
            UserOnlineDistributionTimeEntity oldSleepTime = userOnlineDistributionTimeMapper.selectOne(new LambdaQueryWrapper<UserOnlineDistributionTimeEntity>()
                    .eq(UserOnlineDistributionTimeEntity::getUserId, userId)
                    .eq(UserOnlineDistributionTimeEntity::getStatus, 3)
                    .isNull(UserOnlineDistributionTimeEntity::getDownTime)
                    .orderByDesc(UserOnlineDistributionTimeEntity::getCreateTime), false);
            if (ObjUtil.isNotNull(oldSleepTime)) {
                LocalDateTime now = LocalDateTime.now();
                long secondsDifference = Duration.between(oldSleepTime.getUpTime(), now).getSeconds();
                oldSleepTime.setDownTime(now).setChangeTime(secondsDifference);
                userOnlineDistributionTimeMapper.updateById(oldSleepTime);
            } else {
                userOnlineDistributionTimeMapper.insert(new UserOnlineDistributionTimeEntity()
                        .setStatus(state)
                        .setUserId(userId)
                        .setUpTime(LocalDateTime.now()));
            }
        }
        if (ObjUtil.equals(state, 2)) {
            UserOnlineDistributionTimeEntity oldSleepTime = userOnlineDistributionTimeMapper.selectOne(new LambdaQueryWrapper<UserOnlineDistributionTimeEntity>()
                    .eq(UserOnlineDistributionTimeEntity::getUserId, userId)
                    .in(UserOnlineDistributionTimeEntity::getStatus, 1, 4)
                    .isNull(UserOnlineDistributionTimeEntity::getDownTime)
                    .orderByDesc(UserOnlineDistributionTimeEntity::getCreateTime), false);
            if (ObjUtil.isNotNull(oldSleepTime)) {
                LocalDateTime now = LocalDateTime.now();
                long secondsDifference = Duration.between(oldSleepTime.getUpTime(), now).getSeconds();
                oldSleepTime.setDownTime(now).setChangeTime(secondsDifference);
                userOnlineDistributionTimeMapper.updateById(oldSleepTime);
            }
            oldSleepTime = userOnlineDistributionTimeMapper.selectOne(new LambdaQueryWrapper<UserOnlineDistributionTimeEntity>()
                    .eq(UserOnlineDistributionTimeEntity::getUserId, userId)
                    .eq(UserOnlineDistributionTimeEntity::getStatus, 3)
                    .isNull(UserOnlineDistributionTimeEntity::getDownTime)
                    .orderByDesc(UserOnlineDistributionTimeEntity::getCreateTime), false);
            if (ObjUtil.isNotNull(oldSleepTime)) {
                LocalDateTime now = LocalDateTime.now();
                long secondsDifference = Duration.between(oldSleepTime.getUpTime(), now).getSeconds();
                oldSleepTime.setDownTime(now).setChangeTime(secondsDifference);
                userOnlineDistributionTimeMapper.updateById(oldSleepTime);
            }
        }
    }

    public void startTimer(Integer sleepTime, Integer id, Integer userId) {
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        {
            UserOnlineDistributionTimeEntity oldSleepTime = userOnlineDistributionTimeMapper.selectOne(new LambdaQueryWrapper<UserOnlineDistributionTimeEntity>()
                    .eq(UserOnlineDistributionTimeEntity::getUserId, userId)
                    .eq(UserOnlineDistributionTimeEntity::getStatus, 3)
                    .isNull(UserOnlineDistributionTimeEntity::getDownTime)
                    .orderByDesc(UserOnlineDistributionTimeEntity::getCreateTime), false);
            if (ObjUtil.isNull(oldSleepTime)) {
                userOnlineDistributionTimeMapper.insert(new UserOnlineDistributionTimeEntity()
                        .setStatus(3)
                        .setUserId(userId)
                        .setUpTime(LocalDateTime.now()));
            }
        }
        scheduler.schedule(() -> {
            RiskUserStateEntity riskUserStateEntity = riskStateService.getById(id);
            if ((ObjUtil.isNotNull(riskUserStateEntity.getSleepTime()) && riskUserStateEntity.getSleepTime().plusSeconds(5).isAfter(LocalDateTime.now())) || ObjUtil.isNull(riskUserStateEntity.getSleepTime())) {
                if (ObjUtil.equals(riskUserStateEntity.getState(), 3)) {
                    riskStateService.update(new LambdaUpdateWrapper<RiskUserStateEntity>()
                            .set(RiskUserStateEntity::getState, 1)
                            .set(RiskUserStateEntity::getUpdateTime, LocalDateTime.now())
                            .set(RiskUserStateEntity::getSleepTime, null)
                            .eq(RiskUserStateEntity::getId, userId));
                    updateTheOnlineAndOfflineTimes(4, userId);
                }

            }
            scheduler.shutdown();
        }, sleepTime, TimeUnit.MINUTES);
    }

    @Override
    public RiskUserStateEntity selectUserRiskById(LoginUser loginUser) {
        Integer userId = loginUser.getUserId();
        List<Integer> roleIds = loginUser.getRoleIds();
        Integer userType = null;
        if (RoleEnum.RISK_AMOUNT_APPROVE.hasRole(roleIds)
                ||RoleEnum.RISK_AMOUNT_APPROVE_ONLINE.hasRole(roleIds)) {
            userType = 1;
        }else if (RoleEnum.CUSTOMER_SERVICE_SPECIALIST.hasRole(roleIds)) {
            userType = 2;
        } else {
            return new RiskUserStateEntity().setState(2);
        }
        LambdaQueryWrapper<RiskUserStateEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RiskUserStateEntity::getUserId, userId)
                .eq(RiskUserStateEntity::getUserType, userType)
                .eq(RiskUserStateEntity::getDeleteFlag, 0);
        RiskUserStateEntity riskUserStateEntity = riskStateService.getOne(wrapper);
        if (riskUserStateEntity == null) {
            RiskUserStateEntity riskUserStateEntityNew = new RiskUserStateEntity();
            riskUserStateEntityNew.setUserId(userId);
            riskUserStateEntityNew.setState(1);
            riskUserStateEntityNew.setUserType(userType);
            if (userType == 2) {
                List<UserStoreVO> userInfoVOList = userFeign.searchUserStoreBatch(List.of(userId)).getData();
                if (CollUtil.isEmpty(userInfoVOList) || ObjUtil.isEmpty(userInfoVOList.get(0).getStoreId())) {
                    riskUserStateEntityNew.setState(2);
                }
                DistributeAreaEntity distributeAreaEntity = distributeAreaMapper.selectOne(new LambdaQueryWrapper<DistributeAreaEntity>()
                        .eq(DistributeAreaEntity::getDeleteFlag, 0)
                        .eq(DistributeAreaEntity::getStoreId, userInfoVOList.get(0).getStoreId())
                        .orderByDesc(DistributeAreaEntity::getCreateTime), false);
                if (ObjUtil.isNull(distributeAreaEntity) || ObjUtil.equals(distributeAreaEntity.getServiceDispatch(), 0)) {
                    riskUserStateEntityNew.setState(2);
                }
            }
            riskStateService.save(riskUserStateEntityNew);
//            Integer orderId = orderApproveDistributeService.orderDistributionManual(userId);
//            if (orderId != null) {
//                approvalService.riskOrderAssign(orderId, userId);
//            }
        }
        return riskStateService.getOne(wrapper);
    }

    @Override
    public List<UserInfoVO> selectUserRiskListById(UserRiskListDTO userRiskListDTO) {
        log.info("UserOnlineDistributionServiceImpl.selectUserRiskListById userRiskListDTO：{}", userRiskListDTO);
        Integer riskUserType = userRiskListDTO.getRiskUserType();
        if (riskUserType == 1) {
            // 在线人员ID
            List<Integer> userIdList = new ArrayList<>(riskStateService.selectOnlineUser(1, List.of(1, 3)).stream()
                    .map(RiskUserStateEntity::getUserId).toList());
            //小憩人员ID
            List<Integer> sleepUserIdList = riskStateService.selectOnlineUser(3, List.of(1,3)).stream()
                    .map(RiskUserStateEntity::getUserId).toList();
            if (CollUtil.isNotEmpty(sleepUserIdList)){
                userIdList.addAll(sleepUserIdList);
            }
            Result<List<Integer>> listResult = userFeign.searchUserByRoleId(RoleEnum.RISK_AMOUNT_APPROVE.getId());

            if (!Result.isSuccess(listResult) || listResult.getData() == null) {
                log.error("未获取到风控人员信息");
                throw new BusinessException("未获取到风控人员信息");
            }
            listResult.getData().addAll(userFeign.searchUserByRoleId(RoleEnum.RISK_AMOUNT_APPROVE_ONLINE.getId()).getData());

            //获取所有风控人员ID
            List<Integer> integerList = listResult.getData();
            log.info("UserOnlineDistributionServiceImpl.selectUserRiskById integerList：{}", integerList);

            //取交集
            List<Integer> riskUserIdList = userIdList.stream()
                    .filter(integerList::contains).toList();
            log.info("UserOnlineDistributionServiceImpl.selectUserRiskById riskUserIdList：{}", riskUserIdList);

            Result<List<UserInfoVO>> listResult1 = userFeign.searchUserNameBatch(riskUserIdList);
            if (!Result.isSuccess(listResult1) && listResult1.getData() == null) {
                log.error("未获取到风控人员详情信息");
                throw new BusinessException("未获取到风控人员详情信息");
            }
            return listResult1.getData();
        }
        Result<List<Integer>> listResult = userFeign.searchUserByRoleId(RoleEnum.RISK_AMOUNT_APPROVE_FINAL.getId());
        if (!Result.isSuccess(listResult) || listResult.getData() == null) {
            log.error("未获取到最终审核人员信息");
            throw new BusinessException("未获取到最终审核人员信息");
        }
        List<Integer> integerList = listResult.getData();
        log.info("UserOnlineDistributionServiceImpl.selectUserRiskById integerList：{}", integerList);
        Result<List<UserInfoVO>> listResult1 = userFeign.searchUserNameBatch(integerList);
        log.info("UserOnlineDistributionServiceImpl.selectUserRiskById listResult1：{}", listResult1);
        if (!Result.isSuccess(listResult1) || listResult1.getData() == null) {
            log.error("未获取到最终审核人员详情信息");
            throw new BusinessException("未获取到最终审核人员详情信息");
        }
        return listResult1.getData();
    }

    @Override
    public Boolean orderDistributionManual(OrderDistributionDTO orderDistributionDTO, LoginUser loginUser) {
        log.info("UserOnlineDistributionServiceImpl.orderDistributionManual orderDistributionDTO：{}", orderDistributionDTO);
        Integer orderId = orderDistributionDTO.getOrderId();
        Integer newRiskUserId = orderDistributionDTO.getUserId();
        if (ObjUtil.isNull(orderDistributionDTO.getNode()) || ObjUtil.equals(orderDistributionDTO.getNode(), States.RISK_FIRST_APPROVE.getNode())) {
            orderDistributionDTO.setNode(States.RISK_FIRST_APPROVE_ASSIGN.getNode());
        }
        if (ObjUtil.isNull(orderDistributionDTO.getOrderNumber())) {
            OrderInfoEntity orderInfoEntity = orderService.getOne(new LambdaQueryWrapper<OrderInfoEntity>()
                    .eq(OrderInfoEntity::getId, orderDistributionDTO.getOrderId())
                    .eq(OrderInfoEntity::getDeleteFlag, 0), false);
            orderDistributionDTO.setOrderNumber(orderInfoEntity.getOrderNumber());
        }
        OrderApproveDistributeEntity orderApproveDistributeEntity = orderApproveDistributeService.getOne(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                .eq(OrderApproveDistributeEntity::getOrderNumber, orderDistributionDTO.getOrderNumber())
                .eq(OrderApproveDistributeEntity::getNode, orderDistributionDTO.getNode())
                .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                .orderByDesc(OrderApproveDistributeEntity::getCreateTime), false);
        if (orderApproveDistributeEntity == null) {
            throw new BusinessException("派单数据错误不支持分配订单");
        }
        Integer oldUserId = orderApproveDistributeEntity.getUserId();



        log.info("UserOnlineDistributionServiceImpl.orderDistributionManual orderApproveDistributeEntity：{}", orderApproveDistributeEntity);
        Integer node = orderDistributionDTO.getNode();
        String nodeName = States.getByNode(node).getDesc();
        if (ObjUtil.equals(node, States.RISK_FIRST_APPROVE_ASSIGN.getNode()) || ObjUtil.equals(node, States.RISK_FIRST_APPROVE.getNode())) {
            nodeName = States.RISK_FIRST_APPROVE.getDesc();
        } else if (!customerServiceDispatchAssignmentAuthority(loginUser)) {
            throw new BusinessException("您没有权限进行该操作");
        }
        try {
            TodoInfoMessageDTO todoInfoMessageDTO = new TodoInfoMessageDTO()
                    .setSourceType(orderApproveDistributeEntity.getSource())
                    .setState(3)
                    .setNode(TodoInfoEnums.getByNode(orderDistributionDTO.getNode()))
                    .setAccessUrl(null);
            todoInfoMessageDTO
                    .setOrderNumber(orderDistributionDTO.getOrderNumber())
                    .setOrderId(orderId)
                    .setNode(TodoInfoEnums.getByNode(orderDistributionDTO.getNode()));
            userFeign.dealMessage(todoInfoMessageDTO);
        } catch (Exception e) {
            log.error("UserOnlineDistributionServiceImpl.orderDistributionManual todoInfoMessageDTO：{}", e.getMessage());
        }
        // 1. 将历史的分配记录置为失效
        boolean update = orderApproveDistributeService.update(new LambdaUpdateWrapper<OrderApproveDistributeEntity>()
                .set(OrderApproveDistributeEntity::getState, 4)
                .eq(OrderApproveDistributeEntity::getOrderNumber, orderDistributionDTO.getOrderNumber())
                .eq(OrderApproveDistributeEntity::getNode, node)
                .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                .in(OrderApproveDistributeEntity::getState, 0, 1, 2, 5)
                .eq(OrderApproveDistributeEntity::getSource, orderApproveDistributeEntity.getSource())
        );
        log.info("UserOnlineDistributionServiceImpl.orderDistributionManual history update：{}", update);

        // 2. 新增新的分配记录
        OrderApproveDistributeEntity newOrderApproveDistributeEntity = new OrderApproveDistributeEntity()
                .setOrderNumber(orderApproveDistributeEntity.getOrderNumber())
                .setOrderId(orderId)
                .setNode(node)
                .setNodeName(nodeName)
                .setSource(orderApproveDistributeEntity.getSource())
                .setStoreId(orderApproveDistributeEntity.getStoreId())
                .setTitle(orderApproveDistributeEntity.getCustomerName() + "-" + orderApproveDistributeEntity.getVehicleNumber() + "-" +
                        orderApproveDistributeEntity.getStoreName() + "-人工派单")
                .setSignType(orderApproveDistributeEntity.getSignType())
                .setState(0);
        newOrderApproveDistributeEntity.setCustomerName(orderApproveDistributeEntity.getCustomerName())
                .setCustomerPhone(orderApproveDistributeEntity.getCustomerPhone())
                .setManagerId(orderApproveDistributeEntity.getManagerId())
                .setStoreName(orderApproveDistributeEntity.getStoreName())
                .setRegionName(orderApproveDistributeEntity.getRegionName())
                .setVehicleNumber(orderApproveDistributeEntity.getVehicleNumber())
                .setStepDispose(orderApproveDistributeEntity.getStepDispose());
        orderApproveDistributeMapper.insert(newOrderApproveDistributeEntity);
        // 新人从旧队列删除
        userApproveQueueService.removeUserFromQueue(newRiskUserId);
        if (ObjUtil.isNotNull(oldUserId)){
            UserDetailInfoVO userDetailInfoVOResult = userFeign.searchUserDetailById(oldUserId).getData();
            if (ObjUtil.isNotNull(userDetailInfoVOResult)&&RoleEnum.RISK_AMOUNT_APPROVE.hasRole(userDetailInfoVOResult.getRoleIds())){
                // 旧人加入队列
                userApproveQueueService.addUserToQueue(oldUserId);
            }
        }
        // 3.分配逻辑
        orderApproveDistributeService.updateApproveUserId(newRiskUserId, newOrderApproveDistributeEntity);

        insertDistributeLogs(newRiskUserId,newOrderApproveDistributeEntity);

        // 4.更新订单分配人
        if (ObjUtil.equals(node, States.RISK_FIRST_APPROVE_ASSIGN.getNode())) {

            OrderInfoEntity orderInfo = new OrderInfoEntity();
            orderInfo.setId(orderId);
            orderInfo.setRiskUserId(newRiskUserId);
            return orderService.updateById(orderInfo);
        }
        return true;
    }

    @Override
    public Boolean checkAreaStatus(LoginUser loginUser) {
        if (RoleEnum.RISK_AMOUNT_APPROVE.hasRole(loginUser.getRoleIds())||RoleEnum.RISK_AMOUNT_APPROVE_ONLINE.hasRole(loginUser.getRoleIds())) {
            return true;
        }
        List<UserStoreVO> userStoreVOS = userFeign.searchUserStoreBatch(List.of(loginUser.getUserId())).getData();
        if (CollUtil.isEmpty(userStoreVOS) || ObjUtil.isEmpty(userStoreVOS.get(0).getStoreId())) {
            return false;
        }
        DistributeAreaEntity distributeAreaEntity = distributeAreaMapper.selectOne(new LambdaQueryWrapper<DistributeAreaEntity>()
                .eq(DistributeAreaEntity::getDeleteFlag, 0)
                .eq(DistributeAreaEntity::getStoreId, userStoreVOS.get(0).getStoreId())
                .orderByDesc(DistributeAreaEntity::getCreateTime), false);
        log.info("UserOnlineDistributionServiceImpl.changeAreaStatus distributeAreaEntity：{}", distributeAreaEntity);
        if (ObjUtil.isNull(distributeAreaEntity) || ObjUtil.equals(distributeAreaEntity.getServiceDispatch(), 0)) {
            return false;
        }
        return true;
    }

    @Override
    public void theCustomerServiceIsAutomaticallyOffline() {
        LocalDateTime now = LocalDateTime.now();
        List<RiskUserStateEntity> riskUserStateEntities = new ArrayList<>();
//        if (now.getHour() >= 22) {
//            riskUserStateEntities = riskStateMapper.selectList(new LambdaQueryWrapper<RiskUserStateEntity>()
//                    .eq(RiskUserStateEntity::getDeleteFlag, 0)
//                    .eq(RiskUserStateEntity::getUserType, 2)
//                    .ne(RiskUserStateEntity::getState, 2));
//        } else {
            riskUserStateEntities = riskStateMapper.selectList(new LambdaQueryWrapper<RiskUserStateEntity>()
                    .eq(RiskUserStateEntity::getDeleteFlag, 0)
                    .eq(RiskUserStateEntity::getUserType, 2)
                    .ne(RiskUserStateEntity::getState, 2)
                    .and(w -> w.isNull(RiskUserStateEntity::getLastOperationTime)
                            .or().le(RiskUserStateEntity::getLastOperationTime, now.minusMinutes(30))));
//        }
        riskUserStateEntities.forEach(riskUserStateEntity -> {
            Boolean changeRiskStatus = changeRiskStatus(new UserRiskChangeDTO().setState(2).setUserType(2), new LoginUser().setUserId(riskUserStateEntity.getUserId()));
            if (!changeRiskStatus) {
                log.error("theCustomerServiceIsAutomaticallyOffline changeRiskStatus error: userid{}", riskUserStateEntity.getUserId());
            }
        });
    }

    @Override
    public Page<UserStoreVO> selectOnlineUserList(OnlineUserListDTO onlineUserListDTO, LoginUser loginUser) {
        List<Integer> userIds = new ArrayList<>();
        if (StrUtil.isNotBlank(onlineUserListDTO.getName())) {
            List<Integer> userIdByLikeNameList = userFeign.getUserIdByLikeNameList(onlineUserListDTO.getName()).getData();
            if (!CollUtil.isEmpty(userIdByLikeNameList)) {
                userIds.addAll(userIdByLikeNameList);
            }
        }
        if (StrUtil.isNotBlank(onlineUserListDTO.getStoreName())) {
            List<Integer> deptIds = userFeign.getDeptIdByLikeName(onlineUserListDTO.getStoreName()).getData();
            if (!CollUtil.isEmpty(deptIds)) {
                List<Integer> deptUsers = userFeign.getDeptUsers(deptIds).getData();
                if (!CollUtil.isEmpty(deptUsers)) {
                    userIds.addAll(deptUsers);
                }
            }
        }
        if ((StrUtil.isNotBlank(onlineUserListDTO.getName()) || StrUtil.isNotBlank(onlineUserListDTO.getStoreName())) && CollUtil.isEmpty(userIds)) {
            return new Page<>();
        }
        Page<UserStoreVO> riskUserStateEntityPage = riskStateMapper.selectJoinPage(new Page<>(onlineUserListDTO.getPageNum(), onlineUserListDTO.getPageSize()),
                UserStoreVO.class,
                new MPJLambdaWrapper<>(RiskUserStateEntity.class)
                        .selectAs(RiskUserStateEntity::getUserId, UserStoreVO::getUserId)
                        .eq(RiskUserStateEntity::getDeleteFlag, 0)
                        .eq(RiskUserStateEntity::getUserType, 2)
                        .eq(RiskUserStateEntity::getState, 1)
                        .in(!CollUtil.isEmpty(userIds), RiskUserStateEntity::getUserId, userIds));
        if (!CollUtil.isEmpty(riskUserStateEntityPage.getRecords())) {
            userIds = riskUserStateEntityPage.getRecords().stream().map(UserStoreVO::getUserId).toList();
            List<UserStoreVO> userInfoVOS = userFeign.searchUserStoreBatch(userIds).getData();
            Map<Integer, UserStoreVO> userStoreVOMap = userInfoVOS.stream().collect(Collectors.toMap(UserStoreVO::getUserId, Function.identity(), (e1, e2) -> e1));
            if (!CollUtil.isEmpty(userInfoVOS)) {
                if (!CollUtil.isEmpty(userInfoVOS)) {
                    riskUserStateEntityPage.getRecords().replaceAll(userStoreVO ->
                            userStoreVOMap.getOrDefault(userStoreVO.getUserId(), userStoreVO)
                    );
                }
            }
        }
        return riskUserStateEntityPage;
    }

    @Override
    public Boolean customerServiceDispatchAssignmentAuthority(LoginUser loginUser) {
        Assert.notNull(loginUser, () -> new BusinessException("登录用户信息为空"));
        List<Integer> roleIds = loginUser.getRoleIds();
        if (RoleEnum.SYS_ADMIN.hasRole(roleIds)
                || RoleEnum.BIZ_SUPER_ADMIN.hasRole(roleIds)) {
            return true;
        }
        if (RoleEnum.BUSINESS_SUPPORT_SPECIALIST.hasRole(roleIds)
                || RoleEnum.OPERATION_SPECIALIST.hasRole(roleIds)
                || RoleEnum.QUALITY_MANAGER.hasRole(roleIds)
                || RoleEnum.OPERATION_MANAGER.hasRole(roleIds)
                || RoleEnum.RISK_ASSIGN_ORDER.hasRole(roleIds)) {
            return true;
        }
        if (!envUtil.isPrd()) {
            if (ObjUtil.equals(loginUser.getUserId(), 11203)) {
                return true;
            }
        }
        return ObjUtil.equals(loginUser.getUserId(), 10319)
                || ObjUtil.equals(loginUser.getUserId(), 1905);
    }

    private void insertDistributeLogs(Integer newRiskUserId, OrderApproveDistributeEntity newOrderApproveDistributeEntity) {
        OrderDistributeLogsEntity logsEntity = new OrderDistributeLogsEntity();
        BeanUtils.copyProperties(newOrderApproveDistributeEntity, logsEntity);
        UserAndDeptUsersVO userAndDeptUsersVO = userFeign.selectUsersStore(new UserStoreDTO().setUserId(newRiskUserId)).getData();
        logsEntity.setUserId(newRiskUserId)
                .setDistributeTime(LocalDateTime.now())
                .setDispatchStoreName(userAndDeptUsersVO.getStore())
                .setDispatchRegionName(userAndDeptUsersVO.getArea());
        orderDistributeLogsMapper.insert(logsEntity);
    }
}
