@startuml
'https://plantuml.com/use-case-diagram
hide empty description
state "流程终止" as process_terminal
state "待业务补录" as waiting_for_added_business {
state "补录进件信息" as added_business{
state "基本信息" as added_business_basic
state "产品信息" as added_business_product
[*] -> added_business_basic
added_business_basic -> added_business_product
added_business_product -> [*]

}

}

state "客服质检" as quality_inspection
state "待初审" as risk_first_approve
state "待终审" as risk_final_approve
state "资方终审" as funds_final_approve
state "客户确认" as customer_confirm
state "客户预约" as customer_appointment
state "客户签约" as customer_sign {
state fork1 <<fork>>
state join1 <<join>>

fork1 --> manager_interview
fork1 --> gps_install
fork1 --> contract_signing
fork1 --> mortgage

manager_interview --> join1
gps_install --> join1
contract_signing --> join1
mortgage --> join1
}

state "面签" as manager_interview
state "安装GPS" as gps_install
state "合同签约" as contract_signing

state "抵押" as mortgage
state "放款申请" as payment_apply
state "放款审批" as payment_approval
state "资方放款" as funds_payment
state "放款成功" as payment_success

[*] --> waiting_for_added_business
state  waiting_for_added_business_choice <<choice>> :业务补录状态
waiting_for_added_business --> waiting_for_added_business_choice
waiting_for_added_business_choice --> quality_inspection : 提交

state quality_inspection_choice <<choice>>

quality_inspection --> quality_inspection_choice
quality_inspection_choice --> waiting_for_added_business : 驳回
quality_inspection_choice --> risk_first_approve : 通过


state risk_first_approve_choice <<choice>>
risk_first_approve --> risk_first_approve_choice
risk_first_approve_choice -> process_terminal : 拒绝
risk_first_approve_choice --> waiting_for_added_business : 驳回
risk_first_approve_choice --> risk_final_approve : 通过

state risk_final_approve_choice <<choice>>
risk_final_approve --> risk_final_approve_choice
risk_final_approve_choice -> process_terminal : 拒绝
risk_final_approve_choice --> risk_first_approve : 驳回
risk_final_approve_choice --> funds_final_approve : 通过

state funds_final_approve_choice <<choice>>
funds_final_approve --> funds_final_approve_choice
funds_final_approve_choice -> process_terminal : 拒绝
funds_final_approve_choice --> customer_confirm : 通过



customer_confirm --> customer_appointment

customer_appointment --> customer_sign

join1 --> payment_apply

state payment_apply_choice <<choice>>
payment_apply --> payment_apply_choice

payment_apply_choice --> fork1 : 驳回
payment_apply_choice --> payment_approval : 通过


state payment_approval_choice <<choice>>
payment_approval --> payment_approval_choice

payment_approval_choice -> payment_apply : 驳回
payment_approval_choice --> funds_payment : 通过

state payment_status_choice <<choice>>
funds_payment --> payment_status_choice
payment_status_choice -> funds_payment : 失败
payment_status_choice --> payment_success : 成功

payment_success-->[*]
@enduml