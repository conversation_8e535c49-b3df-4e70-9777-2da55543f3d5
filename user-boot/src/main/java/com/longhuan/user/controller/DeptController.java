package com.longhuan.user.controller;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.user.pojo.dto.*;
import com.longhuan.user.pojo.vo.DeptSyncInfoVO;
import com.longhuan.user.pojo.vo.DisposalCompanyVO;
import com.longhuan.user.pojo.vo.RegionInfoVO;
import com.longhuan.user.pojo.vo.StoreInfoVO;
import com.longhuan.user.pojo.entity.DeptEntity;
import com.longhuan.user.pojo.entity.DisposalUpdateFieldRecordsEntity;
import com.longhuan.user.pojo.vo.*;
import com.longhuan.user.service.DeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 部门管理
 *
 * <AUTHOR>
 * @date 2024/07/17
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Api("部门管理")
@RequestMapping("/dept")
public class DeptController {
    private final DeptService deptService;

    /**
     * 部门列表
     *
     * @return 实例对象
     */
    @PostMapping("/getDeptList")
    public Result<List<Tree<Integer>>> selectPage() {
        return Result.success(deptService.getDeptList());
    }

    /**
     * 部门列表
     *
     * @return 实例对象
     */
    @PostMapping("/getRegionList")
    public Result<List<Tree<Integer>>> getRegionList(@RequestBody RegionListDTO regionListDTO) {
        return Result.success(deptService.getRegionList(regionListDTO));
    }
    /**
     * 业务大区+电销大区部门列表
     *
     * @return 实例对象
     */
    @PostMapping("/getRegionLists")
    public Result<List<Tree<Integer>>> getRegionLists() {
        return Result.success(deptService.getRegionLists());
    }

    @PostMapping("/child/list")
    public Result<List<Integer>> selectChildDeptIds(@RequestBody DeptIdDTO deptIds) {
        return Result.success(deptService.getChildDeptByIds(deptIds));
    }

    @PostMapping("/getRegionInfoVoList")
    public Result<List<RegionInfoVO>> getRegionVoList() {
        return Result.success(deptService.getRegionInfoVoList());
    }

    /**
     * 三方处置公司列表
     *
     * @param threePartyDeptDTO
     * @return
     */
    @PostMapping("/getThreePartyDeptList")
    public Result<Page<DisposalCompanyVO>> getThreePartyDeptList(@RequestBody ThreePartyDeptDTO threePartyDeptDTO) {
        return Result.success(deptService.getThreePartyDeptList(threePartyDeptDTO));
    }

    /**
     * 获取部门信息是否闭店
     */
    @PostMapping("/getDeptInfoByDeptId")

    public Result<RegionInfoVO> getDeptInfoByDeptId(@RequestBody ThreePartyDeptDTO threePartyDeptDTO) {
        return Result.success(deptService.getDeptInfoByDeptId(threePartyDeptDTO));
    }

    @PostMapping("/getDigitalStoreIdByDeptId")

    public Result<List<DeptSyncInfoVO>> getDigitalStoreIdByDeptId(@RequestBody DigitalDeptsDTO digitalDeptsDTO) {
        return Result.success(deptService.getDigitalStoreIdByDeptId(digitalDeptsDTO));
    }

    /**
     * 三方处置公司添加
     *
     * @return
     */
    @PostMapping("/editThreePartyDept")
    public Result<Boolean> editThreePartyDept(@RequestBody DisposalCompanyDTO dto, @CurrentUser LoginUser loginUser) {
        return Result.success(deptService.editThreePartyDept(dto, loginUser));
    }

    @PostMapping("/getOriginallyDepts")
    public Result<List<Integer>> getOriginallyDepts(@RequestBody List<Integer> deptIds){
        return Result.success(deptService.getOriginallyDepts(deptIds));
    }

    /**
     * 根据地区查询所属门店
     *
     * @param dto
     * @return
     */
    @PostMapping("/getStoreListByRegionId")
    public Result<List<StoreInfoVO>> getStoreListByRegionId(@RequestBody RegionListDTO dto) {
        return Result.success(deptService.getStoreListByRegionId(dto));
    }

    @ApiOperation(value = "保存处置公司更新记录")
    @PostMapping("/save/disposal/company/record")
    Result<Boolean> saveDisposalRecord(@RequestBody DisposalFieldUpdateDTO dto, @CurrentUser LoginUser loginUser) {
        return Result.success(deptService.saveDisposalRecord(dto, loginUser));
    }

    @ApiOperation(value = "三方处置公司信更新记录")
    @PostMapping("/get/disposal/company/record")
    Result<Page<DisposalFieldUpdateVO>> getOutsourcingMaintenanceRecord(@RequestBody DisposalFieldUpdateDTO dto) {
        return Result.success(deptService.getDisposalCompanyRecord(dto));
    }


    /**
     * 业务大区+电销大区部门列表
     * 根据用户部分显示相应的大区
     * @return 实例对象
     */
    @PostMapping("/getRegionListsByPermission")
    public Result<List<Tree<Integer>>> getRegionListsByPermission(@CurrentUser LoginUser loginUser) {
        return Result.success(deptService.getRegionListsByPermission(loginUser));
    }

    /**
     * 查询业务大区及电销部下的门店列表
     *
     * @return 实例对象
     */
    @PostMapping("/getRegionAndDeptList")
    public Result<List<Tree<Integer>>> getRegionAndDeptList() {
        return Result.success(deptService.getRegionAndDeptList());
    }

    /**
     * 批量查询部门信息
     * @param deptIds
     * @return
     */
    @PostMapping("/selectDeptBatchIds")
    public Result<List<DeptEntityVO>> selectDeptBatchIds(@RequestBody List<Integer> deptIds){
        return Result.success(deptService.selectDeptBatchIds(deptIds));
    }
}
