package com.longhuan.order.controller;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.PreApprovalListDTO;
import com.longhuan.order.pojo.dto.PreApprovalSubmitInfoDTO;
import com.longhuan.order.pojo.dto.ResetManagerDTO;
import com.longhuan.order.pojo.dto.StoreProductDTO;
import com.longhuan.order.pojo.entity.PreApprovalApplyInfoEntity;
import com.longhuan.order.pojo.vo.PreApprovalApplyInfoVO;
import com.longhuan.order.pojo.vo.PreApprovalListVO;
import com.longhuan.order.pojo.vo.ProductFundMappingVO;
import com.longhuan.order.service.ApprovalService;
import com.longhuan.order.service.PreApprovalApplyInfoService;
import com.longhuan.order.service.RiskService;
import com.longhuan.user.pojo.vo.RegionInfoVO;
import io.swagger.annotations.ApiModelProperty;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 预审批
 *
 * <AUTHOR>
 * @date 2024/08/13
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/pre/approval")
@RequiredArgsConstructor
public class PreApprovalController {
    private final PreApprovalApplyInfoService preApprovalApplyInfoService;
    private final ApprovalService approvalService;
    private final RiskService riskService;

    /**
     * 预审批提交
     *
     * @param preApprovalSubmitInfoDTO 插入批准申请信息 DTO
     * @return {@link Result }<{@link PreApprovalApplyInfoEntity }>
     */
    @PostMapping("/submit")
    public Result<PreApprovalApplyInfoEntity> submit(@RequestBody @Validated PreApprovalSubmitInfoDTO preApprovalSubmitInfoDTO) {
        log.info("PreApprovalApplyInfoServiceImpl.submit preApprovalSubmitInfoDTO:{}", JSONUtil.toJsonStr(preApprovalSubmitInfoDTO));
        PreApprovalApplyInfoEntity submit = preApprovalApplyInfoService.submit(preApprovalSubmitInfoDTO);

        return Result.success(submit);
    }

    /**
     * 列表
     *
     * @param preApprovalListDTO 预先批准清单 DTO
     * @param loginUserInfo      登录用户信息
     * @return {@link Result }<{@link Page }<{@link PreApprovalListVO }>>
     */
    @PostMapping("/list")
    public Result<IPage<PreApprovalListVO>> list(@RequestBody @Validated PreApprovalListDTO preApprovalListDTO,
                                                 @CurrentUser LoginUser loginUserInfo) {
        return Result.success(approvalService.list(preApprovalListDTO, loginUserInfo));
    }

    /**
     * 列表导出
     * @param preApprovalListDTO
     * @param loginUserInfo
     * @param response
     */
    @PostMapping("/exportList")
    public void exportList(@RequestBody @Validated PreApprovalListDTO preApprovalListDTO,
                                                 @CurrentUser LoginUser loginUserInfo,
                           HttpServletResponse response) {
        approvalService.exportList(preApprovalListDTO, loginUserInfo,response);
    }

    /**
     * 获取大区列表
     * @return
     */
    @GetMapping("/order/regionList")
    public Result<List<RegionInfoVO>> regionList() {
        return Result.success(approvalService.regionList());
    }
    /**
     * 获取除申请人外其他信息
     *
     * @param preId 预审批id
     * @return {@link Result< PreApprovalApplyInfoVO>}
     */
    @ApiModelProperty("除申请人外其他信息")
    @GetMapping("/queryOtherInfo/{preId}")
    public Result<PreApprovalApplyInfoVO> queryOtherInfo(@PathVariable("preId") Integer preId) {
        return Result.success(approvalService.queryByOtherInfo(preId));
    }

    /**
     * 通过预审id获取申请人信息
     *
     * @param preId 预审批id
     * @return {@link Result< PreApprovalApplyInfoVO>}
     */
    @ApiModelProperty("通过id获取申请人信息")
    @GetMapping("/queryInfoById/{preId}")
    public Result<PreApprovalApplyInfoVO> queryInfo(@PathVariable("preId") Integer preId) {
        return Result.success(approvalService.queryById(preId));
    }

    /**
     * 获取人脸图片
     * @param preId
     */
    @GetMapping("/getFaceByPreId/{preId}")
    public Result<String> getFaceByPreId(@PathVariable("preId") Integer preId) {
        return Result.success(preApprovalApplyInfoService.getFaceByPreId(preId));
    }

    /**
     * 更新人脸图片
     */
    @GetMapping("/updateFaceByPreId")
    public Result<String> updateFaceByPreId(@RequestParam("preId") Integer preId, @RequestParam("facePicture") String facePicture) {
        return Result.success(preApprovalApplyInfoService.updateFaceByPreId(preId, facePicture));
    }


    /**
     * 批量预审批量发起风控
     *
     * @return
     */
    @GetMapping("/risk/launch/batch")
    public Result<String> riskLaunchBatch() {
        return Result.success(riskService.batchRiskLaunchOnPreApproval());
    }

//    /**
//     * 批量预审更新风控取数状态
//     *
//     * @return
//     */
//    @GetMapping("/risk/data/batch")
//    public Result<String> riskDataBatch() {
//        return Result.success(riskService.batchFetchRiskDataStatus());
//    }
//
    /**
     * 预审批批量调用风控引擎
     *
     * @return
     */
    @GetMapping("/risk/policy/batch")
    public Result<String> policyBatch() {
        return Result.success(riskService.batchExecuteRiskEngine());
    }
//
//    /**
//     * 批量预审更新风控结果
//     *
//     * @return
//     */
//    @GetMapping("/risk/result/batch")
//    public Result<String> riskResultBatch() {
//        return Result.success(riskService.batchUpdatePreApprovalRiskResult());
//    }


    /**
     * 重置业务员
     * @return
     */
    @PostMapping("/reset/manager")
    public Result<Boolean> resetManager(@RequestBody @Validated ResetManagerDTO resetManagerDTO, @CurrentUser LoginUser loginUserInfo) {

        return Result.success(preApprovalApplyInfoService.resetManager(resetManagerDTO, loginUserInfo));
    }

    /**
     * 门店适用资方产品
     * @return
     */
    @PostMapping("/store/product/list")
    public Result<List<ProductFundMappingVO>> storeProductList(@RequestBody StoreProductDTO infoDTO, @CurrentUser LoginUser loginUserInfo) {

        return Result.success(preApprovalApplyInfoService.storeProductList(infoDTO,loginUserInfo));
    }

    /**
     * 获取是否支持预审信息更新
     * @param preId 预审批id
     * @return {@link Result<Boolean>} true 支持更新，false 不支持更新
     */
    @GetMapping("/getSupportUpdateFlag/{preId}")
    public Result<Boolean> getSupportUpdateFlag(@PathVariable("preId") Integer preId) {
        return Result.success(preApprovalApplyInfoService.getSupportUpdateFlag(preId));
    }

    /**
     * 获取征信数据
     * @param preId
     * @return
     */
    @GetMapping("/fillRiskCreditFeature")
    public Result<Map<String,Object>> fillRiskCreditFeature(@RequestParam Integer preId, @CurrentUser LoginUser loginUserInfo) {
        return Result.success(preApprovalApplyInfoService.fillRiskCreditFeature(preId,loginUserInfo));
    }

    /**
     * 风控初审预审提醒
     * @param orderId
     * @return
     */
    @GetMapping("/riskReviewPreWarn")
    public Result<String> riskReviewPreWarn(@RequestParam Integer orderId, @CurrentUser LoginUser loginUserInfo) {
        return Result.success(approvalService.riskReviewPreWarn(orderId,loginUserInfo));
    }
}
