<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.longhuan.order.mapper.CaseInfoEntityMapper">
    <resultMap id="caseInfoResultMap" type="com.longhuan.order.pojo.dto.LoanCaseDTO">
        <result property="case_no" column="case_no"/>
        <result property="type" column="type"/>
        <result property="orderId" column="orderId"/>
        <result property="name" column="name"/>
        <result property="mobile" column="mobile"/>
        <result property="loan_money" column="loan_money"/>
        <result property="due_money" column="due_money"/>
        <result property="redeem_money" column="redeem_money"/>
        <result property="surplus_intpri_money" column="surplus_intpri_money"/>
        <result property="overdue_days" column="overdue_days"/>
        <result property="overdue_at" column="overdue_at"/>
        <result property="bill_type" column="bill_type"/>
        <result property="weiwai_type" column="weiwai_type"/>
        <result property="settle_money" column="settle_money"/>
    </resultMap>

    <select id="getCaseInfoList" resultMap="caseInfoResultMap">
        select
        lci.case_no as case_no,
        lci.case_type as type,
        t.id as orderId,
        t.customer_name as name,
        t.customer_phone as mobile,
        t2.customer_confirm_amount as loan_money,
        ( select SUM(t1.repayment_principal) from LH_FUND_REPAYMENT_INFO t1 where t1.order_id = lci.order_id and
        t1.actually_date is null ) as due_money,
        ( select SUM(t1.settlement_amount) from LH_FUND_REPAYMENT_INFO t1 where t1.order_id = lci.order_id and
        t1.actually_date is null ) as redeem_money,
        ( select SUM(t1.repayment_interest) from LH_FUND_REPAYMENT_INFO t1 where t1.order_id = lci.order_id and
        t1.actually_date is null ) as surplus_intpri_money,
        ( select
        (EXTRACT(EPOCH FROM (
        (SELECT min(lci.repayment_date)
        FROM LH_FUND_REPAYMENT_INFO lci
        WHERE t.id = lci.order_id
        AND lci.actually_date IS NULL)
        )))::bigint) AS overdue_at,

        t.overdue_days as overdue_days,
        2 as bill_type,
        t2.customer_confirm_amount as settle_money,
        lci.preservation_state as partner_preserve,
        CASE
        WHEN lci.circulation_type = 1 THEN '委外外访'
        WHEN lci.circulation_type = 0 THEN '委外保全'
        ELSE '未知'
        END AS weiwai_type
       from
        lh_case_info lci
        left join
        LH_ORDER_INFO t on
        lci.order_id = t.id
        left join LH_ORDER_AMOUNT t2 on
        (t2.order_id = t.id)
        where
        <if test="orderId != null ">
            lci.order_id = #{orderId} and lci.delete_flag = 0 and lci.current_node = 30 and lci.circulation_status !=1
        </if>


    </select>
    <resultMap id="repaymentDetailResultMap" type="com.longhuan.order.pojo.vo.RepaymentDetailVO">
        <result property="deadline" column="deadline"/>
        <result property="re_bx" column="re_bx"/>
        <result property="re_benjin" column="re_benjin"/>
        <result property="re_lixi" column="re_lixi"/>
        <result property="re_damages" column="re_damages"/>
        <result property="re_fwf" column="re_fwf"/>
        <result property="re_time" column="re_time"/>
        <result property="status" column="status"/>
        <result property="is_compensatory" column="is_compensatory"/>
        <result property="single_compensatory" column="single_compensatory"/>
        <result property="se_benjin" column="se_benjin"/>
        <result property="se_lixi" column="se_lixi"/>
        <result property="se_damages" column="se_damages"/>
        <result property="se_fwf" column="se_fwf"/>
        <result property="se_bx" column="se_bx"/>
        <result property="surplus_re" column="surplus_re"/>
        <result property="surplus_benjin" column="surplus_benjin"/>
        <result property="pre_settle" column="pre_settle"/>
        <result property="se_time" column="se_time"/>
    </resultMap>
    <select id="getRepaymentDetailList" resultMap="repaymentDetailResultMap">
        select
        t.term as deadline,
        t.repayment_principal+t.repayment_interest  as re_bx,
        t.repayment_principal as re_benjin,
        t.repayment_interest as re_lixi,
        t.repayment_penalty as re_damages,
        '0' as re_fwf,
        t.repayment_date as re_time,
        t.repayment_status as status,
       '0' as is_compensatory,
        '0' as single_compensatory,
        t.actually_principal as se_benjin,
        t.actually_interest as se_lixi,
        t.actually_principal as se_damages,
        '0' as se_fwf,
        t.actually_principal+t.actually_interest as se_bx,
        (t.repayment_principal+t.repayment_interest)-  (t.actually_principal+t.actually_interest) as surplus_re,
        t.repayment_principal-  t.actually_principal as surplus_benjin,
        t.settlement_amount as pre_settle,
        t.actually_date as se_time
        from
        lh_case_info lci left join lh_fund_repayment_info t on
        lci.order_id = t.order_id where
        <if test="orderId != null ">
            lci.case_no = #{caseNo}  and lci.delete_flag = 0
        </if>
        order by  t.term ASC ;
    </select>
    <resultMap id="repaymentSummaryResultMap" type="com.longhuan.order.pojo.vo.CaseRepaymentPlanVO">
        <result property="paid_total" column="paid_total"/>
        <result property="paid_nums" column="paid_nums"/>
        <result property="is_redeem" column="is_redeem"/>
        <result property="compensatory_total" column="compensatory_total"/>
        <result property="early_settle" column="early_settle"/>
        <result property="deposit" column="deposit"/>
        <result property="term" column="term"/>
    </resultMap>
    <select id="getRepaymentDetai" resultMap="repaymentSummaryResultMap">
        select (select SUM(t.actually_principal)
        from LH_FUND_REPAYMENT_INFO t
        where t1.order_id = t.order_id
        and t.actually_principal is not null) as paid_total,
        (select count(*)
        from LH_FUND_REPAYMENT_INFO t
        where t1.order_id = t.order_id and t.actually_date is not null) as paid_nums,
        loi.term as term,
        loi.is_repurchase as is_redeem,
        '' as compensatory_total,
        t2.closed_period as early_settle,
        t3.deposit as deposit,
        '' as redeem_money
        from lh_case_info t1
        left join lh_order_info loi on t1.order_id =loi.id
        inner join lh_fund_product_mapping t2 on t2.product_id=loi.product_id and t2.delete_flag =0 and loi.order_type =t2.business_type
        left join  lh_product_info t3 on loi.product_id=t3.id and t3.delete_flag=0
        where
        <if test="orderId != null ">
            t1.case_no = #{caseNo} and t1.delete_flag = 0
        </if>
        limit 1;
    </select>
    <select id="getPaymentReceiptVOList" resultType="com.longhuan.order.pojo.vo.PaymentReceiptVO">
        select
        t.case_no ,
        t.name,
        t1.payee_amount as receivable_amount,
        '' as is_partners,
        t1.payee_account as collect_bankcard,
        t1.payee_account_branch_name as opening_bank,
        '' as payment_name,
        t1.pay_account as payment_bankcard,
        '' as payment_bank,
        t1.create_time as payment_date,
        t1.payment_voucher_list as payment_img,
        remark as remark
        from
        lh_case_info t
        left join lh_order_pay_apply_info t1 on
        t.order_id = t1.order_id
        where
        <if test="orderId != null ">
            t.case_no = #{caseNo} and t1.apply_type = 2
        </if>

    </select>
    <select id="getFundRepaymentInfo" resultType="com.longhuan.order.pojo.entity.FundRepaymentInfoEntity">
        SELECT *
        FROM (
                 SELECT *,
                        ROW_NUMBER() OVER (PARTITION BY t.order_id
                              ORDER BY t.repayment_date ASC) AS rn
                 FROM lh_fund_repayment_info t
                 WHERE t.actually_date IS NULL
                   AND t.delete_flag = 0
                   AND (CURRENT_DATE - t.repayment_date) > 90
             ) sub
        WHERE rn = 1
        ORDER BY sub.order_id, sub.repayment_date ASC;
    </select>

    <select id="selectCaseInfoOrderApplyList" resultType="com.longhuan.order.pojo.vo.CaseInfoOrderApplyVO">
        select
            t.id as orderId,
            t.order_number as orderNumber,
            t.customer_name as customerName,
            t.fund_name as fundName,
            t.product_name as productName,
            t.store_name as storeName,
            loa.customer_confirm_amount as customerConfirmAmount,
            t.overdue_days as overdueDays,
            t.payment_state as oderState,

            ( select min(lci.repayment_date) from LH_FUND_REPAYMENT_INFO lci where t.id = lci.order_id and
                lci.actually_date is null ) as overdueDate
        from lh_order_info t
                 left join
             lh_order_amount loa on t.id =loa.order_id
        where

            t.delete_flag = 0
          and t.payment_state=2 and t.is_overdue=1 and t.current_node >= 5000
          and t.id not in (select order_id from lh_case_info where delete_flag = 0  and current_node != -10)


    </select>
    <select id="getCaseInfoDetail" resultType="com.longhuan.order.pojo.vo.CaseInfoDetailVO">
        select t.id as caseInfoid,
        t1.id as orderId,
        t1.order_number as orderNumber,
        t1.customer_name as customerName,
        t1.fund_name as fundName,
        t1.product_name as productName,
        t1.store_name as storeName,
        t1.overdue_days as  overdueDays,
        t1.is_overdue as oderState,
        t1.customer_phone as customerPhone,
        loa.customer_confirm_amount as customerConfirmAmount,
        (
        select
        min (lci.repayment_date)
        from
        LH_FUND_REPAYMENT_INFO lci
        where
        t.order_id = lci.order_id
        and lci.actually_date is null) as overdueDate,
        t.disposal_company as disposalCompany,
        t.attachment_list as imageList,
        t.remark as remark,
        t.circulation_type as circulationType,
        t2.id_number as idNumber,
        t3.brand as vehicleBrand,
        t3.vin as vin,
        t1.repay_method as interestType,
        t3.vehicle_model as vehicleModel,
        t4.register_date  as registrationTime
        from
        lh_case_info t
        left join
        lh_order_amount loa on
        t.order_id = loa.order_id
        left join lh_order_info t1 on
        t.order_id = t1.id
        left join lh_order_customer_info t2 on t2.id=t1.customer_id
        left join lh_order_vehicle_info t3 on t3.order_id=t1.id
        left join lh_pre_vehicle_info t4 on t3.vehicle_number =t4.vehicle_number
        where
        t.delete_flag = 0
        <if test="caseInfoId != null ">
            and t.id = #{caseInfoId}
        </if>

    </select>
    <select id="getAllCaseInfoList" resultMap="caseInfoResultMap">
        select lci.case_no                                        as case_no,
               lci.case_type                                      as type,
               t.id                                               as orderId,
               t.customer_name                                    as name,
               t.customer_phone                                   as mobile,
               t2.customer_confirm_amount                         as loan_money,
               (select SUM(t1.repayment_principal)
                from LH_FUND_REPAYMENT_INFO t1
                where t1.order_id = lci.order_id
                  and t1.actually_date is null)                   as due_money,
               (select SUM(t1.settlement_amount)
                from LH_FUND_REPAYMENT_INFO t1
                where t1.order_id = lci.order_id
                  and t1.actually_date is null)                   as redeem_money,
               (select SUM(t1.repayment_interest)
                from LH_FUND_REPAYMENT_INFO t1
                where t1.order_id = lci.order_id
                  and t1.actually_date is null)                   as surplus_intpri_money,
               (select (EXTRACT(EPOCH FROM (
                   (SELECT MAX(lci.repayment_date)
                    FROM LH_FUND_REPAYMENT_INFO lci
                    WHERE t.id = lci.order_id
                      AND lci.actually_date IS NULL)))) ::bigint) AS overdue_at,

               t.overdue_days                                     as overdue_days,
               2                                                  as bill_type,
               t2.customer_confirm_amount                         as settle_money,
               lci.preservation_state                             as partner_preserve
        from lh_case_info lci
                 left join
             LH_ORDER_INFO t on
                 lci.order_id = t.id
                 left join LH_ORDER_AMOUNT t2 on
            (t2.order_id = t.id)

        where lci.delete_flag = 0
          and lci.current_node = 30
          and lci.circulation_status !=1 and lci.data_source=1 and lci.dept_id=100000

    </select>
    <select id="getCaseInfoDetails" resultType="com.longhuan.order.pojo.vo.CaseInfoDetailVO">
        select t.id as caseInfoid,
        t1.id as orderId,
        t1.order_number as orderNumber,
        t1.customer_name as customerName,
        t1.fund_name as fundName,
        t1.product_name as productName,
        t1.store_name as storeName,
        t1.region_name as regionName,
        t1.overdue_days as  overdueDays,
        t1.is_overdue as oderState,
        CASE
        WHEN t1.current_node =8000 THEN '已结清'
        WHEN t1.is_overdue = 1 THEN '逾期'
        WHEN t1.is_overdue = 0 THEN '还款中'
        ELSE '未知'
        END AS oderStateDesc,
       t1.customer_phone as customerPhone,
        loa.customer_confirm_amount as customerConfirmAmount,
        (
        select
        min (lci.repayment_date)
        from
        LH_FUND_REPAYMENT_INFO lci
        where
        t.order_id = lci.order_id
        and lci.actually_date is null) as overdueDate,
        t.disposal_company as disposalCompany,
        t.attachment_list as imageList,
        t.remark as remark,
        t.circulation_type as circulationType,
        t2.id_number as idNumber,
        t3.brand as vehicleBrand,
        t3.vin as vin,
        t1.repay_method as interestType,
        t3.vehicle_model as vehicleModel,
        t4.register_date  as registrationTime,
        t.dept_id as deptId,
        t3.vehicle_number as vehicleNumber,
        t1.region_name as regionName,
        t.data_source as dataSource,
        t.digital_order_id as digitalOrderId,
        t1.is_repurchase as isRepurchase
        from
        lh_case_info t
        left join
        lh_order_amount loa on
        t.order_id = loa.order_id
        left join lh_order_info t1 on
        t.order_id = t1.id
        left join lh_order_customer_info t2 on t2.id=t1.customer_id
        left join lh_order_vehicle_info t3 on t3.order_id=t1.id
        left join lh_pre_vehicle_info t4 on t3.vehicle_number =t4.vehicle_number
        where
        t.delete_flag = 0
        <if test="caseInfoId != null ">
            and t.id = #{caseInfoId}
        </if>
    </select>
    <select id="getRepurchaseRepaymentDetailList" resultType="com.longhuan.order.pojo.vo.RepaymentDetailVO">
        select
        t.term as deadline,
        t.repayment_principal+t.repayment_interest  as re_bx,
        t.repayment_principal as re_benjin,
        t.repayment_interest as re_lixi,
        t.repayment_penalty as re_damages,
        '0' as re_fwf,
        t.repayment_date as re_time,
        t.repayment_status as status,
        '0' as is_compensatory,
        '0' as single_compensatory,
        t.actually_principal as se_benjin,
        t.actually_interest as se_lixi,
        t.actually_principal as se_damages,
        '0' as se_fwf,
        t.actually_principal+t.actually_interest as se_bx,
        (t.repayment_principal+t.repayment_interest)-  (t.actually_principal+t.actually_interest) as surplus_re,
        t.repayment_principal-  t.actually_principal as surplus_benjin,
        t.settlement_amount as pre_settle,
        t.actually_date as se_time
        from
        lh_case_info lci left join lh_repurchase_repayment_info t on
        lci.order_id = t.order_id where
        <if test="orderId != null ">
            lci.case_no = #{caseNo}  and lci.delete_flag = 0
        </if>
        order by  t.term ASC ;
    </select>
    <select id="getRepurchaseRepaymentDetai" resultType="com.longhuan.order.pojo.vo.CaseRepaymentPlanVO">
        select (select SUM(t.actually_principal)
        from lh_repurchase_repayment_info t
        where t1.order_id = t.order_id
        and t.actually_principal is not null) as paid_total,
        (select count(*)
        from lh_repurchase_repayment_info t
        where t1.order_id = t.order_id and t.actually_date is not null) as paid_nums,
        loi.term as term,
        loi.is_repurchase as is_redeem,
        '' as compensatory_total,
        t2.closed_period as early_settle,
        t3.deposit as deposit,
        '' as redeem_money
        from lh_case_info t1
        left join lh_order_info loi on t1.order_id =loi.id
        inner join lh_fund_product_mapping t2 on t2.product_id=loi.product_id and t2.delete_flag =0 and loi.order_type =t2.business_type
        left join  lh_product_info t3 on loi.product_id=t3.id and t3.delete_flag=0
        where
        <if test="orderId != null ">
            t1.case_no = #{caseNo} and t1.delete_flag = 0
        </if>
        limit 1;
    </select>
    <select id="getRepurchaseCaseInfoList" resultMap="caseInfoResultMap">
        select
        lci.case_no as case_no,
        lci.case_type as type,
        t.id as orderId,
        t.customer_name as name,
        t.customer_phone as mobile,
        t2.customer_confirm_amount as loan_money,
        ( select SUM(t1.repayment_principal) from lh_repurchase_repayment_info t1 where t1.order_id = lci.order_id and
        t1.actually_date is null ) as due_money,
        ( select SUM(t1.settlement_amount) from lh_repurchase_repayment_info t1 where t1.order_id = lci.order_id and
        t1.actually_date is null ) as redeem_money,
        ( select SUM(t1.repayment_interest) from lh_repurchase_repayment_info t1 where t1.order_id = lci.order_id and
        t1.actually_date is null ) as surplus_intpri_money,
        ( select
        (EXTRACT(EPOCH FROM (
        (SELECT min(lci.repayment_date)
        FROM lh_repurchase_repayment_info lci
        WHERE t.id = lci.order_id
        AND lci.actually_date IS NULL)
        )))::bigint) AS overdue_at,
        t.overdue_days as overdue_days,
        2 as bill_type,
        t2.customer_confirm_amount as settle_money,
        lci.preservation_state as partner_preserve,
        CASE
        WHEN lci.circulation_type = 1 THEN '委外外访'
        WHEN lci.circulation_type = 0 THEN '委外保全'
        ELSE '未知'
        END AS weiwai_type
        from
        lh_case_info lci
        left join
        LH_ORDER_INFO t on
        lci.order_id = t.id
        left join LH_ORDER_AMOUNT t2 on
        (t2.order_id = t.id)
 where
        <if test="orderId != null ">
            lci.order_id = #{orderId} and lci.delete_flag = 0 and lci.current_node = 30 and lci.circulation_status !=1
        </if>

    </select>

</mapper>