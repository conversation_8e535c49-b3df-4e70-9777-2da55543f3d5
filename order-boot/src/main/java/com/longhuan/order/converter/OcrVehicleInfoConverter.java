package com.longhuan.order.converter;

import com.longhuan.order.pojo.entity.OrderVehicleInfoEntity;
import com.longhuan.order.pojo.entity.PreOcrVehicleInfoEntity;
import com.longhuan.order.pojo.dto.InsertOcrVehicleInfoDTO;
import com.longhuan.order.pojo.vo.PreOcrVehicleInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @Date 2024/7/5
 */
@Mapper(componentModel = "spring")
public interface OcrVehicleInfoConverter {

    @Mapping(source = "transferTimes", target = "transferTimes")
    PreOcrVehicleInfoEntity insertOcrVehicleInfoDto2OcrVehicleInfoEntity(InsertOcrVehicleInfoDTO insertOcrVehicleInfoDTO);

    /**
     * 订购前
     *
     * @param preOcrVehicleInfoEntity 预登记车辆信息实体
     * @return {@link OrderVehicleInfoEntity}
     */
    OrderVehicleInfoEntity preToOrder(PreOcrVehicleInfoEntity preOcrVehicleInfoEntity);


    PreOcrVehicleInfoVO entityToVo(PreOcrVehicleInfoEntity preOcrVehicleInfoEntity);

}
