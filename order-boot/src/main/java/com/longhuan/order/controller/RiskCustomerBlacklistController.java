package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.converter.RiskCustomerBlacklistConverter;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.RiskCustomerBlacklistEntity;
import com.longhuan.order.pojo.entity.RiskCustomerBlacklistLogEntity;
import com.longhuan.order.pojo.vo.RiskCustomerBlacklistVO;
import com.longhuan.order.service.RiskCustomerBlacklistService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 黑名单
 *
 * <AUTHOR>
 * @Date 2024/7/11
 */
@Api(tags = "客户黑名单")
@RestController
@RequestMapping("/api/v1/blacklist")
@RequiredArgsConstructor
public class RiskCustomerBlacklistController {
    private final RiskCustomerBlacklistService riskCustomerBlacklistService;
    private final RiskCustomerBlacklistConverter riskCustomerBlacklistConverter;

    /**
     * 查询黑名单
     * @param dto
     * @return
     */
    @PostMapping("/queryBlacklist")
    public Result<List<RiskCustomerBlacklistVO>> queryBlacklist(@RequestBody RiskCustomerBlacklistQueryDTO dto) {
        List<RiskCustomerBlacklistEntity> list = riskCustomerBlacklistService.queryBlacklist(dto);
        return Result.success(riskCustomerBlacklistConverter.entityList2VOList(list));
    }

    /**
     * 添加黑名单
     *
     * @param blacklistDTO 添加黑名单信息
     */
    @PostMapping("/add")
    public Result<Boolean> addBlacklist(@Validated @RequestBody RiskCustomerBlacklistDTO blacklistDTO) {
        return Result.success(riskCustomerBlacklistService.saveByCustomerId(blacklistDTO.getCustomerId(), blacklistDTO.getBlackReason(),blacklistDTO.getRemarks()));
    }

    /**
     *  根据客户id删除黑名单
     * @return {@link Result< Boolean>}
     */
    @GetMapping("/deleteByCustomerId/{customerId}")
    public Result<Boolean> deleteBlacklist(@PathVariable("customerId") Integer customerId) {
        return Result.success(riskCustomerBlacklistService.deleteByCustomerId(customerId));
    }

    //黑名单页面
    /**
     * 新增
     */
    @ApiOperation(value = "黑名单新增")
    @PostMapping("/addBlacklist")
    public Result<Boolean> addBlacklist(@RequestBody RiskCustomerBlacklistAddDTO riskCustomerBlacklistAddDTO) {
        return riskCustomerBlacklistService.addBlacklist(riskCustomerBlacklistAddDTO);
    }
    /**
     * 客户黑名单列表
     */
    @ApiOperation(value = "黑名单列表")
    @PostMapping("/page/blacklistlist")
    public Result<Page<RiskCustomerBlacklistEntity>> pageBlacklistlist(@RequestBody RiskCustomerBlacklistPageDTO riskCustomerBlacklistPageDTO) {
        return Result.success(riskCustomerBlacklistService.pageBlacklistlist(riskCustomerBlacklistPageDTO));
    }

    /**
     * 客户黑名单列表
     */
    @ApiOperation(value = "黑名单操作日志列表")
    @PostMapping("/page/blacklistlistLog")
    public Result<Page<RiskCustomerBlacklistLogEntity>> pageBlacklistlistLog(@RequestBody RiskCustomerBlacklistAddDTO riskCustomerBlacklistAddDTO) {
        return Result.success(riskCustomerBlacklistService.pageBlacklistlistLog(riskCustomerBlacklistAddDTO));
    }

    /**
     * 下载模板
     */
    @ApiOperation(value = "黑名单下载模板")
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
         riskCustomerBlacklistService.downloadTemplate(response);
    }
    /**
     * 导入黑名单
     */
    @ApiOperation(value = "黑名单导入")
    @PostMapping("/import")
    public Result<Object> importBlacklistlist(MultipartFile file) {
        return Result.success(riskCustomerBlacklistService.importBlacklistlist(file));
    }

    /**
     * 导入T
     */
//    @ApiOperation(value = "导入T")
//    @PostMapping("/importest")
    public Result<Object> importest(MultipartFile file,Integer type) {
        return Result.success(riskCustomerBlacklistService.importest(file,type));
    }


    /**
     * 详情
     */
    @ApiOperation(value = "黑名单详情")
    @PostMapping("/details")
    public Result<RiskCustomerBlacklistEntity> details(@RequestBody RiskCustomerBlacklistAddDTO riskCustomerBlacklistAddDTO) {
        return Result.success(riskCustomerBlacklistService.details(riskCustomerBlacklistAddDTO));
    }

    /**
     * 修改黑名单状态 生效解除
     */
    @ApiOperation(value = "编辑生效解除")
    @PostMapping("/edit")
    public Result<Boolean> edit(@RequestBody RiskCustomerBlacklistAddDTO riskCustomerBlacklistAddDTO) {
        return Result.success(riskCustomerBlacklistService.edit(riskCustomerBlacklistAddDTO));
    }
    /**
     * 定时新增黑名单每日执行
     */
    @ApiOperation(value = "定时新增黑名单")
    @GetMapping("/timingBlacklist")
    public Result<Boolean> timingBlacklist() {
        return Result.success(riskCustomerBlacklistService.timingBlacklist());
    }






    /**
     * 客户黑名单数据跑批-数据同步
     */
    @ApiOperation(value = "客户黑名单数据跑批-数据同步")
    @GetMapping("/updateBlacklist")
    public Result<Boolean> updateBlacklist() {
        return Result.success(riskCustomerBlacklistService.updateBlacklist());
    }
    /**
     * 系统自动将客户加入黑名单--跑历史数据
     * @return
     */
    @ApiOperation(value = "系统自动将客户加入黑名单--跑历史数据")
    @GetMapping("/historyAddBlacklis")
    public Result<Boolean> historyAddBlacklis() {
        return Result.success(riskCustomerBlacklistService.historyAddBlacklis());
    }
    /**
     * 校验公安不良接口是否有涉毒的信息
     * @return
     */
    @ApiOperation(value = "公安不良接口")
    @PostMapping("/policeBad")
    public Result<Boolean> policeBad(@RequestBody PoliceBadDTO policeBadDTO) {
        return Result.success(riskCustomerBlacklistService.policeBad(policeBadDTO));
    }

}
