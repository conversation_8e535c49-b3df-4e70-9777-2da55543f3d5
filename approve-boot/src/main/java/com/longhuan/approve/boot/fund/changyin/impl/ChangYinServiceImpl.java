package com.longhuan.approve.boot.fund.changyin.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.approve.api.pojo.dto.changyin.*;
import com.longhuan.approve.api.pojo.vo.changyin.ChangLPRResDTO;
import com.longhuan.approve.api.pojo.vo.changyin.ChangYinContractPreviewVO;
import com.longhuan.approve.api.pojo.vo.changyin.ChangYinLoanTrialResponseDTO;
import com.longhuan.approve.boot.client.ChangYinClient;
import com.longhuan.approve.boot.config.ChangYinConfig;
import com.longhuan.approve.boot.constants.FundConstant;
import com.longhuan.approve.boot.converter.ChangYinConverter;
import com.longhuan.approve.boot.enums.FundPaymentStatusEnum;
import com.longhuan.approve.boot.enums.*;
import com.longhuan.approve.boot.enums.changyin.ChangYinDictEnum;
import com.longhuan.approve.boot.enums.changyin.ContractTypeMapping;
import com.longhuan.approve.boot.feign.OrderFeign;
import com.longhuan.approve.boot.feign.ResourceFeign;
import com.longhuan.approve.boot.fund.changyin.ChangYinService;
import com.longhuan.approve.boot.fund.finall.FinalFundInfoService;
import com.longhuan.approve.boot.mapper.*;
import com.longhuan.approve.boot.pojo.dto.*;
import com.longhuan.approve.boot.pojo.dto.changyin.*;
import com.longhuan.approve.boot.pojo.dto.changyin.request.*;
import com.longhuan.approve.boot.pojo.dto.changyin.response.*;
import com.longhuan.approve.boot.pojo.entity.*;
import com.longhuan.approve.boot.service.FundBaseInfoService;
import com.longhuan.approve.boot.service.FundRepaymentDeductService;
import com.longhuan.approve.boot.service.FundRepaymentInfoService;
import com.longhuan.approve.boot.service.processing.FundMessageProcess;
import com.longhuan.approve.boot.utils.PareAddressUtils;
import com.longhuan.approve.boot.utils.SerialNumberUtils;
import com.longhuan.approve.boot.utils.changyin.AddressUtil;
import com.longhuan.common.core.constant.BankNameEnum;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.core.enums.dict.*;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.resource.pojo.dto.*;
import com.longhuan.resource.pojo.vo.FileVO;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
@Slf4j
public class ChangYinServiceImpl implements ChangYinService {

    private final ChangYinClient changYinClient;
    private final ChangYinConfig changYinConfig;
    private final FundBaseInfoService fundBaseInfoService;
    private final FundApproveMapper fundApproveMapper;
    private final FinalFundInfoService finalFundInfoService;
    private final OrderFeign orderFeign;
    private final ChangYinConverter changYinConverter;
    private final PreFundInfoMapper preFundInfoMapper;
    private final ResourceFeign resourceFeign;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final OrderFileMapper orderFileMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final FundRepaymentDeductMapper fundRepaymentDeductMapper;
    private final OrderIdCardUpdateMapper orderIdCardUpdateMapper;
    private final FundRepaymentInfoService fundRepaymentInfoService;
    private final FundRepaymentDeductService fundRepaymentDeductService;
    private final FundMessageProcess fundMessageProcess;
    private final FundRepurchaseResultMapper fundRepurchaseResultMapper;


    @Value("${changyin.addressFilePath}")
    private String addressFilePath;

    @PostConstruct
    public void init() {
        AddressUtil.setFilePath(addressFilePath);
    }


    /**
     * 预授信申请
     */
    @Override
    public void approvePreApply(FundPreBaseDTO fundPreBaseDTO) {
        log.info("ChangYinServiceImpl.approvePreApply begin fundPreBaseDTO: {}", fundPreBaseDTO);
        String applyNumber = fundPreBaseDTO.getFundPreNumber();
        // 授信结果
        PreFundInfoEntity preFundInfo = new PreFundInfoEntity();
        preFundInfo.setCreditReqNo(applyNumber)
                .setFundNode(FundApplyNodeEnums.FUND_PRE_APPROVAL);
        preFundInfoMapper.update(preFundInfo, new LambdaUpdateWrapper<PreFundInfoEntity>()
                .eq(PreFundInfoEntity::getPreId, fundPreBaseDTO.getPreId())
                .eq(PreFundInfoEntity::getFundId, fundPreBaseDTO.getFundId())
                .eq(PreFundInfoEntity::getDeleteFlag, 0)
        );
    }


    /**
     * 预授信申请
     */
    @Override
    public void approvePreApplyQuery(Integer preId) {
        log.info("ChangYinServiceImpl.approvePreApplyQuery begin preId: {}", preId);

        try {
            // 备注
            String message = "授信通过";
            // 更新资方申请信息状态
            preFundInfoMapper.update(new LambdaUpdateWrapper<PreFundInfoEntity>()
                    .set(PreFundInfoEntity::getFundNode, FundApplyNodeEnums.FUND_PRE_APPROVAL_RESULT_QUERY)
                    .eq(PreFundInfoEntity::getPreId, preId)
                    .eq(PreFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                    .eq(PreFundInfoEntity::getDeleteFlag, 0)
            );

            // 更新订单授信状态
            PreFundResultEnum updatePreStatus = PreFundResultEnum.PASS;
            PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                    .select(PreFundInfoEntity::getCreditAmount)
                    .eq(PreFundInfoEntity::getPreId, preId)
                    .eq(PreFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                    .eq(PreFundInfoEntity::getDeleteFlag, 0));

            BigDecimal creditAmount = BigDecimal.ZERO;

            if (preFundInfoEntity != null && preFundInfoEntity.getCreditAmount() != null) {
                creditAmount = preFundInfoEntity.getCreditAmount();
            }

            PreApproveFundStatusDTO preApproveFundStatusDTO = new PreApproveFundStatusDTO()
                    .setPreId(preId)
                    .setFundId(FundEnum.CHANG_YIN.getValue())
                    .setStatus(updatePreStatus)
                    .setCreditAmt(creditAmount)
                    .setFailReason(message);
            log.info("PreApproveFundStatusDTO created: {}", JSONUtil.toJsonStr(preApproveFundStatusDTO));
            orderFeign.preUpdateFundStatus(preApproveFundStatusDTO);

            log.info("ChangYinServiceImpl.approvePreApply end");
        } catch (BusinessException e) {
            log.error("FuMinPreServiceImpl.handlePreCreditQueryResult fail msg:{}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("FuMinPreServiceImpl.handlePreCreditQueryResult fail msg:{}", e.getMessage(), e);
            throw new BusinessException("处理预授信查询结果时发生错误：err: " + e.getMessage());
        }
    }

    @Override
    public ChangYinResBodyDTO<ChangYinPreApprovalInfoResDTO> approveApplyV2(FundFinalBaseDTO fundFinalBaseDTO, String fundFinalNumber) {

        ChangYinPreApprovalInfoDTO changYinPreApprovalInfoDTO = buildCreditApplyV2(fundFinalBaseDTO, fundFinalNumber);
        return changYinClient.creditApplyV2(changYinPreApprovalInfoDTO);
    }

    @Override
    public ChangYinResBodyDTO<ChangYinPreApprovalResDTO> preApproveApplyV2(FundPreBaseDTO preBaseDTO) {
        log.info("ChangYinServiceImpl.preApproveApplyV2 begin preId:{} preBaseDTO: {}", preBaseDTO.getPreId(), preBaseDTO);
        ChangYinPreApplyDTO preApplyDTO = new ChangYinPreApplyDTO();
        preApplyDTO.setOutApplNo(preBaseDTO.getFundPreNumber())
                .setMerchantNo(changYinConfig.getMerchantNo())
                .setStoreCode(changYinConfig.getStoreCode())
                .setTerminalType(changYinConfig.getTerminalType())
                .setApplyDt(DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN))
                .setCustName(preBaseDTO.getName())
                .setCardType(ChangYinDictEnum.IdentificationType.ID_CARD.getCode())
                .setCardId(preBaseDTO.getIdNumber())
                .setMobile(preBaseDTO.getPhone())
                .setImageInfoList(buildPreApplyImageInfoList(preBaseDTO.getPreId(), preBaseDTO.getIdNumber(), preBaseDTO.getPhone()))
        ;
        ChangYinResBodyDTO<ChangYinPreApprovalResDTO> changYinPreApprovalResDTOChangYinResBodyDTO = changYinClient.preCreditApplyV2(preApplyDTO);
        log.info("ChangYinServiceImpl.approveApplyV2 end preId:{} result:{}", preBaseDTO.getPreId(), JSONUtil.toJsonStr(changYinPreApprovalResDTOChangYinResBodyDTO));
        LambdaUpdateWrapper<PreFundInfoEntity> luw = new LambdaUpdateWrapper<PreFundInfoEntity>()
                .set(PreFundInfoEntity::getFundResult, PreFundResultEnum.WAIT)
                .set(PreFundInfoEntity::getCreditReqNo, preBaseDTO.getFundPreNumber())
                .eq(PreFundInfoEntity::getPreId, preBaseDTO.getPreId())
                .eq(PreFundInfoEntity::getFundId, preBaseDTO.getFundId())
                .eq(PreFundInfoEntity::getDeleteFlag, 0);
        if (ChangYinResBodyDTO.isSuccess(changYinPreApprovalResDTOChangYinResBodyDTO)) {
            luw.set(PreFundInfoEntity::getFundNumber, changYinPreApprovalResDTOChangYinResBodyDTO.getBody().getPreApplCde());
        }
        preFundInfoMapper.update(luw);
        return changYinPreApprovalResDTOChangYinResBodyDTO;
    }

    /**
     * 长银预授信申请查询
     *
     */
    @Override
    public ChangYinResBodyDTO<ChangYinPreApprovalResDTO> preApproveQueryV2ByPreId(Integer preId) {
        PreFundInfoEntity preFundInfo = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                .eq(PreFundInfoEntity::getPreId, preId)
                .eq(PreFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(PreFundInfoEntity::getDeleteFlag, 0)
        );
        if (ObjUtil.isNull(preFundInfo)){
            throw new BusinessException("预授信申请信息不存在");
        }
        CustomerBaseDTO preBaseDTO = fundApproveMapper.getCustomerPreBaseInfo(preId);
        if (ObjUtil.isNull(preBaseDTO)){
            throw new BusinessException("客户基本信息不存在");
        }

        ChangYinPreApplyDTO preApplyDTO = new ChangYinPreApplyDTO();
        preApplyDTO.setOutApplNo(preFundInfo.getCreditReqNo())
                .setMerchantNo(changYinConfig.getMerchantNo())
                .setStoreCode(changYinConfig.getStoreCode())
                .setTerminalType(changYinConfig.getTerminalType())
                .setApplyDt(DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN))
                .setCustName(preBaseDTO.getName())
                .setCardType(ChangYinDictEnum.IdentificationType.ID_CARD.getCode())
                .setCardId(preBaseDTO.getIdNumber())
                .setMobile(preBaseDTO.getPhone())
                .setImageInfoList(buildPreApplyImageInfoList(preId, preBaseDTO.getIdNumber(), preBaseDTO.getPhone()))
        ;
        ChangYinResBodyDTO<ChangYinPreApprovalResDTO> resultDTO = changYinClient.preCreditApplyV2(preApplyDTO);
        log.info("ChangYinServiceImpl.preApproveQueryV2ByPreId preId:{} result: {}",  preId, JSONUtil.toJsonStr(resultDTO));

        if (ChangYinResBodyDTO.isSuccess(resultDTO)) {
            // 更新订单授信状态
            ChangYinPreApprovalResDTO body = resultDTO.getBody();
            PreFundResultEnum updatePreStatus = PreFundResultEnum.WAIT;
            ChangYinDictEnum.ChangYinPreApplyStatusEnum preCreditResult = body.getPreCreditResult();
            String message = "";
            updatePreStatus = switch (preCreditResult) {
                case ACCOUNT_SUCCESS -> PreFundResultEnum.PASS;
                case ACCOUNT_FAIL -> PreFundResultEnum.REJECT;
                case ACCOUNT_PASS -> PreFundResultEnum.WAIT;
                default -> PreFundResultEnum.WAIT;
            };
            if (ObjUtil.equals(PreFundResultEnum.PASS, updatePreStatus)) {
                message = "授信通过";
            } else if (ObjUtil.equals(PreFundResultEnum.REJECT, updatePreStatus)) {
                message = "授信拒绝";
            }
            if (StrUtil.isNotBlank(body.getRiskRefuseMsg())) {
                message += " " + body.getRiskRefuseMsg();
            }

            BigDecimal creditAmount = BigDecimal.ZERO;

            if (preFundInfo.getCreditAmount() != null) {
                creditAmount = preFundInfo.getCreditAmount();
            }

            PreApproveFundStatusDTO preApproveFundStatusDTO = new PreApproveFundStatusDTO()
                    .setPreId(preId)
                    .setFundId(FundEnum.CHANG_YIN.getValue())
                    .setStatus(updatePreStatus)
                    .setCreditAmt(creditAmount)
                    .setFailReason(message);
            log.info("ChangYinServiceImpl.preApproveQueryV2ByPreId created: {}", JSONUtil.toJsonStr(preApproveFundStatusDTO));
            orderFeign.preUpdateFundStatus(preApproveFundStatusDTO);

        }
        log.info("ChangYinServiceImpl.preApproveQueryV2ByPreId end");
        return resultDTO;
    }

    /**
     * 授信状态查询
     */
    @Override
    public ChangYinResBodyDTO<ChangYinCreditQueryResDTO> creditQueryV2ByOrderId(Integer orderId) {
        FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.CHANG_YIN);
        ChangYinCreditQueryDTO changYinCreditQueryDTO = new ChangYinCreditQueryDTO();
        changYinCreditQueryDTO.setOutApplSeq(finalFundInfo.getCreditReqNo());
        ChangYinResBodyDTO<ChangYinCreditQueryResDTO> result = creditQueryV2(changYinCreditQueryDTO);
        if (!ChangYinResBodyDTO.isSuccess(result)) {
            log.info("ChangYinServiceImpl.creditQueryV2ByOrderId fail orderId:{} result: {}", orderId, JSONUtil.toJsonStr(result));
            return result;
        }
        handleApproveApplyResult(result.getBody());
        return result;
    }

    /**
     * 3.5 用信申请
     * 调用放款风控审核接口
     *
     * @return 审核结果
     */
    @Override
    public ChangYinResBodyDTO<ChangYinLoanApplicationRespDTO> preLoanApplyV2ByOrderId(Integer orderId, String contractNumber) {
        log.info("ChangYinServiceImpl.preLoanApplyV2 begin orderId: {}", orderId);
        FundFinalBaseDTO fundFinalBaseDTO = fundBaseInfoService.getFundFinalBaseApplyInfo(orderId);
        if (ObjUtil.isNull(fundFinalBaseDTO)) {
            throw new BusinessException("获取提交订单基础信息失败");
        }
        log.info("ChangYinServiceImpl.preLoanApplyV2 fundFinalBaseDTO: {}", fundFinalBaseDTO);
        ChangYinLoanApplicationDTO dto = buildPreLoanApplicationRequest(fundFinalBaseDTO, contractNumber);
        ChangYinResBodyDTO<ChangYinLoanApplicationRespDTO> result = changYinClient.preLoanApplyV2(dto);
        log.info("ChangYinServiceImpl.preLoanApplyV2 result: {}", result);
        if (!ChangYinResBodyDTO.isSuccess(result)) {
            throw new BusinessException(result.getHead().getRespMsg());
        }
        ChangYinLoanApplicationRespDTO resultBody = result.getBody();
        //更新为用信申请
        finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                .set(FinalFundInfoEntity::getPrePaymentStatus, FundPrePaymentStatusEnum.WAIT)
                .set(FinalFundInfoEntity::getLoanReqNo, resultBody.getOutLoanSeq())
                .set(FinalFundInfoEntity::getLoanBillNo, resultBody.getLoanSeq())
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );
        return result;
    }

    /**
     * 放款申请
     */
    @Override
    public ChangYinResBodyDTO<ChangYinLoanApplyResDTO> loanApplyV2ByOrderId(Integer orderId) {
        ChangYinLoanApplyDTO changYinLoanApplyDTO = buildLoanApplyRequest(orderId);
        return changYinClient.loanApplyV2(changYinLoanApplyDTO);
    }

    /**
     * 放款状态查询
     */
    @Override
    public ChangYinResBodyDTO<ChangYinPreLoanQueryResDTO> preLoanQueryV2ByOrderId(Integer orderId) {
        FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.CHANG_YIN);
        Assert.notNull(finalFundInfo, () -> new BusinessException("申请信息不存在"));
        ChangYinPreLoanQueryDTO changYinPreLoanQueryDTO = new ChangYinPreLoanQueryDTO();
        changYinPreLoanQueryDTO.setOutLoanSeq(finalFundInfo.getLoanReqNo());
        ChangYinResBodyDTO<ChangYinPreLoanQueryResDTO> result = preLoanQueryV2(changYinPreLoanQueryDTO);
        ChangYinResBodyDTO<ChangYinPreLoanHandlerResDTO> resultHandlerDTO = changYinConverter.prePayQueryTOHandlerDTO(result);
        handlePreLoanQuery(resultHandlerDTO);
        return result;
    }


    /**
     * 用信查询
     */
    private void handlePreLoanQuery(ChangYinResBodyDTO<ChangYinPreLoanHandlerResDTO> result) {
        if (!ChangYinResBodyDTO.isSuccess(result)) {
            log.info("ChangYinServiceImpl.preLoanQueryV2ByOrderId failresult: {}", JSONUtil.toJsonStr(result));
            return;
        }
        //判断是否用信通过
        ChangYinPreLoanHandlerResDTO dto = result.getBody();
        String dnSts = dto.getDnSts();
        if (StrUtil.equals(ChangYinDictEnum.ChangYinPreLoanStatusEnum.PASS.getCode(), dnSts)
                || StrUtil.equals(ChangYinDictEnum.ChangYinPreLoanStatusEnum.REJECT.getCode(), dnSts)
        ) {
            FundPrePaymentStatusEnum preFundResultEnum = StrUtil.equals(ChangYinDictEnum.ChangYinPreLoanStatusEnum.PASS.getCode(), dnSts)
                    ? FundPrePaymentStatusEnum.PASS
                    : FundPrePaymentStatusEnum.FAIL;
            finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .set(FinalFundInfoEntity::getPrePaymentStatus, preFundResultEnum)
                    .eq(FinalFundInfoEntity::getLoanReqNo, dto.getOutLoanSeq())
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
            );
        }


    }

    /**
     * 放款状态查询
     *
     * @param changYinLoanQueryDTO
     * @return
     */
    @Override
    public ChangYinResBodyDTO<ChangYinLoanQueryResDTO> loanQueryV2(ChangYinLoanQueryDTO changYinLoanQueryDTO) {

        return changYinClient.loanQueryV2(changYinLoanQueryDTO);
    }

    /**
     * 签约申请
     *
     * @param changYinSignInfoDTO
     * @return
     */
    @Override
    public ChangYinResBodyDTO<ChangYinSignResDTO> protocolApply(ChangYinSignInfoDTO changYinSignInfoDTO) {
        log.info("ChangYinServiceImpl.protocolApply begin changYinSignInfoDTO: {}", changYinSignInfoDTO);
        changYinSignInfoDTO.setLoanTyp(changYinConfig.getLoanTyp());
//        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        DateTimeFormatter dateTimeFormatter1 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
//        String operaTime = dateTimeFormatter.format(LocalDateTime.now());
//        String sign = dateTimeFormatter1.format(LocalDateTime.now());
//        log.info("签约申请单号============》" + "LH" + sign);
//        changYinSignInfoDTO
//                .setSignSeq("LH" + sign)
//                .setOperateTime(operaTime)
//                .setAcctNo("6228480639292585633")
//                .setAcctName("轩华强")
//                .setIdTyp("20")
//                .setIdNo("412724199812203355")
//                .setAcctPhone("***********")
//                .setAcctBankCode("0102")
//                .setLoanTyp(changYinConfig.getLoanTyp());
        ChangYinResBodyDTO<ChangYinSignResDTO> changYinSignResDTOChangYinResBodyDTO = changYinClient.protocolApply(changYinSignInfoDTO);

        return changYinSignResDTOChangYinResBodyDTO;
    }


    /**
     * 签约确认
     */
    @Override
    public ChangYinResBodyDTO<ChangYinSignResDTO> protocolConfirm(ChangYinSignConfirmDTO changYinSignConfirmDTO) {
        log.info("ChangYinServiceImpl.protocolConfirm begin changYinSignConfirmDTO: {}", changYinSignConfirmDTO);
//        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        String operaTime = dateTimeFormatter.format(LocalDateTime.now());
//        changYinSignConfirmDTO.setSignSeq("LH20250320170145")
//                .setOperateTime(operaTime)
//                .setSmsNo("111111");
        ChangYinResBodyDTO<ChangYinSignResDTO> changYinSignResDTOChangYinResBodyDTO = changYinClient.protocolConfirm(changYinSignConfirmDTO);

        return changYinSignResDTOChangYinResBodyDTO;
    }


    /**
     * 授信回调
     */
    @Override
    public ChangYinResBodyDTO<ChangYinCallbackResDTO> handleApplyCallback(String request) {
        log.info("ChangYinServiceImpl.handleApplyCallback begin request: {}", request);
        //解密
        ChangYinResBodyDTO<ChangYinCreditApprovalCallbackDTO> callBackReqDTO = null;
        try {
            callBackReqDTO = changYinClient.decryptResult(request, ChangYinCreditApprovalCallbackDTO.class);
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.handleApplyCallback error parsing response: {}", e.getMessage(), e);
            return ChangYinResBodyDTO.success(ChangYinCallbackResDTO.fail("解密失败"));
        }
        try {
            handleApproveApplyCallbackResult(callBackReqDTO.getBody());
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.handleApplyCallback error :{}", e.getMessage(), e);
            return ChangYinResBodyDTO.success(ChangYinCallbackResDTO.fail());
        }
        return ChangYinResBodyDTO.success(ChangYinCallbackResDTO.success());
    }

    /**
     * 用信回调
     */
    @Override
    public ChangYinResBodyDTO<ChangYinCallbackResDTO> handlePreLoanCallback(String request) {
        log.info("ChangYinServiceImpl.handlePreLoanCallback begin request: {}", request);
        ChangYinResBodyDTO<ChangYinPreLoanCallbackDTO> callBackReqDTO = null;
        try {
            callBackReqDTO = changYinClient.decryptResult(request, ChangYinPreLoanCallbackDTO.class);
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.handleApplyCallback error parsing response: {}", e.getMessage(), e);
            return ChangYinResBodyDTO.success(ChangYinCallbackResDTO.fail("解密失败"));
        }

        try {
            ChangYinResBodyDTO<ChangYinPreLoanHandlerResDTO> resultHandlerDTO = changYinConverter.prePayCallbackTOHandlerDTO(callBackReqDTO);
            handlePreLoanQuery(resultHandlerDTO);
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.handleApplyCallback error :{}", e.getMessage(), e);
            return ChangYinResBodyDTO.success(ChangYinCallbackResDTO.fail());
        }
        return ChangYinResBodyDTO.success(ChangYinCallbackResDTO.success());
    }

    /**
     * 放款回调
     */
    @Override
    public ChangYinResBodyDTO<ChangYinCallbackResDTO> handlePayCallback(String request) {
        log.info("ChangYinServiceImpl.handlePayCallback begin request: {}", request);
        //解密
        ChangYinResBodyDTO<ChangYinPayCallbackDTO> callBackReqDTO = null;
        try {
            callBackReqDTO = changYinClient.decryptResult(request, ChangYinPayCallbackDTO.class);
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.handleApplyCallback error parsing response: {}", e.getMessage(), e);
            return ChangYinResBodyDTO.success(ChangYinCallbackResDTO.fail("解密失败"));
        }
        try {
            ChangYinResBodyDTO<ChangYinPayHandlerDTO> resultHandlerDTO = changYinConverter.payCallbackTOHandlerDTO(callBackReqDTO);
            updateOrderPaymentStatus(resultHandlerDTO);
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.handleApplyCallback error :{}", e.getMessage(), e);
            return ChangYinResBodyDTO.success(ChangYinCallbackResDTO.fail());
        }
        return ChangYinResBodyDTO.success(ChangYinCallbackResDTO.success());
    }


    /**
     * 资产正向调额申请根据订单
     */
    @Override
    public boolean limitAdjustApplyV2ByOrderId(Integer orderId) {
        log.info("ChangYinServiceImpl.limitAdjustApplyV2ByOrderId begin orderId: {}", orderId);
        FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.CHANG_YIN);
        Assert.notNull(finalFundInfo, () -> new BusinessException("申请信息不存在"));

        if (StrUtil.isNotBlank(finalFundInfo.getOutAdjustReqNo()) && StrUtil.isNotBlank(finalFundInfo.getAdjustReqNo())) {
            ChangYinLimitAdjustQueryDTO dto = new ChangYinLimitAdjustQueryDTO();
            dto.setAdjustNo(finalFundInfo.getAdjustReqNo());
            dto.setOutAdjustNo(finalFundInfo.getOutAdjustReqNo());
            ChangYinResBodyDTO<ChangYinLimitAdjustQueryResDTO> queryResultDTO = limitAdjustQueryV2(dto);
            if (ChangYinResBodyDTO.isSuccess(queryResultDTO)) {
                ChangYinLimitAdjustQueryResDTO bodyDTO = queryResultDTO.getBody();
                String adjustStatus = bodyDTO.getAdjustStatus();
                log.info("ChangYinServiceImpl.limitAdjustApplyV2ByOrderId orderId:{} adjustStatus: {}", orderId, adjustStatus);
                if (StrUtil.equals(adjustStatus, "01") || StrUtil.equals(adjustStatus, "03")) {
                    return true;
                }
            }
        }


        String adjustNo = SerialNumberUtils.generateSerialNumberStr();
        String outAdjustNo = SerialNumberUtils.generateSerialNumberStr();

        ChangYinLimitAdjustApplyDTO dto = new ChangYinLimitAdjustApplyDTO();
        dto.setAdjustNo(adjustNo);
        dto.setOutAdjustNo(outAdjustNo);

        dto.setMerchantNo(changYinConfig.getMerchantNo()) //  商户码值（需从配置或业务中获取）
                .setStoreCode(changYinConfig.getStoreCode()) // 门店码值（需从配置或业务中获取）
                .setLoanType(changYinConfig.getLoanTyp()); // 贷款品种（需从配置或业务中获取）

        dto.setApplCde(finalFundInfo.getCreditNo())
            .setAdjustType("CLEAR_UP_APPLY")
        ;

        ChangYinResBodyDTO<ChangYinLimitAdjustApplyResDTO> resBodyDTO = limitAdjustApplyV2(dto);
        if (ChangYinResBodyDTO.isSuccess(resBodyDTO)) {
            ChangYinLimitAdjustApplyResDTO bodyDTO = resBodyDTO.getBody();
            String adjustStatus = bodyDTO.getAdjustStatus();
            finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .set(FinalFundInfoEntity::getAdjustReqNo, bodyDTO.getAdjustNo())
                    .set(FinalFundInfoEntity::getOutAdjustReqNo, bodyDTO.getOutAdjustNo())
                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
            );
            if (StrUtil.equals(adjustStatus, "01") || StrUtil.equals(adjustStatus, "03")) {
                return true;
            }
        } else {
            throw new BusinessException("撤销申请失败：" + ObjUtil.defaultIfNull(resBodyDTO.getHead().getRespMsg(), ""));
        }
        return false;
    }

    /**
     * 资产正向调额结果查询根据订单
     */
    @Override
    public ChangYinResBodyDTO<ChangYinLimitAdjustQueryResDTO> limitAdjustQueryV2ByOrderId(Integer orderId) {
        log.info("ChangYinServiceImpl.limitAdjustQueryV2ByOrderId begin orderId: {}", orderId);
        FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.CHANG_YIN);
        Assert.notNull(finalFundInfo, () -> new BusinessException("申请信息不存在"));
        ChangYinLimitAdjustQueryDTO dto = new ChangYinLimitAdjustQueryDTO();
        dto.setAdjustNo(finalFundInfo.getAdjustReqNo());
        dto.setOutAdjustNo(finalFundInfo.getOutAdjustReqNo());
        ChangYinResBodyDTO<ChangYinLimitAdjustQueryResDTO> queryResultDTO = limitAdjustQueryV2(dto);

        return queryResultDTO;
    }

    @Override
    public ChangYinResBodyDTO<ChangYinLimitAdjustApplyResDTO> limitAdjustApplyV2(ChangYinLimitAdjustApplyDTO changYinLimitAdjustApplyDTO) {
        log.info("ChangYinServiceImpl.limitAdjustApplyV2 begin changYinLimitAdjustApplyDTO: {}", changYinLimitAdjustApplyDTO);
        try {
            return changYinClient.limitAdjustApplyV2(changYinLimitAdjustApplyDTO);
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.limitAdjustApplyV2 error e:{}", e.getMessage(), e);
            throw new BusinessException("资产正向调额申请异常");
        }
    }

    @Override
    public ChangYinResBodyDTO<ChangYinLimitAdjustQueryResDTO> limitAdjustQueryV2(ChangYinLimitAdjustQueryDTO changYinLimitAdjustQueryDTO) {
        log.info("ChangYinServiceImpl.limitAdjustQueryV2 begin changYinLimitAdjustQueryDTO: {}", changYinLimitAdjustQueryDTO);
        try {
            return changYinClient.limitAdjustQueryV2(changYinLimitAdjustQueryDTO);
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.limitAdjustQueryV2 error e:{}", e.getMessage(), e);
            throw new BusinessException("资产正向调额结果查询异常");
        }
    }

    /**
     * 还款回调
     request     * @return
     */
    @Override
    public ChangYinResBodyDTO<ChangYinCallbackResDTO> handleRepayCallback(String request) {
        log.info("ChangYinServiceImpl.handleRepayCallback begin request: {}", request);
        //解密
        ChangYinResBodyDTO<ChangYinRepayCallbackDTO> callBackReqDTO = null;
        try {
            callBackReqDTO = changYinClient.decryptResult(request, ChangYinRepayCallbackDTO.class);
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.handleRepayCallback error parsing response: {}", e.getMessage(), e);
            return ChangYinResBodyDTO.success(ChangYinCallbackResDTO.fail("解密失败"));
        }
        try {
            ChangYinResBodyDTO<ChangYinRepayHandlerDTO> resultHandlerDTO = changYinConverter.repaymentTOHandlerDTO(callBackReqDTO);
            updateOrderRePaymentStatus(resultHandlerDTO);
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.handleRepayCallback error :{}", e.getMessage(), e);
            return ChangYinResBodyDTO.success(ChangYinCallbackResDTO.fail());
        }
        return ChangYinResBodyDTO.success(ChangYinCallbackResDTO.success());
    }

    private void updateOrderRePaymentStatus(ChangYinResBodyDTO<ChangYinRepayHandlerDTO> resultDTO) {
        log.info("ChangYinPayServiceImpl.updateOrderPaymentStatus begin resultDTO:{}", JSONUtil.toJsonStr(resultDTO));
        //更新订单表状态
        ChangYinRepayHandlerDTO body = resultDTO.getBody();
        FinalFundInfoEntity fundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getLoanNo, body.getDealRefNo())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );
        if (ObjUtil.isNull(fundInfo)){
            throw new BusinessException("资方终审信息为空");
        }
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(fundInfo.getOrderId());

        if (ObjUtil.isNull(orderInfo)){
            throw new BusinessException("订单信息为空");
        }

        Integer orderId = fundInfo.getOrderId();
        FundDeductRepayStatusEnums status = FundDeductRepayStatusEnums.REPAYMENT_NONE;
        Integer index = 1;
        if (ChangYinResBodyDTO.isSuccess(resultDTO)) {

            //1.判断是否划扣成功或失败 还款中返回成功
            //2.调用长银还款计划查询更新覆盖还款计划表
            //3.保存划扣记录（划扣金额取还款计划查询实还金额）
            //4.还款成功保存交易明细
            //5.更新订单还款
            ChangYinRepayHandlerDTO repayBody = resultDTO.getBody();
            ChangYinDictEnum.ChangYinRepayStatusEnum repayStatus = repayBody.getRepayStatus();
            //还款成功 或 还款处理中
            if (repayStatus.equals(ChangYinDictEnum.ChangYinRepayStatusEnum.REPAY_SUCCESS)){
                status = FundDeductRepayStatusEnums.REPAYMENT_SUCCESS;
                //更新还款计划
                repaymentPlanInit(orderId,ChangYinDictEnum.RepaymentPlanQueryType.FULL_PLAN);



            }
            if (repayStatus.equals(ChangYinDictEnum.ChangYinRepayStatusEnum.REPAY_FAIL)){
                status = FundDeductRepayStatusEnums.REPAYMENT_FAILED;
            }

            //成功/失败保存划扣记录
            List<FundRepaymentDeductEntity> fundRepaymentDeductEntities = fundRepaymentDeductMapper.selectList(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                            .eq(FundRepaymentDeductEntity::getRepayDate, repayBody.getRepayTime().toLocalDate())
                            .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
            );
            index = CollUtil.isEmpty(fundRepaymentDeductEntities) ? 1 :  fundRepaymentDeductEntities.size() + 1;

            new FundRepaymentDeductEntity()
                    .setOrderId(orderId)
                    .setDeductReqNo(repayBody.getOutRepaySeq())
                    .setIndex(index)
                    .setRepayStatus(status)
                    .setRepayDate(repayBody.getRepayTime().toLocalDate())
                    .setFundId(fundInfo.getId());


//
//            fundRepaymentDeductMapper.insert()


            List<FundRepaymentInfoEntity> fundRepaymentInfoList = fundRepaymentInfoMapper.selectList(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                    .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                    .eq(FundRepaymentInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                    .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                    .in(FundRepaymentInfoEntity::getTerm,repayBody.getRepayTerm())
                    .orderByDesc(FundRepaymentInfoEntity::getTerm)
            );




        }
    }
    public Integer getOrderIdByOrderNo(String orderNo){
        Integer orderId = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getOrderNumber, orderNo)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
        ).getId();
        return orderId;
    }

    private ChangYinPreApprovalInfoDTO buildCreditApplyV2(FundFinalBaseDTO fundFinalBaseDTO, String applyNumber) {
        log.info("ChangYinServiceImpl.buildCreditApply begin orderId:{}", fundFinalBaseDTO.getOrderId());
        FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(fundFinalBaseDTO.getOrderId(), FundEnum.CHANG_YIN);
        Assert.notNull(finalFundInfo, () -> new BusinessException("订单不存在"));
        String creditReqNo = finalFundInfo.getCreditReqNo();
        if (StrUtil.isNotBlank(creditReqNo) && (ObjUtil.equals(finalFundInfo.getFundResult(), PreFundResultEnum.PASS) || ObjUtil.equals(finalFundInfo.getFundResult(), PreFundResultEnum.WAIT))) {
            applyNumber = creditReqNo;
        } else {
            finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .set(FinalFundInfoEntity::getCreditReqNo, applyNumber)
                    .eq(FinalFundInfoEntity::getOrderId, fundFinalBaseDTO.getOrderId())
                    .eq(FinalFundInfoEntity::getId, finalFundInfo.getId())
            );
        }
        log.info("ChangYinServiceImpl.buildCreditApply begin fundFinalBaseDTO: {}, applyNumber: {}", fundFinalBaseDTO, applyNumber);
        ChangYinPreApprovalInfoDTO dto = new ChangYinPreApprovalInfoDTO();

        // 设置基本信息
        dto.setApptTyp(ChangYinDictEnum.ApplicantType.PRIMARY_APPLICANT.getCode())
                .setCustName(fundFinalBaseDTO.getName())
                .setOutCustId(fundFinalBaseDTO.getIdNumber())
                .setIdTyp(ChangYinDictEnum.IdentificationType.ID_CARD.getCode())
                .setIdNo(fundFinalBaseDTO.getIdNumber())
                .setIdNoStartDate(DateUtil.format(fundFinalBaseDTO.getValidityStartDate(), DatePattern.NORM_DATE_PATTERN))
                .setIdNoEndDate(StrUtil.equals("长期", fundFinalBaseDTO.getValidityEnd()) ? "9999-12-31" : fundFinalBaseDTO.getValidityEnd())
                .setIdOrgan(fundFinalBaseDTO.getIssuingAuthority())
                .setBornDate(fundFinalBaseDTO.getBirthDate())
                .setIndivMobile(fundFinalBaseDTO.getPhone())
                .setIndivSex(ObjUtil.equals(fundFinalBaseDTO.getGender(), 1) ? ChangYinDictEnum.GenderType.MALE.getCode() : ChangYinDictEnum.GenderType.FEMALE.getCode())
                .setApptAge(fundFinalBaseDTO.getAge())
                .setIndivMarital(ChangYinDictEnum.MaritalStatusType.convert(DictMaritalStatus.fromCode(fundFinalBaseDTO.getMaritalStatus())))
                .setIndivEdu(ChangYinDictEnum.HighestEducationType.convert(DictEducationLevel.fromCode(fundFinalBaseDTO.getEducationalBackground())))
                .setIndivDegree(ChangYinDictEnum.HighestDegreeType.convert(DictEducationLevel.fromCode(fundFinalBaseDTO.getEducationalBackground())))
                .setDirectFlag("N")
                .setLoanInfo(buildApplyLoanInfo(fundFinalBaseDTO, applyNumber)) // 贷款信息
                .setOccupationInfo(buildApplyOccupationInfo(fundFinalBaseDTO)) // 职业信息
                .setFamilyInfo(buildApplyFamilyInfo(fundFinalBaseDTO)) // 家庭信息
                .setSpouseInfo(buildApplySpouseInfo(fundFinalBaseDTO)) // 配偶信息
                .setRelationList(buildApplyRelationList(fundFinalBaseDTO))
                .setImageInfoList(buildApplyImageInfoList(fundFinalBaseDTO.getPreId(), fundFinalBaseDTO.getOrderId()))
        ; // 联系人信息


        log.info("ChangYinServiceImpl.buildCreditApply end dto: {}", JSONUtil.toJsonStr(dto));
        return dto;
    }

    private List<ChangYinPreApprovalInfoDTO.ChangYinImageInfo> buildApplyImageInfoList(Integer preId, Integer orderId) {
        Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(
                new FundResourceDTO().setFund(FundEnum.CHANG_YIN)
                        .setType(2).setLinkId(orderId).setPreId(preId)
        );
        Assert.isTrue(Result.isSuccess(listResult), () -> new BusinessException("获取影像文件失败"));
        List<FundResourceResultDTO> data = listResult.getData();
        List<ChangYinPreApprovalInfoDTO.ChangYinImageInfo> imageInfoList = new ArrayList<>();
        data.forEach(item -> {
            imageInfoList.add(new ChangYinPreApprovalInfoDTO.ChangYinImageInfo()
                    .setImageStage("2")
                    .setImageName(item.getFileCode())
                    .setImageType(item.getFileId())
                    .setImageUrl(item.getFilePath()));
        });
        return imageInfoList;
    }

    private List<ChangYinPreApplyDTO.ImageInfo> buildPreApplyImageInfoList(Integer preId, String idNumber, String phone) {
        Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(
                new FundResourceDTO().setFund(FundEnum.CHANG_YIN)
                        .setType(1).setLinkId(preId).setPreId(preId)
                        .setCustomerBaseInfo(
                                new FundResourceDTO.CustomerBaseInfo()
                                        .setCustomerIdNo(idNumber)
                                        .setPhone(phone)
                        )
        );
        Assert.isTrue(Result.isSuccess(listResult), () -> new BusinessException("获取影像文件失败"));
        List<FundResourceResultDTO> data = listResult.getData();
        List<ChangYinPreApplyDTO.ImageInfo> imageInfoList = new ArrayList<>();
        data.forEach(item -> {
            imageInfoList.add(new ChangYinPreApplyDTO.ImageInfo()
                    .setImageStage("1")
                    .setImageName(item.getFileCode())
                    .setImageType(item.getFileId())
                    .setImageUrl(item.getFilePath()));
        });
        return imageInfoList;
    }


    /**
     * 构建家庭信息
     */
    private ChangYinPreApprovalInfoDTO.ChangYinFamilyInfo buildApplyFamilyInfo(FundFinalBaseDTO fundFinalBaseDTO) {
        ChangYinPreApprovalInfoDTO.ChangYinFamilyInfo familyInfo = new ChangYinPreApprovalInfoDTO.ChangYinFamilyInfo();

        // 现住房地址
        String[] liveAddCodes = AddressUtil.getAllCodes(fundFinalBaseDTO.getResidentialProName(), fundFinalBaseDTO.getResidentialCityName(), fundFinalBaseDTO.getResidentialAreaName());
        if (liveAddCodes.length == 0) {
            throw new BusinessException("现住房地址解析失败");
        }
        try {
            familyInfo.setLiveAddInfo(new ChangYinPreApprovalInfoDTO.ChangYinAddress()
                    .setProvince(liveAddCodes[0]) // 省
                    .setCity(liveAddCodes[1]) // 市
                    .setArea(liveAddCodes[2]) // 区
                    .setAddress(fundFinalBaseDTO.getResidentialDetailedAddress())); // 详细地址
        } catch (Exception e) {
            throw new BusinessException("现住房地址解析失败");
        }

        // 身份证地址解析
        String idCardProvince = fundFinalBaseDTO.getIdCardProvinceName();
        String idCardCity = fundFinalBaseDTO.getIdCardCityName();
        String idCardArea = null;

        // 如果省或市为空，解析身份证地址
        if (StrUtil.isBlank(idCardProvince) || StrUtil.isBlank(idCardCity) || StrUtil.isBlank(idCardArea)) {
            String idCardAddress = PareAddressUtils.parseAddress(fundFinalBaseDTO.getIdCardAddress());
            log.info("ChangYinServiceImpl.buildFamilyInfo begin idCardAddress: {}", idCardAddress);
            String[] idCardAddressParts = idCardAddress.split("-");

            // 分别获取省、市、区
            if (StrUtil.isBlank(idCardProvince)) {
                idCardProvince = idCardAddressParts[0];
            }
            if (StrUtil.isBlank(idCardCity)) {
                idCardCity = idCardAddressParts[1];
            }
            if (StrUtil.isBlank(idCardArea)) {
                idCardArea = idCardAddressParts.length > 2 ? idCardAddressParts[2] : null;
            }
        }

        // 如果省、市都有，并且区不为空，则将区改为“市辖区”
        if (StrUtil.isNotBlank(idCardProvince) && StrUtil.isNotBlank(idCardCity)) {
            if (StrUtil.isBlank(idCardArea)) {
                idCardArea = "市辖区";
            }
        }

        // 获取身份证地址代码（允许区域为空）
        String[] idCardAddCodes = AddressUtil.getAllCodes(idCardProvince, idCardCity, idCardArea);
        if (idCardAddCodes.length == 0) {
            throw new BusinessException("身份证地区解析失败");
        }

        // 户籍地址
        familyInfo.setRegAddInfo(new ChangYinPreApprovalInfoDTO.ChangYinAddress()
                .setProvince(idCardAddCodes[0]) // 省
                .setCity(idCardAddCodes.length > 1 ? idCardAddCodes[1] : null) // 市
                .setArea(idCardAddCodes.length > 2 ? idCardAddCodes[2] : null) // 区
                .setAddress(fundFinalBaseDTO.getIdCardDetailedAddress())); // 详细地址

        return familyInfo;
    }

    /**
     * 构建配偶信息
     */
    private ChangYinPreApprovalInfoDTO.ChangYinSpouseInfo buildApplySpouseInfo(FundFinalBaseDTO fundFinalBaseDTO) {
        ChangYinPreApprovalInfoDTO.ChangYinSpouseInfo spouseInfo = new ChangYinPreApprovalInfoDTO.ChangYinSpouseInfo();

        // 如果婚姻状况为已婚，则填充配偶信息
        DictMaritalStatus dictMaritalStatus = DictMaritalStatus.fromCode(fundFinalBaseDTO.getMaritalStatus());

        if (ObjUtil.equals(dictMaritalStatus, DictMaritalStatus.MARRIED_WITH_CHILDREN) || ObjUtil.equals(dictMaritalStatus, DictMaritalStatus.MARRIED_WITHOUT_CHILDREN)) {
            ContactPersonDTO contactPersonSpouse = fundFinalBaseDTO.getContactPersonList().stream().filter(contactPerson -> ObjUtil.equals(DictContactRelation.fromCode(contactPerson.getRelation()), DictContactRelation.SPOUSE))
                    .findFirst().orElse(null);
            if (ObjUtil.isNull(contactPersonSpouse)) {
                throw new BusinessException("配偶信息不存在");
            }
            spouseInfo.setSpouseName(contactPersonSpouse.getContactName()) // 配偶姓名
                    .setSpouseIdTyp(ChangYinDictEnum.IdentificationType.ID_CARD.getCode()) // 配偶证件类型
                    .setSpouseIdNo(contactPersonSpouse.getIdNumber())
                    .setSpouseMobile(contactPersonSpouse.getContactPhone());
        }
        return spouseInfo;
    }

    /**
     * 构建联系人信息
     */
    private List<ChangYinPreApprovalInfoDTO.ChangYinRelationInfo> buildApplyRelationList(FundFinalBaseDTO fundFinalBaseDTO) {
        if (CollUtil.isEmpty(fundFinalBaseDTO.getContactPersonList())) {
            throw new BusinessException("未获取到联系人信息");
        }

        List<ChangYinPreApprovalInfoDTO.ChangYinRelationInfo> relationList = new ArrayList<>();
        for (ContactPersonDTO contactPerson : fundFinalBaseDTO.getContactPersonList()) {
            ChangYinPreApprovalInfoDTO.ChangYinRelationInfo relationInfo = new ChangYinPreApprovalInfoDTO.ChangYinRelationInfo();

            DictContactRelation contactRelation = DictContactRelation.fromCode(contactPerson.getRelation());

            relationInfo.setRelName(contactPerson.getContactName()) // 联系人名称
                    .setRelMobile(contactPerson.getContactPhone()) // 联系人手机
                    .setRelRelation(ChangYinDictEnum.ApplicantContactRelation.convert(contactRelation)); // 联系人与申请人关系
            relationList.add(relationInfo);
        }


        return relationList;
    }

    /**
     * 构建贷款信息
     */
    private ChangYinPreApprovalInfoDTO.ChangYinLoanInfo buildApplyLoanInfo(FundFinalBaseDTO fundFinalBaseDTO, String applyNumber) {


        PreFundInfoEntity preFundInfo = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
                .eq(PreFundInfoEntity::getPreId, fundFinalBaseDTO.getPreId())
                .eq(PreFundInfoEntity::getFundId, fundFinalBaseDTO.getFundId())
                .eq(PreFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(PreFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(preFundInfo)) {
            throw new BusinessException("未获取到预授信信息");
        }

        OrderAmountCalDTO amountCalDTO = fundBaseInfoService.getApplyAmountByOrderId(fundFinalBaseDTO.getOrderId());
        BigDecimal totalAmount = amountCalDTO.getTotalAmount();
        BigDecimal riskAmount = amountCalDTO.getRiskAmount();
        BigDecimal applyAmt = totalAmount.min(riskAmount)
                .divide(BigDecimal.valueOf(1000), 0, RoundingMode.DOWN)
                .multiply(BigDecimal.valueOf(1000));

        ChangYinPreApprovalInfoDTO.ChangYinLoanInfo loanInfo = new ChangYinPreApprovalInfoDTO.ChangYinLoanInfo();
        loanInfo.setOutApplSeq(applyNumber) // 外部授信流水号
                .setPreApplCde(preFundInfo.getFundNumber())
                .setMerchantNo(changYinConfig.getMerchantNo()) //  商户码值（需从配置或业务中获取）
                .setStoreCode(changYinConfig.getStoreCode()) // 门店码值（需从配置或业务中获取）
                .setTerminalType(changYinConfig.getTerminalType()) // 终端类型，默认13
                .setApplyDt(DateUtil.format(new Date(), "yyyy-MM-dd")) // 申请日期
                .setTypGrp(ChangYinDictEnum.LoanType.SELF_PAYMENT_LOAN.getCode()) // 贷款类型，默认自主支付贷
                .setLoanTyp(changYinConfig.getLoanTyp()) // 贷款品种（需从配置或业务中获取）
                .setApplyAmt(applyAmt) // 申请金额
                .setApplyTnr(fundFinalBaseDTO.getTerm().toString()) // 申请期限
                .setApplyTnrTyp(fundFinalBaseDTO.getTerm().toString()) // 申请期限
                .setCustDayRate(fundFinalBaseDTO.getIrr()) //对客展示利率
                .setLoanFreq("1M") // 还款间隔
                .setDueDayOpt("1") // 每期还款日
        ; // 回调地址（需从配置或业务中获取）
        return loanInfo;
    }

    /**
     * 构建职业信息
     */
    private ChangYinPreApprovalInfoDTO.ChangYinOccupationInfo buildApplyOccupationInfo(FundFinalBaseDTO fundFinalBaseDTO) {
        ChangYinPreApprovalInfoDTO.ChangYinOccupationInfo occupationInfo = new ChangYinPreApprovalInfoDTO.ChangYinOccupationInfo();
        occupationInfo.setProfession(ChangYinDictEnum.Occupation.convert(DictOccupationEnum.fromCode(fundFinalBaseDTO.getVocational()))) // 职业
                .setBelongsIndus(ChangYinDictEnum.IndustryType.convert(DictOccupationEnum.fromCode(fundFinalBaseDTO.getVocational())))
                .setIndivEmpName(fundFinalBaseDTO.getEnterpriseName()) // 现单位名称
                .setMonthIncome(ChangYinDictEnum.MonthlyIncome.convert(fundFinalBaseDTO.getMonthlyIncome()))
        ;
        return occupationInfo;
    }


    @Override
    public ChangYinResBodyDTO<ChangYinCreditQueryResDTO> creditQueryV2(ChangYinCreditQueryDTO changYinCreditQueryDTO) {
        ChangYinResBodyDTO<ChangYinCreditQueryResDTO> changYinCreditQueryResDTOChangYinResBodyDTO = changYinClient.creditQueryV2(changYinCreditQueryDTO);
        ChangYinCreditQueryResDTO body = changYinCreditQueryResDTOChangYinResBodyDTO.getBody();
        return changYinCreditQueryResDTOChangYinResBodyDTO;
    }


    /**
     * 构建 LoanApplicationRequest 对象
     *
     * @param fundFinalBaseDTO FundFinalBaseDTO 对象
     * @return LoanApplicationRequest 对象
     */
    public ChangYinLoanApplicationDTO buildPreLoanApplicationRequest(FundFinalBaseDTO fundFinalBaseDTO, String contractNumber) {
        OrderAmountCalDTO orderAmount = fundBaseInfoService.getApplyAmountByOrderId(fundFinalBaseDTO.getOrderId());

        FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(fundFinalBaseDTO.getOrderId(), FundEnum.CHANG_YIN);

        ChangYinLoanApplicationDTO request = new ChangYinLoanApplicationDTO();
        //生成放款流水号 订单号+时间戳+随机数
        String outLoanSeq = fundFinalBaseDTO.getOrderId() + DateUtil.currentSeconds() + RandomUtil.randomNumbers(6);
        //外部放款流水号
        request.setOutLoanSeq(outLoanSeq);
        //外部合同号
        request.setOutContractSeq(outLoanSeq);
        // 长银授信流水号
        request.setApplCde(finalFundInfo.getCreditNo());
        // 长银客户号
        request.setCustId(fundFinalBaseDTO.getIdNumber());
        // 商户码值
        request.setMerchantNo(changYinConfig.getMerchantNo());
        // 门店码值
        request.setStoreCode(changYinConfig.getStoreCode());
        // 终端类型
        request.setTerminalType(changYinConfig.getTerminalType());
        // 贷款品种
        request.setLoanTyp(changYinConfig.getLoanTyp());
        // 放款金额
        request.setDnAmt(orderAmount.getCustomerConfirmAmount());
        // 申请期限
        request.setApplyTnr(fundFinalBaseDTO.getTerm().toString());
        // 还款方式
        request.setMtdCde(ChangYinDictEnum.RepaymentMethod.convert(DictRepaymentMethod.fromCode(fundFinalBaseDTO.getRepayMethod())));
        // 贷款用途
        request.setPurpose(ChangYinDictEnum.LoanPurpose.convert(DictApplyPurposeEnum.fromCode(fundFinalBaseDTO.getApplyPurpose())));
        //贷款用途其他方式
        if (StrUtil.equals(request.getPurpose(), ChangYinDictEnum.LoanPurpose.OTHER.getCode())) {
            request.setOtherPurpose(DictApplyPurposeEnum.fromCode(fundFinalBaseDTO.getApplyPurpose()).getDescription());
        }
        // 对客利率
        request.setCustDayRate(fundFinalBaseDTO.getIrr());
        request.setPriceIntRat(new BigDecimal("0.065"));
        request.setGuaranteeInfo(new ChangYinLoanApplicationDTO.GuaranteeInfo()
                .setGuarRate(request.getCustDayRate().subtract(new BigDecimal("0.065")))
                .setGuarContNo(contractNumber)
        );
        // 账号信息列表
        request.setAccInfoList(buildPreLoanAccInfoList(fundFinalBaseDTO));
        // 影像信息列表
        request.setImageInfoList(buildPreLoanImageInfoList(fundFinalBaseDTO.getPreId(), fundFinalBaseDTO.getOrderId()));
        // 主申人信息
        request.setBasicInfo(buildPreLoanBasicInfo(fundFinalBaseDTO));

        return request;
    }

    /**
     * 构建贷款申请请求
     */
    private ChangYinLoanApplyDTO buildLoanApplyRequest(Integer orderId) {
        FundFinalBaseDTO fundFinalBaseDTO = fundBaseInfoService.getFundFinalBaseApplyInfo(orderId);
        OrderAmountCalDTO orderAmount = fundBaseInfoService.getApplyAmountByOrderId(fundFinalBaseDTO.getOrderId());

        FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(fundFinalBaseDTO.getOrderId(), FundEnum.CHANG_YIN);

        ChangYinLoanApplyDTO request = new ChangYinLoanApplyDTO();
        request.setOutLoanSeq(finalFundInfo.getLoanReqNo())
                .setOutContractSeq(finalFundInfo.getLoanReqNo())
                .setApplCde(finalFundInfo.getCreditNo())
                .setCustId(fundFinalBaseDTO.getIdNumber())
                .setMerchantNo(changYinConfig.getMerchantNo())
                .setStoreCode(changYinConfig.getStoreCode())
                .setTerminalType(changYinConfig.getTerminalType())
                .setDnAmt(orderAmount.getCustomerConfirmAmount())
                .setApplyTnr(fundFinalBaseDTO.getTerm().toString())
                .setMtdCde(ChangYinDictEnum.RepaymentMethod.convert(DictRepaymentMethod.fromCode(fundFinalBaseDTO.getRepayMethod())))
                .setPurpose(ChangYinDictEnum.LoanPurpose.convert(DictApplyPurposeEnum.fromCode(fundFinalBaseDTO.getApplyPurpose())))
                .setCustDayRate(fundFinalBaseDTO.getIrr())
                .setPriceIntRat(new BigDecimal("0.065"))
//                .setGuaranteeInfo(new ChangYinLoanApplyDTO.GuaranteeInfo()
//                     .setGuarRate(request.getPriceIntRat().subtract(request.getCustDayRate()))
//                        .setGuarContNo(changYinConfig.getMerchantNo()).setGuarSignTime("2025-03-01")
//                        .setGuarOdIntRate(fundFinalBaseDTO.getGuaranteeFee().divide(new BigDecimal("360")))
//                        .setGuarTime("10")
//
//                )
                .setAccInfoList(buildLoanApplyAccInfoList(fundFinalBaseDTO))
                .setImageInfoList(buildLoanImageInfoList(fundFinalBaseDTO.getPreId(), fundFinalBaseDTO.getOrderId()));

        ;

        //贷款用途其他方式
        if (StrUtil.equals(request.getPurpose(), ChangYinDictEnum.LoanPurpose.OTHER.getCode())) {
            request.setOtherPurpose(DictApplyPurposeEnum.fromCode(fundFinalBaseDTO.getApplyPurpose()).getDescription());
        }
//        request.setPayOrderId("1202503261655451080007001025");
//                .setExtendInfo("test");
        request.setMtdMode(ChangYinDictEnum.InterestRateMode.FIXED_RATE.getCode());
        request.setLoanFreq("1M");
        request.setDueDayOpt("1");


        return request;
    }

    /**
     * 构建账号信息列表
     *
     * @param fundFinalBaseDTO FundFinalBaseDTO 对象
     * @return 账号信息列表
     */
    private List<ChangYinLoanApplyDTO.ChangYinAccInfo> buildLoanApplyAccInfoList(FundFinalBaseDTO fundFinalBaseDTO) {
        List<BankAccountSignEntity> signCard = fundApproveMapper.getSignCard(fundFinalBaseDTO.getOrderId(), 6);
        if (CollUtil.isEmpty(signCard)) {
            throw new BusinessException("未获取到绑卡信息");
        }
        BankAccountSignEntity bankAccountSignEntity = signCard.get(0);
        ChangYinLoanApplyDTO.ChangYinAccInfo accInfo = new ChangYinLoanApplyDTO.ChangYinAccInfo();
        accInfo.setAcctKind(ChangYinDictEnum.AccountCategory.LOAN_ACCOUNT.getCode());
        accInfo.setAcctTyp(ChangYinDictEnum.AccountType.PERSONAL_ACCOUNT.getCode());
        accInfo.setAcctBankCode(BankNameEnum.getCodeByName(bankAccountSignEntity.getBankName()));
        accInfo.setAcctNo(bankAccountSignEntity.getBankCardNumber());
        accInfo.setAcctName(bankAccountSignEntity.getName());
        accInfo.setIdTyp(ChangYinDictEnum.IdentificationType.ID_CARD.getCode());
        accInfo.setIdNo(bankAccountSignEntity.getIdCardNum());
        accInfo.setAcctPhone(bankAccountSignEntity.getPhone());


        ChangYinLoanApplyDTO.ChangYinAccInfo accRepayInfo = new ChangYinLoanApplyDTO.ChangYinAccInfo();
        accRepayInfo.setAcctKind(ChangYinDictEnum.AccountCategory.REPAYMENT_ACCOUNT.getCode());
        accRepayInfo.setAcctTyp(ChangYinDictEnum.AccountType.PERSONAL_ACCOUNT.getCode());
        accRepayInfo.setAcctBankCode(BankNameEnum.getCodeByName(bankAccountSignEntity.getBankName()));
        accRepayInfo.setAcctNo(bankAccountSignEntity.getBankCardNumber());
        accRepayInfo.setAcctName(bankAccountSignEntity.getName());
        accRepayInfo.setIdTyp(ChangYinDictEnum.IdentificationType.ID_CARD.getCode());
        accRepayInfo.setIdNo(bankAccountSignEntity.getIdCardNum());
        accRepayInfo.setAcctPhone(bankAccountSignEntity.getPhone());
        return Arrays.asList(accInfo, accRepayInfo);
    }


    private ChangYinLoanApplicationDTO.BasicInfo buildPreLoanBasicInfo(FundFinalBaseDTO fundFinalBaseDTO) {
        ChangYinLoanApplicationDTO.BasicInfo basicInfo = new ChangYinLoanApplicationDTO.BasicInfo();
        basicInfo.setIdNo(fundFinalBaseDTO.getIdNumber())
                .setIdNoStartDate(DateUtil.format(fundFinalBaseDTO.getValidityStartDate(), DatePattern.NORM_DATE_PATTERN))
                .setIdNoEndDate(StrUtil.equals("长期", fundFinalBaseDTO.getValidityEnd()) ? "9999-12-31" : fundFinalBaseDTO.getValidityEnd())
        ;
        return basicInfo;
    }

    /**
     * 构建账号信息列表
     *
     * @param fundFinalBaseDTO FundFinalBaseDTO 对象
     * @return 账号信息列表
     */
    private List<ChangYinLoanApplicationDTO.AccInfo> buildPreLoanAccInfoList(FundFinalBaseDTO fundFinalBaseDTO) {
        List<BankAccountSignEntity> signCard = fundApproveMapper.getSignCard(fundFinalBaseDTO.getOrderId(), 6);
        if (CollUtil.isEmpty(signCard)) {
            throw new BusinessException("未获取到绑卡信息");
        }
        BankAccountSignEntity bankAccountSignEntity = signCard.get(0);

        ChangYinLoanApplicationDTO.AccInfo accInfo = new ChangYinLoanApplicationDTO.AccInfo();
        accInfo.setAcctKind(ChangYinDictEnum.AccountCategory.LOAN_ACCOUNT.getCode());
        accInfo.setAcctTyp(ChangYinDictEnum.AccountType.PERSONAL_ACCOUNT.getCode());
        accInfo.setAcctBankCode(BankNameEnum.getCodeByName(bankAccountSignEntity.getBankName()));
        accInfo.setAcctNo(bankAccountSignEntity.getBankCardNumber());
        accInfo.setAcctName(bankAccountSignEntity.getName());
        accInfo.setIdTyp(ChangYinDictEnum.IdentificationType.ID_CARD.getCode());
        accInfo.setIdNo(bankAccountSignEntity.getIdCardNum());
        accInfo.setAcctPhone(bankAccountSignEntity.getPhone());

        ChangYinLoanApplicationDTO.AccInfo accRepayInfo = new ChangYinLoanApplicationDTO.AccInfo();
        accRepayInfo.setAcctKind(ChangYinDictEnum.AccountCategory.REPAYMENT_ACCOUNT.getCode());
        accRepayInfo.setAcctTyp(ChangYinDictEnum.AccountType.PERSONAL_ACCOUNT.getCode());
        accRepayInfo.setAcctBankCode(BankNameEnum.getCodeByName(bankAccountSignEntity.getBankName()));
        accRepayInfo.setAcctNo(bankAccountSignEntity.getBankCardNumber());
        accRepayInfo.setAcctName(bankAccountSignEntity.getName());
        accRepayInfo.setIdTyp(ChangYinDictEnum.IdentificationType.ID_CARD.getCode());
        accRepayInfo.setIdNo(bankAccountSignEntity.getIdCardNum());
        accRepayInfo.setAcctPhone(bankAccountSignEntity.getPhone());

        return Arrays.asList(accInfo, accRepayInfo);
    }

    /**
     * 处理终审授信结果
     */
    private void handleApproveApplyResult(ChangYinCreditQueryResDTO dto) {
        // 处理终审授信结果
        log.info("ChangYinService.handleApproveApplyResult start dto {}", JSONUtil.toJsonStr(dto));
        ChangYinCreditApprovalHandlerDTO approvalHandlerDTO = changYinConverter.applyResultTOHandlerDTO(dto);
        handleFinalCreditQueryResult(approvalHandlerDTO);
    }

    /**
     * 处理终审授信回调结果
     */
    private void handleApproveApplyCallbackResult(ChangYinCreditApprovalCallbackDTO dto) {
        // 处理终审授信回调结果
        log.info("ChangYinService.handleApproveApplyCallbackResult start dto {}", JSONUtil.toJsonStr(dto));
        ChangYinCreditApprovalHandlerDTO approvalHandlerDTO = changYinConverter.applyCallbackTOHandlerDTO(dto);
        handleFinalCreditQueryResult(approvalHandlerDTO);
    }


    /**
     * 处理终审授信结果
     */
    private void handleFinalCreditQueryResult(ChangYinCreditApprovalHandlerDTO dto) {
        log.info("ChangYinService.handleFinalCreditQueryResult start {}", JSONUtil.toJsonStr(dto));

        // 获取资方申请信息
        FinalFundInfoEntity fundApplyInfo = finalFundInfoService.getInfoByCreditReqNo(dto.getOutApplSeq(), FundEnum.CHANG_YIN);
        if (fundApplyInfo == null) {
            log.info("ChangYinService.handleFinalCreditQueryResult not found fundApplyInfo for outApplSeq: {}", dto.getOutApplSeq());
            return;
        }

        String creditReqNo = fundApplyInfo.getCreditReqNo();
        Integer orderId = fundApplyInfo.getOrderId();

        if (ObjUtil.equals(fundApplyInfo.getFundResult(), PreFundResultEnum.PASS)) {
            //通过不处理
            log.info("ChangYinService.handleFinalCreditQueryResult pass creditReqNo:{}", creditReqNo);
            return;
        }

        if (StrUtil.isEmpty(creditReqNo)) {
            log.error("ChangYinService.handleFinalCreditQueryResult not found creditReqNo:{}", creditReqNo);
            return;
        }

        //授信状态
        ChangYinDictEnum.NonCyclicalCreditStatus outSts = dto.getOutSts();
        //原因
        String fundRemark = outSts.getDescription() + (StrUtil.isNotBlank(dto.getOutRiskMsg()) ? dto.getOutRiskMsg() : "");
        //授信金额
        BigDecimal creditAmt = ObjUtil.defaultIfNull(dto.getBaseLimit(), BigDecimal.ZERO);


        PreFundResultEnum fundResultEnum = ChangYinDictEnum.NonCyclicalCreditStatus.convert(outSts);
        if (ObjUtil.isNull(fundResultEnum)) {
            log.error("ChangYinService.handleFinalCreditQueryResult not found fundResultEnum:{}", outSts);
            return;
        }

        if (ObjUtil.equals(fundResultEnum, PreFundResultEnum.PASS)) {
            //授信成功 更新授信金额
            AmountCalDTO amountCalDTO = new AmountCalDTO();
            amountCalDTO.setAmount(creditAmt);
            amountCalDTO.setCalType(OrderAmountEnum.FUND_PRE_AMOUNT);
            amountCalDTO.setOrderId(orderId);
            orderFeign.calAmount(amountCalDTO);
        }


        FinalApproveFundStatusDTO fundStatusDTO = new FinalApproveFundStatusDTO()
                .setOrderId(orderId)
                .setFundId(FundEnum.CHANG_YIN.getValue())
                .setStatus(fundResultEnum)
                .setFundCreditAmt(creditAmt)
                .setFundRemark(fundRemark)
                .setFundCreditTime(LocalDateTime.now())
                .setLoanContractNo(dto.getContractNo())
                .setCreditNo(dto.getApplCde());

        log.info("ChangYinService.FinalApproveFundStatusDTO created: {}", JSONUtil.toJsonStr(fundStatusDTO));
        orderFeign.updateFundFinalStatus(fundStatusDTO);


    }

    /**
     * 构建影像信息列表
     *
     * @return 影像信息列表
     */
    private List<ChangYinLoanApplicationDTO.ImageInfo> buildPreLoanImageInfoList(Integer preId, Integer orderId) {
        Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(
                new FundResourceDTO().setFund(FundEnum.CHANG_YIN)
                        .setType(5).setLinkId(orderId).setPreId(preId)
        );
        Assert.isTrue(Result.isSuccess(listResult), () -> new BusinessException("获取影像文件失败"));
        List<FundResourceResultDTO> data = listResult.getData();
        List<ChangYinLoanApplicationDTO.ImageInfo> imageInfoList = new ArrayList<>();
        data.forEach(item -> {
            imageInfoList.add(new ChangYinLoanApplicationDTO.ImageInfo()
                    .setImageStage("3")
                    .setImageName(item.getFileCode())
                    .setImageType(item.getFileId())
                    .setImageUrl(item.getFilePath()));
        });

        return imageInfoList;
    }

    /**
     * 构建影像信息列表
     *
     * @return 影像信息列表
     */
    private List<ChangYinLoanApplyDTO.ImageInfo> buildLoanImageInfoList(Integer preId, Integer orderId) {
        Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(
                new FundResourceDTO().setFund(FundEnum.CHANG_YIN)
                        .setType(3).setLinkId(orderId).setPreId(preId)
        );
        Assert.isTrue(Result.isSuccess(listResult), () -> new BusinessException("获取影像文件失败"));
        List<FundResourceResultDTO> data = listResult.getData();
        List<ChangYinLoanApplyDTO.ImageInfo> imageInfoList = new ArrayList<>();
        data.forEach(item -> {
            imageInfoList.add(new ChangYinLoanApplyDTO.ImageInfo()
                    .setImageStage("3")
                    .setImageName(item.getFileCode())
                    .setImageType(item.getFileId())
                    .setImageUrl(item.getFilePath()));
        });

        return imageInfoList;
    }

    /**
     * 放款状态查询
     *
     * @param changYinPreLoanQueryDTOe
     * @return
     */
    @Override
    public ChangYinResBodyDTO<ChangYinPreLoanQueryResDTO> preLoanQueryV2(ChangYinPreLoanQueryDTO changYinPreLoanQueryDTOe) {

        return changYinClient.preLoanQueryV2(changYinPreLoanQueryDTOe);
    }

    /**
     * 身份证更新查询
     *
     * @return
     */
    @Override
    public ChangYinResBodyDTO<ChangYinIdCardUpdateQueryResDTO> idCardUpdateQuery(Integer orderId) {
        OrderIdCardUpdateEntity orderIdCardUpdateEntity = orderIdCardUpdateMapper.selectOne(new LambdaQueryWrapper<OrderIdCardUpdateEntity>()
                .eq(OrderIdCardUpdateEntity::getOrderId, orderId)
                .eq(OrderIdCardUpdateEntity::getDeleteFlag, 0)
        );
        if (ObjUtil.isNull(orderIdCardUpdateEntity)){
            throw new BusinessException("身份信息为空");
        }

        ChangYinIdCardUpdateQueryDTO changYinIdCardUpdateQueryDTO = new ChangYinIdCardUpdateQueryDTO();
        changYinIdCardUpdateQueryDTO.setMerchantNo(changYinConfig.getMerchantNo())
                .setStoreCode(changYinConfig.getStoreCode())
                .setTerminalType(changYinConfig.getTerminalType())
                .setOutApplyNo(orderIdCardUpdateEntity.getOutApplyNo());
        return changYinClient.idCardUpdateQuery(changYinIdCardUpdateQueryDTO);
    }

    /**
     * 身份证更新
     *
     * @param orderId
     * @return
     */
    @Override
    public ChangYinResBodyDTO<ChangYinIdCardUpdateApplyResDTO> idCardUpdateApply(Integer orderId) {
        log.info("ChangYinServiceImpl.idCardUpdateApply begin orderId: {}", orderId);
        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(
                new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .last("limit 1")
        );
        if (ObjUtil.isNull(finalFundInfoEntity)){
            throw new BusinessException("资方终审列表为空");
        }

        //生成更换卡号流水号 订单号+时间戳+随机数   记录流水号

        OrderIdCardUpdateEntity orderIdCardUpdateEntity = orderIdCardUpdateMapper.selectOne(new LambdaQueryWrapper<OrderIdCardUpdateEntity>()
                .eq(OrderIdCardUpdateEntity::getOrderId, orderId)
                .eq(OrderIdCardUpdateEntity::getDeleteFlag, 0)
        );
        if (ObjUtil.isNull(orderIdCardUpdateEntity)){
            throw new BusinessException("身份信息为空");
        }
        String outApplyNo = orderId + DateUtil.currentSeconds() + RandomUtil.randomNumbers(6);
        orderIdCardUpdateEntity.setOutApplyNo(outApplyNo);

        orderIdCardUpdateMapper.updateById(orderIdCardUpdateEntity);

        ChangYinIdCardUpdateApplyDTO changYinIdCardUpdateApplyDTO = new ChangYinIdCardUpdateApplyDTO();
        changYinIdCardUpdateApplyDTO.setMerchantNo(changYinConfig.getMerchantNo())
                .setStoreCode(changYinConfig.getStoreCode())
                .setTerminalType(changYinConfig.getTerminalType())
                .setOutApplyNo(outApplyNo)
                .setOutApplSeq(finalFundInfoEntity.getCreditNo())
                .setCustName(orderIdCardUpdateEntity.getCustName())
                .setIdNo(orderIdCardUpdateEntity.getIdNo())
                .setIdNoStartDate( DateUtil.format(orderIdCardUpdateEntity.getIdNoStartDate(), DatePattern.NORM_DATE_PATTERN) )
                .setIdNoEndDate(DateUtil.format(orderIdCardUpdateEntity.getIdNoEndDate(), DatePattern.NORM_DATE_PATTERN) );

        Result<List<FundResourceResultDTO>> fileResult = resourceFeign.fundResourceUpload(
                new FundResourceDTO().setFund(FundEnum.CHANG_YIN)
                        .setType(4).setLinkId(orderId));
        if (!Result.isSuccess(fileResult) || CollUtil.isEmpty(fileResult.getData())){
            throw new BusinessException("获取影像文件失败");
        }
        ChangYinIdCardUpdateApplyDTO.ImageInfo imageInfo = new ChangYinIdCardUpdateApplyDTO.ImageInfo();
        fileResult.getData().stream().forEach(item -> {
            Result<String> stringResult = resourceFeign.temporaryAccessRouteRequest(item.getResourceId(), 24);
            if (!Result.isSuccess(stringResult) || ObjUtil.isNotNull(stringResult.getData()) ){
                throw new BusinessException("生成临时url失败");
            }
            String url = stringResult.getData();
            if ("ID_CARD_UPDATE_FRONT".equals(item.getFileCode())){
               imageInfo.setPositive(url);
           }
           if ("ID_CARD_UPDATE_BACK".equals(item.getFileCode())){
               imageInfo.setNegative(url);
           }
           if ("FACE_IDENTIFY".equals(item.getFileCode())){
               imageInfo.setFaceImg(url);
           }
        });

        changYinIdCardUpdateApplyDTO.setImageInfo(imageInfo);
        log.info("ChangYinServiceImpl.idCardUpdateApply changYinIdCardUpdateApplyDTO: {}", changYinIdCardUpdateApplyDTO);
        return changYinClient.idCardUpdateApply(changYinIdCardUpdateApplyDTO);
    }

    /**
     * 卡号变更
     *
     * @param changYinUpdateAccountDTO
     * @return
     */
    @Override
    public ChangYinResBodyDTO<ChangYinAccountChangeResDTO> changeAccountApply(ChangYinUpdateAccountDTO changYinUpdateAccountDTO ) {
        log.info("ChangYinServiceImpl.changeAccountApply begin fundPreBaseDTO: {}", changYinUpdateAccountDTO);
        Integer orderId = changYinUpdateAccountDTO.getOrderId();

        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(
                new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .last("limit 1")
        );
        if (ObjUtil.isNull(finalFundInfoEntity)){
            throw new BusinessException("资方终审列表为空");
        }
        List<BankAccountSignEntity> signCard = fundApproveMapper.getSignCard(orderId, 6);
        if (CollUtil.isEmpty(signCard)) {
            throw new BusinessException("未获取到绑卡信息");
        }
        BankAccountSignEntity bankAccountSignEntity = signCard.get(0);

        ChangYinLoanApplicationDTO.AccInfo accRepayInfo = new ChangYinLoanApplicationDTO.AccInfo();
        accRepayInfo.setAcctKind(ChangYinDictEnum.AccountCategory.REPAYMENT_ACCOUNT.getCode());

        ChangYinAccountChangeInfoDTO changYinAccountChangeInfoDTO = new ChangYinAccountChangeInfoDTO();
        //生成放款流水号 订单号+时间戳+随机数
        String outLoanSeq = orderId + DateUtil.currentSeconds() + RandomUtil.randomNumbers(6);
        changYinAccountChangeInfoDTO.setOutAcctChgSeq(outLoanSeq)
                .setApplCde(finalFundInfoEntity.getCreditNo())
                .setLoanNo(finalFundInfoEntity.getLoanNo())
                .setIdTyp("20")
                .setIdNo(changYinUpdateAccountDTO.getIdCardNum())
                .setAcctName(changYinUpdateAccountDTO.getName())
                .setAcctKind("02")
                .setAcctTyp(ChangYinDictEnum.AccountType.PERSONAL_ACCOUNT.getCode())
                .setOldAcctBankCde(BankNameEnum.getCodeByName(bankAccountSignEntity.getBankName()))
                .setOldAcctNo(bankAccountSignEntity.getBankCardNumber())
                .setOldAcctPhone(bankAccountSignEntity.getPhone())
                .setNewAcctBankCde(BankNameEnum.getCodeByName(changYinUpdateAccountDTO.getBankName()))
                .setNewAcctNo(changYinUpdateAccountDTO.getBankCardNumber())
                .setNewAcctPhone(changYinUpdateAccountDTO.getPhone());
        return changYinClient.changeAccountApply(changYinAccountChangeInfoDTO);
    }

    @Override
    public ChangYinResBodyDTO<ChangYinLoanUploadResDTO> loanPurposeUpload(Integer orderId) {
        FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(
                new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, orderId)
                        .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .last("limit 1")
        );
        if (ObjUtil.isNull(finalFundInfoEntity)){
            throw new BusinessException("资方终审列表为空");
        }
        log.info("MortgageServiceImpl.loanUploadInfo finalFundInfoEntity:{}", finalFundInfoEntity);
        String stringByResourceId = null;
        Result<List<FundResourceResultDTO>> fileResult = resourceFeign.fundResourceUpload(
                new FundResourceDTO().setFund(FundEnum.CHANG_YIN)
                        .setType(4).setLinkId(orderId));
        if (!Result.isSuccess(fileResult) || CollUtil.isEmpty(fileResult.getData())){
            throw new BusinessException("获取影像文件失败");
        }
        stringByResourceId = fileResult.getData().get(0).getFilePath();
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        ChangYinLoanUploadDTO changYinLoanUploadDTO = new ChangYinLoanUploadDTO();
        changYinLoanUploadDTO.setMerchantNo(changYinConfig.getMerchantNo())
                .setStoreCode(changYinConfig.getStoreCode())
                .setTerminalType(changYinConfig.getTerminalType())
                .setFileContent(stringByResourceId)
                .setCustName(orderInfoEntity.getCustomerName())
                .setDealRefNo(finalFundInfoEntity.getLoanNo());
        return changYinClient.loanPurposeUpload(changYinLoanUploadDTO);
    }


    /**
     * 更新订单放款状态
     */
    @Override
    public void updateOrderPaymentStatus(ChangYinResBodyDTO<ChangYinPayHandlerDTO> resultDTO) {
        log.info("ChangYinPayServiceImpl.updateOrderPaymentStatus begin resultDTO:{}", JSONUtil.toJsonStr(resultDTO));

        FundNodeStatusEnum nextNodeStatus = null;
        ChangYinDictEnum.ChangYinConclusionStatusEnum fundStatus = null;
        String failReason = null;
        String remark = null;
        if (ChangYinResBodyDTO.isSuccess(resultDTO) || ObjUtil.isNotNull(resultDTO.getBody().getLoanSeq())) {
            FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(
                    Wrappers.<FinalFundInfoEntity>lambdaQuery()
                            .eq(FinalFundInfoEntity::getLoanReqNo, resultDTO.getBody().getOutLoanSeq())
                            .eq(FinalFundInfoEntity::getStatus, InitStatusEnums.SUCCESS)
                            .eq(FinalFundInfoEntity::getFundResult, PreFundResultEnum.PASS)
                            .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                            .orderByDesc(FinalFundInfoEntity::getCreateTime)
                    , false
            );
            Integer orderId = finalFundInfo.getOrderId();

            ChangYinDictEnum.ChangYinConclusionStatusEnum statusEnum = resultDTO.getBody().getDnSts();
            String loanNo = resultDTO.getBody().getLoanNo();
            if (ObjUtil.isNotNull(statusEnum)) {
                if (ObjUtil.equals(statusEnum, ChangYinDictEnum.ChangYinConclusionStatusEnum.PAYMENT_WAIT)) {
                    log.info("ChangYinPayServiceImpl.ChangYinResBodyDTO on payment ： {}", statusEnum);
                    fundStatus = ChangYinDictEnum.ChangYinConclusionStatusEnum.PAYMENT_WAIT;
                } else {
                    failReason = resultDTO.getHead().getRespMsg();
                    if (ObjUtil.equals(statusEnum, ChangYinDictEnum.ChangYinConclusionStatusEnum.PAYMENT_PASS)) {
                        nextNodeStatus = FundNodeStatusEnum.FUND_PAYMENT_SUCCESS;
                        updateOrderPaymentStatus(orderId, FundEnum.CHANG_YIN.getValue(), resultDTO.getBody().getDnAmt(),
                                resultDTO.getBody().getLoanActvTime(), failReason, FundPaymentStatusEnum.PASS, loanNo);
                        //更新还款计划
                        repaymentPlanInit(orderId,ChangYinDictEnum.RepaymentPlanQueryType.FULL_PLAN);
                    } else if (ObjUtil.equals(statusEnum, ChangYinDictEnum.ChangYinConclusionStatusEnum.PAYMENT_REJECT)) {
                        nextNodeStatus = FundNodeStatusEnum.FUND_PAYMENT_FAIL;
                        failReason = "拒绝原因:" + resultDTO.getBody().getPayMsg();
                        updateOrderPaymentStatus(orderId, FundEnum.CHANG_YIN.getValue(), null,
                                null,
                                failReason, FundPaymentStatusEnum.FAIL,loanNo);
                    }
                }
                remark = resultDTO.getHead().getRespMsg();
                if (StrUtil.isNotBlank(resultDTO.getBody().getPayMsg())) {
                    remark += "|" + resultDTO.getBody().getPayMsg();
                }
                if (nextNodeStatus != null) {
                    log.info("ChangYinPayServiceImpl.ChangYinResBodyDTO Updating next node status to: {}", nextNodeStatus);

                    finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                            .set(FinalFundInfoEntity::getNodeStatus, nextNodeStatus)
                            .set(FinalFundInfoEntity::getFundRemark, remark)
                            .set(FinalFundInfoEntity::getStatus, InitStatusEnums.SUCCESS)
                            .eq(FinalFundInfoEntity::getOrderId, orderId)
                            .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                            .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                    );
                }
            }
        }
    }


    private void updateOrderPaymentStatus(Integer orderId, Integer fundId, BigDecimal loanAmt,
                                          LocalDateTime payTime, String remark, FundPaymentStatusEnum fundPaymentStatusEnum,String loanNo) {

        log.info("ChangYinPayServiceImpl.updateOrderPaymentStatus begin orderId:{} fundId:{}", orderId, fundId);
        try {
            String failReason = remark + " 放款金额->" + loanAmt;

            // 设置要更新的字段
            finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .set(FinalFundInfoEntity::getRemark, failReason)
                    .set(FinalFundInfoEntity::getPaymentStatus, fundPaymentStatusEnum.getValue())
                    .set(FinalFundInfoEntity::getPaymentAmount, loanAmt)
                    .set(FinalFundInfoEntity::getLoanNo, loanNo)
                    .set(FinalFundInfoEntity::getPaymentTime, payTime)
                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                    .eq(FinalFundInfoEntity::getFundId, fundId));

            //放款单号

            OrderFundPaymentEnum orderFundPaymentEnum = OrderFundPaymentEnum.WAIT;
            if (FundPaymentStatusEnum.PASS.equals(fundPaymentStatusEnum)) {
                orderFundPaymentEnum = OrderFundPaymentEnum.PASS;
            } else if (FundPaymentStatusEnum.FAIL.equals(fundPaymentStatusEnum)) {
                orderFundPaymentEnum = OrderFundPaymentEnum.FAIL;
            }

            OrderApproveFundPaymentStatusDTO fundPaymentStatusDTO = new OrderApproveFundPaymentStatusDTO()
                    .setOrderId(orderId)
                    .setFundId(fundId)
                    .setLoanApplyNo(null)
                    .setLoanAmt(loanAmt)
                    .setLoanPayTime(payTime)
                    .setFailReason(remark)
                    .setStatus(orderFundPaymentEnum);

            orderFeign.updateFundPaymentStatus(fundPaymentStatusDTO);
            log.info("ChangYinPayServiceImpl.updateOrderPaymentStatus end orderId:{} fundId:{}", orderId, fundId);
        } catch (Exception e) {
            log.error("ChangYinPayServiceImpl.updateOrderPaymentStatus error e:{}", e.getMessage(), e);
            throw new BusinessException("更新订单放款状态异常");
        }
    }

    @Override
    public ChangYinResBodyDTO<ChangYinRepaymentPlanQueryResDTO> changYinRepaymentPlanQueryV2(Integer orderId,ChangYinDictEnum.RepaymentPlanQueryType enqTyp) {
        FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.CHANG_YIN);
        if (ObjUtil.isNull(finalFundInfo)){
            throw new BusinessException("该订单未查询到相关信息" + orderId );
        }
        ChangYinRepaymentPlanQueryDTO changYinRepaymentPlanQueryDTO = new ChangYinRepaymentPlanQueryDTO();
        changYinRepaymentPlanQueryDTO.setApplCde(finalFundInfo.getCreditNo())
                .setLoanSeq(finalFundInfo.getLoanBillNo())
                .setLoanNo(finalFundInfo.getLoanNo())
                .setEnqTyp(enqTyp.getCode());

        return changYinClient.repaymentPlanQueryV2(changYinRepaymentPlanQueryDTO);
    }

    /**
     * 下载资方合同文件
     *
     */
    @Override
    public List<ChangYinContractPreviewVO> downloadFundContract(Integer orderId) {
        log.info("ChangYinServiceImpl.downloadFundContract orderId:{}", orderId);
        try{
            CustomerBaseDTO customerFinalBaseInfo = fundApproveMapper.getCustomerFinalBaseInfo(orderId);
            log.info("ChangYinServiceImpl.downloadFundContract orderId:{} customerFinalBaseInfo:{}", orderId, JSONUtil.toJsonStr(customerFinalBaseInfo));
            FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.CHANG_YIN);
            if (ObjUtil.isNull(finalFundInfo)){
                throw new BusinessException("该订单申请信息不存在");
            }
            FundResourceDTO fundResourceDTO = new FundResourceDTO();
            fundResourceDTO.setLinkId(orderId);
            fundResourceDTO.setFund(FundEnum.CHANG_YIN);
            fundResourceDTO.setType(5);
            fundResourceDTO.setCustomerBaseInfo(new FundResourceDTO.CustomerBaseInfo().setCustomerIdNo(customerFinalBaseInfo.getIdNumber()));
            fundResourceDTO.setFundBaseInfo(new FundResourceDTO.FundBaseInfo().setLoanSeq(finalFundInfo.getLoanBillNo()));

            Result<List<FundResourceResultDTO>> resourceDownload = resourceFeign.fundResourceDownload(fundResourceDTO);
            if (!Result.isSuccess(resourceDownload)) {
                throw new BusinessException("下载合同文件异常");
            }

            List<FundResourceResultDTO> data = resourceDownload.getData();
            if (CollUtil.isEmpty(data)) {
                throw new BusinessException("下载合同文件为空");
            }
            String filePath = data.get(0).getFilePath();
            log.info("ChangYinServiceImpl.downloadFundContract filePath:{}", filePath);
            List<ChangYinContractPreviewVO> changYinContractPreviewVOList = new ArrayList<>();
            for (FundResourceResultDTO item : data) {
                ChangYinContractPreviewVO changYinContractPreviewVO = new ChangYinContractPreviewVO();
                changYinContractPreviewVO.setResourceId(item.getResourceId());

                ContractTypeMapping mapping = ContractTypeMapping.getByFileCode(item.getFileCode());
                if (mapping != null) {
                    changYinContractPreviewVO.setTemplateId(mapping.getContractType().getTemplateId());
                    changYinContractPreviewVO.setFileName(mapping.getContractType().getDescription());
                }

                changYinContractPreviewVOList.add(changYinContractPreviewVO);
            }

            return changYinContractPreviewVOList;
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.downloadFundContract error e:{}", e.getMessage(), e);
            throw new BusinessException("下载合同文件异常");
        }
    }

    @Override
    public void repaymentPlanInit(Integer orderId,ChangYinDictEnum.RepaymentPlanQueryType enqTyp) {
        log.info("ChangYinServiceImpl.repaymentPlanInit orderId:{}", orderId);
        ChangYinResBodyDTO<ChangYinRepaymentPlanQueryResDTO> result = changYinRepaymentPlanQueryV2(orderId, enqTyp);
        if (ChangYinResBodyDTO.isSuccess(result)){
            List<ChangYinRepaymentPlanQueryResDTO.RepaymentPlan> repaymentPlanList = result.getBody().getRepaymentPlanList();
            String intStartDate = result.getBody().getIntStartDate();
            if (StrUtil.isEmpty(intStartDate)){
                log.error("ChangYinServiceImpl.repaymentPlanInit 该订单为放款成功 orderId:{} ", orderId);
                return;
            }
            if (CollUtil.isNotEmpty(repaymentPlanList)) {

                repaymentPlanList.forEach(plan -> updateRepaymentPlan(orderId, plan));

                //更新订单还款状态
                try {
                    OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
                    orderApproveFundPlanStatusDTO.setOrderId(orderId);
                    orderFeign.updateFundPlanStatus(orderApproveFundPlanStatusDTO);
                } catch (Exception e) {
                    log.error("ChangYinServiceImpl.repaymentPlanInit orderId:{}", orderId);
                }
                try {
                    orderFeign.updateOrderFundRepayment(orderId);
                } catch (Exception e) {
                    log.error("ChangYinServiceImpl.repaymentPlanInit orderId:{}", orderId);
                }
            } else {
                log.warn("ChangYinServiceImpl.repaymentPlanInit orderId:{}, planList is empty", orderId);
            }
        }
    }


    /**
     * 更新还款计划表应还
     *
     */
    @Override
    public void repaymentPlanRepay(Integer orderId, ChangYinDictEnum.RepaymentPlanQueryType enqTyp) {
        log.info("ChangYinServiceImpl.repaymentPlanRepay orderId:{}", orderId);
        ChangYinResBodyDTO<ChangYinRepaymentPlanQueryResDTO> result = changYinRepaymentPlanQueryV2(orderId, enqTyp);
        if (ChangYinResBodyDTO.isSuccess(result)){
            List<ChangYinRepaymentPlanQueryResDTO.RepaymentPlan> repaymentPlanList = result.getBody().getRepaymentPlanList();
            String intStartDate = result.getBody().getIntStartDate();
            if (StrUtil.isEmpty(intStartDate)){
                log.error("ChangYinServiceImpl.repaymentPlanRepay 该订单为放款成功 orderId:{} ", orderId);
                return;
            }
            if (CollUtil.isNotEmpty(repaymentPlanList)) {

                repaymentPlanList.forEach(plan -> updateRepaymentPlanRepay(orderId, plan));

                //更新订单还款状态
                try {
                    OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
                    orderApproveFundPlanStatusDTO.setOrderId(orderId);
                    orderFeign.updateFundPlanStatus(orderApproveFundPlanStatusDTO);
                } catch (Exception e) {
                    log.error("ChangYinServiceImpl.repaymentPlanRepay orderId:{}", orderId);
                }
                try {
                    orderFeign.updateOrderFundRepayment(orderId);
                } catch (Exception e) {
                    log.error("ChangYinServiceImpl.repaymentPlanRepay  orderId:{}", orderId);
                }
            } else {
                log.warn("ChangYinServiceImpl.repaymentPlanRepay  orderId:{}, planList is empty", orderId);
            }
        }
    }

    public void updateRepaymentPlan(Integer orderId, ChangYinRepaymentPlanQueryResDTO.RepaymentPlan plan) {
        Integer term = plan.getPerdNo();
        log.info("ChangYinServiceImpl.updateRepaymentPlan orderId:{}, term:{} plan:{}", orderId, term, plan);

        FundRepaymentInfoEntity repaymentInfoEntity = new FundRepaymentInfoEntity();
        repaymentInfoEntity.setOrderId(orderId);
        repaymentInfoEntity.setFundId(FundEnum.CHANG_YIN.getValue());
        repaymentInfoEntity.setTerm(term);
        repaymentInfoEntity.setRepaymentDate(LocalDate.parse(plan.getDueDt()));

        BigDecimal repaymentTotal = plan.getPsPrcpAmt()
                .add(plan.getPsNormIntAmt())
                .add(plan.getPsOdIntAmt())
                .add(plan.getPsCommOdInt())
                .add(plan.getPsFeeAmt())
                .add(plan.getGuaraFeeAmt())
                .add(plan.getGuaraFeeOdAmt())
                ;

        repaymentInfoEntity.setRepaymentAmountTotal(repaymentTotal);



        repaymentInfoEntity.setRepaymentPrincipal(plan.getPsPrcpAmt());
        repaymentInfoEntity.setRepaymentInterest(plan.getPsNormIntAmt());
        repaymentInfoEntity.setRepaymentPenaltyInterest(ObjUtil.isNotNull(plan.getPsOdIntAmt()) ? plan.getPsOdIntAmt() : BigDecimal.ZERO );

        repaymentInfoEntity.setRepaymentPsCommOdAmount(Convert.toBigDecimal(plan.getPsCommOdInt(), BigDecimal.ZERO));
        repaymentInfoEntity.setRepaymentPsFeeAmount(Convert.toBigDecimal(plan.getPsFeeAmt(), BigDecimal.ZERO));
        repaymentInfoEntity.setRepaymentGuaraFeeAmount(Convert.toBigDecimal(plan.getGuaraFeeAmt(), BigDecimal.ZERO));
        repaymentInfoEntity.setRepaymentGuaraFeeOdAmount(Convert.toBigDecimal(plan.getGuaraFeeOdAmt(), BigDecimal.ZERO));


        repaymentInfoEntity.setActuallyDate(null);
        repaymentInfoEntity.setActuallyCommOdAmount(BigDecimal.ZERO);
        repaymentInfoEntity.setActuallyFeeAmount(BigDecimal.ZERO);
        repaymentInfoEntity.setActuallyGuaraFeeAmount(BigDecimal.ZERO);
        repaymentInfoEntity.setActuallyGuaraFeeOdAmount(BigDecimal.ZERO);

        repaymentInfoEntity.setActuallyPrincipal(BigDecimal.ZERO);
        repaymentInfoEntity.setActuallyInterest(BigDecimal.ZERO);
        repaymentInfoEntity.setActuallyPenaltyInterest(BigDecimal.ZERO);
        repaymentInfoEntity.setActuallyPenalty(BigDecimal.ZERO);
        // 总已还金额 = 已还本金 + 已还利息 + 已还罚息 + 已还费用 + 已还复利 + 已还担保费 + 已还担保费罚息
        repaymentInfoEntity.setActuallyAmountTotal(BigDecimal.ZERO);
        //还款状态判断
        FundRepayStatusEnum fundRepayStatusEnum =  checkReStatus(plan);
        repaymentInfoEntity.setRepaymentStatus(fundRepayStatusEnum);

        boolean isOverdue = false;
        if (ObjUtil.equals("Y",plan.getPsOdInd())) {
            isOverdue = true;
        }
        else {
            //设置还款状态、是否逾期
            isOverdue = determineOverdue(repaymentInfoEntity.getRepaymentAmountTotal(), repaymentInfoEntity.getActuallyAmountTotal(),
                    LocalDate.now(), repaymentInfoEntity.getRepaymentDate(), repaymentInfoEntity.getActuallyDate());
        }

        repaymentInfoEntity.setIsOverdue(isOverdue ? 1 : 0);
        Long count = fundRepaymentInfoMapper.selectCount(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                .eq(FundRepaymentInfoEntity::getTerm, term)
                .eq(FundRepaymentInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
        );
        if (Objects.equals(count, 0L)) {
            fundRepaymentInfoMapper.insert(repaymentInfoEntity);
        } else {
            fundRepaymentInfoMapper.update(repaymentInfoEntity, new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
                    .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                    .eq(FundRepaymentInfoEntity::getTerm, term)
                    .eq(FundRepaymentInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                    .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
            );
        }

    }


    /**
     * 更新还款计划表应还
     */
    public void updateRepaymentPlanRepay(Integer orderId, ChangYinRepaymentPlanQueryResDTO.RepaymentPlan plan) {
        Integer term = plan.getPerdNo();
        log.info("ChangYinServiceImpl.updateRepaymentPlanRepay orderId:{}, term:{} plan:{}", orderId, term, plan);

        FundRepaymentInfoEntity repaymentInfoEntity = new FundRepaymentInfoEntity();
        repaymentInfoEntity.setOrderId(orderId);
        repaymentInfoEntity.setFundId(FundEnum.CHANG_YIN.getValue());
        repaymentInfoEntity.setTerm(term);
        repaymentInfoEntity.setRepaymentDate(LocalDate.parse(plan.getDueDt()));

        BigDecimal repaymentTotal = plan.getPsPrcpAmt()
                .add(plan.getPsNormIntAmt())
                .add(plan.getPsOdIntAmt())
                .add(plan.getPsCommOdInt())
                .add(plan.getPsFeeAmt())
                .add(plan.getGuaraFeeAmt())
                .add(plan.getGuaraFeeOdAmt())
                ;

        repaymentInfoEntity.setRepaymentAmountTotal(repaymentTotal);


        repaymentInfoEntity.setActuallyDate(plan.getLastSetlDt());

        repaymentInfoEntity.setRepaymentPrincipal(plan.getPsPrcpAmt());
        repaymentInfoEntity.setRepaymentInterest(plan.getPsNormIntAmt());
        repaymentInfoEntity.setRepaymentPenaltyInterest(ObjUtil.isNotNull(plan.getPsOdIntAmt()) ? plan.getPsOdIntAmt() : BigDecimal.ZERO );

        repaymentInfoEntity.setRepaymentPsCommOdAmount(Convert.toBigDecimal(plan.getPsCommOdInt(), BigDecimal.ZERO));
        repaymentInfoEntity.setRepaymentPsFeeAmount(Convert.toBigDecimal(plan.getPsFeeAmt(), BigDecimal.ZERO));
        repaymentInfoEntity.setRepaymentGuaraFeeAmount(Convert.toBigDecimal(plan.getGuaraFeeAmt(), BigDecimal.ZERO));
        repaymentInfoEntity.setRepaymentGuaraFeeOdAmount(Convert.toBigDecimal(plan.getGuaraFeeOdAmt(), BigDecimal.ZERO));


        //还款状态判断
        FundRepayStatusEnum fundRepayStatusEnum =  checkReStatus(plan);
        repaymentInfoEntity.setRepaymentStatus(fundRepayStatusEnum);

        boolean isOverdue = false;
        if (ObjUtil.equals(FundRepayStatusEnum.OVERDUE, fundRepayStatusEnum)) {
            isOverdue = true;
        }
        else {
            //设置还款状态、是否逾期
            isOverdue = determineOverdue(repaymentInfoEntity.getRepaymentAmountTotal(), repaymentInfoEntity.getActuallyAmountTotal(),
                    LocalDate.now(), repaymentInfoEntity.getRepaymentDate(), repaymentInfoEntity.getActuallyDate());
        }

        repaymentInfoEntity.setIsOverdue(isOverdue ? 1 : 0);
        Long count = fundRepaymentInfoMapper.selectCount(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                .eq(FundRepaymentInfoEntity::getTerm, term)
                .eq(FundRepaymentInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
        );
        if (Objects.equals(count, 0L)) {
            fundRepaymentInfoMapper.insert(repaymentInfoEntity);
        } else {
            fundRepaymentInfoMapper.update(repaymentInfoEntity, new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
                    .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                    .eq(FundRepaymentInfoEntity::getRepaymentPrincipal, repaymentInfoEntity.getRepaymentPrincipal())
                    .eq(FundRepaymentInfoEntity::getTerm, term)
                    .eq(FundRepaymentInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                    .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
            );
        }

    }




    private boolean determineOverdue(BigDecimal repaymentAmountTotal, BigDecimal actuallyAmountTotal, LocalDate currentDate, LocalDate repaymentDate, LocalDate actuallyDate) {
        return actuallyDate != null && ObjUtil.defaultIfNull(actuallyAmountTotal, BigDecimal.ZERO).compareTo(repaymentAmountTotal) >= 0 ? actuallyDate.isAfter(repaymentDate) : currentDate.isAfter(repaymentDate);

    }

    private FundRepayStatusEnum checkReStatus(ChangYinRepaymentPlanQueryResDTO.RepaymentPlan plan) {
        // 1. 已结清状态优先级最高
        if ("Y".equals(plan.getSetlInd())) {
            return FundRepayStatusEnum.SETTLED;
        }

        // 2. 逾期标志判断
        if ("Y".equals(plan.getPsOdInd())) {
            return FundRepayStatusEnum.OVERDUE;
        }

        // 3. 计算总已还金额
        BigDecimal totalRepaid = Optional.ofNullable(plan.getSetlPrcp()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(plan.getSetlNormInt()).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(plan.getSetlOdIntAmt()).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(plan.getSetlFeeAmt()).orElse(BigDecimal.ZERO));

        // 4. 部分还款判断
        if (totalRepaid.compareTo(BigDecimal.ZERO) > 0
                && totalRepaid.compareTo(plan.getPsInstmAmt()) < 0) {
            return FundRepayStatusEnum.PART_RETURN;
        }

        // 5. 默认未还状态
        return FundRepayStatusEnum.NONE;
    }

    @Override
    public ChangYinResBodyDTO<ChangLPRResDTO> queryLPR(ChangYinLPRDTO changYinLPRDTO) {
        return changYinClient.queryLPR(changYinLPRDTO);
    }

    /**
     * 还款试算
     * @param orderId
     * @return
     */
    @Override
    public ChangYinResBodyDTO<ChangYinRepaymentTrialResDTO> repaymentTrial(Integer orderId,ChangYinDictEnum.RepaymentMode changYinRepayType ,Integer term) {

        FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.CHANG_YIN);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getId, orderId)
                .eq(OrderInfoEntity::getDeleteFlag, 0));

        if (ObjUtil.isNull(finalFundInfo) || ObjUtil.isNull(orderInfoEntity)){
            throw new BusinessException("该订单申请信息不存在");
        }

        ChangYinRepaymentTrialDTO changYinRepaymentTrialDTO = new ChangYinRepaymentTrialDTO();
        changYinRepaymentTrialDTO.setApplCde(finalFundInfo.getCreditNo());
        changYinRepaymentTrialDTO.setLoanNo(finalFundInfo.getLoanNo());
        changYinRepaymentTrialDTO.setRepaymentMode(changYinRepayType.getCode());
        changYinRepaymentTrialDTO.setOperateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (ObjUtil.isNotNull(term)){
            changYinRepaymentTrialDTO.setPeriod(term.toString());
        }
        return changYinClient.repaymentTrial(changYinRepaymentTrialDTO);
    }

    /**
     * 还款申请
     * @param orderId
     */
    @Override
    public ChangYinResBodyDTO<ChangYinRepaymentApplyResDTO> repaymentApply(Integer orderId) {
        OrderAmountCalDTO orderAmount = fundBaseInfoService.getApplyAmountByOrderId(orderId);

        FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.CHANG_YIN);
        FundRepaymentInfoEntity fundRepaymentInfoEntity = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                .eq(FundRepaymentInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .between(FundRepaymentInfoEntity::getRepaymentDate, LocalDate.now().withDayOfMonth(1), LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth()))
        );
        List<BankAccountSignEntity> signCard = fundApproveMapper.getSignCard(orderId, 6);
        if (CollUtil.isEmpty(signCard)) {
            throw new BusinessException("未获取到绑卡信息");
        }
        BankAccountSignEntity bankAccountSignEntity = signCard.get(0);

        ChangYinLoanApplicationDTO.AccInfo accInfo = new ChangYinLoanApplicationDTO.AccInfo();
        accInfo.setAcctKind(ChangYinDictEnum.AccountCategory.LOAN_ACCOUNT.getCode());
        accInfo.setAcctTyp(ChangYinDictEnum.AccountType.PERSONAL_ACCOUNT.getCode());
        accInfo.setAcctBankCode(BankNameEnum.getCodeByName(bankAccountSignEntity.getBankName()));
        accInfo.setAcctNo(bankAccountSignEntity.getBankCardNumber());
        accInfo.setAcctName(bankAccountSignEntity.getName());
        accInfo.setIdTyp(ChangYinDictEnum.IdentificationType.ID_CARD.getCode());
        accInfo.setIdNo(bankAccountSignEntity.getIdCardNum());
        accInfo.setAcctPhone(bankAccountSignEntity.getPhone());
        //生成放款流水号 订单号+时间戳+随机数
        String outRepaySeq = orderId + DateUtil.currentSeconds() + RandomUtil.randomNumbers(6);
        ChangYinRepaymentApplyDTO changYinRepaymentApplyDTO = new ChangYinRepaymentApplyDTO();
        changYinRepaymentApplyDTO.setMerchantNo(changYinConfig.getMerchantNo())
                .setStoreCode(changYinConfig.getStoreCode())
                .setTerminalType(changYinConfig.getTerminalType())
                .setOutRepaySeq(outRepaySeq)
                .setFinanceChannel("01")
                .setPaymInd("Y") //TODO不确定
//                .setPayChannel()
                .setRepayDate(String.valueOf(fundRepaymentInfoEntity.getRepaymentDate()))
                .setNeedAct("Y")
                .setAmount(fundRepaymentInfoEntity.getRepaymentAmountTotal())
                .setRepayInfoList( new ChangYinRepaymentApplyDTO.RepayInfoList()
                        .setDealRefNo(finalFundInfo.getLoanReqNo())
                        .setRepayType(fundRepaymentInfoEntity.getIsOverdue() == 0 ? "03" : "01")
                        .setRepayAmount(fundRepaymentInfoEntity.getRepaymentAmountTotal()))
                .setRepayBankList(new ChangYinRepaymentApplyDTO.RepayBankList()
                        .setDealRefNo(finalFundInfo.getLoanReqNo())
                        .setPayerAcctNo(bankAccountSignEntity.getSignProtocolNo())
                        .setPayerName(bankAccountSignEntity.getName())
                        .setBankCode(BankNameEnum.getCodeByName(bankAccountSignEntity.getBankName()))
                        .setMobile(bankAccountSignEntity.getPhone())
                        .setBankName(bankAccountSignEntity.getBankName()));

        return changYinClient.repaymentApply(changYinRepaymentApplyDTO);
    }

    @Override
    public ChangYinResBodyDTO<ChangYinCustRepayAccountQueryResDTO> custRepayAccountQuery(Integer orderId) {
        FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.CHANG_YIN);
        if (ObjUtil.isNull(finalFundInfo)){
            throw new BusinessException("订单终审信息不存在");
        }
        ChangYinCustRepayAccountQueryDTO changYinCustRepayAccountQueryDTO = new ChangYinCustRepayAccountQueryDTO();
        changYinCustRepayAccountQueryDTO.setMerchantNo(changYinConfig.getMerchantNo())
                .setStoreCode(changYinConfig.getStoreCode())
                .setTerminalType(changYinConfig.getTerminalType())
                .setDealRefNo(finalFundInfo.getLoanNo());
        return changYinClient.custRepayAccountQuery(changYinCustRepayAccountQueryDTO);
    }


    @Override
    public void syncRepaymentUpdateBatch(LocalDate fixedUpdatePlanDate) {
        log.info("ChangYinServiceImpl.syncRepaymentUpdateBatch begin fixedUpdatePlanDate: {}", fixedUpdatePlanDate);
        if (ObjUtil.isNull(fixedUpdatePlanDate)) {
            log.info("ChangYinServiceImpl.syncRepaymentUpdateBatch: fixedUpdatePlanDate is null, using current date");
            fixedUpdatePlanDate = LocalDate.now().minusDays(1);
        }
        //获取文件路径
        String filePath = getRepaymentPlanSftpPath(fixedUpdatePlanDate,FundConstant.CHANG_YIN_PAYMENT_PLAN_UPDATE_TYPE);
        String fileName = getRepaymentPlanSftpFileName(fixedUpdatePlanDate,FundConstant.CHANG_YIN_PAYMENT_PLAN_UPDATE_TYPE);
        log.info("ChangYinServiceImpl.syncRepaymentUpdateBatch: File path: {}", filePath);

        // 下载文件
        FileVO fileVO = downloadAndParseFile(filePath, fileName);
        if (fileVO != null) {
            String resourceId = fileVO.getResourceId();
            String resourceName = fileVO.getResourceName();
            //下载成功
            //解析还款文件
            ParseCsvDTO parseCsvDTO = new ParseCsvDTO();
            parseCsvDTO.setTitleLine(1);
            parseCsvDTO.setFileUidList(List.of(resourceId));
            Result<Map<String, List<List<String>>>> mapResult = resourceFeign.parseCsv(parseCsvDTO);
            if (!Result.isSuccess(mapResult)) {
                log.error("ChangYinServiceImpl.syncRepaymentUpdateBatch: parseCsv fail, result: {}", mapResult);
                return;
            }
            List<List<String>> dataList = mapResult.getData().get(resourceId);

            List<ChangYinCsvRepayDTO> changYinCsvRepayDTOS = convertToChangYinCsvRepayDTO(dataList);
            log.info("ChangYinServiceImpl.syncRepaymentUpdateBatch: changYinCsvRepayDTOS: {}", changYinCsvRepayDTOS);
            handleRepaymentUpdateBatch(changYinCsvRepayDTOS, fileName, fileVO);


            log.info("ChangYinServiceImpl.syncRepaymentUpdateBatch: fileVO: {}", fileVO);
        }
    }


    /**
     * 放款明细文件同步
     *
     */
    @Override
    public void syncPaymentDetailFileBatch(LocalDate fixedUpdatePlanDate) {
        log.info("ChangYinServiceImpl.syncPaymentDetailFileBatch begin fixedUpdatePlanDate: {}", fixedUpdatePlanDate);
        if (ObjUtil.isNull(fixedUpdatePlanDate)) {
            log.info("ChangYinServiceImpl.syncPaymentDetailFileBatch: fixedUpdatePlanDate is null, using current date");
            fixedUpdatePlanDate = LocalDate.now().minusDays(1);
        }
        //获取文件路径
        String filePath = getRepaymentPlanSftpPath(fixedUpdatePlanDate,FundConstant.CHANG_YIN_PAYMENT_DETAIL_TYPE);
        String fileName = getRepaymentPlanSftpFileName(fixedUpdatePlanDate,FundConstant.CHANG_YIN_PAYMENT_DETAIL_TYPE);
        log.info("ChangYinServiceImpl.syncPaymentDetailFileBatch: File path: {}", filePath);

        // 下载文件
        FileVO fileVO = downloadAndParseFile(filePath, fileName);
        if (fileVO != null) {
            //下载成功
            //根据日期查询放款订单
            List<OrderInfoEntity> orderInfoList = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
                    .ge(OrderInfoEntity::getCurrentNode, 5000)
                    .eq(OrderInfoEntity::getPaymentTime, fixedUpdatePlanDate)
                    .eq(OrderInfoEntity::getDeleteFlag, 0)
            );
            if (CollUtil.isEmpty(orderInfoList)) {
                log.info("ChangYinServiceImpl.syncPaymentDetailFileBatch: No orders found for the specified date");
                return;
            }
            List<Integer> orderIdList = orderInfoList.stream().map(OrderInfoEntity::getId).toList();
            log.info("ChangYinServiceImpl.syncPaymentDetailFileBatch: orderIdList: {}", orderIdList);
            Integer fileConfigId = getFileConfigId(FundConstant.LOAN_DETAIL);

            if (ObjUtil.isNotNull(fileConfigId)) {
                BatchSaveOrderFileDTO orderFileDTO = new BatchSaveOrderFileDTO();
                orderFileDTO.setFileId(fileConfigId);
                orderFileDTO.setOrderIdList(List.of(fileConfigId));
                orderFileDTO.setFileName(fileName);
                orderFileDTO.setResourceName(fileVO.getResourceName());
                orderFileDTO.setResourceId(fileVO.getResourceId());
                orderFeign.batchOrderFile(orderFileDTO);
            }
        }
    }

    /**
     * 将CSV数据列表转换为ChangYinCsvRepayDTO列表
     * @param dataList CSV数据(二维列表)
     * @return List<ChangYinCsvRepayDTO>
     */
    private List<ChangYinCsvRepayDTO> convertToChangYinCsvRepayDTO(List<List<String>> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        List<ChangYinCsvRepayDTO> result = new ArrayList<>();

        for (List<String> row : dataList) {

            try {
                ChangYinCsvRepayDTO dto = new ChangYinCsvRepayDTO()
                        .setLoanNo(StrUtil.trimToNull(row.get(0)))  // loan_no
                        .setRequestNo(StrUtil.trimToNull(row.get(1)))  // repayment_seq
                        .setSetlSeq(StrUtil.trimToNull(row.get(2)))  // setl_seq
                        .setOutRequestSeq(StrUtil.trimToNull(row.get(3)))  // out_appl_seq
                        .setCustId(StrUtil.trimToNull(row.get(4)))  // cust_Id
                        .setPayMode(StrUtil.trimToNull(row.get(5)))  // pay_mode
                        .setSetlDt(Convert.toLocalDateTime(row.get(6)).toLocalDate())  // setl_dt
                        .setSetlTime(Convert.convert(LocalDateTime.class, row.get(7)))  // setl_time
                        .setIsCy(StrUtil.trimToNull(row.get(8)))  // is_cy
                        .setTotalAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(9))))  // total_amt
                        .setPrcpAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(10))))  // prcp_amt
                        .setIntAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(11))))  // int_amt
                        .setOdIntAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(12))))  // od_int_amt
                        .setCommOdIntAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(13))))  // comm_od_int_amt
                        .setFeeAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(14))))  // fee_amt
                        .setCurrPrincipal(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(15))))  // curr_principal
                        .setIsDc(StrUtil.trimToNull(row.get(16)))  // is_dc
                        .setGuaranteeFeeAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(17))))  // guarantee_fee_amt
                        .setGuaranteeFeeOdAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(18))))  // guarantee_fee_od_amt
                        .setPlatformFlowNo(StrUtil.trimToNull(row.get(19)));  // platform_flow_no

                result.add(dto);
            } catch (Exception e) {
                log.error("转换CSV行数据失败，行数据: {}", row, e);
            }
        }

        return result;
    }
    /**
     * 获取文件配置ID
     *
     * @param code 文件配置code
     * @return 文件配置ID
     */
    private Integer getFileConfigId(String code) {
        Result<Integer> result = resourceFeign.selectFileConfigByCode(code);
        if (Result.isSuccess(result)) {
            return result.getData();
        }
        return null;
    }


    /**
     * 处理还款计划更新
     */
    private void handleRepaymentUpdateBatch(List<ChangYinCsvRepayDTO> repayDTOList, String fileName, FileVO fileVO) {
        if (CollUtil.isEmpty(repayDTOList)) {
            log.info("ChangYinServiceImpl.syncPaymentDetailFileBatch: No repayment data found fileVo:{}", JSONUtil.toJsonStr(fileVO));
            return;
        }
        Integer fileConfigId = getFileConfigId(FundConstant.REPAYMENT_PLAN_UPDATE_FILE_CONFIG_CODE);

        //1.根据借据号分组处理
        Map<String, List<ChangYinCsvRepayDTO>> repayDTOMap = repayDTOList.stream()
                .collect(Collectors.groupingBy(ChangYinCsvRepayDTO::getLoanNo));

        //2.根据借据号查询订单号
        List<FinalFundInfoEntity> finalFundInfoByLoanNoList = finalFundInfoMapper.selectList(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .in(FinalFundInfoEntity::getLoanNo, repayDTOMap.keySet())
                .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );
        //3.根据解决号转为map<借据号,订单id> 遇到键重复使用最新
        Map<String, Integer> loanNoOrderIdMap = finalFundInfoByLoanNoList.stream()
                .collect(Collectors.toMap(FinalFundInfoEntity::getLoanNo, FinalFundInfoEntity::getOrderId, (v1, v2) -> v2));

        Map<String, FinalFundInfoEntity> loanNoFinalFundInfoMap = finalFundInfoByLoanNoList.stream()
                .collect(Collectors.toMap(
                        FinalFundInfoEntity::getLoanNo,
                        Function.identity(),
                        (v1, v2) -> v2
                ));


        //过滤已经处理的订单
        List<OrderFileEntity> orderFileList = orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                .select(OrderFileEntity::getOrderId)
                .eq(OrderFileEntity::getFileName, fileName)
                .eq(OrderFileEntity::getFileId, fileConfigId)
                .in(OrderFileEntity::getOrderId, loanNoOrderIdMap.values())
                .eq(OrderFileEntity::getDeleteFlag, 0)
        );
        //orderFileList 转为 orderId 集合
        Set<Integer> orderIdSet = orderFileList.stream().map(OrderFileEntity::getOrderId).collect(Collectors.toSet());


        //处理结清的订单id集合
        Set<Integer> settledOrderIdSet = new HashSet<>();

        //处理的订单Id集合
        Set<Integer> handledOrderIdSet = new HashSet<>();

        //还款流水号集合
        List<String> deductNoList = new ArrayList<>();


        repayDTOMap.forEach((loanNo,csvRepayDTOList)->{
            //4.更新还款计划
            Integer orderId = loanNoOrderIdMap.get(loanNo);

            //5.过滤已经处理的订单
            if (orderIdSet.contains(orderId)) {
                return;
            }

            //根据csvRepayDTOList.setlTime 升序处理
            csvRepayDTOList.sort(Comparator.comparing(ChangYinCsvRepayDTO::getSetlDt));
            try {
                csvRepayDTOList.forEach(plan -> {
                    // 根据还款类型确定查询条件
                    String payMode = plan.getPayMode();
                    FundDeductRepayTypeEnums fundDeductRepayType;

                    switch (payMode) {
                        case "01":  // 正常还款  只能还款1期 可能有还款逾期的情况
                            fundDeductRepayType = FundDeductRepayTypeEnums.NORMAL_REPAYMENT;
                            break;
                        case "02":  // 提前结清
                            fundDeductRepayType = FundDeductRepayTypeEnums.EARLY_SETTLEMENT;

                            break;
                        case "04":  // 逾期还款
                            fundDeductRepayType = FundDeductRepayTypeEnums.OVERDUE_REPAYMENT;
                            break;
                        case "06":  // 提前还一期
                            fundDeductRepayType = FundDeductRepayTypeEnums.ARLY_REPAYMENT;
                            break;
                        default:
                            // 处理未知的还款模式
                            log.info("ChangYinServiceImpl.handleRepaymentUpdateBatch unknown payMode: {}", payMode);
                            return;
                    }
                    //处理还款计划
                    BigDecimal actuallyTotal = ObjUtil.defaultIfNull(plan.getTotalAmt(), BigDecimal.ZERO)
                            .add(ObjUtil.defaultIfNull(plan.getGuaranteeFeeAmt(), BigDecimal.ZERO))
                            .add(ObjUtil.defaultIfNull(plan.getGuaranteeFeeOdAmt(), BigDecimal.ZERO));
                    //更新还款计划
                    updateQueryPlanSettle(loanNoFinalFundInfoMap.get(loanNo));


                    //根据
                    LambdaQueryWrapper<FundRepaymentInfoEntity> fundRepaymentInfoLqw = new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                            .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                            .eq(FundRepaymentInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                            .eq(FundRepaymentInfoEntity::getActuallyDate, plan.getSetlDt())
                            .orderByDesc(FundRepaymentInfoEntity::getTerm)
                            .last("limit 1")
                            ;
                    FundRepaymentInfoEntity fundRepaymentInfo = fundRepaymentInfoMapper.selectOne(fundRepaymentInfoLqw);
                    log.info("ChangYinServiceImpl.handleRepaymentUpdateBatch - fundRepaymentInfo: {}", fundRepaymentInfo);


                    //保存还款记录
                    String requestNo = plan.getRequestNo();
                    LocalDate repayDate = plan.getSetlDt();

                    String failReason = StrUtil.equals(plan.getIsCy(), "Y") ? "长银扣款" : "非长银扣款";
                    if (StrUtil.equals(plan.getIsDc(), "Y")) {
                        failReason += " 代偿 ";
                    }
                    fundRepaymentDeductService.saveRepaymentDeductInfo(requestNo, 1, fundDeductRepayType,
                            fundRepaymentInfo.getTerm(), orderId, FundEnum.CHANG_YIN.getValue(), repayDate,
                            JSONUtil.toJsonStr(plan), null,
                            FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                            FundDeductRepayStatusEnums.REPAYMENT_SUCCESS,
                            failReason, actuallyTotal
                    );


                    OrderFeeDetailExpandTypeEnum expandType = ObjUtil.equals(fundDeductRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT) ?
                            OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT : OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT;
                    saveFeeDetail(fundRepaymentInfo.getTerm(), orderId, "fund_repay_"+requestNo, actuallyTotal, expandType);

                    //更新订单还款状态
                    OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
                    orderApproveFundPlanStatusDTO.setOrderId(orderId);
                    orderFeign.updateFundPlanStatus(orderApproveFundPlanStatusDTO);
                    orderFeign.updateOrderFundRepayment(orderId);
                    log.info("ChangYinServiceImpl.handleRepaymentUpdateBatch: 更新订单还款状态成功 orderId:{}", orderId);


                    handledOrderIdSet.add(orderId);
                    if (ObjUtil.equals(fundDeductRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT) && StrUtil.equals("N",  plan.getIsDc())) {
                        settledOrderIdSet.add(orderId);
                    }
                    deductNoList.add(plan.getRequestNo());
                });
            } catch (Exception e) {
                log.error("ChangYinServiceImpl.handleRepaymentUpdateBatch loanNo:{} error:{}", loanNo, e.getMessage(), e);
            }

        });


        //异步发起结清申请
        if (!settledOrderIdSet.isEmpty()) {
            settledOrderIdSet.forEach(orderId -> {
                ThreadUtil.execute(() -> {
                    try {
                        signatureApplyByOrderId(orderId);
                    } catch (Exception e) {
                        log.error("ChangYinServiceImpl.handleRepaymentUpdateBatch signatureApplyByOrderId orderId:{} error:{}", orderId, e.getMessage(), e);
                    }
                });
            });
        }

        BatchSaveOrderFileDTO orderFileDTO = new BatchSaveOrderFileDTO();
        orderFileDTO.setFileId(fileConfigId);
        orderFileDTO.setOrderIdList(handledOrderIdSet.stream().toList());
        orderFileDTO.setFileName(fileName);
        orderFileDTO.setResourceName(fileVO.getResourceName());
        orderFileDTO.setResourceId(fileVO.getResourceId());
        orderFeign.batchOrderFile(orderFileDTO);

        try {
            if (CollUtil.isNotEmpty(deductNoList)) {
                ThreadUtil.execAsync(() -> fundMessageProcess.sendRepayMessage(deductNoList));
            }
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.handleRepaymentUpdateBatch sendRepayMessage deductNoList:{} Exception e:{}", deductNoList, e.getMessage(), e);
        }


    }

    /**
     * 保存交易明细
     */
    public void saveFeeDetail(Integer term, Integer orderId, String repayOrderNo, BigDecimal amount, OrderFeeDetailExpandTypeEnum expenseType) {
        FundRepaymentInfoEntity updateRepaymentInfo = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                .eq(FundRepaymentInfoEntity::getTerm, term)
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
        );

        if (ObjUtil.equals(updateRepaymentInfo.getRepaymentStatus(), FundRepayStatusEnum.SETTLED)) {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(updateRepaymentInfo.getOrderId());
            orderFeign.saveOrderFeeDetail(new OrderFeeDetailSaveDTO()
                    .setOrderId(updateRepaymentInfo.getOrderId())
                    .setTradingSerialNumber(repayOrderNo)
                    .setAmount(amount)
                    .setTradingMethods(OrderFeeDetailTradingMethodsEnum.FUND_BUCKLES)
                    .setPayer(orderInfoEntity.getCustomerName())
                    .setPayee(FundEnum.getFundEnum(updateRepaymentInfo.getFundId()).getFundName())
                    .setExpenseType(expenseType)
                    .setStatus(OrderFeeDetailStatusEnum.INCOME)
                    .setTerm(updateRepaymentInfo.getTerm())
                    .setTradingTime(updateRepaymentInfo.getActuallyDate().atStartOfDay())
                    .setRemark(null));
        }
    }


    /**
     * 获取文件路径
     * @param fixedUpdatePlanDate
     * @return
     */
    private String getRepaymentPlanSftpPath(LocalDate fixedUpdatePlanDate ,Integer type) {
        String dateStr = fixedUpdatePlanDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String dateStrWithDash = fixedUpdatePlanDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        String pathTemplate = "";
        if (ObjUtil.equals(type, FundConstant.CHANG_YIN_PAYMENT_DETAIL_TYPE)){
            //放款明细文件
            pathTemplate =FundConstant.CHANG_YIN_LOAN_FILE_PATH;
        }
        if (ObjUtil.equals(type, FundConstant.CHANG_YIN_PAYMENT_PLAN_UPDATE_TYPE)){
            //还款更新文件
            pathTemplate =FundConstant.CHANG_YIN_REPAYMENT_FILE_PATH;
        }
        if (ObjUtil.equals(type, FundConstant.CHANG_YIN_PAYMENT_PLAN_TYPE)){
            //还款文件
            pathTemplate =FundConstant.CHANG_YIN_PLAN_FILE_PATH;
        }
        if (ObjUtil.equals(type, FundConstant.CHANG_YIN_PRE_INDEMNITY_TYPE)){
            //代偿文件
            pathTemplate = FundConstant.CHANG_YIN_PRE_INDEMNITY_FILE_PATH;
        }
        if (ObjUtil.equals(type, FundConstant.CHANG_YIN_INDEMNITY_TYPE)){
            //代偿文件
            pathTemplate = FundConstant.CHANG_YIN_INDEMNITY_FILE_PATH;
        }

//        String newPath = pathTemplate.replace("{YYYYMMDD}", "20280215").replace("${yyyyMMdd}", "20280215");
        String newPath = pathTemplate.replace("{YYYYMMDD}", dateStr).replace("${yyyyMMdd}", dateStrWithDash);
        return newPath;

    }
    /**
     * 获取文件路径
     * @param fixedUpdatePlanDate
     * @return
     */
    private String getRepaymentPlanSftpFileName(LocalDate fixedUpdatePlanDate,Integer type) {
        String fileName = "";
        String dateStrWithDash = fixedUpdatePlanDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String pathTemplate = "";
        if (ObjUtil.equals(type, FundConstant.CHANG_YIN_PAYMENT_DETAIL_TYPE)){
            pathTemplate =FundConstant.CHANG_YIN_LOAN_FILE_NAME;
        }
        if (ObjUtil.equals(type, FundConstant.CHANG_YIN_PAYMENT_PLAN_UPDATE_TYPE)){
            pathTemplate =FundConstant.CHANG_YIN_REPAY_FILE_NAME;
        }
        if (ObjUtil.equals(type, FundConstant.CHANG_YIN_PRE_INDEMNITY_TYPE)){
            pathTemplate =FundConstant.CHANG_YIN_PRE_INDEMNITY_FILE_NAME;
        }
        if (ObjUtil.equals(type, FundConstant.CHANG_YIN_INDEMNITY_TYPE)){
            pathTemplate =FundConstant.CHANG_YIN_INDEMNITY_FILE_NAME;
        }
//        fileName = pathTemplate.replace("{YYYYMMDD}", dateStrWithDash).replace("${yyyyMMdd}", dateStrWithDash);
        fileName = pathTemplate.replace("${yyyyMMdd}", dateStrWithDash);
        return fileName;



    }

    /**
     * 下载文件
     *
     * @param filePath 文件路径
     * @param fileName 文件名称
     * @return 文件对象
     */
    private FileVO downloadAndParseFile(String filePath, String fileName) {
        log.info("ChangYinServiceImpl.downloadAndParseFile: Downloading file from SFTP: {}", filePath);

        try {
            SftpBaseDTO sftpBaseDTO = new SftpBaseDTO()
                    .setHost(changYinConfig.getSftpHost())
                    .setPort(changYinConfig.getSftpPort())
                    .setUserName(changYinConfig.getSftpUser())
                    .setPassword(changYinConfig.getSftpPwd());

            DownloadSftpDTO downloadSftpDTO = new DownloadSftpDTO()
                    .setRemoteFilePath(filePath)
                    .setDownloadFileName(fileName)
                    .setSftpBaseDTO(sftpBaseDTO);

            FileVO fileVO = resourceFeign.downloadSftp(downloadSftpDTO).getData();
            if (fileVO == null) {
                log.warn("ChangYinServiceImpl.downloadAndParseFile: Failed to download file: {}", filePath);
                return null;
            }
            log.info("ChangYinServiceImpl.downloadAndParseFile: File content downloaded  fileUid: {}", fileVO.getResourceId());
            return fileVO;
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.downloadAndParseFile: Error downloading or parsing file: {}", filePath, e);
            return null;
        }
    }

    @Override
    public List<ChangYinContractPreviewVO> downCardChangeInOutSupply(Integer orderId) {
        log.info("ChangYinServiceImpl.downCardChangeInOutSupply orderId:{}", orderId);
        try{
            CustomerBaseDTO customerFinalBaseInfo = fundApproveMapper.getCustomerFinalBaseInfo(orderId);
            log.info("ChangYinServiceImpl.downCardChangeInOutSupply orderId:{} customerFinalBaseInfo:{}", orderId, JSONUtil.toJsonStr(customerFinalBaseInfo));
            FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.CHANG_YIN);
            if (ObjUtil.isNull(finalFundInfo)){
                throw new BusinessException("该订单申请信息不存在");
            }
            FundResourceDTO fundResourceDTO = new FundResourceDTO();
            fundResourceDTO.setLinkId(orderId);
            fundResourceDTO.setFund(FundEnum.CHANG_YIN);
            fundResourceDTO.setType(6);
            fundResourceDTO.setCustomerBaseInfo(new FundResourceDTO.CustomerBaseInfo().setCustomerIdNo(customerFinalBaseInfo.getIdNumber()));
            fundResourceDTO.setFundBaseInfo(new FundResourceDTO.FundBaseInfo().setLoanSeq(finalFundInfo.getCreditNo()));

            Result<List<FundResourceResultDTO>> resourceDownload = resourceFeign.fundResourceDownload(fundResourceDTO);
            if (!Result.isSuccess(resourceDownload)) {
                throw new BusinessException("下载合同文件异常");
            }

            List<FundResourceResultDTO> data = resourceDownload.getData();
            if (CollUtil.isEmpty(data)) {
                throw new BusinessException("下载合同文件为空");
            }
            String filePath = data.get(0).getFilePath();
            log.info("ChangYinServiceImpl.downCardChangeInOutSupply filePath:{}", filePath);
            List<ChangYinContractPreviewVO> changYinContractPreviewVOList = new ArrayList<>();
            for (FundResourceResultDTO item : data) {
                ChangYinContractPreviewVO changYinContractPreviewVO = new ChangYinContractPreviewVO();
                changYinContractPreviewVO.setResourceId(item.getResourceId());

                ContractTypeMapping mapping = ContractTypeMapping.getByFileCode(item.getFileCode());
                if (mapping != null) {
                    changYinContractPreviewVO.setTemplateId(mapping.getContractType().getTemplateId());
                    changYinContractPreviewVO.setFileName(mapping.getContractType().getDescription());
                }

                changYinContractPreviewVOList.add(changYinContractPreviewVO);
            }

            return changYinContractPreviewVOList;
        } catch (Exception e) {
            log.error("ChangYinServiceImpl.downloadFundContract error e:{}", e.getMessage(), e);
            throw new BusinessException("下载合同文件异常");
        }

    }

    /**
     * 还款计划初始化
     */
    @Override
    public void initRepaymentPlan() {
        List<FinalFundInfoEntity> finalFundInfoList = finalFundInfoMapper.selectJoinList(FinalFundInfoEntity.class, new MPJLambdaWrapper<FinalFundInfoEntity>()
                .selectAll(FinalFundInfoEntity.class)
                .innerJoin(OrderInfoEntity.class, qw ->
                        qw.eq(OrderInfoEntity::getState, 5000)
                            .eq(OrderInfoEntity::getId, FinalFundInfoEntity::getOrderId)
                )
                .leftJoin(FundRepaymentInfoEntity.class, qw ->
                        qw.eq(FundRepaymentInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                                .eq(FundRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0))
                .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .isNull(FundRepaymentInfoEntity::getId)
        );
        log.info("ChangYinServiceImpl.initRepaymentPlan finalFundInfoList:{}", JSONUtil.toJsonStr(finalFundInfoList));
        List<FinalFundInfoEntity> distinctFinalFundInfoList = finalFundInfoList.stream()
                .distinct()
                .toList();

        for (FinalFundInfoEntity finalFundInfo : distinctFinalFundInfoList) {
            log.info("ChangYinServiceImpl.initRepaymentPlan finalFundInfo:{}", JSONUtil.toJsonStr(finalFundInfo));
            try {
                //查询放款结果
                ChangYinLoanQueryDTO changYinLoanQueryDTO = new ChangYinLoanQueryDTO();
                changYinLoanQueryDTO.setApplCde(finalFundInfo.getCreditNo())
                        .setLoanSeq(finalFundInfo.getLoanBillNo())
                        .setOutLoanSeq(finalFundInfo.getLoanReqNo());
                if (StrUtil.isBlank(finalFundInfo.getLoanNo())) {
                    ChangYinResBodyDTO<ChangYinLoanQueryResDTO> changYinResBodyDTO = changYinClient.loanQueryV2(changYinLoanQueryDTO);
                    if (ChangYinResBodyDTO.isSuccess(changYinResBodyDTO) && ObjUtil.isNotNull(changYinResBodyDTO.getBody())
                            && ObjUtil.equals(changYinResBodyDTO.getBody().getDnSts(), ChangYinDictEnum.ChangYinConclusionStatusEnum.PAYMENT_PASS)
                    ) {
                        ChangYinLoanQueryResDTO body = changYinResBodyDTO.getBody();
                        String loanNo = body.getLoanNo();
                        //更新loanNo
                        finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                                .set(FinalFundInfoEntity::getLoanNo, loanNo)
                                .eq(FinalFundInfoEntity::getId, finalFundInfo.getId())
                        );
                        finalFundInfo.setLoanNo(loanNo);
                    }
                }
                //查询还款计划
                repaymentPlanInit(finalFundInfo.getOrderId() ,ChangYinDictEnum.RepaymentPlanQueryType.FULL_PLAN);
            } catch (Exception e) {
                log.error("ChangYinServiceImpl.initRepaymentPlan error e:{}", e.getMessage(), e);
            }

        }



    }

    @Override
    public void repayPlanFileBatch() {
        log.info("ChangYinService.syncRepaymentBatch begin");

        // 获取文件配置ID
        Integer fileConfigId = getFileConfigId(FundConstant.REPAYMENT_PLAN_FILE_CONFIG_CODE);

        // 获取不存在文件订单列表
        List<OrderInfoEntity> orderNotFileList = getOrderNotFileList(fileConfigId);
        if (CollUtil.isEmpty(orderNotFileList)) {
            log.info("ChangYinService.syncRepaymentBatch: No files found for the given orders");
            return;
        }

        log.info("ChangYinService.syncRepaymentBatch: Found {} files for the given orders", orderNotFileList.size());

        // 创建支付时间与订单ID的映射
        Map<LocalDate, List<Integer>> paymentTimeToOrderIdsMap = createPaymentTimeMap(orderNotFileList);

        log.info("ChangYinService.syncRepaymentBatch: Payment time to order IDs map created with {} entries", paymentTimeToOrderIdsMap.size());

        // 处理每个支付时间对应的订单ID列表
        List<Integer> parseOrderIdList = processPaymentTimeEntries(paymentTimeToOrderIdsMap, fileConfigId);

        log.info("ChangYinService.syncRepaymentBatch: Successfully processed {} orders", parseOrderIdList.size());

        log.info("ChangYinService.syncRepaymentBatch end");
    }


    /**
     * 获取订单文件不存在的列表
     *
     * @param fileConfigId 文件配置ID
     * @return 不存在文件的 orderId
     */
    private List<OrderInfoEntity> getOrderNotFileList(Integer fileConfigId) {
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectJoinList(OrderInfoEntity.class, new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId, OrderInfoEntity::getPaymentTime)
                .leftJoin(OrderFileEntity.class, on ->
                        on.eq(OrderFileEntity::getOrderId, OrderInfoEntity::getId)
                                .eq(OrderFileEntity::getFileId, fileConfigId)
                                .eq(OrderFileEntity::getDeleteFlag, 0)
                )
                .eq(OrderInfoEntity::getCurrentNode, 5000)
                .eq(OrderInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(OrderInfoEntity::getPaymentState, OrderPaymentStateEnum.PASS)
                .and(q -> q.eq(OrderInfoEntity::getPlanState, 0).or().isNull(OrderInfoEntity::getPlanState))
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .isNull(OrderFileEntity::getId)
        );
        if (CollUtil.isEmpty(orderInfoEntityList)){
            return Collections.emptyList();
        }
        return orderInfoEntityList;
    }


    /**
     * 创建支付时间与订单ID的映射
     *
     * @param orderInfoList 订单信息列表
     * @return 支付时间与订单ID的映射
     */
    private Map<LocalDate, List<Integer>> createPaymentTimeMap(List<OrderInfoEntity> orderInfoList) {
        Map<LocalDate, List<Integer>> map = new HashMap<>();
        for (OrderInfoEntity orderInfo : orderInfoList) {
            Integer orderId = orderInfo.getId();
            if (ObjUtil.isNull(orderInfo.getPaymentTime())){
                log.info("ChangYinService.syncRepaymentBatch: Order {} has no payment time", orderId);
                continue;
            }
            LocalDate paymentDate = orderInfo.getPaymentTime().toLocalDate();
            map.computeIfAbsent(paymentDate, k -> new ArrayList<>()).add(orderId);
        }
        return map;
    }


    /**
     * 处理每个支付时间对应的订单ID列表
     *
     * @param paymentTimeToOrderIdsMap 支付时间与订单ID的映射
     * @param fileConfigId 文件配置ID
     * @return 成功处理的订单ID列表
     */
    private List<Integer> processPaymentTimeEntries(Map<LocalDate, List<Integer>> paymentTimeToOrderIdsMap, Integer fileConfigId) {
        List<Integer> parseOrderIdList = new ArrayList<>();
        for (Map.Entry<LocalDate, List<Integer>> entry : paymentTimeToOrderIdsMap.entrySet()) {
            LocalDate paymentDate = entry.getKey();
            List<Integer> orderIdsForPaymentTime = entry.getValue();

            log.info("ChangYinService.syncRepaymentBatch: Processing orders for payment date: {}", paymentDate);
            processFileSaveOrderFileBatch(orderIdsForPaymentTime, paymentDate, fileConfigId, FundConstant.CHANG_YIN_PAYMENT_PLAN_TYPE);
        }
        return parseOrderIdList;
    }


    /**
     * 处理文件
     *
     * @param orderIdList 订单ID
     * @param paymentTime 放款时间
     * @param fileId        文件配置Id
     * @param type        文件类型
     */
    private FileVO processFileSaveOrderFileBatch(List<Integer> orderIdList, LocalDate paymentTime, Integer fileId, Integer type) {
        log.info("ChangYinService.processFileSaveOrderFileBatch begin orderId: {}, paymentTime: {}, fileId: {}, type: {}", orderIdList, paymentTime, fileId, type);

        // 获取文件路径
        String filePath = getRepaymentPlanSftpPath(paymentTime, type);
        log.info("ChangYinService.processFileSaveOrderFileBatch: File path: {}", filePath);

        // 下载文件
        FileVO fileVO = downloadAndParseFile(filePath, FileUtil.getName(filePath));
        if (fileVO != null) {
            log.info("ChangYinService.processFileSaveOrderFileBatch: File downloaded and parsed successfully: {}", fileVO);
            saveOrderFileBatch(orderIdList, fileId, FileUtil.getName(filePath), fileVO);
            return fileVO;
        } else {
            log.info("ChangYinService.processFileSaveOrderFileBatch: Failed to download or parse file for orderId: {}, filePath: {}", orderIdList, filePath);
        }
        return null;
    }
    /**
     * 保存订单文件
     *
     * @param orderIdList  订单ID
     * @param fileId   文件配置ID
     * @param fileName 文件名称
     * @param fileVO   文件对象
     */
    private void saveOrderFileBatch(List<Integer> orderIdList, Integer fileId, String fileName, FileVO fileVO) {
        BatchSaveOrderFileDTO orderFileDTO = new BatchSaveOrderFileDTO();
        orderFileDTO.setFileId(fileId);
        orderFileDTO.setOrderIdList(orderIdList);
        orderFileDTO.setFileName(fileName);
        orderFileDTO.setResourceName(fileVO.getResourceName());
        orderFileDTO.setResourceId(fileVO.getResourceId());
        orderFeign.batchOrderFile(orderFileDTO);
    }


    /**
     * 结清证明申请
     */
    @Override
    public ChangYinResBodyDTO<ChangYinSignatureApplyResDTO> signatureApplyByOrderId(Integer orderId) {
        log.info("ChangYinServiceImpl.signatureApplyByOrderId orderId:{}", orderId);
        //查询资方信息
        FinalFundInfoEntity fundApproveEntity = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                ,false
        );
        if (ObjUtil.isNull(fundApproveEntity)) {
            throw new BusinessException("资方信息为空");
        }
        CustomerBaseDTO customerFinalBaseInfo = fundApproveMapper.getCustomerFinalBaseInfo(orderId);
        ChangYinSignatureApplyDTO applyDTO = new ChangYinSignatureApplyDTO();
        applyDTO.setBizMode("1");
        applyDTO.setOutSeq(fundApproveEntity.getCreditReqNo());
        applyDTO.setLoanNo(fundApproveEntity.getLoanNo());
        applyDTO.setIdNo(customerFinalBaseInfo.getIdNumber());
        ChangYinResBodyDTO<ChangYinSignatureApplyResDTO> applyResDTOChangYinResBodyDTO = changYinClient.signatureApply(applyDTO);
        if (ChangYinResBodyDTO.isSuccess(applyResDTOChangYinResBodyDTO)) {
            FundSettleApplyStatusEnums applyStatusEnums = FundSettleApplyStatusEnums.APPLY_FAILED;
            if (StrUtil.equals(applyResDTOChangYinResBodyDTO.getBody().getStatus(), "0")) {
                applyStatusEnums = FundSettleApplyStatusEnums.APPLY_SUCCESS;
            }
            //更新申请状态
            finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .set(FinalFundInfoEntity::getSettleApplyStatus, applyStatusEnums)
                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                    .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
            );
        }
        log.info("ChangYinServiceImpl.signatureApplyByOrderId applyResDTOChangYinResBodyDTO:{}", JSONUtil.toJsonStr(applyResDTOChangYinResBodyDTO));
        return applyResDTOChangYinResBodyDTO;
    }

    /**
     * 结清证明查询下载
     */
    @Override
    public ChangYinResBodyDTO<ChangYinSignatureQueryResDTO> signatureQueryByOrderId(Integer orderId) {
        log.info("ChangYinServiceImpl.signatureQueryByOrderId orderId:{}", orderId);
        //查询资方信息
        FinalFundInfoEntity fundApproveEntity = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getSettleApplyStatus, FundSettleApplyStatusEnums.APPLY_SUCCESS)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                ,false
        );
        if (ObjUtil.isNull(fundApproveEntity)) {
            throw new BusinessException("未查询到结清证明申请信息");
        }
        CustomerBaseDTO customerFinalBaseInfo = fundApproveMapper.getCustomerFinalBaseInfo(orderId);
        //查询结果
        ChangYinSignatureQueryDTO signatureQueryDTO = new ChangYinSignatureQueryDTO();
        signatureQueryDTO.setBizMode("1");
        signatureQueryDTO.setOutSeq(fundApproveEntity.getCreditReqNo());
        signatureQueryDTO.setLoanNo(fundApproveEntity.getLoanNo());
        signatureQueryDTO.setIdNo(customerFinalBaseInfo.getIdNumber());
        ChangYinResBodyDTO<ChangYinSignatureQueryResDTO> signatureQueryResult = changYinClient.signatureQuery(signatureQueryDTO);
        if (ChangYinResBodyDTO.isSuccess(signatureQueryResult)) {
            String status = signatureQueryResult.getBody().getStatus();
            FundSettleApplyStatusEnums applyStatusEnums = FundSettleApplyStatusEnums.APPLY_SUCCESS;
            if (StrUtil.equals(status, "1")) {
                applyStatusEnums = FundSettleApplyStatusEnums.APPLY_FAILED;
            }
            if (StrUtil.equals(status, "0")) {
                String settleVoucherPath = signatureQueryResult.getBody().getImgUrl();
                Integer fundSettlementFileId = getFileConfigId(FundConstant.FUND_SETTLEMENT_VOUCHER);
                FundResourceDTO fundResourceDTO = new FundResourceDTO()
                        .setFund(FundEnum.CHANG_YIN)
                        .setType(2)
                        .setLinkId(orderId)
                        .setResourceList(Collections
                                .singletonList(new FundResourceDTO.Resource()
                                        .setFundFileCode(FundConstant.FUND_SETTLEMENT_VOUCHER)
                                        .setFilePath(settleVoucherPath)));
                List<FundResourceResultDTO> fundSettlementFileList = resourceFeign.fundResourceDownload(fundResourceDTO).getData();
                if (CollUtil.isNotEmpty(fundSettlementFileList)) {
                    FundResourceResultDTO fundResourceResultDTO = fundSettlementFileList.get(0);
                    OrderFileEntity OrderFileSettleVoucher = new OrderFileEntity();
                    OrderFileSettleVoucher.setOrderId(orderId);
                    OrderFileSettleVoucher.setFileId(fundSettlementFileId);
                    OrderFileSettleVoucher.setFileName(FileUtil.getName(settleVoucherPath));
                    OrderFileSettleVoucher.setResourceId(fundResourceResultDTO.getResourceId());
                    OrderFileSettleVoucher.setResourceName(FileUtil.getName(fundResourceResultDTO.getFilePath()));
                    int insert = orderFileMapper.insert(OrderFileSettleVoucher);
                    if (insert > 0) {
                        applyStatusEnums = FundSettleApplyStatusEnums.DOWNLOADED;
                    }
                }


            } else if (StrUtil.equals(status, "1")) {
                applyStatusEnums = FundSettleApplyStatusEnums.APPLY_FAILED;
            }
            finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .set(FinalFundInfoEntity::getSettleApplyStatus, applyStatusEnums)
                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                    .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
            );
        }
        log.info("ChangYinServiceImpl.signatureQueryByOrderId signatureQueryResult:{}", JSONUtil.toJsonStr(signatureQueryResult));
        return signatureQueryResult;
    }

    /**
     * 贷后预代偿文件拉取
     */
    @Override
    public void getPreIndemnityFile(String jobTime) {
        LocalDate downFileDate  = LocalDate.now();
        if (StrUtil.isNotBlank(jobTime)) {
            downFileDate  = LocalDate.parse(jobTime,  DateTimeFormatter.ofPattern("yyyyMMdd"));
        }

        log.info("ChangYinServiceImpl.getPreIndemnityFile begin");
        /**
         * 1.下载文件
         * 2.解析文件数据
         * 3.处理赎回逻辑==》要生成一条申请记录
         * 4.根据文件数据查询订单号，根据订单号保存oder_file记录
         */
        String filePath = getRepaymentPlanSftpPath(downFileDate,FundConstant.CHANG_YIN_PRE_INDEMNITY_TYPE);
        String fileName = getRepaymentPlanSftpFileName(downFileDate,FundConstant.CHANG_YIN_PRE_INDEMNITY_TYPE);
        log.info("ChangYinServiceImpl.getPreIndemnityFile: File path: {}", filePath);

        // 下载文件
        FileVO fileVO = downloadAndParseFile(filePath, fileName);
        if (fileVO != null) {
            String resourceId = fileVO.getResourceId();
            String resourceName = fileVO.getResourceName();
            //下载成功
            //解析代偿文件
            ParseCsvDTO parseCsvDTO = new ParseCsvDTO();
            parseCsvDTO.setTitleLine(1);
            parseCsvDTO.setFileUidList(List.of(resourceId));
            Result<Map<String, List<List<String>>>> mapResult = resourceFeign.parseCsv(parseCsvDTO);
            if (!Result.isSuccess(mapResult)) {
                log.error("ChangYinServiceImpl.getPreIndemnityFile: parseCsv fail, result: {}", mapResult);
                return;
            }
            List<List<String>> dataList = mapResult.getData().get(resourceId);
            List<FundIndemnityDTO> changYinCsvPreIndemnityList = convertToChangYinCsvPreIndemnityDTO(dataList);

            log.info("ChangYinServiceImpl.getPreIndemnityFile: changYinCsvPreIndemnityList: {}", changYinCsvPreIndemnityList);

            handlePreIndemnityUpdateBatch(changYinCsvPreIndemnityList, fileName, fileVO);
        }




    }

    /**
     * 贷后代偿文件拉取
     */
    @Override
    public void getIndemnityFile(String jobTime) {
        LocalDate downFileDate  = LocalDate.now();
        if (StrUtil.isNotBlank(jobTime)) {
            downFileDate  = LocalDate.parse(jobTime,  DateTimeFormatter.ofPattern("yyyyMMdd"));
        }
        log.info("ChangYinServiceImpl.getIndemnityFile begin");
        String filePath = getRepaymentPlanSftpPath(downFileDate,FundConstant.CHANG_YIN_INDEMNITY_TYPE);
        String fileName = getRepaymentPlanSftpFileName(downFileDate,FundConstant.CHANG_YIN_INDEMNITY_TYPE);
        log.info("ChangYinServiceImpl.getIndemnityFile: File path: {}", filePath);

        // 下载文件
        FileVO fileVO = downloadAndParseFile(filePath, fileName);
        if (fileVO != null) {
            String resourceId = fileVO.getResourceId();
            String resourceName = fileVO.getResourceName();
            //下载成功
            //解析代偿文件
            ParseCsvDTO parseCsvDTO = new ParseCsvDTO();
            parseCsvDTO.setTitleLine(1);
            parseCsvDTO.setFileUidList(List.of(resourceId));
            Result<Map<String, List<List<String>>>> mapResult = resourceFeign.parseCsv(parseCsvDTO);
            if (!Result.isSuccess(mapResult)) {
                log.error("ChangYinServiceImpl.getIndemnityFile: parseCsv fail, result: {}", mapResult);
                return;
            }
            List<List<String>> dataList = mapResult.getData().get(resourceId);
            List<FundIndemnityDTO> changYinCsvIndemnityList = convertToChangYinCsvIndemnityDTO(dataList);

            log.info("ChangYinServiceImpl.getIndemnityFile: changYinCsvIndemnityList: {}", changYinCsvIndemnityList);

            handleIndemnityUpdateBatch(changYinCsvIndemnityList, fileName, fileVO);
        }
    }


    /**
     * 根据订单贷款试算
     */
    public ChangYinResBodyDTO<ChangYinLoanTrialResponseDTO> loanTrialV2ByOrder(ChangYinLoanTrialByOrderDTO changYinLoanTrialByOrderDTO) {
        //查询授信申请信息
        Integer orderId = changYinLoanTrialByOrderDTO.getOrderId();
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isNull(orderInfo)) {
            log.info("ChangYinServiceImpl.loanTrialV2ByOrder.orderInfo is null orderId:{}", orderId);
            throw new BusinessException("订单信息不存在");
        }
        BigDecimal priceIntRat = changYinLoanTrialByOrderDTO.getPriceIntRat();
        if (ObjUtil.isNull(priceIntRat)) {
            FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.CHANG_YIN);
            ChangYinCreditQueryDTO changYinCreditQueryDTO = new ChangYinCreditQueryDTO();
            changYinCreditQueryDTO.setOutApplSeq(finalFundInfo.getCreditReqNo());
            ChangYinResBodyDTO<ChangYinCreditQueryResDTO> result = creditQueryV2(changYinCreditQueryDTO);
            if (!ChangYinResBodyDTO.isSuccess(result)) {
                log.info("ChangYinServiceImpl.loanTrialV2ByOrder.creditQueryV2 fail orderId:{} result: {}", orderId, JSONUtil.toJsonStr(result));
                throw new BusinessException("授信查询失败");
            }
            ChangYinCreditQueryResDTO creditQueryResDTO = result.getBody();
            priceIntRat = creditQueryResDTO.getRiskPriceIntRat();
        }
        String serialNumber = SerialNumberUtils.generateSerialNumberStr();
        ChangYinLoanTrialReqDTO changYinLoanTrialDTO = new ChangYinLoanTrialReqDTO()
                .setOutTrailSeq(serialNumber)
                .setIdNo(changYinLoanTrialByOrderDTO.getIdNo())
                .setLoanType(changYinConfig.getLoanTyp())
                .setApplyAmt(changYinLoanTrialByOrderDTO.getApplyAmt())
                .setApplyTnr(changYinLoanTrialByOrderDTO.getApplyTnr())
                .setPriceIntRat(priceIntRat)
                .setCustShowRate(changYinLoanTrialByOrderDTO.getCustShowRate())
                .setLoanFreq(ChangYinDictEnum.RepaymentInterval.ONE_MONTH.getCode())
                .setContSignDt(changYinLoanTrialByOrderDTO.getContSignDt())
                .setFirstPayDt(changYinLoanTrialByOrderDTO.getFirstPayDt())
                .setMtdCde(ChangYinDictEnum.RepaymentMethod.convert(DictRepaymentMethod.fromCode(orderInfo.getRepayMethod())));
        log.info("ChangYinServiceImpl.loanTrialV2ByOrder: serialNumber:{} changYinLoanTrialDTO: {}",serialNumber, JSONUtil.toJsonStr(changYinLoanTrialDTO));
        ChangYinResBodyDTO<ChangYinLoanTrialResponseDTO> loanTrialResult = changYinClient.loanTrialV2(changYinLoanTrialDTO);
        log.info("ChangYinServiceImpl.loanTrialV2ByOrder: serialNumber:{} loanTrialResult: {}", serialNumber, JSONUtil.toJsonStr(loanTrialResult));
        if (!ChangYinResBodyDTO.isSuccess(loanTrialResult)) {
            log.info("ChangYinServiceImpl.loanTrialV2ByOrder fail orderId:{} result: {}", orderId, JSONUtil.toJsonStr(loanTrialResult));
            throw new BusinessException(loanTrialResult.getHead().getRespMsg());
        }
        return loanTrialResult;
    }




    private List<FundIndemnityDTO> convertToChangYinCsvIndemnityDTO(List<List<String>> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        List<FundIndemnityDTO> result = new ArrayList<>();
        for (List<String> row : dataList) {
            try {
                FundIndemnityDTO dto = new FundIndemnityDTO()
                        .setSetlSeq(StrUtil.trimToNull(row.get(0)))  // setl_seq
                        .setApplSeq(StrUtil.trimToNull(row.get(1)))  // appl_seq
                        .setOutRequestSeq(StrUtil.trimToNull(row.get(2)))  // out_request_seq
                        .setLoanNo(StrUtil.trimToNull(row.get(3)))  // loan_no
                        .setSetlDt(Convert.toLocalDateTime(row.get(4)).toLocalDate())  // setl_dt
                        .setTnr(StrUtil.trimToNull(row.get(5)))  // tnr
                        .setSetlTotalAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(6))))  // setl_total_amt
                        .setTotalAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(7))))  // total_amt
                        .setPrcpAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(8))))  // prcp_amt
                        .setIntAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(9))))  // int_amt
                        .setOdIntAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(10))))  // od_int_amt
                        .setCommOdIntAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(11))))  // comm_od_int_amt
                        .setFeeAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(12))))  // fee_amt
                        .setPayMode(StrUtil.trimToNull(row.get(13)))
                        .setDealSts(StrUtil.trimToNull(row.get(14))) ; // deal_sts;
                result.add(dto);
            } catch (Exception e) {
                log.error("转换CSV行数据失败，行数据: {}", row, e);
            }
        }

        return result;
    }

    /**
     * 将CSV数据列表转换为ChangYinCsvRepayDTO列表
     * @param dataList CSV数据(二维列表)
     * @return List<ChangYinCsvRepayDTO>
     */
    private List<FundIndemnityDTO> convertToChangYinCsvPreIndemnityDTO(List<List<String>> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        List<FundIndemnityDTO> result = new ArrayList<>();
        for (List<String> row : dataList) {
            try {
                FundIndemnityDTO dto = new FundIndemnityDTO()
                        .setApplSeq(StrUtil.trimToNull(row.get(0)))  // appl_seq
                        .setOutRequestSeq(StrUtil.trimToNull(row.get(1)))  // out_request_seq
                        .setLoanNo(StrUtil.trimToNull(row.get(2)))  // loan_no
                        .setSetlDt(Convert.toLocalDateTime(row.get(3)).toLocalDate())  // setl_dt
                        .setTnr(StrUtil.trimToNull(row.get(4)))  // tnr
                        .setSetlTotalAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(5))))  // setl_total_amt
                        .setTotalAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(6))))  // total_amt
                        .setPrcpAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(7))))  // prcp_amt
                        .setIntAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(8))))  // int_amt
                        .setOdIntAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(9))))  // od_int_amt
                        .setCommOdIntAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(10))))  // comm_od_int_amt
                        .setFeeAmt(NumberUtil.toBigDecimal(StrUtil.trimToNull(row.get(11))))  // fee_amt
                        .setPayMode(StrUtil.trimToNull(row.get(12))) ; // pay_mode
                result.add(dto);
            } catch (Exception e) {
                log.error("转换CSV行数据失败，行数据: {}", row, e);
            }
        }

        return result;
    }



    /**
     * 处理预代偿数据
     */
    private void handlePreIndemnityUpdateBatch(List<FundIndemnityDTO> indemnityDTOList, String fileName, FileVO fileVO) {
        log.info("ChangYinServiceImpl.handlePreIndemnityUpdateBatch: 开始处理预代偿数据");
        //1.查询文件配置
        Integer fileConfigId = getFileConfigId(FundConstant.PRE_INDEMNITY_FILE_CONFIG_CODE);
        List<String> loanNoList = indemnityDTOList.stream().map(FundIndemnityDTO::getLoanNo).distinct().toList();


        //2.根据借据号查询订单号
        List<FinalFundInfoEntity> finalFundInfoByLoanNoList = finalFundInfoMapper.selectList(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .in(FinalFundInfoEntity::getLoanNo, loanNoList)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );

        //3.根据解决号转为map<借据号,订单id> 遇到键重复使用最新
        Map<String, Integer> loanNoOrderIdMap = finalFundInfoByLoanNoList.stream()
                .collect(Collectors.toMap(FinalFundInfoEntity::getLoanNo, FinalFundInfoEntity::getOrderId, (v1, v2) -> v2));

        for (FundIndemnityDTO dto: indemnityDTOList ){
            //设置订单号
            dto.setOrderId(loanNoOrderIdMap.get(dto.getLoanNo()));
            //设置资方
            dto.setFundId(FundEnum.CHANG_YIN.getValue());
        }

        log.info("ChangYinServiceImpl.handlePreIndemnityUpdateBatch: 处理完之后的长银预代偿数据 ：{}", indemnityDTOList);

        log.info("ChangYinServiceImpl.handlePreIndemnityUpdateBatch: start handle changyin batch apply");
        orderFeign.changyinPreIndemnityBatchApply(indemnityDTOList);
        log.info("ChangYinServiceImpl.handlePreIndemnityUpdateBatch: end handle changyin batch apply");

        Set<Integer> orderIdSet = indemnityDTOList.stream().map(FundIndemnityDTO::getOrderId).collect(Collectors.toSet());
        BatchSaveOrderFileDTO orderFileDTO = new BatchSaveOrderFileDTO();
        orderFileDTO.setFileId(fileConfigId);
        orderFileDTO.setOrderIdList(orderIdSet.stream().toList());
        orderFileDTO.setFileName(fileName);
        orderFileDTO.setResourceName(fileVO.getResourceName());
        orderFileDTO.setResourceId(fileVO.getResourceId());
        log.info("ChangYinServiceImpl.handlePreIndemnityUpdateBatch.batchOrderFile: start handle insert 订单文件信息信息数据:{}" ,orderFileDTO);
        orderFeign.batchOrderFile(orderFileDTO);
    }



    /**
     * 处理代偿数据
     */
    private void handleIndemnityUpdateBatch(List<FundIndemnityDTO> indemnityDTOList, String fileName, FileVO fileVO) {
        log.info("ChangYinServiceImpl.handleIndemnityUpdateBatch: 开始处理代偿数据");
        //1.查询文件配置
        Integer fileConfigId = getFileConfigId(FundConstant.INDEMNITY_FILE_CONFIG_CODE);
        List<String> loanNoList = indemnityDTOList.stream().map(FundIndemnityDTO::getLoanNo).distinct().toList();

        //2.根据借据号查询订单号
        List<FinalFundInfoEntity> finalFundInfoByLoanNoList = finalFundInfoMapper.selectList(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .in(FinalFundInfoEntity::getLoanNo, loanNoList)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );

        //3.根据解决号转为map<借据号,订单id> 遇到键重复使用最新
        Map<String, Integer> loanNoOrderIdMap = finalFundInfoByLoanNoList.stream()
                .collect(Collectors.toMap(FinalFundInfoEntity::getLoanNo, FinalFundInfoEntity::getOrderId, (v1, v2) -> v2));

        Set<Integer> orderIdSet = new HashSet<>();
        for (FundIndemnityDTO dto: indemnityDTOList ){
            Integer orderId = loanNoOrderIdMap.get(dto.getLoanNo());
            //设置订单号
            dto.setOrderId(orderId);
            //设置资方
            dto.setFundId(FundEnum.CHANG_YIN.getValue());
            //判断代偿数据是否成功，成功的数据进行处理
            if (dto.getDealSts().equals("01")){
                //处理赎回数据
                fundRepurchase(dto);
                orderIdSet.add(orderId);
            }else {
                //不成功的数据不存到order_file表
                orderIdSet.remove(orderId);
            }
        }
        BatchSaveOrderFileDTO orderFileDTO = new BatchSaveOrderFileDTO();
        orderFileDTO.setFileId(fileConfigId);
        orderFileDTO.setOrderIdList(orderIdSet.stream().toList());
        orderFileDTO.setFileName(fileName);
        orderFileDTO.setResourceName(fileVO.getResourceName());
        orderFileDTO.setResourceId(fileVO.getResourceId());
        log.info("ChangYinServiceImpl.handleIndemnityUpdateBatch.batchOrderFile: start handle insert 订单文件信息信息数据:{}" ,orderFileDTO);
        orderFeign.batchOrderFile(orderFileDTO);
    }


    /**
     * 赎回
     */
    public void fundRepurchase(FundIndemnityDTO dto) {
        Integer orderId = dto.getOrderId();
        log.info("ChangYinServiceImpl.fundRepurchase orderId:{}", orderId);

        //更新为赎回状态
        OrderInfoEntity orderInfo = new OrderInfoEntity();
        orderInfo.setIsRepurchase(1);
        orderInfoMapper.update(orderInfo, new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getId, orderId)
        );

        fundRepurchaseResultMapper.update(new LambdaUpdateWrapper<FundRepurchaseResultEntity>()
                .eq(FundRepurchaseResultEntity::getOrderId, orderId)
                .eq(FundRepurchaseResultEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FundRepurchaseResultEntity::getDeleteFlag, 0)
                .set(FundRepurchaseResultEntity::getDeleteFlag, 1)
        );

       /* //根据流水号，订单号查询是否有数据
        FundRepurchaseResultEntity resultEntity = fundRepurchaseResultMapper.selectOne(new LambdaUpdateWrapper<FundRepurchaseResultEntity>()
                .eq(FundRepurchaseResultEntity::getOrderId, orderId)
                .eq(FundRepurchaseResultEntity::getFundFlowNo, dto.getSetlSeq())
                .eq(FundRepurchaseResultEntity::getDeleteFlag, 0)
                .eq(FundRepurchaseResultEntity::getFundId, FundEnum.CHANG_YIN.getValue())
        );
        //如果有数据的话进行更新，没有数据进行添加
        if (resultEntity != null){
            fundRepurchaseResultMapper.update(new LambdaUpdateWrapper<FundRepurchaseResultEntity>()
                    .eq(FundRepurchaseResultEntity::getOrderId, orderId)
                    .eq(FundRepurchaseResultEntity::getFundFlowNo, dto.getSetlSeq())
                    .eq(FundRepurchaseResultEntity::getDeleteFlag, 0)
                    .eq(FundRepurchaseResultEntity::getFundId, FundEnum.CHANG_YIN.getValue())

                    .set(FundRepurchaseResultEntity::getLoanApplyNo, dto.getLoanNo())
                    .set(FundRepurchaseResultEntity::getRepurchaseDate, DateUtil.format(dto.getSetlDt().atStartOfDay(), "yyyy-MM-dd"))
                    .set(FundRepurchaseResultEntity::getEventStartTerm, Integer.valueOf(dto.getTnr()))
                    .set(FundRepurchaseResultEntity::getPreRepayAmt, dto.getTotalAmt())
                    .set(FundRepurchaseResultEntity::getPrePrincipal, dto.getPrcpAmt())
                    .set(FundRepurchaseResultEntity::getPreInterest, dto.getIntAmt())
                    .set(FundRepurchaseResultEntity::getPrePenalty, dto.getOdIntAmt())
                    .set(FundRepurchaseResultEntity::getRepurchaseResult, 1)
            );
            log.info("ChangYinServiceImpl.fundRepurchase update success:{}", orderId);
        }else {*/
            FundRepurchaseResultEntity fundRepurchaseResult = new FundRepurchaseResultEntity();
            fundRepurchaseResult
                    .setFundFlowNo(dto.getSetlSeq())
                    .setLoanApplyNo(dto.getLoanNo())
                    .setRepurchaseDate(DateUtil.format(dto.getSetlDt().atStartOfDay(), "yyyy-MM-dd"))
                    .setOrderId(orderId)
                    .setFundId(FundEnum.CHANG_YIN.getValue())
                    .setEventStartTerm(Integer.valueOf(dto.getTnr()))
                    .setPreRepayAmt(dto.getTotalAmt())
                    .setPrePrincipal(dto.getPrcpAmt())
                    .setPreInterest(dto.getIntAmt())
                    .setPrePenalty(dto.getOdIntAmt())
                    .setRepurchaseResult(1);
            fundRepurchaseResultMapper.insert(fundRepurchaseResult);
            log.info("ChangYinServiceImpl.fundRepurchase insert success:{}", orderId);
     //   }


    }
    /**
     * 查询orderId对应实例FinalFundInfoEntity，然后更新还款计划表实还
     */
    public Boolean repaymentPlanSettle(Integer orderId) {
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
        );
        if(ObjUtil.isNotNull(finalFundInfo)){
            try{
                updateQueryPlanSettle(finalFundInfo);
                //更新订单还款状态
                OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
                orderApproveFundPlanStatusDTO.setOrderId(orderId);
                orderFeign.updateFundPlanStatus(orderApproveFundPlanStatusDTO);
                orderFeign.updateOrderFundRepayment(orderId);
                return Boolean.TRUE;
            } catch (BusinessException e) {
                log.error("ChangYinServiceImpl.repaymentPlanSettle error msg:{}", e.getMessage(), e);
                throw new BusinessException(e.getMessage());
            }
        }
        throw new BusinessException("资方终审信息不能为空");
    }
    /**
     * 更新还款计划表实还
     */
    private void updateQueryPlanSettle(FinalFundInfoEntity finalFundInfo) {
        //更新还款计划
        ChangYinRepaymentPlanQueryDTO changYinRepaymentPlanQueryDTO = new ChangYinRepaymentPlanQueryDTO();
        changYinRepaymentPlanQueryDTO.setApplCde(finalFundInfo.getCreditNo())
                .setLoanSeq(finalFundInfo.getLoanBillNo())
                .setLoanNo(finalFundInfo.getLoanNo())
                .setEnqTyp(ChangYinDictEnum.RepaymentPlanQueryType.FULL_PLAN.getCode());

        ChangYinResBodyDTO<ChangYinRepaymentPlanQueryResDTO> planQueryResult = changYinClient.repaymentPlanQueryV2(changYinRepaymentPlanQueryDTO);
        if (!ChangYinResBodyDTO.isSuccess(planQueryResult)) {
            log.error("ChangYinServiceImpl.queryPlanUpdate loanNo:{} fail {}", finalFundInfo.getLoanNo(), planQueryResult.getHead().getRespMsg());
            throw new BusinessException("查询长银还款计划失败");
        }

        ChangYinRepaymentPlanQueryResDTO planBody = planQueryResult.getBody();
        List<ChangYinRepaymentPlanQueryResDTO.RepaymentPlan> repaymentPlanList = planBody.getRepaymentPlanList();
        if (CollUtil.isEmpty(repaymentPlanList)) {
            log.error("ChangYinServiceImpl.queryPlanUpdate loanNo:{} repaymentPlanList is empty", finalFundInfo.getLoanNo());
            throw new BusinessException("查询长银还款计划失败");
        }
        Integer orderId = finalFundInfo.getOrderId();

        for (ChangYinRepaymentPlanQueryResDTO.RepaymentPlan plan : repaymentPlanList) {
            //更新实还
            Integer term = plan.getPerdNo();
            log.info("ChangYinServiceImpl.queryPlanUpdate orderId:{}, term:{} plan:{}", orderId, term, plan);
            // 总已还金额 = 已还本金 + 已还利息 + 已还罚息 + 已还费用 + 已还复利 + 已还担保费 + 已还担保费罚息
            BigDecimal actuallyTotal = plan.getSetlPrcp()
                    .add(plan.getSetlNormInt())
                    .add(plan.getSetlOdIntAmt())
                    .add(plan.getSetlFeeAmt())
                    .add(plan.getSetlCommOdInt())
                    .add(plan.getSetlGuaraFeeAmt())
                    .add(plan.getSetlGuaraFeeOdAmt());
            FundRepaymentInfoEntity repaymentInfoEntity = new FundRepaymentInfoEntity();


            repaymentInfoEntity.setActuallyDate(plan.getLastSetlDt());
            repaymentInfoEntity.setActuallyCommOdAmount(Convert.toBigDecimal(plan.getSetlCommOdInt(), BigDecimal.ZERO));
            repaymentInfoEntity.setActuallyFeeAmount(Convert.toBigDecimal(plan.getSetlFeeAmt(), BigDecimal.ZERO));
            repaymentInfoEntity.setActuallyGuaraFeeAmount(Convert.toBigDecimal(plan.getSetlGuaraFeeAmt(), BigDecimal.ZERO));
            repaymentInfoEntity.setActuallyGuaraFeeOdAmount(Convert.toBigDecimal(plan.getSetlGuaraFeeOdAmt(), BigDecimal.ZERO));

            repaymentInfoEntity.setActuallyPrincipal(ObjUtil.isNotNull(plan.getSetlPrcp()) ? plan.getSetlPrcp() : BigDecimal.ZERO );
            repaymentInfoEntity.setActuallyInterest(ObjUtil.isNotNull(plan.getSetlNormInt()) ? plan.getSetlNormInt() : BigDecimal.ZERO );
            repaymentInfoEntity.setActuallyPenaltyInterest(ObjUtil.isNotNull(plan.getSetlOdIntAmt()) ? plan.getSetlOdIntAmt() : BigDecimal.ZERO );
            repaymentInfoEntity.setActuallyPenalty(BigDecimal.ZERO);

            repaymentInfoEntity.setActuallyAmountTotal(actuallyTotal);
            //还款状态判断
            FundRepayStatusEnum fundRepayStatusEnum =  checkReStatus(plan);
            repaymentInfoEntity.setRepaymentStatus(fundRepayStatusEnum);

            boolean isOverdue = ObjUtil.equals("Y", plan.getPsOdInd());

            repaymentInfoEntity.setIsOverdue(isOverdue ? 1 : 0);
            Long count = fundRepaymentInfoMapper.selectCount(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                    .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                    .eq(FundRepaymentInfoEntity::getTerm, term)
                    .eq(FundRepaymentInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                    .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
            );
            if (Objects.equals(count, 0L)) {
                log.info("ChangYinServiceImpl.queryPlanUpdate insert orderId:{}, term:{} plan:{}", orderId, term, JSONUtil.toJsonStr(plan));
            } else {
                fundRepaymentInfoMapper.update(repaymentInfoEntity, new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                        .eq(FundRepaymentInfoEntity::getTerm, term)
                        .eq(FundRepaymentInfoEntity::getFundId, FundEnum.CHANG_YIN.getValue())
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                );
            }

        }

    }

}

