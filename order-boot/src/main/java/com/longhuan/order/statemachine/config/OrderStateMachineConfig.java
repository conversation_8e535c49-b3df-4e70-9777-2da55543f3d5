package com.longhuan.order.statemachine.config;

import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.statemachine.action.*;
import com.longhuan.order.statemachine.aguard.CustomerAppointmentFinishGuard;
import com.longhuan.order.statemachine.aguard.QuotaControlGuard;
import com.longhuan.order.statemachine.aguard.PaymentApplyGuard;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.order.statemachine.persister.OrderStateMachinePersist;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.statemachine.region.RegionExecutionPolicy;

import java.util.EnumSet;

@Slf4j
@Configuration
@EnableStateMachineFactory
public class OrderStateMachineConfig extends EnumStateMachineConfigurerAdapter<States, Events> {

    public static final String MACHINE_ID = "进件流程";
    @Resource
    private OrderStateMachinePersist orderStateMachinePersist;

    @Resource
    private OrderApproveAction orderApproveAction;
    @Resource
    private CustomerAppointmentFinishAction customerAppointmentFinishAction;
    @Resource
    private CustomerAppointmentFinishGuard customerAppointmentFinishGuard;
    @Resource
    private CustomerAppointmentAction customerAppointmentAction;
    @Resource
    private OverallReviewAction overallReviewAction;
    @Resource
    private RiskFirstApproveAction riskFirstApproveAction;
    @Resource
    private BussinessAddedInfoAction bussinessAddedInfoAction;
    @Resource
    private ApproveReturnAction approveReturnAction;
    @Resource
    private PaymentApplyGuard paymentApplyGuard;
    @Resource
    private QuotaControlGuard quotaControlGuard;
    @Resource
    private QualityInspectionAction qualityInspectionAction;
    @Resource
    private CustomerConformAction customerConformAction;

    @Resource
    private FundSigningRejectAction fundSigningRejectAction;
    @Resource
    private RiskFinalApproveAction riskFinalApproveAction;
    @Resource
    private FundsFinalApproveAction fundsFinalApproveAction;
    @Resource
    private OrderTerminationAction orderTerminationAction;
    @Resource
    private    FundPaymentFailAction fundPaymentFailAction;
    @Resource
    private PaymentContractApproveAction paymentContractApproveAction;
    @Autowired
    private PaymentSuccessAction paymentSuccessAction;

    @Autowired  AlAuditAction alAuditAction;

    @Bean
    public StateMachinePersister<States, Events, Integer> stateMachinePersist() {
        return new DefaultStateMachinePersister<>(orderStateMachinePersist);
    }

    @Override
    public void configure(StateMachineStateConfigurer<States, Events> states)
            throws Exception {
        states
                .withStates()
                .region("MAIN")
                .initial(States.BUSINESS_ADDED_INFO)
                .end(States.SETTLED)
                .end(States.PROCESS_TERMINAL)
                .states(EnumSet.allOf(States.class));
    }

    /**
     * 节点变更配置： external, internal, and local
     * withExternal 是当source和target不同时的写法，如上例子
     * withInternal 当source和target相同时的串联写法，比如付款失败时，付款前及付款后都是待付款状态
     * withChoice 当执行一个动作，可能导致多种结果时，可以选择使用choice+guard来跳转
     *
     * @param transitions the {@link StateMachineTransitionConfigurer}
     * @throws Exception
     */
    @Override
    public void configure(StateMachineTransitionConfigurer<States, Events> transitions)
            throws Exception {
        transitions
                // 补录提交
                .withExternal()
                .source(States.BUSINESS_ADDED_INFO).target(States.QUALITY_INSPECTION)
                .event(Events.AGREES).action(bussinessAddedInfoAction, errorHandlerAction())

                // 补录作废
                .and()
                .withExternal()
                .source(States.BUSINESS_ADDED_INFO).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())

                // 客服质检 提交
                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION).target(States.RISK_FIRST_APPROVE_ASSIGN)
                .event(Events.AGREES).action(qualityInspectionAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION).target(States.OVERALL_REVIEW)
                .event(Events.AGREES_QUALITY_INSPECTION_2_OVERALL_REVIEW).action(orderApproveAction, errorHandlerAction())

                // 客服质检 驳回
                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION).target(States.BUSINESS_ADDED_INFO)
                .event(Events.BACK).action(orderApproveAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION).target(States.BUSINESS_ADDED_INFO)
                .event(Events.BACK_QUALITY_INSPECTION_2_BUSINESS_ADDED_INFO).action(orderApproveAction, errorHandlerAction())

                // 客服质检 拒绝
                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION).target(States.PROCESS_TERMINAL)
                .event(Events.REJECT).action(orderTerminationAction, errorHandlerAction())

                // 客服质检  通过 事故车拒绝
                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION).target(States.PROCESS_TERMINAL)
                .event(Events.AGREES_QUALITY_INSPECTION_2_ACCIDENT_VEHICLE_REJECT).action(orderTerminationAction, errorHandlerAction())

                // 客服质检  通过 门店车评
                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION).target(States.STORE_EVALUATION)
                .event(Events.AGREES_QUALITY_INSPECTION_2_STORE_EVALUATION).action(orderApproveAction, errorHandlerAction())


                //TODO 客服质检如果走al  ，都放到al智能审核节点处理
                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION).target(States.QUALITY_INSPECTION_FINISH)
                .event(Events.AGREES_QUALITY_INSPECTION_2_AL_INTELLIGENT_AUDIT).action(alAuditAction, errorHandlerAction())


                // TODO al智能审核节点 跳转   1. 门店车评  2. 总评复合  3. 风控初审 4.终审          回调需要走的
                 // al智能审核节点 -> 门店车评
                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION_FINISH).target(States.STORE_EVALUATION)
                .event(Events.AL_INTELLIGENT_AUDIT_2_STORE_EVALUATION).action(orderApproveAction, errorHandlerAction())
                 // al智能审核节点 -> 总评复核
                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION_FINISH).target(States.OVERALL_REVIEW)
                .event(Events.AL_INTELLIGENT_AUDIT_2_OVERALL_REVIEW).action(orderApproveAction, errorHandlerAction())
                 // al智能审核节点 -> 风控初审分配
                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION_FINISH).target(States.RISK_FIRST_APPROVE_ASSIGN)
                .event(Events.AL_INTELLIGENT_AUDIT_2_RISK_FIRST_APPROVE_ASSIGN).action(qualityInspectionAction, errorHandlerAction())
                 // al智能审核节点 -> 资方终审
                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION_FINISH).target(States.FUNDS_FINAL_APPROVE)
                .event(Events.AL_INTELLIGENT_AUDIT_2_FUNDS_FINAL_APPROVE).action(riskFinalApproveAction, errorHandlerAction())
                // al智能审核节点 -> 客服质检
                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION_FINISH).target(States.QUALITY_INSPECTION)
                .event(Events.AL_INTELLIGENT_AUDIT_2_QUALITY_INSPECTION).action(orderApproveAction, errorHandlerAction())




                // 门店车评 通过
                .and()
                .withExternal()
                .source(States.STORE_EVALUATION).target(States.RISK_FIRST_APPROVE_ASSIGN)
                .event(Events.AGREES).action(overallReviewAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.STORE_EVALUATION).target(States.OVERALL_REVIEW)
                .event(Events.AGREES_STORE_EVALUATION_2_OVERALL_REVIEW).action(orderApproveAction, errorHandlerAction())

                //todo  门店车评到终审
                .and()
                .withExternal()
                .source(States.STORE_EVALUATION).target(States.FUNDS_FINAL_APPROVE)
                .event(Events.AL_INTELLIGENT_AUDIT_2_FUNDS_FINAL_APPROVE).action(riskFinalApproveAction, errorHandlerAction())


                // 总评复核 提交
                .and()
                .withExternal()
                .source(States.OVERALL_REVIEW).target(States.RISK_FIRST_APPROVE_ASSIGN)
                .event(Events.AGREES).action(overallReviewAction, errorHandlerAction())

                // 总评复核 驳回
                .and()
                .withExternal()
                .source(States.OVERALL_REVIEW).target(States.QUALITY_INSPECTION)
                .event(Events.BACK).action(orderApproveAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.OVERALL_REVIEW).target(States.BUSINESS_ADDED_INFO)
                .event(Events.BACK_OVERALL_REVIEW_2_BUSINESS_ADDED_INFO).action(orderApproveAction, errorHandlerAction())

                //todo  总评复核 到 终审
                .and()
                .withExternal()
                .source(States.OVERALL_REVIEW).target(States.FUNDS_FINAL_APPROVE)
                .event(Events.OVERALL_REVIEW_2_FUNDS_FINAL_APPROVE).action(riskFinalApproveAction, errorHandlerAction())


                // 总评复核 拒绝
                .and()
                .withExternal()
                .source(States.OVERALL_REVIEW).target(States.PROCESS_TERMINAL)
                .event(Events.REJECT).action(orderTerminationAction, errorHandlerAction())

                // 初审分配
                .and()
                .withExternal()
                .source(States.RISK_FIRST_APPROVE_ASSIGN).target(States.RISK_FIRST_APPROVE)
                .event(Events.AGREES).action(orderApproveAction, errorHandlerAction())

                // 初审通过
                .and()
                .withExternal()
                .source(States.RISK_FIRST_APPROVE).target(States.RISK_FINAL_APPROVE)
                .event(Events.AGREES).action(riskFirstApproveAction, errorHandlerAction())

                // 初审通过 单人审批
                .and()
                .withExternal()
                .source(States.RISK_FIRST_APPROVE).target(States.FUNDS_FINAL_APPROVE)
                .event(Events.AGREES_RISK_FIRST_SINGLE_AGREES).action(riskFirstApproveAction, errorHandlerAction())

                // 初审拒绝
                .and()
                .withExternal()
                .source(States.RISK_FIRST_APPROVE).target(States.PROCESS_TERMINAL)
                .event(Events.REJECT).action(riskFirstApproveAction, errorHandlerAction())

                // 初审驳回
                .and()
                .withExternal()
                .source(States.RISK_FIRST_APPROVE).target(States.QUALITY_INSPECTION)
                .event(Events.BACK).action(riskFirstApproveAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.RISK_FIRST_APPROVE).target(States.BUSINESS_ADDED_INFO)
                .event(Events.BACK_RISK_FIRST_APPROVE_2_BUSINESS_ADDED_INFO).action(orderApproveAction, errorHandlerAction())


                .and()
                .withExternal()
                .source(States.RISK_FIRST_APPROVE).target(States.QUALITY_INSPECTION)
                .event(Events.BACK_RISK_FIRST_APPROVE_2_QUALITY_INSPECTION).action(orderApproveAction, errorHandlerAction())


                .and()
                .withExternal()
                .source(States.RISK_FIRST_APPROVE).target(States.OVERALL_REVIEW)
                .event(Events.BACK_RISK_FIRST_APPROVE_2_OVERALL_REVIEW).action(orderApproveAction, errorHandlerAction())


                // 终审通过
                .and()
                .withExternal()
                .source(States.RISK_FINAL_APPROVE).target(States.FUNDS_FINAL_APPROVE)
                .event(Events.AGREES).action(riskFinalApproveAction, errorHandlerAction())

                // 终审拒绝
                .and()
                .withExternal()
                .source(States.RISK_FINAL_APPROVE).target(States.PROCESS_TERMINAL)
                .event(Events.REJECT).action(orderTerminationAction, errorHandlerAction())
                // 终审驳回
                .and()
                .withExternal()
                .source(States.RISK_FINAL_APPROVE).target(States.RISK_FIRST_APPROVE_ASSIGN)
                .event(Events.BACK).action(overallReviewAction, errorHandlerAction())
                .and()
                .withExternal()
                .source(States.RISK_FINAL_APPROVE).target(States.BUSINESS_ADDED_INFO)
                .event(Events.BACK_RISK_FINAL_APPROVE_2_BUSINESS_ADDED_INFO).action(orderApproveAction, errorHandlerAction())
                .and()
                .withExternal()
                .source(States.RISK_FINAL_APPROVE).target(States.QUALITY_INSPECTION)
                .event(Events.BACK_RISK_FINAL_APPROVE_2_QUALITY_INSPECTION).action(orderApproveAction, errorHandlerAction())   .and();


                // 资方审批 -> 客户确认
             transitions
                .withExternal()
                .source(States.FUNDS_FINAL_APPROVE)
                .target(States.CUSTOMER_CONFIRM)
                .event(Events.AGREES)
                .guard(quotaControlGuard)
                .action(fundsFinalApproveAction, errorHandlerAction())

                // 资方审批 拒绝
                .and()
                .withExternal()
                .source(States.FUNDS_FINAL_APPROVE).target(States.PROCESS_TERMINAL)
                .event(Events.REJECT).action(orderTerminationAction, errorHandlerAction())

                // 资方审批 驳回
                .and()
                .withExternal()
                .source(States.FUNDS_FINAL_APPROVE).target(States.BUSINESS_ADDED_INFO)
                .event(Events.BACK).action(orderApproveAction, errorHandlerAction())


                // 客户确认 -> 客户预约
                .and()
                .withExternal()
                .source(States.CUSTOMER_CONFIRM).target(States.CUSTOMER_APPOINTMENT)
                .event(Events.AGREES)
                .guard(quotaControlGuard)
                .action(customerConformAction, errorHandlerAction())



                // 面签
                .and().withInternal().source(States.CUSTOMER_APPOINTMENT)
                .event(Events.REVIEW_APPOINTMENT_FINISH).action(customerAppointmentAction, errorHandlerAction())
                .and().withInternal().source(States.CUSTOMER_APPOINTMENT)
                .event(Events.REVIEW_APPOINTMENT_START).action(orderApproveAction, errorHandlerAction())

                // GPS安装
                .and().withInternal().source(States.CUSTOMER_APPOINTMENT)
                .event(Events.GPS_INSTALL_FINISH).action(customerAppointmentAction, errorHandlerAction())

                .and().withInternal().source(States.CUSTOMER_APPOINTMENT)
                .event(Events.GPS_INSTALL_APPLY).action(orderApproveAction, errorHandlerAction())

                // 合同签约
                .and().withInternal().source(States.CUSTOMER_APPOINTMENT)
                .event(Events.CONTRACT_SIGNING_FINISH).action(customerAppointmentAction, errorHandlerAction()).and();
//                .and().withInternal().source(States.CUSTOMER_APPOINTMENT)
//                .event(Events.CONTRACT_SIGNING_S).action(orderApproveAction, errorHandlerAction())

        // 抵押
        transitions.withInternal().source(States.CUSTOMER_APPOINTMENT)
                .event(Events.MORTGAGE_PRE_STAKE_FINISH).action(customerAppointmentAction, errorHandlerAction())


                .and().withInternal().source(States.CUSTOMER_APPOINTMENT)
                .event(Events.MORTGAGE_LOAN_FINISH).action(customerAppointmentAction, errorHandlerAction())

                .and().withInternal().source(States.PAYMENT_APPLY_INFORMATION)
                .event(Events.MORTGAGE_LOAN_FINISH).action(orderApproveAction, errorHandlerAction())
                .and().withInternal().source(States.FUNDS_PAYMENT_APPROVAL)
                .event(Events.MORTGAGE_LOAN_FINISH).action(orderApproveAction, errorHandlerAction())
                .and().withInternal().source(States.PAYMENT_CONTRACT_APPROVAL)
                .event(Events.MORTGAGE_LOAN_FINISH).action(orderApproveAction, errorHandlerAction())
                .and().withInternal().source(States.FUNDS_PAYMENT_PROCESS)
                .event(Events.MORTGAGE_LOAN_FINISH).action(orderApproveAction, errorHandlerAction())
                .and().withInternal().source(States.FUNDS_PAYMENT_FAIL)
                .event(Events.MORTGAGE_LOAN_FINISH).action(orderApproveAction, errorHandlerAction())
                .and().withInternal().source(States.PAYMENT_SUCCESS)
                .event(Events.MORTGAGE_LOAN_FINISH).action(orderApproveAction, errorHandlerAction())
                .and()

        ;



        transitions.withInternal().source(States.CUSTOMER_APPOINTMENT)
                .event(Events.MORTGAGE_LOAN_START).action(orderApproveAction, errorHandlerAction())
                // 费用支付
                .and().withInternal().source(States.CUSTOMER_APPOINTMENT)
                .event(Events.GPS_FEE_PAY_FINISH).action(customerAppointmentAction, errorHandlerAction())

                .and().withInternal().source(States.CUSTOMER_APPOINTMENT)
                .event(Events.GPS_FEE_PAY_APPLY).action(orderApproveAction, errorHandlerAction())

                // 客户预约 完成
                .and().withExternal()
                .source(States.CUSTOMER_APPOINTMENT).target(States.PAYMENT_APPLY_INFORMATION)
                .event(Events.AGREES)
                .guard(customerAppointmentFinishGuard)
                .action(customerAppointmentFinishAction, errorHandlerAction())

                // 客户预约 驳回
                .and().withExternal()
                .source(States.CUSTOMER_APPOINTMENT).target(States.CUSTOMER_CONFIRM)
                .event(Events.BACK)
                .action(orderApproveAction, errorHandlerAction())

                // 客户预约 拒绝
                .and().withExternal()
                .source(States.CUSTOMER_APPOINTMENT).target(States.PROCESS_TERMINAL)
                .event(Events.REJECT)
                .action(orderTerminationAction, errorHandlerAction())

                // 用信拒绝
                .and()
                .withExternal()
                .source(States.CUSTOMER_APPOINTMENT).target(States.PAYMENT_APPLY_INFORMATION)
                .event(Events.FUND_SIGNING_REJECT)
                .action(fundSigningRejectAction, errorHandlerAction())


                // 放款申请 通过
                .and()
                .withExternal()
                .source(States.PAYMENT_APPLY_INFORMATION).target(States.PAYMENT_CONTRACT_APPROVAL)
//                .guard(paymentApplyGuard)
                .event(Events.AGREES).action(orderApproveAction, errorHandlerAction())

                // 放款申请 驳回
                .and()
                .withExternal()
                .source(States.PAYMENT_APPLY_INFORMATION).target(States.CUSTOMER_APPOINTMENT)
                .event(Events.BACK).action(orderApproveAction, errorHandlerAction())


                .and()
                .withExternal()
                .source(States.PAYMENT_APPLY_INFORMATION).target(States.BUSINESS_ADDED_INFO)
                .event(Events.BACK_PAYMENT_APPLY_INFORMATION_2_BUSINESS_ADDED_INFO).action(orderApproveAction, errorHandlerAction())


                // 放款申请 拒绝
                .and()
                .withExternal()
                .source(States.PAYMENT_APPLY_INFORMATION).target(States.PROCESS_TERMINAL)
                .event(Events.REJECT).action(orderTerminationAction, errorHandlerAction())

                // 合同审批 通过
                .and()
                .withExternal()
                .source(States.PAYMENT_CONTRACT_APPROVAL).target(States.FUNDS_PAYMENT_APPROVAL)
                .event(Events.AGREES).action(paymentContractApproveAction, errorHandlerAction())

                // 合同审批 驳回
                .and()
                .withExternal()
                .source(States.PAYMENT_CONTRACT_APPROVAL).target(States.PAYMENT_APPLY_INFORMATION)
                .event(Events.BACK).action(orderApproveAction, errorHandlerAction())

                // 合同审批 拒绝
                .and()
                .withExternal()
                .source(States.PAYMENT_CONTRACT_APPROVAL).target(States.PROCESS_TERMINAL)
                .event(Events.REJECT).action(orderTerminationAction, errorHandlerAction())

                // 资方放款审批 通过
                .and()
                .withExternal()
                .source(States.FUNDS_PAYMENT_APPROVAL).target(States.PAYMENT_SUCCESS)
                .event(Events.AGREES).action(paymentSuccessAction, errorHandlerAction())

                // 资方放款审批 失败
                .and()
                .withExternal()
                .source(States.FUNDS_PAYMENT_APPROVAL).target(States.FUNDS_PAYMENT_FAIL)
                .event(Events.FAIL).action(fundPaymentFailAction, errorHandlerAction())

                // 资方放款审批 拒绝
                .and()
                .withExternal()
                .source(States.FUNDS_PAYMENT_APPROVAL).target(States.PROCESS_TERMINAL)
                .event(Events.REJECT).action(orderTerminationAction, errorHandlerAction())

                // 资方放款审批 驳回
                .and()
                .withExternal()
                .source(States.FUNDS_PAYMENT_APPROVAL).target(States.PAYMENT_CONTRACT_APPROVAL)
                .event(Events.BACK).action(orderApproveAction, errorHandlerAction())

                // 资方放款审批 驳回到请款资料
                .and()
                .withExternal()
                .source(States.FUNDS_PAYMENT_APPROVAL).target(States.PAYMENT_APPLY_INFORMATION)
                .event(Events.BACK_FUNDS_PAYMENT_PAYMENT_APPLY_INFORMATION).action(orderApproveAction, errorHandlerAction()).and();

        transitions
                .withExternal()
                .source(States.FUNDS_PAYMENT_FAIL).target(States.PAYMENT_APPLY_INFORMATION)
                .event(Events.RE_PAYMENT_PAY).action(orderApproveAction, errorHandlerAction()).and();





        // 订单取消
        transitions
                .withExternal()
                .source(States.BUSINESS_ADDED_INFO).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION_FINISH).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.STORE_EVALUATION).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.OVERALL_REVIEW).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withExternal()
                .source(States.RISK_FIRST_APPROVE).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withExternal()
                .source(States.RISK_FIRST_APPROVE_ASSIGN).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withExternal()
                .source(States.RISK_FINAL_APPROVE).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.FUNDS_FINAL_APPROVE).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.CUSTOMER_CONFIRM).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.CUSTOMER_APPOINTMENT).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.PAYMENT_APPLY_INFORMATION).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.PAYMENT_CONTRACT_APPROVAL).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.FUNDS_PAYMENT_FAIL).target(States.PROCESS_TERMINAL)
                .event(Events.CANCEL).action(orderTerminationAction, errorHandlerAction()).and();


        // 合同重签
        transitions
                .withInternal()
                .source(States.CUSTOMER_APPOINTMENT)
                .event(Events.RESIGN_CONTRACT).action(orderApproveAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.PAYMENT_APPLY_INFORMATION).target(States.CUSTOMER_APPOINTMENT)
                .event(Events.RESIGN_CONTRACT).action(orderApproveAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.PAYMENT_CONTRACT_APPROVAL).target(States.CUSTOMER_APPOINTMENT)
                .event(Events.RESIGN_CONTRACT).action(orderApproveAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.FUNDS_PAYMENT_FAIL).target(States.CUSTOMER_APPOINTMENT)
                .event(Events.RESIGN_CONTRACT).action(orderApproveAction, errorHandlerAction()).and();


        // 重新绑卡
        transitions
                .withInternal()
                .source(States.CUSTOMER_APPOINTMENT)
                .event(Events.REBIND_BLANK_CARD).action(orderApproveAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.PAYMENT_APPLY_INFORMATION).target(States.CUSTOMER_APPOINTMENT)
                .event(Events.REBIND_BLANK_CARD).action(orderApproveAction, errorHandlerAction())

                .and()
                .withExternal()
                .source(States.PAYMENT_CONTRACT_APPROVAL).target(States.CUSTOMER_APPOINTMENT)
                .event(Events.REBIND_BLANK_CARD).action(orderApproveAction, errorHandlerAction())
                .and()
                .withExternal()
                .source(States.FUNDS_PAYMENT_FAIL).target(States.CUSTOMER_APPOINTMENT)
                .event(Events.REBIND_BLANK_CARD).action(orderApproveAction, errorHandlerAction()).and();


        // 系统终止
        transitions
                .withLocal()
                .source(States.BUSINESS_ADDED_INFO).target(States.SYSTEM_TERMINAL)
                .event(Events.SYSTEM_TERMINAL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withLocal()
                .source(States.QUALITY_INSPECTION).target(States.SYSTEM_TERMINAL)
                .event(Events.SYSTEM_TERMINAL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withLocal()
                .source(States.STORE_EVALUATION).target(States.SYSTEM_TERMINAL)
                .event(Events.SYSTEM_TERMINAL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withLocal()
                .source(States.OVERALL_REVIEW).target(States.SYSTEM_TERMINAL)
                .event(Events.SYSTEM_TERMINAL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withLocal()
                .source(States.RISK_FIRST_APPROVE).target(States.SYSTEM_TERMINAL)
                .event(Events.SYSTEM_TERMINAL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withLocal()
                .source(States.RISK_FINAL_APPROVE).target(States.SYSTEM_TERMINAL)
                .event(Events.SYSTEM_TERMINAL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withLocal()
                .source(States.RISK_FIRST_APPROVE_ASSIGN).target(States.SYSTEM_TERMINAL)
                .event(Events.SYSTEM_TERMINAL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withLocal()
                .source(States.CUSTOMER_CONFIRM).target(States.SYSTEM_TERMINAL)
                .event(Events.SYSTEM_TERMINAL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withLocal()
                .source(States.CUSTOMER_APPOINTMENT).target(States.SYSTEM_TERMINAL)
                .event(Events.SYSTEM_TERMINAL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withLocal()
                .source(States.PAYMENT_CONTRACT_APPROVAL).target(States.SYSTEM_TERMINAL)
                .event(Events.SYSTEM_TERMINAL).action(orderTerminationAction, errorHandlerAction())
                .and()
                .withLocal()
                .source(States.PAYMENT_APPLY_INFORMATION).target(States.SYSTEM_TERMINAL)
                .event(Events.SYSTEM_TERMINAL).action(orderTerminationAction, errorHandlerAction())



                // 结清
                .and()
                .withLocal()
                .source(States.PAYMENT_SUCCESS).target(States.SETTLED)
                .event(Events.SETTLED).action(orderApproveAction, errorHandlerAction()).and();
//              ============驳回到订单冻结================
                //门店车评驳回到订单冻结
        transitions
                .withExternal()
                .source(States.STORE_EVALUATION).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //客服质检驳回到订单冻结
                .and()
                .withExternal()
                .source(States.QUALITY_INSPECTION).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //总评复核驳回到订单冻结
                .and()
                .withExternal()
                .source(States.OVERALL_REVIEW).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //风控初审驳回到订单冻结
                .and()
                .withExternal()
                .source(States.RISK_FIRST_APPROVE).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //风控终审驳回到订单冻结
                .and()
                .withExternal()
                .source(States.RISK_FINAL_APPROVE).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //资方终审驳回到订单冻结
                .and()
                .withExternal()
                .source(States.FUNDS_FINAL_APPROVE).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //客户确认驳回到订单冻结
                .and()
                .withExternal()
                .source(States.CUSTOMER_CONFIRM).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //客户预约驳回到订单冻结
                .and()
                .withExternal()
                .source(States.CUSTOMER_APPOINTMENT).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //面签驳回到订单冻结
                .and()
                .withExternal()
                .source(States.MANAGER_INTERVIEW).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //合同签约驳回到订单冻结
                .and()
                .withExternal()
                .source(States.CONTRACT_SIGNING).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //安装GPS驳回到订单冻结
                .and()
                .withExternal()
                .source(States.GPS_INSTALL_APPLY).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //抵押待办驳回到订单冻结
                .and()
                .withExternal()
                .source(States.MORTGAGE_PENDING).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //GPS费用支付驳回到订单冻结
                .and()
                .withExternal()
                .source(States.GPS_FEE_PAYMENT).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //安装GPS完成驳回到订单冻结
                .and()
                .withExternal()
                .source(States.GPS_INSTALL_FINISH).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //请款资料驳回到订单冻结
                .and()
                .withExternal()
                .source(States.PAYMENT_APPLY_INFORMATION).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
                //合同审批驳回到订单冻结
                .and()
                .withExternal()
                .source(States.PAYMENT_CONTRACT_APPROVAL).target(States.ORDER_FREEZE)
                .event(Events.PRE_INFO_UPDATE_BACK_ORDER_FREEZE).action(orderApproveAction, errorHandlerAction())
        ;

    }

    @Override
    public void configure(StateMachineConfigurationConfigurer<States, Events> config) throws Exception {

        config.withConfiguration().regionExecutionPolicy(RegionExecutionPolicy.PARALLEL)

                //                .listener(orderStateMachineListener)
                .machineId(MACHINE_ID);
    }

    /**
     * 异常处理Action
     *
     * @return action对象
     */
    @Bean(name = "errorHandlerAction")
    public Action<States, Events> errorHandlerAction() {

        return context -> {
            Exception exception = context.getException();
            log.error("stateMachine execute error = ", exception);
            context.getStateMachine()
                    .getExtendedState().getVariables()
                    .put(BusinessException.class, exception);
            throw new RuntimeException(exception);

        };
    }

}
