package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.common.core.enums.SignPlateEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.GpsChargeRuleDTO;
import com.longhuan.order.pojo.dto.GpsChargeRuleUpdateDTO;
import com.longhuan.order.pojo.dto.GpsNoChargePersonListDTO;
import com.longhuan.order.pojo.dto.OrderIdDTO;
import com.longhuan.order.pojo.dto.StoreGpsConfigDTO;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.GpsChargeRuleVO;
import com.longhuan.order.pojo.vo.GpsNoChargePersonListVO;
import com.longhuan.order.pojo.vo.GpsPayVO;
import com.longhuan.order.pojo.vo.StoreGpsConfigVO;
import com.longhuan.order.service.CustomerAppointmentService;
import com.longhuan.order.service.GpsPayService;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.user.pojo.vo.UserInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
@Slf4j
@Service
@RequiredArgsConstructor
public class GpsPayServiceImpl extends MPJBaseServiceImpl<GpsChargingRulesInfoMapper, GpsChargingRulesInfoEntity> implements GpsPayService {
    private final CustomerAppointmentService customerAppointmentService;
    private final OrderInfoMapper orderInfoMapper;
    private final GpsChargingRulesInfoMapper gpsChargingRulesInfoMapper;
    private final BankAccountSignMapper bankAccountSignMapper;
    private final OrderFeeInfoMapper orderFeeInfoMapper;
    private final GpsNoChargeUseInfoMapper gpsNoChargeUseInfoMapper;
    private final UserFeign userFeign;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final GpsInstallmentInfoMapper gpsInstallmentInfoMapper;
    private final RedisService redisService;
    private final OrderStateService orderStateService;
    private final StoreGpsConfigMapper storeGpsConfigMapper;

    @Override
    public GpsPayVO queryChargeRuleAndPage(OrderIdDTO dto) {
//        log.info("GpsPayServiceImpl.queryChargeRuleAndPage.dto:{}", dto);
        boolean isOnlineOrder = customerAppointmentService.isOnlineOrder(dto.getOrderId());
        boolean isFree = this.isFree(dto.getOrderId(),isOnlineOrder);

        return new GpsPayVO().setInstallment(isOnlineOrder).setFree(isFree);
    }

    @Override
    public Boolean installment(OrderIdDTO dto, LoginUser loginUser) {
        String lockKey = "gps:installment:lock:" + dto.getOrderId();
        String lockValue = IdUtil.randomUUID();
        try {
            // 尝试获取分布式锁，设置超时时间10秒
            if (!redisService.tryLock(lockKey, lockValue, 10)) {
                throw new BusinessException("操作过于频繁，请稍后重试");
            }
            boolean isOnlineOrder = customerAppointmentService.isOnlineOrder(dto.getOrderId());
            Assert.isTrue(isOnlineOrder, "非线上订单无法使用分期付款");
            //校验绑卡
            BankAccountSignEntity bankAccountSignEntity = bankAccountSignMapper.selectOne(new MPJLambdaWrapper<BankAccountSignEntity>()
                    .eq(ObjUtil.isNotNull(dto.getOrderId()), BankAccountSignEntity::getOrderId, dto.getOrderId())
                    .isNull(ObjUtil.isNull(dto.getOrderId()), BankAccountSignEntity::getOrderId)
                    .eq(BankAccountSignEntity::getDeleteFlag, 0)
                    .eq(BankAccountSignEntity::getSignState, 1)
                    .eq(BankAccountSignEntity::getSignPlate, SignPlateEnum.BAO_FU.getValue()));
            Assert.notNull(bankAccountSignEntity, "请先完成绑卡");

            // 查询订单费用信息
            OrderFeeInfoEntity orderFeeInfo = orderFeeInfoMapper.selectOne(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                    .eq(OrderFeeInfoEntity::getOrderId, dto.getOrderId())
                    .eq(OrderFeeInfoEntity::getFeeType, 1)
                    .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderFeeInfoEntity::getCreateTime)
                    .last("limit 1"));

            if (ObjUtil.isEmpty(orderFeeInfo)) {
                orderFeeInfo = new OrderFeeInfoEntity()
                        .setOrderId(dto.getOrderId())
                        .setGpsTotalFee(BigDecimal.ZERO)
                        .setCarServiceFee(BigDecimal.ZERO)
                        .setGpsFeeStatus(2)
                        .setFeeType(1)
                        .setGpsPayType(2);
                orderFeeInfoMapper.insert(orderFeeInfo);
            }
            if (orderFeeInfo.getGpsFeeStatus() == 2) {
                switch (orderFeeInfo.getGpsPayType()) {
                    case 1 -> throw new BusinessException("您已一次性支付完成，请勿再次选择");
                    case 3 -> throw new BusinessException("您已选择不收费，请勿重复选择");
                    case 0 -> throw new BusinessException("GPS费用已完成，请勿重复选择");
                }
            } else {
                orderFeeInfoMapper.update(new LambdaUpdateWrapper<OrderFeeInfoEntity>()
                        .set(OrderFeeInfoEntity::getDeleteFlag, 1)
                        .ne(OrderFeeInfoEntity::getId, orderFeeInfo.getId())
                        .eq(OrderFeeInfoEntity::getOrderId, orderFeeInfo.getOrderId())
                        .eq(OrderFeeInfoEntity::getDeleteFlag, 0));
                orderFeeInfoMapper.update(new LambdaUpdateWrapper<OrderFeeInfoEntity>()
                        .set(OrderFeeInfoEntity::getCarServiceFee, BigDecimal.ZERO)
                        .set(OrderFeeInfoEntity::getGpsTotalFee,BigDecimal.ZERO)
                        .set(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                        .set(OrderFeeInfoEntity::getGpsPayType, 2)
                        .set(OrderFeeInfoEntity::getGpsPayChooseTime, LocalDateTime.now())
                        .eq(OrderFeeInfoEntity::getId, orderFeeInfo.getId()));
                Integer userId = 1;
                if (ObjUtil.isNotNull(loginUser)) {
                    userId = loginUser.getUserId();
                }
                orderStateService.sendEvent(States.CUSTOMER_APPOINTMENT, Events.GPS_FEE_PAY_FINISH, dto.getOrderId(), userId);
            }
        } finally {
            // 确保锁被释放
            redisService.releaseLock(lockKey, lockValue);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean thereIsNoCharge(OrderIdDTO dto, LoginUser loginUser) {
        String lockKey = "gps:installment:lock:" + dto.getOrderId();
        String lockValue = IdUtil.randomUUID();
        try {
            // 尝试获取分布式锁，设置超时时间10秒
            if (!redisService.tryLock(lockKey, lockValue, 10)) {
                throw new BusinessException("操作过于频繁，请稍后重试");
            }
            boolean isOnlineOrder = customerAppointmentService.isOnlineOrder(dto.getOrderId());
            Assert.isTrue(isOnlineOrder, "非线上订单无法使用不收费");
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
            // 查询订单费用信息
            OrderFeeInfoEntity orderFeeInfo = orderFeeInfoMapper.selectOne(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                    .eq(OrderFeeInfoEntity::getOrderId, dto.getOrderId())
                    .eq(OrderFeeInfoEntity::getFeeType, 1)
                    .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderFeeInfoEntity::getCreateTime)
                    .last("limit 1"));

            if (ObjUtil.isEmpty(orderFeeInfo)) {
                orderFeeInfo = new OrderFeeInfoEntity()
                        .setOrderId(dto.getOrderId())
                        .setCarServiceFee(BigDecimal.ZERO)
                        .setGpsTotalFee(BigDecimal.ZERO)
                        .setGpsFeeStatus(0)
                        .setFeeType(1)
                        .setGpsPayType(3);
                orderFeeInfoMapper.insert(orderFeeInfo);
            }
            if (orderFeeInfo.getGpsFeeStatus() == 2) {
                switch (orderFeeInfo.getGpsPayType()) {
                    case 1 -> throw new BusinessException("您已一次性支付完成，请勿再次选择");
                    case 2 -> throw new BusinessException("您已选择分期支付，请勿重复选择");
                    case 0 -> throw new BusinessException("GPS费用已完成，请勿重复选择");
                }
                return true;
            }
            // 减少剩余名额
            int updated = gpsChargingRulesInfoMapper.update(new LambdaUpdateWrapper<GpsChargingRulesInfoEntity>()
                    .setDecrBy(GpsChargingRulesInfoEntity::getRemainingQuantity, 1)
                    .eq(GpsChargingRulesInfoEntity::getYear, LocalDateTime.now().getYear())
                    .eq(GpsChargingRulesInfoEntity::getMonth, LocalDateTime.now().getMonthValue())
                    .eq(GpsChargingRulesInfoEntity::getEnable, 0)
                    .gt(GpsChargingRulesInfoEntity::getRemainingQuantity, 0));

            if (updated <= 0) {
                throw new BusinessException("无剩余不收费名额");
            }

            //记录使用的订单
            Integer userId = 1;
            if (ObjUtil.isNotNull(loginUser) && ObjUtil.isNotNull(loginUser.getUserId())) {
                userId = loginUser.getUserId();
            }
            GpsNoChargeUseInfoEntity gpsNoChargeUseInfo = new GpsNoChargeUseInfoEntity();
            gpsNoChargeUseInfo.setOrderId(dto.getOrderId());
            gpsNoChargeUseInfo.setUserId(userId); // 假设OrderIdDTO中有userId字段，如果没有需要从上下文获取
            gpsNoChargeUseInfoMapper.insert(gpsNoChargeUseInfo);
            // 更新GPS支付类型和选择时间
            orderFeeInfoMapper.update(new LambdaUpdateWrapper<OrderFeeInfoEntity>()
                    .set(OrderFeeInfoEntity::getDeleteFlag, 1)
                    .ne(OrderFeeInfoEntity::getId, orderFeeInfo.getId())
                    .eq(OrderFeeInfoEntity::getOrderId, orderFeeInfo.getOrderId())
                    .eq(OrderFeeInfoEntity::getDeleteFlag, 0));
            orderFeeInfoMapper.update(new LambdaUpdateWrapper<OrderFeeInfoEntity>()
                    .set(OrderFeeInfoEntity::getGpsTotalFee, BigDecimal.ZERO)
                    .set(OrderFeeInfoEntity::getCarServiceFee, BigDecimal.ZERO)
                    .set(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                    .set(OrderFeeInfoEntity::getGpsPayType, 3)
                    .eq(OrderFeeInfoEntity::getId, orderFeeInfo.getId()));
            if (ObjUtil.isNotEmpty(orderFeeInfo)) {
                orderFeeInfo.setCarServiceFee(BigDecimal.ZERO);
                orderFeeInfo.setGpsTotalFee(BigDecimal.ZERO);
                orderFeeInfo.setGpsPayType(3); // 3表示不收费
                orderFeeInfo.setGpsFeeStatus(2);
                orderFeeInfo.setGpsPayChooseTime(LocalDateTime.now());
                orderFeeInfoMapper.updateById(orderFeeInfo);
                orderStateService.sendEvent(States.CUSTOMER_APPOINTMENT, Events.GPS_FEE_PAY_FINISH, dto.getOrderId(), userId);
            } else {
                throw new BusinessException("订单费用信息不存在");
            }
        } finally {
            // 确保锁被释放
            redisService.releaseLock(lockKey, lockValue);
        }
        return true;
    }

    @Override
    public Page<GpsChargeRuleVO> queryChargeRule(GpsChargeRuleDTO dto) {
        MPJLambdaWrapper<GpsChargingRulesInfoEntity> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper
                .select(GpsChargingRulesInfoEntity::getId)
                .select(GpsChargingRulesInfoEntity::getQuantity)
                .select(GpsChargingRulesInfoEntity::getRemainingQuantity)
                .select(GpsChargingRulesInfoEntity::getYear)
                .select(GpsChargingRulesInfoEntity::getMonth)
                .select(GpsChargingRulesInfoEntity::getEnable)
                .selectAs("(quantity - remaining_quantity)", GpsChargeRuleVO::getQuantityUsed)
                .eq(GpsChargingRulesInfoEntity::getDeleteFlag, 0)
                .eq(ObjUtil.isNotNull(dto.getYear()), GpsChargingRulesInfoEntity::getYear, dto.getYear())
                .eq(ObjUtil.isNotNull(dto.getMonth()), GpsChargingRulesInfoEntity::getMonth, dto.getMonth())
                .orderByAsc(GpsChargingRulesInfoEntity::getYear, GpsChargingRulesInfoEntity::getMonth);

        return gpsChargingRulesInfoMapper.selectJoinPage(
                new Page<>(dto.getPageNum(), dto.getPageSize()),
                GpsChargeRuleVO.class,
                queryWrapper
        );
    }
    @Override
    public Page<GpsNoChargePersonListVO> useNoChargePersonList(GpsNoChargePersonListDTO dto) {
        MPJLambdaWrapper<GpsNoChargeUseInfoEntity> wrapper = new MPJLambdaWrapper<>(GpsNoChargeUseInfoEntity.class)
                .eq(GpsNoChargeUseInfoEntity::getDeleteFlag, 0)
                .innerJoin(OrderInfoEntity.class, OrderInfoEntity::getId, GpsNoChargeUseInfoEntity::getOrderId)
                .innerJoin(OrderCustomerInfoEntity.class,OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .selectAll(OrderInfoEntity.class)
                .selectAs(OrderCustomerInfoEntity::getResidentialCityName, GpsNoChargePersonListVO::getResidentialCity)
                .selectAs(GpsNoChargeUseInfoEntity::getCreateTime, GpsNoChargePersonListVO::getNoChargeUseTime)
                .selectAs(GpsNoChargeUseInfoEntity::getUserId, GpsNoChargePersonListVO::getNoChargeUseSubmitterId)
                .eq(ObjUtil.isNotNull(dto.getFundId()), OrderInfoEntity::getFundId, dto.getFundId())
                .eq(ObjUtil.isNotNull(dto.getProductId()),OrderInfoEntity::getProductId,dto.getProductId())
                .like(ObjUtil.isNotNull(dto.getOrderNumber()), OrderInfoEntity::getOrderNumber, dto.getOrderNumber())
                .like(ObjUtil.isNotNull(dto.getCustomerName()), OrderInfoEntity::getCustomerName, dto.getCustomerName())
                .like(ObjUtil.isNotNull(dto.getCustomerPhone()), OrderInfoEntity::getCustomerPhone, dto.getCustomerPhone())
                .like(ObjUtil.isNotNull(dto.getFundName()), OrderInfoEntity::getFundName, dto.getFundName())
                .like(ObjUtil.isNotNull(dto.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, dto.getVehicleNumber())
                .like(ObjUtil.isNotNull(dto.getProductName()), OrderInfoEntity::getProductName, dto.getProductName());
        if (ObjectUtil.isNotEmpty(dto.getPaymentStartDate())&&ObjUtil.isNotEmpty(dto.getPaymentEndDate())){
            wrapper.between(OrderInfoEntity::getPaymentTime, dto.getPaymentStartDate(), dto.getPaymentEndDate());
        }
        if (StrUtil.isNotBlank(dto.getManagerName())){
            Result<List<Integer>> userIdByLikeNameList = userFeign.getUserIdByLikeNameList(dto.getManagerName());
            if (CollUtil.isEmpty(userIdByLikeNameList.getData())){
                return new Page<>();
            }
            wrapper.in(OrderInfoEntity::getManagerId, userIdByLikeNameList.getData());
        }
        Page<GpsNoChargePersonListVO> gpsNoChargePersonListVOPage = gpsNoChargeUseInfoMapper.selectJoinPage(new Page<>(dto.getPageNum(), dto.getPageSize()),
                GpsNoChargePersonListVO.class,wrapper );
        List<Integer> idList = gpsNoChargePersonListVOPage.getRecords().stream().flatMap(vo -> Stream.of(vo.getManagerId(), vo.getNoChargeUseSubmitterId())).filter(Objects::nonNull).distinct().toList();
        Map<Integer, String> collect = userFeign.searchUserNameByUserIds(idList).getData().stream().collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getName, (v1, v2) -> v1));
        gpsNoChargePersonListVOPage.getRecords().forEach(vo -> {
            vo.setManagerName(collect.get(vo.getManagerId()));
            vo.setNoChargeUseSubmitter(collect.get(vo.getNoChargeUseSubmitterId()));
        });
        return gpsNoChargePersonListVOPage;
    }

    @Override
    public String updateGpsRepaymentPlan() {
        String lockKey = "GPS::update::RepaymentPlan";
        String lockValue = IdUtil.randomUUID();
        try {
            if (redisService.tryLock(lockKey, lockValue, 1200)) {
                List<Integer> noListWasGenerated = orderInfoMapper.selectJoinList(Integer.class, new MPJLambdaWrapper<OrderInfoEntity>()
                        .select(OrderInfoEntity::getId)
                        .innerJoin(OrderFeeInfoEntity.class, on -> on.eq(OrderFeeInfoEntity::getOrderId, OrderInfoEntity::getId)
                                .eq(OrderFeeInfoEntity::getGpsPayType, 2)
                                .eq(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                                .eq(OrderFeeInfoEntity::getDeleteFlag, 0))
                        .leftJoin(GpsInstallmentInfoEntity.class, on -> on.eq(GpsInstallmentInfoEntity::getOrderId, OrderInfoEntity::getId)
                                .eq(GpsInstallmentInfoEntity::getDeleteFlag, 0))
                        .isNull(GpsInstallmentInfoEntity::getId)).stream().distinct().toList();
                log.info("StorePerformanceServiceImpl.adjustableOrderMonthApproval noListWasGenerated:{}", JSONUtil.toJsonStr(noListWasGenerated));
                for (Integer orderId : noListWasGenerated) {
                    List<FundRepaymentInfoEntity> fundRepaymentInfoEntities = fundRepaymentInfoMapper.selectList(new LambdaQueryWrapper<FundRepaymentInfoEntity>().eq(FundRepaymentInfoEntity::getOrderId, orderId).orderByAsc(FundRepaymentInfoEntity::getTerm));
                    if (CollUtil.isEmpty(fundRepaymentInfoEntities)) {
                        continue;
                    }
                    for (FundRepaymentInfoEntity fundRepaymentInfo : fundRepaymentInfoEntities) {
                        if (ObjUtil.isNull(fundRepaymentInfo) || ObjUtil.isNull(fundRepaymentInfo.getTerm()) || fundRepaymentInfo.getTerm() > 5) {
                            break;
                        }
                        GpsInstallmentInfoEntity gpsInstallmentInfoEntity = new GpsInstallmentInfoEntity()
                                .setOrderId(orderId)
                                .setTerm(fundRepaymentInfo.getTerm())
                                .setRepaymentDate(fundRepaymentInfo.getRepaymentDate())
                                .setRepaymentPrincipal(new BigDecimal("160"))
                                .setRepaymentPenaltyInterest(BigDecimal.ZERO)
                                .setRepaymentAmountTotal(new BigDecimal("160"));
                        gpsInstallmentInfoMapper.insert(gpsInstallmentInfoEntity);
                    }
                }
            }
        } finally {
            redisService.releaseLock(lockKey, lockValue);
        }
        return "success";
    }

    @Override
    public Boolean editNoChargeRule(GpsChargeRuleUpdateDTO dto) {
        // 参数校验
        Assert.notNull(dto, "参数不能为空");
        if (ObjUtil.isNotNull(dto.getEnable())){
            gpsChargingRulesInfoMapper.update(new LambdaUpdateWrapper<GpsChargingRulesInfoEntity>()
                    .set(GpsChargingRulesInfoEntity::getEnable,dto.getEnable())
                    .eq(GpsChargingRulesInfoEntity::getDeleteFlag,0));
            return true;
        }
        // 判断是新增还是修改
        if (ObjUtil.isNotNull(dto.getId())) {
            while (true){
                // 修改操作
                GpsChargingRulesInfoEntity existingRule = gpsChargingRulesInfoMapper.selectById(dto.getId());
                if (ObjUtil.isEmpty(existingRule) || existingRule.getDeleteFlag() == 1) {
                    throw new BusinessException("收费规则不存在");
                }
                int oldUsedQuantity = existingRule.getQuantity() - existingRule.getRemainingQuantity();
                if(dto.getQuantity() < oldUsedQuantity) {
                    throw new BusinessException("总数量不能小于已使用的数量");
                }

                int newRemainingQuantity = dto.getQuantity() - oldUsedQuantity;

                int quantityIncrement = dto.getQuantity() - existingRule.getQuantity();
                int remainingIncrement = newRemainingQuantity - existingRule.getRemainingQuantity();

                int update = gpsChargingRulesInfoMapper.update(new LambdaUpdateWrapper<GpsChargingRulesInfoEntity>()
                        .setIncrBy(GpsChargingRulesInfoEntity::getQuantity, quantityIncrement)
                        .setIncrBy(GpsChargingRulesInfoEntity::getRemainingQuantity, remainingIncrement)
                        .eq(GpsChargingRulesInfoEntity::getId, existingRule.getId())
                        .eq(GpsChargingRulesInfoEntity::getRemainingQuantity, existingRule.getRemainingQuantity())
                        .eq(GpsChargingRulesInfoEntity::getDeleteFlag, 0)
                );
                if (update > 0) {
                    return true;
                }
            }

        } else {
            // 新增操作
            // 检查同年同月是否已存在规则
            GpsChargingRulesInfoEntity existingRule = gpsChargingRulesInfoMapper.selectOne(
                    new LambdaQueryWrapper<GpsChargingRulesInfoEntity>()
                            .eq(GpsChargingRulesInfoEntity::getYear, dto.getYear())
                            .eq(GpsChargingRulesInfoEntity::getMonth, dto.getMonth())
                            .eq(GpsChargingRulesInfoEntity::getDeleteFlag, 0)
            );

            if (ObjUtil.isNotEmpty(existingRule)) {
                throw new BusinessException(dto.getYear() + "年" + dto.getMonth() + "月的收费规则已存在");
            }

            // 创建新规则
            GpsChargingRulesInfoEntity newEntity = new GpsChargingRulesInfoEntity();
            newEntity.setQuantity(dto.getQuantity());
            newEntity.setRemainingQuantity(dto.getQuantity()); // 新规则剩余数量等于总数量
            newEntity.setYear(dto.getYear());
            newEntity.setMonth(dto.getMonth());
            newEntity.setEnable(dto.getEnable());
            newEntity.setCreateTime(LocalDateTime.now());
            newEntity.setUpdateTime(LocalDateTime.now());
            newEntity.setDeleteFlag(0);

            return gpsChargingRulesInfoMapper.insert(newEntity) > 0;
        }
    }

    public Boolean isFree(int orderId,boolean isOnlineOrder){
        //查询订单信息
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isEmpty(orderInfoEntity)) {
            throw new BusinessException("订单不存在");
        }
        if (ObjUtil.equals(orderInfoEntity.getProductId(), 67)){
            return false;
        }
        //判断当月是否有剩余名额
        GpsChargingRulesInfoEntity gpsChargingRulesInfo = gpsChargingRulesInfoMapper.selectOne(new LambdaQueryWrapper<GpsChargingRulesInfoEntity>()
                .eq(GpsChargingRulesInfoEntity::getYear, LocalDateTime.now().getYear())
                .eq(GpsChargingRulesInfoEntity::getMonth, LocalDateTime.now().getMonthValue())
                .eq(GpsChargingRulesInfoEntity::getEnable, 0)
                .eq(GpsChargingRulesInfoEntity::getDeleteFlag, 0)
                .orderByDesc(GpsChargingRulesInfoEntity::getCreateTime)
                .last("limit 1"));
        // 添加空值检查，确保查询结果的安全处理
        boolean hasAvailableQuota = false;
        if (ObjUtil.isNotEmpty(gpsChargingRulesInfo) && gpsChargingRulesInfo.getRemainingQuantity() != null && gpsChargingRulesInfo.getRemainingQuantity() > 0) {
            hasAvailableQuota = true;
        }


        return hasAvailableQuota && isOnlineOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setStoreGpsConfig(StoreGpsConfigDTO dto, LoginUser loginUser) {
        log.info("GpsPayServiceImpl.setStoreGpsConfig start, dto: {}, loginUser: {}", JSONUtil.toJsonStr(dto), loginUser.getUserId());

        // 参数校验
        if (CollUtil.isEmpty(dto.getFreeStoreIds()) && CollUtil.isEmpty(dto.getInstallmentStoreIds())) {
            throw new BusinessException("至少需要配置一种类型的门店");
        }

        try {
            // 收集所有需要配置的门店及其配置需求
            Map<Integer, StoreConfigInfo> storeConfigMap = new HashMap<>();

            // 处理免费GPS服务门店
            if (CollUtil.isNotEmpty(dto.getFreeStoreIds())) {
                log.info("GpsPayServiceImpl.setStoreGpsConfig collecting free stores: {}", dto.getFreeStoreIds());
                for (Integer deptId : dto.getFreeStoreIds()) {
                    if (deptId == null) {
                        log.warn("GpsPayServiceImpl.setStoreGpsConfig skip null deptId in freeStoreIds");
                        continue;
                    }

                    StoreConfigInfo configInfo = storeConfigMap.getOrDefault(deptId, new StoreConfigInfo(deptId));
                    configInfo.setIsFree(1);
                    storeConfigMap.put(deptId, configInfo);
                }
            }

            // 处理分期付款门店
            if (CollUtil.isNotEmpty(dto.getInstallmentStoreIds())) {
                log.info("GpsPayServiceImpl.setStoreGpsConfig collecting installment stores: {}", dto.getInstallmentStoreIds());
                for (Integer deptId : dto.getInstallmentStoreIds()) {
                    if (deptId == null) {
                        log.warn("GpsPayServiceImpl.setStoreGpsConfig skip null deptId in installmentStoreIds");
                        continue;
                    }

                    StoreConfigInfo configInfo = storeConfigMap.getOrDefault(deptId, new StoreConfigInfo(deptId));
                    configInfo.setIsInstallment(1);

                    // 检查是否同时支持免费和分期
                    if (configInfo.getIsFree() != null && configInfo.getIsFree() == 1) {
                        log.info("GpsPayServiceImpl.setStoreGpsConfig deptId: {} will support both free and installment services", deptId);
                    }

                    storeConfigMap.put(deptId, configInfo);
                }
            }

            // 批量处理所有门店配置
            int totalProcessed = 0;
            for (StoreConfigInfo configInfo : storeConfigMap.values()) {
                boolean result = processStoreConfig(
                    configInfo.getDeptId(),
                    configInfo.getIsInstallment() != null ? configInfo.getIsInstallment() : 0,
                    configInfo.getIsFree() != null ? configInfo.getIsFree() : 0,
                    loginUser
                );
                if (result) {
                    totalProcessed++;
                }
            }

            log.info("GpsPayServiceImpl.setStoreGpsConfig completed, total processed: {}", totalProcessed);
            return totalProcessed > 0;

        } catch (Exception e) {
            log.error("GpsPayServiceImpl.setStoreGpsConfig error, dto: {}", JSONUtil.toJsonStr(dto), e);
            throw new BusinessException("批量设置门店GPS配置失败: " + e.getMessage());
        }
    }

    /**
     * 处理单个门店的GPS配置
     *
     * @param deptId 门店ID
     * @param isInstallment 是否支持分期
     * @param isFree 是否支持免费
     * @param loginUser 当前登录用户
     * @return 是否处理成功
     */
    private boolean processStoreConfig(Integer deptId, Integer isInstallment, Integer isFree, LoginUser loginUser) {
        try {
            // 查询是否已存在该门店的配置
            StoreGpsConfigEntity existingConfig = storeGpsConfigMapper.selectOne(
                new LambdaQueryWrapper<StoreGpsConfigEntity>()
                    .eq(StoreGpsConfigEntity::getDeptId, deptId)
                    .eq(StoreGpsConfigEntity::getDeleteFlag, 0)
            );

            if (existingConfig != null) {
                // 更新现有配置
                existingConfig.setIsInstallment(isInstallment)
                        .setIsFree(isFree);
                int updateResult = storeGpsConfigMapper.updateById(existingConfig);
                log.info("GpsPayServiceImpl.processStoreConfig update existing config for deptId: {}, result: {}", deptId, updateResult);
                return updateResult > 0;
            } else {
                // 创建新配置
                StoreGpsConfigEntity newConfig = new StoreGpsConfigEntity()
                        .setDeptId(deptId)
                        .setIsInstallment(isInstallment)
                        .setIsFree(isFree);
                int insertResult = storeGpsConfigMapper.insert(newConfig);
                log.info("GpsPayServiceImpl.processStoreConfig insert new config for deptId: {}, result: {}", deptId, insertResult);
                return insertResult > 0;
            }
        } catch (Exception e) {
            log.error("GpsPayServiceImpl.processStoreConfig error for deptId: {}", deptId, e);
            throw new BusinessException("处理门店GPS配置失败，门店ID: " + deptId + ", 错误: " + e.getMessage());
        }
    }

    @Override
    public StoreGpsConfigVO getStoreGpsConfig(Integer deptId) {
        log.info("GpsPayServiceImpl.getStoreGpsConfig start, deptId: {}", deptId);

        Assert.notNull(deptId, "门店ID不能为空");

        try {
            StoreGpsConfigEntity config = storeGpsConfigMapper.selectOne(
                new LambdaQueryWrapper<StoreGpsConfigEntity>()
                    .eq(StoreGpsConfigEntity::getDeptId, deptId)
                    .eq(StoreGpsConfigEntity::getDeleteFlag, 0)
            );

            if (config == null) {
                log.info("GpsPayServiceImpl.getStoreGpsConfig config not found for deptId: {}", deptId);
                return null;
            }

            StoreGpsConfigVO vo = new StoreGpsConfigVO()
                    .setId(config.getId())
                    .setDeptId(config.getDeptId())
                    .setIsInstallment(config.getIsInstallment())
                    .setIsFree(config.getIsFree());

            log.info("GpsPayServiceImpl.getStoreGpsConfig result: {}", JSONUtil.toJsonStr(vo));
            return vo;
        } catch (Exception e) {
            log.error("GpsPayServiceImpl.getStoreGpsConfig error, deptId: {}", deptId, e);
            throw new BusinessException("查询门店GPS配置失败: " + e.getMessage());
        }
    }

    /**
     * 门店配置信息内部类
     */
    private static class StoreConfigInfo {
        private Integer deptId;
        private Integer isInstallment;
        private Integer isFree;

        public StoreConfigInfo(Integer deptId) {
            this.deptId = deptId;
        }

        public Integer getDeptId() {
            return deptId;
        }

        public void setDeptId(Integer deptId) {
            this.deptId = deptId;
        }

        public Integer getIsInstallment() {
            return isInstallment;
        }

        public void setIsInstallment(Integer isInstallment) {
            this.isInstallment = isInstallment;
        }

        public Integer getIsFree() {
            return isFree;
        }

        public void setIsFree(Integer isFree) {
            this.isFree = isFree;
        }
    }


}
