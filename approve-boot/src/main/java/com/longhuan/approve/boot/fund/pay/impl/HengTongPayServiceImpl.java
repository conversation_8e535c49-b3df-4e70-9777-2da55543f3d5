package com.longhuan.approve.boot.fund.pay.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ctc.wstx.util.StringUtil;
import com.longhuan.approve.api.constants.CopperCarDeptMortgageEnums;
import com.longhuan.approve.api.pojo.dto.FundAmountChangeDTO;
import com.longhuan.approve.api.pojo.dto.FundPayDTO;
import com.longhuan.approve.api.pojo.dto.zhongheng.coppercardept.HengTongCopperCarDeptMortgageCancelDTO;
import com.longhuan.approve.api.pojo.dto.zhongheng.coppercardept.SearchOrderStatusDTO;
import com.longhuan.approve.api.pojo.vo.ZhongHengApiResult;
import com.longhuan.approve.boot.enums.FundApiRecordStatusEnum;
import com.longhuan.approve.boot.enums.FundApplyMortgageTypeEnum;
import com.longhuan.approve.boot.enums.InitStatusEnums;
import com.longhuan.approve.boot.fund.hengtong.HengTongService;
import com.longhuan.approve.boot.fund.pay.FundPayStrategy;
import com.longhuan.approve.boot.mapper.CustomerMortgageInfoMapper;
import com.longhuan.approve.boot.mapper.FinalFundInfoMapper;
import com.longhuan.approve.boot.mapper.FundUndoMortgageInfoMapper;
import com.longhuan.approve.boot.mapper.OrderInfoMapper;
import com.longhuan.approve.boot.pojo.dto.FundApiRecordDTO;
import com.longhuan.approve.boot.pojo.dto.ProcessEndDTO;
import com.longhuan.approve.boot.pojo.dto.hengtong.OrderInfoAddResDTO;
import com.longhuan.approve.boot.pojo.dto.keshang.OrderInfoAddDTO;
import com.longhuan.approve.boot.pojo.entity.CustomerMortgageInfoEntity;
import com.longhuan.approve.boot.pojo.entity.FinalFundInfoEntity;
import com.longhuan.approve.boot.pojo.entity.FundUndoMortgageInfoEntity;
import com.longhuan.approve.boot.pojo.entity.OrderInfoEntity;
import com.longhuan.approve.boot.pojo.vo.ProcessEndVO;
import com.longhuan.approve.boot.service.HengTongCopperCarDeptService;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.web.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("PAY_HENG_TONG")
@RequiredArgsConstructor
public class HengTongPayServiceImpl implements FundPayStrategy {

    private final HengTongService hengTongService;

    private  final FinalFundInfoMapper finalFundInfoMapper;

    private final CustomerMortgageInfoMapper customerMortgageInfoMapper;

    private final FundUndoMortgageInfoMapper fundUndoMortgageInfoMapper;

    private final OrderInfoMapper orderInfoMapper;

    private final HengTongCopperCarDeptService hengTongCopperCarDeptService;




    @Override
    public FundApiRecordDTO pay(FundPayDTO fundPayDTO) {
        log.info("HengTongPayServiceImpl.pay start, orderId: {}", fundPayDTO.getOrderId());
        FundApiRecordDTO fundApiRecordDTO = new FundApiRecordDTO();
        try {
            Integer orderId = fundPayDTO.getOrderId();
            String remark = "";
            FundApiRecordStatusEnum status = FundApiRecordStatusEnum.NOT_CALLED;
            // 构建请求参数
            OrderInfoAddDTO carBaseDTO = hengTongService.buildRequestDTO(orderId);
            log.info("HengTongPayServiceImpl.pay requestDTO: {}", JSONUtil.toJsonStr(carBaseDTO));

            ZhongHengApiResult<OrderInfoAddResDTO> zhongHengApiResult = hengTongService.hengTongPay(carBaseDTO);
            if (ZhongHengApiResult.isSuccess(zhongHengApiResult)) {
                OrderInfoAddResDTO responseData = zhongHengApiResult.getResponseData();
                if(responseData!=null && !"F".equalsIgnoreCase(responseData.getZhuCeState())){
                    String orderNum = responseData.getOrderNum();

                    finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                            .set(FinalFundInfoEntity::getLoanBillNo, orderNum)
                            .eq(FinalFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                            .eq(FinalFundInfoEntity::getOrderId, orderId)
                            .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                    );

                    fundApiRecordDTO.setStatus(FundApiRecordStatusEnum.CALL_SUCCESS);
                    fundApiRecordDTO.setRemark("发起成功");
                } else {
                    String remark1 = null;
                    if (responseData != null) {
                        remark1 = responseData.getZhuCeNote();
                    } else {
                        remark1 = "中恒发起失败";
                    }
                    fundApiRecordDTO.setStatus(FundApiRecordStatusEnum.CALL_FAILED);
                    fundApiRecordDTO.setRemark(remark1);
                }
            }else{
                fundApiRecordDTO.setStatus(FundApiRecordStatusEnum.CALL_FAILED);
                fundApiRecordDTO.setRemark(zhongHengApiResult.getMsg());
            }
        } catch (BusinessException e) {
            log.info("HengTongPayServiceImpl.pay BusinessException error: {}", e.getMessage(), e);
            fundApiRecordDTO.setStatus(FundApiRecordStatusEnum.CALL_FAILED);
            fundApiRecordDTO.setRemark(e.getMessage());
        } catch (Exception e) {
            log.error("HengTongPayServiceImpl.pay Exception error: {}", e.getMessage(), e);
            fundApiRecordDTO.setStatus(FundApiRecordStatusEnum.CALL_FAILED);
            fundApiRecordDTO.setRemark("发起失败，系统异常");
        }
        return fundApiRecordDTO;
    }


    @Override
    public FundApiRecordDTO payResult(FundPayDTO fundPayDTO) {
        return null;
    }

    @Override
    public FundApiRecordDTO cancel(FundPayDTO fundPayDTO) {
        Integer orderId = fundPayDTO.getOrderId();
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getOrderId, orderId)
//                .ne(FinalFundInfoEntity::getStatus, InitStatusEnums.FAIL)
                .eq(FinalFundInfoEntity::getFundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0));

        if (ObjUtil.isNull(finalFundInfo)) {
            log.info("HengTongPayServiceImpl.cancel not found finalFundInfoEntity orderId:{}", orderId);
            return null;
        }
        CustomerMortgageInfoEntity customerMortgageInfo = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                .eq(CustomerMortgageInfoEntity::getMortgageType, 0)
                .orderByDesc(CustomerMortgageInfoEntity::getUpdateTime)
                .last("limit 1")
        );
        //线下不用管
        if (ObjUtil.isNull(customerMortgageInfo)){
            return null;
        }
        OrderInfoEntity infoEntity = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isNotNull(infoEntity) && (ObjUtil.equals(infoEntity.getMortgageState(),0) || ObjUtil.equals(infoEntity.getMortgageState(),4))){
            return null;
        }
        //查看解抵表中是否存在  如果存在 等待解抵完成
        FundUndoMortgageInfoEntity fundUndoMortgageInfoEntity = fundUndoMortgageInfoMapper.selectOne(new LambdaQueryWrapper<FundUndoMortgageInfoEntity>()
                        .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                        .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                        .eq(FundUndoMortgageInfoEntity::getCancel, 0)
       );
        log.info("HengTongPayServiceImpl.cancel fundUndoMortgageInfoEntity: {}", JSONUtil.toJsonStr(fundUndoMortgageInfoEntity));
        if (ObjUtil.isNotNull(fundUndoMortgageInfoEntity) && StrUtil.equals(fundUndoMortgageInfoEntity.getOperateType(),"1")){
            //撤回中
            if (StrUtil.equals(fundUndoMortgageInfoEntity.getMortgageStatus(),"0") || StrUtil.equals(fundUndoMortgageInfoEntity.getMortgageStatus(),"4")){
                return null;
            }
           throw new BusinessException("请等待撤回结果");
        }
        if (ObjUtil.isNotNull(fundUndoMortgageInfoEntity) && StrUtil.equals(fundUndoMortgageInfoEntity.getOperateType(),"2")){
            //解抵中
            if (StrUtil.equals(fundUndoMortgageInfoEntity.getMortgageStatus(),"8") || StrUtil.equals(fundUndoMortgageInfoEntity.getMortgageStatus(),"9")){
                return null;
            }
            throw new BusinessException("请等待解抵结果");
        }
        if (ObjUtil.isNull(fundUndoMortgageInfoEntity)){
            //调用抵押撤销
            HengTongCopperCarDeptMortgageCancelDTO hengTongCopperCarDeptMortgageCancelDTO = new HengTongCopperCarDeptMortgageCancelDTO();
            hengTongCopperCarDeptMortgageCancelDTO.setOrderId(orderId);
            hengTongCopperCarDeptMortgageCancelDTO.setType("E");
            try {
                String copperCarId = hengTongCopperCarDeptService.cancelOrder(hengTongCopperCarDeptMortgageCancelDTO);
                log.info("HengTongPayServiceImpl.cancel copperCarId: {}", copperCarId);
            } catch (Exception e) {
                throw new BusinessException("撤销失败,请去发起抵押撤销或解抵");
            }
            throw new BusinessException("请等待抵押撤销结果");
        }

		// 如果订单处在待放款环节 可做中恒的流程终止申请
		if (ObjUtil.equals(4500,infoEntity.getCurrentNode()) && finalFundInfo.getLoanBillNo() != null && finalFundInfo.getFundId() == FundEnum.ZHONG_HENG_TONG_HUI.getValue()){
			ProcessEndDTO processEndDTO = new ProcessEndDTO();
			processEndDTO.setOrderNum(finalFundInfo.getLoanBillNo());
			processEndDTO.setSPOrderNum(infoEntity.getOrderNumber());
			ZhongHengApiResult<ProcessEndVO> res = hengTongService.processEndNotice(processEndDTO);
			if (ZhongHengApiResult.isSuccess(res)){
				log.info("流程终止申请成功,中恒订单编号:{}  云启订单编号:{}",finalFundInfo.getLoanBillNo(),infoEntity.getOrderNumber());
			} else {
				throw new BusinessException(res.getMsg());
			}
		}

        return null;
    }

    @Override
    public FundApiRecordDTO amountChange(FundAmountChangeDTO fundAmountChangeDTO) {
        return null;
    }
}
