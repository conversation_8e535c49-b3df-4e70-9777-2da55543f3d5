package com.longhuan.order.settleCalculation.service.impl.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.approve.api.constants.FundRepayModeEnum;
import com.longhuan.approve.api.pojo.dto.FundRepayCalcDTO;
import com.longhuan.approve.api.pojo.vo.FundRepayCalcVO;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.enums.FundRepayStatusEnum;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.mapper.FundRepaymentInfoMapper;
import com.longhuan.order.mapper.OrderPayApplicationMapper;
import com.longhuan.order.pojo.dto.SettleCalculationContext;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.SettleCalculationVO;
import com.longhuan.order.service.FundRepaymentDeductService;
import com.longhuan.order.settleCalculation.service.impl.strategy.SettleCalculationStrategy;
import com.longhuan.order.settleCalculation.service.impl.strategy.impl.Annotation.SupportSettleType;
import com.longhuan.order.settleCalculation.service.impl.strategy.impl.enums.SettleType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 委外非赎回策略
 */
@Component
@SupportSettleType(SettleType.OUTSOURCING_NON_REDEEM)
@RequiredArgsConstructor
@Slf4j
public class OutsourcingNonRedeemStrategy implements SettleCalculationStrategy {
    private final ApproveFeign approveFeign;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final FundRepaymentDeductService fundRepaymentDeductService;
    private final OrderPayApplicationMapper orderPayApplicationMapper;

    @Override
    public SettleCalculationVO calculate(SettleCalculationContext context) {
        Assert.notNull(context.getFundSettlementAmount(), () -> new BusinessException("资方结清金额不能为空"));
        if (Objects.equals(context.getClosedPeriodRepayMode(), 0)) {
            // 结清金额=资方结清金额
            return SettleCalculationMethod.directSettlement(context.getFundSettlementAmount());
        }
        //委外非赎回封闭期内仅适用于违约金模式
        context.setClosedPeriodRepayMode(2);
        //委外封闭期内特殊策略封闭期不等于六期情况下结清金额等价于通用结清金额策略封闭期内违约金模式还款期数大于封闭期一半时策略
        int paidPeriods = context.getPaidPeriods();
        if (context.getClosedPeriod() != 6 && !context.getFundName().equals("长安新生") && context.getPaidPeriods() < context.getClosedPeriod()) {
            //直接设置已还期数等于封闭期
            context.setPaidPeriods(context.getClosedPeriod()-1);
        }
        //调用通用结算策略
        SettleCalculationVO settleCalculationVO = SettleCalculationMethod.defaultSettlementPolicy(context);
        context.setPaidPeriods(paidPeriods);
        return settleCalculationVO;
    }

    @Override
    public void buildLHContext(SettleCalculationContext context, OrderInfoEntity orderInfoEntity, LocalDate payDate) {
        log.info("OutsourcingNonRedeemStrategy.buildLHContext context:{} orderInfoEntity:{} payDate:{}",context,orderInfoEntity,payDate);
        FundRepayCalcDTO reqDTO = new FundRepayCalcDTO();
        reqDTO.setOrderId(orderInfoEntity.getId());
        reqDTO.setRepayMode(FundRepayModeEnum.ONLINE);
        Result<FundRepayCalcVO> result;
        //本金
        int fundSettleFlag = 0;
        BigDecimal principal = BigDecimal.ZERO;
        BigDecimal amountTotal = BigDecimal.ZERO;
        try {
            result = approveFeign.repayCalc(reqDTO);
        }catch (Exception e){
            context.setRedeem(true);
            context.setOutsourcing(false);
            if (context.getClosedPeriod() != 6 && !context.getFundName().equals("长安新生") && context.getPaidPeriods() < context.getClosedPeriod()) {
                //直接设置已还期数等于封闭期
                context.setPaidPeriods(context.getClosedPeriod()-1);
            }
            result = Result.failed();
        }
        if (Result.isSuccess(result)) {
            log.info("OutsourcingNonRedeemStrategy.buildLHContext result:{}",result);
            principal=result.getData().getPrincipal();
            amountTotal = result.getData().getRepayAmt().add(result.getData().getGuaraFeeOdAmt()).add(result.getData().getGuaraFeeAmt());
            context.setGuaraFeeOdAmt(result.getData().getGuaraFeeOdAmt());
            context.setGuaraFeeAmt(result.getData().getGuaraFeeAmt());
        }else {
            log.error("OutsourcingNonRedeemStrategy.buildLHContext error:{}",result.getMsg());
            context.setRedeem(true);
            context.setOutsourcing(false);
            if (context.getClosedPeriod() != 6 && !context.getFundName().equals("长安新生") && context.getPaidPeriods() < context.getClosedPeriod()) {
                //直接设置已还期数等于封闭期
                context.setPaidPeriods(context.getClosedPeriod()-1);
            }
            List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList = fundRepaymentInfoMapper.selectList(
                    new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                            .eq(FundRepaymentInfoEntity::getOrderId, orderInfoEntity.getId())
                            .eq(FundRepaymentInfoEntity::getFundId, orderInfoEntity.getFundId())
                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
            );
            LocalDate now = LocalDate.now();
            BigDecimal overdueRepaymentTotal = fundRepaymentInfoEntityList.stream()
                    .filter(entity -> entity.getRepaymentDate().isBefore(now))
                    .map(e->ObjUtil.defaultIfNull(e.getRepaymentPrincipal(),BigDecimal.ZERO).add(ObjUtil.defaultIfNull(e.getRepaymentInterest(),BigDecimal.ZERO)))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal overdueActuallyTotal = fundRepaymentInfoEntityList.stream()
                    .filter(entity -> entity.getRepaymentDate().isBefore(now))
                    .map(e->ObjUtil.defaultIfNull(e.getActuallyPrincipal(),BigDecimal.ZERO).add(ObjUtil.defaultIfNull(e.getActuallyInterest(),BigDecimal.ZERO)))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal repaymentPrincipal = fundRepaymentInfoEntityList.stream()
                    .filter(entity -> !entity.getRepaymentDate().isBefore(now))
                    .map(FundRepaymentInfoEntity::getRepaymentPrincipal)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal actuallyPrincipal = fundRepaymentInfoEntityList.stream()
                    .filter(entity -> !entity.getRepaymentDate().isBefore(now))
                    .map(FundRepaymentInfoEntity::getActuallyPrincipal)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            principal=repaymentPrincipal.subtract(actuallyPrincipal);
            context.setOverdueUnpaid(overdueRepaymentTotal.subtract(overdueActuallyTotal));

        }
        List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList = fundRepaymentInfoMapper.selectList(
                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(FundRepaymentInfoEntity::getFundId, orderInfoEntity.getFundId())
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
        );
        context.setRegionId(orderInfoEntity.getRegionId())
                .setLoanAmount(orderInfoEntity.getApprovalAmount())
                //逾期天数 结清计算器判断下。
                .setOverdueDays(null != payDate ?
                        getOverdueDays(payDate, fundRepaymentInfoEntityList) :
                        orderInfoEntity.getOverdueDays())
                .setFundSettlementAmount(amountTotal)
                .setRemainingPrincipal(principal)
//                .setRedeemAmount()
//                .setRedeemSingleRepay()
//                .setRedeemPaidAmount()
                .setUseDays(calUseDays(orderInfoEntity.getPaymentTime().toLocalDate(),
                        orderInfoEntity.getId(),
                        orderInfoEntity.getFundId(),
                        fundSettleFlag,
                        payDate));

    }
    /**
     * 计算逾期天数
     *
     * @param payDate
     * @param fundRepaymentInfoEntityList
     * @return
     */
    private Integer getOverdueDays(LocalDate payDate,
                                   List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList) {

        //未还款的最早期数
        Optional<FundRepaymentInfoEntity> min = fundRepaymentInfoEntityList.stream().filter(v ->
                        FundRepayStatusEnum.NONE.getValue() == v.getRepaymentStatus().getValue())
                .min(Comparator.comparing(FundRepaymentInfoEntity::getTerm));
        if (min.isPresent()) {
            FundRepaymentInfoEntity fundRepaymentInfoEntity = min.get();
            //应还款日期
            LocalDate repaymentDate = fundRepaymentInfoEntity.getRepaymentDate();
            //选择的结清日期在应还款日期前或等于应还款日期,逾期天数=0
            if (payDate.isBefore(repaymentDate)
                    || payDate.isEqual(repaymentDate)) {
                return 0;
            }
            //选择的结清日期在应还款日期后
            else {
                return (int) ChronoUnit.DAYS.between(repaymentDate, payDate);
            }
        }
        return 0;
    }

    /**
     * 计算使用天数
     * 1.存在已结清期数
     * 当前日期 - 上次结清应还日期
     * 2.存在逾期期数
     * 当前日期 - 下一个应还期数应还日期
     * 3.存在实还日期 部分还款
     * 当前日期 - 实还日期
     * 4.存在未到还款日 1期在当前日期之后
     * 当前日期 - 放款日期
     * 5.存在1期逾期 1期在当前日期之前
     * 当前日期 - 1期还款日期
     */
    private Integer calUseDays(LocalDate paymentDate,
                               Integer orderId,
                               Integer fundId,
                               Integer fundSettleFlag,
                               LocalDate payDate) {
        LocalDate beginDate = LocalDate.now();
        //若payDate 不为空,则代表传入选择结清时间
        if (null != payDate) {
            beginDate = payDate;
        }
        LocalDate earliestOverdueDate;
        FundRepaymentInfoEntity calRepaymentInfo;
        int useDays;
        log.info("OutsourcingNonRedeemStrategy.calUseDays - Begin, paymentDate: {}, orderId: {}, fundId: {}", paymentDate, orderId, fundId);
        if (Objects.equals(fundSettleFlag, 1)) {
            List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getOrderId, orderId)
                            .eq(FundRepaymentDeductEntity::getFundId, fundId)
                            .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                            .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                    FundDeductBizTypeEnums.PAYMENT,
                                    FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                    FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                    FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE
                            ))
                            .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                            .orderByAsc(FundRepaymentDeductEntity::getUpdateTime)
            );
            if (CollUtil.isNotEmpty(list)) {
                FundRepaymentInfoEntity fundRepaymentInfoEntity = fundRepaymentInfoMapper.selectOne(
                        new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                                .eq(FundRepaymentInfoEntity::getFundId, fundId)
                                .eq(FundRepaymentInfoEntity::getTerm, list.get(0).getTerm())
                                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(FundRepaymentInfoEntity::getUpdateTime)
                                .last("limit 1")
                );
                List<OrderPayApplicationInfoEntity> orderPayApplicationInfoEntityList = orderPayApplicationMapper.selectList(
                        new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                                .eq(OrderPayApplicationInfoEntity::getOrderId, orderId)
                                .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY)
//                                        .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.CASHIER_APPROVAL)
                                .orderByAsc(OrderPayApplicationInfoEntity::getCreateTime)
                                .last("limit 1")
                );

                earliestOverdueDate = fundRepaymentInfoEntity.getRepaymentDate();
                if (beginDate.isAfter(earliestOverdueDate) || beginDate.isEqual(earliestOverdueDate)) {
                    if (CollUtil.isNotEmpty(orderPayApplicationInfoEntityList)) {
                        useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, orderPayApplicationInfoEntityList.get(0).getPaymentTime().toLocalDate());
                    } else {
                        useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
                    }
                } else {
                    if (!Objects.equals(list.get(0).getTerm(), 1)) {
                        FundRepaymentInfoEntity fundRepaymentInfoEntity1 = fundRepaymentInfoMapper.selectOne(
                                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                                        .eq(FundRepaymentInfoEntity::getFundId, fundId)
                                        .eq(FundRepaymentInfoEntity::getTerm, list.get(0).getTerm() - 1)
                                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                        .orderByDesc(FundRepaymentInfoEntity::getUpdateTime)
                                        .last("limit 1")
                        );
                        useDays = (int) ChronoUnit.DAYS.between(fundRepaymentInfoEntity1.getRepaymentDate(), beginDate);
                        if (CollUtil.isNotEmpty(orderPayApplicationInfoEntityList)) {
                            useDays = (int) ChronoUnit.DAYS.between(fundRepaymentInfoEntity1.getRepaymentDate(), orderPayApplicationInfoEntityList.get(0).getPaymentTime().toLocalDate());
                        }
                    } else {
                        useDays = (int) ChronoUnit.DAYS.between(paymentDate, beginDate);
                        if (CollUtil.isNotEmpty(orderPayApplicationInfoEntityList)) {
                            useDays = (int) ChronoUnit.DAYS.between(paymentDate, orderPayApplicationInfoEntityList.get(0).getPaymentTime().toLocalDate());
                        }
                    }
                }
                log.info("RepaymentServiceImpl.calRepurchaseUseDays - Last settled, useDays: {}", useDays);
                return useDays;
            }
        }
        // 判断全部还款计划是否未到期
        calRepaymentInfo = fundRepaymentInfoMapper.selectOne(
                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                        .eq(FundRepaymentInfoEntity::getFundId, fundId)
                        .eq(FundRepaymentInfoEntity::getTerm, 1)
                        .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                , false
        );

        log.info("OutsourcingNonRedeemStrategy.calUseDays - First term repayment info: {}", calRepaymentInfo);

        if (ObjUtil.isNotNull(calRepaymentInfo) &&ObjUtil.equals(calRepaymentInfo.getRepaymentStatus(),FundRepayStatusEnum.OVERDUE)) {
            // 1 期逾期
            earliestOverdueDate = calRepaymentInfo.getRepaymentDate();
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("OutsourcingNonRedeemStrategy.calUseDays - First term overdue, useDays: {}", useDays);
            return useDays;
        } else if (ObjUtil.isNotNull(calRepaymentInfo) && ObjUtil.equals(calRepaymentInfo.getRepaymentStatus(),FundRepayStatusEnum.NONE)) {
            // 1期未逾期
            earliestOverdueDate = paymentDate;
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("OutsourcingNonRedeemStrategy.calUseDays - First term not overdue, useDays: {}", useDays);
            return useDays;
        }
        // 存在实还日期 部分还款
        calRepaymentInfo = fundRepaymentInfoMapper.selectOne(
                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                        .eq(FundRepaymentInfoEntity::getFundId, fundId)
                        .isNotNull(FundRepaymentInfoEntity::getActuallyDate)
                        .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FundRepaymentInfoEntity::getTerm)
                , false
        );

        log.info("OutsourcingNonRedeemStrategy.calUseDays - Partial repayment info: {}", calRepaymentInfo);

        if (ObjUtil.isNotNull(calRepaymentInfo)) {
            earliestOverdueDate = calRepaymentInfo.getActuallyDate();
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("OutsourcingNonRedeemStrategy.calUseDays - Partial repayment, useDays: {}", useDays);
            return useDays;
        }

        // 存在逾期 获取第一个逾期
        calRepaymentInfo = fundRepaymentInfoMapper.selectOne(
                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                        .eq(FundRepaymentInfoEntity::getFundId, fundId)
                        .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.OVERDUE)
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                        .orderByAsc(FundRepaymentInfoEntity::getTerm)
                , false
        );

        log.info("OutsourcingNonRedeemStrategy.calUseDays - First overdue repayment info: {}", calRepaymentInfo);

        if (ObjUtil.isNotNull(calRepaymentInfo)) {
            earliestOverdueDate = calRepaymentInfo.getRepaymentDate();
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("OutsourcingNonRedeemStrategy.calUseDays - First overdue, useDays: {}", useDays);
            return useDays;
        }
        // 存在已结清期数 没有逾期
        calRepaymentInfo = fundRepaymentInfoMapper.selectOne(
                new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                        .eq(FundRepaymentInfoEntity::getOrderId, orderId)
                        .eq(FundRepaymentInfoEntity::getFundId, fundId)
                        .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FundRepaymentInfoEntity::getTerm)
                , false
        );
        log.info("OutsourcingNonRedeemStrategy.calUseDays - Last settled repayment info: {}", calRepaymentInfo);
        if (ObjUtil.isNotNull(calRepaymentInfo)) {
            earliestOverdueDate = calRepaymentInfo.getRepaymentDate();
            useDays = (int) ChronoUnit.DAYS.between(earliestOverdueDate, beginDate);
            log.info("OutsourcingNonRedeemStrategy.calUseDays - Last settled, useDays: {}", useDays);
            return useDays;
        }
        log.info("OutsourcingNonRedeemStrategy.calUseDays - No matching conditions, returning 0");
        return 0;
    }

}
