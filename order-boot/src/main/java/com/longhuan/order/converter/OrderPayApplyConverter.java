package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.PayApplicationDTO;
import com.longhuan.order.pojo.entity.BankAccountSignEntity;
import com.longhuan.order.pojo.entity.OrderPayApplicationInfoEntity;
import com.longhuan.order.pojo.vo.OrderPayAccountVO;
import com.longhuan.order.pojo.vo.OrderPayApplicationOfflineVO;
import com.longhuan.order.pojo.vo.OrderPayApplicationVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface OrderPayApplyConverter {

    @Mapping(source = "bankName", target = "customerAccountName")
    @Mapping(source = "bankCardNumber", target = "customerAccountNumber")
    @Mapping(target = "customerBankBranchName", expression = "java(getBankBranchName(entity))")
    @Mapping(source = "phone", target = "customerAccountPhone")
    @Mapping(source = "idCardNum", target = "customerCardNumber")
    OrderPayAccountVO entity2Vo(BankAccountSignEntity entity);
    @Mapping(source = "reductionSourceId", target = "reductionSourceId", qualifiedByName = "mapReductionSourceId")
    OrderPayApplicationInfoEntity dto2Entity(PayApplicationDTO dto);


    List<OrderPayAccountVO> entityList2VoList(List<BankAccountSignEntity> entityList);

    OrderPayApplicationOfflineVO entity2OfflineVO(OrderPayApplicationInfoEntity entity);

    OrderPayApplicationVO entity2PayApplyVO(OrderPayApplicationInfoEntity entity);



    default String getBankBranchName(BankAccountSignEntity entity) {
        if (entity.getBankNameUpdate() == null || entity.getBankName() == null) {
            return "";
        }
        return entity.getBankNameUpdate().replace(entity.getBankName(), "").trim();
    }
    @Named("mapReductionSourceId")
    default String mapReductionSourceId(List<Integer> reductionSourceId) {
        if (reductionSourceId == null || reductionSourceId.isEmpty()) {
            return null; // 如果列表为空，返回空数组格式
        }
        return "[" + reductionSourceId.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(",")) + "]"; // 将 List 转换为 [1,2,3] 格式
    }

}
