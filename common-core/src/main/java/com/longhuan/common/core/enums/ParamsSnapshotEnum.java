package com.longhuan.common.core.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * params 快照枚举
 *
 * <AUTHOR>
 * @date 2024/09/09
 */
@Getter
@AllArgsConstructor
public enum ParamsSnapshotEnum {
    PRE_SNAPSHOT(0, "预审批快照"),
    ORDER_SNAPSHOT(1, "订单快照"),
    RESTART_SIGN_SNAPSHOT(2, "重签合同快照"),
    SETTLE_SNAPSHOT(3, "结清材料快照"),
    LAWSUIT_SNAPSHOT(4, "法诉附件快照"),
    OUTSOURCING_SNAPSHOT(5, "委外合同快照"),
    OTHER_SYS_TRANSFER_SNAPSHOT(6, "其他系统债转合同快照"),
    LANHAI_MORTGAGE_SNAPSHOT(7, "蓝海雷霆抵押合同快照"),
    LANHAI_MORTGAGE_RELIEVE_SNAPSHOT(8, "蓝海雷霆解除抵押合同快照"),
    FUMIN_GUARANTEE_SNAPSHOT(9, "富民担保函快照"),
    ;
    @EnumValue
    @JsonValue
    private final Integer code;
    private final String value;
}
