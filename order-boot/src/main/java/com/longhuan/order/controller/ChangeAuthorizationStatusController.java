package com.longhuan.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.FileToFddDTOListDTO;
import com.longhuan.order.pojo.vo.FileToFddNotDeleteVo;
import com.longhuan.order.service.ChangeAuthorizationStatusService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/authStatus")
@RequiredArgsConstructor
public class ChangeAuthorizationStatusController {

    private final ChangeAuthorizationStatusService changeAuthorizationStatusService;
    @PostMapping("/updateAuthStatus")
    @ApiOperation(value = "更改法大大认证状态")
    public Result<Boolean> updateAuthStatus() {
        return Result.success(changeAuthorizationStatusService.updateAuthStatus());
    }

}
