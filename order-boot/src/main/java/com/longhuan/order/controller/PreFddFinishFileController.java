package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.converter.PreFddFinishFileConverter;
import com.longhuan.order.pojo.dto.PreFddFinishFileListReqDTO;
import com.longhuan.order.pojo.entity.PreFddFinishFileEntity;
import com.longhuan.order.pojo.vo.OrderFileVo;
import com.longhuan.order.pojo.vo.PreFddFinishFileVO;
import com.longhuan.order.service.OrderFileService;
import com.longhuan.order.service.PreFddFinishFileService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *  用户已签署文件
 * <AUTHOR>
 * @Date 2024/8/11
 */
@RestController
@RequestMapping("/fddFinishFile")
@RequiredArgsConstructor
public class PreFddFinishFileController {
    private final PreFddFinishFileService preFddFinishFileService;
    private final PreFddFinishFileConverter preFddFinishFileConverter;

    @ApiOperation(value = "查询订单已签署文件")
    @PostMapping("/list")
    public Result<List<PreFddFinishFileVO>> queryList(@RequestBody PreFddFinishFileListReqDTO fddFinishFileListReqDTO){
        List<PreFddFinishFileEntity> finishFileList = preFddFinishFileService.queryList(fddFinishFileListReqDTO);
        List<PreFddFinishFileVO> fddFinishFileVOList = preFddFinishFileConverter.entityList2VoList(finishFileList);
        return Result.success(fddFinishFileVOList);
    }

}
