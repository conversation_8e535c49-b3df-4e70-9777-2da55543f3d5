package com.longhuan.order.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.approve.api.pojo.dto.changyin.ChangYinLPRDTO;
import com.longhuan.approve.api.pojo.dto.changyin.ChangYinLoanTrialByOrderDTO;
import com.longhuan.approve.api.pojo.dto.changyin.ChangYinResBodyDTO;
import com.longhuan.approve.api.pojo.dto.lanhai.LanHaiCreditLimitInfoDTO;
import com.longhuan.approve.api.pojo.dto.zhongheng.hengtong.HengTongPreRepayPlanDTO;
import com.longhuan.approve.api.pojo.vo.FuMinYxLoanCalculateVO;
import com.longhuan.approve.api.pojo.vo.YingFengInfoVO;
import com.longhuan.approve.api.pojo.vo.changyin.ChangLPRResDTO;
import com.longhuan.approve.api.pojo.vo.changyin.ChangYinLoanTrialResponseDTO;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiRepayCalcResponse;
import com.longhuan.approve.api.pojo.vo.zhongheng.HengTongPreRepayPlanVO;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.GlobalConstants;
import com.longhuan.common.core.enums.ProductRongDanEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.constants.SwitchConstants;
import com.longhuan.common.redis.pojo.SwitchVO;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.redis.util.DictUtils;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.enums.*;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.feign.ResourceFeign;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.AmountCalDTO;
import com.longhuan.order.pojo.dto.GenerateContractDTO;
import com.longhuan.order.pojo.dto.RepaymentListDTO;
import com.longhuan.order.pojo.dto.VehicleInfoDTO;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.AmountCalVO;
import com.longhuan.order.pojo.vo.GenerateContractVO;
import com.longhuan.order.pojo.vo.OrderCustomerInfoVo;
import com.longhuan.order.pojo.vo.VehicleInfoVO;
import com.longhuan.order.util.PareAddressUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.longhuan.common.core.enums.ParamsSnapshotEnum.ORDER_SNAPSHOT;

@Slf4j
@Service
@RequiredArgsConstructor
public class ContractFileService {
    private final OrderInfoMapper orderInfoMapper;
    private final OrderContractMapper orderContractMapper;
    private final FundTemplateAssoMapper fundTemplateAssoMapper;
    private final ResourceFeign resourceFeign;
    private final FadadaAuthService fadadaAuthService;
    private final ParamsSnapshotMapper paramsSnapshotMapper;
    private final OrderParamsSnapshotService orderParamsSnapshotService;
    private final RedisService redisService;
    private final AmountService amountService;
    private final OrderCustomerInfoService orderCustomerInfoService;
    private final ProductInfoMapper productInfoMapper;
    private final OrderVehicleInfoService orderVehicleInfoService;
    private final DataAreaMapper dataAreaMapper;
    private final StoreAddressInfoMapper storeAddressInfoMapper;
    private final OrderFeeInfoMapper orderFeeInfoMapper;
    private final RepaymentInfoMapper repaymentInfoMapper;
    private final BankAccountSignMapper bankAccountSignMapper;
    private final UserFeign userFeign;
    private final FundProductMappingMapper fundProductMappingMapper;
    private final FundInfoMapper fundInfoMapper;
    private final FileResourceMapper fileResourceMapper;
    private final RepaymentService repaymentService;
    private final PreFundInfoMapper preFundInfoMapper;
    private final ApproveFeign approveFeign;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final FileTemplateInfoMapper fileTemplateInfoMapper;
    private final FundSignInfoMapper fundSignInfoMapper;
    private final SwitchUtils switchUtils;
    private final SignTaskMapper signTaskMapper;
    private final FundSignInfoService fundSignInfoService;
    @Value("${digitalize.HmacSHA256Key}")
    private String digitalizeKey;

    private final DictUtils dictUtils;
    private final OrderArrivedMapper orderArrivedMapper;
    private final ProductRongdanMapper productRongdanMapper;
    private final SmartContractReportMapper smartContractReportMapper;

    /**
     * 还款信息快照
     *
     * @param orderId                  次序id
     * @param repaymentInfoEntities    还款信息实体
     * @param paramsSnapshotEntityList Order Params 快照实体列表
     */
    private static void repaymentInfoSnapshot(Integer orderId, List<RepaymentInfoEntity> repaymentInfoEntities, List<ParamsSnapshotEntity> paramsSnapshotEntityList) {
        int i = 1;
        for (RepaymentInfoEntity repaymentInfoEntity : repaymentInfoEntities) {
            //月还总额
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(repaymentInfoEntity.getRepaymentAmount() != null ? repaymentInfoEntity.getRepaymentAmount().setScale(2, RoundingMode.HALF_UP) : null)));
            //还款日期
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REPAYMENT_DATE + "_" + repaymentInfoEntity.getTerm())
                    .setValue(repaymentInfoEntity.getRepaymentDate().format(DatePattern.NORM_DATE_FORMATTER)));
            //还款单日
            String format = repaymentInfoEntity.getRepaymentDate().format(DatePattern.NORM_DATE_FORMATTER);
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REPAYMENT_DATE_NUMBER + "_" + repaymentInfoEntity.getTerm())
                    .setValue(StrUtil.toString(LocalDate.parse(format).getDayOfMonth())));
            //剩余本金
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REMAINING_PRINCIPAL + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(repaymentInfoEntity.getRemainingPrincipal() != null ? repaymentInfoEntity.getRemainingPrincipal() : null)));
            //担保费
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.GUARANTEE_FEE + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(repaymentInfoEntity.getRepaymentGuarantee() != null ? repaymentInfoEntity.getRepaymentGuarantee().setScale(2, RoundingMode.HALF_UP) : null)));
            //担保费大写
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.GUARANTEE_FEE_TEXT + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(Convert.digitToChinese(repaymentInfoEntity.getRepaymentGuarantee() != null ? repaymentInfoEntity.getRepaymentGuarantee().setScale(2, RoundingMode.HALF_UP) : null))));
            //电销GPS分期  前五期：月还总额+500
            if (i <= 5) {
                paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                        .setLinkId(orderId)
                        .setType(ORDER_SNAPSHOT)
                        .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_100" + repaymentInfoEntity.getTerm())
                        .setValue(Convert.toStr(repaymentInfoEntity.getRepaymentAmount() != null ? repaymentInfoEntity.getRepaymentAmount().add(new BigDecimal("160")).setScale(2, RoundingMode.HALF_UP) : 160.00)));
                i += 1;
            } else {
                paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                        .setLinkId(orderId)
                        .setType(ORDER_SNAPSHOT)
                        .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_100" + repaymentInfoEntity.getTerm())
                        .setValue("0"));
            }
        }
    }

    /**
     * 通汇还款信息快照
     *
     * @param orderId                  次序id
     * @param startDate                开始时间
     * @param paramsSnapshotEntityList Order Params 快照实体列表
     */
    private  HengTongPreRepayPlanVO repaymentTongHuiInfoSnapshot(Integer orderId, LocalDate startDate,
                                               BigDecimal amount,
                                               List<RepaymentInfoEntity> repaymentInfoEntities,ProductInfoEntity productInfoEntity, List<ParamsSnapshotEntity> paramsSnapshotEntityList) {

        HengTongPreRepayPlanVO planVO = approveFeign.hengTongPreRepayPlan(new HengTongPreRepayPlanDTO().setShiJian(startDate).setZuiZhongEDu(amount)
                .setChanPinLiLv(productInfoEntity.getIrr().multiply(new BigDecimal(100))).setChanPinQiShu(productInfoEntity.getTerm())).getData();
        Assert.notNull(planVO, "通汇还款计划表" + orderId + "信息不存在");
        List<HengTongPreRepayPlanVO.Plan> planList = planVO.getHuanKuanList();
        int i = 1;
        for (HengTongPreRepayPlanVO.Plan repaymentInfoEntity : planList) {
            //月还总额
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_" + repaymentInfoEntity.getQiShu())
                    .setValue(Convert.toStr(repaymentInfoEntity.getYsHeJi() != null ? repaymentInfoEntity.getYsHeJi().setScale(2, RoundingMode.HALF_UP) : null)));
            //还款日期
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REPAYMENT_DATE + "_" + repaymentInfoEntity.getQiShu())
                    .setValue(repaymentInfoEntity.getYsTime().format(DatePattern.NORM_DATE_FORMATTER)));
            //还款单日
            String format = repaymentInfoEntity.getYsTime().format(DatePattern.NORM_DATE_FORMATTER);
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REPAYMENT_DATE_NUMBER + "_" + repaymentInfoEntity.getQiShu())
                    .setValue(StrUtil.toString(LocalDate.parse(format).getDayOfMonth())));
            //剩余本金
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REMAINING_PRINCIPAL + "_" + repaymentInfoEntity.getQiShu())
                    .setValue(Convert.toStr(repaymentInfoEntity.getYsShengYuBenJin() != null ? repaymentInfoEntity.getYsShengYuBenJin().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO)));


            //电销GPS分期  前五期：月还总额+500
            if (i <= 5) {
                paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                        .setLinkId(orderId)
                        .setType(ORDER_SNAPSHOT)
                        .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_100" + repaymentInfoEntity.getQiShu())
                        .setValue(Convert.toStr(repaymentInfoEntity.getYsHeJi() != null ? repaymentInfoEntity.getYsHeJi().add(new BigDecimal("160")).setScale(2, RoundingMode.HALF_UP) : 160.00)));
                i += 1;
            } else {
                paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                        .setLinkId(orderId)
                        .setType(ORDER_SNAPSHOT)
                        .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_100" + repaymentInfoEntity.getQiShu())
                        .setValue("0"));
            }

       }

        for (RepaymentInfoEntity repaymentInfoEntity : repaymentInfoEntities) {
            //担保费
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.GUARANTEE_FEE + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(repaymentInfoEntity.getRepaymentGuarantee() != null ? repaymentInfoEntity.getRepaymentGuarantee().setScale(2, RoundingMode.HALF_UP) : null)));
            //担保费大写
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.GUARANTEE_FEE_TEXT + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(Convert.digitToChinese(repaymentInfoEntity.getRepaymentGuarantee() != null ? repaymentInfoEntity.getRepaymentGuarantee().setScale(2, RoundingMode.HALF_UP) : null))));
        }

        return planVO;


    }


    /**
     * 富民还款信息快照
     *
     * @param orderId                  次序id
     * @param startDate                开始时间
     * @param paramsSnapshotEntityList Order Params 快照实体列表
     */
    private  FuMinYxLoanCalculateVO repaymentFuMinInfoSnapshot(Integer orderId, LocalDate startDate,
                                                                 BigDecimal amount,
                                                                 List<RepaymentInfoEntity> repaymentInfoEntities,ProductInfoEntity productInfoEntity, List<ParamsSnapshotEntity> paramsSnapshotEntityList) {

        FuMinYxLoanCalculateVO planVO = approveFeign.fuminYxLoanCalculate(orderId).getData();
        Assert.notNull(planVO, "富民还款计划表" + orderId + "信息不存在");
        List<FuMinYxLoanCalculateVO.InstallmentPlan> planList = planVO.getInstallmentPlanList();
        int i = 1;
        for (FuMinYxLoanCalculateVO.InstallmentPlan repaymentInfoEntity : planList) {

            //月还总额
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_" + repaymentInfoEntity.getCurrentPeriod())
                    .setValue(Convert.toStr(repaymentInfoEntity.getTotalRepayAmt() != null ? repaymentInfoEntity.getTotalRepayAmt().setScale(2, RoundingMode.HALF_UP) : null)));
            //还款日期
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REPAYMENT_DATE + "_" + repaymentInfoEntity.getCurrentPeriod())
                    .setValue(DateUtil.format(repaymentInfoEntity.getRepayDate(),DatePattern.NORM_DATE_FORMATTER)));
            //剩余本金
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REMAINING_PRINCIPAL + "_" + repaymentInfoEntity.getCurrentPeriod())
                    .setValue(Convert.toStr(repaymentInfoEntity.getRemainPrincipal() != null ? repaymentInfoEntity.getRemainPrincipal().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO)));

            //还款单日
            String format = DateUtil.format(repaymentInfoEntity.getRepayDate(),DatePattern.NORM_DATE_FORMATTER);
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REPAYMENT_DATE_NUMBER + "_" + repaymentInfoEntity.getCurrentPeriod())
                    .setValue(StrUtil.toString(LocalDate.parse(format).getDayOfMonth())));

            //电销GPS分期  前五期：月还总额+500
            if (i <= 5) {
                paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                        .setLinkId(orderId)
                        .setType(ORDER_SNAPSHOT)
                        .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_100" + repaymentInfoEntity.getCurrentPeriod())
                        .setValue(Convert.toStr(repaymentInfoEntity.getTotalRepayAmt() != null ? repaymentInfoEntity.getTotalRepayAmt().add(new BigDecimal("160")).setScale(2, RoundingMode.HALF_UP) : 160.00)));
                i += 1;
            } else {
                paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                        .setLinkId(orderId)
                        .setType(ORDER_SNAPSHOT)
                        .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_100" + repaymentInfoEntity.getCurrentPeriod())
                        .setValue("0"));
            }

        }

        for (RepaymentInfoEntity repaymentInfoEntity : repaymentInfoEntities) {
            //担保费
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.GUARANTEE_FEE + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(repaymentInfoEntity.getRepaymentGuarantee() != null ? repaymentInfoEntity.getRepaymentGuarantee().setScale(2, RoundingMode.HALF_UP) : null)));
            //担保费大写
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.GUARANTEE_FEE_TEXT + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(Convert.digitToChinese(repaymentInfoEntity.getRepaymentGuarantee() != null ? repaymentInfoEntity.getRepaymentGuarantee().setScale(2, RoundingMode.HALF_UP) : null))));
        }

        return planVO;

    }

    /**
     * 长银还款信息快照
     *
     * @param orderId                  次序id
     * @param startDate                开始时间
     * @param paramsSnapshotEntityList Order Params 快照实体列表
     */
    private ChangYinLoanTrialResponseDTO repaymentChangYinInfoSnapshot(Integer orderId, LocalDate startDate,
                                               BigDecimal amount,OrderCustomerInfoVo orderCustomerInfoVo,
                                               List<RepaymentInfoEntity> repaymentInfoEntities,ProductInfoEntity productInfoEntity, List<ParamsSnapshotEntity> paramsSnapshotEntityList) {

        ChangYinResBodyDTO<ChangYinLoanTrialResponseDTO> loanTrialResult = approveFeign.changYinLoanTrialV2ByOrder(
                new ChangYinLoanTrialByOrderDTO()
                        .setOrderId(orderId)
                        .setIdNo(orderCustomerInfoVo.getIdNumber())
                        .setApplyAmt(amount)
                        .setApplyTnr(productInfoEntity.getTerm() + "")
                        .setContSignDt(startDate)
        );
        Assert.isTrue(ChangYinResBodyDTO.isSuccess(loanTrialResult), "长银还款计划表" + orderId + "信息不存在");
        ChangYinLoanTrialResponseDTO planVO = loanTrialResult.getBody();
        Assert.notNull(planVO, "长银还款计划表" + orderId + "信息不存在");
        List<ChangYinLoanTrialResponseDTO.RepayPlan> planList = planVO.getPayShdTryListAll();
        int i = 1;
        for (ChangYinLoanTrialResponseDTO.RepayPlan repaymentInfoEntity : planList) {

            //月还总额
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(repaymentInfoEntity.getPmt() != null ? repaymentInfoEntity.getPmt().setScale(2, RoundingMode.HALF_UP) : null)));
            //还款日期
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REPAYMENT_DATE + "_" + repaymentInfoEntity.getTerm())
                    .setValue(repaymentInfoEntity.getPayDt()));
            //剩余本金
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REMAINING_PRINCIPAL + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(repaymentInfoEntity.getBal() != null ? repaymentInfoEntity.getBal().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO)));

            //还款单日
            String format = repaymentInfoEntity.getPayDt();
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REPAYMENT_DATE_NUMBER + "_" + repaymentInfoEntity.getTerm())
                    .setValue(StrUtil.toString(LocalDate.parse(format).getDayOfMonth())));

            //电销GPS分期  前五期：月还总额+500
            if (i <= 5) {
                paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                        .setLinkId(orderId)
                        .setType(ORDER_SNAPSHOT)
                        .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_100" + repaymentInfoEntity.getTerm())
                        .setValue(Convert.toStr(repaymentInfoEntity.getPmt() != null ? repaymentInfoEntity.getPmt().add(new BigDecimal("160")).setScale(2, RoundingMode.HALF_UP) : 160.00)));
                i += 1;
            } else {
                paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                        .setLinkId(orderId)
                        .setType(ORDER_SNAPSHOT)
                        .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_100" + repaymentInfoEntity.getTerm())
                        .setValue("0"));
            }

        }

        for (RepaymentInfoEntity repaymentInfoEntity : repaymentInfoEntities) {
            //担保费
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.GUARANTEE_FEE + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(repaymentInfoEntity.getRepaymentGuarantee() != null ? repaymentInfoEntity.getRepaymentGuarantee().setScale(2, RoundingMode.HALF_UP) : null)));
            //担保费大写
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.GUARANTEE_FEE_TEXT + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(Convert.digitToChinese(repaymentInfoEntity.getRepaymentGuarantee() != null ? repaymentInfoEntity.getRepaymentGuarantee().setScale(2, RoundingMode.HALF_UP) : null))));
        }

        return planVO;


    }

    /**
     * 蓝海还款信息快照
     *
     * @param orderId                  次序id
     * @param startDate                开始时间
     * @param paramsSnapshotEntityList Order Params 快照实体列表
     */
    private  LanHaiRepayCalcResponse repaymentLanHaiInfoSnapshot(Integer orderId, LocalDate startDate,
                                                               BigDecimal amount,
                                                               List<RepaymentInfoEntity> repaymentInfoEntities,ProductInfoEntity productInfoEntity, List<ParamsSnapshotEntity> paramsSnapshotEntityList) {


        LanHaiCreditLimitInfoDTO lanHaiCreditLimitInfoDTO = new LanHaiCreditLimitInfoDTO();
        lanHaiCreditLimitInfoDTO.setOrderId(orderId)
                .setLoanPeriod(productInfoEntity.getTerm())
                .setLoanAmount(amount)
                .setRepaymentDay(startDate)
                .setTrialRate(productInfoEntity.getIrr().multiply(new BigDecimal(100)));
        LanHaiRepayCalcResponse planVO = approveFeign.lanHaiCreditLimitInfo(lanHaiCreditLimitInfoDTO).getData();
        Assert.notNull(planVO, "蓝海还款计划表" + orderId + "信息不存在");
        List<LanHaiRepayCalcResponse.RepayPlanDTO> planList = planVO.getPlans();
        int i = 1;
        BigDecimal leftRepayPrincipal = planVO.getLeftRepayPrincipal();
        for (LanHaiRepayCalcResponse.RepayPlanDTO repaymentInfoEntity : planList) {

            //月还总额
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_" + repaymentInfoEntity.getCurrentNum())
                    .setValue(Convert.toStr(repaymentInfoEntity.getRepayAmount() != null ? repaymentInfoEntity.getRepayAmount().setScale(2, RoundingMode.HALF_UP) : null)));
            //还款日期
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REPAYMENT_DATE + "_" + repaymentInfoEntity.getCurrentNum())
                    .setValue(repaymentInfoEntity.getRepayDate().format(DatePattern.NORM_DATE_FORMATTER)));
            //每期还款后剩余本金
            leftRepayPrincipal = leftRepayPrincipal.subtract(repaymentInfoEntity.getLeftRepayPrincipal());
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REMAINING_PRINCIPAL + "_" + repaymentInfoEntity.getCurrentNum())
                    .setValue(Convert.toStr(repaymentInfoEntity.getLeftRepayPrincipal() != null ? leftRepayPrincipal : BigDecimal.ZERO)));

            //还款单日
            String format = repaymentInfoEntity.getRepayDate().format(DatePattern.NORM_DATE_FORMATTER);
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.REPAYMENT_DATE_NUMBER + "_" + repaymentInfoEntity.getCurrentNum())
                    .setValue(StrUtil.toString(LocalDate.parse(format).getDayOfMonth())));

            //电销GPS分期  前五期：月还总额+500
            if (i <= 5) {
                paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                        .setLinkId(orderId)
                        .setType(ORDER_SNAPSHOT)
                        .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_100" + repaymentInfoEntity.getCurrentNum())
                        .setValue(Convert.toStr(repaymentInfoEntity.getRepayAmount() != null ? repaymentInfoEntity.getRepayAmount().add(new BigDecimal("160")).setScale(2, RoundingMode.HALF_UP) : 160.00)));
                i += 1;
            } else {
                paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                        .setLinkId(orderId)
                        .setType(ORDER_SNAPSHOT)
                        .setCode(TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT + "_100" + repaymentInfoEntity.getCurrentNum())
                        .setValue("0"));
            }

        }

        for (RepaymentInfoEntity repaymentInfoEntity : repaymentInfoEntities) {
            //担保费
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.GUARANTEE_FEE + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(repaymentInfoEntity.getRepaymentGuarantee() != null ? repaymentInfoEntity.getRepaymentGuarantee().setScale(2, RoundingMode.HALF_UP) : null)));
            //担保费大写
            paramsSnapshotEntityList.add(new ParamsSnapshotEntity()
                    .setLinkId(orderId)
                    .setType(ORDER_SNAPSHOT)
                    .setCode(TemplateParamsCodeEnum.GUARANTEE_FEE_TEXT + "_" + repaymentInfoEntity.getTerm())
                    .setValue(Convert.toStr(Convert.digitToChinese(repaymentInfoEntity.getRepaymentGuarantee() != null ? repaymentInfoEntity.getRepaymentGuarantee().setScale(2, RoundingMode.HALF_UP) : null))));
        }

        return planVO;


    }


    private static String extractPartAfterSeparator(String input) {
        int index = input.indexOf("_");
        if (index != -1) {
            // 找到第二个下划线的位置
            index = input.indexOf("_", index + 1);
            if (index != -1) {
                // 提取下划线后面的部分
                return input.substring(index + 1);
            }
        }
        return ""; // 如果没有找到，则返回空字符串
    }

    public static String formatDate(String inputDate) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年M月d日");

        try {
            Date date = inputFormat.parse(inputDate);
            return outputFormat.format(date);
        } catch (ParseException e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public String getGenerateContractLockKey(Integer orderId) {
        return "order:contract:generate:" + orderId;
    }

    /**
     * 获取合同快照
     *
     * @param orderId 次序id
     * @return {@link Object }
     */
    public List<ParamsSnapshotEntity> getContractSnapshot(Integer orderId) {
        log.info("ContractFileService.getContractSnapshot start orderId {}", orderId);
        //客户信息
        OrderCustomerInfoVo orderCustomerInfoVo = orderCustomerInfoService.getCustomerInfoByOrderId(orderId);
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        //订单编号
        String orderNumber = orderInfo.getOrderNumber();
        //金额信息
        AmountCalVO amountCalVO = amountService.calAmount(new AmountCalDTO().setOrderId(orderId));
        //产品信息
        ProductInfoEntity productInfoEntity = productInfoMapper.selectById(orderInfo.getProductId());
        //资方信息
        FundInfoEntity fundInfoEntity = fundInfoMapper.selectById(orderInfo.getFundId());

        //车辆信息
        VehicleInfoDTO vehicleInfoDTO = new VehicleInfoDTO();
        vehicleInfoDTO.setOrderId(orderId);
        VehicleInfoVO vehicleInfo = orderVehicleInfoService.getVehicleInfo(vehicleInfoDTO);
        //资方预授信信息
        String creditApplyNo = "";
        String fundContractNo = "";

        if (orderInfo.getFundId() == FundEnum.YING_FENG.getValue()) {
            Result<YingFengInfoVO> result = approveFeign.getYingFengInfoByOrderId(orderId);
            if (Result.isSuccess(result) && result.getData() != null) {

                YingFengInfoVO data = result.getData();
                creditApplyNo = data.getCreditApplyNo();
            }
        }

        if (orderInfo.getFundId() == FundEnum.FU_MIN.getValue()) {
            FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(new MPJLambdaWrapper<FinalFundInfoEntity>()
                            .eq(FinalFundInfoEntity::getOrderId, orderId)
                            .eq(FinalFundInfoEntity::getFundId, orderInfo.getFundId())
                            .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                            .orderByDesc(FinalFundInfoEntity::getCreateTime)
                    , false);
            if (finalFundInfoEntity != null) {
                creditApplyNo = finalFundInfoEntity.getCreditReqNo();
                fundContractNo = finalFundInfoEntity.getLoanContractNo();
            }
        }

        List<PreFundInfoEntity> preFundInfoEntities = preFundInfoMapper.selectList(new LambdaQueryWrapper<PreFundInfoEntity>()
                .eq(PreFundInfoEntity::getPreId, orderInfo.getPreId())
                .eq(PreFundInfoEntity::getFundId, orderInfo.getFundId())
                .eq(PreFundInfoEntity::getDeleteFlag, 0));

        String storeName = orderInfo.getStoreName();
        //门店城市
        //资方-产品对应表
        List<FundProductMappingEntity> fundProductMappingEntities = fundProductMappingMapper
                .selectList(new LambdaQueryWrapper<FundProductMappingEntity>()
                        .eq(FundProductMappingEntity::getProductId, orderInfo.getProductId())
                        .eq(FundProductMappingEntity::getFundId, orderInfo.getFundId())
                        .eq(FundProductMappingEntity::getDeleteFlag, 0));
        FundProductMappingEntity fundProductMappingEntity = new FundProductMappingEntity();
        if (!fundProductMappingEntities.isEmpty()) {
            fundProductMappingEntity = fundProductMappingEntities.get(0);
        }

        StoreAddressInfoEntity storeAddressInfoEntity = new StoreAddressInfoEntity();
        log.info("ContractFileService.getContractSnapshot storeName:{}", storeName);
        if (storeName != null) {
            List<StoreAddressInfoEntity> dataAreaEntities = storeAddressInfoMapper.selectList(new MPJLambdaWrapper<StoreAddressInfoEntity>()
                    .eq(StoreAddressInfoEntity::getStoreName, storeName));
            log.info("ContractFileService.getContractSnapshot dataAreaEntities:{}", JSONUtil.toJsonStr(dataAreaEntities));
            if (CollUtil.isEmpty(dataAreaEntities)) {
                throw new BusinessException("当前门店地址信息不存在");
            }
            storeAddressInfoEntity = dataAreaEntities.get(0);
        }
        //费用信息
        OrderFeeInfoEntity orderFeeInfoEntity = new OrderFeeInfoEntity();
        List<OrderFeeInfoEntity> orderFeeInfoEntities = orderFeeInfoMapper.selectList(new LambdaQueryWrapper<OrderFeeInfoEntity>().eq(OrderFeeInfoEntity::getOrderId, orderId));
        if (CollUtil.isNotEmpty(orderFeeInfoEntities)) {
            orderFeeInfoEntity = orderFeeInfoEntities.get(0);
        }
        if (ObjUtil.equals(orderInfo.getRegionId(),56)|| ObjUtil.equals(orderInfo.getRegionId(),24)){
             orderFeeInfoEntity = orderFeeInfoMapper.selectOne(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                    .eq(OrderFeeInfoEntity::getOrderId, orderId)
                    .eq(OrderFeeInfoEntity::getFeeType, 1)
                    .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                    .eq(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                    .orderByDesc(OrderFeeInfoEntity::getCreateTime).last("limit 1"), false);
        }
        List<DataAreaEntity> dataAreaEntities = dataAreaMapper.selectList(new MPJLambdaWrapper<DataAreaEntity>().like(DataAreaEntity::getCarRegion, vehicleInfo.getVehicleNumber().substring(0, 2)));
        DataAreaEntity dataAreaEntity = new DataAreaEntity();
        if (!CollUtil.isEmpty(dataAreaEntities)) {
            dataAreaEntity = dataAreaEntities.get(0);
        }

        List<ParamsSnapshotEntity> paramsSnapshotEntityList = new ArrayList<>();

        List<DataAreaEntity> areaEntities = dataAreaMapper.selectList(new MPJLambdaWrapper<DataAreaEntity>()
                .eq(DataAreaEntity::getFatherId, dataAreaEntity.getId()));

        String parseAddress = PareAddressUtils.parseAddress(orderCustomerInfoVo.getResidentialAddress());

        try {
            if (parseAddress != null) {
                String[] idCardAddressParts = parseAddress.split("-");

                if (idCardAddressParts.length >= 3) { // 确保数组长度以避免数组越界
                    String idCardAddressPart = idCardAddressParts[2];

                    List<DataAreaEntity> filteredList = areaEntities.stream()
                            .filter(entity -> entity.getAreaName().contains(idCardAddressPart))
                            .toList();

                    // 只在找到对应区域时添加邮编
                    if (!CollUtil.isEmpty(filteredList)) {
                        DataAreaEntity areaData = filteredList.get(0);
                        // 添加住址邮编
                        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.CUSTOMER_ZIP_CODE, areaData.getPostcode());
                    }
                }
            }
        } catch (Exception e) {
            // 记录错误日志，并可以选择抛出自定义的业务异常
            log.error("处理地址信息时出错: {}", e.getMessage());
        }

        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.CUSTOMER_NAME, orderCustomerInfoVo.getName());
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.CUSTOMER_ID_NUMBER, orderCustomerInfoVo.getIdNumber());
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.CUSTOMER_EMAIL, orderCustomerInfoVo.getEmail());
        String residentialAddress = orderCustomerInfoVo.getResidentialAddress();
        if (orderCustomerInfoVo.getResidentialAddress() != null && orderCustomerInfoVo.getResidentialDetailedAddress() != null) {
            residentialAddress += orderCustomerInfoVo.getResidentialDetailedAddress();
        }

        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.CUSTOMER_ADDRESS, residentialAddress);

        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.CUSTOMER_HUKOU_ADDRESS, orderCustomerInfoVo.getIdCardDetailedAddress());

        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TERM, orderInfo.getTerm() != null ? orderInfo.getTerm() : "");
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.FUND_CREDIT_APPLY_NO, creditApplyNo);
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.FUND_AGREEMENT_NUMBER, fundContractNo);
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.ANNUALIZED_COMPREHENSIVE_ACTUAL_RATE, productInfoEntity.getIrr() != null ? productInfoEntity.getIrr().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%" : "");
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.EFFECTIVE_ANNUAL_INTEREST_RATE, productInfoEntity.getIrr() != null ? productInfoEntity.getIrr().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%" : "");

        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_BRAND, vehicleInfo.getBrand());
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_MODEL, vehicleInfo.getVehicleModel());
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_LICENSE_PLATE_NUMBER, vehicleInfo.getVehicleNumber());
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_ENGINE_NUMBER, vehicleInfo.getEngineNumber());
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_VIN, vehicleInfo.getVin());
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_COLOR, vehicleInfo.getVehicleColor());
        if (!CollUtil.isEmpty(preFundInfoEntities)) {
            PreFundInfoEntity preFundInfoEntity = preFundInfoEntities.get(0);
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.PLEDGED_VEHICLE_SOFT_EVALUATION_AMOUNT, preFundInfoEntity.getEvaluationAmount() != null ? preFundInfoEntity.getEvaluationAmount().setScale(2, RoundingMode.HALF_UP).toString() : "");
        }
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TERM_PLUS_MONTH, orderInfo.getTerm() + "月");
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.PROVINCE_CITY_OF_PLEDGED_VEHICLE_LICENSE_PLATE, dataAreaEntity.getAreaName());
        RepaymentListDTO repaymentListDTO = new RepaymentListDTO().setOrderId(orderId);
        repaymentService.list(repaymentListDTO);
        //还款计划表
        List<RepaymentInfoEntity> repaymentInfoEntities = repaymentInfoMapper.selectList(new LambdaQueryWrapper<RepaymentInfoEntity>()
                .eq(RepaymentInfoEntity::getOrderId, orderId)
                .eq(RepaymentInfoEntity::getDeleteFlag, 0)
                .eq(RepaymentInfoEntity::getType, 1)
                .orderByAsc(RepaymentInfoEntity::getTerm));
        Assert.notEmpty(repaymentInfoEntities, "还款计划表" + orderId + "信息不存在");
        //还款计划表相关快照信息
        Integer fundId = orderInfo.getFundId();
        //通汇还款计划表
        HengTongPreRepayPlanVO hengTongPreRepayPlanVO = null;
        FuMinYxLoanCalculateVO fuMinYxLoanCalculateVO = null;
        LanHaiRepayCalcResponse lanHaiYxLoanCalculateVO = null;
        ChangYinLoanTrialResponseDTO changYinLoanTrialResponseDTO = null;
        if (ObjUtil.equals(fundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue())) {
            LocalDate repaymentDate = LocalDate.now();
            hengTongPreRepayPlanVO = repaymentTongHuiInfoSnapshot(orderId, repaymentDate, amountCalVO.getCustomerConfirmAmount(),
                    repaymentInfoEntities, productInfoEntity, paramsSnapshotEntityList);
            try {
                SmartContractReportEntity smartContractReportEntity = smartContractReportMapper.selectOne(
                        new LambdaQueryWrapper<SmartContractReportEntity>()
                                .eq(SmartContractReportEntity::getOrderId, orderId)
                                .eq(SmartContractReportEntity::getDeleteFlag, 0)
                                .orderByDesc(SmartContractReportEntity::getId)
                                .last("LIMIT 1")
                );
                if (ObjUtil.isNotNull(smartContractReportEntity)){
                    smartContractReportEntity.setDeleteFlag(1);
                    smartContractReportMapper.updateById(smartContractReportEntity);
                }
                SmartContractReportEntity entity = new SmartContractReportEntity();
                entity.setOrderId(orderId);
                entity.setFirstAmount(hengTongPreRepayPlanVO.getHuanKuanList().get(0).getYsHeJi());
                entity.setRepayAmount(hengTongPreRepayPlanVO.getHuanKuanList().get(1).getYsHeJi());
                entity.setLastRepayAmount(hengTongPreRepayPlanVO.getHuanKuanList().get(hengTongPreRepayPlanVO.getHuanKuanList().size()-1).getYsHeJi());
                smartContractReportMapper.insert(entity);
            } catch (Exception e) {
                log.info("ContractFileService.getContractSnapshot SmartContractReport error:{}",e.getMessage());
            }
        } else if (ObjUtil.equals(fundId, FundEnum.FU_MIN.getValue())) {
            LocalDate repaymentDate = LocalDate.now();
            fuMinYxLoanCalculateVO = repaymentFuMinInfoSnapshot(orderId, repaymentDate, amountCalVO.getCustomerConfirmAmount(),
                    repaymentInfoEntities, productInfoEntity, paramsSnapshotEntityList);
            try {
                SmartContractReportEntity smartContractReportEntity = smartContractReportMapper.selectOne(
                        new LambdaQueryWrapper<SmartContractReportEntity>()
                                .eq(SmartContractReportEntity::getOrderId, orderId)
                                .eq(SmartContractReportEntity::getDeleteFlag, 0)
                                .orderByDesc(SmartContractReportEntity::getId)
                                .last("LIMIT 1")
                );
                if (ObjUtil.isNotNull(smartContractReportEntity)){
                    smartContractReportEntity.setDeleteFlag(1);
                    smartContractReportMapper.updateById(smartContractReportEntity);
                }
                SmartContractReportEntity entity = new SmartContractReportEntity();
                entity.setOrderId(orderId);
                entity.setFirstAmount(fuMinYxLoanCalculateVO.getInstallmentPlanList().get(0).getTotalRepayAmt());
                entity.setRepayAmount(fuMinYxLoanCalculateVO.getInstallmentPlanList().get(1).getTotalRepayAmt());
                entity.setLastRepayAmount(fuMinYxLoanCalculateVO.getInstallmentPlanList().get(fuMinYxLoanCalculateVO.getInstallmentPlanList().size()-1).getTotalRepayAmt());
                smartContractReportMapper.insert(entity);
            } catch (Exception e) {
                log.info("ContractFileService.getContractSnapshot SmartContractReport error:{}",e.getMessage());
            }
        } else if (ObjUtil.equals(fundId, FundEnum.LAN_HAI.getValue())) {
            LocalDate repaymentDate = LocalDate.now();
            lanHaiYxLoanCalculateVO = repaymentLanHaiInfoSnapshot(orderId, repaymentDate, amountCalVO.getCustomerConfirmAmount(),
                    repaymentInfoEntities, productInfoEntity, paramsSnapshotEntityList);
            try {
                SmartContractReportEntity smartContractReportEntity = smartContractReportMapper.selectOne(
                        new LambdaQueryWrapper<SmartContractReportEntity>()
                                .eq(SmartContractReportEntity::getOrderId, orderId)
                                .eq(SmartContractReportEntity::getDeleteFlag, 0)
                                .orderByDesc(SmartContractReportEntity::getId)
                                .last("LIMIT 1")
                );
                if (ObjUtil.isNotNull(smartContractReportEntity)){
                    smartContractReportEntity.setDeleteFlag(1);
                    smartContractReportMapper.updateById(smartContractReportEntity);
                }
                SmartContractReportEntity entity = new SmartContractReportEntity();
                entity.setOrderId(orderId);
                entity.setFirstAmount(lanHaiYxLoanCalculateVO.getPlans().get(0).getRepayAmount());
                entity.setRepayAmount(lanHaiYxLoanCalculateVO.getPlans().get(1).getRepayAmount());
                entity.setLastRepayAmount(lanHaiYxLoanCalculateVO.getPlans().get(lanHaiYxLoanCalculateVO.getPlans().size()-1).getRepayAmount());
                smartContractReportMapper.insert(entity);
            } catch (Exception e) {
                log.info("ContractFileService.getContractSnapshot SmartContractReport error:{}",e.getMessage());
            }
        } else if (ObjUtil.equals(fundId, FundEnum.CHANG_YIN.getValue())) {
            LocalDate repaymentDate = LocalDate.now();
            changYinLoanTrialResponseDTO = repaymentChangYinInfoSnapshot(orderId, repaymentDate, amountCalVO.getCustomerConfirmAmount(), orderCustomerInfoVo,
                    repaymentInfoEntities, productInfoEntity, paramsSnapshotEntityList);
            try {
                SmartContractReportEntity smartContractReportEntity = smartContractReportMapper.selectOne(
                        new LambdaQueryWrapper<SmartContractReportEntity>()
                                .eq(SmartContractReportEntity::getOrderId, orderId)
                                .eq(SmartContractReportEntity::getDeleteFlag, 0)
                                .orderByDesc(SmartContractReportEntity::getId)
                                .last("LIMIT 1")
                );
                if (ObjUtil.isNotNull(smartContractReportEntity)){
                    smartContractReportEntity.setDeleteFlag(1);
                    smartContractReportMapper.updateById(smartContractReportEntity);
                }
                SmartContractReportEntity entity = new SmartContractReportEntity();
                entity.setOrderId(orderId);
                entity.setFirstAmount(changYinLoanTrialResponseDTO.getPayShdTryListAll().get(0).getPmt());
                entity.setRepayAmount(changYinLoanTrialResponseDTO.getPayShdTryListAll().get(1).getPmt());
                entity.setLastRepayAmount(changYinLoanTrialResponseDTO.getPayShdTryListAll().get(changYinLoanTrialResponseDTO.getPayShdTryListAll().size()-1).getPmt());
                smartContractReportMapper.insert(entity);
            } catch (Exception e) {
                log.info("ContractFileService.getContractSnapshot SmartContractReport error:{}",e.getMessage());
            }
        } else {
            repaymentInfoSnapshot(orderId, repaymentInfoEntities, paramsSnapshotEntityList);
            try {
                SmartContractReportEntity smartContractReportEntity = smartContractReportMapper.selectOne(
                        new LambdaQueryWrapper<SmartContractReportEntity>()
                                .eq(SmartContractReportEntity::getOrderId, orderId)
                                .eq(SmartContractReportEntity::getDeleteFlag, 0)
                                .orderByDesc(SmartContractReportEntity::getId)
                                .last("LIMIT 1")
                );
                if (ObjUtil.isNotNull(smartContractReportEntity)){
                    smartContractReportEntity.setDeleteFlag(1);
                    smartContractReportMapper.updateById(smartContractReportEntity);
                }
                SmartContractReportEntity entity = new SmartContractReportEntity();
                entity.setOrderId(orderId);
                entity.setFirstAmount(repaymentInfoEntities.get(0).getRepaymentAmount());
                entity.setRepayAmount(repaymentInfoEntities.get(1).getRepaymentAmount());
                entity.setLastRepayAmount(repaymentInfoEntities.get(repaymentInfoEntities.size()-1).getRepaymentAmount());
                smartContractReportMapper.insert(entity);
            } catch (Exception e) {
                log.info("ContractFileService.getContractSnapshot SmartContractReport error:{}",e.getMessage());
            }
        }

        if (ObjUtil.isNotNull(hengTongPreRepayPlanVO) && CollUtil.isNotEmpty(hengTongPreRepayPlanVO.getHuanKuanList())) {
            //期数排序升序
            hengTongPreRepayPlanVO.getHuanKuanList().sort(Comparator.comparing(HengTongPreRepayPlanVO.Plan::getQiShu));
        }
        if (ObjUtil.isNotNull(fuMinYxLoanCalculateVO) && CollUtil.isNotEmpty(fuMinYxLoanCalculateVO.getInstallmentPlanList())) {
            //期数排序升序
            fuMinYxLoanCalculateVO.getInstallmentPlanList().sort(Comparator.comparing(FuMinYxLoanCalculateVO.InstallmentPlan::getCurrentPeriod));
        }
        if (ObjUtil.isNotNull(changYinLoanTrialResponseDTO) && CollUtil.isNotEmpty(changYinLoanTrialResponseDTO.getPayShdTryListAll())) {
            //期数排序升序
            changYinLoanTrialResponseDTO.getPayShdTryListAll().sort(Comparator.comparing(ChangYinLoanTrialResponseDTO.RepayPlan::getTerm));
        }

        BigDecimal totalInstallmentAmount = repaymentInfoEntities.stream()
                .map(RepaymentInfoEntity::getInstallmentBrokerageServiceFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal remainingPrincipalTotal = null;
        if (Objects.equals(fundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue()) && ObjUtil.isNotNull(hengTongPreRepayPlanVO)
        && CollUtil.isNotEmpty(hengTongPreRepayPlanVO.getHuanKuanList())) {
            HengTongPreRepayPlanVO.Plan hengTongPlan = hengTongPreRepayPlanVO.getHuanKuanList().get(0);
            remainingPrincipalTotal = hengTongPlan.getYsShengYuBenJin().add(ObjUtil.defaultIfNull(hengTongPlan.getYsBenJin(), BigDecimal.ZERO));
        } else if (Objects.equals(fundId, FundEnum.FU_MIN.getValue()) && ObjUtil.isNotNull(fuMinYxLoanCalculateVO)
                && CollUtil.isNotEmpty(fuMinYxLoanCalculateVO.getInstallmentPlanList())) {
            FuMinYxLoanCalculateVO.InstallmentPlan planFirst = fuMinYxLoanCalculateVO.getInstallmentPlanList().get(0);
            List<FuMinYxLoanCalculateVO.InstallmentPlan.SubjectPlan> subjectPlanList = planFirst.getSubjectPlanList();
            BigDecimal ysBenJin = subjectPlanList.stream()
                    .filter(subjectPlan -> ObjUtil.equals("PRI", subjectPlan.getSubject()))
                    .map(FuMinYxLoanCalculateVO.InstallmentPlan.SubjectPlan::getSubjectAmt)
                    .findFirst()
                    .orElse(BigDecimal.ZERO);
            remainingPrincipalTotal = planFirst.getRemainPrincipal().add(ObjUtil.defaultIfNull(ysBenJin, BigDecimal.ZERO));
        } else if (Objects.equals(fundId, FundEnum.LAN_HAI.getValue()) && ObjUtil.isNotNull(lanHaiYxLoanCalculateVO)
                && CollUtil.isNotEmpty(lanHaiYxLoanCalculateVO.getPlans())) {
            remainingPrincipalTotal = lanHaiYxLoanCalculateVO.getLeftRepayPrincipal();
        } else if (Objects.equals(fundId, FundEnum.CHANG_YIN.getValue()) && ObjUtil.isNotNull(changYinLoanTrialResponseDTO)
                && CollUtil.isNotEmpty(changYinLoanTrialResponseDTO.getPayShdTryListAll())) {
            ChangYinLoanTrialResponseDTO.RepayPlan repayPlan = changYinLoanTrialResponseDTO.getPayShdTryListAll().get(0);
            remainingPrincipalTotal = repayPlan.getPmt().add(repayPlan.getBal());
        } else {
            remainingPrincipalTotal = repaymentInfoEntities.stream()
                    .map(RepaymentInfoEntity::getRemainingPrincipal)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }


        RepaymentInfoEntity firstRepaymentInfoEntity = repaymentInfoEntities.get(0);
        RepaymentInfoEntity lastRepaymentInfoEntity = repaymentInfoEntities.get(repaymentInfoEntities.size() - 1);

        //通汇 还款计划 begin
        HengTongPreRepayPlanVO.Plan hengTongfirstPlan = null;
        HengTongPreRepayPlanVO.Plan hengTongLastPlan = null;
        if (Objects.equals(fundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue()) && ObjUtil.isNotNull(hengTongPreRepayPlanVO)
                && CollUtil.isNotEmpty(hengTongPreRepayPlanVO.getHuanKuanList())) {
            //取第一期
            hengTongfirstPlan = hengTongPreRepayPlanVO.getHuanKuanList().get(0);
            //取最后一期
            hengTongLastPlan = hengTongPreRepayPlanVO.getHuanKuanList().get(hengTongPreRepayPlanVO.getHuanKuanList().size() - 1);
        }
        //通汇 还款计划 end

        //富民 还款计划 begin
        FuMinYxLoanCalculateVO.InstallmentPlan fuMinFirstPlan = null;
        FuMinYxLoanCalculateVO.InstallmentPlan fuMinLastPlan = null;
        if (Objects.equals(fundId, FundEnum.FU_MIN.getValue()) && ObjUtil.isNotNull(fuMinYxLoanCalculateVO)
                && CollUtil.isNotEmpty(fuMinYxLoanCalculateVO.getInstallmentPlanList())) {
            //取第一期
            fuMinFirstPlan = fuMinYxLoanCalculateVO.getInstallmentPlanList().get(0);
            //取最后一期
            fuMinLastPlan = fuMinYxLoanCalculateVO.getInstallmentPlanList().get(fuMinYxLoanCalculateVO.getInstallmentPlanList().size() - 1);
        }
        //富民 还款计划 end

        //蓝海 还款计划 begin
        LanHaiRepayCalcResponse.RepayPlanDTO lanHaiFirstPlan = null;
        LanHaiRepayCalcResponse.RepayPlanDTO LanHaiLastPlan = null;
        if (Objects.equals(fundId, FundEnum.LAN_HAI.getValue()) && ObjUtil.isNotNull(lanHaiYxLoanCalculateVO)
                && CollUtil.isNotEmpty(lanHaiYxLoanCalculateVO.getPlans())) {
            //取第一期
            lanHaiFirstPlan = lanHaiYxLoanCalculateVO.getPlans().get(0);
            //取最后一期
            LanHaiLastPlan = lanHaiYxLoanCalculateVO.getPlans().get(lanHaiYxLoanCalculateVO.getPlans().size() - 1);
        }
        //蓝海 还款计划 end

        //长银 还款计划 begin
        ChangYinLoanTrialResponseDTO.RepayPlan changYinFirstPlan = null;
        ChangYinLoanTrialResponseDTO.RepayPlan changYinLastPlan = null;
        if (Objects.equals(fundId, FundEnum.CHANG_YIN.getValue()) && ObjUtil.isNotNull(changYinLoanTrialResponseDTO)
                && CollUtil.isNotEmpty(changYinLoanTrialResponseDTO.getPayShdTryListAll())) {
            //取第一期
            changYinFirstPlan = changYinLoanTrialResponseDTO.getPayShdTryListAll().get(0);
            //取最后一期
            changYinLastPlan = changYinLoanTrialResponseDTO.getPayShdTryListAll().get(changYinLoanTrialResponseDTO.getPayShdTryListAll().size() - 1);
        }
        //长银 还款计划 end

        //最后一期还款金额
        if (ObjUtil.equals(fundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue()) && ObjUtil.isNotNull(hengTongLastPlan)) {
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT_LAST, hengTongLastPlan.getYsHeJi().setScale(2, RoundingMode.HALF_UP) );

        } else if (ObjUtil.equals(fundId, FundEnum.FU_MIN.getValue()) && ObjUtil.isNotNull(fuMinLastPlan)) {
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT_LAST, fuMinLastPlan.getTotalRepayAmt().setScale(2, RoundingMode.HALF_UP) );

        } else if (ObjUtil.equals(fundId, FundEnum.LAN_HAI.getValue()) && ObjUtil.isNotNull(LanHaiLastPlan)) {
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT_LAST, LanHaiLastPlan.getRepayAmount().setScale(2, RoundingMode.HALF_UP) );

        } else if (ObjUtil.equals(fundId, FundEnum.CHANG_YIN.getValue()) && ObjUtil.isNotNull(changYinLastPlan)) {
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT_LAST, changYinLastPlan.getPmt().add(changYinLastPlan.getGuaFeeAmt()).setScale(2, RoundingMode.HALF_UP) );

        } else {
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT_LAST, lastRepaymentInfoEntity.getRepaymentAmount());

            lastRepaymentInfoEntity = repaymentInfoEntities.get(repaymentInfoEntities.size() - 1);

            //最后一期还款金额
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MONTHLY_REPAYMENT_AMOUNT_LAST, lastRepaymentInfoEntity.getRepaymentAmount());
        }
        BigDecimal subtractPrincipal = null;
        if (ObjUtil.equals(fundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue()) && ObjUtil.isNotNull(hengTongfirstPlan)) {

            subtractPrincipal = firstRepaymentInfoEntity.getRemainingPrincipal();
        } else if (ObjUtil.equals(fundId, FundEnum.FU_MIN.getValue()) && ObjUtil.isNotNull(fuMinFirstPlan)) {

            subtractPrincipal = fuMinFirstPlan.getRemainPrincipal();
        } else if (ObjUtil.equals(fundId, FundEnum.LAN_HAI.getValue()) && ObjUtil.isNotNull(lanHaiFirstPlan)) {

            subtractPrincipal = lanHaiFirstPlan.getLeftRepayPrincipal();
        } else if (ObjUtil.equals(fundId, FundEnum.CHANG_YIN.getValue()) && ObjUtil.isNotNull(changYinFirstPlan)) {

            subtractPrincipal = changYinFirstPlan.getBal();
        } else {
            subtractPrincipal = remainingPrincipalTotal.subtract(firstRepaymentInfoEntity.getRemainingTotalAmount());
        }


        //分期居间服务费总金额
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TOTAL_INSTALLMENT_BROKERAGE_SERVICE_FEE_AMOUNT, totalInstallmentAmount.setScale(2, RoundingMode.HALF_UP).toString());

        //融租总本息（每月还款金额*期限）
        if (ObjUtil.equals(fundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue()) && ObjUtil.isNotNull(hengTongPreRepayPlanVO)) {
            BigDecimal totalRepaymentAmount = hengTongPreRepayPlanVO.getHuanKuanList().stream()
                    .map(HengTongPreRepayPlanVO.Plan::getYsHeJi)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TOTAL_LEASE_PRINCIPAL_AND_INTEREST, totalRepaymentAmount);
            //融租总本息大写
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TOTAL_LEASE_PRINCIPAL_AND_INTEREST_TEXT, Convert.digitToChinese(totalRepaymentAmount));
        } else if (ObjUtil.equals(fundId, FundEnum.FU_MIN.getValue()) && ObjUtil.isNotNull(fuMinYxLoanCalculateVO)) {

            BigDecimal totalRepaymentAmount = fuMinYxLoanCalculateVO.getTotalAmt();

            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TOTAL_LEASE_PRINCIPAL_AND_INTEREST, totalRepaymentAmount);
            //融租总本息大写
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TOTAL_LEASE_PRINCIPAL_AND_INTEREST_TEXT, Convert.digitToChinese(totalRepaymentAmount));
        } else if (ObjUtil.equals(fundId, FundEnum.LAN_HAI.getValue()) && ObjUtil.isNotNull(lanHaiYxLoanCalculateVO)) {

            BigDecimal totalRepaymentAmount = lanHaiYxLoanCalculateVO.getRepayAmount();

            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TOTAL_LEASE_PRINCIPAL_AND_INTEREST, totalRepaymentAmount);
            //融租总本息大写
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TOTAL_LEASE_PRINCIPAL_AND_INTEREST_TEXT, Convert.digitToChinese(totalRepaymentAmount));
        } else if (ObjUtil.equals(fundId, FundEnum.CHANG_YIN.getValue()) && ObjUtil.isNotNull(changYinLoanTrialResponseDTO)) {

            BigDecimal totalRepaymentAmount = changYinLoanTrialResponseDTO.getPayShdTryListAll().stream()
                    .map(ChangYinLoanTrialResponseDTO.RepayPlan::getPmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal guaFeeAmount = changYinLoanTrialResponseDTO.getPayShdTryListAll().stream()
                    .map(ChangYinLoanTrialResponseDTO.RepayPlan::getGuaFeeAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalRepaymentAmount = totalRepaymentAmount.add(guaFeeAmount);
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TOTAL_LEASE_PRINCIPAL_AND_INTEREST, totalRepaymentAmount);
            //融租总本息大写
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TOTAL_LEASE_PRINCIPAL_AND_INTEREST_TEXT, Convert.digitToChinese(totalRepaymentAmount));
        } else {
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TOTAL_LEASE_PRINCIPAL_AND_INTEREST, firstRepaymentInfoEntity.getRepaymentAmount().multiply(new BigDecimal(orderInfo.getTerm())));
            //融租总本息大写
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TOTAL_LEASE_PRINCIPAL_AND_INTEREST_TEXT, Convert.digitToChinese(firstRepaymentInfoEntity.getRepaymentAmount().multiply(new BigDecimal(orderInfo.getTerm()))));
        }
        int installTerm = 0;
        for (RepaymentInfoEntity repaymentInfoEntity : repaymentInfoEntities) {
            if (repaymentInfoEntity.getInstallmentBrokerageServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                installTerm++;
            }
        }

        //居间服务费期数(还款计划表中，居间服务费金额不为0的最大期限数)
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.INSTALLMENT_BROKERAGE_TERM, installTerm);

        //分期居间服务费金额(还款计划表中，第一期的居间服务费金额)
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.INSTALLMENT_BROKERAGE_SERVICE_FEE_AMOUNT, firstRepaymentInfoEntity.getInstallmentBrokerageServiceFee() != null ? firstRepaymentInfoEntity.getInstallmentBrokerageServiceFee().setScale(2, RoundingMode.HALF_UP) : "");

        //当期租金支付后 提前回购金额(元) 总应还本金 - 第一期本金
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.BUYOUT_AMOUNT_AFTER_CURRENT_RENT_PAYMENT_CNY, subtractPrincipal);

        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.HNRT_CO_LTD, TemplateParamsCodeEnum.HNRT_CO_LTD.getValue());
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.HNRT_CO_LTD_SEAL, TemplateParamsCodeEnum.HNRT_CO_LTD_SEAL.getValue());

        BigDecimal gpsFee = productInfoEntity.getGpsFee();
        BigDecimal carServiceFee = productInfoEntity.getCarServiceFee();
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.GPS_FEE, gpsFee);
        String content = "GPS租赁费及抵押手续费";
        if (ObjUtil.equals(orderInfo.getRegionId(),56)|| ObjUtil.equals(orderInfo.getRegionId(),24)){
            gpsFee=ObjUtil.defaultIfNull(orderFeeInfoEntity.getGpsFee(),BigDecimal.ZERO);
            carServiceFee=ObjUtil.defaultIfNull(orderFeeInfoEntity.getCarServiceFee(),BigDecimal.ZERO);
            if (!ObjUtil.equals(orderFeeInfoEntity.getGpsPayType(),2)){
                content = "GPS设备租赁费和服务费";
            }
        }
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.GPS_CONTENT_TEXT, content);
        //TODO 判断是否是电销 获取GPS服务费
        if (gpsFee != null && carServiceFee != null) {
            BigDecimal gpsServiceFeePlus = gpsFee.add(carServiceFee);
            //GPS服务费+抵押手续费
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.GPS_SERVICE_FEE_PLUS_MORTGAGE_HANDLING_FEE, gpsServiceFeePlus);
            //GPS服务费+抵押手续费大写
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.GPS_SERVICE_FEE_PLUS_MORTGAGE_HANDLING_FEE_TEXT, Convert.digitToChinese(gpsServiceFeePlus));
        }

        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.HBLHDT_TECHNOLOGY_CO_LTD, TemplateParamsCodeEnum.HBLHDT_TECHNOLOGY_CO_LTD.getValue());
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.HBLHDT_TECHNOLOGY_CO_LTD_SEAL, TemplateParamsCodeEnum.HBLHDT_TECHNOLOGY_CO_LTD_SEAL.getValue());
        //龙环汇丰信息咨询（北京）有限公司
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.LHHF_CO_LTD, TemplateParamsCodeEnum.LHHF_CO_LTD.getValue());
        //龙环汇丰  公章
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.LHHF_CO_LTD_SEAL, TemplateParamsCodeEnum.LHHF_CO_LTD_SEAL.getValue());
        //客户签字
//        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.CUSTOMER_SIGNATURE, orderCustomerInfoVo.getName());
        //车辆
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.VEHICLE, "车辆");
        //居民身份证
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.RESIDENT_ID, "居民身份证");
        //初次登记日期
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.INITIAL_REGISTRATION_DATE, vehicleInfo.getRegisterDate() != null ? vehicleInfo.getRegisterDate() : "");
        //客户银行卡 开户行
        List<BankAccountSignEntity> bankAccountSignEntities = bankAccountSignMapper.selectList(new LambdaQueryWrapper<BankAccountSignEntity>()
                .eq(BankAccountSignEntity::getOrderId, orderId)
                .eq(BankAccountSignEntity::getSignState, 1)
                .eq(BankAccountSignEntity::getDeleteFlag, 0));
        BankAccountSignEntity bankAccountSignEntity = new BankAccountSignEntity();
        if (!bankAccountSignEntities.isEmpty()) {
            bankAccountSignEntity = bankAccountSignEntities.get(0);
        }
        //客户银行卡  户名
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.CUSTOMER_BANK_ACCOUNT_NAME, bankAccountSignEntity.getName());
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.CUSTOMER_BANK_BRANCH, bankAccountSignEntity.getBankNameUpdate());
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.CUSTOMER_BANK_ACCOUNT_NUMBER, bankAccountSignEntity.getBankCardNumber());
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.CUSTOMER_PHONE, bankAccountSignEntity.getPhone());

        //资方授信额度
        if (amountCalVO != null) {
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.DISBURSEMENT_AMOUNT_NUMERIC, amountCalVO.getCustomerConfirmAmount() != null ? amountCalVO.getCustomerConfirmAmount().setScale(2, RoundingMode.HALF_UP) : "");
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.DISBURSEMENT_AMOUNT_TEXT, amountCalVO.getCustomerConfirmAmount() != null ? Convert.digitToChinese(amountCalVO.getCustomerConfirmAmount().setScale(2, RoundingMode.HALF_UP)) : "");
            //资方授信额度大写
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.FINANCING_PARTY_CREDIT_LIMIT, amountCalVO.getFundPreAmount() != null ? amountCalVO.getFundPreAmount().setScale(2, RoundingMode.HALF_UP) : "");
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.FINANCING_PARTY_CREDIT_TEXT, amountCalVO.getFundPreAmount() != null ? Convert.digitToChinese(amountCalVO.getFundPreAmount().setScale(2, RoundingMode.HALF_UP)) : "");
            //融租租赁金额 盈峰融租租赁金额:放款金额 - 5000
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.FINANCIAL_LEASE_AMOUNT, amountCalVO.getCustomerConfirmAmount() != null ? amountCalVO.getCustomerConfirmAmount().subtract(new BigDecimal("5000")).setScale(2, RoundingMode.HALF_UP) : "");
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.FINANCIAL_LEASE_TEXT, amountCalVO.getCustomerConfirmAmount() != null ? Convert.digitToChinese(amountCalVO.getCustomerConfirmAmount().subtract(new BigDecimal("5000")).setScale(2, RoundingMode.HALF_UP)) : "");
        }

        if (orderInfo.getFundId() == 5) {
            //抵押权人
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE, MortgageInfoEnum.YF_MORTGAGEE.getDescription());
            //抵押权人地址
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_ADDRESS, MortgageInfoEnum.YF_MORTGAGEE_ADDRESS.getDescription());
            //抵押权人联系电话
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_CONTACT_NUMBER, MortgageInfoEnum.YF_MORTGAGEE_CONTACT_NUMBER.getDescription());
            //抵押权人联系邮箱
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_CONTACT_EMAIL, MortgageInfoEnum.YF_MORTGAGEE_CONTACT_EMAIL.getDescription());
            //抵押权人邮编
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_ZIP_CODE, MortgageInfoEnum.YF_MORTGAGEE_ZIP_CODE.getDescription());
            //抵押权人统一社会信用代码
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_UNIFIED_SOCIAL_CREDIT_CODE, MortgageInfoEnum.YF_MORTGAGEE_UNIFIED_SOCIAL_CREDIT_CODE.getDescription());
            //抵押权人1
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_1, MortgageInfoEnum.YF_MORTGAGEE1.getDescription());
            //抵押权人地址1
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_ADDRESS_1, MortgageInfoEnum.YF_MORTGAGEE_ADDRESS1.getDescription());
            //抵押权人联系电话1
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_CONTACT_NUMBER_1, MortgageInfoEnum.YF_MORTGAGEE_CONTACT_NUMBER1.getDescription());
            //抵押权人联系邮箱1
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_CONTACT_EMAIL_1, MortgageInfoEnum.YF_MORTGAGEE_CONTACT_EMAIL1.getDescription());
            //抵押权人邮编1
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_ZIP_CODE_1, MortgageInfoEnum.YF_MORTGAGEE_ZIP_CODE1.getDescription());
            //抵押权人统一社会信用代码1
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_UNIFIED_SOCIAL_CREDIT_CODE_1, MortgageInfoEnum.YF_MORTGAGEE_UNIFIED_SOCIAL_CREDIT_CODE1.getDescription());
            //融担公司
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.RONGDAN_COMPANY_NAME, FundRongDanNameEnum.GD_YINGFENG.getName());
        } else if (orderInfo.getFundId() == 11) {
            //抵押权人
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE, MortgageInfoEnum.FM_MORTGAGEE.getDescription());
            //抵押权人地址
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_ADDRESS, MortgageInfoEnum.FM_MORTGAGEE_ADDRESS.getDescription());
            //抵押权人联系电话
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_CONTACT_NUMBER, MortgageInfoEnum.FM_MORTGAGEE_CONTACT_NUMBER.getDescription());
            //抵押权人联系邮箱
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_CONTACT_EMAIL, MortgageInfoEnum.FM_MORTGAGEE_CONTACT_EMAIL.getDescription());
            //抵押权人邮编
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_ZIP_CODE, MortgageInfoEnum.FM_MORTGAGEE_ZIP_CODE.getDescription());
            //抵押权人统一社会信用代码
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGEE_UNIFIED_SOCIAL_CREDIT_CODE, MortgageInfoEnum.FM_MORTGAGEE_UNIFIED_SOCIAL_CREDIT_CODE.getDescription());
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.RONGDAN_COMPANY_NAME, FundRongDanNameEnum.HLJ_DINGSHENG.getName());
        } else if (orderInfo.getFundId() == 4) {
            //合同履行地
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.PLACE_OF_PERFORMANCE, "/");
        } else if (orderInfo.getFundId() == 3) {
            //客户编号
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.CUSTOMER_ORDER_NUMBER, orderInfo.getOrderNumber());
            //还款方式
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.REPAY_METHOD, dictUtils.getDictLabel(GlobalConstants.DictType.REPAYMENT_METHOD.name(), orderInfo.getRepayMethod()));
            //借款用途
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.APPLY_PURPOSE, dictUtils.getDictLabel(GlobalConstants.DictType.FUNDS_PURPOSE.name(), orderInfo.getApplyPurpose()));
            //合同履行地
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.PLACE_OF_PERFORMANCE, "邯郸市邯山区");
            //软评
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.SOFT_REVIEW_AMOUNT, getCollateralCarAmt(vehicleInfo.getVehicleNumber(), amountCalVO));
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.PROVINCE_CITY_OF_PLEDGED, vehicleInfo.getLicenseProvince()+vehicleInfo.getLicenseCity());
            FundProductMappingEntity productMapping = fundProductMappingMapper.selectOne(new LambdaQueryWrapper<FundProductMappingEntity>()
                    .eq(FundProductMappingEntity::getFundId, orderInfo.getFundId())
                    .eq(FundProductMappingEntity::getProductId, orderInfo.getProductId())
                    .eq(FundProductMappingEntity::getDeleteFlag, 0)
                    .eq(FundProductMappingEntity::getStatus, 0), false);
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.LOAN_PRIME_RATE_LPR, productMapping.getLhLpr() + "%");
            //基点 苏商/蓝海(执行年利率-LPR)*100
            BigDecimal lhLpr = productMapping.getLhLpr().divide(new BigDecimal("100"),  4, RoundingMode.HALF_UP);
            BigDecimal basePoint = productInfoEntity.getIrr().subtract(lhLpr).multiply(BigDecimal.valueOf(10000)).setScale(0, RoundingMode.DOWN);
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.BASIS_POINTS, basePoint);
            //融担公司
            ProductRongdanEntity productRongdanEntity = productRongdanMapper.selectById(orderInfo.getRongdanId());
            if (ObjectUtil.equals(productRongdanEntity.getMiddleCode(), ProductRongDanEnum.ZUN_HAO.getCode())) {
                addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.RONGDAN_COMPANY_NAME, FundRongDanNameEnum.HN_ZUNHAO.getName());
            } else if (ObjectUtil.equals(productRongdanEntity.getMiddleCode(), ProductRongDanEnum.YIN_DING.getCode())) {
                addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.RONGDAN_COMPANY_NAME, FundRongDanNameEnum.HN_YINDING.getName());
            }
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.LANHAI_GUARANTEE_CONTRACT_NUMBER, generateContractNumber(TemplateParamsCodeEnum.LANHAI_GUARANTEE_CONTRACT_NUMBER.getCode(), orderNumber));
            //蓝海差异化定价
            try {
//                PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(
//                        new LambdaQueryWrapper<PreFundInfoEntity>()
//                                .eq(PreFundInfoEntity::getPreId, orderInfo.getPreId())
//                                .eq(PreFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
//                                .eq(PreFundInfoEntity::getDeleteFlag, 0)
//                                .orderByDesc(PreFundInfoEntity::getId)
//                                .last("limit 1")
//                );
                FinalFundInfoEntity finalFundInfoEntity = finalFundInfoMapper.selectOne(
                        new LambdaQueryWrapper<FinalFundInfoEntity>()
                                .eq(FinalFundInfoEntity::getOrderId, orderId)
                                .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                                .last("limit 1")
                );
                addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.ANNUALIZED_INTEREST_RATE, ObjUtil.isNotNull(finalFundInfoEntity.getExecuteRate()) ? finalFundInfoEntity.getExecuteRate().multiply(BigDecimal.valueOf(100))+"%" : "");
                addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.ANNUALIZED_RATE_OF_GUARANTEE_FEE, ObjUtil.isNotNull(finalFundInfoEntity.getExecuteRate()) ? (productInfoEntity.getIrr().subtract(finalFundInfoEntity.getExecuteRate())).multiply(BigDecimal.valueOf(100)) : "");
                // PMT函数等效计算（返回正值）
                // 月利率
                double rate = productInfoEntity.getIrr().doubleValue() / 12;
                // 还款期数
                int nper = productInfoEntity.getTerm();
                // 贷款本金
                double pv = lanHaiYxLoanCalculateVO.getRepayPrincipal().doubleValue();
                // PMT函数等效计算
                BigDecimal monthlyPayment;
                if (Objects.equals(rate, 0)) {
                    monthlyPayment = new BigDecimal(pv / nper);
                } else {
                    double rateAdd = rate + 1;
                    double pow = Math.pow(rateAdd, nper);
                    double multiply = pv * rate;
                    double multiplyTotal = multiply* pow;
                    double pow1 = pow- 1;
                    log.info("ContractFileService.getContractSnapshot rate:{} rateAdd:{} pow:{} multiply:{} multiplyTotal:{} pow1:{}",rate,rateAdd, pow, multiply, multiplyTotal, pow1);
                    monthlyPayment = new BigDecimal(multiplyTotal/pow1).setScale(2, RoundingMode.HALF_UP);//.setScale(2, RoundingMode.HALF_UP)
                }
                log.info("ContractFileService.getContractSnapshot monthlyPayment:{}",monthlyPayment);
                //资方授信利率
                BigDecimal fundRate = finalFundInfoEntity.getExecuteRate();//资方授信利率
                // 月利率
                double rateDouble = fundRate.doubleValue() / 12;
                BigDecimal fundMonthlyPayment;
                if (Objects.equals(rateDouble, 0)) {
                    fundMonthlyPayment = new BigDecimal(pv / nper);
                } else {
                    double rateAdd = rateDouble + 1;
                    double pow = Math.pow(rateAdd, nper);
                    double multiply = pv * rateDouble;
                    double multiplyTotal = multiply * pow;
                    double pow1 = pow - 1;
                    fundMonthlyPayment = new BigDecimal(multiplyTotal / pow1).setScale(2, RoundingMode.HALF_UP);
                }
                log.info("ContractFileService.getContractSnapshot fundMonthlyPayment:{}",fundMonthlyPayment);
                BigDecimal guaranteeTotalFee = ((monthlyPayment.subtract(fundMonthlyPayment)) .multiply(BigDecimal.valueOf(productInfoEntity.getTerm())));//.subtract(lanHaiYxLoanCalculateVO.getRepayAmount())
                log.info("ContractFileService.getContractSnapshot guaranteeTotalFee:{}",guaranteeTotalFee);
                addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TOTAL_AMOUNT_OF_GUARANTEE_FEE, guaranteeTotalFee);
                addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TOTAL_AMOUNT_OF_GUARANTEE_FEE_TO_STR, NumberChineseFormatter.format(guaranteeTotalFee.doubleValue(), true));
                addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.REMAINING_PAYABLE_GUARANTEE_FEES,guaranteeTotalFee);
                addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.REMAINING_PAYABLE_GUARANTEE_FEES_TO_STR, NumberChineseFormatter.format(guaranteeTotalFee.doubleValue(), true));
                addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.INSTALLMENT_OF_GUARANTEE_FEE, productInfoEntity.getTerm());
            } catch (Exception e) {
//                throw new RuntimeException(e);
                log.info("ContractFileService.getContractSnapshot lanHaiGuarantee error:{}",e.getMessage());
            }
        }else if (orderInfo.getFundId() == 16) {
            //借款用途
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.APPLY_PURPOSE, dictUtils.getDictLabel(GlobalConstants.DictType.FUNDS_PURPOSE.name(), orderInfo.getApplyPurpose()));
            //产品名称
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.PRODUCT_NAME, productInfoEntity.getName());
            //还款方式
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.REPAY_METHOD, dictUtils.getDictLabel(GlobalConstants.DictType.REPAYMENT_METHOD.name(), orderInfo.getRepayMethod()));
            //LPR
            ChangYinLPRDTO changYinLPRDTO = new ChangYinLPRDTO();
            changYinLPRDTO.setSignDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
            ChangYinResBodyDTO<ChangLPRResDTO> changYinResBodyDTO = approveFeign.changYinQueryLPR(changYinLPRDTO);
            BigDecimal lprRate = BigDecimal.ZERO;
            log.info("LOAN_PRIME_RATE_LPR：{}", JSONUtil.toJsonStr(changYinResBodyDTO));
            if(ChangYinResBodyDTO.isSuccess(changYinResBodyDTO) && Objects.nonNull(changYinResBodyDTO.getBody())){
                lprRate = changYinResBodyDTO.getBody().getLprRate().multiply(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
            }
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.LOAN_PRIME_RATE_LPR, lprRate + "%");
            //融担公司
            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.RONGDAN_COMPANY_NAME, FundRongDanNameEnum.XA_LISHANG.getName());
        }
        //客户签章
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.CUSTOMER_SEAL, orderCustomerInfoVo.getName());
        //担保公司名称
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.GUARANTOR_COMPANY_NAME, "黑龙江鼎盛融资担保有限公司");
        //担保公司法定代表人
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.GUARANTOR_COMPANY_LEGAL_REPRESENTATIVE, "杨红超");
        //担保公司地址
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.GUARANTOR_COMPANY_ADDRESS, "黑龙江省大庆高新区新兴大街4号大庆电子商务产业园A1710-12室");
        //签订地点
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.PLACE_OF_SIGNING, storeAddressInfoEntity.getCityName());
        //封闭期
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.LOCK_IN_PERIOD, fundProductMappingEntity.getClosedPeriod());
        //封闭期/2
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.LOCK_IN_PERIOD_1, fundProductMappingEntity.getClosedPeriod() != null ? Math.round((double) fundProductMappingEntity.getClosedPeriod() / 2) : "");
        //封闭期/2+1
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.LOCK_IN_PERIOD_2, fundProductMappingEntity.getClosedPeriod() != null ? Math.round((double) fundProductMappingEntity.getClosedPeriod() / 2) + 1 : "");
        //一次性支付居间服务费金额
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.ONE_TIME_BROKERAGE_SERVICE_FEE_AMOUNT, orderFeeInfoEntity.getOnceServiceFee() != null ? orderFeeInfoEntity.getOnceServiceFee().setScale(2, RoundingMode.HALF_UP) : "");
        //实际租赁年化利率
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.ACTUAL_LEASE_ANNUALIZED_INTEREST_RATE, productInfoEntity.getLeaseYearlyRate() != null ? productInfoEntity.getLeaseYearlyRate().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP) : "");
        //当前日期加期数：
        int termYear =  productInfoEntity.getTerm() / 12 ;
        LocalDate datePlusThreeYears = LocalDate.now().plusYears(termYear);
        //签订日期
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.SIGNING_DATE, formatDate(LocalDate.now().toString()));
        //签订日+3年
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.SIGNING_DATE_PLUS_3_YEARS, datePlusThreeYears);
        //签订日+3年（显示xXXX年X月X日）
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.SIGNING_DATE_PLUS_3_YEARS_YMD, formatDate(Objects.toString(datePlusThreeYears)));
        //资方联系地址
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.FINANCING_PARTY_CONTACT_ADDRESS, fundInfoEntity.getFundAddress());
        //资方联系电话
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.FINANCING_PARTY_CONTACT_NUMBER, fundInfoEntity.getFundPhone());
        //借款合同编号
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.LOAN_AGREEMENT_NUMBER, generateContractNumber(TemplateParamsCodeEnum.LOAN_AGREEMENT_NUMBER.getCode(), orderNumber));
        //委托交易合同编号
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.ENTRUSTED_TRANSACTION_CONTRACT_NUMBER, generateContractNumber(TemplateParamsCodeEnum.ENTRUSTED_TRANSACTION_CONTRACT_NUMBER.getCode(), orderNumber));
        //车辆融资租赁合同编号
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.VEHICLE_FINANCIAL_LEASE_CONTRACT_NUMBER, generateContractNumber(TemplateParamsCodeEnum.VEHICLE_FINANCIAL_LEASE_CONTRACT_NUMBER.getCode(), orderNumber));
        //签订日期(日)
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.SIGNING_DATE_DAY, LocalDate.now().getDayOfMonth());

        //机动车抵押合同编号
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MOTOR_VEHICLE_MORTGAGE_CONTRACT_NUMBER, generateContractNumber(TemplateParamsCodeEnum.MOTOR_VEHICLE_MORTGAGE_CONTRACT_NUMBER.getCode(), orderNumber));

        //抵押合同编号
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.MORTGAGE_CONTRACT_NUMBER, generateContractNumber(TemplateParamsCodeEnum.MORTGAGE_CONTRACT_NUMBER.getCode(), orderNumber));
        //担保合同编号
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.GUARANTEE_CONTRACT_NUMBER, generateContractNumber(TemplateParamsCodeEnum.GUARANTEE_CONTRACT_NUMBER.getCode(), orderNumber));
        //委托反担保合同编号
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.COUNTER_GUARANTEE_CONTRACT_NUMBER, generateContractNumber(TemplateParamsCodeEnum.COUNTER_GUARANTEE_CONTRACT_NUMBER.getCode(), orderNumber));
        //《居间服务合同》 编号
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.BROKERAGE_SERVICE_CONTRACT_NUMBER, generateContractNumber(TemplateParamsCodeEnum.BROKERAGE_SERVICE_CONTRACT_NUMBER.getCode(), orderNumber));
        //应收账款融资核准书编号
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.ACCOUNTS_RECEIVABLE_FINANCING_APPROVAL_DOCUMENT_NUMBER, generateContractNumber(TemplateParamsCodeEnum.ACCOUNTS_RECEIVABLE_FINANCING_APPROVAL_DOCUMENT_NUMBER.getCode(), orderNumber));
        //应收账款转让通知书编号
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.ACCOUNTS_RECEIVABLE_ASSIGNMENT_NOTICE_NUMBER, generateContractNumber(TemplateParamsCodeEnum.ACCOUNTS_RECEIVABLE_FINANCING_APPROVAL_DOCUMENT_NUMBER.getCode(), orderNumber));
        //委托担保合同编号
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.ENTRUSTMENT_GUARANTEE_CONTRACT_NUMBER, generateContractNumber(TemplateParamsCodeEnum.ENTRUSTMENT_GUARANTEE_CONTRACT_NUMBER.getCode(), orderNumber));

        LocalDate currentDate = LocalDate.now();
        LocalDate loanExpiryDate = currentDate.plusMonths(orderInfo.getTerm()).minusDays(1);
        // 贷款到日期(当前日期+贷款期限-1的日期)
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.LOAN_EXPIRY_DATE, loanExpiryDate);
        // 贷款到日期(显示xXXX年X月X日)
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.LOAN_EXPIRY_DATE_YMD, formatDate(Objects.toString(loanExpiryDate)));
        //贷款期数（年）贷款期限/12
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.TERM_YEAR, orderInfo.getTerm() != null ? productInfoEntity.getTerm() / 12 : "");
        //初始租金（元） todo:盈峰初始租金 = 盈峰融租租赁金额和IRR计算 按等额本息算 根据对应的产品期数计算
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.INITIAL_RENT_CNY, "");
        //租金（元） todo:盈峰租金 = 盈峰融租租赁金额和IRR计算 按等额本息算 根据对应的产品期数计算
        addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.RENT_CNY, "");
        //todo:LPR  todo:调用资方根据资方进行绑定需要每天进行同步（苏商需要）
//        if(orderInfo.getFundId() != 16){
//            addOrderParamsSnapshot(paramsSnapshotEntityList, orderId, TemplateParamsCodeEnum.LOAN_PRIME_RATE_LPR, "");
//        }

        return paramsSnapshotEntityList;
    }


    /**
     * 根据车牌取抵押车辆估值
     * 支用接口抵押类担保信息中：
     * 非京沪牌或者是京B,沪 C
     * 押品初始估值（collateralEstimateAmt）-----车300软评额度、
     * 最新估值（collateralCurrentAmt）-----车300软评额度，
     * 抵押合同中车辆价值-----车300软评额度
     * 京沪牌，京B沪 C除外
     * 押品初始估值（collateralEstimateAmt）-----预审批额度、
     * 最新估值（collateralCurrentAmt）-----预审批额度，
     * 抵押合同中车辆价值-----预审批额度
     *
     */
    private BigDecimal getCollateralCarAmt(String vehicleNumber,AmountCalVO amountCalDTO) {
        //车300软评额度
        BigDecimal softReviewAmount = amountCalDTO.getSoftReviewAmount();
        //预审批额度
        BigDecimal preApprovalAmount = amountCalDTO.getPreAmount();

        // 判断车牌归属地
        if (StringUtils.isNotBlank(vehicleNumber)) {
            String prefix = vehicleNumber.substring(0, 2);
            // 京沪牌，但排除京B和沪C
            if (("京".equals(prefix.substring(0, 1)) || "沪".equals(prefix.substring(0, 1)))
                    && !("京B".equals(prefix) || "沪C".equals(prefix))) {
                return preApprovalAmount;
            }
        }

        // 非京沪牌或者是京B,沪C
        return softReviewAmount;
    }



    public String generateContractNumber(String code, String orderNumber) {
        String orderNum = extractPartAfterSeparator(orderNumber);
        String prefix = "";
        switch (code) {
            case "LOAN_AGREEMENT_NUMBER":
                prefix = "LHHF_CP";
                break;
            case "MORTGAGE_CONTRACT_NUMBER":
                prefix = "LHHFDY_CP";
                break;
            case "GUARANTEE_CONTRACT_NUMBER":
                prefix = "LHHFDB_CP";
                break;
            case "COUNTER_GUARANTEE_CONTRACT_NUMBER":
                //prefix = "LHHFWTFDB_CP";
                prefix = "FDB_";
                break;
            case "BROKERAGE_SERVICE_CONTRACT_NUMBER":
                prefix = "LHHFJJFW_CP";
                break;
            case "ENTRUSTED_TRANSACTION_CONTRACT_NUMBER":
                prefix = "LHHFWTJY_CP";
                break;
            case "VEHICLE_FINANCIAL_LEASE_CONTRACT_NUMBER":
                prefix = "LHHFZL_CP";
                break;
            case "MOTOR_VEHICLE_MORTGAGE_CONTRACT_NUMBER":
                prefix = "LHHFDY_CP";
                break;
            case "ACCOUNTS_RECEIVABLE_FINANCING_APPROVAL_DOCUMENT_NUMBER":
                prefix = "LHHFRZHZ_CP";
                break;
            case "ACCOUNTS_RECEIVABLE_ASSIGNMENT_NOTICE_NUMBER":
                prefix = "LHHFZRTZ_CP";
                break;
            case "ENTRUSTMENT_GUARANTEE_CONTRACT_NUMBER":
                prefix = "WTDB_";
                break;
            case "LANHAI_GUARANTEE_CONTRACT_NUMBER":
                prefix = "LHHFWTDB-CP";
                break;
        }

        if (StrUtil.equals(prefix, "LHHFDY_CP")){
            return prefix +  LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss")) + String.format("%03d", new Random().nextInt(100000));
        }

        return prefix + orderNum;
    }

    /**
     * 添加 Order Params 快照
     *
     * @param list    列表
     * @param orderId 次序id
     * @param code    编码
     * @param value   价值
     */
    private void addOrderParamsSnapshot(List<ParamsSnapshotEntity> list, Integer orderId, TemplateParamsCodeEnum code, Object value) {
        ParamsSnapshotEntity entity = new ParamsSnapshotEntity();
        entity.setLinkId(orderId);
        entity.setType(ORDER_SNAPSHOT);
        entity.setCode(code.getCode());
        entity.setValue(Convert.toStr(value));
        list.add(entity);
    }

    /**
     * 初始化合同快照
     *
     * @param orderId 次序id
     */

    public void initContractSnapshot(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, "订单不存在");
        //1:清空订单参数快照
        paramsSnapshotMapper.update(new LambdaUpdateWrapper<ParamsSnapshotEntity>()
                .eq(ParamsSnapshotEntity::getLinkId, orderId)
                .eq(ParamsSnapshotEntity::getType, 1)
                .eq(ParamsSnapshotEntity::getDeleteFlag, 0)
                .set(ParamsSnapshotEntity::getDeleteFlag, 1));
        //2：获取所有需要用到的数据
        List<ParamsSnapshotEntity> orderParamsSnapshotEntities = getContractSnapshot(orderId);
        //3:保存数据
        orderParamsSnapshotService.saveBatch(orderParamsSnapshotEntities);
    }

    /**
     * 生成合同签约列表
     *
     * @param orderId 订单ID
     */
    @Async
    public void generateContractSignList(Integer orderId) {
        String lockKey = getGenerateContractLockKey(orderId);
        String requestId = IdUtil.randomUUID();
        Boolean tryLock = redisService.tryLock(lockKey, requestId, 120);
        if (!tryLock) {
            log.info("ContractServiceImpl.generateContractSignList, lock failed, orderId = {}", lockKey);
            return;
        }

        //生成合同前删除对应订单合同
        orderContractMapper.update(new LambdaUpdateWrapper<OrderContractEntity>()
                .set(OrderContractEntity::getDeleteFlag, 1)
                .eq(OrderContractEntity::getOrderId, orderId)
                .eq(OrderContractEntity::getDeleteFlag, 0)
                .ne(OrderContractEntity::getFundGenerateFlag, 1)
        );

        //生成合同前删除原签署任务
        signTaskMapper.update(new LambdaUpdateWrapper<SignTaskEntity>()
                .set(SignTaskEntity::getDeleteFlag, 1)
                .eq(SignTaskEntity::getBusiId, orderId)
                .eq(SignTaskEntity::getSignType, 1)
                .eq(SignTaskEntity::getDeleteFlag, 0));

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);;
        fundSignInfoService.verifyFundSignInfo(orderInfoEntity);


        //生成合同快照
        this.initContractSnapshot(orderId);
        int contractState = ContractEnum.WAITING.getCode();
        try {
            Boolean contractFlag = false;
            log.info("ContractServiceImpl.generateContractSignList begin orderId:{}", orderId);
            Assert.notNull(orderId, "订单id不能为空");

            Assert.notNull(orderInfoEntity, "订单不存在");

            //判断蓝海融担公司关联的合同模板
            List<Integer> rongdanTypeList = List.of(0);
            if (Objects.equals(orderInfoEntity.getFundId(), FundEnum.LAN_HAI.getValue()) && ObjUtil.isNotNull(orderInfoEntity.getRongdanId())) {
                ProductRongdanEntity productRongdanEntity = productRongdanMapper.selectById(orderInfoEntity.getRongdanId());
                if (ObjectUtil.equals(productRongdanEntity.getMiddleCode(), ProductRongDanEnum.ZUN_HAO.getCode())) {
                    rongdanTypeList = Arrays.asList(0, 1);
                } else if (ObjectUtil.equals(productRongdanEntity.getMiddleCode(), ProductRongDanEnum.YIN_DING.getCode())) {
                    rongdanTypeList = Arrays.asList(0, 2);
                }
            }

            //判断是否是电销分期  电销分期(0, 2, 3, 4)  电销一次性(0, 1, 3, 5)  非电销(0, 1, 6, 7)
            List<Integer> stagesTypeList = List.of(0, 1, 6, 7);
            if (ObjUtil.equals(orderInfoEntity.getRegionId(),56)|| ObjUtil.equals(orderInfoEntity.getRegionId(),24)){
                OrderFeeInfoEntity orderFeeInfoEntity = orderFeeInfoMapper.selectOne(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                        .eq(OrderFeeInfoEntity::getOrderId, orderId)
                        .eq(OrderFeeInfoEntity::getFeeType, 1)
                        .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                        .eq(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                        .orderByDesc(OrderFeeInfoEntity::getCreateTime).last("limit 1"), false);
                if (ObjUtil.equals(orderFeeInfoEntity.getGpsPayType(),2)){
                    stagesTypeList= Arrays.asList(0,2,3,4);
                }else if(ObjUtil.equals(orderFeeInfoEntity.getGpsPayType(),3)){
                    stagesTypeList= Arrays.asList(0,1,3,6);
                }else {
                    stagesTypeList= Arrays.asList(0,1,3,5);
                }
            }
            Integer fundId = orderInfoEntity.getFundId();
            List<FundTemplateAssoEntity> fundTemplateAssoList = fundTemplateAssoMapper.selectList(new MPJLambdaWrapper<FundTemplateAssoEntity>()
                    .eq(FundTemplateAssoEntity::getFundId, fundId)
                    .eq(FundTemplateAssoEntity::getStatus, 0)
                    .eq(FundTemplateAssoEntity::getType, 1)
                    .eq(FundTemplateAssoEntity::getDeleteFlag, 0)
                    .notIn(FundTemplateAssoEntity::getContractFlag, 3, 4)
                    .like(Objects.equals(orderInfoEntity.getFundId(),FundEnum.FU_MIN.getValue()) || Objects.equals(orderInfoEntity.getFundId(), FundEnum.LAN_HAI.getValue()) || Objects.equals(orderInfoEntity.getFundId(), FundEnum.ZHONG_HENG_TONG_HUI.getValue()), FundTemplateAssoEntity::getProductId, orderInfoEntity.getProductId())
                    .in(Objects.equals(orderInfoEntity.getFundId(), FundEnum.LAN_HAI.getValue()) && CollUtil.isNotEmpty(rongdanTypeList), FundTemplateAssoEntity::getRongdanType, rongdanTypeList)
                    .in(CollUtil.isNotEmpty(stagesTypeList), FundTemplateAssoEntity::getStagesType, stagesTypeList)
            );
            log.info("ContractServiceImpl.generateContractSignList fundTemplateAssoList:{}", fundTemplateAssoList);
            List<Integer> list = fundTemplateAssoList.stream().map(FundTemplateAssoEntity::getTemplateId).toList();
            GenerateContractDTO generateContractDTO = new GenerateContractDTO().setId(orderId).setType(ORDER_SNAPSHOT.getCode()).setTemplateIdList(list);
            Result<List<GenerateContractVO>> listResult = resourceFeign.generateContract(generateContractDTO);
            log.info("ContractServiceImpl.generateContractSignList listResult:{}", JSONUtil.toJsonStr(listResult));
            Map<Integer, List<GenerateContractVO>> generateContract = null;
            if (Result.isSuccess(listResult)) {
                List<GenerateContractVO> data = listResult.getData();
                if (CollUtil.isEmpty(data)) {
                    contractFlag = true;
                    log.error("ContractServiceImpl.generateContractSignList 订单生成合同失败 orderId:{}", orderId);
                }
                //将data转成map
                generateContract = data.stream()
                        .collect(Collectors.groupingBy(GenerateContractVO::getTemplateId));
                log.info("ContractServiceImpl.generateContractSignList generateContract:{}", JSONUtil.toJsonStr(generateContract));
            } else {
                contractFlag = true;
            }
            Map<Integer, List<GenerateContractVO>> generateContractMap = generateContract;
            Assert.notNull(generateContractMap, "合同信息不存在");

            //初始化订单合同列表
            List<String> resourceIdList = new ArrayList<>();
            log.info("ContractServiceImpl.generateContractSignList resourceIdList:{}", JSONUtil.toJsonStr(resourceIdList));
            for (FundTemplateAssoEntity fundTemplateAssoEntity : fundTemplateAssoList) {
                List<GenerateContractVO> generateContractInfo = generateContractMap.get(fundTemplateAssoEntity.getTemplateId());
                GenerateContractVO generateContractVO = generateContractInfo.get(0);
                OrderContractEntity orderContractEntity = new OrderContractEntity()
                        .setOrderId(orderId)
                        .setContractFlag(fundTemplateAssoEntity.getContractFlag())
                        .setName(fundTemplateAssoEntity.getTemplateName())
                        .setTemplateId(fundTemplateAssoEntity.getTemplateId())
                        .setSignStatus(0)
                        .setNumber(generateContractVO.getContractNumber())
                        .setResource(generateContractVO.getResourceId())
                        .setFundGenerateFlag(0);
                log.info("ContractServiceImpl.generateContractSignList orderContractEntity:{}", JSONUtil.toJsonStr(orderContractEntity));
                orderContractMapper.insert(orderContractEntity);
                //判断是否需要资方签署
                FileTemplateInfoEntity fileTemplateInfoEntity = fileTemplateInfoMapper.selectById(fundTemplateAssoEntity.getTemplateId());
                if (Objects.equals(fileTemplateInfoEntity.getFundSign(), 0)) {
                    resourceIdList.add(generateContractVO.getResourceId());
                }
            }

            if (contractFlag) {
                log.error("ContractServiceImpl.generateContractSignList 订单生成合同失败 orderId:{}", orderId);
                contractState = ContractEnum.BUILD_FAILED.getCode();
            } else {
                log.info("ContractServiceImpl.generateContractSignList 订单生成合同成功 orderId:{}", orderId);
                contractState = ContractEnum.SIGNING.getCode();
            }

//            CompletableFuture.supplyAsync(() -> {
            // 上传法大大
            fadadaAuthService.batchUploadFileToFdd(resourceIdList);
//                return "ok";
//            }).thenAccept(processResult -> log.info("上传合同文件到法大大: {}", processResult));

//            if(orderInfoEntity.getFundId()== FundEnum.FU_MIN.getValue()){
//                //获取富民合同
//                Result<List<FMContractPreviewVO>> fuMinContractPreviewAllResult = approveFeign.fuMinContractPreviewAll(orderId);
//                if(!Result.isSuccess(fuMinContractPreviewAllResult)||CollUtil.isEmpty(fuMinContractPreviewAllResult.getData())) {
//                    log.error("ContractServiceImpl.generateContractSignList 富民个人贷款借款合同获取失败 orderId:{}", orderId);
//                }else{
//                    List<FMContractPreviewVO> fuMinContracList = fuMinContractPreviewAllResult.getData();
//                    for(FMContractPreviewVO fuMinContract:fuMinContracList){
//                        if(fuMinContract.getResourceId() == null){
//                            continue;
//                        }
//                        OrderContractEntity orderContractEntity = new OrderContractEntity()
//                                .setOrderId(orderId)
//                                .setContractFlag(1)
//                                .setName(fuMinContract.getFileName())
//                                .setSignStatus(0)
//                                .setNumber(NumberUtils.getContractNumber())
//                                .setTemplateId(fuMinContract.getTemplateId())
//                                .setResource(fuMinContract.getResourceId());
//                        log.info("ContractServiceImpl.fuMinContractPreviewAllResult orderContractEntity:{}", JSONUtil.toJsonStr(orderContractEntity));
//                        orderContractMapper.insert(orderContractEntity);
//                    }
//                }
//            }
            log.info("ContractServiceImpl.generateContractSignList resourceIdList:{}", resourceIdList);
        } catch (Exception e) {
            contractState = ContractEnum.BUILD_FAILED.getCode();
            log.error("ContractServiceImpl.generateContractSignList e:", e);
            throw new BusinessException("生成合同签约列表失败");
        } finally {
            orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                    .set(OrderInfoEntity::getContractState, contractState)
                    .eq(OrderInfoEntity::getId, orderId)
                    .eq(OrderInfoEntity::getDeleteFlag, 0)
            );

            redisService.releaseLock(lockKey, requestId);
        }
    }


    /**
     * 合同生成重试
     */
    public void contractGenerateRetry() {
        //获取合同生成失败的订单
        List<OrderInfoEntity> list = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
                        .eq(OrderInfoEntity::getContractState, -1)
//                .or(e -> e.eq(OrderInfoEntity::getContractState, 0).eq(OrderInfoEntity::getState, CUSTOMER_APPOINTMENT))
                        .eq(OrderInfoEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(list)) {
            return;
        }
        log.info("ContractServiceImpl.contractGenerateRetry list:{}", JSONUtil.toJsonStr(list));
        list.stream().parallel().forEach(orderInfoEntity -> {
            Integer id = orderInfoEntity.getId();
            generateContractSignList(id);
        });
    }


    /**
     * 重置资方签署状态和我司合同状态
     *
     * @param orderId
     */
    public void resetFundSignStatus(Integer orderId) {
        log.info("ContractServiceImpl.resetFundSignStatus orderId:{}", orderId);
        OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getId, orderId)
                .select(OrderInfoEntity::getContractState, OrderInfoEntity::getFundId,OrderInfoEntity::getId));
        Integer contractState = orderInfo.getContractState();
        if (contractState == 0) {
            return;
        }
        //获取线上签约开关状态
        SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.ONLINE_SIGN);
        if (ObjectUtil.isNotEmpty(switchInfo) && Objects.equals(switchInfo.getSwitchFlag(), 1)) {
            //推送线上合同短信
            //数据处理
            if (Objects.equals(orderInfo.getFundId(), FundEnum.YING_FENG.getValue()) || Objects.equals(orderInfo.getFundId(), FundEnum.CHANG_YIN.getValue())
                    || Objects.equals(orderInfo.getFundId(), FundEnum.LAN_HAI.getValue())) {
                log.info("ContractServiceImpl.resetFundSignStatus  orderId:{} fundId:{} reset", orderId, orderInfo.getFundId());
                fundSignInfoMapper.update(new LambdaUpdateWrapper<FundSignInfoEntity>()
                        .set(FundSignInfoEntity::getDeleteFlag, 1)
                        .eq(FundSignInfoEntity::getOrderId, orderId)
                        .eq(FundSignInfoEntity::getDeleteFlag, 0)
                        .ne(FundSignInfoEntity::getSignStatus, FundSignStatusEnum.NOT_INITIATED.getCode()));
            }
        }
        log.info("ContractServiceImpl.resetFundSignStatus orderId:{} contractState reset", orderId);
        //将合同签订状态置为生成中
        orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getId, orderId)
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .set(OrderInfoEntity::getContractState, 0));
    }
}
