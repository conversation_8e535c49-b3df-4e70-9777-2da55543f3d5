package com.longhuan.approve.boot.pojo.dto.lishang.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class LishangSaveDataRequest {


    @JsonProperty("id")
    private String id; // 订单编号

    @JsonProperty("cus_name")
    private String cus_name; // 客户姓名

    @JsonProperty("car_no")
    private String car_no; // 车牌号

    @JsonProperty("bank")
    private String bank; // 资方

    @JsonProperty("pro_name")
    private String pro_name; // 产品名称

    @JsonProperty("contract_amount")
    private BigDecimal contract_amount; // 客户确认额度

    @JsonProperty("repay_period")
    private Integer repay_period; // 担保期数

    @JsonProperty("instill_period")
    private Integer instill_period; // 客户最近一期应还期数

    @JsonProperty("first_period")
    private String first_period; // 第一期应还日期

    @JsonProperty("first_amount")
    private BigDecimal first_amount; // 第一期应还合计

    @JsonProperty("status")
    private Integer status; // 订单当前状态 1还款中，2逾期，3已结清

    @JsonProperty("belongs_stores")
    private String belongs_stores; // 订单所属门店

    @JsonProperty("remain_amount")
    private BigDecimal remain_amount; // 剩余未还在库金额

    @JsonProperty("loan_rate")
    private BigDecimal loan_rate; // 借款合同中的借款利率

    @JsonProperty("lending_time")
    private Long lending_time; // 最近一期实还时间

    @JsonProperty("payment_time")
    private Long payment_time; // 放款时间

    @JsonProperty("credit_time")
    private Long credit_time; // 订单资方终审时间

    @JsonProperty("file_dbh")
    private String file_dbh; // 担保函（非必传，合同文件中存在担保函则必传）

    @JsonProperty("file_jkht")
    private String file_jkht; // 客户借款合同（必传，加盖资方电子章版）

    @JsonProperty("file_wtdb")
    private String file_wtdb; // 个人委托担保合同（必传）
}
