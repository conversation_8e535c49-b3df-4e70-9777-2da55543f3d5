<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.longhuan.order.mapper.PreApprovalApplyInfoMapper">

<!--    <select id="selectPreApprovalListVO" resultType="com.longhuan.order.pojo.vo.PreApprovalListVO">-->
<!--        SELECT-->
<!--        t1.id AS id,-->
<!--        t1.name AS name,-->
<!--        t1.phone AS phoneNumber,-->
<!--        t1.loan_amount AS applyAmount,-->
<!--        t1.loan_period AS term,-->
<!--        t1.create_time AS applyDate,-->
<!--        t1.fund_status AS fundStatus,-->
<!--        t1.risk_status AS riskStatus,-->
<!--        t1.fund_id AS fundId-->
<!--        FROM-->
<!--        LH_PRE_APPROVAL_APPLY_INFO t1-->
<!--        INNER JOIN-->
<!--        (SELECT t.name,-->
<!--        t.phone,-->
<!--        t.id_number,-->
<!--        t.create_time,-->
<!--        t.fund_status,-->
<!--        t.id,-->
<!--        rank() over (partition by t.name, t.phone, t.id_number order by CASE-->
<!--        WHEN t.risk_status = 5 THEN 1-->
<!--        WHEN t.risk_status = 3 THEN 2-->
<!--        WHEN t.risk_status = 2 THEN 3-->
<!--        WHEN t.risk_status = 1 THEN 4-->
<!--        WHEN t.risk_status = 0 THEN 5-->
<!--        ELSE 6 END-->
<!--        , CASE-->
<!--        WHEN t.fund_status = 2 THEN 1-->
<!--        WHEN t.fund_status = 1 THEN 2-->
<!--        WHEN t.fund_status = 0 THEN 3-->
<!--        ELSE 4 END ,t.create_time desc,t.id desc) as rank_value-->
<!--        FROM-->
<!--        lh_pre_approval_apply_info t-->
<!--        INNER JOIN-->
<!--        LH_PRE_APPROVAL_FDD_AUTH t2-->
<!--        ON-->
<!--        (t.id_number = t2.id_number-->
<!--        AND-->
<!--        t.phone = t2.phone-->
<!--        AND t2.auth_result = 'success')-->
<!--        INNER JOIN LH_PRE_FDD_SIGN_TASK t3-->
<!--        ON-->
<!--        (t.id = t3.pre_id-->
<!--        AND-->
<!--        t2.client_user_id = t3.client_user_id-->
<!--        AND t3.sign_task_status = 'task_finished')-->
<!--        <where>-->

<!--            <if test="ew.sqlSegment != null">-->
<!--                ${ew.sqlSegment}-->
<!--            </if>-->
<!--        </where>-->
<!--        ) u-->
<!--        on (u.id=t1.id AND u.rank_value=1)-->
<!--        ORDER BY-->
<!--        t1.create_time DESC-->
<!--    </select>-->
</mapper>