package com.longhuan.order.controller;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.pojo.vo.TongCheShuSm4VO;
import com.longhuan.order.util.crypt.MD5Util;
import com.longhuan.order.util.crypt.RsaSecretUtil;
import com.longhuan.order.util.crypt.SM4Util;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/tongcheshu")
@Api(tags = "铜车署请求加密方法")
public class TongCheShuController {

    @Value("${tongcheshu.publicKey}")
    private String publicKey;


    @Value("${tongcheshu.privateKey}")
    private String privateKey;


    @Value("${tongcheshu.sm4Key}")
    private String sm4Key;


    /**
     * sm4加密
     *
     * @param content 铜车署接口请求内容(base64编码后的字符串)
     * @return {@link Result }<{@link TongCheShuSm4VO }>
     */
    @PostMapping("/sm4Encrypt")
    public Result<TongCheShuSm4VO> sm4Encrypt(@RequestBody String content) {
        try {
            TongCheShuSm4VO tongCheShuSm4VO = new TongCheShuSm4VO();

            // MD5生成摘要
            String jsonStr = MD5Util.encrypt(content);

            // 通过RSA公钥生成签名
            String signature = RsaSecretUtil.publicKeyEncrypt(jsonStr, publicKey);

            // 动态生成国密秘钥
//            String sm4Key = SM4Util.generateKey();

            // 使用国密秘钥对数据进行加密
            byte[] bytes = SM4Util.encrypt_Ecb_Padding(ByteUtils.fromHexString(sm4Key), content.getBytes(StandardCharsets.UTF_8));

            // 通过base64对国密秘钥加密
            String sm4KeyStr = Base64.getEncoder().encodeToString(sm4Key.getBytes(StandardCharsets.UTF_8));
            // 再次通过RSA对加密后的密文进行加密
            String sm4KeySecret = RsaSecretUtil.publicKeyEncrypt(sm4KeyStr, publicKey);
            tongCheShuSm4VO.setBody(Base64.getEncoder().encodeToString(bytes));
            tongCheShuSm4VO.setSignature(signature);
            tongCheShuSm4VO.setSecurityKey(sm4KeySecret);

            return Result.success(tongCheShuSm4VO);

        } catch (Exception e) {
            return Result.failed(e.getMessage());
        }
    }

    /**
     * sm4解密
     *
     * @param content 内容
     * @return {@link Result }<{@link String }>
     */
    @PostMapping("/sm4Decrypt")
    public Result<String> sm4Decrypt(@RequestBody TongCheShuSm4VO content) {

        if (StrUtil.isEmpty(content.getBody())) {
            throw new BusinessException("请求体为空");
        }
        String hexString = content.getBody();
        if ("base64".equalsIgnoreCase(content.getEncodeType())) {
            byte[] body = Base64Decoder.decode(content.getBody());
            hexString = ByteUtils.toHexString(body);
        }
        String json;
        try {
            json = SM4Util.decryptEcb(sm4Key, hexString);
        } catch (Exception e) {
            log.error("sm4 decrypt error", e);
            throw new BusinessException("SM4解密失败");
        }
        return Result.success(json);
    }


    /**
     * 铜车署回调解密带SecurityKey
     *
     * @param content 内容
     * @return {@link Result }<{@link String }>
     */
    @PostMapping("/sm4DecryptWithSecurityKey")
    public Result<String> rsaDecrypt(@RequestBody TongCheShuSm4VO content) {

        byte[] body = Base64Decoder.decode(content.getBody());
        String securityKey = content.getSecurityKey();
        String sign = content.getSignature();

//        RSA rsa = SecureUtil.rsa(privateKey, null);
//        byte[] decrypt = rsa.decrypt(securityKey, KeyType.PrivateKey);
//        log.info("decrypt:{}", ByteUtils.toHexString(decrypt));
        String sm4KeyStr = RsaSecretUtil.privateKeyDecrypt(securityKey, privateKey);
        log.info("sm4KeyStr:{}", sm4KeyStr);
        String sm4Key = Base64Decoder.decodeStr(sm4KeyStr);
        String json;

        try {
            byte[] keyData = ByteUtils.fromHexString(sm4Key);
            byte[] bytes = SM4Util.decryptEcbPadding(keyData, body);
            json = new String(bytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("rsaDecrypt！", e);
            throw new RuntimeException("SM4解密失败");
        }

//        String jsonStr = MD5Util.encrypt(json);
//        String signature = RsaSecretUtil.privateKeyDecrypt(sign, privateKey);
//        if (jsonStr != null && !jsonStr.equals(signature)) {
//            log.error("验签不通过！");
//            throw new RuntimeException("验签异常");
//        }
        return Result.success(json);
    }
}
