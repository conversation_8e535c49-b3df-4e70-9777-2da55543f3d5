package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.SearchStoreDTO;
import com.longhuan.order.pojo.vo.StoreAddressListVO;
import com.longhuan.order.pojo.vo.StoreInfoVO;
import com.longhuan.order.service.StoreService;
import io.swagger.annotations.Api;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 门店地址接口
 *
 * <AUTHOR>
 * @date 2024/08/17
 */
@Api(tags = "门店地址接口")
@RestController
@RequestMapping("/api/v1/store")
@RequiredArgsConstructor
public class StoreController {
    private final StoreService storeService;


    /**
     * 上传
     *
     * @param file    文件
     * @param request 请求
     * @return {@link Result }<{@link List }<{@link StoreInfoVO }>>
     * @throws IOException ioException
     */
    @PostMapping("/upload")
    public Result<List<StoreInfoVO>> upload(@RequestParam("file") MultipartFile file, HttpServletRequest request) throws IOException {
        return Result.success(storeService.upload(file));
    }

    /**
     * 搜索门店
     *
     * @param searchStoreDTO 搜索门店DTO
     * @return {@link Result }<{@link List }<{@link StoreAddressListVO }>>
     */
    @PostMapping("/search_store")
    public Result<List<StoreAddressListVO>> searchStore(@Validated @RequestBody SearchStoreDTO searchStoreDTO) {
        return Result.success(storeService.searchStore(searchStoreDTO));
    }
}
