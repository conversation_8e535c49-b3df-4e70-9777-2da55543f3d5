package com.longhuan.order.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.pojo.dto.OrderLoanReservoirDTO;
import com.longhuan.order.pojo.vo.OrderLoanReservoirListVO;
import com.longhuan.order.service.OrderLoanReservoirService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 订单放款蓄水池
 *
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/orderLoanReservoir")
@Api("订单放款蓄水池")
public class LoanOrderReservoirController {
    private final OrderLoanReservoirService orderLoanReservoirService;

    @ApiOperation("列表查询")
    @PostMapping("/pageList")
    public Result<Page<OrderLoanReservoirListVO>> list(@RequestBody OrderLoanReservoirDTO orderLoanReservoirDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderLoanReservoirService.list(orderLoanReservoirDTO,loginUser));
    }

    @ApiOperation("批量推送放款")
    @PostMapping("/batchPushLoan")
    public Result<Boolean> batchPushLoan(@RequestBody OrderLoanReservoirDTO orderLoanReservoirDTO, @CurrentUser LoginUser loginUser) {
        Assert.isFalse(CollUtil.isEmpty(orderLoanReservoirDTO.getIdList()) , () -> new BusinessException("未选择订单"));
        return Result.success(orderLoanReservoirService.batchPushLoan(orderLoanReservoirDTO,loginUser));
    }

    @ApiOperation("驳回")
    @PostMapping("/reject")
    public Result<Boolean> reject(@RequestBody OrderLoanReservoirDTO orderLoanReservoirDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderLoanReservoirService.reject(orderLoanReservoirDTO.getOrderId(),loginUser));
    }

    @ApiOperation("下载模板")
    @PostMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response,@CurrentUser LoginUser loginUser) {
        orderLoanReservoirService.downloadTemplate(response,loginUser);
    }

    @ApiOperation("上传数据")
    @PostMapping("/importUploadData")
    public Result<Map<String,Object>> importUploadData(@RequestParam("file") MultipartFile file, @CurrentUser LoginUser loginUser) {
        return Result.success(orderLoanReservoirService.importUploadData(file,loginUser));
    }

}
