package com.longhuan.approve.boot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.longhuan.approve.api.constants.PreFileTypeEnums;
import com.longhuan.approve.api.pojo.dto.UploadDesignateTypeDTO;
import com.longhuan.approve.api.pojo.vo.ZhongHengApiResult;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiAfterReplenishVerifyResponse;
import com.longhuan.approve.boot.feign.ResourceFeign;
import com.longhuan.approve.boot.fund.hengtong.HengTongService;
import com.longhuan.approve.boot.mapper.FinalFundInfoMapper;
import com.longhuan.approve.boot.pojo.entity.AfterLoanPatchesEntity;
import com.longhuan.approve.boot.pojo.entity.FinalFundInfoEntity;
import com.longhuan.approve.boot.pojo.entity.OrderInfoEntity;
import com.longhuan.approve.boot.pojo.entity.PatchesAuditConclusionEntity;
import com.longhuan.approve.boot.service.AfterLoanPatchesService;
import com.longhuan.approve.boot.mapper.AfterLoanPatchesMapper;
import com.longhuan.approve.boot.service.PatchesAuditConclusionService;
import com.longhuan.approve.boot.service.processing.YingFengTaskProcess;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.enums.AfterLoanPatchesEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.resource.pojo.dto.FundResourceDTO;
import com.longhuan.resource.pojo.dto.FundResourceResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【lh_after_loan_patches(贷后补件表)】的数据库操作Service实现
* @createDate 2025-09-03 10:52:47
*/
@Service
@RequiredArgsConstructor
@Slf4j
public class AfterLoanPatchesServiceImpl extends ServiceImpl<AfterLoanPatchesMapper, AfterLoanPatchesEntity>
implements AfterLoanPatchesService {
    private final AfterLoanPatchesMapper afterLoanPatchesMapper;
    private final PatchesAuditConclusionService patchesAuditConclusionService;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final ResourceFeign resourceFeign;
    private final YingFengTaskProcess yingFengTaskProcess;
    private final HengTongService hengTongService;
    @Override
    public void saveEntity(OrderInfoEntity orderInfoEntity,boolean flag,String errorMsg,String fundPatchCount) {
        AfterLoanPatchesEntity afterLoanPatchesEntity = afterLoanPatchesMapper.selectOne(new LambdaQueryWrapper<AfterLoanPatchesEntity>()
                .eq(AfterLoanPatchesEntity::getOrderId, orderInfoEntity.getId())
                .eq(AfterLoanPatchesEntity::getDeleteFlag, 0)
                .orderByDesc(AfterLoanPatchesEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isEmpty(afterLoanPatchesEntity)) {
            AfterLoanPatchesEntity entity = new AfterLoanPatchesEntity();
            entity.setOrderId(orderInfoEntity.getId());
            if (!flag){
                entity.setAfterLoanStatus(AfterLoanPatchesEnum.FUNDS_REJECT.getCode());
                entity.setFundPatchCount(1);
            }
            if (flag){
                entity.setAfterLoanStatus(AfterLoanPatchesEnum.FUNDS_APPROVED.getCode());
                entity.setFundPatchCount(0);
                afterLoanPatchesEntity.setFundPatchCount(1);
            }
            entity.setGpsState(orderInfoEntity.getGpsState());
            entity.setPayoffState(orderInfoEntity.getPlanState());
            log.info("AfterLoanPatchesServiceImpl.saveEntity null entity:{}",entity);
            afterLoanPatchesMapper.insert(entity);
            PatchesAuditConclusionEntity entity1 = new PatchesAuditConclusionEntity();
            entity1.setPatchesId(afterLoanPatchesEntity.getId());
            entity1.setOrderId(orderInfoEntity.getId());
            entity1.setProcessState(5);
            entity1.setReviewer(afterLoanPatchesEntity.getUpdateBy());
            log.info("AfterLoanPatchesServiceImpl.saveEntity null entity1:{}",entity1);
            patchesAuditConclusionService.save(entity1);
        }else {
            log.info("AfterLoanPatchesServiceImpl.saveEntity notnull afterLoanPatchesEntity:{}",afterLoanPatchesEntity);
            PatchesAuditConclusionEntity entity = new PatchesAuditConclusionEntity();
            try {
                afterLoanPatchesEntity.setFundPatchCount(Integer.parseInt(fundPatchCount));
            } catch (NumberFormatException e) {
                log.info("AfterLoanPatchesServiceImpl.saveEntity errTime:{} error:{}",fundPatchCount,e.getMessage());
            }
            if (!flag){
                afterLoanPatchesEntity.setAfterLoanStatus(AfterLoanPatchesEnum.FUNDS_REJECT.getCode());
                afterLoanPatchesEntity.setFundPatchCount(afterLoanPatchesEntity.getFundPatchCount()+1);
                entity.setProcessState(4);
            }
            if (flag){
                afterLoanPatchesEntity.setAfterLoanStatus(AfterLoanPatchesEnum.FUNDS_APPROVED.getCode());
                entity.setProcessState(5);
            }
            entity.setPatchesId(afterLoanPatchesEntity.getId());
            entity.setOrderId(orderInfoEntity.getId());
            entity.setAuditConclusion(errorMsg);
            entity.setReviewer(afterLoanPatchesEntity.getUpdateBy());
            log.info("AfterLoanPatchesServiceImpl.saveEntity notnull update afterLoanPatchesEntity:{}",afterLoanPatchesEntity);
            afterLoanPatchesMapper.updateById(afterLoanPatchesEntity);
            log.info("AfterLoanPatchesServiceImpl.saveEntity notnull entity:{}",afterLoanPatchesEntity);
            patchesAuditConclusionService.save(entity);
        }

    }

    @Override
    public void payAfterReplenish(OrderInfoEntity orderInfoEntity) {
        Integer orderId = orderInfoEntity.getId();
        FundEnum fundEnum = FundEnum.getFundEnum(orderInfoEntity.getFundId());
        Boolean flag = true;
        switch (fundEnum) {
            case FU_MIN:
                //上传富民补件
                // 抵押登记联上传路径 upload/afterLoan/yyyyMMdd/申请编号_VEHICLE_REGIST.pdf
                FinalFundInfoEntity fundInfoEntity = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                                .eq(FinalFundInfoEntity::getOrderId, orderId)
                                .eq(FinalFundInfoEntity::getFundId, FundEnum.FU_MIN.getValue())
                                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        , false
                );
                FundResourceDTO.SftpDTO sftpDTO = new FundResourceDTO.SftpDTO();
                String fileName = fundInfoEntity.getCreditReqNo() + "_" + "VEHICLE_REGIST.pdf";
                String destPath = "upload/afterLoan/" + DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN) + "/" + fileName;
                sftpDTO.setDestPath(destPath);

                FundResourceDTO fundResourceDTO = new FundResourceDTO()
                        .setLinkId(orderId)
                        .setType(4)
                        .setFund(FundEnum.FU_MIN)
                        .setSftpDTOList(List.of(sftpDTO));
                Result<List<FundResourceResultDTO>> listResult = resourceFeign.fundResourceUpload(fundResourceDTO);
                if (!Result.isSuccess(listResult)) {
                    flag = false;
                }
                break;
            case YING_FENG:
                //判断是否预加押
                List<String> preFileTypeCodeList = new ArrayList<>();
                preFileTypeCodeList.add(PreFileTypeEnums.PLEDGE_CERTIFICAT.getCode());
                UploadDesignateTypeDTO designateTypeDTO = new UploadDesignateTypeDTO();
                designateTypeDTO.setFundId(FundEnum.YING_FENG.getValue());
                designateTypeDTO.setPreId(orderInfoEntity.getPreId());
                designateTypeDTO.setType(2);
                designateTypeDTO.setPreFileTypeCodeList(preFileTypeCodeList);
                boolean yingFengFlag = yingFengTaskProcess.uploadDesignateByType(designateTypeDTO);
                if (!yingFengFlag){
                    flag = false;
                }
                break;
            case ZHONG_HENG_TONG_HUI:
            case ZHONG_HENG:
                ZhongHengApiResult<Void> result = hengTongService.hengTongJiaYaUpdate(orderId);
                if (!ZhongHengApiResult.isSuccess(result)) {
                    flag = false;
                }
                break;
        }
        this.saveEntity(orderInfoEntity,flag,"", null);
    }
}
