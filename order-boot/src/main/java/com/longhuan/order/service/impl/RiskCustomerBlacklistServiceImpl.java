package com.longhuan.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.feign.DigitalizeFeign;
import com.longhuan.order.feign.RiskFeign;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.dto.digitalize.AddBlacklistDTO;
import com.longhuan.order.pojo.dto.digitalize.DeleteBlacklistDTO;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.service.RiskCustomerBlacklistService;
import com.longhuan.order.util.EasyExcelUtil;
import com.longhuan.risk.pojo.vo.FhldDataVO;
import com.longhuan.user.pojo.vo.UserInfoVO;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 黑名单服务接口实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RiskCustomerBlacklistServiceImpl extends ServiceImpl<RiskCustomerBlacklistMapper, RiskCustomerBlacklistEntity> implements RiskCustomerBlacklistService {
    private final OrderCustomerInfoMapper orderCustomerInfoMapper;
    private final RiskCustomerBlacklistMapper riskCustomerBlacklistMapper;
    private final DigitalizeFeign digitalizeFeign;
    private final RiskCustomerBlacklistLogMapper riskCustomerBlacklistLogMapper;
    private final PreApprovalApplyInfoMapper preApprovalApplyInfoMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final RiskFeign riskFeign;
    private final UserFeign userFeign;
    /**
     * 查询黑名单列表
     *
     * @param dto
     * @return {@link List < RiskCustomerBlacklistEntity>}
     */
    @Override
    public List<RiskCustomerBlacklistEntity> queryBlacklist(RiskCustomerBlacklistQueryDTO dto) {
        LambdaQueryWrapper<RiskCustomerBlacklistEntity> lqw = new LambdaQueryWrapper<>();
        lqw.ge(ObjUtil.isNotNull(dto.getStartTime()), RiskCustomerBlacklistEntity::getCreateTime, dto.getStartTime());
        lqw.le(ObjUtil.isNotNull(dto.getEndTime()), RiskCustomerBlacklistEntity::getCreateTime, dto.getEndTime());
        lqw.eq(StrUtil.isNotBlank(dto.getIdNumber()), RiskCustomerBlacklistEntity::getIdNumber, dto.getIdNumber());
        lqw.like(StrUtil.isNotBlank(dto.getName()), RiskCustomerBlacklistEntity::getName, dto.getName());
        lqw.eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据客户id加入和名单
     *
     * @param customerId  客户id
     * @param blackReason 黑名单原因
     * @return {@link Boolean}
     */
    @Override
    public Boolean saveByCustomerId(Integer customerId, List<String> blackReason,String remarks) {
        OrderCustomerInfoEntity customerInfo = this.getByCustomerId(customerId);
        if (ObjUtil.isNull(customerInfo)) {
            return false;
        }
        LambdaUpdateWrapper<RiskCustomerBlacklistEntity> lqw = new LambdaUpdateWrapper<>();
        lqw.eq(RiskCustomerBlacklistEntity::getIdNumber, customerInfo.getIdNumber())
                .eq(RiskCustomerBlacklistEntity::getPhoneNumber, customerInfo.getPhone())
                .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0);
        //判断是否已存在黑名单中
        if (this.count(lqw) > 0) {
            return true;
        }
        String join = String.join(",", blackReason);
        RiskCustomerBlacklistEntity black = new RiskCustomerBlacklistEntity();
        black.setName(customerInfo.getName());
        black.setIdNumber(customerInfo.getIdNumber());
        black.setPhoneNumber(customerInfo.getPhone());
        black.setBlackReason(join);
        black.setRemarks(remarks);
        black.setInfos(2);
        black.setStatus(0);
        digitalizeFeign.addBlacklist(
                new AddBlacklistDTO()
                        .setCustomerName(customerInfo.getName())
                        .setMobile(customerInfo.getPhone())
                        .setIdNumber(customerInfo.getIdNumber())
                        .setBlackReason(remarks)
                        .setType(join)

        );
        return riskCustomerBlacklistMapper.insert(black)>0;
    }


    /**
     * 根据客户id删除黑名单
     *
     * @param customerId 客户id
     * @return {@link Boolean}
     */
    @Override
    public Boolean deleteByCustomerId(Integer customerId) {
        OrderCustomerInfoEntity customerInfo = this.getByCustomerId(customerId);
        if (ObjUtil.isNull(customerInfo)) {
            return false;
        }
        LambdaUpdateWrapper<RiskCustomerBlacklistEntity> luw = new LambdaUpdateWrapper<>();
        luw.set(RiskCustomerBlacklistEntity::getDeleteFlag, 1)
                .eq(RiskCustomerBlacklistEntity::getIdNumber, customerInfo.getIdNumber())
                .or()
                .eq(RiskCustomerBlacklistEntity::getPhoneNumber, customerInfo.getPhone());
        digitalizeFeign.deleteBlacklist(new DeleteBlacklistDTO()
                .setIdNumber(customerInfo.getIdNumber()));
        return this.update(luw);
    }

    /**
     * 是否在黑名单中
     *
     * @param name     名称
     * @param phone    电话
     * @param idNumber 身份证号
     * @return {@link boolean}
     */
    @Override
    public boolean isBlacklist(String name, String phone, String idNumber) {
        LambdaUpdateWrapper<RiskCustomerBlacklistEntity> lqw = new LambdaUpdateWrapper<>();
        lqw.and(qw -> qw.eq(RiskCustomerBlacklistEntity::getIdNumber, idNumber)
                        .or()
                        .eq(RiskCustomerBlacklistEntity::getPhoneNumber, phone)
                )
                .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0);
        return this.count(lqw) > 0;
    }

    /**
     * 根据客户id查询黑名单
     *
     * @param customerId 客户id
     * @return {@link boolean}
     */
    @Override
    public boolean isBlacklistByCustomerId(Integer customerId) {
        OrderCustomerInfoEntity customerInfo = this.getByCustomerId(customerId);
        if (ObjUtil.isNotNull(customerInfo)) {
            return this.isBlacklist(customerInfo.getName(), customerInfo.getPhone(), customerInfo.getIdNumber());
        }
        return false;
    }

    /**
     * 根据客户id获取客户信息
     *
     * @param customerId 客户id
     * @return {@link OrderCustomerInfoEntity}
     */
    private OrderCustomerInfoEntity getByCustomerId(Integer customerId) {
        LambdaQueryWrapper<OrderCustomerInfoEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(OrderCustomerInfoEntity::getId, customerId);
        lqw.eq(OrderCustomerInfoEntity::getDeleteFlag, 0);
        return orderCustomerInfoMapper.selectOne(lqw);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> addBlacklist(RiskCustomerBlacklistAddDTO riskCustomerBlacklistAddDTO) {
        //校验手机号和身份证是否存在
        List<RiskCustomerBlacklistEntity> phoneAndIdNumber = riskCustomerBlacklistMapper.selectList(Wrappers.<RiskCustomerBlacklistEntity>lambdaQuery()
                .eq(RiskCustomerBlacklistEntity::getPhoneNumber, riskCustomerBlacklistAddDTO.getPhoneNumber())
                .eq(RiskCustomerBlacklistEntity::getIdNumber, riskCustomerBlacklistAddDTO.getIdNumber())
                .eq(RiskCustomerBlacklistEntity::getStatus, 0)
                .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0)
        );
        RiskCustomerBlacklistEntity riskCustomerBlacklistEntity = new RiskCustomerBlacklistEntity();
        BeanUtil.copyProperties(riskCustomerBlacklistAddDTO, riskCustomerBlacklistEntity);
        String blackReason = String.join(",", riskCustomerBlacklistAddDTO.getBlackReason());
        riskCustomerBlacklistEntity.setBlackReason(blackReason);
        riskCustomerBlacklistEntity.setStatus(0);
        riskCustomerBlacklistEntity.setInfoSource(0);
        if (CollUtil.isNotEmpty(phoneAndIdNumber)){
            log.info("com.longhuan.order.service.impl.RiskCustomerBlacklistServiceImpl.addBlacklist :: 修改黑名单数据");
            riskCustomerBlacklistEntity.setId(phoneAndIdNumber.get(0).getId());
            if (riskCustomerBlacklistMapper.updateById(riskCustomerBlacklistEntity) > 0){
                insertLog(riskCustomerBlacklistEntity.getId(), 3, "修改", riskCustomerBlacklistEntity.getBlackReason(), riskCustomerBlacklistEntity.getRemarks());
            }
        }else {
            log.info("com.longhuan.order.service.impl.RiskCustomerBlacklistServiceImpl.addBlacklist :: 新增黑名单数据");
            if (riskCustomerBlacklistMapper.insert(riskCustomerBlacklistEntity) > 0){
                insertLog(riskCustomerBlacklistEntity.getId(), 1, "添加", riskCustomerBlacklistEntity.getBlackReason(), riskCustomerBlacklistEntity.getRemarks());
                digitalizeFeign.addBlacklist(
                        new AddBlacklistDTO()
                                .setCustomerName(riskCustomerBlacklistEntity.getName())
                                .setMobile(riskCustomerBlacklistEntity.getPhoneNumber())
                                .setIdNumber(riskCustomerBlacklistEntity.getIdNumber())
                                .setBlackReason(riskCustomerBlacklistEntity.getRemarks())
                                .setType(blackReason)
                );
            }
        }
        return Result.success(true);
    }

    @Override
    public Page<RiskCustomerBlacklistEntity> pageBlacklistlist(RiskCustomerBlacklistPageDTO dto) {
        MPJLambdaWrapper<RiskCustomerBlacklistEntity> queryWrapper = new MPJLambdaWrapper<RiskCustomerBlacklistEntity>()
                .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0)
                .like(StrUtil.isNotEmpty(dto.getName()),RiskCustomerBlacklistEntity::getName,dto.getName())
                .like(ObjUtil.isNotNull(dto.getIdNumber()),RiskCustomerBlacklistEntity::getIdNumber,dto.getIdNumber())
                .like(ObjUtil.isNotNull(dto.getPhoneNumber()),RiskCustomerBlacklistEntity::getPhoneNumber,dto.getPhoneNumber())
                .like(ObjUtil.isNotNull(dto.getBlackReason()),RiskCustomerBlacklistEntity::getPhoneNumber,dto.getBlackReason())
                .eq(ObjUtil.isNotNull(dto.getStatus()),RiskCustomerBlacklistEntity::getStatus,dto.getStatus())
                .eq(ObjUtil.isNotNull(dto.getInfoSource()),RiskCustomerBlacklistEntity::getInfoSource,dto.getInfoSource())
                .between(dto.getCreateStartDate() != null && dto.getCreateEndDate() != null,
                        RiskCustomerBlacklistEntity::getCreateTime,
                        dto.getCreateStartDate(), dto.getCreateEndDate())
                .between(dto.getReleaseStartDate() != null && dto.getReleaseEndDate() != null,
                        RiskCustomerBlacklistEntity::getReleaseTime,
                        dto.getReleaseStartDate(), dto.getReleaseEndDate()
                        )
                .orderByDesc(RiskCustomerBlacklistEntity::getCreateTime);

        log.info("查询参数：{}", queryWrapper);
        Page<RiskCustomerBlacklistEntity> pageList = riskCustomerBlacklistMapper.selectJoinPage(new Page<>(dto.getPageNum(), dto.getPageSize()),
                RiskCustomerBlacklistEntity.class,
                queryWrapper);
        return pageList;
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        response.addHeader("charset", "utf-8");
        String fileName = String.format("黑名单导出模板%s.xlsx", LocalDate.now());
        String encodeName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        Optional<MediaType> mediaTypeOptional = MediaTypeFactory.getMediaType(encodeName);
        MediaType mediaType = mediaTypeOptional.orElse(MediaType.APPLICATION_OCTET_STREAM);
        log.info("com.longhuan.order.service.impl.RiskCustomerBlacklistServiceImpl.downloadTemplate mediaType = {}", JSONUtil.toJsonStr(mediaType));
        response.setContentType(String.valueOf(mediaType));
        log.info("com.longhuan.order.service.impl.RiskCustomerBlacklistServiceImpl.downloadTemplate contentType = {}", response.getContentType());
        response.setHeader("Content-disposition", "attachment;filename=" + encodeName);
        try {
            EasyExcel.write(response.getOutputStream())
                    // 动态头
                    .head(RiskCustomerBlacklistImportDTO.class)
                    .registerWriteHandler(EasyExcelUtil.getHorizontalCellStyleStrategy())
                    .registerWriteHandler(new EasyExcelUtil.CustomCellWriteWidthConfig())
                    .sheet("黑名单导出模板")
                    // 表格数据
                    .doWrite(List.of());
        } catch (IOException e) {
            log.error("com.longhuan.order.service.impl.PostLoanSupplementReviewServiceImpl.moneyAfterPatchesExport error", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object importBlacklistlist(MultipartFile file) {
        RiskCustomerBlacklistExcelListener riskCustomerBlacklistExcelListener = new RiskCustomerBlacklistExcelListener(riskCustomerBlacklistMapper,riskCustomerBlacklistLogMapper);
        Map<String, Object> map = new HashMap<>();
        try {
            // 读取Excel文件
            EasyExcel.read(file.getInputStream(),
                            RiskCustomerBlacklistImportDTO.class,
                            riskCustomerBlacklistExcelListener)
                    .sheet()
                    .doRead();
            //返回重复和非重复数据条数
            List<RiskCustomerBlacklistEntity> repeat = riskCustomerBlacklistExcelListener.getRepeat();
            List<RiskCustomerBlacklistEntity> noRepeat = riskCustomerBlacklistExcelListener.getNoRepeat();

            if (CollUtil.isEmpty(repeat)) {
                map.put("noRepeat", noRepeat.size()+" 条无重复已成功添加");
            }else {
                map.put("repeat", repeat.size()+" 条数据和已有数据重复，新增内容已放在操作记录里");
                map.put("noRepeat", noRepeat.size()+" 条无重复已成功添加");
            }
        } catch (BusinessException e) {
            log.error("导入黑名单数据业务异常", e);
            throw e;
        } catch (Exception e) {
            log.error("导入黑名单数据系统异常", e);
            throw new BusinessException("导入失败：" + e.getMessage());
        }
        return map;
    }

    /**
     * 增加用户操作日志
     */
    private void insertLog(Integer blacklistId,Integer blacklistType,String blacklistContent,String blackReason,String remarks) {
        RiskCustomerBlacklistLogEntity riskCustomerBlacklistLog = new RiskCustomerBlacklistLogEntity();
        riskCustomerBlacklistLog.setBlacklistId(blacklistId);
        riskCustomerBlacklistLog.setBlacklistType(blacklistType);
        riskCustomerBlacklistLog.setBlacklistContent(blacklistContent);
        riskCustomerBlacklistLog.setBlackReason(blackReason);
        riskCustomerBlacklistLog.setRemarks(remarks);
        riskCustomerBlacklistLogMapper.insert(riskCustomerBlacklistLog);
        //更新操作人名称
        Integer updateBy = riskCustomerBlacklistLog.getUpdateBy();
        List<UserInfoVO> managerInfos = userFeign.searchUserNameBatch(Arrays.asList(updateBy)).getData();
        riskCustomerBlacklistLogMapper.update(Wrappers.<RiskCustomerBlacklistLogEntity>lambdaUpdate()
                .set(RiskCustomerBlacklistLogEntity::getModifierName, managerInfos.get(0).getName())
                .eq(RiskCustomerBlacklistLogEntity::getId, riskCustomerBlacklistLog.getId())
        );
    }

    @Override
    public RiskCustomerBlacklistEntity details(RiskCustomerBlacklistAddDTO riskCustomerBlacklistAddDTO) {
        RiskCustomerBlacklistEntity riskCustomerBlacklistEntity = riskCustomerBlacklistMapper.selectOne(Wrappers.<RiskCustomerBlacklistEntity>lambdaQuery()
                .eq(RiskCustomerBlacklistEntity::getPhoneNumber, riskCustomerBlacklistAddDTO.getPhoneNumber())
                .eq(RiskCustomerBlacklistEntity::getIdNumber, riskCustomerBlacklistAddDTO.getIdNumber())
                .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0)
                .last("limit 1")
        );


        return riskCustomerBlacklistEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(RiskCustomerBlacklistAddDTO riskCustomerBlacklistAddDTO) {
        RiskCustomerBlacklistEntity riskCustomerBlacklistEntity = riskCustomerBlacklistMapper.selectOne(Wrappers.<RiskCustomerBlacklistEntity>lambdaQuery()
                .eq(RiskCustomerBlacklistEntity::getId,riskCustomerBlacklistAddDTO.getId())
                .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0)
                .last("limit 1")
        );
        if (riskCustomerBlacklistEntity != null){
            int update = riskCustomerBlacklistMapper.update(Wrappers.<RiskCustomerBlacklistEntity>lambdaUpdate()
                    .set(RiskCustomerBlacklistEntity::getStatus, riskCustomerBlacklistAddDTO.getStatus())
                    .set(RiskCustomerBlacklistEntity::getReleaseTime,LocalDateTime.now())
                    .set(RiskCustomerBlacklistEntity::getReleaseReason,riskCustomerBlacklistAddDTO.getRemarks())
                    .eq(RiskCustomerBlacklistEntity::getId, riskCustomerBlacklistAddDTO.getId())
                    .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0)
                    );
            if (update > 0){
                insertLog(riskCustomerBlacklistEntity.getId(), 3, "解除", riskCustomerBlacklistEntity.getBlackReason(), riskCustomerBlacklistEntity.getRemarks());
                RiskCustomerBlacklistEntity riskCustomerBlacklist = riskCustomerBlacklistMapper.selectById(riskCustomerBlacklistEntity.getId());
                List<UserInfoVO> managerInfos = userFeign.searchUserNameBatch(Arrays.asList(riskCustomerBlacklist.getUpdateBy())).getData();
//                List<UserStoreVO> userStoreVOS = userFeign.getUserStoreVoByDeptIds(storeIds).getData();
                riskCustomerBlacklistMapper.update(Wrappers.<RiskCustomerBlacklistEntity>lambdaUpdate()
                        .set(RiskCustomerBlacklistEntity::getReleaseName, managerInfos.get(0).getName())
                        .eq(RiskCustomerBlacklistEntity::getId, riskCustomerBlacklistAddDTO.getId())
                        .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0));
                //发送数字化系统
                digitalizeFeign.deleteBlacklist(new DeleteBlacklistDTO()
                        .setIdNumber(riskCustomerBlacklistEntity.getIdNumber()));
            }
        }
        return true;
    }

    @Override
    public Page<RiskCustomerBlacklistLogEntity> pageBlacklistlistLog(RiskCustomerBlacklistAddDTO dto) {
        Page<RiskCustomerBlacklistLogEntity> pageList = riskCustomerBlacklistLogMapper.selectJoinPage(new Page<>(dto.getPageNum(), dto.getPageSize()),
                RiskCustomerBlacklistLogEntity.class,
                new MPJLambdaWrapper<RiskCustomerBlacklistLogEntity>()
                        .eq(RiskCustomerBlacklistLogEntity::getBlacklistId,dto.getId())
                        .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0));
        return pageList;
    }
    //检测是否有重复的数据
    @Override
    public Object importest(MultipartFile file, Integer type) {
        Result<FhldDataVO> detailByOrderId = riskFeign.getDetailByOrderId("340602199106230416");
        RiskCustomerBlacklistlListener riskCustomerBlacklistlListener = new RiskCustomerBlacklistlListener(riskCustomerBlacklistMapper);
        //重复的数据
        List<RiskCustomerBlacklistEntity> repeatRiskCustomerBlacklist = new ArrayList<>();
        //非重复数据
        List<RiskCustomerBlacklistEntity> riskCustomerBlack = new ArrayList<>();
        try {
            //读取数据
            EasyExcel.read(file.getInputStream(),RiskCustomerBlacklistImportDTO.class,riskCustomerBlacklistlListener)
                    .sheet()
                    .doRead();
            List<RiskCustomerBlacklistImportDTO> importExcel = riskCustomerBlacklistlListener.getAllDataList();
            log.info("com.longhuan.order.service.impl.RiskCustomerBlacklistServiceImpl.importest importExcel = {} 条", importExcel.size());
            //查询是否有重复的数据
            importExcel.forEach(item -> {
                RiskCustomerBlacklistEntity riskCustomerBlacklistEntity = riskCustomerBlacklistMapper.selectOne(new LambdaQueryWrapper<RiskCustomerBlacklistEntity>()
                        .eq(RiskCustomerBlacklistEntity::getIdNumber, item.getIdNumber())
                        .eq(RiskCustomerBlacklistEntity::getPhoneNumber, item.getPhoneNumber())
                        .eq(RiskCustomerBlacklistEntity::getStatus, 0)
                        .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0)
                        .last("limit 1")
                );
                //重复数据添加到集合中
                if (riskCustomerBlacklistEntity != null){
                    repeatRiskCustomerBlacklist.add(riskCustomerBlacklistEntity);
                }
                if (riskCustomerBlacklistEntity == null){
                    RiskCustomerBlacklistEntity riskCustomer = new RiskCustomerBlacklistEntity();
                    BeanUtil.copyProperties(item, riskCustomer);
                    riskCustomerBlack.add(riskCustomer);
                }
            });
            if (ObjUtil.equals(type,1)){
                //返回重复数据
                return repeatRiskCustomerBlacklist;
            }else {
                //添加数据
                repeatRiskCustomerBlacklist.forEach(item -> {
                    item.setUpdateTime(null);
                    item.setUpdateBy(null);
                    riskCustomerBlacklistMapper.updateById(item);
                });
                repeatRiskCustomerBlacklist.forEach(item -> {
                    RiskCustomerBlacklistEntity riskCustomer = new RiskCustomerBlacklistEntity();
                    BeanUtil.copyProperties(item, riskCustomer);
                    riskCustomerBlacklistMapper.insert(riskCustomer);
                });
            }
        } catch (BusinessException e) {
            log.error("导入黑名单数据业务异常", e);
            throw e;
        } catch (Exception e) {
            log.error("导入黑名单数据系统异常", e);
            throw new BusinessException("导入失败：" + e.getMessage());
        }
        return true;
    }

    @Override
    public Boolean timingBlacklist() {
        //1、实还款期数≤5期，且逾期=91天，入黑原因为：逾期欺诈
        List<OrderCustomerInfoEntity> orderCustomerInfoEntities = orderInfoMapper.selectJoinList(OrderCustomerInfoEntity.class, new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderCustomerInfoEntity::getIdNumber,OrderCustomerInfoEntity::getPhone)
                .leftJoin(FundRepaymentInfoEntity.class, FundRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                .leftJoin(OrderCustomerInfoEntity.class, OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId)
                .eq(FundRepaymentInfoEntity::getRepaymentStatus, 2)
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                .le(FundRepaymentInfoEntity::getTerm, 5)
                .eq(FundRepaymentInfoEntity::getRepaymentDate, LocalDate.now().minusDays(91))
        );
        Set<OrderCustomerInfoEntity> orderCustomerSet = new HashSet<>(orderCustomerInfoEntities);
        //入库
        insertBlacklist(orderCustomerSet, "逾期欺诈");
        //2、实还款期数＞5期，且逾期=181天，入黑原因为：严重逾期
        List<OrderCustomerInfoEntity> orderCustomerInfo = orderInfoMapper.selectJoinList(OrderCustomerInfoEntity.class, new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderCustomerInfoEntity::getIdNumber,OrderCustomerInfoEntity::getPhone)
                .leftJoin(FundRepaymentInfoEntity.class, FundRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                .leftJoin(OrderCustomerInfoEntity.class, OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId)
                .eq(FundRepaymentInfoEntity::getRepaymentStatus, 2)
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                .gt(FundRepaymentInfoEntity::getTerm, 5)
                .eq(FundRepaymentInfoEntity::getRepaymentDate, LocalDateTime.now().minusDays(181))
        );
        Set<OrderCustomerInfoEntity> orderCustomer = new HashSet<>(orderCustomerInfo);
        insertBlacklist(orderCustomer, "严重逾期");
        return true;
    }


    public void insertBlacklist(Set<OrderCustomerInfoEntity> orderCustomerSet,String blackReason) {
        orderCustomerSet.forEach(item -> {
            RiskCustomerBlacklistEntity riskCustomerBlacklist = riskCustomerBlacklistMapper.selectOne(Wrappers.<RiskCustomerBlacklistEntity>lambdaQuery()
                    .eq(RiskCustomerBlacklistEntity::getIdNumber, item.getIdNumber())
                    .eq(RiskCustomerBlacklistEntity::getPhoneNumber, item.getPhone())
                    .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0)
                    .last("limit 1")
            );
            if (riskCustomerBlacklist==null){
                RiskCustomerBlacklistEntity riskCustomerBlacklistEntity = new RiskCustomerBlacklistEntity();
                Result<FhldDataVO> detailByOrderId = riskFeign.getDetailByOrderId(item.getIdNumber());
                FhldDataVO data = detailByOrderId.getData();
                boolean police = data.getPoliceStatusDesc().contains("涉毒") || data.getPoliceStatusDesc().contains("吸毒");
                if (police){
                    riskCustomerBlacklistEntity.setBlackReason(blackReason+",涉毒");
                }else {
                    riskCustomerBlacklistEntity.setBlackReason(blackReason);
                }
                riskCustomerBlacklistEntity.setBlackReason("逾期");
                riskCustomerBlacklistEntity.setName(item.getName());
                riskCustomerBlacklistEntity.setRemarks(blackReason);


                riskCustomerBlacklistEntity.setIdNumber(item.getIdNumber());
                riskCustomerBlacklistEntity.setPhoneNumber(item.getPhone());
                riskCustomerBlacklistEntity.setStatus(0);
                riskCustomerBlacklistEntity.setDeleteFlag(0);
                riskCustomerBlacklistEntity.setInfoSource(0);
                if (riskCustomerBlacklistMapper.insert(riskCustomerBlacklistEntity) > 0){
                    insertLog(riskCustomerBlacklistEntity.getId(), 1, "添加", riskCustomerBlacklistEntity.getBlackReason(), riskCustomerBlacklistEntity.getRemarks());
                    digitalizeFeign.addBlacklist(
                            new AddBlacklistDTO()
                                    .setCustomerName(riskCustomerBlacklistEntity.getName())
                                    .setMobile(riskCustomerBlacklistEntity.getPhoneNumber())
                                    .setIdNumber(riskCustomerBlacklistEntity.getIdNumber())
                                    .setBlackReason(riskCustomerBlacklistEntity.getRemarks())
                                    .setType("逾期")
                    );
                }
            }else {
                //如果数据存在的话直接增加一条操作记录
                insertLog(riskCustomerBlacklist.getId(), 2, "修改", blackReason, riskCustomerBlacklist.getRemarks());
            }
        });
    }


    @Override
    public Boolean updateBlacklist() {
        List<RiskCustomerBlacklistEntity> riskCustomerBlacklistEntity = riskCustomerBlacklistMapper.selectList(Wrappers.<RiskCustomerBlacklistEntity>lambdaQuery()
                .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isNotEmpty(riskCustomerBlacklistEntity)){
            riskCustomerBlacklistEntity.forEach(item -> {
                String blackReason = item.getBlackReason();
                Set<String> reason = new HashSet<>();
                boolean aType = blackReason.contains("虚假") || blackReason.contains("内批") || blackReason.contains("内匹");
                boolean bType = blackReason.contains("逾期");
                boolean cType = blackReason.contains("涉毒");
                if (aType){
                    reason.add("虚假");
                }
                if (bType) {
                    reason.add("逾期");
                }
                if (cType) {
                    reason.add("涉毒");
                }
                if (CollUtil.isEmpty(reason)){
                    reason.add("其他");
                }
                String join = String.join(",", reason);
                RiskCustomerBlacklistEntity riskCustomerBlacklist = new RiskCustomerBlacklistEntity();
                riskCustomerBlacklist.setBlackReason(join);
                riskCustomerBlacklist.setRemarks(blackReason);
                riskCustomerBlacklistMapper.update(riskCustomerBlacklist, Wrappers.<RiskCustomerBlacklistEntity>lambdaQuery()
                        .eq(RiskCustomerBlacklistEntity::getId, item.getId())
                        .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0)
                );
            });
        }
        return true;
    }

    @Override
    public Boolean policeBad(PoliceBadDTO policeBadDTO) {
        Integer score = policeBadDTO.getScore();
        if (score > 64 || score < 0) {
            return false;
        }
        String poison = null;
        String binaryString = String.format("%6s", Integer.toBinaryString(score)).replace(" ", "0");
        log.debug("binaryString {}", binaryString);
        if (binaryString.length() != 6) {
            return false;
        }
        if (binaryString.charAt(2) == '1') {
            poison= "涉毒";
        }
        if (binaryString.charAt(3) == '1') {
            poison= "涉毒";
        }
//String str = "涉毒,吸毒,逾期";
        //如果包含3或者包含4则证明涉毒数据库添加涉毒
//        if (policeBadDTO != null&&(ObjUtil.equals(score, 3)||ObjUtil.equals(score, 4))){
        if (binaryString.charAt(2) == '1'||binaryString.charAt(3) == '1'){
            PreApprovalApplyInfoEntity preApprovalApplyInfo = preApprovalApplyInfoMapper.selectOne(Wrappers.<PreApprovalApplyInfoEntity>lambdaQuery()
                    .eq(PreApprovalApplyInfoEntity::getName, policeBadDTO.getIdName())
                    .eq(PreApprovalApplyInfoEntity::getIdNumber, policeBadDTO.getIdNumber())
                    .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
                    .last("limit 1")
            );
            if (preApprovalApplyInfo != null) {
                List<RiskCustomerBlacklistEntity> riskCustomerBlacklistEntities = riskCustomerBlacklistMapper.selectList(Wrappers.<RiskCustomerBlacklistEntity>lambdaQuery()
                        .eq(RiskCustomerBlacklistEntity::getIdNumber, preApprovalApplyInfo.getIdNumber())
                        .eq(RiskCustomerBlacklistEntity::getPhoneNumber, preApprovalApplyInfo.getPhone())
                        .eq(RiskCustomerBlacklistEntity::getDeleteFlag, 0)
                );
                RiskCustomerBlacklistEntity riskCustomerBlacklistEntity = new RiskCustomerBlacklistEntity();
                if (CollUtil.isEmpty(riskCustomerBlacklistEntities)){
                    riskCustomerBlacklistEntity.setIdNumber(preApprovalApplyInfo.getIdNumber());
                    riskCustomerBlacklistEntity.setPhoneNumber(preApprovalApplyInfo.getPhone());
                    riskCustomerBlacklistEntity.setStatus(0);
                    riskCustomerBlacklistEntity.setDeleteFlag(0);
                    riskCustomerBlacklistEntity.setBlackReason("涉毒");
                    riskCustomerBlacklistEntity.setRemarks("涉毒");
                    riskCustomerBlacklistEntity.setInfoSource(0);
                    riskCustomerBlacklistMapper.insert(riskCustomerBlacklistEntity);
                }else {
                    RiskCustomerBlacklistEntity riskCustomer = riskCustomerBlacklistEntities.get(0);
                    riskCustomer.setBlackReason("涉毒");
                    riskCustomer.setRemarks("涉毒");
                    riskCustomerBlacklistMapper.updateById(riskCustomer);
                }

            }
        }
        return false;
    }

    @Override
    public Boolean historyAddBlacklis() {
        //1、实还款期数≤5期，且逾期>=91天，入黑原因为：逾期，备注：逾期欺诈
        List<OrderCustomerInfoEntity> orderCustomerInfoEntities = orderInfoMapper.selectJoinList(OrderCustomerInfoEntity.class, new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderCustomerInfoEntity::getIdNumber,OrderCustomerInfoEntity::getPhone,OrderCustomerInfoEntity::getName)
                .leftJoin(FundRepaymentInfoEntity.class, FundRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                .leftJoin(OrderCustomerInfoEntity.class, OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId)
                .eq(FundRepaymentInfoEntity::getRepaymentStatus, 2)
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                .le(FundRepaymentInfoEntity::getTerm, 5)
                .ge(FundRepaymentInfoEntity::getRepaymentDate, LocalDate.now().minusDays(91))
        );
        Set<OrderCustomerInfoEntity> orderCustomerSet = new HashSet<>(orderCustomerInfoEntities);
        log.info("com.longhuan.order.service.impl.RiskCustomerBlacklistServiceImpl.historyAddBlacklis orderCustomerSet ：{} 条", orderCustomerSet.size());
        //入库
        insertBlacklist(orderCustomerSet, "逾期欺诈");
        //2、实还款期数＞5期，且逾期>=181天，入黑原因为：逾期，备注：严重逾期
        List<OrderCustomerInfoEntity> orderCustomerInfo = orderInfoMapper.selectJoinList(OrderCustomerInfoEntity.class, new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderCustomerInfoEntity::getIdNumber,OrderCustomerInfoEntity::getPhone)
                .leftJoin(FundRepaymentInfoEntity.class, FundRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                .leftJoin(OrderCustomerInfoEntity.class, OrderCustomerInfoEntity::getId, OrderInfoEntity::getCustomerId)
                .eq(FundRepaymentInfoEntity::getRepaymentStatus, 2)
                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                .gt(FundRepaymentInfoEntity::getTerm, 5)
                .ge(FundRepaymentInfoEntity::getRepaymentDate, LocalDateTime.now().minusDays(181))
        );
        Set<OrderCustomerInfoEntity> orderCustomer = new HashSet<>(orderCustomerInfo);
        log.info("com.longhuan.order.service.impl.RiskCustomerBlacklistServiceImpl.historyAddBlacklis orderCustomer ：{} 条", orderCustomer.size());

        insertBlacklist(orderCustomer, "严重逾期");
//        Result<FhldDataVO> detailByOrderId = riskFeign.getDetailByOrderId("522530199208180048");
//        System.err.println(detailByOrderId);
        return true;
    }
}
