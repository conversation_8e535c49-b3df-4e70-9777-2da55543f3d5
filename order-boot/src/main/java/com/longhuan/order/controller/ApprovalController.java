package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.DigitizeProductDTO;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.dto.digitalize.DigitizeOrderIdDTO;
import com.longhuan.order.pojo.dto.digitalize.PushPreInfoToDigitizeDTO;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.pojo.vo.digitalize.DigitProductAndCapitalListVO;
import com.longhuan.order.pojo.vo.digitalize.DigitizeOrderIdVO;
import com.longhuan.order.service.ApprovalService;
import com.longhuan.order.service.DigitalizeService;
import com.longhuan.order.service.RiskService;
import com.longhuan.user.pojo.vo.UserDetailInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 审批接口
 *
 * <AUTHOR>
 * @date 2024/08/12
 */
@Api(tags = "业务提交接口")
@RestController
@RequestMapping("/api/v1/approval")
@RequiredArgsConstructor
public class ApprovalController {

    private final ApprovalService approvalService;
    private final DigitalizeService digitalizeService;

    private final RedisService redisService;
    private final RiskService riskService;

    /**
     * 预审批
     *
     * @param submitDTO
     * @param currentUser
     * @return
     */
    @PostMapping("/pre/submit")
    @ApiOperation(value = "预审批审批结论")
    Result<SubmitResultVO> preApprovalSubmit(@RequestBody ApprovalSubmitDTO submitDTO, @CurrentUser LoginUser currentUser) {
        return Result.success(approvalService.approvalSubmit(submitDTO, currentUser));
    }

    /**
     * 审批结论提交
     *
     * @param submitDTO
     * @param currentUser
     * @return
     */
    @PostMapping("/submit")
    @ApiOperation(value = "审批结论提交")
    Result<SubmitResultVO> approvalSubmit(@RequestBody ApprovalSubmitDTO submitDTO, @CurrentUser LoginUser currentUser) {
        return Result.success(approvalService.approvalSubmit(submitDTO, currentUser));
    }

    /**
     * 批准 提交 验证
     *
     * @param submitDTO 提交 DTO
     * @return {@link Result }<{@link SubmitValidateVO }>
     */
    @PostMapping("/submit_validate")
    @ApiOperation(value = "审批结论提交校验")
    Result<SubmitValidateVO> approvalSubmitValidate(@RequestBody ApprovalSubmitValidateDTO submitDTO) {
        return Result.success(approvalService.approvalSubmitValidate(submitDTO));
    }

    /**
     * 订单补录状态
     *
     * @param orderRecordDTO
     * @return
     */
    @PostMapping("/order/page/status")
    @ApiOperation(value = "订单页签状态")
    Result<List<OrderPageVO>> orderStatus(@RequestBody OrderRecordDTO orderRecordDTO) {
        return Result.success(approvalService.orderPageStatus(orderRecordDTO));
    }

    @PostMapping("/order/page/updateStatus")
    @ApiOperation(value = "订单页签修改状态")
    Result<Boolean> updateStatus(@RequestParam Integer orderId, @RequestParam Integer pageId, @RequestParam Integer status) {
        return Result.success(approvalService.updateStatus(orderId, pageId, status));
    }

    /**
     * 订单提交
     *
     * @param submitDTO
     * @param currentUser
     * @return
     */
    @PostMapping("/order/submit")
    @ApiOperation(value = "订单提交")
    Result<SubmitResultVO> orderSubmit(@RequestBody OrderSubmitDTO submitDTO, @CurrentUser LoginUser currentUser) {
        return Result.success(approvalService.orderSubmit(submitDTO, currentUser));
    }

    /**
     * 订单取消
     *
     * @param submitDTO
     * @param currentUser
     * @return
     */
    @PostMapping("/order/cancel")
    @ApiOperation(value = "订单取消")
    Result<SubmitResultVO> orderCancel(@RequestBody OrderSubmitDTO submitDTO, @CurrentUser LoginUser currentUser) {

        Integer orderId = submitDTO.getOrderId();
        Boolean tryLock = redisService.tryLock("order:cancel:order_" + orderId, "order_" + orderId, 120);
        if (!tryLock) {
            return Result.failed("订单取消中，请等待");
        }

        try {
            SubmitResultVO data = approvalService.orderCancel(submitDTO, currentUser);
            return Result.success(data);
        } finally {
            redisService.releaseLock("order:cancel:order_" + orderId, "order_" + orderId);
        }
    }

    /**
     * 订单状态
     *
     * @param orderIdDTO 订单 ID DTO
     * @return {@link Result }<{@link OrderApprovalStatusVO }>
     */
    @PostMapping("/order/status")
    @ApiOperation(value = "订单状态")
    Result<OrderApprovalStatusVO> orderStatus(@RequestBody OrderIdDTO orderIdDTO) {
        return Result.success(approvalService.orderStatus(orderIdDTO));
    }

    /**
     * 审批历史
     *
     * @param historyDTO
     * @return
     */
//    @PostMapping("/history")
//    @ApiOperation(value = "审批历史")
//    Result<Page<ApprovalHistoryVO>> approvalHistory(@RequestBody OrderRecordDTO historyDTO,
//                                                    @CurrentUser LoginUser currentUser) {
//        return Result.success(approvalService.approvalHistory(historyDTO, currentUser));
//
//    }
    @PostMapping("/history")
    @ApiOperation(value = "审批历史")
    Result<Page<ApprovalHistoryVO>> approvalHistory(@RequestBody ApprovalHistoryQueryDTO historyDTO,
                                                    @CurrentUser LoginUser currentUser) {
        return Result.success(approvalService.approvalHistoryCombined(historyDTO, currentUser));
    }

    /**
     * 审批历史（支持预审和订单合并查询）
     *
     * @param queryDTO 查询参数
     * @param currentUser 当前用户
     * @return 审批历史分页结果
     */
    @PostMapping("/history/combined")
    @ApiOperation(value = "审批历史（支持预审和订单合并查询）")
    Result<Page<ApprovalHistoryVO>> approvalHistoryCombined(@RequestBody ApprovalHistoryQueryDTO queryDTO,
                                                           @CurrentUser LoginUser currentUser) {
        return Result.success(approvalService.approvalHistoryCombined(queryDTO, currentUser));
    }

    @PostMapping("/order/engine/risk")
    @ApiOperation(value = "单笔进件风控规则")
    Result<String> riskEngineOnOrder(@RequestParam("orderId") Integer orderId) {
        return Result.success(riskService.riskEngineOnOrder(orderId));
    }

    @PostMapping("/order/launch/risk")
    @ApiOperation(value = "单笔进件风控数据")
    Result<String> riskLaunchOnOrder(@RequestParam("orderId") Integer orderId) {
        return Result.success(riskService.riskLaunchOnOrder(orderId));
    }

    @PostMapping("/order/reject/batch")
    @ApiOperation(value = "进件批量拒绝")
    Result<String> orderAutoRejectBatch() {
        return Result.success(riskService.orderAutoRejectBatch());
    }


    @GetMapping("/fund/final/result/batch")
    @ApiOperation(value = "资方终审审批结果审批流转")
    Result<String> fundFinalResultBatch() {
        return Result.success(approvalService.batchUpdateFundsFinalResult());
    }


    @GetMapping("/getApprovalDetails/{preId}")
    @ApiOperation(value = "获取预审信息详情")
    Result<GetApprovalDetailsVO> getApprovalDetails(@PathVariable("preId") Integer preId) {//获取审批详情
        return Result.success(digitalizeService.getApprovalDetails(preId));
    }

    @PostMapping("/to/digitize")
    @ApiOperation(value = "推送数字化信息")
    Result<Boolean> pushPreInfoToDigitize(@RequestBody PushPreInfoToDigitizeDTO pushPreInfoToDigitizeDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(digitalizeService.pushPreInfoToDigitize(pushPreInfoToDigitizeDTO, loginUser));
    }

    @PostMapping("/digitize/product")
    @ApiOperation(value = "获取数字化产品信息")
    Result<DigitProductAndCapitalListVO> getDigitizeProduct(@RequestBody DigitizeProductDTO digitizeProductDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(digitalizeService.digitizeProduct(digitizeProductDTO, loginUser));
    }

    @PostMapping("/digitize/getTokenAndDigitizeOrderId")
    @ApiOperation(value = "获取token和数字化订单号")
    Result<DigitizeOrderIdVO> getTokenAndDigitizeOrderId(@RequestBody @Nullable DigitizeOrderIdDTO digitizeOrderIdDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(digitalizeService.getTokenAndDigitizeOrderId(digitizeOrderIdDTO, loginUser));
    }

    @PostMapping("/to/auth")
    @ApiOperation(value = "用户授权签约")
    ResponseEntity<byte[]> userToAuth(@RequestParam Integer preId, @CurrentUser LoginUser loginUser) {
        return approvalService.userToAuth(preId, loginUser);
    }

    @GetMapping("/manager/info")
    @ApiOperation(value = "客户经理信息")
    Result<UserDetailInfoVO> getManagerInfo(@CurrentUser() LoginUser loginUser) {
        return Result.success(approvalService.getManagerInfo(loginUser));
    }
    @ApiOperation(value = "数字化终止订单")
    @GetMapping("/digitize/stopCredit/{preId}")
    Result<Boolean> stopCredit(@PathVariable("preId") Integer preId){
        return Result.success(digitalizeService.stopCredit(preId));
    };
}
