package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.service.RepurchaseProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "赎回还款计划")
@RestController
@RequestMapping("/api/v1/repurchaseRepayment")
@RequiredArgsConstructor
public class RepurchaseRepaymentController {

    private final RepurchaseProcessService repurchaseProcessService;

    /**
     * 更新赎回还款计划
     */
    @ApiOperation(value = "更新赎回还款计划")
    @GetMapping("/updateRepurchaseRepayment")
    public Result<Boolean> updateRepurchaseRepayment() {
        repurchaseProcessService.updateRepaymentPlanPenaltyInterest();
        return Result.success();
    }


}
