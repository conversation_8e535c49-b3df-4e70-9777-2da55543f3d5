package com.longhuan.order.statemachine;

import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.state.State;
import org.springframework.statemachine.support.StateMachineInterceptor;
import org.springframework.statemachine.transition.Transition;
import org.springframework.statemachine.transition.TransitionKind;
import org.springframework.statemachine.trigger.Trigger;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.function.Function;

@Slf4j
@Component
public class OrderStateMachineInterceptor implements StateMachineInterceptor<States, Events> {

    @Override
    public Message<Events> preEvent(Message<Events> message, StateMachine<States, Events> stateMachine) {
        log.info("preEvent message:{}", message);
        return message;
    }

    @Override
    public void preStateChange(State<States, Events> state, Message<Events> message, Transition<States, Events> transition, StateMachine<States, Events> stateMachine, StateMachine<States, Events> rootStateMachine) {
        log.info("preStateChange state:{} ,message:{},transition:{}", state.getId(), message.getHeaders(), transition.getName());
    }

    @Override
    public void postStateChange(State<States, Events> state, Message<Events> message, Transition<States, Events> transition, StateMachine<States, Events> stateMachine, StateMachine<States, Events> rootStateMachine) {

       try{
           if (state != null) {
               States currentStates = state.getId();
               Events payload = message.getPayload();

               MessageHeaders headers = message.getHeaders();

               Integer orderId = headers.get(StateConstants.ORDER_ID, Integer.class);
               State<States, Events> source = transition.getSource();
               States sourceStates = source.getId();

               Trigger<States, Events> trigger = transition.getTrigger();
               Events event = null;
               if (trigger != null) {
                   event = trigger.getEvent();
               }
               TransitionKind kind = transition.getKind();

               log.info("postStateChange orderId:{} ,payload:{} ,message:{}", orderId, payload, headers);

               log.info("postStateChange orderId:{}, currentStates:{}, " +
                       "sourceStates:{} ,event:{},kind:{}", orderId, currentStates, sourceStates, event, kind);


           }
       }catch (Exception e){
           log.error("postStateChange error:{}", e.getMessage(), e);
       }
    }

    @Override
    public StateContext<States, Events> preTransition(StateContext<States, Events> stateContext) {
        log.info("preTransition stateContext:{}", stateContext.getMessage());
        return stateContext;
    }

    @Override
    public StateContext<States, Events> postTransition(StateContext<States, Events> stateContext) {
        log.info("postTransition stateContext:{}", stateContext.getMessage());
        return stateContext;
    }

    @Override
    public Exception stateMachineError(StateMachine<States, Events> stateMachine, Exception exception) {
        log.error("postTransition exception:{}", exception.getMessage(), exception);
        return exception;
    }
}
