package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.ReviewResultDTO;
import com.longhuan.order.pojo.dto.ConfirmDTO;
import com.longhuan.order.pojo.dto.OrderReviewDTO;
import com.longhuan.order.pojo.dto.UpdateReviewInfoDTO;
import com.longhuan.order.pojo.vo.OrderReviewVO;
import com.longhuan.order.service.ReviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * @program: new-longhuan-order
 * @description: 面签信息
 * @author: zangxx
 * @create: 2024-08-15 14:10
 **/
@Api(tags = "面签信息")
@RestController
@RequestMapping("/api/v1/review")
@RequiredArgsConstructor
public class ReviewController {
    private final ReviewService reviewService;

    @ApiOperation(value = "面签信息")
    @PostMapping("/info")
    Result<OrderReviewVO> getReviewInfo(@RequestBody @Validated OrderReviewDTO orderReviewDTO) {
        return Result.success(reviewService.getReviewInfo(orderReviewDTO));
    }

    @ApiOperation(value = "获取面签视频文件")
    @GetMapping("/queryReviewVideoUidByOrderId")
    Result<String> queryReviewVideoUidByOrderId(@RequestParam("orderId") Integer orderId){
        return Result.success(reviewService.getReviewVideoUidByOrderId(orderId));
    }

    @ApiOperation(value = "保存面签信息")
    @PostMapping("/insert")
    Result<Boolean> addReviewInfo(@RequestBody ReviewResultDTO reviewResultDTO) {
        return Result.success(reviewService.saveReviewInfo(reviewResultDTO));
    }

    @ApiOperation(value = "更新面签信息")
    @PostMapping("/updateReviewInfo")
    Result<Boolean> updateReviewInfo(@RequestBody UpdateReviewInfoDTO updateReviewInfoDTO) {
        return Result.success(reviewService.updateReviewInfo(updateReviewInfoDTO));
    }

    @ApiOperation(value = "补件确认")
    @PostMapping("/confirm")
    Result<Boolean> confirm(@RequestBody ConfirmDTO confirmDTO) {
        return Result.success(reviewService.confirm(confirmDTO));
    }
    @ApiOperation(value = "面签未处理通知")
    @PostMapping("/notice")
    Result<Boolean> notice() {
        return Result.success(reviewService.notice());
    }

}
