package com.longhuan.data.pojo.vo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class OrderBizPaymentVO {
    @ExcelIgnore
    private Integer orderId;
    @ExcelIgnore
    private Integer fundId;
    @ExcelIgnore
    private Integer teamId;

    /**
     * 订单编号
     */
    @ExcelProperty(value = "订单编号", index = 0)
    @ColumnWidth(24)
    private String orderNumber;

    /**
     * 申请时间
     */
    @ExcelProperty(value = "申请时间", index = 1)
    @ColumnWidth(20)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime submitTime;

    @ExcelProperty(value = "资方终审通过时间", index = 2)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @ColumnWidth(20)
    private LocalDateTime fundApproveTime;


    /**
     * 放款时间
     */
    @ExcelProperty(value = "放款时间", index = 3)
    @ColumnWidth(20)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime paymentTime;
    /**
     * 产品
     */
    @ExcelProperty(value = "产品名称", index = 4)
    @ColumnWidth(20)
    private String productName;

    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户姓名", index = 5)
    @ColumnWidth(20)
    private String customerName;   /**
     * 身份证
     */
    @ExcelProperty(value = "身份证", index = 6)
    @ColumnWidth(24)
    private String idNumber;


    /**
     * 资方授信金额
     */
    @ExcelProperty(value = "合同金额", index = 7)
    @ColumnWidth(20)
    private BigDecimal customerConfirmAmount;

    @ExcelProperty(value = "还款期数", index = 8)
    @ColumnWidth(20)
    private Integer term;

    /**
     * 计息方式
     */
    @ExcelProperty(value = "计息方式", index = 9)
    @ColumnWidth(20)
    private String interestCal = "等额本息";


    @ExcelProperty(value = "车牌号", index = 10)
    @ColumnWidth(20)
    private String vehicleNumber;
    /**
     * 车架号
     */
    @ExcelProperty(value = "车架号", index = 11)
    @ColumnWidth(24)
    private String vin;
    /**
     * 车架号
     */
    @ExcelProperty(value = "是否为新能源", index = 12)
    @ColumnWidth(24)
    private String isGreenDescription;

    @ExcelProperty(value = "所属大区", index = 13)
    @ColumnWidth(20)
    private String regionName;
    /**
     * 门店名称
     */
    @ExcelProperty(value = "业绩所属门店", index = 14)
    @ColumnWidth(20)
    private String storeName;

    @ExcelProperty(value = "所属门店", index = 15)
    @ColumnWidth(20)
    private String belongStoreName;

    @ExcelProperty(value = "所属团队", index = 16)
    @ColumnWidth(20)
    private String teamName;
    @ExcelIgnore
    private Integer managerId;


    @ExcelProperty(value = "业务员工号", index = 17)
    @ColumnWidth(20)
    private String managerJobNumber;

    /**
     * 所属业务 客户经理名称
     */
    @ExcelProperty(value = "所属业务员", index = 18)
    @ColumnWidth(20)
    private String managerName;

    @ExcelProperty(value = "状态", index = 19)
    @ColumnWidth(20)
    private String currentStatus;

    @ExcelProperty(value = "加押/过户公司", index = 20)
    @ColumnWidth(20)
    private String mortgageChannelName;
    /**
     * 所属资方
     */
    @ExcelProperty(value = "所属资方", index = 21)
    @ColumnWidth(20)
    private String fundName;

    @ExcelProperty(value = "来源电销", index = 22)
    @ColumnWidth(20)
    private String source;

    @ExcelProperty(value = "评估师", index = 23)
    @ColumnWidth(20)
    private String storeAppraiser;

    /**
     * 请款节点
     */
    @ExcelProperty(value = "签约客服", index = 24)
    @ColumnWidth(20)
    private String paymentApplyName;

    @ExcelProperty(value = "月还金额", index = 25)
    @ColumnWidth(20)
    private String repaymentAmountTotal;

    @ExcelProperty(value = "产品现返比例(%)", index = 26)
    @ColumnWidth(20)
    private BigDecimal cashPerformancePercentage;

    @ExcelProperty(value = "现返金额", index = 27)
    @ColumnWidth(20)
    private BigDecimal cashAmount;
    @ExcelProperty(value = "已还期数", index = 28)
    @ColumnWidth(20)
    private Integer repaidPeriods;
    @ExcelProperty(value = "进件时间", index = 29)
    @ColumnWidth(20)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime preCreateTime;
    @ExcelProperty(value = "预审时间", index = 30)
    @ColumnWidth(20)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime preApproveTime;
    @ExcelProperty(value = "客户等级", index = 31)
    @ColumnWidth(20)
    private String customerLevel;
    @ExcelProperty(value = "资方订单编号", index = 32)
    @ColumnWidth(20)
    private String creditReqNo;

    @ExcelProperty(value = "抵押时间", index = 33)
    @ColumnWidth(20)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime mortgageTime;
    /**
     * 品牌
     */
    @ExcelProperty(value = "品牌", index = 34)
    @ColumnWidth(20)
    private String brand;
    /**
     * 车系
     */
    @ExcelProperty(value = "车系", index = 35)
    @ColumnWidth(20)
    private String vehicleSeries;

    @ExcelProperty(value = "户籍地", index = 36)
    @ColumnWidth(20)
    private String idCardAddress;

    @ExcelProperty(value = "居住地", index = 37)
    @ColumnWidth(20)
    private String homeAddress;

    @ExcelProperty(value = "年龄", index = 38)
    @ColumnWidth(20)
    private String age;
}
