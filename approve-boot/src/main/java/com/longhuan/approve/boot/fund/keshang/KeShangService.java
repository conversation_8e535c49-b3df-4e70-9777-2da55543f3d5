package com.longhuan.approve.boot.fund.keshang;

import com.longhuan.approve.boot.pojo.dto.FundFinalBaseDTO;
import com.longhuan.approve.boot.pojo.dto.FundPreBaseDTO;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.KeShangRequestHead;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.KeShangResponseHead;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.request.*;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.response.*;

public interface KeShangService {

    /**
     * 预授信申请
     */
    KeShangResponseHead keShangCreditPreApply(FundPreBaseDTO fundPreBaseDTO);

    /**
     * 进件
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangResponseHead keShangAddedBusiness(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 进件撤销
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangAddBusinessCancelRequest addedBusinessCancel(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 进件信息变更
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangAddBusinessChangeRequest addedBusinessChange(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 放款指令
     */
    KeShangResponseHead loanApplyByOrderId(Integer orderId);

    /**
     * 申贷放款结果查询
     * @param orderId
     * @return
     */
    KeShangResponseHead applyLoanResult(Integer orderId);

    /**
     * 查询客户申贷信息
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangApplyLoanResponse queryCustomerApplyLoanInfo(FundFinalBaseDTO fundFinalBaseDTO);

  /*  *//**
     * 查询还款结果
     * @param fundFinalBaseDTO
     * @return
     *//*
    KeShangRepaymentResultResponse queryRepaymentResult(FundFinalBaseDTO fundFinalBaseDTO);

    *//**
     * 还款计划查询接口
     * @param orderId
     * @return
     *//*
    Boolean repaymenPlanQuery(Integer orderId);*/

    /**
     * 凭证开具查询接口
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangQueryResponse proofIssueQuery(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 文件签署查询接口
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangQueryResponse fileSignQuery(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 平台文件通知
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangPlatformFileNoticeRequest platformFileNotice(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 进件结果通知
     * @param callback
     * @return
     */
    KeShangResponseHead addBusinessResultNotice(KeShangRequestHead callback);

    /**
     * 放款结果通知
     * @param callback
     * @return
     */
    KeShangResponseHead loanResultNotice(KeShangLoanResultRequest callback);

    /**
     * 面签结果通知
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangVisaInterviewResultRequest visaInterviewResultNotice(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 文件签署结果通知
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangFileSignResultRequest fileSignResultNotice(FundFinalBaseDTO fundFinalBaseDTO);

   /* *//**
     * 还款计划信息
     * @param fundFinalBaseDTO
     * @return
     *//*
    KeShangRepaymentPlanResponse.RepaymentPlanItem repaymentPlanInfo(FundFinalBaseDTO fundFinalBaseDTO);

    *//**
     * 还款流水信息
     * @param fundFinalBaseDTO
     * @return
     *//*
    KeShangRepaymentDetailRequest repaymentDetailInfo(FundFinalBaseDTO fundFinalBaseDTO);*/

    /**
     * 客商银行卡鉴权
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangBankCardAuthRequest bankCardAuth(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 获取签署链接
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangGetSignatureLinkRequest getSignatureLink(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 协议签约查询
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangAgreeSignQueryResponse agreeSignQuery(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 协议签约发送短信验证码
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangAgreeSignSendCodeRequest agreeSignSendCode(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 协议签约
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangAgreeSignRequest agreeSign(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 绑定卡查询
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangBindCardInfoResponse bindCardInfo(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 凭证开具
     * @param fundFinalBaseDTO
     * @return
     */
    KeShangProofIssueRequest proofIssue(FundFinalBaseDTO fundFinalBaseDTO);

    /**
     * 进件结果查询
     * @param orderId
     */
    void creditApplyQueryByOrderId(Integer orderId);


    /**
     * 预授信结果查询
     * @param preId
     */
    void preApproveQueryV2ByPreId(Integer preId);
}
