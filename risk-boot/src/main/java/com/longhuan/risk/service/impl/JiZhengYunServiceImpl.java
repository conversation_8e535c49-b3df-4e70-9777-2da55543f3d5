package com.longhuan.risk.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.longhuan.common.core.base.RequestResponseInfoEntity;
import com.longhuan.common.core.constant.RequestResponseCode;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.risk.credit.report.pojo.CreditDTO;
import com.longhuan.risk.credit.report.pojo.pmm.Pb02;
import com.longhuan.risk.credit.report.pojo.pmm.Pmm;
import com.longhuan.risk.credit.report.service.CreditReportService;
import com.longhuan.risk.feign.OrderFeign;
import com.longhuan.risk.feign.jizhengyun.JiZhengYunFeign;
import com.longhuan.risk.mapper.*;
import com.longhuan.risk.pojo.CreditCustomerDto;
import com.longhuan.risk.pojo.CreditUserDTO;
import com.longhuan.risk.pojo.UserIdNumberDTO;
import com.longhuan.risk.pojo.dto.GetUserMarriageDTO;
import com.longhuan.risk.pojo.dto.PoliceBadDTO;
import com.longhuan.risk.pojo.dto.jzy.*;
import com.longhuan.risk.pojo.entity.HuaRuiDataEntity;
import com.longhuan.risk.pojo.entity.JizhengyunDataEntity;
import com.longhuan.risk.pojo.entity.JzyLitigationEntity;
import com.longhuan.risk.pojo.entity.JzyPoliceDataEntity;
import com.longhuan.risk.pojo.vo.FactorVO;
import com.longhuan.risk.pojo.vo.JzyMarriageStatusVO;
import com.longhuan.risk.pojo.vo.JzySheSuVo;
import com.longhuan.risk.service.JiZhengYunService;
import com.longhuan.risk.service.JzyEncryptService;
import com.longhuan.risk.service.RequestRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 极证云服务
 * <p>
 * 接口使用sha1验签 和 3des加解密
 *
 * <AUTHOR>
 * @date 2024/07/22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JiZhengYunServiceImpl implements JiZhengYunService {


    private final JizhengyunDataMapper jizhengyunDataMapper;

    private final JiZhengYunFeign jiZhengYunFeign;
    private final HuaRuiDataMapper huaRuiDataMapper;
    private final CreditReportService creditReportService;

    private final JzyEncryptService jzyEncryptService;
    private final RequestRecordService requestRecordService;
    private final JzyPoliceDataMapper jzyPoliceDataMapper;
    private final RequestResponseInfoMapper requestResponseInfoMapper;
    private final OrderFeign orderFeign;

    private final JzyLitigationMapper jzyLitigationMapper;
    @Value("${partner.jzy.customer_code}")
    private String customerCode;
    @Value("${spring.profiles.active}")
    private String profileActive;
    @Autowired
    private RedisService redisService;
    @Autowired
    private EnvUtil envUtil;

    private static String setPhoneOnlineText(JzyPhone fistBean) {
        String desc = null;
        if (fistBean == null) {
            return "查询失败";
        }

        if ("0".equals(fistBean.getResult()) && fistBean.getData() != null) {

            desc = fistBean.getData().getDesc();
        }
        if (Objects.equals("1", fistBean.getResult())) {
            return fistBean.getResultMsg();
        }
        return desc;
    }

    private static Integer setVerify(JzyISPFactor ispFactor) {
        if (ispFactor == null) {
            return null;
        }
        if (ispFactor.getData() != null) {

            return ispFactor.getData().getVerify();

        }
        return null;
    }

    private static String setVerifyText(JzyISPFactor ispFactor) {
        String verifyText = null;
        if (ispFactor == null) {
            return "查询失败";
        }
        if (Objects.equals("0", ispFactor.getResult()) && ispFactor.getData() != null) {
            Integer ispVerify = ispFactor.getData().getVerify();

            switch (ispVerify) {
                case 1:
                    verifyText = "一致";
                    break;
                case 2:
                    verifyText = "不一致";
                    break;
                case -1:
                    verifyText = "查无记录";
                    break;
                default:
                    break;
            }

        }
        if (!Objects.equals("0", ispFactor.getResult())) {
            return ispFactor.getResultMsg();
        }
        return verifyText;
    }

    private static <T> T result2Bean(Class<T> clazz, JzyRes jzyRes) {
        String message = jzyRes.getMessage();

        if (StringUtils.hasText(message)) {
            if (JSONUtil.isTypeJSON(message)) {
                return JSONUtil.toBean(message, clazz);
            } else {
                throw new BusinessException("JiZhengYunServiceImpl result2Bean: " + message);
            }
        } else {
            log.error("result2Bean message is empty jzyRes：{}", jzyRes);
        }
        return null;
    }

    /**
     * 极证云要素验证
     *
     * @param customerDto
     * @return
     */
    @Override

    public FactorVO verifyFactor(CreditCustomerDto customerDto) {

        String idNumber = customerDto.getIdNumber();
        String lockKey = "risk:jzy:id" + idNumber;
        try {

            redisService.acquireLock(lockKey, idNumber, 30, 5);

            boolean validCard = IdcardUtil.isValidCard(idNumber);
            if (!validCard) {
                throw new BusinessException("身份证号码不合法");
            }

            log.info("verifyFactor tryLock fail {}", customerDto);
            // 检查是否存在
            String name = customerDto.getName().trim();
            String phoneNumber = customerDto.getPhoneNumber();
            Long exists = jizhengyunDataMapper.selectCount(new LambdaQueryWrapper<JizhengyunDataEntity>()
                    .eq(JizhengyunDataEntity::getName, name)
                    .eq(JizhengyunDataEntity::getPhoneNumber, phoneNumber)
                    .eq(JizhengyunDataEntity::getIdNumber, idNumber)
                    .eq(JizhengyunDataEntity::getStatus, 3)
                    .eq(JizhengyunDataEntity::getDeleteFlag, 0)
                    .ge(JizhengyunDataEntity::getCreateTime, LocalDateTime.now().minusDays(30)));

            if (exists > 0) {
                log.info("verifyFactor exits {} skip", customerDto);
                return getFactorData(name, phoneNumber, idNumber);
            }


            JizhengyunDataEntity mainEntity = new JizhengyunDataEntity();
            mainEntity.setName(name);
            mainEntity.setIdNumber(idNumber);
            mainEntity.setPhoneNumber(phoneNumber);
            jizhengyunDataMapper.insert(mainEntity);

            Integer dataId = mainEntity.getId();

            if (!Objects.equals("prd", profileActive)) {
                //  null,新入网或离网
                // (0,3],0-3个月
                // (3,6],3-6个月
                // (6,12],6-12个月
                // (12,24],12-24个月
                // (24,+],24个月以上
                mainEntity.setNetworkDurationDesc("(12,24]");
                mainEntity.setValidation(1);
                // 1 一致 2不一致 -1查无记录
                mainEntity.setValidationDesc("一致");
                // 更新
                mainEntity.setStatus(3);
                jizhengyunDataMapper.updateById(mainEntity);
                return getFactorData(name, phoneNumber, idNumber);
            }

            JzyPhone bean = getJzyPhone(dataId, name, phoneNumber, idNumber);

            mainEntity.setNetworkDurationDesc(setPhoneOnlineText(bean));

            JzyISPFactor ispFactor = getJzyISPFactor(dataId, name, phoneNumber, idNumber);
            String verifyText = setVerifyText(ispFactor);
            mainEntity.setValidation(setVerify(ispFactor));
            mainEntity.setValidationDesc(verifyText);
            mainEntity.setValidationJson(JSONUtil.toJsonStr(ispFactor));
//            mainEntity.setValidationNo(ispFactorReq.getOutTradeNo());

            // 更新
            mainEntity.setStatus(3);
            jizhengyunDataMapper.updateById(mainEntity);

            return getFactorData(name, phoneNumber, idNumber);

        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            redisService.releaseLock(lockKey, idNumber);
        }

    }

    private JzyISPFactor getJzyISPFactor(Integer dataId, String name, String phoneNumber, String idNumber) {
        JzyISPFactorReq ispFactorReq = new JzyISPFactorReq(generateOutOrderNo(dataId), generateTm(), customerCode);
        ispFactorReq.setName(name);
        ispFactorReq.setPhone(phoneNumber);
        ispFactorReq.setCertNo(idNumber);

        // 要素验证
        return ispThreeFactorQuery(ispFactorReq, dataId);
    }

    private JzyPhone getJzyPhone(Integer dataId, String name, String phoneNumber, String idNumber) {
        // 主借人 要素
        JzyPhoneReq phone = new JzyPhoneReq(generateOutOrderNo(dataId), generateTm(), customerCode);
        phone.setName(name);
        phone.setPhone(phoneNumber);
        phone.setCertNo(idNumber);
        // 手机号
        return getJzyPhoneQuery(phone, dataId);
    }

    @Override
    public FactorVO getFactorData(String name, String customerPhone, String idNumber) {
        JizhengyunDataEntity jizhengyunDataEntity = jizhengyunDataMapper.selectOne(
                new LambdaQueryWrapper<JizhengyunDataEntity>()
                        .eq(JizhengyunDataEntity::getPhoneNumber, customerPhone)
                        .eq(JizhengyunDataEntity::getName, name)
                        .eq(JizhengyunDataEntity::getIdNumber, idNumber)
                        .orderByDesc(JizhengyunDataEntity::getCreateTime)
                        .last("limit 1"));
        if (jizhengyunDataEntity == null) {
            return new FactorVO().setFactorVerify(false);
        }
        return new FactorVO()
                .setNetworkDuration(jizhengyunDataEntity.getNetworkDurationDesc())
                .setThreeFactorVerify(jizhengyunDataEntity.getValidationDesc())
                .setFactorVerify(Objects.equals(1, jizhengyunDataEntity.getValidation()));
    }

    private JzyISPFactor ispThreeFactorQuery(JzyISPFactorReq ispFactorReq, Integer jzyId) {
        RequestResponseInfoEntity requestEntity = requestRecordService.saveRecord(RequestResponseCode.JZY_FACTORY_QUERY,
                JSONUtil.toJsonStr(ispFactorReq), jzyId, "jzy");
        Integer requestId = requestEntity.getId();

        //
        ispFactorReq.setOutTradeNo(generateOutOrderNo(requestId));
        ispFactorReq.setSign(getSinContent(ispFactorReq));

        log.info("request content:{}", JSONUtil.toJsonStr(ispFactorReq));

        JzyRes jzyRes = jiZhengYunFeign.ispThreeFactorQuery(ispFactorReq);

        //保存相应参数
        requestRecordService.updateRecord(requestEntity, JSONUtil.toJsonStr(jzyRes));

        return result2Bean(JzyISPFactor.class, jzyRes);

    }

    /**
     * 交易流水号 6-32位
     *
     * @param dataId
     * @return
     */
    public String generateOutOrderNo(Integer dataId) {
        return "%s%s".formatted(DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN), String.format("%010d", dataId));
    }

    public String generateTm() {
        return DateUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_PATTERN);
    }

    private JzyPhone getJzyPhoneQuery(JzyPhoneReq mainReq, Integer jzyId) {

        // 保存请求调用请求信息
        RequestResponseInfoEntity requestEntity = requestRecordService.saveRecord(RequestResponseCode.JZY_ONLINE_QUERY, JSONUtil.toJsonStr(mainReq), jzyId, "jzy");
        Integer requestId = requestEntity.getId();
        //
        mainReq.setOutTradeNo(generateOutOrderNo(requestId));
        mainReq.setSign(getSinContent(mainReq));

        log.info("request content:{}", JSONUtil.toJsonStr(mainReq));

        JzyRes jzyRes = jiZhengYunFeign.phoneOnlineQuery(mainReq);

        requestRecordService.updateRecord(requestEntity, JSONUtil.toJsonStr(jzyRes));

        return result2Bean(JzyPhone.class, jzyRes);
    }

    @Override
    public JzyPoliceRecord publicSecurityRecord(UserIdNumberDTO creditUserDTO) {

        JzyPoliceDataEntity jzyPoliceDataEntity = jzyPoliceDataMapper.selectOne(new LambdaQueryWrapper<JzyPoliceDataEntity>()
                .eq(JzyPoliceDataEntity::getName, creditUserDTO.getIdName())
                .eq(JzyPoliceDataEntity::getIdNumber, creditUserDTO.getIdNumber())
                .eq(JzyPoliceDataEntity::getDeleteFlag, 0)
                .gt(JzyPoliceDataEntity::getCreateTime, LocalDateTime.now().minusDays(15))
                .orderByDesc(JzyPoliceDataEntity::getCreateTime)
                .last("limit 1")
        );

        if (jzyPoliceDataEntity != null && Objects.equals(jzyPoliceDataEntity.getStatus(), 3)) {
            String dataJson = jzyPoliceDataEntity.getDataJson();
            log.info("JzyPoliceDataEntity is exist record dataJson {}", dataJson);
            return result2Bean(JzyPoliceRecord.class, JSONUtil.toBean(dataJson, JzyRes.class));
        } else {
            jzyPoliceDataEntity = new JzyPoliceDataEntity();
            jzyPoliceDataEntity.setName(creditUserDTO.getIdName());
            jzyPoliceDataEntity.setIdNumber(creditUserDTO.getIdNumber());
            jzyPoliceDataEntity.setStatus(1);
            jzyPoliceDataMapper.insert(jzyPoliceDataEntity);

        }
        Integer entityId = jzyPoliceDataEntity.getId();

        // 测试环境挡板
        if (!envUtil.isPrd()) {
            JzyPoliceRecord policeRecord = new JzyPoliceRecord()
                    .setResult(1)
                    .setResultMsg("成功")
                    .setData(new JzyPoliceRecord.DataDTO().setHit(0).setScore(0));
            JzyRes jzyRes = new JzyRes().setMessage(JSONUtil.toJsonStr(policeRecord));
            jzyPoliceDataEntity.setDataJson(JSONUtil.toJsonStr(jzyRes));
            jzyPoliceDataEntity.setScore(policeRecord.getData().getScore());
            jzyPoliceDataEntity.setHit(policeRecord.getData().getHit());
            jzyPoliceDataEntity.setStatus(3);
            jzyPoliceDataMapper.updateById(jzyPoliceDataEntity);

            return policeRecord;
        }


        JzyPoliceRecordReq mainReq = new JzyPoliceRecordReq(generateOutOrderNo(entityId), generateTm(), customerCode);
        mainReq.setName(creditUserDTO.getIdName());
        mainReq.setCertNo(creditUserDTO.getIdNumber());
        mainReq.setSign(getSinContent(mainReq));

        // 保存请求调用请求信息
        RequestResponseInfoEntity requestEntity = requestRecordService.saveRecord(RequestResponseCode.JZY_PUBLIC_RECORD,
                JSONUtil.toJsonStr(mainReq), entityId, "jzy");

        log.info("request content:{}", JSONUtil.toJsonStr(mainReq));

        JzyRes jzyRes = jiZhengYunFeign.policeRecord(mainReq);

        requestRecordService.updateRecord(requestEntity, JSONUtil.toJsonStr(jzyRes));

        try {
            jzyPoliceDataEntity.setDataJson(JSONUtil.toJsonStr(jzyRes));

            JzyPoliceRecord jzyPoliceRecord = result2Bean(JzyPoliceRecord.class, jzyRes);

            if (jzyPoliceRecord != null) {

                JzyPoliceRecord.DataDTO data = jzyPoliceRecord.getData();

                if (data != null) {
                    jzyPoliceDataEntity.setScore(data.getScore());
                    jzyPoliceDataEntity.setHit(data.getHit());
                    jzyPoliceDataEntity.setStatus(3);
                    if (data.getScore()!=null){
//                        preApprovalApplyInfoMapper
                        try {
                            PoliceBadDTO policeBadDTO = new PoliceBadDTO();
                            policeBadDTO.setIdName(creditUserDTO.getIdName());
                            policeBadDTO.setIdNumber(creditUserDTO.getIdNumber());
                            policeBadDTO.setScore(data.getScore());
                            orderFeign.policeBad(policeBadDTO);
                        } catch (Exception e) {
                            log.info("policeBad error 调用公安接口失败");
                        }
                    }
                } else {
                    throw new BusinessException("查询失败无数据");
                }
            } else {
                throw new BusinessException("查询失败无数据");
            }
            jzyPoliceDataMapper.updateById(jzyPoliceDataEntity);
            return jzyPoliceRecord;
        } catch (Exception e) {
            log.error("jzy publicSecurityRecord error {} ", e.getMessage(), e);
            jzyPoliceDataEntity.setStatus(4);
            jzyPoliceDataEntity.setFailReason(e.getMessage());
            jzyPoliceDataMapper.updateById(jzyPoliceDataEntity);
            throw e;
        }

    }

    @Override
    public List<String> analyseScore(JzyPoliceRecord publicRecord) {
        if (publicRecord == null || publicRecord.getData() == null || Objects.equals(publicRecord.getData().getHit(), 0)) {
            return List.of();
        }
        Integer score = publicRecord.getData().getScore();
        if (score > 64 || score < 0) {
            return List.of();
        }

        // 1、score由6位二进制数转换而来
        // 2、二进制数位由高到低分别表示：1-其他、2-前科、3-涉毒、4-吸毒、5-在逃、6-涉案（包括在逃撤销）。 值为1表示命中，0表示未命中。
        // 3、score范围为[1,63]
        // 4、例子：score=25，转换成二进制为011001，第二位为1，表示命中前科；第三位为1，表示命中涉毒，即命中前科和涉毒；第六位为1，表示命中涉案。
        // 5、1-其他：命中的是涉稳人员、肇事肇祸精神病人、重点上访人员等
        String binaryString = String.format("%6s", Integer.toBinaryString(score)).replace(" ", "0");
        log.debug("binaryString {}", binaryString);
        if (binaryString.length() != 6) {
            return List.of();
        }
        List<String> list = new ArrayList<>();

        if (binaryString.charAt(0) == '1') {
            list.add("其他");
        }
        if (binaryString.charAt(1) == '1') {
            list.add("前科");
        }
        if (binaryString.charAt(2) == '1') {
            list.add("涉毒");
        }
        if (binaryString.charAt(3) == '1') {
            list.add("吸毒");
        }
        if (binaryString.charAt(4) == '1') {
            list.add("在逃");
        }
        if (binaryString.charAt(5) == '1') {
            list.add("涉案");
        }

        return list;

    }

    @Override
    public String getUserMarriage(GetUserMarriageDTO dto) {
        log.info("getUserMarriage dto:{}", dto);

//        // 1. 从信用报告中获取婚姻状态
//        String marriageStatus = getMarriageStatusByCreditReport(dto);
//        if (StrUtil.isNotEmpty(marriageStatus)) {
//            return marriageStatus;
//        } else {
//            log.info("getUserMarriage not found in creditReport");
//        }

        String marriageStatus = "";
        // 2. 从极证云获取婚姻状态

        String name = dto.getName();
        String idNumber = dto.getIdNumber();
        JizhengyunDataEntity jizhengyunDataEntity = jizhengyunDataMapper.selectOne(new LambdaQueryWrapper<JizhengyunDataEntity>()
                .eq(JizhengyunDataEntity::getName, name)
                .eq(JizhengyunDataEntity::getIdNumber, idNumber)
                .orderByDesc(JizhengyunDataEntity::getCreateTime).last("limit 1"));

        Integer dataId = 0;
        if (jizhengyunDataEntity != null) {
            // 已有数据
            if (Objects.equals(3, jizhengyunDataEntity.getMarriageFlag())) {
                log.info("getUserMarriage found in jizhengyunDataEntity");
                return jizhengyunDataEntity.getMarriageStatus();
            }
            dataId = jizhengyunDataEntity.getId();
        } else {
            JizhengyunDataEntity mainEntity = new JizhengyunDataEntity();
            mainEntity.setName(name);
            mainEntity.setIdNumber(idNumber);
            jizhengyunDataMapper.insert(mainEntity);
            dataId = mainEntity.getId();
        }


        try {
            JzyMarriageStatusVO jzyMarriageStatusVO = getJzyMarriageStatusVO(dataId, name, idNumber);

            log.info("userMarriage jzyRes:{}", jzyMarriageStatusVO);


            marriageStatus = Optional.ofNullable(jzyMarriageStatusVO).map(JzyMarriageStatusVO::getData)
                    .filter(data -> data.getStatus() != null)
                    .map(data -> switch (data.getStatus()) {
                        case "11" -> "已婚";
                        case "12" -> "离婚";
                        default -> "未婚";
                    }).orElse("未婚");


            if (StrUtil.isNotEmpty(marriageStatus)) {
                JizhengyunDataEntity updateEntity = new JizhengyunDataEntity();
                updateEntity.setId(dataId);
                updateEntity.setMarriageFlag(3);
                updateEntity.setMarriageStatus(marriageStatus);
                jizhengyunDataMapper.updateById(updateEntity);
            }
            return marriageStatus;
        } catch (Exception e) {
            log.error("getUserMarriage error {}", e.getMessage(), e);
            return "未婚";
        }


    }

    private JzyMarriageStatusVO getJzyMarriageStatusVO(Integer dataId, String name, String idNumber) {
        JzyMarriageReq jzyMarriageReq = new JzyMarriageReq(generateOutOrderNo(dataId), generateTm(), customerCode);
        jzyMarriageReq.setName(name);
        jzyMarriageReq.setCertNo(idNumber);

        JzyMarriageStatusVO jzyMarriageStatusVO = userMarriage(jzyMarriageReq, dataId);
        return jzyMarriageStatusVO;
    }


    @Override
    public TwoFactorRes twoVerifyFactor(UserIdNumberDTO idNumberDTO) {


        Integer entityId = RandomUtil.randomInt(10000);

        JzyTwoFactorReq mainReq = new JzyTwoFactorReq(generateOutOrderNo(entityId),
                generateTm(),
                customerCode);
        mainReq.setName(idNumberDTO.getIdName());
        mainReq.setCertNo(idNumberDTO.getIdNumber());
        mainReq.setSign(getSinContent(mainReq));

        // 保存请求调用请求信息
        RequestResponseInfoEntity requestEntity = requestRecordService.saveRecord(RequestResponseCode.JZY_PUBLIC_RECORD,
                JSONUtil.toJsonStr(mainReq), entityId, "jzy");

        log.info("request content:{}", JSONUtil.toJsonStr(mainReq));

        JzyRes jzyRes = jiZhengYunFeign.twoVerifyFactor(mainReq);

        requestRecordService.updateRecord(requestEntity, JSONUtil.toJsonStr(jzyRes));

        try {
            TwoFactorRes factorRes = result2Bean(TwoFactorRes.class, jzyRes);
            if (factorRes != null) {

                String result = factorRes.getResult();
                String resultMsg = factorRes.getResultMsg();
                log.info("factorRes:{},resultMsg:{}", result, resultMsg);
            }
            return factorRes;
        } catch (Exception e) {
            log.error("jzy publicSecurityRecord error {} ", e.getMessage(), e);
            throw e;
        }

    }

    @Override
    public JzyPhone phoneOnline(CreditUserDTO creditDto) {
        return getJzyPhone(RandomUtil.randomInt(10000), creditDto.getIdName(), creditDto.getPhoneNumber(), creditDto.getIdNumber());
    }

    @Override
    public JzyMarriageStatusVO marriageStatus(UserIdNumberDTO creditDto) {
        return getJzyMarriageStatusVO(RandomUtil.randomInt(10000), creditDto.getIdName(), creditDto.getIdNumber());
    }

    @Override
    public JzySheSuVo relatedLitigation(UserIdNumberDTO idNumberDTO) {
        log.info("relatedLitigation idNumberDTO:{}", idNumberDTO);
        Integer dataId = 0;
        JzyLitigationEntity jzyLitigationEntity = jzyLitigationMapper.selectOne(new LambdaQueryWrapper<JzyLitigationEntity>()
                .eq(JzyLitigationEntity::getIdNumber, idNumberDTO.getIdNumber())
                .eq(JzyLitigationEntity::getName, idNumberDTO.getIdName())
                .eq(JzyLitigationEntity::getDeleteFlag, 0)
                .ge(JzyLitigationEntity::getUpdateTime, LocalDateTime.now().minusDays(30))
                .orderByDesc(JzyLitigationEntity::getUpdateTime)
                .last("limit 1")
        );

        if (jzyLitigationEntity == null) {
            jzyLitigationEntity = new JzyLitigationEntity();
            jzyLitigationEntity.setName(idNumberDTO.getIdName());
            jzyLitigationEntity.setIdNumber(idNumberDTO.getIdNumber());
            jzyLitigationMapper.insert(jzyLitigationEntity);
        }

        if (!envUtil.isPrd()) {
            log.info("jzy devMode");
            List<JzyLitigationEntity> jzyLitigationEntities = jzyLitigationMapper.selectList(new LambdaQueryWrapper<JzyLitigationEntity>()
                    .eq(JzyLitigationEntity::getDataStatus, 3)
                    .isNotNull(JzyLitigationEntity::getResponseBody)
                    .orderByDesc(JzyLitigationEntity::getId).last("limit 10"));
            if (!jzyLitigationEntities.isEmpty()) {
                JzyLitigationEntity jzyLitigationEntity1 = jzyLitigationEntities.get(RandomUtil.randomInt(jzyLitigationEntities.size()));
                jzyLitigationEntity.setResponseBody(jzyLitigationEntity1.getResponseBody());
                jzyLitigationEntity.setDataStatus(3);
                jzyLitigationMapper.updateById(jzyLitigationEntity);
            }
        }

        if (Objects.equals(jzyLitigationEntity.getDataStatus(), 3) && StrUtil.isNotEmpty(jzyLitigationEntity.getResponseBody())) {
            String responseBody = jzyLitigationEntity.getResponseBody();
            JzyRes bean = JSONUtil.toBean(responseBody, JzyRes.class);
            String message = bean.getMessage();
            if (JSONUtil.isTypeJSON(message)) {
                log.info("JiZhengYunServiceImpl.relatedLitigation 进行测试拒绝 极证云数据:{}",message);
                return JSONUtil.toBean(message, JzySheSuVo.class);
            } else {
                throw new BusinessException("极证云查询失败: " + message);
            }
        }

        JzyRelatedLitigationReq jzyRelatedLitigationReq = new JzyRelatedLitigationReq(generateOutOrderNo(jzyLitigationEntity.getId()), generateTm(), customerCode);
        jzyRelatedLitigationReq.setName(idNumberDTO.getIdName());
        jzyRelatedLitigationReq.setCertNo(idNumberDTO.getIdNumber());
        return userRelatedLitigation(jzyRelatedLitigationReq, jzyLitigationEntity);
    }

    @Override
    public JzyISPFactor threeVerifyFactor(CreditUserDTO creditDto) {
        return getJzyISPFactor(RandomUtil.randomInt(10000), creditDto.getIdName(), creditDto.getPhoneNumber(), creditDto.getIdNumber());
    }

    /**
     * 获取极证云数据
     * @param creditDto
     * @return
     */
    @Override
    public String getJzyRelatedData(UserIdNumberDTO creditDto) {
        JzyLitigationEntity jzyLitigationEntity = jzyLitigationMapper.selectOne(new LambdaQueryWrapper<JzyLitigationEntity>()
                .eq(JzyLitigationEntity::getIdNumber, creditDto.getIdNumber())
                .eq(JzyLitigationEntity::getName, creditDto.getIdName())
                .eq(JzyLitigationEntity::getDeleteFlag, 0)
                .orderByDesc(JzyLitigationEntity::getUpdateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNotNull(jzyLitigationEntity) && ObjUtil.isNotEmpty(jzyLitigationEntity.getResponseBody())) {
            return jzyLitigationEntity.getResponseBody();
        }
        return null;
    }

    /**
     * 通过信用报告获得婚姻
     *
     * @param dto DTO
     * @return {@link String }
     */
    private @Nullable String getMarriageStatusByCreditReport(GetUserMarriageDTO dto) {
        HuaRuiDataEntity huaRuiDataEntity = huaRuiDataMapper.selectOne(new LambdaQueryWrapper<HuaRuiDataEntity>().select(HuaRuiDataEntity::getCreditReport).eq(HuaRuiDataEntity::getIdNumber, dto.getIdNumber()).isNotNull(HuaRuiDataEntity::getCreditReport).eq(HuaRuiDataEntity::getDeleteFlag, 0).orderByDesc(HuaRuiDataEntity::getCreateTime).last("limit 1"));

        if (ObjUtil.isNotEmpty(huaRuiDataEntity)) {
            CreditDTO creditDTO = creditReportService.xmlReport2DTO(huaRuiDataEntity.getCreditReport());

            // 学历
//        String pb01ad02 = creditDTO.getPim().getPb01().getPb01a().getPb01ad02Desc();
            // 婚姻
            Pmm pmm = creditDTO.getPmm();
            if (pmm == null) {
                return null;
            }
            Pb02 pb02 = pmm.getPb02();
            if (pb02 == null) {
                return null;
            }
            String pb020d01Desc = pb02.getPb020d01Desc();
            if (StringUtils.hasText(pb020d01Desc)) {
                return pb020d01Desc;
            }
        }
        return null;
    }

    private JzyMarriageStatusVO userMarriage(JzyMarriageReq mainReq, Integer jzyId) {

        // 保存请求调用请求信息
        RequestResponseInfoEntity requestEntity = requestRecordService.saveRecord(RequestResponseCode.JZY_MARRIAGE_QUERY, JSONUtil.toJsonStr(mainReq), jzyId, "jzy");
        Integer requestId = requestEntity.getId();
        //
        mainReq.setOutTradeNo(generateOutOrderNo(requestId));
        mainReq.setSign(getSinContent(mainReq));


        log.info("request content:{}", JSONUtil.toJsonStr(mainReq));

        JzyRes jzyRes = jiZhengYunFeign.marriageStatusDetection(mainReq);

        requestRecordService.updateRecord(requestEntity, JSONUtil.toJsonStr(jzyRes));

        return result2Bean(JzyMarriageStatusVO.class, jzyRes);
    }

    private JzySheSuVo userRelatedLitigation(JzyRelatedLitigationReq mainReq, JzyLitigationEntity jzyLitigationEntity) {
        // 保存请求调用请求信息
        Integer dataId = jzyLitigationEntity.getId();
        RequestResponseInfoEntity requestEntity = requestRecordService.saveRecord(RequestResponseCode.JZY_RELATED_LITIGATION, JSONUtil.toJsonStr(mainReq),
                dataId, "jzy_related");


        String requestJson = JSONUtil.toJsonStr(mainReq);
        mainReq.setOutTradeNo(generateOutOrderNo(dataId));
        mainReq.setSign(getSinContent(mainReq));

        log.info("userRelatedLitigation request content:{}", JSONUtil.toJsonStr(mainReq));
        jzyLitigationMapper.update(new LambdaUpdateWrapper<JzyLitigationEntity>()
                .set(JzyLitigationEntity::getRequestBody, requestJson)
                .set(JzyLitigationEntity::getDataStatus, 2)
                .eq(JzyLitigationEntity::getId, dataId));

        JzyRes jzyRes = jiZhengYunFeign.relatedLitigation(mainReq);
        String responseJson = JSONUtil.toJsonStr(jzyRes);
        log.info("userRelatedLitigation response content:{}", responseJson);
        requestRecordService.updateRecord(requestEntity, responseJson);

        int dataStatus = 3;
        try {
            return result2Bean(JzySheSuVo.class, jzyRes);
        } catch (Exception e) {
            dataStatus = 4;
            throw  e;
        } finally {
            jzyLitigationMapper.update(new LambdaUpdateWrapper<JzyLitigationEntity>()
                    .set(JzyLitigationEntity::getResponseBody, responseJson)
                    .set(JzyLitigationEntity::getDataStatus, dataStatus)
                    .eq(JzyLitigationEntity::getId, dataId));
        }
    }

    private String getSinContent(JzyRelatedLitigationReq req) {
        return getSinContent("OUT_TRADE_NO", req.getOutTradeNo(), "TRAN_TIME", req.getTranTime(), "VERIFY_TYPE",
                req.getVerifyType(), "NAME", req.getName(), "CERT_NO", req.getCertNo()

        );
    }

    private String getSinContent(JzyMarriageReq req) {
        return getSinContent("OUT_TRADE_NO", req.getOutTradeNo(), "TRAN_TIME", req.getTranTime(), "VERIFY_TYPE",
                req.getVerifyType(), "NAME", req.getName(), "CERT_NO", req.getCertNo()

        );
    }

    private String getSinContent(JzyTwoFactorReq req) {
        return getSinContent("OUT_TRADE_NO", req.getOutTradeNo(), "TRAN_TIME", req.getTranTime(), "VERIFY_TYPE",
                req.getVerifyType(), "NAME", req.getName(), "CERT_NO", req.getCertNo()

        );
    }


    private String getSinContent(JzyPoliceRecordReq req) {
        return getSinContent("OUT_TRADE_NO", req.getOutTradeNo(), "TRAN_TIME", req.getTranTime(), "VERIFY_TYPE",
                req.getVerifyType(), "NAME", req.getName(), "CERT_NO", req.getCertNo());
    }

    /**
     * 返回签名内容
     *
     * @param
     * @return
     */
    private String getSinContent(JzyISPFactorReq req) {
        // OUT_TRADE_NO=20161026110000000032&TRAN_TIME=2024-07-31 15:43:35&VERIFY_TYPE=0130&NAME=张三&CERT_NO=1234565&PHONE=13900010001
        return getSinContent("OUT_TRADE_NO", req.getOutTradeNo(), "TRAN_TIME", req.getTranTime(), "VERIFY_TYPE",
                req.getVerifyType(), "NAME", req.getName(), "CERT_NO", req.getCertNo(), "PHONE", req.getPhone());
    }

    /**
     * 返回签名内容
     *
     * @param req
     * @return
     */
    private String getSinContent(JzyPhoneReq req) {
        // OUT_TRADE_NO=20161026110000000032&TRAN_TIME=2024-07-31 15:41:44&VERIFY_TYPE=0134&NAME=张三&PHONE=13900010001&CERT_NO=1234560
        return getSinContent("OUT_TRADE_NO", req.getOutTradeNo(), "TRAN_TIME", req.getTranTime(), "VERIFY_TYPE",
                req.getVerifyType(), "NAME", req.getName(), "PHONE", req.getPhone(), "CERT_NO", req.getCertNo()

        );
    }

    private String getSinContent(String... value) {
        StringBuilder builder = new StringBuilder();
        int len = value.length / 2;
        for (int i = 0; i < len; i++) {
            builder.append("&");
            builder.append(value[i * 2]);
            builder.append("=");
            builder.append(value[i * 2 + 1]);
        }
        String content = builder.substring(1);
        return jzyEncryptService.signSHA1withRSA(content);
    }


}
