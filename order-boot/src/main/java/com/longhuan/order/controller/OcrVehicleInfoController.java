package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.entity.PreOcrVehicleInfoEntity;
import com.longhuan.order.pojo.dto.InsertOcrVehicleInfoDTO;
import com.longhuan.order.service.PreOcrVehicleInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * ocr识别车辆信息表控制器
 *
 * <AUTHOR>
 * @Date 2024/7/11
 */
@RestController
@RequestMapping("/ocrVehicleInfo")
@RequiredArgsConstructor
public class OcrVehicleInfoController {
    private final PreOcrVehicleInfoService preOcrVehicleInfoService;

    /**
     * 提交行驶证信息
     *
     * @param insertOcrVehicleInfoDTO 行驶证信息
     * @return 行驶证信息
     */
    @PostMapping("/insertOcrVehicleInfo")
    public Result<PreOcrVehicleInfoEntity> insertOcrVehicleInfo(@RequestBody @Validated InsertOcrVehicleInfoDTO insertOcrVehicleInfoDTO) {
        return Result.success(preOcrVehicleInfoService.insertOcrVehicleInfo(insertOcrVehicleInfoDTO));
    }

    /**
     * 根据预审批id查询行驶证信息
     *
     * @param preId 预审批id
     * @return 行驶证信息
     */
    @GetMapping("/getDetailsByApplyInfoId/{preId}")
    public Result<PreOcrVehicleInfoEntity> getDetailsByApplyInfoId(@PathVariable("preId") Integer preId) {
        return Result.success(preOcrVehicleInfoService.getDetailsByApplyInfoId(preId));
    }

}
