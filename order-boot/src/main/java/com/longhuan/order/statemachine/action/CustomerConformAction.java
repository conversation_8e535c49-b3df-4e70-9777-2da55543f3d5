package com.longhuan.order.statemachine.action;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.service.ContractFileService;
import com.longhuan.order.service.LanHaiDifferentiationService;
import com.longhuan.order.service.OrderSendMessage;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 * 客户确认
 * <p>
 * 状态转移动作，在转移进行的时候触发的动作
 */
@Slf4j
@Component
public class CustomerConformAction extends OrderNodeAction implements Action<States, Events> {
    public final OrderSendMessage orderSendMessage;
    public final ContractFileService contractFileService;
    private final DeptQuotaRecordMapper deptQuotaRecordMapper;
    private final DeptQuotaMapper deptQuotaMapper;
    private final OrderAmountMapper orderAmountMapper;
    private final LanHaiDifferentiationService lanHaiDifferentiationService;

    public CustomerConformAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper,
                                 ContractFileService contractFileService, OrderSendMessage orderSendMessage, DeptQuotaRecordMapper deptQuotaRecordMapper,
                                 DeptQuotaMapper deptQuotaMapper, OrderAmountMapper orderAmountMapper,
                                 LanHaiDifferentiationService lanHaiDifferentiationService) {
        super(orderInfoMapper, orderNodeRecordMapper);
        this.contractFileService = contractFileService;
        this.orderSendMessage = orderSendMessage;
        this.deptQuotaRecordMapper = deptQuotaRecordMapper;
        this.deptQuotaMapper = deptQuotaMapper;
        this.orderAmountMapper = orderAmountMapper;
        this.lanHaiDifferentiationService = lanHaiDifferentiationService;
    }


    @Override
    public void execute(StateContext<States, Events> context) {

        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();
        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);

        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event,
                currentNode, nextNode);

        try {
//            // 生成合同
//            log.info("CustomerConformAction.execute generateContractSignList orderId:{} ", orderId);
//            contractFileService.generateContractSignList(orderId);
            // 重置资方签署状态和我司合同状态
            contractFileService.resetFundSignStatus(orderId);

            Integer code = event.getCode();
            // 发送微信消息通知客户
            log.info("CustomerConformAction.execute sendWeChatMsg orderId:{} node:{} status:{}", orderId, nextNode, code);
            if (Objects.equals(nextNode, States.CUSTOMER_CONFIRM.getNode())){
                Boolean resetProduct = lanHaiDifferentiationService.isResetProduct(orderId);
                if (!resetProduct){
                    orderSendMessage.sendWeChatMsg(orderId, nextNode, code);
                }
            }else {
                orderSendMessage.sendWeChatMsg(orderId, nextNode, code);
            }

        } catch (Exception e) {
            log.error("CustomerConformAction.execute error {}", e.getMessage(), e);
        }

        // 1. 修改订单表状态
        if (Boolean.TRUE.equals(target.getId().getValid())) {
            saveOrderNode(orderId, nextNode);
        }

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);
        //3.更新额度
        LocalDate now = LocalDate.now();
        deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                .set(DeptQuotaEntity::getEnable, 1)
                .lt(DeptQuotaEntity::getEndDate, now)
                .eq(DeptQuotaEntity::getEnable, 0)
                .eq(DeptQuotaEntity::getDeleteFlag, 0));
        DeptQuotaRecordEntity deptQuotaRecordEntity = deptQuotaRecordMapper.selectOne(new LambdaQueryWrapper<DeptQuotaRecordEntity>()
                .eq(DeptQuotaRecordEntity::getOrderId, orderId)
                .eq(DeptQuotaRecordEntity::getAction, 0)
                .eq(DeptQuotaRecordEntity::getDeleteFlag, 0)
                .orderByDesc(DeptQuotaRecordEntity::getId), false);
        if (ObjUtil.isNull(deptQuotaRecordEntity)) {
            return;
        }
        OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                .eq(OrderAmountEntity::getOrderId, orderId)
                .eq(OrderAmountEntity::getDeleteFlag, 0)
                .orderByDesc(OrderAmountEntity::getId), false);
        BigDecimal differenceAmount = deptQuotaRecordEntity.getQuota().subtract(orderAmountEntity.getCustomerConfirmAmount());
        int update = deptQuotaMapper.update(new LambdaUpdateWrapper<DeptQuotaEntity>()
                .setIncrBy(DeptQuotaEntity::getRemainingQuota, differenceAmount)
                .eq(DeptQuotaEntity::getId, deptQuotaRecordEntity.getDeptQuotaId())
                .eq(DeptQuotaEntity::getDeptId, deptQuotaRecordEntity.getDeptId())
                .eq(DeptQuotaEntity::getDeleteFlag, 0));
        if (update == 0) {
            if (ObjUtil.notEqual(differenceAmount, BigDecimal.ZERO)) {
                log.error("CustomerConformAction execute ModifyTheDifference orderid:{}", orderId);
                deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                        .set(DeptQuotaRecordEntity::getDeleteFlag, 1)
                        .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
            }
        } else {
            deptQuotaRecordMapper.update(new LambdaUpdateWrapper<DeptQuotaRecordEntity>()
                    .set(DeptQuotaRecordEntity::getQuota, orderAmountEntity.getCustomerConfirmAmount())
                    .eq(DeptQuotaRecordEntity::getId, deptQuotaRecordEntity.getId()));
        }
    }


}