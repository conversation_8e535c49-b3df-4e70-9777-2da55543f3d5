package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.aliyun.oss.*;
import com.aliyun.oss.model.PutObjectResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.GlobalConstants;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.constants.SwitchConstants;
import com.longhuan.common.redis.pojo.SwitchVO;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.data.api.pojo.dto.GetDigitizationCustomerOrderByVinDTO;
import com.longhuan.data.api.pojo.dto.GetDigitizationCustomerOrderDTO;
import com.longhuan.data.api.pojo.dto.SyncDigitizationRiskReasonDTO;
import com.longhuan.order.enums.FileConfigEnums;
import com.longhuan.order.enums.IntelligentEnum;
import com.longhuan.order.enums.PreRiskStatusEnum;
import com.longhuan.order.enums.RiskPolicyResult;
import com.longhuan.order.feign.*;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.dto.intelligentRisk.IntelligentRiskCallBack;
import com.longhuan.order.pojo.dto.intelligentRisk.QueryIntelligentRiskDTO;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.AmountCalVO;
import com.longhuan.order.pojo.vo.GetIntelligentAIRiskReasonVO;
import com.longhuan.order.service.*;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.resource.pojo.vo.FileInfoVO;
import com.longhuan.risk.enums.FundProductResult;
import com.longhuan.risk.pojo.dto.GetUserMarriageDTO;
import com.longhuan.risk.pojo.vo.FactorVO;
import com.longhuan.risk.pojo.vo.FhldDataVO;
import com.longhuan.risk.pojo.vo.FundProductVO;
import com.longhuan.risk.pojo.vo.RiskCreditFeatureVO;
import com.longhuan.user.pojo.vo.DeptInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.*;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class IntelligentRiskControlServiceImpl extends ServiceImpl<IntelligentRiskInfoEntityMapper, IntelligentRiskInfoEntity>
        implements IntelligentRiskControlService {
    private final ProductInfoMapper productInfoMapper;
    private final PreApprovalApplyInfoMapper preApprovalApplyInfoMapper;
    private final FileResourceMapper fileResourceMapper;
    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucketName}")
    private String bucketName;
    //    @Value("${aliyun.oss.region}")
//    private String region = "cn-beijing";
    @Value("${aliyun.oss.bucketPath}")
    private String bucketPath;

//==========面签视频==========
    @Value("${aliyun.live.getOSSEndpoint}")
    private String getOSSEndpoint;
    @Value("${aliyun.live.accessKeyId}")
    private String accessId;
    @Value("${aliyun.live.accessKeySecret}")
    private String accessSecret;
    @Value("${aliyun.live.oSSBucket}")
    private String oSSBucket;
    // 创建固定线程池（建议放在类中作为成员变量或 Bean）
    private final ExecutorService executor = Executors.newFixedThreadPool(10);
    private final IntelligentRiskControlFeign intelligentRiskControlFeign;
    private final OrderCustomerInfoMapper orderCustomerInfoMapper;
    private final OrderFileMapper orderFileMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final DictService dictService;
    private final OrderVehicleInfoMapper orderVehicleInfoMapper;
    private final RiskFeign riskFeign;
    private final OrderAmountMapper orderAmountMapper;
    private final LanBenVehicleDataMapper lanBenVehicleDataMapper;
    private final ResourceFeign resourceFeign;
    private final OrderNodeRecordMapper orderNodeRecordMapper;
    private final ResourceOssInfoEntityMapper resourceOssInfoEntityMapper;
    private final IntelligentRiskInfoEntityMapper intelligentRiskInfoEntityMapper;
    private final PreFundInfoService preFundInfoService;
    private final OrderContactPersonMapper  orderContactPersonMapper;
    private final CustomerMortgageInfoMapper customerMortgageInfoMapper;
    private final PreOcrVehicleInfoMapper preOcrVehicleInfoMapper;
    private final AmountService amountService;
    private final EnvUtil envUtil;
    private final UserFeign userFeign;
    private final JzgVehicleDataMapper jzgVehicleDataMapper;
    private final  OrderContractMapper orderContractMapper;
    private final BankAccountSignMapper bankAccountSignMapper;
    private final SwitchUtils switchUtils;
    private final FundProductMappingMapper fundProductMappingMapper;
    private final SmartContractReportMapper smartContractReportMapper;
    private final PreRiskPolicyResultMapper preRiskPolicyResultMapper;
    private final RiskFundAutoConfigService riskFundAutoConfigService;
    private final RiskAiIntelligentAuditMapper riskAiIntelligentAuditMapper;
    private final OrderStateService orderStateService;
    private final Integer aiUserId = 99999;
    private final RedisService redisService;
    private final RiskPromptMapper riskPromptMapper;
    private final LoanOrderFeign loanOrderFeign;
    private final FeeDisclosureFeign feeDisclosureFeign;
    @Override
    public Boolean intelligentRisk(Integer orderId, Integer step, String orderNumber) {
        if (ObjUtil.isEmpty(orderId) && StringUtils.isEmpty(orderNumber)){
            throw new BusinessException("订单ID和订单编号不能同时为空");
        }
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(ObjUtil.isNotEmpty(orderId),OrderInfoEntity::getId, orderId)
                .like(StringUtils.isNotEmpty(orderNumber),OrderInfoEntity::getOrderNumber, orderNumber));
        ObjectMapper objectMapper = new ObjectMapper();
        SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.INTELLIGENT_AI_QUOTA);
        List<Integer> list = new ArrayList<>();
        if (Objects.equals(switchInfo.getSwitchFlag(),1) && StringUtils.isNotBlank(switchInfo.getValue())){
            try {
                list = objectMapper.readValue(switchInfo.getValue(), new TypeReference<List<Integer>>() {});
            } catch (JsonProcessingException e) {
                return true;
            }
        }else {
            return true;
        }
        if (CollUtil.isNotEmpty(list) && !list.contains(orderInfoEntity.getFundId())){
            return true;
        }else if (CollUtil.isEmpty(list)){
            return true;
        }
        if (Objects.equals(step,2)){
            RiskAiIntelligentAuditEntity riskAiIntelligentAuditEntity = riskAiIntelligentAuditMapper.selectOne(
                    new LambdaQueryWrapper<RiskAiIntelligentAuditEntity>()
                            .eq(RiskAiIntelligentAuditEntity::getOrderId, orderId)
                            .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
            );
            if (riskAiIntelligentAuditEntity != null){
                riskAiIntelligentAuditEntity.setAiReportStatus(0);
                riskAiIntelligentAuditMapper.updateById(riskAiIntelligentAuditEntity);
            }else {
                RiskAiIntelligentAuditEntity insertInfo = new RiskAiIntelligentAuditEntity();
                insertInfo.setOrderId(orderId)
                        .setOrderNumber(orderInfoEntity.getOrderNumber())
                        .setOrderSource(orderInfoEntity.getSourceType())
                        .setAiReportStatus(0)
                        .setAiReportRequestTime(LocalDateTime.now())
                        .setIsAutoAudit(2);
                riskAiIntelligentAuditMapper.insert(insertInfo);
            }
        }
//        if(!Objects.equals(orderInfoEntity.getFundId(), FundEnum.FU_MIN.getValue()) && !Objects.equals(orderInfoEntity.getFundId(), FundEnum.YING_FENG.getValue())){
//            return true;
//        }
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(orderInfoEntity.getPreId());
        OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectOne(
                new LambdaQueryWrapper<OrderCustomerInfoEntity>()
                        .eq(OrderCustomerInfoEntity::getId, orderInfoEntity.getCustomerId())
        );
        OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                .eq(OrderVehicleInfoEntity::getOrderId, orderInfoEntity.getId()));

        List<PreOcrVehicleInfoEntity> preOcrVehicleInfoEntityList = preOcrVehicleInfoMapper.selectList(
                new LambdaQueryWrapper<PreOcrVehicleInfoEntity>()
                        .eq(PreOcrVehicleInfoEntity::getPreId,orderInfoEntity.getPreId())
                        .eq(PreOcrVehicleInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(PreOcrVehicleInfoEntity::getUpdateTime)
                        .last("limit 1")
        );

        PreFundInfoEntity preFundInfo = preFundInfoService.getByPreIdAndFundId(orderInfoEntity.getPreId(), orderInfoEntity.getFundId());
        AmountCalVO amountCalVO = amountService.calAmount(new AmountCalDTO().setOrderId(orderInfoEntity.getId()));
        List<OrderContactPersonEntity> orderContactPersonEntityList = orderContactPersonMapper.selectList(
                new LambdaQueryWrapper<OrderContactPersonEntity>()
                        .eq(OrderContactPersonEntity::getOrderId, orderInfoEntity.getId())
                        .in(OrderContactPersonEntity::getIndex, Arrays.asList(1,2))
                        .eq(OrderContactPersonEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderContactPersonEntity::getCreateTime)
        );
        OrderNodeRecordEntity orderNodeRecordEntity = orderNodeRecordMapper.selectOne(new LambdaQueryWrapper<OrderNodeRecordEntity>().eq(OrderNodeRecordEntity::getOrderId, orderInfoEntity.getId())
                .orderByDesc(OrderNodeRecordEntity::getId).last("limit 1"));
        Result<FactorVO> factorData = riskFeign.getFactorData(orderCustomerInfoEntity.getName(), orderCustomerInfoEntity.getPhone(), orderCustomerInfoEntity.getIdNumber());
        Result<String> userMarriage = riskFeign.getUserMarriage(new GetUserMarriageDTO().setName(orderCustomerInfoEntity.getName()).setIdNumber(orderCustomerInfoEntity.getIdNumber()));
        ProductInfoEntity productInfoEntity = productInfoMapper.selectById(orderInfoEntity.getProductId());
        List<RiskPromptEntity> riskPromptEntities = riskPromptMapper.selectList(
                new LambdaQueryWrapper<RiskPromptEntity>()
                        .eq(RiskPromptEntity::getDeleteFlag, 0)
                        .eq(RiskPromptEntity::getEnable, 0)
                        .eq(RiskPromptEntity::getType, 1)
                        .eq(RiskPromptEntity::getDeptId, orderInfoEntity.getDeptId())
                        .orderByDesc(RiskPromptEntity::getCreateTime)
                        .last("limit 1")
        );
        Boolean orderInfoByIDCard = loanOrderFeign.getOrderInfoByIDCard(orderCustomerInfoEntity.getIdNumber());
        //贷后车架号
        List<String> loanInfoByIDCard = loanOrderFeign.getLoanInfoByIDCard(orderCustomerInfoEntity.getIdNumber());
        //数字化贷前车架号
        List<String> digitizationCustomerOrders = feeDisclosureFeign.getDigitizationCustomerOrders(new GetDigitizationCustomerOrderDTO().setIdCard(orderCustomerInfoEntity.getIdNumber()));
        //云启贷前订单车架号
        List<String> orderInfoList = orderInfoMapper.selectJoinList(String.class,
                new MPJLambdaWrapper<OrderInfoEntity>()
                        .select(OrderVehicleInfoEntity::getVin)
                        .leftJoin(OrderCustomerInfoEntity.class,OrderCustomerInfoEntity::getId, OrderInfoEntity::getManagerId)
                        .leftJoin(OrderVehicleInfoEntity.class,OrderVehicleInfoEntity::getOrderId, OrderInfoEntity::getId)
                        .eq(OrderCustomerInfoEntity::getIdNumber, orderCustomerInfoEntity.getIdNumber())
                        .lt(OrderInfoEntity::getCurrentNode,States.PAYMENT_SUCCESS)
                        .ge(OrderInfoEntity::getCurrentNode,States.BUSINESS_ADDED_INFO)
                        .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                        .eq(OrderCustomerInfoEntity::getDeleteFlag, 0)
                        .eq(OrderInfoEntity::getDeleteFlag, 0)
        );
        //云启贷前预审车架号
        List<String> preApprovalApplyInfoList = preApprovalApplyInfoMapper.selectJoinList(String.class,
                new MPJLambdaWrapper<PreApprovalApplyInfoEntity>()
                        .select(PreOcrVehicleInfoEntity::getVin)
                        .leftJoin(PreOcrVehicleInfoEntity.class,PreOcrVehicleInfoEntity::getPreId, PreApprovalApplyInfoEntity::getId)
                        .eq(PreOcrVehicleInfoEntity::getDeleteFlag, 0)
                        .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
                        .eq(PreApprovalApplyInfoEntity::getIdNumber, preApprovalApplyInfoEntity.getIdNumber())
        );


        //贷后订单
        List<String> orderInfoByVin = loanOrderFeign.getOrderInfoByVin(orderVehicleInfoEntity.getVin());
        //数字化贷前订单
        List<String> digitizationCustomerOrderByVin = feeDisclosureFeign.getDigitizationCustomerOrderByVin(new GetDigitizationCustomerOrderByVinDTO().setVin(orderVehicleInfoEntity.getVin()));
        //云启贷前订单号
        List<OrderInfoEntity> orderInfoVinList = orderInfoMapper.selectJoinList(OrderInfoEntity.class,
                new MPJLambdaWrapper<OrderInfoEntity>()
                        .selectAll(OrderInfoEntity.class)
                        .leftJoin(OrderCustomerInfoEntity.class,OrderCustomerInfoEntity::getId, OrderInfoEntity::getManagerId)
                        .leftJoin(OrderVehicleInfoEntity.class,OrderVehicleInfoEntity::getOrderId, OrderInfoEntity::getId)
                        .eq(OrderVehicleInfoEntity::getVin, orderVehicleInfoEntity.getVin())
                        .lt(OrderInfoEntity::getCurrentNode,States.PAYMENT_SUCCESS)
                        .ge(OrderInfoEntity::getCurrentNode,States.BUSINESS_ADDED_INFO)
                        .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                        .eq(OrderCustomerInfoEntity::getDeleteFlag, 0)
                        .eq(OrderInfoEntity::getDeleteFlag, 0)
        );
        //云启贷前预审车架号
        List<Integer> preApprovalApplyInfoVinList = preApprovalApplyInfoMapper.selectJoinList(Integer.class,
                new MPJLambdaWrapper<PreApprovalApplyInfoEntity>()
                        .select(PreApprovalApplyInfoEntity::getId)
                        .leftJoin(PreOcrVehicleInfoEntity.class,PreOcrVehicleInfoEntity::getPreId, PreApprovalApplyInfoEntity::getId)
                        .eq(PreOcrVehicleInfoEntity::getDeleteFlag, 0)
                        .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
                        .eq(PreOcrVehicleInfoEntity::getVin, orderVehicleInfoEntity.getVin())
        );
        //取出订单
        List<String> collect = new ArrayList<>();
        List<Integer> preIdList = new ArrayList<>();
        if(CollUtil.isNotEmpty(orderInfoVinList)){
            collect = orderInfoVinList.stream().map(OrderInfoEntity::getOrderNumber).filter(number -> !orderInfoByVin.contains(number)).toList();
            preIdList = orderInfoVinList.stream()
                    .map(OrderInfoEntity::getPreId)
                    .distinct()
                    .toList();
        }



        IntelligentRiskDTO intelligentRiskDTO = new IntelligentRiskDTO();
        InputsDTO dto = new InputsDTO();//
        List<String> idNumberList = Stream.of(
                        Optional.ofNullable(orderInfoList).orElse(Collections.emptyList()).stream(),
                        Optional.ofNullable(preApprovalApplyInfoList).orElse(Collections.emptyList()).stream(),
                        Optional.ofNullable(loanInfoByIDCard).orElse(Collections.emptyList()).stream(),
                        Optional.ofNullable(digitizationCustomerOrders).orElse(Collections.emptyList()).stream()
                )
                .flatMap(stream -> stream)
                .distinct() // 去重
                .toList();
        if (CollUtil.isNotEmpty(idNumberList) && idNumberList.size() <= 2){
            dto.setUserMaxCar(1);
        }else if (CollUtil.isNotEmpty(idNumberList) && idNumberList.size() > 2 ){
            dto.setUserMaxCar(0);
        }else if (CollUtil.isEmpty(idNumberList)){
            dto.setUserMaxCar(1);
        }
        List<String> vinList = Stream.of(
                        Optional.of(collect).orElse(Collections.emptyList()).stream(),
                        Optional.ofNullable(orderInfoByVin).orElse(Collections.emptyList()).stream(),
                        Optional.ofNullable(digitizationCustomerOrderByVin).orElse(Collections.emptyList()).stream()
                ).flatMap(stream -> stream)
                .distinct() // 去重
                .toList();
        if (CollUtil.isNotEmpty(vinList) && vinList.size() <= 1){
            List<Integer> finalPreIdList = preIdList;
            List<Integer> preIdDistinctList = preApprovalApplyInfoVinList.stream()
                    .filter(preId -> !finalPreIdList.contains(preId))
                    .distinct() // 去重
                    .toList();
            if (CollUtil.isNotEmpty(preIdDistinctList) && preIdDistinctList.size() > 1){
                dto.setCarMaxOrder(0);
            }else {
                dto.setCarMaxOrder(1);
            }
        }else if (CollUtil.isNotEmpty(vinList) && vinList.size() > 1 ){
            dto.setCarMaxOrder(0);
        }else if (CollUtil.isEmpty(vinList)){
            dto.setCarMaxOrder(1);
        }
        dto.setAge(orderCustomerInfoEntity.getAge());
        List<OrderNodeRecordEntity> orderNodeRecordEntityList = orderNodeRecordMapper.selectList(
                new LambdaQueryWrapper<OrderNodeRecordEntity>()
                        .eq(OrderNodeRecordEntity::getOrderId, orderInfoEntity.getId())
                        .eq(OrderNodeRecordEntity::getCurrentNode, States.RISK_FIRST_APPROVE.getNode())
                        .eq(OrderNodeRecordEntity::getEvent, Events.REJECT)
                        .orderByDesc(OrderNodeRecordEntity::getId)
        );
        //云启
        if (CollUtil.isNotEmpty(orderNodeRecordEntityList)){
            AtomicReference<String> failOrderRea = new AtomicReference<>("");
            List<String> list1 = orderNodeRecordEntityList
                    .stream()
                    .map(OrderNodeRecordEntity::getRejectReason).toList()
                    .stream().map(item -> {
                        try {
                            return dictService.getDictLabel(GlobalConstants.DictType.REVIEW_REFUSE_REASON, Integer.valueOf(item));
                        } catch (NumberFormatException e) {
                            log.info("IntelligentRiskControlServiceImpl.intelligentRisk error:{}",e.getMessage());
                            return null;
                        }
                    }).toList();
            dto.setHistoryFailOrder(1);
            dto.setFailOrderRea(list1.stream()
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining("|")));
        }else {
            dto.setHistoryFailOrder(0);
        }
        //数字化
        List<String> riskReason = feeDisclosureFeign.syncDigitizationRiskReason(new SyncDigitizationRiskReasonDTO().setIdNumber(orderCustomerInfoEntity.getIdNumber()));
        String riskReasonStr = riskReason.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("|"));
        if (StringUtils.isNotBlank(dto.getFailOrderRea())){
            dto.setFailOrderRea(dto.getFailOrderRea()+"|"+riskReasonStr);
        }else {
            dto.setHistoryFailOrder(1);
            dto.setFailOrderRea(riskReasonStr);
        }
        //金蝶
        List<String> riskReason1 = feeDisclosureFeign.syncOldKingDeeRiskReason(new SyncDigitizationRiskReasonDTO().setIdNumber(orderCustomerInfoEntity.getIdNumber()));
        String riskReason1Str = riskReason1.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("|"));
        if (Objects.equals(dto.getHistoryFailOrder(),1) && StringUtils.isNotBlank(riskReason1Str)){
            if (StringUtils.isNotBlank(riskReason1Str)){
                dto.setFailJdieRea(riskReason1Str);
            }
        } else if(Objects.equals(dto.getHistoryFailOrder(),0) && StringUtils.isNotBlank(riskReason1Str)){
            dto.setHistoryFailOrder(1);
            dto.setFailJdieRea(riskReason1Str);
        }

        dto.setHistoryOrder(orderInfoByIDCard ? 1 : 0);
        if (CollUtil.isNotEmpty(riskPromptEntities)){
            dto.setRiskStore(1);
            dto.setRiskStoreReason(riskPromptEntities.get(0).getMessage());
//            if (riskPromptEntities.get(0).getMessage().contains("综合资质不足拒绝")){
//
//            }

        }else {
            dto.setRiskStore(0);
            dto.setRiskStoreReason(null);
        }
        if (ObjUtil.isNotNull(orderVehicleInfoEntity)){
            if (Objects.equals(orderVehicleInfoEntity.getCarReportType(),1)){
                LanBenVehicleDataEntity lanBenVehicleDataEntity = lanBenVehicleDataMapper.selectOne(new LambdaQueryWrapper<LanBenVehicleDataEntity>().eq(LanBenVehicleDataEntity::getVin, orderVehicleInfoEntity.getVin())
                        .eq(LanBenVehicleDataEntity::getDeleteFlag, 0).orderByDesc(LanBenVehicleDataEntity::getCreateTime).last("limit 1"));
                dto.setLbjAmount(String.valueOf(lanBenVehicleDataEntity.getEvaluateAmount()));
                dto.setLbjCondition(lanBenVehicleDataEntity.getEvaluate());
                dto.setLbjMileage(lanBenVehicleDataEntity.getMileage());
            }
            if (Objects.equals(orderVehicleInfoEntity.getCarReportType(),2)){
                JzgVehicleDataEntity jzgVehicleDataEntity = jzgVehicleDataMapper.selectOne(new LambdaQueryWrapper<JzgVehicleDataEntity>().eq(JzgVehicleDataEntity::getVin, orderVehicleInfoEntity.getVin())
                        .eq(JzgVehicleDataEntity::getDeleteFlag, 0).orderByDesc(JzgVehicleDataEntity::getCreateTime).last("limit 1"));
                dto.setLbjAmount(String.valueOf(jzgVehicleDataEntity.getAssessmentPriceB2c()));
                dto.setLbjCondition(jzgVehicleDataEntity.getCarStatusLevel());
                dto.setLbjMileage(jzgVehicleDataEntity.getMileage());
            }
        }


        List<PreRiskPolicyResultEntity> riskResultList = preRiskPolicyResultMapper.selectList(new LambdaQueryWrapper<PreRiskPolicyResultEntity>()
                .eq(PreRiskPolicyResultEntity::getPreId, preApprovalApplyInfoEntity.getId())
                .eq(PreRiskPolicyResultEntity::getDeleteFlag, 0)
//                    .in(PreRiskPolicyResultEntity::getResult, RiskPolicyResult.REJECT, RiskPolicyResult.WARN)
        );
        if (CollUtil.isNotEmpty(riskResultList)) {
            String funds = riskResultList.stream()
                    .filter(entity -> "征信规则".equals(entity.getPolicyName()))
                    .findFirst()
                    .map(PreRiskPolicyResultEntity::getFunds)
                    .orElse(null);
            dto.setRiskReport(funds);
        }

        dto.setIsJzg(Objects.equals(orderVehicleInfoEntity.getCarReportType(),2) ? 1 : 0);
        if(Objects.equals(orderInfoEntity.getFundId(),FundEnum.LAN_HAI.getValue())){
            List<OrderContactPersonEntity> filteredList = orderContactPersonEntityList.stream()
                    .filter(entity -> entity.getIndex() != null && entity.getIndex() == 2)
                    .toList();
            if (CollUtil.isNotEmpty(filteredList)) {
                switch (filteredList.get(0).getRelation()){
                    case 1:
                        dto.setSecondContactRelation("配偶");
                        break;
                    case 2:
                        dto.setSecondContactRelation("父亲");
                        break;
                    case 3:
                        dto.setSecondContactRelation("子女");
                        break;
                    case 4:
                        dto.setSecondContactRelation("其他直系亲属");
                        break;
                    case 5:
                        dto.setSecondContactRelation("朋友");
                        break;
                    case 6:
                        dto.setSecondContactRelation("同事");
                        break;
                    case 7:
                        dto.setSecondContactRelation("母亲");
                        break;
                    default:
                        dto.setSecondContactRelation("其他");
                        break;
                }
//                Result<FactorVO> result = riskFeign.getFactorData(filteredList.get(0).getName(), filteredList.get(0).getPhone(), filteredList.get(0).getIdNumber());
//                if (Result.isSuccess(result) && result.getData() != null) {
//                    dto.setSecondInfoSame(StringUtils.isNotBlank(result.getData().getThreeFactorVerify()) ? (Objects.equals(result.getData().getThreeFactorVerify(),"一致") ? 1 : 2) : 2);
//                }
            }
        }

        if (Objects.equals(orderInfoEntity.getFundId(),3)){
            dto.setPreStatus(switch (preApprovalApplyInfoEntity.getFundStatus()){
                case APPROVAL_SUCCESS -> 1;
                case APPROVAL_FAIL -> 2;
                default -> 0;
            });
            dto.setPreFailReason(StringUtils.isNotBlank(getFundRefuseReason(preApprovalApplyInfoEntity)) ? getFundRefuseReason(preApprovalApplyInfoEntity) : null);
        }
        if (ObjUtil.isNotNull(orderVehicleInfoEntity.getTransferTimes())){
            dto.setTransNum(orderVehicleInfoEntity.getTransferTimes());
        }else{
            dto.setTransNum(0);
        }
        dto.setCarCreateDate(ObjUtil.isNotEmpty(orderVehicleInfoEntity.getProductionDate()) ? orderVehicleInfoEntity.getProductionDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null);
        dto.setLastJieDi(ObjUtil.isNotEmpty(orderVehicleInfoEntity.getLastMortgageReleaseDate()) ? orderVehicleInfoEntity.getLastMortgageReleaseDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null);
        if (CollUtil.isNotEmpty(preOcrVehicleInfoEntityList)){
            dto.setLicenseHaveDate(preOcrVehicleInfoEntityList.get(0).getUpdateIssueDate().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        }else {
            dto.setLicenseHaveDate(orderVehicleInfoEntity.getIssueDate().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        }
        if (amountCalVO != null) {
            dto.setEvaluateAmount(String.valueOf(amountCalVO.getAppraiserAmount()));
            dto.setFirstAmount(String.valueOf(amountCalVO.getSoftReviewAmount()));
            dto.setPreAmount(String.valueOf(amountCalVO.getPreAmount()));
            dto.setCapitalAmount(String.valueOf(amountCalVO.getFundPreAmount()));
            dto.setRiskAmount(String.valueOf(amountCalVO.getRiskAmount()));
            dto.setApplyAmount(String.valueOf(amountCalVO.getHopeAmount()));
            dto.setTotalAmount(String.valueOf(amountCalVO.getTotalAmount()));
        }
        dto.setZifangId(orderInfoEntity.getFundId());
        dto.setProductTerm(productInfoEntity.getTerm());
        List<OrderContactPersonEntity> filteredList = orderContactPersonEntityList.stream()
                .filter(entity -> entity.getIndex() != null && entity.getIndex() == 1)
                .toList();
        if (CollUtil.isNotEmpty(filteredList)) {
            Result<FactorVO> result = riskFeign.getFactorData(filteredList.get(0).getName(), filteredList.get(0).getPhone(), filteredList.get(0).getIdNumber());
            if (Result.isSuccess(result) && result.getData() != null) {
                dto.setFirstInfoSame(StringUtils.isNotBlank(result.getData().getThreeFactorVerify()) ? (Objects.equals(result.getData().getThreeFactorVerify(),"一致") ? 1 : 2) : 2);
            }
            switch (filteredList.get(0).getRelation()){
                case 1:
                    dto.setFirstContactRelation("配偶");
                    break;
                case 2:
                    dto.setFirstContactRelation("父亲");
                    break;
                case 3:
                    dto.setFirstContactRelation("子女");
                    break;
                case 4:
                    dto.setFirstContactRelation("其他直系亲属");
                    break;
                case 5:
                    dto.setFirstContactRelation("朋友");
                    break;
                case 6:
                    dto.setFirstContactRelation("同事");
                    break;
                case 7:
                    dto.setFirstContactRelation("母亲");
                    break;
                default:
                    dto.setFirstContactRelation("其他");
                    break;
            }
        }

        dto.setDriverLicense(getFileUrlList(orderInfoEntity.getId(), FileConfigEnums.DRIVER_LICENSE, FileConfigEnums.DRIVER_LICENSE_SUBPAGE));
        dto.setFileDriverOne(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.DRIVER_LICENSE));
        dto.setFileDriverTwo(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.DRIVER_LICENSE_SUBPAGE));
        dto.setIssuingAuthority(orderCustomerInfoEntity.getIssuingAuthority());
        dto.setFileIdCard(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.ID_CARD_FRONT));
        dto.setName(orderInfoEntity.getCustomerName());
        dto.setSex(dictService.getDictLabel(GlobalConstants.DictType.SEX, orderCustomerInfoEntity.getGender()));
        dto.setNation(dictService.getDictLabel(GlobalConstants.DictType.NATION, orderCustomerInfoEntity.getNation()));
        dto.setIdCard(orderCustomerInfoEntity.getIdNumber());
        dto.setAddress(orderCustomerInfoEntity.getIdCardDetailedAddress());
        dto.setFileIdCardBack(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.ID_CARD_BACK));
        dto.setFileCardAddBefore(getFileUrlList(orderInfoEntity.getId(), FileConfigEnums.DJZ12, FileConfigEnums.DJZ34, FileConfigEnums.DJZ56, FileConfigEnums.DJZ78, FileConfigEnums.DJZ910, FileConfigEnums.JYZDJZS));
        dto.setCarPlate(orderInfoEntity.getVehicleNumber());
        dto.setBuyCar(orderVehicleInfoEntity.getBuyDate().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        dto.setCarVin(orderVehicleInfoEntity.getVin());
        dto.setEngineNum(orderVehicleInfoEntity.getEngineNumber());
        dto.setRegDate(orderVehicleInfoEntity.getRegisterDate().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        dto.setFileCardLicense(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.DRV_LIC_HO));
        dto.setFileCardLicenseBack(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.DRV_LIC_ATT));
        if (Objects.nonNull(orderInfoEntity.getTeamId())) {
            List<DeptInfoVO> data = userFeign.getTheBranchNameBasedOnTheTeamId(List.of(orderInfoEntity.getTeamId())).getData();
            if (CollUtil.isNotEmpty(data)) {
                data.forEach(branchName -> {
                    dto.setStoreName(ObjUtil.defaultIfNull(orderInfoEntity.getStoreName(), "") + ObjUtil.defaultIfNull(branchName.getName(), ""));
                });
            } else {
                dto.setStoreName(orderInfoEntity.getStoreName());
            }
        } else {
            dto.setStoreName(orderInfoEntity.getStoreName());
        }
        dto.setRegionName(orderInfoEntity.getRegionName());
        dto.setApplyTime(orderInfoEntity.getPreApplyTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond());
        dto.setCapitalName(orderInfoEntity.getFundName());
        dto.setProductName(orderInfoEntity.getProductName());
        dto.setIsDianxiao(Objects.equals(orderInfoEntity.getSourceType(), 1) ? "电销" : "非电销");
        dto.setEmpowerTime(preFundInfo.getFundCreditTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond());
        dto.setOrderId(orderInfoEntity.getOrderNumber());

        dto.setLastNodeName(States.getNode(orderNodeRecordEntity.getCurrentNode()).getDesc());
        dto.setLastNodeTime(orderNodeRecordEntity.getCreateTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
        Result<FhldDataVO> detailByOrderId = riskFeign.getDetailByOrderId(orderCustomerInfoEntity.getIdNumber());
        FhldDataVO fhldDataVO = new FhldDataVO();
        if (ObjUtil.isNotEmpty(detailByOrderId.getData())){
            fhldDataVO = detailByOrderId.getData();
        }
        dto.setRiskResult(StringUtils.isNotEmpty(JSON.toJSONString(fhldDataVO, SerializerFeature.WriteMapNullValue)) ? JSON.toJSONString(fhldDataVO, SerializerFeature.WriteMapNullValue) : "");
        dto.setUserRiskInfo("https://images.hfhuankuanbao.com/pro/H5/IMG1745054175epi8Nx7J.jpg");
        dto.setUserCity(orderCustomerInfoEntity.getNormalAddressProName() + orderCustomerInfoEntity.getNormalAddressCityName());
        try {
            if(!Objects.equals(orderInfoEntity.getFundId(),FundEnum.ZHONG_HENG_TONG_HUI.getValue())){
                if (!Objects.equals(orderInfoEntity.getFundId(),FundEnum.YING_FENG.getValue()) && Objects.equals(orderInfoEntity.getRegionId(),56) && Objects.equals(orderInfoEntity.getSourceType(), 1)){
                    log.info("IntelligentRiskControlServiceImpl.intelligentRisk orderId:{}",orderInfoEntity.getId());
                }else {
                    dto.setFileUserAndCar(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.MOTOR_VEHICLE_GROUP_PHOTO));
                }
            }
        } catch (Exception e) {
            log.info("IntelligentRiskControlServiceImpl.intelligentRisk MOTOR_VEHICLE_GROUP_PHOTO error:{}",e.getMessage());
        }
        try {
            dto.setFileLive(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.FACE_RECOGNITION));
        } catch (Exception e) {
            log.info("IntelligentRiskControlServiceImpl.intelligentRisk FACE_RECOGNITION error:{}",e.getMessage());
        }

        FactorVO data = factorData.getData();
        dto.setMileage(orderVehicleInfoEntity.getMileage().toString());
        dto.setMarital(dictService.getDictLabel(GlobalConstants.DictType.MARITAL_STATUS, orderCustomerInfoEntity.getMaritalStatus()));

        dto.setThirdMarital(userMarriage.getData());
        try {
            dto.setFileDashBoard(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.YBP));
        } catch (Exception e) {
            log.info("IntelligentRiskControlServiceImpl.intelligentRisk YBP error:{}",e.getMessage());
        }
        try {
            dto.setIllegal(getFileUrlList(orderInfoEntity.getId(), FileConfigEnums.VIOLATION_INQUIRY));
        } catch (Exception e) {
            log.info("IntelligentRiskControlServiceImpl.intelligentRisk VIOLATION_INQUIRY error:{}",e.getMessage());
        }
//        dto.setIllegal(getFileUrlList(orderInfoEntity.getId(), FileConfigEnums.VIOLATION_INQUIRY, FileConfigEnums.VIOLATION_MAGE));
        dto.setIllegalCount(0);

        dto.setIsSelfMobile(Objects.equals(data.getThreeFactorVerify(), "一致") ? 1 : 0);
        dto.setMobileOnline(data.getNetworkDuration());
        dto.setAutoReview(0);
        if (orderInfoEntity != null && orderInfoEntity.getFundId() != null) {
            RiskFundAutoConfigEntity riskFundAutoConfigEntity = riskFundAutoConfigService.getRiskFundAutoConfigByFundId(orderInfoEntity.getFundId());
            if (riskFundAutoConfigEntity != null && riskFundAutoConfigEntity.getOpenOff() != null) {
                dto.setAutoReview(riskFundAutoConfigEntity.getOpenOff());
            }
        }
        if (envUtil.isPrd()) {
            Result<RiskCreditFeatureVO> creditFeature = riskFeign.getCreditFeature(orderCustomerInfoEntity.getIdNumber());
            if (Result.isSuccess(creditFeature) && creditFeature.getData() != null) {
                dto.setCurOverdueAmount(ObjUtil.isNotEmpty(creditFeature.getData().getNowOverdueAmount()) ? String.valueOf(creditFeature.getData().getNowOverdueAmount()) : null);
                dto.setIsOver(creditFeature.getData().getIsOverdue() ? 1 : 0);
                dto.setIsFirst(creditFeature.getData().getIsBlankAccount() ? 1 : 0);
                dto.setSingleContinuous24Month(ObjUtil.defaultIfNull(creditFeature.getData().getSingleContinuous24Month(),0));
                dto.setAllCumulative24Month(ObjUtil.defaultIfNull(creditFeature.getData().getAllCumulative24Month(),0));
                dto.setOverdueCumulative24Month(ObjUtil.defaultIfNull(creditFeature.getData().getOverdueCumulative24Month(),0));
                dto.setOverAccount(creditFeature.getData().getOverdueOneTermAccount() >= 1 ? 1 : 0);
            }else {
                dto.setCurOverdueAmount("0");
                dto.setIsOver(0);
                dto.setIsFirst(0);
                dto.setSingleContinuous24Month(0);
                dto.setAllCumulative24Month(0);
                dto.setOverdueCumulative24Month(0);
                dto.setOverAccount(0);
            }
            dto.setReturnUrl("https://guanjia.longhuanhuifeng.com/service/order/api/v1/contract/intelligentRiskCallback?orderId="+orderInfoEntity.getId()+"&step="+step + "&type=1");
            dto.setAutoCallBackUrl("https://guanjia.longhuanhuifeng.com/service/order/api/v1/contract/intelligentRiskResultCallBack");
            intelligentRiskDTO.setUser("yunqi_prd");
        }else {
            dto.setReturnUrl("https://car-sit.longjintech.com/service/order/api/v1/contract/intelligentRiskCallback?orderId="+orderInfoEntity.getId()+"&step="+step + "&type=1");
            dto.setIsOver(0);
            dto.setIsFirst(0);
            dto.setSingleContinuous24Month(0);
            dto.setAllCumulative24Month(0);
            dto.setOverdueCumulative24Month(0);
            dto.setAutoCallBackUrl("https://car-sit.longjintech.com/service/order/api/v1/contract/intelligentRiskResultCallBack");
            intelligentRiskDTO.setUser("yunqi_dev");
        }
        intelligentRiskDTO.setResponse_mode("blocking");
        if (Objects.equals(step,2)){
            dto.setFileLanBenJia(getCarReport(orderInfoEntity.getId(), orderVehicleInfoEntity));
            dto.setFileLanBenJiaImgs(getCarReportList(orderInfoEntity.getId(), orderVehicleInfoEntity));
            IntelligentRiskInfoEntity intelligentRiskInfoEntity = intelligentRiskInfoEntityMapper.selectOne(
                    new LambdaQueryWrapper<IntelligentRiskInfoEntity>()
                            .eq(IntelligentRiskInfoEntity::getOrderId, orderInfoEntity.getId())
                            .eq(IntelligentRiskInfoEntity::getStep, 1)
                            .eq(IntelligentRiskInfoEntity::getType, IntelligentEnum.INTELLIGENT_RISK.getKey())
                            .eq(IntelligentRiskInfoEntity::getDeleteFlag, 0)
                            .orderByDesc(IntelligentRiskInfoEntity::getCreateTime)
                            .last("limit 1")
            );
            if (intelligentRiskInfoEntity != null){
                dto.setFirstRunId(intelligentRiskInfoEntity.getWorkflowRunId());
                intelligentRiskDTO.setInputs(dto);
                if (envUtil.isPrd()){
                    switch (orderInfoEntity.getFundId()){
                        case 11:  //富民生产第二阶段
//                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-7korWnpCuWOyQ4VpW76NDS3a",step);
                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-5zT19NdWMeKBUaO20IFZOIWb",step);
                            break;
                        case 5:  //盈峰生产第二阶段
                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-KQKMcgAFJ2x8Gt92JZJjM6rK",step);
//                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-5zT19NdWMeKBUaO20IFZOIWb",step);
                            break;
                        case 3:  //蓝海生产第二阶段
                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-5zT19NdWMeKBUaO20IFZOIWb",step);
                            break;
                        case 16:  //长银生产第二阶段
                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-5zT19NdWMeKBUaO20IFZOIWb",step);
                            break;
                        case 15:  //通汇生产第二阶段
                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-5zT19NdWMeKBUaO20IFZOIWb",step);
                            break;
                    }
                }else {
                    switch (orderInfoEntity.getFundId()){
                        case 11: //富民测试第二阶段
                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-3FtYHTsBQZbyfo0RqgtH9cdM",step);
                            break;
                        case 5:  //盈峰测试第二阶段
                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-3FtYHTsBQZbyfo0RqgtH9cdM",step);
                            break;
                        case 3:  //蓝海测试第二阶段
                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-3FtYHTsBQZbyfo0RqgtH9cdM",step);
                            break;
                        case 16:  //长银测试第二阶段
                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-3FtYHTsBQZbyfo0RqgtH9cdM",step);
                            break;
                        case 15:  //通汇测试第二阶段
                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-3FtYHTsBQZbyfo0RqgtH9cdM",step);
                            break;
                    }
                }
            }
//            else {
//                intelligentRiskDTO.setInputs(dto);
//                if (envUtil.isPrd()){
//                    switch (orderInfoEntity.getFundId()){
//                        case 11:   //富民生产第一阶段
//                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-hLtqiQpLNJ8wLtRrwCe6kMnr",1);
////                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-49FuR5Z9uzHmp2tO85zDW8ba",1);
//                            break;
//                        case 5:  //盈峰生产第一阶段
//                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-gHdQWTKNNOxCM4bdIsnxdFxP",1);
//                            break;
//                        case 3:  //蓝海生产第一阶段
//                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-QhKGilxYYjVQL31LU2oVDanF",1);
//                            break;
//                    }
//                }else {
//                    switch (orderInfoEntity.getFundId()){
//                        case 11:   //富民测试第一阶段
//                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-ZqNrGjvrBxcqe3cY0C21UugL",1);
//                            break;
//                        case 5:  //盈峰测试第一阶段
//                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-ZqNrGjvrBxcqe3cY0C21UugL",1);
//                            break;
//                        case 3:  //蓝海测试第一阶段
//                            retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-ZqNrGjvrBxcqe3cY0C21UugL",1);
//                            break;
//                    }
//                }
//            }
        }else {
            intelligentRiskDTO.setInputs(dto);
            if (envUtil.isPrd()){
                switch (orderInfoEntity.getFundId()){
                    case 11:   //富民生产第一阶段
                        retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-hLtqiQpLNJ8wLtRrwCe6kMnr",step);
//                        retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-49FuR5Z9uzHmp2tO85zDW8ba",step);
                        break;
                    case 5:   //盈峰生产第一阶段
                        retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-gHdQWTKNNOxCM4bdIsnxdFxP",step);
                        break;
                    case 3:  //蓝海生产第一阶段
                        retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-QhKGilxYYjVQL31LU2oVDanF",step);
                        break;
                    case 16:  //长银生产第一阶段
//                        retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-xWEPycp63IWLLLzKfCBYYhsK",step);
                        retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-QhKGilxYYjVQL31LU2oVDanF",step);
                        break;
                    case 15:  //通汇生产第一阶段
//                        retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-xWEPycp63IWLLLzKfCBYYhsK",step);
                        retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-QhKGilxYYjVQL31LU2oVDanF",step);
                        break;
                }
            }else {
                switch (orderInfoEntity.getFundId()){
                    case 11:  //富民测试第一阶段
                        retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-ZqNrGjvrBxcqe3cY0C21UugL",step);
                        break;
                    case 5:  //盈峰测试第一阶段
                        retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-ZqNrGjvrBxcqe3cY0C21UugL",step);
                        break;
                    case 3:  //蓝海测试第一阶段
                        retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-ZqNrGjvrBxcqe3cY0C21UugL",step);
                        break;
                    case 16:  //长银测试第一阶段
                        retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-ZqNrGjvrBxcqe3cY0C21UugL",step);
                        break;
                    case 15:  //通汇测试第一阶段
                        retrieveRisk(orderInfoEntity.getId(),intelligentRiskDTO,"Bearer app-ZqNrGjvrBxcqe3cY0C21UugL",step);
                        break;
                }
            }
        }
        return true;
    }



    @Override
    public String intelligentRiskResult(Integer orderId, Integer step, Integer type, String orderNumber) {
        if (ObjUtil.isEmpty(orderId) && StringUtils.isEmpty(orderNumber)){
            throw new BusinessException("订单ID和订单编号不能同时为空");
        }
        String lockKey = "intelligentRisk:" + orderId+":"+type;
        String lockValue = IdUtil.randomUUID();
        try {
            Boolean tryLock = redisService.tryLock(lockKey, lockValue, 2, TimeUnit.MINUTES);
            if (!tryLock){
                throw new BusinessException("正在评估中");
            }
        } catch (Exception e) {
            log.info("IntelligentRiskControlServiceImpl.intelligentRiskResult tryLock:{},error:{}", lockKey, e.getMessage());
        }
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(ObjUtil.isNotEmpty(orderId),OrderInfoEntity::getId, orderId)
                .like(StringUtils.isNotEmpty(orderNumber),OrderInfoEntity::getOrderNumber, orderNumber));
        List<IntelligentRiskInfoEntity> intelligentRiskInfoEntityList = intelligentRiskInfoEntityMapper.selectList(
                new LambdaQueryWrapper<IntelligentRiskInfoEntity>()
                        .eq(IntelligentRiskInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(IntelligentRiskInfoEntity::getDeleteFlag, 0)
                        .eq(IntelligentRiskInfoEntity::getType, type)
                        .orderByDesc(IntelligentRiskInfoEntity::getCreateTime)
        );
        if (CollUtil.isNotEmpty(intelligentRiskInfoEntityList)){
            boolean allStepFieldsNull = intelligentRiskInfoEntityList.stream()
                    .allMatch(entity -> entity.getStep() == null);
            if (allStepFieldsNull){
                return intelligentRiskInfoEntityList.get(0).getWorkflowRunId();
            }else {
                if (ObjUtil.isNotNull(step)){
                    List<IntelligentRiskInfoEntity> collect = intelligentRiskInfoEntityList.stream()
                            .filter(entity -> entity.getStep() != null && Objects.equals(entity.getStep(), step))
                            .toList();
                    if (CollUtil.isNotEmpty(collect)){
                        return collect.get(0).getWorkflowRunId();
                    }else {
//                        List<IntelligentRiskInfoEntity> collect1 = intelligentRiskInfoEntityList.stream()
//                                .filter(entity -> entity.getStep() != null && Objects.equals(entity.getStep(), 1))
//                                .toList();
//                        log.info("IntelligentRiskControlServiceImpl.intelligentRiskResult collect1:{}",collect1);
//                        try {
//                            if (CollUtil.isNotEmpty(collect1)){
//                                switch (type){
//                                    case 1:
//                                        intelligentRisk(orderInfoEntity.getId(), 2, null);
//                                        break;
//                                    case 2:
//                                        intelligentContract(orderInfoEntity.getId(), 2, null);
//                                        break;
//                                }
//                            }else {
//                                switch (type){
//                                    case 1: intelligentRisk(orderInfoEntity.getId(), 1, null); break;
//                                    case 2: intelligentContract(orderInfoEntity.getId(), 1, null); break;
//                                }
//                            }
//                        } catch (Exception e) {
//                            log.info("IntelligentRiskControlServiceImpl.intelligentRiskResult error:{}",e.getMessage());
//                        }
                        throw new BusinessException("正在评估中");
                    }
                }else {
                    List<IntelligentRiskInfoEntity> collect2 = intelligentRiskInfoEntityList.stream()
                            .filter(entity -> entity.getStep() != null && Objects.equals(entity.getStep(), 2))
                            .toList();
                    log.info("IntelligentRiskControlServiceImpl.intelligentRiskResult collect2:{}",collect2);
//                    if (CollUtil.isEmpty(collect2)){
//                        List<IntelligentRiskInfoEntity> collect1 = intelligentRiskInfoEntityList.stream()
//                                .filter(entity -> entity.getStep() != null && Objects.equals(entity.getStep(), 1))
//                                .toList();
//                        try {
//                            if (CollUtil.isNotEmpty(collect1)){
//                                switch (type){
//                                    case 1: intelligentRisk(orderInfoEntity.getId(), 2, null); break;
//                                    case 2: intelligentContract(orderInfoEntity.getId(), 2, null); break;
//                                }
//                            }else {
//                                switch (type){
//                                    case 1: intelligentRisk(orderInfoEntity.getId(), 1, null); break;
//                                    case 2: intelligentContract(orderInfoEntity.getId(), 1, null); break;
//                                }
//                            }
//                        } catch (Exception e) {
//                            log.info("IntelligentRiskControlServiceImpl.intelligentRiskResult type:{} error:{}",type,e.getMessage());
//                        }
//                    }
                    return intelligentRiskInfoEntityList.get(0).getWorkflowRunId();
                }
            }
        }else {
//            try {
//                switch (type){
//                    case 1: intelligentRisk(orderInfoEntity.getId(), 1, null); break;
//                    case 2: intelligentContract(orderInfoEntity.getId(), 1, null); break;
//                }
//            } catch (Exception e) {
//                log.info("IntelligentRiskControlServiceImpl.intelligentRiskResult type:{} error:{}",type,e.getMessage());
//            }
            throw new BusinessException("正在评估中");
        }
    }

    @Override
    public Boolean batchIntelligentRisk(BatchIntelligentRiskDTO dto) {
        log.info("IntelligentRiskControlServiceImpl.batchIntelligentRisk dto:{}",dto);
        if (dto.getType() == null){
            throw new BusinessException("请选择报告类型");
        }
        int batchSize = 100; // 每批处理数量
        if (CollUtil.isNotEmpty(dto.getOrderIdList())){
            for (int i = 0; i < dto.getOrderIdList().size(); i += batchSize) {
                List<Integer> subList = dto.getOrderIdList().subList(i, Math.min(i + batchSize, dto.getOrderIdList().size()));
                CompletableFuture.runAsync(() -> {
                    subList.forEach(
                            orderId -> {
                                switch (dto.getType()){
                                    case 1 ->
                                        intelligentRisk(orderId, dto.getStep(), null);
                                    case 2 ->
                                        intelligentContract(orderId, dto.getStep(), null);
                                }
                            });
                }, executor);
            }
            return true;
        }
        if (CollUtil.isNotEmpty(dto.getOrderNumberList())){
            for (int i = 0; i < dto.getOrderNumberList().size(); i += batchSize) {
                List<String> subList = dto.getOrderNumberList().subList(i, Math.min(i + batchSize, dto.getOrderNumberList().size()));
                CompletableFuture.runAsync(() -> {
                    subList.forEach(orderNumber -> {
                        switch (dto.getType()){
                            case 1 ->
                                    intelligentRisk(null, dto.getStep(), orderNumber);
                            case 2 ->
                                    intelligentContract(null, dto.getStep(), orderNumber);
                        }
                    });
                }, executor);
            }
            return true;
        }
        return true;
    }
    @Override
    public Boolean intelligentRiskCallback(Integer orderId, Integer step, Integer type, String result) {
        log.info("IntelligentRiskControlServiceImpl.intelligentRiskCallback orderId:{} step:{} type:{} result:{}", orderId,step,type,result);
        if (ObjUtil.isEmpty(orderId)){
            return false;
        }
        if (StringUtils.isEmpty(result)){
            return false;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> resultMap = objectMapper.readValue(result, Map.class);
            try {
                if (Objects.equals(type,1) && Objects.equals(step,2)){
                    String remarkMapValue = (String) resultMap.getOrDefault("remark", "");
                    String selfRemarkMapValue = (String) resultMap.getOrDefault("selfRemark", "");
    //                String finalAmountMapValue = (String) resultMap.getOrDefault("finalAmount", "");
                    riskAiIntelligentAuditMapper.update(new LambdaUpdateWrapper<RiskAiIntelligentAuditEntity>()
                            .eq(RiskAiIntelligentAuditEntity::getOrderId,orderId)
                            .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
                            .set(RiskAiIntelligentAuditEntity::getAiReportStatus, 1)
                            .set(RiskAiIntelligentAuditEntity::getRemark,ObjUtil.equals(remarkMapValue,"暂无") ? "" : remarkMapValue)
    //                        .set(StringUtils.isNotBlank(finalAmountMapValue),RiskAiIntelligentAuditEntity::getFinalAmount, new BigDecimal(finalAmountMapValue))
                            .set(RiskAiIntelligentAuditEntity::getInternalReason, ObjUtil.equals(selfRemarkMapValue,"暂无") ? "" : selfRemarkMapValue)
                            .set(RiskAiIntelligentAuditEntity::getUpdateTime, LocalDateTime.now())
                    );
    //                if (StringUtils.isNotBlank(finalAmountMapValue)){
    //                    orderAmountMapper.update(
    //                            new LambdaUpdateWrapper<OrderAmountEntity>()
    //                                    .eq(OrderAmountEntity::getOrderId, orderId)
    //                                    .set(OrderAmountEntity::getRiskAmount, new BigDecimal(finalAmountMapValue))
    //                    );
    //                }
                }
            } catch (Exception e) {
                log.info("IntelligentRiskControlServiceImpl.intelligentRiskCallback type:{} error:{}",type,e.getMessage());
            }
            List<IntelligentRiskInfoEntity> intelligentRiskInfoEntityList = intelligentRiskInfoEntityMapper.selectList(
                    new LambdaQueryWrapper<IntelligentRiskInfoEntity>()
                            .eq(IntelligentRiskInfoEntity::getOrderId, orderId)
                            .eq(IntelligentRiskInfoEntity::getType, type)
//                            .eq(ObjUtil.isNotNull(step),IntelligentRiskInfoEntity::getStep, step)
                            .like(IntelligentRiskInfoEntity::getWorkflowRunId, resultMap.get("workflow_run_id").toString())
                            .eq(IntelligentRiskInfoEntity::getDeleteFlag, 0)
                            .orderByDesc(IntelligentRiskInfoEntity::getCreateTime)
            );
            if (CollUtil.isEmpty(intelligentRiskInfoEntityList)){
                intelligentRiskInfoEntityMapper.insert(
                        new IntelligentRiskInfoEntity()
                                .setOrderId(orderId)
                                .setStep(step)
                                .setType(type)
                                .setTaskId(resultMap.get("task_id").toString())
                                .setWorkflowRunId(resultMap.get("workflow_run_id").toString())
                                .setResResult(JSONUtil.toJsonStr(result))
                );
            }else {
                if (Objects.equals(step,1)){
                    intelligentRiskInfoEntityMapper.update(
                            new LambdaUpdateWrapper<IntelligentRiskInfoEntity>()
                                    .set(IntelligentRiskInfoEntity::getDeleteFlag,1)
                                    .eq(IntelligentRiskInfoEntity::getType,type)
                                    .eq(IntelligentRiskInfoEntity::getOrderId, orderId)
                                    .eq(IntelligentRiskInfoEntity::getDeleteFlag,0)
                    );
                }else {
                    intelligentRiskInfoEntityMapper.update(
                            new LambdaUpdateWrapper<IntelligentRiskInfoEntity>()
                                    .set(IntelligentRiskInfoEntity::getDeleteFlag,1)
                                    .eq(IntelligentRiskInfoEntity::getOrderId, orderId)
                                    .eq(IntelligentRiskInfoEntity::getType,type)
                                    .eq(IntelligentRiskInfoEntity::getDeleteFlag,0)
                                    .eq(IntelligentRiskInfoEntity::getStep,step)
                    );
                }
                boolean allStepFieldsNull = intelligentRiskInfoEntityList.stream()
                        .allMatch(entity -> entity.getStep() == null);
                if (!allStepFieldsNull){
                    List<IntelligentRiskInfoEntity> collect = intelligentRiskInfoEntityList.stream()
                            .filter(entity -> entity.getStep() != null && Objects.equals(entity.getStep(), step))
                            .toList();
                    if (CollUtil.isEmpty(collect)){
                        intelligentRiskInfoEntityMapper.insert(
                                new IntelligentRiskInfoEntity()
                                        .setOrderId(orderId)
                                        .setStep(step)
                                        .setType(type)
                                        .setTaskId(resultMap.get("task_id").toString())
                                        .setWorkflowRunId(resultMap.get("workflow_run_id").toString())
                                        .setResResult(JSONUtil.toJsonStr(result))
                        );
                    }
                }
            }
            if (Objects.equals(type,1) && Objects.equals(step,2)){
                RiskAiIntelligentAuditEntity riskAiIntelligentAuditEntity = riskAiIntelligentAuditMapper.selectOne(
                        new LambdaQueryWrapper<RiskAiIntelligentAuditEntity>()
                                .eq(RiskAiIntelligentAuditEntity::getOrderId, orderId)
                                .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
                                .eq(RiskAiIntelligentAuditEntity::getIsAutoAudit,2)
                                .orderByDesc(RiskAiIntelligentAuditEntity::getCreateTime)
                                .last("limit 1")
                );
                if (riskAiIntelligentAuditEntity != null){
                    riskAiIntelligentAuditEntity.setAiReportStatus(1);
                    riskAiIntelligentAuditMapper.updateById(riskAiIntelligentAuditEntity);
                }
            }
//            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
//            if(Objects.equals(type,IntelligentEnum.INTELLIGENT_RISK.getKey()) && Objects.equals(step,1) && !Objects.equals(orderInfoEntity.getCurrentNode(), States.QUALITY_INSPECTION.getNode()) && !Objects.equals(orderInfoEntity.getCurrentNode(), States.BUSINESS_ADDED_INFO.getNode()) && !Objects.equals(orderInfoEntity.getCurrentNode(), States.QUALITY_INSPECTION_FINISH.getNode())){
//                intelligentRisk(orderId, 2, null);
//            }
        } catch (JsonProcessingException e) {
            log.info("IntelligentRiskControlServiceImpl.intelligentRisk.toMap.error:{}", e.getMessage());
            throw new BusinessException("解析失败");
        }
        return true;
    }

    @Override
    public String getIntelligentRiskResultById(Integer  orderId, Integer step, Integer type, String orderNumber) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(
                new LambdaQueryWrapper<OrderInfoEntity>()
                        .eq(ObjUtil.isNotEmpty(orderId),OrderInfoEntity::getId, orderId)
                        .like(StringUtils.isNotEmpty(orderNumber),OrderInfoEntity::getOrderNumber, orderNumber)
                        .eq(OrderInfoEntity::getDeleteFlag, 0)
        );
        if (ObjUtil.isEmpty(orderInfoEntity)){
            throw new BusinessException("订单不存在");
        }
        List<IntelligentRiskInfoEntity> intelligentRiskInfoEntityList = intelligentRiskInfoEntityMapper.selectList(
                new LambdaQueryWrapper<IntelligentRiskInfoEntity>()
                        .eq(IntelligentRiskInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(IntelligentRiskInfoEntity::getDeleteFlag, 0)
                        .eq(IntelligentRiskInfoEntity::getType,type)
                        .orderByDesc(IntelligentRiskInfoEntity::getCreateTime)
                        .last("limit 1")
        );

        if (CollUtil.isNotEmpty(intelligentRiskInfoEntityList)){
            boolean allStepFieldsNull = intelligentRiskInfoEntityList.stream()
                    .allMatch(entity -> entity.getStep() == null);
            if (allStepFieldsNull){
                return intelligentRiskControlFeign.getIntelligentRiskResultById(intelligentRiskInfoEntityList.get(0).getWorkflowRunId());
            }else {
                List<IntelligentRiskInfoEntity> collect = intelligentRiskInfoEntityList.stream()
                        .filter(entity -> entity.getStep() != null && Objects.equals(entity.getStep(), step))
                        .toList();
                if (CollUtil.isNotEmpty(collect)){
                    return intelligentRiskControlFeign.getIntelligentRiskResultById(collect.get(0).getWorkflowRunId());
                }else {
                    throw new BusinessException("正在评估中");
                }
            }
        }else {
            throw new BusinessException("正在评估中");
        }
    }

    @Override
    public Boolean getIntelligentRiskReport(Integer orderId, Integer type) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isEmpty(orderInfoEntity)) {
            throw new BusinessException("订单不存在");
        }
        ObjectMapper objectMapper = new ObjectMapper();
        SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.INTELLIGENT_AI_REPORT_BUTTON);
        Map<String,List<Integer>> map = new HashMap<>();
        if (Objects.equals(switchInfo.getSwitchFlag(),1) && StringUtils.isNotBlank(switchInfo.getValue())){
            try {
                map = objectMapper.readValue(switchInfo.getValue(), new TypeReference<Map<String,List<Integer>>>() {});
            } catch (JsonProcessingException e) {
                return false;
            }
        }else {
            return false;
        }
        if (CollUtil.isEmpty(map)){
            return false;
        }else {
            if (map.containsKey(String.valueOf(type))){
                List<Integer> list = map.get(String.valueOf(type));
                if (CollUtil.isNotEmpty(list) && !list.contains(orderInfoEntity.getFundId())){
                    return false;
                }else if (CollUtil.isEmpty(list)){
                    return false;
                }
            }else {
                return false;
            }
        }
        switch (type){
//            case 1:
//                if (Objects.equals(orderInfoEntity.getCurrentNode(), States.RISK_FIRST_APPROVE.getNode())){
//                    return true;
//                }else {
//                    break;
//                }
            case 2:
                if (Objects.equals(orderInfoEntity.getCurrentNode(), States.PAYMENT_CONTRACT_APPROVAL.getNode())){
                    return true;
                }else {
                    break;
                }
        }
        List<IntelligentRiskInfoEntity> list = intelligentRiskInfoEntityMapper.selectList(
                new LambdaQueryWrapper<IntelligentRiskInfoEntity>()
                        .eq(IntelligentRiskInfoEntity::getOrderId, orderId)
                        .eq(IntelligentRiskInfoEntity::getStep, 2)
                        .eq(IntelligentRiskInfoEntity::getType, type)
                        .eq(IntelligentRiskInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(IntelligentRiskInfoEntity::getCreateTime)
                        .last("limit 1")
        );
        return CollUtil.isNotEmpty(list);

    }

    private Boolean switchFund(OrderInfoEntity orderInfoEntity){
        ObjectMapper objectMapper = new ObjectMapper();
        SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.INTELLIGENT_AI_QUOTA);
        List<Integer> list = new ArrayList<>();
        if (Objects.equals(switchInfo.getSwitchFlag(),1) && StringUtils.isNotBlank(switchInfo.getValue())){
            try {
                list = objectMapper.readValue(switchInfo.getValue(), new TypeReference<List<Integer>>() {});
                if (CollUtil.isNotEmpty(list) && !list.contains(orderInfoEntity.getFundId())){
                    return false;
                }else if (CollUtil.isEmpty(list)){
                    return false;
                }
                return list.contains(orderInfoEntity.getFundId());
            } catch (JsonProcessingException e) {
                return false;
            }
        }else {
            return false;
        }

    }
    @Override
    public Boolean intelligentContract(Integer orderId, Integer step, String orderNumber) {
        if (ObjUtil.isEmpty(orderId) && StringUtils.isEmpty(orderNumber)){
            throw new BusinessException("订单ID和订单编号不能同时为空");
        }

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(ObjUtil.isNotEmpty(orderId),OrderInfoEntity::getId, orderId)
                .like(StringUtils.isNotEmpty(orderNumber),OrderInfoEntity::getOrderNumber, orderNumber));
        if (ObjUtil.isEmpty(orderId)){
            orderId = orderInfoEntity.getId();
        }
//        if(!Objects.equals(orderInfoEntity.getFundId(), 11)){s
//            return true;
//        }
        ObjectMapper objectMapper = new ObjectMapper();
        SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.SMART_CONTRACT_REPORT);
        List<Integer> list = new ArrayList<>();
        if (Objects.equals(switchInfo.getSwitchFlag(),1) && StringUtils.isNotBlank(switchInfo.getValue())){
            try {
                list = objectMapper.readValue(switchInfo.getValue(), new TypeReference<List<Integer>>() {});
            } catch (JsonProcessingException e) {
                return true;
            }
        }else {
            return true;
        }
        if (CollUtil.isNotEmpty(list) && !list.contains(orderInfoEntity.getFundId())){
            return true;
        }else if (CollUtil.isEmpty(list)){
            return true;
        }
        OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectOne(
                new LambdaQueryWrapper<OrderCustomerInfoEntity>()
                        .eq(OrderCustomerInfoEntity::getId, orderInfoEntity.getCustomerId())
        );
        OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(
                new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                        .eq(OrderVehicleInfoEntity::getOrderId, orderInfoEntity.getId())
                        .orderByDesc(OrderVehicleInfoEntity::getId)
                        .last("limit 1"));
        List<OrderNodeRecordEntity> orderNodeRecordEntityList = orderNodeRecordMapper.selectList(
                new LambdaQueryWrapper<OrderNodeRecordEntity>()
                        .eq(OrderNodeRecordEntity::getOrderId, orderInfoEntity.getId())
                        .orderByDesc(OrderNodeRecordEntity::getId));
        AmountCalVO amountCalVO = amountService.calAmount(new AmountCalDTO().setOrderId(orderInfoEntity.getId()));
        CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(
                new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                        .eq(CustomerMortgageInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                        .last("limit 1")
        );
        List<BankAccountSignEntity> bankAccountSignEntityList = bankAccountSignMapper.selectList(
                new LambdaQueryWrapper<BankAccountSignEntity>()
                        .eq(BankAccountSignEntity::getOrderId, orderInfoEntity.getId())
                        .eq(BankAccountSignEntity::getSignState, 1)
                        .eq(BankAccountSignEntity::getDeleteFlag, 0)
        );
        List<OrderContractEntity> orderContractEntityList = orderContractMapper.selectList(
                new LambdaQueryWrapper<OrderContractEntity>()
                        .eq(OrderContractEntity::getOrderId, orderInfoEntity.getId())
                        .eq(OrderContractEntity::getDeleteFlag, 0)
        );
        List<OrderFileEntity> orderFileEntityList = orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                .eq(OrderFileEntity::getOrderId, orderId)
                .eq(OrderFileEntity::getFileId, FileConfigEnums.CALL_DETAIL.getFileId())
                .eq(OrderFileEntity::getDeleteFlag, 0)
        );
        FundProductMappingEntity fundProductMappingEntity = fundProductMappingMapper.selectOne(
                new LambdaQueryWrapper<FundProductMappingEntity>()
                        .eq(FundProductMappingEntity::getFundId, orderInfoEntity.getFundId())
                        .eq(FundProductMappingEntity::getProductId, orderInfoEntity.getProductId())
                        .eq(FundProductMappingEntity::getDeleteFlag, 0)
                        .orderByDesc(FundProductMappingEntity::getCreateTime)
                        .last("limit 1")
        );
        ProductInfoEntity productInfoEntity = productInfoMapper.selectById(orderInfoEntity.getProductId());
        SmartContractReportEntity smartContractReportEntity = smartContractReportMapper.selectOne(
                new LambdaQueryWrapper<SmartContractReportEntity>()
                        .eq(SmartContractReportEntity::getOrderId, orderId)
                        .eq(SmartContractReportEntity::getDeleteFlag, 0)
                        .orderByDesc(SmartContractReportEntity::getId)
                        .last("LIMIT 1")
        );
        IntelligentContractDTO contractDTO = new IntelligentContractDTO();
        IntelligentContractInputsDTO dto =new IntelligentContractInputsDTO();
        dto.setBusinessLic(getFileUrlList(orderInfoEntity.getId(),FileConfigEnums.BUS_LIC));
        dto.setCarLeft(getFileUrl(orderInfoEntity.getId(),FileConfigEnums.LEFT_FRONT_45));
        String Marital = switch (orderCustomerInfoEntity.getMaritalStatus()){
            case 0 -> "未婚";
            case 1 -> "丧偶";
            case 2 -> "已婚有子女";
            case 3 -> "已婚无子女";
            case 4 -> "离异";
            default -> "其他";
        };
        dto.setSoftAmount(String.valueOf(amountCalVO.getSoftReviewAmount()));
        dto.setMarital(Marital);
        dto.setMarryPdf(getFileUrlList(orderInfoEntity.getId(),FileConfigEnums.MARRIAGE_PROOF));
        dto.setPhone(orderInfoEntity.getCustomerPhone());
        dto.setZifangId(orderInfoEntity.getFundId());
        dto.setLastJieDi(ObjUtil.isNotEmpty(orderVehicleInfoEntity.getLastMortgageReleaseDate()) ? orderVehicleInfoEntity.getLastMortgageReleaseDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null);
        if (ObjUtil.isNotNull(smartContractReportEntity)){
            dto.setFirstAmount(smartContractReportEntity.getFirstAmount());
            dto.setRepayAmount(smartContractReportEntity.getRepayAmount());
            dto.setLastRepayAmount(smartContractReportEntity.getLastRepayAmount());
        }else {
            dto.setFirstAmount(null);
            dto.setRepayAmount(null);
            dto.setLastRepayAmount(null);
        }
        if (ObjUtil.isNotNull(orderVehicleInfoEntity.getTransferTimes())){
            dto.setTransNum(orderVehicleInfoEntity.getTransferTimes());
        }else{
            dto.setTransNum(0);
        }
        if(Objects.equals(orderInfoEntity.getFundId(),FundEnum.YING_FENG.getValue())){
            dto.setYfLoanContract(getFileUrlOrPDFList(orderInfoEntity.getId(), FileConfigEnums.ZZJKHTQS));
            dto.setYfMortgageContract(getFileUrlOrPDFList(orderInfoEntity.getId(), FileConfigEnums.ZFDYHTQS));
        }
        dto.setPeriod(productInfoEntity.getTerm());
        dto.setClosePeriod(fundProductMappingEntity.getClosedPeriod());
        try {
            dto.setVideo_url(getVideoFileUrl(orderInfoEntity.getId(), FileConfigEnums.INTERVIEW_VIDEO_77));
        } catch (Exception e) {
            log.info("IntelligentRiskControlServiceImpl.retrieveContract INTERVIEW_VIDEO_77 error:{}", e.getMessage());
        }
        dto.setIsCallLog(CollUtil.isNotEmpty(orderFileEntityList) ? 1 : 2);
        dto.setConfirmAmount(amountCalVO.getCustomerConfirmAmount());
        dto.setCarColor(orderVehicleInfoEntity.getVehicleColor());
        dto.setOrderType(Objects.equals(orderInfoEntity.getOrderType(), 0) ? "消费贷" : "经营贷");
        dto.setDateCompulsory(ObjUtil.isNotNull(orderVehicleInfoEntity.getCompulsoryDate()) ? orderVehicleInfoEntity.getCompulsoryDate().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)) : null);
        dto.setOrderId(orderInfoEntity.getOrderNumber());
        dto.setRegionName(orderInfoEntity.getRegionName());
        dto.setStoreName(orderInfoEntity.getStoreName());
        dto.setApplyTime(orderInfoEntity.getPreApplyTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond());
        dto.setCapitalName(orderInfoEntity.getFundName());
        dto.setProductName(orderInfoEntity.getProductName());
        dto.setIsDianxiao(Objects.equals(orderInfoEntity.getSourceType(), 1) ? "电销" : "非电销");
        dto.setLastNodeName(States.getNode(orderNodeRecordEntityList.get(0).getCurrentNode()).getDesc());
        dto.setLastNodeTime(orderNodeRecordEntityList.get(0).getCreateTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
        dto.setRiskAmount(amountCalVO.getRiskAmount());
        dto.setCarPlate(orderVehicleInfoEntity.getVehicleNumber());
        dto.setEngineNum(orderVehicleInfoEntity.getEngineNumber());
        dto.setCarVin(orderVehicleInfoEntity.getVin());
        dto.setFileCompulsory(getFileUrlList(orderInfoEntity.getId(), FileConfigEnums.COMPULSORY_INSURANCE));
        dto.setFileInsurance(getFileUrlList(orderInfoEntity.getId(), FileConfigEnums.COMMERCIAL_INSURANCE));
        dto.setFileCarCheck(getFileUrlList(orderInfoEntity.getId(), FileConfigEnums.ANNUAL_MARK));
        dto.setFileCardLicense(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.DRV_LIC_HO));
        dto.setFileCardAddAfter(getFileUrlList(orderInfoEntity.getId(), FileConfigEnums.DYDJL));
        dto.setMortgage(Objects.equals(customerMortgageInfoEntity.getMortgageType(),0) ? 2 : 1);
        dto.setPreMortgage(Objects.equals(orderInfoEntity.getAdvanceMortgageState(),2) ? 1 : 2);
        dto.setMortgageStatus((Objects.equals(orderInfoEntity.getMortgageState(),2) || Objects.equals(orderInfoEntity.getMortgageState(),3)) ? "抵押完成" : "其他");
        dto.setIsHaveLvBen(switch (orderInfoEntity.getMortgageState()){
            case 2 -> 2;
            case 3 -> 1;
            default -> 0;
        });
        dto.setName(orderInfoEntity.getCustomerName());
        dto.setIdCard(orderCustomerInfoEntity.getIdNumber());
        dto.setSurchargeCompany(getMortgageChannelName(orderInfoEntity.getFundId()));
        dto.setBuyCar(orderVehicleInfoEntity.getIssueDate().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        dto.setFileContract(getContractFileUrlList(orderContractEntityList));
        Map<Integer, List<BankAccountSignEntity>> groupedByCardType = bankAccountSignEntityList.stream()
                .collect(Collectors.groupingBy(
                        BankAccountSignEntity::getCardType,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                accountSignEntityList -> {
                                    accountSignEntityList.sort(Comparator.comparing(BankAccountSignEntity::getUpdateTime).reversed());
                                    return accountSignEntityList;
                                }
                        )
                ));
        dto.setRepayBankNum(CollUtil.isNotEmpty(groupedByCardType.get(1)) ? groupedByCardType.get(1).get(0).getBankCardNumber() : "");
        dto.setRepayBankName(CollUtil.isNotEmpty(groupedByCardType.get(1)) ? groupedByCardType.get(1).get(0).getBankName() : "");
        dto.setLoanCardNumber(CollUtil.isNotEmpty(groupedByCardType.get(0)) ? groupedByCardType.get(0).get(0).getBankCardNumber() : "");
        dto.setLoanBank(CollUtil.isNotEmpty(groupedByCardType.get(0)) ? groupedByCardType.get(0).get(0).getBankName() : "");
        List<IntelligentContractInputsDTO.ContractInfo> contractInfoList = orderContractEntityList.stream()
                .map(entity -> {
                    int status = Objects.equals(entity.getSignStatus(), 2) ? 1 : 2;
                    String date = entity.getUpdateTime()
                            .format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
                    ResourceOssInfoEntity resourceOssInfoEntity = resourceOssInfoEntityMapper.selectOne(
                            new LambdaQueryWrapper<ResourceOssInfoEntity>()
                                    .eq(ResourceOssInfoEntity::getResourceId, entity.getResource())
                                    .eq(ResourceOssInfoEntity::getDeleteFlag, 0)
                                    .orderByDesc(ResourceOssInfoEntity::getUpdateTime)
                                    .last("limit 1")
                    );
                    String url = "";
                    if (resourceOssInfoEntity != null){
                        url = resourceOssInfoEntity.getFileUrl();
                    }
                    return new IntelligentContractInputsDTO.ContractInfo(entity.getName(), status, date,url);
                })
                .toList();
        dto.setContractList(JSONUtil.toJsonStr(contractInfoList));
        Map<String,Integer> needCheckContract = new HashMap<>();
        switch (orderInfoEntity.getFundId()){
            case 11:
                needCheckContract.put("委托交易合同",1);
                needCheckContract.put("个人贷款合同",1);
                needCheckContract.put("委托扣款授权书",1);
                needCheckContract.put("委托扣款协议",1);
                break;
            case 5:
                needCheckContract.put("委托交易合同",1);
                needCheckContract.put("账户委托扣款授权书",1);
                needCheckContract.put("个人借款合同",1);
                needCheckContract.put("委托扣款授权书",1);
                break;
            case 15:
                needCheckContract.put("委托交易合同",1);
                needCheckContract.put("车辆融资租赁合同",1);
                needCheckContract.put("委托交易授权书",1);
                needCheckContract.put("委托扣款授权书",1);
                break;
        }
        dto.setNeedCheckContract(JSONUtil.toJsonStr(needCheckContract));
        String riskRemark = orderNodeRecordEntityList.stream()
                .filter(entity -> entity.getCurrentNode().equals(States.RISK_FIRST_APPROVE.getNode()))
                .filter(entity -> (Objects.equals(entity.getEvent(), Events.AGREES) || Objects.equals(entity.getEvent(), Events.AGREES_RISK_FIRST_SINGLE_AGREES)))
                .map(OrderNodeRecordEntity::getRemarkExternal)
                .findFirst()
                .orElse(null);
        dto.setRiskRemark(StringUtils.isNotBlank(riskRemark) ? riskRemark : "");
        String carEvaluateTime = switch (ObjUtil.isNotNull(orderVehicleInfoEntity.getCarReportType()) ? orderVehicleInfoEntity.getCarReportType() :1){
            case 1 -> {
                LanBenVehicleDataEntity lanBenVehicleDataEntity = lanBenVehicleDataMapper.selectOne(
                        new LambdaQueryWrapper<LanBenVehicleDataEntity>()
                                .eq(LanBenVehicleDataEntity::getVin, orderVehicleInfoEntity.getVin())
                                .eq(LanBenVehicleDataEntity::getDeleteFlag, 0)
                                .orderByDesc(LanBenVehicleDataEntity::getUpdateTime)
                                .last("limit 1")
                );
                if(lanBenVehicleDataEntity != null){
                    yield lanBenVehicleDataEntity.getUpdateTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
                }else {
                    yield null;
                }
            }
            case 2 -> {
                JzgVehicleDataEntity jzgVehicleDataEntity = jzgVehicleDataMapper.selectOne(
                        new LambdaQueryWrapper<JzgVehicleDataEntity>()
                                .eq(JzgVehicleDataEntity::getVin, orderVehicleInfoEntity.getVin())
                                .eq(JzgVehicleDataEntity::getDeleteFlag, 0)
                                .orderByDesc(JzgVehicleDataEntity::getUpdateTime)
                                .last("limit 1")
                );
                if(jzgVehicleDataEntity != null){
                    yield jzgVehicleDataEntity.getUpdateTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
                }else {
                    yield null;
                }
            }
            default -> null;
        };
        dto.setCarEvaluateTime(ObjUtil.isNotEmpty(orderVehicleInfoEntity.getAppraiseTime()) ? orderVehicleInfoEntity.getAppraiseTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : carEvaluateTime);
//        dto.setSignVideo();
//        if (!Objects.equals(orderInfoEntity.getFundId(),FundEnum.YING_FENG.getValue()) && Objects.equals(orderInfoEntity.getRegionId(),56) && Objects.equals(orderInfoEntity.getSourceType(), 1)){
//            log.info("IntelligentRiskControlServiceImpl.intelligentRisk orderId:{}",orderInfoEntity.getId());
//        }else {
//            dto.setFileUserAndCar(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.MOTOR_VEHICLE_GROUP_PHOTO));s
//        }

        try {
            if(!Objects.equals(orderInfoEntity.getFundId(),FundEnum.ZHONG_HENG_TONG_HUI.getValue())){
                if (!Objects.equals(orderInfoEntity.getFundId(),FundEnum.YING_FENG.getValue()) && Objects.equals(orderInfoEntity.getRegionId(),56) && Objects.equals(orderInfoEntity.getSourceType(), 1)){
                    log.info("IntelligentRiskControlServiceImpl.intelligentRisk orderId:{}",orderInfoEntity.getId());
                }else {
                    dto.setFileUserAndCar(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.MOTOR_VEHICLE_GROUP_PHOTO));
                }
            }
        } catch (Exception e) {
            log.info("IntelligentRiskControlServiceImpl.intelligentRisk MOTOR_VEHICLE_GROUP_PHOTO error:{}",e.getMessage());
        }

//        dto.setFileUserAndCar(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.MOTOR_VEHICLE_GROUP_PHOTO));
        dto.setFileLive(getFileUrl(orderInfoEntity.getId(), FileConfigEnums.FACE_RECOGNITION));

        dto.setFileCarKey(getFileUrlList(orderInfoEntity.getId(), FileConfigEnums.CYS));
        contractDTO.setResponse_mode("blocking");
        if (Objects.equals(step,2)){
            LocalDateTime localDateTime = orderNodeRecordEntityList.stream()
                    .filter(entity -> entity.getCurrentNode().equals(States.PAYMENT_APPLY_INFORMATION.getNode()))
                    .filter(entity -> Objects.equals(entity.getEvent(), Events.AGREES))
                    .map(OrderNodeRecordEntity::getCreateTime)
                    .findFirst()
                    .orElse(null);
            dto.setSubPayTime(ObjUtil.isNotNull(localDateTime) ? localDateTime.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)) : "");
            IntelligentRiskInfoEntity intelligentRiskInfoEntity = intelligentRiskInfoEntityMapper.selectOne(
                    new LambdaQueryWrapper<IntelligentRiskInfoEntity>()
                            .eq(IntelligentRiskInfoEntity::getOrderId, orderInfoEntity.getId())
                            .eq(IntelligentRiskInfoEntity::getStep, 1)
                            .eq(IntelligentRiskInfoEntity::getType, IntelligentEnum.INTELLIGENT_CONTRACT.getKey())
                            .eq(IntelligentRiskInfoEntity::getDeleteFlag, 0)
                            .orderByDesc(IntelligentRiskInfoEntity::getCreateTime)
                            .last("limit 1")
            );
            if (intelligentRiskInfoEntity != null){
                if (envUtil.isPrd()){
                    dto.setReturnUrl("https://guanjia.longhuanhuifeng.com/service/order/api/v1/contract/intelligentRiskCallback?orderId="+orderInfoEntity.getId()+"&step="+step + "&type=2");
                    contractDTO.setUser("yunqi_contract_prd");
                }else {
                    dto.setReturnUrl("https://car-sit.longjintech.com/service/order/api/v1/contract/intelligentRiskCallback?orderId=" + orderInfoEntity.getId() + "&step=" + step + "&type=2");
                    contractDTO.setUser("yunqi_contract_dev");
                }
                dto.setFirstRunId(intelligentRiskInfoEntity.getWorkflowRunId());
                contractDTO.setInputs(dto);
                if (envUtil.isPrd()){

                    switch (orderInfoEntity.getFundId()){
                        case 11:   //富民生产第二阶段
                            retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-zZXbhBXdKN2NIObybrY2VKm7",step);
                            break;
                        case 5:   //盈峰生产第二阶段
                            retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-VISSnJUI6Ra69zTowv2ZjOZU",step);
                            break;
                        case 15:   //通汇生产第二阶段
                            retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-zZXbhBXdKN2NIObybrY2VKm7",step);
                            break;
                        case 3:   //蓝海生产第二阶段
                            retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-zZXbhBXdKN2NIObybrY2VKm7",step);
                            break;
                    }
                }else {
                    switch (orderInfoEntity.getFundId()){
                        case 11:   //富民测试第二阶段
                            retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-YFYoeO9B7B9WKwjvn9tg4MsG",step);
                            break;
                        case 5:   //盈峰测试第二阶段
                            retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-EvqTogFbePEFKB6whNn3GboE",step);
                            break;
                        case 15:   //通汇测试第二阶段
                            retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-YFYoeO9B7B9WKwjvn9tg4MsG",step);
                            break;
                        case 3:   //蓝海测试第二阶段
                            retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-YFYoeO9B7B9WKwjvn9tg4MsG",step);
                            break;
                    }
                }
            }
//            else {
//                if (envUtil.isPrd()){
//                    dto.setReturnUrl("https://guanjia.longhuanhuifeng.com/service/order/api/v1/contract/intelligentRiskCallback?orderId="+orderInfoEntity.getId()+"&step=1&type=2");
//                    contractDTO.setUser("yunqi_contract_prd");
//                }else {
//                    dto.setReturnUrl("https://car-sit.longjintech.com/service/order/api/v1/contract/intelligentRiskCallback?orderId=" + orderInfoEntity.getId() + "&step=1&type=2");
//                    contractDTO.setUser("yunqi_contract_dev");
//                }
//                contractDTO.setInputs(dto);
//                if (envUtil.isPrd()){
//                    switch (orderInfoEntity.getFundId()){
//                        case 11:   //富民生产第一阶段
//                            retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-9QAL9Z7QsXs6cQpVhCHP4Tro",1);
//                            break;
//                        case 5:   //盈峰生产第一阶段
//                            retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-P4UibGCzKVaBXHessVjdzwnM",1);
//                            break;
//                    }
//
//                }else {
//                    switch (orderInfoEntity.getFundId()){
//                        case 11:   //富民生产第一阶段
//                            retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-I2INJ9AWNwRUEkaauB2ajLkh",1);
//                            break;
//                        case 5:   //盈峰生产第一阶段
//                            retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-lojTCJjsUpx2lTAR8820QNGI",1);
//                            break;
//                    }
//
//                }
//
//            }
        }else {
            if (envUtil.isPrd()){
                dto.setReturnUrl("https://guanjia.longhuanhuifeng.com/service/order/api/v1/contract/intelligentRiskCallback?orderId="+orderInfoEntity.getId()+"&step="+step + "&type=2");
                contractDTO.setUser("yunqi_contract_prd");
            }else {
                dto.setReturnUrl("https://car-sit.longjintech.com/service/order/api/v1/contract/intelligentRiskCallback?orderId=" + orderInfoEntity.getId()+"&step="+step + "&type=2");
                contractDTO.setUser("yunqi_contract_dev");
            }
            contractDTO.setInputs(dto);
            if (envUtil.isPrd()){
                switch (orderInfoEntity.getFundId()){
                    case 11:   //富民生产第一阶段
                        retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-9QAL9Z7QsXs6cQpVhCHP4Tro",step);
                        break;
                    case 5:   //盈峰生产第一阶段
                        retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-P4UibGCzKVaBXHessVjdzwnM",step);
                        break;
                    case 15:   //通汇生产第一阶段
                        retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-9QAL9Z7QsXs6cQpVhCHP4Tro",step);
                        break;
                    case 3:   //蓝海生产第一阶段
                        retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-9QAL9Z7QsXs6cQpVhCHP4Tro",step);
                        break;
                }

            }else {
                switch (orderInfoEntity.getFundId()){
                    case 11:   //富民测试第一阶段
                        retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-I2INJ9AWNwRUEkaauB2ajLkh",step);
                        break;
                    case 5:   //盈峰测试第一阶段
                        retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-lojTCJjsUpx2lTAR8820QNGI",step);
                        break;
                    case 15:   //通汇测试第一阶段
                        retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-I2INJ9AWNwRUEkaauB2ajLkh",step);
                        break;
                    case 3:   //蓝海测试第一阶段
                        retrieveContract(orderInfoEntity.getId(),contractDTO,"Bearer app-I2INJ9AWNwRUEkaauB2ajLkh",step);
                        break;
                }
            }

        }

        return null;
    }

    @Override
    public Boolean getIntelligentContractFirstStageTask() {
        ObjectMapper objectMapper = new ObjectMapper();
        SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.SMART_CONTRACT_REPORT);
        List<Integer> list = new ArrayList<>();
        if (Objects.equals(switchInfo.getSwitchFlag(),1) && StringUtils.isNotBlank(switchInfo.getValue())){
            try {
                list = objectMapper.readValue(switchInfo.getValue(), new TypeReference<List<Integer>>() {});
            } catch (JsonProcessingException e) {
                return true;
            }
        }else {
            return true;
        }
        if (CollUtil.isEmpty(list)){
            return true;
        }
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(
                new LambdaQueryWrapper<OrderInfoEntity>()
                        .eq(OrderInfoEntity::getCurrentNode, States.PAYMENT_APPLY_INFORMATION.getNode())
                        .eq(OrderInfoEntity::getDeleteFlag, 0)
                        .in(OrderInfoEntity::getFundId, list)
                        .orderByDesc(OrderInfoEntity::getUpdateTime)
                        .last("limit 10")
        );
        if (CollUtil.isNotEmpty(orderInfoEntityList)){
            orderInfoEntityList.forEach(orderInfoEntity -> {
//                List<OrderNodeRecordEntity> orderNodeRecordEntityList = orderNodeRecordMapper.selectList(
//                        new LambdaQueryWrapper<OrderNodeRecordEntity>()
//                                .eq(OrderNodeRecordEntity::getOrderId, orderInfoEntity.getId())
//                                .eq(OrderNodeRecordEntity::getCurrentNode, States.PAYMENT_CONTRACT_APPROVAL.getNode())
//                                .eq(OrderNodeRecordEntity::getEvent, Events.BACK.getCode())
//                                .eq(OrderNodeRecordEntity::getDeleteFlag, 0)
//                );
//                if (CollUtil.isEmpty(orderNodeRecordEntityList)){
                    Long count = intelligentRiskInfoEntityMapper.selectCount(
                            new LambdaQueryWrapper<IntelligentRiskInfoEntity>()
                                    .eq(IntelligentRiskInfoEntity::getOrderId, orderInfoEntity.getId())
                                    .eq(IntelligentRiskInfoEntity::getStep, 1)
                                    .eq(IntelligentRiskInfoEntity::getType, IntelligentEnum.INTELLIGENT_CONTRACT.getKey())
                                    .eq(IntelligentRiskInfoEntity::getDeleteFlag, 0)
                    );
                    if (Objects.equals(count, 0L)){
                        log.info("IntelligentRiskControlServiceImpl.getIntelligentContractFirstStageTask start order:{}",JSONUtil.toJsonStr(orderInfoEntity));
                        try {
                            intelligentContract(orderInfoEntity.getId(), 1, orderInfoEntity.getOrderNumber());
                        } catch (Exception e) {
                            log.info("IntelligentRiskControlServiceImpl.getIntelligentContractFirstStageTask.error:{}", e.getMessage());
                        }
                    }
//                }
            });
        }
        return true;
    }

    /**
     * 获取加押公司名称
     * @param fundId
     * @return
     */
    private static @Nullable String getMortgageChannelName(Integer fundId) {
        String mortgageChannelName = null;
        if (Objects.equals(fundId, FundEnum.YING_FENG.getValue())) {
            mortgageChannelName = "广东盈峰普惠互联小额贷款股份有限公司";

        } else if (Objects.equals(fundId, FundEnum.FU_MIN.getValue())) {
            mortgageChannelName = "华科中星融资租赁(重庆)有限公司";

        } else if(Objects.equals(fundId, FundEnum.ZHONG_HENG_TONG_HUI.getValue()) || Objects.equals(fundId, FundEnum.ZHONG_HENG.getValue()) || Objects.equals(fundId, FundEnum.CHANG_YIN.getValue())){
            mortgageChannelName = "中恒信合(厦门)融资租赁有限公司";
        } else if (Objects.equals(fundId, FundEnum.LAN_HAI.getValue())) {
            mortgageChannelName = "威海蓝海银行股份有限公司";
        }

        return mortgageChannelName;
    }
    private InputsDTO.FileUrlDTO getCarReport(Integer orderId, OrderVehicleInfoEntity entity) {
        InputsDTO.FileUrlDTO fileIdCard = new InputsDTO.FileUrlDTO();
        if (entity.getCarReportType() == 1){
            LanBenVehicleDataEntity lanBenVehicleDataEntity = lanBenVehicleDataMapper.selectOne(new LambdaQueryWrapper<LanBenVehicleDataEntity>().eq(LanBenVehicleDataEntity::getVin, entity.getVin())
                    .eq(LanBenVehicleDataEntity::getDeleteFlag, 0).orderByDesc(LanBenVehicleDataEntity::getCreateTime).last("limit 1"));
            log.info("IntelligentRiskControlServiceImpl.getCarReport.lanBenVehicleDataEntity:{}", JSONUtil.toJsonStr(lanBenVehicleDataEntity));
            if (lanBenVehicleDataEntity != null) {
                fileIdCard.setType("document");
                String url = uploadResourceToOss(lanBenVehicleDataEntity.getReportPdf());
                log.info("IntelligentRiskControlServiceImpl.getCarReport url:{}", JSONUtil.toJsonStr(url));
                fileIdCard.setUrl(url);
            }
        }else {
            JzgVehicleDataEntity jzgVehicleDataEntity = jzgVehicleDataMapper.selectOne(new LambdaQueryWrapper<JzgVehicleDataEntity>().eq(JzgVehicleDataEntity::getVin, entity.getVin())
                    .eq(JzgVehicleDataEntity::getDeleteFlag, 0).orderByDesc(JzgVehicleDataEntity::getCreateTime).last("limit 1"));
            log.info("IntelligentRiskControlServiceImpl.getCarReport.jzgVehicleDataEntity:{}", JSONUtil.toJsonStr(jzgVehicleDataEntity));
            if (jzgVehicleDataEntity != null) {
                fileIdCard.setType("document");
                String url = uploadResourceToOss(jzgVehicleDataEntity.getReportPdf());
                log.info("IntelligentRiskControlServiceImpl.getCarReport jzgVehicleDataEntity url:{}", JSONUtil.toJsonStr(url));
                fileIdCard.setUrl(url);
            }
        }

        return fileIdCard;
    }
    private List<InputsDTO.FileUrlDTO> getCarReportList(Integer id,OrderVehicleInfoEntity entity) {

        List<InputsDTO.FileUrlDTO> fileUrlList = new ArrayList<>();
        String resourceId = "";
        if (Objects.equals(entity.getCarReportType(), 1)){
            LanBenVehicleDataEntity lanBenVehicleDataEntity = lanBenVehicleDataMapper.selectOne(new LambdaQueryWrapper<LanBenVehicleDataEntity>().eq(LanBenVehicleDataEntity::getVin, entity.getVin())
                    .eq(LanBenVehicleDataEntity::getDeleteFlag, 0).orderByDesc(LanBenVehicleDataEntity::getCreateTime).last("limit 1"));
            resourceId = lanBenVehicleDataEntity.getReportPdf();
        }else {
            JzgVehicleDataEntity jzgVehicleDataEntity = jzgVehicleDataMapper.selectOne(
                    new LambdaQueryWrapper<JzgVehicleDataEntity>()
                            .eq(JzgVehicleDataEntity::getVin, entity.getVin())
                            .eq(JzgVehicleDataEntity::getDeleteFlag, 0)
                            .orderByDesc(JzgVehicleDataEntity::getUpdateTime)
                            .last("limit 1")
            );
            resourceId = jzgVehicleDataEntity.getReportPdf();
        }
        if (StringUtils.isNotEmpty(resourceId)) {
            List<ResourceOssInfoEntity> resourceOssInfoEntityList = resourceOssInfoEntityMapper.selectList(
                    new LambdaQueryWrapper<ResourceOssInfoEntity>()
                            .eq(ResourceOssInfoEntity::getResourceId, resourceId+"-Image")
                            .eq(ResourceOssInfoEntity::getDeleteFlag, 0)
                            .orderByAsc(ResourceOssInfoEntity::getCreateTime)
            );
            if (ObjUtil.isNotEmpty(resourceOssInfoEntityList)){
                List<String> list = resourceOssInfoEntityList.stream()
                        .map(ResourceOssInfoEntity::getFileUrl)
                        .filter(StringUtils::isNotEmpty)
                        .toList();
                if (CollUtil.isNotEmpty(list)){
                    for (String url : list) {
                        InputsDTO.FileUrlDTO fileIdCard = new InputsDTO.FileUrlDTO();
                        fileIdCard.setUrl(url);
                        fileUrlList.add(fileIdCard);
                    }
                }
            }else {
                try {
                    ResponseEntity<byte[]> responseEntity = resourceFeign.fileContent(resourceId);
                    List<byte[]> bytes = convertPdfToImageBytes(responseEntity.getBody());
                    List<String> list = uploadResourcePDFToOss(resourceId+"-Image",bytes);
                    if (CollUtil.isNotEmpty(list)){
                        for (String url : list) {
                            InputsDTO.FileUrlDTO fileIdCard = new InputsDTO.FileUrlDTO();
                            fileIdCard.setUrl(url);
                            fileUrlList.add(fileIdCard);
                        }
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }

            }
        }
        log.info("IntelligentRiskControlServiceImpl.getCarReportList.fileUrlList:{}", JSONUtil.toJsonStr(fileUrlList));
        return fileUrlList;
    }
    private List<InputsDTO.FileUrlDTO> getFileUrlList(Integer orderId, FileConfigEnums... fileConfigEnums) {
        log.info("IntelligentRiskControlServiceImpl.getFileUrlList.fileConfigEnums:{}", JSONUtil.toJsonStr(fileConfigEnums));
        List<Integer> fileIds = Arrays.stream(fileConfigEnums).map(FileConfigEnums::getFileId).toList();
        return orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                .eq(OrderFileEntity::getOrderId, orderId)
                .in(OrderFileEntity::getFileId, fileIds)
                .eq(OrderFileEntity::getDeleteFlag, 0)
                .orderByAsc(OrderFileEntity::getFileId)
        ).stream().filter(item -> isImageFile(item.getResourceName()))
                .map(entity -> {
            InputsDTO.FileUrlDTO fileIdCard = new InputsDTO.FileUrlDTO();
            try {
                String url = uploadResourceToOss(entity.getResourceId());
                log.info("IntelligentRiskControlServiceImpl.getFileUrlList url:{}", JSONUtil.toJsonStr(url));
                fileIdCard.setUrl(url);
            } catch (Exception e) {
                log.info("IntelligentRiskControlServiceImpl.getFileUrlList error:{}", fileConfigEnums[0].getCode() + "文件不存在");
            }
            return fileIdCard;
        }).toList();
    }

    private List<InputsDTO.FileUrlDTO> getFileUrlOrPDFList(Integer orderId, FileConfigEnums... fileConfigEnums) {
        log.info("IntelligentRiskControlServiceImpl.getFileUrlOrPDFList.fileConfigEnums:{}", JSONUtil.toJsonStr(fileConfigEnums));
        List<InputsDTO.FileUrlDTO> list = new ArrayList<>();
        List<Integer> fileIds = Arrays.stream(fileConfigEnums).map(FileConfigEnums::getFileId).toList();
        List<OrderFileEntity> orderFileEntityList = orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                .eq(OrderFileEntity::getOrderId, orderId)
                .in(OrderFileEntity::getFileId, fileIds)
                .eq(OrderFileEntity::getDeleteFlag, 0)
                .orderByAsc(OrderFileEntity::getFileId)
        );
        List<InputsDTO.FileUrlDTO> imageUrlList = orderFileEntityList.stream().filter(item -> isImageFile(item.getResourceName()))
                .map(entity -> {
                    InputsDTO.FileUrlDTO fileIdCard = new InputsDTO.FileUrlDTO();
                    try {
                        String url = uploadResourceToOss(entity.getResourceId());
                        log.info("IntelligentRiskControlServiceImpl.getFileUrlOrPDFList url:{}", JSONUtil.toJsonStr(url));
                        fileIdCard.setUrl(url);
                    } catch (Exception e) {
                        log.info("IntelligentRiskControlServiceImpl.getFileUrlOrPDFList error:{}", fileConfigEnums[0].getCode() + "文件不存在");
                    }
                    return fileIdCard;
                }).toList();
        List<InputsDTO.FileUrlDTO> pdfUrlList = orderFileEntityList.stream().filter(item -> !isImageFile(item.getResourceName()))
                .map(entity -> {
                    InputsDTO.FileUrlDTO fileIdCard = new InputsDTO.FileUrlDTO();
                    try {
                        String url = uploadResourceToOss(entity.getResourceId());
                        log.info("IntelligentRiskControlServiceImpl.getFileUrlOrPDFList url:{}", JSONUtil.toJsonStr(url));
                        fileIdCard.setType("document");
                        fileIdCard.setUrl(url);
                    } catch (Exception e) {
                        log.info("IntelligentRiskControlServiceImpl.getFileUrlOrPDFList error:{}", fileConfigEnums[0].getCode() + "文件不存在");
                    }
                    return fileIdCard;
                }).toList();
        if (CollUtil.isNotEmpty(imageUrlList)){
            list.addAll(imageUrlList);
        }
        if (CollUtil.isNotEmpty(pdfUrlList)){
            list.addAll(pdfUrlList);
        }
        return list;
    }
    private List<InputsDTO.FileUrlDTO> getVideoFileUrlList(Integer orderId, FileConfigEnums... fileConfigEnums) {
        log.info("IntelligentRiskControlServiceImpl.getVideoFileUrlList fileConfigEnums:{}", JSONUtil.toJsonStr(fileConfigEnums));
        List<Integer> fileIds = Arrays.stream(fileConfigEnums).map(FileConfigEnums::getFileId).toList();
        return orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                        .eq(OrderFileEntity::getOrderId, orderId)
                        .in(OrderFileEntity::getFileId, fileIds)
                        .eq(OrderFileEntity::getDeleteFlag, 0)
                        .orderByAsc(OrderFileEntity::getFileId)
                ).stream().filter(item -> isImageFile(item.getResourceName()))
                .map(entity -> {
                    InputsDTO.FileUrlDTO fileIdCard = new InputsDTO.FileUrlDTO();
                    try {
                        Result<FileInfoVO> fileInfoVOResult = resourceFeign.fileInfo(entity.getResourceId());
                        if (!Result.isSuccess(fileInfoVOResult)) {
                            throw new BusinessException(fileInfoVOResult.getMsg());
                        }
                        if (fileInfoVOResult.getData().getFileType().equals("mp4")){
                            fileIdCard.setType("video");
                        }
                        if (fileInfoVOResult.getData().getFileType().equals("mp3")){
                            fileIdCard.setType("audio");
                        }
                        if (isImageFile(fileInfoVOResult.getData().getResourceName())){
                            fileIdCard.setType("image");
                        }
                    } catch (BusinessException e) {
                        log.info("IntelligentRiskControlServiceImpl.getVideoFileUrlList fileInfo error:{}",e.getMessage());
                    }
                    try {
                        String url = uploadResourceToOss(entity.getResourceId());
                        // 填写Byte数组。
                        log.info("IntelligentRiskControlServiceImpl.getVideoFileUrlList url:{}", JSONUtil.toJsonStr(url));
                        fileIdCard.setUrl(url);
                    } catch (Exception e) {
                        log.info("IntelligentRiskControlServiceImpl.getVideoFileUrlList error:{}", fileConfigEnums[0].getCode() + "文件不存在");
                    }
                    return fileIdCard;
                }).toList();
    }
    private InputsDTO.FileUrlDTO getVideoFileUrl(Integer orderId, FileConfigEnums fileConfigEnums) {
        log.info("IntelligentRiskControlServiceImpl.getFileUrl.fileConfigEnums:{}", JSONUtil.toJsonStr(fileConfigEnums));
        InputsDTO.FileUrlDTO fileIdCard = new InputsDTO.FileUrlDTO();
        int fileId = fileConfigEnums.getFileId();
        orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                        .eq(OrderFileEntity::getOrderId, orderId)
                        .eq(OrderFileEntity::getFileId, fileId)
                        .eq(OrderFileEntity::getDeleteFlag, 0)
                ).stream()
                .findFirst().ifPresent(item -> {
                    try {
                        try {
                            Result<FileInfoVO> fileInfoVOResult = resourceFeign.fileInfo(item.getResourceId());
                            if (!Result.isSuccess(fileInfoVOResult)) {
                                throw new BusinessException(fileInfoVOResult.getMsg());
                            }
                            if (fileInfoVOResult.getData().getFileType().equals("mp4")){
                                fileIdCard.setType("video");
                            }
                            if (fileInfoVOResult.getData().getFileType().equals("mp3")){
                                fileIdCard.setType("audio");
                            }
                            if (isImageFile(fileInfoVOResult.getData().getResourceName())){
                                fileIdCard.setType("image");
                            }
                        } catch (BusinessException e) {
                            log.info("IntelligentRiskControlServiceImpl.getVideoFileUrlList fileInfo error:{}",e.getMessage());
                        }
                        String url = uploadResourceToOss(item.getResourceId());
                        log.info("IntelligentRiskControlServiceImpl.getVideoFileUrlList url:{}", JSONUtil.toJsonStr(url));
                        fileIdCard.setUrl(url);

                    } catch (Exception e) {
                        log.info("IntelligentRiskControlServiceImpl.getVideoFileUrlList error:{}", e.getMessage());
                        log.info("IntelligentRiskControlServiceImpl.getVideoFileUrlList error:{}", fileConfigEnums.getCode() + "文件不存在");
                    }
                });

        if (fileIdCard.getUrl() == null) {
            throw new BusinessException(fileConfigEnums.getCode() + "文件不存在");
        }

        return fileIdCard;
    }
    private InputsDTO.FileUrlDTO getFileUrl(Integer orderId, FileConfigEnums fileConfigEnums) {
        log.info("IntelligentRiskControlServiceImpl.getFileUrl.fileConfigEnums:{}", JSONUtil.toJsonStr(fileConfigEnums));
        InputsDTO.FileUrlDTO fileIdCard = new InputsDTO.FileUrlDTO();
        int fileId = fileConfigEnums.getFileId();
        orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                .eq(OrderFileEntity::getOrderId, orderId)
                .eq(OrderFileEntity::getFileId, fileId)
                .eq(OrderFileEntity::getDeleteFlag, 0)
        ).stream().filter(item -> isImageFile(item.getResourceName()))
                .findFirst().ifPresent(item -> {
            try {
                String url = uploadResourceToOss(item.getResourceId());
                log.info("IntelligentRiskControlServiceImpl.getFileUrl url:{}", JSONUtil.toJsonStr(url));
                fileIdCard.setUrl(url);

            } catch (Exception e) {
                log.info("IntelligentRiskControlServiceImpl.getFileUrl error:{}", fileConfigEnums.getCode() + "文件不存在");
            }
        });

        if (fileIdCard.getUrl() == null) {
            throw new BusinessException(fileConfigEnums.getCode() + "文件不存在");
        }

        return fileIdCard;
    }

    private String uploadResourceToOss(String resourceId) {
        if (StringUtils.isEmpty(resourceId)){
            return null;
        }
        ResourceOssInfoEntity resourceOssInfoEntity = resourceOssInfoEntityMapper.selectOne(
                new LambdaQueryWrapper<ResourceOssInfoEntity>()
                        .eq(ResourceOssInfoEntity::getResourceId, resourceId)
                        .eq(ResourceOssInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(ResourceOssInfoEntity::getCreateTime)
                        .last("limit 1")
        );
        if (ObjUtil.isNotEmpty(resourceOssInfoEntity)){
            return resourceOssInfoEntity.getFileUrl();
        }
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            // 填写Byte数组。
            Result<FileInfoVO> fileInfoVOResult = resourceFeign.fileInfo(resourceId);
            if (!Result.isSuccess(fileInfoVOResult)) {
                throw new BusinessException(fileInfoVOResult.getMsg());
            }
            String fileType = fileInfoVOResult.getData().getFileType();
            ResponseEntity<byte[]> responseEntity = resourceFeign.fileContent(resourceId);
            byte[] content = responseEntity.getBody();
            long size = 0 ;
            if (content != null){
                size = content.length;
            }
            String objectName = bucketPath + "/lj/" + LocalDate.now().format(DatePattern.PURE_DATE_FORMATTER) + "/" + resourceId + "." + fileType;

            PutObjectResult result = ossClient.putObject(bucketName, objectName, new ByteArrayInputStream(content));
            // 生成以GET方法访问的签名URL。本示例没有额外请求头，其他人可以直接通过浏览器访问相关内容。
            String url = ossClient.generatePresignedUrl(bucketName, objectName, new Date(System.currentTimeMillis() + 3600 * 1000)).toString();
            resourceOssInfoEntityMapper.insert(
                    new ResourceOssInfoEntity()
                            .setResourceId(resourceId)
                            .setObjectName(objectName)
                            .setFileUrl(url.split("\\?")[0])
                            .setFileSize(String.valueOf(Convert.toInt(size)))
            );
            return url.split("\\?")[0];
        } catch (Exception e) {
            log.info("IntelligentRiskControlServiceImpl.uploadResourceToOss error:{}",e.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }
    private List<String> uploadResourcePDFToOss(String resourceId,List<byte[]> bytes) {
        if (StringUtils.isEmpty(resourceId)){
            return null;
        }
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            List<String> list = new ArrayList<>();
            for (byte[] aByte : bytes) {
                long size = 0 ;
                if (aByte != null){
                    size = aByte.length;
                }
                String objectName = bucketPath + "/lj/" + LocalDate.now().format(DatePattern.PURE_DATE_FORMATTER) + "/" + UUID.randomUUID() + ".png";

                PutObjectResult result = ossClient.putObject(bucketName, objectName, new ByteArrayInputStream(aByte));
                // 生成以GET方法访问的签名URL。本示例没有额外请求头，其他人可以直接通过浏览器访问相关内容。
                String url = ossClient.generatePresignedUrl(bucketName, objectName, new Date(System.currentTimeMillis() + 3600 * 1000)).toString();
                resourceOssInfoEntityMapper.insert(
                        new ResourceOssInfoEntity()
                                .setResourceId(resourceId)
                                .setObjectName(objectName)
                                .setFileUrl(url.split("\\?")[0])
                                .setFileSize(String.valueOf(Convert.toInt(size)))
                );
                list.add(url.split("\\?")[0]);
            }
            return list;
        } catch (
                OSSException oe) {
            log.info("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.info("Error Message:{}", oe.getErrorMessage());
            log.info("Error Code:{}", oe.getErrorCode());
            log.info("Request ID:{}", oe.getRequestId());
            log.info("Host ID:{}", oe.getHostId());
            log.error("Error Message: {}", oe.getMessage(), oe);
        } catch (
                ClientException ce) {
            log.info("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message: {}", ce.getMessage(), ce);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }


        return null;
    }
    public boolean isImageFile(String resourceName) {
        if (resourceName == null || resourceName.isEmpty()) {
            return false;
        }

        // 获取文件扩展名
        String extension = resourceName.substring(resourceName.lastIndexOf(".") + 1).toLowerCase();

        // 判断扩展名是否为常见图片格式
        return extension.matches("png|jpg|jpeg|gif|bmp|webp");
    }
    public static List<byte[]> convertPdfToImageBytes(byte[] pdfBytes) throws IOException {
        List<byte[]> imageBytesList = new ArrayList<>();

        try (PDDocument document = PDDocument.load(pdfBytes)) {
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            System.out.println("PDF 总页数: " + document.getNumberOfPages());
            for (int page = 0; page < document.getNumberOfPages(); ++page) {
                BufferedImage bim = pdfRenderer.renderImageWithDPI(page, 150, ImageType.RGB);

                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(bim, "png", baos);
                imageBytesList.add(baos.toByteArray());
            }
        }
        System.out.println("imageBytesList.size() = " + imageBytesList.size());
        return imageBytesList;
    }
    public static byte[] convertPdfToByteArray(String pdfUrl) throws Exception {
        try (InputStream inputStream = new URL(pdfUrl).openStream();
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }

            return byteArrayOutputStream.toByteArray();
        }
    }

    private List<InputsDTO.FileUrlDTO> getContractFileUrlList(List<OrderContractEntity> contractEntityList) {
        log.info("IntelligentRiskControlServiceImpl.getContractFileUrlList contractEntityList:{}", JSONUtil.toJsonStr(contractEntityList));
        return contractEntityList.stream()
                .map(entity -> {
                    InputsDTO.FileUrlDTO fileIdCard = new InputsDTO.FileUrlDTO();
                    try {
                        String url = uploadResourceToOss(entity.getResource());
                        log.info("IntelligentRiskControlServiceImpl.getContractFileUrlList url:{}", JSONUtil.toJsonStr(url));
                        fileIdCard.setType("document");
                        fileIdCard.setUrl(url);
                    } catch (Exception e) {
                        throw new BusinessException( entity.getName()+ "文件不存在");
                    }
                    return fileIdCard;
                }).toList();
    }

    private void retrieveRisk(Integer orderId,IntelligentRiskDTO intelligentRiskDTO,String apiId,Integer step){
        log.info("IntelligentRiskControlServiceImpl.intelligentRisk.intelligentRiskDTO:{}", JSONUtil.toJsonStr(intelligentRiskDTO, JSONConfig.create().setIgnoreNullValue(false)));
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
            LambdaUpdateWrapper<IntelligentRiskInfoEntity> wrapper = new LambdaUpdateWrapper<IntelligentRiskInfoEntity>()
                    .eq(IntelligentRiskInfoEntity::getOrderId, orderId)
                    .eq(IntelligentRiskInfoEntity::getDeleteFlag, 0)
                    .eq(IntelligentRiskInfoEntity::getType, IntelligentEnum.INTELLIGENT_RISK.getKey())
                    .set(IntelligentRiskInfoEntity::getDeleteFlag, 1);
            if (Objects.equals(step,2)){
                wrapper.eq(IntelligentRiskInfoEntity::getStep, step);
            }
            intelligentRiskInfoEntityMapper.update(wrapper);
            String result = intelligentRiskControlFeign.intelligentRisk(intelligentRiskDTO,apiId,"application/json");
            log.info("IntelligentRiskControlServiceImpl.intelligentRisk.supplyAsync.result:{}", result);
            return result;
        });
        future.thenAccept(result -> {
            // 处理接口返回结果
            log.info("IntelligentRiskControlServiceImpl.intelligentRisk.future.thenAccept.result:{}", result);
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Object> resultMap = objectMapper.readValue(result, Map.class);
                log.info("IntelligentRiskControlServiceImpl.intelligentRisk.future.thenAccept.resultMap:{}", resultMap);
//                IntelligentRiskInfoEntity intelligentRiskInfoEntity = intelligentRiskInfoEntityMapper.selectOne(
//                        new LambdaQueryWrapper<IntelligentRiskInfoEntity>()
//                                .eq(IntelligentRiskInfoEntity::getOrderId, orderId)
//                                .like(IntelligentRiskInfoEntity::getWorkflowRunId, resultMap.get("workflow_run_id").toString())
//                                .eq(IntelligentRiskInfoEntity::getDeleteFlag, 0)
//                                .eq(IntelligentRiskInfoEntity::getType, IntelligentEnum.INTELLIGENT_RISK.getKey())
//                                .orderByDesc(IntelligentRiskInfoEntity::getCreateTime)
//                                .last("limit 1")
//                );
//                if (ObjUtil.isEmpty(intelligentRiskInfoEntity)){
//                    intelligentRiskInfoEntityMapper.insert(
//                            new IntelligentRiskInfoEntity()
//                                    .setOrderId(orderId)
//                                    .setTaskId(resultMap.get("task_id").toString())
//                                    .setStep(step)
//                                    .setType(IntelligentEnum.INTELLIGENT_RISK.getKey())
//                                    .setWorkflowRunId(resultMap.get("workflow_run_id").toString())
//                                    .setResResult(JSONUtil.toJsonStr(result))
//                    );
//                }
            } catch (JsonProcessingException e) {
                log.info("IntelligentRiskControlServiceImpl.intelligentRisk.toMap error:{}", e.getMessage());
            }
        });
    }
    private void retrieveContract(Integer orderId,IntelligentContractDTO intelligentContractDTO,String apiId,Integer step){

        log.info("IntelligentRiskControlServiceImpl.retrieveContract intelligentContractDTO:{}", JSONUtil.toJsonStr(intelligentContractDTO, JSONConfig.create().setIgnoreNullValue(false)));
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
            LambdaUpdateWrapper<IntelligentRiskInfoEntity> wrapper = new LambdaUpdateWrapper<IntelligentRiskInfoEntity>()
                    .eq(IntelligentRiskInfoEntity::getOrderId, orderId)
                    .eq(IntelligentRiskInfoEntity::getDeleteFlag, 0)
                    .eq(IntelligentRiskInfoEntity::getType, IntelligentEnum.INTELLIGENT_CONTRACT.getKey())
                    .set(IntelligentRiskInfoEntity::getDeleteFlag, 1);
            if (Objects.equals(step,2)){
                wrapper.eq(IntelligentRiskInfoEntity::getStep, step);
            }
            intelligentRiskInfoEntityMapper.update(wrapper);
            String result = intelligentRiskControlFeign.intelligentContract(intelligentContractDTO,apiId,"application/json");
            log.info("IntelligentRiskControlServiceImpl.retrieveContract supplyAsync.result:{}", result);
            return result;
        });
        future.thenAccept(result -> {
            // 处理接口返回结果
            log.info("IntelligentRiskControlServiceImpl.retrieveContract future.thenAccept.result:{}", result);
//            try {
//                ObjectMapper objectMapper = new ObjectMapper();
//                Map<String, Object> resultMap = objectMapper.readValue(result, Map.class);
//                IntelligentRiskInfoEntity intelligentRiskInfoEntity = intelligentRiskInfoEntityMapper.selectOne(
//                        new LambdaQueryWrapper<IntelligentRiskInfoEntity>()
//                                .eq(IntelligentRiskInfoEntity::getOrderId, orderId)
//                                .like(IntelligentRiskInfoEntity::getWorkflowRunId, resultMap.get("workflow_run_id").toString())
//                                .eq(IntelligentRiskInfoEntity::getDeleteFlag, 0)
//                                .eq(IntelligentRiskInfoEntity::getType, IntelligentEnum.INTELLIGENT_CONTRACT.getKey())
//                                .orderByDesc(IntelligentRiskInfoEntity::getCreateTime)
//                                .last("limit 1")
//                );
//                if (ObjUtil.isEmpty(intelligentRiskInfoEntity)){
//                    intelligentRiskInfoEntityMapper.insert(
//                            new IntelligentRiskInfoEntity()
//                                    .setOrderId(orderId)
//                                    .setTaskId(resultMap.get("task_id").toString())
//                                    .setStep(step)
//                                    .setType(IntelligentEnum.INTELLIGENT_CONTRACT.getKey())
//                                    .setWorkflowRunId(resultMap.get("workflow_run_id").toString())
//                                    .setResResult(JSONUtil.toJsonStr(result))
//                    );
//                }
//            } catch (JsonProcessingException e) {
//                log.info("IntelligentRiskControlServiceImpl.retrieveContract.toMap error:{}", e.getMessage());
//            }
        });
    }
    private String getFundRefuseReason(PreApprovalApplyInfoEntity preApprovalApplyInfoEntity){
        if (ObjUtil.isNotNull(preApprovalApplyInfoEntity) && ObjUtil.isNotNull(preApprovalApplyInfoEntity.getRiskStatus()) && Objects.equals(preApprovalApplyInfoEntity.getRiskStatus(), PreRiskStatusEnum.RISK_DENIED.getCode())) {
            List<PreRiskPolicyResultEntity> riskResultList = preRiskPolicyResultMapper.selectList(new LambdaQueryWrapper<PreRiskPolicyResultEntity>()
                    .eq(PreRiskPolicyResultEntity::getPreId, preApprovalApplyInfoEntity.getId())
                    .eq(PreRiskPolicyResultEntity::getDeleteFlag, 0)
                    .in(PreRiskPolicyResultEntity::getResult, RiskPolicyResult.REJECT, RiskPolicyResult.WARN)
            );
            if (CollUtil.isNotEmpty(riskResultList)) {
                AtomicReference<String> riskRefuseReason = new AtomicReference<>("");
                riskResultList.stream()
                        .filter(entity -> "征信规则".equals(entity.getPolicyName()))
                        .findFirst()
                        .ifPresent(entity -> {
                            String funds = entity.getFunds();
                            if (StrUtil.isNotBlank(funds)) {
                                List<FundProductVO> list = JSONUtil.toList(funds, FundProductVO.class);
                                list.stream()
                                        .filter(vo -> Objects.equals(vo.getResult(), FundProductResult.REJECT) && Objects.equals(vo.getFundId(), 3))
                                        .map(fundProductVO -> {
                                            List<Integer> matchRules = fundProductVO.getMatchRule();
                                            if (CollUtil.isEmpty(matchRules)) {
                                                return null;
                                            }
                                            String matchRule = matchRules.stream()
                                                    .map(rule -> dictService.getDictLabel(GlobalConstants.DictType.PRE_RISK_FUND_SUGGESTION, rule))
                                                    .filter(StrUtil::isNotBlank)
                                                    .collect(Collectors.joining(";"));
                                            if (StrUtil.isNotBlank(matchRule)) {
//                                                String reason = "【" + fundProductVO.getFundName() + "】<br/>" + matchRule;
                                                riskRefuseReason.set(matchRule);
                                                return matchRule;
                                            }
                                            return null;
                                        })
                                        .filter(Objects::nonNull)
                                        .findFirst(); // 只取第一个匹配项
                            }
                        });
                return riskRefuseReason.get();
            }else {
                return null;
            }
        }else {
            return null;
        }
    }

    /**
     * 智能风控报告返回结果
     */

    @Override
    public void getIntelligentRiskResultCallBack(IntelligentRiskCallBack callBack) {
        log.info("IntelligentRiskControlServiceImpl.getIntelligentRiskResultCallBack.callBack:{}", JSONUtil.toJsonStr(callBack));
        //拿到回调结果  更新1350 节点的表数据
        OrderInfoEntity orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getOrderNumber, callBack.getOrderNum())
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .eq(OrderInfoEntity::getCurrentNode, 1350)
                .orderByDesc(OrderInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNull(orderInfo)){
            throw new BusinessException("订单不存在");
        }

        handelAIntelligentRiskResult(orderInfo.getId(),callBack);
        handAiIntelligentNextNode(orderInfo.getOrderNumber());

    }

    private void handelAIntelligentRiskResult(Integer orderId, IntelligentRiskCallBack callBack) {
        String remark = ObjUtil.equals(callBack.getRemark(),"暂无") ? "" : callBack.getRemark();
        String selfRemark = ObjUtil.equals(callBack.getSelfRemark(),"暂无") ? "" : callBack.getSelfRemark();

        riskAiIntelligentAuditMapper.update(new LambdaUpdateWrapper<RiskAiIntelligentAuditEntity>()
                .eq(RiskAiIntelligentAuditEntity::getOrderNumber,callBack.getOrderNum())
                .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
                .set(RiskAiIntelligentAuditEntity::getFinalAmount, callBack.getFinalAmount())
                .set(RiskAiIntelligentAuditEntity::getAiReportStatus, 1)
                .set(RiskAiIntelligentAuditEntity::getIsStoreEvaluation,callBack.getStoreAppraiser())
                .set(RiskAiIntelligentAuditEntity::getIsOverallReview,callBack.getRecheck())
                .set(RiskAiIntelligentAuditEntity::getIsRiskFirst,callBack.getRiskPre())
                .set(RiskAiIntelligentAuditEntity::getAuditRemarks,remark)
                .set(RiskAiIntelligentAuditEntity::getAuditResults,callBack.getTotalStatus())
                .set(RiskAiIntelligentAuditEntity::getInternalReason,selfRemark));
        if (ObjUtil.isNotNull(callBack.getFinalAmount()) ){
            if (ObjUtil.isNotNull(callBack.getTotalStatus()) && ObjUtil.equals(callBack.getTotalStatus(),1)){
                //【最终额度】赋值到云启【风控额度】
                orderAmountMapper.update(new LambdaUpdateWrapper<OrderAmountEntity>()
                        .eq(OrderAmountEntity::getOrderId,orderId)
                        .eq(OrderAmountEntity::getDeleteFlag, 0)
                        .set(OrderAmountEntity::getRiskAmount, callBack.getFinalAmount()));
            }

        }
    }

    /**
     * 智能风控报告主动查询
     */
    @Override
    public void queryIntelligentRiskResult(Integer orderId, String type) {
        log.info("IntelligentRiskControlServiceImpl.queryIntelligentRiskResult start orderId:{} type:{}",orderId,type);
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, () -> new BusinessException("订单信息不存在"));

        //调用al智能风控主动查询
        QueryIntelligentRiskDTO dto = new QueryIntelligentRiskDTO();
        dto.setOrderNum(orderInfo.getOrderNumber())
                .setType(type)
                .setZifangId(orderInfo.getFundId().toString());


        IntelligentRiskCallBack result = null;
        try {
            log.info("IntelligentRiskControlServiceImpl.queryIntelligentRiskResult dto:{}",dto);
            String resultJson = intelligentRiskControlFeign.queryIntelligentRiskResult(dto);
            result = JSONUtil.toBean(resultJson, IntelligentRiskCallBack.class);
            if (ObjUtil.isNull(result.getOrderNum())){
                return;
            }
            handelAIntelligentRiskResult(orderInfo.getId(),result);
            log.info("IntelligentRiskControlServiceImpl.queryIntelligentRiskResult result:{}",JSONUtil.toJsonStr(result));
        } catch (Exception e) {
            throw new BusinessException("主动查询风控结果失败");
        }
        handAiIntelligentNextNode(orderInfo.getOrderNumber());
    }

    @Override
    public void submitIntelligentRiskError(Integer orderId, String type) {
        log.info("IntelligentRiskControlServiceImpl.submitIntelligentRiskError start orderId:{} type:{}",orderId,type);
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, () -> new BusinessException("订单信息不存在"));
        //调用al智能风控失败推送
        QueryIntelligentRiskDTO dto = new QueryIntelligentRiskDTO();
        dto.setOrderNum(orderInfo.getOrderNumber())
                .setType(type)
                .setZifangId(orderInfo.getFundId().toString());
        try {
            log.info("IntelligentRiskControlServiceImpl.submitIntelligentRiskError dto:{}",dto);
            intelligentRiskControlFeign.submitIntelligentRiskError(dto);
            riskAiIntelligentAuditMapper.update(new LambdaUpdateWrapper<RiskAiIntelligentAuditEntity>()
                    .eq(RiskAiIntelligentAuditEntity::getOrderNumber,orderInfo.getOrderNumber())
                    .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
                    .set(RiskAiIntelligentAuditEntity::getAiReportStatus, 2));
            log.info("IntelligentRiskControlServiceImpl.submitIntelligentRiskError end");
        } catch (Exception e) {
            throw new BusinessException("智能风控报告两次失效推送失败");
        }
    }

    /**
     * 根据主动查询 或回调跳转节点
     */
    private void handAiIntelligentNextNode(String orderNumber) {
        RiskAiIntelligentAuditEntity aiIntelligentAuditEntity = riskAiIntelligentAuditMapper.selectOne(new LambdaQueryWrapper<RiskAiIntelligentAuditEntity>()
                .eq(RiskAiIntelligentAuditEntity::getOrderNumber,orderNumber)
                .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
                .eq(RiskAiIntelligentAuditEntity::getIsAutoAudit,1)
                .orderByDesc(RiskAiIntelligentAuditEntity::getCreateTime)
                .last("limit 1")
        );

        if (ObjUtil.isEmpty(aiIntelligentAuditEntity)) {
            log.warn("handAiIntelligentNextNode 未查询到有效的AI智能审核记录，orderNumber: {}", orderNumber);
            return;
        }

        Integer orderId = aiIntelligentAuditEntity.getOrderId();
        String remark = aiIntelligentAuditEntity.getInternalReason();
        String remarkExternal = aiIntelligentAuditEntity.getAuditRemarks();

        log.info("IntelligentRiskControlServiceImpl.handAiIntelligentNextNode start aiIntelligentAuditEntity：{}",JSONUtil.toJsonStr(aiIntelligentAuditEntity));
        //生成总评复核 风控初审 记录
        Integer isRiskFirst = aiIntelligentAuditEntity.getIsRiskFirst();
        Integer isOverallReview = aiIntelligentAuditEntity.getIsOverallReview();
        Integer isStoreEvaluation = aiIntelligentAuditEntity.getIsStoreEvaluation();


        //如果是标准件进行资方终审
        if (ObjUtil.isNotEmpty(aiIntelligentAuditEntity) && Objects.equals(aiIntelligentAuditEntity.getAuditResults(), 1)){
            orderStateService.sendEvent(States.QUALITY_INSPECTION_FINISH,
                    Events.AL_INTELLIGENT_AUDIT_2_FUNDS_FINAL_APPROVE,
                    orderId, aiUserId, null);
            insertNode(aiIntelligentAuditEntity.getOrderId(),isStoreEvaluation,isOverallReview,isRiskFirst,remark,remarkExternal);
            return;
        }
        if (ObjUtil.equals(aiIntelligentAuditEntity.getIsStoreEvaluation(),1)){
            orderStateService.sendEvent(States.QUALITY_INSPECTION_FINISH,
                    Events.AL_INTELLIGENT_AUDIT_2_STORE_EVALUATION,
                    orderId, aiUserId, null);
            insertNode(aiIntelligentAuditEntity.getOrderId(),isStoreEvaluation,isOverallReview,isRiskFirst,remark,remarkExternal);
            return;
        }
        if (ObjUtil.equals(aiIntelligentAuditEntity.getIsOverallReview(),1)){
            orderStateService.sendEvent(States.QUALITY_INSPECTION_FINISH,
                    Events.AL_INTELLIGENT_AUDIT_2_OVERALL_REVIEW,
                    orderId, aiUserId, null);
            insertNode(aiIntelligentAuditEntity.getOrderId(),isStoreEvaluation,isOverallReview,isRiskFirst,remark,remarkExternal);
            return;
        }
        if (ObjUtil.equals(aiIntelligentAuditEntity.getIsRiskFirst(),1)){
            orderStateService.sendEvent(States.QUALITY_INSPECTION_FINISH,
                    Events.AL_INTELLIGENT_AUDIT_2_RISK_FIRST_APPROVE_ASSIGN,
                    orderId, aiUserId,null);
            insertNode(aiIntelligentAuditEntity.getOrderId(),isStoreEvaluation,isOverallReview,isRiskFirst,remark,remarkExternal);
            return;
        }

    }

    /**
     * 在事件之后生成记录
     * @param orderId
     * @param isStoreEvaluation
     * @param isOverallReview
     * @param isRiskFirst
     */
    private void insertNode(Integer orderId,Integer isStoreEvaluation,Integer isOverallReview,Integer isRiskFirst,String remark,String remarkExternal){
        if (ObjUtil.equals(isStoreEvaluation, 2)){
            //标准件添加总评自动审核记录
            insertRecord(orderId,
                    States.STORE_EVALUATION.getNode(),//当前
                    States.OVERALL_REVIEW.getNode(),//下一个
                    States.QUALITY_INSPECTION_FINISH.getNode(),
                    "",//上一个
                    "",
                    Events.AGREES,aiUserId);
        }

        if (ObjUtil.equals(isOverallReview, 2)){
            //标准件添加总评自动审核记录
            insertRecord(orderId,
                    States.OVERALL_REVIEW.getNode(),//当前
                    States.RISK_FIRST_APPROVE_ASSIGN.getNode(),//下一个
                    States.STORE_EVALUATION.getNode(),
                    "",//上一个
                    "",
                    Events.AGREES,aiUserId);
        }
        if (ObjUtil.equals(isRiskFirst, 2)){
            // 判断是需要总评，如果需要的==》 在总评审核通过之后添加 风控初审记录，不需要在这里添加记录
            if (ObjUtil.equals(isOverallReview, 2)) {
                //标准件添加风控自动审核记录
                insertRecord(orderId,
                        States.RISK_FIRST_APPROVE.getNode(),//当前
                        States.FUNDS_FINAL_APPROVE.getNode(),//下一个
                        States.OVERALL_REVIEW.getNode(),
                        remark,//上一个
                        remarkExternal,
                        Events.AGREES, aiUserId);
            }
        }
    }


    @Override
    public void insertRecord(Integer orderId ,Integer currentNode,Integer nextNode,Integer lastNode,String remark,String remarkExternal, Events event,Integer userId){
        OrderNodeRecordEntity insertEntity = new OrderNodeRecordEntity();
        insertEntity.setOrderId(orderId);

        insertEntity.setCurrentNode(currentNode);
        insertEntity.setNextNode(nextNode);
        insertEntity.setLastNode(lastNode);

        // 查询最近次记录
        LocalDateTime lastTime = null;
        OrderNodeRecordEntity orderNodeRecordEntity =
                orderNodeRecordMapper.selectOne(new LambdaQueryWrapper<OrderNodeRecordEntity>()
                        .eq(OrderNodeRecordEntity::getOrderId, orderId)
                        .orderByDesc(OrderNodeRecordEntity::getCreateTime)
                        .last("LIMIT 1")
                );

        if (orderNodeRecordEntity != null) {
            //lastNode = orderNodeRecordEntity.getCurrentNode();
            lastTime = orderNodeRecordEntity.getCreateTime();
        }

        if (lastTime != null) {
            insertEntity.setApprovalCost(DateUtil.between(
                    Date.from(lastTime.atZone(ZoneId.systemDefault()).toInstant()),
                    new Date(System.currentTimeMillis()), DateUnit.SECOND));
        } else {
            insertEntity.setApprovalCost(0L);
        }
        insertEntity.setRemark(remark);
        insertEntity.setRemarkExternal(remarkExternal);
        insertEntity.setEvent(event.eventTransform());
        insertEntity.setCreateBy(userId);
        insertEntity.setUpdateBy(userId);
        log.info("save order {} insertRecord {}", orderId, JSONUtil.toJsonStr(insertEntity));
        orderNodeRecordMapper.insert(insertEntity);
    }

    @Override
    public GetIntelligentAIRiskReasonVO getIntelligentAIRiskReason(GetIntelligentAIRiskReasonDTO dto) {
        log.info("IntelligentRiskControlServiceImpl.getIntelligentAIRiskReason dto:{}",dto);
        RiskAiIntelligentAuditEntity riskAiIntelligentAuditEntity = riskAiIntelligentAuditMapper.selectOne(
                new LambdaQueryWrapper<RiskAiIntelligentAuditEntity>()
                        .eq(RiskAiIntelligentAuditEntity::getOrderId, dto.getOrderId())
                        .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
                        .orderByDesc(RiskAiIntelligentAuditEntity::getCreateTime)
                        .last("LIMIT 1")
        );
        if (ObjUtil.isNotNull(riskAiIntelligentAuditEntity)){
            return new GetIntelligentAIRiskReasonVO()
                    .setOrderId(riskAiIntelligentAuditEntity.getOrderId())
                    .setInternalReason(riskAiIntelligentAuditEntity.getInternalReason())
                    .setExternalReason(riskAiIntelligentAuditEntity.getRemark());
        }else {
            return new GetIntelligentAIRiskReasonVO()
                    .setOrderId(dto.getOrderId())
                    .setInternalReason("")
                    .setExternalReason("");
        }
    }

    private final OrderFileMenuService orderFileMenuService;
    @Override
    public List<String> getSignVideo(String orderNumber) {
        List<String> aLiYunlist = orderInfoMapper.selectJoinList(
                String.class,
                new MPJLambdaWrapper<OrderInfoEntity>()
                        .select(FileResourceEntity::getFilePath)
                        .leftJoin(OrderFileEntity.class,OrderFileEntity::getOrderId, OrderInfoEntity::getId)
                        .leftJoin(FileResourceEntity.class,FileResourceEntity::getFileUid, OrderFileEntity::getResourceId)
                        .like(OrderInfoEntity::getOrderNumber, orderNumber)
                        .eq(FileResourceEntity::getFileType, 1)
                        .eq(FileResourceEntity::getDeleteFlag, 0)
                        .eq(OrderFileEntity::getDeleteFlag, 0)
        ).stream().map(this::getALiYunHref).toList();
        if (CollUtil.isNotEmpty( aLiYunlist)){
            return aLiYunlist;
        }
        List<String> yunQilist = orderInfoMapper.selectJoinList(
                String.class,
                new MPJLambdaWrapper<OrderInfoEntity>()
                        .select(FileResourceEntity::getFilePath)
                        .leftJoin(OrderFileEntity.class,OrderFileEntity::getOrderId, OrderInfoEntity::getId)
                        .leftJoin(FileResourceEntity.class,FileResourceEntity::getFileUid, OrderFileEntity::getResourceId)
                        .like(OrderInfoEntity::getOrderNumber, orderNumber)
                        .eq(FileResourceEntity::getFileType, 1)
                        .eq(FileResourceEntity::getDeleteFlag, 0)
                        .eq(OrderFileEntity::getDeleteFlag, 0)
        ).stream().map(resourceId ->{
            return resourceFeign.longTimeAccessRouteRequest(resourceId).getData();
        }).toList();
        if (CollUtil.isNotEmpty( yunQilist)){
            return yunQilist;
        }
        return null;
    }

    private String getALiYunHref(String filePath) {
        OSS ossClient = new OSSClientBuilder().build(getOSSEndpoint, accessId, accessSecret);
        try {
            // 生成签名URL
            URL signedUrl = ossClient.generatePresignedUrl(oSSBucket, filePath, new Date(System.currentTimeMillis() + 7 * 24 * 3600 * 1000));
            return signedUrl.toString();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭OSS客户端
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }

}