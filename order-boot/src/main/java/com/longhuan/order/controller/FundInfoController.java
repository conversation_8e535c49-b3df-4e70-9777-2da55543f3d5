package com.longhuan.order.controller;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.entity.FundInfoEntity;
import com.longhuan.order.pojo.vo.ProductFundMappingVO;
import com.longhuan.order.service.FundInfoService;
import com.longhuan.order.service.ProductFundMappingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 资方表;(lh_management)表控制层
 *
 * <AUTHOR> yangfumin
 * @date : 2024-7-5
 */
@Api(tags = "资方表对象功能接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/fundingParty")

public class FundInfoController {
    private final FundInfoService lhManagementService ;
    private final ProductFundMappingService productFundMappingService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @ApiOperation("通过ID查询单条数据")
    @GetMapping("{id}")
    public Result<FundInfoEntity> queryById(Integer id) {
        return Result.success(lhManagementService.queryById(id));
    }

    /**
     * 新增数据
     *
     * @param lhManagement 实例对象
     * @return 实例对象
     */
    @ApiOperation("新增数据")
    @PostMapping
    public Result<FundInfoEntity> add(FundInfoEntity lhManagement) {
        return Result.success(lhManagementService.insert(lhManagement));
    }

    /**
     * 查询列表
     *
     * @return 实例对象
     */
    @ApiOperation("查询列表")
    @PostMapping("/selectFundList")
    public Result<List<FundInfoEntity>> selectFundList() {
        return Result.success(lhManagementService.selectFundList());
    }


    /**
     *  根据资方id查询产品列表
     * @param id 资方id
     * @return {@link Result< List<  ProductFundMappingVO >>}
     */
    @ApiOperation("根据资方id查询产品列表")
    @GetMapping("/queryProductListByFundId/{id}")
    public Result<List<ProductFundMappingVO>> queryProductByFundIdList(@PathVariable("id") Integer id) {
        return Result.success(productFundMappingService.queryProductListByFundId(id));
    }
}