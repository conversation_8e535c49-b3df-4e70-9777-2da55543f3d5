package com.longhuan.approve.boot.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.approve.boot.converter.ProductConverter;
import com.longhuan.approve.boot.feign.UserFeign;
import com.longhuan.approve.boot.mapper.*;
import com.longhuan.approve.boot.pojo.dto.*;
import com.longhuan.approve.boot.pojo.entity.*;
import com.longhuan.approve.boot.pojo.vo.*;
import com.longhuan.approve.boot.service.ProductConfigRateInfoService;
import com.longhuan.approve.boot.service.ProductService;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.core.util.NumberUtils;
import com.longhuan.common.web.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 中恒前期服务实施
 *
 * <AUTHOR>
 * @date 2024/08/11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductServiceImpl implements ProductService {
    private final ProductInfoMapper productInfoMapper;
    private final ProductConverter productConverter;
    private final OrderInfoMapper orderInfoMapper;
    private final ProductFundMappingMapper productFundMappingMapper;
    private final ProductConfigRateInfoMapper productConfigRateInfoMapper;
    private final ProductConfigRateInfoService productConfigRateInfoService;
    private final ProductAreaInfoMapper productAreaInfoMapper;
    private final FundRongdanProductMappingMapper fundRongdanProductMappingMapper;
    private final ProductTermInfoMapper productTermInfoMapper;
    private final ProductDeptMappingMapper productDeptMappingMapper;
    private final UserFeign userFeign;
    private final ProductRatingMappingEntityMapper productRatingMappingEntityMapper;
    private final FundInfoMapper fundInfoMapper;

    /**
     * 列表
     *
     * @param productListDTO 产品列表 DTO
     * @return {@link Page }<{@link ProductListVO }>
     */
    @Override
    public Page<ProductListVO> list(ProductListDTO productListDTO) {
        String name = productListDTO.getName();
        String number = productListDTO.getNumber();
        Page<ProductListVO> productList = productInfoMapper.selectJoinPage(new Page<>(productListDTO.getPageNum(), productListDTO.getPageSize()),
                ProductListVO.class,
                new MPJLambdaWrapper<ProductInfoEntity>()
                        .like(!StrUtil.isEmpty(name), ProductInfoEntity::getName, name)
                        .like(!StrUtil.isEmpty(number), ProductInfoEntity::getNumber, number)
                        .orderByDesc(ProductInfoEntity::getCreateTime)
        );
        return productList;
    }

    /**
     * 救
     *
     * @param productSaveDTO 产品保存 DTO
     * @return {@link Boolean }
     */
    @Override
    public Boolean save(ProductSaveDTO productSaveDTO) {
        Integer id = productSaveDTO.getId();
        ProductInfoEntity productInfoEntity = productConverter.dto2Entity(productSaveDTO);
        if (Objects.nonNull(id) && id > 0) {
            productInfoMapper.updateById(productInfoEntity);
        } else {
            productInfoEntity.setNumber(NumberUtils.getProductNumber());
            productInfoMapper.insert(productInfoEntity);
        }
        return Boolean.TRUE;
    }

    /**
     * 细节
     *
     * @param id 同上
     * @return {@link ProductDetailVO }
     */
    @Override
    public ProductDetailVO detail(Integer id) {
        ProductInfoEntity productInfoEntity = productInfoMapper.selectById(id);
        return productConverter.entity2Vo(productInfoEntity);
    }

    /**
     * 基金名单
     *
     * @param fundProductListDTO 基金产品列表 DTO
     * @return {@link List }<{@link ProductFundVO }>
     */
    @Override
    public List<ProductFundVO> fundList(FundProductListDTO fundProductListDTO) {
        Integer orderId = fundProductListDTO.getOrderId();
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, "当前订单不存在");
        Integer fundId = orderInfoEntity.getFundId();

        return productFundMappingMapper.selectJoinList(ProductFundVO.class, new MPJLambdaWrapper<FundProductMappingEntity>()
                .selectAs(FundProductMappingEntity::getProductId, ProductFundVO::getId)
                .selectAs(FundProductMappingEntity::getProductName, ProductFundVO::getName)
                .eq(FundProductMappingEntity::getFundId, fundId)
                .eq(FundProductMappingEntity::getStatus, 0)
                .eq(FundProductMappingEntity::getDeleteFlag, 0)
        );
    }
    @Override
    public Page<ProductPageVO> productList(ProductPageDTO productPageDTO) {
        MPJLambdaWrapper<ProductInfoEntity> Wrapper = new MPJLambdaWrapper<ProductInfoEntity>();

        Wrapper.like(!StrUtil.isEmpty(productPageDTO.getName()), ProductInfoEntity::getName, productPageDTO.getName());
        Wrapper.eq(productPageDTO.getTerm() != null, ProductInfoEntity::getTerm, productPageDTO.getTerm());
        Wrapper.eq(productPageDTO.getProductFlag() != null, ProductInfoEntity::getProductFlag, productPageDTO.getProductFlag());
        Wrapper.eq(ProductInfoEntity::getDeleteFlag, 0);
        if (productPageDTO.getYearRate() != null){
            Wrapper.like("year_rate::varchar", productPageDTO.getYearRate().divide(BigDecimal.valueOf(100)));
        }
        if (productPageDTO.getMonthlyRate() != null) {
            Wrapper.like( "monthly_rate::varchar", productPageDTO.getMonthlyRate().divide(BigDecimal.valueOf(100)));
        }
        Wrapper.orderByDesc(ProductInfoEntity::getCreateTime);;
        Page<ProductPageVO> productList = productInfoMapper.selectJoinPage(new Page<>(productPageDTO.getPageNum(), productPageDTO.getPageSize()),
                ProductPageVO.class,
                Wrapper
        );
        productList.getRecords().forEach(item -> {
            if(item.getMonthlyRate()!=null){
                BigDecimal multiply = item.getMonthlyRate().multiply(BigDecimal.valueOf(100));
                item.setMonthlyRate(multiply);
            }
            if (item.getYearRate()!= null) {
                BigDecimal yearRate = item.getYearRate().multiply(BigDecimal.valueOf(100));
                item.setYearRate(yearRate);
            }
            if (item.getIrr()!= null) {
                BigDecimal irr = item.getIrr().multiply(BigDecimal.valueOf(100));
                item.setIrr(irr);
            }
        });
        return productList;
    }





    @Override
    public Result<Boolean> saveProduct(ProductsDTO productsDTO) {
        //判断库里面是否有相同的产品
        ProductDetailsDTO productDetailsDTO = productsDTO.getProductDetailsDTO();
        ProductInfoEntity productInfoEntity = new ProductInfoEntity();
        BeanUtil.copyProperties(productDetailsDTO, productInfoEntity);
        //判断库里面是否有相同的产品
/*        List<ProductInfoEntity> productInfo = productInfoMapper.selectList(Wrappers.<ProductInfoEntity>lambdaQuery()
                .eq(ProductInfoEntity::getName, productDetailsDTO.getName())
                .eq(ProductInfoEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isNotEmpty(productInfo)){
            return Result.failed("产品："+productDetailsDTO.getName()+" 的名称存在，请修改后新增");
        }*/
        productInfoEntity.setNumber(NumberUtils.getProductNumber());
        List<ProductTermInfoEntity> pcrEntity = new ArrayList<>();
        //基础信息配
        List<ProductsDTO.ProductBasicConfig> productBasicConfigList = productsDTO.getProductBasicConfigList();
        if (CollUtil.isNotEmpty(productBasicConfigList)) {
            if (!(productBasicConfigList.size() > 1)){
                productBasicConfigList.stream().forEach(itme -> {
                    ProductTermInfoEntity productTermInfoEntity = new ProductTermInfoEntity();
                    BeanUtil.copyProperties(itme,productTermInfoEntity);
                    productTermInfoEntity.setProductId(productInfoEntity.getId());
                    pcrEntity.add(productTermInfoEntity);
                });
            }else {
                ProductsDTO.ProductBasicConfig productBasicConfig = productBasicConfigList.get(0);
                productInfoEntity.setCountBreathWay(productBasicConfig.getCountBreathWay());
                productInfoEntity.setTermStart(productBasicConfig.getTermStart());
                productInfoEntity.setTermEnd(productBasicConfig.getTermEnd());
                productInfoEntity.setYearRate(productBasicConfig.getYearRate());
                productInfoEntity.setIrr(productBasicConfig.getIrr());
            }
        }
        //插入产品表
        BigDecimal irr = productDetailsDTO.getIrr().divide(BigDecimal.valueOf(100));
        BigDecimal monthlyRate = productDetailsDTO.getMonthlyRate().divide(BigDecimal.valueOf(100));
        BigDecimal yearRate = productDetailsDTO.getYearRate().divide(BigDecimal.valueOf(100));
        productInfoEntity.setIrr(irr);
        productInfoEntity.setMonthlyRate(monthlyRate);
        productInfoEntity.setYearRate(yearRate);
        if (productDetailsDTO.getCustomerLevelPerformanceA() != null) {
            BigDecimal customerLevelPerformanceA = productDetailsDTO.getCustomerLevelPerformanceA().divide(BigDecimal.valueOf(100));
            BigDecimal customerLevelPerformanceB = productDetailsDTO.getCustomerLevelPerformanceB().divide(BigDecimal.valueOf(100));
            BigDecimal customerLevelPerformanceC = productDetailsDTO.getCustomerLevelPerformanceC().divide(BigDecimal.valueOf(100));
            productInfoEntity.setCustomerLevelPerformanceA(customerLevelPerformanceA);
            productInfoEntity.setCustomerLevelPerformanceB(customerLevelPerformanceB);
            productInfoEntity.setCustomerLevelPerformanceC(customerLevelPerformanceC);
        }
        productInfoMapper.insert(productInfoEntity);
        log.info("新增的产品id：{}",productInfoEntity.getId());
        //添加客户评级表（ABC）lh_product_rating_mapping
        ProductRatingMappingEntity productRatingMappingEntity = new ProductRatingMappingEntity();
        productRatingMappingEntity.setProductId(productInfoEntity.getId());
        productRatingMappingEntity.setProductName(productInfoEntity.getName());
        List<Integer> customerLevelPerformance = productDetailsDTO.getCustomerLevelPerformance();
        String result = customerLevelPerformance.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        productRatingMappingEntity.setCustomerLevel(result);
        productRatingMappingEntityMapper.insert(productRatingMappingEntity);
        //员工回期绩效ww
        List<ProductsDTO.StaffRepaymentPerformance> staffRepaymentPerformanceList = productsDTO.getStaffRepaymentPerformanceList();
/*        staffRepaymentPerformanceList.forEach(itme -> {
            Integer termStart = itme.getTermStart();
            Integer termEnd = itme.getTermEnd();
            IntStream.rangeClosed(termStart, termEnd)
                    .boxed()
                    .collect(Collectors.toSet());
        });*/
        if (CollUtil.isNotEmpty(staffRepaymentPerformanceList)) {
            staffRepaymentPerformanceList.stream().forEach(itme -> {
                //开始计算期数
                Integer termStart = itme.getTermStart();
                Integer termEnd = itme.getTermEnd();
                Set<Integer> collect = IntStream.rangeClosed(termStart, termEnd)
                        .boxed()
                        .collect(Collectors.toSet());
                //封装数据
                collect.forEach(item -> {
                    ProductTermInfoEntity productTermInfoEntity = new ProductTermInfoEntity();
                    BeanUtil.copyProperties(itme,productTermInfoEntity);
                    productTermInfoEntity.setProductId(productInfoEntity.getId());
                    productTermInfoEntity.setRepaymentPerformance(itme.getRepaymentPerformance().divide(BigDecimal.valueOf(100)));
                    productTermInfoEntity.setTerm(item);
                    pcrEntity.add(productTermInfoEntity);
                });
            });
        }else {
            ProductTermInfoEntity productTermInfoEntity = new ProductTermInfoEntity();
            productTermInfoEntity.setProductId(productInfoEntity.getId());
            productTermInfoEntity.setTerm(0);
            productTermInfoEntity.setRepaymentPerformance(BigDecimal.valueOf(1));
            productTermInfoEntity.setDeleteFlag(0);
            productTermInfoEntity.setIntermediaryFeeRate(BigDecimal.valueOf(1));
            productTermInfoEntity.setConfigType(10);
            productTermInfoEntity.setCountBreathWay("无效");
            productTermInfoMapper.insert(productTermInfoEntity);
        }
        //一次性服务收费方
        List<ProductsDTO.OneTimeServicePayer> oneTimeServicePayerList = productsDTO.getOneTimeServicePayerList();
        if (CollUtil.isNotEmpty(oneTimeServicePayerList)) {
            oneTimeServicePayerList.stream().forEach(itme -> {
                ProductTermInfoEntity productTermInfoEntity = new ProductTermInfoEntity();
                BeanUtil.copyProperties(itme,productTermInfoEntity);
                productTermInfoEntity.setProductId(productInfoEntity.getId());
                pcrEntity.add(productTermInfoEntity);
            });
        }
        //分期服务收费方
        List<ProductsDTO.InstallmentServicePayer> installmentServicePayerList = productsDTO.getInstallmentServicePayerList();
        if (CollUtil.isNotEmpty(installmentServicePayerList)) {
            installmentServicePayerList.stream().forEach(itme -> {
                ProductTermInfoEntity productTermInfoEntity = new ProductTermInfoEntity();
                BeanUtil.copyProperties(itme,productTermInfoEntity);
                productTermInfoEntity.setProductId(productInfoEntity.getId());
                pcrEntity.add(productTermInfoEntity);
            });
        }
        //保证金
        List<ProductsDTO.BondConfig> bondConfigList = productsDTO.getBondConfigList();
        if (CollUtil.isNotEmpty(bondConfigList)) {
            bondConfigList.stream().forEach(itme -> {
                ProductTermInfoEntity productTermInfoEntity = new ProductTermInfoEntity();
                BeanUtil.copyProperties(itme,productTermInfoEntity);
                productTermInfoEntity.setProductId(productInfoEntity.getId());
                pcrEntity.add(productTermInfoEntity);
            });
        }
        pcrEntity.stream().forEach(itme -> {
            productTermInfoMapper.insert(itme);
        });
        return Result.success(true);
    }



    @Override
    public Boolean productFlag(ProductDetailsDTO productDetailsDTO) {
        Boolean flag = false;
        ProductInfoEntity productInfoEntity = productInfoMapper.selectById(productDetailsDTO.getId());
        if (productInfoEntity != null) {
            int update = productInfoMapper.update(
                    Wrappers.<ProductInfoEntity>lambdaUpdate()
                            .set(ProductInfoEntity::getProductFlag, productDetailsDTO.getProductFlag())
                            .eq(ProductInfoEntity::getId, productDetailsDTO.getId())
                            .eq(ProductInfoEntity::getDeleteFlag, 0)
            );
            if (update > 0) {
                log.info("产品启用禁用状态修改成功。");
                flag = true;
            }
        }
        return flag;
    }

    @Override
    public productsVO details(Integer id) {
        productsVO productsVO = new productsVO();
        //产品信息
        ProductDetailsDTO productDetailsDTO = new ProductDetailsDTO();
        ProductInfoEntity productInfoEntity = productInfoMapper.selectById(id);
        if (productInfoEntity==null) {
            return null;
        }
        //获取员工评级信息
        List<ProductRatingMappingEntity> productRatingMappingEntities = productRatingMappingEntityMapper.selectList(Wrappers.<ProductRatingMappingEntity>lambdaQuery()
                .eq(ProductRatingMappingEntity::getProductId, productInfoEntity.getId())
                .eq(ProductRatingMappingEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isNotEmpty(productRatingMappingEntities)) {
            ProductRatingMappingEntity productRatingMappingEntity = productRatingMappingEntities.get(0);
            if (StrUtil.isNotBlank(productRatingMappingEntity.getCustomerLevel())){
                String customerLevel = productRatingMappingEntity.getCustomerLevel();
                List<Integer> customerLevelList = Arrays.stream(customerLevel.split(","))
                        .map(String::trim)
                        .filter(StrUtil::isNotBlank)
                        .map(Integer::valueOf)
                        .collect(Collectors.toList());
                productDetailsDTO.setCustomerLevelPerformance(customerLevelList);
            }
        }
        BeanUtil.copyProperties(productInfoEntity,productDetailsDTO);
        //封装主表数据
        BigDecimal irr = productInfoEntity.getIrr().multiply(BigDecimal.valueOf(100));
        BigDecimal monthlyRate = productInfoEntity.getMonthlyRate().multiply(BigDecimal.valueOf(100));
        BigDecimal yearRate = productInfoEntity.getYearRate().multiply(BigDecimal.valueOf(100));
        productDetailsDTO.setIrr(irr);
        productDetailsDTO.setMonthlyRate(monthlyRate);
        productDetailsDTO.setYearRate(yearRate);
        if (productInfoEntity.getCustomerLevelPerformanceA() != null){
            BigDecimal customerLevelPerformanceA = productInfoEntity.getCustomerLevelPerformanceA().multiply(BigDecimal.valueOf(100));
            BigDecimal customerLevelPerformanceB = productInfoEntity.getCustomerLevelPerformanceB().multiply(BigDecimal.valueOf(100));
            BigDecimal customerLevelPerformanceC = productInfoEntity.getCustomerLevelPerformanceC().multiply(BigDecimal.valueOf(100));
            productDetailsDTO.setCustomerLevelPerformanceA(customerLevelPerformanceA);
            productDetailsDTO.setCustomerLevelPerformanceB(customerLevelPerformanceB);
            productDetailsDTO.setCustomerLevelPerformanceC(customerLevelPerformanceC);
        }
        productsVO.setProductDetailsDTO(productDetailsDTO);
        //基础信息配置
        List<ProductTermInfoEntity> proConfigList = productTermInfoMapper.selectList(Wrappers.<ProductTermInfoEntity>lambdaQuery()
                .eq(ProductTermInfoEntity::getProductId, id)
                .eq(ProductTermInfoEntity::getDeleteFlag, 0)
                .orderByDesc(ProductTermInfoEntity::getConfigType)
                .orderByAsc(ProductTermInfoEntity::getTermStart)
        );
        if (CollUtil.isNotEmpty(proConfigList)) {
            //获取员工绩效数据
            List<ProductTermInfoEntity> employeeList = proConfigList.stream()
                    .filter(itme -> ObjUtil.equals(itme.getConfigType(), 2))
                    .toList();
            //删除员工绩效汇款的数据
            proConfigList.removeIf(itme -> ObjUtil.equals(itme.getConfigType(), 2));
            //根据termSaart 进行分组
            Map<Integer, List<ProductTermInfoEntity>> map = employeeList.stream()
                    .filter(entity -> entity.getTermStart() != null)
                    .collect(Collectors.groupingBy(ProductTermInfoEntity::getTermStart));
            //获取员工绩效数据
            map.forEach((termStart, list) -> {
                if (CollUtil.isNotEmpty(map.get(termStart))) {
                    proConfigList.add(list.get(0));
                }
            });
            proConfigList.stream().filter(itme -> ObjUtil.isNotNull(itme.getConfigType()) && ObjUtil.equal(itme.getConfigType(), 2)).forEach(itme -> {
                if (itme.getRepaymentPerformance() != null) {
                    BigDecimal multiply = itme.getRepaymentPerformance().multiply(BigDecimal.valueOf(100));
                    itme.setRepaymentPerformance(multiply);
                }
            });
        }
        /*if (CollUtil.isNotEmpty(proConfigList)){
            //回显员工绩效数据进行分组处理
            Map<Integer, List<ProductTermInfoEntity>> map = proConfigList.stream()
                    .filter(entity -> entity.getTermStart() != null)
                    .collect(Collectors.groupingBy(ProductTermInfoEntity::getTermStart));

            map.forEach((termStart, list) -> {
                if (CollUtil.isNotEmpty(map.get(termStart))){
                    newProductList.add(list.get(0));
                }
            });
            newProductList.stream().filter(itme -> ObjUtil.isNotNull(itme.getConfigType()) && ObjUtil.equal(itme.getConfigType(), 2)).forEach(itme -> {
                if (itme.getRepaymentPerformance() != null) {
                    BigDecimal multiply = itme.getRepaymentPerformance().multiply(BigDecimal.valueOf(100));
                    itme.setRepaymentPerformance(multiply);
                }
            });
        }*/
        productsVO.setProductTermInfoEntitiesList(proConfigList);
        return productsVO;
    }
   @Override
   public synchronized Boolean productEdit(ProductsDTO productsDTO) {
       //修改主表信息
       ProductDetailsDTO productDetailsDTO = productsDTO.getProductDetailsDTO();
       //修改的产品名字是否已经存在
       String name = productDetailsDTO.getName();
/*       ProductInfoEntity productName = productInfoMapper.selectOne(Wrappers.<ProductInfoEntity>lambdaQuery()
               .ne(ProductInfoEntity::getId, productDetailsDTO.getId())
               .eq(ProductInfoEntity::getName, name)
               .eq(ProductInfoEntity::getDeleteFlag, 0)
               .last("limit 1")
       );
       if (productName!=null){
           throw new BusinessException("产品名称已经存在请重新调整");
       }*/
       //修改产品内容
       ProductInfoEntity productInfoEntity = new ProductInfoEntity();
       BeanUtil.copyProperties(productDetailsDTO,productInfoEntity);
       BigDecimal irr = productDetailsDTO.getIrr().divide(BigDecimal.valueOf(100));
       BigDecimal monthlyRate = productDetailsDTO.getMonthlyRate().divide(BigDecimal.valueOf(100));
       BigDecimal yearRate = productDetailsDTO.getYearRate().divide(BigDecimal.valueOf(100));
       if (productDetailsDTO.getCustomerLevelPerformanceA() != null) {
           BigDecimal customerLevelPerformanceA = productDetailsDTO.getCustomerLevelPerformanceA().divide(BigDecimal.valueOf(100));
           BigDecimal customerLevelPerformanceB = productDetailsDTO.getCustomerLevelPerformanceB().divide(BigDecimal.valueOf(100));
           BigDecimal customerLevelPerformanceC = productDetailsDTO.getCustomerLevelPerformanceC().divide(BigDecimal.valueOf(100));
           productInfoEntity.setCustomerLevelPerformanceA(customerLevelPerformanceA);
           productInfoEntity.setCustomerLevelPerformanceB(customerLevelPerformanceB);
           productInfoEntity.setCustomerLevelPerformanceC(customerLevelPerformanceC);
       }
       productInfoEntity.setIrr(irr);
       productInfoEntity.setMonthlyRate(monthlyRate);
       productInfoEntity.setYearRate(yearRate);
       productInfoMapper.updateById(productInfoEntity);
       //获取子表数据
       List<ProductTermInfoEntity> productTermInfoEntity = new ArrayList<>();
       //置为失效
       productTermInfoMapper.update(new LambdaUpdateWrapper<ProductTermInfoEntity>()
                .set(ProductTermInfoEntity::getDeleteFlag,1)
               .eq(ProductTermInfoEntity::getProductId, productDetailsDTO.getId())
               .eq(ProductTermInfoEntity::getDeleteFlag, 0)
       );
       productRatingMappingEntityMapper.update(Wrappers.<ProductRatingMappingEntity>lambdaUpdate()
               .set(ProductRatingMappingEntity::getDeleteFlag,1)
               .eq(ProductRatingMappingEntity::getProductId,productDetailsDTO.getId())
               .eq(ProductRatingMappingEntity::getDeleteFlag, 0)
       );
       //客户评级数据
       ProductRatingMappingEntity productRatingMappingEntity = new ProductRatingMappingEntity();
       productRatingMappingEntity.setProductId(productInfoEntity.getId());
       productRatingMappingEntity.setProductName(productInfoEntity.getName());
       List<Integer> customerLevelPerformance = productDetailsDTO.getCustomerLevelPerformance();
       String result = customerLevelPerformance.stream()
               .map(String::valueOf)
               .collect(Collectors.joining(","));
       productRatingMappingEntity.setCustomerLevel(result);
       productRatingMappingEntityMapper.insert(productRatingMappingEntity);
       //基础信息配置
       List<ProductsDTO.ProductBasicConfig> productBasicConfigList = productsDTO.getProductBasicConfigList();
       if (CollUtil.isNotEmpty(productBasicConfigList)){
           productBasicConfigList.forEach(item -> {
               ProductTermInfoEntity productTermInfo = new ProductTermInfoEntity();
               BeanUtil.copyProperties(item,productTermInfo);
               productTermInfoEntity.add(productTermInfo);
           });
       }
       //员工汇款绩效
       List<ProductsDTO.StaffRepaymentPerformance> staffRepaymentPerformanceList = productsDTO.getStaffRepaymentPerformanceList();
/*       if (CollUtil.isNotEmpty(staffRepaymentPerformanceList)){
           staffRepaymentPerformanceList.forEach(item -> {
               ProductTermInfoEntity productTermInfo = new ProductTermInfoEntity();
               BeanUtil.copyProperties(item,productTermInfo);
               productTermInfo.setRepaymentPerformance(item.getRepaymentPerformance().divide(BigDecimal.valueOf(100)));
               productTermInfoEntity.add(productTermInfo);
           });
       }*/
       if (CollUtil.isNotEmpty(staffRepaymentPerformanceList)) {
           staffRepaymentPerformanceList.stream().forEach(itme -> {
               //开始计算期数
               Integer termStart = itme.getTermStart();
               Integer termEnd = itme.getTermEnd();
               Set<Integer> collect = IntStream.rangeClosed(termStart, termEnd)
                       .boxed()
                       .collect(Collectors.toSet());
               //封装数据
               collect.forEach(item -> {
                   ProductTermInfoEntity productTermInfo = new ProductTermInfoEntity();
                   BeanUtil.copyProperties(itme, productTermInfo);
                   productTermInfo.setProductId(productInfoEntity.getId());
                   productTermInfo.setRepaymentPerformance(itme.getRepaymentPerformance().divide(BigDecimal.valueOf(100)));
                   productTermInfo.setTerm(item);
                   productTermInfoEntity.add(productTermInfo);
               });
           });
       }
       //一次性服务收费方
       List<ProductsDTO.OneTimeServicePayer> oneTimeServicePayerList = productsDTO.getOneTimeServicePayerList();
       if (CollUtil.isNotEmpty(oneTimeServicePayerList)){
           oneTimeServicePayerList.forEach(item -> {
               ProductTermInfoEntity productTermInfo = new ProductTermInfoEntity();
               BeanUtil.copyProperties(item,productTermInfo);
               productTermInfoEntity.add(productTermInfo);
           });
       }
       //一次性服务收费方
       List<ProductsDTO.InstallmentServicePayer> installmentServicePayerList = productsDTO.getInstallmentServicePayerList();
       if (CollUtil.isNotEmpty(installmentServicePayerList)){
           installmentServicePayerList.forEach(item -> {
               ProductTermInfoEntity productTermInfo = new ProductTermInfoEntity();
               BeanUtil.copyProperties(item,productTermInfo);
               productTermInfoEntity.add(productTermInfo);
           });
       }
       //保证金
       List<ProductsDTO.BondConfig> bondConfigList = productsDTO.getBondConfigList();
       if (CollUtil.isNotEmpty(bondConfigList)){
           bondConfigList.forEach(item -> {
               ProductTermInfoEntity productTermInfo = new ProductTermInfoEntity();
               BeanUtil.copyProperties(item,productTermInfo);
               productTermInfoEntity.add(productTermInfo);
           });
       }
       //修改子表数据
       productTermInfoEntity.forEach(tiem ->{
           tiem.setProductId(productInfoEntity.getId());
           productTermInfoMapper.insert(tiem);
       });
       return true;
   }


    @Override
    public Result<Boolean> productCopy(ProductDetailsDTO productDetailsDTO) {
        Integer productId = productDetailsDTO.getId();
        String name = productDetailsDTO.getName();
        //判断库里面是否有相同的产品
        List<ProductInfoEntity> productInfoEntities = productInfoMapper.selectList(Wrappers.<ProductInfoEntity>lambdaQuery()
                .eq(ProductInfoEntity::getName, name)
                .eq(ProductInfoEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isNotEmpty(productInfoEntities)){
            return Result.failed("产品"+productDetailsDTO.getName()+" 名称存在，请修改后复制");
        }
        ProductInfoEntity productInfo = productInfoMapper.selectById(productId);
        if (productInfo != null) {
            //插入主表数据
            ProductInfoEntity productInfoEntity = new ProductInfoEntity();
            BeanUtil.copyProperties(productInfo,productInfoEntity);
            productInfoEntity.setId(null);
            productInfoEntity.setName(name);
            productInfoMapper.insert(productInfoEntity);
            //插入子表数据
            List<ProductTermInfoEntity> productTermInfoEntities = productTermInfoMapper.selectList(Wrappers.<ProductTermInfoEntity>lambdaQuery()
                    .eq(ProductTermInfoEntity::getProductId, productId)
                    .eq(ProductTermInfoEntity::getDeleteFlag, 0)
            );
            if (CollUtil.isNotEmpty(productTermInfoEntities)){
                productTermInfoEntities.stream().forEach(itme -> {
                    itme.setId(null);
                    itme.setProductId(productInfoEntity.getId());
                    productTermInfoMapper.insert(itme);

                });
            }
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> saveProductArea(ProductDeptMappingDTO productDeptMappingDTO) {
        List<ProductDeptMappingEntity> productDeptMappingEntities = productDeptMappingMapper.selectList(Wrappers.<ProductDeptMappingEntity>lambdaQuery()
                .eq(ProductDeptMappingEntity::getProductId, productDeptMappingDTO.getProductId())
                .eq(ProductDeptMappingEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isNotEmpty(productDeptMappingEntities)){
            return Result.failed("新增产品已存在，请更换产品");
        }
        List<Integer> deptIds = productDeptMappingDTO.getDeptIds();
        String ruleNumber = String.valueOf(System.currentTimeMillis());
        deptIds.stream().forEach(deptId -> {
            ProductDeptMappingEntity productDeptMapping = new ProductDeptMappingEntity();
            productDeptMapping.setProductId(productDeptMappingDTO.getProductId());
            productDeptMapping.setDeptId(deptId);
            productDeptMapping.setEnable(productDeptMappingDTO.getEnable());
            productDeptMapping.setRuleNumber(ruleNumber);
            productDeptMappingMapper.insert(productDeptMapping);
        });
        return Result.success(true);
    }

    @Override
    public ProductDeptMappingDTO detailsProductArea(Integer productId) {
        List<ProductDeptMappingEntity> productDeptMappingEntities = productDeptMappingMapper.selectList(Wrappers.<ProductDeptMappingEntity>lambdaQuery()
                .eq(ProductDeptMappingEntity::getProductId, productId)
                .eq(ProductDeptMappingEntity::getDeleteFlag, 0)
        );
        ProductDeptMappingEntity productDeptMappingEntity = productDeptMappingEntities.get(0);
        ProductDeptMappingDTO productDeptMappingDTO = new ProductDeptMappingDTO();
        productDeptMappingDTO.setProductId(productDeptMappingEntity.getProductId());
        productDeptMappingDTO.setEnable(productDeptMappingEntity.getEnable());
        productDeptMappingDTO.setRuleNumber(productDeptMappingEntity.getRuleNumber());
        productDeptMappingDTO.setCreateTime(productDeptMappingEntity.getCreateTime());
        List<Integer> list = productDeptMappingEntities.stream().map(ProductDeptMappingEntity::getDeptId).toList();
        productDeptMappingDTO.setDeptIds(list);
        return productDeptMappingDTO;
    }

    @Override
    public Boolean productFlagmodify(List<ProductDeptMappingDTO> productDeptMappingDTOList) {
        productDeptMappingDTOList.stream().forEach(productDeptMappingDTO -> {
            //查询产品id的数据
            List<ProductDeptMappingEntity> productDeptMappingEntities = productDeptMappingMapper.selectList(Wrappers.<ProductDeptMappingEntity>lambdaQuery()
                            .eq(ProductDeptMappingEntity::getProductId, productDeptMappingDTO.getProductId())
                            .eq(ProductDeptMappingEntity::getDeleteFlag, 0)
            );
            if (CollUtil.isNotEmpty(productDeptMappingEntities)){
                productDeptMappingEntities.stream().forEach(itme ->{
                    productDeptMappingMapper.update(Wrappers.<ProductDeptMappingEntity>lambdaUpdate()
                            .set(ProductDeptMappingEntity::getDeleteFlag,1)
                            .eq(ProductDeptMappingEntity::getId,itme.getId())
                            .eq(ProductDeptMappingEntity::getDeleteFlag,0)
                    );
                });
                productDeptMappingDTO.setCreateTime(productDeptMappingDTO.getCreateTime());
                saveProductArea(productDeptMappingDTO);
            }
        });
        return true;
    }





    @Override
    public Page<ProductStoreVO> productAreaList(ProductAreaDTO productAreaDTO) {
        //查询所有的数据
        List<ProductStoreVO> productAreaDTOS = productDeptMappingMapper.selectJoinList(ProductStoreVO.class, new MPJLambdaWrapper<ProductDeptMappingEntity>()
                .selectAll(ProductDeptMappingEntity.class)
                .selectAs(ProductInfoEntity::getName, ProductStoreVO::getProductName)
                .leftJoin(ProductInfoEntity.class, ProductInfoEntity::getId, ProductTermInfoEntity::getProductId)
                .eq(ProductDeptMappingEntity::getDeleteFlag, 0)
        );
        //根据productId进行分组
        Map<Integer, List<ProductStoreVO>> list = productAreaDTOS.stream()
                .collect(Collectors.groupingBy(
                        ProductStoreVO::getProductId, Collectors.toList()
                ));
        Page<ProductStoreVO> productDTOPage = productDeptMappingMapper.selectJoinPage(new Page<>(productAreaDTO.getPageNum(), productAreaDTO.getPageSize()),
                ProductStoreVO.class,
                new MPJLambdaWrapper<ProductDeptMappingEntity>()
                        .selectAs(ProductDeptMappingEntity::getProductId, ProductAreaDTO::getProductId)
                        .leftJoin(ProductInfoEntity.class, ProductInfoEntity::getId, ProductTermInfoEntity::getProductId)
                        .like(StrUtil.isNotEmpty(productAreaDTO.getProductName()), ProductInfoEntity::getName, productAreaDTO.getProductName())
                        .like(StrUtil.isNotEmpty(productAreaDTO.getRuleNumber()), ProductDeptMappingEntity::getRuleNumber, productAreaDTO.getRuleNumber())
                        .like(productAreaDTO.getStoreId()!= null, ProductDeptMappingEntity::getDeptId, productAreaDTO.getStoreId())
                        .eq(productAreaDTO.getProductFlag() != null, ProductDeptMappingEntity::getEnable, productAreaDTO.getProductFlag())
                        .eq(ProductDeptMappingEntity::getDeleteFlag, 0)
                        .groupBy(ProductDeptMappingEntity::getProductId)
        );
        List<ProductStoreVO> records = productDTOPage.getRecords();
        records.stream().forEach(item->{
            List<ProductStoreVO> productId = list.get(item.getProductId());
            StringBuilder storeId = new StringBuilder();
            StringBuilder storeNames = new StringBuilder();
            productId.stream().forEach(i->{
                storeId.append(i.getDeptId()).append(",");
                String storeName = userFeign.getDeptById(i.getDeptId()).getData();
                storeNames.append(storeName).append(",");
            });
            item.setRuleNumber(list.get(item.getProductId()).get(0).getRuleNumber());
            item.setProductName(list.get(item.getProductId()).get(0).getProductName());
            item.setProductId(list.get(item.getProductId()).get(0).getProductId());
            item.setEnable(list.get(item.getProductId()).get(0).getEnable());
            item.setDeptId(list.get(item.getProductId()).get(0).getDeptId());
            item.setDeptIds(storeId.substring(0, storeId.length() - 1));
            item.setCreateTime(list.get(item.getProductId()).get(0).getCreateTime());
            item.setId(list.get(item.getProductId()).get(0).getId());
            item.setStoreName(storeNames.substring(0, storeId.length() - 1));
        });
        records.removeIf(item -> StrUtil.isNotEmpty(productAreaDTO.getArea())&&!item.getStoreName().contains(productAreaDTO.getArea()));
        return productDTOPage;
    }



    @Override
    public Page<FundRongdanProductVO> productRongDanList(ProductRongDanDTO productRongDanDTO) {

        MPJLambdaWrapper<FundInfoEntity> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper
                .selectAs(FundInfoEntity::getId , FundRongdanProductVO::getFundId)
                .selectAs(FundInfoEntity::getName, FundRongdanProductVO::getFundName)
                .selectAs(ProductRongdanEntity::getCompanyName, FundRongdanProductVO::getCompanyName)
                .selectAs(ProductRongdanEntity::getId, FundRongdanProductVO::getRongdanId)
                .leftJoin(ProductRongdanEntity.class, ProductRongdanEntity::getFundId, FundInfoEntity::getId)
                .eq(FundInfoEntity::getDeleteFlag, 0);
        Page<FundRongdanProductVO> fundRongdanProductVOPage = fundInfoMapper.selectJoinPage(new Page<>(1, 30),
                FundRongdanProductVO.class,
                queryWrapper);
       //查出所有资方的产品
        List<FundProductMappingEntity> fundProductMappingEntities = productFundMappingMapper.selectList(
                new LambdaQueryWrapper<FundProductMappingEntity>()
                        .eq(FundProductMappingEntity::getDeleteFlag, 0)
        );
        Map<Integer, List<FundProductMappingEntity>> fundProductMap = fundProductMappingEntities.stream()
                .collect(Collectors.groupingBy(FundProductMappingEntity::getFundId));

        //查出融担产品表的所有产品
        List<FundRongdanProductMappingEntity> fundRongDanProductList = fundRongdanProductMappingMapper.selectList(new MPJLambdaWrapper<FundRongdanProductMappingEntity>()
                .eq(FundRongdanProductMappingEntity::getDeleteFlag, 0)
        );
        Map<Integer, List<FundRongdanProductMappingEntity>> fundRongDanProductMap = fundRongDanProductList.stream()
                .collect(Collectors.groupingBy(FundRongdanProductMappingEntity::getRongdanId));

        fundRongdanProductVOPage.getRecords().stream().forEach(info ->{
            //todo  除了蓝海 其他都是产品 对 资方  如果多融担    从融担产品 关联表 拿数据
            if (!ObjUtil.equal(info.getFundId(), 3)){
                List<FundProductMappingEntity> fundProductList = fundProductMap.get(info.getFundId());
                // 提取产品ID列表
                List<Integer> productIds = fundProductList.stream()
                        .map(FundProductMappingEntity::getProductId)
                        .collect(Collectors.toList());
                info.setProductIds(productIds);
                //非空 校验
                Integer productId = productIds.get(0);
                info.setProductFlag(CollUtil.isNotEmpty(fundProductList)?fundProductList.get(0).getStatus(): 0);
                info.setRuleNumber( info.getFundId().toString());
                List<String> list = productInfoMapper.selectList(Wrappers.<ProductInfoEntity>lambdaQuery()
                            .in(ProductInfoEntity::getId, productIds)
                            .eq(ProductInfoEntity::getDeleteFlag, 0)
                ).stream().map(ProductInfoEntity::getName).toList();
                String productIdJoin = String.join(",", list);
                info.setProductName(productIdJoin);

            }
            fundRongDanProductMap.forEach((rongDanId, rongdanProductMappingEntityList) -> {
                if (ObjUtil.equal(info.getRongdanId(), rongDanId)){
                    List<Integer> productIds = rongdanProductMappingEntityList.stream().map(rongdanProductMappingEntity -> rongdanProductMappingEntity.getProductId()).collect(Collectors.toList());
                    info.setProductIds(productIds);
                    Integer productId = productIds.get(0);
                    info.setRuleNumber( info.getFundId().toString());
                    //添加产品名称
                    List<String> list = productInfoMapper.selectList(Wrappers.<ProductInfoEntity>lambdaQuery()
                            .in(ProductInfoEntity::getId, productIds)
                            .eq(ProductInfoEntity::getDeleteFlag, 0)
                    ).stream().map(ProductInfoEntity::getName).toList();
                    String productIdJoin = String.join(",", list);
                    info.setProductName(productIdJoin);
                }
            });
            List<Integer> productIds = info.getProductIds();
            Integer flag =0 ;
            if (CollUtil.isNotEmpty(productIds)){
                FundProductMappingEntity fundProductMappingEntity = productFundMappingMapper.selectOne(new LambdaQueryWrapper<FundProductMappingEntity>()
                        .eq(FundProductMappingEntity::getProductId, productIds.get(0))
                        .eq(FundProductMappingEntity::getFundId, info.getFundId())
                        .eq(FundProductMappingEntity::getDeleteFlag, 0)
                        .orderByDesc(FundProductMappingEntity::getCreateTime)
                        .last("limit 1")
                );
               flag = ObjUtil.isNotNull(fundProductMappingEntity)?fundProductMappingEntity.getStatus(): 0;
            }
            info.setProductFlag(flag);
        });
        Integer productFlag = productRongDanDTO.getProductFlag();
        String name = productRongDanDTO.getName();
        Integer fundId = productRongDanDTO.getFundId();
        Integer rongdanId = productRongDanDTO.getRongdanId();
        //组装查询条件
        if (ObjUtil.isNull(productFlag) && ObjUtil.isNull(name) && ObjUtil.isNull(fundId) && ObjUtil.isNull(rongdanId)){
            fundRongdanProductVOPage.getRecords().stream().forEach(info ->{
                info.setFlag("1");
            });

        } else{
            fundRongdanProductVOPage.getRecords().forEach(info ->{
                if (ObjUtil.isNotNull(productFlag)){
                    if (ObjUtil.equal(info.getProductFlag(), productFlag)){
                        info.setFlag("1");
                    }
                }
                if (ObjUtil.isNotNull(name) && ObjUtil.isNotNull(info.getFundName())){
                    if (info.getProductName().contains( name)){
                        info.setFlag("1");
                    }
                }
                if (ObjUtil.isNotNull(fundId) ){
                    if (ObjUtil.equal(info.getFundId(), fundId)){
                        info.setFlag("1");
                    }
                }
                if (ObjUtil.isNotNull(rongdanId) ){
                    info.setFlag(null);
                    if (ObjUtil.equal(info.getRongdanId(), rongdanId)){
                        info.setFlag("1");
                    }
                }
            });
        }
        //todo 处理分页
        int pageNum = productRongDanDTO.getPageNum();
        int pageSize = productRongDanDTO.getPageSize();
        List<FundRongdanProductVO> pageRecords = fundRongdanProductVOPage.getRecords();
        List<FundRongdanProductVO> filteredRecords = pageRecords.stream()
                .filter(record -> "1".equals(record.getFlag()))
                .toList();
        List<FundRongdanProductVO> pagedRecords = filteredRecords.stream()
                .skip((long) (pageNum - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());

        fundRongdanProductVOPage.setRecords(pagedRecords);
        fundRongdanProductVOPage.setTotal(filteredRecords.size());
        return fundRongdanProductVOPage;
    }

    @Override
    public FundRongdanDetailsVO productRongDanDetails(Integer fundId, Integer rongdanId) {
        FundRongdanDetailsVO fundRongdanDetailsVO = new FundRongdanDetailsVO();
        List<Integer> integerStream = new ArrayList<>();
        Integer flag = 0;
        if (ObjUtil.isNull(rongdanId)){
            List<FundProductMappingEntity> fundList = productFundMappingMapper.selectList(Wrappers.<FundProductMappingEntity>lambdaQuery()
                    .eq(FundProductMappingEntity::getFundId, fundId)
                    .eq(FundProductMappingEntity::getDeleteFlag, 0)
            );
             integerStream = fundList.stream().map(FundProductMappingEntity::getProductId).toList();
             if (CollUtil.isNotEmpty(integerStream)){
                 flag = fundList.get(0).getStatus() ;
             }
        }
        if (ObjUtil.isNotNull(rongdanId)){
            MPJLambdaWrapper<FundRongdanProductMappingEntity> eq = new MPJLambdaWrapper<FundRongdanProductMappingEntity>()
                    .selectAs(FundProductMappingEntity::getProductId, FundProductMappingEntity::getProductId)
                    .selectAs(FundProductMappingEntity::getStatus, FundProductMappingEntity::getStatus)
                    .innerJoin(FundProductMappingEntity.class, on -> on.
                            eq(FundProductMappingEntity::getProductId, FundRongdanProductMappingEntity::getProductId)
                            .eq(FundProductMappingEntity::getDeleteFlag, 0)
                            .eq(FundProductMappingEntity::getFundId, FundRongdanProductMappingEntity::getFundId)
                    )
                    .eq(FundRongdanProductMappingEntity::getDeleteFlag, 0)
                    .eq(FundRongdanProductMappingEntity::getFundId, fundId)
                    .eq(FundRongdanProductMappingEntity::getRongdanId, rongdanId);
            List<FundProductMappingEntity> fundList = fundRongdanProductMappingMapper.selectJoinList(FundProductMappingEntity.class, eq);
            integerStream = fundList.stream().map(FundProductMappingEntity::getProductId).toList();
            if (CollUtil.isNotEmpty(integerStream)){
                flag = fundList.get(0).getStatus() ;
            }
        }
            fundRongdanDetailsVO.setFundId(fundId);
            fundRongdanDetailsVO.setRongdanId(rongdanId);
            fundRongdanDetailsVO.setProductFlag(flag);
            fundRongdanDetailsVO.setProductId(integerStream);

        return fundRongdanDetailsVO;
    }
    @Override
    public Boolean productFlagRongDan(FundRongdanDetailsVO fundRongdanDetailsVO) {
//        productFundMappingMapper    fundporductmapper  没有融担id插入这个表数据

        //判断是否有融担公司数据
        List<FundProductMappingEntity> fundProductMappingEntities = productFundMappingMapper.selectList(Wrappers.<FundProductMappingEntity>lambdaQuery()
                .eq(FundProductMappingEntity::getFundId, fundRongdanDetailsVO.getFundId())
                .eq(FundProductMappingEntity::getDeleteFlag, 0)
        );
        Set<Integer> source = fundProductMappingEntities.stream().map(FundProductMappingEntity::getProductId).collect(Collectors.toSet());
        Set<Integer> productId = fundRongdanDetailsVO.getProductId().stream().collect(Collectors.toSet());
        //没有融担公司
        if (fundRongdanDetailsVO.getRongdanId() == null) {
            equalSaveAndUpdate(source,productId,fundRongdanDetailsVO,1);
        }else {
        //有融担公司  添加或修改fundProductMapping表中的数据
        equalSaveAndUpdate(source,productId,fundRongdanDetailsVO,1);
        //添加或修改fundProductMapping表中的数据
        List<FundRongdanProductMappingEntity> fundRongdanProductMappingList = fundRongdanProductMappingMapper.selectList(Wrappers.<FundRongdanProductMappingEntity>lambdaQuery()
                .eq(FundRongdanProductMappingEntity::getFundId, fundRongdanDetailsVO.getFundId())
                .eq(FundRongdanProductMappingEntity::getRongdanId,fundRongdanDetailsVO.getRongdanId())
                .eq(FundRongdanProductMappingEntity::getDeleteFlag, 0));
            Set<Integer> sourceRongDan = fundRongdanProductMappingList.stream().map(FundRongdanProductMappingEntity::getProductId).collect(Collectors.toSet());
        equalSaveAndUpdate(sourceRongDan,productId,fundRongdanDetailsVO,2);
        }
        //todo 将现有的数据集合 重新 修改表状态
        List<FundProductMappingEntity> newList = productFundMappingMapper.selectList(
                new LambdaQueryWrapper<FundProductMappingEntity>()
                        .eq(FundProductMappingEntity::getDeleteFlag, 0)
                        .eq(FundProductMappingEntity::getFundId, fundRongdanDetailsVO.getFundId())
        );
         productFundMappingMapper.update(new LambdaUpdateWrapper<FundProductMappingEntity>()
                .set(FundProductMappingEntity::getStatus, fundRongdanDetailsVO.getProductFlag())
                .eq(FundProductMappingEntity::getDeleteFlag, 0)
                .eq(FundProductMappingEntity::getFundId, fundRongdanDetailsVO.getFundId())
                .in(FundProductMappingEntity::getProductId, fundRongdanDetailsVO.getProductId())
        );
        return true;
    }
    //source库里的数据   productId前端传过来的数据 fundRongdanDetailsVO 参数   是否有融担公司 rongdanId：1：没有融担,2：有融担公司
    public void equalSaveAndUpdate(Set<Integer> source,Set<Integer> productId,FundRongdanDetailsVO fundRongdanDetailsVO,Integer rongdanId){
        // 关键：复制集合进行操作，不修改原始集合
        Set<Integer> productIdCopy = new HashSet<>(productId);
        productIdCopy.removeAll(source);
        Set<Integer> sourceOnly = new HashSet<>(source);
        sourceOnly.removeAll(productId);
        Set<Integer> productIdOnly = new HashSet<>(productId);
        productIdOnly.removeAll(source);
        Set<Integer> commonElements = new HashSet<>(source);
        commonElements.retainAll(productId);
        log.info("source 原表中的数据：{}",sourceOnly);
        log.info("productId 新增的数据：{}",productIdOnly);
        log.info("共同存在的数据：{}",commonElements);
        if (rongdanId == 1){ //没有公司
            if (CollUtil.isNotEmpty(productIdOnly)){
                //添加新数据  productIdOnly有数据说明需要新增
                saveAndUpdate(1, productIdOnly, fundRongdanDetailsVO);
                commonElements.addAll(productIdOnly);
                productFundMappingMapper.update(Wrappers.<FundProductMappingEntity>lambdaUpdate()
                        .set(FundProductMappingEntity::getStatus, fundRongdanDetailsVO.getProductFlag())
                        .eq(FundProductMappingEntity::getFundId, fundRongdanDetailsVO.getFundId())
                        .in(FundProductMappingEntity::getProductId,commonElements)
                        .eq(FundProductMappingEntity::getDeleteFlag, 0)
                );
            }
            if (CollUtil.isNotEmpty(sourceOnly)){
                //删除旧数据 sourceOnly有数据说明需要删除
                saveAndUpdate(2, sourceOnly, fundRongdanDetailsVO);
            }
        }else {//有融担公司
            //id不相同
            if (CollUtil.isNotEmpty(productIdOnly)){
                //添加新数据  productIdOnly有数据说明需要新增
                rongDansaveAndUpdate(1, productIdOnly, fundRongdanDetailsVO);
                commonElements.addAll(productIdOnly);
                fundRongdanProductMappingMapper.update(Wrappers.<FundRongdanProductMappingEntity>lambdaUpdate()
                        .set(FundRongdanProductMappingEntity::getProductFlag, fundRongdanDetailsVO.getProductFlag())
                        .eq(FundRongdanProductMappingEntity::getFundId, fundRongdanDetailsVO.getFundId())
                        .in(FundRongdanProductMappingEntity::getProductId,commonElements)
                        .eq(FundRongdanProductMappingEntity::getDeleteFlag, 0)
                );
            }
            if (CollUtil.isNotEmpty(sourceOnly)){
                //删除旧数据 sourceOnly有数据说明需要删除
                rongDansaveAndUpdate(2, sourceOnly, fundRongdanDetailsVO);
            }
        }

    }
    //更新删除操作    status 1:新增,2:删除   Set操作的数据
    public void saveAndUpdate(Integer status,Set<Integer> IdSet,FundRongdanDetailsVO fundRongdanDetailsVO){
        if (status == 1) {
            IdSet.stream().forEach(pdcId ->{
                FundProductMappingEntity fundProductMappingEntity = new FundProductMappingEntity();
                fundProductMappingEntity.setFundId(fundRongdanDetailsVO.getFundId());
                fundProductMappingEntity.setProductId(pdcId);
                fundProductMappingEntity.setProductName(productNameAndFundName(pdcId, null));
                fundProductMappingEntity.setFundName(productNameAndFundName(null, fundRongdanDetailsVO.getFundId()));
                fundProductMappingEntity.setDeleteFlag(0);
                fundProductMappingEntity.setStatus(fundRongdanDetailsVO.getProductFlag());
                productFundMappingMapper.insert(fundProductMappingEntity);
            });
        }else {
            IdSet.stream().forEach(pdcId ->{
                productFundMappingMapper.update(Wrappers.<FundProductMappingEntity>lambdaUpdate()
                        .set(FundProductMappingEntity::getStatus, fundRongdanDetailsVO.getProductFlag())
                        .set(FundProductMappingEntity::getDeleteFlag, 1)
                        .eq(FundProductMappingEntity::getFundId, fundRongdanDetailsVO.getFundId())
                        .eq(FundProductMappingEntity::getProductId, pdcId)
                );
            });
        }
    }
    public void rongDansaveAndUpdate(Integer status,Set<Integer> IdSet,FundRongdanDetailsVO fundRongdanDetailsVO){
        if (status == 1) {
            IdSet.stream().forEach(pdcId ->{
                String ruleNumber = String.valueOf(System.currentTimeMillis());
                FundRongdanProductMappingEntity fundLists = new FundRongdanProductMappingEntity();
                fundLists.setFundId(fundRongdanDetailsVO.getFundId());
                fundLists.setRongdanId(fundRongdanDetailsVO.getRongdanId());
                fundLists.setProductFlag(fundRongdanDetailsVO.getProductFlag());
                fundLists.setRuleNumber(ruleNumber);
                fundLists.setDeleteFlag(0);
                fundLists.setProductId(pdcId);
                fundLists.setProductFlag(fundRongdanDetailsVO.getProductFlag());
                fundRongdanProductMappingMapper.insert(fundLists);
            });
        }else {
            IdSet.stream().forEach(pdcId ->{
                fundRongdanProductMappingMapper.update(Wrappers.<FundRongdanProductMappingEntity>lambdaUpdate()
                        .set(FundRongdanProductMappingEntity::getDeleteFlag, 1)
                        .eq(FundRongdanProductMappingEntity::getFundId, fundRongdanDetailsVO.getFundId())
                        .eq(FundRongdanProductMappingEntity::getProductId, pdcId)
                );
            });
        }
    }




    //根据id获取name  产品productId  资方fundId
    public String productNameAndFundName(Integer productId,Integer fundId){
        HashMap<String, Object> map = new HashMap<>();
        String name =  null;
        if (productId != null){
            ProductInfoEntity productInfoEntity = productInfoMapper.selectById(productId);
            name = productInfoEntity.getName();
        }else if (fundId != null){
            FundInfoEntity fundInfoEntity = fundInfoMapper.selectById(fundId);
            name = fundInfoEntity.getName();
        }
        return name;
    }
    @Override
    public Result<Boolean> saveRongDanProduct(FundRongdanDetailsVO fundRongdanDetailsVO) {
        List<Integer> productId = fundRongdanDetailsVO.getProductId();
        if (fundRongdanDetailsVO.getRongdanId() == null) {
            productId.stream().forEach(pdcId -> {
                FundProductMappingEntity fundProductMappingEntities = productFundMappingMapper.selectOne(Wrappers.<FundProductMappingEntity>lambdaQuery()
                        .eq(FundProductMappingEntity::getFundId, fundRongdanDetailsVO.getFundId())
                        .eq(FundProductMappingEntity::getProductId,pdcId)
                        .eq(FundProductMappingEntity::getDeleteFlag, 0)
                );
                if (fundProductMappingEntities == null){
                    FundProductMappingEntity fundProductMappingEntity = new FundProductMappingEntity();
                    fundProductMappingEntity.setFundId(fundRongdanDetailsVO.getFundId());
                    fundProductMappingEntity.setProductId(pdcId);
                    fundProductMappingEntity.setProductName(productNameAndFundName(pdcId, null));
                    fundProductMappingEntity.setFundName(productNameAndFundName(null, fundRongdanDetailsVO.getFundId()));
                    fundProductMappingEntity.setDeleteFlag(0);
                    fundProductMappingEntity.setStatus(0);
                    productFundMappingMapper.insert(fundProductMappingEntity);
                }
            });
        }else {
            productId.stream().forEach(pdcId -> {
                FundRongdanProductMappingEntity fundRongdanProductMappingEntity = fundRongdanProductMappingMapper.selectOne(Wrappers.<FundRongdanProductMappingEntity>lambdaQuery()
                        .eq(FundRongdanProductMappingEntity::getFundId, fundRongdanDetailsVO.getFundId())
                        .eq(FundRongdanProductMappingEntity::getProductId, pdcId)
                        .eq(FundRongdanProductMappingEntity::getDeleteFlag, 0)
                );
                if (fundRongdanProductMappingEntity== null){
                    String ruleNumber = String.valueOf(System.currentTimeMillis());
                    FundRongdanProductMappingEntity fundLists = new FundRongdanProductMappingEntity();
                    fundLists.setFundId(fundRongdanDetailsVO.getFundId());
                    fundLists.setRongdanId(fundRongdanDetailsVO.getRongdanId());
                    fundLists.setProductFlag(fundRongdanDetailsVO.getProductFlag());
                    fundLists.setRuleNumber(ruleNumber);
                    fundLists.setDeleteFlag(0);
                    fundLists.setProductId(pdcId);
                    fundLists.setProductFlag(1);
                    fundRongdanProductMappingMapper.insert(fundLists);
                }
                FundProductMappingEntity fundProductMappingEntities = productFundMappingMapper.selectOne(Wrappers.<FundProductMappingEntity>lambdaQuery()
                        .eq(FundProductMappingEntity::getFundId, fundRongdanDetailsVO.getFundId())
                        .eq(FundProductMappingEntity::getProductId,pdcId)
                        .eq(FundProductMappingEntity::getDeleteFlag, 0)
                );
                if (fundProductMappingEntities == null){
                    FundProductMappingEntity fundProductMappingEntity = new FundProductMappingEntity();
                    fundProductMappingEntity.setFundId(fundRongdanDetailsVO.getFundId());
                    fundProductMappingEntity.setProductId(pdcId);
                    fundProductMappingEntity.setProductName(productNameAndFundName(pdcId, null));
                    fundProductMappingEntity.setFundName(productNameAndFundName(null, fundRongdanDetailsVO.getFundId()));
                    fundProductMappingEntity.setDeleteFlag(0);
                    fundProductMappingEntity.setStatus(0);
                    productFundMappingMapper.insert(fundProductMappingEntity);
                }
            });
        }
        return Result.success(true);
    }

    @Override
    public List<ProductInfoEntity> products() {
        List<ProductInfoEntity> productInfoEntities = productInfoMapper.selectList(new MPJLambdaWrapper<ProductInfoEntity>()
                .eq(ProductInfoEntity::getDeleteFlag, 0)
                .eq(ProductInfoEntity::getProductFlag, 0)
        );
        return productInfoEntities;
    }


}
