<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.longhuan.order.mapper.OrderCustomerInfoMapper">
    <!-- 定义 resultMap -->
    <resultMap id="customerResultMap" type="com.longhuan.order.pojo.vo.OrderCustomerVO">
        <result property="realname" column="realname"/>
        <result property="mobile" column="mobile"/>
        <result property="sex" column="sex"/>
        <result property="idcard" column="idcard"/>
        <result property="qq" column="qq"/>
        <result property="nation" column="nation"/>
        <result property="marital_status" column="marital_status"/>
        <result property="wechat" column="wechat"/>
        <result property="birth_address" column="birth_address"/>
        <result property="income" column="income"/>
        <result property="child" column="child"/>
        <result property="email" column="email"/>
        <result property="alipay" column="alipay"/>
        <result property="expend" column="expend"/>
        <result property="house_type" column="house_type"/>
        <result property="bank_card" column="bank_card"/>
        <result property="open_name" column="open_name"/>
        <result property="bank_mobile" column="bank_mobile"/>
        <result property="education" column="education"/>
        <result property="part_work" column="part_work"/>
        <result property="o_house" column="o_house"/>
        <result property="family_income" column="family_income"/>
        <result property="address" column="address"/>
        <result property="start_reside_time" column="start_reside_time"/>
        <result property="idcard_expire" column="idcard_expire"/>
    </resultMap>

    <select id="getcustomerselectJoinOne" resultMap="customerResultMap">
        select
        t.name as realname,
        t.phone as mobile,
        case
        when t.gender = 1 then '男'
        when t.gender = 2 then '女'
        else '未知'
        end as sex,
        t.id_number as idcard,
        ' ' as qq,
        t.nation as nation,
        case
        when t.marital_status = 0 then '未婚'
        when t.marital_status = 1 then '丧偶'
        when t.marital_status = 2 then '已婚有子女'
        when t.marital_status = 3 then '已婚无子女'
        when t.marital_status = 4 then '离异'
        else '未知'
        end as marital_status,
        ' ' as wechat,
        t.id_card_detailed_address as birth_address,
        t.monthly_income as income,
        t.number_of_dependents as child,
        t.email as email,
        ' ' as alipay,
        t.monthly_expenditure as expend,
        t.reside_status as house_type,
        t.bank_card_number as bank_card,
        t.opening_bank as open_name,
        t.bank_reserved_phone as bank_mobile,
        case
        when t.educational_background = 1 then '博士'
        when t.educational_background = 10 then '硕士'
        when t.educational_background = 30 then '大学专科'
        when t.educational_background = 40 then '中专'
        when t.educational_background = 50 then '高中'
        when t.educational_background = 60 then '初中及以下'
        when t.educational_background = 20 then '大学本科'
        else '未知'
        end as education,
        5 as part_work,
        ' ' as o_house,
        monthly_income as family_income,
        t.residential_address as address,
        EXTRACT(EPOCH FROM t.start_reside_date) as start_reside_time,
        CASE
        WHEN t.validity_end = '长期' THEN EXTRACT(EPOCH FROM TIMESTAMP '2125-01-01')
        ELSE EXTRACT(EPOCH FROM TO_TIMESTAMP(t.validity_end, 'YYYY-MM-DD'))
        END AS idcard_expire
        from
        LH_ORDER_CUSTOMER_INFO t
        left join LH_ORDER_INFO t1 on
        (t1.customer_id = t.id) and t.delete_flag =0
        where
        <if test="orderId != null ">
            t1.id = #{orderId}
        </if>
    </select>
</mapper>