package com.longhuan.order.kingdee.service.Impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.enums.PreApplyInfoFundStatus;
import com.longhuan.common.core.enums.PreFundResultEnum;
import com.longhuan.common.core.result.IResultCode;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.core.result.ResultCode;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.converter.ApprovalApplyInfoConverter;
import com.longhuan.order.enums.ApprovalApplyInfoBusinessStatus;
import com.longhuan.order.enums.PreApplyInfoManagerStatus;
import com.longhuan.order.enums.RiskPolicyResult;
import com.longhuan.order.feign.DingDrawMoneyFeign;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.kingdee.feign.KingdeeFeign;
import com.longhuan.order.kingdee.pojo.*;
import com.longhuan.order.kingdee.pojo.vo.*;
import com.longhuan.order.kingdee.service.KingdeeService;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.PreApprovalApplyInfoService;
import com.longhuan.order.service.PreManagerApprovalService;
import com.longhuan.order.service.RiskLaunchOnPreApprovalAgainService;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.order.util.SignatureUtils;
import com.longhuan.user.pojo.dto.GetDeptIdByNameDTO;
import com.longhuan.user.pojo.dto.QrCodeDTO;
import com.longhuan.user.pojo.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KingdeeServiceImpl implements KingdeeService {
    private final KingdeeFeign kingdeeFeign;
    private final OrderInfoMapper orderInfoMapper;
    private final KingdeeInfoMapper kingdeeInfoMapper;
    private final PreApprovalApplyInfoMapper preApprovalApplyInfoMapper;
    private final PreOcrIdentityCardMapper preOcrIdentityCardMapper;
    private final PreOcrVehicleInfoMapper preOcrVehicleInfoMapper;
    private final FundProductMappingMapper fundProductMappingMapper;
    private final ProductInfoMapper productInfoMapper;
    private final OrderContactPersonMapper orderContactPersonMapper;
    private final OrderCustomerInfoMapper orderCustomerInfoMapper;
    private final PreFundInfoMapper preFundInfoMapper;
    private final UserFeign userFeign;
    private final PreOcrBusinessLicenseMapper preOcrBusinessLicenseMapper;
    private final PreApprovalApplyInfoService preApprovalApplyInfoService;
    private final OrderVehicleInfoMapper orderVehicleInfoMapper;
    private final OrderFileMapper orderFileMapper;
    private final OrderNodeRecordMapper orderNodeRecordMapper;
    private final OrderAmountMapper orderAmountMapper;
    private final DxReservationInfoMapper dxReservationInfoMapper;
    private final OrderCompanyInfoMapper orderCompanyInfoMapper;
    private final PreRiskPolicyResultMapper preRiskPolicyResultMapper;
    private final FundDeptMapper fundDeptMapper;
    private final DingDrawMoneyFeign dingDrawMoneyFeign;
    private final StoreAddressInfoMapper storeAddressInfoMapper;
    private final ApprovalApplyInfoConverter approvalApplyInfoConverter;
    private final RiskLaunchOnPreApprovalAgainService riskLaunchOnPreApprovalAgainService;
    private final Car300DataMapper car300DataMapper;
    private final PreFddFinishFileMapper preFddFinishFileMapper;
    private final OrderFeeInfoMapper orderFeeInfoMapper;
    private final PreManagerApprovalService preManagerApprovalService;
    @Value("${kingdee.datapush.signature.bodySecret}")
    private String bodySecret;
    @Value("${kingdee.datapush.signature.key}")
    private String key;
    @Value("${kingdee.datapush.enable}")
    private Boolean enable;
    @Value("${kingdee.datapull.codeurl}")
    private String url;
    @Value("${app.hostname}")
    private String hostname;
    @Value("${kingdee.datapull.staticCodeUrl}")
    private String staticCodeUrl;
    @Value("${kingdee.datapull.msgUrl}")
    private String msgUrl;
    @Value("${kingdee.datapull.businessAddedUrl}")
    private String  businessAddedUrl;
    @Value("${kingdee.datapull.customerConfirmUrl}")
    private String customerConfirmUrl;
    @Value("${kingdee.datapull.contractSigning}")
    private String contractSigning;
    @Value("${kingdee.datapull.carInfoPageUrl}")
    private String carInfoPageUrl;
    //验证错误信息
    private static void extracted(String result) {
        JSONObject entries = JSONUtil.parseObj(result);
        if (!"200".equals(entries.getStr("code"))) {
            throw new BusinessException("电销请求信息失败：" + entries.getStr("message"));
        }
    }

    @Async
    @Override
    public void submit(Object[] args, Object result) {
        // 重试次数
        int retryCount = 3;
        while (retryCount > 0) {
            try {
                if (args.length > 1 && args[0] instanceof IdentityCardDTO dto && result instanceof Result<?> resu && resu.getData() instanceof PreIdVO res) {
                    //保存金蝶信息com.longhuan.order.service.impl.PreOcrIdentityCardServiceImpl.insertIdentityCard
                    insertIdentityCard(dto, res);
//                } else if (result instanceof PreApprovalApplyInfoEntity dto) {
//                    //推送金蝶预审信息com.longhuan.order.service.impl.PreApprovalApplyInfoServiceImpl.submit
//                    pushPreTrialInformation(dto);
                } else if (args.length == 1 && args[0] instanceof Integer dto && result instanceof String res && !"fail".equals(res)) {
                    pushPreTrialInformation(dto);
                    submitOrderFlow(new KingdeeOrderFlowDTO().setState(Events.AGREES.getCode()).setFromNode(States.PRE_APPROVAL.getNode()).setToNode(States.RISK_CONTROL.getNode()).setPreId(dto));
                } else if (args.length == 1 && args[0] instanceof PreApproveFundStatusDTO dto && result instanceof Boolean res) {
                    //更新金蝶预审资方状态com.longhuan.order.service.impl.PreManagerApprovalServiceImpl.updateFundStatus
                    if (PreFundResultEnum.PASS.equals(dto.getStatus()) || PreFundResultEnum.REJECT.equals(dto.getStatus())) {
                        updateFundStatus(dto, res);
                    }
                } else if (args.length == 1 && args[0] instanceof OrderCustomerInfoDTO dto && result instanceof OrderCustomerInfoEntity orderCustomerInfoEntity) {
                    //客户信息补录com.longhuan.order.service.impl.OrderCustomerInfoServiceImpl.insertOrderCustomerInfo
                    insertOrderCustomerInfo(dto);
                } else if (args.length == 1 && args[0] instanceof PreManagerApprovalSubmitDTO dto && result instanceof Integer res) {
                    //orderId绑定金蝶com.longhuan.order.service.impl.PreManagerApprovalServiceImpl.submitPreInfo
                    updateKingdeeInfo(dto, res);
                } else if (args.length == 1 && args[0] instanceof VehicleInfoEditDTO dto && result instanceof Boolean res && res) {
                    //客户车辆信息补录com.longhuan.order.service.impl.OrderVehicleInfoServiceImpl.updateVehicleInfo
                    supplementOfCustomerVehicleInformation(dto);
                } else if (args.length == 1 && args[0] instanceof SaveOrderInfoDTO dto && result instanceof OrderInfoEntity) {
                    //保存订单信息com.longhuan.order.service.impl.OrderServiceImpl.saveOrderInfo
                    saveOrderInfo(dto);
                } else if (args.length == 2 && args[0] instanceof OrderSubmitDTO dto && result instanceof SubmitResultVO) {
                    //提交订单推送附件com.longhuan.order.service.impl.ApprovalServiceImpl.orderSubmit
                    insertAnnex(dto);
                }else if (args.length == 2 && args[0] instanceof ResetManagerDTO dto && result instanceof Boolean res && res) {
                    //订单重置com.longhuan.order.service.impl.PreApprovalApplyInfoServiceImpl.resetManager
                    subMitResetYqOrder(new KingdeeOrderFlowDTO().setPreId(dto.getPreId()));
                }
                break;
            } catch (Exception e) {
                retryCount--;
                if (retryCount == 0) {
                    log.error("failedToPushKingdeeInformation KingdeeServiceImpl:updateKingdeeInfo:{}", e.getMessage());

                }
            }

        }
    }


    //保存orderId到金蝶信息表
    private void updateKingdeeInfo(PreManagerApprovalSubmitDTO dto, Integer result) {
        KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class)
                .eq(KingdeeInfoEntity::getPreId, dto.getPreId())
                .eq(KingdeeInfoEntity::getDeleteFlag, 0), false);
        if (Objects.isNull(kingdeeInfoEntity)) {
               PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(dto.getPreId());
                if (Objects.isNull(preApprovalApplyInfoEntity)) {
                    return ;
                }
                DxReservationInfoEntity dxReservationInfoEntity = dxReservationInfoMapper.selectOne(new LambdaQueryWrapper<>(DxReservationInfoEntity.class)
                        .eq(DxReservationInfoEntity::getPhone, preApprovalApplyInfoEntity.getPhone())
                        .ge(DxReservationInfoEntity::getCreateTime, LocalDateTime.now().minusDays(90))
                        .eq(DxReservationInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(DxReservationInfoEntity::getCreateTime), false);
                if (Objects.isNull(dxReservationInfoEntity)) {
                    return ;
                }
            submitOrderFlow(new KingdeeOrderFlowDTO().setState(Events.AGREES.getCode()).setFromNode(States.FUNDS_PRE_APPROVAL.getNode()).setToNode(States.BUSINESS_ADDED_INFO.getNode()).setPreId(dto.getPreId()).setOrderId(result));
        }else {
            kingdeeInfoEntity.setOrderId(result);
            log.info("updateKingdeeInformation KingdeeServiceImpl:updateKingdeeInfo kingdeeInfoEntity:{}", kingdeeInfoEntity);
            kingdeeInfoMapper.updateById(kingdeeInfoEntity);
            submitOrderFlow(new KingdeeOrderFlowDTO().setState(Events.AGREES.getCode()).setFromNode(States.FUNDS_PRE_APPROVAL.getNode()).setToNode(States.BUSINESS_ADDED_INFO.getNode()).setOrderId(result).setPreId(dto.getPreId()));
        }
//        KingdeePreToOrderDTO kingdeePreToOrderDTO = new KingdeePreToOrderDTO()
//                .setOrderId(result)
//                .setPreId(dto.getPreId());
//        PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(new LambdaQueryWrapper<PreFundInfoEntity>()
//                .eq(PreFundInfoEntity::getPreId, dto.getPreId())
//                .eq(PreFundInfoEntity::getFundId, dto.getFundId())
//                .eq(PreFundInfoEntity::getDeleteFlag, 0), false);
//
//        ProductInfoEntity productInfoEntity = productInfoMapper.selectOne(new LambdaQueryWrapper<ProductInfoEntity>()
//                .eq(ProductInfoEntity::getId, dto.getProductId())
//                .eq(ProductInfoEntity::getStatus, 2)
//                .eq(ProductInfoEntity::getDeleteFlag, 0), false);
//        kingdeePreToOrderDTO.setProduct(new KingdeePreToOrderDTO.Product()
//                .setFund_id(dto.getFundId())
//                .setId(productInfoEntity.getId())
//                .setTitle(productInfoEntity.getName())
//                .setPeriod(productInfoEntity.getTerm())
//                .setRate(productInfoEntity.getMonthlyRate())
//                .setCredit_amt(String.valueOf(preFundInfoEntity.getCreditAmount())));
//        log.info("kingdeeSynchronizesTheOrderStatus KingdeeServiceImpl:updateKingdeeInfo kingdeePreToOrderDTO:{}", kingdeePreToOrderDTO);
//        subMitOfflineOrder(kingdeePreToOrderDTO);

    }

    private void subMitOfflineOrder(KingdeePreToOrderDTO kingdeePreToOrderDTO) {
        String jsonPrettyStr = JSONUtil.toJsonStr(kingdeePreToOrderDTO, new JSONConfig().setIgnoreNullValue(false));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        String result = kingdeeFeign.offlineOrder(body, sign, iv, timestamp);
        extracted(result);
    }

    //客户信息补录
    private void insertOrderCustomerInfo(OrderCustomerInfoDTO dto) {

        KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class).eq(KingdeeInfoEntity::getOrderId, dto.getOrderId()).eq(KingdeeInfoEntity::getDeleteFlag, 0), false);
        if (Objects.isNull(kingdeeInfoEntity)) {
                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
                if (Objects.isNull(orderInfoEntity)) {
                    return ;
                }
                DxReservationInfoEntity dxReservationInfoEntity = dxReservationInfoMapper.selectOne(new LambdaQueryWrapper<>(DxReservationInfoEntity.class)
                        .eq(DxReservationInfoEntity::getPhone, orderInfoEntity.getCustomerPhone())
                        .ge(DxReservationInfoEntity::getCreateTime, LocalDateTime.now().minusDays(90))
                        .eq(DxReservationInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(DxReservationInfoEntity::getCreateTime), false);
                if (Objects.isNull(dxReservationInfoEntity)) {
                    return ;
                }
            }
        KingdeeOrderCustomerInfoDTO kingdeeOrderCustomerInfoDTO = getKingdeeOrderCustomerInfoDTO(dto);
        //调用金蝶接口推送
        log.info("Call the Kingdee interface to push customer information supplement KingdeeServiceImpl:insertOrderCustomerInfo kingdeeOrderCustomerInfoDTO:{}", kingdeeOrderCustomerInfoDTO);
        customerAdditionalRecording(kingdeeOrderCustomerInfoDTO);
    }

    private KingdeeOrderCustomerInfoDTO getKingdeeOrderCustomerInfoDTO(OrderCustomerInfoDTO dto) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<>(OrderInfoEntity.class)
                .eq(OrderInfoEntity::getId, dto.getOrderId()).eq(OrderInfoEntity::getDeleteFlag, 0));
        OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectOne(new LambdaQueryWrapper<>(OrderCustomerInfoEntity.class)
                .eq(OrderCustomerInfoEntity::getId, orderInfoEntity.getCustomerId()));
        List<OrderContactPersonEntity> orderContactPersonEntities = orderContactPersonMapper.selectList(new LambdaQueryWrapper<>(OrderContactPersonEntity.class)
                .eq(OrderContactPersonEntity::getOrderId, dto.getOrderId())
                .eq(OrderContactPersonEntity::getDeleteFlag, 0));
        PreOcrIdentityCardEntity preOcrIdentityCardEntity = preOcrIdentityCardMapper.selectOne(new LambdaQueryWrapper<>(PreOcrIdentityCardEntity.class)
                .eq(PreOcrIdentityCardEntity::getPreId, orderInfoEntity.getPreId())
                .eq(PreOcrIdentityCardEntity::getDeleteFlag, 0)
                .orderByDesc(PreOcrIdentityCardEntity::getCreateTime), false);
        return new KingdeeOrderCustomerInfoDTO()
                .setOrderId(dto.getOrderId())
                .setName(orderCustomerInfoEntity.getName())
                .setPhone(orderCustomerInfoEntity.getPhone())
                .setIdNumber(orderCustomerInfoEntity.getIdNumber())
                .setNation(preOcrIdentityCardEntity.getUpdateNation()).
                setGender(preOcrIdentityCardEntity.getUpdateGender())
                .setEnterpriseNature(orderCustomerInfoEntity.getEnterpriseNature())
                .setAge(orderCustomerInfoEntity.getAge())
                .setMaritalStatus(orderCustomerInfoEntity.getMaritalStatus())
                .setResideStatus(orderCustomerInfoEntity.getResideStatus())
                .setResidentialDetailedAddress(orderCustomerInfoEntity.getResidentialDetailedAddress())
                .setIdCardDetailedAddress(orderCustomerInfoEntity.getIdCardDetailedAddress())
                .setVocational(orderCustomerInfoEntity.getVocational())
                .setResidentialAddress(orderCustomerInfoEntity.getResidentialAddress())
                .setEducationalBackground(orderCustomerInfoEntity.getEducationalBackground())
                .setMonthlyIncome(orderCustomerInfoEntity.getMonthlyIncome().toPlainString())
                .setNumberOfDependents(orderCustomerInfoEntity.getNumberOfDependents())
                .setEnterpriseName(orderCustomerInfoEntity.getEnterpriseName())
                .setEnterpriseAddress(orderCustomerInfoEntity.getEnterpriseAddress())
                .setEnterpriseDetailsAddress(orderCustomerInfoEntity.getEnterpriseDetailsAddress())
                .setCustomerType(orderCustomerInfoEntity.getCustomerType())
                .setDegree(orderCustomerInfoEntity.getDegree())
                .setStartResideDate(LocalDateTimeUtil.format(orderCustomerInfoEntity.getStartResideDate(), "yyyy-MM-dd"))
                .setHouseRent(orderCustomerInfoEntity.getHouseRent())
                .setEmploymentStatus(orderCustomerInfoEntity.getEmploymentStatus())
                .setIndustryInvolved(orderCustomerInfoEntity.getEnterpriseIndustryInvolvedName())
                .setEnterprisePhone(orderCustomerInfoEntity.getEnterprisePhone())
                .setDepartment(orderCustomerInfoEntity.getDepartment())
                .setDuties(orderCustomerInfoEntity.getDuties())
                .setProfessionalTitle(orderCustomerInfoEntity.getProfessionalTitle())
                .setCurrentUnitStartDate(LocalDateTimeUtil.format(orderCustomerInfoEntity.getCurrentUnitStartDate(), "yyyy-MM-dd"))
                .setPayday(orderCustomerInfoEntity.getPayday()).setSocialCreditCode(orderCustomerInfoEntity.getSocialCreditCode())
                .setMonthlyExpenditure(orderCustomerInfoEntity.getMonthlyExpenditure())
                .setMaxMonthlySupply(orderCustomerInfoEntity.getMaxMonthlySupply())
                .setEmail(orderCustomerInfoEntity.getEmail())
                .setBankCardNumber(orderCustomerInfoEntity.getBankCardNumber())
                .setBankReservedPhone(orderCustomerInfoEntity.getBankReservedPhone())
                .setOpeningBank(orderCustomerInfoEntity.getOpeningBank())
                .setIdCardAddress(orderCustomerInfoEntity.getIdCardAddress())
                .setValidityStartDate(LocalDateTimeUtil.format(orderCustomerInfoEntity.getValidityStartDate(), "yyyy-MM-dd"))
                .setValidityEnd(orderCustomerInfoEntity.getValidityEnd()).setIssuingAuthority(orderCustomerInfoEntity.getIssuingAuthority())
                .setIdType(orderCustomerInfoEntity.getIdType())
                .setOrderContactPersonDTOList(JSONUtil.toJsonStr(orderContactPersonEntities, new JSONConfig().setIgnoreNullValue(false)))
                .setJob(orderCustomerInfoEntity.getJob());
    }

    private void customerAdditionalRecording(KingdeeOrderCustomerInfoDTO kingdeeOrderCustomerInfoDTO) {
        String jsonPrettyStr = JSONUtil.toJsonStr(kingdeeOrderCustomerInfoDTO, new JSONConfig().setIgnoreNullValue(false));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        String result = kingdeeFeign.customerAdditionalRecording(body, sign, iv, timestamp);
        extracted(result);
    }

    //更新预审状态
    void updateFundStatus(PreApproveFundStatusDTO dto, Boolean res) {
        KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class).eq(KingdeeInfoEntity::getPreId, dto.getPreId()).eq(KingdeeInfoEntity::getDeleteFlag, 0), false);
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(dto.getPreId());
        if (Objects.isNull(kingdeeInfoEntity)) {
             preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(dto.getPreId());
            if (Objects.isNull(preApprovalApplyInfoEntity)) {
                return ;
            }
            DxReservationInfoEntity dxReservationInfoEntity = dxReservationInfoMapper.selectOne(new LambdaQueryWrapper<>(DxReservationInfoEntity.class)
                    .eq(DxReservationInfoEntity::getPhone, preApprovalApplyInfoEntity.getPhone())
                    .ge(DxReservationInfoEntity::getCreateTime, LocalDateTime.now().minusDays(90))
                    .eq(DxReservationInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(DxReservationInfoEntity::getCreateTime), false);
            if (Objects.isNull(dxReservationInfoEntity)) {
                return ;
            }
            kingdeeInfoEntity= new KingdeeInfoEntity().setPreId(dto.getPreId());
        }
        PreFundInfoEntity preFundInfoEntity = preFundInfoMapper.selectOne(new LambdaQueryWrapper<>(PreFundInfoEntity.class)
                .eq(PreFundInfoEntity::getPreId, dto.getPreId()).
                eq(PreFundInfoEntity::getFundId, dto.getFundId())
                .eq(PreFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(PreFundInfoEntity::getCreateTime), false);
        KingdeeSyncPreInfoDTO kingdeeSyncPreInfoDTO = getKingdeeSyncPreInfoDTO(dto, preFundInfoEntity, kingdeeInfoEntity, preApprovalApplyInfoEntity);
        if (kingdeeSyncPreInfoDTO == null) return;
        log.info("pushKingdeePreTrialInformation KingdeeServiceImpl:updateFundStatus kingdeeSyncPreInfoDTO:{}", kingdeeSyncPreInfoDTO);
        subMitPreInfo(kingdeeSyncPreInfoDTO);
    }

    @Nullable
    private KingdeeSyncPreInfoDTO getKingdeeSyncPreInfoDTO(PreApproveFundStatusDTO dto, PreFundInfoEntity preFundInfoEntity, KingdeeInfoEntity kingdeeInfoEntity, PreApprovalApplyInfoEntity preApprovalApplyInfoEntity) {
        List<FundDeptEntity> fundInfoEntities = fundDeptMapper.selectList(
                new LambdaQueryWrapper<FundDeptEntity>()
                        .in(FundDeptEntity::getDeptId, 134, 135)
                        .eq(FundDeptEntity::getDeleteFlag, 0)
        );
        List<Integer> list = fundInfoEntities.stream().map(FundDeptEntity::getFundId).toList();
        List<PreFundInfoEntity> finalFundInfoEntities = preFundInfoMapper.selectList(
                new LambdaQueryWrapper<PreFundInfoEntity>()
                        .eq(ObjUtil.isNotNull(preFundInfoEntity.getPreId()), PreFundInfoEntity::getPreId, preFundInfoEntity.getPreId())
                        .in(PreFundInfoEntity::getFundId, list)
                        .eq(PreFundInfoEntity::getDeleteFlag, 0)
        );
        Set<Integer> fundIds = finalFundInfoEntities.stream()
                .map(PreFundInfoEntity::getFundId)
                .collect(Collectors.toSet());
        KingdeeSyncPreInfoDTO kingdeeSyncPreInfoDTO = new KingdeeSyncPreInfoDTO()
                .setPre_id(preFundInfoEntity.getPreId())

                .setStatus(preApprovalApplyInfoEntity.getFundStatus().getCode())
                .setCreditAmt(String.valueOf(preFundInfoEntity.getCreditAmount()))
                .setEstimate_amount(String.valueOf(preFundInfoEntity.getEvaluationAmount()))
                .setFund_credit_amount(String.valueOf(preFundInfoEntity.getFundCreditAmount()))
                .setFundId(preFundInfoEntity.getFundId())
                .setFundName(FundEnum.getFundEnum(preFundInfoEntity.getFundId()).getFundName())
                .setRemark(preFundInfoEntity.getFundRemark());
        if (ObjUtil.isNotEmpty(kingdeeInfoEntity)){
            kingdeeSyncPreInfoDTO.setClue_id(kingdeeInfoEntity.getClueId()).setServicer_id(kingdeeInfoEntity.getServiceId());
        }
        kingdeeSyncPreInfoDTO.setIsAllReject(2);
        if (Objects.equals(preApprovalApplyInfoEntity.getSourceType(), 1) && (Objects.equals(preApprovalApplyInfoEntity.getRegionId(), 24) || Objects.equals(preApprovalApplyInfoEntity.getRegionId(), 56))) {
            if (list.size() == 1) {
                if (Objects.equals(preApprovalApplyInfoEntity.getFundStatus(), PreApplyInfoFundStatus.APPROVAL_FAIL)) {
                    kingdeeSyncPreInfoDTO.setIsAllReject(1);
                }
            } else {
                if (fundIds.size() == list.size() && finalFundInfoEntities.stream().noneMatch(entity -> Objects.equals(entity.getFundResult(), PreFundResultEnum.PASS))) {
                    kingdeeSyncPreInfoDTO.setIsAllReject(1);
                }
            }
        }
        if (Objects.equals(dto.getStatus(), PreFundResultEnum.PASS)) {
            StoreProductDTO storeProductDTO = new StoreProductDTO();
            storeProductDTO.setPreId(dto.getPreId());
            storeProductDTO.setFundId(dto.getFundId());
            List<ProductFundMappingVO> productFundMappingVO = preApprovalApplyInfoService.storeProductList(storeProductDTO, null);
            List<Integer> productIds = productFundMappingVO.stream().map(ProductFundMappingVO::getProductId).toList();
            if (productIds.isEmpty()) {
                return null;
            }
            List<KingdeeSyncPreInfoDTO.Product> products = productInfoMapper.selectJoinList(KingdeeSyncPreInfoDTO.Product.class,
                    new MPJLambdaWrapper<>(ProductInfoEntity.class)
                            .selectAs(ProductInfoEntity::getId, KingdeeSyncPreInfoDTO.Product::getId)
                            .selectAs(ProductInfoEntity::getName, KingdeeSyncPreInfoDTO.Product::getTitle)
                            .selectAs(ProductInfoEntity::getTerm, KingdeeSyncPreInfoDTO.Product::getPeriod)
                            .selectAs(ProductInfoEntity::getMonthlyRate, KingdeeSyncPreInfoDTO.Product::getRate)
                            .selectAs(ProductInfoEntity::getYearRate, KingdeeSyncPreInfoDTO.Product::getYearRate)
                            .leftJoin(FundProductMappingEntity.class, FundProductMappingEntity::getProductId, ProductInfoEntity::getId)
                            .in(ProductInfoEntity::getId, productIds)
                            .eq(ProductInfoEntity::getDeleteFlag, 0)
                            .eq(FundProductMappingEntity::getFundId, dto.getFundId())
                            .eq(ProductInfoEntity::getStatus, 2));
            products = products.stream().map(product -> product.setFund_id(dto.getFundId()).setFundName(FundEnum.getFundEnum(preFundInfoEntity.getFundId()).getFundName())).toList();
            kingdeeSyncPreInfoDTO.setProducts(products);
        }
        return kingdeeSyncPreInfoDTO;
    }


    //提交资方审核信息
    private void subMitPreInfo(KingdeeSyncPreInfoDTO kingdeeSyncPreInfoDTO) {
        String jsonPrettyStr = JSONUtil.toJsonStr(kingdeeSyncPreInfoDTO, new JSONConfig().setIgnoreNullValue(false));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        String result = kingdeeFeign.syncPreInfo(body, sign, iv, timestamp);
        extracted(result);
    }

    //存储金蝶信息
    private void insertIdentityCard(IdentityCardDTO dto, PreIdVO res) {
        if (!Objects.isNull(dto)) {
            dto.setClueId(res.getClueId())
                    .setServicePhone(dto.getServicePhone())
                    .setServiceId(dto.getServiceId());
            KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class).eq(KingdeeInfoEntity::getPreId, res.getPreId()).eq(KingdeeInfoEntity::getDeleteFlag, 0));
            if (!Objects.isNull(kingdeeInfoEntity)) {
                return;
            }
            if (!Objects.isNull(dto.getClueId())) {
                kingdeeInfoEntity = new KingdeeInfoEntity();
                kingdeeInfoEntity.setClueId(dto.getClueId());
                kingdeeInfoEntity.setPreId(res.getPreId());
                log.info("storeKingdeeInformation KingdeeServiceImpl：insertIdentityCard kingdeeInfoEntity：{}", kingdeeInfoEntity);
                kingdeeInfoMapper.insert(kingdeeInfoEntity);
            }

        }
    }


    private void pushPreTrialInformation(Integer dto) {
        KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class).eq(KingdeeInfoEntity::getPreId, dto).eq(KingdeeInfoEntity::getDeleteFlag, 0), false);
        if (Objects.isNull(kingdeeInfoEntity)) {
            return;
        }
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectOne(new LambdaQueryWrapper<>(PreApprovalApplyInfoEntity.class).eq(PreApprovalApplyInfoEntity::getId, dto).eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0), false);
        //准备金蝶数据
        log.info("prepareKingdeeInformation KingdeeServiceImpl：pushPreTrialInformation kingdeeInfoEntity：{}", kingdeeInfoEntity);
        KingdeePreDTO kingdeePreDTO = getKingdeePreDTO(preApprovalApplyInfoEntity, kingdeeInfoEntity);
        //提交金蝶数据
        log.info("pushKingdeeKingdeeInformation KingdeeServiceImpl：pushPreTrialInformation kingdeePreDTO：{}", kingdeePreDTO);
        subMitPreData(kingdeePreDTO);

    }
    private KingdeePreDTO getKingdeePreDTO(PreApprovalApplyInfoEntity dto, KingdeeInfoEntity kingdeeInfoEntity) {
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectOne(new LambdaQueryWrapper<>(PreApprovalApplyInfoEntity.class).eq(PreApprovalApplyInfoEntity::getId, dto.getId()).eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0), false);
        PreOcrIdentityCardEntity preOcrIdentityCardEntity = preOcrIdentityCardMapper.selectOne(new LambdaQueryWrapper<>(PreOcrIdentityCardEntity.class).eq(PreOcrIdentityCardEntity::getPreId, dto.getId()).eq(PreOcrIdentityCardEntity::getDeleteFlag, 0), false);
        PreOcrVehicleInfoEntity preOcrVehicleInfoEntity = preOcrVehicleInfoMapper.selectOne(new LambdaQueryWrapper<>(PreOcrVehicleInfoEntity.class).eq(PreOcrVehicleInfoEntity::getPreId, dto.getId()).eq(PreOcrVehicleInfoEntity::getDeleteFlag, 0), false);
        String managerName="";
        String managerPhone="";
        if (ObjUtil.isNotNull(preApprovalApplyInfoEntity.getAccountManagerId())){
            try {
                UserInfoVO userInfoVO = userFeign.searchUserName(preApprovalApplyInfoEntity.getAccountManagerId()).getData();
                managerName= userInfoVO.getName();
                managerPhone= userInfoVO.getMobile();
            }catch (Exception e){
                log.error("getKingdeePreDTO searchUserName userId:{} e:{}",preApprovalApplyInfoEntity.getAccountManagerId(),e.getMessage());
            }
        }
        Car300DataEntity car300DataEntity = car300DataMapper.selectOne(
                new LambdaQueryWrapper<Car300DataEntity>()
                        .eq(Car300DataEntity::getVin, preOcrVehicleInfoEntity.getVin())
                        .eq(Car300DataEntity::getDeleteFlag, 0)
                        .orderByDesc(Car300DataEntity::getCreateTime)
                        .last("LIMIT 1")
        );
        String vehicleModel = "";
        if (ObjUtil.isNotEmpty(car300DataEntity)){
            vehicleModel = car300DataEntity.getModelName();
        }
        return new KingdeePreDTO().setClue_id(kingdeeInfoEntity.getClueId()).setPreId(dto.getId()).setState(preApprovalApplyInfoEntity.getRiskStatus()).setPreHearIngSubMitTime(LocalDateTimeUtil.format(preApprovalApplyInfoEntity.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN)).setAmountMoney(preApprovalApplyInfoEntity.getLoanAmount())

                //preOcrIdentityCardEntity
                .setUpdateName(preOcrIdentityCardEntity.getUpdateName())
                .setUpdateGender(preOcrIdentityCardEntity.getUpdateGender())
                .setUpdateNation(preOcrIdentityCardEntity.getUpdateNation())
                .setUpdateIdNumber(preOcrIdentityCardEntity.getUpdateIdNumber())
                .setUpdateAddress(preOcrIdentityCardEntity.getUpdateAddress())
                .setPhone(preOcrIdentityCardEntity.getPhone())
                .setUpdateBirthDate(preOcrIdentityCardEntity.getUpdateBirthDate().format(DateTimeFormatter.ISO_LOCAL_DATE))
                .setUpdateIssuingAuthority(preOcrIdentityCardEntity.getUpdateIssuingAuthority())
                .setUpdateValidityStart(preOcrIdentityCardEntity.getUpdateValidityStart().format(DateTimeFormatter.ISO_LOCAL_DATE))
                .setUpdateValidityEnd(preOcrIdentityCardEntity.getUpdateValidityEnd())
                .setIdCardBackImage(preOcrIdentityCardEntity.getIdCardBackImage())
                .setIdCardFrontImage(preOcrIdentityCardEntity.getIdCardFrontImage())

                //preOcrVehicleInfoEntity
                .setUpdateVehicleNumber(preOcrVehicleInfoEntity.getUpdateVehicleNumber())
                .setUpdateVehicleType(preOcrVehicleInfoEntity.getUpdateVehicleType())
                .setUpdateEngineNumber(preOcrVehicleInfoEntity.getUpdateEngineNumber())
                .setUpdateRegisterDate(preOcrVehicleInfoEntity.getUpdateRegisterDate().format(DateTimeFormatter.ISO_LOCAL_DATE))
                .setUpdateVin(preOcrVehicleInfoEntity.getUpdateVin())
                .setMileage(String.valueOf(preOcrVehicleInfoEntity.getMileage()))
                .setVehicleAge(String.valueOf(preOcrVehicleInfoEntity.getVehicleAge()))
                .setTransferTimes(String.valueOf(preOcrVehicleInfoEntity.getTransferTimes()))
                .setUpdateHolder(preOcrVehicleInfoEntity.getUpdateHolder())
                .setUpdateNatureOfUse(preOcrVehicleInfoEntity.getUpdateNatureOfUse())
                .setUpdateIssueDate(preOcrVehicleInfoEntity.getUpdateIssueDate().format(DateTimeFormatter.ISO_LOCAL_DATE))
                .setUpdateSealInformation(preOcrVehicleInfoEntity.getUpdateSealInformation())
                .setBrand(preOcrVehicleInfoEntity.getBrand())
                .setVehicleSeries(preOcrVehicleInfoEntity.getVehicleSeries())
                .setVehicleModel(StringUtils.isNotEmpty(vehicleModel) ? vehicleModel : preOcrVehicleInfoEntity.getVehicleModel())
//                .setVehicleModel(preOcrVehicleInfoEntity.getVehicleModel())
                .setVehicleYear(preOcrVehicleInfoEntity.getVehicleYear())
                .setGuidePrice(preOcrVehicleInfoEntity.getGuidePrice().toPlainString())
                .setSeatCount(String.valueOf(preOcrVehicleInfoEntity.getSeatCount()))
                .setDrivingLicenseImage(preOcrVehicleInfoEntity.getDrivingLicenseImage())
                .setDrivingLicenseDeputyImage(preOcrVehicleInfoEntity.getDrivingLicenseDeputyImage())
                .setNode(500)
                .setStoreId(preApprovalApplyInfoEntity.getStoreId())
                .setStoreName(preApprovalApplyInfoEntity.getStoreName())
                .setManager_id(preApprovalApplyInfoEntity.getAccountManagerId())
                .setManager_name(managerName)
                .setManager_phone(managerPhone)
                .setRegion_id(preApprovalApplyInfoEntity.getRegionId())
                .setRegion_name(preApprovalApplyInfoEntity.getRegionName());
    }

    private void subMitPreData(KingdeePreDTO kingdeePreDTO) {
        String jsonPrettyStr = JSONUtil.toJsonStr(kingdeePreDTO, new JSONConfig().setIgnoreNullValue(false));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        String result = kingdeeFeign.acceptPreData(body, sign, iv, timestamp);
        extracted(result);
    }
    private void subMitResetYqOrder(KingdeeOrderFlowDTO kingdeeOrderFlowDTO) {
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(kingdeeOrderFlowDTO.getPreId());
        if (preApprovalApplyInfoEntity == null|| preApprovalApplyInfoEntity.getSourceType()==0){
            return;
        }
        String jsonPrettyStr = JSONUtil.toJsonStr(kingdeeOrderFlowDTO, new JSONConfig().setIgnoreNullValue(false));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        String result = kingdeeFeign.resetYqOrder(body, sign, iv, timestamp);
        extracted(result);
    }

    //提交客户车辆信息
    private void supplementOfCustomerVehicleInformation(VehicleInfoEditDTO dto) {
        KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class).eq(KingdeeInfoEntity::getOrderId, dto.getOrderId()).eq(KingdeeInfoEntity::getDeleteFlag, 0));
        if (ObjUtil.isEmpty(kingdeeInfoEntity)) {
                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
                if (Objects.isNull(orderInfoEntity)) {
                    return ;
                }
                DxReservationInfoEntity dxReservationInfoEntity = dxReservationInfoMapper.selectOne(new LambdaQueryWrapper<>(DxReservationInfoEntity.class)
                        .eq(DxReservationInfoEntity::getPhone, orderInfoEntity.getCustomerPhone())
                        .ge(DxReservationInfoEntity::getCreateTime, LocalDateTime.now().minusDays(90))
                        .eq(DxReservationInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(DxReservationInfoEntity::getCreateTime), false);
                if (Objects.isNull(dxReservationInfoEntity)) {
                    return ;
                }
                kingdeeInfoEntity= new KingdeeInfoEntity().setPreId(orderInfoEntity.getPreId()).setOrderId(dto.getOrderId());

        }
        KingdeeCarsAdditionalRecordingDTO kingdeeCarsAdditionalRecordingDTO = getKingdeeCarsAdditionalRecordingDTO(dto, kingdeeInfoEntity);
        //组装车辆信息
        log.info("kingdeeSubmitsVehicleInformation KingdeeServiceImpl:supplementOfCustomerVehicleInformation kingdeeCarsAdditionalRecordingDTO:{}", kingdeeCarsAdditionalRecordingDTO);
        subMitVehicleInfo(kingdeeCarsAdditionalRecordingDTO);

    }

    private KingdeeCarsAdditionalRecordingDTO getKingdeeCarsAdditionalRecordingDTO(VehicleInfoEditDTO vehicleInfoEditDTO, KingdeeInfoEntity kingdeeInfoEntity) {
        OrderVehicleInfoEntity dto = orderVehicleInfoMapper.selectOne(new LambdaQueryWrapper<>(OrderVehicleInfoEntity.class)
                .eq(OrderVehicleInfoEntity::getOrderId, vehicleInfoEditDTO.getOrderId())
                .eq(OrderVehicleInfoEntity::getDeleteFlag, 0));

        return new KingdeeCarsAdditionalRecordingDTO().setClue_id(kingdeeInfoEntity.getClueId()).setOrderId(dto.getOrderId())
                //过户次数
                .setTransferTimes(Long.valueOf(dto.getTransferTimes()))
                //购买时间
                .setBuyDate(dto.getBuyDate())
                //机动车登记证书编号
                .setRegistrationCode(dto.getRegistrationCode())
                //发动机号码
                .setEngineNumber(dto.getEngineNumber())
                //注册日期
                .setRegisterDate(LocalDateTimeUtil.format(dto.getRegisterDate(), DatePattern.NORM_DATETIME_PATTERN))
                //里程数
                .setMileage(dto.getMileage())
                //车龄
                .setVehicleAge(dto.getVehicleAge())
                //排量
                .setDisplacement(dto.getDisplacement())
                // 燃料类型
                .setFuelType(dto.getFuelType())
                //车辆类型
                .setVehicleType(dto.getVehicleType())


                //车辆扩展表
                //购买价格
                .setBuyPrice(dto.getBuyPrice())
                //所有人性质 1：个人 2：公司
                .setOwnerType(dto.getOwnerType())
                //近2年抵押次数
                .setMortgageTime(dto.getMortgageTime())
                //车身颜色
                .setVehicleColor(dto.getVehicleColor())
                //变速器形式
                .setTransmissionType(dto.getTransmissionType())
                //排放标准
                .setEmission(dto.getEmission())
                //座位数
                .setSeatCount(dto.getSeatCount())
                //年审有限期
                .setAnnualDate(dto.getAnnualDate())
                //交强险有限期
                .setCompulsoryDate(dto.getCompulsoryDate())
                //三者险有限期
                .setThirdInsuranceDate(dto.getThirdInsuranceDate())
                //车损险有限期
                .setVehicleInsuranceDate(dto.getVehicleInsuranceDate())
                //最后解押日
                .setLastMortgageReleaseDate(dto.getLastMortgageReleaseDate())
                //最后转移登记日期
                .setLastTransferRegistrationDate(dto.getLastTransferRegistrationDate())
                //上牌地省份
                .setLicenseProvince(dto.getLicenseProvince())
                //上牌地城市
                .setLicenseCity(dto.getLicenseCity())
                //品牌
                .setBrand(dto.getBrand())
                //车系
                .setVehicleSeries(dto.getVehicleSeries())
                //车型
                .setVehicleModel(dto.getVehicleModel())
                // 使用性质
                .setNatureOfUse(dto.getNatureOfUse())
                // 指导价
                .setGuidePrice(dto.getGuidePrice())
                // 车辆评估价
                .setEstimatedValue(dto.getEstimatedValue())
                // 出厂日期
                .setProductionDate(dto.getProductionDate())
                // 前手抵押权人1、银行 2、汽车金融 3、融资租赁 4、其他 5、无
                .setPreviousMortgagee(dto.getPreviousMortgagee())
                // 前手抵押权人
                .setPreviousMortgageeText(dto.getPreviousMortgagee())
                // 车辆登记证书近6个月补办记录:1、近6个月内，车辆登记证有补办记录，且 “转移登记”及 “抵押登记”有完整打印记录 2、近6个月内，车辆登记证有补办记录，且 “转移登记”及 “抵押登记”无完整打印记录 3、近6个月内无补办记录
                .setCertificateReissueRecords(dto.getCertificateReissueRecords())
                //车辆登记证书近6个月补办记录
                .setCertificateReissueRecordsText(dto.getCertificateReissueRecords())
                //非常规二手车类型：1否、2事故车、3泡水车、4火烧车、5其他
                .setUnconventionalUsedType(dto.getUnconventionalUsedType())
                //非常规二手车类型
                .setUnconventionalUsedTypeText(dto.getUnconventionalUsedType())
                //改装车类型：1、否 2、车辆登记证/行驶证与车辆照片显示的车辆型号不一致 3、车辆登记证/行驶证与车辆照片显示的发动机号不一致 4、车辆登记证/行驶证与车辆照片显示的车架号不一致 5、车辆登记证/行驶证与车辆照片显示的车身颜色不一致 （多选用英文逗号分隔）
                .setModifiedType(dto.getModifiedType())
                //改装车类型
                .setModifiedTypeText(dto.getModifiedType())
                .setVin(dto.getVin())
                .setHolder(dto.getHolder());
    }

    //提交客户车辆信息
    private void subMitVehicleInfo(KingdeeCarsAdditionalRecordingDTO kingdeeCarsAdditionalRecordingDTO) {
        String jsonPrettyStr = JSONUtil.toJsonStr(kingdeeCarsAdditionalRecordingDTO, new JSONConfig().setIgnoreNullValue(false).setDateFormat(DatePattern.NORM_DATE_PATTERN));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        String result = kingdeeFeign.carsAdditionalRecording(body, sign, iv, timestamp);
        extracted(result);
    }

    //保存订单信息
    private void saveOrderInfo(SaveOrderInfoDTO dto) {
        KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class).eq(KingdeeInfoEntity::getOrderId, dto.getOrderId()).eq(KingdeeInfoEntity::getDeleteFlag, 0));
        if (Objects.isNull(kingdeeInfoEntity)) {
                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
                if (Objects.isNull(orderInfoEntity)) {
                    return ;
                }
                DxReservationInfoEntity dxReservationInfoEntity = dxReservationInfoMapper.selectOne(new LambdaQueryWrapper<>(DxReservationInfoEntity.class)
                        .eq(DxReservationInfoEntity::getPhone, orderInfoEntity.getCustomerPhone())
                        .ge(DxReservationInfoEntity::getCreateTime, LocalDateTime.now().minusDays(90))
                        .eq(DxReservationInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(DxReservationInfoEntity::getCreateTime), false);
                if (Objects.isNull(dxReservationInfoEntity)) {
                    return ;
                }
        }
        KingdeeOrderInfoDTO kingdeeOrderInfoDTO = getKingdeeOrderInfoDTO(dto);
        log.info("kingdeeSubmitsTheOrderInformation KingdeeServiceImpl:subMitsaveOrderInfo kingdeeOrderInfoDTO:{}", kingdeeOrderInfoDTO);
        subMitsaveOrderInfo(kingdeeOrderInfoDTO);

    }

    @NotNull
    private KingdeeOrderInfoDTO getKingdeeOrderInfoDTO(SaveOrderInfoDTO dto) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
        OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new LambdaQueryWrapper<>(OrderAmountEntity.class)
                .eq(OrderAmountEntity::getOrderId, dto.getOrderId())
                .eq(OrderAmountEntity::getDeleteFlag, 0), false);
        KingdeeOrderInfoDTO kingdeeOrderInfoDTO = BeanUtil.toBean(orderInfoEntity, KingdeeOrderInfoDTO.class);
        kingdeeOrderInfoDTO.setOrderId(dto.getOrderId()).setPreApprovedId(orderInfoEntity.getPreId())
                .setApplyAmount(orderAmountEntity.getHopeAmount())
                .setPaymentType(orderInfoEntity.getPaymentType())
                .setCapitalId(orderInfoEntity.getFundId())
                .setCapitalName(orderInfoEntity.getFundName())
                .setApprovalAmount(null)
                .setRiskAmount(String.valueOf(orderInfoEntity.getRiskAmount()))
                .setManagementConclusion(orderInfoEntity.getManagementConclusion())
                .setQualityTestCommitTime(LocalDateTimeUtil.format(orderInfoEntity.getQualityTestCommitTime(), DatePattern.NORM_DATETIME_PATTERN))
                .setApprovalUpdateAmount(orderInfoEntity.getApprovalUpdateAmount())
                .setStoreId(orderInfoEntity.getDeptId())
                .setStoreName(orderInfoEntity.getStoreName())
                .setRegion_id(orderInfoEntity.getRegionId())
                .setRegion_name(orderInfoEntity.getRegionName())
                .setManager_id(orderInfoEntity.getManagerId())
                .setLicenseData(JSONUtil.toJsonStr(preOcrBusinessLicenseMapper.selectOne(new LambdaQueryWrapper<PreOcrBusinessLicenseEntity>()
                        .eq(PreOcrBusinessLicenseEntity::getPreId, orderInfoEntity.getPreId()))));
        Result<UserInfoVO> userInfoVOResult = userFeign.searchUserName(kingdeeOrderInfoDTO.getManager_id());
        if (Result.isSuccess(userInfoVOResult) && ObjUtil.isNotEmpty(userInfoVOResult.getData())) {
            kingdeeOrderInfoDTO.setManager_name(userInfoVOResult.getData().getName());
        }
        return kingdeeOrderInfoDTO;
    }

    //提交订单信息
    private void subMitsaveOrderInfo(KingdeeOrderInfoDTO kingdeeOrderInfoDTO) {
        String jsonPrettyStr = JSONUtil.toJsonStr(kingdeeOrderInfoDTO, new JSONConfig().setIgnoreNullValue(false));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        String result = kingdeeFeign.saveOrderInfo(body, sign, iv, timestamp);
        extracted(result);
    }

    //状态流转
    @Async
    @Override
    public void submitOrderFlow(KingdeeOrderFlowDTO kingdeeOrderFlowDTO) {
        if (Boolean.FALSE.equals(enable)) {
            log.info("data push disabled");
            return;
        }
        //离线数据推送
        yqOfflineData(kingdeeOrderFlowDTO);
        KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class).or(e -> e.eq(KingdeeInfoEntity::getOrderId, kingdeeOrderFlowDTO.getOrderId())).or(e -> e.eq(KingdeeInfoEntity::getPreId, kingdeeOrderFlowDTO.getPreId())).eq(KingdeeInfoEntity::getDeleteFlag, 0));
        if (Objects.isNull(kingdeeInfoEntity)) {
            return;
        }
        if (Objects.equals(kingdeeOrderFlowDTO.getFromNode(), States.RISK_CONTROL.getNode()) && ObjUtil.equals(kingdeeOrderFlowDTO.getToNode(), States.PROCESS_TERMINAL.getNode())) {
            PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(kingdeeInfoEntity.getPreId());
            if (ObjUtil.isNotNull(preApprovalApplyInfoEntity)) {
                List<PreRiskPolicyResultEntity> riskResultList = preRiskPolicyResultMapper.selectList(new LambdaQueryWrapper<PreRiskPolicyResultEntity>()
                        .eq(PreRiskPolicyResultEntity::getPreId, preApprovalApplyInfoEntity.getId())
                        .eq(PreRiskPolicyResultEntity::getDeleteFlag, 0)
                        .in(PreRiskPolicyResultEntity::getResult, RiskPolicyResult.REJECT, RiskPolicyResult.WARN)
                );
                if (CollUtil.isNotEmpty(riskResultList)) {
                    String riskRefuseReason = riskResultList.stream().map(PreRiskPolicyResultEntity::getSuggestion).collect(Collectors.joining(","));
                    kingdeeOrderFlowDTO.setRemark(riskRefuseReason);
                }
            }
        }

        kingdeeOrderFlowDTO.setPreId(kingdeeInfoEntity.getPreId());
        if (ObjUtil.isEmpty(kingdeeOrderFlowDTO.getOrderId())) {
            OrderInfoEntity orderInfo = orderInfoMapper.selectOne(
                    new LambdaQueryWrapper<OrderInfoEntity>()
                            .eq(OrderInfoEntity::getPreId, kingdeeOrderFlowDTO.getPreId())
                            .eq(OrderInfoEntity::getDeleteFlag, 0)
                            .notIn(OrderInfoEntity::getCurrentNode, States.PROCESS_TERMINAL.getNode(), States.SYSTEM_TERMINAL.getNode())
                            .orderByDesc(OrderInfoEntity::getCreateTime)
                            .last("limit 1")
            );
            if (ObjUtil.isNotNull(orderInfo)){
                kingdeeOrderFlowDTO.setOrderId(orderInfo.getId());
                List<OrderAmountEntity> orderAmountEntities = orderAmountMapper.selectList(
                        new LambdaQueryWrapper<OrderAmountEntity>()
                                .eq(OrderAmountEntity::getOrderId, orderInfo.getId())
                                .eq(OrderAmountEntity::getDeleteFlag, 0)
                );
                if (CollUtil.isNotEmpty(orderAmountEntities)) {
                    OrderAmountEntity orderAmountEntity = orderAmountEntities.get(0);
                    kingdeeOrderFlowDTO.setFundCreditAmt(orderAmountEntity.getFundPreAmount());
                    kingdeeOrderFlowDTO.setPreAmount(orderAmountEntity.getPreAmount());
                }
            }
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        kingdeeOrderFlowDTO.setVerifyTime((int) Instant.now().getEpochSecond());
        String jsonPrettyStr = JSONUtil.toJsonStr(kingdeeOrderFlowDTO, new JSONConfig().setIgnoreNullValue(false));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        log.info("kingdeeSubmitsTheOrderStatusFlowInformation KingdeeServiceImpl:submitOrderFlow kingdeeOrderFlowDTO:{}", kingdeeOrderFlowDTO);
        String result = kingdeeFeign.orderFlow(body, sign, iv, timestamp);
        log.info("kingdeeSubmitsTheOrderStatusFlowInformation KingdeeServiceImpl:submitOrderFlow result:{}", result);
        if (ObjUtil.equals(kingdeeOrderFlowDTO.getToNode(), States.PROCESS_TERMINAL.getNode())
                || ObjUtil.equals(kingdeeOrderFlowDTO.getToNode(), States.SYSTEM_TERMINAL.getNode())) {
            log.info("kingdeeSubmitsTheOrderStatusFlowInformation KingdeeServiceImpl:submitOrderFlow kingdeeOrderFlowDTO:{}", kingdeeOrderFlowDTO);
            kingdeeInfoMapper.update(new LambdaUpdateWrapper<KingdeeInfoEntity>()
                    .set(KingdeeInfoEntity::getOrderId, null)
                    .eq(KingdeeInfoEntity::getId, kingdeeInfoEntity.getId()));
        }
    }

    private void yqOfflineData(KingdeeOrderFlowDTO kingdeeOrderFlowDTO) {
        if (Objects.isNull(kingdeeOrderFlowDTO.getOrderId())&&ObjUtil.isNull(kingdeeOrderFlowDTO.getPreId())) {
            return;
        }
        OrderInfoEntity orderInfoEntity=null;
        if (ObjUtil.isNull(kingdeeOrderFlowDTO.getPreId())){
             orderInfoEntity = orderInfoMapper.selectById(kingdeeOrderFlowDTO.getOrderId());
            if (Objects.isNull(orderInfoEntity) || Objects.isNull(orderInfoEntity.getCustomerId())) {
                return;
            }
            kingdeeOrderFlowDTO.setPreId(orderInfoEntity.getPreId());
        }
        if (ObjUtil.isNull(kingdeeOrderFlowDTO.getOrderId())){
            orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                    .eq(OrderInfoEntity::getPreId, kingdeeOrderFlowDTO.getPreId())
                    .eq(OrderInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderInfoEntity::getCreateTime)
                    .last("limit 1")
            );
        }else {
            orderInfoEntity = orderInfoMapper.selectById(kingdeeOrderFlowDTO.getOrderId());
        }
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(kingdeeOrderFlowDTO.getPreId());
        if (ObjUtil.isNull(preApprovalApplyInfoEntity)) {
            return;
        }
        DxReservationInfoEntity dxReservationInfoEntity = dxReservationInfoMapper.selectOne(new LambdaQueryWrapper<DxReservationInfoEntity>()
                        .eq(DxReservationInfoEntity::getPhone, preApprovalApplyInfoEntity.getPhone())
                        .ge(DxReservationInfoEntity::getCreateTime, LocalDateTime.now().minusDays(90))
                        .eq(DxReservationInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(DxReservationInfoEntity::getCreateTime)
                , false);
        if (Objects.isNull(dxReservationInfoEntity)) {
            return;
        }
        if (ObjUtil.notEqual(preApprovalApplyInfoEntity.getSourceType(), 1)) {
            preApprovalApplyInfoMapper.update(new LambdaUpdateWrapper<PreApprovalApplyInfoEntity>()
                    .set(PreApprovalApplyInfoEntity::getSourceType, 1)
                    .eq(PreApprovalApplyInfoEntity::getId, kingdeeOrderFlowDTO.getPreId()));
        }
        if (ObjUtil.isNotNull(orderInfoEntity)&&ObjUtil.notEqual(orderInfoEntity.getSourceType(),1)){
            orderInfoEntity.setSourceType(1);
            orderInfoEntity.setSource("电销");
            orderInfoMapper.updateById(orderInfoEntity);
        }
        YqOfflineDataDTO yqOfflineDataDTO = getYqOfflineDataDTO(kingdeeOrderFlowDTO, orderInfoEntity, dxReservationInfoEntity, preApprovalApplyInfoEntity);
        int maxAttempts = 3;
        long delayMillis = 1000;

        Exception lastException = null;
//        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
//            try {
                subMitYqOfflineData(yqOfflineDataDTO);
//                lastException = null;
//                break;
//            } catch (Exception e) {
//                lastException = e;
//                log.warn("Attempt {} failed with exception: {}", attempt, e.getMessage());
//                if (attempt < maxAttempts) {
//                    try {
//                        Thread.sleep(delayMillis);
//                    } catch (InterruptedException ie) {
//                        Thread.currentThread().interrupt();
//                        log.error("Thread interrupted during retry delay", ie);
//                    }
//                }
//            }
//        }
//
//        if (lastException != null) {
//            log.error("All attempts failed to submit YqOfflineData", lastException);
//            throw new BusinessException("Failed to submit YqOfflineData after retries", lastException);
//        }
    }

    @NotNull
    private YqOfflineDataDTO getYqOfflineDataDTO(KingdeeOrderFlowDTO kingdeeOrderFlowDTO,OrderInfoEntity orderInfoEntity, DxReservationInfoEntity dxReservationInfoEntity,PreApprovalApplyInfoEntity preApprovalApplyInfoEntity) {
        YqOfflineDataDTO yqOfflineDataDTO = new YqOfflineDataDTO();
        yqOfflineDataDTO.setUid(dxReservationInfoEntity.getUid())
                .setPre_approved_status(PreApplyInfoFundStatus.APPROVAL_SUCCESS.equals(preApprovalApplyInfoEntity.getFundStatus()) ? 1 : 2)
                .setPre_remark(preApprovalApplyInfoEntity.getRemark())
                .setNode_s(kingdeeOrderFlowDTO.getState())
                .setIs_consistent(1)
                .setFirst_remakes("初审备注信息")
                .setRisk_number(preApprovalApplyInfoEntity.getRiskNumber())
                .setPre_verify_seats(null)
                .setSpecial_msg(0)
                .setType(2)
                .setPre_approved_data(JSONUtil.toJsonStr(preApprovalApplyInfoEntity))
                .setAppointment_time("0")
                .setBusiness_type(1)
                .setPre_approved_id(preApprovalApplyInfoEntity.getId())
                .setCapital_id(preApprovalApplyInfoEntity.getFundId())
                .setCapital_name(ObjUtil.isNull(preApprovalApplyInfoEntity.getFundId())?null:FundEnum.getFundEnum(preApprovalApplyInfoEntity.getFundId()).getFundName())
                .setNode(kingdeeOrderFlowDTO.getToNode())
                .setFromNode(kingdeeOrderFlowDTO.getFromNode())
                .setStore_id(preApprovalApplyInfoEntity.getStoreId())
                .setStore_name(preApprovalApplyInfoEntity.getStoreName())
                .setManager_id(preApprovalApplyInfoEntity.getAccountManagerId())
                .setRegion_id(preApprovalApplyInfoEntity.getRegionId())
                .setRegion_name(preApprovalApplyInfoEntity.getRegionName())
                .setOrder_status(switch (kingdeeOrderFlowDTO.getToNode()) {
                    case -1000, -2000 -> 3;
                    case 5000 -> 2;
                    case 8000 -> 4;
                    default -> 1;
                })
                .setAmount_money(preApprovalApplyInfoEntity.getLoanAmount())
                .setTerm(preApprovalApplyInfoEntity.getLoanPeriod())
        ;
        if (ObjUtil.equal(kingdeeOrderFlowDTO.getToNode(), 5000)&&ObjUtil.equals(kingdeeOrderFlowDTO.getState(), 1)){
            yqOfflineDataDTO.setNode_s(2);
        }
        if (ObjUtil.isNotNull(preApprovalApplyInfoEntity.getCreditInquiryTime())) {
            yqOfflineDataDTO.setPre_verify_time(LocalDateTimeUtil.format(preApprovalApplyInfoEntity.getCreditInquiryTime(), DatePattern.NORM_DATETIME_PATTERN));
        }
        if (ObjUtil.isNotNull(yqOfflineDataDTO.getManager_id())) {
            UserDetailInfoVO userDetailInfoVO = userFeign.searchUserDetailById(yqOfflineDataDTO.getManager_id()).getData();
            if (ObjUtil.isNotNull(userDetailInfoVO)) {
                yqOfflineDataDTO.setManager_name(userDetailInfoVO.getName());
            }
        }
        if (ObjUtil.equals(kingdeeOrderFlowDTO.getToNode(), -1000) || ObjUtil.equals(kingdeeOrderFlowDTO.getToNode(), -2000)) {
            yqOfflineDataDTO.setZhongzhi_node(-1000);
        }
        if (ObjUtil.isNotNull(orderInfoEntity)){
            OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                    .eq(OrderAmountEntity::getOrderId, orderInfoEntity.getId())
                    .eq(OrderAmountEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderAmountEntity::getCreateTime), false);
            yqOfflineDataDTO.setAmount_money(orderAmountEntity.getHopeAmount())
                    .setEstimate_amount(orderAmountEntity.getAppraiserAmount())
                    .setApproval_amount(orderAmountEntity.getCustomerConfirmAmount())
                    .setCredit_amount(orderAmountEntity.getFundPreAmount())
                    .setLoan_amount(orderAmountEntity.getCustomerConfirmAmount())
                    .setApproval_update_amount(orderAmountEntity.getPreAmount())
                    .setRisk_amount(orderAmountEntity.getRiskAmount());
            OrderCompanyInfoEntity orderCompanyInfoEntity = orderCompanyInfoMapper.selectOne(new LambdaQueryWrapper<OrderCompanyInfoEntity>()
                    .eq(OrderCompanyInfoEntity::getOrderId, orderInfoEntity.getId())
                    .eq(OrderCompanyInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderCompanyInfoEntity::getCreateTime), false);
            if (ObjUtil.isNotNull(orderCompanyInfoEntity)) {
                yqOfflineDataDTO.setLicense_data(JSONUtil.toJsonStr(orderCompanyInfoEntity));
            }
            yqOfflineDataDTO.setCar_plate(orderInfoEntity.getVehicleNumber())
                    .setOnline_order_id(orderInfoEntity.getId())
                    .setCapital_id(orderInfoEntity.getFundId())
                    .setCapital_name(orderInfoEntity.getFundName())
                    .setProduct_id(orderInfoEntity.getProductId())
                    .setProduct_name(orderInfoEntity.getProductName())
                    .setApply_purpose(orderInfoEntity.getApplyPurpose())
                    .setTerm(orderInfoEntity.getTerm())
                    .setStore_id(orderInfoEntity.getDeptId())
                    .setStore_name(orderInfoEntity.getStoreName())
                    .setManager_id(orderInfoEntity.getManagerId())
                    .setRegion_id(orderInfoEntity.getRegionId())
                    .setRegion_name(orderInfoEntity.getRegionName())
                    .setGps_state(orderInfoEntity.getGpsState())
                    .setContract_state(orderInfoEntity.getContractState())
                    .setMortgage_state(orderInfoEntity.getMortgageState())
                    .setReconsider_state(orderInfoEntity.getReconsiderState())
                    .setReview_state(orderInfoEntity.getReviewState())
                    .setConfirm_state(orderInfoEntity.getConfirmState())
                    .setManagement_conclusion(orderInfoEntity.getManagementConclusion())
                    .setPayment_type(orderInfoEntity.getPaymentType())
                    .setRepay_method(orderInfoEntity.getRepayMethod());
            if (ObjUtil.isNotNull(orderInfoEntity.getPaymentState())) {
                yqOfflineDataDTO.setPayment_state(orderInfoEntity.getPaymentState().getValue());

            }
            if (ObjUtil.equals(orderInfoEntity.getCurrentNode(), -1000) || ObjUtil.equals(orderInfoEntity.getCurrentNode(), -2000)) {
                yqOfflineDataDTO.setZhongzhi_node(-1000);
            }
            if (ObjUtil.isNotNull(orderInfoEntity.getQualityTestCommitTime())) {
                yqOfflineDataDTO.setQuality_test_commit_time(LocalDateTimeUtil.format(orderInfoEntity.getQualityTestCommitTime(), DatePattern.NORM_DATETIME_PATTERN));
            }
            if (ObjUtil.isNotNull(orderInfoEntity.getPaymentTime())) {
                yqOfflineDataDTO.setSucc_order_time(LocalDateTimeUtil.format(orderInfoEntity.getPaymentTime(), DatePattern.NORM_DATETIME_PATTERN));
            }
            if (ObjUtil.isNotNull(orderInfoEntity.getPreApplyTime())) {
                yqOfflineDataDTO.setPre_apply_time(LocalDateTimeUtil.format(orderInfoEntity.getPreApplyTime(), DatePattern.NORM_DATETIME_PATTERN));
            }
        }else {
            PreOcrVehicleInfoEntity preOcrVehicleInfoEntity = preOcrVehicleInfoMapper.selectOne(new LambdaQueryWrapper<PreOcrVehicleInfoEntity>()
                    .eq(PreOcrVehicleInfoEntity::getPreId, kingdeeOrderFlowDTO.getPreId())
                    .eq(PreOcrVehicleInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(PreOcrVehicleInfoEntity::getCreateTime), false);
            yqOfflineDataDTO.setCar_plate(preOcrVehicleInfoEntity.getVehicleNumber());

        }

        return yqOfflineDataDTO;
    }

    @Override
    public String generateQrCodeUrl(Integer clueId, String cluePhone, Integer userId) {
        log.info("generateQrCodeUrl:clueId={}-userId={}",clueId,userId);
        String format = String.format(hostname + url, clueId,cluePhone);
        return userFeign.generateQrCodeUrl(new QrCodeDTO().setUrl(format).setUserId(userId));
    }

    @Override
    public Boolean moveToAnOfflineOrder(KingdeeOffLineOrderDTO kingdeeOffLineOrderDTO) {
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectOne(new LambdaQueryWrapper<PreApprovalApplyInfoEntity>().eq(PreApprovalApplyInfoEntity::getId, kingdeeOffLineOrderDTO.getPreId()).eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0).eq(PreApprovalApplyInfoEntity::getSourceType, 1), false);
        if (Objects.isNull(preApprovalApplyInfoEntity)) {
            throw new BusinessException("未找到对应的订单");
        }
        if (Objects.equals(preApprovalApplyInfoEntity.getManagerState().getCode(), PreApplyInfoManagerStatus.APPROVAL_SUCCESS.getCode())) {
            throw new BusinessException("当前订单状态不允许线下预审");
        }
        preApprovalApplyInfoEntity.setAccountManagerId(kingdeeOffLineOrderDTO.getManagerId());
        List<Integer> userIdList = new ArrayList<>();
        userIdList.add(kingdeeOffLineOrderDTO.getManagerId());
        Result<List<UserStoreVO>> listResult = userFeign.searchUserStoreBatch(userIdList);
        if (Result.isSuccess(listResult) && CollUtil.isNotEmpty(listResult.getData())) {
            UserStoreVO userStoreVO = listResult.getData().get(0);
            if (Objects.nonNull(userStoreVO)) {
                log.info("KingdeeServiceImpl:moveToAnOfflineOrder userStoreVO:{}", userStoreVO);
                preApprovalApplyInfoEntity.setStoreId(userStoreVO.getStoreId());
                preApprovalApplyInfoEntity.setStoreName(userStoreVO.getStore());
                preApprovalApplyInfoEntity.setRegionId(userStoreVO.getAreaId());
                preApprovalApplyInfoEntity.setRegionName(userStoreVO.getArea());
                preApprovalApplyInfoEntity.setTeamId(userStoreVO.getTeamId());
                preApprovalApplyInfoEntity.setTeamName(userStoreVO.getTeam());
            }
        }
        log.info("KingdeeServiceImpl:moveToAnOfflineOrder preApprovalApplyInfoEntity1:{}", preApprovalApplyInfoEntity);
        boolean b = preApprovalApplyInfoService.updateById(preApprovalApplyInfoEntity);
        kingdeeInfoMapper.update(new LambdaUpdateWrapper<KingdeeInfoEntity>().eq(KingdeeInfoEntity::getPreId, preApprovalApplyInfoEntity.getId()).set(KingdeeInfoEntity::getDeleteFlag, 1));
        if (b) {
            riskLaunchOnPreApprovalAgainService.riskLaunchOnPreApprovalAgain(preApprovalApplyInfoEntity.getId());
        }
        return true;
    }


    //提交附件
    private void insertAnnex(OrderSubmitDTO dto) {
        KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class).eq(KingdeeInfoEntity::getOrderId, dto.getOrderId()).eq(KingdeeInfoEntity::getDeleteFlag, 0));
        if (Objects.isNull(kingdeeInfoEntity)) {
                OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
                if (Objects.isNull(orderInfoEntity)) {
                    return ;
                }
                DxReservationInfoEntity dxReservationInfoEntity = dxReservationInfoMapper.selectOne(new LambdaQueryWrapper<>(DxReservationInfoEntity.class)
                        .eq(DxReservationInfoEntity::getPhone, orderInfoEntity.getCustomerPhone())
                        .ge(DxReservationInfoEntity::getCreateTime, LocalDateTime.now().minusDays(90))
                        .eq(DxReservationInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(DxReservationInfoEntity::getCreateTime), false);
                if (Objects.isNull(dxReservationInfoEntity)) {
                    return ;
                }
            }
        KingdeeOrderFileDTO kingdeeOrderFileDTO = getKingdeeOrderFileDTO(dto);
        log.info("kingdeeSubmitsTheAttachedInformation KingdeeServiceImpl:insertAnnex kingdeeOrderFileDTO:{}", kingdeeOrderFileDTO);
        subMitinsertAnnex(kingdeeOrderFileDTO);
    }

    private KingdeeOrderFileDTO getKingdeeOrderFileDTO(OrderSubmitDTO dto) {
        KingdeeOrderFileDTO kingdeeOrderFileDTO = new KingdeeOrderFileDTO().setOrderId(dto.getOrderId());
        if (dto.getResult() == 1) {
            kingdeeOrderFileDTO.setAnnexInfo(orderFileMapper.selectJoinList(KingdeeOrderFileDTO.annexInfo.class, new MPJLambdaWrapper<>(OrderFileEntity.class)
                    .selectAs(OrderFileEntity::getResourceId, KingdeeOrderFileDTO.annexInfo::getUuid)
                    .selectAs(FileConfigEntity::getName, KingdeeOrderFileDTO.annexInfo::getName)
                    .selectAs(FileConfigEntity::getCode, KingdeeOrderFileDTO.annexInfo::getCode)
                    .selectAs(FileAssoEntity::getMenuId, KingdeeOrderFileDTO.annexInfo::getMenuId)
                    .leftJoin(FileConfigEntity.class, FileConfigEntity::getId, OrderFileEntity::getFileId)
                    .leftJoin(FileAssoEntity.class, FileAssoEntity::getFileId, OrderFileEntity::getFileId)
                    .eq(OrderFileEntity::getOrderId, dto.getOrderId())
                    .eq(OrderFileEntity::getDeleteFlag, 0)));
        }
        return kingdeeOrderFileDTO;
    }

    private void subMitYqOfflineData(YqOfflineDataDTO yqOfflineDataDTO) {
        String jsonPrettyStr = JSONUtil.toJsonStr(yqOfflineDataDTO, new JSONConfig().setIgnoreNullValue(false));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        String result = kingdeeFeign.yqOfflineData(body, sign, iv, timestamp);
        extracted(result);
    }

    private void subMitinsertAnnex(KingdeeOrderFileDTO kingdeeOrderFileDTO) {
        String jsonPrettyStr = JSONUtil.toJsonStr(kingdeeOrderFileDTO, new JSONConfig().setIgnoreNullValue(false));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        String result = kingdeeFeign.insertAnnex(body, sign, iv, timestamp);
        extracted(result);
    }

    /**
     * 三合一接口补偿
     */
    @Override
    public KingdeePreDTO preApproval(Integer preId) {
        KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class)
                .eq(KingdeeInfoEntity::getPreId, preId)
                .eq(KingdeeInfoEntity::getDeleteFlag, 0), false);
        if (Objects.isNull(kingdeeInfoEntity)) {
            PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(preId);
            if (Objects.isNull(preApprovalApplyInfoEntity)) {
                return null;
            }
            DxReservationInfoEntity dxReservationInfoEntity = dxReservationInfoMapper.selectOne(new LambdaQueryWrapper<>(DxReservationInfoEntity.class)
                    .eq(DxReservationInfoEntity::getPhone, preApprovalApplyInfoEntity.getPhone())
                    .ge(DxReservationInfoEntity::getCreateTime, LocalDateTime.now().minusDays(90))
                    .eq(DxReservationInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(DxReservationInfoEntity::getCreateTime), false);
            if (Objects.isNull(dxReservationInfoEntity)) {
                return null;
            }
            kingdeeInfoEntity= new KingdeeInfoEntity().setPreId(preId);
        }
        return getKingdeePreDTO(new PreApprovalApplyInfoEntity().setId(kingdeeInfoEntity.getPreId()), kingdeeInfoEntity);
    }

    /**
     * 资方推送补偿
     */
    @Override
    public List<KingdeeSyncPreInfoDTO> fundsPreApproval(Integer preId) {
        KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class)
                .eq(KingdeeInfoEntity::getPreId, preId)
                .eq(KingdeeInfoEntity::getDeleteFlag, 0), false);
        if (Objects.isNull(kingdeeInfoEntity)) {
            kingdeeInfoEntity=new KingdeeInfoEntity();
        }
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(preId);
        List<PreFundInfoEntity> preFundInfoEntities = preFundInfoMapper.selectList(new LambdaQueryWrapper<>(PreFundInfoEntity.class).eq(PreFundInfoEntity::getPreId, preId)
                .eq(PreFundInfoEntity::getFundId, preApprovalApplyInfoEntity.getFundId())
                .eq(PreFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(PreFundInfoEntity::getId));
        KingdeeInfoEntity finalKingdeeInfoEntity = kingdeeInfoEntity;
        return preFundInfoEntities.stream()
                .map(kingdeeSyncPreInfoDTO ->
                        getKingdeeSyncPreInfoDTO(
                                new PreApproveFundStatusDTO()
                                        .setPreId(preId)
                                        .setCreditAmt(kingdeeSyncPreInfoDTO.getCreditAmount())
                                        .setFundId(kingdeeSyncPreInfoDTO.getFundId())
                                        .setStatus(kingdeeSyncPreInfoDTO.getFundResult())
                                        .setFailReason(kingdeeSyncPreInfoDTO.getFundRemark()), kingdeeSyncPreInfoDTO, finalKingdeeInfoEntity, preApprovalApplyInfoEntity)).toList();
    }

    /**
     * 客户信息补录补偿
     */
    @Override
    public KingdeeOrderCustomerInfoDTO addedBusinessCustomer(Integer orderId) {
        OrderCustomerInfoDTO orderCustomerInfoDTO = new OrderCustomerInfoDTO();
        orderCustomerInfoDTO.setOrderId(orderId);
        return getKingdeeOrderCustomerInfoDTO(orderCustomerInfoDTO);
    }

    /**
     * 车辆信息补录补偿
     */
    @Override
    public KingdeeCarsAdditionalRecordingDTO addedBusinessCar(Integer orderId) {
        KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class)
                .eq(KingdeeInfoEntity::getOrderId, orderId)
                .eq(KingdeeInfoEntity::getDeleteFlag, 0), false);
        if (Objects.isNull(kingdeeInfoEntity)) {
            kingdeeInfoEntity=new KingdeeInfoEntity();
        }
        VehicleInfoEditDTO vehicleInfoEditDTO = new VehicleInfoEditDTO();
        vehicleInfoEditDTO.setOrderId(orderId);
        return getKingdeeCarsAdditionalRecordingDTO(vehicleInfoEditDTO, kingdeeInfoEntity);
    }

    /**
     * 订单信息补录补偿
     */
    @Override
    public KingdeeOrderInfoDTO addedBusinessOrder(Integer orderId) {
        SaveOrderInfoDTO saveOrderInfoDTO = new SaveOrderInfoDTO();
        saveOrderInfoDTO.setOrderId(orderId);
        return getKingdeeOrderInfoDTO(saveOrderInfoDTO);
    }

    /**
     * 附件信息补录补偿
     */
    @Override
    public KingdeeOrderFileDTO addedBusinessAttachment(Integer orderId) {
        OrderSubmitDTO orderSubmitDTO = new OrderSubmitDTO();
        orderSubmitDTO.setOrderId(orderId);
        orderSubmitDTO.setResult(1);
        return getKingdeeOrderFileDTO(orderSubmitDTO);
    }

    /**
     * 节点信息补偿
     */
    @Override
    public List<KingdeeOrderNodeDTO> orderNode(Integer orderId) {
//        KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class)
//                .eq(KingdeeInfoEntity::getOrderId, orderId)
//                .eq(KingdeeInfoEntity::getDeleteFlag, 0), false);
//        if (Objects.isNull(kingdeeInfoEntity)) {
//            return null;
//        }
        List<OrderNodeRecordEntity> orderNodeRecordEntities = orderNodeRecordMapper.selectList(new LambdaQueryWrapper<>(OrderNodeRecordEntity.class)
                .eq(OrderNodeRecordEntity::getOrderId, orderId)
                .eq(OrderNodeRecordEntity::getDeleteFlag, 0)
                .orderByDesc(OrderNodeRecordEntity::getId));
        return orderNodeRecordEntities.stream()
                .map(orderNodeRecordEntity -> new KingdeeOrderNodeDTO()
                        .setOrderId(orderNodeRecordEntity.getOrderId())
                        .setCurrentNode(orderNodeRecordEntity.getCurrentNode())
                        .setNextNode(orderNodeRecordEntity.getNextNode())
                        .setLastNode(orderNodeRecordEntity.getLastNode())
                        .setEvent(orderNodeRecordEntity.getEvent())
                        .setRemark(orderNodeRecordEntity.getRemark())
                        .setRejectReason(orderNodeRecordEntity.getRejectReason())
                        .setRemarkExternal(orderNodeRecordEntity.getRemarkExternal())
                        .setCreateTime(orderNodeRecordEntity.getCreateTime()))
                .toList();
    }

    @Override
    public List<GetOrderInfoByPhoneVO> getOrderInfoByPhone(GetOrderInfoByPhoneDTO dto) {
        MPJLambdaWrapper<OrderInfoEntity> eq = new MPJLambdaWrapper<OrderInfoEntity>()
                .selectAs(OrderInfoEntity::getId, GetOrderInfoByPhoneVO::getOnline_order_id)
                .selectAs(OrderInfoEntity::getPreId, GetOrderInfoByPhoneVO::getPre_approved_id)
                .eq(OrderInfoEntity::getDeleteFlag, 0);
       eq.orderByDesc(OrderInfoEntity::getCreateTime);
        if (StringUtils.isNotEmpty(dto.getCustomerPhone())){
            eq
//                    .ne(OrderInfoEntity::getSourceType, 1)
                    .like(StringUtils.isNotBlank(dto.getCustomerPhone()), OrderInfoEntity::getCustomerPhone, dto.getCustomerPhone())
                    .notIn(OrderInfoEntity::getCurrentNode,
                            Arrays.asList(
                                    States.PROCESS_TERMINAL.getNode(),
                                    States.SYSTEM_TERMINAL.getNode(),
                                    States.ORDER_FREEZE.getNode()
                            )
                    );
        }
        if (ObjUtil.isNotEmpty(dto.getPreId())){
            eq.eq(ObjUtil.isNotNull(dto.getPreId()), OrderInfoEntity::getPreId, dto.getPreId());
            eq.last("limit 1");
        }
        if (ObjUtil.isNotEmpty(dto.getOrderId())){
            eq.eq(ObjUtil.isNotNull(dto.getOrderId()), OrderInfoEntity::getId, dto.getOrderId());
        }
        List<GetOrderInfoByPhoneVO> list = orderInfoMapper.selectJoinList(
                GetOrderInfoByPhoneVO.class,eq);

        log.info("kingdeeServiceImpl.getOrderInfoByPhone:{}", list);
        if (CollUtil.isNotEmpty(list)) {
            //获取订单id列表
            List<Integer> orderIdList = list.stream().map(GetOrderInfoByPhoneVO::getOnline_order_id).toList();
            List<Integer> preIdList = list.stream().map(GetOrderInfoByPhoneVO::getPre_approved_id).toList();
            List<OrderAmountEntity> orderAmountEntityList = orderAmountMapper.selectList(
                    new LambdaQueryWrapper<OrderAmountEntity>()
                            .in(OrderAmountEntity::getOrderId, orderIdList)
                            .eq(OrderAmountEntity::getDeleteFlag, 0)
            );
            Map<Integer, OrderAmountEntity> orderAmountMap = new HashMap<>();
            if (CollUtil.isNotEmpty(orderAmountEntityList)) {
                //订单id和订单额度的映射
                orderAmountMap.putAll(orderAmountEntityList.stream()
                        .collect(Collectors.toMap(OrderAmountEntity::getOrderId, entity -> entity)));
            }

            List<OrderCompanyInfoEntity> orderCompanyInfoEntityList = orderCompanyInfoMapper.selectList(
                    new LambdaQueryWrapper<OrderCompanyInfoEntity>()
                            .in(OrderCompanyInfoEntity::getOrderId, orderIdList)
                            .eq(OrderCompanyInfoEntity::getDeleteFlag, 0)
            );
            Map<Integer, OrderCompanyInfoEntity> orderCompanyInfoEntityMap = new HashMap<>();
            if (CollUtil.isNotEmpty(orderCompanyInfoEntityList)) {
                //订单id和营业执照信息的映射
                orderCompanyInfoEntityMap.putAll(orderCompanyInfoEntityList.stream()
                        .collect(Collectors.toMap(OrderCompanyInfoEntity::getOrderId, entity -> entity)));
            }
            List<PreApprovalApplyInfoEntity> preApprovalApplyInfoEntityList = preApprovalApplyInfoMapper.selectList(
                    new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
                            .in(PreApprovalApplyInfoEntity::getId, preIdList)
                            .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
            );
            Map<Integer, PreApprovalApplyInfoEntity> preApprovalApplyInfoEntityMap = new HashMap<>();
            if (CollUtil.isNotEmpty(preApprovalApplyInfoEntityList)) {
                //订单id和营业执照信息的映射
                preApprovalApplyInfoEntityMap.putAll(preApprovalApplyInfoEntityList.stream()
                        .collect(Collectors.toMap(PreApprovalApplyInfoEntity::getId, entity -> entity)));
            }

            List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(
                    new LambdaQueryWrapper<OrderInfoEntity>()
                            .in(OrderInfoEntity::getId, orderIdList)
            );
            Map<Integer, OrderInfoEntity> orderInfoEntityMap = new HashMap<>();
            if (CollUtil.isNotEmpty(orderInfoEntityList)) {
                orderInfoEntityMap.putAll(orderInfoEntityList.stream()
                        .collect(Collectors.toMap(OrderInfoEntity::getId, entity -> entity)));
            }
            for (GetOrderInfoByPhoneVO getOrderInfoByPhoneVO : list) {
                if (CollUtil.isNotEmpty(orderAmountMap) && orderAmountMap.containsKey(getOrderInfoByPhoneVO.getOnline_order_id())) {
                    OrderAmountEntity orderAmountEntity = orderAmountMap.get(getOrderInfoByPhoneVO.getOnline_order_id());
                    getOrderInfoByPhoneVO.setAmount_money(orderAmountEntity.getHopeAmount());
                    getOrderInfoByPhoneVO.setEstimate_amount(orderAmountEntity.getAppraiserAmount());
                    getOrderInfoByPhoneVO.setApproval_amount(orderAmountEntity.getCustomerConfirmAmount());
                    getOrderInfoByPhoneVO.setCredit_amount(orderAmountEntity.getFundPreAmount());
                    getOrderInfoByPhoneVO.setLoan_amount(orderAmountEntity.getCustomerConfirmAmount());
                    getOrderInfoByPhoneVO.setApproval_update_amount(orderAmountEntity.getPreAmount());
                    getOrderInfoByPhoneVO.setRisk_amount(orderAmountEntity.getRiskAmount());
                }
                if (CollUtil.isNotEmpty(orderCompanyInfoEntityMap) && orderCompanyInfoEntityMap.containsKey(getOrderInfoByPhoneVO.getOnline_order_id())) {
                    OrderCompanyInfoEntity orderCompanyInfoEntity = orderCompanyInfoEntityMap.get(getOrderInfoByPhoneVO.getOnline_order_id());
                    if (ObjUtil.isNotNull(orderCompanyInfoEntity)) {
                        getOrderInfoByPhoneVO.setLicense_data(JSONUtil.toJsonStr(orderCompanyInfoEntity));
                    }
                }
                getOrderInfoByPhoneVO.setType(2);
                if (CollUtil.isNotEmpty(preApprovalApplyInfoEntityMap) && preApprovalApplyInfoEntityMap.containsKey(getOrderInfoByPhoneVO.getPre_approved_id())) {
                    PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoEntityMap.get(getOrderInfoByPhoneVO.getPre_approved_id());
                    getOrderInfoByPhoneVO.setId_number(preApprovalApplyInfoEntity.getIdNumber());
                    getOrderInfoByPhoneVO.setPre_approved_status(PreApplyInfoFundStatus.APPROVAL_SUCCESS.equals(preApprovalApplyInfoEntity.getFundStatus()) ? 1 : 2);
                    getOrderInfoByPhoneVO.setPre_remark(preApprovalApplyInfoEntity.getRemark());
                    getOrderInfoByPhoneVO.setRisk_number(preApprovalApplyInfoEntity.getRiskNumber());
                    getOrderInfoByPhoneVO.setPre_approved_data(JSONUtil.toJsonStr(preApprovalApplyInfoEntity));
                    if (ObjUtil.isNotNull(preApprovalApplyInfoEntity.getCreditInquiryTime())) {
                        getOrderInfoByPhoneVO.setPre_verify_time(LocalDateTimeUtil.format(preApprovalApplyInfoEntity.getCreditInquiryTime(), DatePattern.NORM_DATETIME_PATTERN));
                    }
//                    DxReservationInfoEntity dxReservationInfoEntity = dxReservationInfoMapper.selectOne(new LambdaQueryWrapper<>(DxReservationInfoEntity.class)
//                            .eq(DxReservationInfoEntity::getPhone, preApprovalApplyInfoEntity.getPhone())
//                            .ge(DxReservationInfoEntity::getCreateTime, LocalDateTime.now().minusDays(90))
//                            .eq(DxReservationInfoEntity::getDeleteFlag, 0)
//                            .orderByDesc(DxReservationInfoEntity::getCreateTime), false);
//                    if (ObjUtil.isNotNull(dxReservationInfoEntity)||ObjUtil.equals(preApprovalApplyInfoEntity.getRegionId(),56)) {
                    if (ObjUtil.equals(preApprovalApplyInfoEntity.getRegionId(),56) || ObjUtil.equals(preApprovalApplyInfoEntity.getRegionId(),24)) {
                        getOrderInfoByPhoneVO.setType(1);
                    }
                }
                if (CollUtil.isNotEmpty(orderInfoEntityMap) && orderInfoEntityMap.containsKey(getOrderInfoByPhoneVO.getOnline_order_id())) {
                    OrderInfoEntity orderInfoEntity = orderInfoEntityMap.get(getOrderInfoByPhoneVO.getOnline_order_id());
                    getOrderInfoByPhoneVO.setCustomer_name(orderInfoEntity.getCustomerName());
                    getOrderInfoByPhoneVO.setCustomer_phone(orderInfoEntity.getCustomerPhone());
                    getOrderInfoByPhoneVO.setCapital_id(orderInfoEntity.getFundId());
                    getOrderInfoByPhoneVO.setCapital_name(orderInfoEntity.getFundName());
                    getOrderInfoByPhoneVO.setProduct_id(orderInfoEntity.getProductId());
                    getOrderInfoByPhoneVO.setProduct_name(orderInfoEntity.getProductName());
                    getOrderInfoByPhoneVO.setApply_purpose(orderInfoEntity.getApplyPurpose());
                    getOrderInfoByPhoneVO.setTerm(orderInfoEntity.getTerm());
                    getOrderInfoByPhoneVO.setNode(orderInfoEntity.getCurrentNode());
                    getOrderInfoByPhoneVO.setStore_id(orderInfoEntity.getDeptId());
                    getOrderInfoByPhoneVO.setStore_name(orderInfoEntity.getStoreName());
                    getOrderInfoByPhoneVO.setManager_id(orderInfoEntity.getManagerId());
                    getOrderInfoByPhoneVO.setRegion_id(orderInfoEntity.getRegionId());
                    getOrderInfoByPhoneVO.setRegion_name(orderInfoEntity.getRegionName());
                    getOrderInfoByPhoneVO.setGps_state(orderInfoEntity.getGpsState());
                    getOrderInfoByPhoneVO.setContract_state(orderInfoEntity.getContractState());
                    getOrderInfoByPhoneVO.setMortgage_state(orderInfoEntity.getMortgageState());
                    getOrderInfoByPhoneVO.setReconsider_state(orderInfoEntity.getReconsiderState());
                    getOrderInfoByPhoneVO.setReview_state(orderInfoEntity.getReviewState());
                    getOrderInfoByPhoneVO.setConfirm_state(orderInfoEntity.getConfirmState());
                    getOrderInfoByPhoneVO.setManagement_conclusion(orderInfoEntity.getManagementConclusion());
                    getOrderInfoByPhoneVO.setPayment_type(orderInfoEntity.getPaymentType());
                    getOrderInfoByPhoneVO.setRepay_method(orderInfoEntity.getRepayMethod());
                    getOrderInfoByPhoneVO.setRisk_number(orderInfoEntity.getRiskNumber());
                    getOrderInfoByPhoneVO.setOrder_status(orderInfoEntity.getCurrentNode());
                    getOrderInfoByPhoneVO.setCar_plate(orderInfoEntity.getVehicleNumber());

                    if (ObjUtil.isNotNull(orderInfoEntity.getQualityTestCommitTime())) {
                        getOrderInfoByPhoneVO.setQuality_test_commit_time(LocalDateTimeUtil.format(orderInfoEntity.getQualityTestCommitTime(), DatePattern.NORM_DATETIME_PATTERN));
                    }
                    if (ObjUtil.isNotNull(orderInfoEntity.getPaymentTime())) {
                        getOrderInfoByPhoneVO.setSucc_order_time(LocalDateTimeUtil.format(orderInfoEntity.getPaymentTime(), DatePattern.NORM_DATETIME_PATTERN));
                    }
                    if (ObjUtil.isNotNull(orderInfoEntity.getPreApplyTime())) {
                        getOrderInfoByPhoneVO.setPre_apply_time(LocalDateTimeUtil.format(orderInfoEntity.getPreApplyTime(), DatePattern.NORM_DATETIME_PATTERN));
                    }
                    if (ObjUtil.isNotNull(orderInfoEntity.getPaymentState())) {
                        getOrderInfoByPhoneVO.setPayment_state(orderInfoEntity.getPaymentState().getValue());
                    }
                    if (ObjUtil.equals(orderInfoEntity.getCurrentNode(), -1000) || ObjUtil.equals(orderInfoEntity.getCurrentNode(), -2000)) {
                        getOrderInfoByPhoneVO.setZhongzhi_node(-1000);
                    }
                    OrderNodeRecordEntity orderNodeRecordEntity = orderNodeRecordMapper.selectOne(
                            new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                    .eq(OrderNodeRecordEntity::getOrderId,orderInfoEntity.getId())
                                    .eq(OrderNodeRecordEntity::getNextNode,orderInfoEntity.getCurrentNode())
                                    .orderByDesc(OrderNodeRecordEntity::getCreateTime)
                                    .last("limit 1")
                    );
                    if (ObjUtil.isNotEmpty(orderNodeRecordEntity)){
                        getOrderInfoByPhoneVO.setNode_s(orderNodeRecordEntity.getEvent().getCode());
                        if (ObjUtil.equals(orderNodeRecordEntity.getNextNode(),5000)){
                            getOrderInfoByPhoneVO.setNode_s(2);
                        }
                    }else {
                        getOrderInfoByPhoneVO.setNode_s(1);
                    }
//                    DxReservationInfoEntity dxReservationInfoEntity = dxReservationInfoMapper.selectOne(new LambdaQueryWrapper<>(DxReservationInfoEntity.class)
//                            .eq(DxReservationInfoEntity::getPhone, orderInfoEntity.getCustomerPhone())
//                            .ge(DxReservationInfoEntity::getCreateTime, LocalDateTime.now().minusDays(90))
//                            .eq(DxReservationInfoEntity::getDeleteFlag, 0)
//                            .orderByDesc(DxReservationInfoEntity::getCreateTime), false);
                    if (ObjUtil.equals(orderInfoEntity.getRegionId(),56) || ObjUtil.equals(orderInfoEntity.getRegionId(),24)) {
                        getOrderInfoByPhoneVO.setType(1);
                    }
                }
                if (ObjUtil.isNotNull(getOrderInfoByPhoneVO.getManager_id())) {
                    UserDetailInfoVO userDetailInfoVO = userFeign.searchUserDetailById(getOrderInfoByPhoneVO.getManager_id()).getData();
                    if (ObjUtil.isNotNull(userDetailInfoVO)) {
                        getOrderInfoByPhoneVO.setManager_name(userDetailInfoVO.getName());
                    }
                }
                getOrderInfoByPhoneVO.setIs_consistent(1);
                getOrderInfoByPhoneVO.setFirst_remakes("初审备注信息");
                getOrderInfoByPhoneVO.setOrder_status(switch (getOrderInfoByPhoneVO.getNode()) {
                    case -1000, -2000 -> 3;
                    case 5000 -> 2;
                    case 8000 -> 4;
                    default -> 1;
                });

                getOrderInfoByPhoneVO.setPre_verify_seats(null);
                getOrderInfoByPhoneVO.setSpecial_msg(0);
                getOrderInfoByPhoneVO.setAppointment_time("0");
                getOrderInfoByPhoneVO.setBusiness_type(1);
                KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class)
                        .eq(KingdeeInfoEntity::getPreId, getOrderInfoByPhoneVO.getPre_approved_id())
                        .orderByDesc(KingdeeInfoEntity::getCreateTime), false);
                if (ObjUtil.isNotNull(kingdeeInfoEntity)) {
                    getOrderInfoByPhoneVO.setClue_id(kingdeeInfoEntity.getClueId());
                }
            }
        }
        log.info("getOrderInfoByPhoneVO:{}", JSONUtil.toJsonStr(list));
        return list;
    }

    @Override
    public Boolean orderConversionTelemarketing(OrderConversionTelemarketingDTO dto) {
        log.info("OrderCollateralInfoServiceImpl.orderConversionTelemarketing OrderConversionTelemarketingDTO{}", dto);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
        Assert.notNull(orderInfoEntity, "订单不存在");
        GetUserDeptInfoVO userDeptInfo = dingDrawMoneyFeign.getUserDeptInfo(dto.getSeatPhone());
        if (ObjUtil.isNotNull(userDeptInfo)) {
            orderInfoEntity.setSource("电销");
            orderInfoEntity.setSourceType(1);
//            orderInfoEntity.setDeptId(userDeptInfo.getDeptId());
//            orderInfoEntity.setStoreName(userDeptInfo.getDeptName());
//            orderInfoEntity.setManagerId(userDeptInfo.getUserId());
//            orderInfoEntity.setRegionId(userDeptInfo.getRegionId());
//            orderInfoEntity.setRegionName(userDeptInfo.getRegionName());
            orderInfoMapper.updateById(orderInfoEntity);
            return orderInfoMapper.updateById(orderInfoEntity) > 0;
        } else {
            throw new BusinessException("用户异常，请确认后操作");
        }
    }

    @Override
    public List<GetStoreListVO> getStoreList(GetStoreListDTO dto) {
        List<StoreAddressListVO> storeAddressListVOS = storeAddressInfoMapper.selectJoinList(StoreAddressListVO.class,
                new MPJLambdaWrapper<StoreAddressInfoEntity>()
                        .selectAs(StoreAddressInfoEntity::getId, StoreAddressListVO::getId)
                        .selectAs(StoreAddressInfoEntity::getStoreName, StoreAddressListVO::getStoreName)
                        .selectAs(StoreAddressInfoEntity::getProvince, StoreAddressListVO::getProvinceId)
                        .selectAs(StoreAddressInfoEntity::getProvinceName, StoreAddressListVO::getProvinceName)
                        .selectAs(StoreAddressInfoEntity::getCity, StoreAddressListVO::getCityId)
                        .selectAs(StoreAddressInfoEntity::getCityName, StoreAddressListVO::getCityName)
                        .selectAs(StoreAddressInfoEntity::getArea, StoreAddressListVO::getAreaId)
                        .selectAs(StoreAddressInfoEntity::getAreaName, StoreAddressListVO::getAreaName)
                        .selectAs(StoreAddressInfoEntity::getDetail, StoreAddressListVO::getDetail)
                        .like(StrUtil.isNotEmpty(dto.getStoreName()), StoreAddressInfoEntity::getStoreName, dto.getStoreName())
        );
        List<String> storeNameList = storeAddressListVOS.stream()
                .map(StoreAddressListVO::getStoreName).distinct().collect(Collectors.toList());
        Map<String, Integer> deptIdByName = dingDrawMoneyFeign.getDeptIdByName(new GetDeptIdByNameDTO().setDeptName(storeNameList));
        List<GetStoreListVO> list = new ArrayList<>();
        for (String deptName : storeNameList) {
            if (deptIdByName.containsKey(deptName)) {
                GetStoreListVO getStoreListVO = new GetStoreListVO();
                getStoreListVO.setStoreName(deptName);
                getStoreListVO.setStoreId(deptIdByName.get(deptName));
                list.add(getStoreListVO);
            }
        }
        if (CollUtil.isEmpty(list)){
            return list;
        }else {
            return list.stream()
                    .collect(Collectors.toMap(
                            GetStoreListVO::getStoreId,
                            vo -> vo,
                            (existing, replacement) -> existing
                    ))
                    .values()
                    .stream()
                    .toList();
        }

    }

    @Override
    public Boolean isOnlineOrder(Integer preId) {
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(preId);
        return Objects.equals(preApprovalApplyInfoEntity.getSourceType(), 1) && (Objects.equals(preApprovalApplyInfoEntity.getRegionId(), 24) || Objects.equals(preApprovalApplyInfoEntity.getRegionId(), 56));
    }

    @Override
    public PreApprovalApplyInfoVO getPreApprovalDetails(Integer preId) {
        PreApprovalApplyInfoEntity preApprovalApplyInfo =
                preApprovalApplyInfoMapper.selectById(preId);
        return approvalApplyInfoConverter.entity2Vo(preApprovalApplyInfo);
    }

    @Override
    public GetEvaluatorVO getEvaluator(Integer orderId) {
        List<OrderVehicleInfoEntity> orderVehicleInfoEntities = orderVehicleInfoMapper.selectList(
                new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                        .eq(OrderVehicleInfoEntity::getOrderId, orderId)
                        .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(orderVehicleInfoEntities)) {
            throw new BusinessException("当前订单不存在车辆信息");
        }
        OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoEntities.get(0);
        GetEvaluatorVO getEvaluatorVO = new GetEvaluatorVO();
        getEvaluatorVO.setStoreId(orderVehicleInfoEntity.getStoreId());
        Result<String> deptById = userFeign.getDeptById(orderVehicleInfoEntity.getStoreId());
        if (Result.isSuccess(deptById) && StringUtils.isNotBlank(deptById.getData())) {
            getEvaluatorVO.setStoreName(deptById.getData());
        } else {
            getEvaluatorVO.setStoreName("");
        }
        getEvaluatorVO.setAppraiserId(orderVehicleInfoEntity.getAppraiserId());
        getEvaluatorVO.setAppraiseName(orderVehicleInfoEntity.getAppraiseName());

        return getEvaluatorVO;
    }

    @Override
    public GetOrderInfoByPhoneVO getOrderInfoById(GetOrderInfoByPhoneDTO dto) {
        MPJLambdaWrapper<OrderInfoEntity> eq = new MPJLambdaWrapper<OrderInfoEntity>()
                .selectAs(OrderInfoEntity::getId, GetOrderInfoByPhoneVO::getOnline_order_id)
                .selectAs(OrderInfoEntity::getPreId, GetOrderInfoByPhoneVO::getPre_approved_id)
                .eq(OrderInfoEntity::getDeleteFlag, 0);
        eq.orderByDesc(OrderInfoEntity::getCreateTime);
        if (StringUtils.isNotEmpty(dto.getCustomerPhone())){
            eq
//                    .ne(OrderInfoEntity::getSourceType, 1)
                    .like(StringUtils.isNotBlank(dto.getCustomerPhone()), OrderInfoEntity::getCustomerPhone, dto.getCustomerPhone())
                    .notIn(OrderInfoEntity::getCurrentNode,
                            Arrays.asList(
                                    States.PROCESS_TERMINAL.getNode(),
                                    States.SYSTEM_TERMINAL.getNode(),
                                    States.ORDER_FREEZE.getNode()
                            )
                    );
        }
        if (ObjUtil.isNotEmpty(dto.getPreId())){
            eq.eq(ObjUtil.isNotNull(dto.getPreId()), OrderInfoEntity::getPreId, dto.getPreId());
            eq.last("limit 1");
        }
        if (ObjUtil.isNotEmpty(dto.getOrderId())){
            eq.eq(ObjUtil.isNotNull(dto.getOrderId()), OrderInfoEntity::getId, dto.getOrderId());
        }
        GetOrderInfoByPhoneVO getOrderInfoByPhoneVO = orderInfoMapper.selectJoinOne(
                GetOrderInfoByPhoneVO.class,eq);

        log.info("kingdeeServiceImpl.getOrderInfoById:{}", getOrderInfoByPhoneVO);
        if (ObjUtil.isNotEmpty(getOrderInfoByPhoneVO)) {
            //获取订单id列表
//            List<Integer> orderIdList = list.stream().map(GetOrderInfoByPhoneVO::getOnline_order_id).toList();
//            List<Integer> preIdList = list.stream().map(GetOrderInfoByPhoneVO::getPre_approved_id).toList();
            List<OrderAmountEntity> orderAmountEntityList = orderAmountMapper.selectList(
                    new LambdaQueryWrapper<OrderAmountEntity>()
//                            .in(OrderAmountEntity::getOrderId, orderIdList)
                            .eq(OrderAmountEntity::getOrderId, getOrderInfoByPhoneVO.getOnline_order_id())
                            .eq(OrderAmountEntity::getDeleteFlag, 0)
            );
            Map<Integer, OrderAmountEntity> orderAmountMap = new HashMap<>();
            if (CollUtil.isNotEmpty(orderAmountEntityList)) {
                //订单id和订单额度的映射
                orderAmountMap.putAll(orderAmountEntityList.stream()
                        .collect(Collectors.toMap(OrderAmountEntity::getOrderId, entity -> entity)));
            }

            List<OrderCompanyInfoEntity> orderCompanyInfoEntityList = orderCompanyInfoMapper.selectList(
                    new LambdaQueryWrapper<OrderCompanyInfoEntity>()
//                            .in(OrderCompanyInfoEntity::getOrderId, orderIdList)
                            .eq(OrderCompanyInfoEntity::getOrderId, getOrderInfoByPhoneVO.getOnline_order_id())
                            .eq(OrderCompanyInfoEntity::getDeleteFlag, 0)
            );
            Map<Integer, OrderCompanyInfoEntity> orderCompanyInfoEntityMap = new HashMap<>();
            if (CollUtil.isNotEmpty(orderCompanyInfoEntityList)) {
                //订单id和营业执照信息的映射
                orderCompanyInfoEntityMap.putAll(orderCompanyInfoEntityList.stream()
                        .collect(Collectors.toMap(OrderCompanyInfoEntity::getOrderId, entity -> entity)));
            }
            List<PreApprovalApplyInfoEntity> preApprovalApplyInfoEntityList = preApprovalApplyInfoMapper.selectList(
                    new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
//                            .in(PreApprovalApplyInfoEntity::getId, preIdList)
                            .eq(PreApprovalApplyInfoEntity::getId, getOrderInfoByPhoneVO.getPre_approved_id())
                            .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
            );
            Map<Integer, PreApprovalApplyInfoEntity> preApprovalApplyInfoEntityMap = new HashMap<>();
            if (CollUtil.isNotEmpty(preApprovalApplyInfoEntityList)) {
                //订单id和营业执照信息的映射
                preApprovalApplyInfoEntityMap.putAll(preApprovalApplyInfoEntityList.stream()
                        .collect(Collectors.toMap(PreApprovalApplyInfoEntity::getId, entity -> entity)));
            }

            List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(
                    new LambdaQueryWrapper<OrderInfoEntity>()
                            .eq(OrderInfoEntity::getId,getOrderInfoByPhoneVO.getOnline_order_id())
            );
            Map<Integer, OrderInfoEntity> orderInfoEntityMap = new HashMap<>();
            if (CollUtil.isNotEmpty(orderInfoEntityList)) {
                orderInfoEntityMap.putAll(orderInfoEntityList.stream()
                        .collect(Collectors.toMap(OrderInfoEntity::getId, entity -> entity)));
            }
//            for (GetOrderInfoByPhoneVO getOrderInfoByPhoneVO : list) {
            if (CollUtil.isNotEmpty(orderAmountMap) && orderAmountMap.containsKey(getOrderInfoByPhoneVO.getOnline_order_id())) {
                OrderAmountEntity orderAmountEntity = orderAmountMap.get(getOrderInfoByPhoneVO.getOnline_order_id());
                getOrderInfoByPhoneVO.setAmount_money(orderAmountEntity.getHopeAmount());
                getOrderInfoByPhoneVO.setEstimate_amount(orderAmountEntity.getAppraiserAmount());
                getOrderInfoByPhoneVO.setApproval_amount(orderAmountEntity.getCustomerConfirmAmount());
                getOrderInfoByPhoneVO.setCredit_amount(orderAmountEntity.getFundPreAmount());
                getOrderInfoByPhoneVO.setLoan_amount(orderAmountEntity.getCustomerConfirmAmount());
                getOrderInfoByPhoneVO.setApproval_update_amount(orderAmountEntity.getPreAmount());
                getOrderInfoByPhoneVO.setRisk_amount(orderAmountEntity.getRiskAmount());
            }
            if (CollUtil.isNotEmpty(orderCompanyInfoEntityMap) && orderCompanyInfoEntityMap.containsKey(getOrderInfoByPhoneVO.getOnline_order_id())) {
                OrderCompanyInfoEntity orderCompanyInfoEntity = orderCompanyInfoEntityMap.get(getOrderInfoByPhoneVO.getOnline_order_id());
                if (ObjUtil.isNotNull(orderCompanyInfoEntity)) {
                    getOrderInfoByPhoneVO.setLicense_data(JSONUtil.toJsonStr(orderCompanyInfoEntity));
                }
            }
            getOrderInfoByPhoneVO.setType(2);
            if (CollUtil.isNotEmpty(preApprovalApplyInfoEntityMap) && preApprovalApplyInfoEntityMap.containsKey(getOrderInfoByPhoneVO.getPre_approved_id())) {
                PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoEntityMap.get(getOrderInfoByPhoneVO.getPre_approved_id());
                getOrderInfoByPhoneVO.setId_number(preApprovalApplyInfoEntity.getIdNumber());
                getOrderInfoByPhoneVO.setPre_approved_status(PreApplyInfoFundStatus.APPROVAL_SUCCESS.equals(preApprovalApplyInfoEntity.getFundStatus()) ? 1 : 2);
                getOrderInfoByPhoneVO.setPre_remark(preApprovalApplyInfoEntity.getRemark());
                getOrderInfoByPhoneVO.setRisk_number(preApprovalApplyInfoEntity.getRiskNumber());
                getOrderInfoByPhoneVO.setPre_approved_data(JSONUtil.toJsonStr(preApprovalApplyInfoEntity));
                if (ObjUtil.isNotNull(preApprovalApplyInfoEntity.getCreditInquiryTime())) {
                    getOrderInfoByPhoneVO.setPre_verify_time(LocalDateTimeUtil.format(preApprovalApplyInfoEntity.getCreditInquiryTime(), DatePattern.NORM_DATETIME_PATTERN));
                }
//                DxReservationInfoEntity dxReservationInfoEntity = dxReservationInfoMapper.selectOne(new LambdaQueryWrapper<>(DxReservationInfoEntity.class)
//                        .eq(DxReservationInfoEntity::getPhone, preApprovalApplyInfoEntity.getPhone())
//                        .ge(DxReservationInfoEntity::getCreateTime, LocalDateTime.now().minusDays(90))
//                        .eq(DxReservationInfoEntity::getDeleteFlag, 0)
//                        .orderByDesc(DxReservationInfoEntity::getCreateTime), false);
//                if (ObjUtil.isNotNull(dxReservationInfoEntity)) {
                if (ObjUtil.equals(preApprovalApplyInfoEntity.getRegionId(),56) || ObjUtil.equals(preApprovalApplyInfoEntity.getRegionId(),24)) {
                    getOrderInfoByPhoneVO.setType(1);
                }
            }
            if (CollUtil.isNotEmpty(orderInfoEntityMap) && orderInfoEntityMap.containsKey(getOrderInfoByPhoneVO.getOnline_order_id())) {
                OrderInfoEntity orderInfoEntity = orderInfoEntityMap.get(getOrderInfoByPhoneVO.getOnline_order_id());
                getOrderInfoByPhoneVO.setCustomer_name(orderInfoEntity.getCustomerName());
                getOrderInfoByPhoneVO.setCustomer_phone(orderInfoEntity.getCustomerPhone());
                getOrderInfoByPhoneVO.setCapital_id(orderInfoEntity.getFundId());
                getOrderInfoByPhoneVO.setCapital_name(orderInfoEntity.getFundName());
                getOrderInfoByPhoneVO.setProduct_id(orderInfoEntity.getProductId());
                getOrderInfoByPhoneVO.setProduct_name(orderInfoEntity.getProductName());
                getOrderInfoByPhoneVO.setApply_purpose(orderInfoEntity.getApplyPurpose());
                getOrderInfoByPhoneVO.setTerm(orderInfoEntity.getTerm());
                getOrderInfoByPhoneVO.setNode(orderInfoEntity.getCurrentNode());
                getOrderInfoByPhoneVO.setStore_id(orderInfoEntity.getDeptId());
                getOrderInfoByPhoneVO.setStore_name(orderInfoEntity.getStoreName());
                getOrderInfoByPhoneVO.setManager_id(orderInfoEntity.getManagerId());
                getOrderInfoByPhoneVO.setRegion_id(orderInfoEntity.getRegionId());
                getOrderInfoByPhoneVO.setRegion_name(orderInfoEntity.getRegionName());
                getOrderInfoByPhoneVO.setGps_state(orderInfoEntity.getGpsState());
                getOrderInfoByPhoneVO.setContract_state(orderInfoEntity.getContractState());
                getOrderInfoByPhoneVO.setMortgage_state(orderInfoEntity.getMortgageState());
                getOrderInfoByPhoneVO.setReconsider_state(orderInfoEntity.getReconsiderState());
                getOrderInfoByPhoneVO.setReview_state(orderInfoEntity.getReviewState());
                getOrderInfoByPhoneVO.setConfirm_state(orderInfoEntity.getConfirmState());
                getOrderInfoByPhoneVO.setManagement_conclusion(orderInfoEntity.getManagementConclusion());
                getOrderInfoByPhoneVO.setPayment_type(orderInfoEntity.getPaymentType());
                getOrderInfoByPhoneVO.setRepay_method(orderInfoEntity.getRepayMethod());
                getOrderInfoByPhoneVO.setRisk_number(orderInfoEntity.getRiskNumber());
                getOrderInfoByPhoneVO.setOrder_status(orderInfoEntity.getCurrentNode());
                getOrderInfoByPhoneVO.setCar_plate(orderInfoEntity.getVehicleNumber());

                if (ObjUtil.isNotNull(orderInfoEntity.getQualityTestCommitTime())) {
                    getOrderInfoByPhoneVO.setQuality_test_commit_time(LocalDateTimeUtil.format(orderInfoEntity.getQualityTestCommitTime(), DatePattern.NORM_DATETIME_PATTERN));
                }
                if (ObjUtil.isNotNull(orderInfoEntity.getPaymentTime())) {
                    getOrderInfoByPhoneVO.setSucc_order_time(LocalDateTimeUtil.format(orderInfoEntity.getPaymentTime(), DatePattern.NORM_DATETIME_PATTERN));
                }
                if (ObjUtil.isNotNull(orderInfoEntity.getPreApplyTime())) {
                    getOrderInfoByPhoneVO.setPre_apply_time(LocalDateTimeUtil.format(orderInfoEntity.getPreApplyTime(), DatePattern.NORM_DATETIME_PATTERN));
                }
                if (ObjUtil.isNotNull(orderInfoEntity.getPaymentState())) {
                    getOrderInfoByPhoneVO.setPayment_state(orderInfoEntity.getPaymentState().getValue());
                }
                if (ObjUtil.equals(orderInfoEntity.getCurrentNode(), -1000) || ObjUtil.equals(orderInfoEntity.getCurrentNode(), -2000)) {
                    getOrderInfoByPhoneVO.setZhongzhi_node(-1000);
                }
                OrderNodeRecordEntity orderNodeRecordEntity = orderNodeRecordMapper.selectOne(
                        new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                .eq(OrderNodeRecordEntity::getOrderId,orderInfoEntity.getId())
                                .eq(OrderNodeRecordEntity::getNextNode,orderInfoEntity.getCurrentNode())
                                .orderByDesc(OrderNodeRecordEntity::getCreateTime)
                                .last("limit 1")
                );
                if (ObjUtil.isNotEmpty(orderNodeRecordEntity)){
                    getOrderInfoByPhoneVO.setNode_s(orderNodeRecordEntity.getEvent().getCode());
                    if (ObjUtil.equals(orderNodeRecordEntity.getNextNode(),5000)){
                        getOrderInfoByPhoneVO.setNode_s(2);
                    }
                }else {
                    getOrderInfoByPhoneVO.setNode_s(1);
                }
//                DxReservationInfoEntity dxReservationInfoEntity = dxReservationInfoMapper.selectOne(new LambdaQueryWrapper<>(DxReservationInfoEntity.class)
//                        .eq(DxReservationInfoEntity::getPhone, orderInfoEntity.getCustomerPhone())
//                        .ge(DxReservationInfoEntity::getCreateTime, LocalDateTime.now().minusDays(90))
//                        .eq(DxReservationInfoEntity::getDeleteFlag, 0)
//                        .orderByDesc(DxReservationInfoEntity::getCreateTime), false);
                if (ObjUtil.equals(orderInfoEntity.getRegionId(),56) || ObjUtil.equals(orderInfoEntity.getRegionId(),24)) {
                    getOrderInfoByPhoneVO.setType(1);
                }
            }
            if (ObjUtil.isNotNull(getOrderInfoByPhoneVO.getManager_id())) {
                UserDetailInfoVO userDetailInfoVO = userFeign.searchUserDetailById(getOrderInfoByPhoneVO.getManager_id()).getData();
                if (ObjUtil.isNotNull(userDetailInfoVO)) {
                    getOrderInfoByPhoneVO.setManager_name(userDetailInfoVO.getName());
                }
            }
            getOrderInfoByPhoneVO.setIs_consistent(1);
            getOrderInfoByPhoneVO.setFirst_remakes("初审备注信息");
            getOrderInfoByPhoneVO.setOrder_status(switch (getOrderInfoByPhoneVO.getNode()) {
                case -1000, -2000 -> 3;
                case 5000 -> 2;
                case 8000 -> 4;
                default -> 1;
            });

            getOrderInfoByPhoneVO.setPre_verify_seats(null);
            getOrderInfoByPhoneVO.setSpecial_msg(0);
            getOrderInfoByPhoneVO.setAppointment_time("0");
            getOrderInfoByPhoneVO.setBusiness_type(1);
            KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<>(KingdeeInfoEntity.class)
                    .eq(KingdeeInfoEntity::getPreId, getOrderInfoByPhoneVO.getPre_approved_id())
                    .orderByDesc(KingdeeInfoEntity::getCreateTime), false);
            if (ObjUtil.isNotNull(kingdeeInfoEntity)) {
                getOrderInfoByPhoneVO.setClue_id(kingdeeInfoEntity.getClueId());
            }
//            }
        }
        log.info("getOrderInfoByPhoneVO:{}", JSONUtil.toJsonStr(getOrderInfoByPhoneVO));
        return getOrderInfoByPhoneVO;
    }

    @Override
    public String getLoginToken(LoginUser currentUser) {
        String jsonPrettyStr = JSONUtil.toJsonStr(new JSONObject().set("phone", currentUser.getMobile()));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        String result = kingdeeFeign.getLoginToken(body, sign, iv, timestamp);
        extracted(result);
        if (StrUtil.isNotBlank(result)) {
            JSONObject jsonObject = JSONUtil.parseObj(result);
            if (jsonObject.getInt("code") == 200) {
                JSONObject data = jsonObject.getJSONObject("data");
                if (ObjUtil.isNotNull(data)) {
                    String token = data.getStr("token");
                    if (StrUtil.isNotBlank(token)) {
                        return token;
                    }
                }
            }
        }
        return "";
    }

    @Override
    public String getPermanentUrl(KingdeeInsertUrlDTO dto) {
        log.info("getPermanentUrl:servicePhone",dto.getServicePhone());
        String format = String.format(hostname + staticCodeUrl, dto.getServicePhone(),  dto.getServiceId());
        Result<List<UserInfoVO>> listResult = userFeign.mobile2UserId(Collections.singletonList(dto.getServicePhone()));
        Integer userId = null;
        if (Result.success().getCode().equals(listResult.getCode()) && CollUtil.isNotEmpty(listResult.getData())){
            userId = listResult.getData().get(0).getUserId();
        }
        return userFeign.generateQrCodeUrl(new QrCodeDTO().setUrl(format).setUserId(userId));
    }

    @Override
    public CRMIdentityCardVO incomingCheck(IdentityCardDTO identityCardDTO) {
        JSONObject  jsonObject = new JSONObject();
        jsonObject.putOpt("seat_id", identityCardDTO.getServiceId())
                .putOpt("mobile", identityCardDTO.getPhone())
                .putOpt("clue_id", identityCardDTO.getClueId())
                .putOpt("clue_name", identityCardDTO.getUpdateName())
                .putOpt("city_name", identityCardDTO.getCityName())
                .putOpt("address", identityCardDTO.getUpdateAddress())
                .putOpt("id_number", identityCardDTO.getUpdateIdNumber());
        if (ObjUtil.isNotNull(identityCardDTO.getPreId())){
            PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(identityCardDTO.getPreId());
            if (ObjUtil.isNotNull(preApprovalApplyInfoEntity)) {
                jsonObject.putOpt("pre_id", identityCardDTO.getPreId())
                        .putOpt("old_mobile", preApprovalApplyInfoEntity.getPhone())
                        .putOpt("old_id_number", preApprovalApplyInfoEntity.getIdNumber());
                PreOcrVehicleInfoEntity preOcrVehicleInfoEntity = preOcrVehicleInfoMapper.selectOne(new LambdaQueryWrapper<PreOcrVehicleInfoEntity>()
                        .eq(PreOcrVehicleInfoEntity::getPreId, identityCardDTO.getPreId())
                        .eq(PreOcrVehicleInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(PreOcrVehicleInfoEntity::getUpdateTime)
                        .last("limit 1"));
                if (ObjUtil.isNotNull(preOcrVehicleInfoEntity)) {
                    jsonObject.putOpt("old_vehicle_number", preOcrVehicleInfoEntity.getVehicleNumber())
                            .putOpt("old_vin", preOcrVehicleInfoEntity.getVin());
                }
            }

        }else {
            List<PreApprovalApplyInfoEntity> preApprovalApplyInfoEntities = preApprovalApplyInfoMapper.selectList(new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
                    .eq(PreApprovalApplyInfoEntity::getSourceType, 1)
                    .and(and-> and.eq(PreApprovalApplyInfoEntity::getIdNumber, identityCardDTO.getUpdateIdNumber())
                            .or()
                            .eq(PreApprovalApplyInfoEntity::getPhone, identityCardDTO.getPhone())
                    )
                    .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(PreApprovalApplyInfoEntity::getCreateTime)
            );
            log.info("KingdeeServiceImpl.checkProtection preApprovalApplyInfoEntities:{}", JSONUtil.toJsonStr(preApprovalApplyInfoEntities));
            if (!CollUtil.isEmpty(preApprovalApplyInfoEntities)) {
                for(PreApprovalApplyInfoEntity preApprovalApplyInfoEntity : preApprovalApplyInfoEntities){
                    //征信签署文件
                    List<PreFddFinishFileEntity> preFddFinishFileEntities = preFddFinishFileMapper.selectList(new LambdaQueryWrapper<PreFddFinishFileEntity>()
                                    .eq(PreFddFinishFileEntity::getPreId, preApprovalApplyInfoEntity.getId())
                                    .eq(PreFddFinishFileEntity::getDeleteFlag, 0)
                            //                .eq(PreFddFinishFileEntity::getFundingId, huaRuiFileId)
                    );
                    if(CollUtil.isNotEmpty(preFddFinishFileEntities) && ObjectUtil.equal(preApprovalApplyInfoEntity.getBusinessStatus(),ApprovalApplyInfoBusinessStatus.SUBMITTED_PRELIMINARY_APPROVAL)){
                        jsonObject.putOpt("pre_id", preApprovalApplyInfoEntity.getId())
                                .putOpt("old_mobile", preApprovalApplyInfoEntity.getPhone())
                                .putOpt("old_id_number", preApprovalApplyInfoEntity.getIdNumber());
                        PreOcrVehicleInfoEntity preOcrVehicleInfoEntity = preOcrVehicleInfoMapper.selectOne(new LambdaQueryWrapper<PreOcrVehicleInfoEntity>()
                                .eq(PreOcrVehicleInfoEntity::getPreId, identityCardDTO.getPreId())
                                .eq(PreOcrVehicleInfoEntity::getDeleteFlag, 0)
                                .orderByDesc(PreOcrVehicleInfoEntity::getUpdateTime)
                                .last("limit 1"));
                        if (ObjUtil.isNotNull(preOcrVehicleInfoEntity)) {
                            jsonObject.putOpt("old_vehicle_number", preOcrVehicleInfoEntity.getVehicleNumber())
                                    .putOpt("old_vin", preOcrVehicleInfoEntity.getVin());
                        }
                        break;
                    }
                }
            }
        }
        String jsonPrettyStr = JSONUtil.toJsonStr(jsonObject);
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        log.info("kingdeeFeign.incomingCheck req:{}", jsonObject);
        String result = kingdeeFeign.incomingCheck(body, sign, iv, timestamp);
        log.info("kingdeeFeign.incomingCheck res:{}", result);
        JSONObject entries = JSONUtil.parseObj(result);
        if (!"200".equals(entries.getStr("code"))) {
            throw new BusinessException(new IResultCode() {
                @Override
                public String getCode() {
                    return "OnLineCloseTheLink";
                }

                @Override
                public String getMsg() {
                    return entries.getStr("message");
                }
            });
        }
        JSONArray jsonArray = entries.getJSONObject("data").getJSONArray("dingding_msg");
        List<CRMIdentityCardVO.DingDingMsgDTO> list = JSONUtil.toList(jsonArray, CRMIdentityCardVO.DingDingMsgDTO.class);
        Integer clueId = entries.getJSONObject("data").getInt("clue_id");
        String seatPhone = entries.getJSONObject("data").getStr("seat_phone");
        return new CRMIdentityCardVO()
                .setClueId(clueId)
                .setPhone(seatPhone)
                .setDingDingMsg(list);
    }

    @Override
    public URI getOrderNodeUrl(Integer clueId, String cluePhone, String accessToken, Integer preId, Integer orderId) {
        log.info("KingdeeServiceImpl.getOrderNodeUrl hostname{}", hostname);
        String code=hostname+msgUrl;
        if (ObjUtil.isNotNull(clueId)){
            KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<KingdeeInfoEntity>()
                    .eq(KingdeeInfoEntity::getClueId, clueId)
                    .eq(KingdeeInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(KingdeeInfoEntity::getCreateTime)
                    .last("LIMIT 1"));
            if (ObjUtil.isNotNull(kingdeeInfoEntity)){
                preId = kingdeeInfoEntity.getPreId();
            }else {
                //返回预审进件链接
                return URI.create(String.format(hostname+url,clueId,cluePhone)+"&token="+accessToken);
            }
        }
        //预审信息不为空
        if (ObjUtil.isNotNull(preId)){
            PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(preId);
            if (ObjUtil.isNotNull(preApprovalApplyInfoEntity)&& ObjUtil.notEqual(preApprovalApplyInfoEntity.getIsReset(), 1)){
                if (ObjUtil.notEqual(preApprovalApplyInfoEntity.getBusinessStatus(), ApprovalApplyInfoBusinessStatus.SUBMITTED_PRELIMINARY_APPROVAL)){
                    return URI.create(String.format(hostname+url,clueId,cluePhone)+"&preId="+preId+"&token="+accessToken);
                }else if (ObjUtil.notEqual(preApprovalApplyInfoEntity.getManagerState(),PreApplyInfoManagerStatus.APPROVAL_SUCCESS)){
                    if (ObjUtil.equals(preApprovalApplyInfoEntity.getRiskStatus(), 6)){
                        //返回提示请咨询业务经理
                        return URI.create(String.format(code,"请咨询业务经理"));
                    }else if(ObjUtil.equals(preApprovalApplyInfoEntity.getRiskStatus(), 0)){
                        return URI.create(String.format(hostname+carInfoPageUrl,preId,accessToken));
                    }else{
                        //返回提示 申请审核中，请等待2-5分钟或咨询业务经理
                        return URI.create(String.format(code,"申请审核中，请等待2-5分钟或咨询业务经理"));
                    }
                }else {
                    //返回订单id
                    OrderInfoEntity orderInfoEntities = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                            .eq(OrderInfoEntity::getPreId, preId)
                            .eq(OrderInfoEntity::getDeleteFlag, 0)
                            .orderByDesc(OrderInfoEntity::getCreateTime)
                            .last("limit 1"));
                    if (ObjUtil.isNotNull(orderInfoEntities)){
                        orderId=orderInfoEntities.getId();
                    }else {
                        //返回订单进件链接
                        return URI.create(String.format(hostname+url,preId,clueId,cluePhone)+"&token="+accessToken);
                    }
                }
            }else {
                //返回预审进件链接
                return URI.create(String.format(hostname+url,clueId,cluePhone)+"&token="+accessToken);
            }
        }
        //订单存在
        if (ObjUtil.isNotNull(orderId)){
            URI uri = getOrderNodeUrl(accessToken, preId, orderId, code);
            if (uri != null) return uri;
        }
        return URI.create(String.format(hostname+url,clueId,cluePhone)+"&token="+accessToken);
    }

    @Override
    public CRMIdentityCardVO incomingCheckVehicleNumber(InsertOcrVehicleInfoDTO insertOcrVehicleInfoDTO) {
        PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(insertOcrVehicleInfoDTO.getPreId());
        Assert.notNull(preApprovalApplyInfoEntity,()->new BusinessException( "预审批申请信息不存在"));
        UserInfoVO userInfoVOResult = userFeign.searchUserName(preApprovalApplyInfoEntity.getAccountManagerId()).getData();
        Assert.notNull(userInfoVOResult,()->new BusinessException( "业务经理不存在"));
        if (ObjUtil.notEqual(userInfoVOResult.getMobile(),"***********")){
            return null;
        }
        KingdeeInfoEntity kingdeeInfoEntity = kingdeeInfoMapper.selectOne(new LambdaQueryWrapper<KingdeeInfoEntity>()
                .eq(KingdeeInfoEntity::getPreId, insertOcrVehicleInfoDTO.getPreId())
                .eq(KingdeeInfoEntity::getDeleteFlag, 0)
                .orderByDesc(KingdeeInfoEntity::getCreateTime)
                .last("LIMIT 1"));
        if (ObjUtil.isNull(kingdeeInfoEntity)){
            return null;
        }
        JSONObject  jsonObject = new JSONObject();
        jsonObject.putOpt("vehicle_number", insertOcrVehicleInfoDTO.getUpdateVehicleNumber());
        jsonObject.putOpt("clue_id", kingdeeInfoEntity.getClueId());
        String jsonPrettyStr = JSONUtil.toJsonStr(jsonObject);
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        log.info("kingdeeFeign.incomingCheckVehicleNumber req:{}", jsonObject);
        String result = kingdeeFeign.incomingCheck(body, sign, iv, timestamp);
        log.info("kingdeeFeign.incomingCheckVehicleNumber rep:{}", result);
        JSONObject entries = JSONUtil.parseObj(result);
        if (!"200".equals(entries.getStr("code"))) {
            throw new BusinessException(new IResultCode() {
                @Override
                public String getCode() {
                    return "OnLineCloseTheLink";
                }

                @Override
                public String getMsg() {
                    return entries.getStr("message");
                }
            });
        }
        JSONArray jsonArray = entries.getJSONObject("data").getJSONArray("dingding_msg");
        List<CRMIdentityCardVO.DingDingMsgDTO> list = JSONUtil.toList(jsonArray, CRMIdentityCardVO.DingDingMsgDTO.class);
        CRMIdentityCardVO crmIdentityCardVO = new CRMIdentityCardVO()
                .setClueId(entries.getJSONObject("data").getInt("clue_id"))
                .setPhone(entries.getJSONObject("data").getStr("seat_phone"))
                .setDingDingMsg(list);
        if (ObjUtil.notEqual(kingdeeInfoEntity.getClueId(),crmIdentityCardVO.getClueId())){
            kingdeeInfoEntity.setClueId(crmIdentityCardVO.getClueId());
            kingdeeInfoMapper.updateById(kingdeeInfoEntity);
        }
        return crmIdentityCardVO;
    }

    @Override
    public KingDeeUpdateManagerVO updateManager(KingDeeUpdateManagerDTO dto) {
        UserDetailInfoVO data = userFeign.searchUserDetailByMobile(dto.getServicerPhone()).getData();
        Result<List<UserStoreVO>> result = userFeign.searchUserStoreBatch(Collections.singletonList(data.getUserId()));

        if (result == null || CollUtil.isEmpty(result.getData())) {
            throw new BusinessException("客户经理不存在");
        }
        UserStoreVO userVO = result.getData().get(0);
        if (ObjUtil.isNotNull(data)&&ObjUtil.isNotNull(data.getUserId())){
            preManagerApprovalService.batchUpdateManager(new updateManagerDTO().setManagerId(data.getUserId()).setPreIds(dto.getPreIds()));
        }
        return new KingDeeUpdateManagerVO().setManagerId(userVO.getUserId())
                .setManagerName(userVO.getName())
                .setStoreId(userVO.getStoreId())
                .setStoreName(userVO.getStore())
                .setRegionId(userVO.getAreaId())
                .setRegionName(userVO.getArea());
    }

    @Nullable
    private URI getOrderNodeUrl(String accessToken, Integer preId, Integer orderId, String code) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        if (ObjUtil.isNotNull(orderInfoEntity)){
            switch (States.getNode(orderInfoEntity.getState())){
                // 待业务补录
                case BUSINESS_ADDED_INFO -> {
                    //返回补录链接
                    return URI.create(String.format(hostname + businessAddedUrl, preId, orderId, accessToken));
                }
                case QUALITY_INSPECTION,
                     QUALITY_INSPECTION_FINISH,
                     STORE_EVALUATION,
                     OVERALL_REVIEW,
                     RISK_FIRST_APPROVE,
                     RISK_FIRST_APPROVE_ASSIGN,
                     RISK_FINAL_APPROVE,
                     FUNDS_FINAL_APPROVE-> {
                    //返回提示 审核中，请等待
                    return URI.create(String.format(code,"审核中，请等待"));
                }
                case CUSTOMER_CONFIRM -> {
                    //返回客户确认链接
                    return URI.create(String.format(hostname + customerConfirmUrl, preId, orderId, accessToken));
                }
                case CUSTOMER_APPOINTMENT -> {
                    OrderFeeInfoEntity orderFeeInfoEntity = orderFeeInfoMapper.selectOne(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                            .eq(OrderFeeInfoEntity::getOrderId, orderId)
                            .eq(OrderFeeInfoEntity::getFeeType, 1)
                            .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                            .eq(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                            .orderByDesc(OrderFeeInfoEntity::getCreateTime).last("limit 1"), false);
                    if (ObjUtil.notEqual(orderInfoEntity.getContractState(),2)&&ObjUtil.isNotNull(orderFeeInfoEntity)){
                        //返回合同签约链接
                        return URI.create(String.format(hostname + contractSigning, preId, orderId, accessToken));
                    }else {
                        //返回提示 请联系业务经理预约服务
                        return URI.create(String.format(code, "请联系业务经理预约服务"));
                    }
                }
                case PAYMENT_APPLY_INFORMATION,
                     PAYMENT_CONTRACT_APPROVAL
                     -> {
                    //返回提示 放款审核中
                    return URI.create(String.format(code, "放款审核中"));
                }
                case FUNDS_PAYMENT_PROCESS,
                     FUNDS_PAYMENT_APPROVAL-> {
                    //返回提示 放款中，预估资金24小时到账
                    return URI.create(String.format(code, "放款中，预估资金24小时到账"));
                }
                case PAYMENT_SUCCESS -> {
                    //返回提示 已放款，请查收
                    return URI.create(String.format(code, "已放款，请查收"));
                }
                default -> {
                    //返回提示 请咨询业务经理
                    return URI.create(String.format(code, "请咨询业务经理"));
                }
            }
        }
        return null;
    }

    @Override
    public UserResignationSyncVO syncUserResignation(UserResignationSyncDTO dto) {
        log.info("开始处理人员离职状态同步，手机号：{}，状态：{}", dto.getPhone(), dto.getStatus());

        try {
            // 1. 参数验证
            if (dto.getStatus() != 0 && dto.getStatus() != 1) {
                throw new BusinessException("变动类型参数错误，只能为0（在职）或1（离职）");
            }

            // 2. 根据手机号查询用户信息
            Result<UserDetailInfoVO> userResult = userFeign.searchUserDetailByMobile(dto.getPhone());
            if (!Result.success().getCode().equals(userResult.getCode()) || userResult.getData() == null) {
                log.warn("根据手机号{}未找到用户信息", dto.getPhone());
                return new UserResignationSyncVO()
                        .setSuccess(false)
                        .setMessage("未找到该手机号对应的用户信息")
                        .setPhone(dto.getPhone())
                        .setStatus(dto.getStatus());
            }

            UserDetailInfoVO userInfo = userResult.getData();

            // 3. 检查当前状态是否需要更新
            if (Objects.equals(userInfo.getDeleteFlag(), dto.getStatus())) {
                log.info("用户{}状态无需更新，当前状态已为：{}", userInfo.getName(), dto.getStatus());
                return new UserResignationSyncVO()
                        .setSuccess(true)
                        .setMessage("用户状态无需更新")
                        .setUserId(userInfo.getUserId())
                        .setUserName(userInfo.getName())
                        .setPhone(dto.getPhone())
                        .setStatus(dto.getStatus());
            }

            // 4. 调用用户服务更新delete_flag字段
            Result<Boolean> updateResult = userFeign.updateUserDeleteFlag(userInfo.getUserId(), dto.getStatus());
            if (!Result.success().getCode().equals(updateResult.getCode()) || !Boolean.TRUE.equals(updateResult.getData())) {
                log.error("更新用户{}离职状态失败：{}", userInfo.getName(), updateResult.getMsg());
                return new UserResignationSyncVO()
                        .setSuccess(false)
                        .setMessage("更新用户状态失败：" + (updateResult.getMsg() != null ? updateResult.getMsg() : "未知错误"))
                        .setUserId(userInfo.getUserId())
                        .setUserName(userInfo.getName())
                        .setPhone(dto.getPhone())
                        .setStatus(dto.getStatus());
            }

            log.info("成功更新用户{}的离职状态为：{}", userInfo.getName(), dto.getStatus());

            return new UserResignationSyncVO()
                    .setSuccess(true)
                    .setMessage("用户离职状态同步成功")
                    .setUserId(userInfo.getUserId())
                    .setUserName(userInfo.getName())
                    .setPhone(dto.getPhone())
                    .setStatus(dto.getStatus());

        } catch (BusinessException e) {
            log.error("人员离职状态同步业务异常：{}", e.getMessage());
            return new UserResignationSyncVO()
                    .setSuccess(false)
                    .setMessage(e.getMessage())
                    .setPhone(dto.getPhone())
                    .setStatus(dto.getStatus());
        } catch (Exception e) {
            log.error("人员离职状态同步系统异常", e);
            return new UserResignationSyncVO()
                    .setSuccess(false)
                    .setMessage("系统异常：" + e.getMessage())
                    .setPhone(dto.getPhone())
                    .setStatus(dto.getStatus());
        }
    }
}