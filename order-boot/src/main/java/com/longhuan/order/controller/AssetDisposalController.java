package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.vo.AssetDisposalListVO;
import com.longhuan.order.pojo.vo.AssetDisposalOrderApplyVO;
import com.longhuan.order.service.AssetDisposalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 资产处置接口
 *
 * <AUTHOR>
 * @date 2024/08/12
 */
@Api(tags = "资产处置接口")
@RestController
@RequestMapping("/api/v1/assetDisposal")
@RequiredArgsConstructor
public class AssetDisposalController {
     private final AssetDisposalService assetDisposalService;

     @ApiOperation("订单状态为逾期或者还款中并且订单未做委外添加到资产保全案件信息表")
     @PostMapping("/addAssetDisposalInfo")
     public Result<Boolean> addCaseInfo() {
          return Result.success(assetDisposalService.addAssetDisposalInfo());
     }


     @ApiOperation(value = "资产处置-已申请保全列表")
     @PostMapping("/preserveList")
     public Result<Page<AssetDisposalListVO>> preserveList(@RequestBody AssetDisposalListDTO assetDisposalListDTO, @CurrentUser LoginUser loginUser) {
          return Result.success(assetDisposalService.preserveList(assetDisposalListDTO, loginUser));
     }


     @ApiOperation(value = "资产处置-未申请保全列表")
     @PostMapping("/getAssetInfoOrderApplyList")
     public Result<Page<AssetDisposalOrderApplyVO>> getAssetInfoOrderApplyList(@RequestBody AssetDisposalListDTO assetDisposalListDTO) {
          return Result.success(assetDisposalService.getAssetInfoOrderApplyList(assetDisposalListDTO));
     }


     @ApiOperation(value = "资产处置-保全导出")
     @PostMapping("/preserveExport")
     public void preserveExport(@RequestBody AssetDisposalListDTO assetDisposalListDTO,
                                @CurrentUser LoginUser loginUser,
                                HttpServletResponse response
     ) {
          assetDisposalService.preserveExport(assetDisposalListDTO, loginUser,response);
     }

     //手动添加资产处置订单
     @ApiOperation(value = "手动添加资产处置订单")
     @PostMapping("/manualAddAssetInfo")
     public Result<Boolean> manualAddAssetInfo(@RequestBody @Validated AddCaseInfoDTO addCaseInfoDTO, @CurrentUser LoginUser loginUser) {
          return Result.success(assetDisposalService.manualAddAssetInfo(addCaseInfoDTO, loginUser));
     }

     @ApiOperation(value = "资产处置-保全审批")
     @PostMapping("/preserveApprove")
     public Result<Boolean> preserveApprove(@RequestBody PreserveApproveDTO dto,@CurrentUser LoginUser loginUser) {
          return Result.success(assetDisposalService.preserveApprove(dto,loginUser));
     }



     @ApiOperation(value = "资产保全订单审批")
     @PostMapping("/approveAssetDisposal")
     public Result<Boolean> approveCaseInfo(@RequestBody @Validated ApprovalAssetDisposalDTO approvalAssetDisposalDTO, @CurrentUser LoginUser loginUser) {
          return Result.success(assetDisposalService.approveAssetDisposal(approvalAssetDisposalDTO, loginUser));
     }





     @ApiOperation(value = "资产处置-保全申请")
     @PostMapping("/preserveRequest")
     public Result<Boolean> preserveRequest(@RequestBody PreserveRequestDTO dto) {
          return Result.success(assetDisposalService.preserveRequest(dto));
     }



}
