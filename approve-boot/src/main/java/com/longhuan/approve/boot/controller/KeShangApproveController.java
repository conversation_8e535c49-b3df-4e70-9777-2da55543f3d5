package com.longhuan.approve.boot.controller;


import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.longhuan.approve.boot.fund.keshang.KeShangRepaymentService;
import com.longhuan.approve.boot.fund.keshang.KeShangService;
import com.longhuan.approve.boot.pojo.dto.FundFinalBaseDTO;
import com.longhuan.approve.boot.pojo.dto.FundPreBaseDTO;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.KeShangRequestHead;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.KeShangResponseHead;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.request.*;
import com.longhuan.approve.boot.pojo.dto.mzkeshang.response.*;
import com.longhuan.approve.boot.service.FundBaseInfoService;
import com.longhuan.common.core.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 梅州客商
 */
@Api(tags = "梅州客商")
@Slf4j
@RestController
@RequestMapping("/api/v1/keshang")
@RequiredArgsConstructor
public class KeShangApproveController {

    private final KeShangService keShangService;
    private final FundBaseInfoService fundBaseInfoService;
    private final KeShangRepaymentService keShangRepaymentService;


    @ApiOperation("风控预审批")
    @PostMapping("/createRentplan")
    public Result<KeShangResponseHead> createRentplan(@RequestBody FundPreBaseDTO fundPreBaseDTO) {
        return Result.success(keShangService.keShangCreditPreApply(fundPreBaseDTO));
    }

    @ApiOperation("进件")
    @PostMapping("/addedBusiness")
    public Result<KeShangResponseHead> addedBusiness(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.keShangAddedBusiness(fundFinalBaseDTO));
    }

    @ApiOperation("进行撤销")
    @PostMapping("/addedBusinessCancel")
    public Result<KeShangAddBusinessCancelRequest> addedBusinessCancel(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.addedBusinessCancel(fundFinalBaseDTO));
    }


    @ApiOperation("进件信息变更")
    @PostMapping("/addedBusinessChange")
    public Result<KeShangAddBusinessChangeRequest> addedBusinessChange(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.addedBusinessChange(fundFinalBaseDTO));
    }

    @ApiOperation("放款指令")
    @GetMapping("/loanApply")
    public Result<KeShangResponseHead> loanApply(@RequestParam("orderId") Integer orderId) {
        return Result.success(keShangService.loanApplyByOrderId(orderId));
    }


    @ApiOperation("放款结果查询")
    @PostMapping("/applyLoanResult")
    public Result<KeShangResponseHead> applyLoanResult(@RequestParam("orderId") Integer orderId) {
        return Result.success(keShangService.applyLoanResult(orderId));
    }

    @ApiOperation("查询客户申贷信息")
    @PostMapping("/queryCustomerApplyLoanInfo")
    public Result<KeShangApplyLoanResponse> queryCustomerApplyLoanInfo(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.queryCustomerApplyLoanInfo(fundFinalBaseDTO));
    }

    @ApiOperation("查询还款结果")
    @PostMapping("/queryRepaymentResult")
    public Result<KeShangRepaymentResultResponse> queryRepaymentResult(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangRepaymentService.queryRepaymentResult(fundFinalBaseDTO));
    }

    @ApiOperation("还款计划查询接口")
    @GetMapping("/repaymenPlanQuery")
    public Result<Boolean> repaymenPlanQuery(@RequestParam("orderId") Integer orderId) {
        return Result.success(keShangRepaymentService.repaymenPlanQuery(orderId));
    }

    @ApiOperation("凭证开具查询接口")
    @PostMapping("/proofIssueQuery")
    public Result<KeShangQueryResponse> proofIssueQuery(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.proofIssueQuery(fundFinalBaseDTO));
    }

    @ApiOperation("文件签署查询接口")
    @PostMapping("/fileSignQuery")
    public Result<KeShangQueryResponse> fileSignQuery(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.fileSignQuery(fundFinalBaseDTO));
    }

    @ApiOperation("平台文件通知")
    @PostMapping("/platformFileNotice")
    public Result<KeShangPlatformFileNoticeRequest> platformFileNotice(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.platformFileNotice(fundFinalBaseDTO));
    }

    /**
     * 进件结果回调
     * @param callback
     * @return
     */
    @ApiOperation("进件结果通知")
    @PostMapping("/addBusinessResultNotice")
    public Result<KeShangResponseHead> addBusinessResultNotice(@RequestBody KeShangRequestHead callback) {
        log.info("KeShangApproveController.addBusinessResultNotice callback={}", JSONUtil.toJsonStr(callback));
        if (ObjUtil.isEmpty(callback)) {
            return Result.failed("参数为空");
        }
        return Result.success(keShangService.addBusinessResultNotice(callback));
    }

    @ApiOperation("放款结果通知")
    @PostMapping("/loanResultNotice")
    public Result<KeShangResponseHead> loanResultNotice(@RequestBody KeShangLoanResultRequest callback) {
        log.info("KeShangApproveController.loanResultNotice callPay body={}", JSONUtil.toJsonStr(callback));
        return Result.success(keShangService.loanResultNotice(callback));
    }

    @ApiOperation("面签结果通知")
    @PostMapping("/visaInterviewResultNotice")
    public Result<KeShangVisaInterviewResultRequest> visaInterviewResultNotice(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.visaInterviewResultNotice(fundFinalBaseDTO));
    }

    @ApiOperation("文件签署结果通知")
    @PostMapping("/fileSignResultNotice")
    public Result<KeShangFileSignResultRequest> fileSignResultNotice(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.fileSignResultNotice(fundFinalBaseDTO));
    }

    @ApiOperation("还款计划信息")
    @PostMapping("/repaymentPlanInfo")
    public Result<KeShangRepaymentPlanResponse.RepaymentPlanItem> repaymentPlanInfo(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangRepaymentService.repaymentPlanInfo(fundFinalBaseDTO));
    }

    @ApiOperation("还款流水信息")
    @PostMapping("/repaymentDetailInfo")
    public Result<KeShangRepaymentDetailRequest> RepaymentDetailInfo(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangRepaymentService.repaymentDetailInfo(fundFinalBaseDTO));
    }


    //  todo 借据信息

    @ApiOperation("客商银行卡鉴权")
    @PostMapping("/bankCardAuth")
    public Result<KeShangBankCardAuthRequest> bankCardAuth(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.bankCardAuth(fundFinalBaseDTO));
    }

    @ApiOperation("获取签署人签署链接")
    @PostMapping("/getSignatureLink")
    public Result<KeShangGetSignatureLinkRequest> getSignatureLink(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.getSignatureLink(fundFinalBaseDTO));
    }


    @ApiOperation("协议签约查询")
    @PostMapping("/agreeSignQuery")
    public Result<KeShangAgreeSignQueryResponse> agreeSignQuery(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.agreeSignQuery(fundFinalBaseDTO));
    }

    @ApiOperation("协议签约发送短信验证码")
    @PostMapping("/agreeSignSendCode")
    public Result<KeShangAgreeSignSendCodeRequest> agreeSignSendCode(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.agreeSignSendCode(fundFinalBaseDTO));
    }

    @ApiOperation("协议签约")
    @PostMapping("/agreeSign")
    public Result<KeShangAgreeSignRequest> agreeSign(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.agreeSign(fundFinalBaseDTO));
    }

    @ApiOperation("绑定卡查询")
    @PostMapping("/bindCardInfo")
    public Result<KeShangBindCardInfoResponse> bindCardInfo(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.bindCardInfo(fundFinalBaseDTO));
    }

    @ApiOperation("凭证开具")
    @PostMapping("/proofIssue")
    public Result<KeShangProofIssueRequest> proofIssue(@RequestBody FundFinalBaseDTO fundFinalBaseDTO) {
        return Result.success(keShangService.proofIssue(fundFinalBaseDTO));
    }

    @ApiOperation("test")
    @GetMapping("/test/{orderId}")
    public Result<String> test(@PathVariable("orderId") Integer orderId) {
        FundFinalBaseDTO fundFinalBaseDTO = fundBaseInfoService.getFundFinalBaseApplyInfo(orderId);
        KeShangResponseHead request = keShangService.keShangAddedBusiness(fundFinalBaseDTO);
        return Result.success(JSONUtil.toJsonStr(request));
    }





}
