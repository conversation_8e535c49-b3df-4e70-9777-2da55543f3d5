package com.longhuan.order;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.order.config.FadadaConfig;
import com.longhuan.order.enums.ContractEnum;
import com.longhuan.order.feign.FadadaFeign;
import com.longhuan.order.mapper.ArrivedDataMapper;
import com.longhuan.order.mapper.ContractToFundMapper;
import com.longhuan.order.mapper.OrderArrivedMapper;
import com.longhuan.order.mapper.OrderContractMapper;
import com.longhuan.order.pojo.dto.OrderArrivedDTO;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.service.FadadaAuthService;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.order.util.FadadaUtils;
import com.longhuan.resource.pojo.dto.DownloadContractDTO;
import jakarta.annotation.Resource;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Random;

@SpringBootTest(classes = OrderApplication.class)
public class FddTests {
    @Resource
    private  FadadaFeign fadadaFeign;
    @Resource
    private FadadaConfig fadadaConfig;
    @Test
    public void testTest() {
        //生成签名
        Map<String, String> paramMap = FadadaUtils.getSignature(fadadaConfig.getAppId(), fadadaConfig.getAppSecret());
        String tokenStr = fadadaFeign.getFDDAccessToken(fadadaConfig.getAppId(), fadadaConfig.getAppSecret(), paramMap.get("X-FASC-Timestamp"), paramMap.get("X-FASC-Nonce"), paramMap.get("X-FASC-Sign"));
        Assertions.assertNotNull(tokenStr);
        Assertions.assertNotEquals("", tokenStr);
    }
@Resource
private OrderArrivedMapper orderArrivedMapper;
    @Test
    public void testGetUserAuthUrl() {
        List<OrderArrivedDTO> arrivedDataEntityList = orderArrivedMapper.selectJoinList(
                OrderArrivedDTO.class,
                new MPJLambdaWrapper<OrderArrivedEntity>()
                        .selectAs(ArrivedDataEntity::getMortgageAgentName, OrderArrivedDTO::getMortgageAgentName)
                        .selectAs(ArrivedDataEntity::getMortgageAgentPhone, OrderArrivedDTO::getMortgageAgentPhone)
                        .selectAs(ArrivedDataEntity::getMortgageAgentIdNumber, OrderArrivedDTO::getMortgageAgentIdNumber)
                        .selectAs(OrderArrivedAddressEntity::getPostAddress, OrderArrivedDTO::getPostAddress)
                        .selectAs(OrderArrivedAddressEntity::getPostCode, OrderArrivedDTO::getPostCode)
                        .innerJoin(ArrivedDataEntity.class, on -> on
                                .eq(OrderArrivedEntity::getArrivedId, ArrivedDataEntity::getId)
                                .eq(OrderArrivedEntity::getDeleteFlag, 0))
                        .innerJoin(OrderArrivedAddressEntity.class, on -> on
                                .eq(OrderArrivedAddressEntity::getArrivedId, OrderArrivedEntity::getArrivedId))
                        .eq(OrderArrivedEntity::getOrderId, 1686)
                        .eq(OrderArrivedEntity::getDeleteFlag, 0)
        );
    }
    @Resource
    private FadadaAuthService fadadaAuthService;
    @Test
    public void testUploadFile() {
        String base = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 32; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        System.out.println(sb.toString());

    }
}
