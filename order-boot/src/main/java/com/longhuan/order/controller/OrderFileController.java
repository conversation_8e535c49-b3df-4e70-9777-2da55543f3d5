package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.ResourceMapByOrderFileDTO;
import com.longhuan.order.pojo.dto.UpdateOrderFileDTO;
import com.longhuan.order.pojo.vo.OrderFileVo;
import com.longhuan.order.service.OrderFileService;
import com.longhuan.user.pojo.dto.GenerateCodeDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/8/2
 */
@RestController
@RequestMapping("/orderFile")
@RequiredArgsConstructor
public class OrderFileController {
    private final OrderFileService orderFileService;

    /**
     * 查询所有文件根据orderId
     *
     * @param orderId 订单id
     * @return 订单文件列表
     */
    @PostMapping("/getFileByOrderId/{orderId}")
    public Result<List<OrderFileVo>> getFileByOrderId(@PathVariable("orderId") Integer orderId) {
        return Result.success(orderFileService.getFileByOrderId(orderId));
    }

    /**
     * 根据订单id和文件配置编码获取文件id
     * @param orderId
     * @param fileConfigCode
     * @return {@link Result< String>}
     */
    @PostMapping("/getResourceIdByOrderId")
    public Result<String> getResourceIdByOrderId(@RequestParam("orderId") Integer orderId,
                                                 @RequestParam("fileConfigCode") String fileConfigCode){
        return Result.success(orderFileService.getResourceIdByOrderId(orderId,fileConfigCode));
    }
    /**
     * 根据订单id和文件配置编码获取文件id
     * @return {@link Result< Map<String, List<String>>} map key 文件配置code value 文件uid
     */
    @PostMapping("/getResourceMapByOrderId")
    public Result<Map<String, List<String>>> getResourceMapByOrderId(@RequestBody ResourceMapByOrderFileDTO dto){
        return Result.success(orderFileService.getResourceMapByOrderId(dto.getOrderId(),dto.getFileConfigCodes()));
    }


    @ApiOperation(value = "获取报告的二维码")
    @PostMapping("/generateCodeReportFile")
    ResponseEntity<byte[]> generateCodeReportFile(@RequestBody GenerateCodeDTO generateCodeDTO) {
        return orderFileService.generateCodeReportFile(generateCodeDTO);
    }

}
