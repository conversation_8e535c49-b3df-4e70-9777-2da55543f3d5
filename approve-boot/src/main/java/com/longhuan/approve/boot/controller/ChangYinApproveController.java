package com.longhuan.approve.boot.controller;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.longhuan.approve.api.pojo.dto.FundRepaymentSyncDTO;
import com.longhuan.approve.api.pojo.dto.FundSyncDateDTO;
import com.longhuan.approve.api.pojo.dto.changyin.*;
import com.longhuan.approve.api.pojo.vo.changyin.ChangLPRResDTO;
import com.longhuan.approve.api.pojo.vo.changyin.ChangYinContractPreviewVO;
import com.longhuan.approve.api.pojo.vo.changyin.ChangYinLoanTrialResponseDTO;
import com.longhuan.approve.boot.fund.changyin.ChangYinService;
import com.longhuan.approve.boot.pojo.dto.changyin.ChangYinCreditQueryDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.ChangYinLoanQueryDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.request.ChangYinCustRepayAccountQueryReqDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.response.ChangYinCallbackResDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.response.ChangYinCreditQueryResDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.response.ChangYinCustRepayAccountQueryResDTO;
import com.longhuan.approve.boot.pojo.dto.changyin.response.ChangYinLoanQueryResDTO;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.utils.EnvUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 长银
 */
@Api(tags = "长银")
@Slf4j
@RestController
@RequestMapping("/api/v1/changyin")
@RequiredArgsConstructor
public class ChangYinApproveController {

    private final ChangYinService changYinService;
    private final EnvUtil envUtil;


    @ApiOperation(value = "授信回调")
    @PostMapping("/applyCallback")
    public ChangYinResBodyDTO<ChangYinCallbackResDTO> applyCallback(@RequestBody String requestJson) {
        log.info("ChangYinApproveController.applyCallback request: {}", requestJson);

        return changYinService.handleApplyCallback(requestJson);
    }

    @ApiOperation(value = "用信回调")
    @PostMapping("/loanCallback")
    public ChangYinResBodyDTO<ChangYinCallbackResDTO> preLoanCallback(@RequestBody String requestJson) {
        log.info("ChangYinApproveController.loanCallback request: {}", requestJson);
        return changYinService.handlePreLoanCallback(requestJson);
    }

    @ApiOperation(value = "放款回调")
    @PostMapping("/payCallback")
    public ChangYinResBodyDTO<ChangYinCallbackResDTO> payCallback(@RequestBody String requestJson) {
        log.info("ChangYinApproveController.payCallback request: {}", requestJson);
        return changYinService.handlePayCallback(requestJson);
    }

    @ApiOperation(value = "还款回调")
    @PostMapping("/repayCallback")
    public ChangYinResBodyDTO<ChangYinCallbackResDTO> repayCallback(@RequestBody String requestJson) {
        log.info("ChangYinApproveController.repayCallback request: {}", requestJson);
        return changYinService.handleRepayCallback(requestJson);
    }


    @ApiOperation(value = "签约申请")
    @PostMapping("/protocolApply")
    public ChangYinResBodyDTO<ChangYinSignResDTO> protocolApply(@RequestBody ChangYinSignInfoDTO changYinSignInfoDTO) {

        return changYinService.protocolApply(changYinSignInfoDTO);
    }

    @ApiOperation(value = "签约确认")
    @PostMapping("/protocolConfirm")
    public ChangYinResBodyDTO<ChangYinSignResDTO> protocolConfirm(@RequestBody ChangYinSignConfirmDTO changYinSignConfirmDTO) {

        return changYinService.protocolConfirm(changYinSignConfirmDTO);
    }

    @ApiOperation(value = "卡号变更")
    @PostMapping("/changeAccountApply")
    public ChangYinResBodyDTO<ChangYinAccountChangeResDTO> changeAccountApply(@RequestBody ChangYinUpdateAccountDTO changYinUpdateAccountDTO) {

        return changYinService.changeAccountApply(changYinUpdateAccountDTO);
    }


    @ApiOperation(value = "授信状态查询")
    @PostMapping("/creditQueryV2")
    public ChangYinResBodyDTO<ChangYinCreditQueryResDTO> creditQueryV2(@RequestBody ChangYinCreditQueryDTO changYinSignInfoDTO) {

        return changYinService.creditQueryV2(changYinSignInfoDTO);
    }


    @ApiOperation(value = "放款状态查询")
    @PostMapping("/loanQueryV2")
    public ChangYinResBodyDTO<ChangYinLoanQueryResDTO> loanQueryV2(@RequestBody ChangYinLoanQueryDTO changYinLoanQueryDTO) {

        return changYinService.loanQueryV2(changYinLoanQueryDTO);
    }

    @ApiOperation(value = "用信申请")
    @GetMapping("/preLoanApplyV2ByOrderId")
    public ChangYinResBodyDTO<ChangYinLoanApplicationRespDTO> preLoanApplyV2ByOrderId(@RequestParam("orderId") Integer orderId, @RequestParam("contractNumber") String contractNumber) {

        return changYinService.preLoanApplyV2ByOrderId(orderId, contractNumber);
    }

    @ApiOperation(value = "用信状态查询")
    @GetMapping("/preLoanQueryV2ByOrderId")
    public ChangYinResBodyDTO<ChangYinPreLoanQueryResDTO> preLoanQueryV2ByOrderId(@RequestParam("orderId") Integer orderId) {

        return changYinService.preLoanQueryV2ByOrderId(orderId);
    }

    @ApiOperation(value = "身份证更新")
    @GetMapping("/idCardUpdateApply")
    public ChangYinResBodyDTO<ChangYinIdCardUpdateApplyResDTO> idCardUpdateApply(@RequestParam("orderId") Integer orderId) {

        return changYinService.idCardUpdateApply(orderId);
    }

    @ApiOperation(value = "身份证更新查询")
    @GetMapping("/idCardUpdateQuery")
    public ChangYinResBodyDTO<ChangYinIdCardUpdateQueryResDTO> idCardUpdateQuery(@RequestParam("orderId") Integer orderId) {

        return changYinService.idCardUpdateQuery(orderId);
    }

    @ApiOperation(value = "贷款用途上传")
    @PostMapping("/loanPurposeUpload")
    public ChangYinResBodyDTO<ChangYinLoanUploadResDTO> loanPurposeUpload(@RequestParam("orderId") Integer orderId) {

        return changYinService.loanPurposeUpload(orderId);
    }

    @ApiOperation(value = "下载合同")
    @GetMapping("/downloadContract")
    public Result<List<ChangYinContractPreviewVO>> downloadContract(@RequestParam("orderId") Integer orderId) {
        List<ChangYinContractPreviewVO> changYinContractPreviewVOS = null;
        try {
            changYinContractPreviewVOS = changYinService.downloadFundContract(orderId);
        } catch (Exception e) {
            log.info("ChangYinApproveController.downloadContract error e:{}", e.getMessage(), e);
            return Result.failed("下载合同失败");
        }
        return Result.success(changYinContractPreviewVOS);
    }

    @ApiOperation(value = "长银查询LPR")
    @PostMapping("/queryLPR")
    public ChangYinResBodyDTO<ChangLPRResDTO> queryLPR(@RequestBody ChangYinLPRDTO changYinLPRDTO) {

        return changYinService.queryLPR(changYinLPRDTO);
    }

    /**
     * 批量同步资方还款计划更新文件
     */
    @PostMapping("/syncRepaymentUpdateBatch")
    public Result<Boolean> syncRepaymentUpdateBatch(@RequestBody FundRepaymentSyncDTO repaymentSyncDTO) {
        String startDateStr = repaymentSyncDTO.getStartDate();
        String endDateStr = repaymentSyncDTO.getEndDate();
        if (startDateStr != null && endDateStr != null) {
            LocalDate startDate = Convert.toLocalDateTime(startDateStr).toLocalDate();
            LocalDate endDate = Convert.toLocalDateTime(endDateStr).toLocalDate();

            if (startDate.isAfter(endDate)) {
                return Result.success();
            }
            while (startDate.isBefore(endDate)) {
                startDate = startDate.plusDays(1);
                changYinService.syncRepaymentUpdateBatch(startDate);
            }
        } else {
            changYinService.syncRepaymentUpdateBatch(null);
        }

        return Result.success(true);
    }

    /**
     * 批量同步资方放款明细文件
     */
    @PostMapping("/syncPaymentDetailFileBatch")
    public Result<Boolean> syncPaymentDetailFileBatch(@RequestBody FundSyncDateDTO syncDateDTO) {
        String startDateStr = syncDateDTO.getStartDate();
        String endDateStr = syncDateDTO.getEndDate();
        if (startDateStr != null && endDateStr != null) {
            LocalDate startDate = Convert.toLocalDateTime(startDateStr).toLocalDate();
            LocalDate endDate = Convert.toLocalDateTime(endDateStr).toLocalDate();

            if (startDate.isAfter(endDate)) {
                return Result.success();
            }
            while (startDate.isBefore(endDate)) {
                startDate = startDate.plusDays(1);
                changYinService.syncPaymentDetailFileBatch(startDate);
            }
        } else {
            changYinService.syncPaymentDetailFileBatch(null);
        }

        return Result.success(true);
    }

    @ApiOperation(value = "放款失败下载合同")
    @GetMapping("/downCardChangeInOutSupply")
    public Result<List<ChangYinContractPreviewVO>> downCardChangeInOutSupply(@RequestParam("orderId") Integer orderId) {
        List<ChangYinContractPreviewVO> changYinContractPreviewVOS = null;
        try {
            changYinContractPreviewVOS = changYinService.downCardChangeInOutSupply(orderId);
        } catch (Exception e) {
            log.info("ChangYinApproveController.downCardChangeInOutSupply error e:{}", e.getMessage(), e);
            return Result.failed("下载合同失败");
        }
        return Result.success(changYinContractPreviewVOS);
    }

//    @ApiOperation(value = "抵押还款试算")
//    @PostMapping("/repaymentTrial")
//    public ChangYinResBodyDTO<ChangYinRepaymentTrialResDTO> repaymentTrial(@RequestParam Integer orderId ) {
//
//
//        return changYinService.repaymentTrial(orderId);
//    }

    /**
     * 初始化还款计划表
     */
    @ApiOperation(value = "初始化还款计划表")
    @GetMapping("/initRepaymentPlan")
    public Result<Boolean> initRepaymentPlan() {
        changYinService.initRepaymentPlan();
        return Result.success(true);
    }

    /**
     * 保存还款计划表文件
     */
    @ApiOperation(value = "保存还款计划表文件")
    @GetMapping("/repayPlanFileBatch")
    public Result<Boolean> repayPlanFileBatch() {
        changYinService.repayPlanFileBatch();
        return Result.success(true);
    }


    /**
     * 贷后预代偿文件拉取
     */
    @ApiOperation(value = "贷后预代偿文件拉取")
    @GetMapping("/getPreIndemnityFile")
    public Result<Boolean> getPreIndemnityFile(@RequestParam(name = "jobTime", required = false) String jobTime) {
        changYinService.getPreIndemnityFile(jobTime);
        return Result.success(true);
    }


    /**
     * 贷后代偿文件拉取
     */
    @ApiOperation(value = "贷后代偿文件拉取")
    @GetMapping("/getIndemnityFile")
    public Result<Boolean> getIndemnityFile(@RequestParam(name = "jobTime", required = false) String jobTime) {
        changYinService.getIndemnityFile(jobTime);
        return Result.success(true);
    }


    /**
     * 长银线下还款账号
     */
    @ApiOperation(value = "长银线下还款账号")
    @PostMapping("/custRepayAccountQuery")
    public Result<List<ChangYinCustRepayAccountQueryResDTO.AcountList>> custRepayAccountQuery(@RequestBody ChangYinCustRepayAccountQueryReqDTO dto) {
        ChangYinResBodyDTO<ChangYinCustRepayAccountQueryResDTO> result = changYinService.custRepayAccountQuery(dto.getOrderId());
        if (!ChangYinResBodyDTO.isSuccess(result)) {
            return Result.failed(result.getHead().getRespMsg());
        }
        if (ObjUtil.isNotNull(result.getBody())) {
            if (StrUtil.equals("1", result.getBody().getStatus())) {

                if (!envUtil.isPrd()) {
                    result.getBody().getAccountList().removeIf(accountList -> !"BCMSAX".equals(accountList.getBankCode()));
                } else {
                    result.getBody().getAccountList().removeIf(accountList -> !"CMB".equals(accountList.getBankCode()));
                }
                return Result.success(result.getBody().getAccountList());
            } else {
                return Result.failed(result.getBody().getStatusDesc());
            }

        }

        return Result.failed("查询失败");
    }
    @ApiOperation(value = "长银还款计划查询")
    @GetMapping("/repaymentPlanSettle")
    public Result<Boolean> repaymentPlanSettle(@RequestParam("orderId") Integer orderId) {
        Boolean result = changYinService.repaymentPlanSettle(orderId);
        if (Boolean.TRUE.equals(result)) {
            return Result.success(true);
        } else {
            return Result.failed("操作失败");
        }
    }

    @ApiOperation(value = "根据订单贷款试算")
    @PostMapping("/loanTrialV2ByOrder")
    public ChangYinResBodyDTO<ChangYinLoanTrialResponseDTO> loanTrialV2ByOrder(@RequestBody ChangYinLoanTrialByOrderDTO changYinLoanTrialByOrderDTO) {
        return changYinService.loanTrialV2ByOrder(changYinLoanTrialByOrderDTO);
    }


}
