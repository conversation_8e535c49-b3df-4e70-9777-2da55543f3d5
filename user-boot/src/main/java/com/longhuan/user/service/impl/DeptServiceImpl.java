package com.longhuan.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.query.MPJLambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.common.core.enums.RoleEnum;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.user.api.DeptDetailVO;
import com.longhuan.user.converter.DeptConverter;
import com.longhuan.user.converter.DeptDisposalMappingConverter;
import com.longhuan.user.converter.UserInfoConverter;
import com.longhuan.user.mapper.*;
import com.longhuan.user.pojo.dto.*;
import com.longhuan.user.pojo.entity.*;
import com.longhuan.user.pojo.vo.*;
import com.longhuan.user.service.DeptService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【lh_dept(龙环部门表)】的数据库操作Service实现
 * @createDate 2024-06-27 13:08:44
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeptServiceImpl extends ServiceImpl<DeptMapper, DeptEntity>
        implements DeptService {

    private final DeptMapper deptMapper;
    private final UserDeptMappingMapper userDeptMappingMapper;
    private final DeptAreaMapper deptAreaMapper;
    private final UserServiceImpl userService;
    private final DeptSyncInfoMapper deptSyncInfoMapper;
    private final DeptConverter deptConverter;
    private final DeptDisposalMappingMapper deptDisposalMappingMapper;
    private final DeptDisposalMappingConverter deptDisposalMappingConverter;
    private final DeptJindieSyncInfoMapper deptJindieSyncInfoMapper;
    private final DisposalUpdateFieldRecordsMapper disposalUpdateFieldRecordsMapper;
    private final UserRoleMappingMapper userRoleMappingMapper;
    private final UserMapper userMapper;
    private final UserInfoConverter userInfoConverter;

    @Override
    public Integer getDeptByName(String deptName) {
        LambdaQueryWrapper<DeptEntity> queryWrapper = new LambdaQueryWrapper<DeptEntity>();
        queryWrapper.eq(DeptEntity::getName, deptName);
        queryWrapper.eq(DeptEntity::getDeleteFlag, 0);
        DeptEntity deptEntity = super.getOne(queryWrapper);
        if (deptEntity == null) {
            return null;
        }
        return deptEntity.getId();
    }

    /**
     * 按部门id获取部门名称
     *
     * @param deptId 部门 ID
     * @return {@link String }
     */
    @Override
    public String getDeptById(Integer deptId) {
        LambdaQueryWrapper<DeptEntity> queryWrapper = new LambdaQueryWrapper<DeptEntity>();
        queryWrapper.eq(DeptEntity::getId, deptId);
        queryWrapper.eq(DeptEntity::getDeleteFlag, 0);
        DeptEntity deptEntity = super.getOne(queryWrapper);
        if (deptEntity == null) {
            return null;
        }
        return deptEntity.getName();
    }

    /**
     * 按部门id获取子部门Id列表
     *
     * @param deptId 区域 ID
     * @return {@link List }<{@link Integer }>
     */
    @Override
    public List<Integer> getChildDeptById(Integer deptId) {
        LambdaQueryWrapper<DeptEntity> queryWrapper = new LambdaQueryWrapper<DeptEntity>();
        queryWrapper.eq(DeptEntity::getParentId, deptId);
        queryWrapper.eq(DeptEntity::getDeleteFlag, 0);
        List<Integer> list =
                new java.util.ArrayList<>(super.list(queryWrapper).stream().map(DeptEntity::getId).toList());
        list.add(deptId);
        return list;
    }

    @Override
    public List<Tree<Integer>> getDeptList() {
        LambdaQueryWrapper<DeptEntity> queryWrapper = new LambdaQueryWrapper<DeptEntity>();
        queryWrapper.eq(DeptEntity::getDeleteFlag, 0);
        List<DeptEntity> departmentList = super.list(queryWrapper);

        // 配置树节点
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("parentId");

        // 构建树形结构
        List<Tree<Integer>> treeList = TreeUtil.build(departmentList, 0, treeNodeConfig, (dept, tree) -> {
            tree.setId(dept.getId());
            tree.setName(dept.getName());
            tree.setParentId(dept.getParentId());
            tree.putExtra("syncId", dept.getSyncId());
            tree.putExtra("deptLayer", dept.getDeptLayer());
        });
        return treeList;
    }

    @Override
    public List<Tree<Integer>> getRegionList(RegionListDTO regionListDT) {
        LambdaQueryWrapper<DeptEntity> queryWrapper = new LambdaQueryWrapper<DeptEntity>();
        queryWrapper.eq(DeptEntity::getName, regionListDT.getRegionName());
        queryWrapper.eq(DeptEntity::getDeleteFlag, 0);
        DeptEntity deptEntity = deptMapper.selectOne(queryWrapper);

        List<DeptEntity> deptEntities =
                deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>().eq(DeptEntity::getParentId,
                                deptEntity.getId())
                        .eq(DeptEntity::getDeleteFlag, 0));

        List<Integer> list = deptEntities.stream().map(DeptEntity::getId).toList();

        List<DeptEntity> departmentList =
                deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>().in(DeptEntity::getParentId, list)
                        .eq(DeptEntity::getDeleteFlag, 0));

        //门店id
        List<Integer> storeIds = departmentList.stream().map(DeptEntity::getId).toList();
        List<DeptEntity> fenBuList =
                deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>().in(DeptEntity::getParentId, storeIds)
                        .eq(DeptEntity::getDeleteFlag, 0)
                        .like(DeptEntity::getRemark, "分部"));

        departmentList.addAll(deptEntities);
        fenBuList.addAll(departmentList);

        // 配置树节点
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("parentId");

        // 构建树形结构
        List<Tree<Integer>> treeList = TreeUtil.build(fenBuList, deptEntity.getId(), treeNodeConfig, (dept,
                                                                                                      tree) -> {
            tree.setId(dept.getId());
            if (ObjUtil.isNotNull(dept.getRemark()) && dept.getRemark().contains("分部")) {
                tree.setName(dept.getRemark() + "-" + dept.getName());
            } else {
                tree.setName(dept.getName());
            }
            tree.setParentId(dept.getParentId());
            tree.putExtra("syncId", dept.getSyncId());
            tree.putExtra("deptLayer", dept.getDeptLayer());
        });
        return treeList;
    }

    @Override
    public List<Tree<Integer>> getRegionLists() {
        List<Integer> deptIds = Arrays.asList(429, 65, 47, 48, 49, 46, 45, 56);

        List<DeptEntity> firstLevelDepts = deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>()
                .in(DeptEntity::getId, deptIds)
                .eq(DeptEntity::getDeleteFlag, 0));

        // 提取第一级部门ID
        List<Integer> firstLevelIds = firstLevelDepts.stream()
                .map(DeptEntity::getId)
                .toList();

        // 查询第二级数据（name 包含 "门店"）
        List<DeptEntity> secondLevelDepts = deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>()
                .in(DeptEntity::getParentId, firstLevelIds)
                .like(DeptEntity::getName, "店")
                .eq(DeptEntity::getDeleteFlag, 0));
        //提取第二级门店id
        List<Integer> storeIds = secondLevelDepts.stream()
                .map(DeptEntity::getId)
                .toList();
        //查询第三级数据（remark不为空的"）
        List<DeptEntity> thLevelDepts = deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>()
                .in(DeptEntity::getParentId, storeIds)
                .isNotNull(DeptEntity::getRemark)
                .ne(DeptEntity::getRemark, "")
                .eq(DeptEntity::getDeleteFlag, 0));
        // 将第三级获取到的数据根据parentId匹配第二级数据中的id，并将匹配到的第二级数据的name拼接到第三级数据的name的前边并用 ~ 连接起来
        Map<Integer, String> secondLevelNameMap = secondLevelDepts.stream()
                .collect(Collectors.toMap(DeptEntity::getId, DeptEntity::getName));
        Map<Integer, Integer> secondLevelParentMap = secondLevelDepts.stream()
                .collect(Collectors.toMap(DeptEntity::getId, DeptEntity::getParentId));
        // 处理第三级数据，拼接名称
        List<DeptEntity> processedThirdLevelDepts = thLevelDepts.stream()
                .map(dept -> {
                    String parentName = secondLevelNameMap.get(dept.getParentId());
                    if (parentName != null && dept.getRemark() != null) {
                        dept.setName(parentName + "~" + dept.getRemark() + "("+dept.getName()+")");
                    }
                    //替换原来的parentid改为第二级的parentId方便二级树状图成型
                    Integer parentId = secondLevelParentMap.get(dept.getParentId());
                    dept.setParentId(parentId);
                    return dept;

                })
                .collect(Collectors.toList());



        // 合并数据
        List<DeptEntity> departmentList = new ArrayList<>();
        departmentList.addAll(firstLevelDepts);
        departmentList.addAll(secondLevelDepts);
        departmentList.addAll(processedThirdLevelDepts);

        // 配置树节点
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("parentId");

        // 构建树形结构（根节点为 44）
        List<Tree<Integer>> treeList11 = TreeUtil.build(departmentList, 44, treeNodeConfig, (dept, tree) -> {
            tree.setId(dept.getId());
            tree.setName(dept.getName());
            tree.setParentId(dept.getParentId());
            tree.putExtra("syncId", dept.getSyncId());
            tree.putExtra("deptLayer", dept.getDeptLayer());
        });

        // 构建树形结构（根节点为 24）
        List<Tree<Integer>> treeList24 = TreeUtil.build(departmentList, 11, treeNodeConfig, (dept, tree) -> {
            tree.setId(dept.getId());
            tree.setName(dept.getName());
            tree.setParentId(dept.getParentId());
            tree.putExtra("syncId", dept.getSyncId());
            tree.putExtra("deptLayer", dept.getDeptLayer());
        });
        // 构建树形结构（根节点为 24）
        List<Tree<Integer>> treeList1 = TreeUtil.build(departmentList, 24, treeNodeConfig, (dept, tree) -> {
            tree.setId(dept.getId());
            tree.setName(dept.getName());
            tree.setParentId(dept.getParentId());
            tree.putExtra("syncId", dept.getSyncId());
            tree.putExtra("deptLayer", dept.getDeptLayer());
        });

        // 合并结果
        List<Tree<Integer>> finalTreeList = new ArrayList<>();
        if (ObjUtil.isNotNull(treeList11)) {
            finalTreeList.addAll(treeList11);
        }
        if (ObjUtil.isNotNull(treeList24)) {
            finalTreeList.addAll(treeList24);
        }
        if (ObjUtil.isNotNull(treeList1)) {
            finalTreeList.addAll(treeList1);
        }

        return finalTreeList;
    }


    @Override
    public String getAreaByUserId(Integer userId) {
        List<UserDeptMappingEntity> userDeptMappingEntities =
                userDeptMappingMapper.selectList(new LambdaQueryWrapper<UserDeptMappingEntity>()
                        .eq(UserDeptMappingEntity::getUserId, userId)
                        .eq(UserDeptMappingEntity::getDeleteFlag, 0));
        if (CollUtil.isEmpty(userDeptMappingEntities)) {
            return null;
        }
        UserDeptMappingEntity userDeptMappingEntity = userDeptMappingEntities.get(0);
        List<DeptEntity> deptAreaEntities =
                deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>().eq(DeptEntity::getId,
                        userDeptMappingEntity.getDeptId()));
        if (CollUtil.isEmpty(deptAreaEntities)) {
            return null;
        }
        DeptEntity deptEntity = deptAreaEntities.get(0);
        return deptEntity.getName();
    }

    @Override
    public List<Integer> getChildDeptByIds(DeptIdDTO deptIds) {
        return List.of();
    }

    /**
     * 按部门名称模糊查询部门Id列表
     *
     * @param deptName 部门名称
     * @return {@link List }<{@link Integer }>
     */
    @Override
    public List<Integer> getDeptIdByLikeName(String deptName) {
        LambdaQueryWrapper<DeptEntity> queryWrapper = new LambdaQueryWrapper<DeptEntity>();
        queryWrapper.like(DeptEntity::getName, deptName);
        queryWrapper.eq(DeptEntity::getDeleteFlag, 0);
        List<DeptEntity> deptEntity = deptMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(deptEntity)) {
            return List.of();
        }
        return deptEntity.stream().map(DeptEntity::getId).toList();
    }

    /**
     * 获取第一层级信息 (大区、公司、电销部)
     *
     * @param deptId
     * @return
     */
    @Override
    public RegionInfoVO getRegionInfoByDeptId(Integer deptId) {


        return getRegionInfo(deptId);
    }

    @Override
    public StoreInfoVO getStoreInfoByDeptId(Integer deptId) {

        return getStoreInfo(deptId);
    }

    @Override
    public List<DeptDetailVO> getDeptDetailVO(String deptName) {
        MPJLambdaWrapper<DeptEntity> queryWrapper = new MPJLambdaWrapper<DeptEntity>()
                .selectAs(DeptEntity::getId, DeptDetailVO::getId)
                .selectAs(DeptEntity::getName, DeptDetailVO::getName)
                .selectAs(DeptEntity::getParentId, DeptDetailVO::getParentId)
                .selectAs(DeptEntity::getSyncId, DeptDetailVO::getSyncId)
                .selectAs(DeptEntity::getDeptLayer, DeptDetailVO::getDeptLayer)
                .selectAs(DeptEntity::getStatus, DeptDetailVO::getStatus)
                .selectAs(DeptEntity::getOwnerid, DeptDetailVO::getOwnerid)
                .selectAs(DeptEntity::getRemark, DeptDetailVO::getRemark)
                .like(DeptEntity::getRemark, deptName)
                .eq(DeptEntity::getDeleteFlag, 0)
                .eq(DeptEntity::getDeptType, 1);
        List<DeptDetailVO> deptDetailVOS = deptMapper.selectJoinList(DeptDetailVO.class, queryWrapper);
        if (!CollectionUtils.isEmpty(deptDetailVOS)) {
            return deptDetailVOS;
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<RegionInfoVO> regionList() {
        MPJLambdaWrapper<DeptEntity> queryWrapper = new MPJLambdaWrapper<DeptEntity>()
                .selectAs(DeptEntity::getId, RegionInfoVO::getRegionId)
                .selectAs(DeptEntity::getName, RegionInfoVO::getRegionName)
                .like(DeptEntity::getName, "大区")
                .eq(DeptEntity::getDeleteFlag, 0)
                .eq(DeptEntity::getDeptType, 1);
        return deptMapper.selectJoinList(RegionInfoVO.class, queryWrapper);
    }


    @Override
    public List<Integer> getDeptUsers(List<Integer> deptIds) {
        List<Integer> deptIdsByUserId = new ArrayList<>(deptIds);

        for (Integer deptId : deptIds) {
            deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>()
                            .eq(DeptEntity::getParentId, deptId)
                            .eq(DeptEntity::getDeleteFlag, 0))
                    .stream()
                    .map(DeptEntity::getId)
                    .forEach(deptIdsByUserId::add);
        }

        List<Integer> allChildDeptIds = new ArrayList<>(deptIdsByUserId);
        for (Integer deptId : deptIdsByUserId) {
            userService.getAllChildDepartmentIds(deptId, allChildDeptIds);
        }

        return userService.getDepartmentUserIds(allChildDeptIds);
    }

    @Override
    public List<RegionInfoVO> getRegionInfoVoList() {
        List<DeptEntity> deptEntities = deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>().and(wp -> wp.like(DeptEntity::getName, "大区").or().eq(DeptEntity::getName, "电销部"))
                .ne(DeptEntity::getName, "业务大区")
                .eq(DeptEntity::getDeptLayer, 3)
                .eq(DeptEntity::getDeleteFlag, 0));
        List<RegionInfoVO> regionInfoVOS = deptEntities.stream().map(e -> new RegionInfoVO().setRegionId(e.getId()).setRegionName(e.getName())).toList();
        return regionInfoVOS;
    }


    /**
     * 获取业务第二层级信息 (门店)
     *
     * @param deptId 部门 ID
     * @return {@link StoreInfoVO }
     */
    private StoreInfoVO getStoreInfo(Integer deptId) {
        DeptEntity deptEntity = deptMapper.selectById(deptId);
        if (deptEntity != null) {
            if (deptEntity.getName().endsWith("门店")
                    || deptEntity.getName().endsWith("一店")
                    || deptEntity.getName().endsWith("二店")
                    || deptEntity.getName().endsWith("三店")) {
                return new StoreInfoVO().setStoreId(deptEntity.getId())
                        .setStoreName(deptEntity.getName());
            } else {
                if (deptEntity.getParentId() == 0) {
                    return new StoreInfoVO();
                } else {
                    return getStoreInfo(deptEntity.getParentId());
                }
            }
        } else {
            return new StoreInfoVO();
        }
    }

    /**
     * 获取业务第一层级信息 (大区、公司、部)
     *
     * @param deptId 部门 ID
     * @return {@link RegionInfoVO }
     */
    private RegionInfoVO getRegionInfo(Integer deptId) {
        DeptEntity deptEntity = deptMapper.selectById(deptId);
        if (deptEntity != null) {
            if (deptEntity.getName().endsWith("大区") || deptEntity.getName().endsWith("公司") || deptEntity.getName().endsWith("电销部")) {
                return new RegionInfoVO().setRegionId(deptEntity.getId()).setRegionName(deptEntity.getName());
            } else {
                if (deptEntity.getParentId() == 0) {
                    return new RegionInfoVO();
                } else {
                    return getRegionInfo(deptEntity.getParentId());
                }
            }
        } else {
            return new RegionInfoVO();
        }
    }

    @Override
    public List<DeptInfoVO> getTheBranchNameBasedOnTheTeamId(List<Integer> teamIds) {
        if (CollUtil.isEmpty(teamIds)) {
            return List.of();
        }
        LambdaQueryWrapper<DeptEntity> queryWrapper = new LambdaQueryWrapper<DeptEntity>();
        queryWrapper.in(DeptEntity::getId, teamIds);
        queryWrapper.eq(DeptEntity::getDeleteFlag, 0);
        List<DeptEntity> deptEntities = deptMapper.selectList(queryWrapper);
        return deptEntities.stream().map(e -> new DeptInfoVO().setId(e.getId()).setName(e.getRemark()).setTeamName(e.getName())).toList();
    }

    @Override
    public List<Tree<Integer>> getDeptListByDeptType(DeptListDTO deptListDTO) {
        LambdaQueryWrapper<DeptEntity> queryWrapper = new LambdaQueryWrapper<DeptEntity>();
        queryWrapper.eq(DeptEntity::getDeptType, deptListDTO.getDeptType())
                .eq(ObjUtil.isNotNull(deptListDTO.getStatus()), DeptEntity::getStatus, deptListDTO.getStatus())
                .like(StrUtil.isNotBlank(deptListDTO.getName()), DeptEntity::getName, deptListDTO.getName())
                .eq(ObjUtil.isNotNull(deptListDTO.getParentId()), DeptEntity::getParentId, deptListDTO.getParentId())
                .eq(DeptEntity::getDeleteFlag, 0);
        if (StrUtil.isNotBlank(deptListDTO.getOwnerName())) {
            List<Integer> userIds = userService.getUserIdByLikeNameList(deptListDTO.getOwnerName());
            queryWrapper.in(DeptEntity::getOwnerid, userIds);
        }
        List<DeptEntity> departmentList = super.list(queryWrapper);
        List<Integer> ownerIds = departmentList.stream().map(DeptEntity::getOwnerid).filter(Objects::nonNull).toList();
        List<UserInfoVO> ownerInfos = userService.searchUserNameBatch(ownerIds);
        Map<Integer, UserInfoVO> ownerInfoMap = ownerInfos.stream().collect(Collectors.toMap(UserInfoVO::getUserId, Function.identity()));
        List<DeptEntity> sortedDepartmentList = departmentList.stream()
                .sorted(Comparator.comparingInt((DeptEntity de) -> de.getParentId() == null ? Integer.MAX_VALUE : de.getParentId())
                        .thenComparingInt((DeptEntity de) -> de.getSort() == null ? Integer.MAX_VALUE : de.getSort()))
                .toList();
        // 配置树节点
        if (CollUtil.isEmpty(departmentList)) {
            return List.of();
        }
        Integer rootId = sortedDepartmentList.stream().map(DeptEntity::getParentId).filter(Objects::nonNull).min(Integer::compareTo).orElse(0);
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("parentId");

        // 构建树形结构
        List<Tree<Integer>> treeList = TreeUtil.build(sortedDepartmentList, rootId, treeNodeConfig, (dept, tree) -> {
            tree.setId(dept.getId());
            tree.setName(dept.getName());
            tree.setParentId(dept.getParentId());
            tree.putExtra("syncId", dept.getSyncId());
            tree.putExtra("deptLayer", dept.getDeptLayer());
            tree.putExtra("status", dept.getStatus());
            tree.putExtra("ownerId", dept.getOwnerid());
            tree.putExtra("ownerName", ownerInfoMap.get(dept.getOwnerid()) == null ? "" : ownerInfoMap.get(dept.getOwnerid()).getName());
            tree.putExtra("ownerPhone", ownerInfoMap.get(dept.getOwnerid()) == null ? "" : ownerInfoMap.get(dept.getOwnerid()).getMobile());
            tree.putExtra("sort", dept.getSort());
            tree.putExtra("remark", dept.getRemark());
        });
        return treeList;
    }

    @Override
    public Boolean saveOrUpdateDept(SaveOrUpdateDeptDTO saveOrUpdateDeptDTO) {
        DeptEntity entity = new DeptEntity();
        if (ObjUtil.isNotNull(saveOrUpdateDeptDTO.getId())) {
            entity = getById(saveOrUpdateDeptDTO.getId());
            if (ObjUtil.isNull(entity)) {
                throw new BusinessException("部门不存在");
            }
            if (ObjUtil.isNotNull(saveOrUpdateDeptDTO.getParentId())) {
                DeptEntity parentEntity = getOne(new LambdaQueryWrapper<DeptEntity>()
                        .eq(DeptEntity::getId, saveOrUpdateDeptDTO.getParentId())
                        .eq(DeptEntity::getDeleteFlag, 0)
                        .eq(DeptEntity::getDeptType, saveOrUpdateDeptDTO.getDeptType()));
                if (ObjUtil.isNull(parentEntity)) {
                    throw new BusinessException("上级部门不存在");
                }
                entity.setDeptLayer(parentEntity.getDeptLayer() + 1);
            }
            if (ObjUtil.isNotNull(saveOrUpdateDeptDTO.getStatus()) && ObjUtil.notEqual(saveOrUpdateDeptDTO.getStatus(), entity.getStatus())) {
                entity.setStatus(saveOrUpdateDeptDTO.getStatus());
                Set<Integer> deptIds = new HashSet<>(Set.of(entity.getId()));
                if (ObjUtil.equals(saveOrUpdateDeptDTO.getStatus(), 1)) {
                    List<Integer> parentIds = List.of(entity.getId());
                    while (CollUtil.isNotEmpty(parentIds)) {
                        List<DeptEntity> deptEntities = deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>()
                                .in(DeptEntity::getParentId, parentIds)
                                .eq(DeptEntity::getDeleteFlag, 0));
                        if (CollUtil.isEmpty(deptEntities)) {
                            break;
                        }
                        parentIds = deptEntities.stream().map(DeptEntity::getId).toList();
                        deptIds.addAll(parentIds);
                    }
                } else {
                    Integer parentId = entity.getParentId();
                    deptIds.add(parentId);
                    while (ObjUtil.notEqual(parentId, 0)) {
                        DeptEntity deptEntity = deptMapper.selectById(parentId);
                        if (ObjUtil.isNull(deptEntity)) {
                            break;
                        }
                        deptIds.add(deptEntity.getId());
                        parentId = deptEntity.getParentId();
                    }

                }
                deptMapper.update(new LambdaUpdateWrapper<DeptEntity>()
                        .set(DeptEntity::getStatus, saveOrUpdateDeptDTO.getStatus())
                        .in(DeptEntity::getId, deptIds));
            }
            entity.setName(ObjUtil.defaultIfBlank(saveOrUpdateDeptDTO.getName(), entity.getName()))
                    .setParentId(ObjUtil.defaultIfNull(saveOrUpdateDeptDTO.getParentId(), entity.getParentId()))
                    .setRemark(ObjUtil.defaultIfBlank(saveOrUpdateDeptDTO.getRemark(), entity.getRemark()))
                    .setSort(ObjUtil.defaultIfNull(saveOrUpdateDeptDTO.getSort(), entity.getSort()))
                    .setOwnerid(ObjUtil.defaultIfNull(saveOrUpdateDeptDTO.getOwnerid(), entity.getOwnerid()));
        } else {
            if (ObjUtil.isNotNull(saveOrUpdateDeptDTO.getParentId()) && ObjUtil.notEqual(saveOrUpdateDeptDTO.getParentId(), 0)) {
                DeptEntity parentEntity = getOne(new LambdaQueryWrapper<DeptEntity>()
                        .eq(DeptEntity::getId, saveOrUpdateDeptDTO.getParentId())
                        .eq(DeptEntity::getDeleteFlag, 0)
                        .eq(DeptEntity::getDeptType, saveOrUpdateDeptDTO.getDeptType()));
                if (ObjUtil.isNull(parentEntity)) {
                    throw new BusinessException("上级部门不存在");
                }
                entity.setDeptLayer(parentEntity.getDeptLayer() + 1);
            } else {
                entity.setDeptLayer(1);
            }
            entity.setName(saveOrUpdateDeptDTO.getName())
                    .setParentId(ObjUtil.defaultIfNull(saveOrUpdateDeptDTO.getParentId(), 0))
                    .setRemark(saveOrUpdateDeptDTO.getRemark())
                    .setDeptType(ObjUtil.defaultIfNull(saveOrUpdateDeptDTO.getDeptType(), 0))
                    .setSort(ObjUtil.defaultIfNull(saveOrUpdateDeptDTO.getSort(), 0))
                    .setOwnerid(ObjUtil.defaultIfNull(saveOrUpdateDeptDTO.getOwnerid(), 0))
                    .setStatus(ObjUtil.defaultIfNull(saveOrUpdateDeptDTO.getStatus(), 0));
        }
        return saveOrUpdate(entity);
    }

    @Override
    public Page<DeptPageVO> getDeptPage(DeptListDTO deptListDTO) {
        MPJLambdaWrapper<DeptEntity> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.eq(DeptEntity::getDeptType, deptListDTO.getDeptType())
                .selectAll(DeptEntity.class)
                .eq(ObjUtil.isNotNull(deptListDTO.getStatus()), DeptEntity::getStatus, deptListDTO.getStatus())
                .like(StrUtil.isNotBlank(deptListDTO.getName()), DeptEntity::getName, deptListDTO.getName())
                .eq(ObjUtil.isNotNull(deptListDTO.getParentId()), DeptEntity::getParentId, deptListDTO.getParentId())
                .eq(DeptEntity::getDeleteFlag, 0)
                .orderByDesc(DeptEntity::getCreateTime);
        if (StrUtil.isNotBlank(deptListDTO.getOwnerName())) {
            List<Integer> userIds = userService.getUserIdByLikeNameList(deptListDTO.getOwnerName());
            queryWrapper.in(DeptEntity::getOwnerid, userIds);
        }
        Page<DeptPageVO> deptPageVOPage = deptMapper.selectJoinPage(new Page<>(deptListDTO.getPageNum(), deptListDTO.getPageSize()), DeptPageVO.class, queryWrapper);
        List<DeptPageVO> departmentList = deptPageVOPage.getRecords();
        List<Integer> ownerIds = departmentList.stream().map(DeptPageVO::getOwnerid).filter(Objects::nonNull).toList();
        List<UserInfoVO> ownerInfos = userService.searchUserNameBatch(ownerIds);
        Map<Integer, UserInfoVO> ownerInfoMap = ownerInfos.stream().collect(Collectors.toMap(UserInfoVO::getUserId, Function.identity()));
        List<Integer> parentIds = deptPageVOPage.getRecords().stream().map(DeptPageVO::getParentId).filter(Objects::nonNull).toList();
        Map<Integer, DeptEntity> parentDeptMap = new HashMap<>();
        if (CollUtil.isNotEmpty(parentIds)) {
            List<DeptEntity> parentDepts = deptMapper.selectBatchIds(parentIds);
            parentDeptMap = parentDepts.stream().collect(Collectors.toMap(DeptEntity::getId, Function.identity()));
        }
        Map<Integer, DeptEntity> finalParentDeptMap = parentDeptMap;
        departmentList.forEach(department -> {
            department.setOwnerName(ownerInfoMap.get(department.getOwnerid()) == null ? "" : ownerInfoMap.get(department.getOwnerid()).getName());
            department.setOwnerPhone(ownerInfoMap.get(department.getOwnerid()) == null ? "" : ownerInfoMap.get(department.getOwnerid()).getMobile());
            department.setParentName(finalParentDeptMap.get(department.getParentId()) == null ? "" : finalParentDeptMap.get(department.getParentId()).getName());
        });
        return deptPageVOPage;
    }

    @Override
    public DeptDetailVO getDeptDetail(Integer deptId) {
        DeptDetailVO deptDetailVO = deptMapper.selectJoinOne(DeptDetailVO.class, new MPJLambdaWrapper<>(DeptEntity.class)
                .selectAll(DeptEntity.class)
                .eq(DeptEntity::getId, deptId));
        if (ObjUtil.isNotNull(deptDetailVO.getOwnerid())) {
            UserDetailInfoVO userDetailInfoVO = userService.searchDetailByUserId(deptDetailVO.getOwnerid());
            if (ObjUtil.isNotNull(userDetailInfoVO)) {
                deptDetailVO.setOwnerName(userDetailInfoVO.getName())
                        .setOwnerPhone(userDetailInfoVO.getMobile());
            }
        }
        return deptDetailVO;
    }


    @Override
    public Page<DisposalCompanyVO> getThreePartyDeptList(ThreePartyDeptDTO threePartyDeptDTO) {

        MPJLambdaWrapper<DeptEntity> lambdaWrapper = new MPJLambdaWrapper<>();
        lambdaWrapper.selectAs(DeptEntity::getId, DisposalCompanyVO::getDeptId)
                .selectAs(DeptEntity::getName, DisposalCompanyVO::getDeptName)
                .selectAs(DeptDisposalMappingEntity::getCompanyAddress, DisposalCompanyVO::getCompanyAddress)
                .selectAs(DeptDisposalMappingEntity::getContractName, DisposalCompanyVO::getContractName)
                .selectAs(DeptDisposalMappingEntity::getContractPhone, DisposalCompanyVO::getContractPhone)
                .selectAs(DeptDisposalMappingEntity::getDisposalRate, DisposalCompanyVO::getDisposalRate)
                .selectAs(DeptDisposalMappingEntity::getBusinessScope, DisposalCompanyVO::getBusinessScope)
                .selectAs(DeptDisposalMappingEntity::getBusinessScopeId, DisposalCompanyVO::getBusinessScopeId)
                .selectAs(DeptDisposalMappingEntity::getRegionId, DisposalCompanyVO::getRegionId)
                .selectAs(DeptEntity::getStatus, DisposalCompanyVO::getStatus)
                .leftJoin(DeptDisposalMappingEntity.class, on -> on.eq(DeptEntity::getId, DeptDisposalMappingEntity::getDeptId))
                .eq(ObjUtil.isNotNull(threePartyDeptDTO.getDeptType()), DeptEntity::getDeptType, threePartyDeptDTO.getDeptType())
                .eq(ObjUtil.isNotNull(threePartyDeptDTO.getDeptId()), DeptEntity::getId, threePartyDeptDTO.getDeptId())
                .like(StrUtil.isNotBlank(threePartyDeptDTO.getDeptName()), DeptEntity::getName, threePartyDeptDTO.getDeptName())
                .eq(DeptEntity::getDeleteFlag, 0)
                .eq(DeptEntity::getDeptType, 4)
                .orderByDesc(DeptEntity::getId);

        Page<DisposalCompanyVO> regionInfoVOS = deptMapper.selectJoinPage(Page.of(threePartyDeptDTO.getPageNum(), threePartyDeptDTO.getPageSize()), DisposalCompanyVO.class, lambdaWrapper);
        regionInfoVOS.getRecords().forEach(regionInfoVO -> {
            if (ObjUtil.isNotNull(regionInfoVO.getRegionId())) {
                DeptEntity deptEntity = deptMapper.selectById(regionInfoVO.getRegionId());
                regionInfoVO.setRegionName(deptEntity.getName());
            }
            if (StrUtil.isNotEmpty(regionInfoVO.getBusinessScopeId())) {
                regionInfoVO.setProvinceId(Arrays.stream(regionInfoVO.getBusinessScopeId().split(",")).map(Integer::parseInt).toList());

            }
        });

        return regionInfoVOS;
    }

    @Override
    public RegionInfoVO getDeptInfoByDeptId(ThreePartyDeptDTO threePartyDeptDTO) {
        RegionInfoVO regionInfoVO = deptMapper.selectJoinOne(RegionInfoVO.class, new MPJLambdaWrapper<>(DeptEntity.class)
                .selectAs(DeptEntity::getId, RegionInfoVO::getRegionId)
                .selectAs(DeptEntity::getDeleteFlag, RegionInfoVO::getDeleteFlag)
                .selectAs(DeptEntity::getName, RegionInfoVO::getRegionName)
                .eq(ObjUtil.isNotNull(threePartyDeptDTO.getDeptId()), DeptEntity::getId, threePartyDeptDTO.getDeptId())
                .eq(ObjUtil.isNotNull(threePartyDeptDTO.getDeptName()), DeptEntity::getName, threePartyDeptDTO.getDeptName())
                .orderByDesc(DeptEntity::getCreateTime)
                .last("limit 1"));
        return regionInfoVO;
    }

    @Override
    public List<Integer> getStoreManager(Integer storeId) {
        DeptEntity deptEntity = deptMapper.selectById(storeId);
        if (ObjUtil.isEmpty(deptEntity)) {
            throw new BusinessException("部门不存在");
        }
        List<DeptEntity> allSubDepts = getAllSubDepts(storeId);
        if (CollUtil.isNotEmpty(allSubDepts)) {
            allSubDepts.add(deptEntity);
            return allSubDepts.stream().map(DeptEntity::getId).toList();
        }
        return List.of(storeId);
    }

    public List<DeptEntity> getAllSubDepts(int parentId) {
        // 获取所有部门数据
        List<DeptEntity> allDeptEntities = deptMapper.selectList(
                new LambdaQueryWrapper<DeptEntity>()
                        .eq(DeptEntity::getDeleteFlag, 0)
                        .eq(DeptEntity::getDeptType, 1)
        );
        // 构建部门树
        Map<Integer, List<DeptEntity>> subDeptMap = allDeptEntities.stream()
                .filter(dept -> dept.getParentId() != null)
                .collect(Collectors.groupingBy(DeptEntity::getParentId));
        // 获取所有子部门
        return getAllSubDepts(parentId, subDeptMap);
    }

    private List<DeptEntity> getAllSubDepts(int parentId, Map<Integer, List<DeptEntity>> subDeptMap) {
        List<DeptEntity> result = new ArrayList<>();
        List<DeptEntity> subDepts = subDeptMap.getOrDefault(parentId, Collections.emptyList());
        for (DeptEntity subDept : subDepts) {
            result.add(subDept);
            result.addAll(getAllSubDepts(subDept.getId(), subDeptMap));
        }
        return result;
    }

    @Override
    public List<DeptSyncInfoVO> getDigitalStoreIdByDeptId(DigitalDeptsDTO digitalDeptsDTO) {

        MPJLambdaWrapper<DeptSyncInfoEntity> qureyWrapper = new MPJLambdaWrapper<DeptSyncInfoEntity>();
        qureyWrapper
                .selectAs(DeptSyncInfoEntity::getId, DeptSyncInfoVO::getId)
                .selectAs(DeptSyncInfoEntity::getDeptName, DeptSyncInfoVO::getDeptName)
                .selectAs(DeptSyncInfoEntity::getParentId, DeptSyncInfoVO::getParentId)
                .selectAs(DeptSyncInfoEntity::getLhDeptId, DeptSyncInfoVO::getLhDeptId)
                .selectAs(DeptSyncInfoEntity::getDeptId, DeptSyncInfoVO::getDeptId)
                .selectAs(DeptSyncInfoEntity::getDeptLayer, DeptSyncInfoVO::getDeptLayer)
                .selectAs(DeptSyncInfoEntity::getOrigin, DeptSyncInfoVO::getOrigin)
                .in(DeptSyncInfoEntity::getDeptId, digitalDeptsDTO.getDigitalDeptIds())
                .eq(DeptSyncInfoEntity::getOrigin, 2)
                .eq(DeptSyncInfoEntity::getDeleteFlag, 0);

        List<DeptSyncInfoVO> deptSyncInfoVOS = deptSyncInfoMapper.selectJoinList(DeptSyncInfoVO.class, qureyWrapper);

        return deptSyncInfoVOS;
    }

    @Override
    public DeptDetailVO getDeptIdByDeptName(String deptName) {
        MPJLambdaWrapper<DeptEntity> queryWrapper = new MPJLambdaWrapper<DeptEntity>()
                .selectAs(DeptEntity::getId, DeptDetailVO::getId)
                .selectAs(DeptEntity::getName, DeptDetailVO::getName)
                .selectAs(DeptEntity::getParentId, DeptDetailVO::getParentId)
                .selectAs(DeptEntity::getSyncId, DeptDetailVO::getSyncId)
                .selectAs(DeptEntity::getDeptLayer, DeptDetailVO::getDeptLayer)
                .selectAs(DeptEntity::getDeleteFlag, DeptDetailVO::getStatus)
                .selectAs(DeptEntity::getOwnerid, DeptDetailVO::getOwnerid)
                .selectAs(DeptEntity::getRemark, DeptDetailVO::getRemark)
                .eq(DeptEntity::getName, deptName)
                .eq(DeptEntity::getDeleteFlag, 0)
                .eq(DeptEntity::getDeptType, 1);
        List<DeptDetailVO> deptDetailVOS = deptMapper.selectJoinList(DeptDetailVO.class, queryWrapper);
        if (!CollectionUtils.isEmpty(deptDetailVOS)) {
            return deptDetailVOS.get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<DeptEntityVO> getChildNameDeptById(List<Integer> deptIds) {
        List<DeptEntityVO> deptEntityVOS = deptMapper.selectJoinList(DeptEntityVO.class,
                new MPJLambdaWrapper<DeptEntity>()
                        .selectAs(DeptEntity::getName, DeptEntityVO::getName)
                        .selectAs(DeptEntity::getParentId, DeptEntityVO::getParentId)
                        .selectAs(DeptEntity::getSyncId, DeptEntityVO::getSyncId)
                        .selectAs(DeptEntity::getDeptLayer, DeptEntityVO::getDeptLayer)
                        .selectAs(DeptEntity::getDeleteFlag, DeptEntityVO::getStatus)
                        .selectAs(DeptEntity::getOwnerid, DeptEntityVO::getOwnerid)
                        .selectAs(DeptEntity::getRemark, DeptEntityVO::getRemark)
                        .in(DeptEntity::getId, deptIds)
                        .eq(DeptEntity::getDeleteFlag, 0)
                        .eq(DeptEntity::getDeptType, 1));
        return deptEntityVOS;

    }

    @Override
    public List<DeptDetailVO> getDeptIdByDeptNames(List<String> deptNames) {
        MPJLambdaWrapper<DeptEntity> queryWrapper = new MPJLambdaWrapper<DeptEntity>()
                .selectAs(DeptEntity::getId, DeptDetailVO::getId)
                .selectAs(DeptEntity::getName, DeptDetailVO::getName)
                .selectAs(DeptEntity::getParentId, DeptDetailVO::getParentId)
                .selectAs(DeptEntity::getSyncId, DeptDetailVO::getSyncId)
                .selectAs(DeptEntity::getDeptLayer, DeptDetailVO::getDeptLayer)
                .selectAs(DeptEntity::getDeleteFlag, DeptDetailVO::getStatus)
                .selectAs(DeptEntity::getOwnerid, DeptDetailVO::getOwnerid)
                .selectAs(DeptEntity::getRemark, DeptDetailVO::getRemark)
                .in(ObjUtil.isNotEmpty(deptNames), DeptEntity::getName, deptNames)
                .eq(DeptEntity::getDeleteFlag, 0)
                .eq(DeptEntity::getDeptType, 1);
        List<DeptDetailVO> deptDetailVOS = deptMapper.selectJoinList(DeptDetailVO.class, queryWrapper);
        return deptDetailVOS;

    }

    @Override
    @Transactional
    public Boolean editThreePartyDept(DisposalCompanyDTO disposalCompanyDTO, LoginUser loginUser) {
        DeptEntity dept = deptConverter.dtoToEntity(disposalCompanyDTO);
        DeptDisposalMappingEntity deptDisposalMappingEntity = deptDisposalMappingConverter.dtoToEntity(disposalCompanyDTO);
        if (CollUtil.isNotEmpty(disposalCompanyDTO.getProvinceId())) {
            deptDisposalMappingEntity.setBusinessScopeId(disposalCompanyDTO.getProvinceId().stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(",")));
        }
        if (ObjUtil.isNull(disposalCompanyDTO.getDeptId())) {
            dept.setDeptType(4).setParentId(575).setDeptLayer(2).setCreateBy(loginUser.getUserId());
            dept.setUpdateBy(loginUser.getUserId());
            DeptEntity displayDept = deptMapper.selectOne(new LambdaQueryWrapper<DeptEntity>()
                    .eq(DeptEntity::getDeptType, 4)
                    .orderByDesc(DeptEntity::getId)
                    .last("limit 1"));
            if (ObjUtil.isNotEmpty(displayDept)) {
                dept.setId(displayDept.getId() + 1);
            }
            deptMapper.addDisposalCompany(dept);

            deptDisposalMappingEntity.setDeptId(dept.getId());
            deptDisposalMappingMapper.insert(deptDisposalMappingEntity);
        } else {
            dept.setUpdateTime(LocalDateTime.now());
            deptDisposalMappingEntity.setUpdateTime(LocalDateTime.now());
            deptMapper.update(dept, new LambdaUpdateWrapper<DeptEntity>()
                    .eq(DeptEntity::getId, disposalCompanyDTO.getDeptId())
                    .eq(DeptEntity::getDeleteFlag, 0));
            deptDisposalMappingMapper.update(deptDisposalMappingEntity, new LambdaUpdateWrapper<DeptDisposalMappingEntity>()
                    .eq(DeptDisposalMappingEntity::getDeptId, disposalCompanyDTO.getDeptId())
                    .eq(DeptDisposalMappingEntity::getDeleteFlag, 0));
        }
        return true;
    }

    @Override
    public List<Integer> getOriginallyDepts(List<Integer> deptIds) {
        List<DeptSyncInfoVO> deptSyncInfoVOS = deptSyncInfoMapper.selectJoinList(DeptSyncInfoVO.class,
                new MPJLambdaWrapper<DeptSyncInfoEntity>()
                        .in(DeptSyncInfoEntity::getLhDeptId, deptIds)
                        .eq(DeptSyncInfoEntity::getDeleteFlag, 0)
        );
        List<String> originallyDepts = deptSyncInfoVOS.stream()
                .map(DeptSyncInfoVO::getDeptId)
                .toList();
        return originallyDepts.stream().map(Integer::valueOf).toList();
    }

    @Override
    public DeptDetailVO getDeptDetailVOByjdDeptId(String deptId) {

        DeptJindieSyncInfoEntity deptJindieSyncInfoEntity = deptJindieSyncInfoMapper.selectOne(new LambdaQueryWrapper<DeptJindieSyncInfoEntity>().eq(DeptJindieSyncInfoEntity::getJdDeptId, Integer.valueOf(deptId)).eq(DeptJindieSyncInfoEntity::getDeleteFlag, 0).orderByDesc(DeptJindieSyncInfoEntity::getCreateTime).last("limit 1"));
        log.info("deptJindieSyncInfoEntity:{}", deptJindieSyncInfoEntity);
        if (ObjUtil.isEmpty(deptJindieSyncInfoEntity) || ObjUtil.isEmpty(deptJindieSyncInfoEntity.getLhDeptId())) {
            throw new BusinessException("部门不存在");
        }
        DeptDetailVO deptDetailVO = new DeptDetailVO();

        DeptEntity deptEntity = deptMapper.selectOne(new LambdaQueryWrapper<DeptEntity>().eq(DeptEntity::getId, deptJindieSyncInfoEntity.getLhDeptId()).eq(DeptEntity::getDeleteFlag, 0).orderByDesc(DeptEntity::getCreateTime).last("limit 1"));
        if (ObjUtil.isNotEmpty(deptEntity) && ObjUtil.isNotEmpty(deptEntity.getRemark()) && deptEntity.getRemark().endsWith("分部")) {
            DeptEntity parentDeptEntity = deptMapper.selectOne(new LambdaQueryWrapper<DeptEntity>().eq(DeptEntity::getId, deptEntity.getParentId()).eq(DeptEntity::getDeleteFlag, 0).orderByDesc(DeptEntity::getCreateTime).last("limit 1"));
            deptDetailVO.setStatus(parentDeptEntity.getDeleteFlag());
            deptDetailVO.setId(parentDeptEntity.getId());
        } else if (ObjUtil.isNotEmpty(deptEntity)) {
            deptDetailVO.setStatus(deptEntity.getDeleteFlag());
            deptDetailVO.setId(deptEntity.getId());
        }


        return deptDetailVO;
    }

    @Override
    public List<DeptSyncInfoVO> getJinDieSyncDeptByLhDeptIds(SearchDeptSyncInfoDTO searchDeptSyncInfoDTO) {
        List<DeptSyncInfoVO> deptSyncInfoVOS = deptJindieSyncInfoMapper.selectJoinList(DeptSyncInfoVO.class,
                new MPJLambdaWrapper<DeptJindieSyncInfoEntity>()
                        .selectAs(DeptJindieSyncInfoEntity::getJdDeptId, DeptSyncInfoVO::getId)
                        .in(DeptJindieSyncInfoEntity::getLhDeptId, searchDeptSyncInfoDTO.getLhDeptIdList())
                        .eq(DeptJindieSyncInfoEntity::getDeleteFlag, 0)
        );

        return deptSyncInfoVOS;
    }

    /**
     * 根据地区查询所属门店
     *
     * @param dto
     * @return
     */
    @Override
    public List<StoreInfoVO> getStoreListByRegionId(RegionListDTO dto) {
        return deptMapper.selectJoinList(StoreInfoVO.class,
                new MPJLambdaWrapper<DeptEntity>()
                        .selectAs(DeptEntity::getId, StoreInfoVO::getStoreId)
                        .selectAs(DeptEntity::getName, StoreInfoVO::getStoreName)
                        .eq(DeptEntity::getParentId, dto.getRegionId())
                        .eq(DeptJindieSyncInfoEntity::getDeleteFlag, 0));
    }

    @Override
    public Boolean saveDisposalRecord(DisposalFieldUpdateDTO disposalFieldUpdateDTO, LoginUser loginUser) {
        Assert.isTrue(ObjUtil.isNotNull(loginUser), () -> new BusinessException("无用户信息"));
        if (CollUtil.isEmpty(disposalFieldUpdateDTO.getTitles())) {
            return true;
        }
        for (String title : disposalFieldUpdateDTO.getTitles()) {
            DisposalUpdateFieldRecordsEntity caseUpdateFieldRecordsEntity = new DisposalUpdateFieldRecordsEntity()
                    .setDeptId(disposalFieldUpdateDTO.getDeptId())
                    .setFieldTitle(title);
            disposalUpdateFieldRecordsMapper.insert(caseUpdateFieldRecordsEntity);
        }
        return true;
    }

    @Override
    public Page<DisposalFieldUpdateVO> getDisposalCompanyRecord(DisposalFieldUpdateDTO dto) {
        Page<DisposalFieldUpdateVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<DisposalFieldUpdateVO> updateVOPage = disposalUpdateFieldRecordsMapper.selectJoinPage(page,
                DisposalFieldUpdateVO.class,
                new MPJLambdaWrapper<DisposalUpdateFieldRecordsEntity>()
                        .selectAs(DisposalUpdateFieldRecordsEntity::getDeptId, DisposalFieldUpdateVO::getDeptId)
                        .selectAs(DisposalUpdateFieldRecordsEntity::getFieldTitle, DisposalFieldUpdateVO::getFieldTitle)
                        .selectAs(DisposalUpdateFieldRecordsEntity::getId, DisposalFieldUpdateVO::getId)
                        .selectAs(DisposalUpdateFieldRecordsEntity::getUpdateBy, DisposalFieldUpdateVO::getUpdateUserId)
                        .eq(DisposalUpdateFieldRecordsEntity::getDeleteFlag, 0)
        );

        updateVOPage.getRecords().forEach(record -> {
            record.setUpdateUserName(userService.getUserInfoById(record.getUpdateUserId()).getName());
        });

        return updateVOPage;
    }

    @Override
    public List<DeptSyncInfoVO> getSyncDeptByUserId(SearchDeptSyncInfoDTO dto) {
        List<UserDeptMappingEntity> userDeptMappingEntities = userDeptMappingMapper.selectList(new LambdaQueryWrapper<UserDeptMappingEntity>().eq(UserDeptMappingEntity::getUserId, dto.getUserId()).eq(UserDeptMappingEntity::getDeleteFlag, 0));
        List<Integer> deptIds = userDeptMappingEntities.stream()
                .map(UserDeptMappingEntity::getDeptId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        return deptSyncInfoMapper.selectJoinList(DeptSyncInfoVO.class, new MPJLambdaQueryWrapper<DeptSyncInfoEntity>()
                .select(DeptSyncInfoEntity::getId,
                        DeptSyncInfoEntity::getDeptId,
                        DeptSyncInfoEntity::getDeptName,
                        DeptSyncInfoEntity::getParentId,
                        DeptSyncInfoEntity::getLhDeptId,
                        DeptSyncInfoEntity::getOrigin,
                        DeptSyncInfoEntity::getDeptLayer)
                .in(CollUtil.isNotEmpty(deptIds), DeptSyncInfoEntity::getLhDeptId, deptIds)
                .eq(ObjUtil.isNotNull(dto.getOrigin()), DeptSyncInfoEntity::getOrigin, dto.getOrigin())
                .eq(DeptSyncInfoEntity::getDeleteFlag, 0));
    }

    @Override
    public List<Integer> getDeptIdListByType(SearchDeptListByTypeDTO dto) {
        if (ObjUtil.equals(dto.getType(), 1)) {
            return deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>()
                    .like(DeptEntity::getRemark, "分部")
                    .eq(DeptEntity::getDeleteFlag, 0)
            ).stream().map(DeptEntity::getId).toList();
        }
        return List.of();
    }

    @Override
    public Map<Integer,List<UserInfoVO>> getUserIdsByRoleIdsAndDeptIds(DeptUsersByRoleIdsDTO deptUsersByRoleIdsDTO) {
        Map<Integer, List<UserInfoVO>> deptIdsMap = new HashMap<>();
        for (Integer storeId : deptUsersByRoleIdsDTO.getDeptIds()) {
            List<Integer> allChildDeptIds = new ArrayList<>();
            allChildDeptIds.add(storeId);

            int type = 0;
            Long tempCount = deptMapper.selectCount(new LambdaQueryWrapper<DeptEntity>()
                    .eq(DeptEntity::getId, storeId)
                    .like(DeptEntity::getName, "门店"));
            if (tempCount > 0) {
                type = 1;
            }

            getAllChildDepartmentIds(storeId, allChildDeptIds,type);
            List<Integer> userIdList = userService.getDepartmentUserIds(allChildDeptIds);
            if (CollUtil.isNotEmpty(userIdList)) {
                List<Integer> roleUserList = userRoleMappingMapper.selectList(new LambdaQueryWrapper<UserRoleMappingEntity>()
                                .in(UserRoleMappingEntity::getUserId, userIdList)
                                .in(UserRoleMappingEntity::getRoleId, deptUsersByRoleIdsDTO.getRoleIds()).eq(UserRoleMappingEntity::getDeleteFlag, 0))
                        .stream().map(UserRoleMappingEntity::getUserId).toList();

                List<UserEntity> userEntities = userMapper.selectList(new LambdaQueryWrapper<UserEntity>()
                        .in(CollUtil.isNotEmpty(roleUserList), UserEntity::getId, roleUserList)
                        .or(or -> or.in(UserEntity::getId, userIdList)
                                .in(UserEntity::getRoleId, deptUsersByRoleIdsDTO.getRoleIds()))
                        .eq(UserEntity::getDeleteFlag, 0));
                if (CollUtil.isNotEmpty(userEntities)) {
                    List<UserInfoVO> list = userEntities.stream().map(userInfoConverter::entity2VO).toList();
                    deptIdsMap.put(storeId, list);
                }
            }
        }



        return deptIdsMap;
    }


    /**
     * 获取所有子部门 ID
     *
     * @param deptId          部门 ID
     * @param allChildDeptIds 所有子部门 ID
     * @param type            部门类型 0 全部 1 门店 2 分部
     */
    public void getAllChildDepartmentIds(Integer deptId, List<Integer> allChildDeptIds, Integer type) {
        //   当前部门下级部门
        List<Integer> childDepartmentIds = getChildDepartmentIds(deptId, type);
        allChildDeptIds.addAll(childDepartmentIds);

        if (CollUtil.isNotEmpty(childDepartmentIds)) {
            // 下级部门递归
            for (Integer childId : childDepartmentIds) {
                getAllChildDepartmentIds(childId, allChildDeptIds, type);
            }
        }

    }

    /**
     * 获取子部门 ID
     *
     * @param deptId 部门 ID
     */
    private List<Integer> getChildDepartmentIds(Integer deptId, Integer type) {
        return
                deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>().eq(DeptEntity::getParentId,
                                deptId)
                        .and(
                                qw->
                                        qw.notLike(ObjUtil.equals(type , 1) , DeptEntity::getRemark, "分部")
                                                .or().isNull(DeptEntity::getRemark)
                        )
                        .eq(DeptEntity::getDeleteFlag, 0)).stream().map(DeptEntity::getId).toList();
    }

    @Override
    public List<Integer> getBranchDeptById(Integer deptId) {
        LambdaQueryWrapper<DeptEntity> queryWrapper = new LambdaQueryWrapper<DeptEntity>();
        queryWrapper.eq(DeptEntity::getParentId, deptId);
        queryWrapper.eq(DeptEntity::getDeleteFlag, 0);
        queryWrapper.like(DeptEntity::getRemark, "分部");
        return super.list(queryWrapper).stream().map(DeptEntity::getId).toList();
    }

    @Override
    public List<Tree<Integer>> getRegionListsByPermission(LoginUser loginUser) {

        List<Integer> deptIds = Arrays.asList(429, 65, 47, 48, 49, 46, 45, 56);
        LambdaQueryWrapper<DeptEntity> queryWrapper = new LambdaQueryWrapper<DeptEntity>()
                .in(DeptEntity::getId, deptIds)
                .eq(DeptEntity::getDeleteFlag, 0);

        // 数据权限
        List<Integer> roleIds = loginUser.getRoleIds();

        //业务督导查询所有大区数据 特殊处理
        if (!RoleEnum.OPERATION_SUPERVISOR.hasRole(roleIds)) {
            limitStoreQuota(loginUser, queryWrapper);
        }

        List<DeptEntity> firstLevelDepts = deptMapper.selectList(queryWrapper);

        // 提取第一级部门ID
        List<Integer> firstLevelIds = firstLevelDepts.stream()
                .map(DeptEntity::getId)
                .toList();

        if (CollUtil.isEmpty(firstLevelIds)){
            return List.of();
        }

        // 查询第二级数据（name 包含 "门店"）
        List<DeptEntity> secondLevelDepts = deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>()
                .in(DeptEntity::getParentId, firstLevelIds)
                .like(DeptEntity::getName, "店")
                .eq(DeptEntity::getDeleteFlag, 0));

        // 合并数据
        List<DeptEntity> departmentList = new ArrayList<>();
        departmentList.addAll(firstLevelDepts);
        departmentList.addAll(secondLevelDepts);

        // 配置树节点
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("parentId");

        // 构建树形结构（根节点为 44）
        List<Tree<Integer>> treeList11 = TreeUtil.build(departmentList, 44, treeNodeConfig, (dept, tree) -> {
            tree.setId(dept.getId());
            tree.setName(dept.getName());
            tree.setParentId(dept.getParentId());
            tree.putExtra("syncId", dept.getSyncId());
            tree.putExtra("deptLayer", dept.getDeptLayer());
        });

        // 构建树形结构（根节点为 24）
        List<Tree<Integer>> treeList24 = TreeUtil.build(departmentList, 11, treeNodeConfig, (dept, tree) -> {
            tree.setId(dept.getId());
            tree.setName(dept.getName());
            tree.setParentId(dept.getParentId());
            tree.putExtra("syncId", dept.getSyncId());
            tree.putExtra("deptLayer", dept.getDeptLayer());
        });
        // 构建树形结构（根节点为 24）
        List<Tree<Integer>> treeList1 = TreeUtil.build(departmentList, 24, treeNodeConfig, (dept, tree) -> {
            tree.setId(dept.getId());
            tree.setName(dept.getName());
            tree.setParentId(dept.getParentId());
            tree.putExtra("syncId", dept.getSyncId());
            tree.putExtra("deptLayer", dept.getDeptLayer());
        });

        // 合并结果
        List<Tree<Integer>> finalTreeList = new ArrayList<>();
        if (ObjUtil.isNotNull(treeList11)) {
            finalTreeList.addAll(treeList11);
        }
        if (ObjUtil.isNotNull(treeList24)) {
            finalTreeList.addAll(treeList24);
        }
        if (ObjUtil.isNotNull(treeList1)) {
            finalTreeList.addAll(treeList1);
        }

        return finalTreeList;
    }

    private final static String DATA_ALL = "data:all";
    private final static String DATA_REGION = "data:region";
    private void limitStoreQuota(LoginUser loginUser, LambdaQueryWrapper<DeptEntity> queryWrapper) {
        Objects.requireNonNull(loginUser, "用户信息不存在");
        // 数据权限
        List<Integer> roleIds = loginUser.getRoleIds();
        if (RoleEnum.SYS_ADMIN.hasRole(roleIds) || RoleEnum.BIZ_SUPER_ADMIN.hasRole(roleIds)) {
            return;
        }
        // 数据权限控制
        String scopes = loginUser.getScopes();
        if (scopes != null && scopes.contains(DATA_ALL)) {
            return;
        }
        if (scopes != null && scopes.contains(DATA_REGION)) {
            List<Integer> deptIds = loginUser.getDeptIds();
            queryWrapper.in(DeptEntity::getId, deptIds);
            return;
        } else {
            throw new BusinessException("用户无数据权限");
        }

    }

    @Override
    public List<Tree<Integer>> getRegionAndDeptList() {
        //业务大区
        LambdaQueryWrapper<DeptEntity> queryWrapper1 = new LambdaQueryWrapper<DeptEntity>();
        queryWrapper1.eq(DeptEntity::getName, "业务大区");
        queryWrapper1.eq(DeptEntity::getDeleteFlag, 0);
        DeptEntity deptEntity1 = deptMapper.selectOne(queryWrapper1);

        List<DeptEntity> deptEntities =
                deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>().eq(DeptEntity::getParentId,
                                deptEntity1.getId())
                        .eq(DeptEntity::getDeleteFlag, 0));


        List<Integer> list = deptEntities.stream().map(DeptEntity::getId).toList();

        List<DeptEntity> departmentList =
                deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>().in(DeptEntity::getParentId, list)
                        .eq(DeptEntity::getDeleteFlag, 0)
                        .like(DeptEntity::getName, "门店"));

        //门店id
        List<Integer> storeIds = departmentList.stream().map(DeptEntity::getId).toList();
        List<DeptEntity> fenBuList =
                deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>().in(DeptEntity::getParentId, storeIds)
                        .eq(DeptEntity::getDeleteFlag, 0)
                        .like(DeptEntity::getRemark, "分部"));

        departmentList.addAll(deptEntities);
        fenBuList.addAll(departmentList);

        // 配置树节点
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("parentId");

        // 业务大区构建树形结构
        List<Tree<Integer>> treeList = TreeUtil.build(fenBuList, deptEntity1.getId(), treeNodeConfig, (dept,
                                                                                                      tree) -> {
            tree.setId(dept.getId());
            if (ObjUtil.isNotNull(dept.getRemark()) && dept.getRemark().contains("分部")) {
                tree.setName(dept.getRemark() + "-" + dept.getName());
            } else {
                tree.setName(dept.getName());
            }
            tree.setParentId(dept.getParentId());
            tree.putExtra("syncId", dept.getSyncId());
            tree.putExtra("deptLayer", dept.getDeptLayer());
        });

        //电销部
        LambdaQueryWrapper<DeptEntity> queryWrapper2 = new LambdaQueryWrapper<DeptEntity>();
        queryWrapper2.eq(DeptEntity::getName, "电销部");
        queryWrapper2.eq(DeptEntity::getRemark, "电销大区");
        queryWrapper2.eq(DeptEntity::getDeleteFlag, 0);
        DeptEntity deptEntity2 = deptMapper.selectOne(queryWrapper2);
        if (deptEntity2 != null) {
            // 根节点
            DeptEntity deptEntity = deptMapper.selectById(deptEntity2.getParentId());

            // 门店
            List<DeptEntity> deptEntities1 = deptMapper.selectList(new LambdaQueryWrapper<DeptEntity>().eq(DeptEntity::getParentId, deptEntity2.getId())
                    .eq(DeptEntity::getDeleteFlag, 0)
                    .like(DeptEntity::getName, "店"));
            deptEntities1.add(deptEntity2);

            // 电销部构建树形结构
            List<Tree<Integer>> treeList2 = TreeUtil.build(deptEntities1, deptEntity.getId(), treeNodeConfig, (dept,
                                                                                                           tree) -> {
                tree.setId(dept.getId());
                tree.setName(dept.getName());
                tree.setParentId(dept.getParentId());
                tree.putExtra("syncId", dept.getSyncId());
                tree.putExtra("deptLayer", dept.getDeptLayer());
            });
            treeList.addAll(treeList2);
        }
        return treeList;
    }

    @Override
    public List<Integer> getChildStoreById(Integer deptId) {
        LambdaQueryWrapper<DeptEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeptEntity::getParentId, deptId);
        queryWrapper.eq(DeptEntity::getDeleteFlag, 0);
        queryWrapper.like(DeptEntity::getName, "店");
        return super.list(queryWrapper).stream().map(DeptEntity::getId).toList();
    }

    @Override
    public List<DeptEntityVO> selectDeptBatchIds(List<Integer> deptIds) {
        if (deptIds == null || deptIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<DeptEntity> deptEntities = deptMapper.selectBatchIds(deptIds);
        if (deptEntities == null || deptEntities.isEmpty()) {
            return new ArrayList<>();
        }

        // 收集所有需要查询的父部门ID
        Set<Integer> parentIds = deptEntities.stream()
                .map(DeptEntity::getParentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 批量查询所有父部门
        Map<Integer, DeptEntity> parentDeptMap = new HashMap<>();
        if (!parentIds.isEmpty()) {
            List<DeptEntity> parentDepts = deptMapper.selectBatchIds(new ArrayList<>(parentIds));
            if (parentDepts != null) {
                parentDeptMap = parentDepts.stream()
                        .collect(Collectors.toMap(DeptEntity::getId, Function.identity(), (existing, replacement) -> existing));
            }
        }

        List<DeptEntityVO> deptEntityVOList = new ArrayList<>(deptEntities.size());
        for (DeptEntity deptEntity : deptEntities) {
            if (deptEntity == null) {
                continue;
            }
            DeptEntityVO deptEntityVO = new DeptEntityVO();
            BeanUtil.copyProperties(deptEntity, deptEntityVO);

            // 设置父部门名称
            Integer parentId = deptEntity.getParentId();
            if (parentId != null) {
                DeptEntity deptParent = parentDeptMap.get(parentId);
                if (deptParent != null) {
                    deptEntityVO.setParentName(deptParent.getName());
                }
            }

            deptEntityVOList.add(deptEntityVO);
        }

        return deptEntityVOList;
    }
}




