package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.BatchIntelligentRiskDTO;
import com.longhuan.order.pojo.dto.GetIntelligentAIRiskReasonDTO;
import com.longhuan.order.pojo.dto.intelligentRisk.IntelligentRiskCallBack;
import com.longhuan.order.pojo.vo.GetIntelligentAIRiskReasonVO;
import com.longhuan.order.service.IntelligentRiskControlService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 智能风控
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/contract")
@RequiredArgsConstructor
public class IntelligentRiskControlController {

    private final IntelligentRiskControlService intelligentRiskControlService;

    /**
     * 查询智能风控
     * @param orderId
     * @return
     */
    @GetMapping("/intelligentRisk")
    public Result<Boolean> intelligentRisk(
            @RequestParam(name = "orderId", required = false) Integer orderId,
            @RequestParam(name = "step", required = false) Integer step,
            @RequestParam(name = "orderNumber", required = false) String orderNumber) {
        return Result.success(intelligentRiskControlService.intelligentRisk(orderId,step, orderNumber));
    }
    /**
     * 获取智能风控结果
     */
    @GetMapping("/intelligentRiskResult")
    public Result<String> intelligentRiskResult(
            @RequestParam(name = "orderId", required = false) Integer orderId,
            @RequestParam(name = "step", required = false) Integer step,
            @RequestParam(name = "type", required = false) Integer type,
            @RequestParam(name = "orderNumber", required = false) String orderNumber) {
        return Result.success(intelligentRiskControlService.intelligentRiskResult(orderId,step,type,orderNumber));
    }
    /**
     * 风控结果回调
     */
    @PostMapping("/intelligentRiskCallback")
    public Result<Boolean> intelligentRiskCallback(
            @RequestParam(name = "orderId", required = false) Integer  orderId,
            @RequestParam(name = "step", required = false) Integer step,
            @RequestParam(name = "type", required = false) Integer type,
            @RequestParam(name = "result", required = false) String result) {
        return Result.success(intelligentRiskControlService.intelligentRiskCallback(orderId,step,type,result));
    }
    /**
     * 批量查询智能风控
     */
    @PostMapping("/batchIntelligentRisk")
    public Result<Boolean> batchIntelligentRisk(@RequestBody BatchIntelligentRiskDTO dto ) {
        return Result.success(intelligentRiskControlService.batchIntelligentRisk(dto));
    }
    /**
     * 根据id获取风控结果
     */
    @GetMapping("/getIntelligentRiskResultById")
    public Result<String> getIntelligentRiskResultById(
            @RequestParam(name = "orderId", required = false) Integer orderId,
            @RequestParam(name = "step", required = false) Integer step,
            @RequestParam(name = "type", required = false) Integer type,
            @RequestParam(name = "orderNumber", required = false) String orderNumber
    ){
        return Result.success(intelligentRiskControlService.getIntelligentRiskResultById(orderId,step,type,orderNumber));
    }
    /**
     * 获取当前订单有没有风控报告
     */
    @GetMapping("/getIntelligentRiskReport")
    public Result<Boolean> getIntelligentRiskReport(
            @RequestParam(name = "orderId", required = false) Integer orderId,
            @RequestParam(name = "type", required = false) Integer type
            ){
        return Result.success(intelligentRiskControlService.getIntelligentRiskReport(orderId,type));
    }

    /**
     * 智能合同
     */
    @PostMapping("/intelligentContract")
    public Result<Boolean> intelligentContract(
            @RequestParam(name = "orderId", required = false) Integer orderId,
            @RequestParam(name = "step", required = false) Integer step,
            @RequestParam(name = "orderNumber", required = false) String orderNumber
    ){
        return Result.success(intelligentRiskControlService.intelligentContract(orderId,step,orderNumber));
    }

    /**
     * 获取智能合同第一阶段定时任务
     */
    @GetMapping("/getIntelligentContractFirstStageTask")
    public Result<Boolean> getIntelligentContractFirstStageTask(){
        return Result.success(intelligentRiskControlService.getIntelligentContractFirstStageTask());
    }


    /**
     * 智能风控报告返回结果
     */
    @PostMapping("/intelligentRiskResultCallBack")
    public void intelligentRiskResultCallBack(@RequestBody IntelligentRiskCallBack callBack) {
         intelligentRiskControlService.getIntelligentRiskResultCallBack(callBack);
    }

    /**
     * 智能风控报告主动查询
     */
    @GetMapping("/queryIntelligentRiskResult")
    public void intelligentRiskResultCallBack(@RequestParam Integer orderId,
                                              @RequestParam String type) {
        intelligentRiskControlService.queryIntelligentRiskResult(orderId,type);
    }

    /**
     * 超过两次未成功 发送请求
     */
    @GetMapping("/submitIntelligentRiskError")
    public void submitIntelligentRiskError(@RequestParam Integer orderId,
                                              @RequestParam String type) {
        intelligentRiskControlService.submitIntelligentRiskError(orderId,type);
    }
    /**
     * 获取智能AI额度报告返回原因
     */
    @PostMapping("/getIntelligentAIRiskReason")
    public Result<GetIntelligentAIRiskReasonVO> getIntelligentAIRiskReason(@RequestBody GetIntelligentAIRiskReasonDTO dto) {
        return Result.success(intelligentRiskControlService.getIntelligentAIRiskReason(dto));
    }
    /**
     * 根据订单编号获取面签视频
     */
    @GetMapping("/getSignVideo/{orderNumber}")
    public Result<List<String>> getSignVideo(@PathVariable("orderNumber") String orderNumber) {
        return Result.success(intelligentRiskControlService.getSignVideo(orderNumber));
    }

}
