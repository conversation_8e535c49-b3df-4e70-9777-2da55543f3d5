<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.longhuan.order.mapper.OrderSuperaddInfoMapper">

    <!-- resultMap 用于映射 MenuVO 及其嵌套的 OrderFileInfo -->
    <resultMap id="MenuResultMap" type="com.longhuan.order.pojo.vo.MenuVO">
        <id property="menuId" column="menuId"/>
        <result property="fileId" column="fileId"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="minCount" column="minCount"/>
        <result property="maxCount" column="max_count"/>
        <result property="required" column="required"/>
        <result property="uploadFileType" column="uploadFileType"/>
        <result property="show" column="show"/>

        <!-- 使用 collection 元素来映射 OrderFileInfo 列表 -->
        <collection property="orderFileInfo" ofType="com.longhuan.order.pojo.vo.MenuVO$OrderFileInfo">
            <id property="id" column="superaddFileId"/>
<!--            <result property="orderId" column="order_id"/>-->
<!--            <result property="fileId" column="fileId"/>-->
<!--            <result property="fileName" column="file_name"/>-->
<!--            <result property="resourceId" column="resource_id"/>-->
<!--            <result property="resourceName" column="resource_name"/>-->
        </collection>
    </resultMap>
    <select id="getMenuFileList" resultType="com.longhuan.order.pojo.vo.MenuVO">

        SELECT
            0 AS menuId,
            osi."id" AS fileId,
            osi.title_name AS name,
            '' AS code,
            1 AS minCount,
            10 AS maxCount,
            0 AS required,
            0 AS show,
            osi.order_id,
            osf."id" AS superaddFileId,
            osf.file_name,
            osf.resource_id,
            osf.resource_name
        FROM
            lh_order_superadd_info AS osi
            LEFT JOIN
            lh_order_superadd_file AS osf
            ON
                osi."id" = osf.superadd_info_id
        WHERE
            osi.delete_flag = 0
          AND osf.delete_flag = 0
        <if test="orderId!= null and orderId!= ''">
            AND osi.order_id= #{orderId}
        </if>

    </select>
</mapper>