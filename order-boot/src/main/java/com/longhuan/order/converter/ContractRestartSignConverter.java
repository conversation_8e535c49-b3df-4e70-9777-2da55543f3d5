package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.ActorsInfoDTO;
import com.longhuan.order.pojo.entity.ContractRestartSignEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;


@Mapper(componentModel = "spring")
public interface ContractRestartSignConverter {
    @Mapping(target = "actorName ", source = "name")
    @Mapping(target = "accountName ", source = "phone")
    @Mapping(target = "actorId", source = "phone")
    ActorsInfoDTO entityToDto(ContractRestartSignEntity signEntity);


}
