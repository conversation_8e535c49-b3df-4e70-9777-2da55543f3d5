package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.ReviewResultDTO;
import com.longhuan.order.pojo.dto.BusinessInfoDTO;
import com.longhuan.order.pojo.entity.OrderPatchInfoEntity;
import com.longhuan.order.pojo.entity.OrderReviewEntity;
import com.longhuan.order.pojo.vo.OrderReviewVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface OrderReviewConverter {

    OrderReviewEntity dto2Entity(ReviewResultDTO reviewResultDTO);

    OrderReviewVO dto2VO(BusinessInfoDTO businessInfoDTO);

    @Mapping(source = "id", target = "id")
    List<OrderReviewVO.SuperAddFileInfo> entityList2VoList(List<OrderPatchInfoEntity> entityList);
}
