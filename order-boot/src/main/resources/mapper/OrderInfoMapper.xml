<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.longhuan.order.mapper.OrderInfoMapper">
    <select id="getRelatedOrderInfo" resultType="com.longhuan.order.pojo.dto.RelatedOrderInfoDTO">
        select loci.id_number, loi.id as orderId, loi.order_number, loci.name, loci.phone, loi.apply_amount,
               lpi.name as productName,lpa.account_manager_id,lvi.vin, lvi.vehicle_number
        from lh_order_customer_info loci
                 left join lh_order_info loi on loci.id = loi.customer_id
                 left join lh_product_info lpi on loi.product_id = lpi.id
                 left join lh_order_vehicle_info lvi on loi.id = lvi.order_id
                 left join lh_pre_approval_apply_info lpa on loi.pre_id = lpa.id

            <where>
                <if test="dto.type != null and dto.type == 1">
                     loci.id_number = #{dto.typeData}
                </if>
                <if test="dto.type != null and dto.type == 2">
                     loci.phone = #{dto.typeData}
                </if>
                <if test="dto.type != null and dto.type == 5">
                     loci.enterprise_name like CONCAT('%', #{dto.typeData}, '%')
                </if>
                <if test="dto.type != null and dto.type == 6">
                     loci.enterprise_address like CONCAT('%', #{dto.typeData}, '%')
                </if>
                <if test="dto.type != null and dto.type == 7">
                     loci.residential_detailed_address like CONCAT('%', #{dto.typeData}, '%')
                </if>
                <if test="dto.type != null and dto.type == 3">
                    lvi.vin = #{dto.typeData}
                </if>
                <if test="dto.type != null and dto.type == 4">
                    lvi.vehicle_number = #{dto.typeData}
                </if>
            </where>
        order by loi.id
    </select>

    <select id="getRelateByVehicle" resultType="com.longhuan.order.pojo.dto.RelatedOrderInfoDTO">
        select loci.id_number, loi.id as orderId, loi.order_number, loci.name, loci.phone, loi.apply_amount,
               lpi.name as productName,lpa.account_manager_id,lvi.vin, lvi.vehicle_number
        from lh_order_vehicle_info lvi
                 left join lh_order_info loi on loi.id = lvi.order_id
                 left join lh_product_info lpi on loi.product_id = lpi.id
                 left join lh_order_customer_info loci on loci.id = loi.customer_id
                 left join lh_pre_approval_apply_info lpa on loi.pre_id = lpa.id
        <where>
            <if test="dto.type != null and dto.type == 3">
                 lvi.vin = #{dto.typeData}
            </if>
            <if test="dto.type != null and dto.type == 4">
                lvi.vehicle_number = #{dto.typeData}
            </if>
        </where>
        order by loi.id
    </select>

    <resultMap id="DetailsVOResultMap" type="com.longhuan.order.pojo.vo.DetailsVO">
        <result property="sign_capital" column="sign_capital"/>
        <result property="loan_name" column="loan_name"/>
        <result property="loan_term" column="loan_term"/>
        <result property="store" column="store"/>
        <result property="store_id" column="store_id"/>
        <result property="daqu_name" column="daqu_name"/>
        <result property="gps_amount" column="gps_amount"/>
        <result property="service_amount" column="service_amount"/>
        <result property="apply_purpose" column="apply_purpose"/>
        <result property="apply_time" column="apply_time"/>
        <result property="apply_amount" column="apply_amount"/>
        <result property="approve_amount" column="approve_amount"/>
        <result property="is_gps" column="is_gps"/>
        <result property="redeem_fee" column="redeem_fee"/>
        <result property="repay_time" column="repay_time"/>
        <result property="apply_period" column="apply_period"/>


        <result property="credit_amount" column="credit_amount"/>
        <result property="interest_formula" column="interest_formula"/>
        <result property="principal_interest_amount" column="principal_interest_amount"/>
        <result property="attributes" column="attributes"/>
        <result property="customer_type" column="customer_type"/>
        <result property="pawn_type" column="pawn_type"/>
        <result property="pawn_type" column="pawn_type"/>

        <result property="compensatory_fee" column="compensatory_fee"/>
    </resultMap>
    <select id="selectOrderDetail"  resultMap="DetailsVOResultMap">

        select
        t4.fund_name as sign_capital,
        t3.name as loan_name,
        t3.term as loan_term,
        t.store_name as store,
        COALESCE(t.dept_id, 0) AS store_id,
        t.region_name as daqu_name,
        t5.gps_fee as gps_amount,
        ( select SUM(t1.settlement_amount) from LH_FUND_REPAYMENT_INFO t1 where t1.order_id = t.id and t1.actually_date
        is null ) as principal_interest_amount,
        -- t1.actually_penalty as breachAmount,
        t5.once_service_fee as service_amount,
        CAST(EXTRACT(EPOCH FROM t.create_time) AS BIGINT) AS apply_time,
        t.apply_amount as apply_amount,
        t.apply_purpose as apply_purpose,
        case
        when t.gps_state = 0 then '未安装'
        when t.gps_state = 1 then '安装中'
        when t.gps_state = 2 then '已安装'
        when t.gps_state = 3 then '拆除中'
        when t.gps_state = 4 then '已拆除'
        else '未知'
        end as is_gps,
        case
        when t.repay_method = 1 then '等额本息'
        when t.repay_method = 2 then '先息后本'
        when t.repay_method = 3 then '等额本金'
        else '未知'
        end as interest_formula,

        case
        when t.advance_mortgage_state = 0 then '未加押'
        when t.advance_mortgage_state = 1 then '未加押完成'
        when t.advance_mortgage_state = 2 then '加押完成'
        else '未知'
        end as attributes,
        t.approval_amount as credit_amount,
        t.approval_amount as approve_amount,
        '车辆' as pawn_type,
        case
        when t.apply_purpose = 1 then '个人'
        when t.apply_purpose = 2 then '公司'
        else '未知'
        end as customer_type,
        t2.repurchase_total_amt as redeem_fee,
        (select EXTRACT(EPOCH FROM MAX(t1.actually_date)) from LH_FUND_REPAYMENT_INFO t1 where t1.order_id = t.id ) as
        repay_time,
        t.term as apply_period,
        ( select SUM(t1.late_penalty_amount) from LH_FUND_REPAYMENT_INFO t1 where t1.order_id = t.id ) as
        compensatory_fee
        from
        lh_order_info t
        left join LH_FUND_REPURCHASE_CALC t2 on
        (t2.order_id = t.id and t2.delete_flag=0)
        left join LH_PRODUCT_INFO t3 on
        (t3.id = t.product_id and t3.delete_flag=0 )
        left join LH_FUND_PRODUCT_MAPPING t4 on
        (t.product_id = t4.product_id) and t.fund_id =t4.fund_id and t4.delete_flag=0
        left join LH_ORDER_FEE_INFO t5 on
        (t5.order_id = t.id) and t5.gps_fee_status =2 and t5.delete_flag=0
        <where>
        <if test="orderId != null ">
               t.id = #{orderId}
        </if>
        </where>

    </select>
    <select id="findrepurchase" resultType="java.util.Map">
        select
            SUM(t.pre_repay_amt)  as  redemptionAmt,
            MAX(t.repurchase_date) as redemptionTime
        from
            lh_fund_repurchase_result t
        where
            t.order_id = #{orderId} and t.delete_flag = 0
    </select>


</mapper>