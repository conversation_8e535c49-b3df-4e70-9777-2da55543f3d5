package com.longhuan.common.redis.pojo;

import jakarta.annotation.Nonnull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.Accessors;
import org.springframework.boot.autoconfigure.condition.ConditionalOnNotWarDeployment;

import java.util.ArrayList;
import java.util.List;

/**
 * 登录用户信息
 *
 * <AUTHOR>
 * @date 2024/07/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class LoginUser {
    /**
     * 用户id
     */
    @Nonnull
    Integer userId;
    /**
     * 用户名
     */
    @Nonnull
    String userName;
    /**
     * 名字
     */
    String name;
    /**
     * 手机号
     */
    String mobile;

    /**
     * 工号
     */
    String jobNumber;

    /**
     * 部门id
     */
    List<Integer> deptIds = new ArrayList<>();

    /**
     * 角色 ID 列表
     */
    List<Integer> roleIds = new ArrayList<>();


    /**
     * 代理权限用户ID 列表
     * <p>
     * 上级代理下级用户查询权限
     * </p>
     */
    List<Integer> proxyUserIds = new ArrayList<>();

    /**
     * 角色 ID
     */
    Integer roleId;

    /**
     * 权限范围
     */
    String scopes;

    /**
     * 状态
     */
    Integer status = 0;
    /**
     * 删除标志
     */
    Integer deleteFlag = 0;

    /**
     * 用户类型 5:内部用户，10: 外部客户，15: 客户端  20:同心
     */

    Integer userType = 5;
    /**
     * 缓存token
     */
    String token;

    /**
     * 访问来源
     */
    String origin;

    /**
     * 续期次数
     */
    Integer renewal = 0;
}
