package com.longhuan.order.converter;

import com.longhuan.order.pojo.entity.CustomerSignInfoEntity;
import com.longhuan.order.pojo.vo.AppointmentSignDetailVO;
import org.mapstruct.Mapper;

/**
 * 客户签约信息转炉
 *
 * <AUTHOR>
 * @date 2024/08/16
 */
@Mapper(componentModel = "spring")
public interface CustomerSignInfoConverter {
    /**
     * DTO 转实体
     *
     * @param appointmentSignDetailVO 约会签约详情信息VO
     * @return {@link CustomerSignInfoEntity }
     */
    CustomerSignInfoEntity dtoToEntity(AppointmentSignDetailVO appointmentSignDetailVO);
}
