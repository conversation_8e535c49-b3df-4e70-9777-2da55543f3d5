<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.longhuan.order.mapper.FundFeeMapper">

    <select id="serviceFeeExport" resultType="com.longhuan.order.pojo.vo.FundServiceFeeExportVO">

        select l1.fund_id,
               l1.product_id,
               l1.fund_name,
               l6.year_rate               as interestRate,
               l1.customer_name,
               l1.order_number,
               l1.vehicle_number,
               l3.id_number,
               l4.create_time             as creditTime,
               l1.payment_time,
               l5.customer_confirm_amount as contractAmount,
               l2.repayment_date,
               l2.actually_date,
               l2.term                    as currentTerm,
               l1.term                    as totalTerm,
               l2.actually_amount_total,
               l2.actually_principal,
               l2.actually_interest,
               l2.actually_penalty
        from lh_order_info l1
                 inner join lh_product_info l6 on l6.id = l1.product_id
                 inner join lh_fund_repayment_info l2 on l2.order_id = l1.id
                 inner join lh_order_customer_info l3 on l3.id = l1.customer_id
                 inner join lh_final_fund_info l4 on l4.order_id = l1.id
                 inner join lh_order_amount l5 on l5.order_id = l1.id
        where l2.actually_date between #{startTime} and #{endTime}

    </select>
    <select id="orderRemainBalance" resultType="com.longhuan.order.pojo.dto.OrderRemainAmountDTO">

        select l1.fund_id, l2.order_id, l2.sum_actually_principal, l2.sum_repayment_principal
        from lh_order_info l1
                 inner join (select order_id,
                                    sum(repayment_principal) as sum_repayment_principal,
                                    sum(actually_principal)  as sum_actually_principal
                             from lh_fund_repayment_info
                             where repayment_status in (0, 1, 2)
                             group by order_id) l2 on l2.order_id = l1.id
        where l1.current_node = 5000
    </select>
    <select id="guaranteeFeeCalc" resultType="com.longhuan.order.pojo.vo.FundPaymentAmountVO">

        select l1.fund_id, sum(l1.approval_amount) as paymentAmountTotal
        from lh_order_info l1
        where l1.payment_time between #{startTime} and #{endTime}
        group by l1.fund_id
    </select>
</mapper>