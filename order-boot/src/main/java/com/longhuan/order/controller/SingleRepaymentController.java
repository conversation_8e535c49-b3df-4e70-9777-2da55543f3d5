package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.singleRepayment.SingleRepaymentDTO;
import com.longhuan.order.pojo.dto.singleRepayment.SingleRepaymentFeeTypeDTO;
import com.longhuan.order.pojo.dto.singleRepayment.SingleRepaymentGenerateQrCodeDTO;
import com.longhuan.order.pojo.vo.TongLianPayVO;
import com.longhuan.order.pojo.vo.singleRepayment.SingleRepaymentDetailVO;
import com.longhuan.order.pojo.vo.singleRepayment.SingleRepaymentListVO;
import com.longhuan.order.pojo.vo.singleRepayment.SingleRepaymentMoneyDTO;
import com.longhuan.order.service.SingleRepaymentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "单期还款")
@RestController
@RequestMapping("/api/v1/singleRepayment")
@RequiredArgsConstructor
public class SingleRepaymentController {

    private final SingleRepaymentService singleRepaymentService;

    @ApiOperation(value = "分页查询单期还款")
    @PostMapping("/list")
    public Result<Page<SingleRepaymentListVO>> list(@RequestBody SingleRepaymentDTO singleRepaymentDTO,
                                                    @CurrentUser LoginUser loginUser) {
        return Result.success(singleRepaymentService.list(singleRepaymentDTO, loginUser));
    }

    /**
     * 单期还款列表导出
     */
    @ApiOperation(value = "单期还款列表导出")
    @PostMapping("/export")
    public void export(@RequestBody SingleRepaymentDTO singleRepaymentDTO,
                       @CurrentUser LoginUser loginUser,
                       HttpServletResponse response) {
        singleRepaymentService.export(singleRepaymentDTO, loginUser, response);
    }

    /**
     * 单期还款列表查看
     */
    @ApiOperation(value = "单期还款列表查看")
    @PostMapping("/detail")
    public Result<Page<SingleRepaymentDetailVO>> detail(@RequestBody SingleRepaymentDTO singleRepaymentDTO,
                                                        @CurrentUser LoginUser loginUser) {
        return Result.success(singleRepaymentService.detail(singleRepaymentDTO, loginUser));
    }

    /**
     * 单期还款-扫码支付-获取应收金额
     */
    @ApiOperation(value = "单期还款-扫码支付-获取应收金额")
    @PostMapping("/getPayMoney")
    public Result<List<SingleRepaymentMoneyDTO>> getPayMoney(@RequestBody SingleRepaymentFeeTypeDTO singleRepaymentFeeTypeDTO) {
        return Result.success(singleRepaymentService.getPayMoney(singleRepaymentFeeTypeDTO));
    }

    /**
     * 单期还款-扫码支付-生成二维码
     */
    @ApiOperation(value = "单期还款-扫码支付-生成二维码")
    @PostMapping("/generateQrCode")
    public Result<TongLianPayVO> generateQrCode(@RequestBody SingleRepaymentGenerateQrCodeDTO singleRepaymentGenerateQrCodeDTO) {
        return Result.success(singleRepaymentService.generateQrCode(singleRepaymentGenerateQrCodeDTO));
    }
}
