package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.approve.api.pojo.dto.FundRepaymentDTO;
import com.longhuan.approve.api.pojo.dto.FundRepaymentDeductCheckDTO;
import com.longhuan.approve.api.pojo.vo.FundDeductCheckVO;
import com.longhuan.common.core.enums.FundDeductRepayStatusEnums;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.FundDeductService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 资方代扣接口
 */
@Api(tags = "资方代扣接口")
@RestController
@RequestMapping("/api/v1/fundDeduct")
@RequiredArgsConstructor
public class FundDeductController {
    private final FundDeductService fundDeductService;


    /**
     * 贷后记录
     */
    @PostMapping("/page")
    public Result<Page<OrderFundDeductListVO>> page(@RequestBody OrderFundDeductQueryListDTO dto,
                                                    @CurrentUser LoginUser loginUser) {
        return Result.success(fundDeductService.page(dto, loginUser));
    }

    /**
     * 贷后记录划扣明细
     */
    @PostMapping("/detailPage")
    public Result<Page<OrderFundDeductDetailListVO>> detailPage(@Validated @RequestBody OrderFundDeductDetailQueryListDTO dto) {
        return Result.success(fundDeductService.detailPage(dto));
    }

    /**
     * 还款详情
     */
    @PostMapping("/getRepaymentDeductInfo")
    public Result<FundRepaymentDeductVO> getRepaymentDeductInfo(@Validated @RequestBody FundRepaymentDeductQueryInfoDTO dto) {
        FundRepaymentDeductVO result = fundDeductService.repaymentDeductInfo(dto);
        return Result.success(result);
    }

    /**
     * 提前结清还款试算
     */
    @PostMapping("/earlyRepayCalc")
    public Result<FundEarlyRepaymentCalcDTO> earlyRepayCalc(@RequestBody @Validated FundRepayCalcEarlyDTO dto) {
        return Result.success(fundDeductService.earlyRepayCalc(dto));
    }
//    /**
//     * 违约金减免金额
//     */
//    @PostMapping("/reductionAmount")
//    public Result<ReductionAmountVO> reductionAmount(@RequestBody ReductionAmountDTO dto) {
//        return Result.success(fundDeductService.reductionAmount(dto));
//    }
    /**
     * 获取减免违约金定时
     */
    @PostMapping("/getPenaltyWaiver")
    public Result<Boolean> getPenaltyWaiver() {
        return Result.success(fundDeductService.getPenaltyWaiver());
    }
    /**
     * 代扣申请
     */
    @PostMapping("/deductApply")
    public Result<Boolean> deductApply(@Validated @RequestBody FundDeductApplyDTO dto) {
        boolean deductApply = fundDeductService.matchDeductApply(dto);
        return Result.success(deductApply);
    }
    /**
     * 违约金代扣申请
     */
    @PostMapping("/penaltyDeductApply")
    public Result<Boolean> penaltyDeductApply(@Validated @RequestBody PenaltyDeductApplyDTO dto) {
        boolean deductApply = fundDeductService.penaltyDeductApply(dto);
        return Result.success(deductApply);
    }
    /**
     *  违约金扫码支付接口
     */
    @PostMapping("/scanCodePayment")
    public Result<ScanCodePaymentVO> scanCodePayment(@RequestBody ScanCodePaymentDTO dto){
        return Result.success(fundDeductService.scanCodePayment(dto));
    }
    /**
     * 违约金结清结果
     */
    @GetMapping("/scanCodePaymentResult")
    public void scanCodePaymentResult(){
        fundDeductService.scanCodePaymentResult();
    }
    /**
     * 违约金对公转账接口
     */
    @PostMapping("/corporateTransfer")
    public Result<Boolean> corporateTransfer(@RequestBody CorporateTransferDTO dto,
                                             @CurrentUser LoginUser loginUser) {
        return fundDeductService.corporateTransfer(dto,loginUser);
    }

    /**
     * 获取盈峰支付二维码
     *
     * @param fundRepaymentDTO
     * @return
     */
    @PostMapping("/applyQrCode")
    public ResponseEntity<byte[]> applyQrCode(@RequestBody FundRepaymentDTO fundRepaymentDTO) {
        byte[] imageBytes = fundDeductService.applyQrCode(fundRepaymentDTO);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_PNG);
        headers.setContentDisposition(ContentDisposition.builder("attachment").filename(fundRepaymentDTO.getOrderId() + "_" + fundRepaymentDTO.getTerm() + "_" + fundRepaymentDTO.getRepayService() + ".png").build());
        // 返回响应实体
        return new ResponseEntity<>(imageBytes, headers, HttpStatus.OK);
    }

    /**
     * 代扣查询
     */
    @PostMapping("/deductQuery")
    public Result<FundDeductQueryVO> deductQuery(@Validated @RequestBody FundDeductQueryDTO queryDTO) {
        return Result.success(fundDeductService.deductQuery(queryDTO.getOrderId(), queryDTO.getTerm(), queryDTO.getSettleFlag()));
    }

    /**
     * 7.2付款结果查询
     *
     * @param fundRepaymentDTO DTO
     */
    @PostMapping("/qrPayResult")
    public Result<Integer> qrPayResult(@RequestBody FundRepaymentDTO fundRepaymentDTO) {
        Integer result = fundDeductService.qrPayResult(fundRepaymentDTO);
        return Result.success(result);
    }

    /**
     * 7.3 支付后发起还款
     *
     * @param fundRepaymentDTO DTO
     */
    @PostMapping("/repayApply")
    public Result<Boolean> repayApply(@RequestBody FundRepaymentDTO fundRepaymentDTO) {
        Boolean result = fundDeductService.repayApply(fundRepaymentDTO);
        return Result.success(result);
    }

    /**
     * 7.4 查询还款结果
     *
     * @param fundRepaymentDTO DTO
     */
    @PostMapping("/queryRepayResult")
    public Result<Boolean> queryRepayResult(@RequestBody FundRepaymentDTO fundRepaymentDTO) {
        Boolean result = fundDeductService.queryRepayResult(fundRepaymentDTO);
        return Result.success(result);
    }

    /**
     * 检查期次
     */
    @PostMapping("/checkThePeriod")
    public Result<FundDeductCheckVO> checkThePeriod(@RequestBody FundRepaymentDeductCheckDTO fundRepaymentDTO) {
        return Result.success(fundDeductService.checkThePeriod(fundRepaymentDTO));
    }
    /**
     * 根据orderID获取产品信息
     */
    @PostMapping("/getProductInfo")
    public Result<Boolean> getProductInfo(@RequestBody ProductInfoDTO dto) {
        return Result.success(fundDeductService.getProductInfo(dto));
    }

    /**
     * 按期还款-查询资方划扣金额
     */
    @PostMapping("/singleDeduct")
    public Result<FundEarlyRepaymentCalcDTO> getSingleDeductData(@RequestBody FundDeductApplyDTO fundDeductApplyDTO) {
        return Result.success(fundDeductService.getSingleDeductData(fundDeductApplyDTO));
    }
    /**
     * 当前订单是否为封闭期内
     */
    @GetMapping("/isClosedPeriod/{orderId}")
    public Result<Boolean> isClosedPeriod(@PathVariable("orderId") Integer orderId) {
        return Result.success(fundDeductService.isClosedPeriod(orderId));
    }
}