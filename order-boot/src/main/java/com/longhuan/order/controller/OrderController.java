package com.longhuan.order.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.risk.pojo.dto.xueli.QueryVerifyV4DTO;
import com.longhuan.order.pojo.entity.OrderApprovalReasonStagingEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.entity.RiskUserStateEntity;
import com.longhuan.order.pojo.vo.*;

import com.longhuan.order.service.*;
import com.longhuan.risk.pojo.dto.GetUserMarriageDTO;
import com.longhuan.risk.pojo.vo.FhldDataVO;
import com.longhuan.user.pojo.dto.GenerateCodeDTO;
import com.longhuan.user.pojo.dto.SendMessageDTO;
import com.longhuan.user.pojo.vo.UserInfoVO;
import com.longhuan.user.pojo.vo.UserStoreVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.view.RedirectView;

import java.net.URI;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 订单
 *
 * <AUTHOR>
 * @date 2024/06/20
 */
@Api(tags = "业务订单接口")
@RestController
@RequestMapping("/api/v1/order")
@RequiredArgsConstructor
public class OrderController {

    private final OrderService orderService;
    private final UserOnlineDistributionService userOnlineDistributionService;
    private final OrderSendMessage orderSendMessage;
    private final OrderFeeDetailService orderFeeDetailService;
    private final OrderApprovalReasonStagingService orderApprovalReasonStagingService;

    private final RedisService redisService;
    private final XueliService xueliService;

    /**
     * 业务审批数据
     */
    @ApiOperation(value = "分页查询业务审批")
    @PostMapping("/page/approveList")
    public Result<Page<OrderApproveListVO>> pageApproveList(@RequestBody @Validated OrderApproveDTO orderApproveDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderService.pageApproveList(orderApproveDTO, loginUser));
    }

    @ApiOperation(value = "放款成功列表放款金额合计")
    @PostMapping("/page/approveListTotal")
    Result<PayApplicationPageListVO> approveListTotal(@RequestBody @Validated OrderApproveDTO dto, @CurrentUser LoginUser loginUser) {
        return Result.success(orderService.approveListTotal(dto, loginUser));
    }
    /**
     * 移动端订单列表
     */
    @ApiOperation(value = "订单列表", notes = "限制团队经理,客户经理查询,客户")
    @PostMapping("/page/orderInfoList")
    public Result<IPage<OrderInfoListVO>> pageOrderList(@RequestBody OrderInfoDTO orderInfoDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderService.pageOrderList(orderInfoDTO, loginUser));
    }

    /**
     * 根据订单ID查询订单
     *
     * @param orderId 订单ID
     * @return 订单
     */
    @PostMapping("/getOrderByOrderId/{orderId}")
    public Result<GetOrderByOrderIdVo> getOrderByOrderId(@PathVariable("orderId") Integer orderId) {
        return Result.success(orderService.getOrderByOrderId(orderId));
    }

    /**
     * 保存订单信息
     *
     * @param saveOrderInfoDTO 保存订单信息DTO
     * @return 订单实体
     */
    @PostMapping("saveOrderInfo")
    public Result<OrderInfoEntity> saveOrderInfo(@RequestBody @Validated SaveOrderInfoDTO saveOrderInfoDTO) {
        return Result.success(orderService.saveOrderInfo(saveOrderInfoDTO));
    }

    /**
     * 新增总放款额、进件量、放款笔数统计接口
     */
    @ApiOperation(value = "新增总放款额、进件量、放款笔数统计接口")
    @PostMapping("/selectHomeData")
    public Result<OrderHomeDataVO> selectHomeData(@CurrentUser LoginUser loginUser) {
        return Result.success(orderService.selectHomeData(loginUser));
    }

    /**
     * cdd订单序列清空
     */
    @ApiOperation(value = "cdd订单序列清空")
    @PostMapping("/resetCddSequence")
    public Result<String> resetCddSequence() {
        return Result.success(orderService.resetCddSequence());
    }

    /**
     * fdd订单序列清空
     */
    @ApiOperation(value = "fdd订单序列清空")
    @PostMapping("/resetFddSequence")
    public Result<String> resetFddSequence() {
        return Result.success(orderService.resetFddSequence());
    }

    /**
     * 按订单 ID 划分详细信息
     *
     * @param orderId 订单 ID
     * @return {@link Result }<{@link OrderDetailsVo }>
     */
    @GetMapping("/detailsByOrderId")
    public Result<OrderDetailsVo> detailsByOrderId(@RequestParam Integer orderId) {
        return Result.success(orderService.detailsByOrderId(orderId));
    }

    /**
     * 按身份证 id_number查询详细信息
     *
     * @param idNumber 身份证
     * @return {@link Result }<{@link OrderDetailsVo }>
     */
    @GetMapping("/detailsByIdNumber")
    public Result<OrderDetailsVo> detailsByIdNumber(@RequestParam String idNumber) {
        return Result.success(orderService.detailsByIdNumber(idNumber));
    }

    /**
     * 额度确认详情
     *
     * @param orderId 订单 ID
     * @return {@link Result }<{@link AmountDetailVO }>
     */
    @GetMapping("/confirm_detail")
    public Result<AmountDetailVO> confirmDetail(@Validated @RequestParam("id") @NotNull(message = "订单ID不能为空") Integer orderId) {
        return Result.success(orderService.confirmDetail(orderId));
    }

    /**
     * 额度确认提交
     *
     * @param amountConfirmDTO 金额确认 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @PostMapping("/confirm_submit")
    public Result<Boolean> confirmSubmit(@Validated @RequestBody AmountConfirmDTO amountConfirmDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderService.confirmSubmit(amountConfirmDTO, loginUser));
    }

//    /**
//     * 测试订单分配
//     *
//     * @return {@link Result }<{@link Boolean }>
//     */
//    @PostMapping("/orderDistributionAutoTest")
//    public Result<List<Integer>> orderDistributionAutoTest() {
//        return Result.success(orderService.orderDistributionAutoTest());
//    }

    /**
     * 更新风控人员状态
     *
     * @param userRiskChangeDTO 更新状态 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @PostMapping("/changeRiskStatus")
    public Result<Boolean> changeRiskStatus(@Validated @RequestBody UserRiskChangeDTO userRiskChangeDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(userOnlineDistributionService.changeRiskStatus(userRiskChangeDTO, loginUser));
    }

    @ApiOperation(value = "查询风控人员状态")
    @GetMapping("/selectUserRiskById")
    Result<RiskUserStateEntity> selectUserRiskById(@CurrentUser LoginUser loginUser) {
        return Result.success(userOnlineDistributionService.selectUserRiskById(loginUser));
    }

    @ApiOperation(value = "查询风控人员列表")
    @PostMapping("/selectUserRiskListById")
    Result<List<UserInfoVO>> selectUserRiskListById(@Validated @RequestBody UserRiskListDTO userRiskListDTO) {
        return Result.success(userOnlineDistributionService.selectUserRiskListById(userRiskListDTO));
    }
    @ApiOperation(value = "查询客服在线列表")
    @PostMapping("/selectOnlineUserList")
    Result<Page<UserStoreVO>> selectOnlineUserList(@RequestBody OnlineUserListDTO onlineUserListDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(userOnlineDistributionService.selectOnlineUserList(onlineUserListDTO,loginUser));
    }
    @ApiOperation(value = "客服派单分配权限")
    @PostMapping("/customerServiceDispatchAssignmentAuthority")
    Result<Boolean> customerServiceDispatchAssignmentAuthority(@CurrentUser LoginUser loginUser) {
        return Result.success(userOnlineDistributionService.customerServiceDispatchAssignmentAuthority(loginUser));
    }
    /**
     * 风控派单手动分配
     *
     * @param orderDistributionDTO 订单分配 DTO
     * @param loginUser            登录用户
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "派单手动分配")
    @PostMapping("/orderDistribution")
    Result<Boolean> orderDistribution(@Validated @RequestBody OrderDistributionDTO orderDistributionDTO, @CurrentUser LoginUser loginUser) {
        String lockKey = "order:assign:risk:" + orderDistributionDTO.getOrderNumber()+":"+orderDistributionDTO.getOrderId();
        String lockValue = IdUtil.randomUUID();
        if (ObjUtil.isNotNull(orderDistributionDTO.getNode())) {
           lockKey += ":" + orderDistributionDTO.getNode();
        }
        try {
            Boolean tryLock = redisService.tryLock(lockKey, lockValue, 10, TimeUnit.SECONDS);
            if (!tryLock) {
                return Result.failed("订单正在分配中");
            }
            return Result.success(userOnlineDistributionService.orderDistributionManual(orderDistributionDTO, loginUser));
        } finally {
            redisService.releaseLock(lockKey, lockValue);

        }
    }

    /**
     * 修改资方、预审批额度
     *
     * @param updateOrderDTO 更新订单DTO
     * @return {@link Result<Boolean>}
     */
    @PostMapping("/updateOrder")
    public Result<Boolean> updateOrder(@RequestBody UpdateOrderDTO updateOrderDTO) {
        return Result.success(orderService.updateOrder(updateOrderDTO));
    }

    /**
     * 查看外部数据
     */
    @ApiOperation(value = "查看外部数据")
    @GetMapping("/getFhldInfo/{orderId}")
    public Result<FhldDataVO> getFhldInfo(@PathVariable("orderId") Integer orderId) {
        return Result.success(orderService.getFhldInfo(orderId));
    }


    /**
     * 获取相关信息
     *
     * @param relatedOrderDTO 相关订单 DTO
     * @return {@link Result }<{@link Page }<{@link RelatedOrderVO }>>
     */
    @ApiOperation(value = "关联订单")
    @PostMapping("/related")
    public Result<Page<RelatedOrderVO>> getRelatedInfo(@RequestBody @Validated RelatedOrderDTO relatedOrderDTO) {
        return Result.success(orderService.getRelatedInfo(relatedOrderDTO));
    }

    @ApiOperation(value = "更新资方放款审批状态")
    @PostMapping("/updateFundPaymentStatus")
    Result<Boolean> updateFundPaymentStatus(@RequestBody @Validated OrderApproveFundPaymentStatusDTO fundStatusDTO) {
        return Result.success(orderService.updateFundStatus(fundStatusDTO));
    }

    @ApiOperation(value = "更新资方终审状态")
    @PostMapping("/updateFundFinalStatus")
    Result<Boolean> updateFundFinalStatus(@RequestBody @Validated FinalApproveFundStatusDTO fundStatusDTO) {
        return Result.success(orderService.updateFundStatus(fundStatusDTO));
    }

    @ApiOperation(value = "更新资方还款状态")
    @PostMapping("/updateFundPlanStatus")
    Result<Boolean> updateFundPlanStatus(@RequestBody @Validated OrderApproveFundPlanStatusDTO fundStatusDTO) {
        return Result.success(orderService.updateFundPlanStatus(fundStatusDTO));
    }


    @ApiOperation(value = "生成二维码")
    @PostMapping("/generateCodeFile")
    ResponseEntity<byte[]> generateCodeFile(@RequestBody GenerateCodeDTO generateCodeDTO, @CurrentUser LoginUser loginUser) {
        return orderService.generateCodeFile(generateCodeDTO, loginUser);
    }
    @ApiOperation(value = "生成二维码Url")
    @PostMapping("/generateCodeUrl")
    public Result<String> generateCodeUrl(@RequestBody GenerateCodeDTO generateCodeDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderService.getQrCodeUrl(generateCodeDTO, loginUser));
    }

    /**
     * 发送消息
     *
     * @param sendMessageDTO 发送消息 DTO
     * @return {@link Result }<{@link String }>
     */
    @PostMapping("/sendMessage")
    public Result<String> sendMessage(@RequestBody SendMessageDTO sendMessageDTO) {
        orderSendMessage.sendWeChatMsg(sendMessageDTO.getOrderId(), sendMessageDTO.getNode(), sendMessageDTO.getStatus());
        return Result.success();
    }

    /**
     * 获取合约状态 DTO
     *
     * @param generateCodeDTO 生成码值DTO
     * @return {@link Result }<{@link OrderDetailVO }>
     */
    @PostMapping("/getContractState")
    public Result<ContractStateVO> getContractState(@RequestBody @Validated ContractStateDTO generateCodeDTO,
                                                    @CurrentUser LoginUser loginUser) {
        return Result.success(orderService.getContractState(generateCodeDTO, loginUser));
    }

    /**
     * 详情信息
     *
     * @param orderDetailDTO 次序详情信息DTO
     * @return {@link Result }<{@link String }>
     */
    @PostMapping("/detail")
    public Result<OrderDetailVO> detail(@RequestBody @Validated OrderDetailDTO orderDetailDTO,
                                        @CurrentUser LoginUser loginUser) {
        return Result.success(orderService.detail(orderDetailDTO,loginUser));
    }


    /**
     * 更新订单资方还款相关信息
     * @param orderId 订单id
     */
    @PostMapping(value = "/updateOrderFundRepayment")
    Result<Boolean> updateOrderFundRepayment(@RequestParam("orderId") Integer orderId) {
        return Result.success(orderService.updateOrderFundRepayment(orderId));
    }

    /**
     * 逾期任务池列表
     *
     * @param overdueOrdersDTO 订单id
     */
    @PostMapping(value = "/overdueOrdersList")
    public Result<Page<OverdueOrdersListVO>> overdueOrdersList(@RequestBody OverdueOrdersDTO overdueOrdersDTO,
                                                               @CurrentUser LoginUser loginUser) {
        return Result.success(orderService.overdueOrdersList(overdueOrdersDTO, loginUser));
    }
    /**
     * 逾期任务池列表导出
     *
     * @param overdueOrdersDTO 订单id
     */
    @PostMapping(value = "/overdueOrdersListExport")
    public void overdueOrdersListExport(@RequestBody OverdueOrdersDTO overdueOrdersDTO,
                                                               @CurrentUser LoginUser loginUser,
                                                                     HttpServletResponse response) {
        orderService.overdueOrdersListExport(overdueOrdersDTO, loginUser,response);
    }
    /**
     * 保存企业营业执照信息
     *
     * @param enterpriseLicenseDTO 企业许可证 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @PostMapping("/save_enterprise_license")
    public Result<Boolean> saveEnterpriseLicense(@RequestBody @Validated EnterpriseLicenseDTO enterpriseLicenseDTO) {
        return Result.success(orderService.saveEnterpriseLicense(enterpriseLicenseDTO));
    }

    /**
     * 更新资方合同
     *
     * @param fundStatusDTO 资方地位DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "更新资方合同")
    @PostMapping("/updateFundContract")
    Result<Boolean> updateFundContract(@RequestBody OrderFundContractDTO fundStatusDTO) {
        return Result.success(orderService.updateFundContract(fundStatusDTO));
    }

    /**
     * 历史订单
     *
     * @param historyOrderDTO 历史记录顺序 DTO
     * @return {@link Result }<{@link Boolean }>
     */
    @ApiOperation(value = "历史订单")
    @PostMapping("/history_order")
    public Result<List<HistoryOrderVO>> historyOrder(@RequestBody @Validated HistoryOrderDTO historyOrderDTO) {
        return Result.success(orderService.historyOrder(historyOrderDTO));
    }

    /**
     * 同人同车关联订单信息
     */
    @ApiOperation(value = "同人同车关联订单信息")
    @GetMapping("/related/checkInfo")
    public Result<List<CheckRelatedInfoVO>> checkRelatedInfo(@RequestParam("orderId") Integer orderId) {
        return Result.success(orderService.checkRelatedInfo(orderId));
    }

    /**
     * 流程自动终止
     */
    @ApiOperation(value = "流程自动终止")
    @PostMapping("/process/terminal")
    public Result<Boolean> processTerminal() {
        return Result.success(orderService.processTerminal());
    }

    /**
     * 订单费用详情列表
     */
    @ApiOperation(value = "订单费用详情列表")
    @PostMapping("/orderFeeDetail/list")
    public Result<Page<OrderFeeDetailVO>> orderFeeDetailList(@RequestBody OrderFeeDetailDTO orderFeeDetailDTO,@CurrentUser LoginUser loginUser) {
        return Result.success(orderFeeDetailService.orderFeeDetailList(orderFeeDetailDTO, loginUser));
    }
    /**
     * 交易记录分页查询
     */
    @ApiOperation(value = "交易记录分页查询")
    @PostMapping("/page/orderFeeDetail/List")
    public Result<Page<OrderFeeDetailListVO>> pageOrderFeeDetailList(@RequestBody @Validated OrderApproveDTO orderApproveDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderFeeDetailService.pageOrderFeeDetailList(orderApproveDTO, loginUser));
    }
    @ApiOperation(value = "导出订单费用详情到excel")
    @PostMapping("/orderFeeDetail/export")
    public void orderFeeDetailExportExcel(@RequestBody OrderFeeDetailDTO orderFeeDetailDTO, @CurrentUser LoginUser loginUser, HttpServletResponse response) {
        orderFeeDetailService.orderFeeDetailExportExcel(orderFeeDetailDTO, loginUser, response);
    }

    @ApiOperation(value = "保存交易明细")
    @PostMapping("/orderFeeDetail/save")
    public Result<Boolean> saveDetail(@RequestBody OrderFeeDetailSaveDTO orderFeeDetailSaveDTO) {
        return Result.success(orderFeeDetailService.saveOrderFeeDetail(orderFeeDetailSaveDTO.getOrderId(),
                orderFeeDetailSaveDTO.getTradingSerialNumber(),
                orderFeeDetailSaveDTO.getAmount(),
                orderFeeDetailSaveDTO.getTradingMethods(),
                orderFeeDetailSaveDTO.getPayer(),
                orderFeeDetailSaveDTO.getPayee(),
                orderFeeDetailSaveDTO.getExpenseType(),
                orderFeeDetailSaveDTO.getStatus(),
                orderFeeDetailSaveDTO.getTerm(),
                orderFeeDetailSaveDTO.getTradingTime(),
                orderFeeDetailSaveDTO.getRemark(),null));
    }

    @ApiOperation(value = "获取审批原因")
    @PostMapping("/getApprovalReason")
    public Result<OrderApprovalReasonStagingVO> getApprovalReason(@RequestBody OrderApprovalReasonDTO orderApprovalReasonDTO) {
        return Result.success(orderApprovalReasonStagingService.getApprovalReason(orderApprovalReasonDTO));
    }

    @ApiOperation(value = "保存审批原因")
    @PostMapping("/saveApprovalReason")
    public Result<Boolean> saveApprovalReason(@RequestBody OrderApprovalReasonStagingEntity orderApprovalReasonStagingEntity) {
        return Result.success(orderApprovalReasonStagingService.saveApprovalReason(orderApprovalReasonStagingEntity));
    }

    /**
     * 暂存订单信息
     *
     * @param saveOrderInfoDTO 保存订单信息DTO
     * @return 订单实体
     */
    @PostMapping("stagingOrderInfo")
    public Result<OrderInfoEntity> StagingOrderInfo(@RequestBody SaveOrderInfoDTO saveOrderInfoDTO) {
        return Result.success(orderService.StagingOrderInfo(saveOrderInfoDTO));

    }
    /**
     * 获取用户学历信息
     */
    @ApiOperation(value = "获取用户学历信息")
    @PostMapping("/getUserEducation")
    public Result<String> getUserEducation(@RequestBody QueryVerifyV4DTO dto) {
        return Result.success(xueliService.getEducation(dto));
    }

    /**
     * 数字化获取用户学历信息
     */
    @ApiOperation(value = "获取用户学历信息")
    @PostMapping("/digitizationGetUserEducation")
    public Result<String> digitizationGetUserEducation(@RequestBody QueryVerifyV4DTO dto) {
        return Result.success(xueliService.digitizationGetUserEducation(dto));
    }
    /**
     * 获取用户婚姻情况
     */
    @ApiOperation(value = "获取用户婚姻情况")
    @PostMapping("/getUserMarriage")
    public Result<String> getUserMarriage(@RequestBody GetUserMarriageDTO dto) {
        return Result.success(xueliService.getUserMarriage(dto));
    }

    /**
     * 合同签约-发起合同审批流程
     */
    @ApiOperation(value = "合同签约-发起合同审批流程")
    @PostMapping("/signContract/process")
    public Result<Boolean> process(@RequestBody ContractApprovalDTO contractApprovalDTO,
                                   @CurrentUser LoginUser loginUser) {
        return Result.success(orderService.process(contractApprovalDTO,loginUser));
    }

    /**
     * 合同签约-更新合同审批流程节点
     */
    @ApiOperation(value = "合同签约-更新合同审批流程节点")
    @PostMapping("/signContract/updateProcess")
    public Result<Boolean> updateProcess() {
        return Result.success(orderService.updateProcess());
    }

    /**
     * 查询是否购买车损险
     */
    @ApiOperation(value = "查询是否购买车损险")
    @PostMapping("/getIsInsurance/{orderId}")
    public Result<Boolean> getIsInsurance(@PathVariable("orderId") Integer orderId) {
        return Result.success(orderService.getIsInsurance(orderId));
    }



    /**
     * 生成短链接
     * @param shortUrlDTO
     * @return 短链接
     */
    @ApiOperation("生成短链接")
    @PostMapping("/generateShortUrl")
    public Result<String> generateShortUrl(@RequestBody ShortUrlDTO shortUrlDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderService.generateShortUrl(shortUrlDTO, loginUser));
    }

    /**
     * 发送订单拒绝事件
     *
     * @param orderId 订单ID
     * @param rejectReason 拒绝原因
     * @return 是否发送成功
     */
    @ApiOperation(value = "发送订单拒绝事件")
    @PostMapping("/sendRejectEvent")
    public Result<Boolean> sendOrderRejectEvent(@RequestParam("orderId") Integer orderId,
                                              @RequestParam("rejectReason") String rejectReason) {
        return Result.success(orderService.sendOrderRejectEvent(orderId, rejectReason));
    }
}
