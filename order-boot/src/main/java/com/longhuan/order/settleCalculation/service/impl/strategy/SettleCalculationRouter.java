package com.longhuan.order.settleCalculation.service.impl.strategy;

import com.longhuan.order.pojo.dto.SettleCalculationContext;
import com.longhuan.order.pojo.vo.SettleCalculationVO;
import com.longhuan.order.settleCalculation.service.impl.strategy.impl.Annotation.SupportSettleType;
import com.longhuan.order.settleCalculation.service.impl.strategy.impl.enums.SettleType;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SettleCalculationRouter {


    private final Map<SettleType, SettleCalculationStrategy> strategies;

    @Autowired
    public SettleCalculationRouter(List<SettleCalculationStrategy> strategies) {
        this.strategies = strategies.stream()
                .collect(Collectors.toMap(strategy -> {
                    Class<?> clazz = AopUtils.getTargetClass(strategy);
                    SupportSettleType annotation = clazz.getAnnotation(SupportSettleType.class);
                    if (annotation == null) {
                        throw new IllegalArgumentException("策略 [" + clazz.getName() + "] 缺少 @SupportSettleType 注解");
                    }
                    return annotation.value();
                }, Function.identity()));
        for (SettleType type : SettleType.values()) {
            if (!this.strategies.containsKey(type)) {
                throw new IllegalStateException("缺少对应策略的结算类型: " + type);
            }
        }
    }

    public SettleCalculationStrategy route(SettleCalculationContext context) {
        SettleType key = SettleType.getType(context);
        return strategies.get(key);
    }
}
