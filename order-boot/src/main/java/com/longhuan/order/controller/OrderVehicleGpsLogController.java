package com.longhuan.order.controller;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.OrderApproveDTO;
import com.longhuan.order.pojo.dto.OrderVehicleGpsLogDTO;
import com.longhuan.order.pojo.entity.OrderVehicleGpsLogEntity;
import com.longhuan.order.pojo.vo.OrderVehicleGpsLogVO;
import com.longhuan.order.service.OrderVehicleGpsLogService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 车辆定位记录
 *
 * <AUTHOR>
 * @date 2025/5/7 16:37
 */
@RestController
@RequestMapping("/api/v1/orderVehicleGpsLog")
@RequiredArgsConstructor
public class OrderVehicleGpsLogController {
    private static final Logger log = LoggerFactory.getLogger(OrderVehicleGpsLogController.class);
    private final OrderVehicleGpsLogService orderVehicleGpsLogService;

    /**
     * 查询订单车辆定位信息列表
     * @param orderVehicleGpsLogDTO
     * @param loginUser
     * @return
     */
    @ApiOperation(value = "查询订单车辆定位信息列表")
    @PostMapping("/page/orderVehicleGpsLogList")
    public Result<Page<OrderVehicleGpsLogVO>> pageOrderVehicleGpsLogList(@RequestBody @Validated OrderVehicleGpsLogDTO orderVehicleGpsLogDTO,
                                                                         @CurrentUser LoginUser loginUser) {
        return Result.success(orderVehicleGpsLogService.pageOrderVehicleGpsLogList(orderVehicleGpsLogDTO,loginUser));
    }

    /**
     * 查询数据库最新一条定位信息
     * @return
     */
    @ApiOperation(value = "查询数据库最新一条定位信息")
    @PostMapping("/queryLatestPositionByCaseInfoId")
    public Result<Object> queryLatestPositionByCaseInfoId(@RequestBody @Validated OrderVehicleGpsLogDTO orderVehicleGpsLogDTO,
                                                   @CurrentUser LoginUser loginUser) {

        if (ObjUtil.isNull(orderVehicleGpsLogDTO.getCaseInfoId())){
            return Result.failed("必要参数为空");
        }
        OrderVehicleGpsLogEntity entity = orderVehicleGpsLogService.queryLatestPositionByCaseInfoId(orderVehicleGpsLogDTO, loginUser);
        if (ObjUtil.isNotNull(entity)){
            return Result.success(entity);
        }else {
            return Result.failed("无法获取实时位置");
        }
    }

    /**
     * 手动获取车辆的最新定位信息
     * @return
     */
    @ApiOperation(value = "手动获取最新定位信息")
    @PostMapping("/getCarLatestPosition")
    public Result<Object>  getCarLatestPosition(@RequestBody @Validated OrderVehicleGpsLogDTO orderVehicleGpsLogDTO,
                                                  @CurrentUser LoginUser loginUser) {

        if (ObjUtil.isNull(orderVehicleGpsLogDTO.getCaseInfoId())){
            return Result.failed("必要参数为空");
        }
        List<OrderVehicleGpsLogEntity> list = orderVehicleGpsLogService.getCarLatestPosition(orderVehicleGpsLogDTO, loginUser);
        if (ObjUtil.isNotNull(list) && list.size() > 0){
            log.info("OrderVehicleGpsLogController.getCarLatestPosition list size：{}",  list.size());
            return Result.success(list.get(0));
        }else {
            return Result.failed("无法获取实时位置");
        }
    }

    /**
     * 自动获取车辆的最新定位信息
     * @return
     */
    @ApiOperation(value = "自动获取车晓最新定位信息")
    @PostMapping("/autoGetCarLatestPosition")
    public Result<Object> autoGetCarLatestPosition(@RequestBody @Validated OrderVehicleGpsLogDTO orderVehicleGpsLogDTO,
                                               @CurrentUser LoginUser loginUser) {

        if (ObjUtil.isNull(orderVehicleGpsLogDTO.getOrderId()) || ObjUtil.isNull(orderVehicleGpsLogDTO.getOrderSource())){
            return Result.failed("必要参数为空");
        }
        List<OrderVehicleGpsLogEntity> list = orderVehicleGpsLogService.autoGetCarLatestPosition(orderVehicleGpsLogDTO, loginUser);
        if (ObjUtil.isNotNull(list) && list.size() > 0){
            log.info("OrderVehicleGpsLogController.autoGetCarLatestPosition list size：{}",  list.size());
            return Result.success(list.get(0));
        }else {
            return Result.failed("无法获取实时位置");
        }
    }

}
