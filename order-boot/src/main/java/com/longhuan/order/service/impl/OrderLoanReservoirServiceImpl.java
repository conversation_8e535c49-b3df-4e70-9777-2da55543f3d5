package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.enums.States;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.OrderLoanReservoirDTO;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.OrderApproveListVO;
import com.longhuan.order.pojo.vo.OrderLoanReservoirListVO;
import com.longhuan.order.service.OrderLoanReservoirService;
import com.longhuan.user.pojo.vo.UserStoreVO;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * LoanReservoirRulesServiceImol
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderLoanReservoirServiceImpl implements OrderLoanReservoirService {

    private final OrderLoanReservoirMapper orderLoanReservoirMapper;
    private final LoanReservoirRulesMapper loanReservoirRulesMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final UserFeign userFeign;
    private final OrderAmountMapper orderAmountMapper;

    @Override
    public Page<OrderLoanReservoirListVO> list(OrderLoanReservoirDTO dto, LoginUser loginUser) {

        Objects.requireNonNull(loginUser, "用户信息不存在");
        // 数据权限
        List<Integer> roleIds = loginUser.getRoleIds();

        MPJLambdaWrapper<OrderLoanReservoirEntity> queryWrapper = new MPJLambdaWrapper<OrderLoanReservoirEntity>()
                .select(OrderInfoEntity::getOrderNumber, OrderInfoEntity::getCustomerName,
                        OrderInfoEntity::getCustomerPhone, OrderInfoEntity::getCurrentNode,
                        OrderInfoEntity::getApplyAmount,
                        OrderInfoEntity::getTerm,
                        OrderInfoEntity::getProductName, OrderInfoEntity::getSource, OrderInfoEntity::getManagerId,
                        OrderInfoEntity::getPaymentTime,
                        OrderInfoEntity::getSourceType,
                        OrderInfoEntity::getRegionId,
                        OrderInfoEntity::getRegionName,
                        OrderInfoEntity::getTeamId,
                        OrderInfoEntity::getTeamName,
                        OrderInfoEntity::getPlanState,
                        OrderInfoEntity::getIsOverdue,
                        OrderInfoEntity::getIsRepurchase,
                        OrderInfoEntity::getIsPushThirdParty,
                        OrderInfoEntity::getState,
                        OrderInfoEntity::getStoreName
                )
                .selectAs(OrderLoanReservoirEntity::getId, OrderLoanReservoirListVO::getId)
                .selectAs(OrderLoanReservoirEntity::getContractAuditTime, OrderLoanReservoirListVO::getContractAuditTime)
                .selectAs(OrderInfoEntity::getId, OrderLoanReservoirListVO::getOrderId)
                .selectAs(OrderLoanReservoirEntity::getRuleIds, OrderLoanReservoirListVO::getRuleIds)
                .selectAs(OrderInfoEntity::getPreApplyTime, OrderApproveListVO::getApplyDate)
                .selectAs(OrderInfoEntity::getVehicleNumber, OrderApproveListVO::getVehicleNumber)
                .selectAs(OrderInfoEntity::getRiskUserId, OrderApproveListVO::getRisiUserId)
                .selectAs(OrderInfoEntity::getGpsState, OrderApproveListVO::getGpsState)
                .selectAs(FundInfoEntity::getName, OrderApproveListVO::getFundName)
                .selectAs(OrderInfoEntity::getFundId, OrderApproveListVO::getFundId)
                .innerJoin(OrderInfoEntity.class, OrderInfoEntity::getId, OrderLoanReservoirEntity::getOrderId)
                .and(q -> q.eq(OrderInfoEntity::getDeleteFlag, 0))
                .and(q -> q.eq(OrderInfoEntity::getCurrentNode, States.FUNDS_PAYMENT_APPROVAL.getNode()))
                .leftJoin(FundInfoEntity.class, FundInfoEntity::getId, OrderInfoEntity::getFundId)
                .eq(ObjUtil.isNotNull(dto.getDeptId()),OrderInfoEntity::getDeptId,dto.getDeptId())
                .eq(ObjUtil.isNotEmpty(dto.getRegionId()),OrderInfoEntity::getRegionId,dto.getRegionId())
                .eq(ObjUtil.isNotNull(dto.getSourceType()),OrderInfoEntity::getSourceType,dto.getSourceType())
                .eq(ObjUtil.isNotEmpty(dto.getOrderNumber()), OrderInfoEntity::getOrderNumber, dto.getOrderNumber())
                .eq(ObjUtil.isNotNull(dto.getFundId()), OrderInfoEntity::getFundId, dto.getFundId())
                .like(!StrUtil.isEmpty(dto.getCustomerName()), OrderInfoEntity::getCustomerName, dto.getCustomerName())
                .like(StrUtil.isNotBlank(dto.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, dto.getVehicleNumber())
                //合同审核时间
                .between(dto.getContractAuditTimeStart() != null && dto.getContractAuditTimeEnd() != null,
                        OrderLoanReservoirEntity::getContractAuditTime,
                        dto.getContractAuditTimeStart(),dto.getContractAuditTimeEnd())
                .eq(OrderLoanReservoirEntity::getDeleteFlag,0)
                .eq(OrderLoanReservoirEntity::getReservoirStatus,1)
                .in(CollUtil.isNotEmpty(dto.getIdList()),OrderLoanReservoirEntity::getId,dto.getIdList())
                .orderByDesc(OrderLoanReservoirEntity::getCreateTime);

        //规则编号查询
        if (StrUtil.isNotBlank(dto.getRuleNumber())) {
           List<LoanReservoirRulesEntity> rulesEntityList = loanReservoirRulesMapper.selectList(new LambdaQueryWrapper<LoanReservoirRulesEntity>()
                           .like(LoanReservoirRulesEntity::getRuleNumber, dto.getRuleNumber())
                           .eq(LoanReservoirRulesEntity::getDeleteFlag, 0)
                   );
            if (CollUtil.isEmpty(rulesEntityList)) {
                return new Page<>(dto.getPageNum(), dto.getPageSize(), 0);
            }else {
                List<Integer> ruleIdList = rulesEntityList.stream().map(LoanReservoirRulesEntity::getId).collect(Collectors.toList());
                if (ObjUtil.isNotEmpty(ruleIdList)) {
                    queryWrapper.and(wrapper -> {
                        for (Integer ruleId : ruleIdList) {
                            wrapper.or().apply("{0} = ANY(STRING_TO_ARRAY(rule_ids, ','))", Integer.toString(ruleId));
                        }
                    });
                }
            }
        }
        Page<OrderLoanReservoirListVO> pageList = orderLoanReservoirMapper.selectJoinPage(new Page<>(dto.getPageNum(), dto.getPageSize()), OrderLoanReservoirListVO.class, queryWrapper);
        List<OrderLoanReservoirListVO> records = pageList.getRecords();

        // 收集所有需要查询的 orderId
        Set<Integer> orderIds = records.stream().map(OrderLoanReservoirListVO::getOrderId).collect(Collectors.toSet());
        Map<Integer, List<OrderAmountEntity>> orderAmountMap;
        if (CollUtil.isNotEmpty(records)) {
            orderAmountMap = orderAmountMapper.selectList(new LambdaQueryWrapper<OrderAmountEntity>()
                            .in(OrderAmountEntity::getOrderId, orderIds)
                            .eq(OrderAmountEntity::getDeleteFlag, 0)
                            .orderByDesc(OrderAmountEntity::getCreateTime))
                    .stream()
                    .collect(Collectors.groupingBy(OrderAmountEntity::getOrderId,
                            Collectors.collectingAndThen(Collectors.toList(), list -> list.isEmpty() ? Collections.emptyList() : Collections.singletonList(list.get(0)))));
        } else {
            orderAmountMap = new HashMap<>();
        }
        // 查询经办人信息
        List<Integer> manageIds = records.stream().map(OrderLoanReservoirListVO::getManagerId).filter(Objects::nonNull).toList();
        Result<List<UserStoreVO>> listResult = userFeign.searchUserStoreBatch(manageIds);
        Map<Integer, UserStoreVO> managerInfos;
        if (Result.isSuccess(listResult)) {
            managerInfos =
                    listResult.getData().stream().collect(Collectors.toMap(UserStoreVO::getUserId, item -> item));
        } else {
            managerInfos = null;
        }

        records.forEach(record -> {
            String ruleIds = record.getRuleIds();
            List<Integer> ruleList = Arrays.stream(ruleIds.split(",")).map(String::trim).filter(s -> !s.isEmpty())
                    .map(s -> {
                        try {
                            return Integer.valueOf(s);
                        } catch (NumberFormatException e) {
                            log.warn("Invalid regionId format: {}", s);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            List<LoanReservoirRulesEntity> rulesEntityList = loanReservoirRulesMapper.selectList(new LambdaQueryWrapper<LoanReservoirRulesEntity>()
                    .in(LoanReservoirRulesEntity::getId, ruleList)
                    .eq(LoanReservoirRulesEntity::getDeleteFlag, 0)
            );
            String ruleNumbers = rulesEntityList.stream().map(LoanReservoirRulesEntity::getRuleNumber).collect(Collectors.joining(","));
            record.setRuleNumbers(ruleNumbers);


            List<OrderAmountEntity> orderAmountEntities = orderAmountMap.getOrDefault(record.getOrderId(), Collections.emptyList());
            if (CollUtil.isNotEmpty(orderAmountEntities)) {
                OrderAmountEntity orderAmountEntity = orderAmountEntities.get(0);
                record.setCustomerAmount(orderAmountEntity.getCustomerConfirmAmount());
                record.setApplyAmount(orderAmountEntity.getCustomerConfirmAmount());
            }

            if (ObjUtil.isNotNull(managerInfos) && record.getManagerId() != null) {
                UserStoreVO userInfoVOS = managerInfos.get(record.getManagerId());
                if (ObjUtil.isNotNull(userInfoVOS)) {
                    record.setManagerName(userInfoVOS.getName());
                    record.setStoreName(userInfoVOS.getStore());
                }

            }

        });
        return pageList;
    }

    @Override
    public Boolean batchPushLoan(OrderLoanReservoirDTO orderLoanReservoirDTO, LoginUser loginUser) {
        List<Integer> idList = orderLoanReservoirDTO.getIdList();
        for (Integer id : idList){
            int update = orderLoanReservoirMapper.update(new LambdaUpdateWrapper<OrderLoanReservoirEntity>()
                    .set(OrderLoanReservoirEntity::getReservoirStatus, 2)
                    .eq(OrderLoanReservoirEntity::getId, id)
                    .eq(OrderLoanReservoirEntity::getDeleteFlag, 0)
                    .eq(OrderLoanReservoirEntity::getReservoirStatus, 1)
            );
        }
        return true;
    }

    /**
     * 驳回
     * @param orderId
     * @param loginUser
     * @return
     */
    @Override
    public Boolean reject(Integer orderId, LoginUser loginUser) {
        log.info("OrderLoanReservoirServiceImpl.reject start orderId  :{}", orderId);
        Assert.notNull(orderId, () -> new BusinessException("订单ID不能为空"));
        OrderLoanReservoirEntity orderLoanReservoirEntity = orderLoanReservoirMapper.selectOne(new LambdaQueryWrapper<OrderLoanReservoirEntity>()
                .eq(OrderLoanReservoirEntity::getOrderId, orderId)
                .eq(OrderLoanReservoirEntity::getReservoirStatus, 1)
                .eq(OrderLoanReservoirEntity::getDeleteFlag, 0)
                .orderByDesc(OrderLoanReservoirEntity::getCreateTime).last("limit 1")
        );
        Assert.notNull(orderLoanReservoirEntity, () -> new BusinessException("放款蓄水池拦截订单不存在"));

        log.info("OrderLoanReservoirServiceImpl.reject orderId :{},orderLoanReservoirEntity :{}", JSONUtil.toJsonStr(orderLoanReservoirEntity));

        int update = orderLoanReservoirMapper.update(new LambdaUpdateWrapper<OrderLoanReservoirEntity>()
                .set(OrderLoanReservoirEntity::getReservoirStatus, 3)
                .eq(OrderLoanReservoirEntity::getId, orderLoanReservoirEntity.getId())
                .eq(OrderLoanReservoirEntity::getDeleteFlag, 0)
                .eq(OrderLoanReservoirEntity::getReservoirStatus, 1)
        );
        if (update > 0){
            log.info("OrderLoanReservoirServiceImpl.reject success orderId :{},orderLoanReservoirEntity :{} success", JSONUtil.toJsonStr(orderLoanReservoirEntity));
            return true;
        }else {
            return false;
        }
    }

    @Override
    public Map<String,Object> importUploadData(MultipartFile file, LoginUser loginUser) {
        if (ObjUtil.isEmpty(file)){
            throw new BusinessException("文件不可为空");
        }
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            List<Map<Integer, String>> orderNumberMapList = EasyExcel.read(inputStream).sheet(0).doReadSync();
            if (CollUtil.isEmpty(orderNumberMapList)){
                throw new BusinessException("文件解析无数据，请确认上传文件数据");
            }
            List<String> orderNumberList = orderNumberMapList.stream().map(map -> map.get(0)).collect(Collectors.toList());
            if (CollUtil.isEmpty(orderNumberList)){
                throw new BusinessException("文件解析无数据，请确认上传文件数据");
            }
            log.info("FileResourceServiceImpl.importUploadData orderNumberList:{}", JSONUtil.toJsonStr(orderNumberList));
            //1.根据orderNumber 查询orderId
            List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
                    .in(OrderInfoEntity::getOrderNumber,orderNumberList)
                    .eq(OrderInfoEntity::getDeleteFlag, 0)
            );
            if (CollUtil.isEmpty(orderInfoEntityList)){
                throw new BusinessException("订单编号不存在");
            }
            //2.根据orderId 查询订单放款蓄水池订单
            List<OrderLoanReservoirEntity> orderLoanList = new ArrayList<>();
            List<Integer> orderIdList = orderInfoEntityList.stream().map(OrderInfoEntity::getId).collect(Collectors.toList());
            orderLoanList = orderLoanReservoirMapper.selectList(new LambdaQueryWrapper<OrderLoanReservoirEntity>()
                    .in(OrderLoanReservoirEntity::getOrderId, orderIdList)
                    .eq(OrderLoanReservoirEntity::getReservoirStatus, 1)
                    .eq(OrderLoanReservoirEntity::getDeleteFlag, 0)
            );
            List<String> loanNumberList = orderLoanList.stream().map(OrderLoanReservoirEntity::getOrderNumber).collect(Collectors.toList());

            List<String> notInReservoir = orderNumberList.stream()
                    .filter(orderNum -> !loanNumberList.contains(orderNum))
                    .collect(Collectors.toList());

            Map<String,Object> result = new HashMap<>();
            result.put("count", loanNumberList.size());
            result.put("selectedList", loanNumberList);
            List<Integer> orderLoanIds;//订单蓄水池
            List<Integer> orderIds;

            if (loanNumberList == null || loanNumberList.isEmpty()) {
                orderLoanIds = new ArrayList<>();
                orderIds = new ArrayList<>();
            } else {
                Set<String> orderNumberSet = new HashSet<>(loanNumberList);
                orderLoanIds = orderLoanList.stream()
                        .filter(entity -> entity.getOrderNumber() != null && orderNumberSet.contains(entity.getOrderNumber()))
                        .map(OrderLoanReservoirEntity::getId)
                        .collect(Collectors.toList());
                orderIds = orderLoanList.stream()
                        .filter(entity -> entity.getOrderNumber() != null && orderNumberSet.contains(entity.getOrderNumber()))
                        .map(OrderLoanReservoirEntity::getOrderId)
                        .collect(Collectors.toList());
            }
            result.put("selectedList", orderLoanIds);

            if (CollUtil.isNotEmpty(orderIds)){
                //计算合计值
                List<OrderAmountEntity> orderAmountEntityList = orderAmountMapper.selectList(new LambdaQueryWrapper<OrderAmountEntity>()
                        .select(OrderAmountEntity::getCustomerConfirmAmount)
                        .in(OrderAmountEntity::getOrderId, orderIds)
                        .eq(OrderAmountEntity::getDeleteFlag, 0)
                );
                BigDecimal totalAmount = orderAmountEntityList.stream()
                        .map(OrderAmountEntity::getCustomerConfirmAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                result.put("totalAmount", totalAmount);
            }

            if (CollUtil.isNotEmpty(notInReservoir)) {
                log.warn("以下订单编号在蓄水池中未找到: " + String.join(",", notInReservoir));
                result.put("success", false);
                result.put("noSelectedList", notInReservoir);
            }else {
                result.put("success", true);
            }
            log.info("OrderLoanReservoirServiceImpl.importUploadData End result:{}", JSONUtil.toJsonStr(result));
            return result;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String downloadTemplate(HttpServletResponse response,LoginUser loginUser) {
        try {
            String fileName = "导入模板.xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=" +
                    URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("订单编号模板");

            // 创建表头行
            Row headerRow = sheet.createRow(0);
            Cell headerCell = headerRow.createCell(0);
            headerCell.setCellValue("订单编号");

            // 设置样式（可选）
            CellStyle headerStyle = workbook.createCellStyle();
            Font font = workbook.createFont();
            font.setBold(true);
            headerStyle.setFont(font);
            headerCell.setCellStyle(headerStyle);

            // 自动调整列宽
            sheet.autoSizeColumn(0);

            // 写入响应输出流
            workbook.write(response.getOutputStream());
            workbook.close();


            return "success";
        } catch (IOException e) {
            log.error("导出模板失败", e);
            throw new BusinessException("导出模板失败: " + e.getMessage());
        }
    }

}
