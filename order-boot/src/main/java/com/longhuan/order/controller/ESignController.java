package com.longhuan.order.controller;


import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.UserAuthReqDTO;

import com.longhuan.order.pojo.dto.esign.*;
import com.longhuan.order.pojo.vo.esign.AuthFaceDetailVO;
import com.longhuan.order.pojo.vo.esign.AuthPaceUrlVO;
import com.longhuan.order.pojo.vo.esign.OrgAccountVO;
import com.longhuan.order.service.ESignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequiredArgsConstructor
@Api("e签宝接口")
@RequestMapping("/api/v1/eSign")
public class ESignController{


    private final ESignService eSignService;

    /**
     * 初始化客户端
     * @return
     */
    @ApiOperation(value = "初始化客户端")
    @PostMapping("/regist/client")
    public Result<Boolean> registClient() {
        return Result.success(eSignService.registClient());
    }

    /**
     * 创建个人签署账号
     * @param userAuthReqDTO
     * @return
     */
    @ApiOperation(value = "创建个人签署账号")
    @PostMapping("/add/person/account")
    public Result<String> addPersonAccount(@RequestBody @Validated UserAuthReqDTO userAuthReqDTO) {
        return Result.success(eSignService.addPersonAccount(userAuthReqDTO));
    }

    /**
     * 创建企业签署账号
     * @param orgAccountDTO
     * @return
     */
    @ApiOperation(value = "创建企业签署账号")
    @PostMapping("/add/org/account")
    public Result<OrgAccountVO> addOrgAccount(@RequestBody @Validated OrgAccountDTO orgAccountDTO) {
        return Result.success(eSignService.addOrgAccount(orgAccountDTO));
    }

    /**
     * 个人核身认证-人脸识别
     */
    @ApiOperation(value = "个人核身认证-人脸识别")
    @PostMapping("/identity/auth/face")
    Result<AuthPaceUrlVO> identityFace(@RequestBody @Validated UserAuthReqDTO userAuthReqDTO) {
        return Result.success(eSignService.identityFace(userAuthReqDTO));
    }

    /**
     * 查询认证信息
     * @param userAuthReqDTO
     * @return
     */
    @ApiOperation(value = "查询认证信息")
    @PostMapping("/auth/common/detail")
    Result<AuthFaceDetailVO> getAuthCommonDetail(@RequestBody @Validated UserAuthReqDTO userAuthReqDTO) {
        return Result.success(eSignService.getAuthCommonDetail(userAuthReqDTO));
    }

    @ApiOperation(value = "签署文件预览")
    @PostMapping("/preview/file")
    public ResponseEntity<byte[]> previewFile(@RequestBody @Validated UserAuthReqDTO userAuthReqDTO) {
        return eSignService.previewFile(userAuthReqDTO);
    }
    @ApiOperation(value = "【线上】发起企业授权书签署任务")
    @PostMapping("/initiated/enterprise/powerOfAttorney")
    public Result<String> signingOfTheEnterprisePowerOfAttorney(@RequestBody SignTheAuthorizationDTO signTheAuthorizationDTO){
        return Result.success(eSignService.signingOfTheEnterprisePowerOfAttorney(signTheAuthorizationDTO));
    }
    @ApiOperation(value = "【线上】获取授权书签署任务链接")
    @PostMapping("/obtain/authorization/sign/task/link")
    Result<String> getTheLinkToAuthorizeTheSigningTask(@RequestBody ESignAuthIdDTO eSignAuthIdDTO){
        return Result.success(eSignService.getTheLinkToAuthorizeTheSigningTask(eSignAuthIdDTO));
    }
    @ApiOperation(value = "查询授权结果")
    @PostMapping("/query/authorizationResults")
    public Result<String> queryAuthorizationResults(@RequestBody ESignAuthIdDTO eSignAuthIdDTO) {
        return Result.success(eSignService.queryAuthorizationResults(eSignAuthIdDTO.getAuthId()));
    }


    /**
     * 创建授权书
     *
     * @return {@link Result }<{@link String }>
     */
    @ApiOperation(value = "创建授权书")
    @PostMapping("/create/auth/file")
    public Result<String> createAuthFile(@RequestBody SignAuthDTO signContractDTO) {
        return Result.success(eSignService.createAuthFile(signContractDTO));
    }

    /**
     * 查询个人刷脸状态
     * @param userAuthReqDTO
     * @return
     */
    @ApiOperation(value = "查询个人刷脸状态")
    @PostMapping("/auth/face/detail")
    Result<AuthFaceDetailVO> getAuthFaceDetail(@RequestBody @Validated UserAuthReqDTO userAuthReqDTO) {
        return Result.success(eSignService.getAuthFaceDetail(userAuthReqDTO));
    }

    /**
     * 签署授权书
     * @param userAuthReqDTO
     * @return
     */
    @ApiOperation(value = "签署授权书")
    @PostMapping("/sign/pre/auth/file")
    public Result<Boolean> signAuthFile(@RequestBody @Validated UserAuthReqDTO userAuthReqDTO) {
        return Result.success(eSignService.signAuthFile(userAuthReqDTO));
    }

    /**
     * 创建合同签署
     * @param signContractDTO
     * @return
     */
    @ApiOperation(value = "创建合同签署")
    @PostMapping("/sign/contract")
    public Result<Boolean> signContract(@RequestBody @Validated SignContractDTO signContractDTO) {
        return Result.success(eSignService.signContract(signContractDTO));
    }
}