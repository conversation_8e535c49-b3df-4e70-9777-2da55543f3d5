package com.longhuan.approve.api;


import com.longhuan.approve.api.constants.LanHaiMortgageEnums;
import com.longhuan.approve.api.pojo.dto.*;
import com.longhuan.approve.api.pojo.dto.changyin.*;
import com.longhuan.approve.api.pojo.dto.fumin.FuMinCommonReqDTO;
import com.longhuan.approve.api.pojo.dto.fumin.FuMinQueryGuarResultResDTO;
import com.longhuan.approve.api.pojo.dto.fumin.FuMinTradeGuarApplyResDTO;
import com.longhuan.approve.api.pojo.dto.lanhai.*;
import com.longhuan.approve.api.pojo.dto.lanhai.yibao.YiBaoAgreementConfirmDTO;
import com.longhuan.approve.api.pojo.dto.lanhai.yibao.YiBaoAgreementSignDTO;
import com.longhuan.approve.api.pojo.dto.zhongheng.coppercardept.HengTongCopperCarDeptMortgageCancelDTO;
import com.longhuan.approve.api.pojo.dto.zhongheng.coppercardept.SearchOrderStatusDTO;
import com.longhuan.approve.api.pojo.dto.zhongheng.hengtong.HengTongATMCardInfoSyncDTO;
import com.longhuan.approve.api.pojo.dto.zhongheng.hengtong.HengTongPreRepayPlanDTO;
import com.longhuan.approve.api.pojo.vo.*;
import com.longhuan.approve.api.pojo.vo.changyin.ChangLPRResDTO;
import com.longhuan.approve.api.pojo.vo.changyin.ChangYinContractPreviewVO;
import com.longhuan.approve.api.pojo.vo.changyin.ChangYinLoanTrialResponseDTO;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiAfterReplenishVerifyResponse;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiContractSignApplyResponse;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiRepayCalcResponse;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiTrialRepayResponse;
import com.longhuan.approve.api.pojo.vo.lanhai.yibao.YiBaoAgreementConfirmVO;
import com.longhuan.approve.api.pojo.vo.lanhai.yibao.YiBaoAgreementSignVO;
import com.longhuan.approve.api.pojo.vo.yingfeng.YingFengQuerySignResultVO;
import com.longhuan.approve.api.pojo.vo.yingfeng.YingFengRepayCalcVO;
import com.longhuan.approve.api.pojo.vo.yingfeng.YingFengSendSignVO;
import com.longhuan.approve.api.pojo.vo.zhongheng.*;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.result.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 资方审批 API
 */
public interface ApproveApi {
    /**
     * 计算
     *
     * @param calculationDTO 计算 DTO
     * @return {@link Result }<{@link RepaymentVO }>
     */
    @PostMapping("/api/v1/repayment/calculation")
    Result<RepaymentVO> calculation(@RequestBody CalculationDTO calculationDTO);


    @ApiOperation(value = "还款计划结清计算")
    @PostMapping("/api/v1/repayment/calLoanSettlement")
    Result<LoanSettlementVO> calLoanSettlement(@Validated @RequestBody LoanSettlementDTO dto);

    /**
     * 资方预审批申请
     *
     * @param fundApprovePreDTO 基金批准 pre dto
     * @return {@link Result }<{@link String }>
     */
    @ApiOperation(value = "预审批")
    @PostMapping("/api/v1/fund/approve/pre")
    Result<String> fundApprovePre(@Validated @RequestBody FundApprovePreDTO fundApprovePreDTO);

    /**
     * 4.1.3	借款试算
     */
    @ApiOperation(value = "4.1.3 借款试算")
    @GetMapping("/api/v1/fumin/pds/loan/yxLoanCalculate")
    Result<FuMinYxLoanCalculateVO> fuminYxLoanCalculate(@RequestParam("orderId") Integer orderId);

    /**
     * 资方终审申请
     *
     * @param fundApproveFinalDTO 资方审批最后DTO
     * @return {@link Result }<{@link String }>
     */
    @ApiOperation(value = "终审")
    @PostMapping("/api/v1/fund/approve/final")
    Result<String> fundApproveFinal(@Validated @RequestBody FundApproveFinalDTO fundApproveFinalDTO);

    @ApiOperation(value = "授信额度降额")
    @PostMapping("/api/v1/fund/approve/amount/change")
    Result<String> fundAmountChange(@RequestBody FundAmountChangeDTO fundAmountChangeDTO);

    /**
     * 资方终审申请
     *
     * @param fundApproveCancelDTO 资方审批最后DTO
     * @return {@link Result }<{@link String }>
     */
    @ApiOperation(value = "终审")
    @PostMapping("/api/v1/fund/approve/cancel")
    Result<String> fundApproveCancel(@RequestBody FundApproveCancelDTO fundApproveCancelDTO);


    @ApiOperation(value = "合同上传给资方")
    @PostMapping("/api/v1/fund/contract/upload")
    Result<Boolean> uploadContract(@Validated @RequestBody YingFengContractDTO yingFengContractDTO);

    @ApiOperation(value = "绑卡签约申请")
    @PostMapping("/api/v1/yingfeng/bindCardApply")
    Result<YingFengBindCardApplyVO> yingFengBindCardApply(@Validated @RequestBody YingFengBindCardApplyDTO yingFengBindCardApplyDTO);

    @ApiOperation(value = "绑卡验证码校验")
    @PostMapping("/api/v1/yingfeng/bindCardVerifyCode")
    Result<YingFengBindCardVerifyCodeVO> yingFengBindCardVerifyCode(@Validated @RequestBody YingFengBindCardVerifyCodeDTO yingFengBindCardVerifyCodeDTO);

    @ApiOperation(value = "16.2通联签约发送验证码")
    @PostMapping("/api/v1/zhongheng/tYTLFaSongYZM")
    ZhongHengApiResult<ZhongHengTYTLFaSongYZMVO> zhongHengTYTLFaSongYZM(@RequestBody @Validated ZhongHengTYTLFaSongYzmDTO zhongHengTYTLFaSongYzmDTO);

    @ApiOperation(value = "16.3通联签约签约")
    @PostMapping("/api/v1/zhongheng/tonglianContracted")
    ZhongHengApiResult<ZhongHengTYTLQianYueVO> zhongHengTonglianContracted(@RequestBody @Validated ZhongHengTYTongLianContractendDto zhongHengTYTongLianContractendDto);

    @ApiOperation(value = "抵押授信结果查询")
    @GetMapping("/api/v1/yingfeng/creditQuery")
    Result<YingFengApiResult<YingFengCreditApplicationVO>> yingFengCreditQuery(@Validated @RequestParam Integer orderId);

    @ApiOperation(value = "进入放款流程")
    @PostMapping("/api/v1/fund/approve/finalApprovalLenders")
    Result finalApprovalLenders();

    @ApiOperation(value = "根据订单号进入放款流程(有上限重试次数)")
    @GetMapping("/api/v1/fund/approve/paymentApprovalByOrderIdLimit/{orderId}")
    Result<Boolean> paymentApprovalLimit(@PathVariable("orderId") Integer orderId);

    @ApiOperation(value = "根据订单号进入放款流程(无上限重试次数)")
    @GetMapping("/api/v1/fund/approve/paymentApprovalByOrderId/{orderId}")
    Result<Boolean> paymentApproval(@PathVariable("orderId") Integer orderId);

    @ApiOperation(value = "抵押借款申请")
    @PostMapping("/api/v1/yingfeng/loanApply")
    Result<YingFengApiResult<YingFengLoanApplyVO>> loanApply(@RequestBody @Validated FactoringLoanApplyDTO factoringLoanApplyDTO);

    @ApiOperation(value = "批量重新发起预审")
    @GetMapping("/api/v1/fund/approve/fundFailBatch")
    Result<String> fundFailBatch();

    @ApiOperation(value = "批量更新资方预审终审状态")
    @GetMapping("/api/v1/fund/approve/updateStatusBatch")
    Result<String> updateStatusBatch();

    @ApiOperation(value = "批量更新资方代扣状态")
    @PostMapping("/api/v1/fund/approve/fundDeductStatusBatchUpdate")
    Result<Boolean> fundDeductStatusBatchUpdate();


    @GetMapping("/api/v1/yingfeng/getInfoByOrderId/{orderId}")
    Result<YingFengInfoVO> getYingFengInfoByOrderId(@PathVariable("orderId") Integer orderId);


    @ApiOperation(value = "盈峰发送短信签署")
    @GetMapping("/api/v1/fund/mortgage/yingfeng/sendSign/{orderId}")
    Result<YingFengSendSignVO> yingFengSendSign(@PathVariable("orderId") Integer orderId);

    @ApiOperation(value = "合同签署催签")
    @GetMapping("/api/v1/fund/mortgage/yingfeng/urgeSign/{signId}")
    Result<Boolean> yingFengUrgeSign(@PathVariable("signId") String signId);

    @ApiOperation(value = "盈峰获取签署结果")
    @GetMapping("/api/v1/fund/mortgage/yingfeng/querySignResult/{signId}")
    Result<YingFengQuerySignResultVO> yingFengQuerySignResult(@PathVariable("signId") String signId);


    @GetMapping("/api/v1/fund/approve/uploadRetry/{preId}")
    Result<Boolean> uploadRetry(@PathVariable("preId") Integer preId);

    @GetMapping("/api/v1/fund/approve/uploadPaymentRetry/{orderId}")
    Result<Boolean> uploadPaymentRetry(@PathVariable("orderId") Integer preId);

    @GetMapping("/api/v1/fund/approve//updateContractRetry/{orderId}")
    Result<Boolean> updateContractRetry(@PathVariable("orderId") Integer orderId);

    @PostMapping("/api/v1/fund/approve/uploadDesignateType")
    Result<Boolean> uploadDesignateType(@Validated @RequestBody UploadDesignateTypeDTO uploadDesignateTypeDTO);

    /**
     * 4.1抵押提前结清还款试算
     */
    @PostMapping("/api/v1/yingfeng/repayCalc")
    Result<YingFengRepayCalcVO> yingFengRepayCalc(@RequestBody @Validated YingFengRepayCalcReqDTO req);

    @PostMapping("/api/v1/yingfeng/syncRepaymentRecon")
    Result<Boolean> yingFengSyncRepaymentRecon(@RequestBody YingFengRepaymentReconSyncDTO repaymentReconSyncDTO);

    @PostMapping("/api/v1/fumin/fuMinContractPreview")
    Result<String> fuMinContractPreview(@RequestBody ContractPreviewDTO contractPreviewDTO);

    @GetMapping("/api/v1/fumin/fuMinContractPreviewAll/{orderId}")
    Result<List<FMContractPreviewVO>> fuMinContractPreviewAll(@PathVariable("orderId") Integer orderId);

    @ApiOperation(value = "资方付款")
    @GetMapping("/api/v1/fund/approve/pay")
    Result<Boolean> pay();

    @ApiOperation(value = "查询资方付款结果")
    @PostMapping("/api/v1/fund/approve/pay/result")
    Result<Boolean> payResult();

    @ApiOperation(value = "资方凭据下载")
    @GetMapping("/api/v1/fund/approve/post/information")
    Result<Boolean> postOrderInformation();

    @ApiOperation(value = "资方还款试算")
    @PostMapping("/api/v1/fund/approve/repay/calc")
    Result<FundRepayCalcVO> repayCalc(@RequestBody FundRepayCalcDTO fundRepayDTO);

    @ApiOperation(value = "资方还款划扣")
    @PostMapping("/api/v1/fund/approve/repay")
    Result<Boolean> repay(@RequestBody FundRepayDTO fundRepayDTO);

    @ApiOperation(value = "资方还款划扣结果")
    @PostMapping("/api/v1/fund/approve/repay/result")
    Result<Boolean> repayResult(@RequestBody FundRepayQueryDTO fundRepayQueryDTO);

    /**
     * 获取富民抵押链接
     */
    @GetMapping("/api/v1/fumin/fuMinInitialToken/{orderId}")
    Result<String> fuMinInitialToken(@PathVariable("orderId") Integer orderId);

    /**
     * 富民借款合同签约
     */
    @GetMapping("/api/v1/fumin/fuMinLoanContractSign/{orderId}")
    Result<FuMinPreviewContractDTO> fuMinLoanContractSign(@PathVariable("orderId") Integer orderId);

    /**
     * 盈峰批量同步资方还款计划
     */
    @PostMapping("/api/v1/yingfeng/syncRepaymentBatch")
    Result<Boolean> yingFengSyncRepaymentBatch();

    /**
     * 盈峰批量同步资方还款计划更新文件
     */
    @PostMapping("/api/v1/yingfeng/syncRepaymentUpdateBatch")
    Result<Boolean> yingFengSyncRepaymentUpdateBatch(@RequestBody FundRepaymentSyncDTO repaymentSyncDTO);

    /**
     * 7.1获取盈峰支付二维码
     */
    @PostMapping("/api/v1/yingfeng/applyQrCode")
    Result<String> yingFengApplyQrCode(@RequestBody FundRepaymentDTO fundRepaymentDTO);

    /**
     * 7.2付款结果查询
     */
    @PostMapping("/api/v1/yingfeng/qrPayResult")
    Result<Integer> yingFengQrPayResult(@RequestBody FundRepaymentDTO fundRepaymentDTO);

    /**
     * 7.3 支付后发起还款
     */
    @PostMapping("/api/v1/yingfeng/repayApply")
    Result<Boolean> yingFengRepayApply(@RequestBody FundRepaymentDTO fundRepaymentDTO);

    /**
     * 7.4 查询还款结果
     */
    @PostMapping("/api/v1/yingfeng/queryRepayResult")
    Result<Boolean> yingFengQueryRepayResult(@RequestBody FundRepaymentDTO fundRepaymentDTO);

    @PostMapping("/api/v1/fund/approve/checkThePeriod")
    Result<FundDeductCheckVO> checkThePeriod(@RequestBody FundRepaymentDeductCheckDTO dto);

    /**
     * 8.1.回购还款试算接口
     *
     * @param repurchaseRepayCalcDTO
     * @return
     */
    @PostMapping("/api/v1/yingfeng/repurchaseRepayCalc")
    Result<YingFengRepurchaseRepayCalcVO> repurchaseRepayCalc(@RequestBody RepurchaseRepayCalcDTO repurchaseRepayCalcDTO);

    /**
     * 8.2.回购还款接口
     */
    @PostMapping("/api/v1/yingfeng/assetRepayment")
    Result<Boolean> assetRepayment(@RequestBody YingFengAssetRepaymentDTO assetRepaymentDTO);

    /**
     * 8.3.回购结果查询接口
     */
    @PostMapping("/api/v1/yingfeng/queryRepurchaseRepayment")
    Result<YingFengQueryRepurchaseRepaymentVO> queryRepurchaseRepayment(@RequestBody QueryRepurchaseRepaymentDTO queryRepurchaseRepaymentDTO);


    @PostMapping("/api/v1/fumin/receive/card/change")
    Result<FuMinPreviewContractDTO> fuMinReceiveCardChange(@RequestBody BankCardInfoDTO bankCardInfoDTO);

    /**
     * 文件接口3.3 回购结果对账文件
     *
     * @return
     */
    @PostMapping("/api/v1/yingfeng/repurchase/result")
    Result<Boolean> repurchaseResult(@RequestBody RepurchaseResultDTO repurchaseResultDTO);


    @GetMapping("/api/v1/fumin/fuMinInitialTokenForReleasePledge/{orderId}")
    Result<String> getMortgageUrlForReleasePledge(@PathVariable("orderId") Integer orderId);

    @GetMapping("/api/v1/fumin/getMortgageUrlForReleasePledgeCancel/{orderId}")
    Result<Boolean> getMortgageUrlForReleasePledgeCancel(@PathVariable("orderId") Integer orderId);

    @PostMapping("/api/v1/fumin/fuMinMortgageStatusQuery")
    Result<String> fuMinMortgageStatusQuery(@RequestBody MortgageStatusQueryDTO mortgageStatusQueryDTO);

    @PostMapping("/api/v1/fund/approve/fundSyncOverdueRepayment")
    Result<Boolean> fundSyncOverdueRepayment(@RequestBody List<FundEnum> fundEnumList);

    @GetMapping("/api/v1/fumin/cancelMortgageStatus/{orderId}")
    Result<Boolean> cancelMortgageStatus(@PathVariable("orderId") Integer orderId);

    @GetMapping("/api/v1/fumin/fuMinWithholdCard/{orderId}")
    Result<Boolean> fuMinWithholdCard(@PathVariable("orderId") Integer orderId);


    @ApiOperation(value = "恒通银行卡签约发送验证码")
    @PostMapping("/api/v1/hengtong/bankCardSMSCode")
    ZhongHengApiResult<ZhongHengTYTLFaSongYZMVO> hengTongBankCardSMSCode(@RequestBody HengTongBankCardSMSCodeDTO dto);

    @ApiOperation(value = "恒通银行卡签约")
    @PostMapping("/api/v1/hengtong/bankCardSign")
    ZhongHengApiResult<ZhongHengTYTLQianYueVO> hengTongBankCardSign(@RequestBody HengTongBankSignDTO dto);

    @ApiOperation(value = "银行卡签约解约")
    @PostMapping("/api/v1/hengtong/bankCardCancelSign")
    ZhongHengApiResult<ZhongHengTYTLJieYueVO> hengTongBankCardCancelSign(@RequestBody HengTongBankCancelSignDTO dto);

    @PostMapping("/api/v1/fumin/fuMinMortgageCancelQuery")
    Result<String> fuMinMortgageCancelQuery(@RequestBody MortgageStatusQueryDTO mortgageStatusQueryDTO);


    @ApiOperation(value = "获取抵押、解除抵押链接")
    @PostMapping("/api/v1/hengtong/copperCarMortgageUrl")
    Result<String> hengTongCopperCarMortgageH5Url(@RequestBody HengTongCopperCarDeptMortgageDTO dto);

    @ApiOperation(value = "恒通获取抵押、解除抵押结果")
    @PostMapping("/api/v1/hengtong/queryCopperCarMortgageStatus")
    Result<String> hengTongCopperCarMortgageStatus(@RequestBody SearchOrderStatusDTO dto);

    @ApiOperation(value = "恒通抵押、解抵 撤回")
    @PostMapping("/api/v1/hengtong/cancelCopperCarMortgage")
    Result<Boolean> hengTongCancelCopperCarMortgage(@RequestBody HengTongCopperCarDeptMortgageCancelDTO dto);

    @ApiOperation(value = "贷后补件上传抵押登记联")
    @PostMapping("/api/v1/hengtong/hengTongJiaYaUpdate")
    Result<Boolean> hengTongJiaYaUpdate(@RequestParam("orderId") Integer orderId);

    @ApiOperation(value = "长银签约申请")
    @PostMapping("/api/v1/changyin/protocolApply")
    ChangYinResBodyDTO<ChangYinSignResDTO> protocolApply(@RequestBody ChangYinSignInfoDTO changYinSignInfoDTO);

    @ApiOperation(value = "长银签约确认")
    @PostMapping("/api/v1/changyin/protocolConfirm")
    ChangYinResBodyDTO<ChangYinSignResDTO> protocolConfirm(@RequestBody ChangYinSignConfirmDTO changYinSignConfirmDTO);

    @ApiOperation(value = "长银卡号变更")
    @PostMapping("/api/v1/changyin/changeAccountApply")
    ChangYinResBodyDTO<ChangYinAccountChangeResDTO> changeAccountApply(@RequestBody ChangYinUpdateAccountDTO changYinUpdateAccountDTO);

    @ApiOperation(value = "长银用信申请")
    @GetMapping("/api/v1/changyin/preLoanApplyV2ByOrderId")
    ChangYinResBodyDTO<ChangYinLoanApplicationRespDTO> changYinPreLoanApplyV2ByOrderId(@RequestParam("orderId") Integer orderId, @RequestParam("contractNumber") String contractNumber);

    @ApiOperation(value = "长银用信状态查询")
    @GetMapping("/api/v1/changyin/preLoanQueryV2ByOrderId")
    ChangYinResBodyDTO<ChangYinPreLoanQueryResDTO> changYinPreLoanQueryV2ByOrderId(@RequestParam("orderId") Integer orderId);

    @ApiOperation(value = "长银贷款用途证明上传")
    @PostMapping("/api/v1/changyin/loanPurposeUpload")
    ChangYinResBodyDTO<ChangYinLoanUploadResDTO> changYinLoanPurposeUpload(@RequestParam("orderId") Integer orderId);

    @ApiOperation(value = "长银下载合同")
    @GetMapping("/api/v1/changyin/downloadContract")
    Result<List<ChangYinContractPreviewVO>> changYinDownloadContract(@RequestParam("orderId") Integer orderId);

    @ApiOperation(value = "长银查询LPR")
    @PostMapping("/api/v1/changyin/queryLPR")
    ChangYinResBodyDTO<ChangLPRResDTO> changYinQueryLPR(@RequestBody ChangYinLPRDTO changYinLPRDTO);

    @ApiOperation(value = "长银身份证件更新")
    @GetMapping("/api/v1/changyin/idCardUpdateApply")
    ChangYinResBodyDTO<ChangYinIdCardUpdateApplyResDTO> changYinIdCardUpdateApply(@RequestParam("orderId") Integer orderId);

    @ApiOperation(value = "长银身份证件查询")
    @GetMapping("/api/v1/changyin/idCardUpdateQuery")
    ChangYinResBodyDTO<ChangYinIdCardUpdateQueryResDTO> changYinIdCardUpdateQuery(@RequestParam("orderId") Integer orderId);

    @ApiOperation(value = "长银根据订单贷款试算")
    @PostMapping("/api/v1/changyin/loanTrialV2ByOrder")
    ChangYinResBodyDTO<ChangYinLoanTrialResponseDTO> changYinLoanTrialV2ByOrder(@RequestBody ChangYinLoanTrialByOrderDTO changYinLoanTrialByOrderDTO);

    @ApiOperation(value = "放款失败下载合同")
    @GetMapping("/api/v1/changyin/downCardChangeInOutSupply")
    Result<List<ChangYinContractPreviewVO>> downCardChangeInOutSupply(@RequestParam("orderId") Integer orderId);

    /**
     * 批量同步资方还款计划更新文件
     */
    @PostMapping("/api/v1/changyin/syncRepaymentUpdateBatch")
    Result<Boolean> changYinSyncRepaymentUpdateBatch(@RequestBody FundRepaymentSyncDTO repaymentSyncDTO);

    /**
     * 还款计划文件批量下载
     */
    @GetMapping("/api/v1/changyin/repayPlanFileBatch")
    Result<Boolean> changYinRepayPlanFileBatch();

    /**
     * 批量同步资方放款明细文件
     */
    @PostMapping("/api/v1/changyin/syncPaymentDetailFileBatch")
    Result<Boolean> changYinSyncPaymentDetailFileBatch(@RequestBody FundSyncDateDTO syncDateDTO);

    @PostMapping("/api/v1/hengtong/preRepayPlan")
    Result<HengTongPreRepayPlanVO> hengTongPreRepayPlan(@RequestBody HengTongPreRepayPlanDTO preRepayPlanDTO);

    /**
     * 长银预代偿
     */
    @GetMapping("/api/v1/changyin/getPreIndemnityFile")
    Result<Boolean> getPreIndemnityFile(@RequestParam(name = "jobTime", required = false) String jobTime);

    /**
     * 长银代偿
     */
    @GetMapping("/api/v1/changyin/getIndemnityFile")
    Result<Boolean> getIndemnityFile(@RequestParam(name = "jobTime", required = false) String jobTime);

    /**
     * 通汇发起退款
     */
    @ApiOperation(value = "通汇发起退款")
    @PostMapping("/api/v1/hengtong/tongHuiInitiateRefund")
    Result<TongHuiInitiateRefundVO> tongHuiInitiateRefund(@RequestBody TongHuiInitiateRefundDTO dto);

    /**
     * 蓝海抵押查询
     */
    @ApiOperation(value = "蓝海抵押查询")
    @GetMapping("/api/v1/lanhai/mortgageApplyVerify/{orderId}")
    Result<LanHaiMortgageEnums> lanHaiMortgageApplyVerify(@PathVariable("orderId") Integer orderId);

    /**
     * 蓝海抵押申请
     */
    @ApiOperation(value = "蓝海抵押申请")
    @GetMapping("/api/v1/lanhai/mortgageApply/{orderId}")
    Result<Boolean> lanHaiMortgageApply(@PathVariable("orderId") Integer orderId);

    /**
     * 蓝海抵押退办
     */
    @ApiOperation(value = "蓝海抵押退办")
    @GetMapping("/api/v1/lanhai/mortgageCancel")
    Result<Boolean> lanHaiMortgageCancel(@RequestParam("orderId") Integer orderId,
                                         @RequestParam String cancelReason);

    /**
     * 蓝海抵押编辑
     */
    @ApiOperation(value = "蓝海抵押编辑")
    @GetMapping("/api/v1/lanhai/mortgageEdit/{orderId}")
    Result<Boolean> lanHaiMortgageEdit(@PathVariable("orderId") Integer orderId);


    @ApiOperation(value = "恒通银行卡签约发送验证码")
    @PostMapping("/api/v1/hengtong/hengTongATMCardInfoSync")
    ZhongHengApiResult<HengTongATMCardInfoSyncVO> hengTongATMCardInfoSync(@RequestBody HengTongATMCardInfoSyncDTO dto);

    @ApiOperation(value = "蓝海抵押结果查询")
    @GetMapping("/api/v1/lanhai/mortgageApplyVerify/{orderId}")
    Result<LanHaiMortgageEnums> mortgageQuery(@PathVariable("orderId") Integer orderId);

    @ApiOperation(value = "蓝海获取抵押流水号")
    @GetMapping("/api/v1/lanhai/mortgageStatus/{orderId}")
    Result<String> mortgageStatus(@PathVariable("orderId") Integer orderId);


    /**
     * 蓝海 合同签订
     */
    @PostMapping("/api/v1/lanhai/contractSignApplyByOrderId")
    Result<LanHaiContractSignApplyResponse> lanHaiContractSignApplyByOrderId(@RequestBody LanHaiContractSignApplyDTO dto);

    /**
     * 蓝海 签订合同申请下载
     */
    @PostMapping("/api/v1/lanhai/contractSignDownloadByOrderId")
    Result<List<LanHaiContractSignFileDTO>> lanHaiContractSignDownloadByOrderId(@RequestBody LanHaiContractSignDownloadDTO dto);

    /**
     * 支用申请试算
     */
    @ApiOperation(value = "支用申请试算")
    @PostMapping("/api/v1/lanhai/creditLimitInfo")
    Result<LanHaiRepayCalcResponse> lanHaiCreditLimitInfo(@RequestBody LanHaiCreditLimitInfoDTO dto);


    /**
     * 获取LPR文件
     *
     * @return
     */
    @GetMapping("/api/v1/lanhai/getLprFile")
    Result<Boolean> getLprFile();

    @ApiOperation(value = "通汇代偿开启/关闭")
    @PostMapping("/api/v1/hengtong/tongHuiCompensateSwitch")
    Result<Boolean> tongHuiCompensateSwitch(@RequestBody TongHuiCompensateSwitchDTO dto);

    @ApiOperation(value = "获取钉钉减免")
    @PostMapping("/api/v1/hengtong/getDingDrawMoney")
    Result<Boolean> getDingDrawMoney();

    @ApiOperation(value = "代偿结清通知三方")
    @PostMapping("/api/v1/hengtong/compensateSettleNotice")
    Result<Boolean> compensateSettleNotice(@RequestBody DaiChangJieQingTongZhiResultDTO dto);


    @ApiOperation(value = "贷后补件上传查证")
    @GetMapping("/api/v1/lanhai/payAfterReplenishQuery")
    Result<LanHaiAfterReplenishVerifyResponse> lanHaiPayAfterReplenishQuery(@RequestParam("orderId") Integer orderId);

    @ApiOperation(value = "贷后补件上传")
    @GetMapping("/api/v1/lanhai/payAfterReplenish")
    Result<Boolean> lanHaiPayAfterReplenish(@RequestParam("orderId") Integer orderId);

    /**
     * 蓝海还款试算
     */
    @ApiOperation(value = "还款试算")
    @PostMapping("/api/v1/lanhai/trialRepay")
    Result<LanHaiTrialRepayResponse> lanHaiTrialRepay(@RequestBody TrialRepayDTO dto);

    /**
     * 蓝海预授信终结
     */
    @ApiOperation(value = "还款试算")
    @GetMapping("/api/v1/lanhai/creditPreEnd")
    boolean creditPreEnd(@RequestParam("preId") Integer preId);

    /**
     * 蓝海代偿申请
     */
    @ApiOperation(value = "蓝海代偿申请")
    @GetMapping("/api/v1/lanhai/compensateApply/{orderId}")
    Result<Boolean> lanHaiCompensateApply(@PathVariable("orderId") Integer orderId);

    /**
     * 更新划扣状态
     */
    @ApiOperation(value = "更新划扣状态")
    @PostMapping("/api/v1/hengtong/updateRepayDeductStatus")
    Result<Boolean> tongHuiUpdateRepayDeductStatus(@RequestBody UpdateHengTongRepayDeductStatusDTO dto);

    @ApiOperation("协议支付-签约请求")
    @PostMapping("/api/v1/yibao/agreement/sign")
    Result<YiBaoAgreementSignVO> agreementSign(@RequestBody YiBaoAgreementSignDTO dto);

    @ApiOperation("协议支付-签约确认")
    @PostMapping("/api/v1/yibao/agreement/confirm")
    Result<YiBaoAgreementConfirmVO> agreementConfirm(@RequestBody YiBaoAgreementConfirmDTO dto);

    @ApiOperation("回传代偿后还款数据")
    @PostMapping("/api/v1/lanhai/back/repaymentData")
    Result<Boolean> repaymentData();

    @ApiOperation("荔商融担订单数据同步")
    @PostMapping(value = "/api/v1/lishang/saveData")
    Result<Boolean> synSaveData();

    @GetMapping("/api/v1/fund/approve/orderTimeoutAutomaticTermination")
    Result<Boolean> orderTimeoutAutomaticTermination();

    @ApiOperation(value = "富民担保申请接口")
    @PostMapping("/api/v1/fumin/trade/guarApply")
    Result<FuMinTradeGuarApplyResDTO> save(@RequestBody FuMinCommonReqDTO fuMinCommonReqDTO);

    @ApiOperation(value = "富民担保结果查询接口   ")
    @PostMapping("/api/v1/fumin/query/guarResult")
    Result<FuMinQueryGuarResultResDTO> query(@RequestBody FuMinCommonReqDTO fuMinCommonReqDTO);

	@ApiOperation(value = "嘉泰代偿后客户还款通知")
	@PostMapping("/api/v1/hengtong/daiChangHouHKTZ")
	ZhongHengApiResult<CommuteAndPayVO> daiChangHouHKTZ(@RequestBody CommuteAndPayDTO dto);

	/**
	 * 3.16流程终止申请
	 */
	@ApiOperation(value = "中恒流程终止申请")
	@PostMapping("/api/v1/hengtong/processEndNotice")
	ZhongHengApiResult<ProcessEndVO> daiChangHouHKTZ(@RequestBody ProcessEndDTO dto);

	@ApiOperation(value = "代偿查账通知")
	@PostMapping("/api/v1/hengtong/daichangChaZhangTZ")
	ZhongHengApiResult<Void> daichangChaZhangTZ(@RequestBody DaiChangChaZhangTongZhiDTO dto);

    @ApiOperation(value = "贷后补件上传抵押登记联")
    @PostMapping("/api/v1/hengtong/autoPatchesByPaymentType")
    Result<Boolean> autoPatchesByPaymentType();

    /**
     * 定时推送贷后补件
     */
    @ApiOperation(value = "定时推送贷后补件")
    @GetMapping("/api/v1/lanhai/regularlyPushPostLoanSupplements/{days}")
    void regularlyPushPostLoanSupplements(@PathVariable Integer days);
    /**
     * 定时查询贷后补件结果
     */
    @ApiOperation(value = "定时查询贷后补件结果")
    @PostMapping("/regularlyQueryPostLoanSupplements")
    Result<Map<Integer,String>> regularlyQueryPostLoanSupplements(@RequestBody RegularlyQueryPostLoanSupplementsDTO dto);
}