package com.longhuan.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.FundDeptListDTO;
import com.longhuan.order.pojo.dto.FundDeptUploadDTO;
import com.longhuan.order.pojo.dto.FundDeptLevelDTO;
import com.longhuan.order.pojo.dto.ManagementDeptDTO;
import com.longhuan.order.pojo.vo.FundDeptListVO;
import com.longhuan.order.service.FundDeptService;
import com.longhuan.resource.pojo.vo.FileVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 资方-部门表;(lh_management_dept)表控制层
 *
 * <AUTHOR> yangfumin
 * @date : 2024-7-5
 */
@Api(tags = "资方-部门表对象功能接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/fundDept")
public class FundDeptController {
    private final FundDeptService fundDeptService;

    @ApiOperation("新增数据")
    @PostMapping({"/add"})
    public Result<Boolean> add(@RequestBody FundDeptLevelDTO fundDeptLevelDTO) {
        return Result.success(fundDeptService.insertOne(fundDeptLevelDTO));
    }

    @ApiOperation("删除数据")
    @GetMapping({"/delFundDept/{id}"})
    public Result<Boolean> delFundDept(@PathVariable("id") Integer id) {
        return Result.success(fundDeptService.delFundDept(id));
    }

    @PostMapping({"/selectPage"})
    public Result<IPage<FundDeptListVO>> selectPage(@RequestBody ManagementDeptDTO managementDeptDTO) {
        return Result.success(fundDeptService.selectPage(managementDeptDTO));
    }

    @ApiOperation("资金路由上传")
    @PostMapping({"/dataUpload"})
    public Result<FundDeptUploadDTO> dataUpload(@RequestParam("file") MultipartFile file) {
        return Result.success(fundDeptService.fundDeptUploadData(file));
    }

    @ApiOperation("派单区域上传")
    @PostMapping({"/areaUpload"})
    public Result<Boolean> areaUpload(@RequestParam("file") MultipartFile file) {
        return Result.success(fundDeptService.areaUpload(file));
    }

    @ApiOperation("资金路由下载")
    @PostMapping({"/dataDownload"})
    public Result<List<FileVO>> dataDownload(HttpServletResponse response) {
        return Result.success(fundDeptService.dataDownload(response));
    }

    @ApiOperation("批量上下架")
    @GetMapping({"/delFundDeptByIds"})
    public Result<Boolean> delFundDeptByIds(@RequestParam("ids") List<Integer> ids, @RequestParam("type") Integer type) {
        return Result.success(fundDeptService.delFundDeptByIds(ids, type));
    }

    @ApiOperation("修改数据")
    @PostMapping({"/update/{id}"})
    public Result<Boolean> update(@PathVariable("id") Integer id, @RequestParam Integer level) {
        return Result.success(fundDeptService.updateOne(id, level));
    }

    @ApiOperation("批量新增")
    @PostMapping({"/addList"})
    public Result<Boolean> AddList(@RequestBody FundDeptListDTO fundDeptListDTO) {
        return Result.success(fundDeptService.AddList(fundDeptListDTO));
    }
}