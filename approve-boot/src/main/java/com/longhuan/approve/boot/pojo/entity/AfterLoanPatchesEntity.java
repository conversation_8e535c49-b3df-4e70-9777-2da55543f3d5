package com.longhuan.approve.boot.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

import com.longhuan.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 贷后补件表
 * @TableName lh_after_loan_patches
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "资方产品关联表")
@TableName("lh_after_loan_patches")
@KeySequence("lh_after_loan_patches_id_seq")
@EqualsAndHashCode(callSuper = true)
public class AfterLoanPatchesEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId
    private Integer id;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 贷后状态
     */
    private Integer afterLoanStatus;

    /**
     * GPS安装状态（0:未安装,1:安装中,2:已安装,3:拆除中,4:已拆除）
     */
    private Integer gpsState;

    /**
     * 结清状态 
     */
    private Integer payoffState;

    /**
     * 补件提交时间
     */
    private Date patchesSubmissionTime;

    /**
     * 上传资方资料标识 0:未上传,1:已上传 2:处理中 3.通汇的先抵押后放款的补件
     */
    private Integer fundUploadFlag;

    /**
     * 抵押时间
     */
    private Date mortgageTime;

    /**
     * 
     */
    private Integer thPatches;

    /**
     * 资方补件次数
     */
    private Integer fundPatchCount;
}