package com.longhuan.order;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.feign.ResourceFeign;
import com.longhuan.order.mapper.StoreAddressInfoMapper;
import com.longhuan.order.pojo.dto.chexiao.PushGongdanProgressDTO.OptMsg;
import com.longhuan.order.pojo.dto.chexiao.PushGongdanProgressDTO.GongdanDetail;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.feign.CheXiaoFeign;
import com.longhuan.order.mapper.OrderAmountMapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.mapper.OrderVehicleInfoMapper;
import com.longhuan.order.pojo.dto.chexiao.PushGongdanProgressDTO;
import com.longhuan.order.pojo.entity.OrderAmountEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.entity.OrderVehicleInfoEntity;
import com.longhuan.order.pojo.entity.StoreAddressInfoEntity;
import com.longhuan.order.pojo.vo.chexiao.CheXiaoVO;
import com.longhuan.order.pojo.vo.chexiao.PushGongdanProgressVO;
import com.longhuan.order.pojo.vo.chexiao.QueryWorkOrderInfoVO;
import com.longhuan.order.service.XinshuService;
import com.longhuan.resource.pojo.vo.FileVO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.*;

@SpringBootTest(classes = OrderApplication.class)
public class CheXiaoTest {
    @Value("${chexiao.token}")
    private String token;
    @Resource
    private CheXiaoFeign cheXiaoFeign;
    @Resource
    private OrderVehicleInfoMapper orderVehicleInfoMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private OrderAmountMapper orderAmountMapper;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private ResourceFeign resourceFeign;
    @Resource
    private XinshuService xinshuService;

    /**
     * 创建装机工单
     */
    @Test
    public void createInstallationWorkOrder() {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(1314);
        OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(
                new LambdaQueryWrapper<OrderAmountEntity>()
                        .eq(OrderAmountEntity::getOrderId, orderInfoEntity.getId())
                        .eq(OrderAmountEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderAmountEntity::getCreateTime)
                        .last("limit 1")
        );
        OrderVehicleInfoEntity orderVehicleInfoEntity = orderVehicleInfoMapper.selectOne(
                new LambdaQueryWrapper<OrderVehicleInfoEntity>()
                        .eq(OrderVehicleInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(OrderVehicleInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderVehicleInfoEntity::getCreateTime)
                        .last("limit 1")
        );
//        CreateInstallationWorkOrderDTO dto=new CreateInstallationWorkOrderDTO();
//        dto.setToken(token);
//        dto.setName(orderVehicleInfoEntity.getHolder());
//        dto.setTel("13295484447");
//        dto.setBrand(orderVehicleInfoEntity.getBrand());
//        dto.setModel(orderVehicleInfoEntity.getVehicleModel());
//        dto.setFrame_no(orderVehicleInfoEntity.getVin());
//        dto.setPlate_no(orderVehicleInfoEntity.getVehicleNumber());
//        if (orderAmountEntity.getCustomerConfirmAmount().compareTo(new BigDecimal("150000.00")) >= 0){
//            dto.setWire_equip("1");
//            dto.setWireless_equip("3");
//            dto.setObd_equip("0");
//        }else {
//            dto.setWire_equip("1");
//            dto.setWireless_equip("2");
//            dto.setObd_equip("0");
//        }
//        dto.setContact_name("测试");
//        dto.setContact_status("测试");
//        dto.setContact_tel("13295484447");
//        dto.setPlan_installTime("2025-03-30 14:00:00");
//        dto.setInstall_province("河北省");
//        dto.setInstall_city("石家庄市");
//        dto.setInstall_county("新华区");
//        dto.setInstall_location("测试地址");
//        dto.setCredit_period(String.valueOf(orderInfoEntity.getTerm()));
//        dto.setCollectingKey("1");
//        dto.setRecipient("路雨欣");
//        dto.setRecipientTel("15830050600");
//        dto.setRecipientAddress("河北省邯郸市邯山区启信大厦6层");
//        dto.setInsured("河北省邯郸市邯山区启信大厦6层");

        String installationWorkOrder = cheXiaoFeign.createInstallationWorkOrder(
                token,
                orderVehicleInfoEntity.getHolder(),
                "13295484447",
                orderVehicleInfoEntity.getBrand(),
                orderVehicleInfoEntity.getVehicleModel(),
                orderVehicleInfoEntity.getVin(),
                orderVehicleInfoEntity.getVehicleNumber(),
                "1",
                "3",
                "0",
                "测试",
                "测试",
                "13295484447",
                "2025-03-30 14:00:00",
                "河北省",
                "石家庄市",
                "新华区",
                "测试地址",
                "1个无线不可拆",
                String.valueOf(orderInfoEntity.getTerm()),
                "1",
                "路雨欣",
                "15830050600",
                "河北省邯郸市邯山区启信大厦6层",
                "2",
                ""
                );
        try {
            CheXiaoVO cheXiaoVO = objectMapper.readValue(installationWorkOrder, CheXiaoVO.class);
            System.out.println("installationWorkOrder = " + cheXiaoVO);
            if (ObjUtil.isNotEmpty(cheXiaoVO)){
                if (Objects.equals(cheXiaoVO.getResult(), 1)){
                    if (Objects.equals(cheXiaoVO.getData().getCode(), 2)){
                        throw new BusinessException(cheXiaoVO.getData().getErrormsg());
                    }
                }else if (Objects.equals(cheXiaoVO.getResult(), 0)){
                    throw new BusinessException("GPS预约安装失败");
                }else {
                    throw new BusinessException("系统错误");
                }
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建拆机工单
     */
    @Test
    void createDismantleWorkOrder(){
        String dismantleWorkOrder = cheXiaoFeign.createDismantleWorkOrder(
                token,
                "1857338567168640",
                "device_nums",
                "service_manager",
                "dealer",
                "contact_name",
                "contact_status",
                "contact_tel",
                "plan_installTime",
                "install_province",
                "install_city",
                "install_location",
                "install_remark"
        );
        try {
            CheXiaoVO cheXiaoVO = objectMapper.readValue(dismantleWorkOrder, CheXiaoVO.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 新装/拆除工单信息查询接口
     */
    @Test
    void getGongdanInfo(){
        String gongdanInfo = cheXiaoFeign.getGongdanInfo(
                token,
                "1857338567168640"
        );
        System.out.println("gongdanInfo = " + gongdanInfo);
        try {
            QueryWorkOrderInfoVO queryWorkOrderInfoVO = objectMapper.readValue(gongdanInfo, QueryWorkOrderInfoVO.class);
            System.out.println("queryWorkOrderInfoVO = " + queryWorkOrderInfoVO);
            if (ObjUtil.isNotEmpty(queryWorkOrderInfoVO)){
                if (Objects.equals(queryWorkOrderInfoVO.getResult(), 1)){
                    if (Objects.equals(queryWorkOrderInfoVO.getData().getCode(), 1)){

                    }else if (ObjUtil.isNotEmpty(queryWorkOrderInfoVO.getData().getErrormsg())){
                        throw new BusinessException(queryWorkOrderInfoVO.getData().getErrormsg());
                    }

                }else if (Objects.equals(queryWorkOrderInfoVO.getResult(), 0)){
                    throw new BusinessException("查询失败");
                }else {
                    throw new BusinessException("系统错误");
                }
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 工单修改接口（限新装工单）
     */
    @Test
    void updateGongdan(){
        String updateGongdan = cheXiaoFeign.updateGongdan(
                token,
                "1857338567168640",
                "name",
                "tel",
                "brand",
                "model",
                "frame_no",
                "plate_no",
                "wire_equip",
                "wireless_equip",
                "obd_equip",
                "service_manager",
                "dealer",
                "contact_name",
                "contact_status",
                "contact_tel",
                "plan_installTime",
                "install_province",
                "install_city",
                "install_county",
                "install_location",
                "install_remark",
                "driverIdNum",
                "family_address",
                "working_address",
                "shop_name",
                "orderNum",
                "1",
                "路雨欣",
                "15830050600",
                "河北省邯郸市邯山区启信大厦6层",
                "2",
                ""
        );
        try {
            CheXiaoVO cheXiaoVO = objectMapper.readValue(updateGongdan, CheXiaoVO.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 取消工单
     */
    @Test
    void cancelGongdan(){
        String cancelGongdan = cheXiaoFeign.cancelGongdan(
                token,
                "1857338567168640"
        );
        try {
            CheXiaoVO cheXiaoVO = objectMapper.readValue(cancelGongdan, CheXiaoVO.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 推送工单进度信息接口
     */
    @Test
    void pushGongdanProgress(){
        List<String> list = new ArrayList<>();
        list.add("https://p2.qhimgs4.com/t01fd61ef8c5088feff.jpg");
        list.add("https://p2.qhimgs4.com/t01fd61ef8c5088feff.jpg");
        try {
            List<MultipartFile> list1 = convertImageUrlsToMultipartFiles(list);
            for (MultipartFile multipartFile : list1) {
                Result<List<FileVO>> listResult = resourceFeign.uploadFile(Collections.singletonList(multipartFile));
                if (Result.isSuccess(listResult) && CollUtil.isNotEmpty(listResult.getData())){
                    List<String> urlList = listResult.getData().stream().map(FileVO::getResourceId).toList();
                    System.out.println("urlList = " + urlList);
                }
            }

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    public List<MultipartFile> convertImageUrlsToMultipartFiles(List<String> imageUrls) throws IOException {
        List<MultipartFile> list = new ArrayList<>();
        for (String imageUrl : imageUrls) {
            URL url = new URL(imageUrl);
            String fileName = url.getFile().substring(url.getFile().lastIndexOf('/') + 1);
            try (InputStream inputStream = url.openStream()) {
                Path tempFile = Files.createTempFile(fileName, null);
                Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);
                MockMultipartFile mockMultipartFile = new MockMultipartFile(
                        "files", // 显式指定参数名称为 "files"
                        fileName,
                        "image/jpeg",
                        Files.readAllBytes(tempFile)
                );
                list.add(mockMultipartFile);
            }
        }
        return list;
    }
    @Resource
    StoreAddressInfoMapper storeAddressInfoMapper;
    @Test
    void cheXiaoGpsCallback(){
        StoreAddressInfoEntity storeAddressInfoEntity = storeAddressInfoMapper.selectById(438);
        if (ObjUtil.isNotEmpty(storeAddressInfoEntity)){
            System.out.println("storeAddressInfoEntity = " + storeAddressInfoEntity);
        }
    }
    @Test
    void xinshuEntInfo(){
        JSONObject s = xinshuService.getOrderWorkUnitInfo(1922);
        System.out.println("s = " + s);
    }

}
