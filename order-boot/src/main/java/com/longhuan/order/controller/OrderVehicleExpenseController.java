package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.OrderVehicleExpenseQueryDTO;
import com.longhuan.order.pojo.vo.OrderVehicleExpenseVO;
import com.longhuan.order.service.OrderVehicleExpenseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订单车务费用控制器
 *
 * <AUTHOR>
 * @date 2025/07/22
 */
@Slf4j
@RestController
@RequestMapping("/order/vehicle-expense")
@Api(tags = "订单车务费用")
public class OrderVehicleExpenseController {

    @Resource
    private OrderVehicleExpenseService orderVehicleExpenseService;

    /**
     * 获取可申请车务费报销的订单列表
     */
    @PostMapping("/list")
    @ApiOperation("获取可申请车务费报销的订单列表")
    public Result<Page<OrderVehicleExpenseVO>> pageOrdersForVehicleExpense(
            @RequestBody OrderVehicleExpenseQueryDTO queryDTO,
            @CurrentUser LoginUser loginUser) {
        return Result.success(orderVehicleExpenseService.pageOrdersForVehicleExpense(queryDTO,loginUser));
    }
}
