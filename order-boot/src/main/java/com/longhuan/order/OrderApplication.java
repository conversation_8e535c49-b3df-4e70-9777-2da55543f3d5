package com.longhuan.order;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 订单申请
 *
 * <AUTHOR>
 * @date 2024/06/20
 */
@EnableAsync
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan("com.longhuan.order.mapper")
@EnableFeignClients(basePackages = "com.longhuan")
public class OrderApplication {
    public static void main(String[] args) {
        SpringApplication.run(OrderApplication.class, args);
    }
}

