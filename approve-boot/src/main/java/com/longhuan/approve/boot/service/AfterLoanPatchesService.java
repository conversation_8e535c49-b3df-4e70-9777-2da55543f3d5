package com.longhuan.approve.boot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.longhuan.approve.api.pojo.vo.lanhai.LanHaiAfterReplenishVerifyResponse;
import com.longhuan.approve.boot.pojo.entity.AfterLoanPatchesEntity;
import com.longhuan.approve.boot.pojo.entity.OrderInfoEntity;

/**
* <AUTHOR>
* @description 针对表【lh_after_loan_patches(贷后补件表)】的数据库操作Service
* @createDate 2025-09-03 10:52:47
*/
public interface AfterLoanPatchesService extends IService<AfterLoanPatchesEntity> {

    void saveEntity(OrderInfoEntity orderInfoEntity,boolean flag,String errorMsg,String fundPatchCount);

    void payAfterReplenish(OrderInfoEntity orderInfoEntity);
}
