package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.RepaymentListDTO;
import com.longhuan.order.pojo.vo.RepaymentInfoVO;
import com.longhuan.order.pojo.vo.RepaymentOverdueStatusVO;
import com.longhuan.order.service.RepaymentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 还款信息
 *
 * <AUTHOR>
 * @date 2024/08/12
 */
@Api(tags = "还款信息")
@RestController
@RequestMapping("/api/v1/repayment")
@RequiredArgsConstructor
public class RepaymentController {
    private final RepaymentService repaymentService;

    /**
     * 列表
     *
     * @param repaymentListDTO 还款清单 DTO
     * @return {@link Result }<{@link List }<{@link RepaymentInfoVO }>>
     */
    @ApiOperation(value = "还租计划表")
    @PostMapping("/list")
    public Result<List<RepaymentInfoVO>> list(@RequestBody @Validated RepaymentListDTO repaymentListDTO) {
        return Result.success(repaymentService.list(repaymentListDTO));
    }
    /**
     * 根据orderId获取逾期
     *
     * @param orderId orderId
     * @return {@link Result }<{@link List }<{@link RepaymentInfoVO }>>
     */
    @ApiOperation(value = "根据orderId查询逾期状态")
    @GetMapping("/overdueStatus/{orderId}")
    public Result<RepaymentOverdueStatusVO> overdueStatusByOrderId(@PathVariable Integer orderId) {
        return Result.success(repaymentService.overdueStatusByOrderId(orderId));
    }
}
