package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.tongLianTong.*;
import com.longhuan.order.pojo.vo.FundDeductQueryVO;
import com.longhuan.order.pojo.vo.tongliantong.TongLianTongProtocolPayVO;
import com.longhuan.order.pojo.vo.tongliantong.TongLianTongQtransreqVO;
import com.longhuan.order.pojo.vo.tongliantong.TongLianTongRefundVO;
import com.longhuan.order.service.TongLianTongService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通联通
 */
@Api(tags = "通联通")
@RestController
@RequestMapping("/api/v1/tong_lian_tong")
@RequiredArgsConstructor
public class TongLianTongController {
    private final TongLianTongService tongLianTongService;

    /**
     * 3.1.1签约短信触发(310001)
     * 3.1.1.1 报文功能
     * 商户端页面设置重新触发短信的间隔时间可以按业界标准的60秒。
     * 短信触发时若头部与明细返回码都返回0000，则会给持卡人发送验证码。
     * 测试环境默认短信验证码为111111。
     */
    @ApiOperation(value = "绑卡验证码发送")
    @PostMapping("/SMSTriggered")
    public Result<String> sMSTriggered(@RequestBody TongLianTongSMSTriggeredDTO tongLianTongSMSTriggeredDTO) {
        return Result.success(tongLianTongService.sMSTriggered(tongLianTongSMSTriggeredDTO));
    }

    /**
     * 3.1.2 协议支付签约(310002)
     * 3.1.2.1 报文功能
     * 短信验证码有效确认时间为2-10分钟。
     * 当客户端没有获取到协议支付签约结果时，可不经过签约短信触发重新发起协议支付签约，但REQ_SN流水号仍需保证不能重复。
     * 同一商户并且同一持卡人，若验证信息正确，则返回的协议号是一样的，返回码也都是0000。
     * @param tongLianTongSignDTO
     * @return
     */
    @ApiOperation(value = "通联绑卡验证")
    @PostMapping("/paymentSign")
    public Result<String> paymentSign(@RequestBody TongLianTongSignDTO tongLianTongSignDTO) {
        return Result.success(tongLianTongService.paymentSign(tongLianTongSignDTO));
    }
    /**
     * 3.1.3协议支付解约(310003)
     */
    @ApiOperation(value = "协议支付解约")
    @PostMapping("/protocolPayCancel")
    public Result<Boolean> protocolPayCancel(@RequestBody TongLianTongProtocolPayCancelDTO tongLianTongProtocolPayCancelDTO) {
        return Result.success(tongLianTongService.protocolPayCancel(tongLianTongProtocolPayCancelDTO));
    }
    /**
     * 3.1.4协议支付(310011)
     * 3.1.4.1 报文功能
     * 当持卡人在银行端变更了手机号，或者在网银取消了快捷协议，导致协议无效，则协议支付时会返回3043（未与银行签约）的错误，商户针对该错误码需引导持卡人重新进行签约。
     */
    @PostMapping("/negotiatedPayments")
    public Result<TongLianTongProtocolPayVO> negotiatedPayments(@RequestBody TongLianTongNegotiatedPaymentsDTO tongLianTongNegotiatedPaymentsDTO) {
        TongLianTongProtocolPayVO tongLianTongProtocolPayVO = tongLianTongService.negotiatedPayments(tongLianTongNegotiatedPaymentsDTO);
        return Result.success(tongLianTongProtocolPayVO);
    }
    /**
     * 3.9.2交易结果查询(200004)
     * 3.9.2.1 报文功能
     * 只能查询40天之内的交易数据
     * 单笔实时交易结果的查询
     * l 对于某笔超时的实时交易需要查询结果，超时后3分钟内，相邻查询时间间隔不应短于20秒
     * l 在超时后3-10分钟内，相邻查询时间间隔不应短于1分钟
     * l 在超时后10分钟以上的，相邻查询时间间隔不应短于5分钟
     * l 对于30分钟内通联一直返回1002的，应确认该笔交易失败，通联没有成功接收，应立刻停止继续查询。
     * 批量交易结果的查询
     * l 建议至少间隔5分钟查询一次
     * l 对于50分钟内通联一直返回1002的，应确认该笔交易失败，通联没有成功接收，应立刻停止继续查询。
     */
    @PostMapping("/transactionResultInquiry")
    public Result<List<TongLianTongQtransreqVO>> transactionResultInquiry(@RequestBody TongLianTongQtransreqDTO tongLianTongQtransreqDTO) {
        List<TongLianTongQtransreqVO> result = tongLianTongService.transactionResultInquiry(tongLianTongQtransreqDTO);
        return Result.success(result);
    }
    /**
     * 3.7.1  退款(REFUND)
     * 3.7.1.1  报文功能
     * 该接口适合收款类交易类型的退款，包括单笔/批量协议支付、直接支付、批量代收、实时代收。
     * 退款接口业务类型用：09200 商户退款
     */
    @PostMapping("/refund")
    public Result<TongLianTongRefundVO> refund(@RequestBody TongLianTongRefundDTO tongLianTongRefundDTO){
        return Result.success(tongLianTongService.refund(tongLianTongRefundDTO));
    }
    @PostMapping("/repay")
    public Result<Boolean> repay(@RequestParam("orderId")Integer orderId, @RequestParam("term") Integer term) {
        return tongLianTongService.matchDeductApply(orderId,term,false,null);
    }
    @PostMapping("/repayQuery")
    public Result<FundDeductQueryVO> repayQuery(@RequestParam("orderId")Integer orderId, @RequestParam("term") Integer term) {
        return Result.success(tongLianTongService.deductQuery(orderId, term,false));
    }
}
