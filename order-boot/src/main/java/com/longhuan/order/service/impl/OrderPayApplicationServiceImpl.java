package com.longhuan.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.toolkit.SpringContentUtils;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.approve.api.pojo.dto.DaiChangChaZhangTongZhiDTO;
import com.longhuan.approve.api.pojo.dto.DaiChangJieQingTongZhiResultDTO;
import com.longhuan.approve.api.pojo.dto.FundRepayCalcDTO;
import com.longhuan.approve.api.pojo.dto.RepurchaseRepayCalcDTO;
import com.longhuan.approve.api.pojo.vo.ZhongHengApiResult;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.constant.GlobalConstants;
import com.longhuan.common.core.constant.MsgConstants;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.constants.SwitchConstants;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.pojo.SwitchVO;
import com.longhuan.common.redis.util.DictUtils;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.order.converter.OrderPayApplyConverter;
import com.longhuan.order.enums.*;
import com.longhuan.order.feign.*;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.MenuVO;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.*;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.order.util.DigitalizeZhongxinEncryptUtil;
import com.longhuan.order.util.EasyExcelUtil;
import com.longhuan.order.util.ExportExcelUtils;
import com.longhuan.resource.pojo.vo.FileVO;
import com.longhuan.user.enums.DingTaskConclusionEnum;
import com.longhuan.user.pojo.dto.MessageContent;
import com.longhuan.user.pojo.dto.SearchUserSyncInfoDTO;
import com.longhuan.user.pojo.vo.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.longhuan.order.constants.OrderPayConstants.*;


/**
 * 付款申请表
 *
 * <AUTHOR>
 * @date 2024/10/29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderPayApplicationServiceImpl implements OrderPayApplicationService {

    //对公转账枚举
    final List<OrderFeeDetailExpandTypeEnum> offlineFeeEnumList = List.of(OrderFeeDetailExpandTypeEnum.INSTALLMENT_SECURITY_DEPOSIT, OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT,
            OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT, OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY, OrderFeeDetailExpandTypeEnum.INSTALLMENT_SERVICE_FEE);

    private final FundDeductService fundDeductService;
    private final OrderPayApplicationMapper orderPayApplicationMapper;
    private final DataPermissionService dataPermissionService;
    private final OrderInfoMapper orderInfoMapper;
    private final BankAccountSignMapper bankAccountSignMapper;
    private final ManageBankAccountSignMapper manageBankAccountSignMapper;
    private final OrderFeeInfoMapper orderFeeInfoMapper;
    private final OrderAmountMapper orderAmountMapper;
    private final ProductInfoMapper productInfoMapper;
    private final UserFeign userFeign;
    private final DictUtils dictUtils;
    private final AmountService amountService;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final SwitchUtils switchUtils;
    private final OrderFeeDetailService orderFeeDetailService;
    private final FundRepurchaseAccountMapper fundRepurchaseAccountMapper;
    private final RepurchaseRepayService repurchaseRepayService;
    private final OrderPayApplyNodeRecordMapper orderPayApplyNodeRecordMapper;
    private final ResourceFeign resourceFeign;
    private final EnvUtil envUtil;
    private final DingTaskFeign dingTaskFeign;
    private final OrderPayApplyConverter orderPayApplyConverter;
    private final PreOcrIdentityCardService preOcrIdentityCardService;
    private final OrderFileMapper orderFileMapper;
    private final FundRepaymentDeductService fundRepaymentDeductService;
    private final OrderService orderService;
    private final FundRepaymentDeductMapper fundRepaymentDeductMapper;
    private final ApproveFeign approveFeign;
    private final SprEductionUsageService sprEductionUsageService;
    private final MessageFeign messageFeign;
    private final RepurchaseRepaymentInfoMapper repurchaseRepaymentInfoMapper;
    private final RepurchaseProcessService repurchaseProcessService;
    private final ZhongXinService zhongXinService;
    private final TongLianTongService tongLianTongService;
    private final OrderCustomerInfoMapper orderCustomerInfoMapper;
    private final PreApprovalApplyInfoMapper preApprovalApplyInfoMapper;
    private final DigitalizeFeign digitalizeFeign;
    private final DigitalizeZhongxinEncryptUtil digitalizeZhongxinEncryptUtil;
    private final DigitalOutsourcingOrderEntityMapper digitalOutsourcingOrderEntityMapper;
    private final CaseInfoEntityMapper caseInfoEntityMapper;
    private final ExemptionApplicationEntityMapper exemptionApplicationEntityMapper;
    private final OrderStateService orderStateService;
    private final CaseInfoServiceImpl caseInfoServiceImpl;
    private final KingdeeOutsourcingService kingdeeOutsourcingService;
    private final OrderPayApplicationPriveteMethod orderPayApplicationPriveteMethod;
    private final OrderSettleAmountRecordMapper orderSettleAmountRecordMapper;
    private final OrderDetailsInfoService orderDetailsInfoService;
    private final OutsourcingSettlementTrialCalculationsService outsourcingSettlementTrialCalculationsService;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final OutsourcingContractService outsourcingContractService;
    private final CaseUpdateFieldRecordsMapper caseUpdateFieldRecordsMapper;
	private final FundRepurchaseResultMapper fundRepurchaseResultMapper;


	@Override
    public boolean orderPayApplication(PayApplicationDTO payApplicationDTO, LoginUser loginUser) {
        log.info("OrderPayApplicationServiceImpl OrderPayApplication start payApplicationDTO:{}", payApplicationDTO);

        // 保存申请单
        OrderPayApplicationInfoEntity payApplicationInfo = orderPayApplicationPriveteMethod.saveOrderPayApplication(payApplicationDTO, loginUser);
        if (ObjUtil.isNull(payApplicationInfo)) {
            log.warn("OrderPayApplicationServiceImpl OrderPayApplication failed to save application for payApplicationDTO:{}", payApplicationDTO);
            return false;
        }
        boolean applyFlag = true;

        List<PayApplicationNodeEnums> dingTaskApplyNodeList = List.of(PayApplicationNodeEnums.STORE_MANAGER_APPROVAL, PayApplicationNodeEnums.REGION_MANAGER_APPROVAL);
        if (ObjUtil.isNotNull(payApplicationInfo.getCurrentNode()) && dingTaskApplyNodeList.contains(payApplicationInfo.getCurrentNode())) {
            // 发起钉钉审批
            applyFlag = orderPayApplicationPriveteMethod.initiateDingTalkApproval(payApplicationDTO.getResourceId(), payApplicationInfo, loginUser.getUserId());
        }

        orderPayApplicationPriveteMethod.saveNodeRecord(payApplicationInfo.getId(), PayApplicationNodeEnums.ACCOUNTANT_APPLY, payApplicationInfo.getCurrentNode(), PayApplicationAuditTypeEnum.YUNQI
                , null, payApplicationDTO.getRemark(), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), LocalDateTime.now());

        log.info("OrderPayApplicationServiceImpl OrderPayApplication applyFlag:{}", applyFlag);
        return applyFlag;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approvePayApplication(List<ApprovalPayApplicationDTO> approvalPayApplicationDTOs, Integer currentUserId, boolean isDingTask) {
        List<OrderPayApplicationInfoEntity> orderPayApplicationInfoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(approvalPayApplicationDTOs)) {
            orderPayApplicationInfoList = orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                    .in(OrderPayApplicationInfoEntity::getId, approvalPayApplicationDTOs.stream().map(ApprovalPayApplicationDTO::getId).collect(Collectors.toList()))
                    .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
            );
        }
        if (CollUtil.isEmpty(orderPayApplicationInfoList)) {
            log.info("OrderPayApplicationServiceImpl approvePayApplication orderPayApplicationInfoList is empty");
            return true;
        }
        Map<Integer, OrderPayApplicationInfoEntity> orderPayApplicationInfoMap =
                orderPayApplicationInfoList.stream()
                        .collect(Collectors.toMap(OrderPayApplicationInfoEntity::getId, Function.identity()));
        if (CollUtil.isEmpty(orderPayApplicationInfoMap)) {
            log.info("OrderPayApplicationServiceImpl approvePayApplication orderPayApplicationInfoMap is empty");
            return true;
        }


        log.info("OrderPayApplicationServiceImpl ApprovePayApplication start approvalPayApplicationDTO:{}", JSONUtil.toJsonStr(approvalPayApplicationDTOs));
        for (ApprovalPayApplicationDTO approvalPayApplicationDTO : approvalPayApplicationDTOs) {
            Integer operatorUserId = isDingTask ? approvalPayApplicationDTO.getOperatorUserId() : currentUserId;

            OrderPayApplicationInfoEntity orderPayApplicationInfoEntity = orderPayApplicationInfoMap.get(approvalPayApplicationDTO.getId());
            Assert.notNull(orderPayApplicationInfoEntity, () -> new BusinessException("付款单信息不存在"));
            log.info("OrderPayApplicationServiceImpl ApprovePayApplication retrieved orderPayApplicationInfoEntity:{}", orderPayApplicationInfoEntity);

            // 获取当前节点
            PayApplicationNodeEnums currentNode = orderPayApplicationInfoEntity.getCurrentNode();
            PayApplicationNodeEnums nextNode = null;
            PayApplicationEventEnums event = PayApplicationEventEnums.APPROVE_PASS; // 默认通过

            OrderPayApplicationInfoEntity updateOrderPayApplicationInfoEntity = new OrderPayApplicationInfoEntity()
                    .setId(orderPayApplicationInfoEntity.getId())
                    .setPaymentDetails(orderPayApplicationInfoEntity.getPaymentDetails());

            // 根据当前节点和审批结论确定下一个节点
            switch (currentNode) {
                case ACCOUNTANT_APPROVAL:
                    // 设置会计审核信息
                    updateOrderPayApplicationInfoEntity.setAccountantRemark(approvalPayApplicationDTO.getRemark())
                            .setAccountantUserId(operatorUserId)
                            .setAccountantApproveTime(LocalDateTime.now());
                    if (approvalPayApplicationDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {
                        // 审核通过，下一个节点为出纳审批
                        nextNode = PayApplicationNodeEnums.CASHIER_APPROVAL;
                    } else if (approvalPayApplicationDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
                        // 审核拒绝，下一个节点为流程终止
                        nextNode = PayApplicationNodeEnums.FAIL;
                        event = PayApplicationEventEnums.APPROVE_REJECT;
                    } else if (approvalPayApplicationDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
                        // 驳回，下一个节点为付款单申请
                        nextNode = PayApplicationNodeEnums.ACCOUNTANT_APPLY;
                        event = PayApplicationEventEnums.APPROVE_REVOKE;
                    }
                    break;
                case CASHIER_APPROVAL:
                    OrderInfoEntity orderInfo = new OrderInfoEntity();
                    FundRepaymentDeductEntity one = new FundRepaymentDeductEntity();
                    if (ObjUtil.equal(orderPayApplicationInfoEntity.getOrderSource(), 1)) {
                        orderInfo = orderInfoMapper.selectById(orderPayApplicationInfoEntity.getOrderId());
                        one = fundRepaymentDeductService.getOne(
                                new LambdaUpdateWrapper<FundRepaymentDeductEntity>()
                                        .eq(FundRepaymentDeductEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                        .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                                        .eq(FundRepaymentDeductEntity::getBizType, FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION)
                                        .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)
                        );
                        Assert.notNull(orderInfo, "订单信息不存在");
                    }

                    // 设置出纳审核信息
                    updateOrderPayApplicationInfoEntity.setCashierRemark(approvalPayApplicationDTO.getRemark())
                            .setCashierUserId(operatorUserId)
                            .setCashierApproveTime(LocalDateTime.now());
                    if (approvalPayApplicationDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {
                        if (ObjUtil.isNull(orderPayApplicationInfoEntity.getPaymentTime())) {
                            updateOrderPayApplicationInfoEntity.setPaymentTime(LocalDateTime.now());
                        }

						if(Objects.equals(orderPayApplicationInfoEntity.getPayeeType(),PayApplicationPayeeTypeEnum.ZHONG_HENG) &&
								(Objects.equals(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.SINGLE_PERIOD_COMPENSATION) ||
										Objects.equals(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT))){

							//单期代偿
							if (Objects.equals(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.SINGLE_PERIOD_COMPENSATION)){
								// 资方终审信息
								FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
										.eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
										.in(FinalFundInfoEntity::getFundId, Arrays.asList(FundEnum.ZHONG_HENG.getValue(), FundEnum.ZHONG_HENG_TONG_HUI.getValue()))
										.eq(FinalFundInfoEntity::getDeleteFlag, 0)
										.last("LIMIT 1"));
								//Assert.notNull(finalFundInfo, () -> new BusinessException("资方终审信息不存在"));

								FundRepaymentInfoEntity fundRepaymentInfoEntity = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
										.eq(FundRepaymentInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
										.eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
										.eq(FundRepaymentInfoEntity::getTerm, orderPayApplicationInfoEntity.getRepaymentTerm()));

								if (finalFundInfo != null && fundRepaymentInfoEntity != null) {

									var dcTime = Optional.ofNullable(orderPayApplicationInfoEntity.getPaymentTime())
											.map(Convert::toLocalDateTime)
											.orElse(LocalDateTime.now())
											.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

									DaiChangChaZhangTongZhiDTO dto = new DaiChangChaZhangTongZhiDTO();
									dto.setOrderNum(finalFundInfo.getLoanBillNo());
									dto.setSpOrderNum(orderInfo.getOrderNumber());
									dto.setDcOrderNum(orderPayApplicationInfoEntity.getPaymentDetails());
									dto.setDcHeJi(fundRepaymentInfoEntity.getRepaymentAmountTotal().toString());
									dto.setDcBenJin(fundRepaymentInfoEntity.getRepaymentPrincipal().toString());
									dto.setDcLiXi(fundRepaymentInfoEntity.getRepaymentInterest().toString());
									dto.setDcWeiYueJin(Convert.toBigDecimal(fundRepaymentInfoEntity.getRepaymentPenaltyInterest(),
											BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP)).toString());
									dto.setDcYinLiuFei(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toString());
									dto.setDcTime(dcTime);
									dto.setFuKuanYinHang(orderPayApplicationInfoEntity.getPayAccountName());
									dto.setFuKuanYinHangNum(orderPayApplicationInfoEntity.getPayAccountNumber());

									ZhongHengApiResult<Void> voidZhongHengApiResult = approveFeign.daichangChaZhangTZ(dto);
									if (ZhongHengApiResult.isSuccess(voidZhongHengApiResult)) {
										log.info("单期代偿-代偿查账通知成功! 代偿单编号:{}",orderPayApplicationInfoEntity.getPaymentDetails());
									}else {
										throw new BusinessException(voidZhongHengApiResult.getMsg());
									}
								}
							}

							//整笔赎回
							if (Objects.equals(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)){
								// 资方终审信息
								FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
										.eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
										.in(FinalFundInfoEntity::getFundId, Arrays.asList(FundEnum.ZHONG_HENG.getValue(), FundEnum.ZHONG_HENG_TONG_HUI.getValue()))
										.eq(FinalFundInfoEntity::getDeleteFlag, 0)
										.last("LIMIT 1"));

								FundRepurchaseResultEntity fundRepurchaseResult = fundRepurchaseResultMapper.selectOne(new LambdaQueryWrapper<FundRepurchaseResultEntity>()
										.eq(FundRepurchaseResultEntity::getOrderId,orderInfo.getId())
										.eq(FundRepurchaseResultEntity::getEventStartTerm,orderPayApplicationInfoEntity.getRepaymentTerm())
										.eq(FundRepurchaseResultEntity::getDeleteFlag, 0)
										.orderByDesc(FundRepurchaseResultEntity::getId)
										.last("LIMIT 1")
								);

								if (finalFundInfo != null && fundRepurchaseResult != null) {
									var dcTime = Optional.ofNullable(orderPayApplicationInfoEntity.getPaymentTime())
											.map(Convert::toLocalDateTime)
											.orElse(LocalDateTime.now())
											.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

									DaiChangChaZhangTongZhiDTO dto = new DaiChangChaZhangTongZhiDTO();
									dto.setOrderNum(finalFundInfo.getLoanBillNo());
									dto.setSpOrderNum(orderInfo.getOrderNumber());
									dto.setDcOrderNum(orderPayApplicationInfoEntity.getPaymentDetails());
									dto.setDcHeJi(fundRepurchaseResult.getPreRepayAmt().toString());
									dto.setDcBenJin(fundRepurchaseResult.getPrePrincipal().toString());
									dto.setDcLiXi(fundRepurchaseResult.getPreInterest().toString());
									dto.setDcWeiYueJin(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toString());
									dto.setDcYinLiuFei(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toString());
									dto.setDcTime(dcTime);
									dto.setFuKuanYinHang(orderPayApplicationInfoEntity.getPayAccountName());
									dto.setFuKuanYinHangNum(orderPayApplicationInfoEntity.getPayAccountNumber());

									ZhongHengApiResult<Void> voidZhongHengApiResult = approveFeign.daichangChaZhangTZ(dto);
									if (ZhongHengApiResult.isSuccess(voidZhongHengApiResult)) {
										log.info("整笔代偿-代偿查账通知成功! 代偿单编号:{}",orderPayApplicationInfoEntity.getPaymentDetails());
									}else {
										throw new BusinessException(voidZhongHengApiResult.getMsg());
									}
								}
							}

							// 审核通过，下一个节点为资方审核中
							nextNode = PayApplicationNodeEnums.APPROVE_APPROVAL;

						}
						else {
							// 审核通过，下一个节点为申请成功
							nextNode = PayApplicationNodeEnums.SUCCESS;
						}

                        log.info("OrderPayApplicationServiceImpl ApprovePayApplication saved order fee details for orderId:{}", orderPayApplicationInfoEntity.getOrderId());
                        // 出纳审批通过时，调用资方赎回申请接口
                        if (Objects.equals(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)) {
                            if (Objects.equals(orderInfo.getFundId(), FundEnum.YING_FENG.getValue())) {
                                log.info("OrderPayApplicationServiceImpl.approvePayApplication assetRepayment");
                                RepurchaseRepayCalcDTO repurchaseRepayCalcDTO = new RepurchaseRepayCalcDTO();
                                repurchaseRepayCalcDTO.setOrderId(orderPayApplicationInfoEntity.getOrderId());
                                Boolean result = repurchaseRepayService.assetRepayment(repurchaseRepayCalcDTO);
                                log.info("OrderPayApplicationServiceImpl.approvePayApplication assetRepayment result:{}", result);
                            }
                            if (ObjUtil.equal(orderInfo.getFundId(), FundEnum.LAN_HAI.getValue())) {
                                log.info("OrderPayApplicationServiceImpl.approvePayApplication lanHaiCompensateApply");
                                Result<Boolean> booleanResult = approveFeign.lanHaiCompensateApply(orderPayApplicationInfoEntity.getOrderId());
                                log.info("OrderPayApplicationServiceImpl.approvePayApplication lanHaiCompensateApply result:{}", booleanResult.getData());
                            }

                        }

                        if (Objects.equals(orderPayApplicationInfoEntity.getFeeDetails(), 2)) {
                            if (Objects.equals(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY)) {
                                sprEductionUsageService.update(
                                        new LambdaUpdateWrapper<SprEductionUsageEntity>()
                                                .eq(SprEductionUsageEntity::getVehicleNumber, orderInfo.getVehicleNumber())
                                                .eq(SprEductionUsageEntity::getDeleteFlag, 0)
                                                .set(SprEductionUsageEntity::getUsageStatus, 1)
                                );
                                FundRepaymentDeductEntity one1 = fundRepaymentDeductService.getOne(
                                        new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                                                .eq(FundRepaymentDeductEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                .eq(FundRepaymentDeductEntity::getDeleteFlag, 0)
                                                .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_PROCESSING)
                                                .eq(FundRepaymentDeductEntity::getBizType, FundDeductBizTypeEnums.PENALTY_INITIATE_REPAYMENT_AFTER_PAYMENT)
                                );
                                if (ObjUtil.isNotNull(one1)) {
                                    one1.setFundId(orderInfo.getFundId());
                                    one1.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_SUCCESS);
                                    fundRepaymentDeductService.updateById(one1);
                                    if (Objects.equals(orderInfo.getIsRepurchase(), 0)) {
                                        OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
                                        orderApproveFundPlanStatusDTO.setOrderId(orderInfo.getId());
                                        orderService.updateFundPlanStatus(orderApproveFundPlanStatusDTO);
                                    } else {
                                        tongLianTongService.updateRepayment(orderInfo.getId());
                                    }

                                } else {
                                    try {
                                        List<FundRepaymentDeductEntity> list = fundRepaymentDeductService.list(new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                                                .eq(FundRepaymentDeductEntity::getOrderId, one.getOrderId())
                                                .in(FundRepaymentDeductEntity::getBizType, Arrays.asList(
                                                        FundDeductBizTypeEnums.PENALTY_DEDUCTION,
                                                        FundDeductBizTypeEnums.PENALTY_PAYMENT,
                                                        FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION
                                                )));
//                                        List<OrderPayApplicationInfoEntity> preApprovalApplyInfoEntities = orderPayApplicationMapper.selectList(
//                                                new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
//                                                        .eq(OrderPayApplicationInfoEntity::getOrderId, one.getOrderId())
//                                                        .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY)
//                                        );
//                                        BigDecimal reduce1 = preApprovalApplyInfoEntities.stream()
//                                                .filter(entity -> entity.getCurrentNode().equals(PayApplicationNodeEnums.SUCCESS))
//                                                .map(OrderPayApplicationInfoEntity::getReductionAmount)
//                                                .reduce(BigDecimal.ZERO, BigDecimal::add);
//                                        BigDecimal reduce = list.stream()
//                                                .filter(entity -> entity.getBizType().equals(FundDeductBizTypeEnums.PENALTY_PUBLIC_ACCOUNT_APPLICATION)
//                                                        && entity.getRepayStatus().equals(FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
//                                                        && entity.getRepayType().equals(FundDeductRepayTypeEnums.EARLY_SETTLEMENT))
//                                                .map(FundRepaymentDeductEntity::getDeductAmount)
//                                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                                        FundEarlyRepaymentCalcDTO fundEarlyRepaymentCalcDTO = fundDeductService.earlyRepayCalc(new FundRepayCalcEarlyDTO().setOrderId(one.getOrderId()).setLoanSettlementMethod(orderPayApplicationInfoEntity.getLoanSettlementMethod()));
//                                    Objects.equals(repaymentPrincipal.compareTo(actuallyPrincipal),0)
                                        if (fundEarlyRepaymentCalcDTO.getFirmRepayAmountTotal().compareTo((one.getDeductAmount().add(ObjUtil.defaultIfNull(orderPayApplicationInfoEntity.getReductionAmount(), BigDecimal.ZERO)))) <= 0) {
                                            FundRepaymentDeductEntity deductEntity = new FundRepaymentDeductEntity();
                                            deductEntity.setOrderId(one.getOrderId());
                                            deductEntity.setDeductReqNo(one.getDeductReqNo());
                                            deductEntity.setIndex(list.size());
                                            deductEntity.setRepayDate(one.getRepayDate());
                                            deductEntity.setRepayType(FundDeductRepayTypeEnums.EARLY_SETTLEMENT);
                                            deductEntity.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_SUCCESS);
                                            deductEntity.setBizType(FundDeductBizTypeEnums.PENALTY_INITIATE_REPAYMENT_AFTER_PAYMENT);
                                            deductEntity.setDeductAmount(one.getDeductAmount());
                                            deductEntity.setTerm(one.getTerm());
                                            deductEntity.setFundId(orderInfo.getFundId());
                                            fundRepaymentDeductService.save(deductEntity);
                                            if (Objects.equals(orderInfo.getIsRepurchase(), 0)) {
                                                OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
                                                orderApproveFundPlanStatusDTO.setOrderId(orderInfo.getId());
                                                orderService.updateFundPlanStatus(orderApproveFundPlanStatusDTO);
                                            } else {
                                                tongLianTongService.updateRepayment(orderInfo.getId());
                                            }

                                        }
                                    } catch (Exception e) {
                                        log.info("OrderPayApplicationServiceImpl.approvePayApplication assetRepayment error:{}", e.getMessage());
                                    } finally {
                                        log.info("OrderPayApplicationServiceImpl.approvePayApplication assetRepayment end");
                                    }

                                }
                                one.setFundId(orderInfo.getFundId());
                                one.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_SUCCESS);
                                fundRepaymentDeductService.updateById(one);
                            }
                            if (Objects.equals(orderPayApplicationInfoEntity.getOrderApplicationSource(), 1)) {
                                Result<UserInfoVO> userInfoVOResult = userFeign.searchUserName(orderPayApplicationInfoEntity.getApplyUserId());
                                String userName;
                                if (Result.isSuccess(userInfoVOResult)) {
                                    UserInfoVO data = userInfoVOResult.getData();
                                    userName = data.getName();

                                } else {
                                    userName = "";
                                }
                                //todo 委外对公查账通过
                                if (ObjUtil.equal(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT)) {
                                    //todo 分期还款
                                    switch (orderPayApplicationInfoEntity.getOrderSource()) {
                                        case 1 -> {
                                            Boolean isClearedOrNot = false;
                                            //todo 云起月还款
                                            if (ObjUtil.equal(orderInfo.getIsRepurchase(), 1)) {
                                                FundRepaymentDeductEntity repaymentDeduct = orderPayApplicationPriveteMethod.repurchaseRepayment(orderPayApplicationInfoEntity, updateOrderPayApplicationInfoEntity.getCashierApproveTime());
                                                updateOrderPayApplicationInfoEntity.setPaymentDetails(ObjUtil.isNotNull(repaymentDeduct) ? repaymentDeduct.getDeductReqNo() : updateOrderPayApplicationInfoEntity.getId().toString());
                                            } else {
                                                FundRepaymentInfoEntity fundRepaymentInfoEntity = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                                        .eq(FundRepaymentInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                                        .eq(FundRepaymentInfoEntity::getTerm, orderPayApplicationInfoEntity.getRepaymentTerm()));
                                                // 需要分配的金额
                                                BigDecimal remainingAmount = orderPayApplicationInfoEntity.getPayeeAmount();
                                                BigDecimal remainingPrincipal = fundRepaymentInfoEntity.getRepaymentPrincipal()
                                                        .subtract(ObjUtil.defaultIfNull(fundRepaymentInfoEntity.getActuallyPrincipal(), BigDecimal.ZERO));
                                                BigDecimal remainingInterest = fundRepaymentInfoEntity.getRepaymentInterest()
                                                        .subtract(ObjUtil.defaultIfNull(fundRepaymentInfoEntity.getActuallyInterest(), BigDecimal.ZERO));
                                                BigDecimal remainingPenalty = fundRepaymentInfoEntity.getRepaymentPenalty()
                                                        .subtract(ObjUtil.defaultIfNull(fundRepaymentInfoEntity.getActuallyPenalty(), BigDecimal.ZERO));
                                                if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                                                    BigDecimal principalAdd = remainingPrincipal.min(remainingAmount);
                                                    fundRepaymentInfoEntity.setActuallyPrincipal(
                                                            ObjUtil.defaultIfNull(fundRepaymentInfoEntity.getActuallyPrincipal(), BigDecimal.ZERO).add(principalAdd));
                                                    remainingAmount = remainingAmount.subtract(principalAdd);
                                                }
                                                if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                                                    BigDecimal interestAdd = remainingInterest.min(remainingAmount);
                                                    fundRepaymentInfoEntity.setActuallyInterest(
                                                            ObjUtil.defaultIfNull(fundRepaymentInfoEntity.getActuallyInterest(), BigDecimal.ZERO).add(interestAdd));
                                                    remainingAmount = remainingAmount.subtract(interestAdd);
                                                }
                                                if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                                                    BigDecimal penaltyAdd = remainingPenalty.min(remainingAmount);
                                                    fundRepaymentInfoEntity.setActuallyPenalty(
                                                            ObjUtil.defaultIfNull(fundRepaymentInfoEntity.getActuallyPenalty(), BigDecimal.ZERO).add(penaltyAdd));
                                                    remainingAmount = remainingAmount.subtract(penaltyAdd);
                                                }
                                                if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                                                    fundRepaymentInfoEntity.setActuallyPenaltyInterest(
                                                            ObjUtil.defaultIfNull(fundRepaymentInfoEntity.getActuallyPenaltyInterest(), BigDecimal.ZERO).add(remainingAmount));
                                                }
                                                fundRepaymentInfoEntity.setActuallyAmountTotal(ObjUtil.defaultIfNull(fundRepaymentInfoEntity.getActuallyAmountTotal(), BigDecimal.ZERO).add(orderPayApplicationInfoEntity.getPayeeAmount()));
                                                fundRepaymentInfoEntity.setOutsourceReductionAmount(ObjUtil.defaultIfNull(fundRepaymentInfoEntity.getOutsourceReductionAmount(), BigDecimal.ZERO).add(orderPayApplicationInfoEntity.getReductionAmount()));
                                                fundRepaymentInfoEntity.setUpdateTime(LocalDateTime.now());
                                                if (fundRepaymentInfoEntity.getActuallyAmountTotal().compareTo(fundRepaymentInfoEntity.getRepaymentAmountTotal().add(fundRepaymentInfoEntity.getReductionAmount())) >= 0) {
                                                    fundRepaymentInfoEntity.setActuallyDate(updateOrderPayApplicationInfoEntity.getCashierApproveTime().toLocalDate());
                                                    fundRepaymentInfoEntity.setRepaymentStatus(FundRepayStatusEnum.SETTLED);
                                                }
                                                fundRepaymentInfoMapper.updateById(fundRepaymentInfoEntity);
                                                orderService.updateOrderFundRepayment(orderPayApplicationInfoEntity.getOrderId());
                                                List<FundRepaymentInfoEntity> fundRepaymentInfoEntities = fundRepaymentInfoMapper.selectList(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                                        .eq(FundRepaymentInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                                        .orderByDesc(FundRepaymentInfoEntity::getTerm));
                                                Optional<FundRepaymentInfoEntity> first = fundRepaymentInfoEntities.stream().filter(e -> ObjUtil.notEqual(e.getRepaymentStatus(), (FundRepayStatusEnum.SETTLED)))
                                                        .findFirst();
                                                if (first.isEmpty()) {
                                                    int count = 0;
                                                    CaseInfoEntity caseInfoEntity1 = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                                            .eq(CaseInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                            .eq(CaseInfoEntity::getDeleteFlag, 0)
                                                            .eq(CaseInfoEntity::getDataSource, 1)
                                                            .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                                                    count = settleCaseInfo(caseInfoEntity1, count, orderPayApplicationInfoEntity, userName);
                                                    if (count > 0) {
                                                        BigDecimal repaymentAmountTotal = fundRepaymentInfoEntities.stream().map(FundRepaymentInfoEntity::getRepaymentAmountTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                                                        BigDecimal actuallyAmountTotal = fundRepaymentInfoEntities.stream().map(FundRepaymentInfoEntity::getActuallyAmountTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
                                                        orderSettleAmountRecordMapper.update(new LambdaUpdateWrapper<OrderSettleAmountRecordEntity>()
                                                                .set(OrderSettleAmountRecordEntity::getDeleteFlag, 1)
                                                                .eq(OrderSettleAmountRecordEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                                .eq(OrderSettleAmountRecordEntity::getDeleteFlag, 0));
                                                        OrderSettleAmountRecordEntity entity = new OrderSettleAmountRecordEntity()
                                                                .setOrderId(orderPayApplicationInfoEntity.getOrderId())
                                                                .setOrderSource(orderPayApplicationInfoEntity.getOrderSource())
                                                                .setOrderNumber(orderPayApplicationInfoEntity.getOrderNumber())
                                                                .setTrialSettlementAmount(repaymentAmountTotal)
                                                                .setActualSettlementAmount(actuallyAmountTotal)
                                                                .setOutsourcingStatus(1)
                                                                .setSettlementMethod("正常结清")
                                                                .setRemainingPrincipal(remainingPrincipal)
                                                                .setInstalmentsRepaid(fundRepaymentInfoEntity.getTerm() - 1);
                                                        orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                                                                        .eq(OrderPayApplicationInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                                        .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                                                                        .eq(OrderPayApplicationInfoEntity::getApplyType, 1)
                                                                        .in(OrderPayApplicationInfoEntity::getFeeType, List.of(OrderFeeDetailExpandTypeEnum.OTHER,
                                                                                OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE, OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT,
                                                                                OrderFeeDetailExpandTypeEnum.DISPOSABLE_SECURITY_DEPOSIT, OrderFeeDetailExpandTypeEnum.INSTALLMENT_SERVICE_FEE,
                                                                                OrderFeeDetailExpandTypeEnum.GPS_DATA_TRANSFER_FEE, OrderFeeDetailExpandTypeEnum.GPS_EQUIPMENT_COMPENSATION_FEE))
                                                                        .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)).stream().map(OrderPayApplicationInfoEntity::getPayeeAmount)
                                                                .reduce(BigDecimal::add).ifPresentOrElse(entity::setRefundsTotalAmount, () -> entity.setRefundsTotalAmount(BigDecimal.ZERO));
                                                        orderSettleAmountRecordMapper.insert(entity
                                                        );
                                                        OrderStateService bean = SpringContentUtils.getBean(OrderStateService.class);
                                                        bean.sendEvent(States.PAYMENT_SUCCESS, Events.SETTLED, orderPayApplicationInfoEntity.getOrderId(), currentUserId);
                                                        orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                                                                .set(OrderInfoEntity::getPlanState, 2)
                                                                .set(OrderInfoEntity::getCompanyPlanState, 1)
                                                                .eq(OrderInfoEntity::getId, orderPayApplicationInfoEntity.getOrderId()));
                                                        isClearedOrNot = true;
                                                    }
                                                }
                                            }
                                            try {
                                                CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                                        .eq(CaseInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                        .eq(CaseInfoEntity::getDeleteFlag, 0)
                                                        .eq(CaseInfoEntity::getDataSource, 1)
                                                        .eq(CaseInfoEntity::getDeptId, DeptEnum.ZHONG_XIN.getId())
                                                        .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                                                if (caseInfoEntity != null) {
                                                    zhongXinService.pushToZhongXinAsRetrieveAccept(new ReceivePaymentDTO()
                                                            .setOrderId(orderPayApplicationInfoEntity.getOrderId())
                                                            .setTerm(orderPayApplicationInfoEntity.getRepaymentTerm())
                                                            .setAmount(orderPayApplicationInfoEntity.getPayeeAmount())
                                                            .setTradingTime(LocalDateTime.now())
                                                            .setPayer(orderInfo.getCustomerName())
                                                            .setPayee(FundEnum.getFundEnum(orderInfo.getFundId()).getFundName())
                                                            .setTradingMethods(OrderFeeDetailTradingMethodsEnum.CORPORATE_TRANSFERS)
                                                            .setSettling(2));
                                                    if (isClearedOrNot) {
                                                        zhongXinService.pushToZhongXinAsRetrieveAcceptSettle(
                                                                new ZhongXinAsRetrieveAcceptSettleDTO()
                                                                        .setCase_no(caseInfoEntity.getCaseNo())
                                                                        .setReceivable_amount(orderPayApplicationInfoEntity.getPayeeAmount())
                                                                        .setReduction_amount(BigDecimal.ZERO)
                                                                        .setRemark(orderPayApplicationInfoEntity.getRemark())
                                                                        .setFinance_remark(approvalPayApplicationDTO.getRemark())
                                                        );
                                                    }
                                                }
                                            } catch (Exception e) {
                                                log.error("OrderPayApplicationServiceImpl.repurchaseRepayment sendMessageToZhongXin orderId:{}", orderInfo.getId());
                                            }
                                        }
                                        case 2 -> {
                                            throw new BusinessException("暂不支持数字化月还款");
                                        }
                                        case 3 -> {
                                            throw new BusinessException("暂不支持金蝶月还款");
                                        }
                                    }

                                }
                                if (ObjUtil.equal(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)) {
                                    Boolean isClearedOrNot = false;
                                    //todo 结清
                                    switch (orderPayApplicationInfoEntity.getOrderSource()) {
                                        case 1 -> {
                                            //todo 云起结清
                                            //todo 试算
                                            BigDecimal actualAmount = orderPayApplicationInfoEntity.getPayeeAmount();
                                            BigDecimal reductionAmount = ObjUtil.defaultIfNull(orderPayApplicationInfoEntity.getReductionAmount(), BigDecimal.ZERO);
                                            BigDecimal depositAmount = ObjUtil.defaultIfNull(orderPayApplicationInfoEntity.getDeposit(), BigDecimal.ZERO);
                                            List<OrderPayApplicationInfoEntity> relatedOrders = orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                                                    .eq(OrderPayApplicationInfoEntity::getOrderNumber, orderPayApplicationInfoEntity.getOrderNumber())
                                                    .eq(OrderPayApplicationInfoEntity::getOrderApplicationSource, 1)
                                                    .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)
                                                    .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)
                                                    .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                                                    .eq(OrderPayApplicationInfoEntity::getOrderSource, 1));

                                            for (OrderPayApplicationInfoEntity entity : relatedOrders) {
                                                // 累加实收金额（仅包括 payeeAmount）
                                                actualAmount = actualAmount.add(ObjUtil.defaultIfNull(entity.getPayeeAmount(), BigDecimal.ZERO));
                                                //减免金额
                                                reductionAmount = reductionAmount.add(ObjUtil.defaultIfNull(entity.getReductionAmount(), BigDecimal.ZERO));
                                                //保证金
                                                depositAmount = depositAmount.add(ObjUtil.defaultIfNull(entity.getDeposit(), BigDecimal.ZERO));

                                            }
                                            BigDecimal totalAmount = actualAmount.add(reductionAmount).add(depositAmount);
                                            CaseInfoRepayCalcVO caseInfoRepayCalcVO = caseInfoServiceImpl.earlyRepayCalc(orderPayApplicationInfoEntity.getOrderId());
                                            //赎回结清
                                            if (ObjUtil.equals(orderInfo.getIsRepurchase(),1)){
                                                RepurchaseRepaymentInfoEntity fundRepaymentInfoEntity=repurchaseRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<RepurchaseRepaymentInfoEntity>()
                                                        .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                        .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                                                        .ne(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                                                        .orderByAsc(RepurchaseRepaymentInfoEntity::getTerm), false);
                                                fundRepaymentInfoEntity.setActuallyAmountTotal(ObjUtil.defaultIfNull(fundRepaymentInfoEntity.getActuallyAmountTotal(), BigDecimal.ZERO).add(orderPayApplicationInfoEntity.getPayeeAmount()));
                                                fundRepaymentInfoEntity.setActuallyPrincipal(ObjUtil.defaultIfNull(fundRepaymentInfoEntity.getActuallyPrincipal(), BigDecimal.ZERO).add(orderPayApplicationInfoEntity.getPayeeAmount()));
                                                fundRepaymentInfoEntity.setOutsourceReductionAmount(ObjUtil.defaultIfNull(orderPayApplicationInfoEntity.getReductionAmount(), BigDecimal.ZERO));
                                                fundRepaymentInfoEntity.setUpdateTime(LocalDateTime.now());
                                                if (fundRepaymentInfoEntity.getActuallyAmountTotal().compareTo(fundRepaymentInfoEntity.getRepaymentAmountTotal()) >= 0) {
                                                    fundRepaymentInfoEntity.setActuallyDate(updateOrderPayApplicationInfoEntity.getCashierApproveTime().toLocalDate());
                                                }
                                                repurchaseRepaymentInfoMapper.updateById(fundRepaymentInfoEntity);
                                                //todo 未赎回结清
                                            }else {
                                                FundRepaymentInfoEntity fundRepaymentInfoEntity = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                                        .eq(FundRepaymentInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                                        .ne(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
                                                        .orderByAsc(FundRepaymentInfoEntity::getTerm),false);
                                                //未找到未还期数取最早一期实还为0的期数
                                                if (ObjUtil.isNull(fundRepaymentInfoEntity)){
                                                    fundRepaymentInfoEntity = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                                            .eq(FundRepaymentInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                                            .eq(FundRepaymentInfoEntity::getActuallyAmountTotal, BigDecimal.ZERO)
                                                            .orderByAsc(FundRepaymentInfoEntity::getTerm), false);
                                                    //如果还未找到直接造一条
                                                    if (ObjUtil.isNull(fundRepaymentInfoEntity)){
                                                        fundRepaymentInfoEntity = fundRepaymentInfoMapper.selectOne(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                                                                .eq(FundRepaymentInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                                .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                                                                .eq(FundRepaymentInfoEntity::getActuallyAmountTotal, BigDecimal.ZERO)
                                                                .orderByDesc(FundRepaymentInfoEntity::getTerm), false);
                                                        fundRepaymentInfoEntity.setTerm(fundRepaymentInfoEntity.getTerm() + 1).setId(null);
                                                        fundRepaymentInfoMapper.insert(fundRepaymentInfoEntity);
                                                    }
                                                }
                                                fundRepaymentInfoEntity.setActuallyAmountTotal(ObjUtil.defaultIfNull(fundRepaymentInfoEntity.getActuallyAmountTotal(), BigDecimal.ZERO).add(orderPayApplicationInfoEntity.getPayeeAmount()));
                                                fundRepaymentInfoEntity.setActuallyPrincipal(ObjUtil.defaultIfNull(fundRepaymentInfoEntity.getActuallyPrincipal(), BigDecimal.ZERO).add(orderPayApplicationInfoEntity.getPayeeAmount()));
                                                fundRepaymentInfoEntity.setOutsourceReductionAmount(ObjUtil.defaultIfNull(orderPayApplicationInfoEntity.getReductionAmount(), BigDecimal.ZERO));
                                                fundRepaymentInfoEntity.setUpdateTime(LocalDateTime.now());
                                                if (fundRepaymentInfoEntity.getActuallyAmountTotal().compareTo(fundRepaymentInfoEntity.getRepaymentAmountTotal()) >= 0) {
                                                    fundRepaymentInfoEntity.setActuallyDate(updateOrderPayApplicationInfoEntity.getCashierApproveTime().toLocalDate());
                                                }
                                                fundRepaymentInfoMapper.updateById(fundRepaymentInfoEntity);}
                                            OrderApproveFundPlanStatusDTO orderApproveFundPlanStatusDTO = new OrderApproveFundPlanStatusDTO();
                                            orderApproveFundPlanStatusDTO.setOrderId(orderInfo.getId());
                                            orderApproveFundPlanStatusDTO.setIsOutsourcing(1);
                                            orderService.updateFundPlanStatus(orderApproveFundPlanStatusDTO);
                                            orderService.updateOrderFundRepayment(orderPayApplicationInfoEntity.getOrderId());
                                            BigDecimal loanSettlementAmount = caseInfoRepayCalcVO.getLoanSettlementAmount();
                                                if (totalAmount.compareTo(loanSettlementAmount)>=0){
                                                    int count=0;
                                                    CaseInfoEntity caseInfoEntity1 = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                                            .eq(CaseInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                            .eq(CaseInfoEntity::getDeleteFlag, 0)
                                                            .eq(CaseInfoEntity::getDataSource, 1)
                                                            .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                                                    settleCaseInfo(caseInfoEntity1, count, orderPayApplicationInfoEntity, userName);
                                                    CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                                            .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                                                            .eq(CaseInfoEntity::getDeptId, DeptEnum.ZHONG_XIN.getId())
                                                            .eq(CaseInfoEntity::getDeleteFlag, 0)
                                                            .eq(CaseInfoEntity::getDataSource, 1)
                                                            .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                                                    orderSettleAmountRecordMapper.update(new LambdaUpdateWrapper<OrderSettleAmountRecordEntity>()
                                                            .set(OrderSettleAmountRecordEntity::getDeleteFlag, 1)
                                                            .eq(OrderSettleAmountRecordEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                            .eq(OrderSettleAmountRecordEntity::getDeleteFlag, 0));
                                                    OrderSettleAmountRecordEntity entity = new OrderSettleAmountRecordEntity()
                                                            .setOrderId(orderPayApplicationInfoEntity.getOrderId())
                                                            .setOrderSource(orderPayApplicationInfoEntity.getOrderSource())
                                                            .setOrderNumber(orderPayApplicationInfoEntity.getOrderNumber())
                                                            .setTrialSettlementAmount(loanSettlementAmount)
                                                            .setApplySettlementAmount(actualAmount)
                                                            .setActualSettlementAmount(actualAmount)
                                                            .setSettlementMethod(caseInfoRepayCalcVO.getLoanSettlementMethod())
                                                            .setOutsourcingStatus(1)
                                                            .setUsageDays(caseInfoRepayCalcVO.getUsageDays())
                                                            .setRemainingPrincipal(caseInfoRepayCalcVO.getRemainingPrincipal())
                                                            .setInstalmentsRepaid(caseInfoRepayCalcVO.getInstalmentsRepaid());
                                                    ExemptionApplicationEntity exemptionApplicationEntity = exemptionApplicationEntityMapper.selectOne(new LambdaQueryWrapper<ExemptionApplicationEntity>()
                                                            .eq(ExemptionApplicationEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                            .eq(ExemptionApplicationEntity::getDeleteFlag, 0)
                                                            .eq(ExemptionApplicationEntity::getIsUse, 1)
                                                            .orderByDesc(ExemptionApplicationEntity::getId), false);
                                                    if (ObjUtil.isNotNull(exemptionApplicationEntity)){
                                                        entity.setApplySettlementAmount(exemptionApplicationEntity.getApplySettlementAmount());
                                                    }
                                                    if (ObjUtil.equals(caseInfoRepayCalcVO.getLoanSettlementEnum(), LoanSettlementEnum.CLOSED_PERIOD_PENALTY_INSIDE)){
                                                        entity.setSettlementMethod(entity.getSettlementMethod()+caseInfoRepayCalcVO.getSettlePenaltyRate().multiply(new BigDecimal("100"))+ "%");
                                                    }
                                                    orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                                                                    .eq(OrderPayApplicationInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
                                                                    .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                                                                    .eq(OrderPayApplicationInfoEntity::getApplyType,1)
                                                                    .in(OrderPayApplicationInfoEntity::getFeeType, List.of(OrderFeeDetailExpandTypeEnum.OTHER,
                                                                            OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE, OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT,
                                                                            OrderFeeDetailExpandTypeEnum.DISPOSABLE_SECURITY_DEPOSIT, OrderFeeDetailExpandTypeEnum.INSTALLMENT_SERVICE_FEE,
                                                                            OrderFeeDetailExpandTypeEnum.GPS_DATA_TRANSFER_FEE, OrderFeeDetailExpandTypeEnum.GPS_EQUIPMENT_COMPENSATION_FEE))
                                                                    .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)).stream().map(OrderPayApplicationInfoEntity::getPayeeAmount)
                                                            .reduce(BigDecimal::add).ifPresentOrElse(entity::setRefundsTotalAmount, () -> entity.setRefundsTotalAmount(BigDecimal.ZERO));
                                                    orderSettleAmountRecordMapper.insert(entity);
                                                    if (ObjUtil.isNotNull(caseInfoEntity)) {
                                                        zhongXinService.pushToZhongXinAsRetrieveAcceptSettle(
                                                                new ZhongXinAsRetrieveAcceptSettleDTO()
                                                                        .setCase_no(caseInfoEntity.getCaseNo())
                                                                        .setReceivable_amount(orderPayApplicationInfoEntity.getPayeeAmount())
                                                                        .setReduction_amount(BigDecimal.ZERO)
                                                                        .setRemark(orderPayApplicationInfoEntity.getRemark())
                                                                        .setFinance_remark(approvalPayApplicationDTO.getRemark())
                                                        );
                                                    }
//                                                    if (ObjUtil.equals(orderInfo.getIsRepurchase(),1)){
//                                                    repurchaseRepaymentInfoMapper.update(new LambdaUpdateWrapper<RepurchaseRepaymentInfoEntity>()
//                                                            .set(RepurchaseRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.SETTLED)
//                                                            .eq(RepurchaseRepaymentInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId())
//                                                    );
//                                                }else {fundRepaymentInfoMapper.update(new LambdaUpdateWrapper<FundRepaymentInfoEntity>()
//                                                            .set(FundRepaymentInfoEntity::getRepaymentStatus,FundRepayStatusEnum.SETTLED)
//                                                            .eq(FundRepaymentInfoEntity::getOrderId, orderPayApplicationInfoEntity.getOrderId()));
//                                                }

                                                    try {
                                                        OrderStateService bean = SpringContentUtils.getBean(OrderStateService.class);
                                                        bean.sendEvent(States.PAYMENT_SUCCESS, Events.SETTLED, orderPayApplicationInfoEntity.getOrderId(),
                                                                currentUserId);
                                                    }catch (Exception e){
                                                        log.error("订单{}结算成功后更新订单状态异常", orderPayApplicationInfoEntity.getOrderId(), e);
                                                    }
                                                    orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                                                            .set(OrderInfoEntity::getPlanState, 2)
                                                            .set(OrderInfoEntity::getCompanyPlanState,1)
                                                            .eq(OrderInfoEntity::getId,  orderPayApplicationInfoEntity.getOrderId()));

                                                    if (ObjUtil.isNotNull(orderPayApplicationInfoEntity.getOrderApplicationSource()) && orderPayApplicationInfoEntity.getOrderApplicationSource() == 1 && Objects.equals(orderInfo.getFundId(), FundEnum.ZHONG_HENG_TONG_HUI.getValue())) {
                                                    FundEnum fundEnum = FundEnum.getFundEnum(orderInfo.getFundId());
                                                    FinalFundInfoEntity fundApplyInfo = finalFundInfoMapper.selectOne(
                                                            new LambdaQueryWrapper<FinalFundInfoEntity>()
                                                                    .eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
                                                                    .eq(FinalFundInfoEntity::getFundId, fundEnum.getValue())
                                                                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                                                                    .orderByDesc(FinalFundInfoEntity::getCreateTime)
                                                                    .last("limit 1")
                                                    );
                                                    DaiChangJieQingTongZhiResultDTO daiChangJieQingTongZhiResultDTO = new DaiChangJieQingTongZhiResultDTO();
                                                    daiChangJieQingTongZhiResultDTO.setOrderNum(fundApplyInfo.getLoanBillNo());
                                                    daiChangJieQingTongZhiResultDTO.setSpOrderNum(orderInfo.getOrderNumber());
                                                    daiChangJieQingTongZhiResultDTO.setQiShu(ObjUtil.isNotNull(orderPayApplicationInfoEntity.getRepaymentTerm()) ? String.valueOf(orderPayApplicationInfoEntity.getRepaymentTerm()) : "");
                                                    daiChangJieQingTongZhiResultDTO.setDcHeJi(String.valueOf(orderPayApplicationInfoEntity.getPayeeAmount()));
                                                    daiChangJieQingTongZhiResultDTO.setDcBenJin(String.valueOf(orderPayApplicationInfoEntity.getPayeeAmount()));
                                                    daiChangJieQingTongZhiResultDTO.setDcTime(orderPayApplicationInfoEntity.getCreateTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
                                                    approveFeign.compensateSettleNotice(daiChangJieQingTongZhiResultDTO);
                                                    }

                                            }

                                            try {
                                                CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                                        .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                                                        .eq(CaseInfoEntity::getDeptId, DeptEnum.ZHONG_XIN.getId())
                                                        .eq(CaseInfoEntity::getDeleteFlag, 0)
                                                        .eq(CaseInfoEntity::getDataSource, 1)
                                                        .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                                                if (ObjUtil.isNotNull(caseInfoEntity)) {
                                                    zhongXinService.pushToZhongXinAsRetrieveAccept(new ReceivePaymentDTO()
                                                            .setOrderId(orderPayApplicationInfoEntity.getOrderId())
                                                            .setTerm(orderPayApplicationInfoEntity.getRepaymentTerm())
                                                            .setAmount(orderPayApplicationInfoEntity.getPayeeAmount())
                                                            .setTradingTime(LocalDateTime.now())
                                                            .setPayer(orderInfo.getCustomerName())
                                                            .setPayee(FundEnum.getFundEnum(orderInfo.getFundId()).getFundName())
                                                            .setTradingMethods(OrderFeeDetailTradingMethodsEnum.CORPORATE_TRANSFERS)
                                                            .setSettling(2));
                                                }

                                            } catch (Exception e) {
                                                log.error("OrderPayApplicationServiceImpl.repurchaseRepayment sendMessageToZhongXin orderId:{}", orderInfo.getId());
                                            }
                                        }
                                        case 2 -> {
                                            String jsonObject = new JSONObject()
                                                    .set("case_no", "HF" + orderPayApplicationInfoEntity.getOrderNumber())
                                                    .set("receivable_amount", orderPayApplicationInfoEntity.getPayeeAmount())
                                                    .set("collect_name", orderPayApplicationInfoEntity.getPayeeAccount())
                                                    .set("payment_name", orderPayApplicationInfoEntity.getPayAccount())
                                                    .set("payment_date", orderPayApplicationInfoEntity.getPaymentTime().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond())
                                                    .set("collect_bankcard", orderPayApplicationInfoEntity.getPayeeAccountNumber())
                                                    .set("payment_bankcard", orderPayApplicationInfoEntity.getPayAccountNumber())
                                                    .set("payment_method", orderPayApplicationInfoEntity.getFeeType().getDescription())
                                                    .set("opening_bank", orderPayApplicationInfoEntity.getPayeeAccountBranchName())
                                                    .set("payment_bank", orderPayApplicationInfoEntity.getPayeeAccountName())
                                                    .set("payment_img", orderPayApplicationInfoEntity.getPaymentDetails())
                                                    .set("remark", orderPayApplicationInfoEntity.getRemark())
                                                    .set("check_at", Instant.now().getEpochSecond())
                                                    .set("status", 2)
                                                    .set("check_opinion", approvalPayApplicationDTO.getRemark())
                                                    .set("full_name", userName)
                                                    .set("req_no", orderPayApplicationInfoEntity.getCreateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() + orderPayApplicationInfoEntity.getOrderNumber()).toString();
                                            log.info("OrderPayApplicationServiceImpl digitalizeFeign.weiwaiDigitalizeZhongXinPaymentNotification:{}", jsonObject);
                                            LinkedMultiValueMap<String, Object> objectObjectLinkedMultiValueMap = new LinkedMultiValueMap<>();
                                            objectObjectLinkedMultiValueMap.add("DATA", digitalizeZhongxinEncryptUtil.encode(jsonObject));
                                            digitalizeFeign.weiwaiDigitalizeZhongXinPaymentNotification(objectObjectLinkedMultiValueMap);
                                            //todo 如果使用保证金推送数字化
                                            if (ObjUtil.equal(orderPayApplicationInfoEntity.getUseDeposit(), 1)) {
                                                DigitalizeWeiwaiOrderStatusDTO digitalizeWeiwaiOrderStatusDTO = new DigitalizeWeiwaiOrderStatusDTO();
                                                digitalizeWeiwaiOrderStatusDTO.setOrder_id(orderPayApplicationInfoEntity.getOrderNumber());
                                                digitalizeWeiwaiOrderStatusDTO.setAmount(orderPayApplicationInfoEntity.getDeposit());
                                                String res = digitalizeFeign.weiwaiDigitalizeBondDeducteNotify(digitalizeWeiwaiOrderStatusDTO);
                                                log.info("weiwaiDigitalizeBondDeducteNotifyres:{}", res);
                                            }

                                            BigDecimal actualAmount = orderPayApplicationInfoEntity.getPayeeAmount();
                                            BigDecimal reductionAmount = ObjUtil.defaultIfNull(orderPayApplicationInfoEntity.getReductionAmount(), BigDecimal.ZERO);
                                            BigDecimal depositAmount = ObjUtil.defaultIfNull(orderPayApplicationInfoEntity.getDeposit(), BigDecimal.ZERO);
                                            List<OrderPayApplicationInfoEntity> relatedOrders = orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                                                    .eq(OrderPayApplicationInfoEntity::getOrderNumber, orderPayApplicationInfoEntity.getOrderNumber())
                                                    .eq(OrderPayApplicationInfoEntity::getOrderApplicationSource, 1)
                                                    .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)
                                                    .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)
                                                    .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                                                    .eq(OrderPayApplicationInfoEntity::getOrderSource, 2));

                                            for (OrderPayApplicationInfoEntity entity : relatedOrders) {
                                                // 累加实收金额（仅包括 payeeAmount）
                                                actualAmount = actualAmount.add(ObjUtil.defaultIfNull(entity.getPayeeAmount(), BigDecimal.ZERO));
                                                //减免金额
                                                reductionAmount = reductionAmount.add(ObjUtil.defaultIfNull(entity.getReductionAmount(), BigDecimal.ZERO));
                                                //保证金
                                                depositAmount = depositAmount.add(ObjUtil.defaultIfNull(entity.getDeposit(), BigDecimal.ZERO));
                                            }
                                            BigDecimal totalAmount = actualAmount.add(reductionAmount).add(depositAmount);
                                            BigDecimal finalActualAmount = actualAmount;
                                            BigDecimal finalReductionAmount = reductionAmount;
                                            Optional.ofNullable(outsourcingContractService.getDigitalizeSettleList(
                                                            new DigitalizeWeiwaiOrderStatusDTO().setOrder_id(orderPayApplicationInfoEntity.getOrderNumber())))
                                                    .filter(zongValue -> totalAmount.compareTo(zongValue.getSettleAmt()) >= 0)
                                                    .ifPresent(zongValue -> {
                                                        DigitalOutsourcingOrderEntity digitalOrder = digitalOutsourcingOrderEntityMapper.selectOne(
                                                                new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>()
                                                                        .eq(DigitalOutsourcingOrderEntity::getOrderNo, orderPayApplicationInfoEntity.getOrderNumber())
                                                                        .eq(DigitalOutsourcingOrderEntity::getDataSource, 2)
                                                                        .eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0)
                                                                        .orderByDesc(DigitalOutsourcingOrderEntity::getCreateTime));

                                                        Optional.ofNullable(digitalOrder).ifPresent(order -> {
                                                            String payload = new JSONObject()
                                                                    .set("order_id", orderPayApplicationInfoEntity.getOrderNumber())
                                                                    .set("name", order.getCustomerName())
                                                                    .set("receivable_amount", finalActualAmount)
                                                                    .set("is_help", 1)
                                                                    .set("status", 1)
                                                                    .set("is_verify", 1)
                                                                    .set("is_gps", 1)
                                                                    .set("req_no", orderPayApplicationInfoEntity.getCreateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() + orderPayApplicationInfoEntity.getOrderNumber())
                                                                    .set("usage_days", order.getUsageDays())
                                                                    .set("receivable_total_amount", zongValue.getSettleAmt())
                                                                    .toString();
                                                            LinkedMultiValueMap<String, Object> objectLinkedMultiValueMap = new LinkedMultiValueMap<>();
                                                            objectLinkedMultiValueMap.add("datainfo", digitalizeZhongxinEncryptUtil.encode(payload));
                                                            digitalizeFeign.weiwaiDigitalizeZhongXinSettleNotify(objectLinkedMultiValueMap);
                                                            int count=0;
                                                            CaseInfoEntity caseInfoEntity1 = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                                                    .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                                                                    .eq(CaseInfoEntity::getDeleteFlag, 0)
                                                                    .eq(CaseInfoEntity::getDataSource, 2)
                                                                    .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                                                            settleCaseInfo(caseInfoEntity1, count, orderPayApplicationInfoEntity, userName);
                                                            CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                                                    .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                                                                    .eq(CaseInfoEntity::getDeptId, DeptEnum.ZHONG_XIN.getId())
                                                                    .eq(CaseInfoEntity::getDeleteFlag, 0)
                                                                    .eq(CaseInfoEntity::getDataSource, 2)
                                                                    .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                                                            if (ObjUtil.isNotNull(caseInfoEntity)) {
                                                                zhongXinService.pushToZhongXinAsRetrieveAcceptSettle(
                                                                        new ZhongXinAsRetrieveAcceptSettleDTO()
                                                                                .setCase_no("HF" + caseInfoEntity.getCaseNo())
                                                                                .setReceivable_amount(finalActualAmount)
                                                                                .setReduction_amount(finalReductionAmount)
                                                                                .setRemark(orderPayApplicationInfoEntity.getRemark())
                                                                                .setFinance_remark(approvalPayApplicationDTO.getRemark())
                                                                );
                                                            }

                                                        });
                                                    });

                                            try {
                                                CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                                        .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                                                        .eq(CaseInfoEntity::getDeptId, DeptEnum.ZHONG_XIN.getId())
                                                        .eq(CaseInfoEntity::getDeleteFlag, 0)
                                                        .eq(CaseInfoEntity::getDataSource, 2)
                                                        .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                                                if (ObjUtil.isNotNull(caseInfoEntity)) {
                                                    zhongXinService.pushToZhongXinAsRetrieveAccept(new ReceivePaymentDTO()
                                                            .setOrderId(orderPayApplicationInfoEntity.getOrderId())
                                                            .setTerm(orderPayApplicationInfoEntity.getRepaymentTerm())
                                                            .setAmount(orderPayApplicationInfoEntity.getPayeeAmount())
                                                            .setTradingTime(LocalDateTime.now())
                                                            .setPayer(orderInfo.getCustomerName())
                                                            .setPayee(FundEnum.getFundEnum(orderInfo.getFundId()).getFundName())
                                                            .setTradingMethods(OrderFeeDetailTradingMethodsEnum.CORPORATE_TRANSFERS)
                                                            .setSettling(2));
                                                }

                                            } catch (Exception e) {
                                                log.error("OrderPayApplicationServiceImpl.repurchaseRepayment sendMessageToZhongXin orderId:{}", orderInfo.getId());
                                            }
                                        }
                                        case 3 -> {
                                            CaseInfoEntity caseInfoEntity1 = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                                    .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                                                    .eq(CaseInfoEntity::getDeleteFlag, 0)
                                                    .eq(CaseInfoEntity::getDataSource, 3)
                                                    .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                                            BigDecimal actualAmount = orderPayApplicationInfoEntity.getPayeeAmount();
                                            BigDecimal reductionAmount = ObjUtil.defaultIfNull(orderPayApplicationInfoEntity.getReductionAmount(), BigDecimal.ZERO);
//                                            List<OrderPayApplicationInfoEntity> relatedOrders = orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
//                                                    .eq(OrderPayApplicationInfoEntity::getOrderNumber, orderPayApplicationInfoEntity.getOrderNumber())
//                                                    .eq(OrderPayApplicationInfoEntity::getOrderApplicationSource, 1)
//                                                    .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)
//                                                    .eq(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)
//                                                    .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
//                                                    .eq(OrderPayApplicationInfoEntity::getOrderSource, 2));
//
//                                            for (OrderPayApplicationInfoEntity entity : relatedOrders) {
//                                                // 累加实收金额（仅包括 payeeAmount）
//                                                actualAmount = actualAmount.add(ObjUtil.defaultIfNull(entity.getPayeeAmount(), BigDecimal.ZERO));
//                                                //减免金额
//                                                reductionAmount = reductionAmount.add(ObjUtil.defaultIfNull(entity.getReductionAmount(), BigDecimal.ZERO));
//                                            }
                                            BigDecimal totalAmount = actualAmount.add(reductionAmount);
                                            BigDecimal finalActualAmount = actualAmount;
                                            BigDecimal finalReductionAmount = reductionAmount;
                                            Optional.ofNullable(outsourcingSettlementTrialCalculationsService.getReductionRecordConditional(
                                                            new ReductionRecordConditionalDTO().setCaseId(caseInfoEntity1.getId()).setRepayType(2)))
                                                    .filter(zongValue -> totalAmount.compareTo(zongValue.getLoanAmount()) >= 0)
                                                    .ifPresent(zongValue -> {
                                                        DigitalOutsourcingOrderEntity digitalOrder = digitalOutsourcingOrderEntityMapper.selectOne(
                                                                new LambdaQueryWrapper<DigitalOutsourcingOrderEntity>()
                                                                        .eq(DigitalOutsourcingOrderEntity::getOrderNo, orderPayApplicationInfoEntity.getOrderNumber())
                                                                        .eq(DigitalOutsourcingOrderEntity::getDataSource, 3)
                                                                        .eq(DigitalOutsourcingOrderEntity::getDeleteFlag, 0)
                                                                        .orderByDesc(DigitalOutsourcingOrderEntity::getCreateTime));

                                                        Optional.ofNullable(digitalOrder).ifPresent(order -> {
                                                            int count=0;
                                                            settleCaseInfo(caseInfoEntity1, count, orderPayApplicationInfoEntity,userName);
                                                            CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                                                    .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                                                                    .eq(CaseInfoEntity::getDeleteFlag, 0)
                                                                    .eq(CaseInfoEntity::getDataSource, 3)
                                                                    .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                                                            KingdeeStatusRequestDTO kingdeeStatusRequestDTO = new KingdeeStatusRequestDTO();
                                                            kingdeeStatusRequestDTO.setCode(1);
                                                            kingdeeStatusRequestDTO.setMsg("成功");
                                                            KingdeeRequestData data = new KingdeeRequestData();
                                                            data.setOrderNumber(caseInfoEntity.getDigitalOrderId());
                                                            data.setFlowType("委外保全");
                                                            data.setOutsourceOrg(caseInfoEntity.getDisposalCompany());
                                                            data.setOutsourceTime(caseInfoEntity.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                                                            data.setIs_seettle(1);
                                                            data.setSeettle_time(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                                                            data.setSeettle_amount(totalAmount.toPlainString());
                                                            kingdeeStatusRequestDTO.setData(data);
                                                            kingdeeOutsourcingService.pushKingdeeorderStatus(kingdeeStatusRequestDTO);
                                                            if (ObjUtil.equals(caseInfoEntity.getDeptId(), DeptEnum.ZHONG_XIN.getId())) {
                                                                zhongXinService.pushToZhongXinAsRetrieveAcceptSettle(
                                                                        new ZhongXinAsRetrieveAcceptSettleDTO()
                                                                                .setCase_no(caseInfoEntity.getCaseNo())
                                                                                .setReceivable_amount(finalActualAmount)
                                                                                .setReduction_amount(finalReductionAmount)
                                                                                .setRemark(orderPayApplicationInfoEntity.getRemark())
                                                                                .setFinance_remark(approvalPayApplicationDTO.getRemark())
                                                                );
                                                            }

                                                        });
                                                    });
                                            try {
                                                CaseInfoEntity caseInfoEntity = caseInfoEntityMapper.selectOne(new LambdaQueryWrapper<CaseInfoEntity>()
                                                        .eq(CaseInfoEntity::getDigitalOrderId, orderPayApplicationInfoEntity.getOrderNumber())
                                                        .eq(CaseInfoEntity::getDeptId, DeptEnum.ZHONG_XIN.getId())
                                                        .eq(CaseInfoEntity::getDeleteFlag, 0)
                                                        .eq(CaseInfoEntity::getDataSource, 3)
                                                        .eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS));
                                                if (ObjUtil.isNotNull(caseInfoEntity)) {
                                                    zhongXinService.pushToZhongXinAsRetrieveAccept(new ReceivePaymentDTO()
                                                            .setOrderId(orderPayApplicationInfoEntity.getOrderId())
                                                            .setTerm(orderPayApplicationInfoEntity.getRepaymentTerm())
                                                            .setAmount(orderPayApplicationInfoEntity.getPayeeAmount())
                                                            .setTradingTime(LocalDateTime.now())
                                                            .setPayer(orderInfo.getCustomerName())
                                                            .setPayee(FundEnum.getFundEnum(orderInfo.getFundId()).getFundName())
                                                            .setTradingMethods(OrderFeeDetailTradingMethodsEnum.CORPORATE_TRANSFERS)
                                                            .setSettling(2));
                                                }

                                            } catch (Exception e) {
                                                log.error("OrderPayApplicationServiceImpl.repurchaseRepayment sendMessageToZhongXin orderId:{}", orderInfo.getId());
                                            }
                                        }
                                    }

                                }
                            }
                        } else {
                            //如果为赎回后转对公单，则更新还款计划实还金额
                            if (ObjUtil.equals(orderPayApplicationInfoEntity.getPayeeType(), PayApplicationPayeeTypeEnum.HUI_FENG) && OrderFeeDetailExpandTypeEnum.getOfflineFeeEnumList().contains(orderPayApplicationInfoEntity.getFeeType())) {
                                if (ObjUtil.equals(orderInfo.getIsRepurchase(), 1)) {
                                    FundRepaymentDeductEntity repaymentDeduct = orderPayApplicationPriveteMethod.repurchaseRepayment(orderPayApplicationInfoEntity, updateOrderPayApplicationInfoEntity.getCashierApproveTime());
                                    updateOrderPayApplicationInfoEntity.setPaymentDetails(ObjUtil.isNotNull(repaymentDeduct) ? repaymentDeduct.getDeductReqNo() : updateOrderPayApplicationInfoEntity.getId().toString());
                                }
                            }
                        }

                        // 保存订单费用详情
                        if (StrUtil.isBlank(updateOrderPayApplicationInfoEntity.getPaymentDetails())) {
                            String tradingSerialNumber = "payApply_" + orderPayApplicationInfoEntity.getOrderId()
                                    + "_" + orderPayApplicationInfoEntity.getId();
                            updateOrderPayApplicationInfoEntity.setPaymentDetails(tradingSerialNumber);
                        }

                        String tradingSerialNumber = null;
                        if (ObjUtil.equals(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE)) {
                            //车务费可能和gps支付记录冲突所以单独处理
                            tradingSerialNumber = orderPayApplicationInfoEntity.getId() + "_" + orderPayApplicationInfoEntity.getPaymentDetails();
                        } else {
                            tradingSerialNumber = updateOrderPayApplicationInfoEntity.getPaymentDetails();
                        }
                        String payeeAccount = "";
                        if (Objects.equals(orderInfo.getFundId(), FundEnum.FU_MIN.getValue()) && Objects.equals(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)) {
                            payeeAccount = FundEnum.FU_MIN.getFundName();
                        } else {
                            payeeAccount = orderPayApplicationInfoEntity.getPayeeAccount();
                        }

	                    if (!Objects.equals(orderPayApplicationInfoEntity.getPayeeType(),PayApplicationPayeeTypeEnum.ZHONG_HENG) &&
			                    (!Objects.equals(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.SINGLE_PERIOD_COMPENSATION) ||
					                    !Objects.equals(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT))){
		                    orderFeeDetailService.saveOrderFeeDetail(orderPayApplicationInfoEntity.getOrderId(),
				                    tradingSerialNumber,
				                    orderPayApplicationInfoEntity.getPayeeAmount(),
				                    feeDetailIsPublicAccountTransfer(orderPayApplicationInfoEntity.getFeeType()),
				                    orderPayApplicationInfoEntity.getPayAccount(),
//                                ObjUtil.defaultIfNull(dictUtils.getDictLabel(GlobalConstants.DictType.PAYEE_ACCOUNT.name(), Convert.toInt(orderPayApplicationInfoEntity.getPayAccount())), "").split(" ")[0],
				                    payeeAccount,
				                    orderPayApplicationInfoEntity.getFeeType(),
				                    orderPayApplicationInfoEntity.getApplyType(),
				                    orderPayApplicationInfoEntity.getRepaymentTerm(),
				                    updateOrderPayApplicationInfoEntity.getCashierApproveTime(),
				                    orderPayApplicationInfoEntity.getRemark(),
				                    orderPayApplicationInfoEntity.getOrderNumber(),
				                    orderPayApplicationInfoEntity.getOrderApplicationSource(),
				                    orderPayApplicationInfoEntity.getOrderSource(),
				                    (ObjUtil.isNotEmpty(orderPayApplicationInfoEntity.getReductionAmount()) ? orderPayApplicationInfoEntity.getReductionAmount() : null)
		                    );
	                    }
                    } else if (approvalPayApplicationDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
                        // 审核拒绝，下一个节点为流程终止
                        nextNode = PayApplicationNodeEnums.FAIL;
                        event = PayApplicationEventEnums.APPROVE_REJECT;
                        if (ObjUtil.isNotEmpty(one) && Objects.equals(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY)) {
                            one.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_FAILED);
                            fundRepaymentDeductService.updateById(one);
                            Result<UserInfoVO> userInfoVOResult = userFeign.searchUserName(orderPayApplicationInfoEntity.getApplyUserId());
                            if (Result.isSuccess(userInfoVOResult) && ObjUtil.isNotEmpty(userInfoVOResult.getData())) {
                                messageFeign.sendMessage(new MessageContent()
                                        .setMsgType(MsgConstants.MSG_TEXT)
                                        .setSendType(MsgConstants.SEND_DD_NOTICE)
                                        .setTitle("客户" + orderInfo.getCustomerName() + "，发起的第" + (ObjUtil.isNotEmpty(one.getTerm()) ? one.getTerm() : "1") + "期的费用类型是提前结清违约金，金额为" + orderPayApplicationInfoEntity.getPayeeAmount() + "元的对公转账查账申请被拒绝，请重新发起")
                                        .setContent("客户" + orderInfo.getCustomerName() + "，发起的第" + (ObjUtil.isNotEmpty(one.getTerm()) ? one.getTerm() : "1") + "期的费用类型是提前结清违约金，金额为" + orderPayApplicationInfoEntity.getPayeeAmount() + "元的对公转账查账申请被拒绝，请重新发起")
                                        .setMediaContent(null)
                                        .setReceiver(userInfoVOResult.getData().getMobile())
                                        .setFileType(null)
                                );
                            }
                        }
                        if (Objects.equals(orderPayApplicationInfoEntity.getOrderApplicationSource(), 1)) {
                            //todo 委外对公查账拒绝
                        }
                        //todo 解除对公查账锁定结清金额
                        OrderSettleAmountRecordEntity orderSettleAmountRecord = orderSettleAmountRecordMapper.selectOne(new LambdaQueryWrapper<OrderSettleAmountRecordEntity>().eq(OrderSettleAmountRecordEntity::getOrderPayId, updateOrderPayApplicationInfoEntity.getId()).eq(OrderSettleAmountRecordEntity::getDeleteFlag, 0).orderByDesc(OrderSettleAmountRecordEntity::getCreateTime).last("limit 1"));
                        if (ObjUtil.isNotEmpty(orderSettleAmountRecord)) {
                            orderSettleAmountRecord.setDeleteFlag(1);
                            orderSettleAmountRecordMapper.updateById(orderSettleAmountRecord);
                        }

                    } else if (approvalPayApplicationDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
                        // 驳回，下一个节点为付款单申请
                        nextNode = PayApplicationNodeEnums.ACCOUNTANT_APPLY;
                        event = PayApplicationEventEnums.APPROVE_REVOKE;
                        if (ObjUtil.isNotEmpty(one) && Objects.equals(orderPayApplicationInfoEntity.getFeeType(), OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY)) {
                            one.setRepayStatus(FundDeductRepayStatusEnums.REPAYMENT_FAILED);
                            fundRepaymentDeductService.updateById(one);
                            Result<UserInfoVO> userInfoVOResult = userFeign.searchUserName(orderPayApplicationInfoEntity.getApplyUserId());
                            if (Result.isSuccess(userInfoVOResult) && ObjUtil.isNotEmpty(userInfoVOResult.getData())) {
                                messageFeign.sendMessage(new MessageContent()
                                        .setMsgType(MsgConstants.MSG_TEXT)
                                        .setSendType(MsgConstants.SEND_DD_NOTICE)
                                        .setTitle("客户" + orderInfo.getCustomerName() + "，发起的第" + (ObjUtil.isNotEmpty(one.getTerm()) ? one.getTerm() : "1") + "期的费用类型是提前结清违约金，金额为" + orderPayApplicationInfoEntity.getPayeeAmount() + "元的对公转账查账申请被驳回，请重新发起")
                                        .setContent("客户" + orderInfo.getCustomerName() + "，发起的第" + (ObjUtil.isNotEmpty(one.getTerm()) ? one.getTerm() : "1") + "期的费用类型是提前结清违约金，金额为" + orderPayApplicationInfoEntity.getPayeeAmount() + "元的对公转账查账申请被驳回，请重新发起")
                                        .setMediaContent(null)
                                        .setReceiver(userInfoVOResult.getData().getMobile())
                                        .setFileType(null)
                                );
                            }
                        }
                        if (Objects.equals(orderPayApplicationInfoEntity.getOrderApplicationSource(), 1)) {
                            //todo 委外对公查账驳回
                        }
                    }
                    break;
                default:
                    // 根据收款方类型和当前节点确定下一个节点
                    if (approvalPayApplicationDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_PASS) {
                        nextNode = orderPayApplicationPriveteMethod.getNextNode(orderPayApplicationInfoEntity.getFeeType(), orderPayApplicationInfoEntity.getApplyType(),
                                orderPayApplicationInfoEntity.getPayeeType(), currentNode);
                    } else if (approvalPayApplicationDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REJECT) {
                        // 审核拒绝，下一个节点为流程终止
                        nextNode = PayApplicationNodeEnums.FAIL;
                        event = PayApplicationEventEnums.APPROVE_REJECT;
                    } else if (approvalPayApplicationDTO.getConclusion() == PayApplicationConclusionEnum.APPROVE_REVOKE) {
                        // 驳回，下一个节点为当前节点
                        nextNode = currentNode;
                        event = PayApplicationEventEnums.APPROVE_REVOKE;
                    }
                    break;
            }

            // 更新付款申请信息实体的当前节点
            updateOrderPayApplicationInfoEntity.setCurrentNode(nextNode);
            orderPayApplicationMapper.updateById(updateOrderPayApplicationInfoEntity);
            log.info("OrderPayApplicationServiceImpl ApprovePayApplication updated orderPayApplicationInfoEntity with nextNode:{}", nextNode);
            if (ObjUtil.equal(nextNode, PayApplicationNodeEnums.FAIL) && StrUtil.isNotBlank(orderPayApplicationInfoEntity.getReductionSourceId())) {
                List<Integer> reductionSourceIds = JSONUtil.toList(orderPayApplicationInfoEntity.getReductionSourceId(), Integer.class);
                if (CollUtil.isNotEmpty(reductionSourceIds)) {
                    exemptionApplicationEntityMapper.update(new LambdaUpdateWrapper<ExemptionApplicationEntity>()
                            .set(ExemptionApplicationEntity::getIsUse, 0)
                            .in(ExemptionApplicationEntity::getId, reductionSourceIds));
                }
            }
            PayApplicationAuditTypeEnum auditType;
            if (ObjUtil.equal(currentNode, PayApplicationNodeEnums.STORE_MANAGER_APPROVAL) ||
                    ObjUtil.equal(currentNode, PayApplicationNodeEnums.REGION_MANAGER_APPROVAL)) {
                auditType = PayApplicationAuditTypeEnum.DINGTALK;
            } else {
                auditType = PayApplicationAuditTypeEnum.YUNQI;
            }

            // 记录审核节点
            orderPayApplicationPriveteMethod.saveNodeRecord(orderPayApplicationInfoEntity.getId(), currentNode, nextNode, auditType,
                    approvalPayApplicationDTO.getProcessId(), approvalPayApplicationDTO.getRemark(), event,
                    operatorUserId, approvalPayApplicationDTO.getApproveTime());
            log.info("OrderPayApplicationServiceImpl ApprovePayApplication saved node record for orderId:{} from {} to {}", orderPayApplicationInfoEntity.getOrderId(), currentNode, nextNode);
        }
        return true;
    }

    private Integer settleCaseInfo(CaseInfoEntity caseInfoEntity1, int count, OrderPayApplicationInfoEntity orderPayApplicationInfoEntity, String userName) {
        if (ObjUtil.equals(caseInfoEntity1.getCirculationType(), 1)) {
            count = caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                    .set(CaseInfoEntity::getIsSettled, 1)
                    .set(CaseInfoEntity::getVisitResult, 2)
                    .set(CaseInfoEntity::getSettledTime, orderPayApplicationInfoEntity.getCreateTime())
                    .eq(CaseInfoEntity::getId, caseInfoEntity1.getId())
            );
            CaseUpdateFieldRecordsEntity caseUpdateFieldRecordsEntity = new CaseUpdateFieldRecordsEntity()
                    .setApprover(userName)
                    .setCaseInfoId(caseInfoEntity1.getId())
                    .setTitle("更新“外访状态”为：“全额结清回款”");
            caseUpdateFieldRecordsMapper.insert(caseUpdateFieldRecordsEntity);
        } else if (ObjUtil.equals(caseInfoEntity1.getCirculationType(), 2)) {
            count = caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                    .set(CaseInfoEntity::getIsSettled, 1)
                    .set(CaseInfoEntity::getPreservationResult, 3)
                    .set(CaseInfoEntity::getSettledTime, orderPayApplicationInfoEntity.getCreateTime())
                    .eq(CaseInfoEntity::getId, caseInfoEntity1.getId())
            );
            CaseUpdateFieldRecordsEntity caseUpdateFieldRecordsEntity = new CaseUpdateFieldRecordsEntity()
                    .setApprover(userName)
                    .setCaseInfoId(caseInfoEntity1.getId())
                    .setTitle("更新“保全状态”为：“面谈全额结清”");
            caseUpdateFieldRecordsMapper.insert(caseUpdateFieldRecordsEntity);
        } else if (ObjUtil.equals(caseInfoEntity1.getCirculationType(), 3)) {
            count = caseInfoEntityMapper.update(new LambdaUpdateWrapper<CaseInfoEntity>()
                    .set(CaseInfoEntity::getIsSettled, 1)
                    .set(CaseInfoEntity::getTransferStatus, 2)
                    .set(CaseInfoEntity::getSettledTime, orderPayApplicationInfoEntity.getCreateTime())
                    .eq(CaseInfoEntity::getId, caseInfoEntity1.getId())
            );
            CaseUpdateFieldRecordsEntity caseUpdateFieldRecordsEntity = new CaseUpdateFieldRecordsEntity()
                    .setApprover(userName)
                    .setCaseInfoId(caseInfoEntity1.getId())
                    .setTitle("更新“债转状态”为：“变现出售”");
            caseUpdateFieldRecordsMapper.insert(caseUpdateFieldRecordsEntity);
        }
        return count;
    }

    @Override
    public void batchDingTaskApprove() {
        // 获取钉钉节点的审批列表 门店经理 、大区品质主管
        List<OrderPayApplicationInfoEntity> dingTaskApproveVOList = orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                .in(OrderPayApplicationInfoEntity::getCurrentNode, Arrays.asList(PayApplicationNodeEnums.STORE_MANAGER_APPROVAL, PayApplicationNodeEnums.REGION_MANAGER_APPROVAL))
                .isNotNull(OrderPayApplicationInfoEntity::getProcessId)
                .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(dingTaskApproveVOList)) {
            return;
        }
        log.info("OrderPayApplicationServiceImpl batchDingTaskApprove retrieved dingTaskApproveVOList size:{}", dingTaskApproveVOList.size());
        // 钉钉审批实例id
        List<String> dingTaskProcessIdList = dingTaskApproveVOList.stream().map(OrderPayApplicationInfoEntity::getProcessId).filter(StrUtil::isNotBlank).toList();
        Map<String, List<DingTaskApproveVO>> processDetailMap = dingTaskFeign.queryDetailByProcessIdList(dingTaskProcessIdList).getData();
        log.info("OrderPayApplicationServiceImpl batchDingTaskApprove retrieved processDetailMap size:{}", processDetailMap.size());

        List<ApprovalPayApplicationDTO> approvalPayApplicationDTOs = new ArrayList<>();

        dingTaskApproveVOList.forEach(payApplicationInfo -> {
            String processId = payApplicationInfo.getProcessId();
            List<DingTaskApproveVO> dingTaskApproveVOListByProcessId = processDetailMap.get(processId);
            if (CollUtil.isEmpty(dingTaskApproveVOListByProcessId)) {
                return;
            }
            // 获取当前审批节点
            PayApplicationNodeEnums currentNode = payApplicationInfo.getCurrentNode();
            AtomicBoolean isProcessed = new AtomicBoolean(false); // 标志变量

            dingTaskApproveVOListByProcessId.forEach(dingTaskApproveVO -> {
                if (isProcessed.get()) {
                    return;
                }
                String activityId = dingTaskApproveVO.getActivityId();

                String userNumberApprove = dingTaskApproveVO.getUserId();
                Integer userId = null;
                List<Integer> roleIdList = null;

                if (StrUtil.isNotBlank(userNumberApprove)) {
                    List<UserSyncInfoListVO> syncInfoListVOList = userFeign.queryUserSyncInfoList(new SearchUserSyncInfoDTO().setUserNumberList(List.of(userNumberApprove))).getData();
                    if (CollUtil.isNotEmpty(syncInfoListVOList)) {
                        UserSyncInfoListVO userSyncInfoListVO = syncInfoListVOList.get(0);
                        userId = userSyncInfoListVO.getLhUserId();
                    }
                }
                if (!envUtil.isPrd()) {
                    if (ObjUtil.isNotNull(userId)) {
                        UserDetailInfoVO userDetailInfoVO = userFeign.searchUserDetailById(userId).getData();
                        if (ObjUtil.isNotNull(userDetailInfoVO)) {
                            roleIdList = userDetailInfoVO.getRoleIds();
                        }
                    }
                }

                switch (currentNode) {
                    case STORE_MANAGER_APPROVAL:
                        // 判断是否为门店经理审批
                        if (!List.of(switchUtils.getStrValue(ORDER_PAY_CUSTOMER_STORE_MANAGER_ACTOR_ID),
                                switchUtils.getStrValue(ORDER_PAY_NO_CUSTOMER_STORE_MANAGER_ACTOR_ID)).contains(activityId)) {
                            return;
                        }
                        // 测试环境挡板 根据用户角色判断
                    /*
                    if (!envUtil.isPrd() && CollUtil.isNotEmpty(roleIdList) && !roleIdList.contains(RoleEnum.STORE_MANAGER.getId())) {
                        return;
                    }*/
                        break;
                    case REGION_MANAGER_APPROVAL:
                        // 判断是否为大区品质主管审批
                        if (!Objects.equals(switchUtils.getStrValue(ORDER_PAY_NO_CUSTOMER_REGION_QUALITY_MANAGER_ACTOR_ID), activityId)) {
                            return;
                        }
                        // 测试环境挡板 根据用户角色判断
                    /*
                    if (!envUtil.isPrd() && CollUtil.isNotEmpty(roleIdList) && !roleIdList.contains(RoleEnum.QUALITY_MANAGER.getId())) {
                        return;
                    }*/
                    default:
                        break;
                }

                PayApplicationConclusionEnum conclusion = null;
                if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.AGREE.getCode())) {
                    conclusion = PayApplicationConclusionEnum.APPROVE_PASS;
                }
                if (ObjUtil.equal(dingTaskApproveVO.getResult(), DingTaskConclusionEnum.REFUSE.getCode())) {
                    conclusion = PayApplicationConclusionEnum.APPROVE_REJECT;
                }

                if (ObjUtil.isNotNull(conclusion)) {
                    approvalPayApplicationDTOs.add(new ApprovalPayApplicationDTO().setId(payApplicationInfo.getId()).setRemark(dingTaskApproveVO.getRemark())
                            .setProcessId(payApplicationInfo.getProcessId()).setOperatorUserId(userId).setConclusion(conclusion).setApproveTime(Convert.toLocalDateTime(dingTaskApproveVO.getFinishTime())));
                    log.info("OrderPayApplicationServiceImpl batchDingTaskApprove added ApprovalPayApplicationDTO for orderId:{} with conclusion:{}", payApplicationInfo.getOrderId(), conclusion);
                    isProcessed.set(true);
                }
            });

        });
        if (CollUtil.isNotEmpty(approvalPayApplicationDTOs)) {
            try {
                approvePayApplication(approvalPayApplicationDTOs, null, true);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            log.info("OrderPayApplicationServiceImpl batchDingTaskApprove processed approvalPayApplicationDTOs size:{}", approvalPayApplicationDTOs.size());
        }
    }


    /**
     * 是否对公转账
     */
    public boolean isPublicAccountTransfer(OrderFeeDetailExpandTypeEnum feeType) {
        log.info("OrderPayApplicationServiceImpl isPublicAccountTransfer feeType:{}", feeType);
        if (ObjUtil.isNull(feeType)) {
            return false;
        }
        List<OrderFeeDetailExpandTypeEnum> publicAccountTransferEnumList = List.of(
                OrderFeeDetailExpandTypeEnum.INSTALLMENT_SECURITY_DEPOSIT,
                OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT,
                OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT,
                OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY,
                OrderFeeDetailExpandTypeEnum.INSTALLMENT_SERVICE_FEE,
                OrderFeeDetailExpandTypeEnum.GPS_DATA_TRANSFER_FEE,
                OrderFeeDetailExpandTypeEnum.GPS_EQUIPMENT_COMPENSATION_FEE,
                OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT
        );
        return publicAccountTransferEnumList.contains(feeType);
    }

    /**
     * 费用明细是否对公转账
     */
    public OrderFeeDetailTradingMethodsEnum feeDetailIsPublicAccountTransfer(OrderFeeDetailExpandTypeEnum feeType) {
        log.info("OrderPayApplicationServiceImpl feeDetailIsPublicAccountTransfer feeType:{}", feeType);
        if (ObjUtil.isNull(feeType)) {
            return null;
        }

        if (isPublicAccountTransfer(feeType)) {
            return OrderFeeDetailTradingMethodsEnum.CORPORATE_TRANSFERS;
        }
        if (feeType == OrderFeeDetailExpandTypeEnum.CURRENT_RETURN_PERFORMANCE
                || feeType == OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE) {
            return OrderFeeDetailTradingMethodsEnum.ONLINE_BANKING_PAYMENT;
        }
        if (ObjUtil.equals(feeType, OrderFeeDetailExpandTypeEnum.TRANSFER_MONTHLY_REPAYMENT)) {
            return OrderFeeDetailTradingMethodsEnum.CORPORATE_TRANSFERS;
        }
        return null;
    }


    @Override
    public Page<OrderPayApplyListVO> searchOptionalOrder(SearchOptionalOrderDTO searchOptionalOrderDTO, LoginUser loginUser) {
        log.info("OrderPayApplicationServiceImpl searchOptionalOrder start searchOptionalOrderDTO:{}", searchOptionalOrderDTO);
        MPJLambdaWrapper<OrderInfoEntity> queryWrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .selectAs(OrderInfoEntity::getId, OrderPayApplyListVO::getOrderId)
                .selectAs(OrderInfoEntity::getFundId, OrderPayApplyListVO::getFundId)
                .selectAs(OrderInfoEntity::getOrderNumber, OrderPayApplyListVO::getOrderNumber)
                .selectAs(OrderInfoEntity::getCustomerName, OrderPayApplyListVO::getCustomerName)
                .selectAs(OrderInfoEntity::getManagerId, OrderPayApplyListVO::getManagerId)
                .selectAs(OrderInfoEntity::getProductId, OrderPayApplyListVO::getProductId)
                .selectAs(OrderInfoEntity::getStoreName, OrderPayApplyListVO::getStoreName)
                .selectAs(OrderInfoEntity::getVehicleNumber, OrderPayApplyListVO::getVehicleNumber)
                .selectAs(OrderFeeInfoEntity::getPaySnNumBack, OrderPayApplyListVO::getPaySnNumBack)
                .leftJoin(OrderFeeInfoEntity.class, OrderFeeInfoEntity::getOrderId, OrderInfoEntity::getId)
                .eq(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                .like(ObjectUtil.isNotEmpty(searchOptionalOrderDTO.getCustomerName()), OrderInfoEntity::getCustomerName, searchOptionalOrderDTO.getCustomerName())
                .like(ObjectUtil.isNotEmpty(searchOptionalOrderDTO.getOrderNumber()), OrderInfoEntity::getOrderNumber, searchOptionalOrderDTO.getOrderNumber())
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                // 使用 notInSql 方法直接传入 SQL 子查询
                .orderByDesc(OrderInfoEntity::getCreateTime)
                ;
        if (ObjUtil.equals(searchOptionalOrderDTO.getFeeType(), OrderFeeDetailExpandTypeEnum.TRANSFER_MONTHLY_REPAYMENT.getCode())) {
            queryWrapper.notInSql(OrderInfoEntity::getId,
                    "SELECT t.order_id FROM lh_order_pay_apply_info t WHERE t.delete_flag = 0 AND t.current_node != -10 AND t.current_node != 30 AND t.order_id IS NOT NULL AND t.fee_type=" + searchOptionalOrderDTO.getFeeType());

        } else {
            queryWrapper.notInSql(OrderInfoEntity::getId,
                    "SELECT t.order_id FROM lh_order_pay_apply_info t WHERE t.delete_flag = 0 AND t.current_node != -10 AND t.order_id IS NOT NULL AND t.fee_type=" + searchOptionalOrderDTO.getFeeType());

        }
        Integer currentNode = null;
        if (searchOptionalOrderDTO.getFeeType() == 1) {
            queryWrapper.in(OrderInfoEntity::getCurrentNode, List.of(States.PROCESS_TERMINAL.getNode(), States.SYSTEM_TERMINAL.getNode()));
            currentNode = States.PROCESS_TERMINAL.getNode();
        }
        Integer feeType = searchOptionalOrderDTO.getFeeType();
        boolean isOfflineFee = false;
        for (OrderFeeDetailExpandTypeEnum type : OrderFeeDetailExpandTypeEnum.getOfflineFeeEnumList()) {
            if (type.getCode().equals(feeType)) {
                isOfflineFee = true;
                break;
            }
        }
        if (isOfflineFee) {
            queryWrapper.in(OrderInfoEntity::getCurrentNode, List.of(States.PAYMENT_SUCCESS.getNode(), States.SETTLED.getNode()));
            currentNode = States.PAYMENT_SUCCESS.getNode();
        }
        if (searchOptionalOrderDTO.getFeeType() == 2) {
            queryWrapper.eq(OrderInfoEntity::getCurrentNode, States.PAYMENT_SUCCESS.getNode());
            currentNode = States.PAYMENT_SUCCESS.getNode();
        }

        dataPermissionService.limitOrder(loginUser, currentNode, queryWrapper);

        Page<OrderPayApplyListVO> pageList = orderInfoMapper.selectJoinPage(
                new Page<>(searchOptionalOrderDTO.getPageNum(), searchOptionalOrderDTO.getPageSize()),
                OrderPayApplyListVO.class,
                queryWrapper
        );
        log.info("OrderPayApplicationServiceImpl searchOptionalOrder end pageList:{}", pageList);
        List<OrderPayApplyListVO> records = pageList.getRecords();
        for (OrderPayApplyListVO record : records) {
            log.info("OrderPayApplicationServiceImpl searchOptionalOrder end record:{}", record);
            if (searchOptionalOrderDTO.getPayeeType() == 1) {
                BankAccountSignEntity bankAccountSignEntity = bankAccountSignMapper.selectOne(new MPJLambdaWrapper<BankAccountSignEntity>()
                        .eq(BankAccountSignEntity::getOrderId, record.getOrderId())
                        .eq(BankAccountSignEntity::getSignState, 1)
                        .eq(BankAccountSignEntity::getDeleteFlag, 0)
                        .orderByDesc(BankAccountSignEntity::getCreateTime)
                        .last("limit 1"));
                if (bankAccountSignEntity != null) {
                    log.info("OrderPayApplicationServiceImpl searchOptionalOrder end bankAccountSignEntity:{}", bankAccountSignEntity);
                    record.setPayeeAccountName(bankAccountSignEntity.getBankName());
                    record.setPayeeAccountNumber(bankAccountSignEntity.getBankCardNumber());
                    record.setPayeeAccountBranchName(ObjUtil.isNotEmpty(bankAccountSignEntity.getBankNameUpdate()) ? bankAccountSignEntity.getBankNameUpdate().replace(bankAccountSignEntity.getBankName(), "") : "");
                    record.setPayeePhone(bankAccountSignEntity.getPhone());
                    record.setPayeeCardNumber(bankAccountSignEntity.getIdCardNum());
                }

            }

            if (searchOptionalOrderDTO.getPayeeType() == 2) {
                ManageBankAccountSignEntity manageBankAccountSignEntity = orderPayApplicationPriveteMethod.getDefaultCard(record.getManagerId());
                if (manageBankAccountSignEntity != null) {
                    log.info("OrderPayApplicationServiceImpl searchOptionalOrder end manageBankAccountSignEntity:{}", manageBankAccountSignEntity);
                    record.setPayeeAccountName(manageBankAccountSignEntity.getBankName());
                    record.setPayeeAccountNumber(manageBankAccountSignEntity.getBankCardNumber());
                    if ((manageBankAccountSignEntity.getBankNameUpdate() != null && !manageBankAccountSignEntity.getBankNameUpdate().isEmpty()) &&
                            (manageBankAccountSignEntity.getBankName() != null && !manageBankAccountSignEntity.getBankName().isEmpty())) {
                        record.setPayeeAccountBranchName(manageBankAccountSignEntity.getBankNameUpdate().replace(manageBankAccountSignEntity.getBankName(), ""));
                    } else {
                        if (manageBankAccountSignEntity.getBankNameUpdate() != null && !manageBankAccountSignEntity.getBankNameUpdate().isEmpty()) {
                            record.setPayeeAccountBranchName(manageBankAccountSignEntity.getBankName());
                        } else {
                            record.setPayeeAccountBranchName(manageBankAccountSignEntity.getBankNameUpdate());
                        }
                    }
                    record.setPayeePhone(manageBankAccountSignEntity.getPhone());
                    record.setPayeeCardNumber(manageBankAccountSignEntity.getIdCardNum());
                }
            }

            if (searchOptionalOrderDTO.getFeeType() == 2) {
                OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new MPJLambdaWrapper<OrderAmountEntity>()
                        .eq(OrderAmountEntity::getOrderId, record.getOrderId())
                        .eq(OrderAmountEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderAmountEntity::getCreateTime)
                        .last("limit 1"));
                ProductInfoEntity productInfoEntity = productInfoMapper.selectById(record.getProductId());
                if (orderAmountEntity != null && productInfoEntity != null) {
                    record.setPayeeAmount(orderAmountEntity.getCustomerConfirmAmount().multiply(productInfoEntity.getCashPerformance()));
                }
            }

            if (searchOptionalOrderDTO.getFeeType() == 1) {
                OrderFeeInfoEntity orderFeeInfoEntity = orderFeeInfoMapper.selectOne(new MPJLambdaWrapper<OrderFeeInfoEntity>()
                        .eq(OrderFeeInfoEntity::getOrderId, record.getOrderId())
                        .eq(OrderFeeInfoEntity::getGpsFeeStatus, 2)
                        .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderFeeInfoEntity::getCreateTime)
                        .last("limit 1"));
                if (orderFeeInfoEntity != null) {
                    record.setPayeeAmount(orderFeeInfoEntity.getGpsTotalFee());
                }
            }

            Result<UserInfoVO> userInfoVOResult = userFeign.searchUserName(record.getManagerId());
            if (Result.isSuccess(userInfoVOResult)) {
                UserInfoVO data = userInfoVOResult.getData();
                record.setManagerName(data.getName());
            }

        }
        log.info("OrderPayApplicationServiceImpl searchOptionalOrder end records:{}", records);
        return pageList.setRecords(records);
    }



    @Override
    public Page<OrderPayApplyNodeRecordListVO> getApproveRecordPage(PayRecordDTO dto) {
        MPJLambdaWrapper<OrderPayApplyNodeRecordEntity> lqw = new MPJLambdaWrapper<>();
        lqw.eq(ObjUtil.isNotNull(dto.getId()), OrderPayApplyNodeRecordEntity::getApplyInfoId, dto.getId());
        lqw.eq(OrderPayApplyNodeRecordEntity::getDeleteFlag, 0);
        lqw.orderByDesc(OrderPayApplyNodeRecordEntity::getCreateTime);

        Page<OrderPayApplyNodeRecordListVO> pageList = orderPayApplyNodeRecordMapper.selectJoinPage(
                new Page<>(dto.getPageNum(), dto.getPageSize()),
                OrderPayApplyNodeRecordListVO.class,
                lqw
        );
        OrderPayApplicationInfoEntity entity = orderPayApplicationMapper.selectById(dto.getId());
        OrderInfoEntity byId = orderService.getById(entity.getOrderId());
        List<OrderPayApplyNodeRecordListVO> records = pageList.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<Integer> updateUserIdList = records.stream()
                    .map(OrderPayApplyNodeRecordListVO::getUpdateBy)
                    .distinct() // 避免重复的 userId
                    .collect(Collectors.toList());

            Result<List<UserInfoVO>> updateUserListResult = userFeign.searchUserNameByUserIds(updateUserIdList);
            List<UserInfoVO> userInfoVOList = Result.isSuccess(updateUserListResult)
                    ? updateUserListResult.getData()
                    : Collections.emptyList();

            Map<Integer, UserInfoVO> userInfoMap = userInfoVOList.stream()
                    .collect(Collectors.toMap(UserInfoVO::getUserId, Function.identity()));

            records.forEach(record -> {
                if (ObjUtil.isNotNull(byId) && Objects.equals(byId.getFundId(), FundEnum.ZHONG_HENG_TONG_HUI.getValue()) && Objects.equals(record.getCurrentNode(), PayApplicationNodeEnums.APPROVE_APPROVAL)) {
                    record.setUpdateByName(entity.getFundAuditor());
                } else {
                    UserInfoVO userInfo = userInfoMap.get(record.getUpdateBy());
                    if (ObjectUtil.isNotNull(userInfo)) {
                        record.setUpdateByName(userInfo.getName());
                    }
                }

            });
        }

        return pageList;
    }


    @Override
    public Page<OrderPayApplyListVO> payApplicationPageList(PayApplicationPageListDTO payApplicationPageListDTO, LoginUser loginUser) {
        log.info("OrderPayApplicationServiceImpl payApplicationPageList start payApplicationPageListDTO:{}", JSONUtil.toJsonStr(payApplicationPageListDTO));
        PayApplicationNodeEnums currentNode = payApplicationPageListDTO.getCurrentNode();
        MPJLambdaWrapper<OrderPayApplicationInfoEntity> queryWrapper = new MPJLambdaWrapper<OrderPayApplicationInfoEntity>()
                .select(OrderInfoEntity::getManagerId,
                        OrderInfoEntity::getTeamId)
                .selectAs(OrderInfoEntity::getId, OrderPayApplyListVO::getOrderId)
                .selectAs(OrderFeeInfoEntity::getPaySnNumBack, OrderPayApplyListVO::getPaySnNumBack)
                .select(
                        "CASE WHEN t.order_source = 1 THEN t1.vehicle_number ELSE t3.vehicle_number END AS vehicleNumber",
                        "CASE WHEN t.order_source = 1 THEN t1.store_name ELSE t3.store_name END AS storeName",
                        "CASE WHEN t.order_source = 1 THEN t1.customer_name ELSE t3.customer_name END AS customerName",
                        "CASE WHEN t.order_source = 1 THEN t1.order_number ELSE t3.order_id END AS orderNumber",
                        "CASE WHEN t.order_source = 1 THEN t1.region_name ELSE t3.region_name END AS regionName",
                        "CASE WHEN t.order_source = 1 THEN t1.fund_name ELSE t3.fund_name END AS fundName",
                        "CASE WHEN t.order_source = 1 THEN t1.product_name ELSE t3.product_name END AS productName"
                )
                .selectAs(DigitalOutsourcingOrderEntity::getBusinessManager, OrderPayApplyListVO::getManagerName)

                .selectAs(OrderPayApplicationInfoEntity::getCurrentNode, OrderPayApplyListVO::getCurrentNode)
                .selectAs(OrderPayApplicationInfoEntity::getPaymentDetails, OrderPayApplyListVO::getPaymentDetails)
                .selectAs(OrderPayApplicationInfoEntity::getPayAccount, OrderPayApplyListVO::getPayAccount)
                .selectAs(OrderPayApplicationInfoEntity::getPayAccountName, OrderPayApplyListVO::getPayAccountName)
                .selectAs(OrderPayApplicationInfoEntity::getPayAccountNumber, OrderPayApplyListVO::getPayAccountNumber)
                .selectAs(OrderPayApplicationInfoEntity::getId, OrderPayApplyListVO::getId)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccount, OrderPayApplyListVO::getPayeeAccount)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccountName, OrderPayApplyListVO::getPayeeAccountName)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAmount, OrderPayApplyListVO::getPayeeAmount)
                .selectAs(OrderPayApplicationInfoEntity::getFeeType, OrderPayApplyListVO::getFeeType)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeType, OrderPayApplyListVO::getPayeeType)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccountNumber, OrderPayApplyListVO::getPayeeAccountNumber)
                .selectAs(OrderPayApplicationInfoEntity::getApplyUserId, OrderPayApplyListVO::getUserId)
                .selectAs(OrderPayApplicationInfoEntity::getCreateTime, OrderPayApplyListVO::getSubmitTime)
                .selectAs(OrderPayApplicationInfoEntity::getRemark, OrderPayApplyListVO::getRemark)
                .selectAs(OrderPayApplicationInfoEntity::getAccountantRemark, OrderPayApplyListVO::getAccountantRemark)
                .selectAs(OrderPayApplicationInfoEntity::getCashierRemark, OrderPayApplyListVO::getCashierRemark)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccountBranchName, OrderPayApplyListVO::getPayeeAccountBranchName)
                .selectAs(OrderPayApplicationInfoEntity::getPayeePhone, OrderPayApplyListVO::getPayeePhone)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeCardNumber, OrderPayApplyListVO::getPayeeCardNumber)
                .selectAs(OrderPayApplicationInfoEntity::getAccountantApproveTime, OrderPayApplyListVO::getAccountantApproveTime)
                .selectAs(OrderPayApplicationInfoEntity::getAccountantUserId, OrderPayApplyListVO::getAccountantChecker)
                .selectAs(OrderPayApplicationInfoEntity::getCashierApproveTime, OrderPayApplyListVO::getCashierApproveTime)
                .selectAs(OrderPayApplicationInfoEntity::getPaymentTime, OrderPayApplyListVO::getPaymentTime)
                .selectAs(OrderPayApplicationInfoEntity::getCashierUserId, OrderPayApplyListVO::getCashierChecker)
                .selectAs(OrderPayApplicationInfoEntity::getApplyType, OrderPayApplyListVO::getApplyType)
                .selectAs(OrderPayApplicationInfoEntity::getFeeDetails, OrderPayApplyListVO::getFeeDetails)
                .selectAs(OrderPayApplicationInfoEntity::getPaymentVoucherList, OrderPayApplyListVO::getPaymentVoucherList)
                .selectAs(OrderPayApplicationInfoEntity::getOrderSource, OrderPayApplyListVO::getOrderSource)
                .selectAs(OrderPayApplicationInfoEntity::getOrderApplicationSource, OrderPayApplyListVO::getOrderApplicationSource)
                .selectAs(OrderPayApplicationInfoEntity::getRepaymentTerm, OrderPayApplyListVO::getRepaymentTerm)
                .leftJoin(OrderInfoEntity.class, on -> on.eq(OrderInfoEntity::getId, OrderPayApplicationInfoEntity::getOrderId).eq(OrderPayApplicationInfoEntity::getOrderSource, 1))
                .leftJoin(OrderFeeInfoEntity.class,
                        on -> on.eq(OrderFeeInfoEntity::getOrderId, OrderPayApplicationInfoEntity::getOrderId).eq(OrderFeeInfoEntity::getGpsFeeStatus, 2).eq(OrderPayApplicationInfoEntity::getOrderSource, 1).eq(OrderFeeInfoEntity::getDeleteFlag,0))
                .leftJoin(DigitalOutsourcingOrderEntity.class, on-> on.eq(DigitalOutsourcingOrderEntity::getOrderId, OrderPayApplicationInfoEntity::getOrderNumber).ne(OrderPayApplicationInfoEntity::getOrderSource, 1).eq(DigitalOutsourcingOrderEntity::getDeleteFlag,0))
                .leftJoin(CaseInfoEntity.class,on->on.eq(CaseInfoEntity::getDigitalOrderId, DigitalOutsourcingOrderEntity::getOrderNo).eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS).eq(CaseInfoEntity::getDeleteFlag, 0))
                .eq(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getFeeDetails()), OrderPayApplicationInfoEntity::getFeeDetails, payApplicationPageListDTO.getFeeDetails())
                .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                .in(CollUtil.isNotEmpty(payApplicationPageListDTO.getFeeType()), OrderPayApplicationInfoEntity::getFeeType, payApplicationPageListDTO.getFeeType())
                .eq(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getPaymentDetails()), OrderPayApplicationInfoEntity::getPaymentDetails, payApplicationPageListDTO.getPaymentDetails())
                .like(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getPayeeAccount()), OrderPayApplicationInfoEntity::getPayeeAccount, payApplicationPageListDTO.getPayeeAccount())
                .and(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getVehicleNumber()), or -> or.like(OrderInfoEntity::getVehicleNumber, payApplicationPageListDTO.getVehicleNumber())
                        .or().like(DigitalOutsourcingOrderEntity::getVehicleNumber, payApplicationPageListDTO.getVehicleNumber())
                )
//                .like(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, payApplicationPageListDTO.getVehicleNumber())
                .and(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getOrderNumber()), or -> or.like(OrderInfoEntity::getOrderNumber, payApplicationPageListDTO.getOrderNumber())
                        .or().like(DigitalOutsourcingOrderEntity::getOrderId, payApplicationPageListDTO.getOrderNumber())
                )
//                .like(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getOrderNumber()), OrderInfoEntity::getOrderNumber, payApplicationPageListDTO.getOrderNumber())
                .eq(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getCurrentNode()), OrderPayApplicationInfoEntity::getCurrentNode, payApplicationPageListDTO.getCurrentNode())
                .eq(ObjUtil.isNotNull(payApplicationPageListDTO.getOrderApplicationSource()), OrderPayApplicationInfoEntity::getOrderApplicationSource, payApplicationPageListDTO.getOrderApplicationSource())
                .eq(ObjUtil.isNotNull(payApplicationPageListDTO.getOrderSource()), OrderPayApplicationInfoEntity::getOrderSource, payApplicationPageListDTO.getOrderSource())
                .and(StringUtils.hasText(payApplicationPageListDTO.getCustomerName()), or -> or.like(OrderInfoEntity::getCustomerName, payApplicationPageListDTO.getCustomerName())
                        .or().like(DigitalOutsourcingOrderEntity::getCustomerName, payApplicationPageListDTO.getCustomerName())
                )
                .orderByDesc(OrderPayApplicationInfoEntity::getCreateTime);

        List<Integer> roleIds = loginUser.getRoleIds();
        String scopes = loginUser.getScopes();
        boolean hasRole = RoleEnum.ACCOUNTANT.hasRole(roleIds) || RoleEnum.CASHIER.hasRole(roleIds) || RoleEnum.SYS_ADMIN.hasRole(roleIds);
        if (currentNode == null || !hasRole || (scopes != null && !scopes.contains("data:all"))) {
            // 和订单状态权限控 orderPayApplicationMapper
            if ((!ObjUtil.equals(payApplicationPageListDTO.getOrderApplicationSource(), 1))) {
                dataPermissionService.limitPayApplicationWithOrder(loginUser, queryWrapper);
            }
        }
        if (ObjUtil.isNotNull(payApplicationPageListDTO.getPaymentTime())) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getPaymentTime,
                    LocalDateTime.of(payApplicationPageListDTO.getPaymentTime(), LocalTime.MIN), LocalDateTime.of(payApplicationPageListDTO.getPaymentTime(), LocalTime.MAX));
        }

        if (payApplicationPageListDTO.getCashierApproveTime() != null && !payApplicationPageListDTO.getCashierApproveTime().isEmpty()) {
            String cashierApproveTime = payApplicationPageListDTO.getCashierApproveTime();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            LocalDate specifiedDate = LocalDate.parse(cashierApproveTime, formatter);
            queryWrapper.between(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getCashierApproveTime()), OrderPayApplicationInfoEntity::getCashierApproveTime, specifiedDate.atStartOfDay(), specifiedDate.atTime(23, 59, 59));
        }

        if (payApplicationPageListDTO.getCashierApproveTimeStartDate() != null && payApplicationPageListDTO.getCashierApproveTimeEndDate() == null) {
            queryWrapper.ge(OrderPayApplicationInfoEntity::getCashierApproveTime, getTimeOfStart(payApplicationPageListDTO.getCashierApproveTimeStartDate()));
        }
        if (payApplicationPageListDTO.getCashierApproveTimeStartDate() == null && payApplicationPageListDTO.getCashierApproveTimeEndDate() != null) {
            queryWrapper.le(OrderPayApplicationInfoEntity::getCashierApproveTime, getTimeOfEnd(payApplicationPageListDTO.getCashierApproveTimeEndDate()));
        }
        if (payApplicationPageListDTO.getCashierApproveTimeStartDate() != null && payApplicationPageListDTO.getCashierApproveTimeEndDate() != null) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getCashierApproveTime,
                    getTimeOfStart(payApplicationPageListDTO.getCashierApproveTimeStartDate()),
                    getTimeOfEnd(payApplicationPageListDTO.getCashierApproveTimeEndDate()));
        }

        if (payApplicationPageListDTO.getApproveTime() != null && !payApplicationPageListDTO.getApproveTime().isEmpty()) {
            String approveTime = payApplicationPageListDTO.getApproveTime();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            LocalDate approveTimeDate = LocalDate.parse(approveTime, formatter);
            queryWrapper.between(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getApproveTime()), OrderPayApplicationInfoEntity::getCreateTime, approveTimeDate.atStartOfDay(), approveTimeDate.atTime(23, 59, 59));
        }
        if (payApplicationPageListDTO.getApproveStartDate() != null && payApplicationPageListDTO.getApproveEndDate() == null) {
            queryWrapper.ge(OrderPayApplicationInfoEntity::getCreateTime, getTimeOfStart(payApplicationPageListDTO.getApproveStartDate()));
        }
        if (payApplicationPageListDTO.getApproveStartDate() == null && payApplicationPageListDTO.getApproveEndDate() != null) {
            queryWrapper.le(OrderPayApplicationInfoEntity::getCreateTime, getTimeOfEnd(payApplicationPageListDTO.getApproveEndDate()));
        }
        if (payApplicationPageListDTO.getApproveStartDate() != null && payApplicationPageListDTO.getApproveEndDate() != null) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getCreateTime,
                    getTimeOfStart(payApplicationPageListDTO.getApproveStartDate()),
                    getTimeOfEnd(payApplicationPageListDTO.getApproveEndDate()));
        }
        if (payApplicationPageListDTO.getPaymentStartTime() != null && payApplicationPageListDTO.getPaymentEndTime() == null) {
            queryWrapper.ge(OrderPayApplicationInfoEntity::getPaymentTime, getTimeOfStart(payApplicationPageListDTO.getApproveStartDate()));
        }
        if (payApplicationPageListDTO.getPaymentStartTime() == null && payApplicationPageListDTO.getPaymentEndTime() != null) {
            queryWrapper.le(OrderPayApplicationInfoEntity::getPaymentTime, getTimeOfEnd(payApplicationPageListDTO.getApproveEndDate()));
        }
        if (payApplicationPageListDTO.getPaymentStartTime() != null && payApplicationPageListDTO.getPaymentEndTime() != null) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getPaymentTime,
                    getTimeOfStart(payApplicationPageListDTO.getPaymentStartTime()),
                    getTimeOfEnd(payApplicationPageListDTO.getPaymentEndTime()));
        }


        Page<OrderPayApplyListVO> orderPayApplyListVOPage = orderPayApplicationMapper.selectJoinPage(new Page<>(payApplicationPageListDTO.getPageNum(), payApplicationPageListDTO.getPageSize()), OrderPayApplyListVO.class, queryWrapper);
        List<OrderPayApplyListVO> records = orderPayApplyListVOPage.getRecords();
        BigDecimal totalAmount = BigDecimal.ZERO;


        // 客户经理id 提交人id List
        List<Integer> userIds = Stream.concat(
                        records.stream().filter(orderPayApplyListVO -> ObjUtil.isNotEmpty(orderPayApplyListVO.getManagerId()))
                                .map(OrderPayApplyListVO::getManagerId),
                        records.stream().filter(orderPayApplyListVO -> ObjUtil.isNotEmpty(orderPayApplyListVO.getUserId()))
                                .map(OrderPayApplyListVO::getUserId)
                )
                .distinct()
                .toList();

        Map<Integer, UserInfoVO> userInfoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userIds)) {
            // 获取用户列表
            Result<List<UserInfoVO>> userInfoResult = userFeign.searchUserNameByUserIds(userIds);
            if (Result.isSuccess(userInfoResult) && ObjUtil.isNotEmpty(userInfoResult.getData())) {
                userInfoMap = userInfoResult.getData().stream()
                        .collect(Collectors.toMap(UserInfoVO::getUserId, userInfoVO -> userInfoVO));
            }
        }


        for (OrderPayApplyListVO orderPayApplyListVO : records) {
            if (ObjUtil.isNotNull(orderPayApplyListVO.getPaymentTime())){
                orderPayApplyListVO.setPaymentTimeToDate(orderPayApplyListVO.getPaymentTime().toLocalDate());
            }
            if (ObjUtil.isNotNull(orderPayApplyListVO.getPayeeAmount())) {
                totalAmount = totalAmount.add(orderPayApplyListVO.getPayeeAmount());
            }
            if (orderPayApplyListVO.getUserId() != null && orderPayApplyListVO.getUserId() != 1) {
                UserInfoVO userInfoVO = userInfoMap.get(orderPayApplyListVO.getUserId());
                if (ObjUtil.isNotNull(userInfoVO)) {
                    orderPayApplyListVO.setUserName(userInfoVO.getName());
                }
            } else {
                orderPayApplyListVO.setUserName("系统自动提交");
            }
            if (StrUtil.isNotBlank(orderPayApplyListVO.getPaymentVoucherList())) {
                // 将 JSON 字符串转换为 List<String>
                List<String> paymentVoucherList = JSON.parseArray(orderPayApplyListVO.getPaymentVoucherList(), String.class);
                orderPayApplyListVO.setResourceId(paymentVoucherList);
            }
            if (Objects.equals(orderPayApplyListVO.getFeeType(), OrderFeeDetailExpandTypeEnum.CURRENT_RETURN_PERFORMANCE.getCode())) {
                if (ObjUtil.isNotEmpty(orderPayApplyListVO.getManagerId())) {
                    Integer managerId = orderPayApplyListVO.getManagerId();
                    UserInfoVO userInfoVO = userInfoMap.get(managerId);
                    if (ObjUtil.isNotNull(userInfoVO)) {
                        orderPayApplyListVO.setPayeeName(userInfoVO.getName());
                    }
                }
            }
            orderPayApplyListVO.setBranchName(ObjUtil.defaultIfNull(orderPayApplyListVO.getStoreName(), ""));
//            PreOcrIdentityCardEntity entity = preOcrIdentityCardService.getOne(new LambdaQueryWrapper<PreOcrIdentityCardEntity>()
//                    .eq(PreOcrIdentityCardEntity::getName, orderPayApplyListVO.getPayeeCardNumber())
//                    .eq(PreOcrIdentityCardEntity::getDeleteFlag, 0)
//                    .orderByDesc(PreOcrIdentityCardEntity::getCreateTime)
//                    .last("limit 1")
//            );
//            if (ObjUtil.isNotEmpty(entity)){
//                orderPayApplyListVO.setUserName(entity.getName());
//            }else {
//                if (ObjUtil.isNotEmpty(orderPayApplyListVO.getPayeePhone())){
//                    Result<UserDetailInfoVO> userDetailInfoVOResult = userFeign.searchUserDetailByMobile(orderPayApplyListVO.getPayeePhone());
//                    if (Result.isSuccess(userDetailInfoVOResult)&&userDetailInfoVOResult.getData()!=null){
//                        UserDetailInfoVO userDetailInfoVO = userDetailInfoVOResult.getData();
//                        orderPayApplyListVO.setPayeeName(userDetailInfoVO.getName());
//                    }
//                }
//            }
        }

        List<Integer> teamIds = records.stream().map(OrderPayApplyListVO::getTeamId).filter(Objects::nonNull).toList();
        if (CollUtil.isNotEmpty(teamIds)) {
            List<DeptInfoVO> branchNameVOS = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData();
            Map<Integer, String> branchNameMap = branchNameVOS.stream().filter(e -> ObjUtil.isNotNull(e.getName())).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
            records.stream().filter(item -> item.getTeamId() != null).forEach(record -> {
                record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(branchNameMap.get(record.getTeamId()), ""));
            });
        }


//        orderPayApplyListVOPage.setRecords(records);
        return orderPayApplyListVOPage;
    }

    @Override
    public String automaticSubmit() {
        // 获取昨天的零点时间
//        LocalDateTime yesterdayMidnight = LocalDate.now().minusDays(1).atStartOfDay();

        // 获取今天的零点时间
//        LocalDateTime todayMidnight = LocalDate.now().atStartOfDay();

        // 查询昨天零点到今天零点之间放款的订单
        List<OrderInfoEntity> orderInfoEntities = orderInfoMapper.selectList(
                new LambdaQueryWrapper<OrderInfoEntity>()
                        .eq(OrderInfoEntity::getCurrentNode, States.PAYMENT_SUCCESS)
                        .eq(OrderInfoEntity::getDeleteFlag, 0)
                        .ne(OrderInfoEntity::getSourceType, 1)
                        .ne(OrderInfoEntity::getSource, "电销")
                        .ne(OrderInfoEntity::getRegionId, 65) //屏蔽创新大区
//                        .between(OrderInfoEntity::getPaymentTime, yesterdayMidnight, todayMidnight) // 使用 between 方法来指定时间范围
                        .notInSql(OrderInfoEntity::getId,
                                "SELECT t.order_id FROM lh_order_pay_apply_info t WHERE t.fee_type = 2 AND t.order_id IS NOT NULL AND t.current_node != -10")
        );
        //付款方账户
        String payAccount = null;
        //付款方银行卡
        String payAccountNumber = null;
        //付款方开户行
        String payAccountName = null;
//        try {
//            String[] strings = splitString(dictUtils.getDictLabel(GlobalConstants.DictType.PAYEE_ACCOUNT.name(), 1));
//            payAccount = strings[0];
//            payAccountNumber = strings[2];
//            payAccountName = strings[1];
//        } catch (Exception e) {
//            log.error("格式化付款方信息失败，原因：{}", e.getMessage());
//        }
        for (OrderInfoEntity orderInfoEntity : orderInfoEntities) {
            OrderPayApplicationInfoEntity orderPayApplicationInfoEntity = new OrderPayApplicationInfoEntity()
                    .setOrderId(orderInfoEntity.getId())
                    .setFeeType(OrderFeeDetailExpandTypeEnum.CURRENT_RETURN_PERFORMANCE)
                    .setPayeeType(PayApplicationPayeeTypeEnum.BUSINESS_PERSONNEL)
//                    .setPayAccount("1")
                    //去掉谛听
//                    .setPayAccount(payAccount)
//                    .setPayAccountName(payAccountName)
//                    .setPayAccountNumber(payAccountNumber)
                    .setCurrentNode(PayApplicationNodeEnums.ACCOUNTANT_APPROVAL)
                    .setApplyUserId(1)
                    .setRemark("自动提交");
            ManageBankAccountSignEntity defaultCard = orderPayApplicationPriveteMethod.getDefaultCard(orderInfoEntity.getManagerId());
            if (defaultCard == null) {
                log.error("未查询到银行卡号，订单号：{}", orderInfoEntity.getId());
                continue;
            }

//            if (orderInfoEntity.getManagerId() != null) {
//                Result<UserInfoVO> userInfoVOResult = userFeign.searchUserName(orderInfoEntity.getManagerId());
//                if (Result.isSuccess(userInfoVOResult)) {
//                    UserInfoVO data = userInfoVOResult.getData();
//                    orderPayApplicationInfoEntity.setPayeeAccount(data.getName());
//                }
//            }
            //收款方姓名、身份证号、手机号、账号、开户行及网点
            orderPayApplicationInfoEntity.setPayeeAccountName(defaultCard.getBankName())
                    .setPayeeAccountNumber(defaultCard.getBankCardNumber())
                    .setPayeeAccountBranchName(defaultCard.getBankNameUpdate())
                    .setPayeeCardNumber(defaultCard.getIdCardNum())
                    .setPayeeAccountBranchName(defaultCard.getBankNameUpdate())
                    .setPayeePhone(defaultCard.getPhone())
                    .setPayeeAccount(defaultCard.getName());
            if (ObjUtil.isNotEmpty(defaultCard.getBankName())
                    &&
                    ObjUtil.isNotEmpty(defaultCard.getBankCardNumber())
                    &&
                    ObjUtil.isNotEmpty(defaultCard.getPhone())
                    &&
                    ObjUtil.isNotEmpty(defaultCard.getIdCardNum())
                    &&
                    ObjUtil.isNotEmpty(defaultCard.getName())
                    &&
                    ObjUtil.isNotEmpty(defaultCard.getBankNameUpdate())
            ) {
                orderPayApplicationInfoEntity.setCurrentNode(PayApplicationNodeEnums.CASHIER_APPROVAL);
            }
            OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new MPJLambdaWrapper<OrderAmountEntity>()
                    .eq(OrderAmountEntity::getOrderId, orderInfoEntity.getId())
                    .eq(OrderAmountEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderAmountEntity::getCreateTime)
                    .last("limit 1"));
            ProductInfoEntity productInfoEntity = productInfoMapper.selectById(orderInfoEntity.getProductId());
            if (orderAmountEntity != null && productInfoEntity != null) {
                //设置使用新计算方式的资方
                List<Integer> newFundIds = List.of(FundEnum.FU_MIN.getValue(), FundEnum.LAN_HAI.getValue());
                SwitchVO newLendersPerformance = switchUtils.getSwitchInfo("NEW_LENDERS_PERFORMANCE");
                //通汇20250812开始
                if (ObjUtil.equals(orderInfoEntity.getFundId(),FundEnum.ZHONG_HENG_TONG_HUI.getValue())){
                    LocalDateTime effectiveTime= LocalDateTimeUtil.parse("2025-08-12 00:00:00", "yyyy-MM-dd HH:mm:ss");
                    PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(orderInfoEntity.getPreId());
                    if (preApprovalApplyInfoEntity.getRiskSubmitTime().isBefore(effectiveTime)) {
                        orderPayApplicationInfoEntity.setPayeeAmount(orderAmountEntity.getCustomerConfirmAmount().multiply(productInfoEntity.getCashPerformance()));
                    }else {
                        //新计算方式
                        OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectById(orderInfoEntity.getCustomerId());
                        Integer customerLevel = orderCustomerInfoEntity.getPreCustomerLevel();
                        if (ObjUtil.equal(customerLevel, 1)) {
                            orderPayApplicationInfoEntity.setPayeeAmount(orderAmountEntity.getCustomerConfirmAmount()
                                    .multiply(productInfoEntity.getCustomerLevelPerformanceA()));
                        } else if (ObjUtil.equal(customerLevel, 3)) {
                            orderPayApplicationInfoEntity.setPayeeAmount(orderAmountEntity.getCustomerConfirmAmount()
                                    .multiply(productInfoEntity.getCustomerLevelPerformanceC()));
                        } else {
                            orderPayApplicationInfoEntity.setPayeeAmount(orderAmountEntity.getCustomerConfirmAmount()
                                    .multiply(productInfoEntity.getCustomerLevelPerformanceB()));
                        }
                    }
                }else
                //todo 调整金额
                if (newFundIds.contains(orderInfoEntity.getFundId())
                        && ObjUtil.isNotNull(newLendersPerformance)
                        && ObjUtil.equal(newLendersPerformance.getSwitchFlag(), 1)) {
                    LocalDateTime effectiveTime = LocalDateTimeUtil.parse(newLendersPerformance.getValue(), "yyyy-MM-dd HH:mm:ss");
                    PreApprovalApplyInfoEntity preApprovalApplyInfoEntity = preApprovalApplyInfoMapper.selectById(orderInfoEntity.getPreId());
                    if (preApprovalApplyInfoEntity.getRiskSubmitTime().isAfter(effectiveTime)) {
                        //新计算方式
                        OrderCustomerInfoEntity orderCustomerInfoEntity = orderCustomerInfoMapper.selectById(orderInfoEntity.getCustomerId());
                        Integer customerLevel = orderCustomerInfoEntity.getPreCustomerLevel();
                        if (ObjUtil.equal(customerLevel, 1)) {
                            orderPayApplicationInfoEntity.setPayeeAmount(orderAmountEntity.getCustomerConfirmAmount()
                                    .multiply(productInfoEntity.getCustomerLevelPerformanceA()));
                        } else if (ObjUtil.equal(customerLevel, 3)) {
                            orderPayApplicationInfoEntity.setPayeeAmount(orderAmountEntity.getCustomerConfirmAmount()
                                    .multiply(productInfoEntity.getCustomerLevelPerformanceC()));
                        } else {
                            orderPayApplicationInfoEntity.setPayeeAmount(orderAmountEntity.getCustomerConfirmAmount()
                                    .multiply(productInfoEntity.getCustomerLevelPerformanceB()));
                        }
                    } else {
                        //旧计算方式
                        orderPayApplicationInfoEntity.setPayeeAmount(orderAmountEntity.getCustomerConfirmAmount().multiply(productInfoEntity.getCashPerformance()));
                    }
                } else {
                    //旧计算方式
                    orderPayApplicationInfoEntity.setPayeeAmount(orderAmountEntity.getCustomerConfirmAmount().multiply(productInfoEntity.getCashPerformance()));
                }
            }
            log.info("自动提交订单：{}", orderPayApplicationInfoEntity);
            orderPayApplicationMapper.insert(orderPayApplicationInfoEntity);

            orderPayApplicationPriveteMethod.saveNodeRecord(orderPayApplicationInfoEntity.getId(), PayApplicationNodeEnums.ACCOUNTANT_APPLY, orderPayApplicationInfoEntity.getCurrentNode(), PayApplicationAuditTypeEnum.YUNQI
                    , null, orderPayApplicationInfoEntity.getRemark(), PayApplicationEventEnums.APPROVE_PASS, 1, LocalDateTime.now());

        }

        return "success";

    }

    @Override
    public List<FileVO> exportPayApplyExcel(PayApplicationPageListDTO payApplicationDTO, LoginUser loginUser) {
        payApplicationDTO.setPageNum(1);
        payApplicationDTO.setPageSize(-1);
        try {
            String[] head = new String[]{"单据编号", "车牌号", "进件编号", "现返绩效比率", "放款时间", "所属资方", "放款金额", "客户名称", "产品名称", "产品期数", "地区(门店)",
                    "所属大区", "费用类型", "交易金额", "备注", "申请类型", "收款方", "收款方账号", "收款方开户行", "收款方预留手机号", "收款方身份证号", "付款方"
                    , "付款方账户", "付款方开户行", "审核状态", "支付时间", "申请时间", "申请备注", "会计审核人", "会计审核时间", "会计审核备注", "出纳审核人", "出纳审核时间", "出纳审核备注"
                    , "提交人", "提交人角色"};
            Page<OrderPayApplyListVO> orderPayApplyListVOPage = this.payApplicationPageList(payApplicationDTO, loginUser);
            List<OrderPayApplyListVO> records = orderPayApplyListVOPage.getRecords();
            if (!records.isEmpty()) {
                List<HashMap<String, Object>> dataList = new ArrayList<>();

                // 异步获取productMap
                CompletableFuture<Map<Integer, ProductInfoEntity>> productMapFuture = CompletableFuture.supplyAsync(() -> {
                    Map<Integer, ProductInfoEntity> productMap = new HashMap<>();
                    List<Integer> productIdList = records.stream()
                            .filter(item -> ObjUtil.isNotNull(item.getProductId()))
                            .map(OrderPayApplyListVO::getProductId)
                            .distinct()
                            .collect(Collectors.toList());
                    if (!productIdList.isEmpty()) {
                        List<ProductInfoEntity> productInfoEntities = productInfoMapper.selectBatchIds(productIdList);
                        productMap = productInfoEntities.stream()
                                .collect(Collectors.toMap(ProductInfoEntity::getId, product -> product));
                    }
                    return productMap;
                });

                // 异步获取orderInfoMap
                CompletableFuture<Map<Integer, OrderInfoEntity>> orderInfoMapFuture = CompletableFuture.supplyAsync(() -> {
                    Map<Integer, OrderInfoEntity> orderInfoMap = new HashMap<>();
                    List<Integer> orderInfoList = records.stream()
                            .filter(item -> ObjUtil.isNotNull(item.getOrderId()))
                            .map(OrderPayApplyListVO::getOrderId)
                            .distinct()
                            .collect(Collectors.toList());
                    if (!orderInfoList.isEmpty()) {
                        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectBatchIds(orderInfoList);
                        orderInfoMap = orderInfoEntityList.stream()
                                .collect(Collectors.toMap(OrderInfoEntity::getId, orderInfo -> orderInfo));
                    }
                    return orderInfoMap;
                });

                // 异步获取userInfoMap
                CompletableFuture<Map<Integer, UserInfoVO>> userInfoMapFuture = CompletableFuture.supplyAsync(() -> {
                    Map<Integer, UserInfoVO> userInfoMap = new HashMap<>();
                    List<Integer> userIds = Stream.concat(
                                    records.stream().filter(orderPayApplyListVO -> ObjUtil.isNotEmpty(orderPayApplyListVO.getAccountantChecker()))
                                            .map(OrderPayApplyListVO::getAccountantChecker),
                                    records.stream().filter(orderPayApplyListVO -> ObjUtil.isNotEmpty(orderPayApplyListVO.getCashierChecker()))
                                            .map(OrderPayApplyListVO::getCashierChecker)
                            )
                            .distinct()
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(userIds)) {
                        // 获取用户列表
                        Result<List<UserInfoVO>> userInfoResult = userFeign.searchUserNameByUserIds(userIds);
                        if (Result.isSuccess(userInfoResult) && ObjUtil.isNotEmpty(userInfoResult.getData())) {
                            userInfoMap = userInfoResult.getData().stream()
                                    .collect(Collectors.toMap(UserInfoVO::getUserId, userInfoVO -> userInfoVO));
                        }
                    }
                    return userInfoMap;
                });

                // 异步获取orderAmountMap
                CompletableFuture<Map<Integer, OrderAmountEntity>> orderAmountMapFuture = orderInfoMapFuture.thenApply(orderInfoMap -> {
                    Map<Integer, OrderAmountEntity> orderAmountMap = new HashMap<>();
                    List<Integer> orderInfoList = new ArrayList<>(orderInfoMap.keySet());
                    if (!orderInfoList.isEmpty()) {
                        List<OrderAmountEntity> orderAmountEntityList = orderAmountMapper.selectList(new LambdaQueryWrapper<OrderAmountEntity>()
                                .in(OrderAmountEntity::getOrderId, orderInfoList)
                                .eq(OrderAmountEntity::getDeleteFlag, 0)
                        );
                        if (CollUtil.isNotEmpty(orderAmountEntityList)) {
                            orderAmountMap = orderAmountEntityList.stream()
                                    .collect(Collectors.toMap(OrderAmountEntity::getOrderId, orderAmountEntity -> orderAmountEntity));
                        }
                    }
                    return orderAmountMap;
                });

                // 等待所有异步任务完成
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                        productMapFuture,
                        orderInfoMapFuture,
                        userInfoMapFuture,
                        orderAmountMapFuture
                );

                // 使用 CompletableFuture.supplyAsync 来返回 dataList
                CompletableFuture<List<HashMap<String, Object>>> dataListFuture = allFutures.thenApply(v -> {
                    try {
                        Map<Integer, ProductInfoEntity> productMap = productMapFuture.join();
                        Map<Integer, OrderInfoEntity> orderInfoMap = orderInfoMapFuture.join();
                        Map<Integer, UserInfoVO> userInfoMap = userInfoMapFuture.join();
                        Map<Integer, OrderAmountEntity> orderAmountMap = orderAmountMapFuture.join();

                        for (OrderPayApplyListVO orderPayApplyListVO : records) {
                            OrderInfoEntity orderInfo = orderInfoMap.get(orderPayApplyListVO.getOrderId());
                            if (ObjUtil.isNull(orderInfo)) {
                                log.warn("OrderPayApplicationServiceImpl.exportPayApplyExcel OrderInfoEntity is null, orderId：{}", orderPayApplyListVO.getOrderId());
                                continue;
                            }
                            ProductInfoEntity productInfoEntity = productMap.get(orderInfo.getProductId());
                            if (ObjUtil.isNull(productInfoEntity)) {
                                log.warn("OrderPayApplicationServiceImpl.exportPayApplyExcel productInfoEntity is null, orderId：{}", orderPayApplyListVO.getOrderId());
                            }
                            OrderAmountEntity orderAmountEntity = orderAmountMap.get(orderInfo.getId());
                            HashMap<String, Object> dataMap = new HashMap<>();
                            dataMap.put("单据编号", orderPayApplyListVO.getPaymentDetails());
                            dataMap.put("车牌号", orderPayApplyListVO.getVehicleNumber());
                            dataMap.put("进件编号", orderPayApplyListVO.getOrderNumber());
                            if (ObjUtil.isNotNull(productInfoEntity)) {
                                dataMap.put("现返绩效比率", productInfoEntity.getCashPerformance() != null ? productInfoEntity.getCashPerformance().multiply(new BigDecimal(100)) + "%" : "");
                                dataMap.put("产品名称", productInfoEntity.getName());
                                dataMap.put("产品期数", productInfoEntity.getTerm());
                            }
                            dataMap.put("放款时间", orderInfo.getPaymentTime() != null ? orderInfo.getPaymentTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                            dataMap.put("所属资方", orderPayApplyListVO.getFundName());
                            dataMap.put("放款金额", orderAmountEntity.getCustomerConfirmAmount());
                            dataMap.put("客户名称", orderPayApplyListVO.getCustomerName());
                            dataMap.put("地区(门店)", orderInfo.getStoreName());
                            dataMap.put("所属大区", orderPayApplyListVO.getRegionName());
                            dataMap.put("费用类型", orderPayApplyListVO.getFeeType() == 1 ? "车务费" : "现返绩效");
                            dataMap.put("交易金额", orderPayApplyListVO.getPayeeAmount());
                            dataMap.put("备注", orderPayApplyListVO.getRemark());
                            dataMap.put("申请类型", orderPayApplyListVO.getApplyType() == 1 ? "付款单" : "收款单");
                            dataMap.put("收款方", orderPayApplyListVO.getPayeeAccount());
                            dataMap.put("收款方账号", orderPayApplyListVO.getPayeeAccountNumber());
                            dataMap.put("收款方开户行", orderPayApplyListVO.getPayeeAccountName());
                            dataMap.put("收款方预留手机号", orderPayApplyListVO.getPayeePhone());
                            dataMap.put("收款方身份证号", orderPayApplyListVO.getPayeeCardNumber());

                            dataMap.put("付款方", orderPayApplyListVO.getPayAccount());
                            dataMap.put("付款方账户", orderPayApplyListVO.getPayAccountNumber());
                            dataMap.put("付款方开户行", orderPayApplyListVO.getPayAccountName());
                           /* try {
                                String[] strings = splitString(dictUtils.getDictLabel(GlobalConstants.DictType.PAYEE_ACCOUNT.name(), Convert.toInt(orderPayApplyListVO.getPayAccount())));
                                dataMap.put("付款方", strings[0]);
                                dataMap.put("付款方账户", strings[2]);
                                dataMap.put("付款方开户行", strings[1]);
                            } catch (Exception e) {
                                log.error("格式化付款方信息失败，原因：{}", e.getMessage());
                            }*/

                            //                    if (Objects.equals(orderPayApplyListVO.getCurrentNode(), PayApplicationNodeEnums.SUCCESS)) {
                            //                        dataMap.put("出纳审核状态", "出纳审核通过");
                            //                    } else {
                            //                        dataMap.put("出纳审核状态", "待出纳审核");
                            //                    }

                            dataMap.put("审核状态", PayApplicationNodeEnums.getDescriptionByCode(orderPayApplyListVO.getCurrentNode().getCode()));

                            dataMap.put("支付时间", "-");

                            dataMap.put("申请时间", orderPayApplyListVO.getSubmitTime() != null ? orderPayApplyListVO.getSubmitTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                            dataMap.put("申请备注", orderPayApplyListVO.getRemark());
                            if (orderPayApplyListVO.getAccountantChecker() != null) {
                                UserInfoVO userInfoVO = userInfoMap.get(orderPayApplyListVO.getAccountantChecker());
                                if (ObjUtil.isNotNull(userInfoVO)) {
                                    dataMap.put("会计审核人", userInfoVO.getName());
                                }
                            }
                            dataMap.put("会计审核时间", orderPayApplyListVO.getAccountantApproveTime() != null ? orderPayApplyListVO.getAccountantApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                            dataMap.put("会计审核备注", orderPayApplyListVO.getAccountantRemark());
                            if (orderPayApplyListVO.getCashierChecker() != null) {
                                UserInfoVO userInfoVO = userInfoMap.get(orderPayApplyListVO.getCashierChecker());
                                if (ObjUtil.isNotNull(userInfoVO)) {
                                    dataMap.put("出纳审核人", userInfoVO.getName());
                                }
                            }
                            dataMap.put("出纳审核时间", orderPayApplyListVO.getCashierApproveTime() != null ? orderPayApplyListVO.getCashierApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                            dataMap.put("出纳审核备注", orderPayApplyListVO.getCashierRemark());
                            dataMap.put("提交人", orderPayApplyListVO.getUserName());
                            dataMap.put("提交人角色", orderPayApplyListVO.getUserId() == 1 ? "系统自动提交" : "线上运营人员");
                            dataList.add(dataMap);
                        }
                    } catch (Exception e) {
                        log.error("OrderPayApplicationServiceImpl.exportPayApplyExcel error, {}", e.getMessage(), e);
                        throw new BusinessException("导出失败，请重试");
                    }
                    return dataList;
                });

                // 获取 dataList 并导出 Excel
                return ExportExcelUtils.dealAndExportExcel(head, dataListFuture.join());
            } else {
                throw new Exception("无可导出数据");
            }
        } catch (Exception e) {
            log.error("付款申请数据导出,异常{}", e.getMessage(), e);
            throw new BusinessException("付款申请数据导出,异常" + e.getMessage());
        }
    }


    @Override
    public Boolean automatedRepurchaseRepay() {
        log.info("OrderPayApplicationServiceImpl.automatedRepurchaseRepay start");
        //放款成功信息
        List<OrderInfoEntity> orderInfoEntities = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getDeleteFlag, 0)
                .eq(OrderInfoEntity::getCurrentNode, States.PAYMENT_SUCCESS.getNode())
                .isNotNull(OrderInfoEntity::getPaymentTime)
                .eq(OrderInfoEntity::getIsRepurchase, 0)
                .notInSql(OrderInfoEntity::getId,
                        "SELECT t.order_id FROM lh_order_pay_apply_info t WHERE t.fee_type = 3 AND t.delete_flag = 0 AND t.order_id IS NOT NULL")

        );
        log.info("OrderPayApplicationServiceImpl.automatedRepurchaseRepay orderInfoEntities.size():{}", orderInfoEntities.size());
        if (CollUtil.isEmpty(orderInfoEntities)) {
            return true;
        }
        for (OrderInfoEntity orderInfoEntity : orderInfoEntities) {
            log.info("OrderPayApplicationServiceImpl.automatedRepurchaseRepay orderInfoEntity:{}", orderInfoEntity);
            List<OrderPayApplicationInfoEntity> orderPayApplicationInfoEntityList = orderPayApplicationMapper.selectList(
                    new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                            .eq(OrderPayApplicationInfoEntity::getOrderId, orderInfoEntity.getId())
            );
            if (CollUtil.isNotEmpty(orderPayApplicationInfoEntityList) && orderPayApplicationInfoEntityList.stream()
                    .anyMatch(entity -> Objects.equals(entity.getFeeType(),OrderFeeDetailExpandTypeEnum.TRANSFER_MONTHLY_REPAYMENT) || Objects.equals(entity.getFeeType(),OrderFeeDetailExpandTypeEnum.TRANSFER_SETTLE_REPAYMENT))){
                continue;
            }
            //获取所有的逾期还款计划
            List<FundRepaymentInfoEntity> repaymentInfoEntityList = fundRepaymentInfoMapper.selectList(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
                    .eq(FundRepaymentInfoEntity::getOrderId, orderInfoEntity.getId())
                    .eq(FundRepaymentInfoEntity::getFundId, orderInfoEntity.getFundId())
                    .eq(FundRepaymentInfoEntity::getRepaymentStatus, FundRepayStatusEnum.OVERDUE.getValue())
                    .orderByAsc(FundRepaymentInfoEntity::getTerm)
            );
            log.info("OrderPayApplicationServiceImpl.automatedRepurchaseRepay repaymentInfoEntityList.size():{}", repaymentInfoEntityList.size());
            if (CollUtil.isNotEmpty(repaymentInfoEntityList)) {
                //最近一期的逾期信息
                FundRepaymentInfoEntity repaymentInfoEntity = repaymentInfoEntityList.get(0);
                LocalDate repaymentDate = repaymentInfoEntity.getRepaymentDate();
                LocalDate currentDate = LocalDate.now();
                long daysBetween = ChronoUnit.DAYS.between(repaymentDate, currentDate);
                //根据不同资方配置不同的赎回规则
                //资方id
                Integer fundId = orderInfoEntity.getFundId();
                FundEnum fundEnum = FundEnum.getFundEnum(fundId);
                Assert.notNull(fundEnum, () -> new BusinessException("资方不存在"));
                Integer days = null;
                FundRepurchaseAccountEntity fundRepurchaseAccountEntity = new FundRepurchaseAccountEntity();
                List<FundRepurchaseAccountEntity> fundRepurchaseAccountEntityList = fundRepurchaseAccountMapper.selectList(new LambdaQueryWrapper<FundRepurchaseAccountEntity>()
                        .eq(FundRepurchaseAccountEntity::getFundId, fundId)
                        .eq(FundRepurchaseAccountEntity::getDeleteFlag, 0)
                        .orderByDesc(FundRepurchaseAccountEntity::getId));
                if (CollUtil.isNotEmpty(fundRepurchaseAccountEntityList)) {
                    fundRepurchaseAccountEntity = fundRepurchaseAccountEntityList.get(0);
                }
                //根据 fundEnum switch 匹配资方 赎回规则
                switch (Objects.requireNonNull(fundEnum)) {
                    case YING_FENG:
                        SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.YINGFENG_REPURCHASE_RULE);
                        days = Integer.parseInt(switchInfo.getValue());
                        if (daysBetween > days) {
                            orderPayApplicationPriveteMethod.yingFengRepurchase(fundRepurchaseAccountEntity, days, orderInfoEntity);
                            log.info("OrderPayApplicationServiceImpl.automatedRepurchaseRepay fundName:{} 逾期天数:{} 大于配置天数:{}", fundEnum.getFundName(), daysBetween, days);
                        }
                        break;
                    case FU_MIN:
                        SwitchVO funMinSwitchInfo = switchUtils.getSwitchInfo(SwitchConstants.FUMIN_REPURCHASE_RULE);
                        days = Integer.parseInt(funMinSwitchInfo.getValue());
                        if (daysBetween > days) {
                            orderPayApplicationPriveteMethod.fuMinRepurchase(fundRepurchaseAccountEntity, days, orderInfoEntity, null);
                            log.info("OrderPayApplicationServiceImpl.automatedRepurchaseRepay fundName:{} 逾期天数:{} 大于配置天数:{}", fundEnum.getFundName(), daysBetween, days);
                        }
                        break;
                    /*case ZHONG_HENG_TONG_HUI:
                        SwitchVO tongHuiSwitchInfo = switchUtils.getSwitchInfo(SwitchConstants.TONGHUI_REPURCHASE_RULE);
                        days = Integer.parseInt(tongHuiSwitchInfo.getValue());
                        if (daysBetween > days) {
                            tonghuiRepurchase(fundRepurchaseAccountEntity, days, orderInfoEntity);
                            log.info("OrderPayApplicationServiceImpl.automatedRepurchaseRepay fundName:{} 逾期天数:{} 大于配置天数:{}", fundEnum.getFundName(), daysBetween, days);
                        }
                        break;*/
                    case LAN_HAI:
                        SwitchVO lanHaiSwitchInfo = switchUtils.getSwitchInfo(SwitchConstants.LAN_HAI_REPURCHASE_RULE);
                        days = Integer.parseInt(lanHaiSwitchInfo.getValue());
                        if (daysBetween > days) {
                            orderPayApplicationPriveteMethod.lanHaiRepurchase(fundRepurchaseAccountEntity, days, orderInfoEntity, null);
                            log.info("OrderPayApplicationServiceImpl.automatedRepurchaseRepay fundName:{} 逾期天数:{} 大于配置天数:{}", fundEnum.getFundName(), daysBetween, days);
                        }
                        break;
                    default:
                        log.error("暂不支持该资方");
                }


            }
        }

        return true;
    }



    //  TODO 赎回需要再确认
    private void tonghuiRepurchase(FundRepurchaseAccountEntity fundRepurchaseAccountEntity, Integer days, OrderInfoEntity orderInfoEntity) {
        List<OrderPayApplicationInfoEntity> orderPayApplicationInfoEntities = orderPayApplicationMapper.selectList(
                new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                        .eq(OrderPayApplicationInfoEntity::getOrderId, orderInfoEntity.getId())
                        .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)
                        .eq(OrderPayApplicationInfoEntity::getPayeeType, PayApplicationPayeeTypeEnum.FU_MIN)
                        .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(orderPayApplicationInfoEntities)){
            OrderPayApplicationInfoEntity orderPayApplyInfo = new OrderPayApplicationInfoEntity();
            orderPayApplyInfo.setOrderId(orderInfoEntity.getId())
                    .setFeeType(OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)
                    .setCurrentNode(PayApplicationNodeEnums.ACCOUNTANT_APPROVAL)
                    .setApplyUserId(1)
                    .setRemark("自动提交")
                    //付款单
                    .setApplyType(OrderFeeDetailStatusEnum.SPENDING);
            //收款主体-资方
            //付款方账户
            String payAccount = null;
            //付款方银行卡
            String payAccountNumber = null;
            //付款方开户行
            String payAccountName = null;
            try {
                String[] strings = splitString(dictUtils.getDictLabel(GlobalConstants.DictType.PAYEE_ACCOUNT.name(), 2));
                payAccount = strings[0];
                payAccountNumber = strings[2];
                payAccountName = strings[1];
            } catch (Exception e) {
                log.error("OrderPayApplicationServiceImpl.fuMinRepurchase.error 格式化付款方信息失败，原因：{}", e.getMessage());
            }
            orderPayApplyInfo.setPayeeType(PayApplicationPayeeTypeEnum.ZHONG_HENG);
            orderPayApplyInfo.setPaymentDetails("")
                    .setPayAccount(payAccount)
                    .setPayAccountName(payAccountName)
                    .setPayAccountNumber(payAccountNumber);
            //收款方账户
            orderPayApplyInfo.setPayeeAccount(fundRepurchaseAccountEntity.getAccountName());
            orderPayApplyInfo.setPayeeAccountName(fundRepurchaseAccountEntity.getAccountBank());
            orderPayApplyInfo.setPayeeAccountNumber(fundRepurchaseAccountEntity.getAccountNumber());
            FundRepayCalcDTO reqDTO = new FundRepayCalcDTO();
            reqDTO.setOrderId(orderInfoEntity.getId());
            FundRepaymentDeductEntity fundRepaymentDeductEntity = fundRepaymentDeductMapper.selectOne(
                    new LambdaQueryWrapper<FundRepaymentDeductEntity>()
                            .eq(FundRepaymentDeductEntity::getOrderId, orderInfoEntity.getId())
                            .eq(FundRepaymentDeductEntity::getRepayType, FundDeductRepayTypeEnums.EARLY_SETTLEMENT)
                            .eq(FundRepaymentDeductEntity::getRepayStatus, FundDeductRepayStatusEnums.REPAYMENT_SUCCESS)
                            .in(FundRepaymentDeductEntity::getBizType,Arrays.asList(
                                    FundDeductBizTypeEnums.WITHHOLDING_REQUESTS,
                                    FundDeductBizTypeEnums.PAYMENT,
                                    FundDeductBizTypeEnums.INITIATE_REPAYMENT_AFTER_PAYMENT,
                                    FundDeductBizTypeEnums.OFFLINE_REPAYMENT,
                                    FundDeductBizTypeEnums.TONG_LIAN_TONG_BUCKLE
                            ))
                            .orderByDesc(FundRepaymentDeductEntity::getCreateTime)
                            .last("limit 1")
            );
//        Result<FundRepayCalcVO> fundRepayCalcVOResult = approveFeign.repayCalc(reqDTO);
            log.info("OrderPayApplicationServiceImpl.tonghuiRepurchase fundRepaymentDeductEntity:{} orderId:{}", fundRepaymentDeductEntity,orderInfoEntity.getId());
            orderPayApplyInfo.setPayeeAmount(fundRepaymentDeductEntity.getDeductAmount());
            //赎回付款摘要
//        //todo  通汇赎回付款摘要
//        Result<YingFengInfoVO> yingFengInfoVOResult = approveFeign.getYingFengInfoByOrderId(orderInfoEntity.getId());
//        if (Result.isSuccess(yingFengInfoVOResult)) {
//            YingFengInfoVO infoVO = yingFengInfoVOResult.getData();
//            orderPayApplyInfo.setSummary(Objects.isNull(infoVO) ? "" : String.format("%s#龙环独资回购", infoVO.getLoanApplyNo()));
//        }

            orderPayApplicationMapper.insert(orderPayApplyInfo);

            orderPayApplicationPriveteMethod.saveNodeRecord(orderPayApplyInfo.getId(), PayApplicationNodeEnums.ACCOUNTANT_APPLY, orderPayApplyInfo.getCurrentNode(), PayApplicationAuditTypeEnum.YUNQI
                    , null, orderPayApplyInfo.getRemark(), PayApplicationEventEnums.APPROVE_PASS, 1, LocalDateTime.now());

        }
    }





    private static LocalDateTime getTimeOfEnd(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.MAX);
    }

    private static LocalDateTime getTimeOfStart(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.MIN);
    }

    @Override
    public void exportCarServiceFee(PayApplicationPageListDTO payApplicationDTO, LoginUser loginUser, HttpServletResponse response) {
        payApplicationDTO.setPageNum(1);
        payApplicationDTO.setPageSize(99999);
        MPJLambdaWrapper<OrderPayApplicationInfoEntity> wrapper = new MPJLambdaWrapper<OrderPayApplicationInfoEntity>()
                .selectAll(OrderPayApplicationInfoEntity.class)
                .selectAs(OrderInfoEntity::getVehicleNumber, CarServiceFeeExportVO::getVehicleNumber)
                .selectAs(OrderInfoEntity::getCustomerName, CarServiceFeeExportVO::getCustomerName)
                .selectAs(OrderInfoEntity::getTeamId, CarServiceFeeExportVO::getTeamId)
                .selectAs(OrderInfoEntity::getManagerId, CarServiceFeeExportVO::getManagerId)
                .selectAs(OrderInfoEntity::getStoreName, CarServiceFeeExportVO::getStoreName)
                .innerJoin(OrderInfoEntity.class, OrderInfoEntity::getId, OrderPayApplicationInfoEntity::getOrderId).eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.CAR_SERVICE_FEE)
                .eq(ObjectUtil.isNotEmpty(payApplicationDTO.getCurrentNode()), OrderPayApplicationInfoEntity::getCurrentNode, payApplicationDTO.getCurrentNode())
                .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                .like(ObjectUtil.isNotEmpty(payApplicationDTO.getOrderNumber()), OrderInfoEntity::getOrderNumber, payApplicationDTO.getOrderNumber())
                .like(ObjectUtil.isNotEmpty(payApplicationDTO.getPayeeAccount()), OrderPayApplicationInfoEntity::getPayeeAccount, payApplicationDTO.getPayeeAccount())
                .like(ObjectUtil.isNotEmpty(payApplicationDTO.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, payApplicationDTO.getVehicleNumber())
                .orderByDesc(OrderPayApplicationInfoEntity::getCreateTime);

        if (ObjUtil.isNotNull(payApplicationDTO.getPaymentTime())) {
            wrapper.between(OrderPayApplicationInfoEntity::getPaymentTime,
                    LocalDateTime.of(payApplicationDTO.getPaymentTime(), LocalTime.MIN), LocalDateTime.of(payApplicationDTO.getPaymentTime(), LocalTime.MAX));
        }
        if (payApplicationDTO.getCashierApproveTime() != null && !payApplicationDTO.getCashierApproveTime().isEmpty()) {
            String cashierApproveTime = payApplicationDTO.getCashierApproveTime();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate specifiedDate = LocalDate.parse(cashierApproveTime, formatter);
            wrapper.between(ObjectUtil.isNotEmpty(payApplicationDTO.getCashierApproveTime()), OrderPayApplicationInfoEntity::getCashierApproveTime, specifiedDate.atStartOfDay(), specifiedDate.atTime(23, 59, 59));
        }
        if (payApplicationDTO.getCashierApproveTimeStartDate() != null && payApplicationDTO.getCashierApproveTimeEndDate() == null) {
            wrapper.ge(OrderPayApplicationInfoEntity::getCashierApproveTime, getTimeOfStart(payApplicationDTO.getCashierApproveTimeStartDate()));
        }
        if (payApplicationDTO.getCashierApproveTimeStartDate() == null && payApplicationDTO.getCashierApproveTimeEndDate() != null) {
            wrapper.le(OrderPayApplicationInfoEntity::getCashierApproveTime, getTimeOfEnd(payApplicationDTO.getCashierApproveTimeEndDate()));
        }
        if (payApplicationDTO.getCashierApproveTimeStartDate() != null && payApplicationDTO.getCashierApproveTimeEndDate() != null) {
            wrapper.between(OrderPayApplicationInfoEntity::getCashierApproveTime,
                    getTimeOfStart(payApplicationDTO.getCashierApproveTimeStartDate()),
                    getTimeOfEnd(payApplicationDTO.getCashierApproveTimeEndDate()));
        }
        if (payApplicationDTO.getApproveTime() != null && !payApplicationDTO.getApproveTime().isEmpty()) {
            String approveTime = payApplicationDTO.getApproveTime();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            LocalDate approveTimeDate = LocalDate.parse(approveTime, formatter);
            wrapper.between(ObjectUtil.isNotEmpty(payApplicationDTO.getApproveTime()), OrderPayApplicationInfoEntity::getCreateTime, approveTimeDate.atStartOfDay(), approveTimeDate.atTime(23, 59, 59));
        }
        if (payApplicationDTO.getApproveStartDate() != null && payApplicationDTO.getApproveEndDate() == null) {
            wrapper.ge(OrderPayApplicationInfoEntity::getCreateTime, getTimeOfStart(payApplicationDTO.getApproveStartDate()));
        }
        if (payApplicationDTO.getApproveStartDate() == null && payApplicationDTO.getApproveEndDate() != null) {
            wrapper.le(OrderPayApplicationInfoEntity::getCreateTime, getTimeOfEnd(payApplicationDTO.getApproveEndDate()));
        }
        if (payApplicationDTO.getApproveStartDate() != null && payApplicationDTO.getApproveEndDate() != null) {
            wrapper.between(OrderPayApplicationInfoEntity::getCreateTime,
                    getTimeOfStart(payApplicationDTO.getApproveStartDate()),
                    getTimeOfEnd(payApplicationDTO.getApproveEndDate()));
        }
        //判断数据权限
        List<Integer> roleIds = loginUser.getRoleIds();
        log.info("OrderPayApplicationServiceImpl.exportCarServiceFee roleIds:{}", roleIds);
        if (!roleIds.contains(RoleEnum.ACCOUNTANT.getId()) && !roleIds.contains(RoleEnum.BIZ_SUPER_ADMIN.getId()) && !RoleEnum.SYS_ADMIN.hasRole(roleIds) && !RoleEnum.CASHIER.hasRole(roleIds)) {
            throw new BusinessException("暂无权限");
        }

        List<CarServiceFeeExportVO> exportData = orderPayApplicationMapper.selectJoinList(CarServiceFeeExportVO.class, wrapper);
        if (CollUtil.isEmpty(exportData)) {
            throw new BusinessException("暂无数据");
        }
        log.info("OrderPayApplicationServiceImpl.exportCarServiceFee exportData.size():{}", exportData.size());
        List<Integer> teamIds = exportData.stream().map(CarServiceFeeExportVO::getTeamId).filter(Objects::nonNull).toList();
        List<DeptInfoVO> branchNameVOS = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData();
        Map<Integer, String> branchNameMap = branchNameVOS.stream().filter(e -> ObjUtil.isNotNull(e.getName())).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
        exportData.forEach(record -> {
            if (ObjectUtil.isNotEmpty(record.getTeamId())) {
                record.setStoreName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(branchNameMap.get(record.getTeamId()), ""));
            }
            //客户
            if (Objects.equals(record.getPayeeType(), 1)) {
                record.setPayeeName(record.getCustomerName());
                List<BankAccountSignEntity> bankAccountSignEntityList = bankAccountSignMapper.selectList(new LambdaQueryWrapper<BankAccountSignEntity>().eq(BankAccountSignEntity::getBankCardNumber, record.getPayeeAccountNumber()).eq(BankAccountSignEntity::getDeleteFlag, 0).orderByDesc(BankAccountSignEntity::getCreateTime));
                if (CollUtil.isNotEmpty(bankAccountSignEntityList)) {
                    record.setOpenBankNumber(bankAccountSignEntityList.get(0).getOpenBankNumber());
                }
            } else if (Objects.equals(record.getPayeeType(), 2) && ObjectUtil.isNotEmpty(record.getManagerId())) {
                UserDetailInfoVO userDetailInfoVO = userFeign.searchUserDetailById(record.getManagerId()).getData();
                record.setPayeeName(ObjectUtil.isNotEmpty(userDetailInfoVO) ? userDetailInfoVO.getName() : "");
                List<ManageBankAccountSignEntity> manageBankAccountSignEntityList = manageBankAccountSignMapper.selectList(new LambdaQueryWrapper<ManageBankAccountSignEntity>().eq(ManageBankAccountSignEntity::getBankCardNumber, record.getPayeeAccountNumber()).eq(ManageBankAccountSignEntity::getDeleteFlag, 0).orderByDesc(ManageBankAccountSignEntity::getCreateTime));
                if (CollUtil.isNotEmpty(manageBankAccountSignEntityList)) {
                    record.setOpenBankNumber(manageBankAccountSignEntityList.get(0).getOpenBankNumber());
                }
            }
            if (ObjectUtil.isNotEmpty(record.getAccountantUserId())) {
                UserDetailInfoVO userDetailInfoVO = userFeign.searchUserDetailById(record.getAccountantUserId()).getData();
                record.setAccountantUserName(ObjectUtil.isNotEmpty(userDetailInfoVO) ? userDetailInfoVO.getName() : "");
            }
            if (ObjectUtil.isNotEmpty(record.getCashierUserId())) {
                UserDetailInfoVO userDetailInfoVO = userFeign.searchUserDetailById(record.getCashierUserId()).getData();
                record.setCashierUserName(ObjectUtil.isNotEmpty(userDetailInfoVO) ? userDetailInfoVO.getName() : "");
            }
            if (ObjectUtil.isEmpty(record.getPayeeAccount())) {
                record.setPayeeAccount("");
            }

            String payAccount = (record.getPayAccount() != null ? record.getPayAccount() : "")
                    + " "
                    + (record.getPayAccountName() != null ? record.getPayAccountName() : "")
                    + " "
                    + (record.getPayAccountNumber() != null ? record.getPayAccountNumber() : "");

//            record.setPayAccountNumber(dictUtils.getDictLabel(GlobalConstants.DictType.PAYEE_ACCOUNT.name(), record.getPayAccount()));
            record.setPayAccountNumber(payAccount);

        });
        try {
            response.addHeader("charset", "utf-8");
            String fileName = String.format("车务费-%s.xlsx", LocalDate.now());
            String encodeName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
            Optional<MediaType> mediaTypeOptional = MediaTypeFactory.getMediaType(encodeName);
            MediaType mediaType = mediaTypeOptional.orElse(MediaType.APPLICATION_OCTET_STREAM);
            log.info("LoanPerformanceServiceImpl.exportExcel mediaType = {}", JSONUtil.toJsonStr(mediaType));
            response.setContentType(String.valueOf(mediaType));
            log.info("LoanPerformanceServiceImpl.exportExcel response contentType = {}", response.getContentType());
            response.setHeader("Content-disposition", "attachment;filename=" + encodeName);
            String sheetName = "车务费";
            EasyExcel.write(response.getOutputStream())
                    // 动态头
                    .head(CarServiceFeeExportVO.class)
                    .registerWriteHandler(EasyExcelUtil.getHorizontalCellStyleStrategy())
                    .registerWriteHandler(new EasyExcelUtil.CustomCellWriteWidthConfig())
                    .sheet(sheetName)
                    // 表格数据
                    .doWrite(exportData);
        } catch (IOException e) {
            log.error("LoanPerformanceServiceImpl.exportExcel error", e);
        }
    }

    @Override
    public PayApplicationPageListVO payApplicationPageListTotal(PayApplicationPageListDTO payApplicationPageListDTO, LoginUser loginUser) {
        log.info("OrderPayApplicationServiceImpl payApplicationPageList start payApplicationPageListDTO:{}", JSONUtil.toJsonStr(payApplicationPageListDTO));
        PayApplicationNodeEnums currentNode = payApplicationPageListDTO.getCurrentNode();
        MPJLambdaWrapper<OrderPayApplicationInfoEntity> queryWrapper = new MPJLambdaWrapper<OrderPayApplicationInfoEntity>()
                .selectSum(OrderPayApplicationInfoEntity::getPayeeAmount, OrderPayApplyListVO::getPayeeAmount)
                .leftJoin(OrderInfoEntity.class, on -> on.eq(OrderInfoEntity::getId, OrderPayApplicationInfoEntity::getOrderId).eq(OrderPayApplicationInfoEntity::getOrderSource, 1))
                .leftJoin(OrderFeeInfoEntity.class,
                        on -> on.eq(OrderFeeInfoEntity::getOrderId, OrderPayApplicationInfoEntity::getOrderId).eq(OrderFeeInfoEntity::getGpsFeeStatus, 2).eq(OrderPayApplicationInfoEntity::getOrderSource, 1))
                .leftJoin(DigitalOutsourcingOrderEntity.class, on -> on.eq(DigitalOutsourcingOrderEntity::getOrderId, OrderPayApplicationInfoEntity::getOrderNumber).ne(OrderPayApplicationInfoEntity::getOrderSource, 1))
                .leftJoin(CaseInfoEntity.class, on -> on.eq(CaseInfoEntity::getDigitalOrderId, DigitalOutsourcingOrderEntity::getOrderNo).eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS).eq(CaseInfoEntity::getDeleteFlag, 0))
                .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                .in(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getFeeType()), OrderPayApplicationInfoEntity::getFeeType, payApplicationPageListDTO.getFeeType())
                .eq(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getPaymentDetails()), OrderPayApplicationInfoEntity::getPaymentDetails, payApplicationPageListDTO.getPaymentDetails())
                .like(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getPayeeAccount()), OrderPayApplicationInfoEntity::getPayeeAccount, payApplicationPageListDTO.getPayeeAccount())
                .and(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getVehicleNumber()), or -> or.like(OrderInfoEntity::getVehicleNumber, payApplicationPageListDTO.getVehicleNumber())
                        .or().like(DigitalOutsourcingOrderEntity::getVehicleNumber, payApplicationPageListDTO.getVehicleNumber())
                )
//                .like(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, payApplicationPageListDTO.getVehicleNumber())
                .and(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getOrderNumber()), or -> or.like(OrderInfoEntity::getOrderNumber, payApplicationPageListDTO.getOrderNumber())
                        .or().like(DigitalOutsourcingOrderEntity::getOrderId, payApplicationPageListDTO.getOrderNumber())
                )
                .and(StringUtils.hasText(payApplicationPageListDTO.getCustomerName()), or -> or.like(OrderInfoEntity::getCustomerName, payApplicationPageListDTO.getCustomerName())
                        .or().like(DigitalOutsourcingOrderEntity::getCustomerName, payApplicationPageListDTO.getCustomerName())
                )
//                .like(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getOrderNumber()), OrderInfoEntity::getOrderNumber, payApplicationPageListDTO.getOrderNumber())
                .eq(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getCurrentNode()), OrderPayApplicationInfoEntity::getCurrentNode, payApplicationPageListDTO.getCurrentNode());

        List<Integer> roleIds = loginUser.getRoleIds();
        String scopes = loginUser.getScopes();
        boolean hasRole = RoleEnum.ACCOUNTANT.hasRole(roleIds) || RoleEnum.CASHIER.hasRole(roleIds) || RoleEnum.SYS_ADMIN.hasRole(roleIds);
        if (currentNode == null || !hasRole || (scopes != null && !scopes.contains("data:all"))) {
            // 和订单状态权限控 orderPayApplicationMapper
            dataPermissionService.limitPayApplicationWithOrder(loginUser, queryWrapper);
        }
        if (ObjUtil.isNotNull(payApplicationPageListDTO.getPaymentTime())) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getPaymentTime,
                    LocalDateTime.of(payApplicationPageListDTO.getPaymentTime(), LocalTime.MIN), LocalDateTime.of(payApplicationPageListDTO.getPaymentTime(), LocalTime.MAX));
        }

        if (payApplicationPageListDTO.getCashierApproveTime() != null && !payApplicationPageListDTO.getCashierApproveTime().isEmpty()) {
            String cashierApproveTime = payApplicationPageListDTO.getCashierApproveTime();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            LocalDate specifiedDate = LocalDate.parse(cashierApproveTime, formatter);
            queryWrapper.between(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getCashierApproveTime()), OrderPayApplicationInfoEntity::getCashierApproveTime, specifiedDate.atStartOfDay(), specifiedDate.atTime(23, 59, 59));
        }

        if (payApplicationPageListDTO.getCashierApproveTimeStartDate() != null && payApplicationPageListDTO.getCashierApproveTimeEndDate() == null) {
            queryWrapper.ge(OrderPayApplicationInfoEntity::getCashierApproveTime, getTimeOfStart(payApplicationPageListDTO.getCashierApproveTimeStartDate()));
        }
        if (payApplicationPageListDTO.getCashierApproveTimeStartDate() == null && payApplicationPageListDTO.getCashierApproveTimeEndDate() != null) {
            queryWrapper.le(OrderPayApplicationInfoEntity::getCashierApproveTime, getTimeOfEnd(payApplicationPageListDTO.getCashierApproveTimeEndDate()));
        }
        if (payApplicationPageListDTO.getCashierApproveTimeStartDate() != null && payApplicationPageListDTO.getCashierApproveTimeEndDate() != null) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getCashierApproveTime,
                    getTimeOfStart(payApplicationPageListDTO.getCashierApproveTimeStartDate()),
                    getTimeOfEnd(payApplicationPageListDTO.getCashierApproveTimeEndDate()));
        }

        if (payApplicationPageListDTO.getApproveTime() != null && !payApplicationPageListDTO.getApproveTime().isEmpty()) {
            String approveTime = payApplicationPageListDTO.getApproveTime();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            LocalDate approveTimeDate = LocalDate.parse(approveTime, formatter);
            queryWrapper.between(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getApproveTime()), OrderPayApplicationInfoEntity::getCreateTime, approveTimeDate.atStartOfDay(), approveTimeDate.atTime(23, 59, 59));
        }
        if (payApplicationPageListDTO.getApproveStartDate() != null && payApplicationPageListDTO.getApproveEndDate() == null) {
            queryWrapper.ge(OrderPayApplicationInfoEntity::getCreateTime, getTimeOfStart(payApplicationPageListDTO.getApproveStartDate()));
        }
        if (payApplicationPageListDTO.getApproveStartDate() == null && payApplicationPageListDTO.getApproveEndDate() != null) {
            queryWrapper.le(OrderPayApplicationInfoEntity::getCreateTime, getTimeOfEnd(payApplicationPageListDTO.getApproveEndDate()));
        }
        if (payApplicationPageListDTO.getApproveStartDate() != null && payApplicationPageListDTO.getApproveEndDate() != null) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getCreateTime,
                    getTimeOfStart(payApplicationPageListDTO.getApproveStartDate()),
                    getTimeOfEnd(payApplicationPageListDTO.getApproveEndDate()));
        }
        if (payApplicationPageListDTO.getPaymentStartTime() != null && payApplicationPageListDTO.getPaymentEndTime() == null) {
            queryWrapper.ge(OrderPayApplicationInfoEntity::getPaymentTime, getTimeOfStart(payApplicationPageListDTO.getApproveStartDate()));
        }
        if (payApplicationPageListDTO.getPaymentStartTime() == null && payApplicationPageListDTO.getPaymentEndTime() != null) {
            queryWrapper.le(OrderPayApplicationInfoEntity::getPaymentTime, getTimeOfEnd(payApplicationPageListDTO.getApproveEndDate()));
        }
        if (payApplicationPageListDTO.getPaymentStartTime() != null && payApplicationPageListDTO.getPaymentEndTime() != null) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getPaymentTime,
                    getTimeOfStart(payApplicationPageListDTO.getPaymentStartTime()),
                    getTimeOfEnd(payApplicationPageListDTO.getPaymentEndTime()));
        }

        queryWrapper.eq(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getFeeDetails()), OrderPayApplicationInfoEntity::getFeeDetails, payApplicationPageListDTO.getFeeDetails());
//        payApplicationDTO.setPageNum(1);
//        payApplicationDTO.setPageSize(Integer.MAX_VALUE);
//        Page<OrderPayApplyListVO> page = payApplicationPageList(payApplicationDTO, loginUser);
//        BigDecimal totalAmount=BigDecimal.ZERO;
//        if (page.getTotal() > 0){
//            for (OrderPayApplyListVO record : page.getRecords()) {
//                totalAmount=totalAmount.add(record.getPayeeAmount() != null ? record.getPayeeAmount() : BigDecimal.ZERO);
//            }
//        }
        return new PayApplicationPageListVO().setTotalAmount(orderPayApplicationMapper.selectJoinOne(BigDecimal.class, queryWrapper));
    }

    @Override
    public List<OrderPayAccountVO> getCustomerAccountByOrderId(Integer orderId) {
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));
        List<BankAccountSignEntity> bankAccountSignEntityList = bankAccountSignMapper.selectList(new MPJLambdaWrapper<BankAccountSignEntity>()
                .eq(BankAccountSignEntity::getOrderId, orderId)
                .eq(BankAccountSignEntity::getSignState, 1)
                .eq(BankAccountSignEntity::getDeleteFlag, 0)
                .orderByDesc(BankAccountSignEntity::getCreateTime)
        );
        List<OrderPayAccountVO> voList = orderPayApplyConverter.entityList2VoList(bankAccountSignEntityList);
        voList.forEach(vo -> {
            vo.setCustomerName(orderInfo.getCustomerName());
            vo.setCustomerId(orderInfo.getCustomerId());
        });

        return voList;
    }

    // 方法：根据空格分割字符串并返回字符串数组
    public static String[] splitString(String input) {
        return input.split(" ");
    }


    /**
     * 修改收款人账户
     *
     * @param dto
     * @return boolean
     */
    @Override
    public boolean editPayersAccount(OrderPayApplicationDTO dto) {
        LambdaUpdateWrapper<OrderPayApplicationInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderPayApplicationInfoEntity::getId, dto.getId());
        OrderPayApplicationInfoEntity entity = new OrderPayApplicationInfoEntity();
        BeanUtil.copyProperties(dto, entity);
        if (orderPayApplicationMapper.update(entity, updateWrapper) > 0) {
            return true;
        }
        return false;
    }

    @Override
    public OrderPayApplicationOfflineVO getOfflinePayApplication(SearchOrderPayOfflineDTO dto) {

        LambdaQueryWrapper<OrderPayApplicationInfoEntity> lqw = new LambdaQueryWrapper<>();
        if (ObjUtil.isNotNull(dto.getSettleFlag()) && dto.getSettleFlag()) {
            lqw.eq(OrderPayApplicationInfoEntity::getOrderId, dto.getOrderId())
                    .eq(OrderPayApplicationInfoEntity::getPayeeType, PayApplicationPayeeTypeEnum.HUI_FENG)
                    .eq(OrderPayApplicationInfoEntity::getFeeType, OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT)
                    .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderPayApplicationInfoEntity::getCreateTime);
        } else {
            lqw.eq(OrderPayApplicationInfoEntity::getOrderId, dto.getOrderId())
                    .eq(OrderPayApplicationInfoEntity::getPayeeType, PayApplicationPayeeTypeEnum.HUI_FENG)
                    .eq(OrderPayApplicationInfoEntity::getRepaymentTerm, dto.getRepaymentTerm())
                    .in(OrderPayApplicationInfoEntity::getFeeType, offlineFeeEnumList)
                    .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(OrderPayApplicationInfoEntity::getCreateTime);
        }
        OrderPayApplicationInfoEntity orderPayApplicationInfoEntity = orderPayApplicationMapper.selectOne(lqw, false);
        if (orderPayApplicationInfoEntity == null) {
            return null;
        }
        String paymentVoucherList = orderPayApplicationInfoEntity.getPaymentVoucherList();
        OrderPayApplicationOfflineVO vo = orderPayApplyConverter.entity2OfflineVO(orderPayApplicationInfoEntity);
        List<String> resourceIdList = null;
        if (StrUtil.isNotBlank(paymentVoucherList)) {
            resourceIdList = JSONUtil.toList(paymentVoucherList, String.class);
        }
        if (CollUtil.isNotEmpty(resourceIdList)) {
            List<MenuVO.OrderFileInfo> orderFileInfoList = orderFileMapper.selectJoinList(MenuVO.OrderFileInfo.class, new MPJLambdaWrapper<OrderFileEntity>()
                    .select(OrderFileEntity::getId,
                            OrderFileEntity::getOrderId,
                            OrderFileEntity::getFileId,
                            OrderFileEntity::getFileName,
                            OrderFileEntity::getResourceId,
                            OrderFileEntity::getResourceName
                    )
                    .eq(OrderFileEntity::getOrderId, dto.getOrderId())
                    .in(OrderFileEntity::getResourceId, resourceIdList)
                    .eq(OrderFileEntity::getDeleteFlag, 0)
            );
            vo.setResourceFileList(orderFileInfoList);
        }
        return vo;
    }

    @Override
    public OrderPayApplicationVO getDetailById(Integer id) {
        MPJLambdaWrapper<OrderPayApplicationInfoEntity> queryWrapper = new MPJLambdaWrapper<OrderPayApplicationInfoEntity>()
                .selectAs(OrderInfoEntity::getId, OrderPayApplicationVO::getOrderId)
                .select(
                        "CASE WHEN t.order_source = 1 THEN t1.vehicle_number ELSE t3.vehicle_number END AS vehicleNumber",
                        "CASE WHEN t.order_source = 1 THEN t1.customer_name ELSE t3.customer_name END AS customerName",
                        "CASE WHEN t.order_source = 1 THEN t1.order_number ELSE t3.order_id END AS orderNumber",
                        "CASE WHEN t.order_source = 1 THEN t1.fund_name ELSE t3.fund_name END AS fundName"
                )
                .selectAs(OrderPayApplicationInfoEntity::getId, OrderPayApplicationVO::getId)
                .selectAs(OrderPayApplicationInfoEntity::getCurrentNode, OrderPayApplicationVO::getCurrentNode)
                .selectAs(OrderPayApplicationInfoEntity::getPaymentDetails, OrderPayApplicationVO::getPaymentDetails)
                .selectAs(OrderPayApplicationInfoEntity::getPayAccount, OrderPayApplicationVO::getPayAccount)
                .selectAs(OrderPayApplicationInfoEntity::getPayAccountName, OrderPayApplicationVO::getPayAccountName)
                .selectAs(OrderPayApplicationInfoEntity::getPayAccountNumber, OrderPayApplicationVO::getPayAccountNumber)
                .selectAs(OrderPayApplicationInfoEntity::getId, OrderPayApplicationVO::getId)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccount, OrderPayApplicationVO::getPayeeAccount)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccountName, OrderPayApplicationVO::getPayeeAccountName)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAmount, OrderPayApplicationVO::getPayeeAmount)
                .selectAs(OrderPayApplicationInfoEntity::getFeeType, OrderPayApplicationVO::getFeeType)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeType, OrderPayApplicationVO::getPayeeType)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccountNumber, OrderPayApplicationVO::getPayeeAccountNumber)
                .selectAs(OrderPayApplicationInfoEntity::getRemark, OrderPayApplicationVO::getRemark)
                .selectAs(OrderPayApplicationInfoEntity::getAccountantRemark, OrderPayApplicationVO::getAccountantRemark)
                .selectAs(OrderPayApplicationInfoEntity::getCashierRemark, OrderPayApplicationVO::getCashierRemark)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccountBranchName, OrderPayApplicationVO::getPayeeAccountBranchName)
                .selectAs(OrderPayApplicationInfoEntity::getPayeePhone, OrderPayApplicationVO::getPayeePhone)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeCardNumber, OrderPayApplicationVO::getPayeeCardNumber)
                .selectAs(OrderPayApplicationInfoEntity::getAccountantApproveTime, OrderPayApplicationVO::getAccountantApproveTime)
                .selectAs(OrderPayApplicationInfoEntity::getCashierApproveTime, OrderPayApplicationVO::getCashierApproveTime)
                .selectAs(OrderPayApplicationInfoEntity::getPaymentTime, OrderPayApplicationVO::getPaymentTime)
                .selectAs(OrderPayApplicationInfoEntity::getPenalty, OrderPayApplicationVO::getPenalty)
                .selectAs(OrderPayApplicationInfoEntity::getApplyType, OrderPayApplicationVO::getApplyType)
                .selectAs(OrderPayApplicationInfoEntity::getReductionAmount, OrderPayApplicationVO::getReductionAmount)
                .selectAs(OrderPayApplicationInfoEntity::getPaymentVoucherList, OrderPayApplicationVO::getPaymentVoucherList)
                .selectAs(OrderPayApplicationInfoEntity::getRepaymentTerm, OrderPayApplicationVO::getRepaymentTerm)
                .selectAs(OrderPayApplicationInfoEntity::getLoanSettlementMethod, OrderPayApplicationVO::getLoanSettlementMethod)
                .selectAs(OrderPayApplicationInfoEntity::getSummary, OrderPayApplicationVO::getSummary)
                .selectAs(OrderPayApplicationInfoEntity::getOrderSource, OrderPayApplicationVO::getOrderSource)
                .selectAs(OrderPayApplicationInfoEntity::getOrderApplicationSource, OrderPayApplicationVO::getOrderApplicationSource)
                .selectAs(OrderInfoEntity::getFundId, OrderPayApplicationVO::getFundId)
                .selectAs(OrderPayApplicationInfoEntity::getRepaymentShowAmount, OrderPayApplicationVO::getRepaymentShowAmount)
                .leftJoin(OrderInfoEntity.class, on -> on.eq(OrderInfoEntity::getId, OrderPayApplicationInfoEntity::getOrderId).eq(OrderPayApplicationInfoEntity::getOrderSource, 1))
                .leftJoin(OrderFeeInfoEntity.class,
                        on -> on.eq(OrderFeeInfoEntity::getOrderId, OrderPayApplicationInfoEntity::getOrderId).eq(OrderFeeInfoEntity::getGpsFeeStatus, 2).eq(OrderPayApplicationInfoEntity::getOrderSource, 1))
                .leftJoin(DigitalOutsourcingOrderEntity.class, on -> on.eq(DigitalOutsourcingOrderEntity::getOrderId, OrderPayApplicationInfoEntity::getOrderNumber).ne(OrderPayApplicationInfoEntity::getOrderSource, 1))
                .eq(OrderPayApplicationInfoEntity::getId, id);


        List<OrderPayApplicationVO> pageList = orderPayApplicationMapper.selectJoinList(OrderPayApplicationVO.class, queryWrapper);
        OrderPayApplicationVO vo = pageList.isEmpty() ? null : pageList.get(0);
        if (ObjUtil.isNull(vo)) {
            return null;
        }
        String paymentVoucherList = vo.getPaymentVoucherList();
        List<String> resourceIdList = null;
        if (StrUtil.isNotBlank(paymentVoucherList)) {
            resourceIdList = JSONUtil.toList(paymentVoucherList, String.class);
        }
        List<MenuVO.OrderFileInfo> orderFileInfoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(resourceIdList) && vo.getOrderId() != null) {
            orderFileInfoList = orderFileMapper.selectJoinList(MenuVO.OrderFileInfo.class, new MPJLambdaWrapper<OrderFileEntity>()
                    .select(OrderFileEntity::getId,
                            OrderFileEntity::getOrderId,
                            OrderFileEntity::getFileId,
                            OrderFileEntity::getFileName,
                            OrderFileEntity::getResourceId,
                            OrderFileEntity::getResourceName
                    )
                    .eq(OrderFileEntity::getOrderId, vo.getOrderId())
                    .in(OrderFileEntity::getResourceId, resourceIdList)
                    .eq(OrderFileEntity::getDeleteFlag, 0)
            );
        }
        if (CollUtil.isNotEmpty(resourceIdList)) {
            List<String> list1 = orderFileInfoList.stream().map(MenuVO.OrderFileInfo::getResourceId).toList();
            for (String s : resourceIdList) {
                if (!list1.contains(s)) {
                    orderFileInfoList.add(new MenuVO.OrderFileInfo().setResourceId(s));
                }
            }
        }
        vo.setResourceFileList(orderFileInfoList);
        return vo;
    }

    @Override
    public void retryDingTask() {
        List<OrderPayApplicationInfoEntity> retryOrderPayList = orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                .in(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.STORE_MANAGER_APPROVAL, PayApplicationNodeEnums.REGION_MANAGER_APPROVAL)
                .isNull(OrderPayApplicationInfoEntity::getProcessId)
                .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(retryOrderPayList)) {
            return;
        }
        retryOrderPayList.forEach(entity -> {
            try {
                String paymentVoucherList = entity.getPaymentVoucherList();
                List<String> list = new ArrayList<>();
                if (StrUtil.isNotBlank(paymentVoucherList)) {
                    list = JSONUtil.toList(paymentVoucherList, String.class);
                }
                orderPayApplicationPriveteMethod.initiateDingTalkApproval(list, entity, entity.getApplyUserId());

                orderPayApplicationPriveteMethod.saveNodeRecord(entity.getId(), PayApplicationNodeEnums.ACCOUNTANT_APPLY, entity.getCurrentNode(), PayApplicationAuditTypeEnum.YUNQI
                        , null, entity.getRemark(), PayApplicationEventEnums.APPROVE_PASS, entity.getCreateBy(), LocalDateTime.now());

            } catch (Exception e) {
                log.error("OrderPayApplicationServiceImpl.retryDingTask id:{} error:{}", entity.getId(), e.getMessage(), e);
            }

        });

    }


    /**
     * 赎回费导出
     *
     * @param payApplicationDTO
     * @return
     */
    @Override
    public List<FileVO> exportRedeemFee(PayApplicationPageListDTO payApplicationDTO, LoginUser loginUser) {
//        if (ObjUtil.isNull(payApplicationDTO.getApproveStartDate()) && ObjUtil.isNull(payApplicationDTO.getApproveEndDate())) {
//            payApplicationDTO.setApproveStartDate(LocalDate.now().minusMonths(3));
//            payApplicationDTO.setApproveEndDate(LocalDate.now());
//        }
        payApplicationDTO.setPageNum(1);
        payApplicationDTO.setPageSize(-1);
        try {
            String[] head = new String[]{"单据编号", "车牌号", "进件编号", "所属资方", "放款金额", "客户名称", "产品名称", "产品期数", "地区(门店)",
                    "所属大区", "费用类型", "应收金额", "交易金额", "备注", "订单状态", "申请类型", "收款方", "收款方账号", "收款方开户行", "收款方预留手机号", "收款方身份证号", "付款方"
                    , "付款方账户", "付款方开户行", "出纳审核状态", "支付时间", "申请时间", "申请备注", "会计审核人", "会计审核时间", "会计审核备注", "出纳审核人", "出纳审核时间", "出纳审核备注"
                    , "提交人", "提交人角色"};
            Page<OrderPayApplyListVO> orderPayApplyListVOPage = this.payApplicationPageList(payApplicationDTO, loginUser);
            List<OrderPayApplyListVO> records = orderPayApplyListVOPage.getRecords();
            if (!records.isEmpty()) {
                List<HashMap<String, Object>> dataList = new ArrayList<>();

                // 异步获取productMap
                CompletableFuture<Map<Integer, ProductInfoEntity>> productMapFuture = CompletableFuture.supplyAsync(() -> {
                    Map<Integer, ProductInfoEntity> productMap = new HashMap<>();
                    List<Integer> productIdList = records.stream()
                            .filter(item -> ObjUtil.isNotNull(item.getProductId()))
                            .map(OrderPayApplyListVO::getProductId)
                            .distinct()
                            .collect(Collectors.toList());
                    if (!productIdList.isEmpty()) {
                        List<ProductInfoEntity> productInfoEntities = productInfoMapper.selectBatchIds(productIdList);
                        productMap = productInfoEntities.stream()
                                .collect(Collectors.toMap(ProductInfoEntity::getId, product -> product));
                    }
                    return productMap;
                });

                // 异步获取orderInfoMap
                CompletableFuture<Map<Integer, OrderInfoEntity>> orderInfoMapFuture = CompletableFuture.supplyAsync(() -> {
                    Map<Integer, OrderInfoEntity> orderInfoMap = new HashMap<>();
                    List<Integer> orderInfoList = records.stream()
                            .filter(item -> ObjUtil.isNotNull(item.getOrderId()))
                            .map(OrderPayApplyListVO::getOrderId)
                            .distinct()
                            .collect(Collectors.toList());
                    if (!orderInfoList.isEmpty()) {
                        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectBatchIds(orderInfoList);
                        orderInfoMap = orderInfoEntityList.stream()
                                .collect(Collectors.toMap(OrderInfoEntity::getId, orderInfo -> orderInfo));
                    }
                    return orderInfoMap;
                });

                // 异步获取userInfoMap
                CompletableFuture<Map<Integer, UserInfoVO>> userInfoMapFuture = CompletableFuture.supplyAsync(() -> {
                    Map<Integer, UserInfoVO> userInfoMap = new HashMap<>();
                    List<Integer> userIds = Stream.concat(
                                    records.stream().filter(orderPayApplyListVO -> ObjUtil.isNotEmpty(orderPayApplyListVO.getAccountantChecker()))
                                            .map(OrderPayApplyListVO::getAccountantChecker),
                                    records.stream().filter(orderPayApplyListVO -> ObjUtil.isNotEmpty(orderPayApplyListVO.getCashierChecker()))
                                            .map(OrderPayApplyListVO::getCashierChecker)
                            )
                            .distinct()
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(userIds)) {
                        // 获取用户列表
                        Result<List<UserInfoVO>> userInfoResult = userFeign.searchUserNameByUserIds(userIds);
                        if (Result.isSuccess(userInfoResult) && ObjUtil.isNotEmpty(userInfoResult.getData())) {
                            userInfoMap = userInfoResult.getData().stream()
                                    .collect(Collectors.toMap(UserInfoVO::getUserId, userInfoVO -> userInfoVO));
                        }
                    }
                    return userInfoMap;
                });

                // 异步获取orderAmountMap
                CompletableFuture<Map<Integer, OrderAmountEntity>> orderAmountMapFuture = orderInfoMapFuture.thenApply(orderInfoMap -> {
                    Map<Integer, OrderAmountEntity> orderAmountMap = new HashMap<>();
                    List<Integer> orderInfoList = new ArrayList<>(orderInfoMap.keySet());
                    if (!orderInfoList.isEmpty()) {
                        List<OrderAmountEntity> orderAmountEntityList = orderAmountMapper.selectList(new LambdaQueryWrapper<OrderAmountEntity>()
                                .in(OrderAmountEntity::getOrderId, orderInfoList)
                                .eq(OrderAmountEntity::getDeleteFlag, 0)
                        );
                        if (CollUtil.isNotEmpty(orderAmountEntityList)) {
                            orderAmountMap = orderAmountEntityList.stream()
                                    .collect(Collectors.toMap(OrderAmountEntity::getOrderId, orderAmountEntity -> orderAmountEntity));
                        }
                    }
                    return orderAmountMap;
                });

                // 等待所有异步任务完成
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                        productMapFuture,
                        orderInfoMapFuture,
                        userInfoMapFuture,
                        orderAmountMapFuture
                );

                // 使用 CompletableFuture.supplyAsync 来返回 dataList
                CompletableFuture<List<HashMap<String, Object>>> dataListFuture = allFutures.thenApply(v -> {
                    try {
                        Map<Integer, ProductInfoEntity> productMap = productMapFuture.join();
                        Map<Integer, OrderInfoEntity> orderInfoMap = orderInfoMapFuture.join();
                        Map<Integer, UserInfoVO> userInfoMap = userInfoMapFuture.join();
                        Map<Integer, OrderAmountEntity> orderAmountMap = orderAmountMapFuture.join();

                        for (OrderPayApplyListVO orderPayApplyListVO : records) {
                            OrderInfoEntity orderInfo = orderInfoMap.get(orderPayApplyListVO.getOrderId());
                            if (ObjUtil.isNull(orderInfo)) {
                                log.warn("OrderPayApplicationServiceImpl.exportRedeemFee OrderInfoEntity is null, orderId：{}", orderPayApplyListVO.getOrderId());
                                continue;
                            }
                            ProductInfoEntity productInfoEntity = productMap.get(orderInfo.getProductId());
                            if (ObjUtil.isNull(productInfoEntity)) {
                                log.warn("OrderPayApplicationServiceImpl.exportRedeemFee productInfoEntity is null, orderId：{}", orderPayApplyListVO.getOrderId());
                            }
                            OrderAmountEntity orderAmountEntity = orderAmountMap.get(orderInfo.getId());
                            HashMap<String, Object> dataMap = new HashMap<>();
                            dataMap.put("单据编号", orderPayApplyListVO.getPaymentDetails());
                            dataMap.put("车牌号", orderPayApplyListVO.getVehicleNumber());
                            dataMap.put("进件编号", orderPayApplyListVO.getOrderNumber());
                            dataMap.put("所属资方", orderPayApplyListVO.getFundName());
                            dataMap.put("放款金额", orderAmountEntity.getCustomerConfirmAmount());
                            dataMap.put("客户名称", orderPayApplyListVO.getCustomerName());

                            if (ObjUtil.isNotNull(productInfoEntity)) {
                                dataMap.put("产品名称", productInfoEntity.getName());
                                dataMap.put("产品期数", productInfoEntity.getTerm());
                            }
                            dataMap.put("地区(门店)", orderInfo.getStoreName());
                            dataMap.put("所属大区", orderPayApplyListVO.getRegionName());
                            dataMap.put("费用类型", orderPayApplyListVO.getFeeType() == 3 ? "赎回" : "");
                            dataMap.put("应收金额", orderPayApplyListVO.getPayeeAmount());
                            dataMap.put("交易金额", orderPayApplyListVO.getPayeeAmount());
                            dataMap.put("备注", orderPayApplyListVO.getRemark());
                            //TODO   订单状态修改
                            dataMap.put("订单状态", orderPayApplyListVO.getCurrentNode().getDescription());
                            dataMap.put("申请类型", orderPayApplyListVO.getApplyType() == 1 ? "付款单" : "收款单");
                            dataMap.put("收款方", orderPayApplyListVO.getPayeeAccount());
                            dataMap.put("收款方账号", orderPayApplyListVO.getPayeeAccountNumber());
                            dataMap.put("收款方开户行", orderPayApplyListVO.getPayeeAccountName());
                            dataMap.put("收款方预留手机号", orderPayApplyListVO.getPayeePhone());
                            dataMap.put("收款方身份证号", orderPayApplyListVO.getPayeeCardNumber());
                            dataMap.put("付款方", orderPayApplyListVO.getPayAccount());
                            dataMap.put("付款方账户", orderPayApplyListVO.getPayAccountNumber());
                            dataMap.put("付款方开户行", orderPayApplyListVO.getPayAccountName());
                            String approveStatus = "";
                            String approveTime = "";
                            OrderPayApplyNodeRecordEntity orderPayApplyNodeRecordEntities = orderPayApplyNodeRecordMapper.selectOne(
                                    Wrappers.lambdaQuery(OrderPayApplyNodeRecordEntity.class)
                                            .eq(OrderPayApplyNodeRecordEntity::getApplyInfoId, orderPayApplyListVO.getId())
                                            .eq(OrderPayApplyNodeRecordEntity::getDeleteFlag, 0)
                                            .eq(OrderPayApplyNodeRecordEntity::getCurrentNode, PayApplicationNodeEnums.ACCOUNTANT_APPLY.getCode())
                                            .orderByDesc(OrderPayApplyNodeRecordEntity::getCreateTime)
                                            .last("limit 1")
                            );
                            if (ObjUtil.isNotNull(orderPayApplyNodeRecordEntities)) {
                                OrderPayApplyNodeRecordEntity orderPayApplyNodeRecordEntity = orderPayApplyNodeRecordMapper.selectOne(Wrappers.lambdaQuery(OrderPayApplyNodeRecordEntity.class)
                                        .eq(OrderPayApplyNodeRecordEntity::getApplyInfoId, orderPayApplyListVO.getId())
                                        .eq(OrderPayApplyNodeRecordEntity::getDeleteFlag, 0)
                                        .eq(OrderPayApplyNodeRecordEntity::getCurrentNode, PayApplicationNodeEnums.CASHIER_APPROVAL.getCode())
                                        .orderByDesc(OrderPayApplyNodeRecordEntity::getCreateTime)
                                        .ge(OrderPayApplyNodeRecordEntity::getCreateTime, orderPayApplyNodeRecordEntities.getCreateTime())
                                        .last("limit 1")
                                );
                                if (ObjUtil.isNotNull(orderPayApplyNodeRecordEntity)) {
                                    approveStatus = orderPayApplyNodeRecordEntity.getEvent().getDescription();
                                    approveTime = orderPayApplyNodeRecordEntity.getCreateTime() != null ? orderPayApplyNodeRecordEntity.getCreateTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "";
                                }
                            }
                            dataMap.put("出纳审核状态", approveStatus);
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            String payTime = ObjUtil.isNotEmpty(orderPayApplyListVO.getPaymentTime()) ? formatter.format(orderPayApplyListVO.getPaymentTime()) : "-";
                            dataMap.put("支付时间", payTime);
                            dataMap.put("申请时间", orderPayApplyListVO.getSubmitTime() != null ? orderPayApplyListVO.getSubmitTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                            dataMap.put("申请备注", orderPayApplyListVO.getRemark());
                            if (orderPayApplyListVO.getAccountantChecker() != null) {
                                UserInfoVO userInfoVO = userInfoMap.get(orderPayApplyListVO.getAccountantChecker());
                                if (ObjUtil.isNotNull(userInfoVO)) {
                                    dataMap.put("会计审核人", userInfoVO.getName());
                                }
                            }
                            dataMap.put("会计审核时间", orderPayApplyListVO.getAccountantApproveTime() != null ? orderPayApplyListVO.getAccountantApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
                            dataMap.put("会计审核备注", orderPayApplyListVO.getAccountantRemark());
                            if (orderPayApplyListVO.getCashierChecker() != null) {
                                UserInfoVO userInfoVO = userInfoMap.get(orderPayApplyListVO.getCashierChecker());
                                if (ObjUtil.isNotNull(userInfoVO)) {
                                    dataMap.put("出纳审核人", userInfoVO.getName());
                                }
                            }
                            dataMap.put("出纳审核时间", approveTime);
                            dataMap.put("出纳审核备注", orderPayApplyListVO.getCashierRemark());
                            dataMap.put("提交人", orderPayApplyListVO.getUserName());
                            dataMap.put("提交人角色", orderPayApplyListVO.getUserId() == 1 ? "系统自动提交" : "线上运营人员");
                            dataList.add(dataMap);
                        }
                    } catch (Exception e) {
                        log.error("OrderPayApplicationServiceImpl.exportRedeemFee error, {}", e.getMessage(), e);
                        throw new BusinessException("导出失败，请重试");
                    }
                    return dataList;
                });

                // 获取 dataList 并导出 Excel
                return ExportExcelUtils.dealAndExportExcel(head, dataListFuture.join());
            } else {
                throw new Exception("无可导出数据");
            }
        } catch (Exception e) {
            log.error("赎回数据导出,异常{}", e.getMessage(), e);
            throw new BusinessException("赎回数据导出,异常" + e.getMessage());
        }
    }

    @Override
    public void payApplicationPageListExport(PayApplicationPageListDTO payApplicationPageListDTO, LoginUser loginUser, HttpServletResponse response) {
        orderDetailsInfoService.payApplicationPageListExport(payApplicationPageListDTO, loginUser, response);

    }

    /**
     * 长银代偿生产申请记录
     *
     * @param fundIndemnityDTOList
     * @return
     */
    @Override
    public Boolean changyinPreIndemnityBatchApply(List<FundIndemnityDTO> fundIndemnityDTOList) {
        for (FundIndemnityDTO dto : fundIndemnityDTOList) {
            OrderPayApplicationInfoEntity orderPayApplyInfo = new OrderPayApplicationInfoEntity();
            orderPayApplyInfo.setOrderId(dto.getOrderId())
                    .setFeeType(OrderFeeDetailExpandTypeEnum.REDEMPTION_PAYMENT)
                    .setCurrentNode(PayApplicationNodeEnums.ACCOUNTANT_APPROVAL)
                    .setApplyUserId(1)
                    .setRemark("自动提交")
                    //付款单
                    .setApplyType(OrderFeeDetailStatusEnum.SPENDING);
            //收款主体-资方
            //付款方账户
            String payAccount = null;
            //付款方银行卡
            String payAccountNumber = null;
            //付款方开户行
            String payAccountName = null;
            try {
                String[] strings = splitString(dictUtils.getDictLabel(GlobalConstants.DictType.PAYEE_ACCOUNT.name(), 2));
                payAccount = strings[0];
                payAccountNumber = strings[2];
                payAccountName = strings[1];
            } catch (Exception e) {
                log.error("OrderPayApplicationServiceImpl.fuMinRepurchase.error 格式化付款方信息失败，原因：{}", e.getMessage());
            }

            orderPayApplyInfo.setPaymentDetails("")
                    .setPayAccount(payAccount)
                    .setPayAccountName(payAccountName)
                    .setPayAccountNumber(payAccountNumber);


            //收款方账户
            FundRepurchaseAccountEntity fundRepurchaseAccountEntity = new FundRepurchaseAccountEntity();
            List<FundRepurchaseAccountEntity> fundRepurchaseAccountEntityList = fundRepurchaseAccountMapper.selectList(new LambdaQueryWrapper<FundRepurchaseAccountEntity>()
                    .eq(FundRepurchaseAccountEntity::getFundId, dto.getFundId())
                    .eq(FundRepurchaseAccountEntity::getDeleteFlag, 0)
                    .orderByDesc(FundRepurchaseAccountEntity::getId));
            if (CollUtil.isNotEmpty(fundRepurchaseAccountEntityList)) {
                fundRepurchaseAccountEntity = fundRepurchaseAccountEntityList.get(0);
            }
            orderPayApplyInfo.setPayeeType(PayApplicationPayeeTypeEnum.CHANGYIN_MIN);
            orderPayApplyInfo.setPayeeAccount(fundRepurchaseAccountEntity.getAccountName());
            orderPayApplyInfo.setPayeeAccountName(fundRepurchaseAccountEntity.getAccountBank());
            orderPayApplyInfo.setPayeeAccountNumber(fundRepurchaseAccountEntity.getAccountNumber());
            orderPayApplyInfo.setPayeeAmount(dto.getTotalAmt());

            log.info("OrderPayApplicationServiceImpl.changyinPreIndemnityBatchApply start insert apply orderId: {}", orderPayApplyInfo.getOrderId());
            orderPayApplicationMapper.insert(orderPayApplyInfo);
        }
        return true;
    }

    @Override
    public Boolean fuMinRepurchaseCallback(Integer orderId, BigDecimal actuallyTotalAmount) {
        try {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);

            List<OrderPayApplicationInfoEntity> orderPayApplicationInfoEntityList = orderPayApplicationMapper.selectList(
                    new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                            .eq(OrderPayApplicationInfoEntity::getOrderId, orderInfoEntity.getId())
            );
            if (CollUtil.isNotEmpty(orderPayApplicationInfoEntityList) && orderPayApplicationInfoEntityList.stream()
                    .anyMatch(entity -> Objects.equals(entity.getFeeType(),OrderFeeDetailExpandTypeEnum.TRANSFER_MONTHLY_REPAYMENT) || Objects.equals(entity.getFeeType(),OrderFeeDetailExpandTypeEnum.TRANSFER_SETTLE_REPAYMENT))){
                return true;
            }
            FundRepurchaseAccountEntity fundRepurchaseAccountEntity = new FundRepurchaseAccountEntity();
            List<FundRepurchaseAccountEntity> fundRepurchaseAccountEntityList = fundRepurchaseAccountMapper.selectList(new LambdaQueryWrapper<FundRepurchaseAccountEntity>()
                    .eq(FundRepurchaseAccountEntity::getFundId, orderInfoEntity.getFundId())
                    .eq(FundRepurchaseAccountEntity::getDeleteFlag, 0)
                    .orderByDesc(FundRepurchaseAccountEntity::getId));
            if (CollUtil.isNotEmpty(fundRepurchaseAccountEntityList)) {
                fundRepurchaseAccountEntity = fundRepurchaseAccountEntityList.get(0);
            }


            log.info("OrderPayApplicationServiceImpl.fuMinRepurchaseCallback start insert apply orderId: {} actuallyTotalAmount:{}", orderId, actuallyTotalAmount);
            orderPayApplicationPriveteMethod.fuMinRepurchase(fundRepurchaseAccountEntity, 0, orderInfoEntity, actuallyTotalAmount);
        } catch (Exception e) {
            log.error("OrderPayApplicationServiceImpl.fuMinRepurchaseCallback e:{}", e.getMessage());
        }
        return true;
    }

    @Override
    public Boolean thRefundApply(ThRefundApplyDTO dto, LoginUser loginUser) {
        return orderDetailsInfoService.thRefundApply(dto, loginUser);
    }

    @Override
    public Boolean lanHaiRepurchaseRepay(Integer orderId, BigDecimal actuallyTotalAmount) {
        try {
            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
            FundRepurchaseAccountEntity fundRepurchaseAccountEntity = new FundRepurchaseAccountEntity();
            List<FundRepurchaseAccountEntity> fundRepurchaseAccountEntityList = fundRepurchaseAccountMapper.selectList(new LambdaQueryWrapper<FundRepurchaseAccountEntity>()
                    .eq(FundRepurchaseAccountEntity::getFundId, orderInfoEntity.getFundId())
                    .eq(FundRepurchaseAccountEntity::getDeleteFlag, 0)
                    .orderByDesc(FundRepurchaseAccountEntity::getId));
            if (CollUtil.isNotEmpty(fundRepurchaseAccountEntityList)) {
                fundRepurchaseAccountEntity = fundRepurchaseAccountEntityList.get(0);
            }
            orderPayApplicationPriveteMethod.lanHaiRepurchase(fundRepurchaseAccountEntity, 0, orderInfoEntity, actuallyTotalAmount);
        } catch (Exception e) {
            log.error("OrderPayApplicationServiceImpl.fuMinRepurchaseCallback e:{}", e.getMessage());
        }
        return true;
    }

    @Override
    public void directSettle(String orderNumber) {
        orderPayApplicationPriveteMethod.directSettle(orderNumber);
    }

    @Override
    public SpecialRefundApplyVO getSpecialRefundApply(SpecialRefundApplyDTO dto) {
        return orderPayApplicationPriveteMethod.getSpecialRefundApply(dto);
    }

    @Override
    public void expenseApplicationExport(ExpenseApplicationExportDTO dto, LoginUser loginUser, HttpServletResponse response) {
        orderPayApplicationPriveteMethod.expenseApplicationExport(dto,loginUser, response);
    }

    @Override
    public Boolean autoRecognize(MultipartFile file) {
        return orderPayApplicationPriveteMethod.autoRecognize(file);
    }

    @Override
    public void autoRecognizeExport(HttpServletResponse response) {
        orderPayApplicationPriveteMethod.autoRecognizeExport(response);
    }


}
