package com.longhuan.order.statemachine.action;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.enums.FundPaymentStatusEnum;
import com.longhuan.common.core.enums.OrderPaymentStateEnum;
import com.longhuan.order.mapper.FinalFundInfoMapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.mapper.OrderNodeRecordMapper;
import com.longhuan.order.pojo.entity.FinalFundInfoEntity;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.service.impl.OrderSendMessageImpl;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 状态转移动作，在转移进行的时候触发的动作
 */
@Slf4j
@Component
public class PaymentContractApproveAction extends OrderNodeAction implements Action<States, Events> {

    private final OrderSendMessageImpl orderSendMessage;

    private final FinalFundInfoMapper finalFundInfoMapper;


    public PaymentContractApproveAction(OrderInfoMapper orderInfoMapper, OrderNodeRecordMapper orderNodeRecordMapper, OrderSendMessageImpl orderSendMessage, FinalFundInfoMapper finalFundInfoMapper) {
        super(orderInfoMapper, orderNodeRecordMapper);
        this.orderSendMessage = orderSendMessage;
        this.finalFundInfoMapper = finalFundInfoMapper;
    }

    @Override
    public void execute(StateContext<States, Events> context) {


        StateMachine<States, Events> stateMachine = context.getStateMachine();

        MessageHeaders messageHeaders = context.getMessage().getHeaders();

        Integer userId = messageHeaders.get(StateConstants.USER_ID, Integer.class);
        Integer orderId = messageHeaders.get(StateConstants.ORDER_ID, Integer.class);


        State<States, Events> source = context.getSource();
        State<States, Events> target = context.getTarget();

        Events event = context.getEvent();
        Integer currentNode = source.getId().getNode();
        Integer nextNode = target.getId().getNode();

        log.info("orderId: {} ,userId: {} ,event: {} ,currentNode: {} ,nextNode: {}", orderId, userId, event, currentNode, nextNode);


        // 0. 重置付款状态为未发起
        if(Events.AGREES.equals(event)){

            //蓝海合同审批通过置为null 因为之前失败的回调可能会被更新
            int updateLoanBillNo = finalFundInfoMapper.update(new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .set(FinalFundInfoEntity::getLoanBillNo, null)
                    .eq(FinalFundInfoEntity::getFundId, FundEnum.LAN_HAI.getValue())
                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                    .eq(FinalFundInfoEntity::getPaymentStatus, FundPaymentStatusEnum.FAIL)
            );

            FinalFundInfoEntity updateEntity = new FinalFundInfoEntity();
            updateEntity.setPaymentStatus(FundPaymentStatusEnum.NONE);
            int update = finalFundInfoMapper.update(updateEntity, new LambdaQueryWrapper<FinalFundInfoEntity>()
                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                    .eq(FinalFundInfoEntity::getPaymentStatus, FundPaymentStatusEnum.FAIL)
            );

            log.info("order {} reset payment  updateLoanBillNo: {}", orderId, updateLoanBillNo);
            if (update > 0) {
                log.info("order {} reset payment status: {}", orderId, update);
            }

            //重置放款失败的订单放款状态
            OrderInfoEntity orderUpdateEntity = new OrderInfoEntity();
            orderUpdateEntity.setPaymentState(OrderPaymentStateEnum.WAIT);
            int orderUpdate = orderInfoMapper.update(orderUpdateEntity, new LambdaQueryWrapper<OrderInfoEntity>()
                    .eq(OrderInfoEntity::getId, orderId)
                    .eq(OrderInfoEntity::getDeleteFlag, 0)
                    .eq(OrderInfoEntity::getPaymentState, OrderPaymentStateEnum.FAIL)
            );
            if (orderUpdate > 0) {
                log.info("order {} reset order payment status: {}", orderId, orderUpdateEntity);
            }
        }



        // 1. 修改订单表状态
        if (Boolean.TRUE.equals(target.getId().getValid())) {
            saveOrderNode(orderId, nextNode);
        }

        // 2. 保存状态变更记录
        saveOrderNodeRecord(event, orderId, currentNode, nextNode, messageHeaders);

        // 3. 发送消息
        Integer currentEventCode = context.getEvent().getCode();
        if (Objects.equals(currentNode, States.RISK_FINAL_APPROVE.getNode())
                || Objects.equals(currentNode, States.PAYMENT_CONTRACT_APPROVAL.getNode())
                || (Objects.equals(currentNode, States.FUNDS_PAYMENT_APPROVAL.getNode()) && "1".equals(String.valueOf(currentEventCode)))
                || Objects.equals(currentNode, States.FUNDS_PAYMENT_FAIL.getNode())
                || Objects.equals(currentNode, States.PAYMENT_SUCCESS.getNode())) {
            orderSendMessage.sendMessage(orderId, currentNode, currentEventCode);
        }


    }


}