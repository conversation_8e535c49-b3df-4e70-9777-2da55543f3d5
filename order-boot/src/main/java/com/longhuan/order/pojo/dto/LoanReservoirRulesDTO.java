package com.longhuan.order.pojo.dto;


import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.longhuan.common.core.base.BaseEntity;
import com.longhuan.common.core.base.BasePageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * 放款蓄水池规则
 *
 */
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class LoanReservoirRulesDTO   extends BasePageQuery {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    @TableId
    private Integer id;

    @ApiModelProperty(value = "规则编码")
    private String ruleNumber;

    @ApiModelProperty(value = "蓄水状态：1-拦截，2-放水，3-驳回")
    private Integer reservoirStatus;

    /**
     * 资方id
     */
    @ApiModelProperty(value = "资方ID")
    private String fundIds;
    /**
     * 大区ID
     */
    @ApiModelProperty(value = "大区ID")
    private String regionIds;
    /**
     * 大区名称
     */
    @ApiModelProperty(value = "大区名称")
    private String regionName;
    /**
     * 门店ID
     */
    @ApiModelProperty(value = "门店ID")
    private String deptIds;
    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String deptName;
    /**
     * 数据来源(0：门店，1：电销)
     */
    @ApiModelProperty(value = "数据来源(0：门店，1：电销)")
    private Integer sourceType;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime beginDate;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime endDate;
    /**
     * 是否启用（0启用,1关闭）
     */
    @ApiModelProperty(value = "是否启用（0启用,1关闭）")
    private Integer enable;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 操作开始时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime operateStartDate;
    /**
     * 操作结束时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime operateEndDate;

    /*
    * 所属区域 0 全选
     */
    private Integer areaId;


}