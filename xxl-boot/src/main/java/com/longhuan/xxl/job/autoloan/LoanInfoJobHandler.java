package com.longhuan.xxl.job.autoloan;

import com.longhuan.xxl.feign.AutoLoanRepaymentFeign;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 资管列表当前期数刷新
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LoanInfoJobHandler {
    private final AutoLoanRepaymentFeign autoLoanRepaymentFeign;

    @XxlJob("LoanInfoJobHandler")
    public ReturnT<String> runJobHandler() {
        log.info("LoanInfoJobHandler starter");

        autoLoanRepaymentFeign.changeLoanCurrentTerm();
        log.info("LoanInfoJobHandler end");

        return ReturnT.SUCCESS;

    }

}
