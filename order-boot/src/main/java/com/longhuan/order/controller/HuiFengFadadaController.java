package com.longhuan.order.controller;

import com.fasc.open.api.bean.base.BaseRes;
import com.fasc.open.api.v5_1.res.common.ECorpAuthUrlRes;
import com.fasc.open.api.v5_1.res.seal.GetSealFreeSignUrlRes;
import com.fasc.open.api.v5_1.res.signtask.CreateSignTaskRes;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.ContractTaskDTO;
import com.longhuan.order.pojo.vo.FuMinContractVO;
import com.longhuan.order.service.HuiFengFadadaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/hf/fdd")
@Api("汇丰主体法大大接口")
public class HuiFengFadadaController {

    private final HuiFengFadadaService huiFengFadadaService;

    @ApiOperation(value = "创建合同签署任务")
    @PostMapping("/create/sign")
    BaseRes<CreateSignTaskRes> createSignTask(@RequestBody ContractTaskDTO contractTaskDTO) {
//        return huiFengFadadaService.createSignTask(contractTaskDTO);
        return null;
    }

    @ApiOperation(value = "获取印章")
    @GetMapping("/corp")
    BaseRes<GetSealFreeSignUrlRes> getSealFreeSignUrl(@RequestParam("openCorpId") String openCorpId) {
        return huiFengFadadaService.getSealFreeSignUrl(openCorpId);
    }

    @ApiOperation(value = "获取企业授权链接")
    @PostMapping("/fdd/corp/auth/url")
    BaseRes<ECorpAuthUrlRes> getFaddCorpAuthUrl() {
        return huiFengFadadaService.getFaddCorpAuthUrl();
    }

    @ApiOperation(value = "法大大合同签署文件同步")
    @PostMapping("/fdd/contract/sync/file")
    Result<Boolean> downLoadFddContractFile() {
        return Result.success(huiFengFadadaService.syncFddContractFile());
    }

    @ApiOperation(value = "创建富民-融资担保服务合同签署任务")
    @PostMapping("/create/signV2")
    Result<Boolean> createSignTaskV2(@RequestBody List<String> resourceIdList) {
        return Result.success(huiFengFadadaService.createSignTaskV2(resourceIdList));
    }

    @ApiOperation(value = "回传签署完成的富民-融资担保服务合同")
    @PostMapping ("/returnSignContract")
    Result<List<String>> returnSignContract() {
        return Result.success(huiFengFadadaService.returnSignContract());
    }
}
