package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.common.core.constant.MsgConstants;
import com.longhuan.common.core.enums.RoleEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.service.RedisService;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.common.web.utils.EnvUtil;
import com.longhuan.data.api.pojo.dto.DigitalDistributeDTO;
import com.longhuan.data.api.pojo.dto.DigitalStoreIdByDeptIdDTO;
import com.longhuan.order.enums.StatisticsEnum;
import com.longhuan.order.feign.DataFeign;
import com.longhuan.order.feign.DigitalizeFeign;
import com.longhuan.order.feign.MessageFeign;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.DistributeAreaVO;
import com.longhuan.order.pojo.vo.OrderApproveDistributeVO;
import com.longhuan.order.pojo.vo.TaskStatisticsVO;
import com.longhuan.order.service.*;
import com.longhuan.order.statemachine.OrderStateService;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.user.api.DeptDetailDTO;
import com.longhuan.user.api.DeptDetailVO;
import com.longhuan.user.enums.TodoInfoEnums;
import com.longhuan.user.pojo.dto.*;
import com.longhuan.user.pojo.vo.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 订派单分配服务
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderApproveDistributeServiceImpl extends MPJBaseServiceImpl<OrderApproveDistributeMapper, OrderApproveDistributeEntity> implements OrderApproveDistributeService {

    private final OrderInfoMapper orderInfoMapper;
    private final OrderService orderService;
    private final RiskStateService riskStateService;
    private final UserFeign userFeign;
    private final Random random = new Random();
    private final OrderApproveDistributeMapper orderApproveDistributeMapper;
    private final DistributeAreaMapper distributeAreaMapper;
    private final OrderStateService orderStateService;
    private final DataFeign dataFeign;
    private final ApproveNodeConfigInfoMapper approveNodeConfigInfoMapper;
    private final EnvUtil envUtil;
    private final UserOnlineDistributionTimeMapper userOnlineDistributionTimeMapper;
    private final RedisService redisService;
    private final DigitalizeFeign digitalizeFeign;
    private final MessageFeign messageFeign;
    private final DataPermissionService dataPermissionService;
    private final UserApproveQueueService userApproveQueueService;
    private final StatisticsStrategyFactory statisticsStrategyFactory;
    private final OrderNodeRecordMapper orderNodeRecordMapper;


    @Override
    public Boolean distributeOrder() {
        //将无心跳用户设为下线
//        riskStateService.update(new LambdaUpdateWrapper<RiskUserStateEntity>()
//                .set(RiskUserStateEntity::getState,2)
//                .eq(RiskUserStateEntity::getState, 1)
//                .eq(RiskUserStateEntity::getDeleteFlag,0)
//                .lt(RiskUserStateEntity::getUpdateTime, LocalDateTime.now().minusMinutes(10)));
        //更新数字化订单状态
        asyncProcessDigitalOrders();


        //分配
        List<OrderApproveDistributeEntity> riskOrderList = orderApproveDistributeMapper.selectList(new MPJLambdaWrapper<OrderApproveDistributeEntity>()
                .eq(OrderApproveDistributeEntity::getState, 0)
                .eq(OrderApproveDistributeEntity::getNode, 1510)
                .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                .last("ORDER BY CASE WHEN region_name = '电销部' THEN 0 ELSE 1 END, create_time ASC LIMIT 20")
        );

        for (OrderApproveDistributeEntity waitAssignOrder : riskOrderList) {
            log.info("OrderApproveDistributeServiceImpl distributeOrder riskOrderList:{}", JSONUtil.toJsonStr(waitAssignOrder));
            long startTime = System.currentTimeMillis();
            // 如果当前节点在待初审分配调用客服自动分配
            // 当前节点
            States assignOrderNode = States.getByNode(waitAssignOrder.getNode());

            // 风控初审
            if (assignOrderNode.equals(States.RISK_FIRST_APPROVE_ASSIGN)) {
                assignRiskFirstApproval(waitAssignOrder);
            }

            log.info("OrderApproveDistributeServiceImpl order {} cost {} ms", waitAssignOrder.getOrderNumber(), System.currentTimeMillis() - startTime);
        }


        List<OrderApproveDistributeEntity> csOrderList = orderApproveDistributeMapper.selectList(new MPJLambdaWrapper<OrderApproveDistributeEntity>()
                .eq(OrderApproveDistributeEntity::getState, 0)
                .ne(OrderApproveDistributeEntity::getNode, 1510)
                .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                .orderByAsc(OrderApproveDistributeEntity::getCreateTime)
                .last("limit 20")
        );
        if (CollUtil.isEmpty(csOrderList)) {
            log.info("distributeOrder not found order");
            return true;
        }

        log.info("OrderApproveDistributeServiceImpl distributeOrder wait num: {}", csOrderList.size());
        for (OrderApproveDistributeEntity waitAssignOrder : csOrderList) {

            log.info("OrderApproveDistributeServiceImpl distributeOrder csOrderList:{}", JSONUtil.toJsonStr(waitAssignOrder));
            long startTime = System.currentTimeMillis();
            // 如果当前节点在待初审分配调用客服自动分配
            // 当前节点
            States assignOrderNode = States.getByNode(waitAssignOrder.getNode());
            if (assignOrderNode.equals(States.QUALITY_INSPECTION)
                    || assignOrderNode.equals(States.PAYMENT_APPLY_INFORMATION)) {

                List<Integer> oldApproveUserList = historyApproveUserId(waitAssignOrder);


                Integer storeId = waitAssignOrder.getStoreId();

                ApproveNodeConfigInfoEntity approveNodeConfigInfoEntity = approveNodeConfigInfoMapper.selectOne(new MPJLambdaWrapper<ApproveNodeConfigInfoEntity>()
                        .eq(ApproveNodeConfigInfoEntity::getStoreId, storeId)
                        .eq(ApproveNodeConfigInfoEntity::getApproveNode, waitAssignOrder.getNode())
                        .eq(ApproveNodeConfigInfoEntity::getDataSource, waitAssignOrder.getSource())
                        .eq(ApproveNodeConfigInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(ApproveNodeConfigInfoEntity::getCreateTime)
                        .last("limit 1")
                );

                if (approveNodeConfigInfoEntity == null) {
                    log.error("OrderApproveDistributeServiceImpl distributeOrder storeId {} approveNode {} is not setting", storeId, waitAssignOrder.getNode());
                    continue;
                }
                // 门店审批单人最大数量
                int storeApproveSingleMaxNum = Convert.toInt(approveNodeConfigInfoEntity.getMaxNum(), 1);
                // 数据检验节点
                List<Integer> nodeList;
                if (StrUtil.isNotEmpty(approveNodeConfigInfoEntity.getDataNode())) {
                    nodeList = JSONUtil.toList(approveNodeConfigInfoEntity.getDataNode(), Integer.class);
                } else {
                    nodeList = Collections.singletonList(waitAssignOrder.getNode());
                }


                // 来源
                Integer source = null;
                if (Objects.equals(approveNodeConfigInfoEntity.getRange(), 2)) {
                    source = waitAssignOrder.getSource();
                }

                DistributeAreaEntity distributeAreaEntity = distributeAreaMapper.selectOne(new LambdaQueryWrapper<DistributeAreaEntity>()
                        .eq(DistributeAreaEntity::getStoreId, storeId)
                        .eq(DistributeAreaEntity::getDeleteFlag, 0)
                );
                // 不存在则不分配
                if (distributeAreaEntity == null) {
                    continue;
                }
                // 门店禁用则不分配
                Integer serviceDispatch = distributeAreaEntity.getServiceDispatch();
                if (Convert.toInt(serviceDispatch, 0) == 0) {
                    continue;
                }
                // 门店所属区域
                Integer areaType = distributeAreaEntity.getAreaType();

                // 最终审批人
                Integer approvalUserId = null;


                // 1. 先判断该订单是否为驳回单，如果是，则分配到原先审核人
                if (CollUtil.isNotEmpty(oldApproveUserList)) {
                    // 驳回单不限制人员单量上限
                    Integer historyUserId = oldApproveUserList.get(0);
//                        Boolean b = riskStateService.selectUserIsOnline(historyUserId, 2);
//                        if (b) {
                    approvalUserId = historyUserId;
//                        } else {
//                            log.info("OrderApproveDistributeServiceImpl order {} node {} historyUserId {} not online",
//                                    waitAssignOrder.getOrderNumber(), waitAssignOrder.getNode(), historyUserId);
//                        }
                    // 筛选在线客服
                    if (assignOrderNode.equals(States.PAYMENT_APPLY_INFORMATION)) {
                        List<Integer> onlineList = validOnlineUserList(oldApproveUserList);
                        if (CollUtil.isEmpty(onlineList)) {
                            approvalUserId = null;
                        } else {
                            Integer i = assignCsApproveUser(oldApproveUserList, storeApproveSingleMaxNum, nodeList, source);
                            if (i == null) {
                                continue;
                            }
                        }
                    }
                }

                // 3 不存在则分配给新审核人
                if (approvalUserId == null) {
                    // 4. 按顺序查询客服人员

                    // 4.2、根据进件业务员所属门店，优先派给本门店的所属客服
                    // 查询门店客服
                    List<Integer> userList = getStoreCsUserIdList(storeId);

                    approvalUserId = assignCsApproveUser(userList, storeApproveSingleMaxNum, nodeList, source);


                    //                    4.3、如果订单当前客服订单处理达到上限，则派给本区域内的其他客服处理
                    if (approvalUserId == null) {
                        log.info("OrderApproveDistributeServiceImpl distributeOrder order {} assign to area", waitAssignOrder.getOrderNumber());

                        List<Integer> storeIdList = distributeAreaMapper.selectList(new LambdaQueryWrapper<DistributeAreaEntity>()
                                .eq(DistributeAreaEntity::getAreaType, areaType).ne(DistributeAreaEntity::getStoreId, storeId)
                                .eq(DistributeAreaEntity::getDeleteFlag, 0)
                        ).stream().map(DistributeAreaEntity::getStoreId).toList();

                        if (CollUtil.isNotEmpty(storeIdList)) {

                            List<Integer> storeCsUserIdList = getStoreCsUserIdList(storeIdList);

                            approvalUserId = assignCsApproveUser(storeCsUserIdList, storeApproveSingleMaxNum, nodeList, source);

                        }
                    }


                    //                    4.4、如果本区域内的客服都达到处理上限，则随机派给其他区域的客服处理
                    if (approvalUserId == null) {

                        List<Integer> storeIdList = distributeAreaMapper.selectList(new LambdaQueryWrapper<DistributeAreaEntity>()
                                .ne(DistributeAreaEntity::getAreaType, areaType).ne(DistributeAreaEntity::getStoreId, storeId)
                                .eq(DistributeAreaEntity::getDeleteFlag, 0)
                        ).stream().map(DistributeAreaEntity::getStoreId).toList();

                        if (CollUtil.isNotEmpty(storeIdList)) {

                            List<Integer> storeCsUserIdList = getStoreCsUserIdList(storeIdList);

                            approvalUserId = assignCsApproveUser(storeCsUserIdList, storeApproveSingleMaxNum, nodeList, source);

                        }
                    }

                }

                //  更新审批人
                if (approvalUserId != null) {
                    updateApproveUserId(approvalUserId, waitAssignOrder);
                }

            } else if (assignOrderNode.equals(States.MANAGER_INTERVIEW)) {

                if (assignInterviews(waitAssignOrder))
                    continue;
            }
            log.info("OrderApproveDistributeServiceImpl order {} cost {} ms", waitAssignOrder.getOrderNumber(), System.currentTimeMillis() - startTime);
        }
        return true;
    }
    @Override
    public boolean assignInterviews(OrderApproveDistributeEntity waitAssignOrder) {

        List<Integer> oldApproveUserList = historyApproveUserId(waitAssignOrder);


        Integer storeId = waitAssignOrder.getStoreId();

        ApproveNodeConfigInfoEntity approveNodeConfigInfoEntity = approveNodeConfigInfoMapper.selectOne(new MPJLambdaWrapper<ApproveNodeConfigInfoEntity>()
                .eq(ApproveNodeConfigInfoEntity::getStoreId, storeId)
                .eq(ApproveNodeConfigInfoEntity::getApproveNode, waitAssignOrder.getNode())
                .eq(ApproveNodeConfigInfoEntity::getDataSource, waitAssignOrder.getSource())
                .eq(ApproveNodeConfigInfoEntity::getDeleteFlag, 0)
                .orderByDesc(ApproveNodeConfigInfoEntity::getCreateTime)
                .last("limit 1")
        );

        if (approveNodeConfigInfoEntity == null) {
            log.error("OrderApproveDistributeServiceImpl distributeOrder storeId {} approveNode {} is not setting", storeId, waitAssignOrder.getNode());
            return true;
        }
        // 门店审批单人最大数量
        int storeApproveSingleMaxNum = Convert.toInt(approveNodeConfigInfoEntity.getMaxNum(), 1);
        // 数据检验节点
        List<Integer> nodeList;
        if (StrUtil.isNotEmpty(approveNodeConfigInfoEntity.getDataNode())) {
            nodeList = JSONUtil.toList(approveNodeConfigInfoEntity.getDataNode(), Integer.class);
        } else {
            nodeList = Collections.singletonList(waitAssignOrder.getNode());
        }


        // 来源
        Integer source = null;
        if (Objects.equals(approveNodeConfigInfoEntity.getRange(), 2)) {
            source = waitAssignOrder.getSource();
        }

        DistributeAreaEntity distributeAreaEntity = distributeAreaMapper.selectOne(new LambdaQueryWrapper<DistributeAreaEntity>()
                .eq(DistributeAreaEntity::getStoreId, storeId)
                .eq(DistributeAreaEntity::getDeleteFlag, 0)
        );
        // 不存在则不分配
        if (distributeAreaEntity == null) {
            return true;
        }
        // 门店禁用则不分配
        Integer serviceDispatch = distributeAreaEntity.getServiceDispatch();
        if (Convert.toInt(serviceDispatch, 0) == 0) {
            return true;
        }
        // 门店所属区域
        Integer areaType = distributeAreaEntity.getAreaType();

        // 签约方式
        Integer signType = Convert.toInt(waitAssignOrder.getSignType(), 0);

        // 最终审批人
        Integer approvalUserId = null;

        // 1. 先判断该订单是否为驳回单，如果是，则分配到原先审核人
        if (CollUtil.isNotEmpty(oldApproveUserList)) {
            // 驳回单不限制人员单量上限
            Integer historyUserId = oldApproveUserList.get(0);
            Boolean b = riskStateService.selectUserIsOnline(historyUserId, 2);
            if (b) {
                approvalUserId = historyUserId;
            } else {
                log.info("OrderApproveDistributeServiceImpl order {} node {} historyUserId {} not online",
                        waitAssignOrder.getOrderNumber(), waitAssignOrder.getNode(), historyUserId);
            }
        }

        if (approvalUserId == null) {
            // 查询门店客服
            List<Integer> userList = getStoreCsUserIdList(storeId);

            approvalUserId = assignCsApproveUser(userList, storeApproveSingleMaxNum, nodeList, source);
            // 线上面签
            if (signType == 0 && approvalUserId == null) {

                //                    4.3、如果订单当前客服订单处理达到上限，则派给本区域内的其他客服处理

                log.info("OrderApproveDistributeServiceImpl distributeOrder order {} assign to area", waitAssignOrder.getOrderNumber());

                List<Integer> storeIdList = distributeAreaMapper.selectList(new LambdaQueryWrapper<DistributeAreaEntity>()
                        .eq(DistributeAreaEntity::getAreaType, areaType).ne(DistributeAreaEntity::getStoreId, storeId)
                        .eq(DistributeAreaEntity::getDeleteFlag, 0)
                ).stream().map(DistributeAreaEntity::getStoreId).toList();

                if (CollUtil.isNotEmpty(storeIdList)) {

                    List<Integer> storeCsUserIdList = getStoreCsUserIdList(storeIdList);

                    approvalUserId = assignCsApproveUser(storeCsUserIdList, storeApproveSingleMaxNum, nodeList, source);

                }


                //                    4.4、如果本区域内的客服都达到处理上限，则随机派给其他区域的客服处理
                if (approvalUserId == null) {

                    List<Integer> otherStoreIdList = distributeAreaMapper.selectList(new LambdaQueryWrapper<DistributeAreaEntity>()
                            .ne(DistributeAreaEntity::getAreaType, areaType).ne(DistributeAreaEntity::getStoreId, storeId)
                            .eq(DistributeAreaEntity::getDeleteFlag, 0)
                    ).stream().map(DistributeAreaEntity::getStoreId).toList();

                    if (CollUtil.isNotEmpty(otherStoreIdList)) {

                        List<Integer> storeCsUserIdList = getStoreCsUserIdList(otherStoreIdList);

                        approvalUserId = assignCsApproveUser(storeCsUserIdList, storeApproveSingleMaxNum, nodeList, source);

                    }
                }

            }
        }
        //  更新审批人
        if (approvalUserId != null) {
            updateApproveUserId(approvalUserId, waitAssignOrder);
        }
        return false;
    }

    @Async("asyncExecutor")
    public void asyncProcessDigitalOrders() {
        try {
            List<DigitalDistributeDTO> digitalizeOrderApproveDistributeEntities = orderApproveDistributeMapper.selectList(new MPJLambdaWrapper<OrderApproveDistributeEntity>()
                    .notIn(OrderApproveDistributeEntity::getState, 3, 4)
                    .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                    .eq(OrderApproveDistributeEntity::getSource, 1)
                    .orderByDesc(OrderApproveDistributeEntity::getCreateTime)).stream().map(digitalizeOrderApproveDistributeEntity -> new DigitalDistributeDTO()
                    .setId(digitalizeOrderApproveDistributeEntity.getId())
                    .setOrderNumber(digitalizeOrderApproveDistributeEntity.getOrderNumber())
                    .setNode(digitalizeOrderApproveDistributeEntity.getNode())
                    .setNodeName(digitalizeOrderApproveDistributeEntity.getNodeName())
                    .setSignType(digitalizeOrderApproveDistributeEntity.getSignType())
                    .setStoreId(digitalizeOrderApproveDistributeEntity.getStoreId())
                    .setOrderOpenTime(digitalizeOrderApproveDistributeEntity.getOrderOpenTime())
                    .setDistributeTime(digitalizeOrderApproveDistributeEntity.getDistributeTime())
                    .setDistributeEndTime(digitalizeOrderApproveDistributeEntity.getDistributeEndTime())
                    .setSource(digitalizeOrderApproveDistributeEntity.getSource())
                    .setState(digitalizeOrderApproveDistributeEntity.getState())
                    .setTitle(digitalizeOrderApproveDistributeEntity.getTitle())
            ).toList();

            digitalizeOrderApproveDistributeEntities = dataFeign.getDistributeOrder(digitalizeOrderApproveDistributeEntities).getData();
            for (DigitalDistributeDTO digitalizeOrderApproveDistributeEntity : digitalizeOrderApproveDistributeEntities) {
                if (ObjUtil.isNotNull(digitalizeOrderApproveDistributeEntity.getId())) {
                    updateOrderApproveDistribute(new UpdateOrderApproveDistributeDTO()
                            .setOrderNumber(digitalizeOrderApproveDistributeEntity.getOrderNumber())
                            .setCurrentNode(digitalizeOrderApproveDistributeEntity.getNode())
                            .setSource(digitalizeOrderApproveDistributeEntity.getSource())
                            .setBusinessType(2));
                } else {
                    initiateDispatch(new OrderApproveDistributeInsertDTO()
                            .setNode(digitalizeOrderApproveDistributeEntity.getNode())
                            .setOrderNumber(digitalizeOrderApproveDistributeEntity.getOrderNumber())
                            .setSource(digitalizeOrderApproveDistributeEntity.getSource())
                            .setStoreId(digitalizeOrderApproveDistributeEntity.getStoreId())
                            .setNodeName(digitalizeOrderApproveDistributeEntity.getNodeName())
                            .setTitle(digitalizeOrderApproveDistributeEntity.getTitle())
                            .setSignType(digitalizeOrderApproveDistributeEntity.getSignType())
                            .setCustomerName(digitalizeOrderApproveDistributeEntity.getCustomerName())
                            .setCustomerPhone(digitalizeOrderApproveDistributeEntity.getCustomerPhone())
                            .setManagerId(digitalizeOrderApproveDistributeEntity.getManagerId())
                            .setStoreName(digitalizeOrderApproveDistributeEntity.getStoreName())
                            .setRegionName(digitalizeOrderApproveDistributeEntity.getRegionName())
                            .setVehicleNumber(digitalizeOrderApproveDistributeEntity.getVehicleNumber())
                            .setStepDispose(digitalizeOrderApproveDistributeEntity.getStepDispose())
                            .setStepTime(digitalizeOrderApproveDistributeEntity.getStepTime()));
                }
            }
        } catch (Exception e) {
            log.error("saveModelScore error", e);
        }
    }

    /**
     * 订单历史分配审批完成的人列表
     *
     * @param waitAssignOrder 等待分配顺序
     * @return {@link Integer }
     */
    private List<Integer> historyApproveUserId(OrderApproveDistributeEntity waitAssignOrder) {
        List<Integer> nodes= new ArrayList<>(List.of(waitAssignOrder.getNode()));
        if (ObjUtil.equals(waitAssignOrder.getNode(), States.PAYMENT_APPLY_INFORMATION.getNode())){
            nodes.add(States.MANAGER_INTERVIEW.getNode());
        }
        return orderApproveDistributeMapper.selectList(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                        .select(OrderApproveDistributeEntity::getUserId)
                        .eq(OrderApproveDistributeEntity::getVehicleNumber, waitAssignOrder.getVehicleNumber())
                        .in(OrderApproveDistributeEntity::getNode, nodes)
                        .eq(OrderApproveDistributeEntity::getState, 3)
                        .isNotNull(OrderApproveDistributeEntity::getUserId)
                        .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderApproveDistributeEntity::getCreateTime)
                        .last("limit 1")
                )
                .stream().map(OrderApproveDistributeEntity::getUserId).toList();
    }

    /**
     * 筛选用户列表
     * 1. 在线用户
     * 2. 用户待办数量
     * 3. 用户历史审批数量
     *
     * @param userList                 用户列表
     * @param storeApproveSingleMaxNum Store 批准单个最大数量
     * @param nodeList                 节点列表
     * @param source                   源
     * @return {@link Integer }
     */
    private Integer assignCsApproveUser(List<Integer> userList, int storeApproveSingleMaxNum, List<Integer> nodeList, Integer source) {

        Integer approvalUserId = null;
        if (CollUtil.isEmpty(userList)) {
            log.warn("OrderApproveDistributeServiceImpl distributeOrder  not found user");
        } else {
            // 筛选在线客服
            List<Integer> onlineList = validOnlineUserList(userList);

            if (CollUtil.isEmpty(onlineList)) {
                log.warn("OrderApproveDistributeServiceImpl distributeOrder  not found online user");
            } else {
                // 筛选客服代办订单量
                List<Integer> avalibeUserList = filterAvailableAgentsUserList(storeApproveSingleMaxNum, nodeList, source, onlineList);
                if (CollUtil.isNotEmpty(avalibeUserList)) {
                    // 筛选绩效最少的人
                    // 按用户id分组计算订单量
//                    UserWorkNumDTO minUserWorkNum = filterFewestPerformersUserList(avalibeUserList);
//                    if (minUserWorkNum != null) {
//                        approvalUserId = minUserWorkNum.getUserId();
//                    }
//                    int randomIndex = random.nextInt(avalibeUserList.size());
//                    approvalUserId = avalibeUserList.get(randomIndex);
                    //查找最长时间未派单的用户(模拟轮询)
                    approvalUserId = getLongestIdleUser(avalibeUserList,nodeList);
                } else {
                    // 门店客服人员没有空闲人员
                    log.info("OrderApproveDistributeServiceImpl distributeOrder waiting order has max limit");
                }
            }
        }
        return approvalUserId;
    }
    private @Nullable Integer getLongestIdleUser(List<Integer> avalibeUserList, List<Integer> nodeList) {
        if (CollUtil.isEmpty(avalibeUserList)) {
            return null;
        }

        // 查询这些用户最近一次派单的时间
        List<OrderApproveDistributeEntity> latestDistributions = orderApproveDistributeMapper.selectList(
                new MPJLambdaWrapper<OrderApproveDistributeEntity>()
                        .select(OrderApproveDistributeEntity::getUserId)
                        .selectAs("MAX(distribute_time)", OrderApproveDistributeEntity::getDistributeTime)
                        .in(OrderApproveDistributeEntity::getUserId, avalibeUserList)
                        .in(OrderApproveDistributeEntity::getNode, nodeList)
                        .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                        .groupBy(OrderApproveDistributeEntity::getUserId)
        );

        // 创建用户ID到最近派单时间的映射
        Map<Integer, LocalDateTime> userLatestDistributeTimeMap = new HashMap<>();
        for (OrderApproveDistributeEntity entity : latestDistributions) {
            userLatestDistributeTimeMap.put(entity.getUserId(), entity.getDistributeTime());
        }

        // 找到最长时间未派单的用户（即最近派单时间最早的用户）
        // 如果用户从未派过单，则使用LocalDateTime.MIN，这样会优先分配给这些用户
        return avalibeUserList.stream()
                .min(Comparator.comparing(user ->
                        userLatestDistributeTimeMap.getOrDefault(user, LocalDateTime.MIN)))
                .orElse(null);
    }
    /**
     * 筛选绩效最少的人
     *
     * @param avalibeUserList avalibe 用户列表
     * @return {@link UserWorkNumDTO }
     */
    private @Nullable UserWorkNumDTO filterFewestPerformersUserList(List<Integer> avalibeUserList) {
        Map<Integer, List<OrderApproveDistributeEntity>> userHistoryOrderMap = orderApproveDistributeMapper.selectList(new MPJLambdaWrapper<OrderApproveDistributeEntity>()
                        .in(OrderApproveDistributeEntity::getUserId, avalibeUserList)
//                            .in(OrderApproveDistributeEntity::getNode, nodeList) // 历史分配量不区分节点
                        .ne(OrderApproveDistributeEntity::getState, 4)
                        .gt(OrderApproveDistributeEntity::getDistributeTime, LocalDate.now().minusMonths(1).atStartOfDay())
                        .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
        ).stream().collect(Collectors.groupingBy(OrderApproveDistributeEntity::getUserId));

        // 获取用户id和订单量最少的第一个人
        return avalibeUserList.stream().map(userId -> {
            UserWorkNumDTO userWorkNumDTO = new UserWorkNumDTO();
            userWorkNumDTO.setUserId(userId);
            List<OrderApproveDistributeEntity> orderApproveDistributeEntities = userHistoryOrderMap.get(userId);
            if (CollUtil.isNotEmpty(orderApproveDistributeEntities)) {
                long count = orderApproveDistributeEntities.stream().map(OrderApproveDistributeEntity::getOrderNumber).distinct().count();
                userWorkNumDTO.setWorkNum(Convert.toInt(count, 0));
            } else {
                userWorkNumDTO.setWorkNum(0);
            }
            return userWorkNumDTO;
        }).min(Comparator.comparingInt(UserWorkNumDTO::getWorkNum)).orElse(null);
    }

    /**
     * 筛选空闲的客服人员
     * 1. 请款资料环节，数字化的订单已接单数量每日0时自动清零
     *
     * @param storeApproveSingleMaxNum Store 批准单个最大数量
     * @param nodeList                 节点列表
     * @param source                   源
     * @param onlineList               在线列表
     * @return {@link List }<{@link Integer }>
     */
    private @NotNull List<Integer> filterAvailableAgentsUserList(int storeApproveSingleMaxNum, List<Integer> nodeList, Integer source, List<Integer> onlineList) {
        MPJLambdaWrapper<OrderApproveDistributeEntity> queryWrapper = new MPJLambdaWrapper<OrderApproveDistributeEntity>()
                .in(OrderApproveDistributeEntity::getUserId, onlineList)
                .in(OrderApproveDistributeEntity::getNode, nodeList)
                .eq(Objects.nonNull(source), OrderApproveDistributeEntity::getSource, source)
                .in(OrderApproveDistributeEntity::getState, 1, 2)
                .eq(OrderApproveDistributeEntity::getDeleteFlag, 0);

        if (Objects.equals(source, 1) && nodeList.size() == 1 && nodeList.get(0).equals(States.PAYMENT_APPLY_INFORMATION.getNode())) {
            queryWrapper.gt(OrderApproveDistributeEntity::getDistributeTime, LocalDate.now().atStartOfDay());
        }

        Map<Integer, List<OrderApproveDistributeEntity>> userOrderMap = orderApproveDistributeMapper.selectList(queryWrapper
        ).stream().collect(Collectors.groupingBy(OrderApproveDistributeEntity::getUserId));

        // 筛选出待审批订单量小于门店审批单人最大数量
        return onlineList.stream().filter(userId -> {
            List<OrderApproveDistributeEntity> entities = userOrderMap.get(userId);
            if (CollUtil.isNotEmpty(entities)) {
                return entities.size() < storeApproveSingleMaxNum;
            } else {
                return true;
            }
        }).toList();
    }

    /**
     * 校验在线用户列表
     *
     * @param userList 用户列表
     * @return {@link List }<{@link Integer }>
     */
    private List<Integer> validOnlineUserList(List<Integer> userList) {
        return riskStateService.list(new LambdaQueryWrapper<RiskUserStateEntity>()
                .in(RiskUserStateEntity::getUserId, userList)
                .eq(RiskUserStateEntity::getState, 1)
                .eq(RiskUserStateEntity::getUserType, 2)
                .eq(RiskUserStateEntity::getDeleteFlag, 0)
        ).stream().map(RiskUserStateEntity::getUserId).toList();
    }

    /**
     * 获取门店用户 ID 列表
     *
     * @param storeId 门店 ID
     * @return {@link List }<{@link Integer }>
     */
    private List<Integer> getStoreCsUserIdList(Integer storeId) {
        List<Integer> deptIds = Collections.singletonList(storeId);

        return getStoreCsUserIdList(deptIds);
    }

    /**
     * 获取 Store CS 用户 ID 列表
     *
     * @param deptIds 部门 ID
     * @return {@link List }<{@link Integer }>
     */
    private List<Integer> getStoreCsUserIdList(List<Integer> deptIds) {
        List<Integer> roleIds = Collections.singletonList(RoleEnum.CUSTOMER_SERVICE_SPECIALIST.getId());
        DeptUsersByRoleIdsDTO deptUsersByRoleIdsDTO = new DeptUsersByRoleIdsDTO();
        deptUsersByRoleIdsDTO.setDeptIds(deptIds);
        deptUsersByRoleIdsDTO.setRoleIds(roleIds);
        List<UserInfoVO> userInfoVOList = userFeign.getDeptUsersByRoleIds(deptUsersByRoleIdsDTO).getData();
        List<Integer> list = userInfoVOList.stream().map(UserInfoVO::getUserId).toList();
        log.info("OrderApproveDistributeServiceImpl getStoreCsUserIdList userList:{}", list);
        return list;
    }

    /**
     * 分配风险初审审批
     *
     * @param orderApproveDistributeEntity Order Approve Distribute 实体
     */
    @Override
    public void assignRiskFirstApproval(OrderApproveDistributeEntity orderApproveDistributeEntity) {
        String lockValue = IdUtil.randomUUID();
        String orderNumber = orderApproveDistributeEntity.getOrderNumber();
        String lockKey = "order:assign:riskFirstApproval:" + orderNumber;
        try {
            log.info("OrderApproveDistributeServiceImpl assignRiskFirstApproval orderApproveDistributeEntity:{}", JSONUtil.toJsonStr(orderApproveDistributeEntity));
            Boolean tryLock = redisService.tryLock(lockKey, lockValue, 5);
            if (tryLock) {

                Integer riskUserId = null;
                // 1. 先判断该订单是否为驳回单，如果是，则分配到原先审核人
                List<Integer> historyList = historyApproveUserId(orderApproveDistributeEntity);
                if (CollUtil.isNotEmpty(historyList)) {
                    // 驳回单不限制人员单量上限
                    Integer historyUserId = historyList.get(0);
                    Boolean b = riskStateService.orderRiskHistoryUserIdOnline(historyUserId, 1);
                    if (b) {
                        riskUserId = historyUserId;
                    } else {
                        log.info("OrderApproveDistributeServiceImpl assignRiskFirstApproval order {} historyUserId {} not online", orderNumber, historyUserId);
                    }
                }
                // 2. 无审批人，分配到随机一个在线人员
                //电销订单优先分配电销风控
                if (riskUserId == null) {
                    if (ObjUtil.isNotNull(orderApproveDistributeEntity.getOrderId())&&orderApproveDistributeEntity.getSource()==0){
                        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderApproveDistributeEntity.getOrderId());
                        if (ObjUtil.equals(orderInfoEntity.getRegionId(),56)|| ObjUtil.equals(orderInfoEntity.getRegionId(),24)){
                            riskUserId = getRandomRiskUserId(3);
                        }
                    }
                }
                if (riskUserId == null) {
//                    riskUserId = getRandomRiskUserId();
                    riskUserId = getNextRiskUserId();
                }
                if (riskUserId == null) {
                    return;
                } else {

                    userApproveQueueService.removeUserFromQueue(riskUserId);
                }
                updateApproveUserId(riskUserId, orderApproveDistributeEntity);
            } else {
                log.info("OrderApproveDistributeServiceImpl assignRiskFirstApproval order {} tryLock {}", orderNumber, false);
            }
        } finally {
            redisService.releaseLock(lockKey, lockValue);
        }
    }

    /**
     * 通过队列方式获取风控初审审批人
     *
     * @return
     */
    private Integer getNextRiskUserId() {

        Integer nextUserId = userApproveQueueService.popFirstUser();
        if (nextUserId == null) {
            log.info("OrderApproveDistributeServiceImpl getNextRiskUserId no user in queue");
            return getRandomRiskUserId(1);
        }
        return nextUserId;
    }


    /**
     * 更新审批用户ID
     *
     * @param approvalUserId  审批用户 ID
     * @param waitAssignOrder 待分配订单
     */
    @Override
    public void updateApproveUserId(Integer approvalUserId, OrderApproveDistributeEntity waitAssignOrder) {

        String orderNumber = waitAssignOrder.getOrderNumber();
        Integer assignOrderNode = waitAssignOrder.getNode();
        String lockValue = IdUtil.randomUUID();
        String lockKey = "order:assign:updateApproveUserId:" + orderNumber;
        try {
            log.info("OrderApproveDistributeServiceImpl updateApproveUserId orderApproveDistributeEntity:{}", JSONUtil.toJsonStr(waitAssignOrder));
            Boolean tryLock = redisService.tryLock(lockKey, lockValue, 20);
            if (tryLock) {
                if (approvalUserId == null) {
                    log.info("OrderApproveDistributeServiceImpl updateApproveUserId order {} node {} assign fail, approvalUserId is null",
                            orderNumber, assignOrderNode);
                    return;
                }

                log.info("OrderApproveDistributeServiceImpl updateApproveUserId order {} node {} assign to user {}",
                        orderNumber, assignOrderNode, approvalUserId);
                // 1. 新增待办消息
                TodoInfoMessageDTO todoInfoMessageDTO = new TodoInfoMessageDTO()
                        .setSourceType(waitAssignOrder.getSource())
                        .setRemark("请在" + waitAssignOrder.getNodeName() + "节点处理")
                        .setState(0)
                        .setTitle(waitAssignOrder.getTitle())
                        .setNode(TodoInfoEnums.getByNode(assignOrderNode))
                        .setAccessUrl(null)
                        .setOrderNumber(orderNumber)
                        .setOrderId(waitAssignOrder.getOrderId())
                        .setTableId(String.valueOf(waitAssignOrder.getId()))
                        .setCustomerName(waitAssignOrder.getCustomerName())
                        .setCustomerPhone(waitAssignOrder.getCustomerPhone())
                        .setVehicleNumber(waitAssignOrder.getVehicleNumber())
                        .setManagerId(waitAssignOrder.getManagerId())
                        .setStoreName(waitAssignOrder.getStoreName())
                        .setRegionName(waitAssignOrder.getRegionName());

//        UserDetailInfoVO userDetailInfoVO = userFeign.searchUserDetailById(approvalUserId).getData();
                UserAndDeptUsersVO userAndDeptUsersVO = userFeign.selectUsersStore(new UserStoreDTO().setUserId(approvalUserId)).getData();
                List<TodoInfoMessageDTO.TodoUser> todoUsers = new ArrayList<>();
                todoUsers.add(new TodoInfoMessageDTO.TodoUser().setName(userAndDeptUsersVO.getName()).setPhoneNumber(userAndDeptUsersVO.getMobile()).setJobNumber(userAndDeptUsersVO.getJobNumber()));
                todoInfoMessageDTO.setTodoUserList(todoUsers);
                userFeign.dealMessage(todoInfoMessageDTO);

                // 2. 更新审批用户ID
                log.info("OrderApproveDistributeServiceImpl updateApproveUserId order {} approval user update", orderNumber);
                OrderApproveDistributeEntity orderApproveDistributeEntity = new OrderApproveDistributeEntity();
                LocalDateTime now = LocalDateTime.now();
                orderApproveDistributeEntity
                        .setId(waitAssignOrder.getId())
                        .setUserId(approvalUserId)
                        .setState(1)
                        .setDistributeTime(now)
                        .setDispatchStoreName(userAndDeptUsersVO.getStore())
                        .setDispatchRegionName(userAndDeptUsersVO.getArea());
                OrderApproveDistributeEntity newOrderApproveDistributeEntity = orderApproveDistributeMapper.selectById(orderApproveDistributeEntity.getId());
                if (ObjUtil.notEqual(newOrderApproveDistributeEntity.getState(),waitAssignOrder.getState())){
                    return;
                }
                orderApproveDistributeMapper.update(new LambdaUpdateWrapper<OrderApproveDistributeEntity>()
                        .set(OrderApproveDistributeEntity::getState, 4)
                        .set(OrderApproveDistributeEntity::getDeleteFlag, 1)
                        .eq(OrderApproveDistributeEntity::getOrderNumber, waitAssignOrder.getOrderNumber())
                        .eq(OrderApproveDistributeEntity::getNode, assignOrderNode)
                        .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                        .notIn(OrderApproveDistributeEntity::getState, 3, 4)
                        .ne(OrderApproveDistributeEntity::getId, waitAssignOrder.getId()));
                int update = orderApproveDistributeMapper.update(orderApproveDistributeEntity, new LambdaUpdateWrapper<OrderApproveDistributeEntity>()
                        .eq(OrderApproveDistributeEntity::getId, waitAssignOrder.getId())
                        .eq(OrderApproveDistributeEntity::getState, 0));

                // 3. 订单状态流转
                if (update > 0) {
                    if (ObjUtil.equals(waitAssignOrder.getSource(), 0) && ObjUtil.equals(assignOrderNode, States.RISK_FIRST_APPROVE_ASSIGN.getNode())) {
                        log.info("OrderApproveDistributeServiceImpl updateApproveUserId order {} node {}  submit", orderNumber, assignOrderNode);
                        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                                .eq(OrderInfoEntity::getOrderNumber, orderNumber), false);
                        orderInfoMapper.update(new LambdaUpdateWrapper<OrderInfoEntity>()
                                .set(OrderInfoEntity::getRiskUserId, approvalUserId)
                                .eq(OrderInfoEntity::getId, orderInfoEntity.getId())
                                .eq(OrderInfoEntity::getDeleteFlag, 0));
                        if (ObjUtil.equals(orderInfoEntity.getCurrentNode(), States.RISK_FIRST_APPROVE_ASSIGN.getNode())) {
                            orderStateService.sendEvent(States.RISK_FIRST_APPROVE_ASSIGN, Events.AGREES, orderInfoEntity.getId(), 1, null, null, null);
                        }
                    }
                    if (ObjUtil.equals(waitAssignOrder.getSource(), 1)) {
                        //同步给数字化
                        try {
                            MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
                            formData.add("order_id", waitAssignOrder.getOrderNumber());
                            formData.add("mobile", userAndDeptUsersVO.getMobile());
                            switch (assignOrderNode) {
                                case 1300 -> {
                                    formData.add("verify_node", 2);
                                }
                                case 3550 -> {
                                    formData.add("verify_node", 601);
                                }
                                case 1510 -> {
                                    formData.add("verify_node", 4);
                                }
                            }
                            formData.add("dispatch_time", LocalDateTimeUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
                            log.info("OrderApproveDistributeServiceImpl updateApproveUserId order {} node {}  submit digitalize req{}", orderNumber, assignOrderNode, JSONUtil.toJsonStr(formData));
                            DispatchNotificationsDTO dispatchNotificationsDTO = digitalizeFeign.dispatchNotifications(formData).getData();
                            log.info("OrderApproveDistributeServiceImpl updateApproveUserId order {} node {}  submit digitalize rep {}", orderNumber, assignOrderNode, dispatchNotificationsDTO);
                        } catch (Exception e) {
                            log.error("OrderApproveDistributeServiceImpl updateApproveUserId order {} node {}  submit digitalize error", orderNumber, assignOrderNode, e);
                        }
                    }
                    //4. 消息通知
                    try {
                        if (Objects.equals(assignOrderNode, States.QUALITY_INSPECTION.getNode())
                                || Objects.equals(assignOrderNode, States.MANAGER_INTERVIEW.getNode())
                                || Objects.equals(assignOrderNode, States.PAYMENT_APPLY_INFORMATION.getNode())) {
                            if (ObjUtil.isNotNull(waitAssignOrder.getManagerId())) {

                                List<Integer> userIdList = new ArrayList<>();
                                userIdList.add(waitAssignOrder.getManagerId());
                                UserAndDeptUsersVO managerUser = userFeign.selectUsersStore(new UserStoreDTO().setUserId(waitAssignOrder.getManagerId())).getData();
                                //添加团队经理
                                if (ObjUtil.isNotNull(managerUser.getTeamId())) {
                                    UserInfoVO userInfoVO = userFeign.getTeamManagerByTeamId(managerUser.getTeamId()).getData();
                                    userIdList.add(userInfoVO.getUserId());
                                }

                                Result<List<UserInfoVO>> listResult = userFeign.searchUserNameBatch(userIdList);

                                if (!Result.isSuccess(listResult)) {
                                    log.error("查询用户信息失败");
                                    return;
                                }
                                for (UserInfoVO userInfo : listResult.getData()) {
                                    MessageContent messageContent = new MessageContent()
                                            .setMsgType(MsgConstants.MSG_TEXT)
                                            .setSendType(MsgConstants.SEND_DD_NOTICE)
                                            .setContent(LocalDateTimeUtil.format(now, DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss")) + "\n" + waitAssignOrder.getCustomerName() + "-" + waitAssignOrder.getVehicleNumber() + "-" + States.getDescriptionByCode(assignOrderNode) + "\n当前节点已指派至" + userAndDeptUsersVO.getStore() + "-" + userAndDeptUsersVO.getName() + "处理中")
                                            .setReceiver(userInfo.getMobile());
                                    messageFeign.sendMessage(messageContent);
                                }
                            }
                            //给当前指派客服推送消息
                            UserInfoVO userInfo = userFeign.searchByUserId(approvalUserId).getData();
                            MessageContent messageContent = new MessageContent()
                                    .setMsgType(MsgConstants.MSG_TEXT)
                                    .setSendType(MsgConstants.SEND_DD_NOTICE)
                                    .setContent(LocalDateTimeUtil.format(now, DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss")) + "\n" + waitAssignOrder.getStoreName() + "-" + waitAssignOrder.getCustomerName() + "-" + waitAssignOrder.getVehicleNumber() + "-" + States.getDescriptionByCode(assignOrderNode) + "\n当前节点已指派至您处理，请注意处理")
                                    .setReceiver(userInfo.getMobile());
                            messageFeign.sendMessage(messageContent);

                        }
                        if (Objects.equals(assignOrderNode, States.MANAGER_INTERVIEW.getNode())&& Objects.equals(waitAssignOrder.getSource(), 0) && Objects.equals(waitAssignOrder.getSignType(), 0)) {
                            userFeign.sendEventWithDataByUserId(approvalUserId, "MANAGER_INTERVIEW:orderId:"+waitAssignOrder.getOrderId(),
                                    LocalDateTimeUtil.format(now, DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss")) + "\n" + waitAssignOrder.getStoreName() + "-" + waitAssignOrder.getCustomerName() + "-" + waitAssignOrder.getVehicleNumber() + "-" + States.getDescriptionByCode(assignOrderNode) + "\n当前节点已指派至您处理，请注意处理").getData();
                        }
                    } catch (Exception e) {
                        log.error("add team leader error {}", e.getMessage(), e);
                    }
                }
            }
        } finally {
            redisService.releaseLock(lockKey, lockValue);
        }
    }

//    public Integer getRandomRiskUserId(OrderApproveDistributeEntity orderApproveDistributeEntity) {
//        // 在线人员ID
//        List<Integer> userIdList = riskStateService.selectOnlineUser(1, 2).stream()
//                .map(RiskUserStateEntity::getUserId).toList();
//
//        log.info("OrderApproveDistributeServiceImpl getRandomRiskUserId userIdList:{}", userIdList);
//
//        //获取当前门店客服,若当前节点为客户预约，并且面前方式为线下，则获取预约门店客服
//        List<Integer> deptList = new ArrayList<>();
//        if ((Objects.equals(orderApproveDistributeEntity.getNode(), States.MANAGER_INTERVIEW.getNode()) && orderApproveDistributeEntity.getSignType() == 1)
//                || Objects.equals(orderApproveDistributeEntity.getNode(), States.PAYMENT_APPLY_INFORMATION.getNode())) {
//            deptList.add(orderApproveDistributeEntity.getStoreId());
//            List<Integer> storeList = userFeign.getDeptUsers(deptList).getData();
//
//            List<Integer> noBusinessUserIdList = getAvailableList(userIdList, storeList);
//
//            if (CollUtil.isNotEmpty(noBusinessUserIdList)) {
//                int randomIndex = random.nextInt(noBusinessUserIdList.size());
//                return noBusinessUserIdList.get(randomIndex);
//            } else {
//                return null;
//            }
//        }
//
//        deptList.add(orderApproveDistributeEntity.getStoreId());
//
//        List<Integer> storeList = userFeign.getDeptUsers(deptList).getData();
//
//        List<Integer> noBusinessUserIdList = getAvailableList(userIdList, storeList);
//
//        if (CollUtil.isNotEmpty(noBusinessUserIdList)) {
//            int randomIndex = random.nextInt(noBusinessUserIdList.size());
//            return noBusinessUserIdList.get(randomIndex);
//        }
//
//        //若当前节点为请款资料，客服不能分配直接进入到待办任务
////        if (Objects.equals(orderApproveDistributeEntity.getNode(), States.PAYMENT_APPLY_INFORMATION.getNode())) {
////            return null;
////        }
//
//        //获取当前区域客服
//        DistributeAreaEntity distributeAreaEntity = distributeAreaMapper.selectOne(new LambdaQueryWrapper<DistributeAreaEntity>()
//                .eq(DistributeAreaEntity::getStoreId, orderApproveDistributeEntity.getStoreId())
//                .eq(DistributeAreaEntity::getDeleteFlag, 0)
//                .orderByDesc(DistributeAreaEntity::getCreateTime)
//                .last("limit 1"));
//        if (distributeAreaEntity != null) {
//            List<Integer> list = distributeAreaMapper.selectList(new LambdaQueryWrapper<DistributeAreaEntity>()
//                            .eq(DistributeAreaEntity::getAreaType, distributeAreaEntity.getAreaType())
//                            .eq(DistributeAreaEntity::getServiceDispatch, 1)
//                            .eq(DistributeAreaEntity::getDeleteFlag, 0))
//                    .stream()
//                    .map(DistributeAreaEntity::getStoreId)
//                    .filter(Objects::nonNull)
//                    .toList();
//            deptList.addAll(list);
//            List<Integer> areaList = userFeign.getDeptUsers(deptList).getData();
//
//            noBusinessUserIdList = getAvailableList(userIdList, areaList);
//
//            if (CollUtil.isNotEmpty(noBusinessUserIdList)) {
//                int randomIndex = random.nextInt(noBusinessUserIdList.size());
//                return noBusinessUserIdList.get(randomIndex);
//            }
//        }
//
//        //获取全国客服
//        List<Integer> list = distributeAreaMapper.selectList(new LambdaQueryWrapper<DistributeAreaEntity>()
//                        .eq(DistributeAreaEntity::getServiceDispatch, 1)
//                        .eq(DistributeAreaEntity::getDeleteFlag, 0))
//                .stream()
//                .map(DistributeAreaEntity::getStoreId)
//                .filter(Objects::nonNull)
//                .toList();
//        deptList.addAll(list);
//        List<Integer> countryList = userFeign.getDeptUsers(deptList).getData();
//        log.info("OrderApproveDistributeServiceImpl getRandomRiskUserId integerList:{}", countryList);
//
//        noBusinessUserIdList = getAvailableList(userIdList, countryList);
//
//        log.info("OrderApproveDistributeServiceImpl getRandomRiskUserId noBusinessUserIdList:{}", noBusinessUserIdList);
//
//        if (CollUtil.isEmpty(noBusinessUserIdList)) {
//            return null;
//        }
//
//        // 订单分配
//        int randomIndex = random.nextInt(noBusinessUserIdList.size());
//        return noBusinessUserIdList.get(randomIndex);
//    }
//
//    @Override
//    public Integer orderDistributionManual(Integer riskUserId) {
//        log.info("OrderApproveDistributeServiceImpl orderDistributionManual start，riskUserId：{}", riskUserId);
//
//        List<RiskUserStateEntity> riskUserStateEntities = riskStateService.list(new LambdaQueryWrapper<RiskUserStateEntity>()
//                .eq(RiskUserStateEntity::getUserId, riskUserId)
//                .eq(RiskUserStateEntity::getDeleteFlag, 0));
//
//        if (CollUtil.isEmpty(riskUserStateEntities)) {
//            throw new BusinessException("风控人员不存在");
//        }
//
//        RiskUserStateEntity riskUserStateEntity = riskUserStateEntities.get(0);
//
//        //若风控人员状态为下线，则不分配订单
//        if (riskUserStateEntity.getState() != 1) {
//            return null;
//        }
//
//        Result<UserDetailInfoVO> userDetailInfoVOResult = userFeign.searchUserDetailById(riskUserId);
//        if (!Result.isSuccess(userDetailInfoVOResult) || userDetailInfoVOResult.getData() == null) {
//            log.error("未获取到用户角色，riskUserId：{}", riskUserId);
//            return null;
//        }
//
//        UserDetailInfoVO userDetailInfoVO = userDetailInfoVOResult.getData();
//        List<Integer> roleIds = userDetailInfoVO.getRoleIds();
//        if (!RoleEnum.RISK_AMOUNT_APPROVE.hasRole(roleIds)) {
//            log.error("用户角色不匹配，riskUserId：{}", roleIds);
//            return null;
//        }
//        OrderApproveDistributeEntity orderApproveDistributeEntity = orderApproveDistributeMapper.selectOne(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
//                .eq(OrderApproveDistributeEntity::getNode, States.RISK_FIRST_APPROVE_ASSIGN.getNode())
//                .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
//                .eq(OrderApproveDistributeEntity::getState, 0)
//                .orderByAsc(OrderApproveDistributeEntity::getCreateTime), false);
//        if (orderApproveDistributeEntity == null) {
//            return null;
//        }
//        log.info("待初审分配任务池订单：{}", orderApproveDistributeEntity.getOrderNumber());
//        updateApproveUserId(riskUserId, orderApproveDistributeEntity);
//
//
//        if (ObjUtil.equals(orderApproveDistributeEntity.getSource(), 0)) {
//            OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
//                    .eq(OrderInfoEntity::getOrderNumber, orderApproveDistributeEntity.getOrderNumber())
//                    .eq(OrderInfoEntity::getDeleteFlag, 0)
//                    .orderByDesc(OrderInfoEntity::getCreateTime));
//            orderInfoMapper.update(new OrderInfoEntity().setId(orderInfoEntity.getId()).setRiskUserId(riskUserId),
//                    new LambdaUpdateWrapper<OrderInfoEntity>().eq(OrderInfoEntity::getId, orderInfoEntity.getId()));
//            return orderInfoEntity.getId();
//        }
//        return null;
//    }

//    private List<Integer> getAvailableList(List<Integer> userIdList, List<Integer> customerServiceList) {
//        //取交集
//        List<Integer> availableUserIds = userIdList.stream().filter(customerServiceList::contains).toList();
//
//        log.info("OrderApproveDistributeServiceImpl getAvailableList availableUserIds:{}", availableUserIds);
//
//        if (CollUtil.isEmpty(availableUserIds)) {
//            return null;
//        }
//
//        // 手中有业务的客服人员ID
//        List<Integer> businessUserIdList = getRiskFirstNodeUserIdList();
//
//        log.info("OrderApproveDistributeServiceImpl getAvailableList businessUserIdList:{}", businessUserIdList);
//
//        //
//        return availableUserIds.stream()
//                .filter(userId -> !businessUserIdList.contains(userId)).toList();
//    }

    /**
     * 获取风险首节点用户 ID 列表
     *
     * @return {@link List }<{@link Integer }>
     */
    public List<Integer> getRiskFirstNodeUserIdList() {
        MPJLambdaWrapper<OrderApproveDistributeEntity> wrapper = new MPJLambdaWrapper<>();

        // 定义查询条件
        wrapper.select(OrderApproveDistributeEntity::getUserId)
                .in(OrderApproveDistributeEntity::getNode, Arrays.asList(States.QUALITY_INSPECTION.getNode(),
                        States.MANAGER_INTERVIEW.getNode()))
                .in(OrderApproveDistributeEntity::getState, 1, 2)
                .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                .isNotNull(OrderApproveDistributeEntity::getUserId)
                .groupBy(OrderApproveDistributeEntity::getUserId)
                .having("COUNT(t.user_id) >= 2");

        return orderApproveDistributeMapper.selectList(wrapper).stream()
                .map(OrderApproveDistributeEntity::getUserId)
                .filter(Objects::nonNull)
                .toList();
    }

    @Nullable
    public Integer getRandomRiskUserId(Integer type) {
        // 在线人员ID
        List<Integer> onlineUserIds = riskStateService.selectOnlineUser(1, List.of(1)).stream()
                .map(RiskUserStateEntity::getUserId).toList();

        log.info("OrderApproveDistributeServiceImpl getRandomRiskUserId onlineUserIds:{}", onlineUserIds);
            Result<List<Integer>> listResult = userFeign.searchUserByRoleId(type==3?RoleEnum.RISK_AMOUNT_APPROVE_ONLINE.getId():RoleEnum.RISK_AMOUNT_APPROVE.getId());
        if (!Result.isSuccess(listResult) || listResult.getData() == null) {
            log.error("未获取到风控人员信息");
            return null;
        }

        //获取所有风控人员ID
        List<Integer> roleUserIds = listResult.getData();

        log.info("OrderApproveDistributeServiceImpl getRandomRiskUserId roleUserIds:{}", roleUserIds);

        //取交集
        List<Integer> onlineRiskUserIds = onlineUserIds.stream().filter(roleUserIds::contains).toList();

        log.info("OrderApproveDistributeServiceImpl getRandomRiskUserId onlineRiskUserIds:{}", onlineRiskUserIds);

        // 手中有业务的风控人员ID
        List<Integer> businessUserIdList = orderApproveDistributeMapper.selectList(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                        .select(OrderApproveDistributeEntity::getUserId)
                        .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                        .isNotNull(OrderApproveDistributeEntity::getUserId)
                        .eq(OrderApproveDistributeEntity::getNode, States.RISK_FIRST_APPROVE_ASSIGN.getNode())
                        .in(OrderApproveDistributeEntity::getState, 1, 2)).stream()
                .map(OrderApproveDistributeEntity::getUserId).filter(Objects::nonNull)
                .toList();

        log.info("OrderApproveDistributeServiceImpl getRandomRiskUserId businessUserIdList:{}", businessUserIdList);

        List<Integer> freeUserIds = onlineRiskUserIds.stream().filter(userId -> !businessUserIdList.contains(userId)).toList();

        log.info("OrderApproveDistributeServiceImpl getRandomRiskUserId freeUserIds:{}", freeUserIds);

        if (CollUtil.isEmpty(freeUserIds)) {
            return null;
        }
        if (type==3){
            List<Integer> list = freeUserIds.stream()
                    .map(userFeign::searchUserDetailById)
                    .map(Result::getData)
                    .filter(Objects::nonNull)
                    .filter(e -> CollUtil.isNotEmpty(e.getDeptIds()))
                    .filter(e -> e.getDeptIds().contains(56))
                    .map(UserInfoVO::getUserId)
                    .toList();
            if (CollUtil.isNotEmpty(list)){
                freeUserIds = list;
            }
        }
        // 订单分配
        int randomIndex = random.nextInt(freeUserIds.size());
        Integer userId = freeUserIds.get(randomIndex);
        log.info("OrderApproveDistributeServiceImpl getRandomRiskUserId userId:{}", userId);
        return userId;
    }

    @Override
    public IPage<DistributeAreaVO> searchDistributeAreas(SearchDistributeAreasDTO searchDistributeAreasDTO) {
        Page<DistributeAreaEntity> page = new Page<>(searchDistributeAreasDTO.getPageNum(), searchDistributeAreasDTO.getPageSize());
        LambdaQueryWrapper<DistributeAreaEntity> wrapper = new LambdaQueryWrapper<DistributeAreaEntity>()
                .eq(ObjectUtil.isNotEmpty(searchDistributeAreasDTO.getAreaType()), DistributeAreaEntity::getAreaType, searchDistributeAreasDTO.getAreaType())
                .like(ObjectUtil.isNotEmpty(searchDistributeAreasDTO.getStoreName()), DistributeAreaEntity::getStoreName, searchDistributeAreasDTO.getStoreName())
                .eq(DistributeAreaEntity::getDeleteFlag, 0)
                .orderByDesc(DistributeAreaEntity::getAreaType, DistributeAreaEntity::getCreateTime);

        Page<DistributeAreaVO> newPage = new Page<>(searchDistributeAreasDTO.getPageNum(), searchDistributeAreasDTO.getPageSize());

        page = distributeAreaMapper.selectPage(page, wrapper);
        List<DistributeAreaVO> newRecords = new ArrayList<>();
        for (DistributeAreaEntity areaEntity : page.getRecords()) {
            DistributeAreaVO distributeAreaVO = new DistributeAreaVO();
            BeanUtils.copyProperties(areaEntity, distributeAreaVO);
            distributeAreaVO.setCreateBy(areaEntity.getCreateBy() == 1 ? "系统" : userFeign.searchUserName(areaEntity.getCreateBy()).getData().getName());
            distributeAreaVO.setUpdateBy(areaEntity.getUpdateBy() == 1 ? "系统" : userFeign.searchUserName(areaEntity.getUpdateBy()).getData().getName());
            newRecords.add(distributeAreaVO);
        }
        BeanUtils.copyProperties(page, newPage);
        return newPage.setRecords(newRecords);
    }

    @Override
    public Boolean updateDistributeArea(UpdateDistributeAreaDTO updateDistributeAreaDTO, LoginUser loginUser) {
        DistributeAreaEntity distributeAreaEntity = distributeAreaMapper.selectById(updateDistributeAreaDTO.getId());
        //如果开启客服派单将需分配状态订单放入任务池
        if (ObjUtil.equals(updateDistributeAreaDTO.getServiceDispatch(), 1) && ObjUtil.equals(distributeAreaEntity.getServiceDispatch(), 0)) {
            distributeAreaEntity.setServiceDispatch(1);
            distributeAreaEntity.setOpenTime(LocalDateTime.now());
//            distributeAreaMapper.updateById(distributeAreaEntity);
//            List<OrderInfoEntity> orderInfoEntities = orderInfoMapper.selectJoinList(OrderInfoEntity.class, new MPJLambdaWrapper<OrderInfoEntity>()
//                    .select(OrderInfoEntity::getCurrentNode, OrderInfoEntity::getId, OrderInfoEntity::getDeptId, OrderInfoEntity::getOrderNumber,
//                            OrderInfoEntity::getCustomerName, OrderInfoEntity::getVehicleNumber,OrderInfoEntity::getCustomerPhone)
//                    .select(CustomerSignInfoEntity::getSignType)
//                    .eq(OrderInfoEntity::getDeptId, distributeAreaEntity.getStoreId())
//                    .and(wrapper -> wrapper.in(OrderInfoEntity::getCurrentNode, Arrays.asList(States.QUALITY_INSPECTION.getNode(), States.PAYMENT_APPLY_INFORMATION.getNode()))
//                            .or(or -> or.eq(OrderInfoEntity::getCurrentNode, States.CUSTOMER_APPOINTMENT.getNode())
//                                    .eq(OrderInfoEntity::getReviewState, 1)))
//                    .leftJoin(CustomerSignInfoEntity.class, CustomerSignInfoEntity::getOrderId, OrderInfoEntity::getId)
//                    .eq(OrderInfoEntity::getDeleteFlag, 0));
//            log.info("updateDistributeArea orderInfoEntities:{}", orderInfoEntities);
//            for (OrderInfoEntity orderInfoEntity : orderInfoEntities) {
//                OrderApproveDistributeInsertDTO orderApproveDistributeInsertDTO = new OrderApproveDistributeInsertDTO()
//                        .setOrderNumber(orderInfoEntity.getOrderNumber())
//                        .setOrderId(orderInfoEntity.getId())
//                        .setNode(orderInfoEntity.getCurrentNode())
//                        .setNodeName(States.getDescriptionByCode(orderInfoEntity.getCurrentNode()))
//                        .setTitle(orderInfoEntity.getCustomerName()+" "+orderInfoEntity.getVehicleNumber()+" "+orderInfoEntity.getCustomerPhone()+" "+States.getDescriptionByCode(orderInfoEntity.getCurrentNode()))
//                        .setSignType(orderInfoEntity.getSignType())
//                        .setStoreId(orderInfoEntity.getDeptId())
//                        .setSource(0);
//                if (ObjUtil.equals(orderInfoEntity.getCurrentNode(), States.CUSTOMER_APPOINTMENT.getNode())) {
//                    orderApproveDistributeInsertDTO.setNode(States.MANAGER_INTERVIEW.getNode())
//                            .setNodeName(States.MANAGER_INTERVIEW.getDesc());
//
//                }
//                initiateDispatch(orderApproveDistributeInsertDTO);
//            }
            //如果关闭客服派单将未处理订单从任务池中移除
        } else if (ObjUtil.equals(updateDistributeAreaDTO.getServiceDispatch(), 0) && ObjUtil.equals(distributeAreaEntity.getServiceDispatch(), 1)) {
            distributeAreaEntity.setServiceDispatch(0);

            List<OrderApproveDistributeEntity> orderApproveDistributeEntities = orderApproveDistributeMapper.selectList(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                    .eq(OrderApproveDistributeEntity::getStoreId, distributeAreaEntity.getStoreId())
                    .ne(OrderApproveDistributeEntity::getNode, States.RISK_FIRST_APPROVE_ASSIGN.getNode())
                    .notIn(OrderApproveDistributeEntity::getState, 3, 4));
            log.info("updateDistributeArea orderApproveDistributeEntities:{}", orderApproveDistributeEntities);
            //将本门店订单释放
            for (OrderApproveDistributeEntity orderApproveDistributeEntity : orderApproveDistributeEntities) {
                UpdateOrderApproveDistributeDTO updateOrderApproveDistributeDTO = new UpdateOrderApproveDistributeDTO();
                updateOrderApproveDistributeDTO
                        .setOrderNumber(orderApproveDistributeEntity.getOrderNumber())
                        .setSource(orderApproveDistributeEntity.getSource())
                        .setBusinessType(3)
                        .setCurrentNode(orderApproveDistributeEntity.getNode());
                updateOrderApproveDistribute(updateOrderApproveDistributeDTO);
            }
            //将本门店客服下线
            List<Integer> userIds = userFeign.getDeptUsers(Collections.singletonList(distributeAreaEntity.getStoreId())).getData();
            log.info("updateDistributeArea userIds:{}", userIds);
            if (CollUtil.isNotEmpty(userIds)) {
                riskStateService.update(new LambdaUpdateWrapper<RiskUserStateEntity>()
                        .set(RiskUserStateEntity::getState, 2)
                        .set(RiskUserStateEntity::getUpdateTime, LocalDateTime.now())
                        .in(RiskUserStateEntity::getUserId, userIds)
                        .ne(RiskUserStateEntity::getState, 2)
                        .eq(RiskUserStateEntity::getDeleteFlag, 0)
                        .eq(RiskUserStateEntity::getUserType, 2));
                for (Integer userId : userIds) {
                    UserOnlineDistributionTimeEntity oldSleepTime = userOnlineDistributionTimeMapper.selectOne(new LambdaQueryWrapper<UserOnlineDistributionTimeEntity>()
                            .eq(UserOnlineDistributionTimeEntity::getUserId, userId)
                            .in(UserOnlineDistributionTimeEntity::getStatus, 1, 4)
                            .isNull(UserOnlineDistributionTimeEntity::getDownTime)
                            .orderByDesc(UserOnlineDistributionTimeEntity::getCreateTime), false);
                    if (ObjUtil.isNotNull(oldSleepTime)) {
                        LocalDateTime now = LocalDateTime.now();
                        long secondsDifference = Duration.between(oldSleepTime.getUpTime(), now).getSeconds();
                        oldSleepTime.setDownTime(now).setChangeTime(secondsDifference);
                        userOnlineDistributionTimeMapper.updateById(oldSleepTime);
                    }
                    oldSleepTime = userOnlineDistributionTimeMapper.selectOne(new LambdaQueryWrapper<UserOnlineDistributionTimeEntity>()
                            .eq(UserOnlineDistributionTimeEntity::getUserId, userId)
                            .eq(UserOnlineDistributionTimeEntity::getStatus, 3)
                            .isNull(UserOnlineDistributionTimeEntity::getDownTime)
                            .orderByDesc(UserOnlineDistributionTimeEntity::getCreateTime), false);
                    if (ObjUtil.isNotNull(oldSleepTime)) {
                        LocalDateTime now = LocalDateTime.now();
                        long secondsDifference = Duration.between(oldSleepTime.getUpTime(), now).getSeconds();
                        oldSleepTime.setDownTime(now).setChangeTime(secondsDifference);
                        userOnlineDistributionTimeMapper.updateById(oldSleepTime);
                    }
                }
                //将分配本门店订单重新分配
                orderApproveDistributeEntities = orderApproveDistributeMapper.selectList(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                        .in(OrderApproveDistributeEntity::getUserId, userIds)
                        .ne(OrderApproveDistributeEntity::getNode, States.RISK_FIRST_APPROVE_ASSIGN.getNode())
                        .notIn(OrderApproveDistributeEntity::getState, 3, 4));
            }
            log.info("updateDistributeArea orderApproveDistributeEntities:{}", orderApproveDistributeEntities);
            for (OrderApproveDistributeEntity orderApproveDistributeEntity : orderApproveDistributeEntities) {
                UpdateOrderApproveDistributeDTO updateOrderApproveDistributeDTO = new UpdateOrderApproveDistributeDTO();
                updateOrderApproveDistributeDTO
                        .setOrderNumber(orderApproveDistributeEntity.getOrderNumber())
                        .setSource(orderApproveDistributeEntity.getSource())
                        .setBusinessType(3)
                        .setCurrentNode(orderApproveDistributeEntity.getNode());
                updateOrderApproveDistribute(updateOrderApproveDistributeDTO);
                orderApproveDistributeEntity.setId(null).setState(0).setDistributeTime(null).setOrderOpenTime(null);
                orderApproveDistributeMapper.insert(orderApproveDistributeEntity);
            }
        }
        distributeAreaEntity.setId(updateDistributeAreaDTO.getId())
                .setAreaType(updateDistributeAreaDTO.getAreaType())
                .setUpdateBy(loginUser.getUserId());
        return distributeAreaMapper.updateById(distributeAreaEntity) > 0;
    }

    /**
     * 更新订单派单状态
     *
     * @param updateDistributeAreaDTO 更新分发区域 DTO
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateOrderApproveDistribute(UpdateOrderApproveDistributeDTO updateDistributeAreaDTO) {

        String orderNumber = updateDistributeAreaDTO.getOrderNumber();
        Integer businessType = updateDistributeAreaDTO.getBusinessType();

        String lockValue = IdUtil.randomUUID();
        String lockKey = "OrderApproveDistributeServiceImpl:updateOrderApproveDistribute:" + orderNumber;
        try {
            if (redisService.tryLock(lockKey, lockValue, 10)) {
                Integer currentNode = updateDistributeAreaDTO.getCurrentNode();

                if (ObjUtil.equals(currentNode, States.RISK_FIRST_APPROVE.getNode())) {
                    updateDistributeAreaDTO.setCurrentNode(States.RISK_FIRST_APPROVE_ASSIGN.getNode());
                    currentNode = States.RISK_FIRST_APPROVE_ASSIGN.getNode();
                }
                if (ObjUtil.equals(currentNode, States.CUSTOMER_APPOINTMENT.getNode())){
                    updateDistributeAreaDTO.setCurrentNode(States.MANAGER_INTERVIEW.getNode());
                    currentNode = States.MANAGER_INTERVIEW.getNode();
                }
                TodoInfoMessageDTO todoInfoMessageDTO = new TodoInfoMessageDTO()
                        .setSourceType(updateDistributeAreaDTO.getSource())
                        .setState(businessType)
                        .setNode(TodoInfoEnums.getByNode(currentNode))
                        .setAccessUrl(null);
                todoInfoMessageDTO.setSourceType(Convert.toInt(updateDistributeAreaDTO.getSource(), 0));

                Integer orderId = updateDistributeAreaDTO.getOrderId();

                if (StrUtil.isBlank(orderNumber)) {
                    Assert.notNull(orderId, () -> new BusinessException("订单不存在"));
                    OrderInfoEntity orderInfoEntity = orderService.getById(orderId);
                    Assert.notNull(orderInfoEntity, () -> new BusinessException("订单不存在"));
                    updateDistributeAreaDTO.setOrderNumber(orderInfoEntity.getOrderNumber());
                    orderNumber = orderInfoEntity.getOrderNumber();
                }

                // 更新待办状态
                OrderApproveDistributeEntity orderApproveDistributeEntity = getWaitingOrderDistributeId(orderNumber, currentNode);

                if (Objects.isNull(orderApproveDistributeEntity)) {
                    todoInfoMessageDTO
                            .setOrderNumber(orderNumber)
                            .setOrderId(orderId)
                            .setNode(TodoInfoEnums.getByNode(currentNode));
                    userFeign.dealMessage(todoInfoMessageDTO);
                    return true;
                } else {
                    todoInfoMessageDTO
                            .setOrderNumber(orderNumber)
                            .setOrderId(orderId)
                            .setTableId(String.valueOf(orderApproveDistributeEntity.getId()));
                    userFeign.dealMessage(todoInfoMessageDTO);
                }

                // 更新派单状态
                OrderApproveDistributeEntity updateEntity = new OrderApproveDistributeEntity();
                updateEntity.setId(orderApproveDistributeEntity.getId());
                LocalDateTime now = LocalDateTime.now();
                Integer state = switch (businessType) {
                    case 1 -> {
                        if (ObjUtil.isNotNull(orderApproveDistributeEntity.getOrderOpenTime())) {
                            yield orderApproveDistributeEntity.getState();
                        }
                        if (orderApproveDistributeEntity.getState() != 1 && orderApproveDistributeEntity.getState() != 5) {
                            yield orderApproveDistributeEntity.getState();
                        }
                        updateEntity.setOrderOpenTime(now);
                        yield orderApproveDistributeEntity.getState() == 5 ? 5 : 2;
                    }
                    case 2 -> {
                        orderApproveDistributeEntity.setDistributeEndTime(now);
                        LocalDateTime orderOpenTime = orderApproveDistributeEntity.getOrderOpenTime();
                        if (orderOpenTime == null) {
                            orderOpenTime=orderApproveDistributeEntity.getDistributeTime();
                        }
                        if (orderOpenTime == null){
                            orderOpenTime=orderApproveDistributeEntity.getCreateTime();
                        }
                        updateEntity.setApproveDuration((int)Duration.between(orderOpenTime, now).getSeconds());
                        yield 3;
                    }
                    case 3 -> {
                        updateEntity.setDeleteFlag(1);
                        yield 4;
                    }
                    case 4 -> {
                        if (ObjUtil.isNull(orderApproveDistributeEntity.getPendingTime())) {
                            updateEntity.setPendingTime(now);
                        }
                        yield 5;
                    }
                    default -> orderApproveDistributeEntity.getState();
                };
                updateEntity.setState(state);
                boolean req = orderApproveDistributeMapper.updateById(updateEntity) > 0;
                riskStateService.update(new LambdaUpdateWrapper<RiskUserStateEntity>()
                        .set(RiskUserStateEntity::getLastOperationTime, LocalDateTime.now())
                        .eq(RiskUserStateEntity::getUserId, orderApproveDistributeEntity.getUserId())
                        .eq(RiskUserStateEntity::getUserType, 2)
                        .eq(RiskUserStateEntity::getDeleteFlag, 0));

                // 风控节点审批完成处理
                if (req && state == 3 && Objects.equals(currentNode, States.RISK_FIRST_APPROVE_ASSIGN.getNode())) {
                    userApproveQueueService.addUserToQueue(orderApproveDistributeEntity.getUserId());
                    initiateARiskControlDispatch();
                }
                return req;
            } else {
                return false;
            }
        } finally {
            redisService.releaseLock(lockKey, lockValue);
        }
    }

    private OrderApproveDistributeEntity getWaitingOrderDistributeId(String orderNumber, Integer currentNode) {
        OrderApproveDistributeEntity orderApproveDistributeEntity = orderApproveDistributeMapper.selectOne(
                new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                        .eq(OrderApproveDistributeEntity::getOrderNumber, orderNumber)
                        .eq(OrderApproveDistributeEntity::getNode, currentNode)
                        .in(OrderApproveDistributeEntity::getState, 0, 1, 2, 5)
                        .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderApproveDistributeEntity::getCreateTime)
                        .last("limit 1"));
        return orderApproveDistributeEntity;
    }


    /**
     * 风控派单批量
     */
    private void initiateARiskControlDispatch() {
        log.info("initiateARiskControlDispatch");
        List<OrderApproveDistributeEntity> waitAssignOrders = orderApproveDistributeMapper.selectList(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                .eq(OrderApproveDistributeEntity::getNode, States.RISK_FIRST_APPROVE_ASSIGN.getNode())
                .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                .eq(OrderApproveDistributeEntity::getState, 0)
                .orderByAsc(OrderApproveDistributeEntity::getCreateTime)
                .last("limit 20"));
        waitAssignOrders = waitAssignOrders.stream()
                .sorted(Comparator.comparingInt(order -> StrUtil.equals(order.getRegionName(), "电销部")? 0 : 1))
                .toList();
        for (OrderApproveDistributeEntity waitAssignOrder : waitAssignOrders) {
            assignRiskFirstApproval(waitAssignOrder);
        }
    }

    @Override
    public Boolean returnDistributeOrder(UpdateOrderApproveDistributeDTO updateDistributeAreaDTO) {
        if (Objects.equals(updateDistributeAreaDTO.getCurrentNode(), States.MANAGER_INTERVIEW.getNode())) {
            updateDistributeAreaDTO.setCurrentNode(States.CUSTOMER_APPOINTMENT.getNode());
        }
        OrderInfoEntity orderInfoEntity = orderService.getById(updateDistributeAreaDTO.getOrderId());
        Assert.notNull(orderInfoEntity, () -> new BusinessException("订单不存在"));
        OrderApproveDistributeEntity orderApproveDistributeEntity = orderApproveDistributeMapper.selectOne(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                .eq(OrderApproveDistributeEntity::getOrderNumber, orderInfoEntity.getOrderNumber())
                .eq(OrderApproveDistributeEntity::getNode, updateDistributeAreaDTO.getCurrentNode())
                .isNull(OrderApproveDistributeEntity::getDistributeEndTime)
                .eq(OrderApproveDistributeEntity::getDeleteFlag, 0)
                .orderByDesc(OrderApproveDistributeEntity::getCreateTime)
                .last("limit 1"));
        Assert.notNull(orderApproveDistributeEntity, "该订单未分配，请刷新后重试");

        OrderApproveDistributeEntity newOrderApproveDistribute = new OrderApproveDistributeEntity();
        newOrderApproveDistribute.setId(orderApproveDistributeEntity.getId());
        newOrderApproveDistribute.setOrderOpenTime(LocalDateTime.now());
        newOrderApproveDistribute.setDistributeEndTime(LocalDateTime.now());
        newOrderApproveDistribute.setState(4);
        newOrderApproveDistribute.setDeleteFlag(1);
        orderApproveDistributeMapper.updateById(newOrderApproveDistribute);
        this.distributeOrder();
        return true;
    }

    @Override
    public Boolean initiateDispatch(OrderApproveDistributeInsertDTO dto) {
        if (ObjUtil.equals(dto.getNode(), States.MANAGER_INTERVIEW.getNode())
                || Objects.equals(dto.getNode(), States.QUALITY_INSPECTION.getNode())
                || Objects.equals(dto.getNode(), States.PAYMENT_APPLY_INFORMATION.getNode())) {
            List<Integer> deptIds = distributeAreaMapper.selectList(new LambdaQueryWrapper<DistributeAreaEntity>()
                            .eq(DistributeAreaEntity::getDeleteFlag, 0)
                            .eq(DistributeAreaEntity::getServiceDispatch, 1)).stream()
                    .map(DistributeAreaEntity::getStoreId)
                    .filter(Objects::nonNull)
                    .distinct().toList();
            if (CollUtil.isEmpty(deptIds)) {
                log.info("暂无开启的门店");
                return false;
            }
        }
        Long count = orderApproveDistributeMapper.selectCount(new LambdaQueryWrapper<OrderApproveDistributeEntity>()
                .eq(OrderApproveDistributeEntity::getOrderNumber, dto.getOrderNumber())
                .eq(OrderApproveDistributeEntity::getOrderId, dto.getOrderId())
                .eq(OrderApproveDistributeEntity::getNode, dto.getNode())
                .in(OrderApproveDistributeEntity::getState, 0, 1, 2, 5));
        if (count==0) {
            OrderApproveDistributeEntity newOrderApproveDistribute = new OrderApproveDistributeEntity();
            newOrderApproveDistribute.setOrderNumber(dto.getOrderNumber());
            newOrderApproveDistribute.setOrderId(dto.getOrderId());
            newOrderApproveDistribute.setNode(dto.getNode());
            newOrderApproveDistribute.setNodeName(dto.getNodeName());
            newOrderApproveDistribute.setTitle(dto.getTitle());
            newOrderApproveDistribute.setSignType(dto.getSignType());
            newOrderApproveDistribute.setStoreId(dto.getStoreId());
            newOrderApproveDistribute.setSource(dto.getSource());
            newOrderApproveDistribute.setState(0);
            newOrderApproveDistribute.setDeleteFlag(0);
            newOrderApproveDistribute.setCustomerName(dto.getCustomerName())
                    .setCustomerPhone(dto.getCustomerPhone())
                    .setManagerId(dto.getManagerId())
                    .setStoreName(dto.getStoreName())
                    .setRegionName(dto.getRegionName())
                    .setVehicleNumber(dto.getVehicleNumber())
                    .setStepDispose(dto.getStepDispose())
                    .setCreateTime(dto.getStepTime());
            orderApproveDistributeMapper.insert(newOrderApproveDistribute);
        }
        return true;
    }

    @Override
    public List<DistributeAreaVO> queryDistributeAreaToDigitalize(Integer serviceDispatch) {
        List<DistributeAreaEntity> distributeAreaEntities = distributeAreaMapper.selectList(new
                LambdaQueryWrapper<DistributeAreaEntity>()
                .eq(DistributeAreaEntity::getDeleteFlag, 0)
                .eq(ObjUtil.isNotNull(serviceDispatch), DistributeAreaEntity::getServiceDispatch, serviceDispatch));
        List<Integer> storeIds = distributeAreaEntities.stream().map(DistributeAreaEntity::getStoreId).filter(Objects::nonNull).toList();
        List<DeptSyncInfoVO> deptSyncInfoVOS = userFeign.getSyncDeptByLhDeptIds(new SearchDeptSyncInfoDTO().setLhDeptIdList(storeIds).setOrigin(2)).getData();
        List<Integer> digitalStoreIds = deptSyncInfoVOS.stream().map(DeptSyncInfoVO::getDeptId).filter(Objects::nonNull).map(Integer::valueOf).toList();
        List<DigitalStoreIdByDeptIdDTO> dtos = dataFeign.getStoreIdByDeptId(digitalStoreIds).getData();
        List<DistributeAreaVO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(dtos)) {
            for (DigitalStoreIdByDeptIdDTO dto : dtos) {
                deptSyncInfoVOS.stream().filter(deptSyncInfoVO -> ObjUtil.equals(Integer.valueOf(deptSyncInfoVO.getDeptId()), dto.getDeptId()))
                        .findFirst().flatMap(deptSyncInfoVO -> distributeAreaEntities.stream().filter(distributeAreaEntity -> Objects.equals(distributeAreaEntity.getStoreId(), deptSyncInfoVO.getLhDeptId()))
                                .findFirst()).ifPresent(distributeAreaEntity -> {
                            result.add(new DistributeAreaVO()
                                    .setAreaType(distributeAreaEntity.getAreaType())
                                    .setServiceDispatch(distributeAreaEntity.getServiceDispatch())
                                    .setStoreId(distributeAreaEntity.getStoreId())
                                    .setDigitalStoreId(dto.getStoreId())
                                    .setStoreName(dto.getStoreName())
                                    .setOpenTime(distributeAreaEntity.getOpenTime()));
                        });
            }
            if (ObjUtil.isNotNull(serviceDispatch)) {
                return result.stream().filter(distributeAreaVO -> Objects.equals(distributeAreaVO.getServiceDispatch(), serviceDispatch)).toList();
            }
        }
        return result;
    }

    @Override
    public Page<OrderApproveDistributeVO> queryOrderApproveDistributeList(QueryOrderApproveDistributeListDTO queryOrderApproveDistributeListDTO, LoginUser loginUser) {
        MPJLambdaWrapper<OrderApproveDistributeEntity> wrapper = new MPJLambdaWrapper<>(OrderApproveDistributeEntity.class)
                .select(OrderApproveDistributeEntity::getId,
                        OrderApproveDistributeEntity::getOrderId,
                        OrderApproveDistributeEntity::getOrderNumber,
                        OrderApproveDistributeEntity::getCustomerName,
                        OrderApproveDistributeEntity::getVehicleNumber,
                        OrderApproveDistributeEntity::getCustomerPhone,
                        OrderApproveDistributeEntity::getStoreId,
                        OrderApproveDistributeEntity::getStoreName,
                        OrderApproveDistributeEntity::getRegionName,
                        OrderApproveDistributeEntity::getDispatchStoreName,
                        OrderApproveDistributeEntity::getManagerId,
                        OrderApproveDistributeEntity::getNode,
                        OrderApproveDistributeEntity::getState,
                        OrderApproveDistributeEntity::getDistributeTime,
                        OrderApproveDistributeEntity::getDistributeEndTime,
                        OrderApproveDistributeEntity::getSource)
                .selectAs(OrderApproveDistributeEntity::getUserId, OrderApproveDistributeVO::getServiceDispatchId)
                .selectAs(OrderApproveDistributeEntity::getUpdateBy, OrderApproveDistributeVO::getLastOperateUserId)
                .like(StrUtil.isNotBlank(queryOrderApproveDistributeListDTO.getCustomerName()), OrderApproveDistributeEntity::getCustomerName, queryOrderApproveDistributeListDTO.getCustomerName())
                .like(StrUtil.isNotBlank(queryOrderApproveDistributeListDTO.getCustomerPhone()), OrderApproveDistributeEntity::getCustomerPhone, queryOrderApproveDistributeListDTO.getCustomerPhone())
                .like(StrUtil.isNotBlank(queryOrderApproveDistributeListDTO.getVehicleNumber()), OrderApproveDistributeEntity::getVehicleNumber, queryOrderApproveDistributeListDTO.getVehicleNumber())
                .like(StrUtil.isNotBlank(queryOrderApproveDistributeListDTO.getStoreName()), OrderApproveDistributeEntity::getStoreName, queryOrderApproveDistributeListDTO.getStoreName())
                .eq(ObjUtil.isNotNull(queryOrderApproveDistributeListDTO.getNode()), OrderApproveDistributeEntity::getNode, queryOrderApproveDistributeListDTO.getNode())
                .eq(ObjUtil.isNotNull(queryOrderApproveDistributeListDTO.getState()), OrderApproveDistributeEntity::getState, queryOrderApproveDistributeListDTO.getState())
                .eq(StrUtil.isNotBlank(queryOrderApproveDistributeListDTO.getDispatchStoreName()), OrderApproveDistributeEntity::getDispatchStoreName, queryOrderApproveDistributeListDTO.getDispatchStoreName())
                .orderByDesc(OrderApproveDistributeEntity::getDistributeTime)
                .orderByDesc(OrderApproveDistributeEntity::getCreateTime);
        if (ObjUtil.isNotNull(queryOrderApproveDistributeListDTO.getDistributeStartTime()) && ObjUtil.isNotNull(queryOrderApproveDistributeListDTO.getDistributeEndTime())) {
            wrapper.between(ObjUtil.isNotNull(queryOrderApproveDistributeListDTO.getDistributeStartTime()), OrderApproveDistributeEntity::getDistributeTime, queryOrderApproveDistributeListDTO.getDistributeStartTime(), queryOrderApproveDistributeListDTO.getDistributeEndTime());
        }
        //todo 权限控制
        dataPermissionService.limitQueryOrderApproveDistributeList(loginUser, wrapper);
        //查询所有接单人属于该大区
        if (StrUtil.isNotBlank(queryOrderApproveDistributeListDTO.getDispatchRegionName()) || StrUtil.isNotBlank(queryOrderApproveDistributeListDTO.getServiceDispatchName())) {
           /* List<OrderApproveDistributeVO> orderApproveDistributes = orderApproveDistributeMapper.selectJoinList(OrderApproveDistributeVO.class, wrapper);
            List<Integer> userIds = orderApproveDistributes.stream()
                    .flatMap(e -> Stream.of(e.getServiceDispatchId()))
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList();*/
            List<Integer> userIdsQuery1 = new ArrayList<>();
            //List<UserStoreVO> userInfoVOS = userFeign.searchUserStoreBatch(userIds).getData();
            if (StrUtil.isNotBlank(queryOrderApproveDistributeListDTO.getDispatchRegionName())) {
                if (queryOrderApproveDistributeListDTO.getDispatchRegionName().contains("大区") || queryOrderApproveDistributeListDTO.getDispatchRegionName().contains("电销部")) {
                    List<Integer> deptIds = userFeign.getDeptIdByLikeName((queryOrderApproveDistributeListDTO.getDispatchRegionName())).getData();
                    if (!CollUtil.isEmpty(deptIds)) {
                        for (Integer deptId : deptIds) {
                            List<Integer> data = userFeign.getUserIdByStoreIdAndRoleId(new getUserIdByStoreIdAndRoleIdDTO().setDeptId(deptId)).getData();
                            userIdsQuery1.addAll(data);
                        }
                    }
                }
                /*List<Integer> userIdsArea = userInfoVOS.stream()
                        .filter(UserStoreVO -> Objects.equals(UserStoreVO.getArea(), queryOrderApproveDistributeListDTO.getDispatchRegionName()))
                        .map(UserStoreVO::getUserId).toList();
                if (CollUtil.isNotEmpty(userIdsArea)) {
                    userIdsQuery1.addAll(userIdsArea);
                }*/
            }
            List<Integer> userIdsQuery2 = new ArrayList<>();
            if (StrUtil.isNotBlank(queryOrderApproveDistributeListDTO.getServiceDispatchName())) {
                //根据名称匹配接单人id
                List<Integer> userIdsName = userFeign.getUserIdByLikeNameList(queryOrderApproveDistributeListDTO.getServiceDispatchName()).getData();
                /*List<Integer> userIdsName = userInfoVOS.stream()
                        .filter(UserStoreVO -> Objects.equals(UserStoreVO.getName(), queryOrderApproveDistributeListDTO.getServiceDispatchName()))
                        .map(UserStoreVO::getUserId).toList();*/
                if (CollUtil.isNotEmpty(userIdsName)) {
                    userIdsQuery2.addAll(userIdsName);
                }
            }
            //取两list交集  如果没数据返回空
            if (StrUtil.isNotBlank(queryOrderApproveDistributeListDTO.getDispatchRegionName()) && StrUtil.isNotBlank(queryOrderApproveDistributeListDTO.getServiceDispatchName())) {
                List<Integer> intersection = new ArrayList<>(userIdsQuery1);
                intersection.retainAll(userIdsQuery2);
                if (CollUtil.isNotEmpty(intersection)) {
                    wrapper.in(OrderApproveDistributeEntity::getUserId, intersection);
                } else {
                    wrapper.in(OrderApproveDistributeEntity::getUserId, -1);
                }
            } else {
                if (StrUtil.isNotBlank(queryOrderApproveDistributeListDTO.getDispatchRegionName())) {
                    if (CollUtil.isNotEmpty(userIdsQuery1)) {
                        wrapper.in(OrderApproveDistributeEntity::getUserId, userIdsQuery1);
                    } else {
                        wrapper.in(OrderApproveDistributeEntity::getUserId, -1);
                    }
                }
                if (StrUtil.isNotBlank(queryOrderApproveDistributeListDTO.getServiceDispatchName())) {
                    if (CollUtil.isNotEmpty(userIdsQuery2)) {
                        wrapper.in(OrderApproveDistributeEntity::getUserId, userIdsQuery2);
                    } else {
                        wrapper.in(OrderApproveDistributeEntity::getUserId, -1);
                    }
                }
            }
        }
        Page<OrderApproveDistributeVO> page = orderApproveDistributeMapper.selectJoinPage(new Page<>(queryOrderApproveDistributeListDTO.getPageNum(), queryOrderApproveDistributeListDTO.getPageSize()), OrderApproveDistributeVO.class, wrapper);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<Integer> userIds = page.getRecords().stream()
                    .flatMap(entity -> Stream.of(entity.getManagerId(), entity.getServiceDispatchId(),entity.getLastOperateUserId()))
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList();
            if (CollUtil.isNotEmpty(userIds)) {
                List<UserStoreVO> UserStoreVOS = userFeign.searchUserStoreBatch(userIds).getData();
                Map<Integer, String> userMap = UserStoreVOS.stream().collect(Collectors.toMap(UserInfoVO::getUserId, UserInfoVO::getName, (v1, v2) -> v1));
                Map<Integer, String> userAreaMap = UserStoreVOS.stream().collect(Collectors.toMap(UserStoreVO::getUserId, userStoreVO -> Optional.ofNullable(userStoreVO.getArea()).orElse(""), (v1, v2) -> v1));
                page.getRecords().forEach(record -> {
                    record.setManagerName(userMap.get(record.getManagerId()));
                    record.setServiceDispatchName(userMap.get(record.getServiceDispatchId()));
                    record.setLastOperateUserName(userMap.get(record.getLastOperateUserId()));
                    record.setDispatchRegionName(userAreaMap.get(record.getServiceDispatchId()));
                });
            }
        }
        return page;
    }

    @Override
    public Boolean whetherToShowMoreButtons(LoginUser loginUser) {
        String DATA_ALL = "data:all";
        String DATA_REGION = "data:region";
        String DATA_STORE = "data:store";
        List<Integer> roleIds = loginUser.getRoleIds();
        if (RoleEnum.SYS_ADMIN.hasRole(roleIds)
                || RoleEnum.BIZ_SUPER_ADMIN.hasRole(roleIds)
                || RoleEnum.CUSTOMER_SERVICE_SUPERVISOR.hasRole(roleIds)
                || RoleEnum.STORE_MANAGER.hasRole(roleIds)
                || RoleEnum.OPERATION_MANAGER.hasRole(roleIds)
                || RoleEnum.RISK_ASSIGN_ORDER.hasRole(roleIds)
                || RoleEnum.RISK_AMOUNT_APPROVE.hasRole(roleIds)
                || RoleEnum.RISK_AMOUNT_APPROVE_ONLINE.hasRole(roleIds)) {
            return true;
        }
        Integer userId = loginUser.getUserId();
        if (!envUtil.isPrd()) {
            if (ObjUtil.equals(userId, 11203)) {
                return true;
            }
        }
        if (ObjUtil.equals(userId, 1908) || ObjUtil.equals(userId, 1905) || ObjUtil.equals(userId, 11208)) {
            return true;
        }
        // 数据权限控制
        String scopes = loginUser.getScopes();
        return scopes != null && (scopes.contains(DATA_ALL) || scopes.contains(DATA_REGION) || scopes.contains(DATA_STORE));
    }

    @Override
    public Boolean clearStaleData() {
        List<OrderApproveDistributeEntity> orderApproveDistributeEntities = orderApproveDistributeMapper.selectJoinList(OrderApproveDistributeEntity.class, new MPJLambdaWrapper<OrderApproveDistributeEntity>()
                .innerJoin(OrderInfoEntity.class, e -> e.eq(OrderInfoEntity::getId, OrderApproveDistributeEntity::getOrderId)
                        .notIn(OrderInfoEntity::getCurrentNode, 1300, 1500, 1510, 3000, 3550))
                .notIn(OrderApproveDistributeEntity::getState, 3, 4));
        for (OrderApproveDistributeEntity orderApproveDistributeEntity : orderApproveDistributeEntities) {
            updateOrderApproveDistribute(new UpdateOrderApproveDistributeDTO().setCurrentNode(orderApproveDistributeEntity.getNode())
                    .setOrderId(orderApproveDistributeEntity.getOrderId())
                    .setOrderNumber(orderApproveDistributeEntity.getOrderNumber())
                    .setSource(0)
                    .setBusinessType(3));
        }
        return null;
    }

    @Override
    public TaskStatisticsVO taskStatistics(LoginUser loginUser) {
        TaskStatisticsVO taskStatisticsVO = null;
        try {
            StatisticsService statisticsStrategy = statisticsStrategyFactory.getStatisticsStrategy(StatisticsEnum.TASK_STATISTICS);
            taskStatisticsVO = statisticsStrategy.calculate(loginUser);
            log.info("OrderApproveDistributeServiceImpl.taskStatistics - taskStatisticsVO: {}", taskStatisticsVO);
        } catch (Exception e) {
            log.error("OrderApproveDistributeServiceImpl.taskStatistics e:", e);
        }
        return taskStatisticsVO;
    }

    @Override
    public void exportOrderApproveDistributeList(QueryOrderApproveDistributeListDTO queryOrderApproveDistributeListDTO, LoginUser loginUser, HttpServletResponse response) {
        queryOrderApproveDistributeListDTO.setPageNum(1);
        queryOrderApproveDistributeListDTO.setPageSize(-1);
        response.addHeader("charset", "utf-8");
        String fileName = String.format("派单明细报表-%s.xlsx", LocalDate.now());
        String encodeName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        Optional<MediaType> mediaTypeOptional = MediaTypeFactory.getMediaType(encodeName);
        MediaType mediaType = mediaTypeOptional.orElse(MediaType.APPLICATION_OCTET_STREAM);
        response.setContentType(String.valueOf(mediaType));
        response.setHeader("Content-disposition", "attachment;filename=" + encodeName);
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build()) {

            WriteSheet writeSheet = EasyExcel.writerSheet("派单明细")
                    .head(OrderApproveDistributeVO.class)
                    .build();
            List<OrderApproveDistributeVO> exportData = queryOrderApproveDistributeList(queryOrderApproveDistributeListDTO, loginUser).getRecords();
            List<Integer> orderIds = exportData.stream()
                    .flatMap(e -> Stream.of(e.getOrderId()))
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList();
            List<OrderInfoEntity> orderInfoEntities = orderInfoMapper.selectList(new MPJLambdaWrapper<OrderInfoEntity>().in(OrderInfoEntity::getId, orderIds).eq(OrderInfoEntity::getDeleteFlag, 0));
            Map<Integer, OrderInfoEntity> collect = orderInfoEntities.stream().collect(Collectors.toMap(OrderInfoEntity::getId, entity -> entity));
            //查询导出的订单所有风控初审、总评审核、风控终审、合同审批、资方放款节点的数据
            List<OrderNodeRecordEntity> orderNodeRecordEntities = orderNodeRecordMapper.selectList(new MPJLambdaWrapper<OrderNodeRecordEntity>()
                    .in(OrderNodeRecordEntity::getOrderId, orderIds)
                    .in(OrderNodeRecordEntity::getCurrentNode, States.RISK_FIRST_APPROVE.getNode(), States.OVERALL_REVIEW.getNode(), States.RISK_FINAL_APPROVE.getNode(), States.PAYMENT_CONTRACT_APPROVAL.getNode(), States.FUNDS_PAYMENT_APPROVAL.getNode())
                    .eq(OrderNodeRecordEntity::getDeleteFlag, 0));
            //根据订单id进行分组并且筛选出id最大的一条
            Map<Integer, Optional<OrderNodeRecordEntity>> orderNodeRecordEntityMap = orderNodeRecordEntities.stream().collect(Collectors.groupingBy(OrderNodeRecordEntity::getOrderId, Collectors.maxBy(Comparator.comparing(OrderNodeRecordEntity::getId))));
            exportData = exportData.stream().peek(vo ->
            {
                vo.setStateName(switch (vo.getState()) {
                    case 0 -> "待分配";
                    case 1 -> "已分配";
                    case 2 -> "已查看";
                    case 3 -> "已处理";
                    case 4 -> "废弃";
                    case 5 -> "挂起";
                    default -> "未知";
                });
                vo.setNodeName(States.getByNode(vo.getNode()).getDesc());
                vo.setSourceName(ObjUtil.equals(vo.getSource(), 1) ? "数字化" : "龙金云启");
                vo.setApprovalAmount(collect.get(vo.getOrderId()) != null ? collect.get(vo.getOrderId()).getApprovalAmount() : BigDecimal.ZERO);
                vo.setPaymentTime(collect.get(vo.getOrderId()) != null ? collect.get(vo.getOrderId()).getPaymentTime() : null);
                if (collect.get(vo.getOrderId()) != null) {
                    OrderInfoEntity orderInfoEntity = collect.get(vo.getOrderId());
                    if (Objects.equals(States.RISK_FIRST_APPROVE.getNode(), orderInfoEntity.getCurrentNode()) ||
                            Objects.equals(States.OVERALL_REVIEW.getNode(), orderInfoEntity.getCurrentNode()) ||
                            Objects.equals(States.RISK_FINAL_APPROVE.getNode(), orderInfoEntity.getCurrentNode()) ||
                            Objects.equals(States.PAYMENT_CONTRACT_APPROVAL.getNode(), orderInfoEntity.getCurrentNode()) ||
                            Objects.equals(States.FUNDS_PAYMENT_APPROVAL.getNode(), orderInfoEntity.getCurrentNode())) {
                        vo.setOrderProgress(States.getByNode(orderInfoEntity.getCurrentNode()).getDesc());
                        vo.setOrderStatus( switch (orderInfoEntity.getState()) {
                            case 5000, 8000 -> "审核通过";
                            case -1000, -2000 -> "拒绝";
                            default -> "待审核";
                        });
                    } else {
                        vo.setOrderProgress(orderNodeRecordEntityMap.get(vo.getOrderId()) != null && orderNodeRecordEntityMap.get(vo.getOrderId()).isPresent() ?
                                States.getByNode(orderNodeRecordEntityMap.get(vo.getOrderId()).get().getCurrentNode()).getDesc() : "");
                        if (orderNodeRecordEntityMap.get(vo.getOrderId()) != null && orderNodeRecordEntityMap.get(vo.getOrderId()).isPresent()) {
                            OrderNodeRecordEntity orderNodeRecordEntity = orderNodeRecordEntityMap.get(vo.getOrderId()).get();
                            if (orderNodeRecordEntity.getEvent().getDesc().contains("驳回")) {
                                vo.setOrderStatus("驳回");
                            } else if (Objects.equals(Events.CANCEL.getCode(), orderNodeRecordEntity.getEvent().getCode()) ||
                                    Objects.equals(Events.FINISH.getCode(), orderNodeRecordEntity.getEvent().getCode()) ||
                                    Objects.equals(Events.FAIL.getCode(), orderNodeRecordEntity.getEvent().getCode()) ||
                                    Objects.equals(Events.SYSTEM_TERMINAL.getCode(), orderNodeRecordEntity.getEvent().getCode()) ||
                                    Objects.equals(Events.RESIGN_CONTRACT.getCode(), orderNodeRecordEntity.getEvent().getCode()) ||
                                    Objects.equals(Events.REBIND_BLANK_CARD.getCode(), orderNodeRecordEntity.getEvent().getCode())) {
                                vo.setOrderStatus("拒绝");
                            } else if (orderNodeRecordEntity.getEvent().getDesc().contains("通过") || orderNodeRecordEntity.getEvent().getDesc().contains("完成")){
                                vo.setOrderStatus("审核通过");
                            } else {
                                vo.setOrderStatus(orderNodeRecordEntity.getEvent().getDesc());
                            }
                        }
                    }
                }
            }).toList();
            excelWriter.write(exportData, writeSheet);
        } catch (IOException e) {
            log.error("exportOrderApproveDistributeList.exportExcel error", e);
        }
    }

    @Override
    public String updateAreaTypeToRegionId(QueryOrderApproveDistributeListDTO dto) {
        List<DistributeAreaEntity> distributeAreaEntities = distributeAreaMapper.selectList(
                new LambdaQueryWrapper<DistributeAreaEntity>().eq(DistributeAreaEntity::getDeleteFlag, 0)
                        .notIn(DistributeAreaEntity::getStoreName,List.of("岳阳门店", "聊城门店", "南充门店", "延安门店", "榆林门店", "鄂尔多斯门店"))
        );
        distributeAreaMapper.update(new LambdaUpdateWrapper<DistributeAreaEntity>()
                .set(DistributeAreaEntity::getDeleteFlag, 1)
                .in(DistributeAreaEntity::getStoreName, List.of("岳阳门店", "聊城门店", "南充门店", "延安门店", "榆林门店", "鄂尔多斯门店")));

        for (DistributeAreaEntity entity : distributeAreaEntities){
            //查询有storeId有值的门店
            if (entity.getStoreId() != null){
                DeptDetailDTO detailDTO = new DeptDetailDTO().setDeptId(entity.getStoreId());
                Result<DeptDetailVO>  deptResult =  userFeign.getDeptDetail(detailDTO);
                if (Result.isSuccess(deptResult)){
                    DeptDetailVO deptDetailVO = deptResult.getData();
                    distributeAreaMapper.update(new LambdaUpdateWrapper<DistributeAreaEntity>()
                            .set(DistributeAreaEntity::getAreaType, deptDetailVO.getParentId())
                            .eq(DistributeAreaEntity::getId, entity.getId()));
                }
            }else {
                //查询分部
                String storeName = entity.getStoreName();
                int index = storeName.indexOf("门店");
                if (index != -1) {
                    String result = storeName.substring(0, index) + "门店";
                    List<DistributeAreaEntity> distributeAreaEntityList = distributeAreaMapper.selectList(new LambdaQueryWrapper<DistributeAreaEntity>()
                            .eq(DistributeAreaEntity::getStoreName, result)
                            .eq(DistributeAreaEntity::getDeleteFlag, 0)
                    );
                    if (CollUtil.isNotEmpty(distributeAreaEntityList) && distributeAreaEntityList.size() == 1){
                        DistributeAreaEntity upArea = distributeAreaEntityList.get(0);

                        DeptDetailDTO detailDTO = new DeptDetailDTO().setDeptId(upArea.getStoreId());
                        Result<DeptDetailVO>  deptResult =  userFeign.getDeptDetail(detailDTO);
                        if (Result.isSuccess(deptResult)){
                            DeptDetailVO deptDetailVO = deptResult.getData();
                            distributeAreaMapper.update(new LambdaUpdateWrapper<DistributeAreaEntity>()
                                    .set(DistributeAreaEntity::getAreaType, deptDetailVO.getParentId())
                                    .eq(DistributeAreaEntity::getId, entity.getId()));
                        }
                    }
                }
            }
        }
        return "成功";
    }
}
