package com.longhuan.order.statemachine.aguard;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.longhuan.order.mapper.OrderInfoMapper;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.service.OrderFileService;
import com.longhuan.order.statemachine.StateConstants;
import com.longhuan.order.statemachine.enums.Events;
import com.longhuan.order.statemachine.enums.States;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.guard.Guard;
import org.springframework.stereotype.Component;

/**
 * 付款申请
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PaymentApplyGuard implements Guard<States, Events> {

    private final OrderInfoMapper orderInfoMapper;

    private final OrderFileService orderFileService;

    @Override
    public boolean evaluate(StateContext<States, Events> context) {
        Integer orderId = context.getMessageHeaders().get(StateConstants.ORDER_ID, Integer.class);

        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getId, OrderInfoEntity::getPaymentType, OrderInfoEntity::getMortgageState)
                .eq(OrderInfoEntity::getId, orderId));

        Integer paymentType = Convert.toInt(orderInfoEntity.getPaymentType(), 0);

        Integer mortgageState = Convert.toInt(orderInfoEntity.getMortgageState(), 0);
        log.info("payment_apply approve paymentType: {} ,mortgageState: {}", paymentType, mortgageState);
        if (paymentType == 1 && mortgageState == 1) {
            return true;
        } else if (paymentType == 2) {

            return orderFileService.isExistMortgageReceiptFile(orderId);
        } else {
            return false;
        }
    }
}