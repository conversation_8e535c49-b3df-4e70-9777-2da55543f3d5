package com.longhuan.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.FileToFddDTOListDTO;
import com.longhuan.order.pojo.vo.FileToFddNotDeleteVo;
import com.longhuan.order.service.FileToFddService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 上传到法大大文件信息控制器
 *
 * <AUTHOR>
 * @Date 2024/8/5
 */
@RestController
@RequestMapping("/api/v1/fileToFdd")
@RequiredArgsConstructor
public class FileToFddController {
    private final FileToFddService fileToFddService;

    /**
     * 获取未删除的法大大文件信息列表
     * @return 未删除的法大大文件信息列表
     */
    @PostMapping("/listNotDelete")
    public Result<IPage<FileToFddNotDeleteVo>> listNotDelete(@RequestBody FileToFddDTOListDTO dto) {
        return Result.success(fileToFddService.listNotDelete(dto));
    }

    /**
     * 删除法大大文件信息
     *
     * @param id 文件ID
     * @return 删除结果
     */
    @PostMapping("/deleteById/{id}")
    public Result<Boolean> deleteById(@PathVariable("id") Integer id) {
        return Result.success(fileToFddService.deleteById(id));
    }
}
