package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.enums.OrderFeeDetailExpandTypeEnum;
import com.longhuan.common.core.enums.OrderFeeDetailStatusEnum;
import com.longhuan.common.core.enums.PayApplicationNodeEnums;
import com.longhuan.common.core.enums.PayApplicationPayeeTypeEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.web.annotation.CurrentUser;
import com.longhuan.order.pojo.dto.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.OrderPayApplicationPriveteMethod;
import com.longhuan.order.service.OrderPayApplicationService;
import com.longhuan.resource.pojo.vo.FileVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * 付款单申请
 *
 * <AUTHOR>
 * @date 2024/10/30
 */
@Api(tags = "付款单申请")
@RestController
@RequestMapping("/api/v1/payApplication")
@RequiredArgsConstructor
public class OrderPayApplicationController {

    private final OrderPayApplicationService orderPayApplicationService;

	private  final OrderPayApplicationPriveteMethod orderPayApplicationPriveteMethod;

    @ApiOperation(value = "付款单申请")
    @PostMapping("/orderPayApplication")
    Result<Boolean> submitMortgage(@RequestBody @Validated PayApplicationDTO payApplicationDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderPayApplicationService.orderPayApplication(payApplicationDTO,loginUser));
    }

    @ApiOperation(value = "钉钉发起失败重试")
    @GetMapping("/retryDingTask")
    Result<Boolean> approvePayApplication() {
        orderPayApplicationService.retryDingTask();
        return Result.success(true);
    }

    @ApiOperation(value = "付款单审批")
    @PostMapping("/approvePayApplication")
    Result<Boolean> approvePayApplication(@RequestBody @Validated List<ApprovalPayApplicationDTO> approvalPayApplicationDTOs, @CurrentUser LoginUser loginUser) {
        return Result.success(orderPayApplicationService.approvePayApplication(approvalPayApplicationDTOs, loginUser.getUserId(), false));
    }
    @ApiOperation(value = "审批记录")
    @PostMapping("/approveRecordPage")
    Result<Page<OrderPayApplyNodeRecordListVO>> approveRecordPage(@RequestBody @Validated PayRecordDTO dto) {
        return Result.success(orderPayApplicationService.getApproveRecordPage(dto));
    }

    @ApiOperation(value = "钉钉费用申请审批")
    @GetMapping("/approvePayApplicationDingTaskBatch")
    Result<Boolean> approvePayApplicationDingTask() {
        orderPayApplicationService.batchDingTaskApprove();
        return Result.success(true);
    }

    @ApiOperation(value = "获取转对公申请")
    @PostMapping("/getOfflinePayApplication")
    Result<OrderPayApplicationOfflineVO> getOfflinePayApplication(@RequestBody @Validated SearchOrderPayOfflineDTO dto) {
        return Result.success(orderPayApplicationService.getOfflinePayApplication(dto));
    }

    @ApiOperation(value = "获取可用订单")
    @PostMapping("/searchOptionalOrder")
    Result<Page<OrderPayApplyListVO>> searchOptionalOrder(@RequestBody SearchOptionalOrderDTO searchOptionalOrderDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderPayApplicationService.searchOptionalOrder(searchOptionalOrderDTO, loginUser));
    }

    @ApiOperation(value = "根据id获取信息")
    @GetMapping("/getDetailById/{id}")
    Result<OrderPayApplicationVO> getDetailById(@PathVariable("id") Integer id) {
        return Result.success(orderPayApplicationService.getDetailById(id));
    }

    @ApiOperation(value = "根据订单id获取客户账户信息")
    @GetMapping("/getCustomerAccount/{orderId}")
    Result<List<OrderPayAccountVO>> getCustomerAccountByOrderId(@PathVariable("orderId") Integer orderId) {
        return Result.success(orderPayApplicationService.getCustomerAccountByOrderId(orderId));
    }

    @ApiOperation(value = "付款申请审批列表合计")
    @PostMapping("/payApplicationPageListTotal")
    Result<PayApplicationPageListVO> payApplicationPageListTotal(@RequestBody @Validated PayApplicationPageListDTO payApplicationDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderPayApplicationService.payApplicationPageListTotal(payApplicationDTO, loginUser));
    }
    @ApiOperation(value = "付款申请审批列表")
    @PostMapping("/payApplicationPageList")
    Result<Page<OrderPayApplyListVO>> submitMortgage(@RequestBody @Validated PayApplicationPageListDTO payApplicationDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderPayApplicationService.payApplicationPageList(payApplicationDTO, loginUser));
    }
    @ApiOperation(value = "付款申请审批列表导出")
    @PostMapping("/payApplicationPageListExport")
    void payApplicationPageListExport(@RequestBody @Validated PayApplicationPageListDTO payApplicationDTO,
                                      @CurrentUser LoginUser loginUser,
                                      HttpServletResponse response
    ) {
        orderPayApplicationService.payApplicationPageListExport(payApplicationDTO, loginUser,response);
    }



    @ApiOperation(value = "自动提交绩效审核定时任务")
    @PostMapping("/automaticSubmit")
    Result<String> automaticSubmit() {
        return Result.success(orderPayApplicationService.automaticSubmit());
    }

    @ApiOperation(value = "自动提交绩效导出")
    @PostMapping("/exportPayApplyExcel")
    Result<List<FileVO>> exportPayApplyExcel(@RequestBody @Validated PayApplicationPageListDTO payApplicationDTO, @CurrentUser LoginUser loginUser) {
        return Result.success(orderPayApplicationService.exportPayApplyExcel(payApplicationDTO, loginUser));
    }

    @ApiOperation(value = "自动提交赎回审核定时任务")
    @PostMapping("/auto/repurchaseRepay")
    Result<Boolean> automatedRepurchaseRepay() {
        return Result.success(orderPayApplicationService.automatedRepurchaseRepay());
    }
    @ApiOperation(value = "富民赎回自动提交审核")
    @PostMapping("/fuMinRepurchaseCallback")
    public Result<Boolean> fuMinRepurchaseCallback(@RequestBody FuMinRepurchaseCallbackDTO dto){
        return Result.success(orderPayApplicationService.fuMinRepurchaseCallback(dto.getOrderId(),dto.getActuallyTotalAmount()));
    }
    @ApiOperation(value = "车务费导出")
    @PostMapping("/exportCarServiceFee")
    public void exportCarServiceFee(@RequestBody PayApplicationPageListDTO payApplicationDTO, @CurrentUser LoginUser loginUser, HttpServletResponse response){
        orderPayApplicationService.exportCarServiceFee(payApplicationDTO, loginUser, response);
    }

    @ApiOperation(value = "修改收款人账户")
    @PostMapping("/editPayersAccount")
    Result<Boolean> editPayersAccount(@RequestBody  OrderPayApplicationDTO orderPayApplicationDTO) {
        return Result.success(orderPayApplicationService.editPayersAccount(orderPayApplicationDTO));
    }

    @ApiOperation(value = "赎回费导出")
    @PostMapping("/exportRedeemFee")
    Result<List<FileVO>> exportRedeemFee(@RequestBody PayApplicationPageListDTO payApplicationDTO, @CurrentUser LoginUser loginUser) {
        return Result.success( orderPayApplicationService.exportRedeemFee(payApplicationDTO,  loginUser));
    }


    /**
     * 长银预代偿生产申请记录
     * @param fundInPreIdemnityDTOList
     * @return
     */
    @ApiOperation(value = "长银预代偿生产申请记录")
    @PostMapping("/changyinPreIndemnityBatchApply")
    Result<Boolean> changyinPreIndemnityBatchApply(@RequestBody @Validated List<FundIndemnityDTO> fundInPreIdemnityDTOList) {
        return Result.success(orderPayApplicationService.changyinPreIndemnityBatchApply(fundInPreIdemnityDTOList));
    }
    /**
     * 通汇退费申请
     */
    @ApiOperation(value = "通汇退费申请")
    @PostMapping("/thRefundApply")
    Result<Boolean> thRefundApply(@RequestBody ThRefundApplyDTO dto,@CurrentUser LoginUser loginUser) {
        return Result.success(orderPayApplicationService.thRefundApply(dto,loginUser));
    }
    /**
     * 蓝海赎回自动提交审核
     */
    @ApiOperation(value = "蓝海赎回自动提交审核")
    @PostMapping("lanHaiRepurchaseRepay")
    Result<Boolean> lanHaiRepurchaseRepay(@RequestParam(name = "orderId", required = false) Integer orderId,
                                           @RequestParam(name = "actuallyTotalAmount", required = false) BigDecimal actuallyTotalAmount) {
        return Result.success(orderPayApplicationService.lanHaiRepurchaseRepay(orderId,actuallyTotalAmount));
    }
    /**
     * 委外直接结清（禁止滥用）
     */
    @ApiOperation(value = "委外直接结清（禁止滥用）")
    @PostMapping("directSettle")
    Result<Boolean> directSettle(@RequestBody List<String> orderNumbers) {
        for (String orderNumber : orderNumbers) {
            orderPayApplicationService.directSettle(orderNumber);
        }
        return  Result.success(true);
    }
    /**
     * 获取特殊退款申请
     */
    @ApiOperation(value = "获取特殊退款申请")
    @PostMapping("/getSpecialRefundApply")
    Result<SpecialRefundApplyVO> getSpecialRefundApply(@RequestBody SpecialRefundApplyDTO dto) {
        return Result.success(orderPayApplicationService.getSpecialRefundApply(dto));
    }
    /**
     * 费用申请导出
     */
    @ApiOperation(value = "费用申请导出")
    @PostMapping("/expenseApplicationExport")
    void expenseApplicationExport(
            @RequestBody @Validated ExpenseApplicationExportDTO dto,
            @CurrentUser LoginUser loginUser,
            HttpServletResponse response
    ) {
        orderPayApplicationService.expenseApplicationExport(dto,loginUser,response);
    }
    /**
     * 查账申请自动认账
     */
    @ApiOperation(value = "查账申请自动认账")
    @PostMapping("/autoRecognize")
    Result<Boolean> autoRecognize(@RequestParam("file") MultipartFile file) {
        return Result.success(orderPayApplicationService.autoRecognize(file));
    }
    /**
     * 自动认账导出
     */
    @ApiOperation(value = "自动认账导出")
    @PostMapping("/autoRecognizeExport")
    public void autoRecognizeExport(HttpServletResponse response) {
        orderPayApplicationService.autoRecognizeExport(response);
    }

	/**
	 * 根据收款方类型和当前节点确定下一个节点
	 */
	@ApiOperation(value = "根据收款方类型和当前节点确定下一个节点")
	@PostMapping("/getNextNode")
	public Result<PayApplicationNodeEnums> getNextNode(@RequestBody GetNextNodeDTO dto) {
		return  Result.success(orderPayApplicationPriveteMethod.getNextNode(dto.getFeeType(),dto.getApplyType(),dto.getPayeeType(),dto.getCurrentNode()));
	}

	/**
	 * 保存审核节点
	*/
	@ApiOperation(value = "保存审批节点")
	@PostMapping("/saveAuditNode")
	public Result<Void> saveAuditNode(@RequestBody SaveAuditNodeDTO dto) {
		orderPayApplicationPriveteMethod.saveNodeRecord(dto.getApplyInfoId(),dto.getCurrentNode(),dto.getNextNode()
				,dto.getAuditType(),dto.getProcessId(),dto.getRemark(),dto.getEvent(),dto.getCurrentUserId(),dto.getApproveTime());
		return Result.success();
	}

}
