package com.longhuan.order.feign;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.longhuan.order.OrderApplication;
import com.longhuan.order.pojo.dto.TuQiangGetTokenDTO;
import com.longhuan.order.util.TuQiangMD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@SpringBootTest(classes = OrderApplication.class)
class TuQiangOLFeignTest {
    @Autowired
    TuQiangOLFeign tuQiangOLFeign;
    @Value("${tuqiangol.appKey}")
    private String tqappKey;
    @Value("${tuqiangol.primaryAccount}")
    private String tqAccount;
    @Value("${tuqiangol.primaryPwd}")
    private String tqPwd;
    @Test
    public void getToken() {
        TuQiangGetTokenDTO tuQiangGetTokenDTO = new TuQiangGetTokenDTO();
        tuQiangGetTokenDTO
        .setApp_key(tqappKey)
                .setUser_id(tqAccount)
                .setUser_pwd_md5( DigestUtils.md5Hex(tqPwd))
                .setExpires_in(3600)
                .setTimestamp(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .setMethod("jimi.oauth.token.get")
                .setV("1.0")
                .setSign_method("md5")
                .setFormat("json");
        Map<String,Object> stringObjectMap = BeanUtil.beanToMap(tuQiangGetTokenDTO,new HashMap<>(), CopyOptions.create().ignoreNullValue());
        try {
            String sign=TuQiangMD5Utils.signTopRequest(stringObjectMap,"7c715e62a22e450fa0110f9c5021fa08", "md5");
            stringObjectMap.put("sign",sign);

        } catch (IOException e) {
        }
        tuQiangOLFeign.routeRest(stringObjectMap);
    }

    private void getTuQiangResult(Object Dto){
        Map<String,Object> stringObjectMap = BeanUtil.beanToMap(Dto);
        try {
            String sign=TuQiangMD5Utils.signTopRequest(stringObjectMap,"secret", "md5");
            stringObjectMap.put("sign",sign);

        } catch (IOException e) {
        }
        tuQiangOLFeign.routeRest(stringObjectMap);
    }
}