package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.github.yulichang.wrapper.segments.Fun;
import com.longhuan.approve.api.pojo.dto.FundApprovePreDTO;
import com.longhuan.approve.api.pojo.dto.TongHuiInitiateRefundDTO;
import com.longhuan.approve.api.pojo.vo.TongHuiInitiateRefundVO;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.enums.*;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.redis.constants.SwitchConstants;
import com.longhuan.common.redis.pojo.LoginUser;
import com.longhuan.common.redis.pojo.SwitchVO;
import com.longhuan.common.redis.util.SwitchUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.enums.*;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.feign.UserFeign;
import com.longhuan.order.kingdee.feign.KingdeeFeign;
import com.longhuan.order.kingdee.pojo.TheFinalReviewRejectedAllOfThemDTO;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.FinalApproveFundStatusDTO;
import com.longhuan.order.pojo.dto.OrderApproveDTO;
import com.longhuan.order.pojo.dto.PayApplicationPageListDTO;
import com.longhuan.order.pojo.dto.ThRefundApplyDTO;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.*;
import com.longhuan.order.service.DataPermissionService;
import com.longhuan.order.service.FundRefundInfoEntityService;
import com.longhuan.order.service.OrderDetailsInfoService;
import com.longhuan.order.service.RepaymentService;
import com.longhuan.order.statemachine.enums.States;
import com.longhuan.order.util.SignatureUtils;
import com.longhuan.user.pojo.vo.DeptInfoVO;
import com.longhuan.user.pojo.vo.UserInfoVO;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
@Slf4j
public class OrderDetailsInfoServiceImpl implements OrderDetailsInfoService {

    private final PreFundInfoMapper preFundInfoMapper;
    @Value("${kingdee.datapush.signature.bodySecret}")
    private String bodySecret;
    @Value("${kingdee.datapush.signature.key}")
    private String key;
    private final Car300DataMapper car300DataMapper;
    private final PreApprovalApplyInfoMapper preApprovalApplyInfoMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final FundDeptMapper fundDeptMapper;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final ApproveFeign approveFeign;
    private final KingdeeFeign kingdeeFeign;
    private final DataPermissionService dataPermissionService;
    private final OrderPayApplicationMapper orderPayApplicationMapper;
    private final UserFeign userFeign;
    private final OrderNodeRecordMapper orderNodeRecordMapper;
    private final RepaymentService repaymentService;
    private final OrderAmountMapper orderAmountMapper;
    private final FundRefundInfoEntityService fundRefundInfoService;
    private final OrderPayApplyNodeRecordMapper orderPayApplyNodeRecordMapper;
    private final RiskAiIntelligentAuditMapper riskAiIntelligentAuditMapper;
    private final IntelligentRiskInfoEntityMapper intelligentRiskInfoEntityMapper;
    private final SwitchUtils switchUtils;
    private final RiskFundAutoConfigMapper riskFundAutoConfigMapper;
    @Override
    public void detailsByOrderId(OrderDetailsVo vo) {

        Car300DataEntity car300DataEntity = car300DataMapper.selectOne(
                new LambdaQueryWrapper<Car300DataEntity>()
                        .eq(Car300DataEntity::getVin, vo.getVin())
                        .eq(Car300DataEntity::getDeleteFlag, 0)
                        .orderByDesc(Car300DataEntity::getCreateTime)
                        .last("LIMIT 1")
        );
        if (ObjUtil.isNotEmpty(car300DataEntity)){
            vo.setVehicleModel(ObjUtil.isNotEmpty(car300DataEntity.getModelName()) ? car300DataEntity.getModelName() : vo.getVehicleModel());
        }
    }

    @Override
    public OrderHomeDataVO selectHomeData(LoginUser loginUser) {
        Assert.notNull(loginUser, "用户信息不能为空");
        Integer userId = loginUser.getUserId();

        // 进件量：目前为客户经理为当前登录人且预审批状态为通过的订单数量
        Long allIncomingNum = preApprovalApplyInfoMapper.selectCount(new LambdaQueryWrapper<PreApprovalApplyInfoEntity>()
                .eq(PreApprovalApplyInfoEntity::getAccountManagerId, userId)
                .eq(PreApprovalApplyInfoEntity::getManagerState, 2)
                .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0));

        // 放款订单量：目前为客户经理为当前登录人且订单状态为放款成功的订单数量
        List<OrderInfoEntity> orderInfoEntities = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getManagerId, userId)
                .eq(OrderInfoEntity::getCurrentNode, States.PAYMENT_SUCCESS.getNode())
                .eq(OrderInfoEntity::getDeleteFlag, 0));

        // 总放款额：目前为客户经理为当前登录人且订单状态为放款成功的订单客户确认金额总和
        BigDecimal totalApprovalAmount = orderInfoEntities.stream()
                .map(order -> Optional.ofNullable(order.getApprovalAmount()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        OrderHomeDataVO orderHomeDataVO = new OrderHomeDataVO();
        orderHomeDataVO.setAllIncomingNum(allIncomingNum);
        orderHomeDataVO.setAllLoanNum(orderInfoEntities.size());
        orderHomeDataVO.setAllLoanAmount(totalApprovalAmount);
        return orderHomeDataVO;
    }

    @Override
    public void rejectSave(FinalApproveFundStatusDTO fundStatusDTO) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(fundStatusDTO.getOrderId());
        Assert.notNull(orderInfoEntity, "订单不存在");
        if (Objects.equals(fundStatusDTO.getStatus(), PreFundResultEnum.REJECT) && Objects.equals(orderInfoEntity.getSourceType(), 1) && (Objects.equals(orderInfoEntity.getRegionId(), 24) || Objects.equals(orderInfoEntity.getRegionId(), 56))) {
            //获取电销所有的资方
            List<PreFundInfoEntity> preFundInfoEntityList = preFundInfoMapper.selectList(
                    new LambdaQueryWrapper<PreFundInfoEntity>()
                            .eq(PreFundInfoEntity::getPreId, orderInfoEntity.getPreId())
                            .eq(PreFundInfoEntity::getDeleteFlag, 0)
                            .orderByAsc(PreFundInfoEntity::getLevel)
                            .orderByAsc(PreFundInfoEntity::getCreateTime)
            );
            TheFinalReviewRejectedAllOfThemDTO dto =new TheFinalReviewRejectedAllOfThemDTO();
            dto.setOrderId(fundStatusDTO.getOrderId());
            //终审是否全部拒绝 1是 2否
            dto.setFinalApprovalStatus(2);
            //如果只有一个资方
            if (Objects.equals(preFundInfoEntityList.size(), 1)) {
                if (Objects.equals(fundStatusDTO.getStatus(), PreFundResultEnum.REJECT)) {
                    dto.setFinalApprovalStatus(1);
                    dto.setFundId(fundStatusDTO.getFundId());
                    dto.setPreId(orderInfoEntity.getPreId());
                    theFinalReviewRejectedAllOfThem(dto);
                    preApprovalApplyInfoMapper.update(new LambdaUpdateWrapper<PreApprovalApplyInfoEntity>()
                            .set(PreApprovalApplyInfoEntity::getManagerState, PreApplyInfoManagerStatus.APPROVAL_FAIL)
                            .eq(PreApprovalApplyInfoEntity::getId, orderInfoEntity.getPreId())
                            .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
                    );
                }
            } else {
                //如果资方全部拒绝
                if (preFundInfoEntityList.stream().allMatch(entity -> Objects.equals(entity.getResult(),  RiskPolicyResult.REJECT))) {
                    dto.setFinalApprovalStatus(1);
                    dto.setFundId(fundStatusDTO.getFundId());
                    dto.setPreId(orderInfoEntity.getPreId());
                    theFinalReviewRejectedAllOfThem(dto);
                    preApprovalApplyInfoMapper.update(new LambdaUpdateWrapper<PreApprovalApplyInfoEntity>()
                            .set(PreApprovalApplyInfoEntity::getManagerState, PreApplyInfoManagerStatus.APPROVAL_FAIL)
                            .eq(PreApprovalApplyInfoEntity::getId, orderInfoEntity.getPreId())
                            .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
                    );
                } else {
                    List<PreFundInfoEntity> filteredAndSortedList = preFundInfoEntityList.stream()
                            .filter(entity -> Objects.equals(entity.getResult(),  RiskPolicyResult.PASS)) // 过滤 result = 1 的数据
                            .sorted(Comparator.comparing(PreFundInfoEntity::getLevel)
                                    .thenComparing(PreFundInfoEntity::getCreateTime)) // 按 level 和 createTime 升序排序
                            .toList();
                    Optional<Integer> indexOpt = IntStream.range(0, filteredAndSortedList.size())
                            .filter(i -> Objects.equals(filteredAndSortedList.get(i).getFundId(), orderInfoEntity.getFundId()))
                            .boxed()
                            .findFirst();
                    if (indexOpt.isPresent()) {
                        int index = indexOpt.get();
                        if (Objects.equals(filteredAndSortedList.size(),index)){
                            dto.setFinalApprovalStatus(1);
                            dto.setFundId(fundStatusDTO.getFundId());
                            dto.setPreId(orderInfoEntity.getPreId());
                            theFinalReviewRejectedAllOfThem(dto);
                            preApprovalApplyInfoMapper.update(new LambdaUpdateWrapper<PreApprovalApplyInfoEntity>()
                                    .set(PreApprovalApplyInfoEntity::getManagerState, PreApplyInfoManagerStatus.APPROVAL_FAIL)
                                    .eq(PreApprovalApplyInfoEntity::getId, orderInfoEntity.getPreId())
                                    .eq(PreApprovalApplyInfoEntity::getDeleteFlag, 0)
                            );
                        }else {
                            dto.setFinalApprovalStatus(2);
                            dto.setFundId(fundStatusDTO.getFundId());
                            dto.setPreId(orderInfoEntity.getPreId());
                            theFinalReviewRejectedAllOfThem(dto);
                            PreFundInfoEntity preFundInfoEntity = filteredAndSortedList.get(index + 1);
                            approveFeign.fundApprovePre(new FundApprovePreDTO()
                                    .setFund(FundEnum.getFundEnum(preFundInfoEntity.getFundId()))
                                    .setPreId(orderInfoEntity.getPreId())
                            );
                        }
                    }else {
                        dto.setFinalApprovalStatus(2);
                        dto.setFundId(fundStatusDTO.getFundId());
                        dto.setPreId(orderInfoEntity.getPreId());
                        theFinalReviewRejectedAllOfThem(dto);
                        PreFundInfoEntity preFundInfoEntity = filteredAndSortedList.get(0);
                        approveFeign.fundApprovePre(new FundApprovePreDTO()
                                .setFund(FundEnum.getFundEnum(preFundInfoEntity.getFundId()))
                                .setPreId(orderInfoEntity.getPreId())
                        );
                    }
                }
            }
        }
    }

    @Override
    public void getVehicleSupplementInfo(OrderVehicleInfoEntity orderVehicleInfoEntity) {
        if (isNumeric(orderVehicleInfoEntity.getFuelType())){
            Car300DataEntity car300DataEntity = car300DataMapper.selectOne(
                    new LambdaQueryWrapper<Car300DataEntity>()
                            .eq(Car300DataEntity::getVin, orderVehicleInfoEntity.getVin())
                            .eq(Car300DataEntity::getDeleteFlag, 0)
                            .ge(Car300DataEntity::getCreateTime, LocalDateTime.now().minusDays(30))
                            .orderByDesc(Car300DataEntity::getCreateTime)
                            .last(" limit 1")
            );
            if (ObjUtil.isNotEmpty(car300DataEntity)){
                if (ObjUtil.isNotEmpty(car300DataEntity.getGreenType()) && !Objects.equals(car300DataEntity.getGreenType(),"0") && ObjUtil.isNotEmpty(car300DataEntity.getVehicleType()) && Objects.equals(car300DataEntity.getVehicleType(), 1)){
                    switch (car300DataEntity.getGreenType()){
                        case "0":
                            orderVehicleInfoEntity.setFuelType("不适用");
                            break;
                        case "1":
                            orderVehicleInfoEntity.setFuelType("纯电动");
                            break;
                        case "2":
                            orderVehicleInfoEntity.setFuelType("插电式混动");
                            break;
                        case "4":
                            orderVehicleInfoEntity.setFuelType("增程式混动");
                            break;
                        case "8":
                            orderVehicleInfoEntity.setFuelType("燃料电池");
                            break;
                    }
                }else {
                    switch (car300DataEntity.getFuelType()){
                        case "0":
                            orderVehicleInfoEntity.setFuelType("汽油");
                            break;
                        case "1":
                            orderVehicleInfoEntity.setFuelType("柴油");
                            break;
                        case "2":
                            orderVehicleInfoEntity.setFuelType("电力");
                            break;
                        case "3":
                            orderVehicleInfoEntity.setFuelType("油电");
                            break;
                        case "4":
                            orderVehicleInfoEntity.setFuelType("油气");
                            break;
                        case "5":
                            orderVehicleInfoEntity.setFuelType("天然气");
                            break;
                        case "6":
                            orderVehicleInfoEntity.setFuelType("氢能源");
                            break;
                        case "7":
                            orderVehicleInfoEntity.setFuelType("甲醇");
                            break;
                        default: orderVehicleInfoEntity.setFuelType("未知");
                    }
                }
            }
        }
    }

    @Override
    public void payApplicationPageListExport(PayApplicationPageListDTO payApplicationPageListDTO, LoginUser loginUser, HttpServletResponse response) {
        log.info("OrderPayApplicationServiceImpl payApplicationPageList start payApplicationPageListDTO:{}", JSONUtil.toJsonStr(payApplicationPageListDTO));
        PayApplicationNodeEnums currentNode = payApplicationPageListDTO.getCurrentNode();
        MPJLambdaWrapper<OrderPayApplicationInfoEntity> queryWrapper = new MPJLambdaWrapper<OrderPayApplicationInfoEntity>()
                .select(OrderInfoEntity::getManagerId,
                        OrderInfoEntity::getTeamId)
                .selectAs(OrderInfoEntity::getId, PayApplicationPageListExportVO::getOrderId)
                .selectAs(OrderFeeInfoEntity::getPaySnNumBack, PayApplicationPageListExportVO::getPaySnNumBack)
                .select(
                        "CASE WHEN t.order_source = 1 THEN t1.vehicle_number ELSE t3.vehicle_number END AS vehicleNumber",
                        "CASE WHEN t.order_source = 1 THEN t1.store_name ELSE t3.store_name END AS storeName",
                        "CASE WHEN t.order_source = 1 THEN t1.customer_name ELSE t3.customer_name END AS customerName",
                        "CASE WHEN t.order_source = 1 THEN t1.order_number ELSE t3.order_id END AS orderNumber",
                        "CASE WHEN t.order_source = 1 THEN t1.region_name ELSE t3.region_name END AS regionName",
                        "CASE WHEN t.order_source = 1 THEN t1.fund_name ELSE t3.fund_name END AS fundName",
                        "CASE WHEN t.order_source = 1 THEN t1.product_name ELSE t3.product_name END AS productName"
                )
                .selectAs(DigitalOutsourcingOrderEntity::getBusinessManager, PayApplicationPageListExportVO::getManagerName)

                .selectAs(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationPageListExportVO::getCurrentNode)
                .selectAs(OrderPayApplicationInfoEntity::getPaymentDetails, PayApplicationPageListExportVO::getPaymentDetails)
                .selectAs(OrderPayApplicationInfoEntity::getPayAccount, PayApplicationPageListExportVO::getPayAccount)
                .selectAs(OrderPayApplicationInfoEntity::getPayAccountName, PayApplicationPageListExportVO::getPayAccountName)
                .selectAs(OrderPayApplicationInfoEntity::getPayAccountNumber, PayApplicationPageListExportVO::getPayAccountNumber)
                .selectAs(OrderPayApplicationInfoEntity::getId, PayApplicationPageListExportVO::getId)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccount, PayApplicationPageListExportVO::getPayeeAccount)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccountName, PayApplicationPageListExportVO::getPayeeAccountName)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAmount, PayApplicationPageListExportVO::getPayeeAmount)
                .selectAs(OrderPayApplicationInfoEntity::getFeeType, PayApplicationPageListExportVO::getFeeType)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeType, PayApplicationPageListExportVO::getPayeeType)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccountNumber, PayApplicationPageListExportVO::getPayeeAccountNumber)
                .selectAs(OrderPayApplicationInfoEntity::getApplyUserId, PayApplicationPageListExportVO::getUserId)
                .selectAs(OrderPayApplicationInfoEntity::getCreateTime, PayApplicationPageListExportVO::getSubmitTime)
                .selectAs(OrderPayApplicationInfoEntity::getRemark, PayApplicationPageListExportVO::getRemark)
                .selectAs(OrderPayApplicationInfoEntity::getAccountantRemark, PayApplicationPageListExportVO::getAccountantRemark)
                .selectAs(OrderPayApplicationInfoEntity::getCashierRemark, PayApplicationPageListExportVO::getCashierRemark)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeAccountBranchName, PayApplicationPageListExportVO::getPayeeAccountBranchName)
                .selectAs(OrderPayApplicationInfoEntity::getPayeePhone, PayApplicationPageListExportVO::getPayeePhone)
                .selectAs(OrderPayApplicationInfoEntity::getPayeeCardNumber, PayApplicationPageListExportVO::getPayeeCardNumber)
                .selectAs(OrderPayApplicationInfoEntity::getAccountantApproveTime, PayApplicationPageListExportVO::getAccountantApproveTime)
                .selectAs(OrderPayApplicationInfoEntity::getAccountantUserId, PayApplicationPageListExportVO::getAccountantChecker)
                .selectAs(OrderPayApplicationInfoEntity::getCashierApproveTime, PayApplicationPageListExportVO::getCashierApproveTime)
                .selectAs(OrderPayApplicationInfoEntity::getPaymentTime, PayApplicationPageListExportVO::getPaymentTime)
                .selectAs(OrderPayApplicationInfoEntity::getCashierUserId, PayApplicationPageListExportVO::getCashierChecker)
                .selectAs(OrderPayApplicationInfoEntity::getApplyType, PayApplicationPageListExportVO::getApplyType)
                .selectAs(OrderPayApplicationInfoEntity::getFeeDetails, PayApplicationPageListExportVO::getFeeDetails)
                .selectAs(OrderPayApplicationInfoEntity::getPaymentVoucherList, PayApplicationPageListExportVO::getPaymentVoucherList)
                .selectAs(OrderPayApplicationInfoEntity::getOrderSource, PayApplicationPageListExportVO::getOrderSource)
                .selectAs(OrderPayApplicationInfoEntity::getOrderApplicationSource, PayApplicationPageListExportVO::getOrderApplicationSource)
                .selectAs(OrderPayApplicationInfoEntity::getRepaymentTerm, PayApplicationPageListExportVO::getRepaymentTerm)
                .leftJoin(OrderInfoEntity.class, on -> on.eq(OrderInfoEntity::getId, OrderPayApplicationInfoEntity::getOrderId).eq(OrderPayApplicationInfoEntity::getOrderSource, 1))
                .leftJoin(OrderFeeInfoEntity.class,
                        on -> on.eq(OrderFeeInfoEntity::getOrderId, OrderPayApplicationInfoEntity::getOrderId).eq(OrderFeeInfoEntity::getGpsFeeStatus, 2).eq(OrderPayApplicationInfoEntity::getOrderSource, 1))
                .leftJoin(DigitalOutsourcingOrderEntity.class, on-> on.eq(DigitalOutsourcingOrderEntity::getOrderId, OrderPayApplicationInfoEntity::getOrderNumber).ne(OrderPayApplicationInfoEntity::getOrderSource, 1))
                .leftJoin(CaseInfoEntity.class,on->on.eq(CaseInfoEntity::getDigitalOrderId, DigitalOutsourcingOrderEntity::getOrderNo).eq(CaseInfoEntity::getCurrentNode, CaseApproveNodeEnums.SUCCESS).eq(CaseInfoEntity::getDeleteFlag, 0))
                .eq(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getFeeDetails()), OrderPayApplicationInfoEntity::getFeeDetails, payApplicationPageListDTO.getFeeDetails())
                .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                .in(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getFeeType()), OrderPayApplicationInfoEntity::getFeeType, payApplicationPageListDTO.getFeeType())
                .eq(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getPaymentDetails()), OrderPayApplicationInfoEntity::getPaymentDetails, payApplicationPageListDTO.getPaymentDetails())
                .like(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getPayeeAccount()), OrderPayApplicationInfoEntity::getPayeeAccount, payApplicationPageListDTO.getPayeeAccount())
                .and(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getVehicleNumber()),or -> or.like(OrderInfoEntity::getVehicleNumber, payApplicationPageListDTO.getVehicleNumber())
                        .or().like(DigitalOutsourcingOrderEntity::getVehicleNumber, payApplicationPageListDTO.getVehicleNumber())
                )
//                .like(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, payApplicationPageListDTO.getVehicleNumber())
                .and(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getOrderNumber()),or -> or.like(OrderInfoEntity::getOrderNumber, payApplicationPageListDTO.getOrderNumber())
                        .or().like(DigitalOutsourcingOrderEntity::getOrderId, payApplicationPageListDTO.getOrderNumber())
                )
//                .like(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getOrderNumber()), OrderInfoEntity::getOrderNumber, payApplicationPageListDTO.getOrderNumber())
                .eq(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getCurrentNode()), OrderPayApplicationInfoEntity::getCurrentNode, payApplicationPageListDTO.getCurrentNode())
                .eq(ObjUtil.isNotNull(payApplicationPageListDTO.getOrderApplicationSource()), OrderPayApplicationInfoEntity::getOrderApplicationSource, payApplicationPageListDTO.getOrderApplicationSource())
                .eq(ObjUtil.isNotNull(payApplicationPageListDTO.getOrderSource()), OrderPayApplicationInfoEntity::getOrderSource, payApplicationPageListDTO.getOrderSource())
                .and(org.springframework.util.StringUtils.hasText(payApplicationPageListDTO.getCustomerName()), or -> or.like(OrderInfoEntity::getCustomerName, payApplicationPageListDTO.getCustomerName())
                        .or().like(DigitalOutsourcingOrderEntity::getCustomerName, payApplicationPageListDTO.getCustomerName())
                )
                .orderByDesc(OrderPayApplicationInfoEntity::getCreateTime);

        List<Integer> roleIds = loginUser.getRoleIds();
        String scopes = loginUser.getScopes();
        boolean hasRole = RoleEnum.ACCOUNTANT.hasRole(roleIds) || RoleEnum.CASHIER.hasRole(roleIds) || RoleEnum.SYS_ADMIN.hasRole(roleIds);
        if (currentNode == null || !hasRole || (scopes!=null && !scopes.contains("data:all"))) {
            // 和订单状态权限控 orderPayApplicationMapper
            if ((!ObjUtil.equals(payApplicationPageListDTO.getOrderApplicationSource(),1))){
                dataPermissionService.limitPayApplicationWithOrder(loginUser, queryWrapper);
            }
        }
        if (ObjUtil.isNotNull(payApplicationPageListDTO.getPaymentTime())) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getPaymentTime,
                    LocalDateTime.of(payApplicationPageListDTO.getPaymentTime(), LocalTime.MIN), LocalDateTime.of(payApplicationPageListDTO.getPaymentTime(), LocalTime.MAX));
        }

        if (payApplicationPageListDTO.getCashierApproveTime() != null && !payApplicationPageListDTO.getCashierApproveTime().isEmpty()) {
            String cashierApproveTime = payApplicationPageListDTO.getCashierApproveTime();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            LocalDate specifiedDate = LocalDate.parse(cashierApproveTime, formatter);
            queryWrapper.between(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getCashierApproveTime()), OrderPayApplicationInfoEntity::getCashierApproveTime, specifiedDate.atStartOfDay(), specifiedDate.atTime(23, 59, 59));
        }

        if (payApplicationPageListDTO.getCashierApproveTimeStartDate() != null && payApplicationPageListDTO.getCashierApproveTimeEndDate() == null) {
            queryWrapper.ge(OrderPayApplicationInfoEntity::getCashierApproveTime, getTimeOfStart(payApplicationPageListDTO.getCashierApproveTimeStartDate()));
        }
        if (payApplicationPageListDTO.getCashierApproveTimeStartDate() == null && payApplicationPageListDTO.getCashierApproveTimeEndDate() != null) {
            queryWrapper.le(OrderPayApplicationInfoEntity::getCashierApproveTime, getTimeOfEnd(payApplicationPageListDTO.getCashierApproveTimeEndDate()));
        }
        if (payApplicationPageListDTO.getCashierApproveTimeStartDate() != null && payApplicationPageListDTO.getCashierApproveTimeEndDate() != null) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getCashierApproveTime,
                    getTimeOfStart(payApplicationPageListDTO.getCashierApproveTimeStartDate()),
                    getTimeOfEnd(payApplicationPageListDTO.getCashierApproveTimeEndDate()));
        }

        if (payApplicationPageListDTO.getApproveTime() != null && !payApplicationPageListDTO.getApproveTime().isEmpty()) {
            String approveTime = payApplicationPageListDTO.getApproveTime();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            LocalDate approveTimeDate = LocalDate.parse(approveTime, formatter);
            queryWrapper.between(ObjectUtil.isNotEmpty(payApplicationPageListDTO.getApproveTime()), OrderPayApplicationInfoEntity::getCreateTime, approveTimeDate.atStartOfDay(), approveTimeDate.atTime(23, 59, 59));
        }
        if (payApplicationPageListDTO.getApproveStartDate() != null && payApplicationPageListDTO.getApproveEndDate() == null) {
            queryWrapper.ge(OrderPayApplicationInfoEntity::getCreateTime, getTimeOfStart(payApplicationPageListDTO.getApproveStartDate()));
        }
        if (payApplicationPageListDTO.getApproveStartDate() == null && payApplicationPageListDTO.getApproveEndDate() != null) {
            queryWrapper.le(OrderPayApplicationInfoEntity::getCreateTime, getTimeOfEnd(payApplicationPageListDTO.getApproveEndDate()));
        }
        if (payApplicationPageListDTO.getApproveStartDate() != null && payApplicationPageListDTO.getApproveEndDate() != null) {
            queryWrapper.between(OrderPayApplicationInfoEntity::getCreateTime,
                    getTimeOfStart(payApplicationPageListDTO.getApproveStartDate()),
                    getTimeOfEnd(payApplicationPageListDTO.getApproveEndDate()));
        }


//        Page<PayApplicationPageListExportVO> orderPayApplyListVOPage = orderPayApplicationMapper.selectJoinPage(new Page<>(payApplicationPageListDTO.getPageNum(), payApplicationPageListDTO.getPageSize()), PayApplicationPageListExportVO.class, queryWrapper);
        List<PayApplicationPageListExportVO> records = orderPayApplicationMapper.selectJoinList( PayApplicationPageListExportVO.class, queryWrapper);
        BigDecimal totalAmount = BigDecimal.ZERO;


        // 客户经理id 提交人id List
        List<Integer> userIds = Stream.concat(
                        records.stream().filter(orderPayApplyListVO -> ObjUtil.isNotEmpty(orderPayApplyListVO.getManagerId()))
                                .map(PayApplicationPageListExportVO::getManagerId),
                        records.stream().filter(orderPayApplyListVO -> ObjUtil.isNotEmpty(orderPayApplyListVO.getUserId()))
                                .map(PayApplicationPageListExportVO::getUserId)
                )
                .distinct()
                .toList();

        Map<Integer, UserInfoVO> userInfoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userIds)) {
            // 获取用户列表
            Result<List<UserInfoVO>> userInfoResult = userFeign.searchUserNameByUserIds(userIds);
            if (Result.isSuccess(userInfoResult) && ObjUtil.isNotEmpty(userInfoResult.getData())) {
                userInfoMap = userInfoResult.getData().stream()
                        .collect(Collectors.toMap(UserInfoVO::getUserId, userInfoVO -> userInfoVO));
            }
        }



        for (PayApplicationPageListExportVO orderPayApplyListVO : records) {
            if (ObjUtil.isNotNull(orderPayApplyListVO.getPayeeAmount())) {
                totalAmount = totalAmount.add(orderPayApplyListVO.getPayeeAmount());
            }
            if (orderPayApplyListVO.getUserId() != null && orderPayApplyListVO.getUserId() != 1) {
                UserInfoVO userInfoVO = userInfoMap.get(orderPayApplyListVO.getUserId());
                if (ObjUtil.isNotNull(userInfoVO)) {
                    orderPayApplyListVO.setUserName(userInfoVO.getName());
                }
            } else {
                orderPayApplyListVO.setUserName("系统自动提交");
            }
            if (StrUtil.isNotBlank(orderPayApplyListVO.getPaymentVoucherList())) {
                // 将 JSON 字符串转换为 List<String>
                List<String> paymentVoucherList = JSON.parseArray(orderPayApplyListVO.getPaymentVoucherList(), String.class);
                orderPayApplyListVO.setResourceId(paymentVoucherList);
            }
            if (Objects.equals(orderPayApplyListVO.getFeeType(), OrderFeeDetailExpandTypeEnum.CURRENT_RETURN_PERFORMANCE.getCode())) {
                if (ObjUtil.isNotEmpty(orderPayApplyListVO.getManagerId())) {
                    Integer managerId = orderPayApplyListVO.getManagerId();
                    UserInfoVO userInfoVO = userInfoMap.get(managerId);
                    if (ObjUtil.isNotNull(userInfoVO)) {
                        orderPayApplyListVO.setPayeeName(userInfoVO.getName());
                    }
                }
            }
            orderPayApplyListVO.setBranchName(ObjUtil.defaultIfNull(orderPayApplyListVO.getStoreName(), ""));
//            PreOcrIdentityCardEntity entity = preOcrIdentityCardService.getOne(new LambdaQueryWrapper<PreOcrIdentityCardEntity>()
//                    .eq(PreOcrIdentityCardEntity::getName, orderPayApplyListVO.getPayeeCardNumber())
//                    .eq(PreOcrIdentityCardEntity::getDeleteFlag, 0)
//                    .orderByDesc(PreOcrIdentityCardEntity::getCreateTime)
//                    .last("limit 1")
//            );
//            if (ObjUtil.isNotEmpty(entity)){
//                orderPayApplyListVO.setUserName(entity.getName());
//            }else {
//                if (ObjUtil.isNotEmpty(orderPayApplyListVO.getPayeePhone())){
//                    Result<UserDetailInfoVO> userDetailInfoVOResult = userFeign.searchUserDetailByMobile(orderPayApplyListVO.getPayeePhone());
//                    if (Result.isSuccess(userDetailInfoVOResult)&&userDetailInfoVOResult.getData()!=null){
//                        UserDetailInfoVO userDetailInfoVO = userDetailInfoVOResult.getData();
//                        orderPayApplyListVO.setPayeeName(userDetailInfoVO.getName());
//                    }
//                }
//            }

//

            orderPayApplyListVO.setFeeTypeToStr(ObjUtil.isNotEmpty(OrderFeeDetailExpandTypeEnum.fromCode(orderPayApplyListVO.getFeeType())) ? OrderFeeDetailExpandTypeEnum.fromCode(orderPayApplyListVO.getFeeType()).getDescription() : "");
            orderPayApplyListVO.setPayeeAmountToStr(ObjUtil.isNotEmpty(orderPayApplyListVO.getPayeeAmount()) ? orderPayApplyListVO.getPayeeAmount().toString() : "");
//            switch (orderPayApplyListVO.getPayeeType()){
//                case 1:
//                    orderPayApplyListVO.setPayeeTypeToStr("客户");
//                    break;
//                case 2:
//                    orderPayApplyListVO.setPayeeTypeToStr("汇丰员工");
//                    break;
//                case 3:
//                    orderPayApplyListVO.setPayeeTypeToStr("盈峰");
//                    break;
//                case 4:
//                    orderPayApplyListVO.setPayeeTypeToStr("龙环汇丰");
//                    break;
//                case 5:
//                    orderPayApplyListVO.setPayeeTypeToStr("担保公司");
//                    break;
//                case 6:
//                    orderPayApplyListVO.setPayeeTypeToStr("富民银行");
//                    break;
//                case 7:
//                    orderPayApplyListVO.setPayeeTypeToStr("长银");
//                    break;
//                case 8:
//                    orderPayApplyListVO.setPayeeTypeToStr("河北谛听");
//                    break;
//                default:
//                    orderPayApplyListVO.setPayeeTypeToStr("");
//            }
//            orderPayApplyListVO.getPayeeType()
            orderPayApplyListVO.setPayeeTypeToStr(ObjUtil.defaultIfNull(PayApplicationPayeeTypeEnum.fromCode(orderPayApplyListVO.getPayeeType()).getDesc(),""));

//            try {

//            if(Objects.equals(orderPayApplyListVO.getCurrentNode(), PayApplicationNodeEnums.ACCOUNTANT_APPLY)){
//                orderPayApplyListVO.setCurrentNodeToStr("付款单申请");
//            }
//            if(Objects.equals(orderPayApplyListVO.getCurrentNode(), PayApplicationNodeEnums.CASHIER_APPROVAL)){
//                orderPayApplyListVO.setCurrentNodeToStr("出纳审批");
//            }
//            if(Objects.equals(orderPayApplyListVO.getCurrentNode(), PayApplicationNodeEnums.SUCCESS)){
//                orderPayApplyListVO.setCurrentNodeToStr("申请成功");
//            }
//            if(Objects.equals(orderPayApplyListVO.getCurrentNode(), PayApplicationNodeEnums.FAIL)){
//                orderPayApplyListVO.setCurrentNodeToStr("流程终止");
//            }
            orderPayApplyListVO.setCurrentNodeToStr(PayApplicationNodeEnums.getDescriptionByCode(orderPayApplyListVO.getCurrentNode().getCode()));
//            } catch (Exception e) {
//                orderPayApplyListVO.setCurrentNodeToStr("");
//            }
            orderPayApplyListVO.setPaymentTimeToStr(ObjUtil.isNotEmpty(orderPayApplyListVO.getPaymentTime()) ? orderPayApplyListVO.getPaymentTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
            orderPayApplyListVO.setCashierApproveTimeToStr(ObjUtil.isNotEmpty(orderPayApplyListVO.getCashierApproveTime()) ? orderPayApplyListVO.getCashierApproveTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
            orderPayApplyListVO.setSubmitTimeToStr(ObjUtil.isNotEmpty(orderPayApplyListVO.getSubmitTime()) ? orderPayApplyListVO.getSubmitTime().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)) : "");
        }

        List<Integer> teamIds = records.stream().map(PayApplicationPageListExportVO::getTeamId).filter(Objects::nonNull).toList();
        if (CollUtil.isNotEmpty(teamIds)) {
            List<DeptInfoVO> branchNameVOS = userFeign.getTheBranchNameBasedOnTheTeamId(teamIds).getData();
            Map<Integer, String> branchNameMap = branchNameVOS.stream().filter(e -> ObjUtil.isNotNull(e.getName())).collect(Collectors.toMap(DeptInfoVO::getId, DeptInfoVO::getName, (v1, v2) -> v1));
            records.stream().filter(item -> item.getTeamId() != null).forEach(record -> {
                record.setBranchName(ObjUtil.defaultIfNull(record.getStoreName(), "") + ObjUtil.defaultIfNull(branchNameMap.get(record.getTeamId()), ""));
            });
        }
        String fileName = "查账申请报表.xlsx"; // 定义输出文件名
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream(); // 创建字节数组输出流;

        // 创建ExcelWriter对象，指定输出流、数据类和表头
        ExcelWriter excelWriter = EasyExcel.write(outputStream, PayApplicationPageListExportVO.class)
                .build();
        // 创建WriteSheet对象，指定工作表名称、数据类和表头
        WriteSheet writeSheet = EasyExcel.writerSheet("查账申请").build();

        if (CollUtil.isEmpty(records)){
            records = new ArrayList<>();
        }else {
            excelWriter.writeContext().writeWorkbookHolder().setWorkbook(new SXSSFWorkbook(records.size()));
        }
        excelWriter.write(records, writeSheet);
        // 获取Workbook对象
        Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();

        // 获取Sheet对象
        Sheet sheet = workbook.getSheetAt(0);
        // 执行额外的Excel创建操作（如果有）
        createExcel(sheet, workbook);
        // 完成ExcelWriter的写入操作
        excelWriter.finish();
        // 将字节数组输出流转换为字节数组
        byte[] bytes = outputStream.toByteArray();

        // 设置文件下载头
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + URLEncoder.encode(fileName, StandardCharsets.UTF_8) + "\"");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        // 获取HttpServletResponse的输出流并写入字节数组
        try (ServletOutputStream outputStream1 = response.getOutputStream()) {
            outputStream1.write(bytes);
            outputStream1.flush();
        } catch (IOException e) {
            // 处理异常
            e.printStackTrace();
        }
    }

    @Override
    public PayApplicationPageListVO approveListTotal(OrderApproveDTO dto, LoginUser loginUser) {
        log.info("approveListTotal loginUser = {}", JSONUtil.toJsonStr(loginUser));

        //查询签约客服
        List<Integer> orderIdBySignCustomerIdList = new ArrayList<>();
        if (StrUtil.isNotBlank(dto.getSignCustomerName())) {
            //根据名称匹配签约客服id
            List<Integer> customerIdList = userFeign.getUserIdByLikeNameList(dto.getSignCustomerName()).getData();

            List<OrderNodeRecordEntity> orderBySignCustomerIdList = null;
            if (CollUtil.isNotEmpty(customerIdList)) {
                orderBySignCustomerIdList = orderNodeRecordMapper.selectList(
                        new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                .select(OrderNodeRecordEntity::getOrderId, OrderNodeRecordEntity::getUpdateBy, OrderNodeRecordEntity::getCreateTime)
                                .apply("id IN (SELECT id FROM (SELECT order_id,id, ROW_NUMBER() OVER (PARTITION BY order_id ORDER BY create_time DESC) as rn FROM lh_order_node_record WHERE current_node = {0}) subquery WHERE rn = 1)",
                                        States.PAYMENT_APPLY_INFORMATION.getNode())
                                .in(OrderNodeRecordEntity::getUpdateBy, customerIdList));
            }
            if (CollUtil.isEmpty(orderBySignCustomerIdList)) {
                return new PayApplicationPageListVO().setTotalAmount(BigDecimal.ZERO);
            }
            //获取每个的最新一条
            orderIdBySignCustomerIdList = orderBySignCustomerIdList.stream().map(OrderNodeRecordEntity::getOrderId).collect(Collectors.toList());
        }

        //客户经理名称获取客户经理id
        List<Integer> name2ManagerIdList = new ArrayList<>();
        if (StrUtil.isNotBlank(dto.getManagerName())) {
            name2ManagerIdList = userFeign.getUserIdByLikeNameList(dto.getManagerName()).getData();
            if (CollUtil.isEmpty(name2ManagerIdList)) {
                return new PayApplicationPageListVO().setTotalAmount(BigDecimal.ZERO);
            }
        }

        //还款日期在指定日期区间的订单id

        List<Integer> repaymentDateOrderIdList = repaymentService.betweenRepaymentDateOrderIdList(dto.getRepaymentStartDate(), dto.getRepaymentEndDate());

        MPJLambdaWrapper<OrderInfoEntity> queryWrapper = new MPJLambdaWrapper<OrderInfoEntity>()
                .select(OrderInfoEntity::getOrderNumber, OrderInfoEntity::getCustomerName,
                        OrderInfoEntity::getCustomerPhone, OrderInfoEntity::getCurrentNode,
                        OrderInfoEntity::getApplyAmount,
                        OrderInfoEntity::getTerm,
                        OrderInfoEntity::getProductName, OrderInfoEntity::getSource, OrderInfoEntity::getManagerId,
                        OrderInfoEntity::getPaymentTime,
                        OrderInfoEntity::getSourceType,
                        OrderInfoEntity::getRegionId,
                        OrderInfoEntity::getRegionName,
                        OrderInfoEntity::getTeamId,
                        OrderInfoEntity::getTeamName
                )
                .selectAs(OrderInfoEntity::getId, OrderApproveListVO::getOrderId)
                .selectAs(OrderInfoEntity::getPreApplyTime, OrderApproveListVO::getApplyDate)
                .selectAs(OrderInfoEntity::getVehicleNumber, OrderApproveListVO::getVehicleNumber)
                .selectAs(OrderInfoEntity::getRiskUserId, OrderApproveListVO::getRisiUserId)
                .selectAs(OrderInfoEntity::getGpsState, OrderApproveListVO::getGpsState)
                .selectAs(FundInfoEntity::getName, OrderApproveListVO::getFundName)
                .selectAs(OrderInfoEntity::getFundId, OrderApproveListVO::getFundId)
                .selectAs(FinalFundInfoEntity::getFundCreditTime, OrderApproveListVO::getAppropriationTime)
                //.selectAs(OrderInfoEntity::getRiskUserId, OrderApproveListVO::getIsReturn)
                .selectFunc("CASE WHEN %s IS NULL THEN 0 ELSE 1 END", arg -> arg.accept(
                        Fun.f("t", OrderInfoEntity::getRiskUserId)
                ), OrderApproveListVO::getIsReturn)
                .selectAs(OrderInfoEntity::getQualityTestCommitTime, OrderApproveListVO::getQualityTestCommitTime)
                .selectAs(OrderInfoEntity::getLastNodeFinishTime, OrderApproveListVO::getApprovalSubmitTime)
                .leftJoin(FundInfoEntity.class, FundInfoEntity::getId, OrderInfoEntity::getFundId)
                //                .eq(hasNodeCondition(dto), OrderInfoEntity::getCurrentNode,
                //                        currentNodeTransform(dto.getCurrentNode()))
                // 面签状态筛选
                .leftJoin(FinalFundInfoEntity.class, q ->
                        q.eq(FinalFundInfoEntity::getOrderId, OrderInfoEntity::getId)
                                .eq(FinalFundInfoEntity::getFundId, OrderInfoEntity::getFundId)
                                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                )

                .eq(Objects.equals(dto.getCurrentNode(), States.MANAGER_INTERVIEW.getNode()),
                        OrderInfoEntity::getReviewState, 1)
                // 抵押待办状态筛选
                .in(Objects.equals(dto.getCurrentNode(), States.MORTGAGE_PENDING.getNode()),
                        OrderInfoEntity::getMortgageState, Arrays.asList(1, 2))
//                .notIn(Objects.equals(dto.getCurrentNode(), States.MORTGAGE_PENDING.getNode()),
//                        OrderInfoEntity::getCurrentNode,
//                        Arrays.asList(States.SYSTEM_TERMINAL.getNode(), States.PROCESS_TERMINAL.getNode()))
                .eq(Objects.equals(dto.getCurrentNode(), States.MORTGAGE_PENDING.getNode()),
                        OrderInfoEntity::getCurrentNode, States.CUSTOMER_APPOINTMENT.getNode())
                // 合同签署状态筛选
                .eq(Objects.equals(dto.getCurrentNode(), States.CONTRACT_SIGNING.getNode()),
                        OrderInfoEntity::getContractState, 1)
                .eq(ObjUtil.isNotNull(dto.getFundId()), OrderInfoEntity::getFundId, dto.getFundId())
                .in(CollUtil.isNotEmpty(name2ManagerIdList), OrderInfoEntity::getManagerId, name2ManagerIdList)
                .like(StrUtil.isNotBlank(dto.getStoreName()), OrderInfoEntity::getStoreName, dto.getStoreName())
                .like(StrUtil.isNotBlank(dto.getVehicleNumber()), OrderInfoEntity::getVehicleNumber, dto.getVehicleNumber())
                .like(StrUtil.isNotBlank(dto.getRegionName()), OrderInfoEntity::getRegionName, dto.getRegionName())
                .like(StrUtil.isNotBlank(dto.getTeamName()), OrderInfoEntity::getTeamName, dto.getTeamName())
                .in(CollUtil.isNotEmpty(repaymentDateOrderIdList), OrderInfoEntity::getId, repaymentDateOrderIdList)
                .in(CollUtil.isNotEmpty(orderIdBySignCustomerIdList), OrderInfoEntity::getId, orderIdBySignCustomerIdList);
        //                        // GPS状态筛选
        //                        .eq(Objects.equals(dto.getCurrentNode(), States
        //                        .GPS_INSTALL_APPLY.getNode()),
        //                                OrderInfoEntity::getGpsState, 1)
        //抵押代办逻辑
//        if (Objects.equals(dto.getCurrentNode(), States.MORTGAGE_PENDING.getNode())) {
//            queryWrapper.ne(OrderInfoEntity::getCurrentNode, States.PAYMENT_SUCCESS.getNode());
//        }
        if (hasNodeCondition(dto)) {
//            if (Objects.equals(dto.getCurrentNode(), States.QUALITY_INSPECTION.getNode()) ||
//                    Objects.equals(dto.getCurrentNode(), States.MANAGER_INTERVIEW.getNode())) {
//                List<Integer> deptIds = loginUser.getDeptIds();
//                Long count = distributeAreaMapper.selectCount(new LambdaQueryWrapper<DistributeAreaEntity>()
//                        .in(DistributeAreaEntity::getStoreId, deptIds)
//                        .eq(DistributeAreaEntity::getDeleteFlag, 0)
//                        .eq(DistributeAreaEntity::getServiceDispatch, 1));
//                if (count > 0) {
//                    queryWrapper.leftJoin(OrderApproveDistributeEntity.class, OrderApproveDistributeEntity::getOrderId, OrderInfoEntity::getId)
//                            .isNull(OrderApproveDistributeEntity::getDistributeEndTime);
//                }
//            }

            if (Objects.equals(dto.getCurrentNode(), States.RISK_FIRST_APPROVE_ASSIGN.getNode())) {
                queryWrapper.in(OrderInfoEntity::getCurrentNode,
                        Arrays.asList(States.RISK_FIRST_APPROVE_ASSIGN.getNode(), States.RISK_FIRST_APPROVE.getNode()));
            } else {
                List<Integer> currentNodeList = new ArrayList<>();
                currentNodeList.add(currentNodeTransform(dto.getCurrentNode()));
                if (ObjUtil.equals(dto.getShowSettled(), Boolean.TRUE)) {
                    currentNodeList.add(States.SETTLED.getNode());
                }
                if (Objects.equals(dto.getCurrentNode(), States.PAYMENT_SUCCESS.getNode())){
                    currentNodeList.add(States.SETTLED.getNode());
                }
                queryWrapper.in(OrderInfoEntity::getCurrentNode, currentNodeList);
            }
            if (Objects.equals(dto.getCurrentNode(), States.RISK_FIRST_APPROVE_ASSIGN.getNode()) ||
                    Objects.equals(dto.getCurrentNode(), States.RISK_FINAL_APPROVE.getNode())) {
                queryWrapper.eq(!StrUtil.isEmpty(dto.getCustomerPhone()), OrderInfoEntity::getCustomerPhone,
                        dto.getCustomerPhone());
            } else {
                queryWrapper.like(!StrUtil.isEmpty(dto.getCustomerPhone()), OrderInfoEntity::getCustomerPhone,
                        dto.getCustomerPhone());
            }

        } else {
            queryWrapper.like(!StrUtil.isEmpty(dto.getCustomerPhone()), OrderInfoEntity::getCustomerPhone,
                    dto.getCustomerPhone());
        }

        if (dto.getGpsState() != null) {
            queryWrapper
                    .selectAs(OrderGpsInfoEntity::getThirdParty , OrderApproveListVO::getGpsThirdParty)
                    .leftJoin(OrderGpsInfoEntity.class, OrderGpsInfoEntity::getOrderId, OrderInfoEntity::getId)
                    .eq(OrderGpsInfoEntity::getDeleteFlag, 0);
            if (dto.getGpsState() == 0) {
                List<Integer> gpsStateList = Arrays.asList(1, 2, 3, 4);
                queryWrapper.in(OrderInfoEntity::getGpsState, gpsStateList);
            } else {
                queryWrapper.eq(OrderInfoEntity::getGpsState, dto.getGpsState());
            }
        }
        if (dto.getApplyStartDate() != null && dto.getApplyEndDate() == null) {
            queryWrapper.ge(OrderInfoEntity::getPreApplyTime, getTimeOfStart(dto.getApplyStartDate()));
        }
        if (dto.getApplyStartDate() == null && dto.getApplyEndDate() != null) {
            queryWrapper.le(OrderInfoEntity::getPreApplyTime, getTimeOfEnd(dto.getApplyEndDate()));
        }
        if (dto.getApplyStartDate() != null && dto.getApplyEndDate() != null) {
            queryWrapper.between(OrderInfoEntity::getPreApplyTime,
                    getTimeOfStart(dto.getApplyStartDate()),
                    getTimeOfEnd(dto.getApplyEndDate()));
        }
        if (dto.getPaymentStartDate() != null && dto.getPaymentEndDate() == null) {
            queryWrapper.ge(OrderInfoEntity::getPaymentTime, getTimeOfStart(dto.getPaymentStartDate()));
        }
        if (dto.getPaymentStartDate() == null && dto.getPaymentEndDate() != null) {
            queryWrapper.le(OrderInfoEntity::getPaymentTime, getTimeOfEnd(dto.getPaymentEndDate()));
        }
        if (dto.getPaymentStartDate() != null && dto.getPaymentEndDate() != null) {
            queryWrapper.between(OrderInfoEntity::getPaymentTime,
                    getTimeOfStart(dto.getPaymentStartDate()),
                    getTimeOfEnd(dto.getPaymentEndDate()));
        }
        queryWrapper.like(!StrUtil.isEmpty(dto.getOrderNumber()), OrderInfoEntity::getOrderNumber,
                        dto.getOrderNumber())
                .like(!StrUtil.isEmpty(dto.getCustomerName()), OrderInfoEntity::getCustomerName,
                        dto.getCustomerName());

        // 时间排序
        if (Objects.equals(dto.getCurrentNode(), States.PAYMENT_SUCCESS.getNode())) {
            queryWrapper.orderByDesc(Objects.equals(dto.getCurrentNode(), States.PAYMENT_SUCCESS.getNode()),
                    OrderInfoEntity::getPaymentTime);

        } else if (Objects.equals(dto.getCurrentNode(), States.PAYMENT_CONTRACT_APPROVAL.getNode())) {
            queryWrapper.orderByAsc(OrderInfoEntity::getLastNodeFinishTime);
        } else {
            queryWrapper.orderByDesc(!Objects.equals(dto.getCurrentNode(), States.PAYMENT_SUCCESS.getNode()),
                    OrderInfoEntity::getPreApplyTime);

        }
        //是否是交易明细查询
        if (ObjUtil.defaultIfNull(dto.getIsTrading(), false)) {
            queryWrapper.distinct()
                    .innerJoin(OrderFeeDetailEntity.class, OrderFeeDetailEntity::getOrderId, OrderInfoEntity::getId)
                    .eq(OrderFeeDetailEntity::getDeleteFlag, 0);
        }

        //根据还款计划状态筛选
        if (CollUtil.isNotEmpty(dto.getRepaymentStatus())) {
             repaymentService.queryOrderIdListByRepaymentStatusLimit(queryWrapper ,dto.getRepaymentStatus(), dto.getWaitRepayment());
        }

        // 权限控制
        dataPermissionService.limitOrder(loginUser, dto.getCurrentNode(), queryWrapper);
        log.info("OrderDetailsInfoServiceImpl.approveListTotal.sql:{}", queryWrapper.getSqlSelect());
        List<OrderApproveListVO> orderApproveListVOList = orderInfoMapper.selectJoinList(
                OrderApproveListVO.class,
                queryWrapper
        );
        List<Integer> orderIdList = orderApproveListVOList.stream()
                .map(OrderApproveListVO::getOrderId)
                .filter(ObjUtil::isNotEmpty)
                .toList();
        if (CollUtil.isNotEmpty(orderIdList)){
            MPJLambdaWrapper<OrderAmountEntity> wrapper = new MPJLambdaWrapper<OrderAmountEntity>()
                    .selectSum(OrderAmountEntity::getCustomerConfirmAmount, PayApplicationPageListVO::getTotalAmount)
                    .in(OrderAmountEntity::getOrderId, orderIdList)
                    .eq(OrderAmountEntity::getDeleteFlag, 0);
            return new PayApplicationPageListVO().setTotalAmount(orderAmountMapper.selectJoinOne(BigDecimal.class, wrapper));
        }else {
            return new PayApplicationPageListVO().setTotalAmount(BigDecimal.ZERO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean thRefundApply(ThRefundApplyDTO dto, LoginUser loginUser) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
        if (ObjUtil.isEmpty(orderInfoEntity)){
            throw new BusinessException("订单不存在");
        }
        int feeApplyId = 0;
        OrderPayApplicationInfoEntity entity = new OrderPayApplicationInfoEntity();
        if (ObjUtil.isNotEmpty(dto.getId())){
            entity = orderPayApplicationMapper.selectById(dto.getId());
            entity.setPayeeType(dto.getPayeeType());
            entity.setFeeType(dto.getFeeType());
            entity.setPaymentDetails(dto.getPaymentDetails());
            entity.setPayAccount(dto.getPayAccount());
            entity.setPayAccountName(dto.getPayAccountName());
            entity.setPayAccountNumber(dto.getPayAccountNumber());
            entity.setPayeeAccount(dto.getPayeeAccount());
            entity.setPayeeAccountName(dto.getPayeeAccountName());
            entity.setPayeeAccountNumber(dto.getPayeeAccountNumber());
            entity.setPayeeAmount(dto.getPayeeAmount());
            entity.setPaymentVoucherList(JSONUtil.toJsonStr(dto.getResourceId()));
            entity.setRemark(dto.getRemark());
            entity.setRepaymentTerm(dto.getRepaymentTerm());
            entity.setCurrentNode(PayApplicationNodeEnums.APPROVE_APPROVAL);
            entity.setApplyUserId(loginUser.getUserId());
            entity.setRepaymentShowAmount(dto.getRepaymentShowAmount());
            orderPayApplicationMapper.updateById(entity);
            feeApplyId = entity.getId();
        }else {
            List<OrderPayApplicationInfoEntity> orderPayApplicationInfoEntities = orderPayApplicationMapper.selectList(
                    new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                            .eq(OrderPayApplicationInfoEntity::getOrderId, dto.getOrderId())
                            .eq(OrderPayApplicationInfoEntity::getOrderApplicationSource, 0)
                            .eq(OrderPayApplicationInfoEntity::getFeeType, dto.getFeeType())
                            .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
                            .eq(OrderPayApplicationInfoEntity::getRepaymentTerm,dto.getRepaymentTerm())
                            .eq(OrderPayApplicationInfoEntity::getOrderSource, 1)
                            .ne(OrderPayApplicationInfoEntity::getCurrentNode, PayApplicationNodeEnums.SUCCESS)
            );
            if (CollUtil.isNotEmpty(orderPayApplicationInfoEntities)){
                throw new BusinessException("该订单所选期数存在未审批的数据");
            }
            entity.setId(dto.getId());
            entity.setOrderId(dto.getOrderId());
            entity.setPayeeType(dto.getPayeeType());
            entity.setFeeType(dto.getFeeType());
            entity.setPaymentDetails(dto.getPaymentDetails());
            entity.setPayAccount(dto.getPayAccount());
            entity.setPayAccountName(dto.getPayAccountName());
            entity.setPayAccountNumber(dto.getPayAccountNumber());
            entity.setPayeeAccount(dto.getPayeeAccount());
            entity.setPayeeAccountName(dto.getPayeeAccountName());
            entity.setPayeeAccountNumber(dto.getPayeeAccountNumber());
            entity.setPayeeAmount(dto.getPayeeAmount());
            entity.setRepaymentTerm(dto.getRepaymentTerm());
            entity.setPaymentVoucherList(JSONUtil.toJsonStr(dto.getResourceId()));
            entity.setRemark(dto.getRemark());
            entity.setCurrentNode(PayApplicationNodeEnums.APPROVE_APPROVAL);
            entity.setApplyUserId(loginUser.getUserId());
            entity.setApplyType(OrderFeeDetailStatusEnum.SPENDING);
            entity.setFeeDetails(1);
            entity.setOrderSource(1);
            entity.setOrderNumber(orderInfoEntity.getOrderNumber());
            orderPayApplicationMapper.insert(entity);
            feeApplyId = entity.getId();
        }
        saveNodeRecord(entity.getId(), PayApplicationNodeEnums.APPROVE_APPROVAL,entity.getCurrentNode(), PayApplicationAuditTypeEnum.YUNQI
                , null, entity.getRemark(), PayApplicationEventEnums.APPROVE_PASS, loginUser.getUserId(), LocalDateTime.now());

        FundRefundInfoEntity one = fundRefundInfoService.getOne(
                new LambdaQueryWrapper<FundRefundInfoEntity>()
                        .eq(FundRefundInfoEntity::getOrderId, dto.getOrderId())
                        .eq(FundRefundInfoEntity::getFundId, orderInfoEntity.getFundId())
                        .eq(FundRefundInfoEntity::getApplyId, feeApplyId)
                        .eq(FundRefundInfoEntity::getDeleteFlag, 0)
        );

        TongHuiInitiateRefundDTO tongHuiInitiateRefundDTO = new TongHuiInitiateRefundDTO();
        if (ObjUtil.isNotEmpty(one)){
            tongHuiInitiateRefundDTO.setRefundOrderNumber(one.getRefundOrderNumber());
            tongHuiInitiateRefundDTO.setChannelOrderNumber(one.getChannelOrderNumber());
        }else {
            tongHuiInitiateRefundDTO.setChannelOrderNumber(IdUtil.simpleUUID());
        }
        tongHuiInitiateRefundDTO.setOrderId(dto.getOrderId());
        tongHuiInitiateRefundDTO.setPayeeAccount(dto.getPayeeAccount());
        tongHuiInitiateRefundDTO.setPayeeAccountName(dto.getPayeeAccountName());
        tongHuiInitiateRefundDTO.setPayeeAccountNumber(dto.getPayeeAccountNumber());
        tongHuiInitiateRefundDTO.setPayeeAmount(dto.getPayeeAmount());
        tongHuiInitiateRefundDTO.setRemark(dto.getRemark());
        tongHuiInitiateRefundDTO.setResourceIdList(dto.getResourceId());

        Result<TongHuiInitiateRefundVO> tongHuiInitiateRefundVOResult = approveFeign.tongHuiInitiateRefund(tongHuiInitiateRefundDTO);

        if (Result.isSuccess(tongHuiInitiateRefundVOResult) &&ObjUtil.isNotEmpty(tongHuiInitiateRefundVOResult.getData())){
            fundRefundInfoService.save(
                    new FundRefundInfoEntity()
                            .setOrderId(dto.getOrderId())
                            .setFundId(orderInfoEntity.getFundId())
                            .setApplyId(feeApplyId)
                            .setRefundOrderNumber(tongHuiInitiateRefundVOResult.getData().getTFOrderNum())
                            .setChannelOrderNumber(tongHuiInitiateRefundVOResult.getData().getQDFYOrderNum())
                            .setRefundAmount(dto.getPayeeAmount())
                            .setRemark(dto.getRemark())
            );
        }else {
            throw new BusinessException(tongHuiInitiateRefundVOResult.getMsg());
        }
        return true;
    }

    @Override
    public void riskAIReportStatus(OrderInfoVO orderInfoVO, OrderInfoEntity orderInfoEntity) {
        //设置AI报告状态
        RiskAiIntelligentAuditEntity riskAiIntelligentAudit = riskAiIntelligentAuditMapper.selectOne(new LambdaQueryWrapper<RiskAiIntelligentAuditEntity>()
                .eq(RiskAiIntelligentAuditEntity::getOrderId, orderInfoEntity.getId())
                .eq(RiskAiIntelligentAuditEntity::getDeleteFlag, 0)
                .orderByDesc(RiskAiIntelligentAuditEntity::getCreateTime)
                .last("limit 1")
        );
        RiskFundAutoConfigEntity riskFundAutoConfigEntity = riskFundAutoConfigMapper.selectOne(
                new LambdaQueryWrapper<RiskFundAutoConfigEntity>()
                        .eq(RiskFundAutoConfigEntity::getFundId, orderInfoEntity.getFundId())
                        .eq(RiskFundAutoConfigEntity::getDeleteFlag, 0)
                        .eq(RiskFundAutoConfigEntity::getRiskType, 1)
                        .orderByDesc(RiskFundAutoConfigEntity::getCreateTime)
                .last("limit 1")
        );
        if (riskAiIntelligentAudit != null && riskAiIntelligentAudit.getAiReportStatus() != null && riskFundAutoConfigEntity != null && Objects.equals(riskFundAutoConfigEntity.getOpenOff(),1)){
            orderInfoVO.setAiReportStatus(riskAiIntelligentAudit.getAiReportStatus());
        }else {
            ObjectMapper objectMapper = new ObjectMapper();
            SwitchVO switchInfo = switchUtils.getSwitchInfo(SwitchConstants.INTELLIGENT_AI_QUOTA);
            List<Integer> list = new ArrayList<>();
            if (Objects.equals(switchInfo.getSwitchFlag(),1) && StringUtils.isNotBlank(switchInfo.getValue())){
                try {
                    list = objectMapper.readValue(switchInfo.getValue(), new TypeReference<List<Integer>>() {});
                } catch (JsonProcessingException e) {
                    orderInfoVO.setAiReportStatus(0);
                }
            }
            if (CollUtil.isNotEmpty(list) && list.contains(orderInfoEntity.getFundId())){
                List<IntelligentRiskInfoEntity> intelligentRiskInfoEntityList = intelligentRiskInfoEntityMapper.selectList(
                        new LambdaQueryWrapper<IntelligentRiskInfoEntity>()
                                .eq(IntelligentRiskInfoEntity::getOrderId, orderInfoEntity.getId())
                                .eq(IntelligentRiskInfoEntity::getType, IntelligentEnum.INTELLIGENT_RISK.getKey())
                                .eq(IntelligentRiskInfoEntity::getStep, 2)
                                .eq(IntelligentRiskInfoEntity::getDeleteFlag, 0)
                );
                if (CollUtil.isNotEmpty(intelligentRiskInfoEntityList)){
                    orderInfoVO.setAiReportStatus(1);
                }else {
                    OrderNodeRecordEntity orderNodeRecordEntity = orderNodeRecordMapper.selectOne(
                            new LambdaQueryWrapper<OrderNodeRecordEntity>()
                                    .eq(OrderNodeRecordEntity::getOrderId, orderInfoEntity.getId())
                                    .eq(OrderNodeRecordEntity::getCurrentNode, States.QUALITY_INSPECTION.getNode())
                                    .in(OrderNodeRecordEntity::getEvent,Arrays.asList(
                                            Events.AGREES,
                                            Events.AGREES_QUALITY_INSPECTION_2_OVERALL_REVIEW,
                                            Events.AGREES_QUALITY_INSPECTION_2_STORE_EVALUATION
                                    ))
                                    .eq(OrderNodeRecordEntity::getDeleteFlag, 0)
                                    .orderByDesc(OrderNodeRecordEntity::getId)
                                    .last("limit 1")
                    );
                    if (orderNodeRecordEntity != null && orderNodeRecordEntity.getCreateTime().isBefore(LocalDateTime.now().minusMinutes(10))){
                        orderInfoVO.setAiReportStatus(2);
                    }
                }
            }

        }
    }

    public static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return str.matches("-?\\d+(\\.\\d+)?");
    }
    public void theFinalReviewRejectedAllOfThem(TheFinalReviewRejectedAllOfThemDTO dto) {
        String jsonPrettyStr = JSONUtil.toJsonStr(dto, new JSONConfig().setIgnoreNullValue(false));
        String iv = RandomUtil.randomString(12);
        String body;
        try {
            body = SignatureUtils.encrypt(jsonPrettyStr, bodySecret, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String timestamp = new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN).format(new Date());
        String sign = SignatureUtils.getSign(timestamp, iv, body, key);
        kingdeeFeign.rejectSave(body, sign, iv, timestamp);
    }
    /**
     * 设置门店报表单元格样式
     *
     * @param sheet
     * @param workbook
     */
    public static void createExcel(Sheet sheet, Workbook workbook) {
        // 创建一个新的字体样式
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);

        // 创建一个新的单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        cellStyle.setWrapText(true); // 启用自动换行
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        applyCellStyleToSheet(sheet, cellStyle);
    }

    private static void applyCellStyleToSheet(Sheet sheet, CellStyle cellStyle) {
        for (Row row : sheet) {
            for (Cell cell : row) {
                if (cell == null) {
                    cell = row.createCell(cell.getColumnIndex());
                }
                cell.setCellStyle(cellStyle);
            }
        }
    }
    private static LocalDateTime getTimeOfEnd(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.MAX);
    }

    private static LocalDateTime getTimeOfStart(LocalDate localDate) {
        return LocalDateTime.of(localDate, LocalTime.MIN);
    }
    /**
     * 是否查询按照节点条件
     *
     * @param orderApproveDTO 订单批准 DTO
     * @return boolean
     */
    private static boolean hasNodeCondition(OrderApproveDTO orderApproveDTO) {
        Integer currentNode = Convert.toInt(orderApproveDTO.getCurrentNode(), 0);
        if (Objects.equals(currentNode, 0)) {
            return false;
        } else if (Objects.equals(currentNode, States.MORTGAGE_PENDING.getNode())) {
            return false;
        } else {
            return true;
        }
    }
    /**
     * 子节点状态转换成大状态节点
     *
     * @param currentNode
     * @return
     */
    private static Integer currentNodeTransform(Integer currentNode) {
        if (Stream.of(States.MANAGER_INTERVIEW, States.CONTRACT_SIGNING,
                States.GPS_INSTALL_APPLY, States.GPS_FEE_PAYMENT,
                States.MORTGAGE_PENDING).map(States::getNode).anyMatch(value -> Objects.equals(value, currentNode))) {
            return States.CUSTOMER_APPOINTMENT.getNode();
        }
        return currentNode;
    }
    public void saveNodeRecord(int applyInfoId, PayApplicationNodeEnums currentNode, PayApplicationNodeEnums nextNode,
                               PayApplicationAuditTypeEnum auditType, String processId,
                               String remark, PayApplicationEventEnums event, Integer currentUserId, LocalDateTime approveTime) {
        try {
            OrderPayApplyNodeRecordEntity recordEntity = new OrderPayApplyNodeRecordEntity()
                    .setApplyInfoId(applyInfoId)
                    .setCurrentNode(currentNode)
                    .setRemark(remark)
                    .setEvent(event)
                    .setAuditType(auditType)
                    .setProcessId(processId)
                    .setNextNode(nextNode)
                    ;
            recordEntity.setCreateBy(currentUserId);
            recordEntity.setUpdateBy(currentUserId);
            recordEntity.setUpdateTime(approveTime);
            orderPayApplyNodeRecordMapper.insert(recordEntity);
        } catch (Exception e) {
            log.error("OrderPayApplicationServiceImpl.saveNodeRecord applyInfoId:{} err:{}", applyInfoId, e.getMessage(), e);
        }
    }
}
