package com.longhuan.order.converter;

import com.longhuan.order.pojo.entity.FundRepaymentInfoEntity;
import com.longhuan.order.pojo.entity.RepurchaseRepaymentInfoEntity;
import org.mapstruct.Mapper;

import java.util.List;


@Mapper(componentModel = "spring")
public interface RepurchaseRepaymentInfoConverter {

    List<RepurchaseRepaymentInfoEntity> fund2RepurchaseList(List<FundRepaymentInfoEntity> fundRepaymentInfoList);

    List<FundRepaymentInfoEntity> repurchase2FundList(List<RepurchaseRepaymentInfoEntity> repaymentInfoList);

}
