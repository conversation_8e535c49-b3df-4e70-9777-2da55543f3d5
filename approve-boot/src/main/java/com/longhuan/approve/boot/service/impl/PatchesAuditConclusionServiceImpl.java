package com.longhuan.approve.boot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.longhuan.approve.boot.pojo.entity.PatchesAuditConclusionEntity;
import com.longhuan.approve.boot.service.PatchesAuditConclusionService;
import com.longhuan.approve.boot.mapper.PatchesAuditConclusionMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【lh_patches_audit_conclusion(贷后补件审核结论表)】的数据库操作Service实现
* @createDate 2025-09-03 10:56:19
*/
@Slf4j
@Service
@RequiredArgsConstructor
public class PatchesAuditConclusionServiceImpl extends ServiceImpl<PatchesAuditConclusionMapper, PatchesAuditConclusionEntity>
implements PatchesAuditConclusionService {

}
