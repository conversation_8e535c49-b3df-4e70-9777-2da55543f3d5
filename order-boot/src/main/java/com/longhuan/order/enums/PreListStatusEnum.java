package com.longhuan.order.enums;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PreListStatusEnum {

    PENDING_PRE_TRIAL(0, "待预审"),
    TO_BE_ENTERED(1, "待录入"),
    TO_BE_AUTHORIZED(2, "待授权"),
    PENDING_PRE_CAPITAL(3, "待资方预审"),
    PRODUCTS_TO_BE_SELECTED(4, "待选产品"),
    APPROVAL_SUCCESS(5, "预审通过")
    ;

    private final Integer code;
    @EnumValue
    @JsonValue
    private final String description;
    public static PreListStatusEnum fromCode(int code) {
        for (PreListStatusEnum node : values()) {
            if (node.getCode() == code) {
                return node;
            }
        }
        return null;
    }
    public static PreListStatusEnum fromDescription(String description) {
        for (PreListStatusEnum node : values()) {
            if (StrUtil.equals(node.getDescription(),description)) {
                return node;
            }
        }
        return null;
    }

}
