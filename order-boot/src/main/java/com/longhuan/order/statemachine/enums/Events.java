package com.longhuan.order.statemachine.enums;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * 1:通过，2：拒绝，3：驳回，4：作废，5：完成
 */
@Getter
public enum Events {


    AGREES(1, "通过"),
    REJECT(2, "拒绝"),
    BACK(3, "驳回"),
    CANCEL(4, "取消"),
    FINISH(5, "完成"),
    FAIL(6, "失败"),

    SYSTEM_TERMINAL(30, "系统终止"),

    CUSTOMER_CONFIRM(11, "客户确认"),

    CONTRACT_SIGNING_FINISH(9, "合同签约"),
    FUND_SIGNING_REJECT(3072, "资方用信拒绝"),

    GPS_INSTALL_FINISH(7, "GPS安装完成"),
    GPS_INSTALL_APPLY(30501, "GPS安装预约"),
    REVIEW_APPOINTMENT_FINISH(8, "面签通过"),
    REVIEW_APPOINTMENT_START(3031, "面签预约"),
    MORTGAGE_PRE_STAKE_FINISH(3535, "预加押完成"),
    MORTGAGE_LOAN_FINISH(10, "抵押完成"),

    GPS_FEE_PAY_FINISH(17, "GPS费用支付完成"),
    GPS_FEE_PAY_APPLY(3061, "GPS费用支付确定"),
    MORTGAGE_LOAN_START(3531, "抵押预约"),

    RE_PAYMENT_PAY(4551, "重新请款"),
    REBIND_BLANK_CARD(20, "重新绑卡"),
    RESIGN_CONTRACT(21, "合同重签"),
    SETTLED(8000, "结清"),
    /**
     * 通过(单人审批)
     */
    AGREES_RISK_FIRST_SINGLE_AGREES(12, "通过"),


    // 驳回事件
    BACK_RISK_FINAL_APPROVE_2_BUSINESS_ADDED_INFO(20001000, "风控终审驳回补充资料"),
    BACK_RISK_FINAL_APPROVE_2_QUALITY_INSPECTION(20001300, "风控终审驳回客服质检"),

    BACK_RISK_FIRST_APPROVE_2_BUSINESS_ADDED_INFO(15001000, "风控初审驳回补充资料"),
    BACK_RISK_FIRST_APPROVE_2_QUALITY_INSPECTION(15001300, "风控初审驳回客服质检"),
    BACK_RISK_FIRST_APPROVE_2_OVERALL_REVIEW(15001400, "风控初审驳回总评复核"),

    BACK_OVERALL_REVIEW_2_BUSINESS_ADDED_INFO(14001000, "总评复核驳回补充资料"),
    OVERALL_REVIEW_2_FUNDS_FINAL_APPROVE(140002500, "总评复核通过资方终审"),

    BACK_QUALITY_INSPECTION_2_BUSINESS_ADDED_INFO(13001000, "客服质检驳回补充资料"),


    BACK_PAYMENT_APPLY_INFORMATION_2_BUSINESS_ADDED_INFO(35501000, "请款资料驳回补充资料"),

    AGREES_QUALITY_INSPECTION_2_OVERALL_REVIEW(13001400, "客服质检通过总评复核"),
    AGREES_QUALITY_INSPECTION_2_STORE_EVALUATION(13001200, "客服质检通过门店评估"),
    AGREES_QUALITY_INSPECTION_2_AL_INTELLIGENT_AUDIT(13001350, "客服质检通过"),

    AL_INTELLIGENT_AUDIT_2_STORE_EVALUATION(13501200, "Al智能审核通过"),
    AL_INTELLIGENT_AUDIT_2_OVERALL_REVIEW(13501400, "Al智能审核通过"),
    AL_INTELLIGENT_AUDIT_2_RISK_FIRST_APPROVE_ASSIGN(13501510, "Al智能审核通过"),
    AL_INTELLIGENT_AUDIT_2_FUNDS_FINAL_APPROVE(13502500, "Al智能审核通过"),
    AL_INTELLIGENT_AUDIT_2_QUALITY_INSPECTION(13501300, "Al智能审核转人工"),
    //资方放款中驳回到请款资料 45003550
    BACK_FUNDS_PAYMENT_PAYMENT_APPLY_INFORMATION(45003550, "驳回"),



    AGREES_STORE_EVALUATION_2_OVERALL_REVIEW(12001400, "门店评估通过总评复核"),
    AGREES_STORE_EVALUATION_2_FUNDS_FINAL_APPROVE(12002500, "门店评估通过资方终审"),

    PRE_INFO_UPDATE_BACK_ORDER_FREEZE(21000, "订单冻结"),

    AGREES_QUALITY_INSPECTION_2_ACCIDENT_VEHICLE_REJECT(13002, "事故车拒绝")

    ;
    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;


    Events(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static Events getEnum(Integer result) {
        return Arrays.stream(Events.values()).filter(value -> Objects.equals(value.getCode(), result))
                .findFirst().orElse(null);

    }

    public static Events getExtendEvents(States node, States backNode) {
        if (node == null || backNode == null) {
            return null;
        }
        return Arrays.stream(Events.values())
                .filter(value -> Objects.equals(value.getCode(),
                        Convert.toInt(Convert.toStr(node.getNode()) + Convert.toStr(backNode.getNode()))))
                .findFirst().orElse(null);
    }

    public Events eventTransform() {
        boolean anyMatch = Stream.of(BACK_RISK_FINAL_APPROVE_2_BUSINESS_ADDED_INFO
                        , BACK_RISK_FINAL_APPROVE_2_QUALITY_INSPECTION
                        , BACK_RISK_FIRST_APPROVE_2_BUSINESS_ADDED_INFO)
                .anyMatch(value -> Objects.equals(value.getCode(), code));
        if (anyMatch) {
            return BACK;
        }
        anyMatch = Stream.of(AGREES_QUALITY_INSPECTION_2_OVERALL_REVIEW, AGREES_QUALITY_INSPECTION_2_STORE_EVALUATION
                        , AGREES_STORE_EVALUATION_2_OVERALL_REVIEW
                        , AGREES)
                .anyMatch(value -> Objects.equals(value.getCode(), code));
        if (anyMatch) {
            return AGREES;
        }
        return this;
    }
}