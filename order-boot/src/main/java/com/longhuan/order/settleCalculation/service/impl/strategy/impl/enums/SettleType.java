package com.longhuan.order.settleCalculation.service.impl.strategy.impl.enums;

/**
 * SupportSettleType
 *
 * <AUTHOR>
 * @date 2025/6/15 下午9:25
 */

import com.longhuan.order.pojo.dto.SettleCalculationContext;

import java.util.function.Predicate;

/**
 * SupportSettleType
 *
 * <AUTHOR>
 * @date 2025/6/15 下午9:25
 */
public enum SettleType {
    /**
     * 非委外非赎回
     */
    NON_OUTSOURCING_NON_REDEEM(context -> !context.isOutsourcing()&&!context.isRedeem()),
    /**
     * 非委外赎回
     */
    NON_OUTSOURCING_REDEEM(context -> !context.isOutsourcing()&&context.isRedeem()),
    /**
     * 委外非赎回
     */
    OUTSOURCING_NON_REDEEM(context -> context.isOutsourcing()&&!context.isRedeem()),
    /**
     * 委外赎回
     */
    OUTSOURCING_REDEEM(context -> context.isOutsourcing()&&context.isRedeem()),
    ;
    private final Predicate<SettleCalculationContext> predicate;
    SettleType(Predicate<SettleCalculationContext> predicate) {
        this.predicate = predicate;
    }
    public static SettleType getType(SettleCalculationContext value) {
        for (SettleType type : values()) {
            if (type.predicate.test(value)) {
                return type;
            }
        }
        return null;
    }
}
