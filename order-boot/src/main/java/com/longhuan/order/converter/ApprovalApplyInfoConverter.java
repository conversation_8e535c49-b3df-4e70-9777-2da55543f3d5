package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.IdentityCardDTO;
import com.longhuan.order.pojo.entity.PreApprovalApplyInfoEntity;
import com.longhuan.order.pojo.vo.PreApprovalApplyInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @Date 2024/7/9
 */
@Mapper(componentModel = "spring")
public interface ApprovalApplyInfoConverter {
    @Mapping(target = "name", source = "updateName")
    @Mapping(target = "idNumber", source = "updateIdNumber")
    @Mapping(target = "phone", source = "phone")
    @Mapping(target = "address", source = "updateAddress")
    PreApprovalApplyInfoEntity dtoToEntity(IdentityCardDTO identityCardDTO);


    PreApprovalApplyInfoVO entity2Vo(PreApprovalApplyInfoEntity entity);


}
