package com.longhuan.order.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.longhuan.order.pojo.entity.StoreGpsConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

/**
 * 门店GPS分期和收费策略配置表数据库访问层
 *
 * <AUTHOR>
 * @date 2025/01/01
 */
@Mapper
public interface StoreGpsConfigMapper extends MPJBaseMapper<StoreGpsConfigEntity> {

    /**
     * 批量重置所有门店的免费服务标志
     *
     * @return 影响的记录数
     */
    @Update("UPDATE lh_store_gps_config SET is_free = 0 WHERE delete_flag = 0")
    int resetAllStoreFreeFlag();

    /**
     * 批量重置所有门店的分期服务标志
     *
     * @return 影响的记录数
     */
    @Update("UPDATE lh_store_gps_config SET is_installment = 0 WHERE delete_flag = 0")
    int resetAllStoreInstallmentFlag();
}
