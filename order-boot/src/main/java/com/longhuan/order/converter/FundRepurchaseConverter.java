package com.longhuan.order.converter;

import com.longhuan.approve.api.pojo.vo.YingFengRepurchaseRepayCalcVO;
import com.longhuan.order.pojo.entity.FundRepurchaseCalcEntity;
import com.longhuan.order.pojo.entity.FundRepurchaseDetailEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface FundRepurchaseConverter {

    @Mapping(target = "repurchasePrincipal", source = "principal")
    @Mapping(target = "repurchaseInterest", source = "interest")
    @Mapping(target = "repurchaseFine", source = "fine")
    @Mapping(target = "repurchaseTotalAmt", source = "totalAmt")
    FundRepurchaseCalcEntity voToEntity(YingFengRepurchaseRepayCalcVO.RepurchaseRepay vo);

    List<FundRepurchaseDetailEntity> voListToEntityList(List<YingFengRepurchaseRepayCalcVO.RepurchaseRepayDetails> voList);


}
