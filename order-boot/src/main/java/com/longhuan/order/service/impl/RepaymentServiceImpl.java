package com.longhuan.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.longhuan.approve.api.constants.RepaymentMethodEnums;
import com.longhuan.approve.api.constants.RepaymentOfflineStatusEnum;
import com.longhuan.approve.api.pojo.dto.CalculationDTO;
import com.longhuan.approve.api.pojo.vo.RepaymentVO;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.enums.OrderFeeDetailExpandTypeEnum;
import com.longhuan.common.core.enums.OrderPaymentStateEnum;
import com.longhuan.common.core.enums.PayApplicationNodeEnums;
import com.longhuan.common.core.enums.PayApplicationPayeeTypeEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.converter.RepaymentConverter;
import com.longhuan.order.enums.FundRepayStatusEnum;
import com.longhuan.order.feign.ApproveFeign;
import com.longhuan.order.mapper.*;
import com.longhuan.order.pojo.dto.RepaymentCalBaseEntity;
import com.longhuan.order.pojo.dto.RepaymentListDTO;
import com.longhuan.order.pojo.dto.SearchFundRepaymentDTO;
import com.longhuan.order.pojo.entity.*;
import com.longhuan.order.pojo.vo.RepaymentInfoVO;
import com.longhuan.order.pojo.vo.RepaymentOverdueStatusVO;
import com.longhuan.order.service.FundRepaymentDeductService;
import com.longhuan.order.service.FundRepaymentInfoService;
import com.longhuan.order.service.RepaymentService;
import com.longhuan.order.service.RepurchaseRepaymentInfoService;
import com.longhuan.order.statemachine.enums.States;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.longhuan.order.statemachine.enums.States.getNode;

/**
 * 还款服务实施
 *
 * <AUTHOR>
 * @date 2024/08/12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RepaymentServiceImpl extends ServiceImpl<RepaymentInfoMapper, RepaymentInfoEntity> implements RepaymentService {
    private final RepaymentInfoMapper repaymentInfoMapper;
    private final RepaymentConverter repaymentConverter;
    private final OrderInfoMapper orderInfoMapper;
    private final OrderFeeInfoMapper orderFeeInfoMapper;
    private final ApproveFeign approveFeign;
    private final OrderAmountMapper orderAmountMapper;
    private final FundRepaymentInfoService fundRepaymentInfoService;
    private final FundRepaymentDeductService fundRepaymentDeductService;
    private final OrderPayApplicationMapper orderPayApplicationMapper;
    private final RepurchaseRepaymentInfoService repurchaseRepaymentInfoService;
    private final FundRepaymentInfoMapper fundRepaymentInfoMapper;
    private final RepurchaseRepaymentInfoMapper repurchaseRepaymentInfoMapper;
    private final GpsInstallmentInfoMapper gpsInstallmentInfoMapper;

    /**
     * 列表
     *
     * @return {@link List }<{@link RepaymentInfoVO }>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<RepaymentInfoVO> list(RepaymentListDTO repaymentListDTO) {
        Integer orderId = repaymentListDTO.getOrderId();
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, "订单不存在");

        // 判断是否为放款
        if (orderInfoEntity.getCurrentNode() >= States.PAYMENT_SUCCESS.getNode()
                && orderInfoEntity.getPaymentState() == OrderPaymentStateEnum.PASS) {
            // 获取还款计划
            //是否赎回
            List<RepaymentInfoVO> repaymentInfoVOS = new ArrayList<>();
            SearchFundRepaymentDTO searchFundRepaymentDTO = new SearchFundRepaymentDTO()
                    .setOrderId(orderId)
                    .setFundId(orderInfoEntity.getFundId())
                    .setRepaymentStatus(repaymentListDTO.getRepaymentStatus())
                    .setWaitRepayment(repaymentListDTO.getWaitRepayment());
            if (ObjUtil.equals(orderInfoEntity.getIsRepurchase(), 1)) {
                List<RepurchaseRepaymentInfoEntity> repurchaseRepaymentInfoEntityList = repurchaseRepaymentInfoService.queryList(searchFundRepaymentDTO);
                repaymentInfoVOS = repaymentConverter.repurchaseListToVoList(repurchaseRepaymentInfoEntityList);

	            List<FundRepaymentInfoEntity> fundRepaymentInfoEntities = fundRepaymentInfoMapper.selectList(new LambdaQueryWrapper<FundRepaymentInfoEntity>()
			            .eq(FundRepaymentInfoEntity::getOrderId, orderId)
			            .eq(FundRepaymentInfoEntity::getIsdaichang, 1)
			            .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
			            .orderByAsc(FundRepaymentInfoEntity::getTerm));

	            for (RepaymentInfoVO repaymentInfoVO : repaymentInfoVOS) {
		            Integer term = repaymentInfoVO.getTerm();
		            FundRepaymentInfoEntity fundRepaymentInfoDaiChang = null;
		            for (FundRepaymentInfoEntity fundRepaymentInfoEntity : fundRepaymentInfoEntities) {
			            if (ObjUtil.equals(fundRepaymentInfoEntity.getTerm(),term)) {
							fundRepaymentInfoDaiChang =  fundRepaymentInfoEntity;
				            break;
			            }
		            }
					if (fundRepaymentInfoDaiChang != null) {
						//代偿
						repaymentInfoVO.setIsdaichang(1);
					} else {
						//未代偿
						repaymentInfoVO.setIsdaichang(0);
					}
	            }
            } else {
                List<FundRepaymentInfoEntity> fundRepaymentInfoEntityList = fundRepaymentInfoService.queryList(searchFundRepaymentDTO);
                repaymentInfoVOS = repaymentConverter.fundEntityListToVoList(fundRepaymentInfoEntityList);
            }

            this.calculateOverdueStatus(repaymentInfoVOS, orderInfoEntity);
            this.setRepaymentStatus(repaymentInfoVOS, orderInfoEntity.getId(), orderInfoEntity.getFundId());
            this.getGpsCarFee(repaymentInfoVOS, orderInfoEntity.getId());
            // 计算 endPrincipal 字段
            OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                    .select(OrderAmountEntity::getCustomerConfirmAmount)
                    .eq(OrderAmountEntity::getOrderId, orderId)
                    .eq(OrderAmountEntity::getDeleteFlag, 0)
            );
            BigDecimal remainingPrincipal = orderAmountEntity.getCustomerConfirmAmount(); // 初始为 applyAmount
            for (RepaymentInfoVO vo : repaymentInfoVOS) {
                BigDecimal repaymentPrincipal = ObjUtil.defaultIfNull(vo.getRepaymentPrincipal(), BigDecimal.ZERO);
                remainingPrincipal = remainingPrincipal.subtract(repaymentPrincipal);
                vo.setEndPrincipal(remainingPrincipal);
            }
            return repaymentInfoVOS;
        }

        Integer state = orderInfoEntity.getState();
        Integer repayMethod = orderInfoEntity.getRepayMethod();
        Integer term = orderInfoEntity.getTerm();
        Integer productId = orderInfoEntity.getProductId();
        List<RepaymentInfoEntity> repaymentInfoList = null;
        List<OrderAmountEntity> orderAmountEntities = orderAmountMapper.selectList(new LambdaQueryWrapper<OrderAmountEntity>()
                .eq(OrderAmountEntity::getOrderId, orderId)
                .eq(OrderAmountEntity::getDeleteFlag, 0)
        );
        Assert.isTrue(!CollectionUtils.isEmpty(orderAmountEntities), "订单额度为空");
        OrderAmountEntity orderAmountEntity = orderAmountEntities.get(0);
        BigDecimal calAmount = BigDecimal.ZERO;
        States node = getNode(state);
        log.info("RepaymentServiceImpl.list node:{}", node);
        if (Objects.nonNull(node)) {
            switch (node) {
                case OVERALL_REVIEW:
                    calAmount = orderAmountEntity.getTotalAmount();
                    break;
                case RISK_FIRST_APPROVE:
                case RISK_FINAL_APPROVE:
                    calAmount = orderAmountEntity.getRiskAmount();
                    break;
                case CUSTOMER_CONFIRM:
                case CUSTOMER_APPOINTMENT:
                case MORTGAGE_PENDING:
                case PAYMENT_APPLY_INFORMATION:
                case PAYMENT_CONTRACT_APPROVAL:
                case FUNDS_PAYMENT_APPROVAL:
                case FUNDS_PAYMENT_PROCESS:
                case FUNDS_PAYMENT_FAIL:
                case PAYMENT_SUCCESS:
                    calAmount = orderAmountEntity.getCustomerConfirmAmount();
                    break;
                default:
                    log.warn("orderId {} node {} cannot calc", orderId, node);
                    return null;
            }
        }
        CalculationDTO calculationDTO = new CalculationDTO()
                .setRepaymentDate(new Date())
                .setRepaymentMethod(RepaymentMethodEnums.getEnum(repayMethod))
                .setLoanTerm(term)
                .setProductId(productId)
                .setLoanAmount(calAmount);
        log.info("RepaymentServiceImpl.list calculationDTO:{}", JSONUtil.toJsonStr(calculationDTO));
        Result<RepaymentVO> result = approveFeign.calculation(calculationDTO);
        log.info("RepaymentServiceImpl.list result:{}", JSONUtil.toJsonStr(result));
        Assert.isTrue(Result.isSuccess(result), result.getMsg());
        RepaymentVO data = result.getData();
        List<RepaymentVO.RepaymentPlanDTO> repaymentPlanList = data.getRepaymentPlanList();
        Assert.notEmpty(repaymentPlanList, "还款计划为空");
        BigDecimal intermediaryFeeAmount = data.getIntermediaryFeeAmount();

        List<RepaymentInfoEntity> list = repaymentPlanList.stream().map(repaymentPlanDTO -> new RepaymentInfoEntity()
                .setOrderId(orderId)
                .setType(1)
                .setTerm(repaymentPlanDTO.getTerm())
                .setRepaymentDate(Convert.toLocalDateTime(repaymentPlanDTO.getRepaymentDate()).toLocalDate())
                .setRepaymentPerformance(repaymentPlanDTO.getRepaymentPerformance())
                .setRepaymentInterest(repaymentPlanDTO.getInterest())
                .setRepaymentPrincipal(repaymentPlanDTO.getMonthlyPrincipal())
                .setRepaymentAmount(repaymentPlanDTO.getRepaymentAmount())
                .setRemainingPrincipal(repaymentPlanDTO.getRemainingPrincipal())
                .setRemainingTotalAmount(repaymentPlanDTO.getRemainingTotalDue())
                .setRepaymentGuarantee(repaymentPlanDTO.getGuaranteeFeeAmount())
                .setInstallmentBrokerageServiceFee(repaymentPlanDTO.getIntermediaryFeeAmount())
        ).toList();
        log.info("RepaymentServiceImpl.list list:{}", JSONUtil.toJsonStr(list));
        if (!CollectionUtils.isEmpty(list)) {
            this.remove(new LambdaUpdateWrapper<RepaymentInfoEntity>()
                    .set(RepaymentInfoEntity::getDeleteFlag, 1)
                    .eq(RepaymentInfoEntity::getOrderId, orderId)
                    .eq(RepaymentInfoEntity::getDeleteFlag, 0)
            );
            this.saveBatch(list);
        }

        // 更新订单费用信息
        List<OrderFeeInfoEntity> orderFeeInfoList = orderFeeInfoMapper.selectList(new LambdaQueryWrapper<OrderFeeInfoEntity>()
                .eq(OrderFeeInfoEntity::getOrderId, orderId)
                .eq(OrderFeeInfoEntity::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(orderFeeInfoList)) {
            orderFeeInfoMapper.insert(new OrderFeeInfoEntity().setOnceServiceFee(intermediaryFeeAmount)
                    .setOrderId(orderId)
                    .setCashBackPerformance(data.getCashBackPerformance())
                    .setAmountReceivable(data.getAmountReceivable()));
        } else {
            OrderFeeInfoEntity orderFeeInfoEntity = orderFeeInfoList.get(0);
            orderFeeInfoMapper.updateById(orderFeeInfoEntity.setOnceServiceFee(intermediaryFeeAmount)
                    .setCashBackPerformance(data.getCashBackPerformance())
                    .setAmountReceivable(data.getAmountReceivable()));
        }

        repaymentInfoList = repaymentInfoMapper.selectList(new LambdaQueryWrapper<RepaymentInfoEntity>()
                .eq(RepaymentInfoEntity::getOrderId, orderId)
                .eq(RepaymentInfoEntity::getType, 1)
                .eq(RepaymentInfoEntity::getDeleteFlag, 0)
                .orderByAsc(RepaymentInfoEntity::getTerm)
        );
        List<RepaymentInfoVO> repaymentInfoVOS = repaymentConverter.entityToVo(repaymentInfoList);
        // 设置是否逾期
        this.calculateOverdueStatus(repaymentInfoVOS, orderInfoEntity);
        return repaymentInfoVOS;

    }

    private void getGpsCarFee(List<RepaymentInfoVO> repaymentInfoVOS, Integer orderId) {
        List<GpsInstallmentInfoEntity> gpsInstallmentInfoEntities = gpsInstallmentInfoMapper.selectList(new LambdaQueryWrapper<GpsInstallmentInfoEntity>()
                .eq(GpsInstallmentInfoEntity::getOrderId, orderId)
                .eq(GpsInstallmentInfoEntity::getDeleteFlag, 0)
                .orderByAsc(GpsInstallmentInfoEntity::getTerm));
        if (CollUtil.isNotEmpty(gpsInstallmentInfoEntities)) {
            Map<Integer, GpsInstallmentInfoEntity> gpsFeeMap = gpsInstallmentInfoEntities.stream()
                    .collect(Collectors.toMap(
                            GpsInstallmentInfoEntity::getTerm,
                            e -> e,
                            (existing, replacement) -> existing  // 相同term取第一个
                    ));
            repaymentInfoVOS.forEach(vo -> {
                        GpsInstallmentInfoEntity gpsInstallmentInfoEntity = gpsFeeMap.get(vo.getTerm());
                        if (gpsInstallmentInfoEntity != null) {
                            vo.setGpsCarServiceFee(gpsInstallmentInfoEntity.getRepaymentPrincipal())
                                    .setGpsPenaltyFee(gpsInstallmentInfoEntity.getRepaymentPenaltyInterest())
                                    .setActuallyGpsCarServiceFee(gpsInstallmentInfoEntity.getActuallyPrincipal())
                                    .setActuallyGpsPenaltyFee(gpsInstallmentInfoEntity.getActuallyPenaltyInterest())
                                    .setGpsReduceAmount(gpsInstallmentInfoEntity.getReductionAmount());
                            if (vo.getGpsCarServiceFee() != null && ObjUtil.notEqual(gpsInstallmentInfoEntity.getRepaymentStatus(), FundRepayStatusEnum.SETTLED) && ObjUtil.isNotNull(vo.getNumberOfDaysOverdue()) && vo.getNumberOfDaysOverdue() > 0) {
                                vo.setGpsPenaltyFee(vo.getGpsCarServiceFee().multiply(new BigDecimal(vo.getNumberOfDaysOverdue())).multiply(BigDecimal.valueOf(0.005)));
                            }
                        }
                    }
            );
        }
    }

    /**
     * 计算每个还款计划的逾期状态、逾期天数和逾期金额。
     *
     * @param repaymentInfoVOS 还款计划列表
     */
    private void calculateOverdueStatus(List<RepaymentInfoVO> repaymentInfoVOS, OrderInfoEntity orderInfoEntity) {
        repaymentInfoVOS.forEach(repaymentInfoVO -> {
            LocalDate nowDate = LocalDate.now();
            LocalDate repaymentDate = repaymentInfoVO.getRepaymentDate();
            LocalDate actuallyDate = repaymentInfoVO.getActuallyDate();
            FundRepayStatusEnum repaymentStatus = repaymentInfoVO.getRepaymentStatus();

            if (ObjUtil.equals(orderInfoEntity.getFundId(), FundEnum.LAN_HAI.getValue())) {
                BigDecimal repaymentAmount = repaymentInfoVO.getRepaymentAmount();
                // 累加担保费和担保费滞纳金
                BigDecimal totalRepaymentAmount = repaymentAmount
                        .add(ObjUtil.defaultIfNull(repaymentInfoVO.getRepaymentGuarantee(), BigDecimal.ZERO))
                        .add(ObjUtil.defaultIfNull(repaymentInfoVO.getRepaymentGuaraFeeOd(), BigDecimal.ZERO));
                repaymentInfoVO.setRepaymentAmount(totalRepaymentAmount);
            }

            // 计算实际已还款总额
            BigDecimal totalAmountRepaid = ObjUtil.defaultIfNull(repaymentInfoVO.getActuallyAmount(), BigDecimal.ZERO)
                    .add(ObjUtil.defaultIfNull(repaymentInfoVO.getActuallyInterest(), BigDecimal.ZERO))
                    .add(ObjUtil.defaultIfNull(repaymentInfoVO.getActuallyPenaltyInterest(), BigDecimal.ZERO))
                    .add(ObjUtil.defaultIfNull(repaymentInfoVO.getActuallyCompound(), BigDecimal.ZERO))
                    .add(ObjUtil.defaultIfNull(repaymentInfoVO.getActuallyPenalty(), BigDecimal.ZERO))
                    .add(ObjUtil.defaultIfNull(repaymentInfoVO.getActuallyGuarantee(), BigDecimal.ZERO))
                    .add(ObjUtil.defaultIfNull(repaymentInfoVO.getActuallyGuaraFeeOd(), BigDecimal.ZERO))
                    .add(ObjUtil.defaultIfNull(repaymentInfoVO.getActuallyLatePenaltyAmount(), BigDecimal.ZERO))
                    .add(ObjUtil.defaultIfNull(repaymentInfoVO.getReductionAmount(), BigDecimal.ZERO))
                    .add(ObjUtil.defaultIfNull(repaymentInfoVO.getActuallyOther(), BigDecimal.ZERO));
            repaymentInfoVO.setTotalAmountRepaid(totalAmountRepaid);
            BigDecimal repaymentAmount = ObjUtil.defaultIfNull(repaymentInfoVO.getRepaymentAmount(), BigDecimal.ZERO);

            if (FundRepayStatusEnum.SETTLED == repaymentStatus && totalAmountRepaid.compareTo(repaymentAmount) == 0) {
                repaymentInfoVO.setWhetherItIsOverdue(false);
                return;
            }

            if (actuallyDate != null && repaymentAmount.compareTo(totalAmountRepaid) <= 0) {
                // 有实还日期的情况
                if (repaymentDate.isBefore(actuallyDate)
                        && repaymentAmount.compareTo(totalAmountRepaid) != 0) {
                    //应还日期 < 实际还款日期  &&  应还金额 != 实际还款金额   逾期天数 = 当前日期 - 应还日期
                    repaymentInfoVO.setWhetherItIsOverdue(true);
                    repaymentInfoVO.setOverdueAmount(repaymentAmount.compareTo(totalAmountRepaid) < 0 ? BigDecimal.ZERO : repaymentAmount.subtract(totalAmountRepaid));
                    repaymentInfoVO.setNumberOfDaysOverdue(ChronoUnit.DAYS.between(repaymentDate, nowDate));
                } else if (repaymentDate.isBefore(actuallyDate)
                        && repaymentAmount.compareTo(totalAmountRepaid) == 0) {
                    //应还日期 < 实际还款日期  &&  应还金额 == 实际还款金额   逾期天数 = 实际还款日期 -  应还日期
                    repaymentInfoVO.setWhetherItIsOverdue(true);
                    repaymentInfoVO.setOverdueAmount(repaymentAmount.subtract(totalAmountRepaid));
                    repaymentInfoVO.setNumberOfDaysOverdue(ChronoUnit.DAYS.between(repaymentDate, actuallyDate));
                } else {
                    repaymentInfoVO.setWhetherItIsOverdue(false);
                }
            } else {
                // 无实还日期的情况
                if (repaymentDate.isBefore(nowDate)
                        && repaymentAmount.compareTo(totalAmountRepaid) >= 0) {
                    repaymentInfoVO.setWhetherItIsOverdue(true);
                    repaymentInfoVO.setOverdueAmount(repaymentAmount.subtract(totalAmountRepaid));
                    repaymentInfoVO.setNumberOfDaysOverdue(ChronoUnit.DAYS.between(repaymentDate, nowDate));
                } else {
                    repaymentInfoVO.setWhetherItIsOverdue(false);
                }
            }
            repaymentInfoVO.setOutsourceReductionAmount(ObjUtil.defaultIfNull(repaymentInfoVO.getOutsourceReductionAmount(), ObjUtil.defaultIfNull(repaymentInfoVO.getReductionAmount(), BigDecimal.ZERO)));
            repaymentInfoVO.setRemainingAmountTotal(repaymentAmount.subtract(totalAmountRepaid));

        });
    }


    /**
     * 根据orderId获取逾期
     *
     * @param orderId orderId
     * @return {@link RepaymentOverdueStatusVO }
     */
    @Override
    public RepaymentOverdueStatusVO overdueStatusByOrderId(Integer orderId) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        Assert.notNull(orderInfoEntity, "订单不存在");
        if (orderInfoEntity.getCurrentNode() >= States.PAYMENT_SUCCESS.getNode()) {
            //资方还款记录
            return this.calculateOverdueStatus(orderId, orderInfoEntity.getFundId());
        }
        throw new BusinessException("该订单未放款，暂无还款信息");
//        // 获取未还款的记录
//        List<RepaymentInfoEntity> nonRepaymentHistory = repaymentInfoMapper.selectList(new LambdaQueryWrapper<RepaymentInfoEntity>()
//                .eq(RepaymentInfoEntity::getOrderId, orderId)
//                .eq(RepaymentInfoEntity::getDeleteFlag, 0)
//                .isNull(RepaymentInfoEntity::getActuallyDate)
//                .le(RepaymentInfoEntity::getRepaymentDate, Date.from(Instant.now()))
//                .orderByDesc(RepaymentInfoEntity::getRepaymentDate));
//        RepaymentOverdueStatusVO repaymentOverdueStatusVO = new RepaymentOverdueStatusVO();
//        //获取未到期的还款计划
//        List<RepaymentInfoEntity> unexpiredPlans = repaymentInfoMapper.selectList(new LambdaQueryWrapper<RepaymentInfoEntity>()
//                .eq(RepaymentInfoEntity::getOrderId, orderId)
//                .eq(RepaymentInfoEntity::getDeleteFlag, 0)
//                .isNull(RepaymentInfoEntity::getActuallyDate)
//                .ge(RepaymentInfoEntity::getRepaymentDate, Date.from(Instant.now()))
//                .orderByAsc(RepaymentInfoEntity::getRepaymentDate));
//        // 如果未找到未还款记录，则返回剩余本金及剩余期数
//        if (CollectionUtils.isEmpty(nonRepaymentHistory)) {
//            if (!CollectionUtils.isEmpty(unexpiredPlans)) {
//                repaymentOverdueStatusVO.setRemainingPrincipal(unexpiredPlans.get(0).getRemainingPrincipal().add(unexpiredPlans.get(0).getRepaymentPrincipal()));
//                repaymentOverdueStatusVO.setRemainingInstalments(unexpiredPlans.size());
//            }
//            return repaymentOverdueStatusVO.setWhetherItIsOverdue(false);
//        }
//        // 如果找到未还款记录，则返回剩余本金和逾期金额及逾期天数
//        repaymentOverdueStatusVO.setWhetherItIsOverdue(true);
//        RepaymentInfoEntity repaymentInfoEntity = nonRepaymentHistory.get(0);
//        repaymentOverdueStatusVO.setOverdueAmount(repaymentInfoEntity.getRemainingTotalAmount()).setRemainingPrincipal(repaymentInfoEntity.getRemainingPrincipal());
//        repaymentInfoEntity = nonRepaymentHistory.get(nonRepaymentHistory.size() - 1);
//        repaymentOverdueStatusVO.setRemainingInstalments(unexpiredPlans.size() + nonRepaymentHistory.size());
//        LocalDate now = LocalDateTime.now().toLocalDate();
//        LocalDate repaymentDate = LocalDateTimeUtil.of(repaymentInfoEntity.getRepaymentDate()).toLocalDate();
//        repaymentOverdueStatusVO.setNumberOfDaysOverdue(ChronoUnit.DAYS.between(repaymentDate, now));
//        return repaymentOverdueStatusVO;
    }

    /**
     * 查询还款日期在指定日期区间的订单id
     */
    @Override
    public List<Integer> betweenRepaymentDateOrderIdList(LocalDate startDate, LocalDate endDate) {
        if (ObjUtil.isNull(startDate) || ObjUtil.isNull(endDate)) {
            return Collections.emptyList();
        }
        List<Integer> list = fundRepaymentInfoMapper.selectJoinList(FundRepaymentInfoEntity.class, new MPJLambdaWrapper<FundRepaymentInfoEntity>()
                        .select(FundRepaymentInfoEntity::getOrderId)
                        .innerJoin(OrderInfoEntity.class, on ->
                                on.eq(FundRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                                        .eq(OrderInfoEntity::getIsRepurchase, 0)
                                        .eq(OrderInfoEntity::getDeleteFlag, 0)
                        )
                        .between(FundRepaymentInfoEntity::getRepaymentDate, startDate, endDate.plusDays(1))
                        .eq(FundRepaymentInfoEntity::getDeleteFlag, 0)
                        .groupBy(FundRepaymentInfoEntity::getOrderId)
                ).stream()
                .map(FundRepaymentInfoEntity::getOrderId).filter(Objects::nonNull)
                .toList();


        MPJLambdaWrapper<RepurchaseRepaymentInfoEntity> repurcahseLqw = new MPJLambdaWrapper<>();
        repurcahseLqw.select(RepurchaseRepaymentInfoEntity::getOrderId)
                .innerJoin(OrderInfoEntity.class, on ->
                        on.eq(RepurchaseRepaymentInfoEntity::getOrderId, OrderInfoEntity::getId)
                                .eq(OrderInfoEntity::getIsRepurchase, 1)
                                .eq(OrderInfoEntity::getDeleteFlag, 0)
                )
                .eq(RepurchaseRepaymentInfoEntity::getDeleteFlag, 0)
                .between(RepurchaseRepaymentInfoEntity::getRepaymentDate, startDate, endDate)
                .groupBy(RepurchaseRepaymentInfoEntity::getOrderId);
        List<Integer> repurcahseList = repurchaseRepaymentInfoMapper.selectJoinList(RepurchaseRepaymentInfoEntity.class, repurcahseLqw).stream()
                .map(RepurchaseRepaymentInfoEntity::getOrderId).filter(Objects::nonNull)
                .toList();
        if (CollUtil.isEmpty(list)) {
            list = new ArrayList<>();
        }
        if (CollUtil.isNotEmpty(repurcahseList)) {
            list = CollUtil.union(list, repurcahseList).stream().toList();
        }
        return list;

    }


    /**
     * 查询还款计划还款状态
     */
    @Override
    public void queryOrderIdListByRepaymentStatusLimit(MPJLambdaWrapper<OrderInfoEntity> queryWrapper, List<FundRepayStatusEnum> repaymentStatus, Boolean waitRepayment) {
        queryWrapper.and(wrapper -> wrapper
                        .or(q -> q
                                        .apply("EXISTS (SELECT 1 FROM lh_fund_repayment_info fr " +
                                                "WHERE fr.order_id = t.id " +
                                                "AND fr.delete_flag = 0 " +
                                                "AND fr.repayment_status in ( " +
                                                repaymentStatus.stream().map(t -> String.valueOf(t.getValue())).collect(Collectors.joining(",")) +
                                                ") " +
//                                (waitRepayment ? "AND fr.repayment_date <= CURRENT_DATE" : "") +
                                                ")")
                                        .eq(OrderInfoEntity::getIsRepurchase, 0)
                        )
                        .or(q -> q
                                        .apply("EXISTS (SELECT 1 FROM lh_repurchase_repayment_info rr " +
                                                "WHERE rr.order_id = t.id " +
                                                "AND rr.delete_flag = 0 " +
                                                "AND rr.repayment_status in ( " +
                                                repaymentStatus.stream().map(t -> String.valueOf(t.getValue())).collect(Collectors.joining(",")) +
                                                ") " +
//                                (waitRepayment ? "AND rr.repayment_date <= CURRENT_DATE" : "") +
                                                ")")
                                        .eq(OrderInfoEntity::getIsRepurchase, 1)
                        )
        );
    }

    /**
     * 处理资方还款记录
     *
     * @param orderId 订单ID
     * @param fundId  资方id
     */
    public RepaymentOverdueStatusVO calculateOverdueStatus(Integer orderId, Integer fundId) {
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        if (orderInfo == null) {
            return new RepaymentOverdueStatusVO();
        }
        boolean isOverdue = Convert.toBool(orderInfo.getIsOverdue(), false);
        boolean isRepurchase = Convert.toBool(orderInfo.getIsRepurchase(), false);
        Integer daysOverdue = orderInfo.getOverdueDays();
        BigDecimal overdueAmount = orderInfo.getTotalOverdueAmount();

        RepaymentOverdueStatusVO vo = new RepaymentOverdueStatusVO();
        if (ObjUtil.equals(orderInfo.getIsRepurchase(), 1)) {
            List<RepurchaseRepaymentInfoEntity> repurchaseRepaymentInfoEntityList = repurchaseRepaymentInfoService.queryByOrderIdList(orderId, fundId);
            List<RepaymentCalBaseEntity> repaymentCalBaseEntities = repaymentConverter.repurchaseListToCalBaseList(repurchaseRepaymentInfoEntityList);
            calculateOverdueStatus(orderInfo, repaymentCalBaseEntities, vo);

        } else {
            List<FundRepaymentInfoEntity> repaymentInfos = fundRepaymentInfoService.queryByOrderIdList(orderId, fundId);
            List<RepaymentCalBaseEntity> repaymentCalBaseEntities = repaymentConverter.fundListToCalBaseList(repaymentInfos);
            calculateOverdueStatus(orderInfo, repaymentCalBaseEntities, vo);
        }


        vo.setWhetherItIsOverdue(isOverdue);
        vo.setNumberOfDaysOverdue(daysOverdue);
        vo.setOverdueAmount(overdueAmount);
        vo.setIsRepurchase(isRepurchase);
        return vo;
    }


    private void calculateOverdueStatus(OrderInfoEntity orderInfo, List<RepaymentCalBaseEntity> repaymentInfos, RepaymentOverdueStatusVO vo) {
        //是否结清
        boolean planState = (ObjUtil.equals(orderInfo.getPlanState(), 1) && ObjUtil.equals(orderInfo.getIsRepurchase(), 0))
                || (ObjUtil.equals(orderInfo.getCompanyPlanState(), 1) && ObjUtil.equals(orderInfo.getIsRepurchase(), 1));
        int remainingTerms = 0;
        BigDecimal remainingPrincipal = BigDecimal.ZERO;
        BigDecimal remainingInterest = BigDecimal.ZERO;

        // 如果存在还款记录，则进行后续处理
        if (!planState && !repaymentInfos.isEmpty()) {

            for (RepaymentCalBaseEntity info : repaymentInfos) {

                // 检查是否逾期
                BigDecimal totalAmountRepaid = ObjUtil.defaultIfNull(info.getActuallyAmountTotal(), BigDecimal.ZERO);
                if (totalAmountRepaid.compareTo(info.getRepaymentAmountTotal()) >= 0) {
                    remainingTerms++;
                }

                // 累加剩余本金
                BigDecimal repaymentPrincipal = ObjUtil.defaultIfNull(info.getRepaymentPrincipal(), BigDecimal.ZERO);
                BigDecimal actuallyPrincipal = ObjUtil.defaultIfNull(info.getActuallyPrincipal(), BigDecimal.ZERO);
                remainingPrincipal = remainingPrincipal.add(repaymentPrincipal.subtract(actuallyPrincipal));

                //累加剩余利息
                BigDecimal repaymentInterest = ObjUtil.defaultIfNull(info.getRepaymentInterest(), BigDecimal.ZERO);
                BigDecimal actuallyInterest = ObjUtil.defaultIfNull(info.getActuallyInterest(), BigDecimal.ZERO);
                remainingInterest = remainingInterest.add(repaymentInterest.subtract(actuallyInterest));
            }

        }
        vo.setRemainingPrincipal(remainingPrincipal);
        vo.setRemainingInstalments(planState ? 0 : repaymentInfos.size() - remainingTerms);
        vo.setRemainingInterest(remainingInterest);
        vo.setAlreadyRepaidInstalments(remainingTerms);
    }


    /**
     * 设置还款信息
     */
    private void setRepaymentStatus(List<RepaymentInfoVO> repaymentInfoVOS, Integer orderId, Integer fundId) {
        if (CollectionUtils.isEmpty(repaymentInfoVOS)) {
            return;
        }

        List<FundRepaymentDeductEntity> deductList = fundRepaymentDeductService.selectByOrderIdList(orderId, fundId);

        Map<Integer, FundRepaymentDeductEntity> latestDeductMap = deductList.stream()
                .filter(i->ObjUtil.isNotNull(i.getTerm()))
                .collect(Collectors.groupingBy(FundRepaymentDeductEntity::getTerm,
                        Collectors.reducing((entity1, entity2) ->
                                entity1.getCreateTime().isAfter(entity2.getCreateTime()) ? entity1 : entity2)))
                .entrySet().stream()
                .filter(entry -> entry.getValue().isPresent())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().get()));

        repaymentInfoVOS.forEach(repaymentInfoVO -> {
            Integer term = repaymentInfoVO.getTerm();
            FundRepaymentDeductEntity deductEntity = latestDeductMap.get(term);
            if (ObjUtil.isNotNull(deductEntity)) {
                repaymentInfoVO.setRepayStatus(deductEntity.getRepayStatus());
                repaymentInfoVO.setRepayType(deductEntity.getRepayType());
                repaymentInfoVO.setBizType(deductEntity.getBizType());
            }
        });

        List<OrderFeeDetailExpandTypeEnum> offlineFeeEnumList = List.of(OrderFeeDetailExpandTypeEnum.INSTALLMENT_SECURITY_DEPOSIT, OrderFeeDetailExpandTypeEnum.MONTHLY_REPAYMENT,
                OrderFeeDetailExpandTypeEnum.SETTLE_REPAYMENT, OrderFeeDetailExpandTypeEnum.EARLY_SETTLEMENT_PENALTY, OrderFeeDetailExpandTypeEnum.INSTALLMENT_SERVICE_FEE);

        List<OrderPayApplicationInfoEntity> orderPayApplicationInfoEntities = orderPayApplicationMapper.selectList(new LambdaQueryWrapper<OrderPayApplicationInfoEntity>()
                .eq(OrderPayApplicationInfoEntity::getOrderId, orderId)
                .in(OrderPayApplicationInfoEntity::getFeeType, offlineFeeEnumList)
                .eq(OrderPayApplicationInfoEntity::getPayeeType, PayApplicationPayeeTypeEnum.HUI_FENG)
                .orderByAsc(OrderPayApplicationInfoEntity::getCreateTime)
                .eq(OrderPayApplicationInfoEntity::getDeleteFlag, 0)
        );
        Map<Integer, OrderPayApplicationInfoEntity> orderPayApplicationInfoMap = orderPayApplicationInfoEntities.stream()
                .collect(Collectors.toMap(
                        OrderPayApplicationInfoEntity::getRepaymentTerm,
                        entity -> entity,
                        (existing, replacement) ->
                                existing.getCreateTime().isBefore(replacement.getCreateTime()) ? replacement : existing
                ));

        repaymentInfoVOS.forEach(repaymentInfoVO -> {
            OrderPayApplicationInfoEntity orderPayApplicationInfoEntity = orderPayApplicationInfoMap.get(repaymentInfoVO.getTerm());
            if (ObjUtil.isNull(orderPayApplicationInfoEntity)) {
                return;
            }
            PayApplicationNodeEnums currentNode = orderPayApplicationInfoEntity.getCurrentNode();
            RepaymentOfflineStatusEnum offlinePayNode =
                    switch (currentNode) {
                        case FAIL -> RepaymentOfflineStatusEnum.REFUSED;
                        case SUCCESS -> RepaymentOfflineStatusEnum.SUCCESS;
                        case ACCOUNTANT_APPLY -> RepaymentOfflineStatusEnum.REJECTED;
                        default -> RepaymentOfflineStatusEnum.APPLYING;
                    };
            repaymentInfoVO.setOfflinePayStatus(offlinePayNode);
            repaymentInfoVO.setOfflineFeeType(orderPayApplicationInfoEntity.getFeeType());
        });

    }


}
