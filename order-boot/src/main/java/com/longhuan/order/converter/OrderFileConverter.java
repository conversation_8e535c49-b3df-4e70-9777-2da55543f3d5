package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.SaveOrderFileDTO;
import com.longhuan.order.pojo.entity.OrderFileEntity;
import com.longhuan.order.pojo.vo.OrderFileVo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 订单文件转换器
 *
 * <AUTHOR>
 * @date 2024/07/24
 */
@Mapper(componentModel = "spring")
public interface OrderFileConverter {
    /**
     * dto2 实体
     *
     * @param saveOrderFileDTO 保存订单文件 DTO
     * @return {@link OrderFileEntity }
     */
    OrderFileEntity dto2Entity(SaveOrderFileDTO saveOrderFileDTO);

    List<OrderFileVo> entityList2VoList(List<OrderFileEntity> orderFileEntity);
}
