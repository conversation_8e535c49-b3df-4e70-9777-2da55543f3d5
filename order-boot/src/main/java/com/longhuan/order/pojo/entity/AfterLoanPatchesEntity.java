package com.longhuan.order.pojo.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.longhuan.common.core.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 贷后补件表
 * @TableName lh_after_loan_patches
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "客户信息维护表", description = "贷后补件表")
@TableName("lh_after_loan_patches")
@KeySequence("lh_after_loan_patches_id_seq")
public class AfterLoanPatchesEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId
    private Integer id;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 贷后状态
     */
    private Integer afterLoanStatus;

    /**
     * GPS安装状态（0:未安装,1:安装中,2:已安装,3:拆除中,4:已拆除）
     */
    private Integer gpsState;

    /**
     * 结清状态 
     */
    private Integer payoffState;
    /**
     * 补件提交时间
     */
    private LocalDateTime patchesSubmissionTime;

    /**
     * 上传资方资料标识 0:未上传,1:已上传
     */
    private Integer fundUploadFlag;
    /**
     * 抵押时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "抵押时间")
    private LocalDateTime mortgageTime;

    /**
     *
     */
    private Integer thPatches;

    /**
     * 资方补件失败次数
     */
    private Integer fundPatchCount;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}