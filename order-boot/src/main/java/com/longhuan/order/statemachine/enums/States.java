package com.longhuan.order.statemachine.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

@Getter
public enum States {

    ORDER_FREEZE(-3000,"订单冻结",true),

    SYSTEM_TERMINAL(-2000, "系统终止", true),
    PROCESS_TERMINAL(-1000, "流程终止", true),

    // 预审批
    SUPPLEMENT_ID_CARD(200, "身份证", true),
    SUPPLEMENT_VEHICLE_INFO(300,"行驶证",true),
    SIGN_AUTHORIZATION_CONTRACT(400,"授权书",true),
    PRE_APPROVAL(500, "预审批", true),
    RISK_CONTROL(600, "风控", true),
    FUNDS_PRE_APPROVAL(700, "资方预审", true),

    BUSINESS_ADDED_INFO(1000, "待业务补录", true),
    ADDED_BUSINESS(1050, "补录进件信息", true),
    // 页签
    ADDED_BUSINESS_CUSTOMER(1051, "客户信息", true),
    ADDED_BUSINESS_CAR(1052, "车辆信息", true),
    ADDED_BUSINESS_ORDER(1053, "订单信息", true),
    ADDED_BUSINESS_ATTACHMENT(1054, "附件清单", true),

    QUALITY_INSPECTION(1300, "客服质检", true),

    QUALITY_INSPECTION_FINISH(1350, "Al智能审核", true),

    STORE_EVALUATION(1200, "门店车评", true),

    OVERALL_REVIEW(1400, "总评复核", true),

    RISK_FIRST_APPROVE(1500, "风控初审", true),
    RISK_FIRST_APPROVE_ASSIGN(1510, "风控初审分配", true),

    RISK_FINAL_APPROVE(2000, "风控终审", true),

    FUNDS_FINAL_APPROVE(2500, "资方终审", true),

    CUSTOMER_CONFIRM(2700, "客户确认", true),
    // 页签
    CUSTOMER_CONFIRM_ORDER(2720, "确认额度", true),
    CUSTOMER_CONFIRM_IMAGE(2723, "补充资料", true),
    CUSTOMER_CONFIRM_BANKCARD(2725, "绑银行卡", true),

    CUSTOMER_APPOINTMENT(3000, "客户预约", true),


    MANAGER_INTERVIEW(3030, "面签", true),
    CONTRACT_SIGNING(3070, "合同签约", true),
    GPS_INSTALL_APPLY(3050, "安装GPS", true),
    MORTGAGE_PENDING(3530, "抵押待办", true),
    GPS_FEE_PAYMENT(3080, "GPS费用支付", true),

//    GPS_INSTALL(3055, "安装GPS", true),
    GPS_INSTALL_FINISH(3058, "安装GPS完成", true),

//    MORTGAGE_PAYMENT(3500, "抵押放款", false),

    PAYMENT_APPLY_INFORMATION(3550, "请款资料", true),
    PAYMENT_CONTRACT_APPROVAL(4000, "合同审批", true),

    FUNDS_PAYMENT_APPROVAL(4500, "资方放款审批", true),

    FUNDS_PAYMENT_PROCESS(4530, "放款中", true),
    FUNDS_PAYMENT_FAIL(4550, "放款失败", true),


    PAYMENT_SUCCESS(5000, "放款成功", true),
    SETTLED(8000, "已结清", true),
    UNKNOWN(0, "",true);

    @EnumValue
    @JsonValue
    private final Integer node;
    private final String desc;
    private final Boolean valid;

    States(Integer state, String desc, Boolean valid) {
        this.node = state;
        this.desc = desc;
        this.valid = valid;
    }

    public static States getNode(Integer state) {
        return Arrays.stream(States.values()).filter(value -> Objects.equals(value.getNode(), state))
                .findFirst().orElse(UNKNOWN);

    }


    /**
     * status是否合法
     *
     * @param node
     * @return
     */
    public static boolean isIn(Integer node) {
        return Arrays.asList(States.values()).parallelStream().
                anyMatch(value -> Objects.equals(value.getNode(), node));

    }

    /**
     * 判断status是否相等
     *
     * @param node
     * @param statusEnum
     * @return
     */
    public static boolean equals(Integer node, States statusEnum) {
        return Objects.equals(node, statusEnum.getNode());

    }

    /**
     * status-->statusEnum
     *
     * @param node
     * @return
     */
    public static States getByNode(Integer node) {
        Optional<States> statusEnumOptional = Arrays.asList(States.values()).parallelStream()
                .filter(statusEnum -> Objects.equals(node, statusEnum.getNode())).findAny();

        return statusEnumOptional.orElse(UNKNOWN);

    }

    /**
     * 判断status是否合法
     *
     * @param node
     * @param statusEnums
     * @return
     */
    public static boolean isIn(Integer node, States... statusEnums) {
        return Arrays.asList(statusEnums).parallelStream().
                anyMatch(value -> Objects.equals(value.getNode(), node));

    }

    // 根据code获取对应的枚举项描述
    public static String getDescriptionByCode(int node) {
        for (States ce : States.values()) {
            if (ce.getNode() == node) {
                return ce.getDesc();
            }
        }
        return "未知状态码";
    }


}