package com.longhuan.approve.boot.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.longhuan.approve.api.constants.CopperCarDeptMortgageEnums;
import com.longhuan.approve.api.pojo.dto.HengTongCopperCarDeptMortgageDTO;
import com.longhuan.approve.api.pojo.dto.zhongheng.coppercardept.HengTongCopperCarDeptMortgageCancelDTO;
import com.longhuan.approve.api.pojo.dto.zhongheng.coppercardept.SearchOrderStatusDTO;
import com.longhuan.approve.api.pojo.vo.zhongheng.coppercardept.CopperCarDeptOrderStatusVO;
import com.longhuan.approve.boot.config.ZhongHengConfig;
import com.longhuan.approve.boot.enums.FundApplyNodeEnums;
import com.longhuan.approve.boot.enums.FundApplyNodeStatusEnum;
import com.longhuan.approve.boot.enums.FundMortgageStatusEnum;
import com.longhuan.approve.boot.enums.fumin.MortgageEnums;
import com.longhuan.approve.boot.enums.zhongheng.ZhongHengCopperCarDeptEventEnum;
import com.longhuan.approve.boot.feign.*;
import com.longhuan.approve.boot.fund.finall.FinalFundInfoService;
import com.longhuan.approve.boot.mapper.*;
import com.longhuan.approve.boot.pojo.dto.CustomerBaseDTO;
import com.longhuan.approve.boot.pojo.dto.FundPreCarBaseDTO;
import com.longhuan.approve.boot.pojo.dto.hengtong.coppercardept.CancelOrderDTO;
import com.longhuan.approve.boot.pojo.dto.hengtong.coppercardept.CopperCarApiResult;
import com.longhuan.approve.boot.pojo.dto.hengtong.coppercardept.CreateOrderDTO;
import com.longhuan.approve.boot.pojo.dto.hengtong.coppercardept.GetOrderStatusDTO;
import com.longhuan.approve.boot.pojo.entity.*;
import com.longhuan.approve.boot.pojo.vo.hengtong.coppercardept.CopperCarDeptCancelOrderVO;
import com.longhuan.approve.boot.pojo.vo.hengtong.coppercardept.CopperCarDeptStatusCallbackVO;
import com.longhuan.approve.boot.service.HengTongCopperCarDeptService;
import com.longhuan.approve.boot.utils.hengtong.coppercardept.AESCryptoUtil;
import com.longhuan.common.core.constant.FundEnum;
import com.longhuan.common.core.result.Result;
import com.longhuan.common.core.util.NumberUtils;
import com.longhuan.common.web.exception.BusinessException;
import com.longhuan.order.pojo.dto.MortgageStateUpdateDTO;
import com.longhuan.resource.pojo.dto.ResourceFileCodeDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class HengTongCopperCarDeptServiceImpl implements HengTongCopperCarDeptService {

    private final HengTongCopperCarDeptFeign hengTongCopperCarDeptFeign;
    private final FundApproveMapper fundApproveMapper;
    private final FinalFundInfoMapper finalFundInfoMapper;
    private final OrderFileMapper orderFileMapper;
    private final ResourceFeign resourceFeign;
    private final ZhongHengConfig zhongHengConfig;
    private final ObjectMapper objectMapper;
    private final OrderInfoMapper orderInfoMapper;
    private final FundUndoMortgageInfoMapper fundUndoMortgageInfoMapper;
    private final OrderFeign orderFeign;
    private final CustomerMortgageInfoMapper customerMortgageInfoMapper;
    private final FinalFundInfoService finalFundInfoService;
    private final OrderAmountMapper orderAmountMapper;

    /**
     * 创建订单并处理相关逻辑
     *
     * @param dto 订单数据传输对象
     * @return 返回生成的订单令牌
     */
    @Override
    public String createOrder(HengTongCopperCarDeptMortgageDTO dto) {
        log.info("HengTongCopperCarDeptServiceImpl.createOrder dto:{}", JSONUtil.toJsonStr(dto));

        OrderInfoEntity orderInfo = orderInfoMapper.selectById(dto.getOrderId());
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                        .eq(FinalFundInfoEntity::getOrderId, dto.getOrderId())
                        .eq(FinalFundInfoEntity::getFundId, orderInfo.getFundId())
                        .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                        .orderByDesc(FinalFundInfoEntity::getCreateTime)
                , false);
        if (ObjUtil.isNull(finalFundInfo)) {
            log.info("HengTongCopperCarDeptServiceImpl.createOrder finalFundInfo is null orderId:{}", dto.getOrderId());
            throw new BusinessException("未查询到抵押相关信息");
        }
        //判断抵押状态 如果抵押中 则不允许再次申请
        //不要重复调用订单写入接口（状态01之前是可以重复调用订单写入接口） 抵押、解抵撤回 、创建失败状态可以重新发起订单写入
        if (StrUtil.isNotBlank(finalFundInfo.getRedirectUrl())
                && (FundMortgageStatusEnum.WAIT.equals(finalFundInfo.getMortgageState())
                || FundMortgageStatusEnum.REMOVE_ING.equals(finalFundInfo.getMortgageState())
        )) {
            log.info("HengTongCopperCarDeptServiceImpl.createOrder mortgageState is mortgageing orderId:{}", dto.getOrderId());
            return finalFundInfo.getRedirectUrl();
        }
        //查询状态
        if (StrUtil.equals(dto.getType(), "J") && StrUtil.isNotBlank(finalFundInfo.getRedirectUrl())) {
            CopperCarDeptMortgageEnums copperCarDeptStatusEnum  = null;
            try {
                SearchOrderStatusDTO searchOrderStatusDTO = new SearchOrderStatusDTO();
                searchOrderStatusDTO.setOrderId(dto.getOrderId());
                searchOrderStatusDTO.setOrderType("E");
                String copperCarDeptStatus = getOrderStatus(searchOrderStatusDTO);
                log.info("HengTongCopperCarDeptServiceImpl.createOrder orderId:{} copperCarDeptStatus:{}", dto.getOrderId(), copperCarDeptStatus);
                copperCarDeptStatusEnum = CopperCarDeptMortgageEnums.getByCode(copperCarDeptStatus);
            } catch (BusinessException e) {
                log.info("HengTongCopperCarDeptServiceImpl.createOrder BusinessException getOrderStatus error:{}", e.getMessage(), e);
                throw e;
            }catch (Exception e) {
                log.info("HengTongCopperCarDeptServiceImpl.createOrder Exception getOrderStatus error:{}", e.getMessage(), e);
                throw new BusinessException("查询抵押状态异常");
            }

            if (!ObjUtil.equals(CopperCarDeptMortgageEnums.COMPLETED, copperCarDeptStatusEnum)) {
                log.info("HengTongCopperCarDeptServiceImpl.createOrder copperCarDeptStatusEnum is not COMPLETED orderId:{} copperCarDeptStatusEnum:{}", dto.getOrderId(), copperCarDeptStatusEnum);
                String currentStatus = ObjUtil.isNotNull(copperCarDeptStatusEnum) ? copperCarDeptStatusEnum.getDescription() : "未知状态";
                throw new BusinessException("订单抵押未完成,请勿解抵,当前订单：" + currentStatus);
            }
        }


        // 获取客户基本信息
        CustomerBaseDTO customerFinalBaseInfo = fundApproveMapper.getCustomerFinalBaseInfo(dto.getOrderId());
        log.info("HengTongCopperCarDeptServiceImpl.createOrder orderId:{} customerFinalBaseInfo:{} ", dto.getOrderId(), JSONUtil.toJsonStr(customerFinalBaseInfo));

        // 获取车辆基本信息
        FundPreCarBaseDTO carFinalBaseInfo = fundApproveMapper.getCarFinalBaseInfo(dto.getOrderId());
        log.info("HengTongCopperCarDeptServiceImpl.createOrder orderId:{} carFinalBaseInfo:{} ", dto.getOrderId(), JSONUtil.toJsonStr(carFinalBaseInfo));


        // 对应文件code
        String faceRecognition = "FACE_RECOGNITION"; // 活体照片
        String cardFront = "CARD_FRONT"; // 身份证人像面
        String cardBack = "CARD_BACK"; // 身份证国徽面

        // 获取文件配置信息
        ResourceFileCodeDTO resourceFileCodeDTO = new ResourceFileCodeDTO();
        resourceFileCodeDTO.setCodes(List.of(faceRecognition, cardFront, cardBack));
        Map<String, Integer> fileConfigMap = resourceFeign.selectFileConfigByCodeList(resourceFileCodeDTO).getData();
        Map<Integer, String> fileIdToCodeMap = fileConfigMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
        List<OrderFileEntity> orderFileList = orderFileMapper.selectList(new LambdaQueryWrapper<OrderFileEntity>()
                .eq(OrderFileEntity::getOrderId, dto.getOrderId())
                .in(OrderFileEntity::getFileId, fileConfigMap.values())
                .eq(OrderFileEntity::getDeleteFlag, 0)
        );
        Map<String, String> fileMap = new HashMap<>();
        for (OrderFileEntity orderFileEntity : orderFileList) {
            if (orderFileEntity.getFileId() == null) continue;
            String code = fileIdToCodeMap.get(orderFileEntity.getFileId());
            if (StrUtil.isNotBlank(code)) {
                String viewResourceUrl = getViewResourceUrl(orderFileEntity.getResourceId());
                fileMap.put(code, viewResourceUrl);
            }
        }

        String mortgageContractNo = finalFundInfo.getMortgageContractNo();
        if (StrUtil.isBlank(mortgageContractNo)) {
            mortgageContractNo = NumberUtils.getContractNumber();
        }
        Integer agentRadio = dto.getAgentRadio();
        if (ObjUtil.equals(dto.getType(), "J") && ObjUtil.isNull(agentRadio)) {
            //获取代理方式
            CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                    .eq(CustomerMortgageInfoEntity::getOrderId, dto.getOrderId())
                    .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                    .last("LIMIT 1"));
            if (ObjUtil.isNull(customerMortgageInfoEntity)) {
                throw new BusinessException("未查询到抵押相关信息");
            }
            agentRadio = customerMortgageInfoEntity.getAgentRadio();

        }

        String mortgageType = ObjUtil.equals(dto.getType(), "E") ? "E" : "J";


        // 获取订单金额信息
        OrderAmountEntity orderAmountEntity = orderAmountMapper.selectOne(new LambdaQueryWrapper<OrderAmountEntity>()
                        .eq(OrderAmountEntity::getOrderId, dto.getOrderId())
                        .eq(OrderAmountEntity::getDeleteFlag, 0)
                        .orderByDesc(OrderAmountEntity::getCreateTime)
                , false);

        // 获取贷款发放时间
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String startTime = sdf.format(date);

        // 获取贷款到期时间
        LocalDate localDate = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        LocalDate newDate = localDate.plusMonths(orderInfo.getTerm());

        // 格式化输出 yyyy-MM-dd
        DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE;
        String endTime = newDate.format(formatter);
        // 构建创建订单DTO
        CreateOrderDTO createOrderDTO = new CreateOrderDTO()
                .setEvent(ZhongHengCopperCarDeptEventEnum.CREATE_ORDER.getCode())
                .setOrderId(finalFundInfo.getEntrustReceiptNo())
                .setOrderType(mortgageType) // 抵押固定 E
                .setDealChannel(2) // 固定 2
                .setCarNature(1) // 固定私户
                .setMortgageNumber(mortgageContractNo)
                .setMortgageSigntime(DateUtil.format(DateUtil.date(), "yyyy-MM-dd")) // 发送时间
                .setIdCardTypeCode("A") // 固定 A
                .setCarOwnerIdNumber(customerFinalBaseInfo.getIdNumber())
                .setCarOwnerIdName(customerFinalBaseInfo.getName())
                .setCarOwnerPhone(customerFinalBaseInfo.getPhone())
                .setCarFrameNumber(carFinalBaseInfo.getVin())
                .setCarLicenceNumber(carFinalBaseInfo.getVehicleNumber())
                .setCarKind(ObjUtil.equals(carFinalBaseInfo.getVehicleNumber().length(), 8) ? 52 : 2) // 新能源 52 非新能源 2 车牌8位就是新能源
                .setAgentRadio(agentRadio)
                .setCarSeriesModel(carFinalBaseInfo.getBrand() + "/" + carFinalBaseInfo.getVehicleSeries() + "/" + carFinalBaseInfo.getVehicleModel()) // 车辆信息 品牌/车系/车型（斜杆区分）
                .setCarOwnerIdZImage(fileMap.get(cardFront)) // 证件照正面 (私户)
                .setCarOwnerIdFImage(fileMap.get(cardBack)) // 证件照反面 (私户)
                .setCarOwnerImages(fileMap.get(faceRecognition)) // 人脸半身照 (私户)
                .setOrder_id(customerFinalBaseInfo.getOrderNumber())
                .setLoanAmount(orderAmountEntity != null ? orderAmountEntity.getCustomerConfirmAmount().toString() : null)
                .setLoanTerm(orderInfo.getTerm().toString())
                .setContractStartTime(startTime)
                .setContractEndTime(endTime);

        // 加密订单信息
        String encrypt = AESCryptoUtil.encrypt(toJsonStr(createOrderDTO), zhongHengConfig.getCopperKey(), zhongHengConfig.getCopperIv());
        CopperCarApiResult<String> result = null;
        try {
            // 调用远程服务创建订单
            String resultJson = hengTongCopperCarDeptFeign.createOrder(encrypt);
            log.info("HengTongCopperCarDeptServiceImpl.createOrder resultJson:{}", resultJson);
            result = toApiBean(String.class, resultJson);
            // 如果订单创建失败，抛出业务异常
            if (!CopperCarApiResult.isSuccess(result)) {
                log.error("HengTongCopperCarDeptServiceImpl.createOrder error: {}", result.getMsg());
                throw new BusinessException(result.getMsg());
            }
        } catch (BusinessException e) {
            log.error("HengTongCopperCarDeptServiceImpl.createOrder BusinessException error: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("HengTongCopperCarDeptServiceImpl.createOrder error: {}", e.getMessage(), e);
            if (ObjUtil.isNotNull(result) && StrUtil.isNotBlank(result.getMsg())) {
                throw new BusinessException(result.getMsg());
            } else {
                throw new BusinessException("请求异常,请稍后再试");
            }
        } finally {
            log.info("HengTongCopperCarDeptServiceImpl.createOrder result:{}", result);

            String remark = ObjUtil.isNotNull(result) && !CopperCarApiResult.isSuccess(result) ? result.getMsg() : null;
            FundApplyNodeEnums fundNode = "E".equals(dto.getType()) ? FundApplyNodeEnums.MORTGAGE_H5_PAGE : FundApplyNodeEnums.MORTGAGE_H5_PAGE_REMOVE;
            FundMortgageStatusEnum mortgageStatus = "E".equals(dto.getType()) ? FundMortgageStatusEnum.WAIT : FundMortgageStatusEnum.REMOVE_ING;
            LambdaUpdateWrapper<FinalFundInfoEntity> luw = new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .eq(FinalFundInfoEntity::getOrderId, dto.getOrderId())
                    .eq(FinalFundInfoEntity::getFundId, orderInfo.getFundId())
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                    .set(FinalFundInfoEntity::getFundNode, fundNode)
//                    .set(FinalFundInfoEntity::getMortgageState, mortgageStatus)
                    .set(FinalFundInfoEntity::getMortgageContractNo, mortgageContractNo)
                    .set(FinalFundInfoEntity::getAgentRadio, dto.getAgentRadio())
                    ;
            if (ObjUtil.isNull(result) || !CopperCarApiResult.isSuccess(result)) {
                luw.set(FinalFundInfoEntity::getFundNodeStatus, FundApplyNodeStatusEnum.FAILURE);
            }
            luw.set(FinalFundInfoEntity::getRemark, remark);
            if (ObjUtil.isNotNull(result) && CopperCarApiResult.isSuccess(result)) {
                luw.set(FinalFundInfoEntity::getRedirectUrl, result.getData());
                luw.set(FinalFundInfoEntity::getFundNodeStatus, FundApplyNodeStatusEnum.SUCCESS);
                luw.set(FinalFundInfoEntity::getEntrustReceiptNo, null);
            }
            finalFundInfoMapper.update(luw);
        }
        //如果再次抵押 将(抵押撤销 解抵 解抵撤销)的的记录  变为历史记录 重新来一遍新的流程
        if (ObjUtil.equals(dto.getType(), "E")) {
            fundUndoMortgageInfoMapper.update(new LambdaUpdateWrapper<FundUndoMortgageInfoEntity>()
                    .eq(FundUndoMortgageInfoEntity::getOrderId, dto.getOrderId())
                    .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                    .set(FundUndoMortgageInfoEntity::getHistory, 1)
                    .set(FundUndoMortgageInfoEntity::getDeleteFlag, 1)

            );
        }

        //解抵 存入记录表
        if (ObjUtil.equals(dto.getType(), "J")) {
            fundUndoMortgageInfoMapper.update(new FundUndoMortgageInfoEntity(), new LambdaUpdateWrapper<FundUndoMortgageInfoEntity>()
                    .eq(FundUndoMortgageInfoEntity::getOrderId, dto.getOrderId())
                    .eq(FundUndoMortgageInfoEntity::getOperateType, "2")
                    .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                    .set(FundUndoMortgageInfoEntity::getHistory, 1)
                    .set(FundUndoMortgageInfoEntity::getDeleteFlag, 1)
            );



            FundUndoMortgageInfoEntity fundUndoMortgageInfoEntity = new FundUndoMortgageInfoEntity()
                    .setFundId(orderInfo.getFundId())
                    .setOrderId(dto.getOrderId())
                    .setApplyNo(finalFundInfo.getCreditReqNo())
                    .setChannelCode(zhongHengConfig.getChannelCode())
                    .setOperateType("2")
                    .setMortgageStatus(orderInfo.getMortgageState().toString())
                    .setCancel(0)
                    .setHistory(0);

            if (ObjUtil.isNotNull(orderInfo)) {
                if (Objects.equals(orderInfo.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_SUCCESS.getCode())) {
                    fundUndoMortgageInfoEntity.setMortgageStatus(MortgageEnums.MORTGAGE_STATUS_CANCEL_PROCESSING.getCode().toString());
                }
                if (Objects.equals(orderInfo.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_PRE.getCode())) {
                    fundUndoMortgageInfoEntity.setMortgageStatus(MortgageEnums.MORTGAGE_STATUS_CANCEL_PROCESSING_HAVE.getCode().toString());
                }
            }
            fundUndoMortgageInfoMapper.insert(fundUndoMortgageInfoEntity);
        }

        // 返回url
        return result.getData();
    }

    /**
     * 订单撤销
     * 根据订单ID撤销抵押或解抵订单，并更新相关状态。
     *
     * @param cancelDTO 包含订单ID和订单类型的DTO
     * @return 铜车署订单ID
     */
    @Override
    public String cancelOrder(HengTongCopperCarDeptMortgageCancelDTO cancelDTO) {
        // 获取订单ID并记录日志
        Integer orderId = cancelDTO.getOrderId();
        log.info("HengTongCopperCarDeptServiceImpl.cancelOrder orderId:{}", orderId);
        //查询状态
        CopperCarDeptMortgageEnums copperCarDeptStatusEnum  = null;
        try {
            SearchOrderStatusDTO searchOrderStatusDTO = new SearchOrderStatusDTO();
            searchOrderStatusDTO.setOrderId(orderId);
            searchOrderStatusDTO.setOrderType(cancelDTO.getType());
            String copperCarDeptStatus = getOrderStatus(searchOrderStatusDTO);
            log.info("HengTongCopperCarDeptServiceImpl.cancelOrder orderId:{} copperCarDeptStatus:{}", orderId, copperCarDeptStatus);
            copperCarDeptStatusEnum = CopperCarDeptMortgageEnums.getByCode(copperCarDeptStatus);
        } catch (BusinessException e) {
            log.info("HengTongCopperCarDeptServiceImpl.cancelOrder BusinessException getOrderStatus error:{}", e.getMessage(), e);
            throw e;
        }catch (Exception e) {
            log.info("HengTongCopperCarDeptServiceImpl.cancelOrder Exception getOrderStatus error:{}", e.getMessage(), e);
            throw new BusinessException("查询抵押状态异常");
        }

        if (!(
                ObjUtil.equals(CopperCarDeptMortgageEnums.CREATED, copperCarDeptStatusEnum)||
                ObjUtil.equals(CopperCarDeptMortgageEnums.OWNER_SINGED, copperCarDeptStatusEnum)||
                ObjUtil.equals(CopperCarDeptMortgageEnums.PROXY_SINGED, copperCarDeptStatusEnum)||
                ObjUtil.equals(CopperCarDeptMortgageEnums.SIGNED, copperCarDeptStatusEnum)||
                ObjUtil.equals(CopperCarDeptMortgageEnums.PUSH_TO_TMRI, copperCarDeptStatusEnum)||
                ObjUtil.equals(CopperCarDeptMortgageEnums.WAIT_CHOOSE_PROXY, copperCarDeptStatusEnum)||
                ObjUtil.equals(CopperCarDeptMortgageEnums.PROXY_FINISHED, copperCarDeptStatusEnum)
            )){
            throw new BusinessException("主动撤消订单交科所成功受理之前发起");
        }


        // 查询订单信息
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        log.info("HengTongCopperCarDeptServiceImpl.cancelOrder orderInfo:{}", JSONUtil.toJsonStr(orderInfo));
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));

        // 获取我方订单号和订单类型
        String order_id = orderInfo.getOrderNumber();
        String type = cancelDTO.getType();

        // 构建撤销订单的DTO
        CancelOrderDTO dto = new CancelOrderDTO()
                .setEvent(ZhongHengCopperCarDeptEventEnum.CANCEL_ORDER.getCode())
                .setOrder_id(order_id)
                .setOrderType(cancelDTO.getType());

        // 加密DTO并发送撤销请求
        String encrypt = AESCryptoUtil.encrypt(toJsonStr(dto), zhongHengConfig.getCopperKey(), zhongHengConfig.getCopperIv());

        // 初始化结果和铜车署订单ID
        CopperCarApiResult<CopperCarDeptCancelOrderVO> result = null;
        String copperCarId = null;

        try {
            // 发送撤销请求并解析返回结果
            String resultJson = hengTongCopperCarDeptFeign.cancelOrder(encrypt);
            log.info("HengTongCopperCarDeptServiceImpl.cancelOrder resultJson:{}", resultJson);
            result = toApiBean(CopperCarDeptCancelOrderVO.class, resultJson);

            // 如果请求成功，获取铜车署订单ID
            if (!CopperCarApiResult.isSuccess(result)) {
                log.error("HengTongCopperCarDeptServiceImpl.cancelOrder error: {}", result.getMsg());
                throw new BusinessException(result.getMsg());
            }
            copperCarId = result.getData().getOrderId();
        } catch (BusinessException e) {
            log.error("HengTongCopperCarDeptServiceImpl.cancelOrder BusinessException error: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("HengTongCopperCarDeptServiceImpl.cancelOrder error: {}", e.getMessage(), e);
            throw new BusinessException("请求异常,请稍后再试");
        } finally {
            // 根据订单类型设置不同的节点
            FundApplyNodeEnums fundNode = "E".equals(type) ? FundApplyNodeEnums.MORTGAGE_REVOKE : FundApplyNodeEnums.MORTGAGE_RELEASE;
            Integer fundId = orderInfo.getFundId();
            // 构建更新条件
            LambdaUpdateWrapper<FinalFundInfoEntity> luw = new LambdaUpdateWrapper<FinalFundInfoEntity>()
                    .eq(FinalFundInfoEntity::getOrderId, orderId)
                    .eq(FinalFundInfoEntity::getFundId, fundId)
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                    .set(FinalFundInfoEntity::getFundNode, fundNode)
                    .set(FinalFundInfoEntity::getFundNodeStatus, FundApplyNodeStatusEnum.SUCCESS);

            // 如果撤销请求失败，更新状态为失败并记录失败原因
            if (CopperCarApiResult.isSuccess(result)) {
                luw.set(FinalFundInfoEntity::getMortgageState, "E".equals(type) ? FundMortgageStatusEnum.NONE : FundMortgageStatusEnum.PASS);
            }
            if (!CopperCarApiResult.isSuccess(result)) {
                luw.set(FinalFundInfoEntity::getRemark, result != null ? result.getMsg() : "取消失败")
                    .set(FinalFundInfoEntity::getMortgageState, "E".equals(type) ? FundMortgageStatusEnum.WAIT : FundMortgageStatusEnum.REMOVE_ING);
            }
            finalFundInfoMapper.update(luw);

            // 更新undo表撤回记录
            FinalFundInfoEntity finalFundInfoEntity = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.getFundEnum(fundId));

            if (ObjUtil.equals(type, "E")) {
                fundUndoMortgageInfoMapper.update(new LambdaUpdateWrapper<FundUndoMortgageInfoEntity>()
                        .set(FundUndoMortgageInfoEntity::getHistory,1)
                        .set(FundUndoMortgageInfoEntity::getDeleteFlag,1)
                        .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                        .eq(FundUndoMortgageInfoEntity::getOperateType, "1")
                        .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                        .eq(FundUndoMortgageInfoEntity::getCancel, 1)
                );
                FundUndoMortgageInfoEntity fundUndoMortgageInfoEntity = new FundUndoMortgageInfoEntity().setFundId(fundId)
                        .setOrderId(orderId)
                        .setApplyNo(finalFundInfoEntity.getCreditReqNo())
                        .setChannelCode(zhongHengConfig.getChannelCode())
                        .setOperateType("1")
                        .setCancel(1)
                        .setHistory(0);

                //抵押撤回失败  变为抵押撤销中 等待回调或者主动查询 变为终态时才可以解抵或者重新发起抵押
                if (!CopperCarApiResult.isSuccess(result)) {
                    fundUndoMortgageInfoEntity.setMortgageStatus(MortgageEnums.MORTGAGE_STATUS_PROCESSING.getCode().toString());
                }
                if (CopperCarApiResult.isSuccess(result)) {
                    fundUndoMortgageInfoEntity.setMortgageStatus(MortgageEnums.MORTGAGE_STATUS_WAITING.getCode().toString());
                }
                fundUndoMortgageInfoEntity.setDeleteFlag(1);
                fundUndoMortgageInfoEntity.setHistory(1);

                fundUndoMortgageInfoMapper.insert(fundUndoMortgageInfoEntity);
            }
            //撤回成功 失效解抵记录
            if (ObjUtil.equals(type, "J")) {
                if (CopperCarApiResult.isSuccess(result)) {
                    LambdaUpdateWrapper<FundUndoMortgageInfoEntity> undoLuw = new LambdaUpdateWrapper<>();
                    undoLuw.eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                            .eq(FundUndoMortgageInfoEntity::getOperateType, "2")
                            .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                            .set(FundUndoMortgageInfoEntity::getDeleteFlag, 1)
                            .set(FundUndoMortgageInfoEntity::getCancel, 1)
                            .set(FundUndoMortgageInfoEntity::getHistory, 1);
                    fundUndoMortgageInfoMapper.update(undoLuw);
                } else {
                    FundUndoMortgageInfoEntity fundUndoMortgageInfoEntity = new FundUndoMortgageInfoEntity().setFundId(fundId)
                            .setOrderId(orderId)
                            .setApplyNo(finalFundInfoEntity.getCreditReqNo())
                            .setChannelCode(zhongHengConfig.getChannelCode())
                            .setOperateType("2")
                            .setCancel(1);

                    //抵押撤回失败  变为抵押撤销中 等待回调或者主动查询 变为终态时才可以解抵或者重新发起抵押
                    if (!CopperCarApiResult.isSuccess(result)) {
                        if (ObjUtil.equals(orderInfo.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_SUCCESS.getCode())) {
                            //无登记证
                            fundUndoMortgageInfoEntity.setMortgageStatus(MortgageEnums.MORTGAGE_STATUS_CANCEL_BACK_FAIL.getCode().toString());
                        }
                        if (ObjUtil.equals(orderInfo.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_PRE.getCode())) {
                            //有登记证
                            fundUndoMortgageInfoEntity.setMortgageStatus(MortgageEnums.MORTGAGE_STATUS_CANCEL_BACK_FAIL_HAVE.getCode().toString());
                        }
                    }
                    if (CopperCarApiResult.isSuccess(result)) {
                        if (ObjUtil.equals(orderInfo.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_SUCCESS.getCode())) {
                            //无登记证
                            fundUndoMortgageInfoEntity.setMortgageStatus(MortgageEnums.MORTGAGE_STATUS_CANCEL_BACK_SUCCESS.getCode().toString());
                        }
                        if (ObjUtil.equals(orderInfo.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_PRE.getCode())) {
                            //有登记证
                            fundUndoMortgageInfoEntity.setMortgageStatus(MortgageEnums.MORTGAGE_STATUS_CANCEL_BACK_SUCCESS_HAVE.getCode().toString());
                        }
                    }
                    fundUndoMortgageInfoEntity.setDeleteFlag(1);
                    fundUndoMortgageInfoEntity.setHistory(1);

                    fundUndoMortgageInfoMapper.insert(fundUndoMortgageInfoEntity);
                }

            }
        }

        // 返回铜车署订单ID
        return copperCarId;
    }


    /**
     * 订单状态查询
     */
    @Override
    public String getOrderStatus(SearchOrderStatusDTO dto) {
        log.info("HengTongCopperCarDeptServiceImpl.getOrderStatus dto:{}", JSONUtil.toJsonStr(dto));
        Integer orderId = dto.getOrderId();
        String orderType = dto.getOrderType();
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        log.info("HengTongCopperCarDeptServiceImpl.getOrderStatus orderInfo:{}", JSONUtil.toJsonStr(orderInfo));
        Assert.notNull(orderInfo, () -> new BusinessException("订单不存在"));
        //我方订单编号
        String order_id = orderInfo.getOrderNumber();

        GetOrderStatusDTO getOrderStatusDTO = new GetOrderStatusDTO()
                .setEvent(ZhongHengCopperCarDeptEventEnum.GET_ORDER_STATUS.getCode())
                .setOrder_id(order_id)
                .setOrderType(orderType);
        String encrypt = AESCryptoUtil.encrypt(toJsonStr(getOrderStatusDTO), zhongHengConfig.getCopperKey(), zhongHengConfig.getCopperIv());
        String resultJson = hengTongCopperCarDeptFeign.getOrderStatus(encrypt);
        log.info("HengTongCopperCarDeptServiceImpl.getOrderStatus resultJson:{}", resultJson);
        CopperCarApiResult<CopperCarDeptOrderStatusVO> result = toApiBean(CopperCarDeptOrderStatusVO.class, resultJson);
        if (!CopperCarApiResult.isSuccess(result)) {
            log.error("HengTongCopperCarDeptServiceImpl.getOrderStatus error: {}", resultJson);
            if (ObjUtil.isNotNull(result) && StrUtil.isNotBlank(result.getMsg())) {
                throw new BusinessException(result.getMsg());
            } else {
                throw new BusinessException("请求异常,请稍后再试");
            }
        }
        handleOrderStatus(order_id, orderType, result.getData());
        log.info("HengTongCopperCarDeptServiceImpl.getOrderStatus end result:{}", JSONUtil.toJsonStr(result));
        CopperCarDeptMortgageEnums mortgageEnums = CopperCarDeptMortgageEnums.getByCode(result.getData().getStatus());
        if (ObjUtil.isNotNull(mortgageEnums)) {
            return mortgageEnums.getCode();
        }
        return null;
    }

    /**
     * 订单状态回调
     */
    @Override
    public boolean handleOrderStatusCallback(CopperCarDeptStatusCallbackVO callbackData) {
        log.info("HengTongCopperCarDeptServiceImpl.handleOrderStatusCallback callbackData:{}", JSONUtil.toJsonStr(callbackData));
        CopperCarDeptOrderStatusVO vo = new CopperCarDeptOrderStatusVO();
        if (!ObjUtil.equals(callbackData.getCode(), "00000")) {
            return false;
        }
        CopperCarDeptStatusCallbackVO.OrderStatusVo data = callbackData.getData();
        vo.setOrderId(data.getOrderId());
        vo.setStatus(data.getStatus());
        vo.setAbnormalDesc(data.getDesc());
        //增加代理人姓名、代理人手机号、邮寄地址
        vo.setProxyName(data.getProxyName());//姓名
        vo.setProxyPhone(data.getProxyPhone());//手机号
        vo.setRecAddress(data.getRecAddress());//邮寄地址
        handleOrderStatus(callbackData.getOrder_id(), callbackData.getOrderType(), vo);
        return true;
    }

    /**
     * 订单状态处理
     *
     * @param vo        订单状态vo
     * @param orderType 订单类型 E:抵押 J:解除抵押
     * @param order_id  我方订单号
     * @return 铜车署订单ID
     */
    @Override
    public String handleOrderStatus(String order_id, String orderType, CopperCarDeptOrderStatusVO vo) {
        log.info("HengTongCopperCarDeptServiceImpl.handleOrderStatus vo:{}", JSONUtil.toJsonStr(vo));
        //判断订单是否存在
        Long orderCount = orderInfoMapper.selectCount(new LambdaQueryWrapper<OrderInfoEntity>()
                .eq(OrderInfoEntity::getOrderNumber, order_id)
        );
        if (orderCount < 1) {
            log.warn("HengTongCopperCarDeptServiceImpl.handleOrderStatus order_id:{}不存在", order_id);
            return vo.getOrderId();
        }
        if (ObjUtil.equals(orderType, "E")) {
            //抵押
            handleMortgage(order_id, orderType, vo);
        }
        if (ObjUtil.equals(orderType, "J")) {
            //解除抵押
            handleRelease(order_id, orderType, vo);
        }


        return vo.getOrderId();
    }

    /**
     * 处理抵押状态
     */
    private void handleMortgage(String order_id, String orderType, CopperCarDeptOrderStatusVO vo) {
        log.info("HengTongCopperCarDeptServiceImpl.handleMortgage start order_id:{} vo:{}", order_id, JSONUtil.toJsonStr(vo));
        if (!ObjUtil.equals(orderType, "E")) {
            return;
        }
        OrderInfoEntity orderInfo = null;
        //先判断是否存在对应铜车署id 不存在再使用order_id查询
        FinalFundInfoEntity finalFundInfoByMortgageNo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getEntrustReceiptNo, vo.getOrderId())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNotNull(finalFundInfoByMortgageNo)) {
            orderInfo = orderInfoMapper.selectById(finalFundInfoByMortgageNo.getOrderId());
            log.info("HengTongCopperCarDeptServiceImpl.handleMortgage no:{} finalFundInfoByMortgageNo:{}", vo.getOrderId(), JSONUtil.toJsonStr(finalFundInfoByMortgageNo));
        } else {
            orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                    .eq(OrderInfoEntity::getOrderNumber, order_id)
                    .eq(OrderInfoEntity::getDeleteFlag, 0)
            );
            log.info("HengTongCopperCarDeptServiceImpl.handleMortgage order_id:{} orderInfo:{}", order_id, JSONUtil.toJsonStr(orderInfo));
        }
        if (null == orderInfo) {
            log.info("HengTongCopperCarDeptServiceImpl.handleMortgage order_id:{} orderInfo is null", order_id);
            return;
        }
        LambdaQueryWrapper<FinalFundInfoEntity> finalFundInfoLqw = new LambdaQueryWrapper<>();
        finalFundInfoLqw.eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
                .eq(FinalFundInfoEntity::getFundId, orderInfo.getFundId())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
        ;
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(finalFundInfoLqw, false);
        if (null == finalFundInfo) {
            log.warn("HengTongCopperCarDeptServiceImpl.handleMortgage order_id:{} finalFundInfo is null", order_id);
            return;
        }

        LambdaUpdateWrapper<FinalFundInfoEntity> luw = new LambdaUpdateWrapper<>();
        luw.set(FinalFundInfoEntity::getFundNode, FundApplyNodeEnums.MORTGAGE_RESULT_QUERY);
        //增加代理人姓名、代理人手机号、邮寄地址
        luw.set(StringUtils.isNotEmpty(vo.getProxyName()),FinalFundInfoEntity::getProxyName,vo.getProxyName());
        luw.set(StringUtils.isNotEmpty(vo.getProxyPhone()),FinalFundInfoEntity::getProxyPhone,vo.getProxyPhone());
        luw.set(StringUtils.isNotEmpty(vo.getRecAddress()),FinalFundInfoEntity::getRecipientAddress,vo.getRecAddress());
        luw.eq(FinalFundInfoEntity::getId, finalFundInfo.getId())
                .set(FinalFundInfoEntity::getFundNodeStatus, FundApplyNodeStatusEnum.SUCCESS)
                .set(FinalFundInfoEntity::getEntrustReceiptNo, vo.getOrderId())
                .set(FinalFundInfoEntity::getRemark, vo.getAbnormalDesc())
                .set(StringUtils.isNotEmpty(vo.getAbnormalDesc()),FinalFundInfoEntity::getFailReason, vo.getAbnormalDesc())
        ;

        String status = vo.getStatus();
        CopperCarDeptMortgageEnums mortgageEnums = CopperCarDeptMortgageEnums.getByCode(status);
        luw.set(FinalFundInfoEntity::getMortgageState, FundMortgageStatusEnum.getCopperCarDeptMortgageStatus(mortgageEnums));
        finalFundInfoMapper.update(luw);

        //更新订单抵押状态
        updateOrderMortgageStatus(orderInfo.getId(), mortgageEnums);

        CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                .eq(CustomerMortgageInfoEntity::getOrderId, orderInfo.getId())
                .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                .last("LIMIT 1"));

        FinalFundInfoEntity finalFundInfoUpdate = finalFundInfoService.getInfoByOrderId(orderInfo.getId(), FundEnum.getFundEnum(orderInfo.getFundId()));
        if (ObjUtil.isNotNull(customerMortgageInfoEntity) && ObjUtil.isNotNull(finalFundInfoUpdate)) {
            FundMortgageStatusEnum mortgageState = finalFundInfoUpdate.getMortgageState();
            FundMortgageStatusEnum mortgageStateOld = finalFundInfoByMortgageNo.getMortgageState();
            if (ObjUtil.isNotNull(mortgageState) && ObjUtil.isNotNull(mortgageStateOld) && (
                    ObjUtil.equals(mortgageState.getValue(), FundMortgageStatusEnum.COMPLETED.getValue())
                            || ObjUtil.equals(mortgageState.getValue(), FundMortgageStatusEnum.PUSH_TO_TMRI.getValue())
                            || ObjUtil.equals(mortgageState.getValue(), FundMortgageStatusEnum.ACCEPT_COMPLETED.getValue())
            ) && (
                    !ObjUtil.equals(mortgageStateOld.getValue(), FundMortgageStatusEnum.COMPLETED.getValue())
                            && !ObjUtil.equals(mortgageStateOld.getValue(), FundMortgageStatusEnum.PUSH_TO_TMRI.getValue())
                            && !ObjUtil.equals(mortgageStateOld.getValue(), FundMortgageStatusEnum.ACCEPT_COMPLETED.getValue())
            )
            ) {
                log.info("HengTongCopperCarDeptServiceImpl.handleMortgage updateCustomerMortgageInfoEntity orderId:{} mortgageState:{}", orderInfo.getId(), mortgageState);
                customerMortgageInfoMapper.update(new LambdaUpdateWrapper<CustomerMortgageInfoEntity>()
                        .set(CustomerMortgageInfoEntity::getMortgageTime, LocalDateTime.now())
                        .eq(CustomerMortgageInfoEntity::getId, customerMortgageInfoEntity.getId())
                );
            }

        }


        //更新抵押状态
        log.info("HengTongCopperCarDeptServiceImpl.handleMortgage end order_id:{}", order_id);
    }


    /**
     * 处理解抵状态
     */
    private void handleRelease(String order_id, String orderType, CopperCarDeptOrderStatusVO vo) {
        log.info("HengTongCopperCarDeptServiceImpl.handleRelease start order_id:{} vo:{}", order_id, JSONUtil.toJsonStr(vo));
        if (!ObjUtil.equals(orderType, "J")) {
            return;
        }
        OrderInfoEntity orderInfo = null;
        //先判断是否存在对应铜车署id 不存在再使用order_id查询
        FinalFundInfoEntity finalFundInfoByMortgageNo = finalFundInfoMapper.selectOne(new LambdaQueryWrapper<FinalFundInfoEntity>()
                .eq(FinalFundInfoEntity::getEntrustReceiptNo, vo.getOrderId())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .orderByDesc(FinalFundInfoEntity::getCreateTime)
                .last("limit 1")
        );
        if (ObjUtil.isNotNull(finalFundInfoByMortgageNo)) {
            orderInfo = orderInfoMapper.selectById(finalFundInfoByMortgageNo.getOrderId());
            log.info("HengTongCopperCarDeptServiceImpl.handleMortgage no:{} finalFundInfoByMortgageNo:{}", vo.getOrderId(), JSONUtil.toJsonStr(finalFundInfoByMortgageNo));
        } else {
            orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoEntity>()
                    .eq(OrderInfoEntity::getOrderNumber, order_id)
                    .eq(OrderInfoEntity::getDeleteFlag, 0)
            );
            log.info("HengTongCopperCarDeptServiceImpl.handleMortgage order_id:{} orderInfo:{}", order_id, JSONUtil.toJsonStr(orderInfo));
        }
        if (null == orderInfo) {
            log.warn("HengTongCopperCarDeptServiceImpl.handleRelease order_id:{} orderInfo is null", order_id);
            return;
        }
        LambdaQueryWrapper<FinalFundInfoEntity> finalFundInfoLqw = new LambdaQueryWrapper<>();
        finalFundInfoLqw.eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
                    .eq(FinalFundInfoEntity::getFundId, orderInfo.getFundId())
                    .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(FinalFundInfoEntity::getCreateTime)
        ;
        FinalFundInfoEntity finalFundInfo = finalFundInfoMapper.selectOne(finalFundInfoLqw, false);
        if (null == finalFundInfo) {
            log.warn("HengTongCopperCarDeptServiceImpl.handleRelease order_id:{} finalFundInfo is null", order_id);
            return;
        }
        LambdaUpdateWrapper<FinalFundInfoEntity> luw = new LambdaUpdateWrapper<>();
        luw.eq(FinalFundInfoEntity::getOrderId, orderInfo.getId())
                .eq(FinalFundInfoEntity::getDeleteFlag, 0)
                .set(FinalFundInfoEntity::getFundNodeStatus, FundApplyNodeStatusEnum.SUCCESS)
                .set(FinalFundInfoEntity::getRemark, vo.getAbnormalDesc())
                .set(FinalFundInfoEntity::getEntrustReceiptNo, vo.getOrderId())
                .set(FinalFundInfoEntity::getFundNode, FundApplyNodeEnums.MORTGAGE_RELEASE_RESULT_QUERY)
        ;
        String status = vo.getStatus();
        CopperCarDeptMortgageEnums mortgageEnums = CopperCarDeptMortgageEnums.getByCode(status);
        luw.set(FinalFundInfoEntity::getMortgageState, FundMortgageStatusEnum.getCopperCarDeptMortgageReleaseStatus(mortgageEnums));
        finalFundInfoMapper.update(luw);

        //更新订单解抵状态
        updateOrderMortgageSCancelStatus(orderInfo.getId(), mortgageEnums);

        log.info("HengTongCopperCarDeptServiceImpl.handleRelease end order_id:{}", order_id);
    }


    /**
     * 转json
     */
    public String toJsonStr(Object dto) {
        log.info("HengTongCopperCarDeptServiceImpl.toJsonStr begin dto: {}", JSONUtil.toJsonStr(dto));
        try {
            return objectMapper.writeValueAsString(dto);
        } catch (JsonProcessingException e) {
            log.error("HengTongCopperCarDeptServiceImpl.toJsonStr failed error: {}", e.getMessage());
            throw new BusinessException(e);
        }
    }


    /**
     * 将解密后的 JSON 字符串转换为泛型对象
     *
     * @param responseType 响应类型
     * @param decrypt      解密后的 JSON 字符串
     * @return {@link CopperCarApiResult <T>}
     */
    private <T> CopperCarApiResult<T> toApiBean(Class<T> responseType, String decrypt) {
        log.info("HengTongCopperCarDeptServiceImpl.toApiBean start decrypt result: {}", decrypt);
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(CopperCarApiResult.class, responseType);
        try {
            CopperCarApiResult<T> apiResult = objectMapper.readValue(decrypt, javaType);
            log.info("HengTongCopperCarDeptServiceImpl.toApiBean end apiResult: {}", apiResult);
            return apiResult;
        } catch (Exception e) {
            log.error("HengTongCopperCarDeptServiceImpl.toApiBean error parsing response: {}", e.getMessage(), e);
            throw new BusinessException("请求中恒抵押接口异常 err: " + e.getMessage());
        }
    }

    /**
     * 更新订单抵押状态
     */
    private void updateOrderMortgageStatus(Integer orderId, CopperCarDeptMortgageEnums mortgageStatus) {
        log.info("HengTongCopperCarDeptServiceImpl.updateOrderMortgageStatus orderId {} mortgageStatus {}", orderId, mortgageStatus);
        MortgageStateUpdateDTO mortgageStateUpdateDTO = new MortgageStateUpdateDTO();
        mortgageStateUpdateDTO.setOrderId(orderId);
        Integer mortgageState = null;

        if (CopperCarDeptMortgageEnums.CREATED.equals(mortgageStatus)) {
            mortgageState = 1;
            // 查询抵押表是否有数据
            CustomerMortgageInfoEntity customerMortgageInfoEntity = customerMortgageInfoMapper.selectOne(new LambdaQueryWrapper<CustomerMortgageInfoEntity>()
                    .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                    .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0)
                    .orderByDesc(CustomerMortgageInfoEntity::getCreateTime)
                    .last("LIMIT 1"));

            OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);

            FinalFundInfoEntity finalFundInfo = finalFundInfoService.getInfoByOrderId(orderId, FundEnum.getFundEnum(orderInfo.getFundId()));

            if (Objects.isNull(customerMortgageInfoEntity) ||
                    (ObjUtil.isNotNull(customerMortgageInfoEntity) && !ObjUtil.equals(customerMortgageInfoEntity.getMortgageType(), 0))
            ) {
                customerMortgageInfoEntity = new CustomerMortgageInfoEntity()
                        .setOrderId(orderId)
                        .setMortgageType(0)
                ;
                customerMortgageInfoMapper.insert(customerMortgageInfoEntity);
            } else {
                //失效线下抵押
                customerMortgageInfoMapper.update(new LambdaUpdateWrapper<CustomerMortgageInfoEntity>()
                        .set(CustomerMortgageInfoEntity::getDeleteFlag, 1)
                        .eq(CustomerMortgageInfoEntity::getOrderId, orderId)
                        .eq(CustomerMortgageInfoEntity::getMortgageType, 1)
                        .eq(CustomerMortgageInfoEntity::getDeleteFlag, 0));
            }

            if (ObjUtil.isNotNull(customerMortgageInfoEntity)
                    && ObjUtil.isNotNull(finalFundInfo) && ObjUtil.isNotNull(finalFundInfo.getAgentRadio())) {

                customerMortgageInfoEntity.setAgentRadio(finalFundInfo.getAgentRadio());
                customerMortgageInfoMapper.update(new LambdaUpdateWrapper<CustomerMortgageInfoEntity>()
                        .set(CustomerMortgageInfoEntity::getAgentRadio, finalFundInfo.getAgentRadio())
                        .eq(CustomerMortgageInfoEntity::getId, customerMortgageInfoEntity.getId())
                );
            }

        } else if (CopperCarDeptMortgageEnums.CREATE_FAILED.equals(mortgageStatus) || CopperCarDeptMortgageEnums.HANDLE_FAILED.equals(mortgageStatus)) {
            mortgageState = 4;

        } else if (CopperCarDeptMortgageEnums.CANCELED.equals(mortgageStatus)) {
            mortgageState = 0;

        } else if (CopperCarDeptMortgageEnums.PUSH_TO_TMRI.equals(mortgageStatus)) {
            mortgageState = 2;
        }else if (CopperCarDeptMortgageEnums.ACCEPT_COMPLETED.equals(mortgageStatus)) {
            mortgageState = 2;
        } else if (CopperCarDeptMortgageEnums.COMPLETED.equals(mortgageStatus)) {
            mortgageState = 2;
        } else {
            log.info("HengTongCopperCarDeptServiceImpl.updateOrderMortgageStatus mortgageStatus {} not support ", mortgageStatus);
            mortgageState = 1;
        }
        log.info("HengTongCopperCarDeptServiceImpl.updateOrderMortgageStatus mortgageState {}", mortgageState);
        FundUndoMortgageInfoEntity fundUndoMortgageInfoEntity = fundUndoMortgageInfoMapper.selectOne(new LambdaQueryWrapper<FundUndoMortgageInfoEntity>()
                .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                .eq(FundUndoMortgageInfoEntity::getOperateType, "1")
                .eq(FundUndoMortgageInfoEntity::getHistory, 0)
        );
        if (ObjUtil.isNotNull(fundUndoMortgageInfoEntity)) {
            fundUndoMortgageInfoEntity.setMortgageStatus(mortgageState.toString());
            fundUndoMortgageInfoMapper.updateById(fundUndoMortgageInfoEntity);
        }
        log.info("HengTongCopperCarDeptServiceImpl.updateOrderMortgageStatus mortgageState {}", mortgageState);
        mortgageStateUpdateDTO.setMortgageState(mortgageState);
        mortgageStateUpdateDTO.setMortgageType(0);
        orderFeign.updateMortgageState(mortgageStateUpdateDTO);

    }


    /**
     * 更新订单解抵状态
     */
    private void updateOrderMortgageSCancelStatus(Integer orderId, CopperCarDeptMortgageEnums mortgageStatus) {
        log.info("FuMinPreServiceImpl.updateOrderMortgageSCancelStatus orderId {} mortgageStatus {}", orderId, mortgageStatus);
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        MortgageStateUpdateDTO mortgageStateUpdateDTO = new MortgageStateUpdateDTO();
        mortgageStateUpdateDTO.setOrderId(orderId);
        Integer mortgageState = orderInfoEntity.getMortgageState();
        if (ObjUtil.isNull(orderInfoEntity) || ObjUtil.isNull(orderInfoEntity.getMortgageState())) {
            return;
        }
        FundUndoMortgageInfoEntity fundUndoMortgageInfoEntity = fundUndoMortgageInfoMapper.selectOne(new LambdaQueryWrapper<FundUndoMortgageInfoEntity>()
                .eq(FundUndoMortgageInfoEntity::getOrderId, orderId)
                .eq(FundUndoMortgageInfoEntity::getDeleteFlag, 0)
                .eq(FundUndoMortgageInfoEntity::getOperateType, "2")
                .eq(FundUndoMortgageInfoEntity::getHistory, 0)
        );
        if (CopperCarDeptMortgageEnums.COMPLETED.equals(mortgageStatus)) {
            if (ObjUtil.isNotNull(orderInfoEntity)) {
                if (Objects.equals(orderInfoEntity.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_SUCCESS.getCode())) {
                    mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_SUCCESS.getCode();
                }
                if (Objects.equals(orderInfoEntity.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_PRE.getCode())) {
                    mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_SUCCESS_HAVE.getCode();
                }
            }
        } else if (CopperCarDeptMortgageEnums.HANDLE_FAILED.equals(mortgageStatus) || CopperCarDeptMortgageEnums.CREATE_FAILED.equals(mortgageStatus)) {
            if (ObjUtil.isNotNull(orderInfoEntity)) {
                if (Objects.equals(orderInfoEntity.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_SUCCESS.getCode())) {
                    mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_FAIL.getCode();
                }
                if (Objects.equals(orderInfoEntity.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_PRE.getCode())) {
                    mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_FAIL_HAVE.getCode();
                }
            }
        } else if (CopperCarDeptMortgageEnums.CANCELED.equals(mortgageStatus)) {
            if (Objects.equals(orderInfoEntity.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_SUCCESS.getCode())) {
                mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_BACK_SUCCESS.getCode();
            }
            if (Objects.equals(orderInfoEntity.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_PRE.getCode())) {
                mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_BACK_SUCCESS_HAVE.getCode();
            }
            if (ObjUtil.isNotNull(fundUndoMortgageInfoEntity)) {
                fundUndoMortgageInfoEntity.setDeleteFlag(1);
                fundUndoMortgageInfoEntity.setHistory(1);
            }
        } else {
            if (ObjUtil.isNotNull(orderInfoEntity)) {
                if (Objects.equals(orderInfoEntity.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_SUCCESS.getCode())) {
                    mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_PROCESSING.getCode();
                }
                if (Objects.equals(orderInfoEntity.getMortgageState(), MortgageEnums.MORTGAGE_STATUS_PRE.getCode())) {
                    mortgageState = MortgageEnums.MORTGAGE_STATUS_CANCEL_PROCESSING_HAVE.getCode();
                }
            }
        }
        log.info("FuMinPreServiceImpl.updateOrderMortgageSCancelStatus mortgageState {}", mortgageState);
        if (ObjUtil.isNotNull(fundUndoMortgageInfoEntity)) {
            fundUndoMortgageInfoEntity.setMortgageStatus(mortgageState.toString());
            fundUndoMortgageInfoMapper.updateById(fundUndoMortgageInfoEntity);
            log.info("FuMinPreServiceImpl.updateOrderMortgageSCancelStatus fundUndoMortgageInfoEntity {}", fundUndoMortgageInfoEntity);
        }
    }

    /**
     * 获取查看资源 URL
     *
     * @param resourceId 资源 ID
     * @return {@link String }
     */
    private String getViewResourceUrl(String resourceId) {
        if (StrUtil.isNotEmpty(resourceId)) {
            Result<String> stringResult = resourceFeign.longTimeAccessRouteRequest(resourceId);
            if (Result.isSuccess(stringResult)) {
                return stringResult.getData();
            }
        }
        return resourceId;
    }
}
