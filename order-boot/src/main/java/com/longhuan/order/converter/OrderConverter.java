package com.longhuan.order.converter;

import com.longhuan.order.pojo.dto.RelatedOrderInfoDTO;
import com.longhuan.order.pojo.dto.SaveOrderInfoDTO;
import com.longhuan.order.pojo.entity.OrderInfoEntity;
import com.longhuan.order.pojo.vo.OrderInfoVO;
import com.longhuan.order.pojo.vo.RelatedOrderVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/31
 */
@Mapper(componentModel = "spring")
public interface OrderConverter {
    @Mapping(source = "orderId", target = "id")
    OrderInfoEntity saveOrderInfoDtoToEntity(SaveOrderInfoDTO orderDTO);


    List<RelatedOrderVO> dtoToRelatedOrderVO(List<RelatedOrderInfoDTO> infoDTOList);

    /**
     * 实体 2vo
     *
     * @param orderDTO 订购 DTO
     * @return {@link OrderInfoVO }
     */
    OrderInfoVO entity2vo(OrderInfoEntity orderDTO);
}
