package com.longhuan.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.OrderArrivedDTO;
import com.longhuan.order.pojo.vo.OrderArrivedVO;
import com.longhuan.order.service.OrderArrivedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订单办抵信息表控制层
 *
 * <AUTHOR> css
 * @date : 2025-05-22
 */
@Api(tags = "订单办抵信息功能接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/arrived")
public class OrderArrivedController {
    private final OrderArrivedService orderArrivedService;

    /**
     * 查询列表
     *
     * @return 实例对象
     */
    @ApiOperation(value = "分页查询订单办抵")
    @PostMapping("/page/orderArrivedList")
    public Result<Page<OrderArrivedVO>> selectOrderArrivedList(@RequestBody OrderArrivedDTO dto) {
        return Result.success(orderArrivedService.selectProductRongdanList(dto));
    }

    /**
     * 编辑订单办抵
     *
     * @param dto 订单办抵信息
     */
    @PostMapping("/updateOrderArrivedInfo")
    public Result<Boolean> updateOrderArrivedInfo(@RequestBody @Validated OrderArrivedDTO dto) {
        return Result.success(orderArrivedService.updateOrderArrivedInfo(dto));
    }
}