package com.longhuan.order.controller;

import com.longhuan.common.core.result.Result;
import com.longhuan.order.pojo.dto.OrderCopyCheckDTO;
import com.longhuan.order.pojo.dto.OrderCopyPreApprovalDTO;
import com.longhuan.order.pojo.vo.OrderCopyCheckVO;
import com.longhuan.order.pojo.vo.OrderCopyPreApprovalVO;
import com.longhuan.order.service.OrderCopyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 订单复制控制器
 *
 * <AUTHOR>
 * @date 2025/01/21
 */
@Slf4j
@Api(tags = "订单复制接口")
@RestController
@RequestMapping("/api/v1/order/copy")
@RequiredArgsConstructor
public class OrderCopyController {

    private final OrderCopyService orderCopyService;

    /**
     * 检测是否可以复制订单
     *
     * @param orderId 订单ID
     * @return 检测结果
     */
    @ApiOperation(value = "检测是否可以复制订单", notes = "根据订单号检测订单是否可以复制")
    @GetMapping("/check")
    public Result<OrderCopyCheckVO> checkOrderCopy(@RequestParam Integer orderId) {
        log.info("接收到订单复制检测请求，订单号：{}", orderId);
        OrderCopyCheckVO result = orderCopyService.checkOrderCopy(orderId);

        log.info("订单复制检测完成，订单号：{}，结果：{}", orderId, result.getCanCopy() ? "可以复制" : "不可以复制");

        return Result.success(result);
    }

    /**
     * 复制预授信订单
     *
     * @param dto 复制预授信订单请求参数
     * @return 复制结果
     */
    @ApiOperation(value = "复制预授信订单", notes = "根据原订单ID复制预授信订单，包括预审记录和相关OCR数据")
    @PostMapping("/copyPreApproval")
    public Result<OrderCopyPreApprovalVO> copyPreApprovalOrder(@RequestBody @Validated OrderCopyPreApprovalDTO dto) {
        log.info("接收到复制预授信订单请求，原订单ID：{}", dto.getOriginalOrderId());

        OrderCopyPreApprovalVO result = orderCopyService.copyPreApprovalOrder(dto);

        log.info("复制预授信订单完成，结果：{}，新预审ID：{}", result.getSuccess() ? "成功" : "失败", result.getNewPreApprovalId());

        return Result.success(result);
    }
}
